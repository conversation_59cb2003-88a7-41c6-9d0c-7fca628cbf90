import path from 'path';
const resolve = (p) => {
    return path.resolve(__dirname, p);
};
import alias from '@rollup/plugin-alias';
import {
    makeBabelPlugin,
    makeCommonJsPlugin,
    makeImagePlugin,
    makeJsonPlugin,
    makeNodeResolvePlugin,
    makeProgressPlugin,
    makeResolvePlugin,
    makeTerserPlugin,
    makePostCssPlugin,
    makeVuePlugin,
} from './rollup-plugins';

export default {
    input: resolve('../src/components-composite/index.js'),
    output: [
        {
            exports: 'auto',
            dir: './src/components-composite/lib',
            entryFileNames: 'pc-components.min.js',
            chunkFileNames: 'pc-components.[name].chunk.js',
        },
    ],
    plugins: [
        alias({
            entries: [
                { find: '@', replacement: resolve('../src') },
                { find: '~', replacement: resolve('../src/') },
                { find: 'src', replacement: resolve('../src') },
            ],
        }),
        makeResolvePlugin(),
        makeProgressPlugin(),
        makeVuePlugin(),
        makeBabelPlugin(),
        makePostCssPlugin('pc-components.min'),
        makeImagePlugin(),
        makeJsonPlugin(),
        makeNodeResolvePlugin(),
        makeCommonJsPlugin(),
        makeTerserPlugin(),
    ],
    external: [
        'jquery',
        'vue',
        'fs',
        'path',
        'qrcode',
        'echarts',
        'vue-i18n',
        'vuedraggable',
        '@abc/ui-pc',
        '@abc/constants',
        "@abc/sortable",
        "@abc/utils",
        "@abc/utils-date",
        "@abc/utils-dom",
    ],
};
