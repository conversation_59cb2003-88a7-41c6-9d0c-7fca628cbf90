import { terser } from 'rollup-plugin-terser';
import commonjs from '@rollup/plugin-commonjs';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import postcss from 'rollup-plugin-postcss';
import postcssImport from 'postcss-import';
import postcssUrl from 'postcss-url';
import autoprefixer from 'autoprefixer';
import babel from '@rollup/plugin-babel';
import vue from 'rollup-plugin-vue';
import progress from 'rollup-plugin-progress';
import resolve from 'rollup-plugin-node-resolve';
import image from '@rollup/plugin-image';
import json from '@rollup/plugin-json';
// import path from 'path';

export function makeResolvePlugin() {
    return resolve({ extensions: ['.vue'] });
}

export function makeProgressPlugin() {
    return progress();
}

export function makeVuePlugin() {
    return vue({
        // 把单文件组件中的样式插入到html的style标签中
        // css: false,
        // 转成render function
        compileTemplate: true,
        style: {
            preprocessOptions: {
                scss: {
                    includePaths: ['node_modules']
                }
            }
        }
    });
}

export function makeTerserPlugin() {
    return terser();
}

export function makeCommonJsPlugin() {
    return commonjs();
}

export function makeNodeResolvePlugin() {
    return nodeResolve(
        {
            preferBuiltins: false,
            mainFields: ['module', 'main', 'browser'],
            moduleDirectories: ['es'],
        },
    );
}

export function makePostCssPlugin(outputFileName) {
    return postcss({
        use: {
            sass: {
                includePaths: [
                    'node_modules'
                ],
            },
        },
        plugins: [
            postcssImport(),
            postcssUrl({
                url: 'inline',
                maxSize: 1000000, // ! to base64
            }),
            autoprefixer({
                remove: false,
            }),
        ],
        extensions: ['.css', '.scss'],
        minimize: true,
        extract: outputFileName ? `${outputFileName}.css` : false,
    });
}

export function makeBabelPlugin() {
    return babel({
        extensions: ['.vue', '.js'],
        presets: ['@vue/babel-preset-jsx'],
        babelHelpers: 'bundled',
        skipPreflightCheck: true,
        plugins: [
            '@babel/plugin-proposal-class-properties',
            '@babel/plugin-proposal-nullish-coalescing-operator',
            '@babel/plugin-proposal-optional-chaining'
        ]
    });
}

export function makeImagePlugin() {
    return image();
}

export function makeJsonPlugin() {
    return json();
}
