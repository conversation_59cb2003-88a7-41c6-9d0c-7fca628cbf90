'use strict';
const path = require('path');
const utils = require('../build/utils');
const config = require('../config');
const { VueLoaderPlugin } = require('vue-loader');
const { getEntry } = require('./lite-config');
const rspack = require('@rspack/core');

const {
    container: { ModuleFederationPlugin },
} = require('@rspack/core');
const {
    isLocal, isFeature, isOwn,
} = require('../build/utils');
const exposesBase = require('../src/exposes-base');

function resolve(dir) {
    return path.join(__dirname, '..', dir);
}

module.exports = {
    context: path.resolve(__dirname, '../'),
    entry: getEntry(),
    output: {
        path: config.build.assetsRoot,
        filename: '[name].js',
        publicPath: config.devonline.assetsPublicPath,

        // ❓❓❓ set uniqueName explicitly to make HMR work
        uniqueName: 'app',
    },
    resolve: {
        extensions: ['.js', '.vue', '.json', '.scss'],
        alias: {
            'vue$': 'vue/dist/vue.esm.js',
            '@': resolve('src'),
            'src': path.resolve(__dirname, '../src'),
            'assets': path.resolve(__dirname, '../src/assets'),
            'components': path.resolve(__dirname, '../src/components'),
            'views': path.resolve(__dirname, '../src/views'),
            'styles': path.resolve(__dirname, '../src/styles'),
            'theme': path.resolve(__dirname, '../src/theme'),
            'api': path.resolve(__dirname, '../src/api'),
            'utils': path.resolve(__dirname, '../src/utils'),
            'store': path.resolve(__dirname, '../src/store'),
            'router': path.resolve(__dirname, '../src/router'),
            'mock': path.resolve(__dirname, '../src/mock'),
            'static': path.resolve(__dirname, '../static'),
            '@ohif-core': path.resolve(__dirname, '../src/medical-imaging-viewer/core'),
            '@modules': path.resolve(__dirname),
        },
    },
    module: {
        rules: [
            {
                test: /\.vue$/,
                use: [
                    {
                        loader: 'thread-loader',
                    },
                    {
                        loader: 'vue-loader',
                        options: {
                            experimentalInlineMatchResource: true,
                        },
                    },
                ],
                exclude: [/node_modules/, /cypress/, /packages/],
            },
            {
                test: /\.mjs$/,
                include: /node_modules/,
                type: 'javascript/auto',
            },
            {
                // js
                test: /\.js$/,
                exclude: [/node_modules/, /cypress/, /packages/],
                use: [
                    {
                        loader: require.resolve('./lite-loader.js'),
                    },
                ],
                type: 'javascript/auto',
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset/resource',
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset/resource',
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset/resource',
            },
        ],
    },
    plugins: [
        new rspack.ProgressPlugin(),
        new VueLoaderPlugin(),

        new ModuleFederationPlugin({
            name: 'MfBase',
            filename: (isLocal || isFeature || isOwn) ? 'mf-entry.js' : 'mf-entry.[contenthash:8].js',
            exposes: exposesBase,
            remotes: {
                'MfOrderCloud': 'MfOrderCloud@[window.MF_CONFIG.MfOrderCloud.url]',
                'MfFeEngine': 'MfFeEngine@[window.MF_CONFIG.MfFeEngine.url]',
            },
            shared: {
                vue: {
                    singleton: true,
                    requiredVersion: '2.7.14',
                    strictVersion: true,
                },
                'vue-router': {
                    singleton: true,
                    requiredVersion: '^3.5.4',
                },
                axios: {
                    singleton: true,
                    requiredVersion: '0.15.3',
                },
                '@abc/constants': {
                    singleton: true,
                },
                '@abc/cornerstone-tools': {
                    singleton: true,
                },
                '@abc/error-monitor': {
                    singleton: true,
                },
                '@abc/sortable': {
                    singleton: true,
                },
                '@abc/ui-pc': {
                    singleton: true,
                },
                '@abc/utils': {
                    singleton: true,
                },
                '@abc/utils-date': {
                    singleton: true,
                },
                '@abc/utils-dom': {
                    singleton: true,
                },
            },

            // 🔥🔥🔥 mf 模块联邦
            runtimePlugins: [path.join(__dirname, './runtime-plugin.js')],
        }),

        utils.AbcIconfontSvgInjectPlugin(),
        utils.AbcFedConfigLoaderPlugin({
            onInjectHTML: (htmlPluginData) => ['index.html', 'app.html', 'home.html', 'chain.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename),
        }),
        utils.AbcMedicalImagingViewerSDKLoaderPlugin({
            onInjectHTML: (htmlPluginData) => ['index.html', 'app.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename),
        }),
        utils.AbcEmrEditorSDKLoaderPlugin({
            onInjectHTML: (htmlPluginData) => ['index.html', 'app.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename),
        }),
        utils.AbcDesktopExtSDKLoaderPlugin({
            onInjectHTML: (htmlPluginData) => ['index.html', 'app.html', 'home.html', 'chain.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename),
        }),
        utils.AbcIconfontSvgInjectPlugin(),
        utils.AbcRumLoaderPlugin({
            onInjectHTML: htmlPluginData => ['index.html', 'app.html', 'home.html', 'chain.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename)
        }),
    ],
};

