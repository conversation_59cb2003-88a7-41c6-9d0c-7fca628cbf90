const path = require('path');
const fs = require('fs');

function checkDirAndCreate(dir) {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
}

function readCacheFile(filePath) {
    try {
        return fs.existsSync(filePath) ? JSON.parse(fs.readFileSync(filePath, 'utf-8')) : { compilerByBabelFiles: [] };
    } catch (error) {
        console.error('读取缓存文件出错:', error);
        return { compilerByBabelFiles: [] };
    }
}

function writeCacheFile(filePath, data) {
    try {
        fs.writeFileSync(filePath, JSON.stringify(data), 'utf-8');
    } catch (error) {
        console.error('写入缓存文件出错:', error);
    }
}

class CustomCachePlugin {
    constructor() {
        this.cacheFilePath = path.resolve(__dirname, '..', 'node_modules/.cache/lite.json');
        this.cacheDir = path.resolve(__dirname, '..', 'node_modules/.cache');
    }

    apply(compiler) {
        compiler.hooks.emit.tapAsync('CustomCachePlugin', (compilation, callback) => {
            if (!compilation.compilerByBabelFiles || !compilation.compilerByBabelFiles.length) {
                return callback();
            }

            const cache = readCacheFile(this.cacheFilePath);
            cache.compilerByBabelFiles = [...new Set([...cache.compilerByBabelFiles, ...compilation.compilerByBabelFiles])];
            writeCacheFile(this.cacheFilePath, cache);

            callback();
        });

        compiler.hooks.beforeCompile.tap('CustomCachePlugin', () => {
            checkDirAndCreate(this.cacheDir);
        });
    }
}

module.exports = CustomCachePlugin;
