
const swc = require('@swc/core');
const babel = require('@babel/core');

const path = require('path');
const fs = require('fs');

let promise;
let resolve;
let reject;
function generatePromise() {
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject) => {
        resolve = _resolve;
        reject = _reject;
    });
    return {
        resolve,
        reject,
        promise,
    };
}

(function() {
    const { resolve: s, reject: f, promise: p  } = generatePromise();
    promise = p;
    resolve = s;
    reject = f;

    try {
        const cacheFile = path.join(__dirname, '..', 'node_modules/.cache/lite.json');

        if (fs.existsSync(cacheFile)) {
            const cache = JSON.parse(fs.readFileSync(cacheFile, 'utf-8'));
            resolve(cache.compilerByBabelFiles || []);
        } else {
            resolve([])
        }
    } catch (e) {
        resolve([])
    }
})()

module.exports = async function (content) {
    // https://webpack.js.org/api/loaders/#the-loader-context
    const callback = this.async();

    // 获取当前文件的完整路径
    const { resourcePath, _compilation } = this;
    const cacheRecords = await promise;

    // 计算项目根目录的绝对路径
    const projectRoot = path.join(__dirname, '../');
    const filename = path.relative(projectRoot, resourcePath);


    // ❓❓❓ handle debugger sourcemap work
    const baseOptions = {
        sourceFileName: resourcePath,
        filename: resourcePath,
        sourceMaps: true,
    }
    const babelOptions = {
        presets: [
            [
                '@babel/preset-env',
                {
                    'modules': false, // 对ES6的模块文件不做转化，以便使用tree shaking、sideEffects等
                    'useBuiltIns': 'entry', // browserslist环境不支持的所有垫片都导入
                    // https://babeljs.io/docs/en/babel-preset-env#usebuiltins
                    // https://github.com/zloirock/core-js/blob/master/docs/2019-03-19-core-js-3-babel-and-a-look-into-the-future.md
                    'corejs': {
                        'version': 3, // 使用core-js@3
                    },
                },
            ],
            '@vue/babel-preset-jsx',
        ],
        plugins: [
            [
                '@babel/plugin-transform-runtime',
                {
                    'corejs': false, // 解决 helper 函数重复引入
                },
            ],
            '@babel/plugin-proposal-object-rest-spread',
            '@babel/plugin-proposal-optional-chaining',
            '@babel/plugin-proposal-nullish-coalescing-operator',
            ['@babel/plugin-proposal-decorators', { 'legacy': true }],
            ['@babel/plugin-proposal-class-properties'],
        ],

        ...baseOptions,
    };
    const swcOptions = {
        'jsc': {
            'parser': {
                'syntax': 'ecmascript',
            },
        },
        ...baseOptions
    };

    // 处理 require 函数
    function formatContent(i) {
        const regex = /const\s+(\w+)\s*=\s*\((\w+)\)\s*=>\s*require\(\[(?:[\s\S]*?)\],\s*\2\)(?:;)?/g;

        return i.replace(regex, (match, functionName) => {
            return `const ${functionName} = () => import('${match.split('\'')[1]}');`;
        });
    }
    content = formatContent(content);

    const transformBySwc = () => {
        return swc.transform(content, swcOptions);
    }
    const transformByBabel = () => {
        return new Promise((resolve, reject) => {
            babel.transform(content, babelOptions, (err, result) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(result);
                }
            });
        });
    }

    // cacheRecords 为已经处理过的文件（不能使用swc编译），在二次编译时直接使用babel
    if (cacheRecords.includes(filename)) {
        try {
            const { code, map } = await transformByBabel();
            callback(null, code, map);
        } catch (e) {
            callback(e);
        }

        return;
    }

    // 兼容处理：如果swc不支持的语法，则使用babel
    try {
        const { code, map } = await transformBySwc();
        callback(null, code, map);
    } catch (e) {
        // 记录 swc编译错误文件
        if (!_compilation.compilerByBabelFiles) {
            _compilation.compilerByBabelFiles = [];
        }

        _compilation.compilerByBabelFiles.push(filename);

        try {
            const { code, map } = await transformByBabel();
            callback(null, code, map);
        } catch (e) {
            callback(e);
        }
    }
};
