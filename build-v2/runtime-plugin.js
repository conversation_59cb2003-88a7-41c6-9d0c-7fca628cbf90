// ❓❓❓ customMap 需要和 inject-mf-develop.js 保持一致
// ❓❓❓ MfFeEngine 本地调试 - 使用ip, 别使用localhost.

const customMap = {
    MfBase: {
        url: `${location.origin}/mf-entry.js`,
    },
    // MfFeEngine: {
    //     url: 'http://*************:10001/mf-entry.js',
    // },
    // MfOrderCloud: {
    //     url: `//${location.hostname}:9000/mf-entry.js`,
    // },
    MfDeepseek: {
        url: `//${location.hostname}:9999/mf-entry.js`,
    },
};


export default function () {
    return {
        name: 'ExternalRemotesPlugin',

        // ! 运行时处理 - 动态entry
        beforeRequest: async (args) => {
            const { options, origin } = args;

            const map = Object.assign({}, window.MF_CONFIG, customMap);
            const remotes = options.remotes.map((remote) => {
                remote.entry = map[remote.name].url;
                return remote;
            })
            origin.initOptions({
                remotes,
            })

            console.log('beforeRequest', args);

            return args
        },
    };
}
