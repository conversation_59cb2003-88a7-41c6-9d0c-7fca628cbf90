'use strict';
const path = require('path');
const utils = require('../build/utils');
const config = require('../config');
const { VueLoaderPlugin } = require('vue-loader');
const { getEntry } = require('./lite-config');
const rspack = require('@rspack/core');

const {
    container: { ModuleFederationPlugin },
} = require('@rspack/core');
const {
    isLocal, isFeature, isOwn,
} = require('../build/utils');
const exposesBase = require('../src/exposes-base');

function resolve(dir) {
    return path.join(__dirname, '..', dir);
}

module.exports = {
    context: path.resolve(__dirname, '../'),
    entry: getEntry(),
    output: {
        path: config.build.assetsRoot,
        filename: '[name].js',
        publicPath: config.devonline.assetsPublicPath,

        // ❓❓❓ set uniqueName explicitly to make HMR work
        uniqueName: 'pc',
    },
    resolve: {
        extensions: ['.js', '.vue', '.json', '.scss'],
        alias: {
            'vue$': 'vue/dist/vue.esm.js',
            '@': resolve('src'),
            'src': path.resolve(__dirname, '../src'),
            'assets': path.resolve(__dirname, '../src/assets'),
            'components': path.resolve(__dirname, '../src/components'),
            'views': path.resolve(__dirname, '../src/views'),
            'styles': path.resolve(__dirname, '../src/styles'),
            'theme': path.resolve(__dirname, '../src/theme'),
            'api': path.resolve(__dirname, '../src/api'),
            'utils': path.resolve(__dirname, '../src/utils'),
            'store': path.resolve(__dirname, '../src/store'),
            'router': path.resolve(__dirname, '../src/router'),
            'mock': path.resolve(__dirname, '../src/mock'),
            'static': path.resolve(__dirname, '../static'),
            '@ohif-core': path.resolve(__dirname, '../src/medical-imaging-viewer/core'),
            '@modules': path.resolve(__dirname),
            // https://github.com/vueuse/vue-demi/issues/202
            'vue-demi$': 'vue-demi/lib/index.cjs',
        },
    },
    module: {
        rules: [
            {
                test: /\.vue$/,
                use: [
                    {
                        loader: 'thread-loader',
                    },
                    {
                        loader: 'vue-loader',
                        options: {
                            experimentalInlineMatchResource: true,
                        },
                    },
                ],
                exclude: [/node_modules/, /cypress/, /src\/modules/, /packages/],
            },
            {
                test: /\.mjs$/,
                include: /node_modules/,
                type: 'javascript/auto',
            },
            {
                // js
                test: /\.js$/,
                exclude: [/node_modules/, /cypress/, /src\/modules/, /packages/],
                use: [
                    {
                        loader: require.resolve('./lite-loader.js'),
                    },
                ],
                type: 'javascript/auto',
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset/resource',
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset/resource',
            },
            {
                test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
                type: 'asset/resource',
            },
        ],
    },
    plugins: [
        new rspack.ProgressPlugin(),
        new VueLoaderPlugin(),

        new ModuleFederationPlugin({
            name: 'MfBase',
            filename: (isLocal || isFeature || isOwn) ? 'mf-entry.js' : 'mf-entry.[contenthash:8].js',
            exposes: exposesBase,
            remotes: {
                'MfOrderCloud': 'MfOrderCloud@[window.MF_CONFIG.MfOrderCloud.url]',
                'MfFeEngine': 'MfFeEngine@[window.MF_CONFIG.MfFeEngine.url]',
            },
            shared: {
                vue: {
                    // 启用单例模式，那么 remote 应用组件和 host 应用共享的依赖只加载一次，当版本不一致时加载更高的版本
                    singleton: true,
                    /**
                     * 在使用共享依赖时，会判断该依赖版本是否大于等于 requiredVersion ，如果是则会正常使用。如果小于 requiredVersion 那么会在控制台警告，并使用当前共享依赖中最小的版本。
                     * 当一方设置 requiredVersion ，另一方设置 singleton 时，会加载 requiredVersion 的依赖，singleton 方直接使用 requiredVersion 的依赖，不论版本高低。
                     */
                    requiredVersion: '2.7.14',
                    // 用来强化 requiredVersion。如果设置为 true，那么必须精确地匹配 requiredVersion 中规定的版本，否则共享模块会报错并且不会加载该模块
                    strictVersion: true,
                },
                'vue-router': {
                    singleton: true,
                    requiredVersion: '^3.5.4',
                },
                axios: {
                    singleton: true,
                    requiredVersion: '0.15.3',
                },
                '@abc/constants': {
                    singleton: true,
                },
                '@abc/error-monitor': {
                    singleton: true,
                },
                '@abc/sortable': {
                    singleton: true,
                },
                '@abc/ui-pc': {
                    singleton: true,
                },
                '@abc/utils': {
                    singleton: true,
                },
                '@abc/utils-date': {
                    singleton: true,
                },
                '@abc/utils-dom': {
                    singleton: true,
                },
            },

            // 🔥🔥🔥 mf 模块联邦
            runtimePlugins: [path.join(__dirname, './runtime-plugin.js')],

            // 复用优先。设置后，不会自动加载 remotes 入口文件（仅在有需求时才会加载），优先复用已注册的共享依赖。
            shareStrategy: 'loaded-first',
        }),

        utils.AbcIconfontSvgInjectPlugin(),
        utils.AbcFedConfigLoaderPlugin({
            onInjectHTML: (htmlPluginData) => ['index.html', 'app.html', 'home.html', 'chain.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename),
        }),
        utils.AbcMedicalImagingViewerSDKLoaderPlugin({
            onInjectHTML: (htmlPluginData) => ['index.html', 'app.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename),
        }),
        utils.AbcEmrEditorSDKLoaderPlugin({
            onInjectHTML: (htmlPluginData) => ['index.html', 'app.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename),
        }),
        utils.AbcIconfontSvgInjectPlugin(),
        utils.AbcRumLoaderPlugin({
            onInjectHTML: htmlPluginData => ['index.html', 'app.html', 'home.html', 'chain.html', 'hospital-app.html', 'pharmacy-app.html'].includes(htmlPluginData.plugin.options.filename)
        }),
    ],
};

