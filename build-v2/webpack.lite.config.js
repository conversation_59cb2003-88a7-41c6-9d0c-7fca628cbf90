'use strict';
const utils = require('../build/utils');
const config = require('../config');
const { merge } = require('webpack-merge');
const baseWebpackConfig = require('./webpack.lite.base.conf');
const openInEditor = require('launch-editor-middleware');
const portfinder = require('portfinder');
const path = require('path');
const { getTemplate } = require('./lite-config');
const rspack = require('@rspack/core');
const { RsdoctorRspackPlugin } = require('@rsdoctor/rspack-plugin');
const { getLazyLoadRule } = require('./lite-experiments');
const CustomCachePlugin = require('./cache-plugin');

const defineConfig = require('../config/dev.env');
const PrintDevPrePlugin = require('../build/print-dev-pre-plugin');

const plugins = [
    new rspack.DefinePlugin({
        'process.env': defineConfig,
    }),
    new rspack.ProvidePlugin({
        $: 'jquery',
        'jQuery': 'jquery',
        Popper: ['popper.js', 'default'],
    }),
    ...getTemplate(),
    process.env.RSDOCTOR && new RsdoctorRspackPlugin({
        disableClientServer: false,
        features: ['lite', 'loader', 'plugins', 'bundle'],
    }),
    process.env.PRINT_DEV && new PrintDevPrePlugin({
        scriptUrl: `http://${utils.getIPAddress()}:9999`,
    }),
    new CustomCachePlugin()
].filter(Boolean);

const liteConfig = merge(baseWebpackConfig, {
    mode: 'development',

    cache: true,

    watch: true,

    // ❓❓❓ 处理debugger - cheap-module-source-map 最适用开发环境定位
    devtool: 'cheap-module-source-map',

    watchOptions: { // 最小化监控范围
        ignored: [
            '**/node_modules',
            path.posix.resolve(__dirname, '../tool'),
            path.posix.resolve(__dirname, '../cypress'),
            path.posix.resolve(__dirname, '../config'),
            path.posix.resolve(__dirname, '../library'),
            path.posix.resolve(__dirname, '../src/modules'),
            path.posix.resolve(__dirname, '../packages'),
        ],

        // ❓❓❓ 处理hmr死循环
        poll: false,
        aggregateTimeout: 1500,
    },

    // 优化选项关闭
    optimization: {
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false, // 分包
        minimize: false, // 压缩代码
        concatenateModules: false, // 模块合并
        usedExports: false, // Tree-shaking


        // ❓❓❓ 处理hmr
        runtimeChunk: 'single',
    },

    module: {
        // 🔥🔥🔥 启用lightningcss
        rules: [
            {
                test: /\.css$/,
                use: [
                    { loader: 'vue-style-loader' },
                    { loader: 'css-loader', options: { sourceMap: false } },
                    {
                        loader: 'builtin:lightningcss-loader',
                        options: {
                            targets: 'ie 10',
                        },
                    },
                ]
            },
            {
                test: /\.sass$/,
                use: [
                    { loader: 'vue-style-loader' },
                    { loader: 'css-loader', options: { sourceMap: false } },
                    {
                        loader: 'builtin:lightningcss-loader',
                        options: {
                            targets: 'ie 10',
                        },
                    },
                    {
                        loader: 'sass-loader',
                        options: {
                            indentedSyntax: true,
                            data: '@import "~@/styles/theme.scss";',
                            additionalData: '@import "~@/styles/theme.scss";',
                            sourceMap: false
                        }
                    }
                ]
            },
            {
                test: /\.scss$/,
                use: [
                    { loader: 'vue-style-loader' },
                    { loader: 'css-loader', options: { sourceMap: false } },
                    {
                        loader: 'builtin:lightningcss-loader',
                        options: {
                            targets: 'ie 10',
                        },
                    },
                    {
                        loader: 'sass-loader',
                        options: {
                            data: '@import "~@/styles/theme.scss";',
                            additionalData: '@import "~@/styles/theme.scss";',
                            sourceMap: false
                        }
                    }
                ]
            }]
    },


    performance: false,

    // 🔥🔥🔥按需编译
    experiments: {
        css: false,

        // ❓❓❓ 懒加载
        lazyCompilation: process.env.ABC_EXPERIMENTS ?  {  // Lazy compilation 依赖 Rspack 在本地启动的开发服务器
            test(module) {
                const name = module.nameForCondition();

                const rules = getLazyLoadRule();

                const shouldLazyCompile = rules.some(rule => name.includes(rule));

                return shouldLazyCompile;
            },
        } : false,

        futureDefaults: true,

        outputModule: true,

        // !增量地进行重构建，加快重构建或 HMR 的速度
        incremental:  { make: true, emitAssets: true },

                // !启用持久化缓存
        cache: process.env.ABC_CACHE ? {
            type: 'persistent',
            buildDependencies: [
                __filename,
                path.join(__dirname, '../jsconfig.json'),
                path.join(__dirname, '../package.json')
            ],
            storage: {
                type: 'filesystem',
                directory: 'node_modules/.cache/rspack',
            },
            snapshot: {
                immutablePaths: [
                    path.join(__dirname, "../patches"),
                    path.join(__dirname, "../external-app"),
                    path.join(__dirname, "../src/medical-imaging-viewer"),
                    path.join(__dirname, "../src/core/message"),
                    path.join(__dirname, "../src/core/page"),
                ],
                managedPaths: [path.join(__dirname, "node_modules")],
                unmanagedPaths: []
            }
        } : { type: 'memory' },
    },

    // these devServer options should be customized in /config/index.js
    devServer: {
        client: {
            overlay: false,
        },
        hot: true,
        compress: false,
        devMiddleware: {
            publicPath: config.devonline.assetsPublicPath,
        },
        static: {
            watch: config.devonline.poll,
            directory: path.join(__dirname, '../'),
        },
        historyApiFallback: {
            index: '/app.html',
            rewrites: [
                {
                    from: /^\/auth-callback.*target=pharmacy.*$/,
                    to: (context) => {
                        // 保留查询参数，并重定向到 /biz-pharmacy/region-auth/
                        return `/biz-pharmacy/region-auth${context.parsedUrl.search}`;
                    },
                },
                {
                    // 当请求路径为 /auth-callback 并且 query 中包含 target=hospital
                    from: /^\/auth-callback.*target=hospital.*$/,
                    to: (context) => {
                        // 保留查询参数，并重定向到 /hospital/region-auth/
                        return `/hospital/region-auth${context.parsedUrl.search}`;
                    },
                },
                {
                    // 当请求路径为 /auth-callback 并且 query 中包含 target=chain
                    from: /^\/auth-callback.*target=chain.*$/,
                    to: (context) => {
                        // 保留查询参数，并重定向到 /chain/region-auth/
                        return `/chain/region-auth${context.parsedUrl.search}`;
                    },
                },
                {
                    // 当请求路径为 /auth-callback 并且 query 中包含 target=chain
                    from: /^\/auth-callback.*target=clinic.*$/,
                    to: (context) => {
                        // 保留查询参数，并重定向到 /chain/region-auth/
                        return `/region-auth${context.parsedUrl.search}`;
                    },
                },
                {
                from: /^\/chain/,
                to: '/chain.html',
            },{
                from: /^\/$/,
                to: '/static/app.html',
            },{
                from: /^\/(auth-callback|air-pharmacy-introduce|medical-development|medical-device-promotion|record-guidelines|examination-equipment-sale-activity\/.+?)$/,
                to: '/home.html',
            }, {
                from: /^\/(external\/.+?)$/,
                to: '/external-app.html',
            }, {
                from: /^\/hospital/,
                to: '/hospital-app.html',
            }, {
                from: /^\/biz-pharmacy/,
                to: '/pharmacy-app.html',
            }],
        },
        port: config.devonline.port,
        // 保证启动多个pc项目时，自动打开对应的端口页面
        open: ['/login/password'],
        setupMiddlewares (mid, devServer) {
            if (!devServer) {
                throw new Error('webpack-dev-server is not defined');
            }

            utils.redirectLogin('/login', devServer);
            utils.redirectLogin('/login/password', devServer);

            devServer.app.use('/__open-in-editor', openInEditor('webstorm'));

            return mid;
        },
        proxy: config.devonline.proxyTable,
    },
    plugins,
});

module.exports = new Promise((resolve, reject) => {
    portfinder.basePort = process.env.PORT || config.devonline.port;
    portfinder.getPort((err, port) => {
        if (err) {
            reject(err);
        } else {
            // publish the new Port, necessary for e2e tests
            process.env.PORT = port;
            // add port to devServer config
            liteConfig.devServer.port = port;

            resolve(liteConfig);
        }
    });
});
