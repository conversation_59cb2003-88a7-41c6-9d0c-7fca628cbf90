import { getEnvNo } from '@/assets/configure/build-env.js';
const base = process.env.DOMAIN;

export function getModuleJson() {
    const envNo = getEnvNo();
    const moduleLoaderSuffix = envNo ? `-${envNo}` : '';
    return [
        {
            'domain': 'mall',
            'dirName': 'b2b-mall',
            'js': `${base}abc-micro-frontend/mall/module-loader${moduleLoaderSuffix}.js`,
            'isLocalMode': false,
        },
        {
            'domain': 'social',
            'dirName': 'social-security-lite',
            'js': `${base}abc-micro-frontend/social/module-loader${moduleLoaderSuffix}.js`,
            'isLocalMode': false,
        },
    ];
}
