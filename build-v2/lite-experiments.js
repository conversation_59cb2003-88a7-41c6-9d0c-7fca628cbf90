// 🔥🔥🔥 试验性特性：将时间分摊到懒加载上去
const getLazyLoadRule = () => {
    const base = [
      // 文件夹
      'src/external/app',
      'src/lis',
      'src/medical-imaging-viewer',
      'src/medical-imaging-viewer-v2',
      'src/print',
      'src/printer',
      'src/regulatory',
      'src/social-security',
      'src/modules',
    ]

    return [
       ...base,
        // 关键字
        'dialog',
        'components/',
        'component/',
        'common/',
        'home/landing-page',
        'popper',
        'public-health',
        'popover',
    ]
};

module.exports = {
    getLazyLoadRule,
};
