diff --git a/node_modules/mini-css-extract-plugin/dist/index.js b/node_modules/mini-css-extract-plugin/dist/index.js
index 648a799..59a2379 100644
--- a/node_modules/mini-css-extract-plugin/dist/index.js
+++ b/node_modules/mini-css-extract-plugin/dist/index.js
@@ -620,7 +620,7 @@ class MiniCssExtractPlugin {
         const renderedModules = Array.from( /** @type {CssModule[]} */
         this.getChunkModules(chunk, chunkGraph)).filter(module =>
         // @ts-ignore
-        module.type === MODULE_TYPE);
+        module.type === MODULE_TYPE && module.content);
         const filenameTemplate = /** @type {string} */
 
         chunk.canBeInitial() ? this.options.filename : this.options.chunkFilename;
