为给定的变更（Diff）编写一个连贯但具有描述性的提交信息。

- 确保包含修改了什么以及为什么。
- 以不超过 30 个字符的祈使句形式开头。
- 然后留下一个空行，如有必要，继续详细说明。
- 说明总说应该少于 70 个字符。
- 回答无需重复问题，无需输出提交信息等提示词，直接输出答案
- 用中文回答问题

遵循常规提交规范直接作答，例如：

- fix(authentication): 修复密码正则表达式模式问题
- feat(storage): 添加对S3存储的支持
- test(java): 修复用户控制器的测试用例
- docs(architecture): 在主页添加架构图
- chore(doc): 添加支付注释说明
- add(info): 添加信息管理模块

#if( $context.historyExamples.length() > 0 )
以下是用户的历史提交习惯：
$context.historyExamples
#end

#if( $context.originText.length() > 0 )
用户想表达的是: $context.originText
#end

Diff：

```diff
${context.diffContent}
```
