'use strict';
const path = require('path');
const fs = require('fs-extra');
const ejs = require('ejs');
const index = require('./codegen');
const got = require('got');
function format(date, fmt) {
    const o = {
        'M+': date.getMonth() + 1, //月份
        'd+': date.getDate(), //日
        'h+': date.getHours(), //小时
        'm+': date.getMinutes(), //分
        's+': date.getSeconds(), //秒
        'q+': Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds(), //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    for (const k in o)
        if (new RegExp('(' + k + ')').test(fmt))
            fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    return fmt;
}

const generateTime = format(new Date(), 'yyyy-MM-dd hh:mm:ss');
// 需要生成的 controller
const blackList = [];
const whiteList = ['mc-component-controller', 'mc-config-controller'];

async function start() {
    try {
        const swaggerSchema = await getSwaggerSchema();
        generate(swaggerSchema);
    } catch (e) {
        console.log('生成失败', e);
    }

    console.log('生成完毕..', generateTime);
}

async function getSwaggerSchema() {
    const res = await got('https://dev.abczs.cn/rpc/mc/api-docs', {
        headers: {
            'abc-rpc': 'we-will-win'
        },
        responseType: 'json'
    })
    return res.body;
}

function generate(schema) {
    // const swagger = fs.readJsonSync(path.resolve('/Users/<USER>/Desktop/api-docs.json'));
    const swaggerData = index.getViewForSwagger({
        swagger: JSON.stringify(schema),
        className: 'AbcAPI',
    });

    function copyTpl(templatePath, destPath, data) {
        ejs.renderFile(templatePath, { ...data, generateTime }, {}, function (err, str) {
            let destDir = path.resolve(process.cwd(), path.dirname(destPath));
            // Console.log("destDir", destDir);
            if (!fs.existsSync(destDir)) {
                console.log('创建文件夹', destDir);
                fs.mkdirSync(destDir);
            }

            fs.writeFileSync(destPath, str);
        });
    }

    // 生成 API
    swaggerData.tags.forEach((tag) => {
        const { className, apiFileName, description, name } = tag;
        // 排除黑名单
        if (blackList.indexOf(name) !== -1) {
            return;
        }
        // 通过白名单过滤
        if (whiteList.indexOf(name) === -1) {
            return;
        }
        const methods = swaggerData.methods.filter((method) => method.className === className);
        copyTpl(
          './tool/swagger2js/templates/api.ejs',
          `./api/${apiFileName}.js`,
          {
              className,
              description,
              methods,
          }
        );
    });

    // 生成 definitions
    // swaggerData.definitions.forEach((definition) => {
    //     copyTpl(
    //       './tools/swagger2ts/templates/definition.ejs',
    //       `./miniprogram/typings/api/${definition.name}.d.ts`,
    //       definition
    //     );
    // });

}

start();

