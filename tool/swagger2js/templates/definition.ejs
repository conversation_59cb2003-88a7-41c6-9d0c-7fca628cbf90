declare namespace  AbcAPI {
    <%if(description){%>
    /**
     * <%=description%>
     */<%}%>
    type <%=name%> = {_%>
    <%tsType.properties.forEach(propertity =>{%>
        <%if (propertity.description){%>//<%-propertity.description%><%}%>
        <%=propertity.name%><%if(propertity.optional){_%>?<%}%>:<%if(propertity.isRef){%><%=propertity.target%><%}%><%if(propertity.isAtomic){%><%- propertity.tsType %><%}%><%if(propertity.isArray){%>Array<<%if(propertity.elementType.target){%><%- propertity.elementType.target %><%} else {%>any<%}%>><%} else if (propertity.isObject && propertity.recordType) {%>Record<string, <%-propertity.recordType%>><%}_%>
    <%})%>
    }
}
