import inquirer from 'inquirer'
import { execaCommand } from 'execa'

inquirer.prompt([{
    type: 'list',
    message: '请选择开发业务线',
    name: 'ABC_LITE_ENTRY',
    choices: [
        { name: '全部', value: 'all' },
        { name: '诊所管家连锁', value: 'chain' },
        { name: '诊所管家单店', value: 'app' },
        { name: '医院', value: 'hospital' },
        { name: '药店', value: 'pharmacy' },
    ],
    default: '全部'
},{
    type: 'confirm',
    message: '是否启用构建实验特性',
    name: 'experiments',
    default: true // 默认为是
},{
    type: 'confirm',
    message: '是否启用持久化缓存',
    name: 'cache',
    default: true // 默认为是
},{
    type: 'confirm',
    message: '是否开发打印',
    name: 'print',
    default: false // 默认为否
},{
    type: 'confirm',
    message: '是否开发文书',
    name: 'emr',
    default: false // 默认为否
},{
    type: 'confirm',
    message: '是否开发微诊所',
    name: 'micro',
    default: false // 默认为否
}]).then(answers => {
    const chains = [
        `ABC_LITE_ENTRY=${answers.ABC_LITE_ENTRY}`,
        `NODE_OPTIONS="--max-old-space-size=6144"`,
        `rspack serve --config build-v2/webpack.lite.config.js`
    ];

    if (answers.experiments) {
        chains.unshift(`ABC_EXPERIMENTS=true`)
    }

    if(answers.cache) {
        chains.unshift(`ABC_CACHE=true`)
    }

    if(answers.print) {
        chains.unshift(`PRINT_DEV=true`)
    }

    if(answers.emr) {
        chains.unshift(`ABC_EMR_EDITOR_SDK_URL=\"http://localhost:12222/loader.js\"`)
    }

    if(answers.micro) {
        chains.unshift(`ABC_MICRO_CLINIC=true`)
    }

    chains.unshift('cross-env')

    const command = chains.join(' ');
    console.log(command)
    execaCommand(command, { shell: true, stdio: 'inherit' })
})
