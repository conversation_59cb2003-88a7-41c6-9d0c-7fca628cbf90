module.exports = (plop) => {
    plop.setGenerator('component', {
        description: '新增一个 composite-component',
        prompts: [
            {
                type: 'input',
                name: 'componentName',
                message: '组合组件名字：',
                default: '',
            },
        ],
        actions(data) {
            let actions = [];

            const {
                componentName,
            } = data;

            if (!componentName) {
                return actions
            }

            const createPath = `../../src/components-composite/${componentName}`

            const path = `/${componentName}`

            Object.assign(data, {
                folderPath: componentName,
            })
            actions.push({
                type: "addMany",
                base: "components-composite",
                stripExtensions: true,
                destination: `${createPath}/`,
                templateFiles: "components-composite/**/**",
                data
            });

            // 显式创建 assets 目录结构
            actions.push({
                type: "add",
                path: `${createPath}/src/assets/images/.gitkeep`,
                template: ""
            });

            return actions;
        },
    });
};
