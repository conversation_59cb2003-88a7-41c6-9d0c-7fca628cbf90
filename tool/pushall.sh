#!/bin/bash
#git checkout only_for_integration &&  (git push || git pull && git push)
#if [ $? != 0 ]; then
#    exit
#fi

git checkout master && (git push || git pull --rebase && git push)
if [ $? != 0 ]; then
    exit
fi

git checkout develop && ( git push || git pull --rebase && git push)
if [ $? != 0 ]; then
    exit
fi

git checkout rc && (git push || git pull --rebase && git push)
if [ $? != 0 ]; then
    exit
fi

git checkout gray && (git push || git pull --rebase && git push)
