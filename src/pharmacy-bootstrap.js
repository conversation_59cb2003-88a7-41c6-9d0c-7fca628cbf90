import 'requestidlecallback-polyfill';

// 样式
import '@abc/ui-pc/lib/ui-pc.min.css';
import 'normalize.css/normalize.css'; // normalize.css 样式格式化
import 'views/statistics/index.scss'; //统计模块
import './styles/statistics.scss';


import { AbcDatePickerBar } from '@abc/ui-pc';
const { DatePickerBarOptions } = AbcDatePickerBar;
import { ModalFunc as AbcModal } from '@abc/ui-pc';
import { ToastFunc as Toast } from '@abc/ui-pc';
import * as feEngineCore from 'MfFeEngine/core';
import * as repository from 'MfFeEngine/repository';

import { pinia } from 'store/pinia.js';
import fetch, {
    cancelPendingRequest, addSignToRequest,
} from 'utils/fetch';
import { exportFileByAxios } from 'utils/excel';
import 'core-js/stable'; // polyfill
import 'regenerator-runtime/runtime'; // polyfill
import Vue from 'vue';
import AbcUI from '@abc/ui-pc';
import VueRouter from 'vue-router';
import Pharmacy from './Pharmacy.vue';
import components from './components/';
import * as directives from './directive'; // 全局vue directives
import * as filters from './filters'; // 全局vue filter
import { create as createRouter } from './router/biz-pharmacy.js';
import store from './store';
import { watchUserClinicChange } from './views/common/login-optimize';

// ABC 微前端
import ExpireAlert from '@/views/edition/expire-alert/index';
import { getModuleConfig } from '@modules/config';
import {
    ABCPlatform, MFEError,
} from 'abc-micro-frontend';
import Service from './service/index';
// 打印模板文件初始化
import { loadAbcPrint } from '@/printer/print-init/index.js';
import abcRegulatory from '@/regulatory';
// 社保注入
import lifecycle from '@/lifecycle';
import { ABCPrintConfigKeyMap } from '@/printer/constants';
import AbcPrinter from '@/printer/index.js';
import abcSocialSecurity from '@/social-security';
import PropertyAPI from 'api/property/index.js';
import AbcEventBus from 'utils/event-bus';
import {
    BaseApp, NavigateHelper,
} from '@/core/index.js';
import {
    isPharmacy,
} from 'views/common/clinic.js';

import AbcSocket from 'views/common/single-socket.js';
// LIS
import abcPcLis from '@/lis';
import { MultiTabLisServer } from '@/lis/MultiTabLisServer.js';
import { TodoService } from '@/service/todo';
import Logger from 'utils/logger';
import i18n from '@/i18n/index.js';
import { AbcMedicalImagingViewerService } from '@/service/abcMedicalImagingViewer';
// 提供给社保的依赖
import * as AbcUIModule from '@abc/ui-pc';
import AbcChargeService from '@/service/charge';
import { pdfLodopPrint } from '@/printer/utils/index';
import ABCPrinterConfig from '@/printer/config.js';
import {
    BizSettingLayout,
    BizSettingContent,
    BizSettingFooter,
    BizSettingSidebar,
    BizFillRemainHeight,
} from '@/components-composite/setting-form-layout/index.js';

import {
    BizSettingForm,
    BizSettingFormGroup,
    BizSettingFormItem,
    BizSettingFormItemTip,
    BizSettingFormItemIndent,
    BizSettingFormHeader,
} from '@/components-composite/setting-form/index.js';
import { AnnouncementDialog } from 'views/layout/announcement/notice-dialog/index.js';

console.info('构建时间：', process.env.buildInfo && process.env.buildInfo.BUILD_TIME);

const { currentClinic } = store.getters;
if (currentClinic && !isPharmacy(currentClinic)) {
    NavigateHelper.navigateToAppIndex(currentClinic);
}

import AbcTextTips from '@/views-pharmacy/components/abc-text-tips/index.vue';
import { MultiTabLisClient } from '@/lis/MultiTabLisClient';
import { bindRelationType } from '@/views-pharmacy/common/constants';
import { initI18n } from '@/i18n/modify-i18n-messages';
import { OrderCloudModuleId } from 'MfOrderCloud/routes';
import BizPatientSelector from '@/views/layout/patient/patient-section';
import BizDataStatisticsCard from '@/components-composite/biz-data-statistics-card';
import AbcFileUploader from '@/components/abc-file-uploader/index.vue';
import {
    BizMixedSelectionFilter, BizValueAddedCard, BizVersionTips,
} from '@/components-composite/index';
import { FunctionalDialog } from 'views/common/functional-dialog';
import SignService from 'utils/sign-service'; // 签名服务
import DecodeService from '@/service/decode';

class PharmacyApp extends BaseApp {
    onInit() {
        const { socket } = AbcSocket.getSocket();

        feEngineCore.init({
            network: fetch,
            socket,
        });

        lifecycle.beforeCreate();

        Vue.use(AbcUI, {
            theme: 'pharmacy',
        });

        Vue.use(abcPcLis);
        Vue.use(abcRegulatory);
        Vue.component('AbcTextTips', AbcTextTips);
        Vue.use(abcSocialSecurity);

        const moduleConfig = getModuleConfig(process.env.BUILD_ENV);
        Vue.use(ABCPlatform, {
            Vue,
            VueRouter,
            routeBase: '/biz-pharmacy/',
            store,
            fetch,
            addSignToRequest,
            exportFileByAxios,
            moduleConfig,
            ExpireAlert,
            Confirm: AbcModal.confirm,
            AbcModal,
            Toast,
            DatePickerBarOptions,
            AbcPrinter,
            ABCPrintConfigKeyMap,
            PropertyAPI,
            AbcUI: AbcUIModule,
            AbcChargeService,
            pdfLodopPrint,
            ABCPrinterConfig,
            BizSetting: {
                BizMixedSelectionFilter,
                BizSettingLayout,
                BizSettingContent,
                BizSettingFooter,
                BizSettingSidebar,
                BizFillRemainHeight,
                BizSettingForm,
                BizSettingFormGroup,
                BizSettingFormItem,
                BizSettingFormItemTip,
                BizSettingFormItemIndent,
                BizSettingFormHeader,
            },
            BizComponent: {
                BizPatientSelector,
                BizDataStatisticsCard,
                AbcFileUploader,
                BizValueAddedCard,
                BizVersionTips,
            },
            AnnouncementDialog,
            FunctionalDialog,
            SignService,
        });
        watchUserClinicChange();

        Vue.use(AbcEventBus);

        // 注册全局 filter
        Object.keys(filters).forEach((key) => {
            Vue.filter(key, filters[key]);
        });

        // 注册全局指令
        Object.keys(directives).forEach((key) => {
            Vue.directive(key, directives[key]);
        });

        // 全局组件注册
        Object.keys(components).forEach((key) => {
            Vue.component(key, components[key]);
        });


        //是否客户端环境判断，注册window.ipcRendererInit方法，在客户端环境完成初始化后，会调用
        window.ipcRendererInit = () => {
            store.commit('SET_ELECTRON', true);
        };
        if (window.electronFlag) {
            window.ipcRendererInit();
        }
    }

    async onBoot() {
        this.store = store;
        await store.dispatch('updateUserActionByCookie');
        // 拉取用户基本信息、社保 Config 信息，空中药房开关，统计-慢病，需要在 router 生成前获取
        await store.dispatch('acFetchUserInfo');
        // 进entry界面由于没有 当前登录的clinic信息，不需要拉取config
        await Promise.all([
            store.dispatch('acFetchCurrentClinicInfo'),
            store.dispatch('acGetClinicJoined'),
            store.dispatch('initGoodsConfig'),
            store.dispatch('fetchAdjustmentList'),
            store.dispatch('fetchChainBasic'),
            store.dispatch('fetchClinicBasic'),
            store.dispatch('fetchCurrentClinicConfig'),
            store.dispatch('fetchClinicBasicConfig'),
            store.dispatch('socialPc/acInitSocialConfig').then(() => abcSocialSecurity.initNational()),
            store.dispatch('regulatoryPc/acInitRegulatoryConfig').then(() => abcRegulatory.initRegulatory()),
            store.dispatch('edition/acFetchEditionConf'),
            store.dispatch('coPharmacyClinic/getCoClinicCountInfo', bindRelationType.PHARMACY),
            store.dispatch('getTraceCodeCollectionOpenConfig'),
        ]);
        try {
            await abcRegulatory.initRegulatoryData();
        } catch (e) {
            console.error('abcRegulatory.initRegulatoryData error ', e);
        }


        let moduleIds = [];
        let roleIds = [];
        const { userInfo } = store.getters;
        const { currentClinic } = store.getters;
        if (userInfo && userInfo.moduleIds) {
            moduleIds = userInfo.moduleIds.split(',');
            roleIds = userInfo.roleIds;
        }

        const noModuleAuthList = ['/buying']; // 验证权限白名单

        this.router = createRouter({
            moduleIds, roleIds, currentClinic, store,
        });
        this.router.beforeEach(async (to, from, next) => {
            cancelPendingRequest();
            try {
                if (store.getters.userInfo && store.getters.clinics) { // 判断是否有 userId clinics
                    if (store.getters.userInfo.moduleIds) {
                        store.dispatch('initRefreshToken');
                        await this.pageHandler(to, from, next);
                    } else {
                        console.log('没有moduleIds', store.getters.userInfo.moduleIds);
                        if (noModuleAuthList.indexOf(to.path) !== -1) { // 在验证权限白名单，直接进入
                            next();
                        } else {
                            NavigateHelper.navigateToLogin();
                        }
                    }
                } else {
                    console.log('没有用户和clinic信息', store.getters.userInfo, store.getters.clinics);
                    NavigateHelper.navigateToLogin();
                }
            } catch (err) {
                console.log('登录报错', err);
                NavigateHelper.navigateToLogin();
            }
        });
        this.router.afterEach(() => {
            $('.app-wrapper > .container-wrapper').scrollTop(0, 0);
        });

        // 初始化 TodoService
        const { socket } = AbcSocket.getSocket();
        const todoService = new TodoService(socket, this.store);
        this.registerService(TodoService.NAME, todoService);

        //设置DecodeService key
        DecodeService.setKey(this.store.getters['theme/curTypeId']);

        // 更改i18n配置
        await initI18n(i18n);

        this.vm = window._vue = new Vue({
            router: this.router,
            store,
            pinia,
            i18n,
            render: (h) => h(Pharmacy),
        });
        this.vm.$mount('#app');
        // window._vue.$abcPlatform.registerGlobalComponents('Schedules', Schedules);
        // 注册后 可以通过 this.$abcPlatform.service.xxx 或者 window.$platform.service.xxx 调用，例如：
        // this.$abcPlatform.service.wallet.payOrder(id, data);
        this.vm.$abcPlatform.registerService(Service);
        this.vm.$abcPlatform.registerErrorHandler((err) => {
            if (err.type === MFEError.ERR_MODULE_NOT_FOUND) {
                Logger.error({
                    scene: 'LOGOUT',
                    err,
                    data: 'ERR_MODULE_NOT_FOUND',
                });
                NavigateHelper.navigateToLogin();
            }
        });
        this.vm.$abcPlatform.boot(this.vm);
    }

    onAfterBoot() {
        super.onAfterBoot();
        const goodsRepoInstance = repository.GoodsRepositoryService.getInstance();

        window.requestIdleCallback(async () => {
            MultiTabLisServer.getInstance().init(); // 加载LIS插件
            MultiTabLisClient.getInstance().init();

            // 初始化 PrintManager
            const {
                chainBasic,
                clinicBasic,
                userInfo,
                isChainSubStore,
                isSingleStore,
            } = store.getters;
            loadAbcPrint({
                isEnableDesktopPrint: chainBasic.isEnableDesktopPrint, printConfig: clinicBasic.printConfig,
            });

            goodsRepoInstance.init();
            AbcMedicalImagingViewerService.getInstance().start();

            // 连锁总店和单店诊所启动OrderCloud
            if (isChainSubStore || isSingleStore) {
                const ids = userInfo?.moduleIds || '';
                const _arr = ids.split(',');
                if (+ids === 0 || _arr.indexOf(OrderCloudModuleId.main) > -1) {
                    const daemon = await import('MfOrderCloud/daemon');
                    daemon.OrderCloudDaemonService.getInstance().start();
                    console.log('OrderCloudDaemonService start');
                }
            }
        }, {
            timeout: 2000,
        });
    }

}

new PharmacyApp(Vue, {
    showTabBar: false,
    enableOrderCloudDaemon: true,
}).boot().catch((error) => {
    console.log('error', error);
    NavigateHelper.navigateToLogin();
});
