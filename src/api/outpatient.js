import fetch from 'utils/fetch';
import Qs from 'qs';
import {
    initKeyId,
    getSimplePostData,
} from 'views/outpatient/utils.js';
import { getOralExamKey } from '@/views-distribute/utils.js';
import DecodeService from '@/service/decode';

/**
 * 门诊相关
 */
const Outpatient = {
    /**
     * 获取科室备案列表
     * <AUTHOR>
     * @date 2021-09-07
     */
    async fetchDepartmentDataList() {
        const res = await fetch({
            url: '/api/v2/shebao/ningbo/management/reg-departments',
            method: 'get',
            params: {
                offset: 0,
                limit: 1000,
            },
        });
        return res.data;
    },
    /**
     * 获取医师备案列表
     * <AUTHOR>
     * @date 2021-09-07
     */
    async fetchDoctorDataList() {
        const res = await fetch({
            url: '/api/v2/shebao/ningbo/management/reg-doctors',
            method: 'get',
            params: {
                offset: 0,
                limit: 1000,
            },
        });
        return res.data;
    },
    /**
     * @desc 自己曾经开出的检查项目和治疗项目，按 诊断结果 相关性
     * <AUTHOR>
     * @date 2018/10/11 10:47:04
     * @params diagnosis 诊断结果
     */
    async fetchExaminations(clinicId, keyword, diagnosis) {
        const res = await fetch({
            url: '/api/v3/goods/query/examinations',
            method: 'get',
            params: {
                clinicId,
                keyword,
                diagnosis,
            },
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },


    /**
     * @desc  重构 门诊 获取门诊单列表
     * <AUTHOR>
     * @date 2019/03/23 14:14:55
     */
    async fetchQuickList(params, disabledCancel = false) {
        const res = await fetch({
            url: '/api/v2/outpatients',
            method: 'get',
            params,
            disabledCancel,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },

    /**
     * @desc 获取门诊详情
     * <AUTHOR>
     * @date 2019/03/21 20:32:23
     * @params id 收费单id
     */
    async fetch(id, {
        disabledCache = false,
        withAllChargeInfo = undefined,
        excludePharmacyTypeFlag = undefined,
    } = {}) {
        const res = await fetch({
            url: `/api/v2/outpatients/${id}/`,
            method: 'get',
            disabledCache,
            params: {
                withAllChargeInfo,
                excludePharmacyTypeFlag,
                e: 1,
            },
        });
        if (res.data) {
            res.data.data = await DecodeService.decodeAESObjectForKey(res.data.data, {
                doctorName: true,
                departmentName: true,
                patient: {
                    mobile: true,
                    name: true,
                    sex: true,
                    idCard: true,
                    birthday: true,
                },
            });
        }
        const { data } = res.data;
        // 拉取完成初始化keyId
        initKeyId(data);

        if (data?.medicalRecord) {
            // dentistryExaminations oralExamination 这两个字段都代表 口腔检查
            // 口腔诊所 || 医院口腔病历 ：dentistryExaminations
            // 其他产品线 ：oralExamination
            if (getOralExamKey(data?.medicalRecord?.type) === 'dentistryExaminations') {
                data.medicalRecord.oralExamination = null;
            } else {
                data.medicalRecord.dentistryExaminations = null;
            }
        }
        // 外治处方acupoints、externalGoodsItems 类型是list，之前空数组存的也是空的list
        // 后端优化存储，空数组存为null，返回可能为null
        if (data?.prescriptionExternalForms) {
            data.prescriptionExternalForms?.forEach((form) => {
                form.prescriptionFormItems?.forEach((item) => {
                    if (!item.acupoints) {
                        item.acupoints = [];
                    }
                    if (!item.externalGoodsItems) {
                        item.externalGoodsItems = [];
                    }
                });
            });
        }
        return { data: data || {} };
    },
    async fetchCrmOutpatientDetails(id) {
        const res = await fetch({
            url: `/api/v2/outpatients/${id}/abstract`,
            method: 'get',
        });
        const { data } = res.data;
        return data;
    },

    /**
     * @desc 获取手机上传二维码
     * <AUTHOR>
     * @date
     * @params outpatientSheetId 门诊outpatientSheetId
     */
    async qrCode(outpatientSheetId, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/${outpatientSheetId}/attachments/upload/qr-code`,
            method: 'post',
            data,
        });
        return res?.data?.data;
    },
    /**
     * @desc 获取手机上传二维码 —— 执行记录
     * <AUTHOR>
     * @date
     * @params  businessType业务类型
     */
    async getQrCode(data) {
        const res = await fetch({
            url: 'api/v2/short-url/qr-code',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 获取手机扫码二维码状态
     * <AUTHOR>
     * @date
     * @params id 门诊id
     */
    async getMedicalRecordUploadQrCodeStatus(outpatientSheetId, params) {
        const res = await fetch({
            url: `/api/v2/outpatients/${outpatientSheetId}/attachments/upload/qr-code/status`,
            params,
        });
        return res?.data?.data;
    },

    /**
     * @desc 创建门诊
     * <AUTHOR>
     * @date 2019/03/28 14:01:42
     */
    async create(data) {
        const res = await fetch({
            url: '/api/v2/outpatients/',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 修改门诊
     * <AUTHOR>
     * @date 2019/03/28 14:01:52
     */
    async update(id, data) {
        const postData = getSimplePostData(data);
        const res = await fetch({
            url: `/api/v2/outpatients/${id}/`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    /**
     * @desc 门诊费用预览算费
     * <AUTHOR>
     * @date 2019/10/11 16:45:38
     * @params data 门诊提交结构
     * @return
     */
    async calcFee(data) {
        const res = await fetch({
            url: '/api/v2/outpatients/calculate',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 门诊费用预览算费 v2
     * <AUTHOR>
     * @params data 门诊提交结构
     * @return
     */
    async calculate(data) {
        const postData = getSimplePostData(data);
        const res = await fetch({
            url: '/api/v2/outpatients/calculate/sheet',
            method: 'post',
            data: postData,
        });
        return res.data;
    },

    /**
     * @desc 获取网诊列表
     * <AUTHOR>
     * @date 2020/02/16 15:24:18
     * @params
     * @return
     */
    async fetchConsultations(params) {
        const res = await fetch({
            url: '/api/v2/outpatients/consultations',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },
    /**
     * @desc 获取网诊详情
     * <AUTHOR>
     * @date 2020/02/16 20:14:15
     */
    async fetchConsultation(id) {
        const res = await fetch({
            url: `/api/v2/outpatients/consultation/${id}`,
        });
        return res.data;
    },
    /**
     * @desc 更新网诊详情
     * <AUTHOR>
     * @date 2020/02/16 20:14:15
     */
    async updateConsultation(id, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/consultation/${id}/outpatient`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 根据类型获取模板文件夹
     * <AUTHOR>
     * @date 2020/03/27 16:18:20
     * @params type String in['prescription', 'medicalrecord']
     */
    async fetchTemplateCatalogue(type, params) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue/list`,
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },
    /**
     * @desc 根据类型创建模板文件夹
     * <AUTHOR>
     * @date 2020/05/20 11:36:16
     * @params type String in['prescription', 'medicalrecord']
     */
    async createCatalogueByType(type, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue`,
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 根据类型删除模板文件夹
     * <AUTHOR>
     * @date 2020/05/20 11:37:06
     * @params type String in['prescription', 'medicalrecord']
     */
    async deleteTemplateCatalogue(type, id) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue/${id}`,
            method: 'delete',
        });
        return res.data;
    },

    /**
     * @desc 根据类型创建 处方模板 || 病历模板
     * <AUTHOR>
     * @date 2020/05/19 15:33:34
     * @params type String in['prescription', 'medicalrecord']
     */
    async createTemplate(type, data) {
        if (!type) {
            console.error('create template detail need type');
            return false;
        }
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}`,
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 根据类型修改 处方模板 || 病历模板
     * <AUTHOR>
     * @date 2020/05/19 15:37:13
     * @params type String in['prescription', 'medicalrecord', 'diagnosis-treatment']
     */
    async updateTemplate(type, id, data) {
        if (!type) {
            console.error('update template detail need type');
            return false;
        }
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/${id}`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 根据id获取(处方||病历)模板详情
     * <AUTHOR>
     * @date 2020/03/30 11:24:34
     * @params type String in['prescription', 'medicalrecord']
     */
    async fetchTemplateDetail(type, id) {
        if (!type) {
            console.error('fetch template detail need type');
            return false;
        }
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/${id}`,
        });
        return res.data;
    },

    /**
     * @desc 应用病历模板
     * <AUTHOR>
     * @date 2020/03/30 15:03:53
     */
    async applyTemplate(type, id) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/${id}/apply`,
            method: 'put',
        });
        return res.data;
    },

    async rename(type, id, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue/${id}/rename`,
            method: 'put',
            data,
        });
        return res.data && res.data.data;
    },

    /**
     * @desc 拉取目录树
     * <AUTHOR> Yang
     * @date 2020-06-23 08:41:14
     */
    async fetchTemplatesFolderTree(type, category = 2) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue/folder-tree`,
            params: {
                category,
            },
        });
        return res?.data || {};
    },

    /**
     * @desc 搜索目录
     * <AUTHOR>
     * @date 2020/03/31 15:06:28
     */
    async searchCatalogue(type, params) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue/search`,
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },

    /**
     * @desc 目录重新排序
     * <AUTHOR>
     * @date 2020/03/31 16:45:33
     */
    async sortCatalogue(type, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue/reorder`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 复制病历模板
     * <AUTHOR>
     * @date 2020/04/01 15:47:04
     */
    async copyTemplate(type, id) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue/${id}/copy`,
            method: 'put',
        });
        return res.data;
    },

    /**
     * @desc 移动模板到指定文件夹
     * <AUTHOR>
     * @date 2020/05/21 08:47:17
     */
    async moveTemplate(type, id, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/templates/${type}/catalogue/${id}/move`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 创建门诊单草稿
     * <AUTHOR> Yang
     * @date 2020-06-05 19:58:37
     * @params type[number] 1：儿保；0：普通门诊）
     */
    async createOutpatientDraft(data) {
        const res = await fetch({
            url: '/api/v2/outpatients/draft',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 删除门诊草稿单
     * <AUTHOR> Yang
     * @date 2020-06-12 10:19:13
     * @params id[string] 门诊单id
     * @params type[number] 1：儿保；0：普通门诊）
     */
    async deleteOutpatientDraft(id, type) {
        const res = await fetch({
            url: `/api/v2/outpatients/draft/${id}`,
            method: 'delete',
            params: {
                type,
            },
        });
        return res.data;
    },

    async getHealthReport(outpatientId, patientId, ageYear, ageMonth, ageDay) {
        const MOCK = false;
        if (MOCK) {
            return {
                patientId: '',
                outpatientId: '',
                healthReportRecord: {
                    conclusion: '',
                    advice: '',
                },
                bodyGrowthRecordList: [
                    {
                        height: '',
                        weight: '',
                        sitHeight: '',
                        headSize: '',
                        upperBodyLength: '',
                        lowerBodyLength: '',
                        bmi: '',
                        age: '',
                    },
                ],
                physicalExamination: '',
                suggestion: {
                    inoculation: '',
                    diet: '',
                    sleep: '',
                    mouth: '',
                },
                evaluationResult: [
                    {
                        indicators: [
                            {
                                name: '',
                                value: '',
                                grade: '',
                                remark: '',
                            },
                        ],
                        formId: '',
                        formDataId: '',
                        values: '',
                    },
                ],
            };
        }

        const res = await fetch.get(
            `/api/v2/outpatients/child-care/${outpatientId}/health-report/view?ageYear=${ageYear}&ageMonth=${ageMonth}&ageDay=${ageDay}&patientId=${patientId}`,
        );
        return res.data;
    },

    async updateHealthReport(data) {
        const res = await fetch.put('/api/v2/outpatients/child-care/health-report/record', data);
        return res.data;
    },
    /**
     * @desc 儿保健康报告上传手机二维码
     * <AUTHOR>
     * @date 2022/08/08 15:29:36
     * @param {String} outpatientSheetId 门诊id
     * @return
     */
    async getHealthReportQrCode(outpatientSheetId) {
        const res = await fetch({
            url: `/api/v2/outpatients/child-care/${outpatientSheetId}/health-report/attachments/upload/qr-code`,
            method: 'post',
        });
        return res.data?.data ?? {};
    },

    /**
     * 获取就诊时间列表
     * <AUTHOR>
     * @date 2020-07-01
     * @param {String} patientId 患者id
     * @returns {Promise}
     */
    async fetchHistoryDiagnosedDate(patientId) {
        const res = await fetch({
            url: `/api/v2/outpatients/history/diagnosed-date/${patientId}`,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 根据 medical-record 获取推荐 项目
     * <AUTHOR> Yang
     * @date 2020-10-29 11:22:14
     * @params data:
     * {
     *     jsonType
     *     sex：男||女
     *     age
     *     chiefComplaint：主诉
     *     diagnosis
     *     goodsList：[{goodsId, goodsName}]
     *     offset
     *     limit
     * }
     * @params offset
     * @params limit
     * */
    async fetchGoodsRecommendByMR(data) {
        const res = await fetch({
            url: '/api/v3/goods/recommend',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 填写问诊单
     * <AUTHOR> Yang
     * @date 2021-01-22 08:46:32
     */
    async createOutpatientQuestionForm(outpatientSheetId, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/${outpatientSheetId}/question/sheet`,
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 更新问诊单
     * <AUTHOR> Yang
     * @date 2021-01-22 08:46:32
     */
    async updateOutpatientQuestionForm(formId, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/question/sheet/${formId}`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 删除问诊单
     * <AUTHOR> Yang
     * @date 2021-01-22 08:46:32
     */
    async deleteOutpatientQuestionForm(formId, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/question/sheet/${formId}`,
            method: 'delete',
            data,
        });
        return res.data;
    },

    /**
     * @desc 获取问诊单
     * <AUTHOR> Yang
     * @date 2021-01-22 08:46:32
     */
    async fetchOutpatientQuestionForm(formId) {
        const res = await fetch({
            url: `/api/v2/outpatients/question/sheet/${formId}`,
        });
        return res.data;
    },

    /**
     * @desc 获取问诊单
     * <AUTHOR> Yang
     * @date 2021-01-22 08:46:32
     */
    async pushOutpatientQuestionForm(outpatientSheetId, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/${outpatientSheetId}/question/sheet/push`,
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 门诊处方外部审核
     * <AUTHOR>
     * @date 2021-05-21 18:02:53
     * @params
     * @return
     */
    async outpatientExternalVerify(data) {
        const res = await fetch({
            url: '/api/v2/outpatients/external/verify',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 上传病例图片接口
     * <AUTHOR>
     * @date 2021-10-18 08:46:32
     */
    async addAttachments(outpatientSheetId, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/${outpatientSheetId}/attachments`,
            method: 'post',
            data,
        });
        return res.data;
    },

    async addAttachmentsBatch(outpatientSheetId, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/${outpatientSheetId}/attachments/batch`,
            method: 'post',
            data,
        });
        return res.data || {};
    },

    /**
     * @desc 获取患者今日临时附件
     * <AUTHOR>
     */
    async fetchTempAttachments(patientId) {
        const res = await fetch({
            url: `/api/v2/outpatients/patients/${patientId}/medical-record/attachments/temp`,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 删除病例图片接口
     * <AUTHOR>
     * @date 2021-10-18 08:46:32
     */
    async deleteAttachments(outpatientSheetId, id) {
        const res = await fetch({
            url: `/api/v2/outpatients/${outpatientSheetId}/attachments/${id}`,
            method: 'delete',
        });
        return res.data;
    },

    async getPatientVisitStatus(params) {
        const res = await fetch({
            url: `/api/v2/outpatients/patients/${params.patientId}/revisit-status`,
            method: 'get',
            params,
        });
        return res.data;
    },
    /**
     * @desc 开启上传收费告知书任务
     * <AUTHOR>
     * @date 2021-11-30 08:46:32
     */
    async startChargeNoticeUploadTask(patientOrderId) {
        const res = await fetch({
            url: `/api/v2/charges/patientorders/notification/${patientOrderId}/start-upload`,
            method: 'put',
        });
        return res?.data;
    },
    /**
     * @desc 收费告知书场景获取二维码状态
     * <AUTHOR>
     * @date 2021-11-30 08:46:32
     */
    async getCommonUploadQrStatus(params) {
        const res = await fetch({
            url: '/api/v2/short-url/qr-code/status',
            method: 'get',
            params,
        });
        return res?.data?.data;
    },
    /**
     * @desc 上传收费告知书
     * <AUTHOR>
     * @date 2021-11-30 08:46:32
     */
    async uploadChargeNotification({ patientOrderId }, data) {
        const res = await fetch({
            url: `/api/v2/charges/patientorders/notification/${patientOrderId}/upload`,
            method: 'put',
            data,
        });
        return res?.data?.data;
    },

    // 获取门诊设置下所有关联项目

    async getInfusionRelatedConfig(params) {
        const res = await fetch({
            url: '/api/v3/goods/association/usages/detail',
            method: 'get',
            params,
            // disallowDuplicate: true,
            disabledCancel: true,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });
        return res && res.data;
    },

    /**
     * @desc 查询就诊单收费告知书详情
     * <AUTHOR>
     * @date 2022-01-19 08:46:32
     */
    async patientOrderNotification(patientOrderId) {
        const res = await fetch.get(`/api/v2/charges/patientorders/notification/${patientOrderId}/query-patient-order-notification`);
        return res.data;
    },

    /**
     * @desc 删除收费告知书
     * <AUTHOR>
     * @date 2022-01-19 08:46:32
     */
    async deleteChargeNotice(patientOrderId, notificationId) {
        const res = await fetch({
            url: `/api/v2/charges/patientorders/notification/${patientOrderId}/${notificationId}`,
            method: 'delete',
        });
        return res.data;
    },

    /**
     * @desc 根据 @patientOrderId 查询推送支付需要的订单详情
     */
    async getPushPaymentOrderDetail(patientOrderId, type) {
        const res = await fetch({
            url: `/api/v2/charges/patientorder/${patientOrderId}/push-scan-code`,
            method: 'get',
            params: {
                type,
            },
        });
        return res.data;
    },
    /**
     * @desc 根据 @sceneKey 查询二维码状态 @sceneKey required
     */
    async getPushPaymentQRCodeStatus(sceneKey) {
        const res = await fetch({
            url: '/api/v2/mc/qr-code/scene/status',
            method: 'get',
            params: {
                sceneKey,
            },
        });
        return res.data;
    },
    /**
     * @desc 根据 @chargeSheetId 推送订单给患者
     */
    async sendOrderToPatient(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/push-order`,
            method: 'put',
        });
        return res.data;
    },

    /**
     * @desc 费用预览保存
     * <AUTHOR>
     * @date 2022-03-23 17:12:08
     */
    async saveAdjustmentPrice(outpatientSheetId, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/${outpatientSheetId}/adjustment-price`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 业务锁单
     * <AUTHOR>
     * @date 2022/08/01 15:11:54
     * @param {String} key 业务id
     * @param {String} businessKey 业务类型
     */
    async lockOutpatient(key, businessKey, businessScene = 0) {
        const res = await fetch({
            url: `/api/v2/patientorders/${key}/lock`,
            method: 'post',
            params: {
                businessKey,
                businessScene,
            },
        });
        return res.data;
    },

    /**
     * @desc 业务锁单续期
     * <AUTHOR>
     * @date 2022/08/01 15:19:22
     * @param {String} key 业务id
     * @param {Object} params {businessKey, identity}业务类型,锁单标识
     */
    async lockOutpatientRenew(key, params) {
        const {
            businessKey, value,
        } = params;
        const res = await fetch({
            url: `/api/v2/patientorders/${key}/lock/renew`,
            method: 'put',
            params: { businessKey },
            data: value,
        });
        return res.data;
    },

    /**
     * @desc 业务解锁
     * <AUTHOR>
     * @date 2022/08/01 15:25:05
     * @param {String} key 业务id
     * @param {String} businessKey 业务类型
     * @param {Object} params {businessKey, identity}业务类型,锁单标识
     */
    async unlockOutpatient(key, params) {
        const {
            businessKey, value,
        } = params;
        const res = await fetch({
            url: `/api/v2/patientorders/${key}/unlock`,
            method: 'post',
            params: { businessKey },
            data: value,
        });
        return res.data;
    },

    /**
     * @desc 获取业务锁
     * <AUTHOR>
     * @date 2022/08/02 10:12:24
     * @param{String} key 业务id
     * @param {Object} params {businessKey}业务类型
     */
    async getOutpatientLock(key, params) {
        const res = await fetch({
            url: `/api/v2/patientorders/${key}/lock`,
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * @desc 获取快递信息
     * <AUTHOR>
     * @date 2023/03/29 10:22:31
     * @param {string} formId
     * @return {array} list
     */
    async getDeliveryTraceList(formId) {
        const res = await fetch({
            url: `/api/v2/outpatients/prescription/form/${formId}/logistics-trace`,
            method: 'get',
        });
        return res?.data || {};
    },
    async getExaminationApplySheets(patientOrderId) {
        const res = await fetch({
            url: `/api/v2/examinations/apply-sheet/list-by-patient-order-id/${patientOrderId}`,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 修改门诊单状态
     * <AUTHOR>
     * @date 2023/05/31 17:38:39
     * @param {string} id 门诊单id
     * @param {object}
     * @return {object}
     */
    async updateStatus(id, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/${id}/status`,
            method: 'put',
            data,
        });
        return res?.data?.data || {} ;
    },

    /**
     * @desc 获取门诊单简单摘要列表
     * <AUTHOR>
     * @date 2023/07/25 09:55:32
     * @param {Object}
     * @return {Object}
     */
    async getOutpatientList(params) {
        const res = await fetch({
            url: '/api/v2/outpatients/abstract/simple/list',
            method: 'get',
            params,
        });
        return res?.data?.data || {};
    },

    /**
     * @desc 单独提交病历
     * <AUTHOR>
     * @date 2023/07/26 14:38:03
     */
    async updateMedicalRecord(id, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/medical-record/${id}`,
            method: 'put',
            data,
        });
        return res?.data || {};
    },

    /**
     * @desc 单独提交病历
     * <AUTHOR>
     * @date 2023/07/26 14:38:03
     */
    async getExaminationsByPatientOrderId(patientOrderId) {
        const res = await fetch({
            url: `/api/v2/outpatients/patientorders/${patientOrderId}/examinations`,
            method: 'get',
        });
        return res.data;
    },

    /**
     * 根据患者 id 获取上次就诊单
     * @param patientId {string} 患者 id
     * @return {Promise<*>}
     */
    async fetchRecentPatientOrderByPatientId(patientId) {
        const res = await fetch({
            url: '/api/v2/patientorders/recent/by-patient-id',
            method: 'get',
            params: {
                patientId,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams);
            },
        });
        return res.data || {};
    },

    async putOutpatientPrintCount(id, data) {
        const res = await fetch({
            url: `/api/v2/outpatients/${id}/print`,
            method: 'put',
            data,
        });
        return res.data || {};
    },
    async getOutpatientCount(id, params) {
        const res = await fetch({
            url: '/api/v2/outpatients/count',
            method: 'get',
            params,
        });
        return res.data || {};
    },

    /**
     * @desc 查询是否需要处理社保ICPC
     * @return {Boolean}
     */
    async getUpgradeAbleICPC(patientOrderId) {
        const res = await fetch({
            url: '/api/v2/shebao/national/settle/upload/icpc/upgrade-able',
            method: 'get',
            params: {
                patientOrderId,
            },
        });
        return res.data || {};
    },

    async fetchTongueWeAppQrCode(outpatientSheetId, data) {
        const res = await fetch.post(`/api/v2/outpatients/${outpatientSheetId}/attachments/upload/weapp/qr-code`, data);
        return res.data || {};
    },

    async getAIDiagnosisConfig(outpatientSheetId, isDraft = 0) {
        const res = await fetch({
            url: '/api/v2/outpatients/ai-diagnosis-config',
            method: 'get',
            params: {
                isDraft,
                outpatientSheetId,
            },
        });
        return res.data || {};
    },

    async updateAIDiagnosisConfig(outpatientSheetId, data, isDraft = 0) {
        const res = await fetch({
            url: `/api/v2/outpatients/ai-diagnosis-config/${outpatientSheetId}`,
            method: 'put',
            params: {
                isDraft,
            },
            data,
        });
        return res.data || {};
    },

    async fetchPatientRevisitUse(patientId, params) {
        const res = await fetch({
            url: `/api/v2/outpatients/abstract/${patientId}/revisit-use`,
            params,
        });
        return res && res.data;
    },

    /**
     * @desc 获取最近一次就诊记录
     */
    async fetchPatientRecent(patientId) {
        const res = await fetch({
            url: `/api/v2/outpatients/history/${patientId}/recent`,
        });
        return res && res.data;
    },
};

export default Outpatient;
