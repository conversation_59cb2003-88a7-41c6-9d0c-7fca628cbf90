import fetch from 'utils/fetch';
import Qs from 'qs';
import {
    exportFileByAxios,
} from 'utils/excel.js';
import { fetchDecode } from 'api/goods/help';

const purchaseAPI = {
    async fetchPurchaseTodoCount() {
        const res = await fetch({
            url: '/api/v3/goods/mall/orders/count',
            method: 'get',
        });
        return res && res.data;
    },

    /**
     * @desc 获取采购计划列表
     * <AUTHOR>
     * @date 2020-11-16 17:10:59
     */
    async fetchShoppPurchasePlan(params, purchaseId) {
        const res = await fetch({
            url: `/api/v3/goods/mall/shopping-plan/${purchaseId}`,
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res && res.data;
    },
    /**
     * @desc 更新采购计划
     * <AUTHOR>
     * @date 2020-11-16 17:10:59
     */
    async updatePurchasePlan(data, purchaseId) {
        const res = await fetch({
            url: `/api/v3/goods/mall/shopping-plan/${purchaseId}`,
            method: 'put',
            data,
        });
        return res && res.data;
    },
    /**
     * @desc 删除采购计划的一些项
     * <AUTHOR>
     * @date 2020-11-16 17:10:59
     * items: [{ id: '', goodsId: ''}]
     */
    async deletePurchasePlan(data, purchaseId) {
        const res = await fetch({
            url: `/api/v3/goods/mall/shopping-plan/${purchaseId}`,
            method: 'delete',
            data,
        });
        return res && res.data;
    },
    /**
     * @desc 采购库存告警推荐列表
     * <AUTHOR>
     * @date 2020-11-16 17:34:31
     */
    async fetchStockWarn(params) {
        const res = await fetch({
            url: '/api/v3/goods/mall/warning/stocks',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'repeat' });
            },
        });
        return res && res.data;
    },

    /**
     * @desc 采购单列表
     * <AUTHOR>
     * @date 2020-11-16 17:43:46
     */
    async fetchPurchaseOrders(params) {
        const res = await fetch({
            url: '/api/v3/goods/mall/orders',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res && res.data;
    },
    /**
     * @desc 获取采购单详情
     * <AUTHOR>
     * @date 2020-11-16 17:44:47
     */
    async fetchOrderDetail(orderId) {
        const res = await fetch({
            url: `/api/v3/goods/mall/order/${orderId}`,
            method: 'get',
        });
        return res && res.data;
    },

    /** ********* 商城相关接口 *************/
    /**
     * @desc 获取商城推荐的商品
     * <AUTHOR>
     */
    async fetchMallRecommendProducts(fetchParams) {
        const {
            offset,
            limit,
            goodsId,
            skuGoodsId,
            spuGoodsId,
            keyword,
            barCode,
            manufacturer,
            specification,
            provenance,
            medicineNmpn,
            medicineCadn,
            specificationFilter,
            vendorFilter,
            manufacturerFilter,
            purchasePlanId,
        } = fetchParams;
        const params = {};
        if (offset !== '' && offset !== undefined) {
            params.offset = offset;
        }
        if (limit) {
            params.limit = limit;
        }
        if (goodsId) {
            params.goodsId = goodsId;
        }
        if (skuGoodsId) {
            params.skuGoodsId = skuGoodsId;
        }
        if (spuGoodsId) {
            params.spuGoodsId = spuGoodsId;
        }
        if (keyword) {
            params.keyword = keyword;
        }
        if (barCode) {
            params.barCode = barCode;
        }
        if (manufacturer) {
            params.manufacturer = manufacturer;
        }
        if (specification) {
            params.specification = specification;
        }
        if (provenance) {
            params.provenance = provenance;
        }
        if (medicineNmpn) {
            params.medicineNmpn = medicineNmpn;
        }
        if (medicineCadn) {
            params.medicineCadn = medicineCadn;
        }
        if (specificationFilter) {
            params.specificationFilter = specificationFilter;
        }
        if (vendorFilter) {
            params.vendorFilter = vendorFilter;
        }
        if (manufacturerFilter) {
            params.manufacturerFilter = manufacturerFilter;
        }
        params.purchasePlanId = purchasePlanId;
        const res = await fetch({
            url: '/api/v3/goods/mall/shopping-plan/recommend',
            method: 'post',
            data: params,
        });
        return res && res.data;
    },
    /**
     * @desc 创建商城采购单
     * <AUTHOR>
     * @date 2020-11-20 11:45:53
     * @params
     * @return
     */
    async createMallPurchaseOrder(data) {
        const res = await fetch({
            url: '/api/v3/goods/mall/shopping-plan/prepare-purchase',
            method: 'post',
            data,
        });
        return res && res.data;
    },

    /**
     * @desc 重新下单
     */
    async reOrder(hisOrderId) {
        const res = await fetch({
            url: `/api/v3/goods/mall/order/${hisOrderId}/re-buy`,
            method: 'put',
        });
        return res && res.data;
    },
    /**
     * @desc his 和 商城采购数量的映射关系
     */
    async updateSkuGoodsRelation(goodsId, skuGoodsId, data) {
        const res = await fetch({
            url: `/api/v3/goods/mall/relation/${goodsId}/${skuGoodsId}`,
            method: 'put',
            data,
        });
        return res && res.data;
    },
    /**
     * @desc 查询 skuGoods 和 His 映射关系
     * <AUTHOR>
     * @date 2020-12-29 23:33:21
     * @params
     * @return
     */
    async fetchSkuGoodsRelation(goodsId, skuGoodsId) {
        const res = await fetch({
            url: `/api/v3/goods/mall/relation/${goodsId}/${skuGoodsId}`,
            method: 'get',
        });
        return res && res.data;
    },

    /**
     * @desc 获取采购单列表
     */
    async fetchOrdersList(params) {
        const res = await fetch({
            url: '/api/v3/goods/mall/orders',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res && res.data;
    },

    /**
     * @desc 拉取所有未付款的金额
     */
    async fetchNeedPayPurchase() {
        const res = await fetch({
            url: '/api/v3/goods/mall/orders/unpay',
            method: 'get',
        });
        return res && res.data;
    },
    /**
     * offset
     * limit
     * begDate
     * endDate
     * clinicId
     *
     * @param   {[type]}  query  [query description]
     *
     * @return  {[type]}
     */
    async orderList({
        offset, limit, begDate, endDate, clinicId, withGoodsId, status, dateField,
    }) {
        const ret = await fetch.get('/api/goods/purchase/orders', {
            params: {
                offset,
                limit,
                begDate,
                endDate,
                clinicId,
                withGoodsId,
                status,
                dateField,
            },
        });
        return fetchDecode(ret);
    },

    /**
     * 创建采购单
     * @param   {String}  comment  备注
     * @param   {Array}  list  药品列表
     * @param   {String}  planId  采购计划id
     * [{
     *      goodsId,
     *      pieceCount,
     *      packageCount,
     *      lastSupplier,
     *      lastPackageCostPrice,
     *      minPackageCostPrice,
     * }]
     *
     */
    async createOrder({
        comment, list, planId, purchaseType,
    }) {
        const ret = await fetch.post('/api/v3/goods/purchase/orders', {
            comment,
            list,
            planId,
            purchaseType,
        });
        return fetchDecode(ret);
    },

    /**
     * 获取订单
     *
     * @param   {[type]}  id  [id description]
     *
     * @return  {[type]}
     */
    async getById(id) {
        let ret = await fetch.get(`/api/v3/goods/purchase/orders/${id}`);
        ret = fetchDecode(ret);
        ret.list.forEach((item) => {
            item.packageCount = +item.packageCount;
            item.pieceCount = +item.pieceCount;
        });
        return ret;
    },

    /**
     * 随货单导出
     *
     * @param   string  id
     *
     * @return  {[type]}
     */
    async exportById(id) {
        return exportFileByAxios({
            url: `/api/v3/goods/mall/order/${id}/export`,
        });
    },

    // 采购单导出
    async exportAccompanyingBillById(id) {
        return exportFileByAxios({
            url: `/api/v3/goods/mall/order/${id}/export-accompanying-bill`,
        });
    },

    /**
     * 列表导出excel
     */
    async exportList(params) {
        const {
            limit,
            offset,
            clinicId,
            confirmBeginDate,
            confirmEndDate,
            createBeginDate,
            createEndDate,
            purchaseType,
            goodsId,
            queryType,
            status,
        } = params;

        return exportFileByAxios({
            url: '/api/v3/goods/mall/orders/export',
            params: {
                limit,
                offset,
                clinicId,
                confirmBeginDate,
                confirmEndDate,
                createBeginDate,
                createEndDate,
                purchaseType,
                goodsId,
                queryType,
                status,
            },
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });
    },

    /**
     * 更新订单
     * @param   {String}  list  备注
     * @param   {Array<Objct>}  list  采购列表
     * [{
     *      id,
     *      goodsId,
     *      pieceCount,
     *      packageCount,
     *      lastSupplier,
     *      lastPackageCostPrice,
     *      minPackageCostPrice,
     * }]
     * @param lastModifiedDate 最近更新时间
     *
     */
    async updateOrder(id, {
        list, comment, lastModifiedDate, purchaseType,
    }) {
        const ret = await fetch.put(`/api/v3/goods/purchase/orders/${id}`, {
            list,
            comment,
            lastModifiedDate,
            purchaseType,
        });
        return fetchDecode(ret);
    },

    /**
     * 确认订单
     * @param   id  备注
     * @param   {String}  list  备注
     * @param   {Array<Object>}  list  采购列表
     * [{
     *      id,
     *      goodsId,
     *      pieceCount,
     *      packageCount,
     *      lastSupplier,
     *      lastPackageCostPrice,
     *      minPackageCostPrice,
     * }]
     * @param comment 备注
     * @param lastModifiedDate 最近更新时间
     * @param planId 确认采购单时，同时将采购单加入到采购计划
     *
     */
    async confirmOrder(id, {
        list, comment, lastModifiedDate, planId,
    }) {
        const ret = await fetch.put(`/api/v3/goods/purchase/orders/${id}/confirm`, {
            list,
            comment,
            lastModifiedDate,
            planId,
        });
        return fetchDecode(ret);
    },

    /**
     * @desc 拒绝采购单，审核不通过
     * <AUTHOR>
     * @date 2019/09/11 21:24:54
     * @params
     * @return
     */
    async refuseOrder(id, {
        comment, lastModifiedDate,
    }) {
        const ret = await fetch.put(`/api/v3/goods/purchase/orders/${id}/reject`, {
            comment,
            lastModifiedDate,
        });
        return fetchDecode(ret);
    },

    /**
     * @desc 获取计划中的 采购计划数量
     * <AUTHOR>
     * @date 2019/07/25 18:05:44
     */
    async fetchPurchasePlanCount(isComplete) {
        const { data } = await fetch({
            url: '/api/v3/goods/purchase/plans/count',
            params: {
                isComplete,
            },
        });
        return (data && data.data.count) || '';
    },

    /**
     * @desc 获取采购计划列表
     * <AUTHOR>
     * @date 2019/07/24 17:29:11
     */
    async fetchPurchasePlan(params) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/plans',
            params,
        });
        return res.data;
    },
    /**
     * @desc 导出采购列表
     * <AUTHOR>
     * @date 2019/12/19
     */
    exportPurchasePlan(params) {
        const {
            isComplete, begDate, endDate,
        } = params;
        const url = '/api/v3/goods/purchase/plans/export';
        return exportFileByAxios({
            url,
            params: {
                isComplete,
                begDate,
                endDate,
            },
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });
    },

    async fetchPurchasePlanSimple() {
        const res = await fetch({
            url: '/api/v3/goods/purchase/plans/simple',
        });
        return res.data.data.rows;
    },

    /**
     * @desc 获取采购计划详情
     * <AUTHOR>
     * @date 2019/07/25 11:27:57
     */
    async fetchPurchasePlanDetail(id) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/plans/${id}`,
        });
        return res.data;
    },

    /**
     * @desc 获取采购计划名字
     * <AUTHOR>
     * @date 2019/07/25 14:17:57
     */
    async fetchPurchasePlanName() {
        const res = await fetch({
            url: '/api/v3/goods/purchase/plans/name',
        });
        return res.data;
    },

    /**
     * @desc 新增采购计划接口
     * <AUTHOR>
     * @date 2019/07/25 10:43:33
     */
    async createPurchasePlan(data) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/plans',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 修改采购计划的名字
     * <AUTHOR>
     * @date 2019/07/25 17:29:23
     */
    async updatePurchasePlanName(id, name) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/plans/${id}/rename`,
            method: 'put',
            data: {
                name,
            },
        });
        return res.data;
    },

    /**
     * @desc 完成采购计划
     * <AUTHOR>
     * @date 2019/07/25 11:55:48
     */
    async completePlan(id) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/plans/${id}/complete`,
            method: 'put',
        });
        return res.data;
    },

    /**
     * @desc 将采购单加入到采购计划
     * <AUTHOR>
     * @date 2019/07/25 20:21:37
     * @param planId 采购计划id
     * @param orderId 采购单id
     */
    async purchaseIntoPlan(planId, orderId) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/plans/${planId}/add`,
            method: 'put',
            data: {
                orderId,
            },
        });
        return res.data;
    },

    /**
     * @desc 在采购计划未完成前，总部可以修改药品数量
     * <AUTHOR>
     * @date 2019/07/29 18:29:32
     */
    async updatePurchaseItem(orderId, itemId, data) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/orders/${orderId}/${itemId}`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 导出汇总清单
     * <AUTHOR>
     * @date 2019/07/25 18:26:00
     */
    async exportPurchaseTotal(id) {
        return exportFileByAxios({
            url: `/api/v3/goods/purchase/plans/${id}/export`,
        });
    },

    /**
     * @desc  导出门店清单zip包
     * <AUTHOR>
     * @date 2019/07/25 18:27:16
     */
    async exportPurchaseZip(id) {
        return exportFileByAxios({
            url: `/api/v3/goods/purchase/plans/${id}/export/zip`,
        });
    },
    /**
     * @desc 采购单撤回  id: orderId
     * <AUTHOR>
     * @date 2019/12/12
     */
    async revokeOrder(id) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/orders/${id}/revoke`,
            method: 'put',
        });
        return res.data;
    },

    /**
     * @desc 获取药品的采购建议量
     * <AUTHOR>
     * @date 2020-08-17 16:51:10
     * @params data: goodsIds数组
     * @return
     */
    async fetchGoodsRecommend(data, clinicId) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/orders/recommend?clinicId=${clinicId}`,
            method: 'post',
            data,
        });
        return res.data;
    },

    /** ********************* 多个采购单相关 ***********************************/
    async fetchPurchaseOrderList() {
        const res = await fetch({
            url: '/api/v3/goods/mall/shopping-plan/list',
        });
        return res && res.data;
    },
    async deletePurchaseOrderById(purchaseId) {
        await fetch({
            url: `/api/v3/goods/mall/shopping-plan/list/${purchaseId}`,
            method: 'delete',
        });
    },
    /**
     * @desc 创建新的空采购单
     */
    async createPurchaseOrder() {
        const res = await fetch({
            url: '/api/v3/goods/mall/shopping-plan/list',
            method: 'post',
        });
        return res && res.data;
    },
};
export default purchaseAPI;
