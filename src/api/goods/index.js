import Qs from 'qs';
import { exportFileByAxios } from 'utils/excel.js';
import fetch from 'utils/fetch';
import { fetchDecode } from './help';
import { GoodsTypeIdEnum } from 'views/inventory/constant';
import DecodeService from '@/service/decode';

export default {
    /**
     * @desc
     * <AUTHOR>
     * @date 2019/04/24 10:46:01
     * @params  withStock 返回药品的库存信息
     *          onlyStock 对入过库的药品进行筛选 （onlyStock 是 withStock的强限制条件）
     *          needFilterDisable 是否需要过滤停用的药品
     *          inorderConfig  0 禁止采购入库 默认为空
     * @return
     */
    async search({
        key, withStock, onlyStock, clinicId, type, subType, cMSpec, needFilterDisable, inorderConfig,pharmacyNo,customTypeId,typeId,
    }) {
        const params = {
            key,
            withStock: withStock ? 1 : '',
            onlyStock: onlyStock ? 1 : '',
            clinicId,
            type,
            subType,
            cMSpec,
            inorderConfig,
            pharmacyNo,
            customTypeId,
            typeId,
        };

        // 不需要过滤禁用药品不传参数
        if (needFilterDisable) {
            params.disable = 0;
        }
        const ret = await fetch.get('/api/v3/goods/stock-goods/search', {
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'comma' });
            },
        });
        return ret && ret.data;
    },

    /**
     * 获取单个药品的详细信息
     *
     * @param   {String}  id           药品id
     * @param   {String}  clinicId     诊所id
     * @param   {Boolean} opts.forPurchase  是否返回与采购相关的信息（最近供货商，最新成本价，最低成本价）
     * @param   {Boolean} opts.withStock    是否返回库存信息（pieceCount, packageCount）
     * @param   {Number} opts.pharmacyNo    药房号
     *
     */
    async goods(id, clinicId, {
        forPurchase,
        withStock,
        withShebaoCode,
        pharmacyNo,
        throwExceptionIfDel,
    } = {}) {
        const ret = await fetch.get(`/api/v3/goods/${id}`, {
            params: {
                clinicId,
                forPurchase: forPurchase ? 1 : '',
                withStock: withStock ? 1 : '',
                withShebaoCode: withShebaoCode ? 1 : '',
                pharmacyNo,
                throwExceptionIfDel,
            },
        });
        return ret && ret.data;
    },
    /**
     * @desc 获取商品的最高进价
     * <AUTHOR>
     * @date 2019/05/10 13:51:34
     */
    async fetchMaxCostPrice(id, params) {
        const res = await fetch({
            url: `/api/v3/goods/${id}/stocks/maxcost`,
            method: 'get',
            params,
        });
        return res.data.data || {};
    },

    async fetchApprovalByBusinessType(businessType = 'goodsReportingLosses') {
        const res = await fetch({
            url: `/api/approval/def/${businessType}/status`,
            method: 'get',
        });
        return res.data.data || {};
    },

    async fetchNoCodeCreateInfo(data) {
        const res = await fetch({
            url: '/api/v2/supervision/national/shanxi-xian/xi-an-have-no-code-create',
            method: 'post',
            data,
        });
        return res.data;
    },
    async fetchNoCodeCreateList(params) {
        const res = await fetch({
            url: '/api/v2/supervision/national/shanxi-xian/xi-an-have-no-code-list-query',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * 获取商品列表
     *
     * @param   {Object<clinicId, goodsId, offset, limit, type, subType, orderBy, orderType>}  query    支持分页、查询以及排序
     *
     * @return  {Object<{rows:Array<medicineItem>,count}>}
     * medicineItem:
     *
     *      {
     *           "goodsId": "478d0d7466fb428da6c6ce0a40044903",
     *           "type": 1,
     *           "subType": 1,
     *           "name": "",
     *           "medicineCadn": "四季抗病毒胶囊",
     *           "manufacturer": "陕西海天制药有限公司",
     *           "medicineDosageNum": "0.3800",
     *           "medicineDosageUnit": "g",
     *           "materialSpec": null,
     *           "pieceNum": "24",
     *           "pieceUnit": "粒",
     *           "packageUnit": "盒",
     *           "packagePrice": "32.0000",
     *           "lastPackageCostPrice": "16.0000",
     *           "profitRat": 100,
     *           "pieceCount": "0.0000",
     *           "packageCount": "28.0000",
     *           "currentCount": "28.00000000",
     *           "recentAvgSell": null,
     *           "turnoverDays": null,
     *           "lastActiveDate": "2018-08-13 17:59:02",
     *           "defaultOrder": "2018-08-13 17:59:02",
     *           "turnoverDaysOrder": "0.000"
     *       }
     *
     */
    async goodsList(params) {
        const res = await fetch({
            url: '/api/v3/goods/goods-list/stock-goods',
            method: 'get',
            params: {
                ...params,
                e: 1,
            },
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'comma' });
            },
        });
        console.log('goodsList', res);
        if (res.data && res.data.data && res.data.data.rows) {
            res.data.data.rows = await Promise.all(res.data.data.rows.map((item) => DecodeService.decodeAESObjectForKey(item, {
                'name': true,
                'medicineCadn': true,
                'displaySpec': true,
                'displayName': true,
                'manufacturer': true,
                'manufacturerFull': true,
                'medcineNpmn': true,
                'medicineNpmn': true,
                'shebaoNationalView': {
                    'shebaoCode': true,
                },
            })));
        }
        return res.data;
    },
    async goodsListForPrint(data) {
        const res = await fetch({
            url: '/api/v3/goods/goods-list/stock-goods/for-print-price?e=1',
            method: 'post',
            data,
        });
        console.log('goodsListForPrint', res);
        if (res.data && res.data.data && res.data.data.rows) {
            res.data.data.rows = await Promise.all(res.data.data.rows.map((item) => DecodeService.decodeAESObjectForKey(item, {
                'name': true,
                'medicineCadn': true,
                'displaySpec': true,
                'displayName': true,
                'manufacturer': true,
                'manufacturerFull': true,
                'medcineNpmn': true,
                'medicineNpmn': true,
                'shebaoNationalView': {
                    'shebaoCode': true,
                },
            })));
        }
        return res.data;
    },
    /**
     * @desc 品种档案列表接口
     * <AUTHOR>
     * @date 2023-05-30 08:42:15
     */
    async archiveGoodsList(params) {
        const res = await fetch({
            url: '/api/v3/goods/goods-list/archive/stock-goods',
            method: 'get',
            params: {
                ...params,
                e: 1,
            },
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'comma' });
            },
        });
        console.log('archiveGoodsList', res);
        if (res.data && res.data.data && res.data.data.rows) {
            res.data.data.rows = await Promise.all(res.data.data.rows.map((item) => DecodeService.decodeAESObjectForKey(item, {
                'name': true,
                'medicineCadn': true,
                'displaySpec': true,
                'displayName': true,
                'manufacturer': true,
                'manufacturerFull': true,
                'medcineNpmn': true,
                'medicineNpmn': true,
                'shebaoNationalView': {
                    'shebaoCode': true,
                },
            })));
        }
        return res.data;
    },
    /**
     * @desc 品种档案导出接口
     * <AUTHOR>
     * @date 2023-05-30 08:42:15
     */
    async exportArchiveGoodsList(params, filename) {
        const url = '/api/v3/goods/goods-list/archive/stock-goods/export';
        return exportFileByAxios({
            filename,
            url,
            params,
            paramsSerializer(p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
    },

    async exportGoodsList(params, filename) {
        const url = '/api/v3/goods/goods-list/stock-goods/export';
        return exportFileByAxios({
            filename,
            url,
            params,
            paramsSerializer(p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
    },

    /**
     * 获取商品分布
     *
     * @param   {String}  goodsId  商品id
     * @param   {Object<offset,limit, orderBy, orderType,>}  params    分页以及排序
     *             onlyStock 1 返回有库存的门店
     *             disable  1  返回禁用的门店  0 返回启用的门店  null 返回所有门店
     * @return   {Object<{rows:Array<medicineItem>,count}>}
     */
    async distribution(goodsId, {
        offset, limit, orderBy, orderType, onlyStock, disable, clinicId,
        stockWarn,
        profitWarn,
        unsalableWarn,
        throwExceptionIfDel,
        queryType,
    }) {
        const ret = await fetch.get(`/api/v3/goods/${goodsId}/distribution`, {
            params: {
                offset,
                limit,
                orderBy,
                orderType,
                onlyStock,
                disable,
                clinicId,
                stockWarn,
                profitWarn,
                unsalableWarn,
                throwExceptionIfDel,
                queryType,
            },
        });
        return ret && ret.data;
    },

    // 获取进销存清单
    async goodsIo(goodsId, {
        clinicId,
        action,
        begDate,
        endDate,
        offset,
        limit,
        withTotalCount,
        batchId,
        pharmacyNo,
    }) {
        const ret = await fetch.get(`/api/v3/goods/${goodsId}/io`, {
            params: {
                clinicId,
                action,
                begDate,
                endDate,
                offset,
                limit,
                withTotalCount,
                batchId,
                pharmacyNo,
            },
        });
        return ret.data;
    },

    /**
     * 缺货商品列表
     */
    async shortagesList({
        clinicId, offset, limit, type, subType, cMSpec,
    }) {
        const ret = await fetch.get('/api/goods/shortages', {
            params: {
                clinicId,
                offset,
                limit,
                type,
                subType,
                cMSpec,
            },
        });

        return fetchDecode(ret);
    },
    // 获取门店药房列表
    async getPharmacyListByClinicId(data) {
        const res = await fetch.get('/api/v3/goods/config/pharmacy',{
            params: {
                queryClinicId: data.queryClinicId || '',
                queryEmployeeId: data.queryEmployeeId || '',
            },
        });
        return res && res.data;
    },
    /**
     * @desc  重构 新增商品
     * <AUTHOR>
     * @date 2019/03/31 10:27:01
     */
    async createdNewGoods(data) {
        const res = await fetch({
            url: '/api/v3/goods/',
            method: 'post',
            data,
        });
        return res && res.data;
    },

    /**
     * @desc  重构  修改商品
     * <AUTHOR>
     * @date 2019/03/31 10:36:00
     */
    async updateGoods(id, data) {
        const res = await fetch({
            url: `/api/v3/goods/${id}`,
            method: 'put',
            data,
        });
        return res;
    },
    /**
     * @desc  重构  获取商品详情
     * <AUTHOR>
     * @date 2019/03/31 10:37:30
     */
    async fetchGoods(id, queryParams) {
        const res = await fetch({
            url: `/api/v3/goods/${id}`,
            params: queryParams,
            method: 'get',
        });
        return res.data;
    },
    async fetchTraceCodeList(params) {
        const res = await fetch({
            url: '/api/v3/goods/traceable-code/stock-list',
            params,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 重构  删除药品
     * <AUTHOR>
     * @date 2019/03/30 22:47:51
     * @params force 强制删除
     */
    async deleteGoods(id, force = '', customErrorTips = false) {
        const res = await fetch({
            url: `/api/v3/goods/${id}?force=${force}`,
            method: 'delete',
            customErrorTips,
        });
        return res;
    },
    /**
     * @desc 根据设置套餐总价计算套餐价格
     * <AUTHOR>
     * @date 2022/5/13 16:41
     */
    async calcComposePrice(data) {
        const res = await fetch({
            url: '/api/v3/goods/compose/price/calculate',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 入库时的快速调价
     * <AUTHOR>
     * @date 2022/5/13 16:41
     */
    async fastAdjustPrices(id, data = '') {
        const res = await fetch({
            url: `/api/v3/goods/prices/${id}`,
            method: 'put',
            data,
        });
        return res;
    },

    /**
     * @desc  商品搜索，用于选择药品，携带 成本价和毛利率
     * <AUTHOR>
     * @date 2019/05/19 17:54:50
     * @params
     * @return
     */
    async queryGoods({
        key,
        clinicId,
        type,
        subType,
        cMSpec,
        offset,
        limit,
        disable = undefined,
        jsonType,
        withCostPrice = '',
    }) {
        const params = {};
        if (key) {
            params.key = key;
        }
        if (clinicId) {
            params.clinicId = clinicId;
        }
        if (type) {
            params.type = type;
        }
        if (offset !== undefined) {
            params.offset = offset;
        }
        if (limit !== undefined) {
            params.limit = limit;
        }
        if (disable !== undefined) {
            params.disable = disable;
        }
        if (subType !== undefined) {
            params.subType = subType;
        }
        if (cMSpec !== undefined) {
            params.cMSpec = cMSpec;
        }
        if (jsonType !== undefined) {
            params.jsonType = jsonType;
        }
        params.withCostPrice = withCostPrice;
        const res = await fetch({
            url: '/api/v3/goods/query',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'repeat' });
            },
        });
        return (res.data && res.data.data) || [];
    },
    /**
     * @desc 查询商品修改日志
     * <AUTHOR>
     * @date 2019/07/03 16:31:38
     * @params goodsId  商品ID
     * @return
     */
    async fetchGoodsLog(id) {
        const res = await fetch({
            url: `/api/v3/goods/${id}/log`,
            method: 'get',
        });
        return res.data || [];
    },

    /**
     * @desc  编辑药品信息的时候获取社保编码
     * <AUTHOR>
     * @date 2019/07/16 17:12:50
     * @params
     * @return
     */
    async matchSocialSecurityCode(data) {
        const res = await fetch({
            url: '/api/v2/shebao/chengdu/code/get_shebao_codes',
            method: 'post',
            data,
        });
        return res.data || {};
    },
    /**
     * @desc 社保限价与挂网价转his大小包装价
     * <AUTHOR>
     * @date 2025/1/6 上午11:51
     */
    async transShebaoPrice(data) {
        const res = await fetch({
            url: '/api/v3/goods/calculate-specification-transform-price',
            method: 'post',
            data,
        });
        return res.data || {};
    },
    /**
     * @desc 获取社保编码药品的相关详情
     * <AUTHOR>
     * @date 2020-09-02 16:54:49
     * @params code 社保编码 goodsType 商品类型  goodsSubType 子类型
     * shebaoCodeType 社保type 1 A类 正常社保， 2 B类 杭州省医保
     * @return
     */
    async fetchSocialCodeDetail(params) {
        const res = await fetch({
            url: 'api/v2/shebao/code/detail',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res && res.data;
    },
    async checkSocialCodeRepeat(params) {
        const res = await fetch({
            url: '/api/v2/shebao/code/check_repeat',
            method: 'get',
            params,
        });
        return res && res.data;
    },
    /**
     * @desc 通过名称药品类型搜索社保编码
     * <AUTHOR>
     * @date 2020-09-02 16:54:49
     * @params keyword 社保编码 goodsType 商品类型  goodsSubType 子类型
     * @return
     */
    async searchSocialCode(params) {
        const res = await fetch({
            url: '/api/v2/shebao/code/search',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res && res.data;
    },

    async fetchGrade() {
        const res = await fetch({
            url: '/api/v2/goods/grades',
            method: 'get',
        });
        return res.data || [];
    },


    /**
     * @desc 获取药房的代办事项
     * <AUTHOR>
     * @date 2021-06-22 14:38:59
     * @params pharmacyNo 不传默认是所有药房的总和
     */
    async fetchTodoCount(pharmacyNo = '') {
        const res = await fetch({
            url: `/api/v3/goods/todo/count?pharmacyNo=${pharmacyNo}`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @desc 药品的预警设置
     * <AUTHOR>
     * @date 2019/09/26 19:22:11
     */
    async updateWarnSet(id, data) {
        const res = await fetch({
            url: `/api/v3/goods/${id}/warn-setting`,
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 获取药品的预警设置
     * <AUTHOR>
     * @date 2019/09/26 19:22:11
     */
    async fetchWarnSet(id) {
        const res = await fetch({
            url: `/api/v3/goods/${id}/warn-setting`,
            method: 'get',
        });
        return res.data;
    },
    /**
     * @desc 获取批次列表
     * <AUTHOR>
     * @date 2019/12/5
     * @params clinicId, batchId, goodsId, limit, offset
     * @return
     */
    async fetchBatchList({
        clinicId,
        batchId,
        batchNo,
        goodsId,
        spuGoodsId,
        supplierId,
        limit,
        offset,
        expiredWarnFirst,
        pharmacyNo,
        spuGoodsCondition,
        batchViewMode,
        expiredWarn,
        costPriceWarn,
        orderBy,
        orderType,
        scene,//查询批次场景 1:药品养护、清斗、装斗选择批次时,2清空剩余库存
    }) {
        const res = await fetch('/api/v3/goods/stocks/batches', {
            params: {
                clinicId,
                batchId,
                batchNo,
                goodsId,
                spuGoodsId,
                supplierId,
                limit,
                offset,
                expiredWarnFirst,
                pharmacyNo,
                spuGoodsCondition,
                batchViewMode,
                expiredWarn,
                costPriceWarn,
                orderBy,
                orderType,
                scene,
            },
        });
        return res.data || {};
    },
    /**
     * @desc 批次信息-用于药品资料处拉取库存批次信息
     * <AUTHOR>
     * @date 2023/2/11
     * actionType, 进销存类型 0 入库 10 出库 20 调拨 30 盘点
     * goodsId, 药品GoodsId
     * orderId,进销存单据Id
     * orderItemId进销存单据详情Id
     */
    async fetchGoodsBatchList(goodsId, params) {
        const res = await fetch({
            url: `/api/v3/goods/${goodsId}/stocks/io`,
            method: 'get',
            params,
        });
        return res.data || {};
    },
    /**
     * @desc 导出批次
     * <AUTHOR>
     * @date 2019/12/5
     * @params clinicId, batchId, goodsId
     * @return
     */
    async exportBatchList({
        clinicId,
        batchId,
        goodsId,
        spuGoodsId,
        pharmacyNo,
        spuGoodsCondition,
    }) {
        const url = '/api/v3/goods/stocks/batches/export';
        if (spuGoodsId) {
            return exportFileByAxios({
                url,
                params: {
                    clinicId,
                    batchId,
                    pharmacyNo,
                    spuGoodsId,
                    spuGoodsCondition,
                },
                paramsSerializer(p) {
                    return Qs.stringify(p);
                },
            });
        }
        return exportFileByAxios({
            url,
            params: {
                clinicId,
                batchId,
                goodsId,
                pharmacyNo,
            },
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });
    },

    /**
     * @desc 针对商品 启用停用开关
     * <AUTHOR>
     * @date 2019/12/12 18:00:31
     * @params checkOnly true or false  检查是否能被停用启用
     *         disable  0 启用药品 1 停用药品
     * @return
     */
    async switchGoodsDisabled(
        id,
        data,
    ) {
        const res = await fetch({
            url: `/api/v3/goods/${id}/disable`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 药品物资商品，启用停用开关
     * <AUTHOR>
     * @date 2020-10-28 10:18:29
     * @params
     *      throwExpIfComposed  boolean  true只是检查goods 是否启用禁用
     *      v2DisableStatus   0 启用
     *                        10 停止采购 入库，可以把剩余库存销售完
     *                        20 停止采购 入库 销售
     */
    async goodsDisabled(goodsId, {
        throwExpIfComposed, v2DisableStatus,
    }) {
        const data = {
            throwExpIfComposed: throwExpIfComposed || false,
            v2DisableStatus,
        };
        const res = await fetch({
            url: `/api/v3/goods/stock-goods/${goodsId}/disable`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 搜索实体商品（需要入库才能销售的）
     * <AUTHOR>
     * @date 2019/12/18 14:50:13
     */
    async queryStockGoods(params) {
        if (params.jsonType) {
            params.jsonType = JSON.stringify(params.jsonType);
        }
        const res = await fetch({
            url: '/api/v3/goods/search/stock-goods',
            params,
            paramsSerializer (p) {
                return Qs.stringify(p, { arrayFormat: 'repeat' });
            },
        });
        return res.data;
    },
    /**
     * @desc  获取商品的库存批次信息 all = 1 获取所有批次
     * <AUTHOR>
     * @date 2019/06/27 14:50:15
     * @params 商品的goodsId
     * @return
     */
    async fetchGoodsBatch(goodsId, params = {
        all: '',
        pharmacyNo: '',
        pharmacyType: '',
        recentStockWithNonBatchNo: '',
    }) {
        const res = await fetch({
            url: `/api/v3/goods/${goodsId}/stocks/batches`,
            method: 'get',
            params,
            paramsSerializer (p) {
                return Qs.stringify(p);
            },
        });
        return res.data;
    },
    // 获取商品的库存批次信息（不同进价的批次）
    async fetchGoodsBatches(goodsId, batchId, params) {
        const res = await fetch({
            url: `/api/v3/goods/${goodsId}/stocks/batches/${batchId}`,
            method: 'get',
            params,
            paramsSerializer (p) {
                return Qs.stringify(p);
            },
        });
        return res.data;
    },
    /**
     * @desc 批量查询药品的库存信息
     * <AUTHOR>
     * @date 2019/12/25
     * @params goodsIds 药品id array all 查询所有所批次，包括库存为0 的数据
     * @return
     */
    async fetchGoodIdsBatchs(goodsIds, clinicId = '',pharmacyNo, pharmacyType) {
        const res = await fetch({
            url: '/api/v3/goods/stocks/batches/in-pharmacy',
            // url: '/api/v2/goods/query/batches',// 老接口
            method: 'post',
            data: {
                goodsIds,
                clinicId,
                pharmacyNo,
                pharmacyType,
            },
        });
        return res.data;
    },

    /**
     * @desc 获取系统所有的药品分类新版
     * <AUTHOR>
     * @date 2020-07-10 09:31:15
     * @params queryType 0 或者 不传 拉取全部类型，
     *                   1 拉取有库存的商品
     *                   2 非库存的商品
     *         needCustomType 是否返回二级分类
     *                  0 不返回
     *                  1 返回
     */
    async fetchGoodsClassificationV3(params) {
        const { getViewDistributeConfig } = await import('@/views-distribute/utils.js');

        const res = await fetch({
            url: '/api/v3/goods/sys/types',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });

        if (res && res.data) {
            const list = res.data?.data?.list ?? [];
            const item = list.find((item) => `${item.id}` === `${GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES}`);
            if (item) {
                const { transGoodsClassificationName } = getViewDistributeConfig();

                if (typeof transGoodsClassificationName === 'function') {
                    item.name = transGoodsClassificationName(item.name);
                }
            }
            // TODO: 自测加非配方饮片
            // res.data.data.list.push({
            //     'id': '93',
            //     'parentId': '13',
            //     'name': '非配方饮片',
            //     'goodsType': 1,
            //     'goodsSubType': 2,
            //     'goodsCMSpec': '非配方饮片',
            //     'isLeaf': 1,
            //     'sort': 3,
            // });
        }

        return res && res.data;
    },
    /**
     * @desc 获取一级分类的二级分类
     */
    async fetchSecondaryClassification(typeId, supplierId) {
        const res = await fetch({
            url: '/api/v3/goods/custom-types',
            params: {
                typeId,
                supplierId,
            },
        });
        return res && res.data;
    },
    /**
     * @desc 编辑二级分类
     */
    async updateSecondaryClassification(data, config = {}) {
        const res = await fetch({
            url: '/api/v3/goods/custom-types',
            method: 'post',
            data,
            ...config,
        });
        return res && res.data;
    },
    async searchProducts(params) {
        const res = await fetch({
            url: '/api/v3/goods/search/for-create-goods',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res.data;
    },

    async getRecommendGoods(params) {
        const res = await fetch({
            url: '/api/v3/goods/search/domain-medicine/create',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res.data;
    },

    /**
     * @desc 获取当前二级目录使用数量
     * <AUTHOR>
     * @date 2020-07-13 15:07:38
     * @params
     * @return
     */
    async fetchCustomTypesCount(customTypeId) {
        const res = await fetch({
            url: `/api/v3/goods/custom-types/${customTypeId}/goods-count`,
            method: 'get',
        });
        return res && res.data;
    },

    /**
     * @desc 获取中药标准名的别名
     * <AUTHOR>
     * @date 2021/4/15 9:34 上午
     * @param medicineName {string} 中药名
     * @returns
     */
    async getStandardAliasName(medicineName) {
        const res = await fetch.get('/api/v3/goods/chinese-medicine/standard-alias-names', {
            params: { medicineName },
        });
        return res.data;
    },

    async loadWarnGoods(params = {}) {
        const res = await fetch.get('/api/v3/goods/stocks/expired-warn-goods', {
            params,
        });

        return res.data;
    },

    /**
     * @desc 查看历史税率
     * <AUTHOR>
     * @date 2022/5/10 16:07
     * @param {object} params
     */
    async getHistoryTaxRate(params = {}) {
        const res = await fetch({
            url: '/api/v3/goods/inout-tax-rate/log',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res.data;
    },

    /**
     * @desc 在Goods规格发生变化的情况下，检查库存数量的变化
     * <AUTHOR>
     * @date 2022/9/16 10:52
     */
    async checkStockPieceNumChange(goodsId, params = {}) {
        const res = await fetch({
            url: `/api/v3/goods/${goodsId}/piece-num-change/check-stock`,
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params);
            },
        });
        return res.data;
    },

    /**
     * 请求费用类型
     * @param innerFlag 是否只获取内置费用类型: 1 只获取内置 null 获取全部
     * @param scopeId 费用类型的范围: null 全部 0 药品 10 检查 20 加工快递 30 挂号
     * @returns {Promise<*>}
     */
    async fetchFeeTypes(params = {}) {
        const {
            innerFlag = null,
            scopeId = null,
            disable = 0,
        } = params;
        const res = await fetch({
            url: '/api/v3/goods/fee-types',
            method: 'get',
            params: {
                innerFlag,
                scopeId,
                disable,
            },
            paramsSerializer(params) {
                return Qs.stringify(params);
            },
        });
        return res.data;
    },

    /**
     * @description 批量获取医保详情
     * @param {String} chainId 总部id
     * @param {Array} itemList goods列表 { goodsId, goodsType, subType }
     */
    async batchGetSocialCodeDetail(data) {
        const url = '/api/v2/shebao/code/get_matched_codes';

        const res = await fetch({
            url,
            method: 'post',
            data,
        });

        return res.data;
    },

    /**
     * 复制检验goods为指定goods类型
     * @param {*} data
     * @returns
     * @file https://dev.abczs.cn/swagger-ui/?urls.primaryName=examination#/examination-goods-controller/copyExaminationGoodsUsingPOST
     */
    async copyExaminationGoods(data) {
        const res = await fetch({
            url: '/api/v2/examination-goods/examinations/copy',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * 复制检验goods为指定goods类型
     * @param {*} data
     * @returns
     * @file https://app.apifox.com/project/4105462/apis/api-171322289
     */
    async batchModifyGoods(data) {
        const res = await fetch({
            url: '/api/v3/goods/goods-list/stock-goods/batch-modify-goods',
            method: 'post',
            data,
        });
        return res.data;
    },

    async batchModifyGoodsProgress(taskId) {
        const res = await fetch({
            url: '/api/v3/goods/goods-list/stock-goods/batch-modify-goods/progress',
            method: 'get',
            params: {
                taskId,
            },
            paramsSerializer(params) {
                return Qs.stringify(params);
            },
        });
        return res.data;
    },

    /**
     * @desc 批量更新goods biz-extensions
     * <AUTHOR>
     * @date 2024/06/24 15:05:21
     */
    async postGoodsExtensions(data) {
        const res = await fetch({
            url: '/api/v3/goods/biz-extensions',
            method: 'post',
            data,
        });
        return res.data;
    },

    async fetchClinicGoodsDeliveryAddress() {
        const res = await fetch.get('/api/v3/goods/delivery/address');

        return res.data;
    },

    /**
     * 物流信息对象
     * @typedef {Object} LogisticsInfo
     * @property {string} logisticsName - 物流名称
     * @property {string} logisticsMobile - 物流联系电话
     * @property {string} addressProvinceId - 省份ID
     * @property {string} addressProvinceName - 省份名称
     * @property {string} addressCityId - 城市ID
     * @property {string} addressCityName - 城市名称
     * @property {string} addressDistrictId - 区域ID
     * @property {string} addressDistrictName - 区域名称
     * @property {string} addressDetail - 详细地址
     */
    /**
     * 新增收货地址
     * @param {LogisticsInfo} data - 物流信息对象
     */
    async addClinicGoodsDeliveryAddress(data) {
        const res = await fetch.post('/api/v3/goods/delivery/address', data);
        return res.data;
    },

    /**
     * 修改收货地址
     * @param id
     * @param {LogisticsInfo} data
     * @returns {Promise<any>}
     */
    async updateClinicGoodsDeliveryAddress(id, data) {
        const res = await fetch.put(`/api/v3/goods/delivery/address/${id}`, data);
        return res.data;
    },

    /**
     * 查询收货地址详情
     * @param id
     * @returns {Promise<any>}
     */
    async fetchClinicGoodsDeliveryAddressDetail(id) {
        const res = await fetch.get(`/api/v3/goods/delivery/address/${id}`);
        return res.data;
    },

    // 获取门店所有检验设备的关联耗材列表
    async fetchClinicDeviceRelateGoodsList() {
        const res = await fetch.get('/api/v3/goods/exam/assay/devices/association-goods-list');
        return res.data;
    },

    async fetchExamApplySheetDeliveryInfo(orderId, orderType) {
        const res = await fetch.get(`/api/v3/goods/stock/order/logistics/trace/${orderId}/${orderType}`);
        return res.data;
    },

    /**
     * @desc 获取总部档案列表
     * @param {Object} params 查询参数
     * @param {string} [params.goodsId] 商品ID
     * @param {string} [params.keyword] 搜索关键词
     * @param {Array<number>} [params.type] 商品类型数组
     * @param {Array<number>} [params.subType] 商品子类型数组
     * @param {Array<number>} [params.typeId] 类型ID数组
     * @param {Array<number>} [params.customTypeId] 自定义类型ID数组
     * @param {number} [params.isNotAdd] 是否未添加 1=是
     * @param {number} [params.offset] 分页偏移量
     * @param {number} [params.limit] 每页数量
     * @returns {Promise<{rows: Array, count: number}>} 返回总部档案列表
     */
    async fetchChainStockGoodsList(params) {
        const res = await fetch.get('/api/v3/goods/goods-list/chain-stock-goods', {
            params,
            paramsSerializer (p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
        return res.data;
    },

    /**
     * @desc 批量添加商品档案到门店
     * @param {Object} data 请求数据
     * @param {Array<string>} data.goodsIdList 商品ID数组
     * @returns {Promise<Object>} 返回操作结果
     */
    async batchAddGoodsArchiveToClinic(data) {
        const res = await fetch.post('/api/v3/goods/batch-add-goods-archive', data);
        return res.data;
    },

    async fetchGoodsStockLockings(params) {
        const res = await fetch.get('/api/v3/goods/stocks/lockings', {
            params,
        });
        return res.data && res.data.data;
    },
    /**
     * @desc 获取社保药监编码的相关详情
     */
    async fetchDrugSupervisionCodeDetail(params) {
        const res = await fetch({
            url: '/api/v3/goods/search/center-code',
            method: 'get',
            params,
        });
        return res && res.data;
    },

    /**
     * @desc 修改商品医保信息
     * <AUTHOR> Yang
     * @date 2025-05-30 17:29:54
     */
    async updateGoodsShebaoInfo(goodsId, data) {
        const res = await fetch({
            url: `/api/v3/goods/${goodsId}/shebao`,
            method: 'put',
            data,
        });
        return res && res.data;
    },

    /**
     * @desc 快速入库
     */
    async quickStockIn(goodsId, data) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/in/${goodsId}/quick-stock-in`,
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 删除清理库存 - 删除并创建入库
     * @param data.opType 0 删除清理库存 1 删除并创建入库
     * @param data.createReq 创建商品信息-和原建档接口参数一样
     * @param data.stockInReqList 入库信息-和原入库接口参数一样
     */
    async clearTransferStock(goodsId, data) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/in/${goodsId}/clear-transfer-stock`,
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 删除验收单药品
     * <AUTHOR> Assistant
     * @date 2025/01/12
     * @param {string} orderId - 需要删除的验收单id
     * @param {Array} ids - 需要删除的验收单药品id列表
     * @return {Promise} 删除结果
     */
    async deleteInspectOrderStockItems(orderId, ids) {
        const res = await fetch({
            url: `/api/v3/goods/inspect/orders/${orderId}/stock-item`,
            method: 'delete',
            data: {
                ids,
            },
        });
        return res.data;
    },

    /**
     * @desc 删除入库单详情
     * <AUTHOR> Assistant
     * @date 2025/01/12
     * @param {string} orderId - 需要删除的入库单id
     * @param {Array} ids - 需要删除的入库单详情id列表
     * @return {Promise} 删除结果
     */
    async deleteStockItems(orderId, ids) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/in/${orderId}/stock-item`,
            method: 'delete',
            data: {
                ids,
            },
        });
        return res.data;
    },
};
