import fetch from 'utils/fetch';
import Qs from 'qs';
import store from '@/store';
import { exportFileByAxios } from 'utils/excel';
import { getApp } from '@/core';

const stat = {
    /**
     * @desc 获取医生列表
     * /api/v2/stat/reports/availableemployees?begDate=2018-10-01&endDate=2018-12-30&clinicId=fff730ccc5ee45d783d82a85b8a0e52d
     * <AUTHOR>
     * @date 2018/11/23 12:15 PM
     * @params
     * @return
     */
    async getDoctorListByClinicId(begin, end, clinicId, seller) {
        const res = await fetch({
            url: `api/v2/sc/stat/member/achievement/seller/select?beginDate=${begin}&endDate=${end}&clinicid=${clinicId}&seller=${seller}`,
            method: 'get',
            disallowDuplicate: true,
        });
        return res.data;
    },

    async getMemberRechargeClinics(begin, end) {
        const res = await fetch({
            url: `api/v2/sc/stat/member/achievement/clinic/select?beginDate=${begin}&endDate=${end}`,
            method: 'get',
            disallowDuplicate: true,
        });
        return res.data;
    },
    /**
     * @desc 获取诊所列表
     * /api/v2/stat/reports/availableemployees?begDate=2018-10-01&endDate=2018-12-30
     * <AUTHOR>
     * @date 2018/11/23 12:13 PM
     * @params
     * @return
     */
    async getClinicLists(begin, end) {
        const res = await fetch({
            url: `/api/v3/clinics/chain/clinics?begDate=${begin}&endDate=${end}`,
            method: 'get',
            disallowDuplicate: true,
        });
        return res.data;
    },

    /**
     * @desc 获取收银员列表
     * /api/v2/stat/reports/availableemployees?begDate=2018-10-01&endDate=2018-12-30&clinicId=fff730ccc5ee45d783d82a85b8a0e52d
     * <AUTHOR>
     * @date 2018/11/23 12:15 PM
     * @params
     * @return
     */
    async getCashiersByClinicId(beginDate, endDate, clinicId) {
        const res = await fetch.get('/api/v2/stat/reports/availableCashiers', {
            params: {
                beginDate,
                endDate,
                clinicId,
            },
            disallowDuplicate: true,
        });
        return res.data;
    },

    /**
     * @desc 获取治疗结果
     *    .get('/stat/reports/treatments', report.statTreatment)
     * <AUTHOR>
     * @date 2018/11/16 6:27 PM
     * @params
     * @return
     */
    async getComposeStat(
        begin,
        end,
        clinicId,
        composeId,
        keyword = '',
    ) {
        const beginDate = `${begin} 00:00:00`;
        const endDate = `${end} 23:59:59`;
        composeId = composeId || '';
        const {
            enableGrossInStatistics,enableCostInStatistics,
        } = store.getters;
        const res = await fetch.get('/api/v2/sc/stat/operation/compose/list', {
            params: {
                beginDate,
                endDate,
                clinicId,
                composeId,
                keyword,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
            },
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
            disallowDuplicate: true,
        });
        return res.data;
    },

    /**
     * @desc 收费套餐统计导出
     * <AUTHOR>
     * @date 2021/05/14
     * @params
     * @return
     */
    composeExport(beginDate, endDate, clinicId, doctorId, composeId, keyword = '') {
        const {
            enableGrossInStatistics,enableCostInStatistics,
        } = store.getters;
        const query = {
            beginDate,
            endDate,
            clinicId,
            doctorId,
            composeId,
            keyword,
            enableCost: enableCostInStatistics,
            enableGross: enableGrossInStatistics,
        };
        const url = '/api/v2/sc/stat/operation/compose/list/export';
        return exportFileByAxios({
            url,
            params: query,
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });
    },
    /**** 药品 ****/
    /**
     * @desc 入库
     *  .get('/stat/reports/stockin', report.statStockIn)
     * <AUTHOR>
     * @date 2018/11/20 6:38 PM
     * @params
     * @return
     */
    async getStockIn(beginDate, endDate, clinicId, isMultiPharmacy, pharmacyType, actionList) {
        const res = await fetch({
            url: '/api/v2/sc/stat/inventory-action/in/info',
            method: 'get',
            params: {
                beginDate,
                endDate,
                clinicId,
                isMultiPharmacy,
                pharmacyType,
                actionList,
            },
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { arrayFormat: 'comma' });
            },
        });
        return res.data;
    },

    // 入库动作筛选
    async stockInAction(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/inventory-action/in/action',
            method: 'get',
            params,
            paramsSerializer (queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });
        return res.data;
    },
    /**
     * @desc 供应商
     *         .get('/stat/reports/stockinsupplier', report.statStockInSupplier)
     * <AUTHOR>
     * @date 2018/11/20 8:04 PM
     * @params
     * @return
     */
    async getStockInSupplier(begin, end, clinicId, pharmacyType) {
        const beginDate = `${begin} 00:00:00`;
        const endDate = `${end} 23:59:59`;
        const res = await fetch.get('/api/v2/stat/reports/goods/in/supplier', {
            params: {
                beginDate,
                endDate,
                clinicId,
                pharmacyType,
            },
        });
        return res.data;
    },

    /**
     * 获取调拨统计-汇总数据
     * <AUTHOR>
     * @date 2020-07-14
     * @param {Object} params 入参数据
     * @returns {Promise}
     */
    async getStockTrans(params) {
        const res = await fetch({
            url: 'api/v2/sc/stat/allocation/summary',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * 获取历史数据报表信息
     * <AUTHOR> @date 2025-04-08
     * @param {Object} params 入参数据
     * @returns {Promise}
     */
    async getExternalReportInfo(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/external/report/info',
            method: 'post',
            data: params,
        });
        return res.data;
    },

    /**
     * 获取历史数据报表列表
     * <AUTHOR> @date 2025-04-08
     * @param {Object} params 入参数据
     * @returns {Promise}
     */
    async getExternalReportList(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/external/report/list',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * 更新历史数据报表数据源
     * <AUTHOR> @date 2025-04-08
     * @param {Object} params 入参数据
     * @returns {Promise}
     */
    async updateExternalReportSource(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/external/report/source/update',
            method: 'post',
            data: params,
        });
        return res.data;
    },
    /**
     * 获取调拨统计-明细数据
     * <AUTHOR>
     * @date 2020-07-14
     * @param {Object} params 入参数据
     * @returns {Promise}
     */
    async getStockTransDetail(params) {
        const res = await fetch({
            url: 'api/v2/sc/stat/allocation/detail',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * @desc 患者清单
     */
    async getPatientStat(data,params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/patient/stat',
            method: 'post',
            data,
            params,
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });

        return res?.data;
    },

    /**
     * @desc
     * //门诊清单
     .get('/stat/reports/outpatientlist', report.statOutPatientList)
     * <AUTHOR>
     * @date 2018/12/16 22:57:17
     * @params
     * @return
     */
    async getOutPatientList(params) {
        const { enablePatientMobileInStatistics } = getApp().store.getters;
        const res = await fetch({
            url: '/api/v2/sc/stat/outpatient/list',
            method: 'get',
            params: {
                ...params,
                enablePatientMobile: enablePatientMobileInStatistics,
            },
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });

        return res?.data;
    },

    /**
     * 随访统计/筛选框
     * <AUTHOR>
     * @date 2020-07-03
     * @param {Object} params 入参
     * @returns {Promise}
     */
    async fetchFollowupSelect(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/medical/followup/select',
            method: 'get',
            params,
        });
        return res.data;
    },
    /**
     * 随访统计/人员
     * <AUTHOR>
     * @date 2020-07-03
     * @param {Object} params 入参
     * @returns {Promise}
     */
    async fetchFollowupPersonnel(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/medical/followup/personnel',
            method: 'get',
            params,
        });
        return res.data;
    },
    /**
     * 随访统计/明细
     * <AUTHOR>
     * @date 2020-07-03
     * @param {Object} params 入参
     * @returns {Promise}
     */
    async fetchFollowupDetail(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/medical/followup/detail',
            method: 'get',
            params,
        });
        return res.data;
    },
    /**
     * 空中药房统计-可选订单类型、商家列表
     * <AUTHOR>
     * @date 2020-07-03
     * @returns {Promise}
     */
    async fetchAirPharmacySelection() {
        const res = await fetch({
            url: '/api/v2/sc/stat/bis/his/pharmacy/selection',
            method: 'get',
        });
        return res.data;
    },
    /**
     * 空中药房统计
     * <AUTHOR>
     * @date 2020-07-03
     * @param {Object} params 入参
     * @returns {Promise}
     */
    async fetchAirPharmacy(params) {
        const {
            enableGrossInStatistics,enableCostInStatistics,
        } = store.getters;
        const res = await fetch({
            url: '/api/v2/sc/stat/bis/his/pharmacy',
            method: 'get',
            params: {
                ...params,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
            },
        });
        return res.data;
    },

    /**
     * 空中药房统计导出
     */

    airPharmacyStatExport(params) {
        const url = '/api/v2/sc/stat/bis/his/pharmacy/export';
        return exportFileByAxios({
            url,
            params,
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });
    },
    /**
     * 空中药房统计-数据汇总
     * <AUTHOR>
     * @date 2020-07-03
     * @param {Object} params 入参
     * @returns {Promise}
     */
    async fetchAirPharmacyTotal(params) {
        const {
            enableGrossInStatistics,enableCostInStatistics,
        } = store.getters;
        const res = await fetch({
            url: '/api/v2/sc/stat/bis/his/pharmacy/total',
            method: 'get',
            params: {
                ...params,
                enableCost: enableCostInStatistics,
                enableGross: enableGrossInStatistics,
            },
        });
        return res.data;
    },

    /**
     * 获取工作台的门诊预览 outpatientCount, firstVisitOutpatientCount, reVisitOutpatientCount
     * @param params
     * @return {Promise<*>}
     */
    async fetchDashboardOutpatientOverview(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/outpatient-first-revisit/info',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * 获取工作台的收费预览
     * @param params
     * @return {Promise<*>}
     */
    async fetchDashboardChargeOverview(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/revenue/overview/summary',
            method: 'get',
            params: {
                ...params,
                employeeType: 2,
            },
        });
        return res.data;
    },

    /**
     * 获取医生列表（姓名资料下沉需求添加）
     */
    async fetchOutpatientEmployees(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/select/outpatient/employee',
            method: 'get',
            params,
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });
        return res.data;
    },
};
export default stat;
