import Qs from 'qs';
import fetch from 'utils/fetch';
import DecodeService from '@/service/decode';

/**
 * 病人相关
 */
const Patients = {
    async fetchExaminations({
        keyword, ...otherParams
    }) {
        const res = await fetch({
            url: '/api/v2/examinations',
            method: 'get',
            params: {
                key: keyword,
                ...otherParams,
            },
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },
    // quicklist  ----end----

    // 根据患者姓名获取患者列表（模糊匹配）
    async fetchPatientsByName(keyword, query) {
        const {
            withDefault,
            clinicId,
            extDataFlag,
        } = query || {};

        const res = await fetch({
            url: '/api/v2/crm/patients/query',
            method: 'get',
            params: {
                key: keyword,
                clinicId,
                withDefault,
                extDataFlag,
            },
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },

    /**
     * @desc 根据name mobile查询患者信息
     * <AUTHOR>
     * @date 2018/10/22 22:21:55
     */
    async fetchPatientByNameMobile(name, mobile) {
        const res = await fetch({
            url: `/api/v2/crm/patients/query/by-name-mobile?name=${name}&mobile=${mobile}`,
            method: 'get',
        });
        return res?.data || {};
    },

    /**
     * @desc  获取患者就诊历史摘要列表
     * <AUTHOR>
     * @date 2019/03/28 20:18:10
     * @params id patient id
     */
    async fetchHistoryAbs(id, params, disabledCancel = false) {
        const res = await fetch({
            url: `/api/v2/outpatients/history/${id}`,
            method: 'get',
            params: {
                ...params,
                e: 1,
            },
            disabledCancel,
        });
        if (res.data && res.data.data && res.data.data.result) {
            res.data.data.result = await Promise.all(res.data.data.result.map((item) => {
                return DecodeService.decodeAESObjectForKey(item, {
                    doctorName: true,
                    departmentName: true,
                    patient: {
                        mobile: true,
                        name: true,
                        sex: true,
                        idCard: true,
                        birthday: true,
                    },
                });
            }));
        }
        return res.data;
    },

    /**
     * @desc  重构  获取患者模块quicklist
     * <AUTHOR>
     * @date 2019/04/02 09:34:06
     */
    async fetchQuickList(keyword, offset, limit) {
        const res = await fetch({
            url: `/api/v2/outpatients/patients/?keyword=${keyword}&offset=${offset}&limit=${limit}`,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 获取用户 既往史
     * <AUTHOR>
     * @date 2019/04/02 16:26:43
     */
    async fetchPastHistory(id) {
        const { data } = await fetch({
            url: `/api/v2/crm/patients/${id}/pastHistory`,
        });
        return data.data || {};
    },

    /**
     * @desc 新增就诊原因
     * <AUTHOR>
     * @date 2021/4/7 2:17 下午
     * @params { name 名称, parentId 一级原因id }
     * @return
     */
    async createVisitSource(data) {
        const res = await fetch.post('/api/v2/crm/patients/source/types/', data);
        return res.data;
    },

    /**
     * @desc 更新就诊原因（只更新名字）
     * <AUTHOR>
     * @date 2021/4/7 2:20 下午
     * @params id 就诊原因id { name, parentId }
     */
    async updateVisitSource(id, data) {
        const res = await fetch.put(`/api/v2/crm/patients/source/types/${id}`, data);
        return res.data;
    },

    /**
     * @desc 删除就诊原因，同时删除子分类
     * <AUTHOR>
     * @date 2021/4/7 2:21 下午
     * @params id 就诊原因id
     */
    async deleteVisitSource(id) {
        const res = await fetch.delete(`/api/v2/crm/patients/source/types/${id}`);
        return res.data;
    },

    /**
     * @desc 获取患者标注数据
     * <AUTHOR>
     * @date 2022/10/19 15:58:06
     */
    async fetchPatientsAttachmentsLabel(urls) {
        const res = await fetch.post('/api/v2/crm/patients/attachment/label', { urls });
        return res.data.data;
    },
    /**
     * @desc 批量保存children来源分类
     * <AUTHOR>
     * @date 2023/3/27 11:39:10
     */
    async batchSaveChildrenSource(parentId,data) {
        const res = await fetch.post(`/api/v2/crm/patients/source/types/parent/${parentId}/children`, data);
        return res.data.data;
    },
};

export default Patients;
