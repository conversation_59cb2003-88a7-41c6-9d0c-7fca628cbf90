import fetch from 'utils/fetch';
import BaseAPI from 'api/base-api';
import Qs from 'qs';
import { exportFileByAxios } from 'utils/excel';
import DecodeService from '@/service/decode';

export async function decodeAESNonStockGoods(res) {
    if (res.data && res.data.data && res.data.data.rows) {
        const obj = {
            'name': true,
            'medicineCadn': true,
            'displaySpec': true,
            'displayName': true,
            'shebaoNationalView': {
                'shebaoCode': true,
            },
        };

        res.data.data.rows = await Promise.all(res.data.data.rows.map(async (item) => {

            item.children = await Promise.all(item.children?.map((child) => {
                return DecodeService.decodeAESObjectForKey(child, obj);
            }) ?? []);

            item.feeComposeList = await Promise.all(item.feeComposeList?.map((child) => {
                return DecodeService.decodeAESObject<PERSON>or<PERSON>ey(child, obj);
            }) ?? []);

            return DecodeService.decodeAESObjectFor<PERSON><PERSON>(item, obj);
        }));
    }
}
export default class OralProcessAPI extends BaseAPI {
    static async createProcess(data) {
        const res = await fetch({
            url: '/api/v2/processings/denture',
            method: 'post',
            data,
        });
        return res.data;
    }

    static async batchCreateProcess(data) {
        const res = await fetch({
            url: '/api/v2/processings/denture/batch',
            method: 'post',
            data,
        });
        return res.data;
    }

    static async updateProcess(id, data) {
        const res = await fetch({
            url: `/api/v2/processings/denture/${id}`,
            method: 'put',
            data,
        });
        return res.data;
    }

    static async fetchProcess(id) {
        const res = await fetch({
            url: `/api/v2/processings/denture/${id}`,
        });
        return res.data;
    }

    static async deleteProcess(id) {
        const res = await fetch({
            url: `/api/v2/processings/denture/${id}`,
            method: 'delete',
        });
        return res.data;
    }

    static async calculate(data) {
        const res = await fetch({
            url: '/api/v2/processings/denture/calculate',
            method: 'post',
            data,
        });
        return res.data;
    }

    static async updateProcessStatus(id, data) {
        const res = await fetch({
            url: `/api/v2/processings/denture/status/${id}`,
            method: 'put',
            data,
        });
        return res.data;
    }

    static async fetchProcessList(params) {
        const res = await fetch({
            url: '/api/v2/processings/denture',
            method: 'get',
            params,
            paramsSerializer(queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });

        return res?.data;
    }

    // static async exportProcessOrder(params) {
    //     const res = await fetch.post("/api/v2/processings/denture/export", {
    //         ...params
    //     });

    //     const url = URL.createObjectURL(res.data);
    //     const a = document.createElement("a");
    //     a.href = url;
    //     a.click();

    //     console.log(res);
    //     debugger;
    //     return res;
    //     // location.href = res;
    // }
    static async exportProcessOrder(params) {
        const url = '/api/v2/processings/denture/export';
        return exportFileByAxios({
            url,
            params,
            paramsSerializer(p) {
                return Qs.stringify(p, { arrayFormat: 'repeat' });
            },
        });
    }

    static async getMachiningProject(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/operationanalysis/machining/project',
            method: 'get',
            params,
        });

        return res?.data;
    }

    static async getMachiningDetail(params) {
        const res = await fetch({
            url: '/api/v2/sc/stat/operationanalysis/machining/details',
            method: 'get',
            params,
        });

        return res?.data;
    }
    static async fetchFactoryList(params) {
        const res = await fetch({
            url: '/api/v3/goods/supplier',
            method: 'get',
            params,
            paramsSerializer(queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });

        return res?.data;
    }
    static async createFactory(data) {
        const res = await fetch({
            url: '/api/v3/goods/supplier',
            method: 'post',
            data,
        });
        return res.data;
    }
    static async fetchFactoryDetail(supplierId) {
        const res = await fetch({
            url: `/api/v3/goods/supplier/${supplierId}`,
        });
        return res.data;
    }
    static async updateFactory(data, supplierId) {
        const res = await fetch({
            url: `/api/v3/goods/supplier/${supplierId}`,
            method: 'put',
            data,
        });
        return res.data;
    }
    static async deleteFactory(supplierId,params) {
        const res = await fetch({
            url: `/api/v3/goods/supplier/${supplierId}`,
            method: 'delete',
            data: params,
        });
        return res.data;
    }
    // 厂家拖拽排序
    static async sortFactory(data) {
        const res = await fetch({
            url: '/api/v3/goods/supplier/sort',
            method: 'post',
            data,
        });
        return res.data;
    }

    // 获取加工厂家项目
    static async fetchFactoryItemList(params) {
        const res = await fetch({
            url: '/api/v3/goods/goods-list/non-stock-goods',
            method: 'get',
            params: {
                ...params,
                e: 1,
            },
            paramsSerializer(queryParams) {
                return Qs.stringify(queryParams, { indices: false });
            },
        });

        await decodeAESNonStockGoods(res);

        return res?.data;
    }

    // 新增、修改、删除加工厂家项目
    static async updateFactoryItemList(data) {
        const res = await fetch({
            url: '/api/v3/goods/processing',
            method: 'post',
            data,
        });
        return res.data;
    }

    // 批量导入项目
    static async importItems(data) {
        const res = await fetch({
            url: '/api/v3/goods/processing/import',
            method: 'post',
            data,
        });
        return res.data;
    }

    // 拉取批量导入项目的进度
    static async getImportItemsProgress(keyId) {
        const res = await fetch({
            url: `/api/v3/goods/processing/progress/${keyId}`,
            method: 'get',
        });
        return res.data;
    }

}
