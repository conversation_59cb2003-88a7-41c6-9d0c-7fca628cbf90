import fetch from 'utils/fetch';
import Qs from 'qs';
import DecodeService from '@/service/decode';

export default {
    async getMemberTypePageListDetail(params) {
        const res = await fetch({
            url: '/api/v2/promotions/discounts/member-goods/page-list',
            method: 'get',
            params,
        });
        return res.data && res.data.data;
    },
    async getDiscountGoodsPageListDetail(params) {
        const res = await fetch({
            url: '/api/v2/promotions/discounts/discount-goods/page-list',
            method: 'get',
            params,
        });
        return res.data && res.data.data;
    },
    async getCouponGoodsPageListDetail(params) {
        const res = await fetch({
            url: '/api/v2/promotions/goods/page-list',
            method: 'get',
            params,
        });
        return res.data && res.data.data;
    },
    async getGiftGoodsPageListDetail(params) {
        const res = await fetch({
            url: '/api/v2/promotions/gifts/goods/page-list',
            method: 'get',
            params,
        });
        return res.data && res.data.data;
    },
    async getReferralGoodsPageListDetail(params) {
        const res = await fetch({
            url: '/api/v2/promotions/referral/referral-goods/page-list',
            method: 'get',
            params,
        });
        return res.data && res.data.data;
    },
    async getMainsGoodsPageListDetail(params) {
        const res = await fetch({
            url: '/api/v2/promotions/mains/goods/page-list',
            method: 'get',
            params,
        });
        return res.data && res.data.data;
    },

    // 获取营销活动列表
    async getPromotionsList(params) {
        const res = await fetch({
            url: '/api/v2/promotions/pharmacy',
            params,
        });
        return res?.data;
    },

    // 营销卡项相关
    promotions: {
        async list(offset, limit, keyword) {
            const res = await fetch.get('/api/v2/promotions/card/list-by-page',{
                params: {
                    limit,
                    offset,
                    keyword,
                },
            });
            return res.data;
        },
        // 新增
        async addCard(data) {
            const res = await fetch({
                url: '/api/v2/promotions/card',
                method: 'post',
                data,
            });
            return res.data;
        },
        // 详情
        async card(cardId) {
            const res = await fetch.get(`/api/v2/promotions/card/${cardId}`);
            return res.data;
        },

        // 删除
        async delete(cardId) {
            const res = await fetch.delete(`/api/v2/promotions/card/${cardId}`);
            return res.data;
        },

        // 修改
        async updateCard(cardId, data) {
            const res = await fetch({
                url: `/api/v2/promotions/card/${cardId}`,
                method: 'put',
                data,
            });
            return res.data;
        },

        // 终止卡项
        async stopCard(cardId, data) {
            const res = await fetch({
                url: `/api/v2/promotions/card/${cardId}/stop`,
                method: 'put',
                data,
            });
            return res?.data;
        },
    },
    /**
     * 删除会员卡类型
     */
    async deleteMemberCard(id) {
        // eslint-disable-next-line no-return-await
        return await fetch({
            url: `/api/v2/crm/patients/member/type/${id}`,
            method: 'delete',
        });
    },
    /**
     * 添加就会员卡类型
     */
    async addMemberCard(data) {
        // eslint-disable-next-line no-return-await
        return await fetch({
            url: '/api/v2/crm/patients/member/type',
            method: 'post',
            data,
        });
    },
    /**
     *
     */
    async updateMemberCard(data, id) {
        // eslint-disable-next-line no-return-await
        return await fetch({
            url: `/api/v2/crm/patients/member/type/${id}`,
            method: 'PUT',
            data,
        });
    },

    /**
     * [desc] 获取会员活动列表
     */

    async getMemberActivities(pageIndex, pageSize, withDetail) {
        const limit = pageSize;
        const offset = pageIndex * pageSize;
        const res = await fetch({
            url: `/api/v2/crm/patients/member/type/list?offset=${offset}&limit=${limit}&withDetail=${withDetail}`,
            method: 'get',
        });
        return res.data;
    },


    /**
     * 获得连锁的所有会员卡类型
     */
    async getAllMemberCardByChainId(pageIndex, pageSize) {
        const limit = pageSize;
        const offset = pageIndex * pageSize;
        const res = await fetch({
            url: `/api/v2/crm/patients/member/type/list?offset=${offset}&limit=${limit}`,
            method: 'get',
        });
        return res.data.data;
    },

    async getMemberCardTypeDetailList(params) {
        const {
            offset, limit, withDetail,
        } = params || {
            offset: 0,
            limit: 999,
            withDetail: 1,
        };
        const res = await fetch({
            url: '/api/v2/crm/patients/member/type/list',
            method: 'get',
            params: {
                offset,
                limit,
                withDetail,
            },
        });
        return res.data && res.data.data;
    },
    async getMemberTypeDetail(memberTypeId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/member/type/${memberTypeId}`,
            method: 'get',
        });
        return res.data && res.data.data;
    },
    async getTypeList(params) {
        const res = await fetch({
            url: '/api/v3/goods/sys/types/promotion',
            method: 'get',
            params,
        });
        return res.data;
    },
    async getPromotions(params) {
        const res = await fetch({
            url: '/api/v2/promotions/gifts',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data && res.data.data;
    },
    async getPromotionDetail(id) {
        const res = await fetch({
            url: `/api/v2/promotions/gifts/${id}`,
            method: 'get',
        });
        return res.data;
    },
    /**
     * @desc 活动商品冲突检查
     * <AUTHOR>
     * @date 2020/4/20
     */
    async checkPromotion(data) {
        const res = await fetch({
            url: '/api/v2/promotions/gifts/check',
            method: 'post',
            data,
        });
        return res.data;
    },
    async addPromotion(params) {
        const res = await fetch({
            url: '/api/v2/promotions/gifts',
            method: 'post',
            data: params,
        });
        return res.data;
    },
    // 满减满赠活动编辑
    async editPromotion(params) {
        const res = await fetch({
            url: `/api/v2/promotions/gifts/${params.id}`,
            method: 'put',
            data: params,
        });
        return res.data;
    },
    async finished(id) {
        const res = await fetch({
            url: `/api/v2/promotions/gifts/${id}/end`,
            method: 'put',
        });
        return res.data;
    },
    async delete(id) {
        const res = await fetch({
            url: `/api/v2/promotions/gifts/${id}`,
            method: 'delete',
        });
        return res.data;
    },
    async stopCoupon(id) {
        const res = await fetch({
            url: `/api/v2/promotions/coupons/${id}/end`,
            method: 'put',
        });
        return res.data && res.data.data;
    },
    async deleteCoupon(id) {
        const res = await fetch({
            url: `/api/v2/promotions/coupons/${id}`,
            method: 'delete',
        });
        return res.data && res.data.data;
    },
    // 获取优惠券列表
    async getCoupon(params) {
        const res = await fetch({
            url: '/api/v2/promotions/coupons',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data && res.data.data;
    },
    // 获取优惠券详情
    async getCouponDetail(id) {
        const res = await fetch({
            url: `/api/v2/promotions/coupons/${id}`,
            method: 'get',
        });
        return res.data && res.data.data;
    },
    // 新增优惠券
    async addCoupon(params) {
        const res = await fetch({
            url: '/api/v2/promotions/coupons',
            data: params,
            method: 'post',
        });
        return res.data && res.data.data;
    },
    // 修改优惠券
    async editCoupon(params) {
        const res = await fetch({
            url: `/api/v2/promotions/coupons/${params.id}`,
            data: params,
            method: 'put',
        });
        return res.data && res.data.data;
    },
    // 作废
    async invalidCoupon(id) {
        const res = await fetch({
            url: `/api/v2/promotions/coupons/${id}/invalid`,
            method: 'put',
        });
        return res.data && res.data.data;
    },
    // 查询优惠券配置
    async querySetting() {
        const res = await fetch({
            url: '/api/v2/property/chain?key=chainBasic.promotion.refundCoupon',
            method: 'get',
        });
        return res.data && res.data.data;
    },
    // 设置优惠券
    async addSetting(params) {
        const res = await fetch({
            url: '/api/v2/property/chain?key=chainBasic.promotion.refundCoupon',
            method: 'post',
            data: params,
        });
        return res.data && res.data.data;
    },

    //老带新
    referrer: {
        // 创建老带新活动
        async createReferrerActivity(data) {
            const res = await fetch({
                url: '/api/v2/promotions/referral',
                method: 'post',
                data,
            });
            return res?.data;
        },

        async getReferrerDetail(id) {
            const res = await fetch({
                url: `/api/v2/promotions/referral/${id}`,
                method: 'get',
            });
            return res?.data;
        },

        // 删除
        async delete(id) {
            const res = await fetch.delete(`/api/v2/promotions/referral/${id}`);
            return res?.data;
        },

        // 修改
        async updateReferrer(id, data) {
            const res = await fetch({
                url: `/api/v2/promotions/referral/${id}`,
                method: 'put',
                data,
            });
            return res?.data;
        },
        // 活动列表
        async referrerList(params) {
            const res = await fetch({
                url: '/api/v2/promotions/referral/page-list',
                method: 'get',
                params,
            });
            return res?.data;
        },
        // 查询患者当前奖励
        async fetchReferrerPatientReward(params) {
            const res = await fetch({
                url: '/api/v2/promotions/referral/patient/reward',
                method: 'get',
                params,
            });
            return res?.data;
        },
        // 查询是否存在活动
        async fetchReferrerPromotionExist(params) {
            const res = await fetch({
                url: '/api/v2/promotions/referral/referrer/exist-promotion',
                method: 'get',
                params,
            });
            return res?.data;
        },
        // 门店列表
        async clinicList(params) {
            const res = await fetch({
                url: '/api/v3/clinics/chain',
                method: 'get',
                params,
            });
            return res?.data;
        },
        // 根据不同场景生成数量不限制的小程序码
        async getReferrerQrCode(data) {
            const res = await fetch({
                url: '/api/v2/mc/qr-code/un-limit/wxa-code/image-bytes',
                method: 'post',
                data,
                responseType: 'blob',
            });
            return res?.data;
        },
        // 查询老带新优惠券列表
        async toObtainCoupons(data) {
            const res = await fetch({
                url: '/api/v2/promotions/coupons/referral/to-obtain',
                method: 'post',
                data,
            });
            return res?.data?.data?.rows;
        },
        // 终止活动
        async stopActivity(id, data) {
            const res = await fetch({
                url: `/api/v2/promotions/referral/${id}/stop`,
                method: 'put',
                data,
            });
            return res?.data;
        },

        // 获取预览数据
        async transPreview(data) {
            const res = await fetch({
                url: '/api/v2/promotions/referral/preview',
                method: 'post',
                data,
            });
            return res?.data;
        },

        // 老带新合计
        async referrerTotal(params) {
            const res = await fetch({
                url: '/api/v2/sc/stat/promotion/referral/list/total',
                method: 'get',
                params,
            });
            return res?.data;
        },

        async getClinicStaff(params) {
            const res = await fetch({
                url: '/api/v3/clinics/employees/list-by-clinic',
                method: 'get',
                params,
            });
            return res?.data;
        },

        // 查询消息是否已读
        async getRemindReadStatus(scene) {
            const res = await fetch({
                url: `/api/v3/clinics/reminder/${scene}/read`,
                method: 'get',
            });
            return res?.data;
        },

        // 更新消息标识
        async updateRemindFlog(scene, data) {
            const res = await fetch({
                url: `/api/v3/clinics/reminder/${scene}/read`,
                method: 'post',
                data,
            });
            return res?.data;
        },
    },

    // 促销活动
    salesPromotion: {
        // 创建活动
        async createPromotion(data) {
            const res = await fetch({
                url: '/api/v2/promotions/mains',
                method: 'post',
                data,
            });
            return res?.data;
        },
        // 修改活动
        async updatePromotion(id, data) {
            const res = await fetch({
                url: `/api/v2/promotions/mains/${id}`,
                method: 'put',
                data,
            });
            return res?.data;
        },
        // 删除活动
        async deletePromotion(id) {
            const res = await fetch({
                url: `/api/v2/promotions/mains/${id}`,
                method: 'delete',
            });
            return res?.data;
        },
        // 查询活动详情
        async getPromotionDetail(id) {
            const res = await fetch({
                url: `/api/v2/promotions/mains/${id}`,
                method: 'get',
            });
            return res?.data?.data;
        },
        // 终止活动
        async stopPromotion(id) {
            const res = await fetch({
                url: `/api/v2/promotions/mains/${id}/end`,
                method: 'put',
            });
            return res?.data;
        },

        // 促销活动列表
        async getPromotionList(params) {
            const res = await fetch({
                url: '/api/v2/promotions/mains',
                method: 'get',
                params,
            });
            return res?.data?.data;
        },

        // 促销活动列表-统计数据
        async getPromotionActivityStatistic(data) {
            const res = await fetch({
                url: '/api/v2/sc/stat/promotion/promotion/activity',
                method: 'post',
                data,
            });
            return res?.data;
        },
    },
    // 会员日
    memberDays: {
        // 会员活动-会员日
        async getMemberDayStatistic(data) {
            const res = await fetch({
                url: '/api/v2/sc/stat/promotion/member/day',
                method: 'post',
                data,
            });
            return res?.data;
        },

        // 会员活动-会员日列表
        async getMemberDayListStatistic(data) {
            const res = await fetch({
                url: '/api/v2/sc/stat/promotion/member/day/list',
                method: 'post',
                data,
            });
            return res?.data;
        },

        // 会员活动分页
        async getDatesPromotionMainId(promotionMainId, params) {
            const res = await fetch({
                url: `/api/v2/promotions/mains/${promotionMainId}/dates`,
                method: 'get',
                params,
            });
            return res?.data;
        },
    },
    // 商品组
    goodsGroup: {
        // 商品组列表
        async getGoodsGroupList(params) {
            const res = await fetch({
                url: '/api/v3/goods/goods-group',
                method: 'get',
                params,
            });
            return res?.data?.data;
        },
        // 商品组详情
        async getGoodsGroupDetail(goodsGroupId, params) {
            const res = await fetch({
                url: `/api/v3/goods/goods-group/${goodsGroupId}`,
                method: 'get',
                params,
                paramsSerializer (val) {
                    return Qs.stringify(val, { indices: false });
                },
            });
            return res?.data?.data;
        },
        // 商品组详情商品列表拉取
        async getGoodsGroupGoodsList(params) {
            const res = await fetch({
                url: '/api/v3/goods/goods-list/stock-goods',
                method: 'get',
                params: {
                    ...params,
                    e: 1,
                },
                paramsSerializer (value) {
                    return Qs.stringify(value, { arrayFormat: 'comma' });
                },
            });
            console.log('getGoodsGroupGoodsList', res);
            if (res.data && res.data.data && res.data.data.rows) {
                res.data.data.rows = await Promise.all(res.data.data.rows.map((item) => DecodeService.decodeAESObjectForKey(item, {
                    'name': true,
                    'medicineCadn': true,
                    'displaySpec': true,
                    'displayName': true,
                    'manufacturer': true,
                    'manufacturerFull': true,
                    'medcineNpmn': true,
                    'medicineNpmn': true,
                    'shebaoNationalView': {
                        'shebaoCode': true,
                    },
                })));
            }
            return res.data;
        },
        // 创建商品组
        async createGoodsGroup(data) {
            const res = await fetch({
                url: '/api/v3/goods/goods-group',
                method: 'post',
                data,
            });
            return res?.data;
        },
        // 修改商品组
        async updateGoodsGroup(goodsGroupId, data) {
            const res = await fetch({
                url: `/api/v3/goods/goods-group/${goodsGroupId}`,
                method: 'put',
                data,
            });
            return res?.data;
        },
        // 删除商品组
        async deleteGoodsGroup(goodsGroupId) {
            const res = await fetch({
                url: `/api/v3/goods/goods-group/${goodsGroupId}`,
                method: 'delete',
            });
            return res?.data;
        },
    },
};
