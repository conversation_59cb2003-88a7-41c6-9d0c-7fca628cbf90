@import 'styles/abc-common.scss';

.outpatient-post-data-form {
    .patient-info {
        display: flex;

        .arrears {
            padding: 8px;
            margin-left: 6px;
            color: #ff3333;
            cursor: pointer;
            background-color: #ffebeb;
            border-radius: var(--abc-border-radius-small);
        }
    }
}

.registration-compose-children-wrapper {
    width: 452px;

    .header-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
    }

    .compose-children-wrapper {
        .item {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            color: $T2;

            & + .item {
                margin-top: 8px;
            }

            .text {
                & + .text {
                    margin-left: 25px;
                }
            }

            .name {
                width: 98px;

                @include ellipsis;
            }

            .unit {
                width: 40px;

                @include ellipsis;
            }

            .shebao-info {
                flex: 1;

                @include ellipsis;
            }
        }
    }
}
