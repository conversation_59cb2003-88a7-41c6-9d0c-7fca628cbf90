<template>
    <div
        v-abc-loading:page="loading"
        :class="{ 'order-is-charged': isCharged }"
        data-cy="charge-main-center"
        class="main-content-wrapper"
    >
        <abc-container-center-top-head class="cashier-center-top-head">
            <h2>{{ hTitle }}</h2>
            <abc-text
                v-if="recordCount && !isCharged"
                tag="div"
                size="mini"
                theme="success-light"
                style="margin-left: 16px;"
            >
                {{ recordCount }}
            </abc-text>

            <div class="buttons-wrapper">
                <!--<transition name="stamp" mode="out-in">-->
                <img
                    v-if="!isHospitalSheet && chargeStatus >= ChargeStatusEnum.CHARGED && chargeStatus < ChargeStatusEnum.REFUND"
                    class="charge-seal"
                    src="~assets/images/Paid.png"
                    alt=""
                />
                <img
                    v-if="chargeStatus === ChargeStatusEnum.REFUND"
                    class="charge-seal"
                    src="~assets/images/icon-money-back.png"
                    alt=""
                />
                <img
                    v-if="chargeStatus === ChargeStatusEnum.CLOSED"
                    class="charge-seal"
                    src="~assets/images/icon/<EMAIL>"
                    alt=""
                />
                <!--</transition>-->
                <div class="amount" style="margin-right: 8px;">
                    <span
                        v-if="chargeStatus !== ChargeStatusEnum.UN_CHARGE || !!lockedInfo"
                        :class="{
                            gray: chargeStatus === ChargeStatusEnum.REFUND,
                            black: showBlackColor,
                        }"
                    >
                        <abc-money
                            style="font-weight: bold; line-height: 28px;"
                            data-cy="charge-retail-amount-money"
                            :value="topHeaderFee.fee || 0"
                            :symbol-icon-size="16"
                            :symbol-style="{
                                marginRight: '3px',
                                position: 'relative',
                                top: '1px',
                            }"
                        ></abc-money>
                    </span>
                    <charge-detail-popover
                        v-else
                        v-model="showChargeDetailPopover"
                        :summary="chargeSheetSummary"
                        :post-data="postData"
                        :loading="calcSheBaoLoading"
                        @adjustmentTotalFee="onAdjustmentTotalFee"
                    >
                        <span
                            class="reference-price-info"
                            data-cy="charge-form-total-price"
                            @click="onClickChargeDetail"
                        >
                            <span
                                :class="{
                                    gray: chargeStatus === ChargeStatusEnum.REFUND,
                                    black: showBlackColor,
                                }"
                            >
                                <abc-money
                                    style=" margin-right: 2px; font-weight: bold; line-height: 28px;"
                                    :value="topHeaderFee.fee || 0"
                                    :symbol-icon-size="16"
                                    :symbol-style="{
                                        marginRight: '3px',
                                        position: 'relative',
                                        top: '1px',
                                    }"
                                ></abc-money>
                            </span>
                            <abc-icon
                                :icon="showChargeDetailPopover ? 's-up-line-medium' : 's-dowline-medium'"
                                :size="16"
                                color="var(--abc-color-T3)"
                            ></abc-icon>
                        </span>
                    </charge-detail-popover>
                </div>

                <abc-button
                    v-if="chargeStatus === ChargeStatusEnum.CLOSED"
                    variant="ghost"
                    :disabled="!!isDisabledOperate"
                    style="margin: 0 2px;"
                    data-cy="charge-retail-recharge-button"
                    @click="openCloseOrderHandle"
                >
                    重新收费
                </abc-button>
                <template v-else-if="chargeStatus === ChargeStatusEnum.UN_CHARGE">
                    <abc-tooltip :content="payDisabledObj.desc" :disabled="!payDisabledObj.disabled">
                        <div style="margin: 0 2px;">
                            <abc-check-access>
                                <abc-button
                                    title="F9"
                                    :disabled="payDisabledObj.disabled"
                                    :loading="inputCalcLoading || buttonLoading"
                                    data-cy="charge-retail-confirm-button"
                                    @click="submit"
                                >
                                    收费
                                </abc-button>
                            </abc-check-access>
                        </div>
                    </abc-tooltip>
                </template>
                <template v-else-if="chargeStatus === ChargeStatusEnum.PART_CHARGED">
                    <abc-check-access>
                        <abc-button
                            :disabled="!!lockedInfo"
                            style="margin: 0 2px;"
                            data-cy="charge-retail-continue-button"
                            @click="continuePay"
                        >
                            继续收费
                        </abc-button>
                    </abc-check-access>
                    <abc-tooltip
                        v-if="chargeSheetSummary.netIncomeFee"
                        :content="disabledRefundReason"
                        :disabled="!disabledRefundReason"
                    >
                        <div style="margin: 0 2px;">
                            <abc-check-access>
                                <abc-button
                                    :disabled="refundDisabled"
                                    variant="ghost"
                                    data-cy="charge-retail-refund-button"
                                    @click="refundFee"
                                >
                                    退费
                                </abc-button>
                            </abc-check-access>
                        </div>
                    </abc-tooltip>
                </template>
                <template v-else-if="chargeStatus === ChargeStatusEnum.REFUND">
                    <!--在线支付不允许重新收费-->
                    <abc-tooltip
                        v-if="chargeSheetType !== ChargeSheetTypeEnum.ONLINE_CONSULTATION"
                        :content="disabledOperateReason"
                        :disabled="!disabledOperateReason"
                    >
                        <div style="margin: 0 2px;">
                            <abc-check-access>
                                <abc-button
                                    :disabled="buttonLoading || !!isDisabledOperate"
                                    variant="ghost"
                                    data-cy="charge-retail-recharge-button"
                                    @click="clickAgainCharge"
                                >
                                    重新收费
                                </abc-button>
                            </abc-check-access>
                        </div>
                    </abc-tooltip>
                </template>
                <template v-else>
                    <abc-check-access>
                        <abc-button
                            v-if="showRepaymentBtn"
                            :loading="repaymentLoading"
                            style="margin: 0 2px;"
                            data-cy="charge-retail-repayment-button"
                            @click="handleRepayment()"
                        >
                            还款
                        </abc-button>
                    </abc-check-access>

                    <abc-tooltip
                        v-if="showRefundBtn"
                        :content="disabledRefundReason"
                        :disabled="!disabledRefundReason"
                    >
                        <div style="margin: 0 2px;">
                            <abc-check-access>
                                <abc-button
                                    :disabled="refundDisabled"
                                    variant="ghost"
                                    data-cy="charge-retail-refund-button"
                                    @click="showRefundDialog"
                                >
                                    退费
                                </abc-button>
                            </abc-check-access>
                        </div>
                    </abc-tooltip>
                </template>

                <!--锁单不能操作-->
                <template v-if="!lockOrder">
                    <abc-check-access>
                        <abc-button
                            v-if="chargeStatus !== ChargeStatusEnum.REFUND && isUpdate"
                            :loading="(!loading && inputCalcLoading) || saveBtnLoading"
                            variant="ghost"
                            style="margin: 0 2px;"
                            data-cy="charge-retail-save-button"
                            @click="saveOrder"
                        >
                            保存
                        </abc-button>
                    </abc-check-access>

                    <template v-if="chargeStatus < ChargeStatusEnum.PART_CHARGED && !isReplay">
                        <abc-check-access>
                            <abc-button
                                v-if="canDeleteServerDraft"
                                variant="ghost"
                                style="margin: 0 2px;"
                                data-cy="charge-retail-delete-button"
                                @click="deleteServerDraft"
                            >
                                删除
                            </abc-button>
                            <abc-button
                                v-else
                                :loading="buttonLoading"
                                variant="ghost"
                                style="margin: 0 2px;"
                                data-cy="charge-retail-close-button"
                                @click="closeOrderHandle"
                            >
                                关闭
                            </abc-button>
                        </abc-check-access>
                    </template>

                    <template v-if="canSendOrder && !isIntranetUser">
                        <abc-check-access>
                            <abc-button
                                v-if="isUpdate"
                                variant="ghost"
                                style="margin: 0 2px;"
                                data-cy="charge-retail-send-pay-button"
                                @click="onBeforePushClick"
                            >
                                推送支付
                            </abc-button>
                            <push-payment-popper
                                v-else
                                style="margin: 0 2px;"
                                :source-type="3"
                                :no-charge-forms="noChargeForms"
                                :patient="postData.patient"
                                :patient-order-id="patientOrderId"
                                :charge-sheet-id="chargeSheetId"
                                @update-patient-info="handleUpdateInfo"
                                @payment-success="fetchDetail"
                                @push-success="fetchDetail"
                            >
                            </push-payment-popper>
                        </abc-check-access>
                    </template>
                </template>

                <!--零售开单未收费的情况下不能打印-->
                <print-popper
                    v-if="!(chargeSheetType === ChargeSheetTypeEnum.DIRECT_SALE && chargeStatus === 0)"
                    v-model="printOpt.printSelect"
                    size="small"
                    :width="64"
                    style="margin: 0 2px;"
                    placement="bottom"
                    data-cy="charge-retail-print"
                    :options="printOptions"
                    :box-style="{
                        width: '128px', right: 0
                    }"
                    @print="printHandler"
                    @select-print-setting="openPrintConfigSettingDialog"
                >
                </print-popper>

                <abc-button
                    slot="reference"
                    variant="ghost"
                    style="margin: 0 2px;"
                    class="app-printer-config-button"
                    icon="s-b-settings-line"
                    @click="handleClickSettings"
                >
                </abc-button>
            </div>
        </abc-container-center-top-head>

        <abc-container-center-main-content class="cashier-main-content">
            <div
                class="charge-form-wrapper"
                :class="chargeStatus !== ChargeStatusEnum.UN_CHARGE ? 'charge-detail-wrapper' : ''"
            >
                <abc-tips-card-v2
                    v-if="asyncPayException"
                    class="pay-exception-tips-card"
                    theme="warning"
                    align="center"
                >
                    收费单收费异常，请及时处理
                    <template #operate>
                        <abc-button
                            variant="text"
                            size="small"
                            @click="handleClickException"
                        >
                            处理
                        </abc-button>
                    </template>
                </abc-tips-card-v2>

                <abc-tips-card-v2
                    v-if="consultLockStatus"
                    theme="warning"
                    class="pay-exception-tips-card"
                    align="center"
                >
                    {{ consultLock.consultLockUserName }}正在调整咨询项目，请稍后再进行操作
                </abc-tips-card-v2>
                <abc-tips-card-v2
                    v-if="lockedInfo"
                    theme="warning"
                    class="pay-exception-tips-card"
                    align="center"
                >
                    {{ lockedTips }}
                    <template v-if="showCancelPay" #operate>
                        <abc-button
                            variant="text"
                            size="small"
                            @click="onClickCancelPay"
                        >
                            支付遇到问题？
                        </abc-button>
                    </template>
                </abc-tips-card-v2>

                <abc-form ref="postDataForm">
                    <!--开单收费，重新收费直接调用 开单收费组件-->
                    <retail-post-data-form
                        v-if="useRetailPostDataForm"
                        :post-data="postData"
                        :hospital-info="hospitalInfo"
                        :hidden-seller="
                            (chargeStatus === ChargeStatusEnum.UN_CHARGE && outpatientStatus === 1) || isChargeCopyWrite
                        "
                        :outpatient-status="outpatientStatus"
                        :is-replay="isReplay"
                        :loading="loading"
                        :charge-sheet-type="chargeSheetType"
                        :is-server-draft="isServerDraft"
                        :charge-sheet-id="chargeSheetId"
                        :input-calc-loading.sync="inputCalcLoading"
                        :close-switch="closeSwitch"
                        :disabled-keyboard="startCharging"
                        :charge-sheet-summary="chargeSheetSummary"
                        :registration-info="registrationInfo"
                        :clone-prescription-type="clonePrescriptionType"
                        :clone-prescription-info="clonePrescriptionInfo"
                        :attachments="attachments"
                        :send-to-patient-status="sendToPatientStatus"
                        :disabled-edit-order="chargeSheetType === ChargeSheetTypeEnum.FAMILY_DOCTOR_SIGN"
                        :disabled-scan-barcode="disabledScanBarcode || startCharging"
                        :readonly="lockOrder"
                        @fetch="fetchDetail"
                        @submit="submit"
                        @change-visit-source="changeVisitSource"
                    ></retail-post-data-form>

                    <!--门诊收费列表-->
                    <outpatient-post-data-form
                        v-else
                        :loading="loading"
                        :is-replay="isReplay"
                        :dispensing-to-home="isToHomeSwitch"
                        :patient.sync="postData.patient"
                        :post-data="postData"
                        :hospital-info="hospitalInfo"
                        :status="chargeStatus"
                        :invoice-status="invoiceStatus"
                        :invoice-data="invoiceData"
                        :charge-sheet-type="chargeSheetType"
                        :close-switch="closeSwitch"
                        :disabled-keyboard="startCharging"
                        :input-calc-loading="inputCalcLoading"
                        :charge-sheet-summary="chargeSheetSummary"
                        :charge-transactions.sync="chargeTransactions"
                        :charge-actions.sync="chargeActions"
                        :member-discount-info="memberDiscountInfo"
                        :charge-forms="postData.chargeForms"
                        :registration-info="registrationInfo"
                        :clone-prescription-type="clonePrescriptionType"
                        :clone-prescription-info="clonePrescriptionInfo"
                        :attachments="attachments"
                        :patient-order-id="patientOrderId"
                        :refresh-charge-notice-detail="refreshChargeNoticeDetail"
                        :is-can-be-update-air-pharmacy-medical-record="isCanBeUpdateAirPharmacyMedicalRecord"
                        :readonly="lockOrder"
                        :is-disabled-operate="isDisabledOperate"
                        :change-pay-mode-records="changePayModeRecords"
                        :disabled-operate-reason="disabledOperateReason"
                        :is-can-see-patient-mobile="isCanSeePatientMobileInCashier"
                        :invoice-list="invoiceList"
                        :disposals="disposals"
                        :disabled-scan-barcode="disabledScanBarcode || startCharging"
                        :is-whole-bill-charge="isWholeBillCharge"
                        @pay-mode-change-success="fetchDetail"
                        @fetch="fetchDetail"
                        @submit="submit"
                        @change="changeHandler"
                        @repayment="handleRepayment"
                        @change-visit-source="changeVisitSource"
                    ></outpatient-post-data-form>
                </abc-form>

                <div class="outpatient-bottom-info">
                    <div v-if="diagnosedDate" class="diagnosed-date">
                        <div class="label">
                            就诊时间：
                        </div>
                        <div>
                            {{ diagnosedDate | parseTime('y-m-d h:i', true) }}
                        </div>
                    </div>
                </div>
            </div>
        </abc-container-center-main-content>

        <refund-dialog
            v-if="showRefund"
            :id="$route.params.id"
            ref="refundPro"
            v-model="showRefund"
            :member-id="postData.memberId"
            :patient-id="postData.patient.id"
            :forms="postData.chargeForms"
            :discount-fee="chargeSheetSummary.discountFee"
            :net-income-fee="chargeSheetSummary.netIncomeFee"
            :owed-refund-fee="chargeSheetSummary.owedRefundFee"
            :adjustment-fee="chargeSheetSummary.adjustmentFee"
            :net-adjustment-fee="chargeSheetSummary.netAdjustmentFee"
            :disabled-total="isWholeBillCharge || isHospitalSheet"
            :refund-auto-destroy="!!writeInvoiceConfig ? writeInvoiceConfig.refundAutoDestroy : 0"
            :invoice-status-flag="invoiceStatusFlag"
            @confirm="refundConfirm"
        ></refund-dialog>

        <refund-way-dialog
            v-if="showRefundWayList"
            ref="refundWay"
            v-model="showRefundWayList"
            :patient-name="postData.patient.name"
            :charge-sheet-id="$route.params.id"
            :refund-fee="refundTotalFee"
            :refund-data="refundData"
            :receivable-fee="chargeSheetSummary.receivableFee"
            :net-income-fee="chargeSheetSummary.netIncomeFee"
            :refund-type="curRefundType"
            :payment-summary-infos.sync="chargeSheetSummary.paymentSummaryInfos"
            :charge-transactions="chargeTransactions"
            :charge-config="chargeConfig"
            :is-hospital-sheet="isHospitalSheet"
            :disabled-refund-way-tab="isHospitalSheet || isContentOutpatientCenterPay || isPrescriptionOut"
            :owed-status="hospitalOwedStatus"
            @input="onChangeValue"
            @refund="refundHandler"
            @finish="refundFinish"
            @auto-destroy-invoice="autoDestroyInvoice"
            @re-open-audit="reOpenAuditFlow"
        ></refund-way-dialog>

        <!--标签打印-->
        <select-print-dialog
            v-if="showSelectPrint"
            v-model="showSelectPrint"
            :tag-forms="tagForms"
            :is-direct="chargeSheetType === ChargeSheetTypeEnum.DIRECT_SALE"
            type="cashier"
            @confirm="selectPrintConfirm"
        ></select-print-dialog>

        <send-order-dialog
            v-if="showSendOrderDialog"
            v-model="showSendOrderDialog"
            :charge-sheet-summary="chargeSheetSummary"
            :status="chargeStatus"
            :charge-sheet-id="chargeSheetId"
            :is-replay="isReplay"
            :loading="inputCalcLoading"
            :post-data="postData"
            @sendSuccess="chargeSuccess"
            @refresh="refreshHandler"
        ></send-order-dialog>

        <!--多个还款单据-->
        <repayment-list-dialog
            v-if="showRepaymentList"
            v-model="showRepaymentList"
            :charge-sheet-id="chargeSheetId"
            :charge-owe-sheets="chargeOweSheets"
            :is-checked-repay="isCheckedRepay"
            @updateChargeOweSheets="updateChargeOweSheets"
        ></repayment-list-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import RetailPostDataForm from
    '@/views-cloud-examination/cashier/components/retail-post-data-form/index.vue';
    import OutpatientPostDataForm from
    '@/views-cloud-examination/cashier/components/outpatient-post-data-form/outpatient-post-data-form.vue';

    const RefundDialog = () => import('@/views/cashier/refund-dialog.vue');
    const RefundWayDialog = () => import('@/views/cashier/refund-way-dialog.vue');
    import SelectPrintDialog from '@/views/cashier/select-print-dialog.vue';
    import PrintPopper from 'views/print/popper';
    import SendOrderDialog from '@/views/cashier/components/send-order-dialog/send-order-dialog';
    import ChargeNoticeDialog from '@/views/cashier/components/charge-notice-dialog';
    import InvoiceDialog from '@/views/cashier/invoice/index.js';
    import RepaymentListDialog from '@/views/cashier/repayment-list-dialog.vue';
    import ChargeDetailPopover from '@/views/cashier/components/charge-detail-popover.vue';
    import PushPaymentPopper from 'views/common/components/push-payment.vue';

    // API
    import ChargeAPI from 'api/charge';
    import SocialAPI from 'api/social';
    import TpsAPI from 'api/tps';
    import HospitalAPI from 'api/hospital/index.js';
    import PrintAPI from 'api/print';
    import CrmAPI from 'api/crm';
    import InvoiceAPI from 'api/invoice/index.js';
    import {
        createGUID, numToChinese,
    } from '@/utils/index';
    import inputSelect from 'views/common/input-select';
    // utils
    import {
        debounce, isEqual,
    } from 'utils/lodash';
    import {
        mapActions, mapGetters,mapState,
    } from 'vuex';
    import clone from 'utils/clone';
    import {
        formatMoney, isChineseMedicine,
    } from 'src/filters/index';
    import {
        isDisabledGoods, isShortage,
    } from 'utils/validate';
    import Printer from 'views/print';
    import {
        chargeIsRetail,
        getTraceCodeChargeItems,
        getTraceCodeChargedItems,
        getWarnChargeItemGroup,
        getTraceCodeDispensingItems, getTraceCodeDispensedItems,
    } from 'views/cashier/utils';
    import AbcSocket from 'views/common/single-socket.js';

    import {
        off, on,
    } from 'utils/dom';
    import { getAbcPrintOptions } from '@/printer/print-handler';
    import { loadAbcPrint } from '@/printer/print-init/index.js';

    import {
        GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import {
        ChargeItemStatusEnum,
        ChargeSheetInvoiceStatus,
        ChargeSheetTypeEnum,
        ChargeStatusEnum,
        ChargeType,
        CreateCashierPostData,
        OwedChargeStatusEnum,
        RefundTypeEnum,
        SourceFormTypeEnum,

        UseMemberFlagEnum,
    } from '@/service/charge/constants';

    import diagnosisSocialCodeHandle from 'src/views/common/diagnosis-social-code-handle';
    import {
        InvoiceBusinessScene, InvoiceCategory, InvoiceSupplierId, InvoiceViewType, PaperInvoiceType,
    } from 'views/cashier/invoice/constants.js';
    import AbcChargeService from '@/service/charge';
    import AbcChargeDialog from '@/service/charge/components/dialog-charge';
    import AbcAbnormalHandleDialog from '@/service/charge/components/dialog-abnormal-handle';
    import CashierSettingsDialog from '@/views-cloud-examination/cashier/components/cashier-settings-dialog/index';
    import { PayModeEnum } from '@/service/charge/constants.js';
    import AbcPrinter from '@/printer/index.js';
    import DischargeSettlementDialog from 'views/hospital/discharge-settlement/index.js';
    import SettleErrorDialog from 'views/hospital/settle-error-dialog/index.js';
    import {
        HospitalSheetChargeStatus, OutpatientHospitalStatusEnum,
    } from 'views/hospital/constants';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import { ABCPrintConfigKeyMap } from '@/printer/constants';
    import AbcAccess from '@/access/utils.js';
    import successSubmitHandle from '@/views/cashier/mixins/success-submit-handle';
    import { sleep } from 'utils/delay';
    import InvoiceService from 'views/cashier/invoice/write-invoice-core-v2/invoice-service';
    import localStorage from 'utils/localStorage-handler';
    import { autoDestroyInvoice } from 'views/cashier/invoice/utils';
    import QRCode from 'qrcode';
    import OutpatientAPI from 'api/outpatient';
    import { getOrigin } from 'views/settings/micro-clinic/decoration/config';
    import ShortUrlAPI from 'api/short-url';
    import { red } from '@/utils/math.js';
    import Logger from 'utils/logger';
    import i18n from '@/i18n';
    import CollectionTraceCodeDialog from '@/service/trace-code/dialog-collection-trace-code';
    import { CollectionTraceCodeCheck } from 'views/settings/trace-code/constants';
    import TraceCode, {
        SceneTypeEnum,
    } from '@/service/trace-code/service';
    import {
        dispenseStatusEnum,
    } from 'views/pharmacy/constants';
    import ABCPrinterConfig from '@/printer/config';
    import { pdfLodopPrint } from '@/printer/utils';
    import { SelectPrintFunctionalDialog } from 'views/cashier/select-print-dialog';
    import {
        ChargeBusinessSceneEnum,
        LockBusinessKeyEnum,
    } from '@/common/constants/business-lock';
    import useChargeLock from 'views/cashier/hooks/useChargeLock';
    import { navigateToInvoiceConfig } from '@/core/navigate-helper';
    import { matchTemplate } from 'views/settings/print-config/medical-bills/receipt/index.vue';
    import {
        PrintCountBusinessTypeEnum,
    } from 'views/cashier/constants';
    import SimpleEventBus from 'views/cashier/meanwhile-print-manager/simple-event-bus';
    import Qs from 'qs';
    import { useCePayRule } from '@/views-cloud-examination/hook/use-ce-pay-rule';

    const PrecriptionPrintType = Object.freeze({
        NOMAL_PR: 0, // 一般处方
        IMG_PR: 1, // 拍照续方
        HISTORY_PR: 2, // 历史续方
    });
    const comparePatientKey = [
        'age',
        'id',
        'mobile',
        'name',
        'sex',
    ];

    const compareAddressKey = [
        'addressCityId',
        'addressDetail',
        'addressDistrictId',
        'addressProvinceId',
        'deliveryMobile',
        'deliveryName',
        'deliveryCompany',
        'deliveryFee',
        'deliveryOrderNo',
        'deliveryPayType',
    ];
    const compareProcessKey = [
        'type',
        'subType',
        'processBagUnitCount',
    ];

    export default {
        components: {
            RetailPostDataForm,
            OutpatientPostDataForm,

            RefundDialog,
            RefundWayDialog,
            PrintPopper,

            SelectPrintDialog,
            SendOrderDialog,

            RepaymentListDialog,
            ChargeDetailPopover,
            PushPaymentPopper,
        },
        mixins: [
            inputSelect,
            diagnosisSocialCodeHandle,
            successSubmitHandle,
        ],
        beforeRouteUpdate (to, from, next) {
            this.updatedRouteConfirm(next);
        },
        beforeRouteLeave(to, from, next) {
            this.updatedRouteConfirm(next);
        },
        setup() {
            const {
                lockedInfo,
                lockedTips,
                showCancelPay,
                queryExceptionType,
                socialPayExceptionInfo,
                socialExceptionRefundLoading,
                onConfirmCancelPay,
                getLockInfo,
                setLockInfo,
                setQueryExceptionType,
                getSocialExceptionInfo,
                handleClickSocialRefund,
            } = useChargeLock();

            const {
                fetchCePayRule,
            } = useCePayRule();

            return {
                lockedInfo,
                lockedTips,
                showCancelPay,
                queryExceptionType,
                socialPayExceptionInfo,
                socialExceptionRefundLoading,
                onConfirmCancelPay,
                getLockInfo,
                setLockInfo,
                setQueryExceptionType,
                getSocialExceptionInfo,
                handleClickSocialRefund,

                fetchCePayRule,
            };
        },
        data() {
            return {
                ChargeStatusEnum,
                ChargeItemStatusEnum,
                ChargeSheetTypeEnum,
                ChargeSheetInvoiceStatus,
                OwedChargeStatusEnum,

                isUpdate: false,
                loading: false,
                saveBtnLoading: false,
                buttonLoading: false,
                calcLoading: false,
                inputCalcLoading: false,
                closeSwitch: false,

                chargeStatus: ChargeStatusEnum.UN_CHARGE,
                chargeSheetType: '',
                chargeSheetId: '',
                chargeSheetSummary: {},
                chargeActions: [],
                chargeTransactions: [],
                memberDiscountInfo: null,

                postData: CreateCashierPostData(),

                startCharging: false,
                dialogVisible: false,
                showRefund: false,
                showRefundWayList: false,
                showSelectPrint: false,

                medicalRecordId: '',
                outpatientStatus: 0, // 0 无门诊单 1待诊 2已诊 3门诊草稿
                copyWriteSource: 0, // 代录来源
                isToHomeSwitch: 0, // 是否开启送药上门
                sendToPatientStatus: 0, // 是否已经给用户推送过
                dispensingStatus: 0,
                hospitalOwedStatus: 0, // 住院欠费状态
                invoiceStatus: 0, // 发票状态
                invoiceStatusFlag: 0, // 发票状态标识
                invoiceData: {}, // 发票内容
                invoiceList: [], // 发票列表
                hasMember: false,

                shortageMedicines: [],

                printOpt: {
                    printSelect: '',
                    finishSelect: [],
                },
                shebaoSettlePrintSheetId: '',

                isFirstPrint: true,
                printLoading: false,
                printPR: false,
                printData: {},
                printable: {
                    chargeSheet: {},
                    refundChargeSheet: {},
                    dispensingSheet: {},
                    undispensingSheet: {},
                    prescription: {},
                    executeInfusionSheet: {},
                    executeTreatmentSheet: {},
                    medicineTag: {},
                    patientTag: {},
                    executeTransfusionSheet: {},
                    executeTherapySheet: {},
                    examination: {},
                    examinationExamination: {},
                    examinationInspection: {},
                    examinationExaminationBarCode: {},
                    receipt: {},
                },
                executedPrintData: {},
                executedType: 1,

                isFirstPrintAdviceTag: true,
                printAdviceTagLoading: false,


                printMedicalBillLoading: false, // 医疗票据
                isFirstPrintMedicalBill: true,

                printMedicalFeeLoading: false, // 医疗清单
                isFirstPrintMedicalFee: true,

                isFirstPrintDispensing: true,
                printDispensingLoading: false,

                curRefundType: '', // 当前退费类型
                refundTotalFee: 0, // 需要退的费用
                refundData: {}, // 项目退费时，{chargeForms, refundFee, needRefundFee}

                isReplay: false, // 是否是重新收费的单子

                itemCache: '',
                allItems: [],
                isDraft: 0,

                showSendOrderDialog: false,
                visibleSocialInfo: false,

                isOnline: 0, // 是否网诊收费
                registrationInfo: null,
                attachments: [],

                /**
                 * @desc 续方
                 * <AUTHOR>
                 * @date 2020/05/27 16:05:51
                 */
                clonePrescriptionType: 0,
                clonePrescriptionInfo: {
                    doseCount: 0,
                    remarks: '',
                    snapshot: {},
                },

                isCanBeUpdateAirPharmacyMedicalRecord: 0,

                // 收费不能支付，不能支付的原因
                isDisabledPay: 0,
                disabledPayReason: '',
                // 不能退费，不能退费的原因
                isDisabledRefund: 0,
                disabledRefundReason: '',
                // 收费单不能操作（优先级高于 不能支付，不能退费）
                isDisabledOperate: 0,
                // 收费单不能操作的原因
                disabledOperateReason: '',

                // 收费告知书
                patientOrderId: '',
                refreshChargeNoticeDetail: false,

                // 长护住院信息
                hospitalInfo: null,
                // 欠费收费
                showRepaymentList: false,
                repaymentLoading: false,
                chargeOweSheets: [],
                isCheckedRepay: true, // 默认勾选当前还款单
                seletedChargeOweSheets: [],


                // 咨询单锁单
                consultLock: {
                    // 咨询单锁单标识
                    consultLockIdentity: '',
                    // 咨询单锁单人id
                    consultLockUserId: '',
                    // 咨询单编辑锁单人
                    consultLockUserName: '',
                    consultLockPatientOrderId: '',
                },

                diagnosedDate: '', // 诊断时间
                airPharmacyOrderId: '',
                changePayModeRecords: [],
                disposals: '',
                showChargeDetailPopover: false,
                calcSheBaoLoading: false,
                disabledScanBarcode: false,
            };
        },
        computed: {
            ...mapState('socialPc', [
                'basicInfo', // 配置基本信息
            ]),
            ...mapGetters([
                'currentClinic',
                'cashier',
                'draftCashierNews',
                'draftCashiers',
                'printConfig',
                'clinicConfig',
                'goodsConfig',
                'printPrescriptionConfig',
                'printDispensingConfig',
                'printCashierConfig',
                'printBillConfig',
                'printMedicalListConfig',
                'westernMedicineConfig',
                'isOpenMp',
                'chargeConfig',
                'weChatPayConfig',
                'needCheckStock',
                'disableNoStockGoods',
                'pharmacyList',
                'modulePermission',
                'isCanSeePatientMobileInCashier',
                'userInfo',
                'traceCodeConfig',
                'dispensingConfig',
                'isCanSeeGoodsCostPriceInCashier',
                'printReceiptConfig',
                'isIntranetUser',
                'clinicBasic',
                'chainBasic',
            ]),
            ...mapGetters('airPharmacy', ['systemConfig']),
            ...mapGetters('invoice', ['isOpenInvoice', 'isOpenMedicalInvoice', 'medicalElectronicAPIConfig', 'writeInvoiceConfig', 'invoiceConfigList']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig', 'featureSupportFilterEyeGlasses']),
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),
            // 追溯码提醒
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            needTraceCodeFormItems() {
                if (this.isCharged) {
                    return getTraceCodeChargedItems(this.postData.chargeForms);
                }
                return getTraceCodeChargeItems(this.postData.chargeForms);
            },
            // 未发药追溯码
            traceCodeDispenseItems() {
                return getTraceCodeDispensingItems(this.needTraceCodeFormItems);
            },
            // 已发药追溯码
            traceCodeDispensedItems() {
                const {
                    formItems,returnedItems,
                } = getTraceCodeDispensedItems(this.needTraceCodeFormItems);
                return [...formItems,...returnedItems];
            },
            selectedPatient() {
                return this.cashier.selectedPatient;
            },
            isGuangZhou() {
                return this.$abcSocialSecurity.isOpenSocial && this.$abcSocialSecurity.config.isGuangdongGuangzhou;
            },
            recordCount() {
                if (!this.isGuangZhou) return '';
                const strArr = [];
                const { shebaoCardInfo } = this.selectedPatient || {};
                const { extend } = shebaoCardInfo || {};
                const { annualFundPay } = extend || {};
                if (Number(annualFundPay ?? 0)) {
                    const feeStr = `本年统筹累计：${i18n.t('currencySymbol')}${annualFundPay}`;
                    strArr.push(feeStr);
                }
                return strArr.join('，');
            },
            // 咨询收费单是否被锁单
            consultLockStatus() {
                return !!(
                    this.consultLock.consultLockPatientOrderId === this.patientOrderId &&
                    this.consultLock.consultLockIdentity &&
                    this.consultLock.consultLockUserId !== this.userInfo.id
                );
            },
            lockOrder() {
                return this.chargeStatus === ChargeStatusEnum.CLOSED || !!this.lockedInfo || this.consultLockStatus;
            },
            showBlackColor() {
                return [
                    ChargeStatusEnum.CHARGED,
                    ChargeStatusEnum.PART_REFUND,
                ].includes(this.chargeStatus) &&
                    this.hospitalOwedStatus === OwedChargeStatusEnum.NO_OWED;
            },

            /**
             * @desc 诊所是否开启了自助支付（微信支付开通，jsapi开通，微诊所开通，设置开通）
             * <AUTHOR> Yang
             * @date 2020-09-03 10:01:30
             */
            autoSendOrderInfoSwitch() {
                return this.weChatPayConfig.weChatPaySwitch === 2 &&
                    this.weChatPayConfig.jsapiPayStatus === 2 &&
                    this.isOpenMp &&
                    this.chargeConfig.autoSendOrderInfoSwitch;
            },
            // 是否为整单收费模式
            isWholeBillCharge() {
                return !!this.chargeConfig.wholeSheetOperateEnable;
            },

            // 标题显示
            hTitle() {
                const { chargeTitleUnify } = this.viewDistributeConfig.Cashier;
                if (chargeTitleUnify) return '收费单';
                if (this.showHospitalDischargeBtn) {
                    return '长护欠费';
                }
                if (this.chargeSheetType === ChargeSheetTypeEnum.CLONE_PRESCRIPTION) {
                    return '续方收费';
                }
                if (this.chargeSheetType === ChargeSheetTypeEnum.FAMILY_DOCTOR_SIGN) {
                    return '家庭医生签约费';
                }
                if (this.isOnline) {
                    return '网诊收费';
                }
                if (this.chargeSheetType === ChargeSheetTypeEnum.MEDICAL_PLAN) {
                    return '咨询方案';
                }
                if (chargeIsRetail(this.chargeSheetType)) {
                    return '零售收费';
                }
                if (this.chargeSheetType === ChargeSheetTypeEnum.PATIENT_ARCHIVE_DIRECT) {
                    return '零售收费';
                }
                return '门诊收费';
            },

            // 在网诊 && 已退费状态 => 不可点击退费，
            refundDisabled() {
                if (this.buttonLoading) return true;
                if (this.isDisabledRefund) return true;
                if (this.isDisabledOperate) return true;
                if (this.lockedInfo) return true;
                return this.isOnline === 1 && this.chargeStatus === ChargeStatusEnum.REFUND;
            },

            /**
             * @desc 能否 看到推送按钮
             * <AUTHOR>
             * @date 2020/02/19 21:46:12
             */
            canSendOrder() {
                return this.weChatPayConfig.weChatPaySwitch === 2 &&
                    this.weChatPayConfig.jsapiPayStatus === 2 &&
                    this.isOpenMp && this.chargeStatus < ChargeStatusEnum.PART_CHARGED;
            },

            /**
             * @desc 收费头部展示的费用, 分为两部分 prefix-总500.00 收200.00, fee-￥300.00
             * @desc 待收、关闭时，     格式为 fee-待收金额
             * @desc 部分收费时，       格式为 prefix-总金额 实收金额 fee-待收金额
             * @desc 全收时，          格式为 fee-实收金额
             * @desc 全收有欠款时，     格式为 prefix-总金额 实收金额 fee-欠款
             * @desc 全收有退款时，     格式为 prefix-总金额 退款金额 fee-实收金额
             * @desc 全收有退费有欠款时，格式为 prefix-总金额 退费金额 实收金额 fee-欠款
             * @desc 全退时，          格式为 fee-总金额
             * <AUTHOR>
             * @date 2023/07/13 10:38:45
             * @return {Object} {prefix, fee}
             */
            topHeaderFee() {
                const {
                    totalFee, // 总金额
                    netIncomeFee, // 实收金额
                    oweFee, // 欠款
                    refundFee, // 退款
                    needPayFee, // 待收金额
                    discountFee, // 优惠金额
                } = this.chargeSheetSummary || {};
                const formattedTotalFee = formatMoney(totalFee + discountFee);
                const formattedReceivedFee = formatMoney(netIncomeFee);
                const formattedOweFee = formatMoney(oweFee);
                const formattedRefundFee = formatMoney(Math.abs(refundFee));
                const formattedNeedPayFee = formatMoney(needPayFee < 0 ? 0 : needPayFee);
                const space = '&nbsp;';
                switch (this.chargeStatus) {
                    case ChargeStatusEnum.UN_CHARGE:
                    case ChargeStatusEnum.CLOSED:
                        return {
                            prefix: '',
                            fee: formattedNeedPayFee,
                        };
                    case ChargeStatusEnum.PART_CHARGED:
                        return {
                            prefix: `总 ${formattedTotalFee}${space}${space}收 ${formattedReceivedFee}`,
                            fee: formattedNeedPayFee,
                        };
                    case ChargeStatusEnum.CHARGED:
                        if (!oweFee) {
                            return {
                                prefix: '',
                                fee: formattedReceivedFee,
                            };
                        }
                        return {
                            prefix: `总 ${formattedTotalFee}${space}${space}收 ${formattedReceivedFee}`,
                            fee: formattedOweFee,
                        };
                    case ChargeStatusEnum.PART_REFUND:
                        if (!oweFee) {
                            return {
                                prefix: `总 ${formattedTotalFee}${space}${space}退 ${formattedRefundFee}`,
                                fee: formattedReceivedFee,
                            };
                        }
                        return {
                            prefix: `总 ${formattedTotalFee}${space}${space}退 ${formattedRefundFee}${space}${space}收 ${formattedReceivedFee}`,
                            fee: formattedOweFee,
                        };
                    case ChargeStatusEnum.REFUND:
                        return {
                            prefix: '',
                            fee: formattedTotalFee,
                        };
                    default:
                        return {
                            prefix: '',
                            fee: formattedNeedPayFee,
                        };
                }
            },

            isCharged() {
                return this.chargeStatus > ChargeStatusEnum.UN_CHARGE;
            },
            noChargeForms() {
                return this.postData.chargeForms.filter((form) => {
                    return form.chargeFormItems.length;
                }).length === 0;
            },

            payDisabledObj() {
                const obj = {
                    disabled: this.noChargeForms || !!this.lockedInfo || this.consultLockStatus,
                };
                if (this.noChargeForms) {
                    obj.desc = '没有收费项目';
                    return obj;
                }
                if (this.lockedInfo) {
                    const {
                        businessKey, value,
                    } = this.lockedInfo;
                    if (businessKey === LockBusinessKeyEnum.CHARGE) {
                        const { businessScene } = value || {};
                        if (businessScene === ChargeBusinessSceneEnum.OUTPATIENT_LOCK_CHARGE_SHEET) {
                            obj.desc = '医生正在调整处方医嘱，请稍后再进行收费';
                            return obj;
                        }
                        if (businessScene === ChargeBusinessSceneEnum.CHARGE_SHEET_PAY) {
                            obj.desc = '正在收费中，请稍后再进行收费';
                            return obj;
                        }
                        if (businessKey === ChargeBusinessSceneEnum.CHARGE_SHEET_REFUND) {
                            obj.desc = '正在退费中，请稍后再进行收费';
                            return obj;
                        }
                    }
                }
                if (this.consultLockStatus) {
                    obj.desc = `${this.consultLock.consultLockUserName}正在调整咨询项目，请稍后再进行操作`;
                    return obj;
                }
                return obj;
            },

            isPrintIncludeTreatment() {
                return this.printPrescriptionConfig.infusionExecute.includeTreatment;
            },
            // 检验检验单打印选项
            examPrintOptions() {
                return this.viewDistributeConfig.Print.examPrintOptions;
            },
            printOptions() {
                const chargePrint = [ {
                    value: this._printOptions.CASHIER.label,
                    disabled: this.disabledCashierPrint,
                    tips: this.chargeStatus === ChargeStatusEnum.REFUND ? '无收费项目' : '收费未完成',
                }];
                if (this.printable?.refundChargeSheet?.canPrint) {
                    chargePrint.push({
                        value: this._printOptions.REFUND_CASHIER.label,
                        disabled: false,
                    });
                }

                chargePrint.push({
                    value: '医疗收费清单',
                    disabled: this.disabledCashierPrint,
                    tips: this.chargeStatus === ChargeStatusEnum.REFUND ? '无收费项目' : '收费未完成',
                    border: true,
                });

                const otherPrint = [
                    {
                        value: this._printOptions.EXAMINATION_CODE.label,
                        disabled: this.disabledPrintExaminationBarCode,
                        tips: `没有${this._printOptions.EXAMINATION_CODE.label}`,
                    },
                    {
                        value: this.examPrintOptions.examination.label,
                        disabled: this.disabledPrintExamination,
                        tips: `没有${this.examPrintOptions.examination.label}`,
                    },
                ];
                return chargePrint.concat(otherPrint);
            },
            socialPrintType() {
                const isWillCharge = this.chargeStatus < ChargeStatusEnum.CHARGED; // 是否待收费
                const isSocialPay = this.checkIsSocialPay(); // 是否社保支付
                const getSocialPrintTypeParams = {
                    isWillCharge,
                    isSocialPay,
                };
                // 获取社保打印类型
                return this.$abcSocialSecurity.getSocialPrintType(getSocialPrintTypeParams);
            },
            disabledCashierPrint() {
                return (this.chargeStatus < ChargeStatusEnum.CHARGED ||
                    this.chargeStatus === ChargeStatusEnum.REFUND ||
                    this.chargeStatus === ChargeStatusEnum.CLOSED) &&
                    !this.printable?.chargeSheet?.canPrint;
            },

            // 判断是否能打印检验单
            disabledPrintExamination() {
                return this.printable && !this.printable.examinationExamination?.canPrint;
            },

            disabledPrintExaminationBarCode() {
                return !this.printable?.examinationExaminationBarCode?.canPrint;
            },
            tagForms() {
                let forms = [];
                const isSale = chargeIsRetail(this.chargeSheetType);
                const tagTypes = isSale ? [ SourceFormTypeEnum.PRESCRIPTION_CHINESE] :
                    [ SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                      SourceFormTypeEnum.PRESCRIPTION_WESTERN,
                      SourceFormTypeEnum.PRESCRIPTION_INFUSION ];
                if (this.postData.chargeForms) {
                    forms = clone(this.postData.chargeForms).filter((form) => {
                        // 西药 输液
                        if (tagTypes.includes(form.sourceFormType)) {
                            form.isFullChecked = true;
                            form.formItems = form.chargeFormItems && form.chargeFormItems.filter((item) => {
                                if (item.status === ChargeStatusEnum.UN_CHARGE || item.status === ChargeStatusEnum.PART_CHARGED) {
                                    item.checked = true;
                                    return item;
                                }
                            }) || [];
                            delete form.chargeFormItems;
                            if (form.formItems.length) {
                                return form;
                            }
                        }
                    });
                }
                return forms;
            },

            /**
             * @desc 挂号直接进收费，收费处代录门诊
             * <AUTHOR>
             * @date 2021-06-15 15:02:00
             */
            isChargeCopyWrite() {
                return this.chargeSheetType === ChargeSheetTypeEnum.OUTPATIENT && this.copyWriteSource === 2;
            },

            /**
             * 这些情况需要使用开单收费可以加药的组件
             * @desc 0：治疗理疗开单收费；
             * @desc 1：开单收费在详情，退费重新收费；
             * @desc 2：挂号 待收费/未诊
             * @desc 3：挂号收费代录门诊后，退费重新收费；
             * @desc 4：开单收费，status === 0；
             * @desc 5：服务器挂单；
             * @desc 6：家庭医生签约费；
             * @desc 7：检查站开单；
             * <AUTHOR>
             * @date 2019/10/31 15:09:50
             */
            useRetailPostDataForm() {
                // 已经收费了，不能用 零售收费的组件
                if (this.isCharged) return false;

                // 开单收费
                // 治疗理疗开单收费
                // 检查站开单
                if (chargeIsRetail(this.chargeSheetType)) {
                    return true;
                }

                // 家庭医生签约费
                if (this.chargeSheetType === ChargeSheetTypeEnum.FAMILY_DOCTOR_SIGN) {
                    return true;
                }

                // 续方
                if (this.clonePrescriptionType > 0) {
                    return true;
                }

                // 挂号直接进收费的 未诊的收费单
                const UN_OUTPATIENT_CODE = 1; // 待诊
                if (this.outpatientStatus === UN_OUTPATIENT_CODE) {
                    return true;
                }

                // 挂号收费代录门诊
                if (this.isChargeCopyWrite) {
                    return true;
                }

                return false;
            },

            /**
             * @desc 是服务器挂单
             * <AUTHOR>
             * @date 2019/11/21 20:05:50
             */
            isServerDraft() {
                return !!this.isDraft;
            },

            // 非记账支付异常
            asyncPayException() {
                return this.queryExceptionType - 1 > 0;
            },

            /**
             * @desc 展示开票按钮
             * <AUTHOR>
             * @date 2022-03-08 15:10:53
             */
            showOpenInvoiceBtn() {
                if (this.hospitalInfo) {
                    const {
                        hospitalSheet = {},
                        hospitalSheetId = '',
                    } = this.hospitalInfo;
                    if (hospitalSheetId) {
                        return false;
                    }
                    return [
                        HospitalSheetChargeStatus.CHARGED,
                        HospitalSheetChargeStatus.REFUNDED,
                    ].indexOf(hospitalSheet.status) > -1;
                }
                return [
                    ChargeStatusEnum.CHARGED,
                    ChargeStatusEnum.PART_REFUND,
                    ChargeStatusEnum.REFUND,
                ].indexOf(this.chargeStatus) > -1;
            },

            /**
             * @desc 非住院单都可以退费
             * @desc 住院单收费结算是待收才能退费
             * <AUTHOR>
             * @date 2022-03-07 17:31:18
             */
            showRefundBtn() {
                if (!this.hospitalInfo) return true;
                const {
                    hospitalSheet = {},
                } = this.hospitalInfo;
                return hospitalSheet.status === HospitalSheetChargeStatus.UNCHARGED;
            },

            /**
             * @desc 展示出院结算按钮
             * <AUTHOR>
             * @date 2022-03-09 14:54:07
             */
            showHospitalDischargeBtn() {
                if (!this.hospitalInfo) return false;
                const {
                    status,
                } = this.hospitalInfo;
                return status === OutpatientHospitalStatusEnum.DISCHARGE;
            },

            isHospitalSheet() {
                return !!this.hospitalInfo;
            },

            // 诊间支付只能全收全退，原路退回
            isContentOutpatientCenterPay() {
                return this.chargeTransactions.some((x) => x.payMode === PayModeEnum.OUTPATIENT_CENTER_PAY);
            },

            // 处方外购
            isPrescriptionOut() {
                return this.chargeSheetType === ChargeSheetTypeEnum.PRESCRIPTION_OUT;
            },

            /**
             * @desc 展示还款按钮:普通门诊欠费
             * <AUTHOR>
             * @date 2022/05/12 18:00:26
             */
            showRepaymentBtn() {
                return this.hospitalOwedStatus === OwedChargeStatusEnum.OWING && !this.isHospitalSheet;
            },
            //代煎中心同时发药按钮不显示
            isDaijianCenterDispensing() {
                const { chargeForms = [] } = this.postData || {};
                return !!chargeForms.find((form) => {
                    return form.chargeFormItems?.find((item) => {
                        return this.pharmacyList.find((pharmacyItem) => {
                            return item.pharmacyNo === pharmacyItem.no && pharmacyItem.externalPharmacyConfig?.chargeDisableAutoDispense;
                        });
                    });

                });
            },

            canDeleteServerDraft() {
                if (!this.isServerDraft) return false;
                // 有交易记录不能删除
                if (this.chargeActions?.length) return false;
                return this.chargeSheetType === ChargeSheetTypeEnum.DIRECT_SALE;
            },
        },
        watch: {
            postData: {
                handler(val) {
                    // 拉取初始化数据过程中不对比是否发生修改
                    if (!this.loading &&
                        !this.inputCalcLoading &&
                        !this.isCharged &&
                        !this._routerConfirm &&
                        this.$route.params.id === val.id) {
                        this._compareUpdate();
                    }
                },
                immediate: true,
                deep: true,
            },
            // 如果路由有变化，会再次执行该方法
            '$route': function(newVal, oldVal) {
                // 如果切换了收费单,则解锁之前的咨询收费单
                if (this.chargeSheetType === ChargeSheetTypeEnum.MEDICAL_PLAN && newVal.params.id !== oldVal.params.id && this.patientOrderId) {
                    this.sendConsultUnlock();
                }
                this.chargeSheetId = newVal.params.id;
                this.loading = false;
                this.fetchDetail(null, true);
                this.initWeChatPayConfig();
                this.initDispensingConfig();
                this.fetchCePayRule();
            },
            'cashier.selectedPatient': {
                handler (patientInfo) {
                    if (patientInfo) {
                        const {
                            isAttention,
                            wxBindStatus,
                            appFlag = 0,
                            arrearsFlag = 0,
                            tags = [],
                            email = '',
                        } = patientInfo;
                        this.postData.patient.isAttention = isAttention;
                        this.postData.patient.wxBindStatus = wxBindStatus;
                        this.postData.patient.appFlag = appFlag;
                        this.postData.patient.tags = tags;
                        this.postData.patient.arrearsFlag = arrearsFlag;
                        this.postData.patient.email = email;
                    }
                },
                deep: true,
            },
        },
        async created() {
            // socket监听
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._handleSocketLock = (data) => {
                this.handleOnLockSocket(data, 'lock');
                this.handleConsultOnSocket(data, 'lock');
            };
            this._handleSocketUnlock = (data) => {
                this.handleOnLockSocket(data, 'unlock');
                this.handleConsultOnSocket(data, 'unlock');
            };
            this._socket.on('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.on('patientOrder.sheet_unlock', this._handleSocketUnlock);
            this._socket.on('charge.charge_form_changed', this.handleChargeFormChange);

            this._sendConsultLock = debounce(this.sendConsultLock, 1000, true);

            this._macAddress = localStorage.get('mac_address', true);
            this._printOptions = this.viewDistributeConfig.Print.printOptions;
            this.invoiceService = new InvoiceService();
            this.chargeSheetId = this.$route.params.id;
            this._HArray = [ '一', '二', '三', '四', '五', '六', '七', '八', '九' ];
            this.fetchDetail(null, true);
            this.fetchCePayRule();
            this._calcFee = debounce(this.calcFee, 200, true);
            // this.$store.dispatch('fetchTpsStatusIfNeed');
            this.$store.dispatch('initChargeConfig');

            this._compareUpdate = debounce(this.compareUpdate, 100, true);
            this.initAirPharmacySystemConfig();
            this.$abcEventBus.$on('consumption-register-success', (chargeSheetId) => {
                if (chargeSheetId === this.chargeSheetId) {
                    this.getSocialSettleInfo();
                }
            }, this);
            this.$store.dispatch('fetchEmployeeListByPractice');
            this.$store.dispatch('fetchAntimicrobialDrugManagementData');
            this.$store.dispatch('outpatientConfig/initOutpatientConfig', { scope: 'employee' });
        },
        mounted() {
            // 新建焦点落在姓名上
            on(document, 'keydown', this.keydownF9Handle);
        },
        beforeDestroy() {
            // 解锁咨询收费单
            if (this.chargeSheetType === ChargeSheetTypeEnum.MEDICAL_PLAN && this.patientOrderId) {
                this.sendConsultUnlock();
            }

            this._cashierPrintTimer && clearTimeout(this._cashierPrintTimer);
            off(document, 'keydown', this.keydownF9Handle);
            if (this._timer) {
                clearInterval(this._timer);
            }
            this.loading = false;

            this.$abcEventBus.$offVmEvent(this._uid);
            // 注销socket监听
            this._socket.off('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.off('patientOrder.sheet_unlock', this._handleSocketUnlock);
            this._socket.off('charge.charge_form_changed', this.handleChargeFormChange);

            this.timeoutId && clearTimeout(this.timeoutId);
        },

        methods: {
            autoDestroyInvoice,
            async printMedicineTagHandler(guid) {
                try {
                    this.loading = true;
                    if (!this.chargeSheetId) return;
                    const { chargeSheetId } = this;

                    Logger.reportAnalytics('medicine-tag-print', {
                        type: '用药标签打印',
                        from: '收费站',
                        guid,
                        info: '请求参数',
                        chargeSheetId,
                    });

                    const res = await ChargeAPI.fetch(chargeSheetId, true);
                    const { data } = res || {};

                    Logger.reportAnalytics('medicine-tag-print', {
                        type: '用药标签打印',
                        from: '收费站',
                        info: '请求返回值',
                        guid,
                        data: clone(data),
                    });

                    if (data.productInfos && data.productInfos.length) {
                        this.updateChargeForms(data.chargeForms, data.productInfos);
                    }

                    const { chargeForms } = data;

                    let forms = [];
                    const isSale = chargeIsRetail(data.type);
                    const tagTypes = isSale ? [ SourceFormTypeEnum.PRESCRIPTION_CHINESE] :
                        [ SourceFormTypeEnum.PRESCRIPTION_CHINESE,
                          SourceFormTypeEnum.PRESCRIPTION_WESTERN,
                          SourceFormTypeEnum.PRESCRIPTION_INFUSION ];
                    if (chargeForms && chargeForms.length) {
                        forms = clone(chargeForms).filter((form) => {
                            // 西药 输液
                            if (tagTypes.includes(form.sourceFormType)) {
                                form.isFullChecked = true;
                                form.formItems = form.chargeFormItems && form.chargeFormItems.filter((item) => {
                                    if (item.status === ChargeStatusEnum.UN_CHARGE || item.status === ChargeStatusEnum.PART_CHARGED) {
                                        item.checked = true;
                                        return item;
                                    }
                                    return false;
                                }) || [];
                                delete form.chargeFormItems;
                                if (form.formItems.length) {
                                    return form;
                                }
                                return false;
                            }
                            return false;
                        });
                    }

                    const onConfirm = async (params) => {
                        Logger.reportAnalytics('medicine-tag-print', {
                            type: '用药标签打印',
                            from: '收费站',
                            info: '选择打印的药品',
                            guid,
                            params: clone(params),
                        });

                        const { selectedData } = params;
                        const medicineForms = selectedData.w.concat(selectedData.c);

                        // 获取收费单对应的皮试结果
                        try {
                            const infusionForms = medicineForms.filter((form) => {
                                return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION ||
                                    form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN;
                            });
                            if (infusionForms && infusionForms.length) {
                                const astResultData = await PrintAPI.getAstResultByCharge(chargeSheetId);
                                const astResultList = astResultData.data?.list || [];
                                infusionForms.forEach((infusionForm) => {
                                    infusionForm.formItems.forEach((infusionFormItem) => {
                                        const formItemAstData = astResultList.find((astResultItem) => astResultItem.formItemId === infusionFormItem.id);
                                        if (formItemAstData) {
                                            infusionFormItem.ast = formItemAstData.ast || 0;
                                            infusionFormItem.astResult = formItemAstData.astResult;
                                        }
                                    });
                                });
                            }
                        } catch (e) {
                            console.error('获取发药单皮试结果失败\n', e);
                        }

                        const {
                            doctorName, patientOrderNo, patient,
                        } = data;

                        Logger.reportAnalytics('medicine-tag-print', {
                            type: '用药标签打印',
                            from: '收费站',
                            info: '患者相关信息',
                            guid,
                            data: {
                                patient: clone(patient),
                                doctorName,
                                patientOrderNo,
                            },
                        });

                        const printOptions = getAbcPrintOptions('用药标签', {
                            forms: medicineForms,
                            patient,
                            doctorName,
                            patientOrderNo,
                            clinicName: this.currentClinic.clinicName,
                            // 手动打印,不做自动打印筛选
                            isManualPreview: true,
                        });
                        AbcPrinter.abcPrint(printOptions);
                    };


                    Logger.reportAnalytics('medicine-tag-print', {
                        type: '用药标签打印',
                        from: '收费站',
                        info: '处理打印数据',
                        guid,
                        tagForms: clone(forms),
                    });

                    if (forms.length) {
                        await new SelectPrintFunctionalDialog({
                            value: true, tagForms: forms, onConfirm,
                        }).generateDialogAsync({ parent: this });
                    }
                } catch (e) {
                    console.error('打印用药标签失败\n', e);
                } finally {
                    this.loading = false;
                }
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                await new PrintConfigDialog({
                    scene: 'cashier',
                    supportConfigPrintList: [
                        ABCPrintConfigKeyMap.cashier.subKey,
                        ABCPrintConfigKeyMap.feeList.subKey,
                        ABCPrintConfigKeyMap.examinationTag.subKey,
                        ABCPrintConfigKeyMap.examinationInspect.subKey,
                    ],
                }).generateDialogAsync({ parent: this });
            },

            // 其他收费异常处理
            handleClickException() {
                new AbcAbnormalHandleDialog({
                    chargeSheetId: this.chargeSheetId,
                    onClose: () => {
                        this.fetchDetail();
                    },
                }).generateDialog({ parent: this });
            },

            handleClickSettings() {
                new CashierSettingsDialog({}).generateDialog({ parent: this });
            },
            /**
             * 是否有 shebaoSettlePrintSheetId
             */
            checkIsSocialPay() {
                return !!this.shebaoSettlePrintSheetId;
            },

            updatedRouteConfirm(next) {
                const _this = this;
                const { selectedItem } = this.cashier;
                const { patient } = this._postDataCache || {};
                if (this._timer) {
                    clearInterval(this._timer);
                }
                if (this.isUpdate) {
                    this._routerConfirm = this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '你的修改内容还未保存，确定离开？',
                        onConfirm: async () => {
                            _this.isUpdate = false;
                            next();
                            if (patient && selectedItem && selectedItem.patient) {
                                Object.assign(selectedItem.patient, {
                                    name: patient.name,
                                    sex: patient.sex,
                                });
                            }
                        },
                        onCancel: async () => {
                            // 取消后还原选中的 selectedItem
                            await _this.$store.dispatch('setSelectedItem', {
                                type: 'cashier',
                                selectedItem,
                            });
                        },
                        onClose: () => {
                            this._routerConfirm = null;
                        },
                    });
                } else {
                    next();
                }
            },

            isShortage,
            ...mapActions('airPharmacy', ['initAirPharmacySystemConfig']),
            ...mapActions([
                'refreshCashierQuickList',
                'updateCashierQuickItem',
                'initWeChatPayConfig',
                'initDispensingConfig',
            ]),

            clearDraft() {
                this.isUpdate = false;
                this.$store.dispatch('ClearDraft', {
                    key: 'cashiers', draftId: this.chargeSheetId,
                });
            },

            /**
             * @desc 算费
             * <AUTHOR>
             * @date 2019/03/30 17:13:03
             */
            async calcFee(inputCalcLoading = true) {
                // 已收费的情况不再发送算费请求
                if (this.isCharged) return false;
                try {
                    this.inputCalcLoading = inputCalcLoading;

                    await this._chargeService.calcFee();

                    this.calcLoading = false;
                    this.inputCalcLoading = false;
                } catch (err) {
                    this.inputCalcLoading = false;
                }
            },

            resetDataHandler() {
                this.loading = true;
                this.isFirstPrint = true;
                this.isFirstPrintAdviceTag = true;
                this.isFirstPrintMedicalBill = true;
                this.isFirstPrintMedicalFee = true;

                this.printLoading = false;
                this.printAdviceTagLoading = false;
                this.isFirstPrintDispensing = true;
                this.printDispensingLoading = false;
                this.printMedicalBillLoading = false;
                this.printMedicalFeeLoading = false;
                this.setLockInfo(null);
            },
            // 校验收费单是否可退费
            async verifyRefundChargeOrder() {
                // 未开启整单退费开关，不校验，返回校验成功
                if (!this.isWholeBillCharge) return true;

                try {
                    await ChargeAPI.verifyRefundChargeOrder(this.chargeSheetId, 2);
                    return true;
                } catch (e) {
                    console.error('verifyRefundChargeOrder error', e);
                    // 满足的业务错误处理码
                    if ([17337, 17338, 17339].includes(e.code)) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                    }
                    return false;
                }
            },
            /**
             * @desc 获取详细信息
             * <AUTHOR>
             * @date 2018/07/24 15:16:54
             * @params id
             */
            async fetchDetail(res, init, isReplay = false) {
                if (this.loading) return false;
                // 每次调用拉取详情前，都重新给chargeSheetId用最新的值，保障数据与url id一直
                this.chargeSheetId = this.$route.params.id;
                this.resetDataHandler();
                if (!res) {
                    if (isReplay) {
                        res = await ChargeAPI.getRenewChargeSheetById(this.chargeSheetId);
                    } else {
                        res = await ChargeAPI.fetch(this.chargeSheetId);
                    }
                }
                const { data } = res;
                if (data.id !== this.$route.params.id) return;
                if (data.id === this.$route.params.id) {
                    const params = {
                        status: data.status,
                        statusName: data.statusName,
                        isDraft: data.isDraft,
                        isClosed: data.isClosed,
                        sendToPatientStatus: data.sendToPatientStatus,
                        queryExceptionType: data.queryExceptionType,
                        // patient: data.patient,
                    };
                    // 更新quicklist指定项信息
                    this.updateCashierQuickItem({
                        id: data.id,
                        params,
                    });
                    // 更新当前选中 status statusName isDraft
                    this.cashier.selectedItem && this.$store.dispatch('setSelectedItem', {
                        type: 'cashier',
                        selectedItem: Object.assign(this.cashier.selectedItem, params),
                    });
                }
                if (data.patient) {
                    let {
                        age,
                    } = data.patient;
                    age = age || {
                        year: null, month: null,
                    };
                    Object.assign(this.postData.patient, data.patient, { age });
                }
                if (data.deliveryInfo && !data.deliveryInfo.deliveryCompany) {
                    data.deliveryInfo.deliveryCompany = {
                        id: '',
                        name: '',
                    };
                }
                if (data.isClosed) {
                    data.status = ChargeStatusEnum.CLOSED;
                }
                this.chargeStatus = data.status;
                this.invoiceStatus = data.invoiceStatus;
                this.invoiceStatusFlag = data.invoiceStatusFlag;
                this.invoiceData = data.invoice || {};
                this.dispensingStatus = data.dispensingStatus;
                this.hospitalOwedStatus = data.owedStatus;
                // 收费不能支付，不能支付的原因
                this.isDisabledPay = data.isDisabledPay;
                this.disabledPayReason = data.disabledPayReason;
                // 不能退费，不能退费的原因
                this.isDisabledRefund = data.isDisabledRefund;
                this.disabledRefundReason = data.disabledRefundReason;
                // 收费单不能操作，不能操作的原因
                this.isDisabledOperate = data.isDisabledOperate;
                this.disabledOperateReason = data.disabledOperateReason;

                // 收费异常状态
                this.setQueryExceptionType(data.queryExceptionType);

                // 收费告知书
                this.isCanUploadNotification = data.isCanUploadNotification;
                this.patientOrderId = data.patientOrderId;
                this.refreshChargeNoticeDetail = !this.refreshChargeNoticeDetail;

                this.diagnosedDate = data.diagnosedDate;
                this.airPharmacyOrderId = data.airPharmacyOrderId;

                this.changePayModeRecords = data.changePayModeRecords || [];

                Object.assign(this.postData, {
                    id: data.id,
                    type: data.type,
                    lockStatus: data.lockStatus,
                    chiefComplaint: data.chiefComplaint, // 医生拍方主诉
                    diagnosis: data.diagnosis, // 医生拍方诊断
                    extendDiagnosisInfos: data.extendDiagnosisInfos || [], // 医保诊断编码
                    departmentId: data.departmentId,
                    departmentName: data.departmentName,
                    doctorId: data.doctorId,
                    doctorName: data.doctorName,
                    retailType: data.retailType,
                    registration: null,
                    memberId: data.memberId,
                    memberInfo: data.memberInfo,
                    sellerId: data.sellerId,
                    sellerName: data.sellerName,
                    sellerDepartmentId: data.sellerDepartmentId,
                    sellerDepartmentName: data.sellerDepartmentName,
                    dataSignature: data.dataSignature || null,
                    deliveryInfo: data.deliveryInfo || {},
                    contactMobile: data.contactMobile || '',
                    shebaoCardInfo: data.shebaoCardInfo || null,
                    expectedAdjustmentFee: data.chargeSheetSummary.draftAdjustmentFee,
                    roundingType: data.chargeSheetSummary.roundingType, // 收费时需要透传 roundingType
                    promotions: data.promotions || [],
                    giftRulePromotions: data.giftRulePromotions || [],
                    couponPromotions: data.couponPromotions || [],
                    patientPointsInfo: data.patientPointsInfo,
                    patientCardPromotions: data.patientCardPromotions || [],
                    isOweSheetCanPayForShebao: data.isOweSheetCanPayForShebao,
                    patientPointDeductProductPromotions: data.patientPointDeductProductPromotions || [],
                    useMemberFlag: data?.useMemberFlag || UseMemberFlagEnum.USE_DEFAULT,
                    patientOrderNo: data.patientOrderNo,
                    airPharmacyOrderId: data.airPharmacyOrderId,
                    consultantName: data.consultantName,
                    consultantId: data.consultantId,
                    prescriptionUrls: data.prescriptionUrls,
                    visitSourceId: data.visitSourceId || null,
                    visitSourceFrom: data.visitSourceFrom || null,
                    visitSourceName: data.visitSourceName || null,
                    visitSourceFromName: data.visitSourceFromName || null,
                    remarks: data.remarks,
                    verifyInfoViews: data.verifyInfoViews || [],
                    diagnosedDate: data.diagnosedDate || data.created,
                    lockPayTransactionInfo: data.lockPayTransactionInfo,
                    patientOrderId: data.patientOrderId,
                });

                this.chargeSheetType = data.type;
                /**
                 * @desc 拍方抓药
                 * clonePrescriptionType 续方类型(0 非续方；1 医生拍照；2 患者拍照；3 患者历史收费单续方)
                 * clonePrescriptionDoseCount  //拍方抓药时患者请求的剂数
                 * cloneChargeSheetSnapshot  //历史续方快照
                 * <AUTHOR>
                 * @date 2020/05/27 16:09:03
                 */
                this.clonePrescriptionType = data.clonePrescriptionType || 0;
                this.clonePrescriptionInfo = Object.assign({}, {
                    remarks: data.remarks || '',
                    doseCount: data.clonePrescriptionDoseCount,
                    snapshot: data.cloneChargeSheetSnapshot,
                });
                this.attachments = data.attachments || [];
                this.memberDiscountInfo = data.memberDiscountInfo;
                this.chargeSheetSummary = data.chargeSheetSummary;
                this.chargeActions = data.chargeActions || [];
                this.chargeTransactions = data.chargeTransactions || [];
                this.disposals = data?.medicalRecord?.disposals?.map((item) => item.value)?.join(',') || '';
                this._patientOrderId = data.patientOrderId;
                this._chargeSheetId = data.id;
                this.isReplay = isReplay;
                this.isDraft = data.isDraft;
                this.isOnline = data.isOnline;
                this.registrationInfo = data.registrationInfo;
                this.attachments = data.attachments || [];
                this.isCanBeUpdateAirPharmacyMedicalRecord = data.isCanBeUpdateAirPharmacyMedicalRecord;
                this.shebaoSettlePrintSheetId = data.shebaoSettlePrintSheetId; // 社保结算单打印 id
                /**
                 * @desc 生成keyId，前端渲染、算费使用
                 * <AUTHOR>
                 * @date 2019/10/30 10:51:26
                 */
                data.chargeForms.forEach((form) => {
                    form.keyId = createGUID();
                    // 门诊过来的 空中药房 form 没有deliveryInfo没有关系，需要兼容一下
                    if (form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY) {
                        form.deliveryInfo = form.deliveryInfo || {
                            addressProvinceId: '',
                            addressProvinceName: '',
                            addressCityId: '',
                            addressCityName: '',
                            addressDistrictId: '',
                            addressDistrictName: '',
                            addressDetail: '',
                            deliveryName: '',
                            deliveryMobile: '',
                            deliveryCompany: {
                                id: '',
                                name: '',
                            },
                        };
                    }
                    form.chargeFormItems.forEach((item) => {
                        item.keyId = createGUID();
                        item.originalDoseCount = item.doseCount;

                        // 重新发药需要清空追溯码
                        if (isReplay) {
                            item.traceableCodeList = [];
                            item.composeChildren?.forEach((child) => {
                                child.traceableCodeList = [];
                            });
                        }
                    });
                });

                // 打印状态
                this.outpatientStatus = data.outpatientStatus;
                this.copyWriteSource = data.copyWriteSource;
                this.isToHomeSwitch = data.isToHomeSwitch;
                this.sendToPatientStatus = data.sendToPatientStatus;
                this.productInfos = data.productInfos || [];
                // 待收费情况
                if (data.status === ChargeStatusEnum.UN_CHARGE) {
                    this.status0Handler(data.chargeForms);
                } else {
                    /**
                     * @desc 更新商品信息
                     * <AUTHOR> Yang
                     * @date 2020-10-15 14:35:45
                     */
                    if (data.productInfos && data.productInfos.length) {
                        this.updateChargeForms(data.chargeForms, data.productInfos);
                    }

                    const multiChineseForm = data.chargeForms.filter((o) => o.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE).length > 1;
                    let chineseFromIndex = 0;

                    this.postData.chargeForms = data.chargeForms.map((form) => {
                        if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                            form.chineseFormDisplayName = multiChineseForm ? `中药处方${numToChinese(chineseFromIndex + 1)}` : '中药处方';
                            chineseFromIndex += 1;
                        }

                        form.chargeFormItems.forEach((item) => {
                            item.checked = data.status === ChargeStatusEnum.CLOSED ||
                                item.status === 1 || item.status === 2 || item.status === 4;
                        });

                        return form;
                    });
                }
                try {
                    /**
                     * @desc 拉取住院信息详情
                     * <AUTHOR>
                     * @date 2022-02-21 15:47:25
                     */
                    if (data.hospitalOrderId) {
                        const { data: hospitalInfo } = await HospitalAPI.fetchHospitalInfoById(data.hospitalOrderId);
                        this.hospitalInfo = hospitalInfo;
                    } else {
                        this.hospitalInfo = null;
                    }

                } catch (err) {
                    console.error(err);
                }

                this.isUpdate = false;

                this._postDataCache = clone(this.postData);

                this._chargeService = new AbcChargeService({
                    chargeSheetId: this.chargeSheetId,
                    chargeStatus: this.chargeStatus, // 收费状态
                    chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                    postData: this.postData, // 提交的收费单数据
                    isReplay: this.isReplay, // 重新收费
                });

                if (data.id === this.$route.params.id && this.chargeStatus === ChargeStatusEnum.UN_CHARGE) {
                    await this.calcFee(false);
                }

                // 获取打印状态
                await this.fetchPrintList();

                this.$nextTick(() => {
                    this.loading = false;
                    this.isUpdate = false;
                });

                if (this.chargeStatus >= ChargeStatusEnum.CHARGED) {
                    //  初次调用判断是否需要打印清单
                    //  未收费完成也不打印
                    const { cache } = Printer;
                    const printCache = cache.get();
                    // 需要同时打印的单据信息
                    const { cashier } = printCache;
                    // 此次是否同时打印
                    const { directSmallNeedPrint } = printCache;

                    const uuid = createGUID();
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_获取本地配置',
                            patientOrderId: this.patientOrderId,
                            chargeSheetId: this.chargeSheetId,
                            printable: clone(this.printable),
                            printCache,
                        },
                    });

                    if (directSmallNeedPrint) {
                        // 如果勾选了同时打印打印发票,则视为收费完成后同时开具发票并打印
                        if (cashier.includes('医疗收费票据') || cashier.includes('打印收费票据')) {
                            this.openInvoice();
                        }
                        if (cashier.includes(('医保结算单'))) {
                            this.print(['医保结算单'], uuid);
                        }
                        cache.set({
                            directSmallNeedPrint: false,
                        });
                    }
                }
                // 拉取发票列表
                await this.fetchInvoiceList();
                // 设置是否锁单
                this.getLockInfo({
                    remote: false,
                    lockList: data.patientOrderLocks,
                });

                this.getSocialExceptionInfo(this.chargeSheetId);

                /**
                 * 如果是咨询收费单, 需要判断锁状态
                 */
                if (this.chargeSheetType === ChargeSheetTypeEnum.MEDICAL_PLAN) {
                    // 查询咨询收费单锁单状态
                    await this.getConsultLockStatus();
                    // 未收费
                    if (data.status < ChargeStatusEnum.CHARGED) {
                        // 如果没有被锁单,则上锁
                        if (!this.consultLock.consultLockIdentity) {
                            this._sendConsultLock();
                        }
                    } else {
                        // 已收费
                        // 如果被锁, 则解锁
                        this.sendConsultUnlock();
                    }
                }

                // 支付方式有医保卡时，获取医保结算信息
                if (this.chargeTransactions.some((x) => x.payMode === PayModeEnum.SOCIAL_CARD)) {
                    this.getSocialSettleInfo();
                }
            },

            /**
             * 获取咨询单锁单状态
             */
            async getConsultLockStatus() {
                if (!this.patientOrderId) return;
                try {
                    const resp = await OutpatientAPI.getOutpatientLock(this.patientOrderId, {
                        businessKey: LockBusinessKeyEnum.CONSULT,
                    });
                    const { id } = this.userInfo || {};
                    const {
                        result, value, employeeId, employeeName,
                    } = resp.data || {};
                    if (result === 1) {
                        // 获取到业务锁
                        if (value) {
                            this.consultLock.consultLockIdentity = value;
                            this.consultLock.consultLockUserId = employeeId;
                            this.consultLock.consultLockUserName = employeeName;
                            this.consultLock.consultLockPatientOrderId = this.patientOrderId;
                            if (employeeId === id) {
                                await this.sendConsultLockRenew();
                            }
                        } else {
                            this.consultLock.consultLockIdentity = '';
                            this.consultLock.consultLockUserId = '';
                            this.consultLock.consultLockUserName = '';
                            this.consultLock.consultLockPatientOrderId = '';
                        }
                    }
                } catch (err) {
                    console.warn('获取咨询单锁单状态失败\n', err);
                }
            },
            /**
             * 咨询单锁单续期
             */
            async sendConsultLockRenew(key = this.patientOrderId) {
                if (!this.consultLock.consultLockIdentity || !this.patientOrderId) {
                    this.clearConsultLockTimeOut();
                    return;
                }
                if (key !== this.patientOrderId) {
                    this.clearConsultLockTimeOut();
                    return;
                }
                try {
                    const resp = await OutpatientAPI.lockOutpatientRenew(this.patientOrderId, {
                        businessKey: LockBusinessKeyEnum.CONSULT,
                        value: this.consultLock.consultLockIdentity,
                    });
                    const { result } = resp.data || {};
                    if (result === 1) {
                        this.continueConsultLock(this.sendConsultLockRenew, key);
                    } else {
                        this.clearConsultLockTimeOut();
                    }
                } catch (err) {
                    console.warn('咨询单锁单续期失败\n', err);
                    this.clearConsultLockTimeOut();
                }
            },
            /**
             * 咨询单上锁
             */
            async sendConsultLock() {
                if (this.consultLock.consultLockIdentity || !this.patientOrderId) return;
                try {
                    const resp = await OutpatientAPI.lockOutpatient(this.patientOrderId, LockBusinessKeyEnum.CONSULT);
                    const {
                        result, value, key, employeeId,
                    } = resp.data || {};
                    if (result === 1) {
                        this.consultLock.consultLockIdentity = value;
                        this.consultLock.consultLockUserId = employeeId;
                        this.consultLock.consultLockPatientOrderId = this.patientOrderId;
                        this.continueConsultLock(this.sendConsultLockRenew(), key);
                    }
                } catch (err) {
                    console.warn('咨询单上锁失败\n', err);
                    this.clearLockTimeOut();
                }
            },
            /**
             * 咨询单解锁
             */
            async sendConsultUnlock() {
                try {
                    if (!this.consultLock.consultLockIdentity || !this.patientOrderId) return;
                    const { id } = this.userInfo || {};
                    if (this.consultLock.consultLockUserId !== id) return;
                    await OutpatientAPI.unlockOutpatient(this.patientOrderId, {
                        businessKey: LockBusinessKeyEnum.CONSULT,
                        value: this.consultLock.consultLockIdentity,
                    });
                    this.consultLock.consultLockIdentity = '';
                    this.consultLock.consultLockUserId = '';
                    this.consultLock.consultLockUserName = '';
                    this.consultLock.consultLockPatientOrderId = '';
                    this.clearConsultLockTimeOut();
                } catch (err) {
                    console.warn('咨询单解锁失败\n', err);
                    this.consultLock.consultLockIdentity = '';
                    this.consultLock.consultLockUserId = '';
                    this.consultLock.consultLockUserName = '';
                    this.consultLock.consultLockPatientOrderId = '';
                    this.clearConsultLockTimeOut();
                }
            },
            /**
             * socket上锁/解锁回调
             */
            async handleConsultOnSocket ({
                key, employeeId, employeeName, value, businessKey,
            }, type) {
                if (businessKey !== LockBusinessKeyEnum.CONSULT) return;
                if (key === this.patientOrderId) {
                    if (type === 'lock') {
                        this.consultLock.consultLockIdentity = value;
                        this.consultLock.consultLockUserName = employeeName;
                        this.consultLock.consultLockUserId = employeeId;
                        this.consultLock.consultLockPatientOrderId = this.patientOrderId;
                    } else {
                        this.consultLock.consultLockIdentity = '';
                        this.consultLock.consultLockUserName = '';
                        this.consultLock.consultLockUserId = '';
                        this.consultLock.consultLockPatientOrderId = '';

                        // 收费单可能有变更, 需要重新获取
                        await this.fetchDetail();
                    }
                }
            },
            /**
             * 咨询单锁单续期重新计时
             */
            continueConsultLock(callback, key, timeout = 1000 * 50) {
                this.clearConsultLockTimeOut();
                this._lockConsultTimeoutId = setTimeout(() => {
                    callback(key);
                }, timeout);
            },
            /**
             * 清除咨询单锁单定时器
             */
            clearConsultLockTimeOut() {
                if (this._lockConsultTimeoutId) {
                    clearTimeout(this._lockConsultTimeoutId);
                }
            },

            /**
             * 拉取发票列表
             */
            async fetchInvoiceList() {
                if (this.chargeStatus >= ChargeStatusEnum.CHARGED && this.chargeStatus !== ChargeStatusEnum.CLOSED) {
                    try {
                        const invoiceListResp = await this.invoiceService.fetchCashierSideBarInvoiceList(this.chargeSheetId,
                                                                                                         InvoiceBusinessScene.CHARGE,
                                                                                                         InvoiceCategory.PAPER,
                                                                                                         0);
                        this.invoiceList = invoiceListResp.rows || [];
                    } catch (e) {
                        this.invoiceList = [];
                        console.warn('获取发票列表失败\n', e);
                    }
                } else {
                    this.invoiceList = [];
                }
            },
            /**
             * 收费后自动开具发票
             */
            async autoWriteInvoice() {
                const postData = {
                    businessScene: InvoiceBusinessScene.CHARGE,
                    invoiceManagementDeviceId: this._macAddress,
                    invoiceType: PaperInvoiceType.OUTPATIENT,
                    patientId: this.postData.patient.id,
                    registrationInvoiceType: 0,
                };
                const data = await this.invoiceService.autoWriteInvoice(this.chargeSheetId, postData);
                return data;
            },

            /**
             * 收费后自动开具发票并打印
             */
            openInvoice() {
                // 如果开启了收费同时开具电子发票,则不开具纸质发票
                if (this.writeInvoiceConfig.enableAutoBill) return;

                // 暂时手动调起开票弹窗
                this.openInvoiceDialog();
                // 开具发票,开票成功后才打印
                // try {
                //     const autoInvoiceResult = await this.autoWriteInvoice();
                //     // status  0: 开票成功 1: 开票失败
                //     if (autoInvoiceResult.status === 0) {
                //         this.$Toast.success('发票已开具');
                //     }
                //
                //     const printData = await this.fetchInvoicePrintPreview();
                //     if (!printData) return;
                //     AbcPrinter.abcPrint({
                //         templateKey: window.AbcPackages.AbcTemplates[this.getTemplateName()],
                //         printConfigKey: ABCPrintConfigKeyMap.bill,
                //         data: printData,
                //         extra: {
                //             $abcSocialSecurity: this.$abcSocialSecurity,
                //         },
                //         matchTemplateCallback: ({ template: { templates = [] } }) => {
                //             return matchTemplate(this.printBillConfig.format, templates, this.printBillConfig);
                //         },
                //         matchRecommendPagesizeCallback: (pageSizeList) => {
                //             return matchPageSize(pageSizeList, this.printBillConfig, this.printBillConfig.format);
                //         },
                //     });
                //
                //     // 发票开具完成,刷新QL列表
                //     this.$abcEventBus.$emit('open-invoice-refresh-quick-list');
                // } catch (e) {
                //     this.$Toast.error('发票开具失败');
                //     console.warn('收费后自动开具发票失败\n', e);
                // }
            },

            /**
             * @desc 更新商品信息
             * <AUTHOR> Yang
             * @date 2020-10-15 10:19:41
             */
            updateChargeForms(chargeForms, productInfos) {
                // 需要对药品基础信息/库存信息进行更新
                chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        this.setGoodsInfo(item, productInfos);
                    });
                });
            },

            setGoodsInfo(item, productInfos) {
                const goodsInfo = productInfos.find((goods) => goods.id === item.productId);
                if (item.productInfo && goodsInfo) {
                    item.stockPackageCount = goodsInfo?.stockPackageCount || item.stockPackageCount;
                    item.stockPieceCount = goodsInfo?.stockPieceCount || item.stockPieceCount;
                    item.realProductInfo = Object.assign({}, item.productInfo, goodsInfo || {});
                }
            },

            isMedicine(form, item) {
                return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN ||
                    form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION ||
                    form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE ||
                    form.sourceFormType === SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM ||
                    form.sourceFormType === SourceFormTypeEnum.MATERIAL ||
                    form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL ||
                    (form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY && item.productType === GoodsTypeEnum.MEDICINE);
            },
            /**
             * desc [判断是否共享会员]
             */
            async exitShareMember(patientId) {
                if (patientId) {
                    try {
                        const { data } = await CrmAPI.selectShareMemberInfo(patientId);
                        if (data.memberInfo) {
                            return {
                                mobile: data.memberInfo.memberCardId,
                                memberId: data.memberInfo.patientId,
                                memberTypeName: data.memberInfo.memberTypeInfo && data.memberInfo.memberTypeInfo.memberTypeName,
                            };
                        }
                    } catch (error) {
                        console.log('exitShareMember error', error);
                    }
                }
                return null;
            },
            /**
             * @desc 待收费情况数据处理
             * <AUTHOR>
             * @date 2019/03/22 17:39:03
             */
            status0Handler(chargeForms) {
                // 需要判断 有无productInfo
                const _needProductInfoList = [
                    SourceFormTypeEnum.EXAMINATION,
                    SourceFormTypeEnum.TREATMENT,
                    SourceFormTypeEnum.OTHER_FEE,
                    SourceFormTypeEnum.NURSING,
                    SourceFormTypeEnum.ADDITIONAL_FORM,
                    SourceFormTypeEnum.COMPOSE,
                    SourceFormTypeEnum.PRESCRIPTION_EXTERNAL,
                    SourceFormTypeEnum.EYEGLASSES,
                ];
                this.postData.chargeForms = chargeForms.map((form) => {

                    /**
                     * @desc if 不需要判断库存，都需要选中：
                     *       零售开单 || 不做提示
                     *       else if 虚拟库存
                     *       药品要判断停用和有无药品资料
                     *       快递加工默认选中
                     *       else 需要判断库存，根据状态及库存判断是否选中
                     *       药品需要判断 停用 库存
                     *       套餐需要判断子项库存
                     * <AUTHOR>
                     * @date 2021-06-07 15:05:32
                     */
                    if (this.isWholeBillCharge || this.useRetailPostDataForm || !this.needCheckStock) {
                        form.chargeFormItems.forEach((item) => {
                            item.checked = true;
                        });
                    } else if (form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                        form.chargeFormItems.forEach((item) => {
                            if (this.isStockGoods(item.productType)) {
                                item.checked = !(!item.productInfo || isDisabledGoods(item).flag);
                            } else {
                                item.checked = true;
                            }
                        });
                    } else {
                        form.chargeFormItems.forEach((item) => {
                            // 根据锁库需求修改，收费处校验可发数量（之前为可售数量）,这里更新数量，下面要校验。
                            if (this.isStockGoods(item.productType)) {
                                const goodsInfo = this.productInfos?.find((goods) => goods.id === item.productId);

                                let availablePieceCount = item.productInfo?.availablePieceCount;
                                let availablePackageCount = item.productInfo?.availablePackageCount;

                                if (goodsInfo) {
                                    availablePieceCount = goodsInfo.availablePieceCount;
                                    availablePackageCount = goodsInfo.availablePackageCount;
                                }

                                // 字段还是使用可售数量，取值为可发数量，用于校验
                                item.stockPieceCount = availablePieceCount || 0;
                                item.stockPackageCount = availablePackageCount || 0;

                                item.realProductInfo = Object.assign({}, item.productInfo, goodsInfo || {});
                            }

                            // 挂号费已收需要反选
                            if (form.sourceFormType === SourceFormTypeEnum.REGISTRATION && item.status === 1) {
                                item.checked = false;
                            } else if (item.sourceItemType === OutpatientChargeTypeEnum.NO_CHARGE) {
                                item.checked = true;
                            } else {
                                if (this.isMedicine(form, item)) {
                                    // 是药 && 有checkbox 需要判断库存 和 是否停用
                                    item.checked = !isShortage(item).flag && !isDisabledGoods(item).flag;
                                } else if (_needProductInfoList.indexOf(form.sourceFormType) > -1 &&
                                    (!item.productInfo || isDisabledGoods(item).flag)) {
                                    // 诊疗项目开出，但是被删除或停用，没有productInfo，不能勾选上
                                    item.checked = false;
                                } else {
                                    let hasShortageGoods = false;
                                    (item.composeChildren || []).forEach((child) => {
                                        if (this.isStockGoods(child.productType) && isShortage(child).flag) {
                                            hasShortageGoods = true;
                                        }
                                    });
                                    item.checked = !hasShortageGoods;
                                }
                            }
                        });
                    }

                    form.chargeFormItems.forEach((item) => {
                        if (item.status === ChargeItemStatusEnum.RETURNED) {
                            item.checked = false;
                        }
                    });

                    return form;
                });
            },

            /**
             * @desc 收费单发生改变
             * <AUTHOR>
             * @date 2019/03/21 23:19:22
             */
            async changeHandler(immediate = false) {
                this.postData.chargeForms = this.postData.chargeForms.filter((form) => {
                    return form.chargeFormItems.length || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_GLASSES;
                });
                if (immediate) {
                    this.calcFee();
                } else {
                    this._calcFee();
                }
            },

            isStockGoods(type) {
                return type === GoodsTypeEnum.MEDICINE ||
                    type === GoodsTypeEnum.MATERIAL ||
                    type === GoodsTypeEnum.GOODS;
            },

            /**
             * @desc 点击收费按钮后先进行库存验证
             * <AUTHOR>
             * @date 2018/07/25 16:39:28
             */
            async submit() {
                if (this.loading || this.inputCalcLoading || this.buttonLoading) return false;
                /**
                 * @desc 能否编辑诊断信息，如果能编辑则需要走医保诊断编码判断
                 * <AUTHOR> Yang
                 * @date 2020-12-10 09:22:03
                 */
                if (this.clonePrescriptionType > 0 || this.postData.retailType === 2 || !!this.airPharmacyForm) {
                    await this.validateSocialCode(this.postData);
                }

                this.validatePostDataForm();
            },
            async asyncCollectionTraceCodeDialog() {
                // eslint-disable-next-line no-async-promise-executor
                return new Promise(async (resolve, reject) => {
                    this.buttonLoading = true;
                    this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                        formItems: this.traceCodeDispenseItems,
                        dispensedFormItems: this.traceCodeDispensedItems,
                        requiredTraceCode: this.traceCodeCollectionCheck === CollectionTraceCodeCheck.charge,
                        confirmText: '继续收费',
                        confirmInnerSet: false,
                        patientOrderId: this._patientOrderId,
                        sceneType: SceneTypeEnum.CHARGE,
                        needInitValidate: false, // 这个方法调用之前执行过TraceCode.validate了
                        onConfirm: (flatFormItems) => {
                            TraceCode.setChargeFormsTraceCodeList(flatFormItems, this.postData.chargeForms);
                            resolve();
                        },
                        onClose: () => {
                            this.disabledScanBarcode = false;
                            this._collectionTraceCodeDialog = null;
                            reject();
                        },
                    });
                    this.disabledScanBarcode = true;
                    await this._collectionTraceCodeDialog.generateDialogAsync();
                    this.buttonLoading = false;
                });
            },
            /**
             * @desc 收费
             * <AUTHOR>
             * @date 2022/10/10 14:31:35
             */
            validatePostDataForm() {
                if (!this.$refs.postDataForm) return;
                this.$refs.postDataForm.validate(async (valid) => {
                    if (valid) {
                        const {
                            noCheckedItems = [],
                            noGoodsInfoItems = [],
                            disabledItems = [],
                            shortageItems = [],
                        } = getWarnChargeItemGroup(this.postData.chargeForms);

                        if (this.isWholeBillCharge && (disabledItems.length > 0 || shortageItems.length > 0)) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: `收费单内包含停售/库存不足商品，${this.useRetailPostDataForm ? '请调整后再收费' : '请医生调整门诊然后再收费'}`,
                            });
                            return false;
                        }

                        if (noGoodsInfoItems.length > 0 || disabledItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品未创建或者已被停用：',
                                content: [`<span>${noGoodsInfoItems.concat(disabledItems).map((item) => {
                                    return item.name || item.medicineCadn;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }

                        this._chargeDialogInstance = new AbcChargeDialog({
                            pcRouterVm: this.$router,
                            pcStoreVm: this.$store,
                            needLockInventory: true,
                            chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                            chargeTransactions: this.chargeTransactions,
                            postData: this.postData, // 提交的收费单数据
                            chargeSheetId: this.chargeSheetId,
                            chargeStatus: this.chargeStatus,
                            isDaijianCenterDispensing: this.isDaijianCenterDispensing,
                            isReplay: this.isReplay,
                            isShowProfit: this.isCanSeeGoodsCostPriceInCashier,
                            showAbcPayRecommendCard: false,
                            disableDispensingBtn: true,
                            onPartChargeSuccess: this.partChargeSuccess,
                            onChargeSuccess: this.chargeSuccess,
                            onChargeError: this.chargeError,
                            onClose: this.closeChargeDialog,
                            cancelPayCallback: () => {
                                this.setLockInfo(null);
                                this.fetchDetail();
                            },
                            supportConfigPrintList: [
                                ABCPrintConfigKeyMap.cashier.subKey,
                                ABCPrintConfigKeyMap.feeList.subKey,
                                ABCPrintConfigKeyMap.examinationTag.subKey,
                                ABCPrintConfigKeyMap.examinationInspect.subKey,
                            ],
                        });

                        // 需要提示的商品：没有勾选的，库存不足的（包含套餐中的）
                        if (noCheckedItems.length > 0 || shortageItems.length > 0) {
                            let arr = noCheckedItems;
                            // 如果状态为忽略 不会添加库存不足的清单
                            if (this.needCheckStock) {
                                arr = arr.concat(shortageItems);
                            }
                            // 列表为空，重置状态
                            if (arr.length <= 0) {
                                this.startCharging = true;
                                this._chargeDialogInstance.generateDialog({
                                    parent: this,
                                });
                                return false;
                            }
                            new ChargeNoticeDialog({
                                data: arr,
                                confirm: () => {
                                    this.startCharging = true;
                                    this._chargeDialogInstance?.generateDialog({
                                        parent: this,
                                    });
                                },
                            }).generateDialogAsync({
                                parent: this,
                            });

                        } else {
                            this.startCharging = true;
                            this._chargeDialogInstance.generateDialog({
                                parent: this,
                            });
                        }
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },

            continuePay() {
                this.startCharging = true;
                this._chargeDialogInstance = new AbcChargeDialog({
                    pcRouterVm: this.$router,
                    pcStoreVm: this.$store,
                    chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                    chargeTransactions: this.chargeTransactions,
                    postData: this.postData, // 提交的收费单数据
                    chargeSheetId: this.chargeSheetId,
                    chargeStatus: this.chargeStatus,
                    isDaijianCenterDispensing: this.isDaijianCenterDispensing,
                    isReplay: this.isReplay,
                    onPartChargeSuccess: this.partChargeSuccess,
                    onChargeSuccess: this.chargeSuccess,
                    onChargeError: this.chargeError,
                    onClose: this.closeChargeDialog,
                });
                this._chargeDialogInstance.generateDialog({
                    parent: this,
                });
            },

            async partChargeSuccess() {
                this.setAirOrderRemark(this.postData);
                // 部分欠费还款更新欠费单
                if (this._chargeDialogInstance?.instance.chargeType === ChargeType.REPAYMENT) {
                    this.fetchChargeOweSheets();
                }
                // 1.清除草稿
                this.clearDraft();
                // 获取最新的详情
                await this.fetchDetail();
                // 2.更新quicklist状态数量
                await this.refreshCashierQuickList(false);
            },

            /**
             * desc [完成收费]
             */
            async chargeSuccess(data) {
                if (data && data.status !== undefined) {
                    this.chargeStatus = data.status;
                }
                // 组合还款dialog
                if (this.showRepaymentList) {
                    this.showRepaymentList = false;
                }
                this._chargeDialogInstance = null;

                // 收费完成后同时打印
                const uuid = createGUID();
                const { cache } = Printer;
                const printCache = cache.get();
                const {
                    isChargeMeanwhilePrint, cashier,
                } = printCache || {};
                const cacheCashier = clone(cashier);
                Logger.report({
                    scene: 'new_cashier_meanwhile_print',
                    data: {
                        uuid,
                        info: '收费同时打印_收费完成',
                        patientOrderId: this.patientOrderId,
                        chargeSheetId: this.chargeSheetId,
                        cashier: cacheCashier,
                    },
                });
                if (isChargeMeanwhilePrint) {
                    SimpleEventBus.getInstance().emit('cashier-meanwhile-print', {
                        uuid,
                        chargeSheetId: this.chargeSheetId,
                        cashier: cacheCashier,
                    });
                }

                await this.partChargeSuccess(data);
            },

            /**
             * @desc 收费失败
             * <AUTHOR> Yang
             * @date 2020-08-03 17:48:07
             */
            async chargeError(err) {
                const { code } = err || {};
                if (code === 17090) {
                    // 空中药房 快递费发生改变
                    this.postData.chargeForms.forEach((form) => {
                        if (form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY) {
                            form.deliveryInfo.deliveryCompany = {
                                id: '',
                                name: '',
                            };
                            const deliveryItem = form.chargeFormItems.find((item) => {
                                return item.productType === GoodsTypeEnum.EXPRESS_DELIVERY;
                            });
                            if (deliveryItem) {
                                deliveryItem.productInfo = {
                                    ...form.deliveryInfo,
                                };
                            }
                        }
                    });
                }
                await this.$store.dispatch('fetchChargeConfig');
                await this.fetchDetail();
            },

            async closeChargeDialog() {
                this.startCharging = false;
                // 待收状态下，关闭弹窗才算费
                if (this.chargeStatus < ChargeStatusEnum.PART_CHARGED) {
                    await this.calcFee();
                }
                this.$nextTick(() => {
                    if (!this.featureSupportFilterEyeGlasses) {
                        $('.search-header .abc-input__inner').focus();
                    }
                });
                // 关闭收费弹窗需要清除固定
                this.postData.chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        item.isFixedData = 0;
                        item.composeChildren?.forEach((child) => {
                            child.isFixedData = 0;
                        });
                    });
                });
                this._chargeDialogInstance = null;
            },

            refreshHandler() {
                this.clearDraft();
                this.showSendOrderDialog = false;
                this.fetchDetail();
            },

            /**
             * @desc 退费前拉一次数据
             * <AUTHOR>
             * @date 2019/04/07 14:49:08
             */
            async showRefundDialog() {
                const isRefundable = await this.verifyRefundChargeOrder();
                if (!isRefundable) {
                    return;
                }
                await this.fetchDetail();
                if (this.judgeHasNeedRefund()) {
                    this.showRefund = true;
                } else {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: [ '已发出的药品，需要在药房完成退药后才能退费' ],
                    });
                }
            },
            /**
             * desc [判断是否有欠退金额/可退药/议价费用]
             */
            judgeHasNeedRefund() {
                // 判断是否有欠退金额
                if (this.chargeSheetSummary.owedRefundFee !== 0) {
                    return true;
                }
                // 判断是否有议价（加价）未退
                if (this.chargeSheetSummary.netAdjustmentFee > 0) {
                    return true;
                }
                // 当全部全部为已退单时，可退费
                let outAll = false;
                // 判断是否有可退药
                let hasDrug = false;

                this.postData.chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        // 中药根据剩余药品剂数判断 canRefundDoseCount > 0
                        const medicineObjType = {
                            type: item.productType, subType: item.productSubType,
                        };
                        if (isChineseMedicine(medicineObjType) && (item.status === 1 || item.status === 4) && item.canRefundUnitCount > 0 && item.canRefundDoseCount > 0) {
                            hasDrug = true;
                        }
                        if (!isChineseMedicine(medicineObjType) && (item.status === 1 || item.status === 4) && item.canRefundUnitCount > 0) {
                            hasDrug = true;
                        }
                        if (item.status !== 3 && outAll === false) {
                            outAll = true;
                        }
                    });
                });
                if (outAll) {
                    return hasDrug;
                }
                return true;

            },
            /**
             * @desc 多次收费退费
             * <AUTHOR>
             * @date 2018/07/25 14:07:34
             */
            async refundFee() {
                const isRefundable = await this.verifyRefundChargeOrder();
                if (!isRefundable) {
                    return;
                }
                this.$confirm({
                    type: 'warn',
                    title: '退费提示',
                    content: [ `已收费 <span>${this.$t('currencySymbol')} ${formatMoney(this.chargeSheetSummary.netIncomeFee)}</span>，退费后还可以再次收费，确定要退费吗？` ],
                    onConfirm: this.openRefundDialog,
                });
            },

            /**
             * @desc 退费相关数据回调
             * <AUTHOR>
             * @date 2018/07/26 18:07:05
             * @params data {list, refund}
             */
            refundConfirm(data) {
                this.refundTotalFee = data.refundFee; // 需要退的总费用
                this.refundData = data;
                this.curRefundType = RefundTypeEnum.NORMAL;// 选择项目退费时

                // 已开电子发票需要做提示
                const {
                    invoiceType, invoiceSupplierId,
                } = this.invoiceData || {};
                if (invoiceType === InvoiceCategory.ELECTRONIC && invoiceSupplierId !== InvoiceSupplierId.NANJING_WUAI) {
                    this.$confirm({
                        type: 'warn',
                        title: '退费提示',
                        content: '退费后，已开具的电子发票将全部冲红。是否继续退费？',
                        confirmText: '继续退费',
                        onConfirm: () => {
                            this.showRefundWayList = true;
                        },
                    });
                    return false;
                }
                this.showRefundWayList = true;
            },
            /**
             * @desc 选择项目退费
             * <AUTHOR>
             * @date 2018/07/26 18:01:52
             */
            async refundHandler(data) {
                if (this.buttonLoading) return false;
                this.buttonLoading = true;
                Object.assign(this.refundData, data);
                try {
                    const { data } = await ChargeAPI.refund(this.chargeSheetId, this.refundData);
                    this.showRefund = false;
                    this.showRefundWayList = false;
                    this.buttonLoading = false;
                    this.cashier.quickList.forEach((item) => {
                        if (item.id === data.id) {
                            Object.assign(item, {
                                status: data.status,
                                statusName: data.statusName || '',
                            });
                        }
                    });
                    this.$Toast({
                        message: '退费成功',
                        type: 'success',
                    });
                    this.fetchDetail();
                } catch (err) {
                    this.buttonLoading = false;
                }
            },

            /**
             * desc [当退费方式弹窗状态改变时]
             */
            async onChangeValue(value) {
                if (value === false) {
                    // 当关闭时
                    await this.fetchDetail();
                    if (this.$refs.refundPro) {
                        this.$refs.refundPro.resetForms();
                    }
                    // this.refundFinish()
                } else {
                    // 当打开时
                }
            },
            /**
             * desc [完成退费时]
             */
            refundFinish() {
                this.showRefund = false;
                this.showRefundWayList = false;
                this.fetchDetail();
            },
            /**
             * @desc 打开退费窗口
             * <AUTHOR>
             * @date 2018/07/24 15:22:40
             */
            openRefundDialog() {
                this.refundTotalFee = this.chargeSheetSummary.netIncomeFee; // 需要退的总费用
                this.curRefundType = RefundTypeEnum.REFUND_PAID; // 部分退费
                this.showRefundWayList = true;
            },
            async selectPrintConfirm(params, uuid) {
                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_打印用药标签',
                            params: clone(params),
                        },
                    });
                }

                const {
                    selectedData, isManualPreview = true,
                } = params;
                this.showSelectPrint = false;
                // 获取打印所需参数
                const patient = clone(this.postData.patient ?? {});
                const {
                    doctorName, patientOrderNo,
                } = this.postData;
                const forms = selectedData.w.concat(selectedData.c);
                // 获取收费单对应的皮试结果
                try {
                    const infusionForms = forms.filter((form) => {
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN;
                    });
                    if (infusionForms && infusionForms.length) {
                        const astResultData = await PrintAPI.getAstResultByCharge(this.chargeSheetId);
                        const astResultList = astResultData.data?.list || [];
                        infusionForms.forEach((infusionForm) => {
                            infusionForm.formItems.forEach((infusionFormItem) => {
                                const formItemAstData = astResultList.find((astResultItem) => astResultItem.formItemId === infusionFormItem.id);
                                if (formItemAstData) {
                                    infusionFormItem.ast = formItemAstData.ast || 0;
                                    infusionFormItem.astResult = formItemAstData.astResult;
                                }
                            });
                        });
                    }
                } catch (e) {
                    console.error('获取发药单皮试结果失败\n', e);
                }
                const printOptions = getAbcPrintOptions('用药标签', {
                    forms,
                    patient,
                    // 手动打印,不做自动打印筛选
                    isManualPreview,
                    doctorName,
                    patientOrderNo,
                    clinicName: this.currentClinic.clinicName,
                });

                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_用药标签数据',
                            printData: clone(printOptions),
                        },
                    });
                }

                AbcPrinter.abcPrint(printOptions, uuid);
            },

            /**
             * @desc 获取打印二维码
             * <AUTHOR>
             * @date 2021-12-09 16:18:52
             */
            async fetchQrCode() {
                let qrcode = '';
                if (this.isOpenMp) {
                    try {
                        qrcode = await TpsAPI.genQrCode(this._patientOrderId);
                        return qrcode;
                    } catch (e) {
                        qrcode = '';
                    }
                }
                return qrcode;
            },

            tryFetchPrintData(fn, totalCount = 5, delay = 300) {
                return async (...params) => {
                    let count = 0;
                    while (count < totalCount) {
                        try {
                            return await fn(...params);
                        } catch (error) {
                            console.error(error);
                            count++;
                            await sleep(delay);
                        }
                    }
                    return {};
                };
            },

            async fetchPrintData(printType, uuid) {
                Logger.report({
                    scene: 'cashier-meanwhile-print',
                    data: {
                        uuid,
                        info: '收费同时打印_请求打印数据的函数',
                        printType,
                        patientOrderId: this.patientOrderId,
                        chargeSheetId: this.chargeSheetId,
                    },
                });

                let printData = null;
                let isPrintQrcode = true;
                const printLoading = this.$Loading({
                    text: '准备打印...',
                    customClass: 'print-loading-wrapper',
                });

                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_获取打印数据',
                            printType,
                            patientOrderId: this.patientOrderId,
                            chargeSheetId: this.chargeSheetId,
                        },
                    });
                }

                try {
                    // 处方
                    if (printType === this._printOptions.PRESCRIPTION.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.prescriptionPrintByPatientOrderIdForXuFang)(this._patientOrderId, this._chargeSheetId, this.postData.type);
                        printData = data;

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取处方数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    // 治疗单
                    if (printType === this._printOptions.TREATMENT_EXECUTE.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.chargeExecutedPrint)(this.$route.params.id, 5);
                        printData = data;
                        if (this.$abcSocialSecurity.config.isGuangdongShenzhen) {
                            printData.isGuangdongShenzhen = true;
                        }
                        printData.supportInputDays = this.outpatientEmployeeConfig?.diagnosisTreatment?.supportInputDays || 0;

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取治疗单数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    // 输注单
                    if (printType === this._printOptions.INFUSION_EXECUTE.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.chargeExecutedPrint)(this.$route.params.id, 1);
                        printData = data;

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取输注单数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    // 检查检验单
                    if (printType === this._printOptions.EXAMINATION.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.chargeExaminationPrint)(this.$route.params.id, 1);
                        printData = data;

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取检查单数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    if (printType === this._printOptions.INSPECT.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.chargeExaminationPrint)(this.$route.params.id, 2);
                        printData = {
                            ...data, title: '检验单',
                        };

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取检验单数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    // 检验申请单
                    if (printType === this._printOptions.EXAMINATION_APPLY_SHEET.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.printExamApplySheetByChargeId)(this.chargeSheetId, {
                            type: 1,
                        });

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取检验申请单数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }

                        data.rows = data.rows || [];
                        printData = {
                            rows: data.rows.filter((r) => !!r.businessFormItems?.length),
                        };
                    }
                    // 检查申请单
                    if (printType === this._printOptions.INSPECT_APPLY_SHEET.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.printExamApplySheetByChargeId)(this.chargeSheetId, {
                            type: 2,
                        });

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取检查申请单数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }

                        data.rows = data.rows || [];
                        printData = {
                            rows: data.rows.filter((r) => !!r.businessFormItems?.length),
                        };
                    }
                    // 收费小票
                    if (printType === this._printOptions.CASHIER.label) {
                        const { data } = await this.tryFetchPrintData(ChargeAPI.fetchChargePrint)(this.$route.params.id);

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取收费小票数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }

                        const {
                            invoiceCategory, // 发票类型
                            invoiceSupplierId, // 开票供应商
                            type,
                            digitalInvoice,
                        } = data?.invoiceView || {};
                        const invoiceImageUrl = digitalInvoice?.invoiceImageUrl; // 发票url
                        const clinicId = this.currentClinic?.clinicId || data.organ.id;

                        // 构造电子发票链接二维码
                        if (this.printCashierConfig?.invoiceCode && invoiceCategory !== InvoiceCategory.PAPER) {
                            if (invoiceImageUrl && type !== InvoiceViewType.RED && invoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC && invoiceSupplierId === InvoiceSupplierId.FUJIAN_BOSI) {
                                data.invoiceQrcode = await QRCode.toDataURL(invoiceImageUrl, { margin: 0 });
                            } else {
                                const path = invoiceImageUrl && type !== InvoiceViewType.RED ? 'invoice-preview' : 'view-invoice';
                                const fullUrl = `${getOrigin()}/mp/${path}?clinicId=${clinicId}&businessScene=${InvoiceBusinessScene.CHARGE}&businessId=${data.id}`;

                                const { data: shortUrlData } = await ShortUrlAPI.createShortUrl({
                                    fullUrl,
                                });
                                const QrcodeStr = shortUrlData.shortUrl;
                                data.invoiceQrcode = await QRCode.toDataURL(QrcodeStr, { margin: 0 });
                            }
                        }

                        if (this.printCashierConfig?.clinicInfo?.traceCodeQrCode) {
                            try {
                                // 获取追溯码二维码
                                const queryParams = Qs.stringify({
                                    chainId: this.currentClinic?.chain?.id ?? '',
                                    chargeSheetId: this.chargeSheetId,
                                });
                                const fullUrl = `${getOrigin()}/mp/trace-code?${queryParams}`;
                                const { data: shortUrlData } = await ShortUrlAPI.createShortUrl({
                                    fullUrl,
                                });
                                const traceCodeQrLink = shortUrlData.shortUrl;
                                data.traceCodeQrCodeUrl = await QRCode.toDataURL(traceCodeQrLink, { margin: 0 });
                            } catch (e) {
                                console.error('构造追溯码二维码失败\n', e);
                            }
                        }

                        printData = data;
                    }
                    // 退费小票
                    if (printType === this._printOptions.REFUND_CASHIER.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.printCashierRefund)(this.$route.params.id);
                        printData = data;

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取退费小票数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    // 发药小票
                    if (printType === this._printOptions.DISPENSING_ORDER.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.printDispensingInCharge)(this.$route.params.id);
                        printData = data;

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取发药小票数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    // 退药单
                    if (printType === this._printOptions.UNDISPENSING_ORDER.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.printDispensingUndispense)(this.$route.params.id);
                        printData = data;

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取退药单数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    // 患者标签
                    if (printType === this._printOptions.PATIENT_TAG.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.patientTagPrint)(this._patientOrderId);
                        printData = data;
                        isPrintQrcode = false;

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取患者标签数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }
                    }
                    // 样本条码
                    if (printType === this._printOptions.EXAMINATION_CODE.label) {
                        const { data } = await this.tryFetchPrintData(PrintAPI.printExaminationBarCode)(this.$route.params.id);

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取检验条吗数据',
                                    data: clone(data),
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }

                        printData = {
                            rows: data.rows || [],
                        };
                        isPrintQrcode = false;
                    }
                    if (printData && isPrintQrcode) {
                        // 二维码
                        const qrCode = await this.fetchQrCode();

                        if (uuid) {
                            Logger.report({
                                scene: 'cashier-meanwhile-print',
                                data: {
                                    uuid,
                                    info: '收费同时打印_获取二维码数据',
                                    qrCode,
                                    patientOrderId: this.patientOrderId,
                                    chargeSheetId: this.chargeSheetId,
                                },
                            });
                        }

                        const { printData: data } = printData;
                        printData.qrCode = qrCode;
                        if (data) {
                            data.qrCode = qrCode;
                        }
                        if (Array.isArray(printData.dispensingSheets) && printData.dispensingSheets.length && printData.dispensingSheets[0]) {
                            printData.dispensingSheets[0].qrCode = qrCode;
                        }
                    }
                    printData._dispensingConfig = {
                        isTakeMedicationTime: this.dispensingConfig.isTakeMedicationTime,
                    };

                    if (uuid) {
                        Logger.report({
                            scene: 'cashier-meanwhile-print',
                            data: {
                                uuid,
                                info: '收费同时打印_返回请求的打印数据',
                                printData: clone(printData),
                                patientOrderId: this.patientOrderId,
                                chargeSheetId: this.chargeSheetId,
                            },
                        });
                    }

                    return printData;
                } catch (e) {
                    console.error(e);

                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_请求打印数据报错',
                            errorInfo: e.toString?.() || '',
                            printData: clone(printData || {}),
                            patientOrderId: this.patientOrderId,
                            chargeSheetId: this.chargeSheetId,
                        },
                    });
                } finally {
                    printLoading.close();
                }
            },
            /**
             * @desc 打印
             * <AUTHOR>
             * @date 2018/09/29 18:18:29
             * @params type 打印类型 ： list / prescriptions
             */
            async print(printSelected, uuid) {
                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_获取printOptions',
                            selected: clone(printSelected),
                            printOptions: clone(this.printOptions),
                            patientOrderId: this.patientOrderId,
                            chargeSheetId: this.chargeSheetId,
                        },
                    });
                }
                let selecteds = [...printSelected];

                // 处理口腔诊所和其他诊所治疗单命名不同的问题
                const treatmentExecutePrintConfigLabel = ABCPrinterConfig.prescription.find((x) => x.key === 'treatment-execute')?.label;
                const findIndex = selecteds.indexOf(treatmentExecutePrintConfigLabel);
                if (findIndex > -1) {
                    selecteds.splice(findIndex, 1, this._printOptions.TREATMENT_EXECUTE.label);
                }

                selecteds = selecteds.filter((printItem) => {
                    if (printItem === '打印收费票据') {
                        return true;
                    }
                    let printAble = false;
                    this.printOptions.forEach((optionItem) => {
                        if (optionItem.value === printItem && !optionItem.disabled) {
                            printAble = true;
                        }
                    });
                    return printAble;
                });

                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_判断printAble后需要打印的单据',
                            selected: clone(selecteds),
                            printOptions: clone(this.printOptions),
                            patientOrderId: this.patientOrderId,
                            chargeSheetId: this.chargeSheetId,
                        },
                    });
                }

                // 打印迁移版本 ========== 迁移后可删除 ============
                // 注意此处的if else
                // 包含print2字段使用新版打印
                if (selecteds.includes(this._printOptions.TREATMENT_EXECUTE.label) ||
                    selecteds.includes(this._printOptions.INFUSION_EXECUTE.label) ||
                    selecteds.includes(this.examPrintOptions.examination.label) ||
                    selecteds.includes(this.examPrintOptions.inspection.label) ||
                    selecteds.includes(this._printOptions.CASHIER.label) ||
                    selecteds.includes(this._printOptions.DISPENSING_ORDER.label) ||
                    selecteds.includes(this._printOptions.REFUND_CASHIER.label) ||
                    selecteds.includes(this._printOptions.PATIENT_TAG.label) ||
                    selecteds.includes(this._printOptions.EXAMINATION_CODE.label) ||
                    selecteds.includes(this._printOptions.UNDISPENSING_ORDER.label)
                ) {
                    await AbcPrinter.abcPrint(async () => {
                        const printPropsList = [];
                        for (let i = 0; i < selecteds.length; i++) {
                            let select = selecteds[i];

                            if (select === this._printOptions.TREATMENT_EXECUTE.label ||
                                select === this._printOptions.INFUSION_EXECUTE.label ||
                                select === this.examPrintOptions.examination.label ||
                                select === this.examPrintOptions.inspection.label ||
                                select === this._printOptions.CASHIER.label ||
                                select === this._printOptions.DISPENSING_ORDER.label ||
                                select === this._printOptions.REFUND_CASHIER.label ||
                                select === this._printOptions.PATIENT_TAG.label ||
                                select === this._printOptions.EXAMINATION_CODE.label ||
                                select === this._printOptions.UNDISPENSING_ORDER.label
                            ) {

                                let printData = await this.fetchPrintData(select, uuid);

                                if (
                                    [
                                        this._printOptions.EXAMINATION_APPLY_SHEET.label,
                                        this._printOptions.INSPECT_APPLY_SHEET.label,
                                    ].includes(select) && !printData?.rows?.length
                                ) {
                                    // 检查检验申请单不存在，转为打印检查检验单
                                    const map = {
                                        [this._printOptions.EXAMINATION_APPLY_SHEET.label]: this._printOptions.EXAMINATION.label,
                                        [this._printOptions.INSPECT_APPLY_SHEET.label]: this._printOptions.INSPECT.label,
                                    };

                                    select = map[select];

                                    printData = await this.fetchPrintData(select);
                                }
                                const printTaskOptions = getAbcPrintOptions(select, printData);
                                if (select === this._printOptions.EXAMINATION_CODE.label) {
                                    const {
                                        templateKey, printConfigKey,
                                    } = this._printOptions.EXAMINATION_CODE;
                                    printTaskOptions.templateKey = window.AbcPackages?.AbcTemplates?.[templateKey];
                                    printTaskOptions.printConfigKey = ABCPrintConfigKeyMap[printConfigKey];
                                }
                                if (printTaskOptions) {
                                    printPropsList.push(printTaskOptions);
                                }
                            }
                        }
                        return printPropsList;
                    }, uuid);
                }
                if (selecteds.includes(this._printOptions.PRESCRIPTION.label)) {
                    await this.printPrecriptionHandler(uuid);

                }
                if (selecteds.includes('医疗收费清单')) {
                    await this.printFeeList(uuid);
                }
                if (selecteds.includes('医疗收费票据') || selecteds.includes('打印收费票据')) {
                    await this.openInvoiceDialog();
                }
                if (selecteds.includes('用药标签')) {
                    const guid = createGUID();

                    Logger.reportAnalytics('medicine-tag-print', {
                        type: '用药标签打印',
                        from: '收费站',
                        info: '开始打印',
                        guid,
                    });

                    await this.printMedicineTagHandler(guid);
                    // this.showSelectPrint = true;
                }
                if (selecteds.includes('收据')) {
                    await this.printReceipt();
                }
                // 社保其余打印类型
                if (this.socialPrintType) {
                    for (const socialPrintType of this.socialPrintType) {
                        if (selecteds.includes(socialPrintType.value)) {
                            const printResponse = await this.$abcSocialSecurity.executePrint({
                                printId: socialPrintType.id,
                                shebaoSettlePrintSheetId: this.shebaoSettlePrintSheetId,
                            });
                            if (printResponse.status === false) {
                                return this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: printResponse.message || '调用结算单打印出错',
                                });
                            }
                            await this.printSocialSettlementSheet(uuid, printResponse);
                        }
                    }
                }
            },
            async printReceipt() {
                try {
                    const { data } = await InvoiceAPI.fetchChargeSheetReceipt(this.chargeSheetId);
                    const { format } = this.printReceiptConfig || {};
                    const templateName = `medicalBill${format[0].toUpperCase()}${format.slice(1)}`;

                    const printOption = {
                        templateKey: window.AbcPackages.AbcTemplates[templateName],
                        printConfigKey: ABCPrintConfigKeyMap.receipt,
                        data,
                        matchTemplateCallback: ({ template: { templates = [] } }) => {
                            return matchTemplate(format, templates, data);
                        },
                        extra: {
                            receiptConfig: this.printReceiptConfig,
                        },
                        onPrintSuccess: async () => {
                            // 上报打印次数
                            const businessType = PrintCountBusinessTypeEnum.RECEIPT;
                            try {
                                await PrintAPI.addOrUpdateChargePrintLog({
                                    businessId: this.chargeSheetId,
                                    businessType,
                                });
                            } catch (e) {
                                console.error('打印记录上报失败', e);
                            }

                        },
                    };
                    AbcPrinter.abcPrint(printOption);
                } catch (e) {
                    console.error('收据打印出错', e);
                }
            },
            async printFeeList(uuid) {
                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_打印收费医疗清单',
                        },
                    });
                }

                const { data } = await ChargeAPI.fetchChargePrint(this.chargeSheetId);

                if (this.printMedicalListConfig?.[this.printMedicalListConfig?.format]?.traceCode) {
                    try {
                        // 获取追溯码二维码
                        const queryParams = Qs.stringify({
                            chainId: this.currentClinic?.chain?.id ?? '',
                            chargeSheetId: this.chargeSheetId,
                        });
                        const fullUrl = `${getOrigin()}/mp/trace-code?${queryParams}`;
                        const { data: shortUrlData } = await ShortUrlAPI.createShortUrl({
                            fullUrl,
                        });
                        const traceCodeQrLink = shortUrlData.shortUrl;
                        data.traceCodeQrCodeUrl = await QRCode.toDataURL(traceCodeQrLink, { margin: 0 });
                    } catch (e) {
                        console.error('构造追溯码二维码失败\n', e);
                    }
                }

                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_收费医疗清单请求返回值',
                            data: clone(data),
                            printMedicalListConfig: clone(this.printMedicalListConfig),
                        },
                    });
                }

                const { format } = this.printMedicalListConfig;
                if (!format) {
                    return console.error('没有format，无法挂载AbcPint');
                }
                const templateName = `medicalFeeList${format[0].toUpperCase()}${format.slice(1)}`;
                console.log(`正在初始化模板:[${templateName}]`);

                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_收费医疗清单数据',
                            data: clone(data),
                        },
                    });
                }

                await AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates[templateName],
                    printConfigKey: ABCPrintConfigKeyMap.feeList,
                    data,
                    extra: {
                        $abcSocialSecurity: this.$abcSocialSecurity,
                    },
                    isDevTools: false,
                }, uuid);
            },
            async printSocialSettlementSheet(uuid, printResponse) {
                const {
                    html,
                    pdfBase64,
                    printConfigKey,
                } = printResponse.data || {};

                if (html) {
                    await AbcPrinter.abcPrint({
                        templateKey: window.AbcPackages.AbcTemplates.medicalFeeSocial,
                        printConfigKey: ABCPrintConfigKeyMap.social,
                        data: {},
                        extra: {
                            // 移除医保传递的外部div
                            // 避免分页的问题
                            getHTML: () => html.replace('<div class="print-stat-wrapper">', '').replace(/<\/div>$/, ''),
                        },
                    }, uuid);
                } else if (pdfBase64) {
                    await pdfLodopPrint({
                        pdfBase64,
                        printConfigKey,
                    });
                } else {
                    //
                }
            },
            /**
             * @Description: 处理处方分类逻辑
             * <AUTHOR> Cai
             * @date 2022/07/05 11:08:35
             */
            async printPrecriptionHandler(uuid) {
                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_打印处方',
                            patientOrderId: this.patientOrderId,
                            chargeSheetId: this.chargeSheetId,
                        },
                    });
                }

                // 确保打印已加载
                try {
                    await loadAbcPrint({
                        isEnableDesktopPrint: this.chainBasic.isEnableDesktopPrint, printConfig: this.clinicBasic.printConfig,
                    });
                } catch (error) {
                    console.error('加载打印失败:', error);
                }

                const data = await this.fetchPrintData(this._printOptions.PRESCRIPTION.label, uuid) || {};

                if (uuid) {
                    Logger.report({
                        scene: 'cashier-meanwhile-print',
                        data: {
                            uuid,
                            info: '收费同时打印_获取处方数据返回值',
                            data: clone(data),
                            patientOrderId: this.patientOrderId,
                            chargeSheetId: this.chargeSheetId,
                        },
                    });
                }

                if (data.prescriptionPrintType === PrecriptionPrintType.NOMAL_PR || data.prescriptionPrintType === PrecriptionPrintType.HISTORY_PR) {
                    const { printData } = data;
                    printData._dispensingConfig = {
                        isTakeMedicationTime: this.dispensingConfig.isTakeMedicationTime,
                    };

                    if (uuid) {
                        Logger.report({
                            scene: 'cashier-meanwhile-print',
                            data: {
                                uuid,
                                info: '收费同时打印_打印普通处方或历史续方',
                                printData: clone(printData),
                                patientOrderId: this.patientOrderId,
                                chargeSheetId: this.chargeSheetId,
                            },
                        });
                    }

                    await AbcPrinter.abcPrint({
                        templateKey: window.AbcPackages.AbcTemplates.prescription,
                        printConfigKey: ABCPrintConfigKeyMap.prescription,
                        data: printData,
                    }, uuid);
                } else if (data.prescriptionPrintType === PrecriptionPrintType.IMG_PR) {
                    const { attachments: printImgList = [] } = data.printData;

                    if (uuid) {
                        Logger.report({
                            scene: 'cashier-meanwhile-print',
                            data: {
                                uuid,
                                info: '收费同时打印_打印拍照续方',
                                printImgList: clone(printImgList),
                                patientOrderId: this.patientOrderId,
                                chargeSheetId: this.chargeSheetId,
                            },
                        });
                    }

                    await AbcPrinter.abcPrint({
                        templateKey: window.AbcPackages.AbcTemplates.prescriptionXufang,
                        printConfigKey: ABCPrintConfigKeyMap.prescription,
                        data: {},
                        extra: {
                            getHTML: () => {
                                let html = '';
                                for (const img of printImgList) {
                                    html += this.imgDirectionHandle(img.url);
                                }
                                return `<div>${html}</div>`;
                            },
                        },
                    }, uuid);
                }
            },
            /**
             * @Description: 根据图片不同方向渲染
             * <AUTHOR> Cai
             * @date 2022/07/05 15:57:56
             */
            imgDirectionHandle(img) {
                return `
                        <div>
                             <div style="height: 600px;"></div>
                             <div style="position: absolute;top:0;width: 100%; height: 100%; margin: 0 auto;
                             text-align: center;padding: 5mm;box-sizing: border-box;">
                                <img src="${img}" style="max-width: 100%; max-height: 100%;" alt="">
                            </div>
                        </div>
                        `;
            },

            printHandler(selected) {
                this.print(selected);
            },
            async fetchPrintList() {
                try {
                    const { data } = await ChargeAPI.fetchChargePrintButtonData(this.chargeSheetId);
                    this.printable = data;
                } catch (e) {
                    console.error('获取收费单打印数据失败', e);
                }
            },
            /**
             * @desc 全部收费退完后，可以进行重新收费；
             * @desc 前端放开禁用，让用户可以重新选择
             * <AUTHOR>
             * @date 2019/07/04 16:41:39
             */
            clickAgainCharge() {
                // 长护重新收费直接到欠费
                if (this.isHospitalSheet) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '该收费单已全部退费，是否确定重新设置为欠费？',
                        onConfirm: () => {
                            this.repaidHospital();
                        },
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '该收费单已全部退费，是否确定打开该收费单并重新收费？',
                        onConfirm: async () => {
                            await this.renewSheet();
                            this.fetchDetail();
                        },
                    });
                }
            },

            // 退费的单据重新打开
            async renewSheet() {
                try {
                    this.loading = true;
                    await ChargeAPI.renew(this.chargeSheetId);
                } catch (e) {
                    Logger.error({
                        scene: 'renew_charge_sheet_error',
                        err: e,
                    });
                } finally {
                    this.loading = false;
                }
            },

            keydownF9Handle(event) {
                if (this._collectionTraceCodeDialog) return;
                if (!AbcAccess.check()) return;
                if (this.lockedInfo) return;

                if (this.chargeStatus > ChargeStatusEnum.PART_CHARGED) return;
                const KEY_F9 = 120;
                if (event.keyCode === KEY_F9) {
                    /**
                     * @desc 当存在转录弹窗时，不打开收费弹窗
                     * <AUTHOR>
                     * @date 2020/05/28 17:12:43
                     */
                    const $writeByPhoto = $('.abc-dialog.write-by-photo');
                    if ($writeByPhoto.length) {
                        return false;
                    }

                    $(this.$el).find('.seller-selector input').blur();
                    event.cancelBubble = true;
                    event.returnValue = false;
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();

                    // 调起收费弹窗的时候需要让下拉框失焦
                    this.closeSwitch = true;
                    this.$nextTick(() => {
                        this.closeSwitch = false;
                        $('.abc-select-wrapper.is-show-popper input').click();
                    });

                    /**
                     * @desc 页面上会处理一些逻辑，延迟触发提交验证
                     * <AUTHOR>
                     * @date 2020/03/13 10:18:27
                     */
                    this.timeoutId = setTimeout(() => {
                        if (this._chargeDialogInstance) {
                            this._chargeDialogInstance.destroyDialog();
                            this._chargeDialogInstance = null;
                            this.startCharging = false;
                        } else {
                            this.submit();
                        }
                    }, 0);
                }
            },

            /**
             * @desc 对比form是否有修改
             * <AUTHOR>
             * @date 2020/03/27 12:18:31
             * @return [Array] forms
             */
            chargeFormIsUpdated(form) {
                let hasDelete = false;
                let hasChangeAirPharmacyDelivery = false;
                let hasChangeProcess = false;
                this._postDataCache && this._postDataCache.chargeForms.forEach((formCache) => {
                    if (formCache.id === form.id) {
                        // 只对比服务器端的数据，有id的 length不一致认为删除了；
                        const cacheLen = formCache.chargeFormItems.filter((item) => item.id).length;
                        const itemLen = form.chargeFormItems.filter((item) => item.id).length;
                        if (cacheLen !== itemLen) {
                            hasDelete = true;
                        }
                        if (form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY || form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                            const _isChange = [
                                'medicalRecord',
                                'usageInfo',
                                'vendorName',
                            ].filter((key) => {
                                return !isEqual(formCache[ key ], form[ key ]);
                            }).length;

                            const _isChangeDelivery = [
                                'addressCityId',
                                'addressDetail',
                                'addressDistrictId',
                                'addressProvinceId',
                                'deliveryName',
                                'deliveryMobile',
                                'deliveryCompany',
                            ].filter((key) => {
                                return !isEqual(formCache.deliveryInfo?.[ key ], form.deliveryInfo?.[ key ]);
                            }).length;

                            if (_isChange || _isChangeDelivery) {
                                hasChangeAirPharmacyDelivery = true;
                            }
                        }

                        if (form.sourceFormType === SourceFormTypeEnum.DECOCTION && form.processInfo) {
                            hasChangeProcess = compareProcessKey.filter((key) => {
                                return !isEqual(formCache.processInfo[ key ], form.processInfo[key]);
                            }).length > 0;
                        }
                    }
                });
                if (hasDelete || hasChangeAirPharmacyDelivery || hasChangeProcess) {
                    return form;
                }

                // 判断是否有议价
                if (form.expectedTotalPrice) {
                    return form;
                }

                // 对比出修改的item
                const _draftItems = form.chargeFormItems
                    .filter((item) => {
                        return this.chargeFormItemIsUpdated(form, item);
                    })
                    .map((item) => {
                        return {
                            id: item.id,
                            productId: item.productId,
                            expectedUnitPrice: item.expectedUnitPrice,
                            expectedTotalPrice: item.expectedTotalPrice,
                            unitCount: item.unitCount,
                            doseCount: item.doseCount,
                        };
                    });
                if (_draftItems.length) {
                    return {
                        id: form.id,
                        chargeFormItems: _draftItems,
                    };
                }
                return null;
            },

            compareUpdate() {
                const draftForms = [];

                this.postData.chargeForms.forEach((form) => {
                    if (form.id) {
                        const hasChangeForm = this.chargeFormIsUpdated(form);
                        hasChangeForm && draftForms.push(hasChangeForm);
                    }
                    // 新加的form都记录草稿
                    if ((!form.id && form.chargeFormItems.length) || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_GLASSES) {
                        draftForms.push(form);
                    }
                });
                const _cacheLength = this._postDataCache && this._postDataCache.chargeForms.length;
                const _length = this.postData.chargeForms.length;

                const unCharge = this.chargeStatus < ChargeStatusEnum.PART_CHARGED;
                const hasChangeForms = draftForms.length > 0 || _cacheLength !== _length;
                const hasChangeAdjustmentFee = this.postData.expectedAdjustmentFee !== this._postDataCache.expectedAdjustmentFee;

                const hasChangeDelivery = this._postDataCache.patient.id === this.postData.patient.id &&
                    !!compareAddressKey.filter((key) => {
                        return !isEqual(this._postDataCache.deliveryInfo[ key ], this.postData.deliveryInfo[ key ]);
                    }).length;

                const hasChangePatient = !!comparePatientKey.filter((key) => {
                    return !isEqual(this._postDataCache.patient[ key ], this.postData.patient[ key ]);
                }).length;

                /**
                 * @desc 挂单修改患者 更新QL
                 * <AUTHOR> Yang
                 * @date 2020-06-19 11:07:01
                 */
                if (hasChangePatient && this.cashier.selectedItem) {
                    Object.assign(this.cashier.selectedItem.patient, {
                        name: this.postData.patient.name,
                        sex: this.postData.patient.sex,
                    });
                }

                const hasChangeContactMobile = this.postData.contactMobile !== this._postDataCache.contactMobile;
                const hasChangeOutpatientInfo = this.postData.chiefComplaint !== this._postDataCache.chiefComplaint ||
                    this.postData.diagnosis !== this._postDataCache.diagnosis ||
                    this.postData.departmentId !== this._postDataCache.departmentId ||
                    this.postData.doctorId !== this._postDataCache.doctorId;

                const hasChangePatientPointsInfo = !isEqual(this._postDataCache.patientPointsInfo, this.postData.patientPointsInfo);
                const changedPromotions = this.getUpdatedPromotions(
                    ['checked'],
                    this.postData.promotions,
                    this._postDataCache.promotions,
                );
                const changedGiftRulePromotions = this.getUpdatedPromotions(
                    ['checked'],
                    this.postData.giftRulePromotions,
                    this._postDataCache.giftRulePromotions,
                );
                const changedCouponPromotions = this.getUpdatedPromotions(
                    ['checked','currentCount'],
                    this.postData.couponPromotions,
                    this._postDataCache.couponPromotions,
                );
                const changedPatientCardPromotions = this.getUpdatedPromotions(
                    ['checked'],
                    this.postData.patientCardPromotions,
                    this._postDataCache.patientCardPromotions,
                );
                const changedPatientPointDeductProductPromotions = this.getUpdatedPromotions(
                    ['checked'],
                    this.postData.patientPointDeductProductPromotions,
                    this._postDataCache.patientPointDeductProductPromotions,
                );

                const hasChangeSeller = (
                    !isEqual(this._postDataCache.sellerId, this.postData.sellerId) ||
                    !isEqual(this._postDataCache.sellerDepartmentId, this.postData.sellerDepartmentId)
                );

                if ((unCharge && (hasChangeForms || hasChangePatient)) ||
                    hasChangeOutpatientInfo ||
                    hasChangeDelivery ||
                    hasChangeAdjustmentFee ||
                    hasChangeContactMobile ||
                    changedPromotions.length ||
                    changedGiftRulePromotions.length ||
                    changedCouponPromotions.length ||
                    changedPatientCardPromotions.length ||
                    hasChangePatientPointsInfo ||
                    changedPatientPointDeductProductPromotions.length ||
                    hasChangeSeller
                ) {
                    this.isUpdate = true;
                } else {
                    this.isUpdate = false;
                    this.clearDraft();
                }
            },
            // 和服务器缓存的postData对比，是否有修改
            chargeFormItemIsUpdated(form, item) {
                let flag = false;
                this._postDataCache && this._postDataCache.chargeForms.forEach((formCache) => {
                    if (formCache.id === form.id) {
                        if (formCache.chargeFormItems.length !== form.chargeFormItems.length) {
                            flag = true;
                        } else {
                            formCache.chargeFormItems.forEach((itemCache) => {
                                if (item.id === itemCache.id) {
                                    // 判断是否有修改数量
                                    if (+item.unitCount !== +itemCache.unitCount ||
                                        +item.doseCount !== +itemCache.doseCount) {
                                        flag = true;
                                    }
                                    // 判断是否有议价
                                    if (item.expectedUnitPrice ||
                                        item.expectedTotalPrice) {
                                        flag = true;
                                    }
                                    // 判断是否有剂数的修改
                                    if (item.expectedDoseCount) {
                                        flag = true;
                                    }

                                    // 判断药房修改
                                    if (item.pharmacyNo !== itemCache.pharmacyNo) {
                                        flag = true;
                                    }

                                    // 备注是否修改
                                    if (item.specialRequirement !== itemCache.specialRequirement) {
                                        flag = true;
                                    }
                                    if (item.remark !== itemCache.remark) {
                                        flag = true;
                                    }
                                    if (item.checked !== itemCache.checked) {
                                        flag = true;
                                    }
                                }
                            });
                        }
                    }
                });
                return flag;
            },

            getUpdatedPromotions(compareKey, newPromotions, oldPromotions) {
                const noEquals = [];
                oldPromotions.forEach((prevValue) => {
                    const afterValue = newPromotions.find((it) => prevValue.id === it.id);
                    let _arr = [];
                    if (afterValue) {
                        _arr = compareKey.filter((key) => {
                            return !isEqual(prevValue[ key ], afterValue[ key ]);
                        });
                    }

                    if (_arr.length) {
                        noEquals.push({
                            newValue: afterValue,
                            oldValue: prevValue,
                        });
                    }
                });
                return noEquals;
            },

            /**
             * @desc 挂单
             * <AUTHOR>
             * @date 2019/11/21 17:37:45
             */
            async hangUpOrder() {
                if (this.validateCompany().flag) return;

                this.$refs.postDataForm.validate((valid) => {
                    if (valid) {
                        this.$confirm({
                            type: 'warn',
                            title: '确定挂单？',
                            content: '收费单挂单后会进入挂单列表，以便随时调出。',
                            onConfirm: () => {
                                this.hangUpOrderSubmit();
                            },
                        });
                    }
                }, ['seller-selector-wrapper', 'outpatient-info-doctor', 'outpatient-info-diagnosis']);
            },

            async hangUpOrderSubmit() {
                try {
                    this.buttonLoading = true;

                    this.postData.chargeForms.forEach((form, PIndex) => {
                        form.sort = PIndex;
                        form.chargeFormItems.forEach((item, index) => {
                            item.sort = index;
                        });
                    });
                    const chargeForms = clone(this.postData.chargeForms).map((form) => {
                        form.chargeFormItems = form.chargeFormItems.filter((item) => item.checked);
                        return form;
                    });
                    await ChargeAPI.hangUpOrder({
                        isRenew: +this.isReplay,
                        id: this.chargeSheetId,
                        ...this.postData,
                        chargeForms,
                    });
                    // 1.清除草稿
                    this.clearDraft();
                    // 2.更新quicklist状态数量
                    await this.refreshCashierQuickList();
                    this.$Toast({
                        message: '挂单成功',
                        type: 'success',
                    });
                    this.selectFirst();
                    this.buttonLoading = false;
                } catch (err) {
                    this.buttonLoading = false;
                    const { message } = err;
                    message && this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: message,
                        onClose: () => {
                            this.clearDraft();
                            this.refreshCashierQuickList();
                            this.fetchDetail(null, true);
                        },
                    });
                }
            },
            /**
             * @desc 删除开单收费
             * <AUTHOR>
             * @date 2019/10/28 10:26:07
             */
            deleteServerDraft() {
                this.$confirm({
                    type: 'warn',
                    title: '删除确认',
                    content: '删除后不能恢复。是否确定删除本收费单？',
                    onConfirm: () => {
                        this.deleteServerDraftSubmit();
                    },
                });
            },
            async deleteServerDraftSubmit() {
                try {
                    this.clearDraft();
                    await ChargeAPI.deleteChargeSheet(this.chargeSheetId);
                    // 2.更新quicklist状态数量
                    await this.refreshCashierQuickList();
                    // 3.选中第一个
                    this.selectFirst();
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                } catch (err) {
                    const { message } = err;
                    message && this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: message,
                        onClose: () => {
                            this.clearDraft();
                            this.refreshCashierQuickList();
                            this.fetchDetail(null, true);
                        },
                    });
                }
            },

            /**
             * @desc 判断是否选中第一个 quicklist item
             * <AUTHOR>
             * @date 2019/02/27 13:56:30
             */
            selectFirst() {
                let selectedItem;

                if (this.draftCashierNews.length && this.cashier.scrollParams.tab === 1) {
                    selectedItem = this.draftCashierNews[ 0 ];
                } else {
                    if (this.cashier.quickList.length > 0) {
                        selectedItem = this.cashier.quickList[ 0 ];
                    } else {
                        selectedItem = null;
                    }
                }
                this.select(selectedItem);
            },

            /**
             * @desc 选择到某一个单子
             * <AUTHOR>
             * @date 2019/11/21 18:03:17
             */
            select(selectedItem) {
                this.$store.dispatch('setSelectedItem', {
                    type: 'cashier',
                    selectedItem,
                });

                this._selectedTab = +this.$route.query.tab || 1;

                if (!selectedItem) {
                    this.replaceRouter('cashier');
                    return false;
                }

                if (selectedItem.draftId) {
                    this.replaceRouter(`cashier/add/${selectedItem.draftId}`);
                } else if (selectedItem.id) {
                    this.replaceRouter(`cashier/${selectedItem.id}`);
                } else {
                    this.replaceRouter('cashier');
                }
            },

            replaceRouter(path) {
                const { routeBasePath } = this.viewDistributeConfig.Cashier;
                this.$router.replace({
                    path: routeBasePath + path,
                    query: {
                        tab: this._selectedTab,
                    },
                });
            },

            validateCompany() {
                let flag = false;
                const deliveryForm = this.postData.chargeForms.find((item) => {
                    return item.sourceFormType === SourceFormTypeEnum.EXPRESS_DELIVERY;
                });
                if (deliveryForm) {
                    const {
                        deliveryPayType,
                        deliveryCompany,
                    } = this.postData.deliveryInfo;
                    const { chargeFormItems } = deliveryForm;
                    const checked = chargeFormItems && chargeFormItems[0] && deliveryForm.chargeFormItems[0].checked;
                    if (checked && deliveryPayType === 1 && (!deliveryCompany || !deliveryCompany.id)) {
                        flag = true;
                    }
                }
                const airPharmacyForms = this.postData.chargeForms.filter((item) => {
                    return item.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY;
                });
                airPharmacyForms.forEach((form) => {
                    // 有选中的药品才判断是否选择地址
                    const checkedMedicines = form.chargeFormItems.filter((item) => {
                        return item.productType !== GoodsTypeEnum.EXPRESS_DELIVERY &&
                            item.productType !== GoodsTypeEnum.DECOCTION &&
                            item.productType !== GoodsTypeEnum.INGREDIENT;
                    }).filter((item) => {
                        return item.checked;
                    });
                    const { deliveryInfo } = form;
                    const { deliveryCompany } = deliveryInfo || {};
                    if (checkedMedicines.length && (!deliveryCompany || !deliveryCompany.id)) {
                        flag = true;
                    }
                });
                return {
                    flag,
                    tips: '请选择快递公司',
                };
            },

            /**
             * @desc 保存收费单
             * <AUTHOR>
             * @date 2020/02/12 22:34:51
             */
            saveOrder() {
                if (this.loading || this.inputCalcLoading) return false;
                if (this.validateCompany().flag) return;
                this.$refs.postDataForm.validate((valid) => {
                    if (valid) {
                        this.saveOrderSubmit();
                    }
                }, ['seller-selector-wrapper', 'outpatient-info-doctor', 'outpatient-info-diagnosis']);
            },
            async saveOrderSubmit() {
                try {
                    this.saveBtnLoading = true;
                    this.loading = true;

                    this.postData.chargeForms.forEach((form, PIndex) => {
                        form.sort = PIndex;
                        form.chargeFormItems.forEach((item, index) => {
                            item.sort = index;
                        });
                    });

                    const chargeForms = clone(this.postData.chargeForms).map((form) => {
                        form.chargeFormItems = form.chargeFormItems.filter((item) => item.checked).map((item) => {
                            return {
                                ...item,
                                traceableCodeList: item.traceableCodeList ? item.traceableCodeList.map((code) => {
                                    const {
                                        count, ...restCode
                                    } = code;
                                    return restCode;
                                }) : [],
                                composeChildren: item.composeChildren && item.composeChildren.map((it) => {
                                    return {
                                        id: it.id,
                                        traceableCodeList: it.traceableCodeList ? it.traceableCodeList.map((code) => {
                                            const {
                                                count, ...restCode
                                            } = code;
                                            return restCode;
                                        }) : [],
                                    };
                                }),
                            };
                        });
                        return form;
                    });

                    await ChargeAPI.saveOrder({
                        id: this.chargeSheetId,
                        isRenew: +this.isReplay,
                        ...this.postData,
                        chargeForms,
                    }, true);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    // 1.清除草稿
                    this.clearDraft();
                    this.loading = false;
                    this.fetchDetail();
                    this.saveBtnLoading = false;
                    this.$store.dispatch('refreshCashierQuickList', false);
                } catch (err) {
                    this.saveBtnLoading = false;
                    this.loading = false;
                    const {
                        code, message,
                    } = err;
                    if (code === 17010) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    } else {
                        message && this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                            onClose: () => {
                                this.clearDraft();
                                this.isUpdate = false;
                                this.refreshCashierQuickList();
                                this.fetchDetail(null, true);
                            },
                        });
                    }
                }
            },

            handleUpdateInfo(e) {
                this.postData.patient = e;
            },

            async sendOrder() {

                if (this.chargeStatus > ChargeStatusEnum.UN_CHARGE) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '已进行了部分收费，无法给患者推送支付订单',
                    });
                    return false;
                }

                if (this.clonePrescriptionType === 1 ||
                    this.clonePrescriptionType === 2 ||
                    this.postData.retailType === 2) {
                    await this.validateSocialCode(this.postData);
                }

                if (!this.autoSendOrderInfoSwitch && this.validateCompany().flag) return;

                this.$refs.postDataForm.validate((valid) => {
                    if (valid) {
                        const {
                            noGoodsInfoItems,
                            disabledItems,
                            shortageItems,
                        } = getWarnChargeItemGroup(this.postData.chargeForms);

                        if (noGoodsInfoItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品无商品资料：',
                                content: [`<span>${noGoodsInfoItems.map((item) => {
                                    return item.name || item.medicineCadn;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }
                        if (disabledItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品已停用不可推送，请修改后重试：',
                                content: [`<span>${disabledItems.map((item) => {
                                    return item.name || item.medicineCadn;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }
                        if (this.disableNoStockGoods && shortageItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品库存不足不可推送，请修改后重试：',
                                content: [`<span>${shortageItems.map((item) => {
                                    let str = '';
                                    if (item.parentName) {
                                        str += `【${item.parentName}】`;
                                    }
                                    str += item.name || item.medicineCadn;
                                    return str;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }
                        if (shortageItems.length > 0) {
                            ChargeNoticeDialog({
                                data: shortageItems,
                                headTips: '库存不足的药品物资不可推送，请修改后重试',
                                showConfirm: false,
                                confirm: () => {
                                    this.showSendOrderDialog = true;
                                },
                            });
                        } else {
                            this.showSendOrderDialog = true;
                        }
                    }
                });
            },

            /**
             * @desc 关闭收费单
             * <AUTHOR> Yang
             * @date 2021-03-02 17:32:40
             */
            closeOrderHandle() {
                let content = '确定关闭收费单？';
                if (this.isServerDraft) {
                    content = '关闭收费单，将同时取消挂单。确定关闭？';
                }
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content,
                    onConfirm: this.closeChargeOrderSubmit,
                });
            },
            async closeChargeOrderSubmit() {
                try {
                    await ChargeAPI.closeChargeOrder(this.chargeSheetId);
                    this.$Toast({
                        message: '关闭成功',
                        type: 'success',
                    });
                    this.$store.dispatch('refreshCashierQuickList', false);
                    await this.fetchDetail();
                } catch (e) {
                    console.error(e);
                    const {
                        code, message,
                    } = e;
                    if (code === 17010) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    }
                }
            },

            /**
             * @desc 重新打开已关闭的收费单
             * <AUTHOR> Yang
             * @date 2021-03-02 19:19:52
             */
            openCloseOrderHandle() {
                this.$confirm({
                    title: '提示',
                    content: '该收费单已关闭。是否确定打开该收费单并重新收费？',
                    onConfirm: async () => {
                        await this.openSheet();
                        this.fetchDetail();
                    },
                });
            },

            // 关闭的单据重新打开
            async openSheet() {
                try {
                    this.loading = true;
                    await ChargeAPI.openChargeSheet(this.chargeSheetId);
                } catch (e) {
                    Logger.error({
                        scene: 'open_charge_sheet_error',
                        err: e,
                    });
                } finally {
                    this.loading = false;
                }
            },

            async openInvoiceDialog() {
                const _trans = this.chargeTransactions.find((item) => {
                    return item.payMode === 5 && item.thirdPartyPayInfo;
                });
                let buyerName = '';
                let disabledBuyerName = false;
                if (_trans) {
                    disabledBuyerName = true;
                    buyerName = _trans.thirdPartyPayInfo.cardOwner || '';
                }
                buyerName = buyerName || this.postData.patient.name;
                console.log('this.postData.patient=', this.postData.patient);
                await new InvoiceDialog({
                    chargeSheetId: this.chargeSheetId,
                    chargeStatus: this.chargeStatus,
                    patientInfo: {
                        patientId: this.postData.patient.id,
                        buyerPhone: this.postData.patient.mobile,
                        idCard: this.postData.patient.idCard,
                        email: this.postData.patient?.email || '',
                        buyerName,
                        disabledBuyerName,
                    },
                    printBillConfig: this.printBillConfig,
                    printMedicalListConfig: this.printMedicalListConfig,
                    medicalElectronicAPIConfig: this.medicalElectronicAPIConfig,
                    writeInvoiceConfig: this.writeInvoiceConfig,
                    isOpenInvoice: this.isOpenInvoice,
                    isOpenMedicalInvoice: this.isOpenMedicalInvoice,
                    invoiceStatus: this.invoiceStatus,
                    userInfo: this.userInfo,
                    invoiceConfigList: this.invoiceConfigList,
                    businessType: InvoiceBusinessScene.CHARGE,
                    isOpenElecSetlCertUpload: this.basicInfo?.isOpenElecSetlCertUpload || 0,
                    toBillPrintSetting: () => {
                        navigateToInvoiceConfig(this.currentClinic);
                    },
                    updateInvoiceStatus: (status) => {
                        this.invoiceStatus = status;
                    },
                    updateInvoice: () => {
                        // 发票更新,刷新QL列表
                        this.$abcEventBus.$emit('open-invoice-refresh-quick-list');
                        // 发票更新,重新拉取发票列表
                        this.fetchInvoiceList();
                    },

                }).generateDialog({ parent: this });

            },

            /**
             * @desc 出院结算
             * <AUTHOR>
             * @date 2022-02-21 15:19:29
             */
            async handleClickDischarge() {
                try {
                    if (this.hospitalOwedStatus === OwedChargeStatusEnum.OWING) {
                        await ChargeAPI.checkHospitalCanPaid(this.hospitalInfo.hospitalSheetId);
                    }
                    new DischargeSettlementDialog({
                        pcRouterVm: this.$router,
                        chargeConfig: this.chargeConfig,
                        hospitalInfo: this.hospitalInfo,
                        onSuccess: () => {
                            this.fetchDetail();
                        },
                    }).generateDialog({
                        parent: this,
                    });
                } catch (err) {
                    const {
                        detail,
                    } = err;
                    new SettleErrorDialog({
                        errorList: detail,
                    }).generateDialog({ parent: this });
                }
            },

            // 打开还款收费dialog
            openRepaymentChargeDialog(chargeOweSheets) {
                this._chargeDialogInstance = new AbcChargeDialog({
                    pcRouterVm: this.$router,
                    pcStoreVm: this.$store,
                    chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                    postData: this.postData, // 提交的收费单数据
                    chargeSheetId: this.chargeSheetId,
                    chargeStatus: this.chargeStatus,
                    dialogTitle: '还款',
                    isDaijianCenterDispensing: this.isDaijianCenterDispensing,
                    hiddenPayModeList: [PayModeEnum.ARREARS], // 还款不能欠费支付
                    isReplay: this.isReplay,
                    chargeType: ChargeType.REPAYMENT,
                    disablePrintBtn: true,
                    chargeOweSheets,
                    onPartChargeSuccess: this.partChargeSuccess,
                    onChargeSuccess: this.chargeSuccess,
                    onChargeError: this.chargeError,
                    onClose: this.closeChargeDialog,
                });
                this._chargeDialogInstance.generateDialog({
                    parent: this,
                });
            },

            /**
             * @desc 获取欠费还款数据
             * <AUTHOR>
             * @date 2022/05/25 13:56:08
             */
            async fetchChargeOweSheets() {
                try {
                    const { data } = await ChargeAPI.getPatientOweList(this.postData.patient.id);
                    const { chargeOweSheets = [] } = data || {};
                    this.chargeOweSheets = chargeOweSheets;
                    // 欠费部分还款更新组件中欠费单
                    if (this.seletedChargeOweSheets.length > 0) {
                        // 多个收费单选择一个，多次还款的场景，更新选中收费单支付后的数据
                        const selsectChargeOweSheets = chargeOweSheets.filter((x) => {
                            const ids = this.seletedChargeOweSheets.map((y) => y.id);
                            return ids.includes(x.id);
                        });
                        this?._chargeDialogInstance?.instance.updateFunctionalProps({
                            chargeOweSheets: selsectChargeOweSheets,
                        });
                    } else {
                        this?._chargeDialogInstance?.instance.updateFunctionalProps({
                            chargeOweSheets,
                        });
                    }

                    return chargeOweSheets;
                } catch (e) {
                    console.log(e);
                }
            },

            /**
             * @desc 欠费还款
             * @params {Boolean} 只有一条数据时是否直接发起还款
             * <AUTHOR>
             * @date 2022/05/11 14:55:03
             */
            async handleRepayment(isGoRepay = true) {
                this.repaymentLoading = true;
                this.isCheckedRepay = isGoRepay;
                try {
                    const chargeOweSheets = await this.fetchChargeOweSheets();
                    if (chargeOweSheets.length === 1 && isGoRepay) {
                        this.openRepaymentChargeDialog(chargeOweSheets);
                    } else {
                        this.showRepaymentList = true;
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.repaymentLoading = false;
                }
            },

            // 多条欠费还款
            updateChargeOweSheets(val) {
                this.seletedChargeOweSheets = val;
                this.openRepaymentChargeDialog(val);
            },

            // 长护重新收费
            async repaidHospital() {
                try {
                    const { data } = await ChargeAPI.repaidHospital(this.chargeSheetId);
                    console.log(data);
                    this.$Toast({
                        message: '操作成功',
                        type: 'success',
                    });
                    this.selectFirst();
                } catch (e) {
                    console.error(e);
                    const {
                        code, message,
                    } = e;
                    if (code === 17010) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    }
                }
            },

            /**
             * @desc 监听socket判断是否锁单
             * <AUTHOR>
             * @date 2022/06/01 18:45:00
             */
            async handleOnLockSocket(data, type) {
                const {
                    businessKey,
                    key: patientOrderId,
                    value,
                    employeeId,
                } = data || {};
                if (businessKey !== LockBusinessKeyEnum.CHARGE) return;
                if (this.patientOrderId !== patientOrderId) return;
                if (type === 'lock') {
                    const { businessDetail } = value || {};
                    const { chargePayTransactionId } = businessDetail || {};
                    this.setLockInfo(data || null);
                    if (!this.postData.lockPayTransactionInfo) {
                        this.postData.lockPayTransactionInfo = {};
                    }
                    Object.assign(this.postData.lockPayTransactionInfo, {
                        id: chargePayTransactionId,
                    });

                    if (this._collectionTraceCodeDialog) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: this.lockedTips,
                            onClose: () => {
                                this._collectionTraceCodeDialog.destroyDialog();
                                this._collectionTraceCodeDialog = null;
                            },
                        });
                    }

                } else {
                    const { businessScene } = value || {};
                    const { id } = this.userInfo || {};
                    if (employeeId === id && businessScene !== ChargeBusinessSceneEnum.OUTPATIENT_LOCK_CHARGE_SHEET) return;
                    this.setLockInfo(null);
                    this.fetchDetail();
                }
            },

            /**
             * @desc 微诊所有修改需要触发提示
             * <AUTHOR>
             * @date 2022-12-27 15:51:31
             */
            handleChargeFormChange(data) {
                const {
                    chargeSheetId,
                    content,
                } = data;
                console.log(data);
                if (this.chargeSheetId !== chargeSheetId) return;
                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content,
                    onClose: () => {
                        this.fetchDetail(null, true);
                    },
                });
            },
            changeVisitSource(data) {
                this.postData.visitSourceId = data?.visitSourceId || null;
                this.postData.visitSourceFrom = data?.visitSourceFrom || null;
                this.postData.visitSourceFromName = data?.visitSourceFromName || null;
            },

            // 收费明细
            async onClickChargeDetail() {
                this.showChargeDetailPopover = !this.showChargeDetailPopover;
                if (!this.$abcSocialSecurity.isOpenSocial) return;
                this.calcSheBaoLoading = true;
                this._chargeService.calcFee({
                    payMode: PayModeEnum.SOCIAL_CARD,
                    isNeedPatientCardBalance: 1,
                }, (data) => {
                    const {
                        receivableFee,
                        sheBaoReceivableFee,
                    } = data;
                    const excludeSheBaoReceivableFee = red(receivableFee, sheBaoReceivableFee);
                    this.$set(this.chargeSheetSummary, 'sheBaoReceivableFee', sheBaoReceivableFee);
                    this.$set(this.chargeSheetSummary, 'excludeSheBaoReceivableFee', excludeSheBaoReceivableFee);
                    this.calcSheBaoLoading = false;
                }, undefined, false);
            },

            // 获取社保结算信息
            async getSocialSettleInfo() {
                try {
                    if (!this.$abcSocialSecurity.isOpenSocial) return;
                    if (this.chargeStatus === ChargeStatusEnum.UN_CHARGE) return;
                    const { data } = await SocialAPI.getSocialSettleInfo(this.shebaoSettlePrintSheetId || this.chargeSheetId);
                    const {
                        acctPay,
                        fundPaymentFee,
                        medTypeLabel,
                        medfeeSumamt,
                        specialNeedsHint,
                    } = data || {};
                    this.$set(this.chargeSheetSummary, 'acctPay', acctPay || 0);
                    this.$set(this.chargeSheetSummary, 'fundPaymentFee', fundPaymentFee || 0);
                    // 以下信息住院不用管，所以跟住院同名方法有差异
                    this.$set(this.chargeSheetSummary, 'medTypeLabel', medTypeLabel || '');
                    this.$set(this.chargeSheetSummary, 'medfeeSumamt', medfeeSumamt || 0);
                    this.$set(this.chargeSheetSummary, 'specialNeedsHint', specialNeedsHint || '');
                } catch (e) {
                    console.error(e);
                }
            },

            onBeforePushClick() {
                this.$confirm({
                    type: 'warn',
                    title: '是否保存收费单？',
                    content: '收费单发生变化，需要先保存收费单才可使用推送支付。',
                    onConfirm: () => {
                        this.saveOrder();
                    },
                });
            },
            async handleOpenTraceCodeDialog() {
                if (this.needTraceCodeFormItems.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '本单项目均无需采集追溯码',
                    });
                    return;
                }
                this.buttonLoading = true;
                this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                    formItems: this.traceCodeDispenseItems,
                    dispensedFormItems: this.traceCodeDispensedItems,
                    requiredTraceCode: this.traceCodeCollectionCheck === CollectionTraceCodeCheck.charge,
                    disabled: this.dispensingStatus > dispenseStatusEnum.WAITING,
                    patientOrderId: this._patientOrderId,
                    confirmInnerSet: false,
                    sceneType: SceneTypeEnum.CHARGE,
                    onClose: () => {
                        this._collectionTraceCodeDialog = null;
                        this.disabledScanBarcode = false;
                    },
                    onConfirm: (flatFormItems) => {
                        TraceCode.setChargeFormsTraceCodeList(flatFormItems, this.postData.chargeForms);
                        this.handleTraceCodeConfirm();
                    },
                });
                this.disabledScanBarcode = true;
                await this._collectionTraceCodeDialog.generateDialogAsync();
                this.buttonLoading = false;
            },
            handleTraceCodeConfirm() {
                // 已收后，仅保存追溯码
                if (this.chargeStatus > ChargeStatusEnum.UN_CHARGE) {
                    this.saveTraceCodeHandler();
                } else {
                    this.saveOrderSubmit();
                }
            },
            async saveTraceCodeHandler() {
                try {
                    await ChargeAPI.saveTraceCode(this.chargeSheetId, {
                        list: this.traceCodeDispenseItems.map((item) => {
                            return {
                                id: item.id,
                                shebaoDismountingFlag: item.shebaoDismountingFlag,
                                traceableCodeList: item.traceableCodeList ? item.traceableCodeList.map((code) => {
                                    const {
                                        count, ...restCode
                                    } = code;
                                    return restCode;
                                }) : [],
                                composeChildren: item.composeChildren && item.composeChildren.map((it) => {
                                    return {
                                        id: it.id,
                                        shebaoDismountingFlag: it.shebaoDismountingFlag,
                                        traceableCodeList: it.traceableCodeList ? it.traceableCodeList.map((code) => {
                                            const {
                                                count, ...restCode
                                            } = code;
                                            return restCode;
                                        }) : [],
                                    };
                                }),
                            };
                        }),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                } catch (e) {
                    console.error(e);
                    this.fetchDetail();
                }
            },

            async onAdjustmentTotalFee(expectedAdjustmentFee) {
                this.postData.expectedAdjustmentFee = expectedAdjustmentFee;
                await this.calcFee();
                this.saveOrder();
            },

            onClickCancelPay() {
                // 后台的数据可能存在有锁信息但是没有lockPayTransactionInfo
                if (!this.postData.lockPayTransactionInfo) return;
                this.onConfirmCancelPay({
                    chargePayTransactionId: this.postData.lockPayTransactionInfo.id,
                    callback: this.fetchDetail,
                });
            },

            /**
             * @desc 授权码过期，重启审核流程
             */
            reOpenAuditFlow() {
                this.showRefundWayList = false;
                this.$refs?.refundPro?.reOpenAuditFn();
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
