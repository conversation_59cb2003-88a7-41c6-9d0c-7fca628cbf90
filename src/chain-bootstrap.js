import { ModalFunc as AbcModal } from '@abc/ui-pc';
import { ToastFunc as Toast } from '@abc/ui-pc';

import Vue from 'vue';
import VueRouter from 'vue-router';
import './abc-ui-regist';
import Chain from './Chain.vue';
import { create as createRouter } from './router/chain';
import store from './store';
import { pinia } from 'store/pinia.js';
import 'normalize.css/normalize.css'; // normalize.css 样式格式化
import * as filters from './filters'; // 全局vue filter
import * as directives from './directive'; // 全局vue directives
import fetch, {
    cancelPendingRequest, addSignToRequest,
} from 'utils/fetch';
import { exportFileByAxios } from 'utils/excel';
import components from './components/';
import 'core-js/stable'; // polyfill
import 'regenerator-runtime/runtime'; // polyfill
import { watchUserClinicChange } from './views/common/login-optimize';


import 'views/statistics/index.scss'; //统计模块
import './styles/statistics.scss'; // 统计
// ABC 微前端
import {
    ABCPlatform, MFEError,
} from 'abc-micro-frontend';
import { getModuleConfig } from '@modules/config';
import Service from './service/index';
import ExpireAlert from 'views/edition/expire-alert';
// 打印模板文件初始化
import { loadAbcPrint } from '@/printer/print-init/index.js';

// 社保注入
import abcSocialSecurity from '@/social-security';
import lifecycle from '@/lifecycle';
import AbcEventBus from 'utils/event-bus';
import {
    BaseApp, NavigateHelper,
} from '@/core/index.js';
import {
    isChainAdminClinic, isHospital,
} from 'views/common/clinic.js';
import AbcSocket from 'views/common/single-socket';
import { TodoService } from '@/service/todo';
import * as feEngineCore from 'MfFeEngine/core';
import * as repository from 'MfFeEngine/repository';
import i18n from '@/i18n/index.js';
import { bindRelationType } from '@/views-pharmacy/common/constants';
import { initI18n } from '@/i18n/modify-i18n-messages';
// 提供给社保的依赖
import * as AbcUI from '@abc/ui-pc';
import AbcChargeService from '@/service/charge';
import { pdfLodopPrint } from '@/printer/utils/index';
import ABCPrinterConfig from '@/printer/config.js';
import {
    BizSettingLayout,
    BizSettingContent,
    BizSettingFooter,
    BizSettingSidebar,
    BizFillRemainHeight,
} from '@/components-composite/setting-form-layout/index.js';

import {
    BizSettingForm,
    BizSettingFormGroup,
    BizSettingFormItem,
    BizSettingFormItemTip,
    BizSettingFormItemIndent,
    BizSettingFormHeader,
} from '@/components-composite/setting-form/index.js';
import BizPatientSelector from '@/views/layout/patient/patient-section';
import BizDataStatisticsCard from '@/components-composite/biz-data-statistics-card';
import { AnnouncementDialog } from 'views/layout/announcement/notice-dialog/index.js';
import AbcFileUploader from '@/components/abc-file-uploader/index.vue';
import BizMixedSelectionFilter from '@/components-composite/biz-mixed-selection-filter';
import {
    BizValueAddedCard, BizVersionTips,
} from '@/components-composite/index';
import { FunctionalDialog } from 'views/common/functional-dialog';
import SignService from 'utils/sign-service'; // 签名服务
import DecodeService from '@/service/decode';

console.info('构建时间：', process.env.buildInfo && process.env.buildInfo.BUILD_TIME);

const { currentClinic } = store.getters;
if (currentClinic && (isHospital(currentClinic) || !isChainAdminClinic(currentClinic))) {
    NavigateHelper.navigateToAppIndex(currentClinic);
}

class ChainApp extends BaseApp {
    onInit() {
        const { socket } = AbcSocket.getSocket();
        feEngineCore.init({
            network: fetch,
            socket,
        });
        const goodsRepoInstance = repository.GoodsRepositoryService.getInstance();
        goodsRepoInstance.init();

        lifecycle.beforeCreate();

        Vue.use(abcSocialSecurity);

        const moduleConfig = getModuleConfig(process.env.BUILD_ENV);
        Vue.use(ABCPlatform, {
            Vue,
            VueRouter,
            store,
            fetch,
            addSignToRequest,
            exportFileByAxios,
            moduleConfig,
            ExpireAlert,
            Confirm: AbcModal.confirm,
            Toast,
            clinicType: 0,
            AbcUI,
            AbcChargeService,
            pdfLodopPrint,
            ABCPrinterConfig,
            BizSetting: {
                BizMixedSelectionFilter,
                BizSettingLayout,
                BizSettingContent,
                BizSettingFooter,
                BizSettingSidebar,
                BizFillRemainHeight,
                BizSettingForm,
                BizSettingFormGroup,
                BizSettingFormItem,
                BizSettingFormItemTip,
                BizSettingFormItemIndent,
                BizSettingFormHeader,
            },
            BizComponent: {
                BizPatientSelector,
                BizDataStatisticsCard,
                AbcFileUploader,
                BizValueAddedCard,
                BizVersionTips,
            },
            AnnouncementDialog,
            FunctionalDialog,
            SignService,
        });

        watchUserClinicChange();


        Vue.use(AbcEventBus);

        // 注册全局 filter
        Object.keys(filters).forEach((key) => {
            Vue.filter(key, filters[key]);
        });

        // 注册全局指令
        Object.keys(directives).forEach((key) => {
            Vue.directive(key, directives[key]);
        });

        // 全局组件注册
        Object.keys(components).forEach((key) => {
            Vue.component(key, components[key]);
        });

        /**
         * desc [是否客户端环境判断，注册window.ipcRendererInit方法，在客户端环境完成初始化后，会调用]
         * 总部打印 会判断是否为electron环境
         */
        window.ipcRendererInit = () => {
            store.commit('SET_ELECTRON', true);
        };
        if (window.electronFlag) {
            window.ipcRendererInit();
        }
    }

    async onBoot() {
        this.store = store;
        await store.dispatch('updateUserActionByCookie');
        // 拉取用户基本信息、社保 Config 信息，需要在 router 生成前获取
        await store.dispatch('acFetchUserInfo');
        await Promise.all([
            store.dispatch('acFetchCurrentClinicInfo'),
            store.dispatch('acGetClinicJoined'),
            store.dispatch('initGoodsConfig'),
            store.dispatch('fetchChainBasic'),
            store.dispatch('fetchClinicBasic'),
            store.dispatch('fetchCurrentClinicConfig'),
            store.dispatch('fetchClinicBasicConfig'),
            store.dispatch('socialPc/acInitSocialConfig').then(() => abcSocialSecurity.initNational()),
            store.dispatch('regulatoryPc/acInitRegulatoryConfig'),
            store.dispatch('edition/acFetchEditionConf'),
            store.dispatch('coPharmacyClinic/getCoClinicCountInfo', bindRelationType.CLINIC),
            store.dispatch('getTraceCodeCollectionOpenConfig'),
        ]);

        let moduleIds = [];
        let roleIds = [];
        const { userInfo } = store.getters;
        const { currentClinic } = store.getters;
        if (userInfo && userInfo.moduleIds) {
            moduleIds = userInfo.moduleIds.split(',');
            roleIds = userInfo.roleIds;
        }

        this.router = createRouter({
            moduleIds, roleIds, currentClinic, store,
        });

        this.router.beforeEach(async (to, from, next) => {
            cancelPendingRequest();
            try {
                if (store.getters.userInfo && store.getters.clinics && store.getters.userInfo.moduleIds) { // 判断是否有csrfToken userId clinics
                    store.dispatch('initRefreshToken');
                    await this.pageHandler(to, from, next);
                    next();
                } else {
                    NavigateHelper.navigateToLogin();
                }
            } catch (err) {
                NavigateHelper.navigateToLogin();
            }
        });
        this.router.afterEach(() => {
            // 老布局
            $('.app-wrapper > .container-wrapper').scrollTop(0, 0);
            // 使用abc-container的需要切换路由后滚动到顶部
            $('#abc-container #abc-container-center').scrollTop(0, 0);
        });

        // 初始化 TodoService
        const { socket } = AbcSocket.getSocket();
        const todoService = new TodoService(socket, this.store);
        this.registerService(TodoService.NAME, todoService);

        //设置DecodeService key
        DecodeService.setKey(this.store.getters['theme/curTypeId']);

        // 更改i18n配置
        await initI18n(i18n);

        this.vm = window._vue = new Vue({
            router: this.router,
            store,
            pinia,
            i18n,
            render: (h) => h(Chain),
        });
        this.vm.$mount('#app');
        // window._vue.$abcPlatform.registerGlobalComponents('Schedules', Schedules);
        // 注册后 可以通过 this.$abcPlatform.service.xxx 或者 window.$platform.service.xxx 调用，例如：
        // this.$abcPlatform.service.wallet.payOrder(id, data);
        this.vm.$abcPlatform.registerService(Service);
        this.vm.$abcPlatform.registerErrorHandler((err) => {
            if (err.type === MFEError.ERR_MODULE_NOT_FOUND) {
                NavigateHelper.navigateToLogin();
            }
        });
        this.vm.$abcPlatform.boot(this.vm);

        // 初始化 PrintManager
        const {
            chainBasic, clinicBasic,
        } = store.getters;
        loadAbcPrint({
            isEnableDesktopPrint: chainBasic.isEnableDesktopPrint, printConfig: clinicBasic.printConfig,
        });
    }

}

new ChainApp(Vue).boot().catch((error) => {
    console.log('error', error);
    NavigateHelper.navigateToLogin();
});

