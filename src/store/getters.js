import {
    MODULE_ID_MAP, ROLE_DOCTOR_ID,
} from 'utils/constants';
import { parseTime } from 'utils/index';

import {
    CLINIC_TYPE,
    isAdminClinic,
    isChainAdminClinic,
    isChainClinic,
    isChainSubClinic,
    isOphthalmology,
    isSingleClinic,
} from 'src/views/common/clinic.js';
import { localeTypeEnum } from 'src/views/common/enum.js';
import { PriceType } from 'views/common/inventory/constants';

const getters = {
    clientWidth: (state) => state.layout.clientWidth,

    outpatient: (state) => state.quickList.outpatient,
    consultQlList: (state) => state.quickList.consult,
    childHealth: (state) => state.quickList.childHealth,
    cashier: (state) => state.quickList.cashier,
    pharmacy: (state) => state.quickList.pharmacy,
    examination: (state) => state.quickList.examination,
    inspect: (state) => state.quickList.inspect,
    treatment: (state) => state.quickList.treatment,

    westernMedicineConfig: (state) => state.medicine.westernMedicineConfig,
    chineseMedicineConfig: (state) => state.medicine.chineseMedicineConfig,
    treatmentConfig: (state) => state.medicine.treatmentConfig,
    F1medicineInfo: (state) => state.medicine.F1medicineInfo,

    modules: (state) => state.clinic.modules,
    employeeList: (state) => state.clinic.employeeList,
    clinicNurseList: (state) => state.clinic.nurseList,
    subClinics: (state) => state.clinic.subClinics,
    clinicConfig: (state, getters) => {
        const { addressDistrictId } = getters.clinicBasicConfig || {};
        const { clinicId } = getters.currentClinic || {};
        if (addressDistrictId === '500101' || clinicId === 'e072b910e9a34433aaf1954809602d98') {
            return {
                ...state.clinic.config,
                b2bMallConfig: {
                    isEnableB2BMall: 0,
                },
            };
        }
        return state.clinic.config;
    },
    tpsStatus: (state) => state.clinic.tpsStatus,
    mpWeappAuthInfo: (state) => state.clinic.mpWeappAuthInfo,

    defaultFirstRegistrationFee: (state) => state.registrations.defaultFirstRegistrationFee,//初诊费用
    defaultRevistRegistrationFee: (state) => state.registrations.defaultRevistRegistrationFee, // 复诊费用
    defaultRegistrationFees: (state) => state.registrations.defaultRegistrationFees, //默认挂号费集合（普通/专家/便民门诊）
    defaultEnableCategories: (state) => state.registrations.defaultEnableCategories, //默认开启号种（普通/专家/便民门诊）
    scanQrCodePatientList: (state) => state.registrations.scanQrCodePatientList, //扫码自助登记历史人员列表
    registrationRemarkList: (state) => state.registrations.registrationRemarkList, //挂号就诊备注列表
    therapyRemarkList: (state) => state.registrations.therapyRemarkList, // 理疗就诊备注列表
    newTherapyRemarkList: (state) => state.registrations.newTherapyRemarkList, // 新版理疗就诊备注列表
    waitRegisterAppointmentPatientList: (state) => state.registrations.waitRegisterAppointmentPatientList,
    isOpenScanQrCodeRegister: (state) => false && state.property.clinicBasic.scanRegisterEpidemiological,// 是否开启了扫码自助登记
    registrationVoice: (state) => state.registrations.registrationVoice, // 是否开启预约挂号语音
    // 是否开通微诊所
    isOpenMp: (state, getters) => {
        return getters['weClinic/isOpenWeClinic'];
    },
    // 是否开通的微诊所小程序版本 在没有选择的情况下默认是开通小程序版本 选中的情况下要判断授权成功并且版本对应小程序版本
    isOpenMicroClinicWechat: (state, getters) => {
        return !getters['weClinic/weClinicType'] || (getters.isOpenMp && getters['weClinic/weClinicType'] === 'weapp');
    },
    // 是否禁用批量调价
    disabledPriceAdjustment: (state, getters) => {
        // 读取自主调价的配置项
        const {
            isChainSubStore, showSubSetPrice,
        } = getters;
        if (!isChainSubStore) { // 非连锁子店不受此状态影响
            return false;
        }
        return !showSubSetPrice; // 连锁子店直接使用此状态
    },
    // 门店医生列表
    clinicDoctors: (state) => state.doctor.doctors,
    // 医生配置相关
    prescriptionTemplates: (state) => state.doctor.prescriptionTemplates,
    chinesePRUsageDefault: (state) => state.doctor.chinesePRUsageDefault,
    // 医生科室-挂号费
    departmentRegFeeList: (state) => state.doctor.departmentRegFeeList,
    // 获取是否能查看历史病例
    isAssistDoctorHisCase: (state) => (id) => {
        return state.doctor.assistDoctorList.find((item) => item.id === id) || state.doctor.isAssistantAll;
    },
    // 当前用户助理的医生
    currentAssistantDoctors: (state, getters) => {
        const {
            allDepartmentDoctors, userInfo,
        } = getters;
        // 如果当前用户是管理员 可以助理所有医生
        if (userInfo?.isAdmin) {
            return allDepartmentDoctors;
        }
        // 筛选出来用户当前助理的医生
        return allDepartmentDoctors.filter((item) => {
            return item.isAssistFor;
        });
    },
    // 科室医生 (所有医生)
    allDepartmentDoctors: (state) => state.doctor.departmentDoctors,
    departmentDoctorsEmployees: (state) => state.doctor.departmentDoctorsEmployees,
    westernPRRemarks: (state) => state.doctor.westernPRRemarks,
    westernPRPharmacy: (state) => state.doctor.westernPRPharmacy,
    inspectEmployees: (state) => state.doctor.inspectEmployees, // 检查人（验光师）
    isOpenRegistrationAppointmentVoice: (state) => state.config.isOpenRegistrationAppointmentVoice,
    userInfo: (state) => state.user.userInfo,
    consultationDoctors: (state) => state.doctor.consultationDoctors,
    userConfig: (state) => state.config.userConfig || {},
    // 校验就诊信息是否必须填写
    requiredVisitInformation: (state, getters) => {
        const { chainBasic } = getters;
        return !!chainBasic.registration.requiredVisitSource;
    },
    clinics: (state) => state.user.clinics,
    waitingOpenClinics: (state) => state.user.waitingOpenClinics,
    currentClinic: (state) => state.user.currentClinic,
    isNeedShowModel: (state) => state.user.hospitalUserUseRule.isNeedShowModel,
    // 是否是模版店
    isTemplateClinic: (state, getters) => {
        const { viewDistributeConfig } = state.viewDistribute;
        const {
            goodsConfig, currentClinic,
        } = getters;
        const { templateClinicType } = viewDistributeConfig.Examination;
        // 判断是否是模版门店
        if (goodsConfig && currentClinic && templateClinicType) {
            return currentClinic.chainId === goodsConfig[templateClinicType];
        }
        return false;
    },

    // 是否为体检模板店
    isPhysicalExaminationTemplateClinic: (state, getters) => {
        const { viewDistributeConfig } = state.viewDistribute;
        const {
            goodsConfig, currentClinic,
        } = getters;
        const { physicalExaminationTemplateClinicType } = viewDistributeConfig.Examination;
        // 判断是否是模版门店
        if (goodsConfig && currentClinic && physicalExaminationTemplateClinicType) {
            return currentClinic.chainId === goodsConfig[physicalExaminationTemplateClinicType];
        }
        return false;
    },

    /**
     * @desc 新建的店都是 总部+门店形式，但是要区分能看到的视角
     * @desc isSingleStore 单店视角 = 总部+子店
     * @desc isChainAdmin 连锁总部视角
     * @desc isChainSubStore 连锁子店视角
     * <AUTHOR>
     * @date 2021-09-13 09:22:56
     */
    // 单店诊所
    isSingleStore: (state, get) => {
        return isSingleClinic(get.currentClinic);
    },
    // 连锁总部视角
    isChainAdmin: (state, get) => {
        return isChainAdminClinic(get.currentClinic);
    },
    // 连锁子店视角
    isChainSubStore: (state, get) => {
        return isChainSubClinic(get.currentClinic);
    },
    // 连锁视角
    isChain: (state, get) => {
        return isChainClinic(get.currentClinic);
    },
    // 能看到总部视角
    isAdmin: (state, get) => {
        return isAdminClinic(get.currentClinic);
    },
    // 眼科管家
    isOphthalmology: (state, get) => {
        return isOphthalmology(get.currentClinic);
    },

    draftOutpatientNews: (state) => state.draft.outpatientNews || [],
    todayDraftOutpatientNews: (state, getters) => {
        const { beginDate } = getters.outpatient.scrollParams;
        return getters.draftOutpatientNews
            .filter((item) => {
                return parseTime(new Date(+item.draftId), 'y-m-d', true) === beginDate;
            }).map((x) => {
                return {
                    ...x,
                    todayDraft: 1,
                };
            });
    },
    draftChildHealthNews: (state) => state.draft.childHealthNews || [],
    todayDraftChildHealthNews: (state, getters) => {
        const { beginDate } = getters.childHealth.scrollParams;
        return getters.draftChildHealthNews.filter((item) => {
            return parseTime(new Date(+item.draftId), 'y-m-d', true) === beginDate;
        });
    },
    draftOutpatients: (state) => state.draft.outpatients || [],
    draftChildHealths: (state) => state.draft.childHealths || [],

    draftCashierNews: (state) => state.draft.cashierNews || [],
    draftCashiers: (state) => state.draft.cashiers || [],

    draftConsults: (state) => state.draft.consults || [],

    draftTreatmentNews: (state) => state.draft.treatmentNews || [],
    draftTreatments: (state) => state.draft.treatments || [],

    draftGoodsPurchase: (state) => state.draft.goodsPurchase,

    departmentsList: (state) => state.settings.departmentsList,
    gspTodo: (state) => state.dashboard.gspCount.todo || 0, // gsp养护的todo
    materialMaintenanceTodoCount: (state) => state.dashboard.materialMaintenanceTodoCount.todo || 0, // gsp器械的todo
    gspDestroyTodo: (state) => state.dashboard.gspDestroy.todo || 0, // gsp销毁申请的todo
    approvalTodo: (state) => state.dashboard.approval.todo || 0,
    registrationTodo: (state) => state.dashboard.registration.todo || 0,
    outpatientTodo: (state) => state.dashboard.outpatient.todo || 0,
    supplierValidTodoCount: (state) => state.dashboard.supplierValidCount.todo || 0,
    outpatientEmployeeTodo: (state) => state.dashboard.outpatientEmployee.todo || 0,
    childHealthEmployeeTodo: (state) => state.dashboard.childHealthEmployee.todo || 0,
    cashierTodo: (state) => state.dashboard.cashier.todo || 0,
    pharmacyTodo: (state) => state.dashboard.pharmacy.todo || 0,
    hospitalPharmacyTodo: (state) => state.dashboard.hospitalDispensingOrderCount.todo || 0,
    consultationTodo: (state) => state.dashboard.consultation.todo || 0,
    examinationTodo: (state) => state.dashboard.examination.todo || 0,
    inspectTodo: (state) => state.dashboard.inspect.todo || 0,
    settingTodo: (state) => state.dashboard.setting.todo || 0,
    coCashierTodo: (state) => state.dashboard.coCashier.todo || 0,
    socialTodo: (state) => state.dashboard.social.todo || 0,
    orderCloudTodo: (state) => state.dashboard.orderCloud.todo || 0,

    stockTodo: (state) => state.dashboard.stock.todo || 0,
    stockInTodo: (state) => state.dashboard.stockIn.todo || 0,
    stockOutTodo: (state) => state.dashboard.stockOut.todo || 0,
    stockApplyTodo: (state) => state.dashboard.stockApply.todo || 0,
    stockTransTodo: (state) => state.dashboard.stockTrans.todo || 0,
    stockInTodoPharmacyNoList: (state) => state.dashboard.stockIn.pharmacyNoList || [],
    stockLossOutTodoPharmacyNoList: (state) => state.dashboard.stockOut.pharmacyNoList || [],
    stockReceptTodoPharmacyNoList: (state) => state.dashboard.stockApply.pharmacyNoList || [],
    stockTransTodoPharmacyNoList: (state) => state.dashboard.stockTrans.pharmacyNoList || [],
    expiredWarnPharmacyNoList: (state) => state.dashboard.expiredWarn.pharmacyNoList || [],
    profitRatWarnPharmacyNoList: (state) => state.dashboard.profitRatWarn.pharmacyNoList || [],
    stockWarnShortagePharmacyNoList: (state) => state.dashboard.stockWarn.pharmacyNoList || [],
    expiredWarnTodo: (state) => state.dashboard.expiredWarn.todo || 0,
    profitRatWarnTodo: (state) => state.dashboard.profitRatWarn.todo || 0,
    stockWarnTodo: (state) => state.dashboard.stockWarn.todo || 0,
    stockSocialGoodsWarn: (state) => state.dashboard.stockSocialGoodsWarn.todo || 0,
    stockSocialDiagnosisTreatmentWarn: (state) => state.dashboard.stockSocialDiagnosisTreatmentWarn.todo || 0,
    implNationalWarningCount: (state) => state.dashboard.implNationalWarningCount.todo || 0,
    cisMallWaitSendTodo: (state) => state.dashboard.cisMallWaitSend.todo || 0,
    hospitalSettlingWaitChargeCount: (state) => state.dashboard.hospitalSettlingWaitChargeCount.todo || 0,
    pointsConfig: (state) => state.config.pointsConfig || 0,
    disabledModifyFirstSourceText: (state, getters) => {
        const { dataPermission } = state.property;
        const {
            userInfo, isChainAdmin,
        } = getters;
        const {
            employeeDataPermission,
        } = state.user;
        const { isCanModifyFirstFromAway = 0 } = employeeDataPermission.crm;
        const patientSource = dataPermission.crm.modifyFirstFromAway || {};
        const {
            value = 1,
        } = patientSource;
        if (isChainAdmin) {
            if (!userInfo.isAdmin) {
                return '仅管理员可修改';
            }
            return '';
        }
        // 管理员有所有权限
        if (!userInfo.isAdmin) {
            if (value === 1) {
                return '仅管理员可修改';
            }
            if (value === 0 && !isCanModifyFirstFromAway) {
                return '仅管理员或患者权限可修改';
            }
            if (value === 2 && !isCanModifyFirstFromAway) {
                return '仅管理员或数据权限指定成员可修改';
            }
        }
        return '';
    },
    todosCounter: (state) => {
        let counter = 0;

        const moduleIds = (state.user.userInfo && state.user.userInfo.moduleIds.split(',')) || [];
        const isGlobal = moduleIds.indexOf(MODULE_ID_MAP.globalModule) > -1;
        // 有门诊模块权限
        if (moduleIds.indexOf(MODULE_ID_MAP.outpatient) > -1 || isGlobal) {
            counter += Number(state.dashboard.outpatientEmployee.todo) || 0;
        }
        // 有儿保模块权限
        if (moduleIds.indexOf(MODULE_ID_MAP.childHealth) > -1 || isGlobal) {
            counter += Number(state.dashboard.childHealthEmployee.todo) || 0;
        }
        // 有收费模块权限
        if (moduleIds.indexOf(MODULE_ID_MAP.cashier) > -1 || isGlobal) {
            counter += Number(state.dashboard.cashier.todo) || 0;
        }
        // 有药房模块权限
        if (moduleIds.indexOf(MODULE_ID_MAP.pharmacy) > -1 || isGlobal) {
            counter += Number(state.dashboard.pharmacy.todo) || 0;
        }
        // 有管理模块权限 医院管家有人员设置权限
        if (moduleIds.indexOf(MODULE_ID_MAP.setting) > -1 || moduleIds.indexOf(MODULE_ID_MAP.hospitalSettingConfig) > -1 || isGlobal) {
            counter += Number(state.dashboard.setting.todo) || 0;
        }
        // 有管理模块权限
        if (moduleIds.indexOf(MODULE_ID_MAP.inventory) > -1 || isGlobal) {
            counter += Number(state.dashboard.stockIn.todo) || 0;
            counter += Number(state.dashboard.stockOut.todo) || 0;
            counter += Number(state.dashboard.stockApply.todo) || 0;
            counter += Number(state.dashboard.stockTrans.todo) || 0;
        }
        // 有检验模块权限
        if (moduleIds.indexOf(MODULE_ID_MAP.examination) > -1 || isGlobal) {
            counter += Number(state.dashboard.examination.todo) || 0;
        }

        // 有检查模块权限
        if (moduleIds.indexOf(MODULE_ID_MAP.inspect) > -1 || isGlobal) {
            counter += Number(state.dashboard.inspect.todo) || 0;
        }
        return counter;
    },
    medicalPlanFollowupTodo: (state) => {
        return state.dashboard.medicalPlanFollowup.todo || 0;
    },
    weClinicTodo: (state, getters) => {
        const {
            cisMallWaitSendTodo,
        } = getters;
        let counter = 0;
        if (cisMallWaitSendTodo) {
            counter += cisMallWaitSendTodo;
        }
        return counter;
    },
    undoReviewList: (state) => state.dashboard.undoReviewList,

    frequentlyMedicines: (state) => state.suggestions.frequentlyMedicines,
    chargeItems: (state) => state.suggestions.chargeItems,
    infusionRelatedConfigs: (state) => state.suggestions.infusionRelatedConfigs,


    /**
     * desc [是否支持医保刷卡支付=>开启医保支付+客户端环境]
     */
    isElectron: (state) => state.clinic.electron,

    // 收费设置
    chargeConfig: (state) => state.config.chargeConfig,
    // 发药设置
    dispensingConfig: (state) => state.config.dispensingConfig,
    // 可用的加工制法列表
    availableProcessUsages: (state) => state.pharmacy.availableProcessUsages,
    allProcessUsages: (state) => state.pharmacy.allProcessUsages,
    retailProfitStatisticsDimension: (state) => state.pharmacy.retailProfitStatisticsDimension,
    // 网络问诊状态
    treatOnlineConfig: (state) => state.config.treatOnlineConfig,
    // 微信支付状态
    weChatPayConfig: (state) => state.config.weChatPayConfig,
    // 开通了ABC 支付
    isOpenAbcPay: (state) => {
        return state.config.weChatPayConfig.type === 1;
    },
    abcPayConfig: (state) => state.config.abcPayConfig,
    statisticsDateParams: (state) => state.statistics.dateParams,
    commissionStatEmployeeId: (state) => state.statistics.employeeId,
    statisticsIsIncludingRegistration: (state) => state.statistics.isIncludingRegistration,
    // 开单业绩是否勾选挂号费
    statisticsIsRevenueKpiIncludingRegistration: (state) => state.statistics.isRevenuKpiIncludingRegistration,
    // 开单业绩是否勾选代录人
    statisticsIsRevenueKpiIncludingWriter: (state) => state.statistics.isRevenuKpiIncludingWriter,
    // 开单业绩是否勾选包含代录
    statisticsIsContainOthersWriterAchievement: (state) => state.statistics.isContainOthersWriterAchievement,

    inventoryTodo: (state) => state.inventory.todo,
    goodsPrimaryClassification: (state) => state.inventory.goodsPrimaryClassification || [],
    // 连锁门店药品相关的配置(库存设置&&定价和税率)
    goodsConfig: (state) => state.inventory.goodsConfig,
    subSetArchiveClinics: (state) => state.inventory.goodsConfig?.subClinicArchive?.subSetArchiveClinics ?? [],
    // 新建档案开关是否开启
    showCreateArchives(state, getters) {
        if (getters.isAdmin) return true;

        const {
            subSetArchiveClinics, currentClinic,
        } = getters;
        const clinic = subSetArchiveClinics.find((item) => {
            return item.clinicId === currentClinic?.clinicId && item.archiveType === 0;
        });
        return !!clinic?.openSwitch;
    },

    // 修改档案开关是否开启
    showModifyArchives(state, getters) {
        if (getters.isAdmin) return true;

        const {
            subSetArchiveClinics, currentClinic,
        } = getters;
        const clinic = subSetArchiveClinics.find((item) => {
            return item.clinicId === currentClinic?.clinicId && item.archiveType === 1;
        });
        return !!clinic?.openSwitch;
    },
    // 删除档案开关是否开启
    showDeleteArchives(state, getters) {
        if (getters.isAdmin) return true;

        const {
            subSetArchiveClinics, currentClinic,
        } = getters;
        const clinic = subSetArchiveClinics.find((item) => {
            return item.clinicId === currentClinic?.clinicId && item.archiveType === 2;
        });
        return !!clinic?.openSwitch;
    },
    // 药店允许调价
    allowPharmacyAdjust: (state) => state.inventory.allowPharmacyAdjust,
    // 药品毛利率预警字段
    allowProfitWarnFlag: (state, getters) => {
        const {
            chainWarnCfgView, clinicExternalFlag, stockGoodsConfig,
        } = getters.goodsConfig;
        // 连锁子店
        if (getters.isChainSubStore && (clinicExternalFlag & 0x80)) {
            return stockGoodsConfig?.profitWarnFlag > 0;
        }
        // 非连锁子店只判断此权限
        return chainWarnCfgView?.profitWarnFlag > 0;
    },
    //拥有自主定价权的门店列表
    goodsConfigPriceAdjustmentClinics: (state) => state.inventory?.goodsConfig?.subClinicPrice?.subSetPriceClinics || [],
    //没有自主定价权的门店id
    goodsConfigPriceAdjustmentNoPowerClinicsId: (state, getters) => {
        const { goodsConfigPriceAdjustmentNoPowerClinicsInfo } = getters;
        return goodsConfigPriceAdjustmentNoPowerClinicsInfo.map((item) => {
            return item.id;
        });
    },
    //没有自主定价权的门店name
    goodsConfigPriceAdjustmentNoPowerClinicsName: (state, getters) => {
        const { goodsConfigPriceAdjustmentNoPowerClinicsInfo } = getters;
        return goodsConfigPriceAdjustmentNoPowerClinicsInfo.map((item) => {
            return item.name;
        });
    },
    //没有自主定价权的门店的名称和id信息
    goodsConfigPriceAdjustmentNoPowerClinicsInfo: (state, getters) => {
        const {
            subClinics, goodsConfig, goodsConfigPriceAdjustmentClinics,
        } = getters;
        const disposeShop = [];
        if (!goodsConfig?.subClinicPrice?.subSetPriceAllClinics) { // 如果不是所有的门店有自主定价权，那么从允许自主定价门店列表中取数据
            const shop = subClinics?.filter((item) => {
                return item.chainAdmin !== 1;
            });
            for (let i = 0; i < shop.length; i++) {
                if (!goodsConfigPriceAdjustmentClinics.find((item) => {
                    return item.clinicId === shop[i].id;
                })) {
                    disposeShop.push({
                        name: shop[i].shortName || shop[i].name, id: shop[i].id,
                    });
                }
            }
        }
        // 如果所有门店都有自主定价权那么不存在没有自主定价权的门店 即总部设置价格对所有子店都不生效
        return disposeShop;
    },
    // 是否允许自主定价
    showSubSetPrice: (state, getters) => {
        const {
            goodsConfig, currentClinic, isAdmin, isChainAdmin,
        } = getters;
        if (isAdmin) return true; // 单店与总部可直接定价
        if (!goodsConfig?.subClinicPrice?.subSetPrice) return false;// 不允许自主定价
        if (goodsConfig?.subClinicPrice?.subSetPriceAllClinics) return true;// 允许所有门店自主定价
        if (isChainAdmin && goodsConfig?.subClinicPrice?.subSetPrice) return true;// 总部开启了自主定价
        // 走到这里必然是勾选了允许门店自主定价，判断当前门店是否在允许自主定价的门店列表中
        return goodsConfig?.subClinicPrice?.subSetPriceClinics?.find((item) => {
            return item.clinicId === currentClinic?.id;
        });
    },
    // 定价模式是否打开进价加成模式
    isOpenCostPriceAddition(state, getters) {
        const {
            goodsConfig, isAdmin,
        } = getters;
        // 单店与连锁总部，取subClinicPrice，子店从stockGoodsConfig取（子店有单独的修改接口）
        const priceMode = isAdmin ? goodsConfig?.subClinicPrice?.priceMode : goodsConfig?.stockGoodsConfig?.priceMode;

        return priceMode === PriceType.PKG_PRICE_MAKEUP;
    },
    // 是否有调价信息
    haveAdjustmentPriceList: (state) => {
        return !!state.inventory.adjustmentPriceListTotal;
    },
    // 带有定价信息的门店列表
    subClinicsWithSetPriceInfo: (state, getters) => {
        const { subClinics } = getters;
        const { goodsConfig } = getters;
        if (!goodsConfig?.subClinicPrice?.subSetPrice) {
            return subClinics.map((clinic) => {
                return {
                    ...clinic,
                    canSetPrice: false,
                };
            });
        }
        if (goodsConfig?.subClinicPrice?.subSetPriceAllClinics) {
            return subClinics.map((clinic) => {
                return {
                    ...clinic,
                    canSetPrice: true,
                };
            });
        }
        return subClinics.map((clinic) => {
            return {
                ...clinic,
                canSetPrice: goodsConfig?.subClinicPrice?.subSetPriceClinics?.find((item) => {
                    return item.clinicId === clinic.id;
                }),
            };
        });
    },
    consultingRoomSetting: (state) => state.registrations.consultingRoomSetting,

    therapyReservationConfig: (state) => state.registrations.therapyReservationConfig,
    registrationsConfig: (state) => state.registrations.registrationsConfig,
    appointmentConfig: (state) => state.registrations.appointmentConfig,
    registrationsConfigIsInit: (state) => state.registrations.registrationsConfigIsInit,
    therapyReservationConfigIsInit: (state) => state.registrations.therapyReservationConfigIsInit,
    oldTherapyConfigIsInit: (state) => state.registrations.oldTherapyConfigIsInit,
    registrationsRemarks: (state) => state.registrations.registrationsRemarks,
    clinicRegistrationFieldConfig: (state) => state.registrations.clinicRegistrationFieldConfig,

    appVersionClicked: (state) => state.dashboard.appVersionClicked,

    // 叫号设置
    callConfig: (state) => state.config.callConfig,
    // 叫号设置
    mallWaitingPaidOrderCount: (state) => state.config.mallWaitingPaidOrderCount,
    // 有采购商品的权限
    isPurchaseMallPrice: (state) => {
        const {
            employeeDataPermission,
        } = state.user;
        const { isCanPurchaseGoods = 0 } = employeeDataPermission?.mall || {};
        return !!isCanPurchaseGoods;
    },
    // 是否可以查看采购价格
    isLookMallPrice: (state, getters) => {
        const {
            employeeDataPermission,
        } = state.user;
        const { isPurchaseMallPrice } = getters;
        const { isCanSeeGoodsPrice = 0 } = employeeDataPermission?.mall || {};
        // 有采购权限必定有查看价格的权限
        return isPurchaseMallPrice || !!isCanSeeGoodsPrice;
    }, // 是否可以查看价格
    isFirstLookPriceAdjustment: (state) => state.inventory.isFirstLookPriceAdjustment,
    priceAdjustmentTabType: (state) => state.inventory.priceAdjustmentTabType,
    priceAdjustmentRatio: (state) => state.inventory.priceRatio,
    priceAdjustmentMode: (state) => state.inventory.priceMode,
    priceAdjustmentRoundingMode: (state) => state.inventory.roundingMode,
    priceAdjustmentScaleType: (state) => state.inventory.scaleType,
    isFirstOpenNoticeDialog: (state) => state.config.isFirstOpenNoticeDialog, // 是否读过了订单待支付消息
    isFirstReadCouponNotice: (state) => state.config.isFirstReadCouponNotice, // 是否读过了红包临期消息
    // 是否开启叫号
    isOpenCall: (state) => state.config.callConfig && state.config.callConfig.enableCalling,
    // 待诊呼叫下一位是否不再弹窗提示
    isCallingNextTips: (state) => state.config.callingNextTips,
    // 是否开启药房叫号
    isOpenPharmacyCall: (state) => state.config.pharmacyCallConfig && state.config.pharmacyCallConfig.enableCalling,
    // 门诊 quicklist 当前的就诊中
    outpatientQLCurrentTreatItem: (state, getters) => {
        const { quickList } = getters.outpatient;
        let target = null;
        if (getters.isOpenCall && quickList && quickList.length !== 0) {
            target = quickList.find((item) => item.callingPatientItem && item.callingPatientItem.status === 2);
        }
        return target;
    },
    isInnerFlag(getters) {
        const { clinicBasicConfig = {} } = getters.clinic;
        return !!clinicBasicConfig?.innerFlag;
    },
    // 开启了scrm
    isOpenScrm: (getters) => {
        const { clinicBasicConfig = {} } = getters.clinic;
        return !!clinicBasicConfig?.isOpenScrm;
    },
    //是否禁止导出 目前前端只拦截库存、统计-门诊日志、患者清单
    isForbiddenExport: (getters) => {
        const { clinicBasicConfig = {} } = getters.clinic;
        //0允许 1不允许
        return clinicBasicConfig?.isForbiddenExport === 1;
    },
    // 支持随访发送信息通过scrm
    supportVisitSendMsgByScrm: (state) => {
        const { viewDistributeConfig = {} } = state.viewDistribute;
        return viewDistributeConfig?.CRM?.isAllowVisitSendMsgByScrm;
    },
    childHealthQLCurrentTreatItem: (state, getters) => {
        const { quickList } = getters.childHealth;
        let target = null;
        if (getters.isOpenCall && quickList && quickList.length !== 0) {
            target = quickList.find((item) => item.callingPatientItem && item.callingPatientItem.status === 2);
        }
        return target;
    },

    isEnableWuhanHealthyCard: (state) => state.property.clinicBasic.isEnableWuhanHealthyCard,
    isEnableMianyangHealthCard: (state) => state.property.clinicBasic.isEnableMianyangHealthCard,

    // property
    // dataPermission
    dataPermission: (state) => state.property.dataPermission,
    isDataPermissionInited: (state) => state.property.isDataPermissionInited,

    crmPermission: (state) => state.property.crmPermission,
    isCrmPermissionInited: (state) => state.property.isCrmPermissionInited,

    selfServiceSettings: (state) => state.property.selfServiceSettings,
    nurseSettings: (state) => state.property.nurseSettings,
    isSelfServicePermissionInited: (state) => state.property.isSelfServicePermissionInited,
    inventoryStatDimension: (state) => state.property.statistics.inventoryStatConfig.statDimension,
    inventoryStatDistinguish: (state) => state.property.statistics.inventoryStatConfig.isDistinguishClassify,
    inventoryStatType: (state) => state.property.statistics.inventoryStatConfig.statType,
    inventoryStatStockHasChange: (state) => state.property.statistics.inventoryStatConfig.stockHaveChange,
    chargeSheetStatDimension: (state) => state.property.statistics.chargeSheetConfig.statDimension,
    isEnableDecoctionCraftCard: (state) => state.property.chainBasic.isEnableDecoctionCraftCard,
    // 常用与判断是否为张仲景门店(退货申请)
    isShowReturnGoodsApplicationButton: (state) => state.property.chainBasic.stockInReturnOutApply,
    isDisableSubClinicStockInReturnOut: (state) => state.property.chainBasic.disableSubClinicStockInReturnOut,
    isDisableWxAutoReply: (state) => state.property.chainBasic.weClinic.disableWxAutoReply,
    wxReplySound: (state) => state.property.clinicBasic.wxReplySound,
    followUpTaskSilentTip: (state) => state.property.clinicBasic.wxRevisitNotify,

    // 连锁店基础config
    chainBasic: (state) => state.property.chainBasic,
    isChainBasicInited: (state) => state.property.isChainBasicInited,
    isEnableChronicRecovery: (state) => state.property.chainBasic.isEnableChronicRecovery,
    isEnableChildHealth: (state) => state.property.chainBasic.isEnableChildHealth,
    hasChildHealthModulePower: (state, getters) => {
        const {
            isEnableChildHealth, userInfo,
        } = getters;
        const moduleIds = (userInfo && userInfo.moduleIds.split(',')) || [];
        const isGlobal = moduleIds.indexOf(MODULE_ID_MAP.globalModule) > -1;
        return isEnableChildHealth && (isGlobal || moduleIds.indexOf(MODULE_ID_MAP.childHealth) > -1);
    },
    isEnableChengduHealthCard: (state) => state.property.chainBasic.isEnableChengduHealthCard,
    isEnableIdCardReader: (state) => state.property.chainBasic.isEnableIdCardReader,
    isEnableRegUpgrade: (state) => state.property.chainBasic.isEnableRegUpgrade,
    isEnablePacsUpgrade: (state) => state.property.isEnablePacsUpgrade,
    isEnableEyeInspectReportV2: (state) => state.property.chainBasic.isEnableEyeInspectReportV2,
    isEnableOperationLog: (state) => state.property.chainBasic.isShowUserOperationLog,
    // 连锁货币类型
    currencyType: (state) => state.property.chainBasic.currencyType,
    clinicBasic: (state) => state.property.clinicBasic,
    isClinicBasicInited: (state) => state.property.isClinicBasicInited,

    clinicBasicConfig: (state) => state.clinic.clinicBasicConfig,
    registrationFeeNameDisplay: (state) => state.property.clinicBasic.feeNameDisplay.registrationFee,
    // 连锁所在的地区
    locale: (state,getters) => {
        const {
            currencyType,
        } = getters;
        return localeTypeEnum[currencyType];
    },
    currentClinicBusinessType: (state) => state.clinic.currentClinicBusinessType,
    currentSellerInfo: (state) => state.clinic.currentSellerInfo,
    // 获取当前地区标识
    currentRegion: (state) => state.socialPc.region,
    isNeedAuthenticationBasicInfo: (state) => {
        return !!state.property.clinicBasic.crm.settings.needRealAuth;
    },
    isOpenAccompanyingNote: (state) => {
        return state.property.clinicBasic.bis.air.accompanyingNote;
    },
    // 打印相关配置
    printerConfig: (state) => state.print.printerConfig, // 打印机配置
    printConfig: (state) => state.print.config,
    printPrescriptionConfig: (state) => state.print.prescriptionConfig,
    printRegistrationConfig: (state) => state.print.registration,
    printCashierConfig: (state) => state.print.cashier,
    printDispensingConfig: (state) => state.print.dispensingConfig,
    printBillConfig: (state) => state.print.billConfig,
    printReceiptConfig: (state) => state.print.receipt,
    printHospitalBillConfig: (state) => state.print.hospitalBillConfig,
    printMedicalListConfig: (state) => state.print.medicalListConfig,
    printMedicalStatementConfig: (state) => state.print.statement,
    isPrintConfigInited: (state) => state.print.isPrintConfigInited,
    printHeaderConfig: (state) => state.print.headerConfig,
    printMedicalDocumentsConfig: (state) => state.print.medicalDocuments, // 医疗文书
    printHospitalMedicalDocumentsConfig: (state) => state.print.hospitalMedicalDocuments, // 住院病例文书
    printHospitalFeeBillsConfig: (state) => state.print.hospitalFeeBills, // 住院收费票据
    printHospitalTagsConfig: (state) => state.print.hospitalTags,

    printGlobalConfig: (state) => state.print,

    // 当前版本
    currentEdition: (state) => state.edition.edition,

    purchaseTodo: (state) => state.purchase.todo,

    isImplNationTipClicked: (state) => state.property.tips.index.implNationalTip,
    isMedicalDevelopmentClicked: (state) => state.property.tips.index.medicalDevelopment,
    lastCommunicatePatient: (state) => state.crm.lastCommunicatePatient, //上次交流患者
    clinicEmployeeSetting: (state) => state.property.clinicEmployeeSetting,
    isHospital: (state) => state.user.currentClinic?.hisType === CLINIC_TYPE.HOSPITAL, // 是否医院管家
    isPharmacy: (state) => state.user.currentClinic?.hisType === CLINIC_TYPE.PHARMACY, // 是否药店管家
    isDentistry: (state) => state.user.currentClinic?.hisType === CLINIC_TYPE.DENTISTRY, // 是否牙科诊所

    examinationProjectCanMerge: (state) => state.examination.ruleSetting.mergeSettings.rule === 1, // 检验项目是否可合并

    examinationProjectNeedCheck: (state) => state.examination.ruleSetting.reportCheckSettings.needCheck === 1, // 检验项目是否可合并

    inspectProjectCanMerge: (state) => state.inspect.ruleSetting.mergeSettings.rule === 1, // 检查项目是否可合并

    examinationProjectUseLastDevice: (state) => {
        return state.examination.ruleSetting.deviceSettings.defaultDevice === 0;
    }, // 检验项目上机检验是否使用上次操作设备

    hasBindHistory: (state) => state.coPharmacyClinic.hasBindHistory,
    // 是否启用挂网价-用于判断当前地区门店是否启用挂网价
    isEnableListingPrice: (state) => !!state.socialPc.isEnableListingPrice,
    // 医保限价助手-挂网价限价开关（前置状态isEnableListingPrice是开启的）
    isEnableLimitListingPriceSwitch: (state) => !!(state.socialPc.basicInfo?.limitListingPriceSwitch ?? 0),
    // 诊间医保支付 角色医生 且开启了医生可以从门诊进行收费
    isEnableInpatientMedicarePayment: (state, getters) => {
        const {
            chainBasic, userInfo,
        } = getters;
        const { roleIds } = userInfo || {};
        const isDoctor = roleIds?.includes(ROLE_DOCTOR_ID);
        return isDoctor && chainBasic?.charge?.doctorChargeFromOutpatient;
    },
};
export default getters;
