import { editionStatus } from '@/access/constants';
import {
    isDev,
    isLocal,
    isTest,
} from '@/assets/configure/build-env.js';
import { AppScene } from '@/core/constants';
import {
    navigateToAuthCallback, windowOpen,
} from '@/core/navigate-helper';
import { defaultCountryCode } from '@/utils/country-codes.js';
import { refreshCurrentUserClinic } from '@/views/common/login-optimize';
import LoginApi from 'api/login';
import RegionLoginAPI from 'api/region-login.js';
import {
    isChainAdminClinic, isChainClinic, isHospital, isPharmacy,
} from 'src/views/common/clinic.js';
import { MODULE_ID_MAP } from 'utils/constants';
import {
    CLINICS,
    CSRF_TOKEN,
    CSRF_TOKEN_SIG,
    CURRENT_CLINIC,
    IS_REFRESH_TOKEN,
    IS_REFRESH_TOKEN_EXPIRE_DAYS,
    LOGIN_WAY_KEY,
    USER_INFO, UUID,
    ABCYUN_TOKEN,
    ABCYUN_TOKEN_STORAGE,
} from 'utils/local-storage-key.js';
import Store from 'utils/localStorage-handler';
import {
    getClientInfo, KEY_REMEMBER,
} from 'views/home/<USER>';
import { closeChildWindow } from 'utils/clear-window-manager';

import cookieService from '@/cookie-service';
import {
    getOfflineBundleByClinicInfo, getRegionByHostname, isAbcClientSchema,
} from 'utils/electron';

function effectiveDomain() {
    const { hostname } = location;
    let len = -3;
    if (hostname.indexOf('abcyun.') !== -1 || hostname.indexOf('abczs.cn') !== -1) {
        // 即为生产域
        len = -2;
    }
    if (process.env.NODE_ENV !== 'production') {
        len = 0;
    }
    return hostname.split('.').slice(len).join('.');
}

const ABC_DOMAIN = effectiveDomain();

function includesModuleId(moduleArr, id) {
    return moduleArr.includes(id);
}

const user = {
    state: {
        // 登录方式
        loginWay: '',
        // 是否记住登录
        rememberLogin: '',
        // 用户基本信息
        userInfo: '',
        // uuid
        userUUID: '',
        // 诊所列表
        clinics: localStorage.getItem(CLINICS) ? JSON.parse(localStorage.getItem(CLINICS)) : null,
        // 当前诊所
        currentClinic: Store.get(CURRENT_CLINIC, true) || null,
        // 待开通门店
        waitingOpenClinics: [],
        // 人员数据权限
        employeeDataPermission: {
            // 收费
            'cashier': {
                // 能否查看药品进价: 0 不能 1 能
                'isCanSeeGoodsCostPrice': 0,
                // 能否查看查看患者就诊历史: 0 不能 1 能
                'isCanSeePatientHistory': 0,
                //查看患者手机号 0 不能 1 能
                'isCanSeePatientMobile': 0,
                //修改支付方式 0 不能 1 能
                'isCanSeeModifyPayMode': 0,
            },
            // 患者
            'crm': {
                // 能否查看患者就诊历史: 0 不能 1 能
                'isCanSeeAllPatients': 0,
                //查看患者手机号 0 不能 1 能
                'isCanSeePatientMobile': 0,
                //编辑患者就诊来源 0 不能 1 能
                'isCanModifyFirstFromAway': 0,
                //查看患者收费金额 0 不能 1 能
                'isCanSeePatientPayAmount': 0,
                //编辑患者姓名 0 不能 1 能
                'isCanModifyName': 0,
                //编辑患者身份证号 0 不能 1 能
                'isCanModifyIdCard': 0,
                //编辑患者编号 0 不能 1 能
                'isCanModifySn': 0,
                // 查看会员毛利率 0 不能 1 能
                isCanSeePatientProfit: 0,
            },
            // 工作台
            'dashboard': {
                // 收费员查看账目: 0 不允许查看账目 1 只允许查看个人经手账目 2 允许查看门店全部账目
                'chargerPermission': 2,
                // 医生查看诊疗收入: 0 不允许查看个人诊疗收入 1 允许查看个人诊疗收入
                'doctorOutpatientFee': 0,
                // 医生查看挂号收入: 0 不允许查看个人挂号收入 1 允许查看个人挂号收入
                'doctorRegistrationFee': 1,
                // 医生查看患者看板: 0 只允许查看自己的患者看板 1 允许查看当天所有患者看板
                'kanbanPermission': 0,
                // 医生查看执行金额 0 不允许查看个人执行金额 1 允许查看个人执行金额
                doctorExecuteFee: 1,
            },
            // 库存
            'inventory': {
                // 查看药品物资成本: 0 不能 1 能
                'isCanSeeGoodsCost': 1,
                // 查看药品物资毛利: 0 不能 1 能
                'isCanSeeGoodsProfit': 1,
                // 查看盘点药品价格: 0 不能 1 能
                'isCanSeeCheckGoodsPrice': 1,
                // 查看报损药品价格: 0 不能 1 能
                'isCanSeeDamageGoodsPrice': 1,
                // 查看领用药品价格: 0 不能 1 能
                'isCanSeeObtainGoodsPrice': 1,
                // 查看调拨药品价格: 0 不能 1 能
                'isCanSeeTransGoodsPrice': 1,
                // 设置定价权限 0 不能 1 能
                'isCanOperateGoodsAdjustPrice': 0,
                // 已废弃-设置建档权限 0 不能 1 能
                'isCanOperateGoodsArchives': 0,
                // 新建档案权限 0 不能 1 能
                'isCanCreateGoodsArchives': 0,
                // 修改档案权限 0 不能 1 能
                'isCanModifyGoodsArchives': 0,
                // 删除档案权限 0 不能 1 能
                'isCanDeleteGoodsArchives': 0,
                // 生产出库查看药品价格: 0 不能 1 能
                'isCanSeeProductionGoodsPrice': 0,
            },
            // 门诊
            'outpatient': {
                // 医生查看药品价格: 0 只允许查看药品总价 1 允许查看药品明细 2 不允许查看药品明细，总价
                'goodsPrice': 1,
                // 医生查看历史处方: 0 只允许查看自己开出的历史处方 1 允许查看患者所有的历史处方
                'historyPrescription': 1,
                // 门诊-医生查看处方价格: 0 不允许 1 允许
                'isCanSeePrescriptionPrice': 1,
                // 门诊-医生查看门诊单总价: 0 不允许 1 允许
                'isCanSeeTotalPrice': 1,
                // 医生查看药品进价: 0 不能 1 能
                'isCanSeeGoodsCostPrice': 0,
                //查看患者手机号 0 不能 1 能
                'isCanSeePatientMobile': 0,
                // 门诊-修改挂号预约权限 0 不能 1 能
                'isCanModifyOutpatientRegistration': 0,
            },
            // 药房
            'pharmacy': {
                // 发药员查看患者就诊历史: 0 不能 1 能
                'isCanSeePatientHistory': 0,
                //查看患者手机号 0 不能 1 能
                'isCanSeePatientMobile': 0,
            },
            // 统计
            'statistics': {
                // 查看药品物资成本: 0 不能 1 能
                'isCanSeeGoodsCost': 0,
                // 查看药品物资毛利: 0 不能 1 能
                'isCanSeeGoodsProfit': 0,
                //查看患者手机号 0 不能 1 能
                'isCanSeePatientMobile': 0,
            },
            // 医保
            'medicalInsurance': {
                // 查看医保主页: 0 不能 1 能
                'isCanSeeHomePage': 0,
                // 查看医保账目: 0 不能 1 能
                'isCanSeeAccount': 0,
                // 查看医保业务登记信息 : 0 不能 1 能
                'isCanSeeBusinessRegistrationRecord': 0,
                // 查看机构资料数据 : 0 不能 1 能
                'isCanSeeProfileData': 0,
                // 查看医保设置信息 : 0 不能 1 能
                'isCanSeeSetupInformation': 0,
            },
            // 执行站
            'nurse': {
                // 查看患者就诊历史: 1允许 0不允许
                'medicalHistory': 1,
                //查看患者手机号 0 不能 1 能
                'isCanSeePatientMobile': 0,
            },
            // 预约挂号
            'registration': {
                // 查看患者就诊历史: 1允许 0不允许
                'medicalHistory': 1,
                //查看患者手机号 0 不能 1 能
                'isCanSeePatientMobile': 0,
                //修改支付方式 0 不能 1 能
                'isCanSeeModifyPayMode': 0,
                //修改挂号预约信息 0 不能 1 能
                'isCanSeeModifyRegistrationInfo': 0,
            },
            // 微诊所
            'microClinic': {
                isCanSeeHomePageData: 1,
            },
            'retail': {
                isCanOptPatientBalance: 0,
                isCanOptPatientCoupon: 0,
                isCanOptPatientPoints: 0,
                isCanSeeGoodsCostPrice: 0,
                isCanSeeProfit: 0,
                // 查看会员毛利率 0 不能 1 能
                isCanSeePatientProfit: 0,
            },
        },

        // 医院管家用户习惯
        hospitalUserUseRule: {
            isNeedShowModel: false,
        },
    },
    getters: {
        userUUID: (state) => state.userUUID,
        // 是否是 loginPassword 登录
        isTestLogin: (state) => state.loginWay === 'testLogin',
        // 判断当前用户的权限
        modulePermission: (state) => {
            const userInfo = state.userInfo || {};
            const moduleIds = userInfo.moduleIds || '';
            let moduleArr = [];
            if (moduleIds) {
                moduleArr = moduleIds.split(',');
            }
            let isGlobalModule = false;
            if (!moduleIds) {
                isGlobalModule = false;
            }
            // 所有模块
            if (moduleIds === '0') {
                isGlobalModule = true;
            }
            const hasStockModule = isGlobalModule ||
                includesModuleId(moduleArr, MODULE_ID_MAP.inventory) ||
                includesModuleId(moduleArr, MODULE_ID_MAP.bizPharmacyInventory);

            const hasStockInModule = isGlobalModule || hasStockModule || includesModuleId(moduleArr, MODULE_ID_MAP.goodsIn);

            const hasPharmacyStockInModule = isGlobalModule || hasStockModule ||
                includesModuleId(moduleArr, MODULE_ID_MAP.bizPharmacyInventorySubModule.buyInSubModule.purchaseGoodsIn);

            const hasPharmacyStockReturnModule = isGlobalModule || hasStockModule ||
                includesModuleId(moduleArr, MODULE_ID_MAP.bizPharmacyInventorySubModule.buyInSubModule.purchaseReturnGoods);

            const hasGoodsModule = isGlobalModule || hasStockModule ||
                includesModuleId(moduleArr, MODULE_ID_MAP.goods);

            const hasPharmacySupplierModule = isGlobalModule || hasStockModule ||
                includesModuleId(moduleArr, MODULE_ID_MAP.bizPharmacyInventorySubModule.supplier);

            const hasPharmacyModule = isGlobalModule ||
                includesModuleId(moduleArr, MODULE_ID_MAP.pharmacy) ||
                includesModuleId(moduleArr, MODULE_ID_MAP.hospitalPharmacyStation);

            const hasSettingModule = isGlobalModule || includesModuleId(moduleArr, MODULE_ID_MAP.setting);

            const hasChargeModule = isGlobalModule || includesModuleId(moduleArr, MODULE_ID_MAP.cashier) || includesModuleId(moduleArr, MODULE_ID_MAP.hospitalChargeStation);
            const hasSocialModule = isGlobalModule || includesModuleId(moduleArr, MODULE_ID_MAP.social);

            // 微诊所权限
            // 1. 管理员
            // 2. 微诊所任意模块权限
            // 3. 勾选了全部模块
            const { isAdmin } = userInfo;
            const WeClinicModuleIds = [MODULE_ID_MAP.weClinic, ...Object.values(MODULE_ID_MAP.weClinicSubModule)];
            const hasWeClinicModule = moduleArr.some((id) => WeClinicModuleIds.includes(id)) || isGlobalModule || isAdmin;
            const hasHospitalSupplyCenterGoodsModule = isGlobalModule ||
                includesModuleId(moduleArr, MODULE_ID_MAP.hospitalSupplyCenter) ||
                includesModuleId(moduleArr, MODULE_ID_MAP.hospitalSupplyCenterGoods);
            return {
                hasGoodsModule, // 药品/物资权限
                hasStockInModule, // 入库权限（诊所退货也用这个判断）
                hasPharmacyStockInModule, // 药店入库权限
                hasPharmacyStockReturnModule, // 药店退货权限
                hasPharmacySupplierModule, // 药店供应商权限
                hasPharmacyModule, // 发药权限
                hasSettingModule, // 设置权限
                hasWeClinicModule, // 微诊所权限
                hasHospitalSupplyCenterGoodsModule, // 医院供应中心-档案权限
                hasChargeModule, // 收费模块权限
                hasSocialModule, // 医保模块权限
            };
        },
        // 收费-能否查看药品进价: 0 不能 1 能
        isCanSeeGoodsCostPriceInCashier: (state) => state.employeeDataPermission?.cashier?.isCanSeeGoodsCostPrice === 1,
        // 收费-能否查看查看患者就诊历史: 0 不能 1 能
        isCanSeePatientHistoryInCashier: (state) => state.employeeDataPermission?.cashier?.isCanSeePatientHistory === 1,

        // 库存-查看药品物资成本: 0 不能 1 能
        isCanSeeGoodsCostInInventory: (state) => state.employeeDataPermission?.inventory?.isCanSeeGoodsCost === 1,
        // 库存-查看药品物资毛利: 0 不能 1 能
        isCanSeeGoodsProfitInInventory: (state) => state.employeeDataPermission?.inventory?.isCanSeeGoodsProfit === 1,
        // 库存-查看盘点药品价格: 0 不能 1 能
        isCanSeeCheckGoodsPriceInInventory: (state) => state.employeeDataPermission?.inventory?.isCanSeeCheckGoodsPrice === 1,
        // 库存-查看报损药品价格: 0 不能 1 能
        isCanSeeDamageGoodsPriceInInventory: (state) => state.employeeDataPermission?.inventory?.isCanSeeDamageGoodsPrice === 1,
        // 库存-查看领用药品价格: 0 不能 1 能
        isCanSeeObtainGoodsPriceInInventory: (state) => state.employeeDataPermission?.inventory?.isCanSeeObtainGoodsPrice === 1,
        // 库存-查看调拨药品价格: 0 不能 1 能
        isCanSeeTransGoodsPriceInInventory: (state) => state.employeeDataPermission?.inventory?.isCanSeeTransGoodsPrice === 1,
        // 库存-查看生产出库价格: 0 不能 1 能
        isCanSeeProductionOutPriceInInventory: (state) => state.employeeDataPermission?.inventory?.isCanSeeProductionGoodsPrice === 1,
        // 库存-操作定价权限 0 不能 1 能
        isCanOperateGoodsAdjustPriceInInventory: (state) => state.employeeDataPermission?.inventory?.isCanOperateGoodsAdjustPrice === 1,
        // 库存-操作建档权限 0 不能 1 能
        isCanOperateGoodsArchivesInInventory: (state) => state.employeeDataPermission?.inventory?.isCanOperateGoodsArchives === 1,
        // 库存-新建建档权限 0 不能 1 能
        isCanCreateGoodsArchivesInInventory: (state, getters) => state.employeeDataPermission?.inventory?.isCanCreateGoodsArchives === 1 && getters.showCreateArchives,
        // 库存-修改建档权限 0 不能 1 能
        isCanModifyGoodsArchivesInInventory: (state, getters) => state.employeeDataPermission?.inventory?.isCanModifyGoodsArchives === 1 && getters.showModifyArchives,
        // 库存-删除建档权限 0 不能 1 能
        isCanDeleteGoodsArchivesInInventory: (state, getters) => state.employeeDataPermission?.inventory?.isCanDeleteGoodsArchives === 1 && getters.showDeleteArchives,


        // 患者-（限制医生和执行人）能否查看所有患者: 0 不能 1 能
        isCanSeeAllPatientsInCrm: (state) => state.employeeDataPermission?.crm?.isCanSeeAllPatients === 1,

        // 执行-查看已执行单据: 0 只能查看自己的 1 查看所有的 2 不能
        executedSheetDetailInNurse: (state) => state.employeeDataPermission?.nurse?.executedSheetDetail,

        isCanModifyNameInCrm: (state) => state.employeeDataPermission?.crm?.isCanModifyName === 1,

        isCanModifyIdCardInCrm: (state) => state.employeeDataPermission?.crm?.isCanModifyIdCard === 1,

        isCanModifySnInCrm: (state) => state.employeeDataPermission?.crm?.isCanModifySn === 1,

        // 执行-查看患者就诊历史: 1允许 0不允许
        isCanSeeMedicalHistoryInNurse: (state) => state.employeeDataPermission?.nurse?.medicalHistory === 1,

        // 预约挂号 - 查看患者就诊历史: 0 不允许 1 允许
        isCanSeeMedicalHistoryInRegistration: (state) => state.employeeDataPermission?.registration?.medicalHistory === 1,

        // 药房-发药员查看患者就诊历史: 0 不能 1 能
        isCanSeePatientHistoryInPharmacy: (state) => state.employeeDataPermission?.pharmacy?.isCanSeePatientHistory === 1,

        // 统计-查看药品物资成本: 0 不能 1 能
        isCanSeeGoodsCostInStatistics: (state) => state.employeeDataPermission?.statistics?.isCanSeeGoodsCost === 1,
        // 统计-查看药品物资毛利: 0 不能 1 能
        isCanSeeGoodsProfitInStatistics: (state) => state.employeeDataPermission?.statistics?.isCanSeeGoodsProfit === 1,
        // 统计-查看患者手机号 0 不能 1 能
        isCanSeePatientMobileInStatistics: (state) => state.employeeDataPermission?.statistics?.isCanSeePatientMobile === 1,

        enablePatientMobileInStatistics: (_, getters) => {
            const {
                isChainAdmin, isCanSeePatientMobileInStatistics,
            } = getters;
            // 0表示不能看，1表示能看，默认不能看
            let enablePatientMobile = 0;
            if (isChainAdmin) {
                // 如果是总部管理员，则可以看
                enablePatientMobile = 1;
            } else {
                // 如果是有统计权限，则可以看
                enablePatientMobile = isCanSeePatientMobileInStatistics ? 1 : 0;
            }
            return enablePatientMobile;
        },
        enableCostInStatistics: (_, getters) => {
            const {
                isChainAdmin, isCanSeeGoodsCostInStatistics,
            } = getters;
            // 0表示不能看，1表示能看，默认不能看
            let enableCost = 0;
            if (isChainAdmin) {
                // 如果是总部管理员，则可以看
                enableCost = 1;
            } else {
                // 如果是有统计权限，则可以看
                enableCost = isCanSeeGoodsCostInStatistics ? 1 : 0;
            }
            return enableCost;
        },
        enableGrossInStatistics: (_, getters) => {
            const {
                isChainAdmin, isCanSeeGoodsProfitInStatistics,
            } = getters;
            // 0表示不能看，1表示能看，默认不能看
            let enableGross = 0;
            if (isChainAdmin) {
                // 如果是总部管理员，则可以看
                enableGross = 1;
            } else {
                // 如果是有统计权限，则可以看
                enableGross = isCanSeeGoodsProfitInStatistics ? 1 : 0;
            }
            return enableGross;
        },

        // 预约挂号-查看患者手机号 0 不能 1 能
        isCanSeePatientMobileInRegistration: (state) => state.employeeDataPermission?.registration?.isCanSeePatientMobile === 1,
        // 预约挂号-修改支付方式 0 不能 1 能
        isCanUpdatePayModeInRegistration: (state) => state.employeeDataPermission?.registration?.isCanSeeModifyPayMode === 1,
        // 预约挂号-修改挂号预约信息 0 不能 1 能
        isCanSeeModifyRegistrationInfo: (state) => state.employeeDataPermission?.registration?.isCanSeeModifyRegistration === 1,
        // 执行站-查看患者手机号 0 不能 1 能
        isCanSeePatientMobileInNurse: (state) => state.employeeDataPermission?.nurse?.isCanSeePatientMobile === 1,
        // 门诊-查看患者手机号 0 不能 1 能
        isCanSeePatientMobileInOutpatient: (state) => state.employeeDataPermission?.outpatient?.isCanSeePatientMobile === 1,
        // 收费-查看患者手机号 0 不能 1 能
        isCanSeePatientMobileInCashier: (state) => state.employeeDataPermission?.cashier?.isCanSeePatientMobile === 1,
        // 收费-修改支付方式 0 不能 1 能
        isCanUpdatePayModeInCashier: (state) => state.employeeDataPermission?.cashier?.isCanSeeModifyPayMode === 1,
        // 药房-查看患者手机号 0 不能 1 能
        isCanSeePatientMobileInPharmacy: (state) => state.employeeDataPermission?.pharmacy?.isCanSeePatientMobile === 1,

        // 患者-查看患者手机号 0 不能 1 能
        isCanSeePatientMobileInCrm: (state) => state.employeeDataPermission?.crm?.isCanSeePatientMobile === 1,
        // 患者-查看患者收费金额 0 不能 1 能
        isCanSeePatientPayAmountInCrm: (state) => state.employeeDataPermission?.crm?.isCanSeePatientPayAmount === 1,
        // 会员-查看会员毛利率 0 不能 1 能
        isCanSeeMemberProfitRateInCrm: (state) => state.employeeDataPermission?.crm?.isCanSeePatientProfit === 1,
        // 患者-修改会员积分 0 不能 1 能
        isCanUpdateMemberPointsInCrm: (state) => state.employeeDataPermission?.crm?.isCanModifyPoint === 1,
        // 患者-修改会员优惠券 0 不能 1 能
        isCanUpdateMemberCouponInCrm: (state) => state.employeeDataPermission?.crm?.isCanModifyCoupon === 1,
        // 工作台-收费员查看账目: 0 不允许查看账目 1 只允许查看个人经手账目 2 允许查看门店全部账目
        chargerPermissionInDashboard: (state) => state.employeeDataPermission?.dashboard?.chargerPermission,
        // 工作台-医生查看诊疗收入: 0 不允许查看个人诊疗收入 1 允许查看个人诊疗收入
        doctorOutpatientFeeInDashboard: (state) => state.employeeDataPermission?.dashboard?.doctorOutpatientFee === 1,
        // 工作台-医生查看挂号收入: 0 不允许查看个人挂号收入 1 允许查看个人挂号收入
        doctorRegistrationFeeInDashboard: (state) => state.employeeDataPermission?.dashboard?.doctorRegistrationFee === 1,
        // 工作台 - 医生查看执行金额: 0 不允许查看个人执行金额 1 允许查看个人执行金额
        doctorExecuteFeeDashboard: (state) => state.employeeDataPermission?.dashboard?.doctorExecuteFee === 1,
        // 工作台-医生查看患者看板: 0 只允许查看自己的患者看板 1 允许查看当天所有患者看板 2不允许查看患者看板
        kanbanPermissionInDashboard: (state) => state.employeeDataPermission?.dashboard?.kanbanPermission,

        // 门诊-医生查看药品价格: 0 只允许查看药品总价 1 允许查看药品明细 2 不允许查看药品明细，总价
        goodsPriceInOutpatient: (state) => state.employeeDataPermission?.outpatient?.goodsPrice,
        // 门诊-医生查看历史处方: 0 只允许查看自己开出的历史处方 1 允许查看患者所有的历史处方 2不允许查看(后端加的，数据权限不会修改为这个字段，通常为无这个模块权限，可能会返回2)
        historyPrescriptionInOutpatient: (state) => state.employeeDataPermission?.outpatient?.historyPrescription === 1,
        // 门诊（暂未使用）-医生查看处方价格: 0 不允许 1 允许
        isCanSeePrescriptionPriceInOutpatient: (state) => state.employeeDataPermission?.outpatient?.isCanSeePrescriptionPrice === 1,
        // 门诊（暂未使用）-医生查看门诊单总价: 0 不允许 1 允许
        isCanSeeTotalPriceInOutpatient: (state) => state.employeeDataPermission?.outpatient?.isCanSeeTotalPrice === 1,
        // 门诊-医生查看药品进价: 0 不能 1 能
        isCanSeeGoodsCostPriceInOutpatient: (state) => state.employeeDataPermission?.outpatient?.isCanSeeGoodsCostPrice === 1,
        // 门诊-修改挂号预约信息 0 不能 1 能
        isCanModifyOutpatientRegistration: (state) => state.employeeDataPermission?.outpatient?.isCanModifyRegistration === 1,

        // 医保 - 是否能查看主页: 0 不允许 1 允许
        isCanSeeSurveyInSocial: (state) => state.employeeDataPermission?.medicalInsurance?.isCanSeeHomePage === 1,
        // 医保 - 是否能查看医保账目: 0 不允许 1 允许
        isCanSeeAccountInSocial: (state) => state.employeeDataPermission?.medicalInsurance?.isCanSeeAccount === 1,
        // 医保 - 是否能查看医保业务登记信息: 0 不允许 1 允许
        isCanSeeRegisterInSocial: (state) => state.employeeDataPermission?.medicalInsurance?.isCanSeeBusinessRegistrationRecord === 1,
        // 医保 - 是否能查看机构资料数据: 0 不允许 1 允许
        isCanSeeMaintainInSocial: (state) => state.employeeDataPermission?.medicalInsurance?.isCanSeeProfileData === 1,
        // 医保 - 是否能查看设置信息: 0 不允许 1 允许
        isCanSeeSettingInSocial: (state) => state.employeeDataPermission?.medicalInsurance?.isCanSeeSetupInformation === 1,
        // 查看微诊所数据权限
        isCanSeeMicroClinicHomePageData: (state) => state.employeeDataPermission?.microClinic.isCanSeeHomePageData === 1,
        // 零售-查看药品物资毛利: 0 不能 1 能
        isCanSeeProfitInRetail: (state) => state.employeeDataPermission?.retail?.isCanSeeProfit === 1,
        // 零售-查看进价/毛利率: 0 不能 1 能
        isCanSeeGoodsCostPriceInRetail: (state) => state.employeeDataPermission?.retail?.isCanSeeGoodsCostPrice === 1,
        // 零售-会员余额充值/退款: 0 不能 1 能
        isCanOptPatientBalanceInRetail: (state) => state.employeeDataPermission?.retail?.isCanOptPatientBalance === 1,
        // 零售-会员优惠券发放: 0 不能 1 能
        isCanOptPatientCouponInRetail: (state) => state.employeeDataPermission?.retail?.isCanOptPatientCoupon === 1,
        // 零售-会员积分发放/抵扣
        isCanOptPatientPointsInRetail: (state) => state.employeeDataPermission?.retail?.isCanOptPatientPoints === 1,
        // 零售-查看会员毛利率
        isCanSeeMemberProfitRateInRetail: (state) => state.employeeDataPermission?.retail?.isCanSeePatientProfit === 1,
    },
    mutations: {
        /**
         * 设置用户诊所列表
         */
        SET_CLINICS: (state, clinics) => {
            state.clinics = (clinics || []).map((item) => {
                item.isUnpurchased = item.editionStatus === editionStatus.UN_PURCHASED; // 未购买
                item.isUneffevtive = item.editionStatus === editionStatus.UN_EFFEVTIVE; // 未生效
                item.isIneffevtive = item.editionStatus === editionStatus.IN_EFFEVTIVE; // 生效中
                item.isExpired = item.editionStatus === editionStatus.EXPIRED; // 已过期
                return item;
            });
            if (clinics) {
                localStorage.setItem(CLINICS, JSON.stringify(clinics));
            } else {
                localStorage.removeItem(CLINICS);
            }
        },
        /**
         * 设置用户当前选择过的诊所
         */
        SET_CURRENT_CLINIC: async (state, currentClinic) => {
            Store.set(CURRENT_CLINIC, currentClinic);
            // 多窗口问题
            refreshCurrentUserClinic();
            state.currentClinic = currentClinic;
        },
        /**
         * 设置待开通门店
         * <AUTHOR>
         * @date 2020-09-17
         * @param {Array} waitingOpenClinics 待开通门店列表
         */
        SET_WAITING_CLINIC: (state, waitingOpenClinics) => {
            state.waitingOpenClinics = waitingOpenClinics;
        },

        /**
         * 设置人员数据权限
         */
        SET_EMPLOYEE_DATA_PERMISSION: (state, data) => {
            state.employeeDataPermission = data;
        },

        UPDATE_USER_USE_RULE: (state, data) => {
            Object.assign(state.hospitalUserUseRule, data);
            console.log(state.hospitalUserUseRule);
        },
    },
    actions: {
        // 有登录状态时-无需登录获取个人信息+诊所列表
        async acGetEmployeesMe({
            commit, dispatch,
        }) {
            try {
                const { data } = await LoginApi.currentLoginInfo();
                // 更新用户权限开关，可能返回null
                if (data.employeeDataPermission) {
                    commit('SET_EMPLOYEE_DATA_PERMISSION', data.employeeDataPermission);
                }
                return dispatch('acHandleLoginSuccess', {
                    employee: {
                        ...data,
                    },
                });
            } catch (error) {
                return error;
            }
        },
        /**
         * desc [登录成功后的处理]
         * 1、保存userInfo
         * 2、拉取诊所列表
         */
        async acHandleLoginSuccess({
            dispatch,
        }, data) {
            const { employee } = data;
            // 本应该在登录之后，删除
            await dispatch('setUserInfo', {
                id: employee.id,
                name: employee.name,
                mobile: employee.mobile,
                countryCode: employee.countryCode,
                headImgUrl: employee.headImgUrl,
                boundWechat: employee.boundWechat, // 是否绑定微信登录，1-已绑定，0-未绑定
                isAdmin: employee?.isAdmin,
                needUpdatePassword: employee?.needUpdatePassword || 0,
                handSign: employee?.handSign || '',
            });
            Store.set('_is_login_', employee.id);
            Store.set('BUser', true);
            return dispatch('acGetClinicJoined');
        },
        /**
         * desc [拉取所在诊所列表]
         */
        async acGetClinicJoined({ commit }) {
            try {
                const { data } = await LoginApi.queryEmployeeJoined();
                const clinics = data.clinics.map((item) => ({
                    ...item,
                    /** 以下为兼容字段 */
                    chainId: isChainClinic(item) ? item.chain.id : '',
                    chainName: isChainClinic(item) ? item.chain.name : '',
                    clinicId: item.id,
                    chainAdmin: +isChainAdminClinic(item),
                    roleId: item.permission.roleId,
                    moduleIds: item.permission.moduleIds,
                }));
                commit('SET_CLINICS', clinics);
                commit('SET_WAITING_CLINIC', data.waitingOpenClinics);
                return null;
            } catch (error) {
                console.log('acLoginSuccess error', error);
                return error;
            }
        },
        /**
         * desc [拉取用户信息]
         */
        async acFetchUserInfo({
            commit, dispatch,
        }) {
            try {
                const { data } = await LoginApi.currentLoginInfo();
                const userInfo = {
                    id: data.id,
                    name: data.name,
                    mobile: data.mobile,
                    countryCode: data.countryCode || defaultCountryCode,
                    headImgUrl: data.headImgUrl,
                    hasPassword: data.hasPassword,
                    handSignType: data.handSignType,
                    handSignPicUrl: data.handSignPicUrl,
                    needUpdatePassword: data.needUpdatePassword || 0,
                    roleId: '',
                    moduleIds: '',
                    status: -1,
                    handSign: data.handSign || '',
                };
                if (data.clinicInfo) {
                    userInfo.roleId = data.clinicInfo.roleId;
                    userInfo.moduleIds = data.clinicInfo.moduleIds;
                    userInfo.modules = data.clinicInfo.modules;
                    userInfo.status = data.clinicInfo.status;
                    userInfo.nationalCode = data.clinicInfo.nationalDoctorCode;
                    const roleIds = data.clinicInfo.roles.map((item) => item.id);
                    const roleNames = data.clinicInfo.roles.filter((it) => it.parentRoleId === null).map((item) => item.name);
                    userInfo.roleIds = roleIds;
                    userInfo.roleNames = roleNames;
                    userInfo.parentRoles = data.clinicInfo.parentRoles;
                    userInfo.isAdmin = data.clinicInfo.isAdmin;
                    userInfo.qwUserInfo = data.clinicInfo.qwUserInfo;
                    userInfo.electronicSignatureCommitmentUrl = data.clinicInfo.config?.electronicSignatureCommitmentUrl;
                }


                if (data.chainInfo) {
                    userInfo.certNo = data.chainInfo.certNo;
                    userInfo.practiceCertCode = data.chainInfo.practiceCertCode;
                }

                if (data.clinicInfo) {
                    userInfo.clinicInfo = data.clinicInfo;
                }

                await dispatch('setUserInfo', userInfo);
                await dispatch('refreshChinesePRUsageDefault');
                await dispatch('setUserConfig', data);
                // 更新用户权限开关，可能返回null
                if (data.employeeDataPermission) {
                    commit('SET_EMPLOYEE_DATA_PERMISSION', data.employeeDataPermission);
                }
                commit('theme/SET_CUR_USE_THEME', data.themeConfig);
                return null;
            } catch (error) {
                console.log('acFetchUserInfo error', error);
                return error;
            }
        },
        /**
         * desc [拉取当前诊所最新信息]
         */
        async acFetchCurrentClinicInfo({
            state, commit,rootState,
        }) {
            try {
                const { data } = await LoginApi.currentClinic();
                commit('SET_CURRENT_CLINIC', {
                    ...data,
                    /** 以下为兼容字段 */
                    chainId: isChainClinic(data) ? data.chainId : '',
                    chainName: isChainClinic(data) ? data.chainName : '',
                    chainAdmin: +isChainAdminClinic(data),
                    clinicId: data.id,
                    clinicName: data.shortName || data.name,
                    userId: state.userInfo.id, // 用户id
                    isDoctor: data.permission.isDoctor, // 是否是医生
                    nationalCode: data.nationalCode,
                });
                commit('viewDistribute/SET_VIEW_CONFIG', {
                    currentClinic: data,
                    currentEdition: rootState.edition.edition,
                });
                return null;
            } catch (error) {
                console.log('acFetchCurrentClinicInfo error', error);
                return error;
            }
        },

        /**
         * @desc 切换门店
         * <AUTHOR>
         * @date 2023-05-23 15:28:22
         * @params
         * @return
         */
        async handleSwitchClinic({ dispatch }, {
            clinicId, scene = AppScene.HIS_PC, deviceMacList = [],
        }) {
            try {
                const res = await dispatch('handleRedirectToSwitchClinic', {
                    clinicId, scene, deviceMacList,
                });
                return res;
            } catch (e) {
                return e;
            }

        },
        // eslint-disable-next-line
        async handleRedirectToSwitchClinic({commit, state}, {clinicId, scene = AppScene.HIS_PC, deviceMacList = []}) {
            try {
                const { data } = await RegionLoginAPI.switchClinicRegion({
                    clinicId,
                    scene,
                    rememberLogin: !!state.rememberLogin,
                    ...getClientInfo(),
                    redirectUrl: (isDev || isTest || isLocal) && scene === AppScene.HIS_PC ? location.origin : '',
                    deviceMacList,
                });

                const {
                    redirectUrl, env, hisType, viewMode, clinicType, nodeType, zone,
                } = data;
                const newUrl = redirectUrl;

                const url = new URL(redirectUrl);

                if (isAbcClientSchema()) {
                    await getOfflineBundleByClinicInfo({
                        region: getRegionByHostname(url.hostname),
                        env,
                        zone,
                    });
                }

                closeChildWindow(state.currentClinic);

                if (scene === AppScene.HIS_PC) {
                    const clinicInfo = {
                        hisType, viewMode, clinicType, nodeType,
                    };
                    const _isHospital = isHospital(clinicInfo);
                    const _isPharmacy = isPharmacy(clinicInfo);
                    const _isChainAdmin = isChainAdminClinic(clinicInfo);
                    let regionAuthUrl = newUrl;
                    if (_isHospital) {
                        regionAuthUrl = newUrl.replace('auth-callback', 'hospital/region-auth');
                    } else if (_isPharmacy) {
                        regionAuthUrl = newUrl.replace('auth-callback', 'biz-pharmacy/region-auth');
                    } else if (_isChainAdmin) {
                        regionAuthUrl = newUrl.replace('auth-callback', 'chain/region-auth');
                    } else {
                        regionAuthUrl = newUrl.replace('auth-callback', 'region-auth');
                    }
                    regionAuthUrl += `&env=${env}`;
                    navigateToAuthCallback(regionAuthUrl, _isHospital);
                } else {
                    windowOpen(newUrl);
                }
                closeChildWindow(state.currentClinic);
                // 防止重定向后，页面还未跳转，就执行了后续的逻辑
                return {
                    errCode: 0, errMsg: 'success',
                };
            } catch (e) {
                return e;
            }
        },
        async handleEmployeeLogout({ dispatch }) {
            try {
                await dispatch('handleRegionLogout');
            } catch (e) {
                return e;
            } finally {
                await dispatch('clearUserInfo');
            }
        },
        async handleRegionLogout() {
            try {
                await RegionLoginAPI.loginOutRegion({
                    scene: AppScene.HIS_PC,
                    logoutWay: 'logout',
                });
            } catch (e) {
                console.log('region logout err', e);
            }
        },

        async clearUserInfo({
            commit, dispatch, state,
        }) {
            Store.set('cashier_pay_mode', null);
            Store.set('examination_edit_cache', null);
            Store.set('outpatient_edit_cache', null);
            Store.set('outpatient_delivery_medicine', null);
            Store.set('cashier_delivery_medicine', null);
            window.localStorage.removeItem(ABCYUN_TOKEN_STORAGE);
            await dispatch('setUserInfo', null);
            commit('SET_CLINICS', null);
            commit('SET_CURRENT_CLINIC', null);
            await dispatch('removeRefreshToken');
            await cookieService.remove(CSRF_TOKEN);
            await cookieService.remove(CSRF_TOKEN_SIG);
            closeChildWindow(state.currentClinic);
        },
        /**
         * desc [每天是否有登录使用，有的话刷新token自动延长有效时间]
         */
        async initRefreshToken({ dispatch }) {
            const isRefresh = await cookieService.get(IS_REFRESH_TOKEN);
            !isRefresh && dispatch('postRefreshToken');
        },

        async setRefreshToken() {
            await cookieService.set(IS_REFRESH_TOKEN, 'TRUE', {
                expires: IS_REFRESH_TOKEN_EXPIRE_DAYS,
            },{ domain: ABC_DOMAIN });
        },

        async removeRefreshToken() {
            await cookieService.remove(IS_REFRESH_TOKEN, { domain: ABC_DOMAIN });
        },

        async postRefreshToken({ dispatch }) {
            try {
                await dispatch('setRefreshToken');
                await RegionLoginAPI.postRefreshTokenRegion();
            } catch (error) {
                await dispatch('removeRefreshToken');
                console.log('refreshToken error', error);
            }
        },

        async setUserUseRule({ commit }, data) {
            commit('UPDATE_USER_USE_RULE', data);
        },
        /**
         * 区域登录
         * @return {Promise<void>}
         */
        async regionLoginByCode({
            commit, dispatch, rootState,
        }, code) {
            try {
                const { data } = await RegionLoginAPI.loginByCode({
                    code,
                    ...getClientInfo(),
                });

                const {
                    employee, clinic, token,
                } = data;
                window.localStorage.setItem(ABCYUN_TOKEN_STORAGE, token);
                commit('SET_CURRENT_CLINIC', {
                    ...clinic,
                    /** 以下为兼容字段 */
                    chainId: isChainClinic(clinic) ? clinic.chainId : '',
                    chainName: isChainClinic(clinic) ? clinic.chainName : '',
                    chainAdmin: +isChainAdminClinic(clinic),
                    clinicId: clinic.id,
                    clinicName: clinic.shortName || clinic.name,
                    userId: employee.id, // 用户id
                    isDoctor: clinic.permission.isDoctor, // 是否是医生
                    nationalCode: clinic.nationalCode,
                });
                commit('viewDistribute/SET_VIEW_CONFIG', {
                    currentClinic: clinic,
                    currentEdition: rootState.edition.edition,
                });
                const roles = (clinic.permission && clinic.permission.roles) || [];
                const roleNames = roles.filter((it) => it.parentRoleId === null).map((item) => item.name);
                const roleIds = roles.map((item) => item.id);
                await dispatch('setUserInfo', {
                    ...employee,
                    /** 以下为兼容字段 */
                    roleId: clinic.permission.roleId, // 1,管理员；2,普通成员；
                    moduleIds: clinic.permission.moduleIds, // 拥有的模块
                    status: clinic.permission.status, // 0,待审核状态；1,正常状态；
                    roleIds, // 用户当前角色
                    roleNames,
                    isAdmin: clinic.permission.isAdmin,
                });
                // region 登录成功，也记录一次 BUser，这样到官网后会跳到登录页
                Store.set('BUser', true);
                return null;
            } catch (error) {
                console.log('regionLoginByCode error', error);
                return error;
            }
        },

        updateDoctorSign({
            state, dispatch,
        }, data) {
            dispatch('setUserInfo', {
                ...state.userInfo,
                ...data,
            });
        },

        /**
         * 增量设置用户信息
         */
        async setUserInfo({ state }, userInfo) {
            state.userInfo = state.userInfo && userInfo ? {
                ...state.userInfo,
                ...userInfo,
            } : userInfo;

            if (userInfo) {
                // hack 如果moduleIds里面包含0，包含所有模块，直接将moduleIds赋值 为0
                let moduleIds = userInfo.moduleIds?.split(',') || [];
                moduleIds = moduleIds.includes('0') ? '0' : moduleIds.join(',');
                state.userInfo.moduleIds = moduleIds;

                let remember = state.rememberLogin;
                remember = !!remember;
                // 设置之前，先清除历史遗留的，没有设置Cookie域的userInfo
                await cookieService.remove(USER_INFO);
                await cookieService.remove(USER_INFO, { domain: ABC_DOMAIN });
                if (remember) {
                    // 记住登录
                    await cookieService.set(USER_INFO, JSON.stringify({ id: userInfo?.id }), {
                        expires: 30, domain: ABC_DOMAIN,
                    });
                } else {
                    await cookieService.set(USER_INFO, JSON.stringify({ id: userInfo?.id }), { domain: ABC_DOMAIN });
                }
            } else {
                await cookieService.remove(USER_INFO);
                await cookieService.remove(USER_INFO, { domain: ABC_DOMAIN });
            }
            refreshCurrentUserClinic();
        },

        async updateUserActionByCookie({ state }) {
            state.loginWay = await cookieService.get(LOGIN_WAY_KEY);
            state.rememberLogin = !!(await cookieService.get(KEY_REMEMBER));
            const userInfo = await cookieService.get(USER_INFO);
            state.userInfo = userInfo ? JSON.parse(userInfo) : null;
            state.userUUID = await cookieService.get(UUID);
            const cookieToken = await cookieService.get(ABCYUN_TOKEN);
            if (cookieToken) {
                window.localStorage.setItem(ABCYUN_TOKEN_STORAGE, cookieToken);
            }
        },
    },
};

export default user;
