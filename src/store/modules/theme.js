import ThemeAPI from 'api/theme.js';
import { AbcSetTheme } from 'utils/theme-set.js';
import variables from '@/styles/theme.module.scss';

const Theme = {
    namespaced: true,
    state: {
        isInit: false,

        backgroundColorList: [],
        backgroundImageList: [],

        currentUse: {
            id: null,
            background: '',
            headerBackgroundColor: '',
            headerFontColor: '',
        },
        // 当前系统使用的主题色等
        style: {
            ...variables,
        },
        typeId: '',
    },

    getters: {
        backgroundColorList(state) {
            return state.backgroundColorList;
        },
        backgroundImageList(state) {
            return state.backgroundImageList;
        },
        curUseTheme(state) {
            return state.currentUse;
        },
        curTypeId(state) {
            return state.typeId;
        },
        /**
         * 是否是深色主题，通过文字颜色判断，非黑色文字，就是深色主题
         */
        isDarkTheme(state) {
            return state.currentUse.headerFontColor !== '#000000';
        },
    },
    mutations: {
        SET_THEME_CONFIG: (state, data) => {
            state.isInit = true;
            Object.assign(state, data);
        },
        SET_CUR_USE_THEME: (state, data) => {
            state.typeId = data.typeId;
            if (!data || !data.content) {
                data = {
                    type: 1,
                    content: {
                        background: '#F5F5F5',
                        headerBackgroundColor: '#3975C6',
                        headerFontColor: '#FFFFFF',
                    },
                };
            }
            const {
                id, type, content,
            } = data;
            let { background } = content;
            if (type === 2) {
                background = `url(${background}) center center / cover no-repeat fixed`;
            } else if (type === 1) {
                // 全屏灰底方案统一背景色
                background = '#F5F5F5';
                content.headerBackgroundColor = content.headerBackgroundColor || '#3975C6';
                content.headerFontColor = content.headerFontColor || '#FFFFFF';
            }
            const themeContent = {
                id,
                ...content,
                background,
            };
            Object.assign(state.currentUse, themeContent);
            AbcSetTheme(state.currentUse);
        },
    },
    actions: {
        async initThemeConfig({
            state, dispatch,
        }) {
            !state.isInit && await dispatch('fetchThemeConfig');
        },
        async fetchThemeConfig({ commit }) {
            try {
                const { data } = await ThemeAPI.fetchThemeConfig();
                commit('SET_THEME_CONFIG', {
                    backgroundColorList: data.rows.filter((item) => item.type === 1),
                    backgroundImageList: data.rows.filter((item) => item.type === 2),
                });
            } catch (e) {
                console.error(e);
            }
        },

        async setCurUseTheme({ commit }, data) {
            try {
                await ThemeAPI.updateTheme(data);
                commit('SET_CUR_USE_THEME', data);
            } catch (e) {
                console.error(e);
            }
        },
    },
};

export default Theme;
