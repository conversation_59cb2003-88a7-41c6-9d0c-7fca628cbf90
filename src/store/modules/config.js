/**
 * 设置相关
 */
import API from 'api/settings';
import SettingApi from 'api/settings';
import CallingAPI from 'api/call';
import keyCodeMap from '../../assets/configure/key-code.js';
import PropertyAPI from 'api/property';
import { getViewDistributeConfig } from '@/views-distribute/utils.js';
import PharmacyCallAPI from 'api/pharmacy-call';
import IntegralAPI from 'api/marketing/integral';

const doctor = {
    state: {
        userConfig: {
            reservedFeeChargeNow: true, // 预约时收费
            regFeeChargeNow: true, // 挂号时收费
            signInChargeNow: true, // 签到时收费

            reservedPrint: {
                printReg: true, //挂号单
                printCashierBill: true, //挂号发票
                printRegTag: false,//挂号标签
                printBillFeeList: false,
                printPatientTag: false,
                isRegistrationMeanwhilePrint: true, //同时打印
            }, // 预约时打印
            regPrint: {
                printReg: true,
                printCashierBill: true,
                printRegTag: false,//挂号标签
                printBillFeeList: false,
                printPatientTag: false,
                isRegistrationMeanwhilePrint: true,
            }, // 挂号时打印
            signInPrint: {
                printReg: true,
                printCashierBill: true,
                printRegTag: false,//挂号标签
                printBillFeeList: false,
                printPatientTag: false,
                isRegistrationMeanwhilePrint: true,
            }, // 签到时打印

            // 挂号面板操作
            // 隐藏用户信息
            regsHiddenIdCard: true,
            regsHiddenSource: false,
            regsHiddenAddress: true,
            // 隐藏患者工作单位
            regsHiddenCompany: true,
            // 隐藏本次推荐
            regsHiddenReCommend: true,
            // 隐藏患者职业
            regsHiddenProfession: true,
            // 是否显示就诊来源
            regsShowVisitSource: true,
            // 隐藏就诊备注Remark
            regsHiddenVisitRemark: false,
            // 隐藏患者SN
            regsHiddenSn: true,
            // 隐藏患者Tags
            regsHiddenTags: true,
            // 隐藏患者备注Remark
            regsHiddenRemark: false,
            // 隐藏预诊信息
            regsHiddenMedicalRecord: true,

            // 理疗预约相关
            therapyHiddenIdCard: true,
            therapyHiddenCompany: true,
            therapyHiddenAddress: true,
            therapyHiddenProfession: true,
            therapyHiddenSn: true,
            therapyHiddenTags: true,
            // 患者备注
            therapyHiddenRemark: false,
            // 隐藏就诊备注Remark
            therapyHiddenVisitRemark: false,
            therapyHiddenSource: false,
            therapySignInPrint: {
                printReg: true, //挂号单
                printCashierBill: false, //挂号发票
                isRegistrationMeanwhilePrint: true, //同时打印
            },
            therapyPrint: {
                printReg: true, //挂号单
                printCashierBill: false, //挂号发票
                isRegistrationMeanwhilePrint: true, //同时打印
            },
        },
        // 叫号设置
        callConfig: null,
        initCallingNextTips: false, // 是否初始化待诊呼叫下一位不再弹窗提示
        callingNextTips: false, // 待诊呼叫下一位不再弹窗提示
        // 药房叫号
        pharmacyCallConfig: null,
        mallWaitingPaidOrderCount: 0,//商城待付款数量
        isFirstOpenNoticeDialog: true,//是否第一次读商城的待付款订单
        isFirstReadCouponNotice: true,//是否第一次读取红包临期信息
        pointsConfig: {},// 积分设置
        // 收费设置初始化
        chargeConfigInited: false,
        // 收费设置
        chargeConfig: {
            // 零头的自动处理方式: 0-保持不变，1-凑整到角，2-凑整到元，3-抹零到角，4-抹零到元，5-四舍五入到角，6-四舍五入到元
            roundingType: 0,
            // 允许收费员整单议价
            bargainSwitch: 0,
            // 允许收费员单项议价
            singleBargainSwitch: 0,
            // 允许医生整单议价
            doctorBargainSwitch: 0,
            // 允许医生单项议价
            doctorSingleBargainSwitch: 0,
            // 执行站开单可对金额单项议价
            nurseBargainSwitch: 1,
            // 检查模块开单可对金额单项议价
            inspectBargainSwitch: 1,
            // 可用的收费方式
            chargePayModeConfigs: [],
            // 自动发送收费订单
            autoSendOrderInfoSwitch: 0,
            // 手动发送收费订单
            sellerSendOrderInfoSwitch: 0,
            // 医生可推送订单给患者
            doctorCanPushChargeSheetSwitch: 1,
            // 允许医生挂号费议价
            doctorRegisteredBargainSwitch: 1,
            // 允许欠费支付
            oweSheetSwitch: 0,
            // 关闭收费单时间
            autoClosedTime: 0,
            // 自动关闭收费单
            autoClosedSwitch: 0,
            // 允许预约挂号模块对挂号费议价
            reservationRegisteredBargainSwitch: 1,
            // 收费限制营销后不可用医保
            usedDiscountNotAllowShebaoSwitch: 0,
            // 零售收费
            directSaleEnable: 1,
            // 退费条件 0-发药完成不可退 1-审核完成不可退 2-调配完成不可退
            refundRestriction: 0,
            // 退费审核 0-不审核 1-审核
            refundCheck: 0,
            // 退费审核人员
            refundCheckEmployees: [],
            // 是否整单收退费
            wholeSheetOperateEnable: 0,
            // 收费权限均可议价-0 指定人员-1
            assignEmployeeBargain: 0,
            // 有门诊权限的成员均可议价-0  指定人员-1
            doctorAssignEmployeeBargain: 0,
        },

        dispensingConfigInited: false,
        dispensingConfig: {
            distributionScope: [],
            isDecoction: 0,
            toHome: 0,
            showMedicinePriceDetail: 1, // 药房 展示药品价格明细
            showMedicineTotalPrice: 1, // 药房 展示药品总价
            showPriceType: 2,
            prescriptionReview: 0,
            prescriptionCompound: 0,
            takeMedicineTimeConfig: 0,
            isTakeMedicationTime: 0, // 取药时间
            // 是否整单发退药
            wholeSheetOperateEnable: 0,
        },

        treatOlineConfigInited: false,
        treatOnlineConfig: {
            treatOnlineSwitch: 0,
        },

        weChatPayConfigInited: false,
        weChatPayConfig: {
            weChatPaySwitch: -1,
            jsapiPayStatus: -1,
            type: -1,
        },
        isOpenRegistrationAppointmentVoice: 0, // 是否开启预约提醒音频

    },

    mutations: {
        SET_REGISTRATION_APPOINTMENT_VOICE: (state, data) => {
            state.isOpenRegistrationAppointmentVoice = data;
        },
        SET_USER_CONFIG: (state, data) => {
            Object.assign(state.userConfig, data || {});
        },
        SET_CALL_CONFIG: (state, data) => {
            state.callConfig = data;
        },
        SET_PHARMACY_CALL_CONFIG: (state, data) => {
            state.pharmacyCallConfig = data;
        },
        SET_MALL_COUNT: (state, data) => {
            state.mallWaitingPaidOrderCount = data;
        },
        SET_NOTICE_FLAG: (state, data) => {
            state.isFirstOpenNoticeDialog = data;
        },
        SET_COUPON_NOTICE_FLAG: (state, data) => {
            state.isFirstReadCouponNotice = data;
        },
        SET_CHARGE_CONFIG: (state, data) => {
            if (data && data.chargePayModeConfigs) {
                // 处理收费方式快捷键
                data.chargePayModeConfigs.forEach((item) => {
                    item.payMode = item.payMode || item.payModeId;
                    item.shortcutKeyCode = -1;
                    if (item.shortcutKey && keyCodeMap[item.shortcutKey]) {
                        item.shortcutKeyCode = keyCodeMap[item.shortcutKey];
                    }
                });
            }

            // 支付订单推送方式数据兼容
            data.sendOrderInfoModes = data.sendOrderInfoModes || [];
            data.sellerSendOrderInfoSwitch = data.sendOrderInfoModes.includes(0) ? 1 : 0;
            data.autoSendOrderInfoSwitch = data.sendOrderInfoModes.includes(1) ? 1 : 0;

            Object.assign(state.chargeConfig, data || {});
        },
        SET_CHARGE_CONFIG_INIT: (state, data) => {
            state.chargeConfigInited = data;
        },
        SET_POINTS_CONFIG: (state, data) => {
            state.pointsConfig = data;
        },
        SET_DISPENSING_CONFIG: (state, data) => {
            state.dispensingConfigInited = true;
            Object.assign(state.dispensingConfig, data || {});
        },
        SET_TREATONLINE_CONFIG: (state, data) => {
            state.treatOlineConfigInited = true;
            Object.assign(state.treatOnlineConfig, data || {});
        },
        SET_WECHATPAY_CONFIG: (state, data) => {
            state.weChatPayConfigInited = true;
            Object.assign(state.weChatPayConfig, data || {});
        },
        SET_ABCPAY_CONFIG: (state, data) => {
            Object.assign(state.abcPayConfig, data || {});
        },
        SET_CALLING_NEXT_TIPS: (state, data) => {
            state.initCallingNextTips = true;
            state.callingNextTips = data || false;
        },
    },

    actions: {
        async fetchRegistrationAppointmentVoiceSetting({ commit }) {
            try {
                const { data } = await API.employee.fetchRegistrationAppointmentVoiceSetting();
                if (data?.waitAuditRegistration) {
                    commit('SET_REGISTRATION_APPOINTMENT_VOICE', data.waitAuditRegistration);
                }
            } catch (e) {
                console.log(e);
            }
        },
        async updateRegistrationAppointmentVoiceSetting({ commit }, data) {
            commit('SET_REGISTRATION_APPOINTMENT_VOICE', data);
        },
        // 获取西成药用法用药频率等列表
        async setUserConfig({ commit }, data) {
            const { clinicInfo } = data;
            const viewDistributeConfig = getViewDistributeConfig();
            commit('SET_USER_CONFIG', (clinicInfo && clinicInfo.config) || {
                regsHiddenReCommend: viewDistributeConfig.Registration.settings.regsHiddenReCommend,
                regsHiddenMedicalRecord: viewDistributeConfig.Registration.settings.regsHiddenMedicalRecord,
            });
        },

        async updateUserConfig({ commit }, data) {
            commit('SET_USER_CONFIG', data);
        },

        async updateUserConfigSubmit({ commit }, data) {
            try {
                await API.personal.updateEmployeeConfig(data);
            } catch (e) {
                console.error(e);
            }
            commit('SET_USER_CONFIG', data);
        },

        async updateRegPrintConfigSubmit({
            state, commit,
        }, data) {
            const postData = Object.assign(state.userConfig, data);

            try {
                await API.personal.updateEmployeeConfig(postData);
            } catch (e) {
                console.error(e);
            }
            commit('SET_USER_CONFIG', data);
        },
        /**
         * desc [初始叫号设置]
         */
        initCallConfig({
            state, dispatch,
        }) {
            !state.callConfig && dispatch('fetchCallConfig');
        },
        /**
         * desc [拉取叫号设置]
         */
        async fetchCallConfig({ commit }) {
            try {
                const { data } = await CallingAPI.fetchCallingSettings();
                if (data) {
                    commit('SET_CALL_CONFIG', data.config);
                }
            } catch (error) {
                console.log('fetchCallConfig error', error);
            }
        },
        /**
         * desc [更新叫号设置]
         */
        async updateCallConfig({ commit }, data) {
            commit('SET_CALL_CONFIG', data);
        },
        /**
         * desc [待诊呼叫下一位不再弹窗提示]
         */
        initCallingNextTips({
            state, dispatch,
        }) {
            !state.initCallingNextTips && dispatch('fetchCallingNextTips');
        },
        /**
         * desc [获取待诊呼叫下一位不再弹窗提示配置]
         */
        async fetchCallingNextTips({ commit }) {
            try {
                const { data } = await API.employee.fetchCallingNextTips();
                if (data) {
                    commit('SET_CALLING_NEXT_TIPS', data.callingNextTips);
                }
            } catch (e) {
                console.log(e);
            }
        },
        /**
         * desc [更新待诊呼叫下一位不再弹窗提示配置]
         */
        async updateCallingNextTips({ commit }, data) {
            commit('SET_CALLING_NEXT_TIPS', data);
        },
        /**
         * desc [初始药房叫号设置]
         */
        initPharmacyCallConfig({
            state, dispatch,
        }) {
            !state.pharmacyCallConfig && dispatch('fetchPharmacyCallConfig');
        },
        /**
         * desc [拉取叫号设置]
         */
        async fetchPharmacyCallConfig({ commit }) {
            try {
                const { data } = await PharmacyCallAPI.fetchDispensingCallConfig();
                if (data) {
                    commit('SET_PHARMACY_CALL_CONFIG', data);
                }
            } catch (error) {
                console.log('fetchCallConfig error', error);
            }
        },
        /**
         * desc [更新叫号设置]
         */
        async updatePharmacyCallConfig({ commit }, data) {
            commit('SET_PHARMACY_CALL_CONFIG', data);
        },
        /**
         * desc [初始收费设置]
         */
        initChargeConfig({
            state, dispatch,
        }) {
            return !state.chargeConfigInited && dispatch('fetchChargeConfig');
        },
        async fetchPointsConfig({
            commit,
        }) {
            try {
                const { data } = await IntegralAPI.fetchPointsConfig();
                data.applicationGoodsList = data.applicationGoodsList?.map((it) => {
                    return {
                        ...it,
                        exceptItems: it.exceptInfo ? it.exceptInfo.goodsItems : [],
                    };
                });
                commit('SET_POINTS_CONFIG', data);
            } catch (error) {
                console.log('fetchPointsConfig error', error);
            }
        },
        /**
         * desc [拉取收费设置]
         */
        async fetchChargeConfig({ commit }) {
            try {
                commit('SET_CHARGE_CONFIG_INIT', true);
                const { data } = await SettingApi.chargeSet.selectChargesConfigAvailable();
                commit('SET_CHARGE_CONFIG', data);
            } catch (error) {
                commit('SET_CHARGE_CONFIG_INIT', false);
                console.log('fetchChargeConfig error', error);
            }
        },
        /**
         * desc [消息推送处获取]
         */
        async fetchMallWaitingPaidOrderCount({ commit }, data) {
            try {
                commit('SET_MALL_COUNT', data);
            } catch (error) {
                console.log('fetchMallCount', error);
            }
        },
        async setNoticeFlag({ commit },data) {
            try {
                commit('SET_NOTICE_FLAG', data);
            } catch (e) {
                console.log('NoticeFlag', e);
            }
        },
        async setCouponNoticeFlag({ commit },data) {
            commit('SET_COUPON_NOTICE_FLAG', data);
        },
        /**
         * desc [拉取商城设置]
         */
        async fetchMallCount({ commit }) {
            try {
                const { data } = await SettingApi.mall.getMallWaitingPaidOrderCountTips();

                commit('SET_MALL_COUNT', data?.waitingPaidOrderCount || 0);

            } catch (error) {
                console.log('fetchMallCount', error);
            }
        },

        initDispensingConfig({
            state, dispatch,
        }) {
            if (!state.dispensingConfigInited) {
                dispatch('fetchDispensingConfig');
                dispatch('fetchPharmacyConfig');
            }
        },
        /**
         * @desc 拉取发药设置
         * <AUTHOR>
         * @date 2020/02/24 12:55:13
         */
        async fetchDispensingConfig({ commit }) {
            const { data } = await PropertyAPI.get('treatOnline.dispensing', 'clinic', undefined, undefined, true);
            commit('SET_DISPENSING_CONFIG', data.dispensing);
        },
        /**
         * @desc 药房设置
         * <AUTHOR>
         * @date 2020-05-29 10:01:18
         */
        async fetchPharmacyConfig({ commit }) {
            const { data } = await PropertyAPI.getV3('dispensing', 'clinic');
            commit('SET_DISPENSING_CONFIG', data);
        },

        initTreatOnlineConfig({
            state, dispatch,
        }) {
            !state.treatOlineConfigInited && dispatch('fetchTreatOnlineConfig');
        },
        /**
         * @desc 获取网诊状态
         * <AUTHOR>
         * @date 2020/02/25 14:24:57
         */
        async fetchTreatOnlineConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.get('treatOnline.treatRule', 'chain');
                if (data && data.treatRule) {
                    commit('SET_TREATONLINE_CONFIG', data.treatRule);
                }
            } catch (e) {
                console.error(e);
            }
        },

        async initWeChatPayConfig({
            state, dispatch,
        }) {
            !state.weChatPayConfigInited && await dispatch('fetchWeChatPayConfig');
        },
        /**
         * @desc 获取微信支付状态
         * <AUTHOR>
         * @date 2020/02/25 14:42:07
         */
        async fetchWeChatPayConfig({ commit }) {
            try {
                const { data } = await SettingApi.weChatPay.fetchWeChatPayConfig();
                if (data) {
                    commit('SET_WECHATPAY_CONFIG', data);
                }
            } catch (e) {
                console.error(e);
            }
        },
    },
};

export default doctor;
