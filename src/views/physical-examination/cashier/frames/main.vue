<template>
    <div v-abc-loading:page="loading">
        <abc-container-center-top-head>
            <h2>体检收费</h2>
            <div class="buttons-wrapper">
                <img
                    v-if="isClosed"
                    class="charge-seal"
                    src="~assets/images/icon/<EMAIL>"
                    alt=""
                />
                <template v-if="isCharged">
                    <div class="amount">
                        <abc-money
                            :value-style="{
                                color: canAllRefund ? themeStyle.T1 : themeStyle.T2
                            }"
                            :symbol-icon-size="16"
                            :symbol-icon-color="canAllRefund ? themeStyle.T1 : themeStyle.T2"
                            is-show-space
                            :value="isClosed ? needPayFee : realReceivedFee"
                        ></abc-money>
                    </div>
                    <abc-button
                        v-if="canAllRefund"
                        type="blank"
                        @click="handleClickAllRefund"
                    >
                        退费
                    </abc-button>
                    <abc-button
                        v-if="isClosed"
                        variant="ghost"
                        @click="handleClickRecharge"
                    >
                        重新收费
                    </abc-button>
                    <abc-button type="blank" @click="openInvoiceDialog">
                        开票
                    </abc-button>
                </template>
                <template v-else>
                    <div class="amount">
                        <abc-money
                            :symbol-icon-size="16"
                            :value-style="{
                                color: themeStyle.T1
                            }"
                            :symbol-icon-color="themeStyle.T1"
                            is-show-space
                            :value="needPayFee"
                        ></abc-money>
                    </div>
                    <abc-button v-if="needPayFee >= 0" @click="handleCharge">
                        {{ isPartCharged ? '继续收费' : '收费' }}
                    </abc-button>
                    <abc-button
                        v-if="needPayFee < 0"
                        type="blank"
                        @click="handleClickRefund"
                    >
                        退费
                    </abc-button>
                    <abc-button
                        variant="ghost"
                        @click="handleClose"
                    >
                        关闭
                    </abc-button>
                </template>

                <print-popper
                    size="small"
                    :width="64"
                    :style="{
                        'margin-left': '4px'
                    }"
                    :box-style="{
                        width: '104px'
                    }"
                    placement="bottom"
                    :options="printOptions"
                    @print="printHandler"
                    @select-print-setting="openPrintConfigSettingDialog"
                >
                </print-popper>
            </div>
        </abc-container-center-top-head>

        <abc-container-center-main-content>
            <abc-layout>
                <abc-section>
                    <patient-section
                        :key="patientInfo.id"
                        v-model="patientInfo"
                        disabled
                        :loading="loading"
                        :default-patient="currentPatient"
                        show-id-card
                        show-marital
                        require-id-card
                        require-mobile
                        is-required
                        @update-patient="changePatientInfo"
                    ></patient-section>
                </abc-section>
                <abc-section>
                    <abc-descriptions
                        :column="3"
                        :label-width="108"
                        grid
                    >
                        <abc-descriptions-item
                            v-for="item in peOrderInfoList"
                            :key="item.label"
                            :label="item.label"
                            content-class-name="ellipsis"
                        >
                            <span :title="item.value">{{ item.value }}</span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-section>

                <abc-section v-for="(form) in chargeForms" :key="form.id">
                    <p-e-table-personal-add v-if="form.type === PEChargeFormTypeEnum.PERSONAL_ADDITION" :form="form" :is-closed="isClosed"></p-e-table-personal-add>
                    <p-e-table-compose v-else-if="form.type === PEChargeFormTypeEnum.PE_GROUP_COMPOSE" :form="form" :is-closed="isClosed"></p-e-table-compose>
                    <p-e-table-compose v-else-if="form.type === PEChargeFormTypeEnum.PE_COMPOSE" :form="form" :is-closed="isClosed"></p-e-table-compose>
                </abc-section>

                <abc-section>
                    <p-e-table-team v-if="false"></p-e-table-team>
                </abc-section>
            </abc-layout>
        </abc-container-center-main-content>

        <sidebar
            :charge-forms="chargeForms"
            :pe-order-info="peOrderInfo"
            :invoice-list="invoiceList"
            :invoice-status-flag="invoiceStatusFlag"
            :charge-sheet-summary="chargeSheetSummary"
            :transactions="chargeSheetSummary.transactions"
        ></sidebar>
    </div>
</template>

<script type="text/ecmascript-6">
    import PatientSection from 'views/layout/patient/patient-section/index.vue';
    import PETablePersonalAdd from '../components/pe-table-personal-add.vue';
    import PETableCompose from '../components/pe-table-compose.vue';
    import PETableTeam from '../components/pe-table-team.vue';
    import {
        PEOrderTypeLabel,
        ReportGetWayLabel,
        PEBusinessTypeLabel,
        OrderStatusLabelEnum,
        PEChargeFormTypeEnum, PEPayStatusEnum,
    } from 'views/physical-examination/constants.js';
    import { address2Str } from 'src/filters/index';
    import { formatDate } from '@abc/utils-date';
    import AbcCommonChargeDialog from 'components/common-charge-dialog/index.js';
    import AbcCommonRefundDialog from '@/components/common-refund-dialog/index.js';
    import {
        navigateToAggregatePaymentContentSetting, navigateToInvoiceConfig,
    } from '@/core/navigate-helper.js';
    import { mapGetters } from 'vuex';
    import PEChargeAPI from 'api/physical-examination/pe-charge.js';
    import { PayModeEnum } from '@/service/charge/constants.js';
    import PERefundSelectDialog from '../components/dialog-select-refund';
    import { getPostItems } from 'views/physical-examination/cashier/utils/index.js';
    import themeStyle from '@/styles/theme.module.scss';
    import InvoiceDialog from 'views/cashier/invoice';
    import {
        InvoiceBusinessScene, InvoiceCategory,
    } from 'views/cashier/invoice/constants';
    import { autoDestroyInvoice } from 'views/cashier/invoice/utils';
    import InvoiceService from 'views/cashier/invoice/write-invoice-core-v2/invoice-service';

    import PrintPopper from 'views/print/popper';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import { PhysicalExaminationApi } from '@/printer/print-api/pe';
    import CrmAPI from 'api/crm';



    export default {
        name: 'PhysicalExaminationCashierMain',
        components: {
            PatientSection,
            Sidebar: () => import('views/physical-examination/cashier/components/sidebar.vue'),
            PETablePersonalAdd,
            PETableCompose,
            PETableTeam,
            PrintPopper,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                themeStyle,
                loading: false,
                PEChargeFormTypeEnum,
                peOrderInfo: {},
                patientInfo: {},
                chargeSheetSummary: {},
                chargeForms: [],
                invoiceList: [],
                invoiceStatusFlag: null,
            };
        },
        computed: {
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            ...mapGetters([
                'currentClinic',
                'printBillConfig',
                'printMedicalListConfig',
                'userInfo',
            ]),
            ...mapGetters('invoice', ['isOpenInvoice', 'isOpenMedicalInvoice', 'medicalElectronicAPIConfig', 'writeInvoiceConfig', 'invoiceConfigList']),
            isCharged() {
                const {
                    chargeStatus,
                } = this.chargeSheetSummary;
                return chargeStatus >= PEPayStatusEnum.CHARGED;
            },

            isClosed() {
                const {
                    chargeStatus,
                } = this.chargeSheetSummary;
                return chargeStatus === PEPayStatusEnum.CLOSED;
            },

            printOptions() {
                return [
                    {
                        value: this.viewDistributeConfig.Print.printOptions.PE_CHARGE_FEE_LIST.label,
                        disabled: this.chargeSheetSummary.chargeStatus !== PEPayStatusEnum.CHARGED,
                    },
                ];
            },

            isPartCharged() {
                const {
                    chargeStatus,
                } = this.chargeSheetSummary;
                return PEPayStatusEnum.PART_CHARGED === chargeStatus;
            },

            // 已经结算的单据，能否退费
            canAllRefund() {
                return this.chargeSheetSummary.realReceivedFee > 0;
            },

            realReceivedFee() {
                const {
                    realReceivedFee,
                } = this.chargeSheetSummary;
                return realReceivedFee || 0;
            },

            /**
             * @desc 小于0代表有退项，需要展示退费按钮
             * <AUTHOR>
             * @date 2023-11-27 14:32:27
             */
            needPayFee() {
                const {
                    needPayFee,
                } = this.chargeSheetSummary;
                return needPayFee || 0;
            },

            currentQuickItem() {
                return this.$abcPage.$store.selectedQuickItem;
            },

            currentPatient() {
                return this.currentQuickItem.patientInfo || {};
            },

            peOrderInfoList() {
                const {
                    orderNo,
                    reportGetWay,
                    address,
                    no,
                    businessTime,
                    businessType,
                    orderCreated,
                    type,
                    status,
                } = this.peOrderInfo;
                return [
                    {
                        label: '个检/团检',
                        value: PEOrderTypeLabel[type],
                    },
                    {
                        label: '体检类型',
                        value: PEBusinessTypeLabel[businessType],
                    },
                    {
                        label: '订单编号',
                        value: orderNo,
                    },
                    {
                        label: '取报告方式',
                        value: ReportGetWayLabel[reportGetWay],
                    },
                    {
                        label: '客户地址',
                        value: address2Str(address),
                    },
                    {
                        label: '体检编号',
                        value: no || '',
                    },
                    {
                        label: '体检时间',
                        value: formatDate(businessTime, 'YYYY-MM-DD'),
                    },
                    {
                        label: '体检状态',
                        value: OrderStatusLabelEnum[status],
                    },
                    {
                        label: '下单时间',
                        value: formatDate(orderCreated, 'YYYY-MM-DD'),
                    },
                ];
            },
            peGroupOrderInfoList() {
                const {
                    orderNo,
                    reportGetWay,
                    address,
                    no,
                    businessTime,
                    businessType,
                    orderCreated,
                    type,
                    status,
                } = this.peOrderInfo;
                return [
                    {
                        label: '个检/团检',
                        value: PEOrderTypeLabel[type],
                    },
                    {
                        label: '体检类型',
                        value: PEBusinessTypeLabel[businessType],
                    },
                    {
                        label: '订单编号',
                        value: orderNo,
                    },
                    {
                        label: '取报告方式',
                        value: ReportGetWayLabel[reportGetWay],
                    },
                    {
                        label: '客户地址',
                        value: address2Str(address),
                    },
                    {
                        label: '体检编号',
                        value: no || '',
                    },
                    {
                        label: '体检时间',
                        value: formatDate(businessTime, 'YYYY-MM-DD'),
                    },
                    {
                        label: '体检状态',
                        value: OrderStatusLabelEnum[status],
                    },
                    {
                        label: '下单时间',
                        value: formatDate(orderCreated, 'YYYY-MM-DD'),
                    },
                ];
            },
        },
        watch: {
            '$route.params.id': {
                handler(val) {
                    this.chargeSheetId = val;
                    this.fetchDetail(true);
                },
                immediate: true,
            },
        },
        created() {
            this.$store.dispatch('invoice/initInvoiceConfig');

            // 监听收费后自动开票的消息,如果开票成功,则重新拉取发票列表
            this.$abcEventBus.$on('refresh-pe-charge-invoice-list', () => {
                this.fetchInvoiceList();
            }, this);
        },
        methods: {
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'pe-charge-fee-list' }).generateDialogAsync({ parent: this });
            },

            printHandler() {
                PhysicalExaminationApi.printPEPersonalFeeList({
                    id: this.chargeSheetId,
                    clinicName: this.currentClinic?.name || '',
                });
            },

            async fetchDetail(refreshInvoice) {
                this.loading = true;
                const { data } = await PEChargeAPI.fetchChargeDetail(this.chargeSheetId);
                this.chargeSheetId = data.id;
                this.patientInfo = data.patientInfo;
                this.peOrderInfo = data.peSheet || {};
                this.chargeForms = data.chargeForms || [];
                this.invoiceStatusFlag = data.invoiceStatusFlag; //开票状态
                this.chargeSheetSummary = {
                    chargeStatus: data.status,
                    actualReceivableFee: data.actualReceivableFee, // 排除套餐中退项金额的应收
                    receivableFee: data.receivableFee, // 应收
                    needPayFee: data.needPayFee,
                    payTransactions: data.payTransactions,
                    receivedFee: data.receivedFee,
                    refundableFee: data.refundableFee,
                    refundedFee: data.refundedFee,
                    totalFee: data.totalFee, // 订单原价
                    transactions: data.transactions,
                    firstChargedTime: data.firstChargedTime,
                    lastChargedTime: data.lastChargedTime,
                    salesEmployeeId: data.salesEmployeeId,
                    salesEmployeeName: data.salesEmployeeName,
                    realReceivedFee: data.realReceivedFee,
                    lastChargedEmployeeName: data.lastChargedEmployeeName,
                };
                this.loading = false;

                this.$abcPage.$store.updatePatientInfo(data.id, {
                    status: data.status,
                    statusName: data.statusName || '',
                });

                if (refreshInvoice) {
                    this.$nextTick(() => {
                        if (this.isCharged) {
                            this.fetchInvoiceList();
                        }
                    });
                }
            },
            changePatientInfo(data) {
                this.$abcPage.$store.updatePatientInfo(data);
            },
            async handleCharge() {
                let memberId = '';
                if (this.currentPatient && this.currentPatient.id) {
                    const { data } = await CrmAPI.loadRelateMember(this.currentPatient.id);
                    memberId = data?.rows?.[0]?.patientId || '';
                }

                const payload = memberId ? {
                    id: memberId,
                } : null;

                this.payHandler(payload);
            },

            payHandler(patient) {
                this._chargeDialog = new AbcCommonChargeDialog({
                    dialogTitle: '收费',
                    hiddenPayModeList: [
                        PayModeEnum.SOCIAL_CARD,
                        PayModeEnum.ARREARS,
                        // PayModeEnum.MEMBER_CARD,
                        PayModeEnum.PATIENT_CARD,
                    ],
                    receivableFee: this.chargeSheetSummary.needPayFee,
                    onAbcPayOpenCallback: () => {
                        navigateToAggregatePaymentContentSetting(this.currentClinic);
                    },
                    submit: this.submitHandler,
                    onPartChargeSuccess: this.fetchDetail,
                    onChargeSuccess: this.fetchDetail,
                    onChargeError: this.fetchDetail,
                    onClose: this.onClose,
                    currentPatient: patient,
                });
                this._chargeDialog.generateDialog({ parent: this });
            },
            async submitHandler(chargeData) {
                return PEChargeAPI.payOrder(this.chargeSheetId, {
                    ...chargeData,
                });
            },
            onClose() {
                if (this._chargeDialog) {
                    this._chargeDialog.destroyDialog();
                    this._chargeDialog = null;
                }
            },

            handleClickRefund() {
                this.confirmRefund({
                    refundFee: Math.abs(this.chargeSheetSummary.needPayFee),
                });
            },

            confirmRefund(refundData) {
                this._refundDialog = new AbcCommonRefundDialog({
                    hiddenPayModeList: [
                        PayModeEnum.SOCIAL_CARD,
                        PayModeEnum.ARREARS,
                        PayModeEnum.MEMBER_CARD,
                        PayModeEnum.PATIENT_CARD,
                    ],
                    refundFee: refundData.refundFee,
                    payTransactions: this.chargeSheetSummary.payTransactions,
                    updatePayTransactions: async () => {
                        const { data } = await PEChargeAPI.fetchChargeDetail(this.chargeSheetId);
                        return data?.payTransactions || [];
                    },
                    submit: (data) => this.refundHandler(data, refundData),
                    onPartSuccess: this.fetchDetail,
                    onSuccess: this.onRefundSuccess,
                });
                this._refundDialog.generateDialog({ parent: this });
            },

            refundHandler(data, refundData) {
                if (this.isCharged) {
                    // 已结算状态走全退费流程
                    Object.assign(data, {
                        chargeFormItems: getPostItems(refundData.items),
                    });
                    return PEChargeAPI.refundOrderAfterSettled(this.chargeSheetId, data);
                }
                // 其他状态是退项再退费流程
                return PEChargeAPI.refundOrder(this.chargeSheetId, data);
            },

            onRefundSuccess(result) {
                const { requestId } = result;
                this.fetchDetail();
                if (this._refundDialog) {
                    this._refundDialog.destroyDialog();
                    this._refundDialog = null;
                }
                if (this._selectRefundDialog) {
                    this._selectRefundDialog.destroyDialog();
                    this._selectRefundDialog = null;
                }
                autoDestroyInvoice(requestId, InvoiceBusinessScene.PE_CHARGE);
            },

            handleClickAllRefund() {
                const list = this.chargeForms.reduce((arr, item) => {
                    return arr.concat(item.items || []);
                }, []);
                this._selectRefundDialog = new PERefundSelectDialog({
                    chargeSheetId: this.chargeSheetId,
                    list,
                    onConfirm: this.confirmRefund,
                });
                this._selectRefundDialog.generateDialog();
            },

            handleClose() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确定关闭收费单？',
                    onConfirm: async () => {
                        await PEChargeAPI.closeChargeSheet(this.chargeSheetId);
                        this.$Toast({
                            message: '关闭成功',
                            type: 'success',
                        });
                        this.fetchDetail();
                    },
                });
            },

            handleClickRecharge() {
                this.$confirm({
                    type: 'info',
                    title: '提示',
                    content: '该收费单已关闭。是否确定打开该收费单并重新收费？',
                    onConfirm: async () => {
                        await PEChargeAPI.recharge(this.chargeSheetId);
                        this.fetchDetail();
                    },
                });
            },

            async openInvoiceDialog() {
                await new InvoiceDialog({
                    chargeSheetId: this.chargeSheetId,
                    chargeStatus: this.chargeSheetSummary.chargeStatus,
                    patientInfo: {
                        patientId: this.currentPatient.id,
                        buyerPhone: this.currentPatient.mobile,
                        buyerName: this.currentPatient.name,
                        idCard: this.currentPatient.idCard,
                        disabledBuyerName: true,
                    },
                    printMedicalListConfig: this.printMedicalListConfig,
                    medicalElectronicAPIConfig: this.medicalElectronicAPIConfig,
                    writeInvoiceConfig: this.writeInvoiceConfig,
                    isOpenMedicalInvoice: this.isOpenMedicalInvoice,
                    printBillConfig: this.printBillConfig,
                    isOpenInvoice: this.isOpenInvoice,
                    invoiceConfigList: this.invoiceConfigList,
                    userInfo: this.userInfo,
                    businessType: InvoiceBusinessScene.PE_CHARGE,
                    toBillPrintSetting: () => {
                        navigateToInvoiceConfig(this.currentClinic);
                    },
                    updateInvoice: () => {
                        // 发票更新,刷新QL列表
                        this.$abcEventBus.$emit('open-invoice-pe-charge-quick-list');
                        // 发票更新,重新拉取发票列表
                        this.fetchInvoiceList();
                    },
                }).generateDialog({ parent: this });
            },
            /**
             * 拉取发票列表
             */
            async fetchInvoiceList() {
                if (this.isCharged && !this.isClosed) {
                    try {
                        const invoiceService = new InvoiceService();
                        const invoiceListResp = await invoiceService.fetchCashierSideBarInvoiceList(this.chargeSheetId,
                                                                                                    InvoiceBusinessScene.PE_CHARGE,
                                                                                                    InvoiceCategory.ELECTRONIC,
                                                                                                    0);
                        this.invoiceList = invoiceListResp.rows || [];
                    } catch (e) {
                        this.invoiceList = [];
                        console.warn('获取发票列表失败\n', e);
                    }
                } else {
                    this.invoiceList = [];
                }
            },
        },
    };
</script>


