<template>
    <div class="p-e-item-operator-wrapper">
        <div ref="reference">
            <abc-delete-icon v-if="supportDelete" @delete="$emit('delete')"></abc-delete-icon>
            <template v-else>
                <abc-button
                    v-if="item.status === PEItemStatusEnum.REFUND || item.status === PEItemStatusEnum.ABANDON"
                    type="text"
                    size="small"
                    :style="{
                        color: themeStyle.R2
                    }"
                    @click="handleClick"
                >
                    {{ PEItemStatusLabel[item.status] }}
                </abc-button>
                <abc-button
                    v-else-if="item.status === PEItemStatusEnum.DELAY"
                    type="text"
                    size="small"
                    :style="{
                        color: themeStyle.Y2
                    }"
                    @click="handleClick"
                >
                    {{ PEItemStatusLabel[item.status] }}
                </abc-button>
                <abc-button
                    v-else
                    type="ghost"
                    size="small"
                    icon="three_dot"
                    @click="handleClick"
                ></abc-button>
            </template>
        </div>

        <div
            v-show="showPopper"
            ref="options"
            v-abc-click-outside="handleClickOutside"
            class="p-e-item-operator-popover"
        >
            <template v-if="showPopper">
                <div class="op-item">
                    <span class="label">操作</span>
                    <abc-radio-group v-model="postData.action" :disabled="!canOperate">
                        <abc-radio
                            v-for="it in radioOption"
                            :key="it.value"
                            :label="it.label"
                            :disabled="it.disabled"
                            enable-cancel
                        >
                            {{ it.text }}
                        </abc-radio>
                    </abc-radio-group>
                </div>
                <div v-if="postData.action === PEItemAction.DELAY" class="op-item">
                    <span class="label">延期时间</span>
                    <abc-date-picker
                        ref="datePicker"
                        v-model="postData.scheduleTime"
                        value-format="YYYY-MM-DD"
                        :picker-options="pickerOptions"
                        :width="224"
                        :disabled="!canOperate"
                    >
                    </abc-date-picker>
                </div>
                <div v-if="postData.action === PEItemAction.ABANDON" class="op-item">
                    <span class="label">备注原因</span>
                    <abc-input
                        v-model="postData.remark"
                        :width="224"
                        max-length="512"
                        placeholder="输入弃项原因"
                        :disabled="!canOperate"
                    ></abc-input>
                </div>
                <div v-if="postData.action === PEItemAction.REFUND" class="op-item">
                    <span class="label">备注原因</span>
                    <abc-input
                        v-model="postData.remark"
                        :width="224"
                        max-length="512"
                        placeholder="输入退项原因"
                        :disabled="!canOperate"
                    ></abc-input>
                </div>
                <div class="opt-footer">
                    <div class="tips">
                        {{ warnTips }}
                    </div>
                    <div class="btn-group">
                        <abc-button
                            ref="confirmBtn"
                            size="small"
                            :disabled="!canOperate"
                            @click="handleConfirm"
                        >
                            确定
                        </abc-button>
                        <abc-button
                            type="blank"
                            size="small"
                            @click="handleClose"
                        >
                            取消
                        </abc-button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
    import {
        OrderStatusEnum, PEBusinessTypeEnum, PEChargeItemStatusEnum,
        PEItemAction, PEItemStatusEnum, PEItemStatusLabel, PEOrderTypeEnum,
    } from 'views/physical-examination/constants.js';
    import PhysicalExaminationAPI from 'api/physical-examination/pe-order.js';
    import Popper from 'utils/vue-popper.js';
    import themeStyle from '@/styles/theme.module.scss';
    import { mapGetters } from 'vuex';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';

    export default {
        name: 'PEItemOperator',
        mixins: [ Popper ],
        props: {
            item: {
                type: Object,
                required: true,
            },

            orderStatus: {
                type: Number,
            },
            orderType: {
                type: Number,
                required: true,
            },
            orderBusinessType: {
                type: Number,
                required: true,
            },
            placement: {
                type: String,
                default: 'bottom-end',
            },

            // 是否支持删除
            canDelete: {
                type: Boolean,
                default: false,
            },

            // 是否需要实时提交
            needSubmit: {
                type: Boolean,
                default: false,
            },

            // needSubmit 为 true 时必传
            orderId: {
                type: String,
            },
        },
        data() {
            return {
                themeStyle,
                PEItemAction,
                PEItemStatusLabel,
                PEItemStatusEnum,
                btnLoading: false,
                pickerOptions: {
                    disabledDate: (date) => {
                        return date <= new Date();
                    },
                },
                postData: {
                    id: this.item.id,
                    action: undefined,
                    scheduleTime: this.item.scheduleTime,
                    remark: this.item.remark,
                },
            };
        },
        computed: {
            ...mapGetters({
                // 使用对象形式映射并重命名
                canRevokeExamOrder: 'examination/canRevokeMedicalOrder',
                canRevokeInspectOrder: 'inspect/canRevokeMedicalOrder',
            }),

            isExamItem() {
                return this.item.goodsSubType === GoodsSubTypeEnum[GoodsTypeEnum.PHYSICAL_EXAMINATION].EXAMINATION;
            },

            isInspectItem() {
                return this.item.goodsSubType === GoodsSubTypeEnum[GoodsTypeEnum.PHYSICAL_EXAMINATION].INSPECT;
            },

            warnTips() {
                if (this.postData.action === PEItemAction.DELAY) {
                    return '提醒：延期后状态保持为体检中状态';
                }
                if (this.postData.action === PEItemAction.ABANDON) {
                    return '提醒：弃检项目不会退款';
                }
                if (this.postData.action === PEItemAction.REFUND) {
                    return '提醒：退项项目会退款';
                }
                return '';
            },
            radioOption() {
                const _arr = [
                    {
                        text: '延期',
                        label: PEItemAction.DELAY,
                        disabled: !this.supportDelay,
                    },
                    {
                        text: '弃检',
                        label: PEItemAction.ABANDON,
                        disabled: !this.supportAbandon,
                    },

                ];

                // 公卫体检不允许退项
                if (this.orderBusinessType === PEBusinessTypeEnum.PUBLIC_HEALTH) return _arr;

                if (this.orderType === PEOrderTypeEnum.INDIVIDUAL) {
                    _arr.push({
                        text: '退项',
                        label: PEItemAction.REFUND,
                        disabled: !this.supportRefund,
                    });
                }
                return _arr;
            },

            /**
             * @desc 支持删除
             * <AUTHOR>
             * @date 2023-12-04 11:30:12
             */
            supportDelete() {
                if (!this.canDelete) return false;

                if (this.orderBusinessType === PEBusinessTypeEnum.PUBLIC_HEALTH) return false;

                if (this.orderType === PEOrderTypeEnum.INDIVIDUAL) {
                    // 没有id代表开单
                    if (!this.item.id || this.item.isNew) return true;
                    // 未支付 && （待预约 || 待登记）
                    return this.item.chargeStatus === PEChargeItemStatusEnum.UN_CHARGE &&
                        [
                            OrderStatusEnum.WAIT_RESERVE,
                            OrderStatusEnum.WAIT_REGISTER,
                        ].indexOf(this.orderStatus) > -1 ;
                }
                return false;
            },

            /**
             * @desc 支持延期
             * <AUTHOR>
             * @date 2023-12-04 11:26:06
             */
            supportDelay() {
                if (this.orderStatus === OrderStatusEnum.WAIT_REGISTER) {

                    if (this.orderType === PEOrderTypeEnum.GROUP) {
                        // 待登记，团检都支持延期
                        return true;
                    }
                    if (this.orderType === PEOrderTypeEnum.INDIVIDUAL) {
                        // 待登记 && 部分支付、已支付 支持延期
                        return this.item.chargeStatus !== PEChargeItemStatusEnum.UN_CHARGE;
                    }

                }
                if (this.orderStatus === OrderStatusEnum.PE_ING) {
                    // 体检中，个检、团检 项目未检都可以延期
                    return [
                        PEItemStatusEnum.EXAM_FINISHED,
                        PEItemStatusEnum.EXAM_FINISHED_REPORT,
                    ].indexOf(this.item.status) === -1;
                }

                return false;
            },

            /**
             * @desc 支持弃检
             * <AUTHOR>
             * @date 2023-12-04 11:26:55
             */
            supportAbandon() {
                if (this.orderType === PEOrderTypeEnum.GROUP) {
                    if (this.orderStatus === OrderStatusEnum.PE_ING) {
                        // 体检中 项目未检 都支持弃检
                        return [
                            PEItemStatusEnum.EXAM_FINISHED,
                            PEItemStatusEnum.EXAM_FINISHED_REPORT,
                        ].indexOf(this.item.status) === -1;
                    }
                    // 团检 待预约/待登记 支持弃检
                    return [
                        OrderStatusEnum.WAIT_RESERVE,
                        OrderStatusEnum.WAIT_REGISTER,
                    ].indexOf(this.orderStatus) > -1;
                }

                // 待预约
                // 待登记
                // 部分支付、已支付 支持弃检
                if (
                    [
                        OrderStatusEnum.WAIT_RESERVE,
                        OrderStatusEnum.WAIT_REGISTER,
                    ].indexOf(this.orderStatus) > -1
                ) {
                    return this.item.chargeStatus !== PEChargeItemStatusEnum.UN_CHARGE;
                }

                // 新增配置：已执行也可以弃检，退项
                if (this.canRevokeInspectOrder && this.isInspectItem) {
                    return true;
                }

                if (this.canRevokeExamOrder && this.isExamItem) {
                    return true;
                }

                if (this.orderStatus === OrderStatusEnum.PE_ING) {
                    // 体检中 项目未检 都支持弃检
                    return [
                        PEItemStatusEnum.EXAM_FINISHED,
                        PEItemStatusEnum.EXAM_FINISHED_REPORT,
                    ].indexOf(this.item.status) === -1;
                }


                return false;
            },


            /**
             * @desc 支持退项
             * <AUTHOR>
             * @date 2023-12-04 11:27:51
             */
            supportRefund() {
                if (this.orderType === PEOrderTypeEnum.GROUP) return false;
                return this.supportAbandon;
            },

            canOperate() {
                return this.supportDelete ||
                    this.supportDelay ||
                    this.supportAbandon ||
                    this.supportRefund;
            },
        },
        created() {
            this.initAction();
        },
        mounted() {
            this.referenceElm = this.$refs.reference;
            this.$parent.popperElm = this.popperElm = this.$refs.options;
        },
        beforeDestroy() {
            this.destroyPopper();
        },
        methods: {
            handleClickOutside() {
                if (this.$refs.datePicker && this.$refs.datePicker.showPopper) return;
                this.handleClose();
            },
            initAction() {
                if (this.item.status === PEItemStatusEnum.REFUND) {
                    this.postData.action = PEItemAction.REFUND;
                } else if (this.item.status === PEItemStatusEnum.DELAY) {
                    this.postData.action = PEItemAction.DELAY;
                } else if (this.item.status === PEItemStatusEnum.ABANDON) {
                    this.postData.action = PEItemAction.ABANDON;
                }
            },
            async handleClick() {
                this.showPopper = !this.showPopper;
            },
            handleClose() {
                this.showPopper = false;
            },
            handleConfirm() {
                const {
                    action,
                    scheduleTime,
                } = this.postData;
                if (action === PEItemAction.DELAY && !scheduleTime) {
                    this.$Toast({
                        message: '请选择延期日期',
                        type: 'error',
                        duration: 2000,
                        referenceEl: this.$refs.confirmBtn.$el,
                    });
                    return;
                }
                this.updatePEOrderItemStatus();
            },
            async updatePEOrderItemStatus() {
                try {
                    if (this.btnLoading) return;
                    this.btnLoading = true;

                    const { action } = this.postData;

                    if (this.needSubmit) {
                        if (this.postData.action === PEItemAction.DELAY) {
                            this.postData.remark = '';
                        } else {
                            this.postData.scheduleTime = '';
                        }

                        const { data } = await PhysicalExaminationAPI.updatePEOrderItemStatus(this.orderId, this.postData);
                        this.$emit('update-order', data);
                    }

                    let status = undefined;
                    if (action === PEItemAction.REFUND) {
                        status = PEItemStatusEnum.REFUND;
                    } else if (action === PEItemAction.ABANDON) {
                        status = PEItemStatusEnum.ABANDON;
                    } else if (action === PEItemAction.DELAY) {
                        status = PEItemStatusEnum.DELAY;
                    }
                    this.$emit('update-item', {
                        scheduleTime: this.postData.scheduleTime,
                        remark: this.postData.remark,
                        status: status ?? this.item.status,
                    });
                    this.handleClose();
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: '操作失败',
                    });
                } finally {
                    this.btnLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .p-e-item-operator-popover {
        z-index: 1992;
        width: 338px;
        padding: 16px;
        font-size: 14px;
        background: #ffffff;
        border: 1px solid $abcCardBorderColor;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

        > div {
            color: $T1;
        }

        .abc-radio-group {
            padding-left: 0;
        }

        .abc-radio-group,
        .abc-radio {
            height: 28px;
            line-height: 28px;
        }

        .opt-footer {
            display: flex;
            align-items: center;
            margin-top: 12px;

            .tips {
                font-size: 12px;
                color: $R2;
            }

            .btn-group {
                margin-left: auto;
            }
        }

        .op-item {
            display: flex;
            align-items: center;

            & + .op-item {
                margin-top: 10px;
            }

            .label {
                width: 80px;
                max-width: 80px;
                line-height: 28px;
                color: $T2;
            }
        }
    }
</style>

