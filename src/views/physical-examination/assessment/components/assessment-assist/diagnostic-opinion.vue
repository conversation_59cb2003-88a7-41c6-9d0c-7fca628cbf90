<template>
    <div class="diagnostic-opinion-wrapper">
        <div class="inspect-split-line"></div>
        <h3>诊断意见：<span>{{ subTitle }}</span></h3>
        <template v-if="results.length > 0">
            <div
                v-for="(item,index) in results"
                :key="item.keyId"
                :class="['diagnostic-opinion-item',{
                    'diagnostic-opinion-item-mouseDown': item.isActive,'opinion-highlight': item.isHighLight
                }]"
                @mousedown="toActive(item)"
                @mouseup="item.isActive = false"
            >
                <div><span style="margin-right: 8px;">{{ index + 1 }}. {{ item.name }}</span><tip-display :tag="item.tag" :tag-text="item.tagText"></tip-display></div>
                <template v-if="item.isRelation">
                    <span style="flex-shrink: 0; color: #1ec761;">
                        <abc-icon
                            icon="chenggong"
                            size="12"
                            class="iconfont"
                            color="#1EC761"
                        ></abc-icon>
                        已总评
                    </span>
                </template>
                <template v-else>
                    <span
                        v-if="isEdit"
                        style="flex-shrink: 0; color: #005ed9;"
                        @click="addSummary(item)"
                    >
                        <abc-icon
                            icon="Edit_Profile"
                            size="12"
                            class="iconfont"
                            color="#005ED9"
                        ></abc-icon>
                        添加总评
                    </span>
                </template>
            </div>

            <add-summary-dialog
                v-if="isShowAddSummaryDialog"
                v-model="isShowAddSummaryDialog"
                @onSelectSummary="setSelectSummary"
            >
            </add-summary-dialog>
        </template>

        <template v-else>
            <div class="empty-opinion">
                暂无
            </div>
        </template>
    </div>
</template>

<script>
    import Clone from 'utils/clone';
    import { createGUID } from '@/utils';
    import TipDisplay
        from 'views/settings/physical-examination/assessment-set/assessment-opinion/components/tip-display.vue';

    export default {
        name: 'DiagnosticOpinion',
        components: {
            TipDisplay,
            AddSummaryDialog: () => import('views/physical-examination/assessment/components/assessment-summary/add-summary-dialog.vue'),
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            subTitle: {
                type: String,
                default: '',
            },
            results: {
                type: Array,
                default: () => [],
            },
            examSheetId: {
                type: [String,Number],
                default: '',
            },
        },
        data() {
            return {
                isMouseDown: false,
                isDetail: false,
                isShowAddSummaryDialog: false,
                currentItem: {},
            };
        },
        computed: {
            isEdit() {
                return this.$abcPage.$store.isEdit;
            },
            summaryAdvicesList: {
                get() {
                    return this.$abcPage.$store.summaryAdvicesList;
                },
                set(newVale) {
                    this.$abcPage.$store.setSummaryAdvicesList(newVale);
                },
            },
        },
        mounted() {
            this.$abcEventBus.$on('relate-opinion', (val) => {
                this.findRelatedOpinion(val,'relate');
            }, this);
            this.$abcEventBus.$on('un-relate-opinion', (val) => {
                this.findRelatedOpinion(val,'unRelate');
            }, this);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            addSummary(item) {
                this.isShowAddSummaryDialog = true;
                this.currentItem = item;
            },
            setSelectSummary(item) {
                const _item = Clone(item);
                this.summaryAdvicesList.push({
                    diagnosisAdviceId: this.currentItem.id,
                    examSheetId: this.examSheetId,
                    keyId: createGUID()
                    ,..._item,
                });
                this.$set(this.currentItem,'isRelation',true);
                this.$Toast({
                    message: '添加总评成功',
                    type: 'success',
                });
                this.$abcPage.$store.setIsChanged(true);
            },
            toActive(item) {
                this.$set(item, 'isActive', true);
                window._vue.$abcEventBus.$emit('relate-summary',item.id);
            },
            findRelatedOpinion(val,type) {
                const arr = this.results;
                const matchedItem = arr.find((item) => (item.id === val));

                //关联 高亮该诊断意见 且更新检查列表位置
                if (type === 'relate') {
                    arr.forEach((item) => {
                        if (item.isHighLight) {
                            this.$set(item,'isHighLight',false);
                        }
                    });
                    if (matchedItem) {
                        this.$nextTick(() => {
                            window._vue.$abcEventBus.$emit('refresh-inspect-result-list',this.examSheetId);
                            this.timer = setTimeout(() => {
                                this.$set(matchedItem,'isHighLight',true);
                            },0);
                            this.$on('hook:beforeDestroy', () => {
                                clearTimeout(this.timer);
                            });
                        });
                    }
                }
                //解除该诊断意见关联状态
                if (type === 'unRelate' && matchedItem?.isRelation) {
                    matchedItem.isRelation = false;
                }
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/theme.scss";

.inspect-split-line {
    width: 100%;
    padding: 0 16px;
    border-bottom: 1px dashed $P6;
}

.diagnostic-opinion-wrapper {
    h3 {
        padding: 0 6px 0 16px;
        margin: 8px 0;
        font-weight: bold;

        >span {
            color: $R2;
        }
    }

    .diagnostic-opinion-item {
        display: flex;
        gap: 4px;
        justify-content: space-between;
        min-height: 32px;
        padding: 4px 6px 4px 16px;
        cursor: pointer;

        &:hover {
            background: #ecf1f4;
        }
    }

    .diagnostic-opinion-item-mouseDown {
        background: #e2e7eb;

        &:hover {
            background: #e2e7eb;
        }
    }

    @keyframes OpinionHighlight {
        0% { background: #fffcea; }
        25% { background: #ffebd6; }
        50% { background: #fffcea; }
        75% { background: #ffebd6; }
        100% { background: #fffcea; }
    }

    .opinion-highlight {
        animation: OpinionHighlight 3s;
    }

    .empty-opinion {
        padding: 0 16px;
        color: $T3;
    }
}
</style>
