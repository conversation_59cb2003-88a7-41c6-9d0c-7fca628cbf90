<template>
    <abc-dialog
        v-if="isShowDialog"
        v-model="isShowDialog"
        append-to-body
        :auto-focus="false"
        custom-class="summary-select-dialog"
        :content-styles="`width:995px;height: 653px;padding:0`"
        header-size="large"
        title="搜索项目"
    >
        <template #title>
            <abc-input
                v-model="searchKeyWord"
                placeholder="输入总评建议名称或拼音"
                :width="428"
                :icon="searchKeyWord ? 'cis-icon-cross_small' : ''"
                @icon-click="handleClear"
                @input="inputHandler"
            >
                <template #prepend>
                    <abc-search-icon></abc-search-icon>
                </template>
            </abc-input>
        </template>

        <div class="summary-select-dialog-body">
            <div class="summary-select-dialog-body-left">
                <ul>
                    <li
                        v-for="item in categoryList"
                        :key="item.label + item.value"
                        :class="{ 'active': isActive(item) }"
                        @click="changeType(item.value)"
                    >
                        {{ item.label }}
                    </li>
                </ul>
                <ul>
                    <li
                        v-for="item in subCategoryList"
                        :key="item.title + item.id"
                        slot="reference"
                        :class="{ 'active': currentSubKey === item.id }"
                        @click="changeSubType(item)"
                    >
                        <abc-popover
                            width="348px"
                            placement="right"
                            trigger="hover"
                            theme="yellow"
                        >
                            <div slot="reference">
                                {{ item.title }}
                            </div>
                            <div>{{ item.title }}</div>
                        </abc-popover>
                    </li>
                </ul>
            </div>
            <div class="summary-select-dialog-body-right">
                <abc-form v-if="summaryData.commonReason">
                    <abc-form-item label="常见原因">
                        <abc-edit-div v-model="summaryData.commonReason" placeholder="输入常见原因" class="content-edit"></abc-edit-div>
                    </abc-form-item>
                    <abc-form-item label="医学解释">
                        <abc-edit-div v-model="summaryData.medicalExplain" placeholder="输入医学解释" class="content-edit"></abc-edit-div>
                    </abc-form-item>
                    <abc-form-item label="医生建议">
                        <abc-edit-div v-model="summaryData.doctorAdvice" placeholder="输入医生建议" class="content-edit"></abc-edit-div>
                    </abc-form-item>
                </abc-form>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleSaveSelectedSummaryItem">
                选择
            </abc-button>
            <abc-button type="blank" @click="isShowDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import {
        clinicalInspect, inspectTypeOptions,
    } from 'views/settings/inspect/diagnosis-advice/constant';
    import PhysicalExaminationAPI from 'api/physical-examination/pe-order';
    import { debounce } from 'utils/lodash';

    export default {
        name: 'AddSummaryDialog',
        components: { },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
        },
        data() {
            return {
                searchKeyWord: '',
                categoryList: [...clinicalInspect,...inspectTypeOptions,{
                    label: '检验',
                    value: '50901',
                }],
                subCategoryList: [],
                currentCategoryKey: 14, //一般检查
                currentSubKey: '',
                summaryData: {},
            };
        },
        computed: {
            isShowDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        created() {
            this._debounceSearch = debounce(
                () => {
                    this.fetchData();
                },
                250,
                true,
            );
            this.initData();
        },
        methods: {
            async initData() {
                await this.fetchData();
            },
            async fetchData() {
                try {
                    const fetchParams = {
                        keyword: this.searchKeyWord,
                        offset: 0,
                        deviceType: this.currentCategoryKey,
                    };
                    if (this.currentCategoryKey === '50901') {
                        fetchParams.deviceType = null;
                        fetchParams.type = 1;
                    } else {
                        fetchParams.deviceType = this.currentCategoryKey;
                        fetchParams.type = null;
                    }
                    const { data } = await PhysicalExaminationAPI.getReportAdviceTemplates(fetchParams);
                    this.subCategoryList = data.rows?.map((item) => {
                        return {
                            title: item.title,
                            id: item.id,
                            commonReason: item.commonReason,
                            medicalExplain: item.medicalExplain,
                            doctorAdvice: item.doctorAdvice,
                        };
                    }) || [];
                    this.currentSubKey = this.subCategoryList?.length && this.subCategoryList[0]?.id;
                    this.summaryData = this.subCategoryList?.length && this.subCategoryList[0];
                } catch (e) {
                    console.log(e);
                }
            },
            handleSaveSelectedSummaryItem() {
                try {
                    this.$emit('onSelectSummary',this.summaryData);
                    this.isShowDialog = false;
                } catch (e) {
                    console.error(e);
                }
            },
            inputHandler() {
                this._debounceSearch();
            },
            handleClear() {
                this.searchKeyWord = '';
            },
            isActive(item) {
                const { value } = item;
                return this.currentCategoryKey === value;
            },
            changeType(value) {
                this.currentCategoryKey = value;
                this.fetchData();
            },
            changeSubType(item) {
                this.currentSubKey = item.id;
                this.summaryData = item;
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

.summary-select-dialog {
    .abc-dialog-header {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 56px;
        background-color: $P4 !important;

        .abc-dialog-headerbtn {
            background-color: $P4;
        }

        .abc-input-wrapper {
            .prepend-input {
                z-index: 3;
            }

            >input {
                background: white;
            }
        }
    }

    &-body {
        display: flex;
        width: 100%;
        height: 100%;

        &-left {
            display: flex;
            width: 320px;
            height: 100%;
            background: #f9fafc;
            box-shadow: inset -1px 0 0 0 $P6;

            > ul {
                height: 100%;
                padding-top: 9px;
                overflow-y: auto;

                @include scrollBar(false, 2px);

                &:first-child {
                    width: 120px;
                    border-right: 1px solid $P6;
                }

                &:nth-child(2) {
                    width: 200px;
                }

                > li {
                    height: 32px;
                    padding: 0 16px;
                    font-size: 14px;
                    line-height: 32px;
                    color: $S1;

                    @include ellipsis(1);

                    &:hover {
                        cursor: pointer;
                        background-color: $P4;
                    }

                    &.active {
                        color: $theme1;
                        background-color: #d4e7fd;
                    }
                }
            }
        }

        &-right {
            flex: 1;
            width: calc(100% - 320px);
            height: 100%;
            padding: 20px 24px;

            .abc-form {
                display: flex;
                flex-direction: column;

                .abc-form-item {
                    margin-bottom: 30px;

                    &-label {
                        .label-name {
                            font-weight: bold;
                            color: black;
                        }
                    }

                    .content-edit {
                        height: auto;
                        color: #7a8794;
                    }
                }
            }
        }
    }
}
</style>

