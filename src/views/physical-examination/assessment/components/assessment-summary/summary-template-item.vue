<template>
    <div class="summary-item" :class="[{ 'is-disabled': !isEdit },{ 'summary-highlight': dataSource.isActive }]">
        <div class="title">
            <h3>
                <abc-icon
                    icon="move_item"
                    color="#7a8794"
                    size="14px"
                    class="can-move-item"
                ></abc-icon>
                <abc-input
                    :value="dataSource.title"
                    :disabled="!isEdit"
                    placeholder="输入建议标题"
                    :input-custom-style="{
                        borderColor: 'transparent', borderRadius: '0',fontWeight: 500
                    }"
                    @focus="inputFocus"
                    @change="(val)=>inputChange(val,'title')"
                ></abc-input>
            </h3>

            <abc-icon
                icon="delete1"
                size="13"
                color="#aab4bf"
                style="cursor: pointer;"
                @click="handleRemoveItem"
            ></abc-icon>
        </div>
        <div class="content">
            <div>
                <label class="can-move-item">常见原因</label>
                <abc-form-item class="edit-wrapper">
                    <abc-edit-div
                        :value="dataSource.commonReason"
                        :disabled="!isEdit"
                        :fixed="true"
                        placeholder="输入常见原因"
                        class="content-edit-div"
                        @focus="inputFocus"
                        @input="(val)=>inputChange(val,'commonReason')"
                    ></abc-edit-div>
                </abc-form-item>
            </div>
            <div>
                <label class="can-move-item">医学解释</label>
                <abc-form-item class="edit-wrapper">
                    <abc-edit-div
                        :value="dataSource.medicalExplain"
                        :disabled="!isEdit"
                        :fixed="true"
                        placeholder="输入医学解释"
                        class="content-edit-div"
                        @focus="inputFocus"
                        @input="(val)=>inputChange(val,'medicalExplain')"
                    ></abc-edit-div>
                </abc-form-item>
            </div>
            <div>
                <label class="can-move-item">医生建议</label>
                <abc-form-item class="edit-wrapper">
                    <abc-edit-div
                        :value="dataSource.doctorAdvice"
                        :disabled="!isEdit"
                        :fixed="true"
                        placeholder="输入医生建议"
                        class="content-edit-div"
                        @focus="inputFocus"
                        @input="(val)=>inputChange(val,'doctorAdvice')"
                    ></abc-edit-div>
                </abc-form-item>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'SummaryTemplateItem',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            item: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                dataSource: {},
            };
        },
        computed: {
            isEdit() {
                return this.$abcPage.$store.isEdit;
            },
        },
        mounted() {
            this.dataSource = this.item;
        },
        methods: {
            handleRemoveItem() {
                if (this.isEdit) {
                    try {
                        this.$emit('onDeleteItem',this.item.keyId,this.item.diagnosisAdviceId);
                    } catch (e) {
                        console.error(e);
                    }
                }
            },
            inputFocus() {
                window._vue.$abcEventBus.$emit('relate-opinion',this.item.diagnosisAdviceId);
            },
            inputChange(val,type) {
                if (val === this.dataSource[type]) return;
                this.dataSource[type] = val;
                this.$abcPage.$store.setIsChanged(true);
            },
        },
    };
</script>

<style scoped lang="scss">
@import 'styles/theme';

.summary-item {
    margin-bottom: 8px;
    background-color: #ffffff;
    border-radius: var(--abc-border-radius-small);

    .title {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        padding: 0 12px;
        font-size: 14px;
        font-weight: bold;
        background-color: $S2;
        border: 1px solid $P6;
        border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;

        h3 {
            flex: 1;

            .abc-input-wrapper {
                width: calc(100% - 22px);
            }
        }
    }

    .content {
        border: 1px solid $P6;
        border-top: none;
        border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

        >div {
            display: flex;
            align-items: center;
            border-top: 1px solid $P6;

            &:nth-child(1) {
                border-top: none;
            }

            label {
                width: 105px;
                line-height: 36px;
                color: $T2;
                text-indent: 12px;
            }

            .edit-wrapper {
                flex: 1;
                margin: 0;
                border-left: 1px solid $P6;
                -webkit-box-flex: 1;

                .content-edit-div {
                    height: auto;
                    min-height: 36px;
                    color: $T1;
                    border-color: transparent;
                    border-radius: 0;
                }

                ::v-deep .abc-form-item-content {
                    padding: 0.5px;

                    .abc-input__inner {
                        line-height: 24px;
                    }
                }
            }
        }
    }

    &:nth-child(1) {
        .title {
            border-top: none;
            border-radius: 0;
        }
    }
}

@keyframes SummaryHighlight {
    0% { background: #fffcea; }
    25% { background: #ffebd6; }
    50% { background: #fffcea; }
    75% { background: #ffebd6; }
    100% { background: #fffcea; }
}

.summary-highlight {
    animation: SummaryHighlight 3s;

    .title {
        background: inherit;

        ::v-deep .abc-input-wrapper {
            animation: SummaryHighlight 3s;

            .abc-input__inner {
                background: unset !important;
            }
        }
    }

    .content {
        ::v-deep .edit-wrapper {
            animation: SummaryHighlight 3s;

            .content-edit-div {
                background: unset !important;
            }
        }
    }
}

.is-disabled {
    background: $abcBgDisabled;

    .title {
        background: inherit;
    }
}

.can-move-item {
    cursor: grab;
}
</style>
