<template>
    <div class="app-header" :class="envClass">
        <div class="header-wrapper">
            <div class="header__left-wrapper">
                <component :is="viewComponents.AppLogo" v-if="showLogo"></component>
                <template v-if="visibleClinicSelector">
                    <app-clinic-dropdown :show-organization-icon="showOrganizationIcon"></app-clinic-dropdown>
                </template>
                <app-ward-area-selector v-else-if="visibleWardSelector">
                </app-ward-area-selector>
                <app-department-selector v-else-if="visibleDepartmentSelector">
                    科室切换
                </app-department-selector>
                <app-stock-room-selector v-else-if="visibleStockRoomSelector">
                    库房切换
                </app-stock-room-selector>
            </div>

            <app-nav-menu
                v-if="visibleNavMenu"
                :is-enable-social-menu-item="isEnableSocialMenuItem"
                :is-enable-mall-menu-item="isEnableMallMenuItem"
                :is-center-nav-menu="isCenterNavMenu"
            ></app-nav-menu>

            <app-drag-area v-if="isEnableDrag" style="flex: 1;"></app-drag-area>

            <div
                class="header-fixed-right"
                :style="supportCloudExaminationUpgrade ? { maxWidth: '540px' } : {}"
                :class="{ 'menu-nav-is-center': menNavIsCenter }"
            >
                <!--云检版本升级提示-->
                <cloud-examination-version-upgrade v-if="supportCloudExaminationUpgrade"></cloud-examination-version-upgrade>

                <app-upgrade-btn v-if="visibleUpgradeBtn"></app-upgrade-btn>

                <!-- 客服入口 -->
                <client-service-popper v-if="showServiceEntry" :todo-theme="todoTheme"></client-service-popper>

                <!--远程码-->
                <app-remote-code v-if="visibleRemoteCode"></app-remote-code>

                <component :is="viewComponents.AppFullscreenBtn" v-if="visibleFullScreenBtn"></component>

                <app-zoom-btn v-if="visibleZoomBtn"></app-zoom-btn>

                <wechat-alert
                    v-if="isOpenMp && hasOnlineCommunicationModule && !isExpired && !isIntranetUser"
                ></wechat-alert>

                <theme-selector v-if="featureTheme"></theme-selector>

                <app-notice placement="bottom-end" :popper-offset="66" :z-index="zIndex"></app-notice>

                <user-setting></user-setting>

                <app-window-manager v-if="visibleWindowManager" style="margin-right: 8px;"></app-window-manager>
            </div>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import AppClinicDropdown from '../app-clinic-dropdown.vue';
    import AppWardAreaSelector from '../app-ward-area-selector.vue';
    import AppDepartmentSelector from '../app-department-selector.vue';
    import AppStockRoomSelector from 'views/layout/app-stock-room-selector.vue';
    import AppRemoteCode from 'views/layout/app-remote-code/app-remote-code.vue';
    import AppWindowManager from 'views/layout/app-window-manager/app-window-manager.vue';
    import UserSetting from '../user-setting/user-setting.vue';

    import ModulePermission from 'views/permission/module-permission.js';
    import WechatAlert from '../wechat-alert.vue';
    import ThemeSelector from '../theme-selector/theme-selector.vue';
    import AppNotice from '../app-notice/app-notice.vue';
    import ClientServicePopper from '../client-service-popper/client-service-popper.vue';
    import AppNavMenu from '../app-nav-menu.vue';
    import AppZoomBtn from '../app-zoom-btn/app-zoom-btn.vue';
    import { AppTabId } from '@/core/index.js';
    import { isHospital } from 'views/common/clinic.js';
    import AppLogo from 'views/layout/app-logo.vue';
    import AppLogoHospital from 'views/layout/app-logo-hospital.vue';
    import { isClientSupportHospital } from 'utils/electron.js';
    import AppUpgradeBtn from 'views/layout/app-upgrade-btn.vue';
    import AppDragArea from 'views/layout/app-drag-area.vue';
    import CloudExaminationVersionUpgrade from 'views/layout/cloud-examination-version-upgrade.vue';

    const buildEnv = process.env.BUILD_ENV;

    export default {
        name: 'AppHeader',
        components: {
            CloudExaminationVersionUpgrade,
            AppDragArea,
            AppUpgradeBtn,
            AppLogoHospital,
            AppLogo,
            AppStockRoomSelector,
            AppZoomBtn,
            AppNotice,
            ClientServicePopper,
            AppClinicDropdown,
            AppWardAreaSelector,
            AppDepartmentSelector,
            WechatAlert,
            ThemeSelector,
            AppNavMenu,
            UserSetting,
            AppRemoteCode,
            AppWindowManager,
        },
        mixins: [
            ModulePermission,
        ],
        props: {
            showLogo: {
                type: Boolean,
                default: true,
            },
            showNavMenu: {
                type: Boolean,
                default: true,
            },
            showService: {
                type: Boolean,
                default: true,
            },
            showOrganizationIcon: {
                type: Boolean,
                default: false,
            },
            isCenterNavMenu: {
                type: Function,
            },
            isEnableClinicSelector: {
                type: Function,
            },
            isEnableMallMenuItem: {
                type: Function,
            },
            isEnableSocialMenuItem: {
                type: Function,
            },
            isEnableDrag: {
                type: Boolean,
                default: false,
            },
            isEnableRemoteCode: {
                type: Boolean,
                default: false,
            },
            isEnableWindowManager: {
                type: Boolean,
                default: false,
            },
            isEnableUpgradeBtn: {
                type: Boolean,
                default: false,
            },
            zIndex: {
                type: Number,
                default: 999999,
            },
            todoTheme: {
                type: String,
                default: undefined,
            },
        },
        computed: {
            ...mapGetters([
                'isOpenMp',
                'currentClinic',
                'isElectron',
                'isIntranetUser',
            ]),
            isHospital() {
                return isHospital(this.currentClinic);
            },
            ...mapGetters('viewDistribute', ['featureTheme', 'viewDistributeConfig', 'viewComponents']),
            ...mapGetters('edition', [
                'isExpired', // 版本是否到期
            ]),
            envClass() {
                if (buildEnv) {
                    return `theme-for-env-${buildEnv}`;
                }
                return '';
            },
            visibleClinicSelector() {
                if (this.isEnableClinicSelector) {
                    return this.isEnableClinicSelector();
                }

                return this.viewDistributeConfig.AppHeader.showClinicSelector;
            },
            visibleNavMenu() {
                if (!this.showNavMenu) return false;
                if (window.appTabId === AppTabId.OUTPATIENT) {
                    return this.hasChildHealthModule && this.hasOutpatientModule;
                }
                return this.viewDistributeConfig.AppHeader.showNavMenu;
            },
            visibleWardSelector() {
                return this.viewDistributeConfig.AppHeader.showWardSelector;
            },
            visibleDepartmentSelector() {
                return this.viewDistributeConfig.AppHeader.showDepartmentSelector;
            },
            visibleStockRoomSelector() {
                return this.viewDistributeConfig.AppHeader.showStockRoomSelector;
            },
            menNavIsCenter() {
                if (this.isCenterNavMenu) {
                    return this.isCenterNavMenu();
                }

                return this.viewDistributeConfig.AppHeader.menNavIsCenter;
            },
            visibleFullScreenBtn() {
                return !isClientSupportHospital();
            },
            visibleZoomBtn() {
                return isClientSupportHospital();
            },
            visibleRemoteCode() {
                return this.isEnableRemoteCode && isClientSupportHospital();
            },
            visibleUpgradeBtn() {
                return this.isEnableUpgradeBtn && isClientSupportHospital();
            },
            visibleWindowManager() {
                return this.isEnableWindowManager && isClientSupportHospital();
            },
            showServiceEntry() {
                return this.showService && !this.isIntranetUser;
            },
            supportCloudExaminationUpgrade() {
                return this.viewDistributeConfig.AppHeader.supportCloudExaminationUpgrade;
            },
        },

        created() {
            this.$store.dispatch('fetchTipsFlag');
        },

        methods: {
            handleVersionUpgrade() {

            },
        },
    };
</script>

