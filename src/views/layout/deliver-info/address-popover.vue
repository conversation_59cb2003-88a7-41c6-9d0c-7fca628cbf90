<template>
    <div v-abc-click-outside="outside">
        <div
            ref="addressPopover"
            class="address-popover-wrapper"
            data-cy="add-address-popover"
            :style="showEditorDialog ? 'z-index: 1991' : 'z-index: 1992'"
        >
            <div
                v-for="(item, index) in availableAddress"
                :key="item.id || index"
                class="address-item"
                :class="{ 'is-selected': isSelected(item) }"
                @click="selectAddress(item)"
            >
                <div class="patient">
                    <h5>{{ item.deliveryName }}</h5>
                    <span>{{ item.deliveryMobile }}</span>
                    <abc-button type="text" @click.stop="updateDelivery(item)">
                        修改
                    </abc-button>
                </div>
                <div class="address-info">
                    {{ formatAddress(item) }}
                </div>
                <i v-if="isSelected(item)" class="iconfont cis-icon-positive_"></i>
            </div>

            <div v-if="unavailableAddress.length" class="red-tips">
                以下地址超出配送范围
            </div>

            <div v-for="item in unavailableAddress" :key="item.id" class="address-item is-disabled">
                <div class="patient">
                    <h5>{{ item.deliveryName }}</h5>
                    <span>{{ item.deliveryMobile }}</span>
                    <abc-button type="text" @click="updateDelivery(item)">
                        修改
                    </abc-button>
                </div>
                <div class="address-info">
                    {{ formatAddress(item) }}
                </div>
            </div>

            <div class="add-address" data-cy="add-address-popover-add" @click="addDelivery">
                添加收货地址
            </div>
        </div>

        <address-editor
            v-if="showEditorDialog"
            :id="currentSelectItem.id"
            v-model="showEditorDialog"
            :patient-id="patient.id"
            :delivery-info="currentSelectItem"
            @confirm="confirmHandler"
            @refresh="refreshHandler"
        ></address-editor>
    </div>
</template>

<script type="text/ecmascript-6">
    import Popper from 'utils/vue-popper';
    import AddressEditor from './address-editor';
    import { defaultCountryCode } from 'utils/country-codes';
    import { formatAddress } from '@/utils';

    export default {
        name: 'AddressPopover',
        components: {
            AddressEditor,
        },
        mixins: [ Popper ],
        props: {
            deliveryInfo: Object,
            visible: Boolean,
            patient: Object,
            address: Array,
            tempAddress: Array,
            clinicBasicConfig: {
                type: Object,
                require: true,
            },
        },

        data() {
            return {
                showEditorDialog: false,
                currentSelectItem: {
                    deliveryName: '',
                    deliveryMobile: '',
                    deliveryCountryCode: defaultCountryCode,
                    addressProvinceName: '',
                    addressCityName: '',
                    addressDistrictName: '',
                    addressProvinceId: '',
                    addressCityId: '',
                    addressDistrictId: '',
                    addressDetail: '',
                },
            };
        },
        computed: {
            availableAddress() {
                return this.address.concat(this.tempAddress);
            },

            // 补充注释原因2024-08-29
            // 最早的版本确实会提示地址不可配送到不能被选择，但是我们自己设置的快递规则拦截只适用于计费和小程序用户，实际使用中，尤其pc面对面不该受到这个限制，应该以当前合作的快递单位实际配送区域规则变化为准
            // 为避免用户扯皮，这里规则暂时不会做调整，只在此处说明原因
            unavailableAddress() {
                // return this.address.filter(item => {
                //     return !item.isAvailable
                // }).concat(this.tempAddress.filter(item => {
                //     return !item.isAvailable
                // }))
                return [];
            },

        },
        watch: {
            visible: {
                immediate: true,
                handler(val) {
                    this.showPopper = val;
                },
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.addressPopover;
            this.referenceElm = this.$parent.$refs.addressRef.$refs.abcinput;
        },

        beforeDestroy() {
            this.doDestroy();
        },
        methods: {
            outside() {
                if (!this.showEditorDialog) {
                    this.$emit('closePopper');
                }
            },
            refreshHandler() {
                this.$emit('refresh');
            },

            isSelected(item) {
                if (!this.deliveryInfo) return false;
                return this.deliveryInfo.addressProvinceId === item.addressProvinceId &&
                    this.deliveryInfo.addressCityId === item.addressCityId &&
                    this.deliveryInfo.addressDistrictId === item.addressDistrictId &&
                    this.deliveryInfo.addressDetail === item.addressDetail &&
                    this.deliveryInfo.deliveryMobile === item.deliveryMobile &&
                    this.deliveryInfo.deliveryName === item.deliveryName;
            },

            selectAddress(address) {
                this.$emit('select', address);
                this.showEditorDialog = false;
            },
            updateDelivery(item) {
                this.currentSelectItem = {
                    deliveryCountryCode: defaultCountryCode, ...item,
                };
                this.showEditorDialog = true;
            },

            addDelivery() {
                const {
                    name = '',
                    mobile = '',
                    countryCode = defaultCountryCode,
                } = this.patient;
                const {
                    addressProvinceId = '',
                    addressProvinceName = '',
                    addressCityId = '',
                    addressCityName = '',
                    addressDistrictId = '',
                    addressDistrictName = '',
                } = this.clinicBasicConfig;
                this.currentSelectItem = {
                    deliveryName: name,
                    deliveryMobile: mobile,
                    deliveryCountryCode: countryCode,
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                    addressProvinceId,
                    addressCityId,
                    addressDistrictId,
                    addressDetail: '',
                };
                this.showEditorDialog = true;
            },

            formatAddress,

            async confirmHandler(address) {
                // let {data} = await ChargeAPI.checkDeliveryScope(address);
                // this.$set(address, 'isAvailable', data.isInScope);
                if (address.tempId) {
                    this.tempAddress.forEach((item) => {
                        if (item.tempId) {
                            Object.assign(item, address);
                        }
                    });
                } else {
                    address.tempId = this.tempAddress.length + 1;
                    this.tempAddress.unshift(address);
                }
                this.showPopper = true;
            },

        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .address-popover-wrapper {
        position: absolute;
        left: 0;
        z-index: 1992;
        width: 430px;
        max-height: 326px;
        margin-top: 4px;
        overflow-y: auto;
        overflow-y: overlay;
        background-color: #ffffff;
        border: 1px solid rgba(183, 185, 194, 1);
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

        .address-item {
            position: relative;
            padding: 12px;
            cursor: pointer;
            border-bottom: 1px solid $P6;

            &:last-child {
                border-bottom: 0;
            }

            &.is-selected {
                .iconfont {
                    position: absolute;
                    top: 50%;
                    right: 16px;
                    margin-top: -8px;
                    color: $B2;
                }
            }

            &.is-disabled {
                cursor: default;

                .patient {
                    > h5 {
                        color: #a0b1c4;
                    }

                    > span {
                        color: #a0b1c4;
                    }
                }

                .address-info {
                    color: #a0b1c4;
                }
            }
        }

        .red-tips {
            padding: 8px 12px;
            font-size: 12px;
            color: $R2;
            border-bottom: 1px solid $P6;
        }

        .patient {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            > h5 {
                color: $T1;
            }

            > span {
                margin-left: 8px;
                color: $T2;
            }
        }

        .address-info {
            padding-right: 20px;
            color: $T2;
            word-break: break-all;
        }

        .add-address {
            height: 42px;
            line-height: 42px;
            color: $B2;
            text-align: center;
            cursor: pointer;
        }
    }
</style>
