<template>
    <div class="deliver-info">
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            :auto-focus="false"
            title="快递信息"
            content-styles="width: 480px"
            class="select-address-dialog"
            data-cy="deliver-info-dialog"
            @close-dialog="showPopper = false"
        >
            <abc-form
                ref="form"
                v-abc-loading="loading"
                label-position="left"
                :label-width="72"
                item-block
            >
                <div class="address-selector">
                    <abc-form-item
                        label="寄送地址"
                        :required="requiredAddress"
                        style="margin-bottom: 24px;"
                        :class="{
                            'is-disabled': isCharged,
                        }"
                    >
                        <abc-edit-div
                            ref="addressRef"
                            v-model="addressInfoStr"
                            :disabled="isCharged"
                            class="address-edit-div"
                            style="width: 360px;"
                            readonly
                            responsive
                            placeholder="请选择收货地址"
                            data-cy="add-address"
                            @click="clickEvent"
                        >
                        </abc-edit-div>
                    </abc-form-item>
                </div>

                <div style="display: flex;">
                    <abc-form-item
                        v-abc-focus-selected
                        label="寄送方式"
                        required
                        style=" display: inline-flex; margin-bottom: 24px;"
                    >
                        <abc-select
                            ref="delivery-company-selector"
                            v-model="postData.deliveryCompany.id"
                            :width="176"
                            custom-class="delivery-company-selector"
                            placeholder="快递公司"
                            :disabled="isCharged"
                            :setting="!!addressInfoStr"
                            data-cy="deliver-info-company-selector"
                            @change="handleChangeCompany"
                            @set="showDialogEvent"
                        >
                            <abc-option
                                v-for="item in deliveryCompanies"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item required style="display: inline-flex; margin-bottom: 24px;">
                        <abc-select
                            v-model="postData.deliveryPayType"
                            :width="176"
                            style="margin-left: 8px;"
                            :disabled="isCharged"
                            placeholder="付款方式"
                            data-cy="deliver-info-pay-type-selector"
                            @change="calcDeliveryFee"
                        >
                            <abc-option
                                v-for="item in deliveryPayTypeList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </div>

                <abc-form-item label="快递单号" style="margin-bottom: 24px;">
                    <abc-input
                        v-model="postData.deliveryOrderNo"
                        v-abc-focus-selected
                        :width="360"
                        trim
                        placeholder="快递单号"
                        data-cy="deliver-info-order-no-input"
                        :disabled="isCharged"
                        max-length="50"
                    >
                    </abc-input>
                </abc-form-item>
                <template v-if="needCalcFee && !isCharged && postData.deliveryPayType">
                    <div class="address-rule-info">
                        <label>快递费</label>
                        <div>
                            <p class="price" data-cy="deliver-info-price">
                                <abc-money :value="deliveryRuleInfo.expressDeliveryFee" :is-show-space="true"></abc-money>
                            </p>
                            <p v-if="deliveryRuleInfo.isMarkRule" class="tips">
                                {{ deliveryRuleInfo.name }}：
                                {{ deliveryRuleInfo.ruleInfo }}
                            </p>
                            <p v-else class="error-tips">
                                该区域未匹配快递自动算费规则
                            </p>
                        </div>
                    </div>
                    <!--                    <div v-if="deliveryRuleInfo.isMarkRule" class="address-rule-info" style="margin-top: 16px;">-->
                    <!--                        <label>配送费用</label>-->
                    <!--                        {{ $t('currencySymbol') }} {{ deliveryRuleInfo.expressDeliveryFee | formatMoney }}-->
                    <!--                    </div>-->
                </template>
            </abc-form>
            <div slot="footer" class="dialog-footer">
                <abc-flex flex="1" justify="start">
                    <abc-button variant="ghost" @click="patientUploadAddressFn">
                        患者扫码新增
                    </abc-button>
                </abc-flex>
                <abc-button
                    type="primary"
                    :disabled="disabledConfirmBtn"
                    data-cy="deliver-info-confirm"
                    @click="confirmHandler"
                >
                    确定
                </abc-button>
                <abc-button type="blank" data-cy="deliver-info-cancel" @click="showDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <address-popover
            v-if="showPopper"
            :visible="showPopper"
            :patient="patient"
            :address="address"
            :temp-address="tempAddress"
            :delivery-info="postData"
            :clinic-basic-config="clinicBasicConfig"
            @closePopper="showPopper = false"
            @select="selectAddress"
            @refresh="refreshHandler"
        ></address-popover>

        <abc-dialog
            v-if="showExpressDialog"
            v-model="showExpressDialog"
            title="设置快递公司"
            append-to-body
            custom-class="setting-company-dialog"
            content-styles="padding: 0;"
            @close-dialog="cancelEdit"
        >
            <abc-form ref="postForm">
                <ul>
                    <li v-for="item in deliveryCompanies" :key="item.id">
                        <abc-form-item v-if="isEditing(item)" required>
                            <abc-input
                                ref="editInput"
                                v-model="currentEditCompany.name"
                                class="edit-input"
                                required
                                trim
                                :width="216"
                                max-length="6"
                                @input="emojiFilter"
                            ></abc-input>
                        </abc-form-item>

                        <div v-else>
                            {{ item.name }}
                        </div>

                        <div class="operation-wrapper">
                            <template v-if="isEditing(item)">
                                <abc-button type="text" @click.stop="confirmEdit(item)">
                                    确定
                                </abc-button>
                                <abc-button class="btn-cancel" type="text" @click.stop="cancelEdit(item)">
                                    取消
                                </abc-button>
                            </template>

                            <template v-else>
                                <abc-button type="text" @click.stop="toEdit(item)">
                                    修改
                                </abc-button>
                                <abc-button class="btn-danger" type="text" @click.stop="confirmDelete(item)">
                                    删除
                                </abc-button>
                            </template>
                        </div>
                    </li>
                    <li class="btn-add" @click="createCompany">
                        <i class="iconfont cis-icon-plus_thin"></i>新增
                    </li>
                </ul>
            </abc-form>
        </abc-dialog>

        <address-upload-dialog
            v-if="showAddressUploadDialog"
            v-model="showAddressUploadDialog"
            scene="cashier"
            :patient-id="patient.id"
            @upload-success="patientUploadAddressSuccessFn"
        ></address-upload-dialog>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import ChargeAPI from 'api/charge';
    import AddressPopover from './address-popover';
    import EmojiFilter from 'utils/emoji-filter';
    import clone from 'utils/clone';
    import { ChargeStatusEnum } from '@/service/charge/constants';
    import { ANONYMOUS_ID } from '@abc/constants';
    import AddressUploadDialog from '../deliver-address-upload/address-upload-dialog.vue';

    export default {
        components: {
            AddressPopover,
            AddressUploadDialog,
        },
        props: {
            value: Boolean,
            deliveryInfo: Object,
            disabled: Boolean,
            patient: Object,
            chargeStatus: [Number],
            chargeForms: Array,
            isReplay: Boolean,
            validateDeliveryRule: {
                type: Boolean,
                default: false,
            },

            needCalcFee: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                ChargeStatusEnum,
                showPopper: false,
                requiredAddress: true,

                address: [],
                tempAddress: [],

                addressInfoStr: '',
                deliveryCompanies: [],
                showExpressDialog: false,
                canEditing: true,

                currentEditCompany: null,

                loading: false,

                postData: {
                    id: '',
                    addressCityId: '',
                    addressCityName: '',
                    addressDetail: '',
                    addressDistrictId: '',
                    addressDistrictName: '',
                    addressProvinceId: '',
                    addressProvinceName: '',
                    deliveryName: '',
                    deliveryMobile: '',
                    deliveryCompany: {
                        id: '',
                        name: '',
                    },
                    deliveryOrderNo: '',
                    deliveryPayType: 1,
                },
                deliveryRuleInfo: {
                    name: '',
                    ruleInfo: '',
                    isMarkRule: 0,
                    expressDeliveryFee: '',
                },
                availablePayTypes: [],

                showAddressUploadDialog: false,
            };
        },
        computed: {
            ...mapGetters(['clinicBasicConfig']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            isCharged() {
                return this.chargeStatus > ChargeStatusEnum.UN_CHARGE;
            },

            disabledConfirmBtn() {
                return this.isCharged;
            },
            deliveryPayTypeList() {
                if (this.availablePayTypes?.length === 2) {
                    return [{
                        label: '寄付',
                        value: 1,
                    },{
                        label: '到付',
                        value: 0,
                    }];
                }
                if (this.availablePayTypes.includes(1)) {
                    return [{
                        label: '寄付',
                        value: 1,
                    }];
                }
                if (this.availablePayTypes.includes(0)) {
                    return [{
                        label: '到付',
                        value: 0,
                    }];
                }
                return [];
            },
        },
        watch: {
            postData: {
                handler(val) {
                    const {
                        addressCityName,
                        addressDetail,
                        addressDistrictName,
                        addressProvinceName,
                        deliveryName,
                        deliveryMobile,
                    } = val;
                    let str = '';
                    if (deliveryName) {
                        str += deliveryName;
                    }
                    if (deliveryMobile) {
                        str += (str ? ' ' : '') + deliveryMobile;
                    }
                    if (str) {
                        str += '<br/>';
                    }
                    str += addressProvinceName || '';
                    str += addressCityName || '';
                    str += addressDistrictName || '';
                    str += addressDetail || '';
                    this.addressInfoStr = str;
                },
                deep: true,
                immediate: true,
            },
        },
        async created() {
            // 已经发药状态需要禁用编辑
            if (this.status) {
                this.canEditing = false;
            }
            if (!this.isCharged) {
                await this.fetchPatientAddress();

                if (this.deliveryInfo) {
                    Object.assign(this.postData, clone(this.deliveryInfo));
                    await this.fetchSuggestionDeliverCompany();
                    await this.changeCompany(this.postData.deliveryCompany.id);
                    await this.calcDeliveryFee();
                }
                this.defaultSelectDeliveryInfo();
            } else {
                if (this.deliveryInfo) {
                    Object.assign(this.postData, clone(this.deliveryInfo));
                    this.deliveryCompanies = [{ ...this.deliveryInfo?.deliveryCompany }];
                    if (this.deliveryInfo?.deliveryPayType === 0) {
                        this.deliveryPayTypeList.push({
                            label: '到付',
                            value: 0,
                        });
                    } else {
                        this.deliveryPayTypeList.push({
                            label: '寄付',
                            value: 1,
                        });
                    }
                }
            }
        },
        methods: {
            clickEvent() {
                if (this.disabled) return false;
                this.showPopper = !this.showPopper;
            },
            handleChangeCompany(companyId) {
                this.postData.deliveryPayType = '';
                this.changeCompany(companyId);
            },

            changeCompany(companyId) {
                this.availablePayTypes = [];
                const company = this.deliveryCompanies.find((item) => {
                    return item.id === companyId;
                });
                if (company) {
                    this.postData.deliveryCompany.id = companyId;
                    this.postData.deliveryCompany.name = company.name;
                    this.availablePayTypes = company?.availablePayTypes || [];
                }
            },

            /**
             * @param autoSelected 是否自动选中最新添加的地址，扫码新增
             */
            async fetchPatientAddress(autoSelected = false) {
                this.address = [];

                if (!this.patient) return;
                if (!this.patient.id) return;
                if (this.patient.id === ANONYMOUS_ID) return;
                const { data } = await ChargeAPI.fetchPatientAddress(this.patient.id);
                this.address = data.deliveryInfos || [];
                if (autoSelected && this.address.length) {
                    this.selectAddress(this.address[this.address.length - 1]);
                }
            },

            defaultSelectDeliveryInfo() {
                // pc端不受配送规则限制，地址只有一个则默认填入，多个仍然需要用户自己确认
                if (this.address.length === 1 && !this.postData.id) {
                    this.selectAddress(this.address[0]);
                }
            },

            updateCurrentDeliveryInfo() {
                if (this.postData.id) {
                    const address = this.address.find((item) => {
                        return item.id === this.postData.id;
                    });
                    if (address) {
                        this.selectAddress(address);
                    }
                }
            },

            selectAddress(address) {
                Object.assign(this.postData, address);
                this.showPopper = false;
                this.requiredAddress = false;
                this.$nextTick(() => {
                    this.requiredAddress = true;
                });
                this.postData.deliveryCompany.id = '';
                this.postData.deliveryPayType = '';
                this.deliveryCompanies = [];
                this.availablePayTypes = [];
                this.fetchSuggestionDeliverCompany();
            },

            async refreshHandler() {
                await this.fetchPatientAddress();
                this.updateCurrentDeliveryInfo();
                this.showPopper = true;
            },

            confirmHandler() {
                if (!this.postData.addressDetail) {
                    this.$Toast({
                        message: '详细地址不能为空',
                        type: 'info',
                    });
                }
                if (!this.postData.addressDetail) return false;

                this.$refs.form.validate((valid) => {
                    if (valid) {
                        this.showPopper = false;
                        this.$emit('confirm', {
                            ...this.postData,
                        });
                    }
                });
            },
            changeHandler(val) {
                const company = this.deliveryCompanies.find((item) => {
                    return item.id === val;
                });
                this.$emit('change', company);
            },
            showDialogEvent() {
                this.showExpressDialog = true;
            },
            async fetchDeliveryCompanies() {
                const { data } = await ChargeAPI.fetchDeliveryCompanies({
                    addressDistrictId: this.postData.addressDistrictId,
                    addressCityId: this.postData.addressCityId,
                    addressProvinceId: this.postData.addressProvinceId,
                    scene: 0,
                }) || {};
                this.deliveryCompanies = data.deliveryCompanies || [];
            },
            async fetchSuggestionDeliverCompany() {
                const postData = {
                    addressDistrictId: this.postData.addressDistrictId,
                    addressCityId: this.postData.addressCityId,
                    addressProvinceId: this.postData.addressProvinceId,
                    scene: 0,
                };
                if (!postData.addressCityId && !postData.addressDistrictId && !postData.addressProvinceId) {
                    return false;
                }
                try {
                    this.loading = true;
                    const { data } = await ChargeAPI.fetchDeliveryCompanies(postData);
                    const { deliveryCompanies = [] } = data || {};
                    this.deliveryCompanies = deliveryCompanies;
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },

            toEdit(item) {
                this.currentEditCompany = item;
                this.focusInput();
            },
            isEditing(item) {
                return this.currentEditCompany && this.currentEditCompany.id === item.id;
            },
            cancelEdit(item) {
                if (item && item.id === '') {
                    this.deliveryCompanies.pop();
                } else {
                    this.deliveryCompanies = this.deliveryCompanies.filter((item) => {
                        return item.id;
                    });
                }
                this.currentEditCompany = null;
            },
            createCompany() {
                // 存在正在新建的诊室，聚焦到该item上，保证同一时间只有一个编辑
                if (this.currentEditCompany && this.currentEditCompany.id === '') {
                    this.focusInput();
                    return;
                }
                const newItem = {
                    id: '',
                    name: '',
                };
                this.deliveryCompanies.push(newItem);
                this.currentEditCompany = newItem;
                this.focusInput();
            },
            focusInput() {
                this.$nextTick(() => {
                    const editInput = $('.setting-company-dialog ul li .edit-input .abc-input__inner');
                    editInput && editInput.focus();
                });
            },
            confirmEdit() {
                this.$refs.postForm.validate((valid) => {
                    if (valid) {
                        this.currentEditCompany.id ? this.updateSubmit() : this.createSubmit();
                    }
                });
            },
            confirmDelete(item) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不可恢复，确认删除？',
                    onConfirm: async () => {
                        this.deleteSubmit(item);
                    },
                });
            },

            async createSubmit() {
                try {
                    this.btnLoading = true;
                    await ChargeAPI.createDeliveryCompany(this.currentEditCompany);
                    this.successHandler('新建');
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            async updateSubmit() {
                try {
                    this.btnLoading = true;
                    await ChargeAPI.updateDeliveryCompany(this.currentEditCompany.id, {
                        name: this.currentEditCompany.name,
                    });
                    this.successHandler('修改');
                } catch (e) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '请输入正确的名称',
                    });
                    this.btnLoading = false;
                }
            },
            async deleteSubmit(item) {
                try {
                    this.btnLoading = true;
                    await ChargeAPI.deleteDeliveryCompany(item.id);
                    this.successHandler('删除');
                } catch (e) {
                    this.btnLoading = false;
                }
            },

            successHandler(type) {
                this.currentEditCompany = null;
                this.fetchDeliveryCompanies();
                this.btnLoading = false;
                this.$Toast({
                    message: `${type}成功`,
                    type: 'success',
                });
            },

            emojiFilter(val) {
                this.currentEditCompany.name = EmojiFilter(val);
            },
            transSimpleChargeForms() {
                const chargeForms = [];
                this.chargeForms.forEach((form) => {
                    const chargeFormItems = form.chargeFormItems.filter((item) => {
                        return item.checked;
                    });
                    if (chargeFormItems.length) {
                        const tempForm = {
                            keyId: form.keyId,
                            id: form.id,
                            registration: form.registration,
                            sourceFormType: form.sourceFormType,
                            chargeFormItems: chargeFormItems.map((item) => {
                                return {
                                    keyId: item.keyId,
                                    id: item.id,
                                    unit: item.unit,
                                    name: item.name,
                                    unitCount: item.unitCount,
                                    doseCount: item.doseCount || 1,
                                    unitPrice: item.unitPrice,
                                    productId: item.productId,
                                    productType: item.productType,
                                    productSubType: item.productSubType,
                                    sourceUnitPrice: item.sourceUnitPrice,
                                    sourceTotalPrice: item.sourceTotalPrice,
                                    expectedUnitPrice: item.expectedUnitPrice,
                                    expectedTotalPrice: item.expectedTotalPrice,
                                    useDismounting: item.useDismounting,
                                };
                            }),
                        };
                        chargeForms.push(tempForm);
                    }
                });
                return chargeForms;
            },

            async calcDeliveryFee() {
                if (!this.needCalcFee) return false;
                const postData = {
                    chargeSheetId: this.$route.params.id,
                    isRenew: +this.isReplay,
                    deliveryInfo: this.postData,
                    chargeForms: this.transSimpleChargeForms(),
                };

                try {
                    const { data } = await ChargeAPI.calcDeliveryFee(postData);
                    this.deliveryRuleInfo.isMarkRule = data.isMarkRule;
                    this.deliveryRuleInfo.name = data.ruleExpressDeliveryInfo && data.ruleExpressDeliveryInfo.name;
                    this.deliveryRuleInfo.ruleInfo =
                        data.ruleExpressDeliveryInfo && data.ruleExpressDeliveryInfo.ruleInfo;
                    this.deliveryRuleInfo.expressDeliveryFee = data.expressDeliveryFee;
                } catch (e) {
                    console.log(e);
                }
            },

            // 患者扫码新增
            patientUploadAddressFn() {
                if (!this.patient.id) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '请先选择患者',
                    });
                    return;
                }
                this.showAddressUploadDialog = true;
            },

            // 患者扫码新增成功回调
            patientUploadAddressSuccessFn() {
                this.fetchPatientAddress(true);
            },
        },
    };
</script>

<style lang="scss">
    @import '~styles/theme.scss';

    .delivery-company-selector {
        .setting {
            position: fixed;
            bottom: -36px;
            left: -1px;
            width: calc(100% + 2px);
            height: 36px;
            text-align: right;
            cursor: pointer;
            background-color: #ffffff;
            border: 1px solid $P3;
            border-bottom-right-radius: 4px;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

            span.icon {
                position: relative;
                top: 50%;
                display: inline-block;
                margin-right: 16px;
                color: $T3;
                transform: translateY(-50%);

                &:hover {
                    color: $B2;
                }
            }
        }
    }

    .abc-dialog.setting-company-dialog {
        z-index: 10000;
        min-width: 326px;

        .abc-dialog-header {
            padding-left: 16px;
        }

        ul li {
            box-sizing: border-box;
            display: flex;
            align-items: center;
            height: 32px;
            padding: 0 16px;
            line-height: 32px;
            cursor: pointer;
            border-bottom: #e6eaee 1px solid;

            &:hover {
                background: $P4;
            }

            :last-child {
                border: none;
            }

            .abc-form-item {
                margin-bottom: 0;

                .edit-input {
                    flex: 1;

                    input.abc-input__inner {
                        padding: 0;

                        &:focus {
                            border: none !important;
                            box-shadow: none !important;
                        }

                        &:hover {
                            border: none !important;
                        }
                    }
                }
            }

            .operation-wrapper {
                display: flex;
                margin-left: auto;

                button.btn-danger {
                    color: $R2;
                }

                button.btn-cancel {
                    color: $T2;
                }
            }

            &.btn-add {
                font-size: 14px;
                color: #8d9aa8;
                user-select: none;
                border-bottom: none;
                border-bottom-right-radius: 4px;
                border-bottom-left-radius: 4px;

                .cis-icon-plus_thin {
                    margin-right: 8px;
                    font-size: 12px;
                }

                &:hover {
                    color: $B1;
                }
            }
        }

        .abc-dialog-footer {
            padding: 10px 16px;

            button.abc-button {
                min-width: 60px;
                height: 28px;
            }
        }
    }
</style>
