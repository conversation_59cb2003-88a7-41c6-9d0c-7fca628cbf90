<template>
    <abc-table
        type="pro"
        theme="white"
        class="hospital-nurse-medicine-apply-dialog-table"
        :loading="loading"
        :render-config="renderConfig"
        :data-list="dataList"
        show-content-empty
        show-hover-tr-bg
        :tr-click-trigger-checked="false"
        :need-selected="false"
        :show-checked="true"
        :fixed-tr-height="false"
        :disabled-item-func="disabledItemFunc"
        @changeAllChecked="$emit('medicineCheckedAllChange', $event)"
    >
        <template #checked="{ trData }">
            <abc-table-cell>
                <!-- 患者记账的药品不能勾选 -->
                <abc-checkbox
                    v-model="trData.checked"
                    :disabled="disabledItemFunc(trData)"
                    @change="$emit('medicineCheckedChange', $event, trData)"
                ></abc-checkbox>
            </abc-table-cell>
        </template>
        <template #bed="{ trData }">
            <abc-table-cell>
                {{ Number(trData.bed) < 10 ? `0${trData.bed}` : trData.bed }}
            </abc-table-cell>
        </template>
        <template #patientName="{ trData }">
            <abc-table-cell>
                <span class="ellipsis" :title="trData.patientName">
                    {{ trData.patientName }}
                </span>
            </abc-table-cell>
        </template>
        <template #sourceSheetType="{ trData }">
            <abc-table-cell>
                {{ trData.sourceSheetType === 60 ? '记账' : '医嘱' }}
            </abc-table-cell>
        </template>
        <template #undispensedTime="{ trData }">
            <abc-table-cell>
                {{ trData.undispensedTime ? formatDate(new Date(trData.undispensedTime), 'MM-DD HH:mm') : '' }}
            </abc-table-cell>
        </template>
        <template #planExecuteTime="{ trData }">
            <abc-table-cell>
                {{ trData.planExecuteTime ? formatDate(new Date(trData.planExecuteTime), 'MM-DD HH:mm') : '' }}
            </abc-table-cell>
        </template>
        <template #adviceView="{ trData }">
            <abc-table-cell>
                <!-- 记账 -->
                <span
                    v-if="trData.sourceSheetType === 60"
                    class="ellipsis"
                    style="display: flex; align-items: center;"
                >
                    -
                    <abc-tooltip
                        theme="yellow"
                        placement="bottom-start"
                    >
                        <abc-icon icon="n-warning-circle-line" color="#7a8794" style="margin-left: 4px;"></abc-icon>
                        <template slot="content">
                            <div>患者记账项目暂不支持重新发药，请通过记账重新开出</div>
                        </template>
                    </abc-tooltip>
                </span>
                <!-- 医嘱 -->
                <span
                    v-else-if="trData.adviceView && isNotNull(trData.adviceView.status)"
                    class="ellipsis"
                    style="display: flex; align-items: center;"
                    :style="`${disabledItemList.includes(trData.adviceView.status) ? 'color: var(--abc-color-Y2)' : ''}`"
                >
                    {{ MedicalAdviceStatusStr[trData.adviceView.status] }}
                    <abc-tooltip
                        v-if="disabledItemList.includes(trData.adviceView.status)"
                        theme="yellow"
                        placement="bottom-start"
                    >
                        <abc-icon icon="n-warning-circle-line" color="#7a8794" style="margin-left: 4px;"></abc-icon>
                        <template slot="content">
                            <div style="color: var(--abc-color-T2);">当前医嘱状态不支持重新发药</div>
                            <div>下达：请先核对医嘱后再尝试重新发药</div>
                            <div>撤销：请下达新医嘱后再申请发药</div>
                        </template>
                    </abc-tooltip>
                </span>
            </abc-table-cell>
        </template>
        <template #medicinalName="{ trData }">
            <div v-if="isSendApply || isReSendApply || trData.isChinese" class="hospital-nurse-medicine-apply-custom-cell-wrapper">
                <div
                    v-for="(it, num) in trData.medicinalName"
                    :key="num"
                    :title="getName(it)"
                    class="hospital-nurse-medicine-apply-custom-cell ellipsis"
                >
                    <abc-flex align="center" style="max-width: 150px;">
                        <span style="flex: 1;" class="ellipsis"> {{ getName(it) }}</span>
                        <abc-tag-v2
                            v-if="trData.needUnDispenseFlag"
                            style="margin-right: 6px;"
                            theme="danger"
                            size="mini"
                        >
                            待退
                        </abc-tag-v2>
                    </abc-flex>
                </div>
            </div>

            <abc-table-cell v-else class="hospital-nurse-medicine-apply-dialog-table-tr">
                <div
                    :title="getName(trData.medicinalName)"
                    style="width: 100%; height: 39px; overflow: hidden; line-height: 39px;"
                    class="ellipsis"
                >
                    <abc-flex align="center" style="max-width: 150px;">
                        <span style="flex: 1;" class="ellipsis"> {{ getName(trData.medicinalName) }}</span>
                        <abc-tag-v2
                            v-if="trData.needUnDispenseFlag"
                            style="margin-right: 6px;"
                            theme="danger"
                            size="mini"
                        >
                            待退
                        </abc-tag-v2>
                    </abc-flex>
                </div>
                <div
                    v-if="trData.groupIds && trData.groupIds.length > 1"
                    class="hospital-nurse-medicine-apply-dialog-table-group-line"
                    :style="{
                        height: `${(trData.groupIds.length - 1) * 40}px`,
                    }"
                ></div>
            </abc-table-cell>
        </template>
        <template #manufacturer="{ trData }">
            <div class="hospital-nurse-medicine-apply-custom-cell-wrapper">
                <div
                    v-for="(it, num) in trData.dispensingFormItems"
                    :key="num"
                    :title="getManufacturer(it)"
                    class="hospital-nurse-medicine-apply-custom-cell ellipsis"
                >
                    {{ getManufacturer(it) }}
                </div>
            </div>
        </template>
        <template #way="{ trData }">
            <div v-if="isSendApply || isReSendApply || trData.isChinese" class="hospital-nurse-medicine-apply-custom-cell-wrapper">
                <div
                    v-for="(it, num) in trData.medicinalName"
                    :key="num"
                    :title="trData.way"
                    class="hospital-nurse-medicine-apply-custom-cell ellipsis"
                >
                    {{ trData.way }}
                </div>
            </div>

            <abc-table-cell v-else>
                <span class="ellipsis" :title="trData.way">
                    {{ trData.way }}
                </span>
            </abc-table-cell>
        </template>
        <template #spec="{ trData }">
            <div v-if="isSendApply || isReSendApply" class="hospital-nurse-medicine-apply-custom-cell-wrapper">
                <div
                    v-for="(it, num) in trData.spec"
                    :key="num"
                    :title="getSpec(it)"
                    class="hospital-nurse-medicine-apply-custom-cell ellipsis"
                    :style="{ color: it?.status === statusEnum.NO_CHARGE_STOCK ? `${$store.state.theme.style.T2}` : `${$store.state.theme.style.T1}` }"
                >
                    {{ getSpec(it) }}
                </div>
            </div>

            <div v-else-if="trData.isChinese" class="hospital-nurse-medicine-apply-custom-cell-wrapper">
                <div
                    v-for="(it, num) in trData.medicinalName"
                    :key="num"
                    :title="getSpec(trData.spec)"
                    class="hospital-nurse-medicine-apply-custom-cell ellipsis"
                >
                    {{ getSpec(trData.spec) }}
                </div>
            </div>

            <abc-table-cell v-else>
                <div
                    :title="getSpec(trData.spec)"
                    class="ellipsis"
                    style="width: 100%; height: 39px; overflow: hidden; line-height: 39px;"
                >
                    {{ getSpec(trData.spec) }}
                </div>
            </abc-table-cell>
        </template>
        <template #applyNumber="{ trData }">
            <div v-if="isSendApply || isReSendApply" class="hospital-nurse-medicine-apply-custom-cell-wrapper">
                <abc-tooltip
                    v-for="(it, num) in trData.applyNumber"
                    :key="num"
                    v-abc-title.ellipsis="getUnitCount(it)"
                    :title="it.totalCount"
                    class="hospital-nurse-medicine-apply-custom-cell ellipsis"
                    :style="{ color: it?.status === statusEnum.NO_CHARGE_STOCK ? `${$store.state.theme.style.T2}` : `${$store.state.theme.style.T1}` }"
                    placement="top"
                    :content="countTipContent(it)"
                    :disabled="!countTipContent(it)"
                    :max-width="190"
                >
                    <span>{{ getUnitCount(it) }}</span>
                </abc-tooltip>
            </div>

            <div v-else-if="trData.isChinese" class="hospital-nurse-medicine-apply-custom-cell-wrapper">
                <abc-tooltip
                    v-for="(it, num) in trData.applyNumber"
                    :key="num"
                    :title="it.totalCount"
                    class="hospital-nurse-medicine-apply-custom-cell ellipsis"
                    :content="countTipContent(it)"
                    :disabled="!countTipContent(it)"
                    :max-width="190"
                >
                    <span v-if="it.totalCount">
                        {{ it.totalCount }}{{ it.unit }}
                    </span>

                    <span class="gray" style="font-size: 12px;">{{ getUnitCount(it) }}</span>
                </abc-tooltip>
            </div>
            <abc-table-cell v-else>
                <abc-tooltip
                    :title="trData.applyNumber"
                    style="width: 100%; height: 39px; overflow: hidden; line-height: 39px;"
                    class="ellipsis"
                    :content="countTipContent(trData)"
                    :disabled="!countTipContent(trData)"
                    :max-width="190"
                >
                    <span>{{ trData.applyNumber }}{{ trData.unit }}</span>
                </abc-tooltip>
            </abc-table-cell>
        </template>
        <template #step="{ trData }">
            <abc-table-cell>
                <span class="ellipsis" :title="trData.step">
                    {{ trData.step }}
                </span>
            </abc-table-cell>
        </template>
        <template #pharmacyName="{ trData }">
            <abc-table-cell>
                <span class="ellipsis" :title="trData.pharmacyName">
                    {{ trData.status === statusEnum.NO_CHARGE_STOCK ? '自备' : trData.pharmacyName }}
                </span>
            </abc-table-cell>
        </template>
        <template #returnNumber="{ trData }">
            <div v-if="trData.isChinese" class="hospital-nurse-medicine-apply-custom-cell-wrapper">
                <div
                    v-for="(it, num) in trData.returnNumber"
                    :key="num"
                    :title="getUnitReturnCount(it)"
                    class="hospital-nurse-medicine-apply-custom-cell ellipsis"
                >
                    {{ getUnitReturnCount(it) }}
                </div>
            </div>
            <abc-table-cell v-else>
                <div
                    :title="trData.returnNumber"
                    style="width: 100%; height: 39px; overflow: hidden; line-height: 39px;"
                    class="ellipsis"
                >
                    {{ trData.returnNumber }}{{ trData.unit }}
                </div>
            </abc-table-cell>
        </template>
        <template #currentGoodsCount="{ trData }">
            <abc-form-item class="hospital-nurse-medicine-apply-dialog-table-current-goods-count">
                <abc-input
                    v-model="trData.currentGoodsCount"
                    type="number"
                    :config="{
                        supportZero: false,
                        max: trData.max,
                    }"
                    :width="79"
                    placeholder="请输入"
                    @blur="handleCurrentGoodsCountBlur(trData)"
                >
                    <span slot="appendInner">{{ trData.isChinese ? '剂' : trData.unit }}</span>
                </abc-input>
            </abc-form-item>
        </template>
        <template #remark="{ trData }">
            <abc-form-item class="hospital-nurse-medicine-apply-dialog-table-remark">
                <abc-input v-model="trData.remark" @blur="handleRemarkBlur(trData)"></abc-input>
            </abc-form-item>
        </template>
    </abc-table>
</template>

<script>
    import { formatDate } from '@abc/utils-date';
    import TableConfig from './index';
    import { dispenseOrderFormItemTypeEnum } from '@/views-hospital/pharmacy/utils/constant';
    import { autoDispensingSourceTypeEnumStr } from '@/views-hospital/medicine-apply/utils/constant';
    import {
        MedicalAdviceStatusEnum,
        MedicalAdviceStatusStr,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import { isNotNull } from '@/utils';
    import { mapGetters } from 'vuex';

    export default {
        name: 'TableNurseMedicineApplyDialogTable',
        props: {
            dataList: {
                type: Array,
                default: () => ([]),
            },
            loading: {
                type: Boolean,
                default: false,
            },
            isSendApply: {
                type: Boolean,
                default: false,
            },
            isReturnApply: {
                type: Boolean,
                default: false,
            },
            isReSendApply: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            const statusEnum = {
                WAIT_SEND: dispenseOrderFormItemTypeEnum.WAITING,
                SEND_FINISH: dispenseOrderFormItemTypeEnum.DISPENSED,
                NO_CHARGE_STOCK: dispenseOrderFormItemTypeEnum.NO_CHARGE_STOCK,
                UNDISPENSED: dispenseOrderFormItemTypeEnum.UNDISPENSED,
            };
            return {
                autoDispensingSourceTypeEnumStr,
                MedicalAdviceStatusStr,
                MedicalAdviceStatusEnum,
                statusEnum,
                disabledItemList: [MedicalAdviceStatusEnum.INIT, MedicalAdviceStatusEnum.UNDONE],
            };
        },
        computed: {
            ...mapGetters([
                'chainBasic',
            ]),
            enableEqCoefficient() {
                return this.chainBasic.enableEqCoefficient;
            },
            renderConfig() {
                return TableConfig.getRenderConfig(this.isSendApply, this.isReSendApply);
            },
        },
        methods: {
            formatDate,
            isNotNull,

            // 因为筛选会记住上一次的状态；所以取消form校验，当值为空的时候单独处理
            handleCurrentGoodsCountBlur(item) {
                if (!item.currentGoodsCount) {
                    item.currentGoodsCount = item.oriCurrentGoodsCount;
                }

                if (item.checked) {
                    this.$emit('updateCheckedData', 'currentGoodsCount', item);
                }
            },


            handleRemarkBlur(item) {
                if (item.checked) {
                    this.$emit('updateCheckedData', 'remark', item);
                }
            },

            getName(item) {
                return item?.productInfo?.medicineCadn || item?.productInfo?.displayName || '';
            },
            getManufacturer(item) {
                return item?.productInfo?.manufacturer || item?.productInfo?.manufacturer || '';
            },
            getSpec(item) {
                return item?.productInfo?.displaySpec || '';
            },
            getUnitCount(item) {
                const {
                    unit, productSubType, productType, unitCount, totalCount, doseCount,
                } = item;
                let totalNumber = `${totalCount}${unit}`;
                if (productSubType === 2 && productType === 1) {
                    totalNumber = `${doseCount}*${unitCount}${unit}`;
                }
                return totalNumber;
            },
            getUnitReturnCount(item) {
                const {
                    unit, productSubType, productType, unitCount, useCount,
                } = item;
                let totalNumber = `${useCount || 1}${unit}`;
                if (productSubType === 2 && productType === 1) {
                    totalNumber = `${unitCount}${unit}*${useCount || 1}`;
                }
                return totalNumber;
            },
            disabledItemFunc(item) {
                return !!item.disabledChecked || item.status === this.statusEnum.NO_CHARGE_STOCK || (this.isReSendApply && item.sourceSheetType === 60);
            },
            countTipContent(item) {
                if (!this.enableEqCoefficient) return '';
                const {
                    eqCoefficient,
                    eqUnitCount,
                    unitCount,
                    unit,
                } = item;
                if (eqCoefficient && eqUnitCount) {
                    return `换算当量1:${eqCoefficient}，饮片等效${eqUnitCount}${unit}/颗粒实际${unitCount}${unit}`;
                }
                return '';
            },
        },
    };
</script>

<style lang="scss">
.hospital-nurse-medicine-apply-dialog-table {
    .abc-table-tr.show-hover-tr-bg:hover {
        input {
            border-bottom: 1px solid var(--abc-color-B6);
        }
    }

    input {
        background-color: transparent;
    }

    .hospital-nurse-medicine-apply-custom-cell-wrapper {
        display: inline-block;
        display: flex;
        flex-direction: column;
        width: 100%;

        .hospital-nurse-medicine-apply-custom-cell {
            box-sizing: border-box;
            width: 100%;
            height: 40px;
            padding: 0 var(--abc-paddingLR-m);
            overflow: hidden;
            line-height: 40px;
        }

        .hospital-nurse-medicine-apply-custom-cell + .hospital-nurse-medicine-apply-custom-cell {
            border-top: 1px solid var(--abc-color-card-divider-color);
        }
    }
}

.hospital-nurse-medicine-apply-dialog-table-tr {
    position: relative;
}

.hospital-nurse-medicine-apply-dialog-table-group-line {
    position: absolute;
    top: 20px;
    right: 4px;
    z-index: 9;
    display: block;
    width: 7px;
    content: '';
    border-top: 2px solid #8f8f8f;
    border-right: 2px solid #8f8f8f;
    border-bottom: 2px solid #8f8f8f;
}
</style>
