<template>
    <abc-form
        ref="antimicrobial-form"
        class="hospital-antimicrobial-drug-classification-restriction-table-wrapper"
        item-block
    >
        <abc-table
            class="hospital-antimicrobial-drug-classification-restriction-table"
            :render-config="renderConfig"
            :data-list="dataList"
            :loading="loading"
            show-content-empty
            type="excel"
            :show-hover-tr-bg="false"
            :tr-click-trigger-checked="false"
            :need-selected="false"
            :show-checked="false"
        >
            <template #availableTitles="{ trData: item }">
                <abc-form-item required>
                    <abc-select
                        v-model="item.availableTitles"
                        multiple
                        multi-label-mode="tag"
                        size="medium"
                        width="100%"
                        :inner-width="447"
                    >
                        <abc-option
                            v-for="it in titleOptions"
                            :key="it.value"
                            :value="it.name"
                            :label="it.shortName"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
            </template>
            <template #detail="{ trData: item }">
                <abc-table-cell>
                    <abc-button variant="text" @click="searchEmployeeListByPractice(item.availableTitles)">
                        详情
                    </abc-button>
                </abc-table-cell>
            </template>
            <template #availableBusiness="{ trData: item }">
                <abc-form-item required>
                    <abc-select
                        v-model="item.availableBusiness"
                        multiple
                        multi-label-mode="tag"
                        size="medium"
                        width="100%"
                        :inner-width="200"
                    >
                        <template v-for="it in businessOptions">
                            <abc-option
                                v-if="item.restrictedLevel === RestrictedLevel.SPECIAL_USE && it.value === RestrictedLevelBusiness.OUTPATIENT"
                                :key="it.value"
                                :value="it.value"
                                :label="`${it.label}(手术)`"
                            ></abc-option>
                            <abc-option
                                v-else
                                :key="it.value"
                                :value="it.value"
                                :label="it.label"
                            ></abc-option>
                        </template>
                    </abc-select>
                </abc-form-item>
            </template>
        </abc-table>
    </abc-form>
</template>

<script>
    import TableConfig from './index.js';
    import PracticeEmployeeListDialog
        from 'views/settings/antimicrobial-management/components/practice-employee-list-dialog';
    import { getEmployeeListByPractice } from 'views/outpatient/utils';
    import {
        RestrictedLevel, RestrictedLevelBusiness,
    } from 'views/settings/antimicrobial-management/common/contants';

    export default {
        name: 'AntimicrobialDrugClassificationRestrictionProTable',
        props: {
            dataList: {
                type: Array,
                default: () => [],
            },
            loading: {
                type: Boolean,
                default: false,
            },
            titleOptions: {
                type: Array,
                default() {
                    return [];
                },
            },
            businessOptions: {
                type: Array,
                default() {
                    return [];
                },
            },
        },
        data() {
            return {
                RestrictedLevel,
                RestrictedLevelBusiness,
            };
        },
        computed: {
            renderConfig() {
                return TableConfig.getRenderConfig();
            },
        },
        methods: {
            searchEmployeeListByPractice(availableTitles) {
                if (!availableTitles || !availableTitles.length) return;
                const allEmployeeList = this.$store.getters.employeeListByPractice;
                const list = getEmployeeListByPractice(allEmployeeList, availableTitles);
                if (!list.length) {
                    this.$Toast.error('该职称下没有对应的人员');
                    return;
                }
                new PracticeEmployeeListDialog({
                    list, availableTitles,
                }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .hospital-antimicrobial-drug-classification-restriction-table-wrapper {
        position: relative;
    }
</style>

