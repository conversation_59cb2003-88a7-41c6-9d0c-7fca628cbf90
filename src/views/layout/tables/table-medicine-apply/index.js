/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable vue/max-len */
import BaseProTable from '@/views/layout/tables/base-pro-table.js';
import { TreatmentTypeEnum } from '@/views-hospital/medical-prescription/utils/constants';
import { dispenseOrderFormItemTypeEnum } from '@/views-hospital/pharmacy/utils/constant';
import { parseTime } from '@/utils';
import { PriceType } from 'views/common/inventory/constants';
import {
    AdviceTagEnum,
} from '@/views-hospital/medical-prescription/utils/constants.js';
import {
    isChineseGoods, getRequirement, getProcessUsageInfo, getDispensingFormItemInfo,
} from '@/views-hospital/medicine-apply/utils/handle-medicinal-list.js';
import { getApp } from '@/core';

const getCountTipContent = (item) => {
    const { chainBasic } = getApp().store.getters;
    if (!chainBasic.enableEqCoefficient) return '';
    const {
        eqCoefficient,
        eqUnitCount,
        unitCount,
        unit,
    } = item;
    if (eqCoefficient && eqUnitCount) {
        return `换算当量1:${eqCoefficient}，饮片等效${eqUnitCount}${unit}/颗粒实际${unitCount}${unit}`;
    }
    return '';
};

export default class TableMedicineApplyTable extends BaseProTable {
    name = 'TableMedicineApplyTable';
    static staticReturnConfig = {
        hasInnerBorder: true,
        list: [{
            'label': ' ',
            'isCheckbox': true,
            'pinned': 'left',
            'style': {
                'width': '40px',
                'maxWidth': '40px',
                'minWidth': '40px',
                'textAlign': 'left',
            },
        },{
            'key': 'bed',
            'label': '床位',
            'testValue': '01',
            'isCheckbox': false,
            'pinned': 'left',
            'style': {
                'width': '44px',
                'maxWidth': '44px',
                'minWidth': '44px',
                'textAlign': 'left',
            },
        },{
            'key': 'patient',
            'label': '患者',
            'testValue': '次仁卓玛',
            'isCheckbox': false,
            'pinned': 'left',
            'style': {
                'width': '76px',
                'maxWidth': '76px',
                'minWidth': '76px',
                'textAlign': 'left',
            },
        },
        {
            'key': 'dispensingTime',
            'label': '退药时间',
            'testValue': '02-24 19:08',
            'isCheckbox': false,
            'colType': 'time',
            'style': {
                'width': '100px',
                'maxWidth': '100px',
                'minWidth': '100px',
                'textAlign': 'left',
            },
        },
        {
            'key': 'status',
            'label': '状态',
            'testValue': '已发',
            'isCheckbox': false,
            'style': {
                'width': '76px',
                'maxWidth': '76px',
                'minWidth': '76px',
                'textAlign': 'left',
            },
        },{
            'key': 'medicinalName',
            'label': '药品名称',
            'testValue': '阿奇霉素颗粒 0.1g*6袋/盒',
            'isCheckbox': false,
            'style': {
                'flex': 3,
                'width': 0,
                'minWidth': '300px',
                'textAlign': 'left',
            },
        },{
            'key': 'manufacturer',
            'label': '厂家',
            'testValue': '四川仁和药业',
            'isCheckbox': false,
            'colType': 'text',
            'style': {
                'flex': 1,
                'width': 0,
                'minWidth': '100px',
                'textAlign': 'left',
            },
        },{
            'key': 'applyNumber',
            'label': '申请数量',
            'testValue': '222',
            'isCheckbox': false,
            'style': {
                'width': '80px',
                'maxWidth': '80px',
                'minWidth': '80px',
                'textAlign': 'right',
            },
        },{
            'key': 'dispensingReturnNumberFinish',
            'label': '实退数量',
            'testValue': '222',
            'isCheckbox': false,
            'style': {
                'width': '80px',
                'maxWidth': '80px',
                'minWidth': '80px',
                'textAlign': 'right',
            },
        },{
            'key': 'applyReason',
            'label': '申请原因',
            'testValue': '患者自备',
            'isCheckbox': false,
            'colType': 'text',
            'style': {
                'width': '90px',
                'maxWidth': '90px',
                'minWidth': '90px',
                'textAlign': 'left',
            },
        },{
            'key': 'way',
            'label': '用法',
            'testValue': '静脉注射',
            'isCheckbox': false,
            'style': {
                'width': '76px',
                'maxWidth': '76px',
                'minWidth': '76px',
                'textAlign': 'left',
            },
        },{
            'key': 'step',
            'label': '频次',
            'testValue': 'q12d',
            'isCheckbox': false,
            'style': {
                'width': '52px',
                'maxWidth': '52px',
                'minWidth': '52px',
                'textAlign': 'left',
            },
        },
        {
            'key': 'sourceSheetType',
            'label': '来源',
            'testValue': '医嘱',
            'isCheckbox': false,
            'style': {
                'width': '48px',
                'maxWidth': '48px',
                'minWidth': '48px',
                'textAlign': 'left',
            },
        },
        {
            'key': 'adviceCreatedTime',
            'label': '下达时间',
            'testValue': '02-24 19:08',
            'isCheckbox': false,
            'colType': 'time',
            'style': {
                'width': '100px',
                'maxWidth': '100px',
                'minWidth': '100px',
                'textAlign': 'left',
            },
        },
        {
            'key': 'planExecuteTime',
            'label': '执行/记账时间',
            'testValue': '02-24 19:08',
            'isCheckbox': false,
            'colType': 'text',
            'style': {
                'width': '106px',
                'maxWidth': '106px',
                'minWidth': '106px',
                'textAlign': 'left',
            },
        },
        {
            'key': 'requirement',
            'label': '备注',
            'testValue': '备注信息文字内容',
            'isCheckbox': false,
            'style': {
                'width': '90px',
                'maxWidth': '90px',
                'minWidth': '90px',
                'textAlign': 'left',
            },
        },{
            'key': 'applyRemark',
            'label': '拒退原因',
            'testValue': '患者自备',
            'isCheckbox': false,
            'colType': 'text',
            'style': {
                'width': '90px',
                'maxWidth': '90px',
                'minWidth': '90px',
                'textAlign': 'left',
            },
        }],
    };
    // 由产品.设计提供的静态配置, 开发只能修改key
    static staticConfig = {
        hasInnerBorder: true,
        list: [
            {
                'label': ' ',
                'isCheckbox': true,
                'pinned': 'left',
                'style': {
                    'width': '40px',
                    'maxWidth': '40px',
                    'minWidth': '40px',
                    'textAlign': 'left',
                },
            },{
                'key': 'bed',
                'label': '床位',
                'testValue': '01',
                'isCheckbox': false,
                'pinned': 'left',
                'style': {
                    'width': '44px',
                    'maxWidth': '44px',
                    'minWidth': '44px',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'patient',
                'label': '患者',
                'testValue': '次仁卓玛',
                'isCheckbox': false,
                'pinned': 'left',
                'style': {
                    'width': '76px',
                    'maxWidth': '76px',
                    'minWidth': '76px',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'dispensingTime',
                'label': '发药时间',
                'testValue': '02-24 19:08',
                'isCheckbox': false,
                'colType': 'time',
                'style': {
                    'width': '100px',
                    'maxWidth': '100px',
                    'minWidth': '100px',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'status',
                'label': '状态',
                'testValue': '已发',
                'isCheckbox': false,
                'style': {
                    'width': '76px',
                    'maxWidth': '76px',
                    'minWidth': '76px',
                    'textAlign': 'left',
                },
            },{
                'key': 'medicinalName',
                'label': '药品名称',
                'testValue': '阿奇霉素颗粒 0.1g*6袋/盒',
                'isCheckbox': false,
                'style': {
                    'flex': 3,
                    'width': 0,
                    'minWidth': '300px',
                    'textAlign': 'left',
                },
            },{
                'key': 'manufacturer',
                'label': '厂家',
                'testValue': '四川仁和药业',
                'isCheckbox': false,
                'colType': 'text',
                'style': {
                    'flex': 1,
                    'width': 0,
                    'minWidth': '116px',
                    'textAlign': 'left',
                },
            },{
                'key': 'dispensingNumber',
                'label': '申请数量',
                'testValue': '222',
                'isCheckbox': false,
                'style': {
                    'width': '82px',
                    'maxWidth': '82px',
                    'minWidth': '82px',
                    'textAlign': 'right',
                },
            },{
                'key': 'dispensingNumberFinish',
                'label': '发药数量',
                'testValue': '222',
                'isCheckbox': false,
                'style': {
                    'width': '82px',
                    'maxWidth': '82px',
                    'minWidth': '82px',
                    'textAlign': 'right',
                },
            },{
                'key': 'way',
                'label': '用法',
                'testValue': '静脉注射',
                'isCheckbox': false,
                'style': {
                    'width': '76px',
                    'maxWidth': '76px',
                    'minWidth': '76px',
                    'textAlign': 'left',
                },
            },{
                'key': 'step',
                'label': '频次',
                'testValue': 'q12d',
                'isCheckbox': false,
                'style': {
                    'width': '58px',
                    'maxWidth': '58px',
                    'minWidth': '58px',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'sourceSheetType',
                'label': '来源',
                'testValue': '医嘱',
                'isCheckbox': false,
                'style': {
                    'width': '48px',
                    'maxWidth': '48px',
                    'minWidth': '48px',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'adviceCreatedTime',
                'label': '下达时间',
                'testValue': '02-24 19:08',
                'isCheckbox': false,
                'colType': 'time',
                'style': {
                    'width': '100px',
                    'maxWidth': '100px',
                    'minWidth': '100px',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'planExecuteTime',
                'label': '执行/记账时间',
                'testValue': '02-24 19:08',
                'isCheckbox': false,
                'colType': 'text',
                'style': {
                    'width': '106px',
                    'maxWidth': '106px',
                    'minWidth': '106px',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'requirement',
                'label': '备注',
                'testValue': '备注信息文字内容',
                'isCheckbox': false,
                'style': {
                    'width': '100px',
                    'maxWidth': '100px',
                    'minWidth': '100px',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'applyDispenseRemark',
                'label': '拒发原因',
                'testValue': '',
                'isCheckbox': false,
                'colType': 'text',
                'style': {
                    'width': '100px',
                    'maxWidth': '100px',
                    'minWidth': '100px',
                    'textAlign': 'left',
                },
            }],
    };

    static summaryRenderKeys = [];
    static getStatusName(status) {
        let statusName = '';
        let color = '#333333';
        switch (status) {
            case dispenseOrderFormItemTypeEnum.WAITING:
                color = '#333333';
                statusName = '待发';
                break;
            case dispenseOrderFormItemTypeEnum.DISPENSED:
                color = '#7A8794';
                statusName = '已发';
                break;

            case dispenseOrderFormItemTypeEnum.STOCK_NOT_DISPENSE:
                color = '#7A8794';
                statusName = '无需发';
                break;
            case dispenseOrderFormItemTypeEnum.UNDISPENSED:
                color = '#7A8794';
                statusName = '已退';
                break;
            case dispenseOrderFormItemTypeEnum.CLOSE:
                color = '#FF3333';
                statusName = '拒发';
                break;
            case dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE:
                color = '#333333';
                statusName = '待退';
                break;
            case dispenseOrderFormItemTypeEnum.APPLY_DISPENSE_REJECT:
                color = '#FF3333';
                statusName = '拒发';
                break;
            case dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT:
                color = '#FF3333';
                statusName = '拒退';
                break;
            case dispenseOrderFormItemTypeEnum.RESET_DISPENSED:
                color = '#FF3333';
                statusName = '重发';
                break;
            case dispenseOrderFormItemTypeEnum.RESET_UNDISPENSED:
                color = '#FF3333';
                statusName = '重退';
                break;
            default:
                break;
        }
        return {
            statusName,
            color,
        };
    }
    static getItemBatches(batches) {
        return (batches || []).map((x) => {
            const { batchInfo } = x;
            const { expiryDate } = batchInfo;
            return {
                ...x,
                expiryDate,
            };
        });
    }

    static getTotalNumber(item) {
        const needShowTotal = (item.status !== dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE && item.status !== dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT);
        const {
            unit, productSubType, productType, unitCount, totalCount, doseCount,
        } = item;
        let totalNumber = `${needShowTotal ? totalCount : 0}${unit}`;
        if (productSubType === 2 && productType === 1) {
            totalNumber = `${unitCount}${unit}*${needShowTotal ? doseCount : 0}剂`;
        }
        return {
            needShowTotal,
            totalNumber,
        };
    }

    static showBatches(item) {
        const {
            productInfo,
            dispensingFormItemBatches,
        } = item;
        const { priceType } = productInfo;
        // 固定售价
        if (priceType === PriceType.PRICE) return false;
        // 没有批次信息
        if (!dispensingFormItemBatches || dispensingFormItemBatches.length === 0) return false;
        return true;
    }

    static getCountRenderH(h, item) {
        const {
            unit,
            productSubType,
            productType,
            unitCount,
            totalCount,
            doseCount,
        } = item;
        const child = [];

        let totalNumber = `${totalCount}${unit}`;
        if (productSubType === 2 && productType === 1) {
            child.push(h('span',{},`${totalNumber}`));
            totalNumber = `${unitCount}${unit}*${doseCount}`;
            child.push(h('span',{
                class: 'gray small-font', style: { marginLeft: '4px' },
            },`${totalNumber}`));
        } else {
            child.push(h('span',{},`${totalNumber}`));
        }
        return child;
    }

    static getRenderConfig(currentTab = 1) {
        if (currentTab === 1) {
            return {
                ...this.staticConfig,
                list: this.staticConfig.list.map((config) => {
                    if (config.key === 'bed') {
                        config.dataFormatter = (val) => {
                            return val < 10 ? `0${val}` : val;
                        };
                    } else if (config.key === 'requirement') {
                        config.customRender = (h, val) => {
                            return h(
                                'div',
                                {
                                    class: 'custom-medicine-apply-td',
                                    style: {
                                        overflow: 'hidden',
                                    },
                                },
                                [

                                    h(
                                        'div',
                                        {
                                            class: 'custom-medicine-apply-td-content-item ellipsis',
                                        },
                                        [
                                            val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.SUPPLEMENT;}) ?
                                                h('span', { class: 'advice-tags warn' }, '补') :
                                                '',
                                            val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.URGENT;}) ?
                                                h('span',{ class: 'advice-tags danger' },'急') :
                                                '',
                                            val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.OPERATE_ING;}) ?
                                                h('span',{
                                                    class: 'advice-tags sign', style: { 'min-width': '30px' },
                                                },'术中') :
                                                '',
                                            val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.OPERATE_AFTER;}) ?
                                                h('span',{
                                                    class: 'advice-tags sign', style: { 'min-width': '30px' },
                                                },'术后') :
                                                '',
                                            val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.JING_1;}) ?
                                                h('span',{
                                                    class: 'advice-tags sign', style: { 'min-width': '30px' },
                                                },'精一') :
                                                '',
                                            val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.JING_2;}) ?
                                                h('span',{
                                                    class: 'advice-tags sign', style: { 'min-width': '30px' },
                                                },'精二') :
                                                '',
                                            val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.DU;}) ?
                                                h('span',{ class: 'advice-tags sign' },'毒') :
                                                '',
                                            val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.MA_ZUI;}) ?
                                                h('span',{ class: 'advice-tags sign' },'麻') :
                                                '',
                                            h('span',{},`${val?.requirement || ''}`),
                                        ],
                                    ),
                                ],
                            );
                        };
                    } else if (config.key === 'planExecuteTime') {
                        config.customRender = (h, val) => {
                            const {
                                planExecuteTime,
                            } = val;
                            return (<div class="table-cell" style="width: 100%; line-height: 32px;">
                                { planExecuteTime ? parseTime(planExecuteTime, 'm-d h:i', true) : ''}
                            </div>);
                        };
                    } else if (config.key === 'status') {
                        config.customRender = (h, val) => {
                            // 判断中药和西药
                            const dispensingFormItems = val?.dispensingForms[0]?.dispensingFormItems || [];
                            // 如果是中药
                            const isChinese = isChineseGoods(dispensingFormItems[0]);
                            const processUsageInfo = getProcessUsageInfo(val);
                            const requirement = getRequirement(val);
                            const dispensingFormItemInfo = getDispensingFormItemInfo(val.dispensingFormItem);

                            const newItem = {
                                processUsageInfo: `${processUsageInfo}${requirement}`, dispensingFormItemInfo,
                            };

                            const { status } = val;
                            const {
                                statusName, color,
                            } = this.getStatusName(status);
                            const styleColor = `color: ${color};`;


                            if (!isChinese) {
                                return (
                                    <div class="table-cell" style={styleColor}>
                                        {statusName}
                                    </div>
                                );
                            }


                            return h('div', {
                                class: 'custom-medicine-apply-td chinese',
                            }, [
                                ...(dispensingFormItems || []).filter((item) => {
                                    return item.status !== dispenseOrderFormItemTypeEnum.UNDISPENSED && item.status !== dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE;
                                })?.map((o) => {
                                    const { status } = o;
                                    const {
                                        statusName, color,
                                    } = this.getStatusName(status);

                                    return h('div', {
                                        class: 'custom-medicine-apply-td-content-item ellipsis',
                                    }, [
                                        h('span', {
                                            style: {
                                                color,
                                            },
                                        },`${statusName}`),
                                    ]);
                                }),
                                ...(isChinese && requirement ? [
                                    h('div', {
                                        class: 'custom-medicine-apply-td-content-item ellipsis chinese',
                                        style: {
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                        },
                                    }, [
                                        h('span', {}, `${newItem.processUsageInfo}`),
                                        h('span', {
                                            style: {
                                                marginLeft: '58px',
                                                color: '#8d9aa8',
                                            },
                                        }, `${newItem.dispensingFormItemInfo}`),
                                    ]),
                                ] : []),
                            ]);
                        };
                    } else if (config.key === 'medicinalName') {
                        config.customRender = (h, val) => {
                            const showGroupLine = val.groupIds && val.groupIds.length > 1;

                            return h('div', {
                                class: 'custom-medicine-apply-td',
                            }, [
                                ...(val.medicinalName || []).filter((item) => {
                                    return item.status !== dispenseOrderFormItemTypeEnum.UNDISPENSED && item.status !== dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE;
                                })?.map((item) => {
                                    const displaySpec = item?.productInfo?.displaySpec;
                                    const usage = item.usageInfo?.specialRequirement || '';

                                    return h('div', {
                                        class: 'custom-medicine-apply-td-content-item ellipsis',
                                    }, [
                                        h('span', {}, `${item?.productInfo?.medicineCadn || item?.productInfo?.displayName || item?.name}`),
                                        h('span', {
                                            style: {
                                                color: '#7a8794',
                                                marginLeft: '8px',
                                            },
                                        }, displaySpec),
                                        h('span', {
                                            style: {
                                                color: '#7a8794',
                                                marginLeft: '8px',
                                                fontSize: '10px',
                                            },
                                        }, usage),
                                        showGroupLine ?
                                            h('div',{
                                                class: 'group-line',
                                                style: {
                                                    height: `${((val?.groupIds.length - 1) * 40)}px`,
                                                    right: '10px',
                                                },
                                            }) : '',
                                        h('span', {
                                            style: {
                                                color: '#1ec761',
                                                marginLeft: '8px',
                                            },
                                        }, item?.sourceItemType === TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE ? '出' : ''),
                                    ]);
                                }),
                            ]);
                        };
                    } else if (config.key === 'manufacturer') {
                        config.customRender = (h, val) => {
                            return h('div', {
                                class: 'custom-medicine-apply-td ellipsis',
                            }, val.manufacturer?.filter((item) => {
                                return item.status !== dispenseOrderFormItemTypeEnum.UNDISPENSED && item.status !== dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE;
                            })?.map((item) => {
                                return h('div',{
                                    class: 'custom-medicine-apply-td-content-item ellipsis',
                                    title: item?.productInfo?.manufacturer || '暂无厂家',
                                }, [h('span',{},`${item?.productInfo?.manufacturer || '暂无厂家'}`),
                                ]);
                            }));
                        };
                    } else if (config.key === 'way') {
                        config.dataFormatter = (val) => {
                            return val?.usage || '';
                        };
                    } else if (config.key === 'step') {
                        config.dataFormatter = (val) => {
                            return val?.freq || '';
                        };
                    } else if (config.key === 'dispensingNumber') {
                        config.customRender = (h, val) => {
                            return h('div', {
                                class: 'custom-medicine-apply-td',
                            }, val.dispensingNumber?.filter((item) => {
                                return item.status !== dispenseOrderFormItemTypeEnum.UNDISPENSED && item.status !== dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE;
                            })?.map((item) => {
                                return h('abc-tooltip', {
                                    class: 'custom-medicine-apply-td-content-item ellipsis',
                                    props: {
                                        content: getCountTipContent(item),
                                        placement: 'top',
                                        maxWidth: 190,
                                        disabled: !getCountTipContent(item),
                                    },
                                }, this.getCountRenderH(h, item));
                            }));
                        };
                    } else if (config.key === 'applyDispenseRemark') {
                        config.customRender = (h, val) => {
                            const jsx = val.applyDispenseRemark?.filter((item) => {
                                return item.status === dispenseOrderFormItemTypeEnum.APPLY_DISPENSE_REJECT;
                            })?.map((item) => {
                                const {
                                    usageInfo,
                                } = item;
                                return h('div',{
                                    class: 'custom-medicine-apply-td-content-item ellipsis',
                                }, [h('span',{}, usageInfo?.dispenseRemark || ''),
                                ]);
                            });

                            const c = jsx?.[0] ? [jsx[0]] : [];

                            return h('div', {
                                class: 'custom-medicine-apply-td',
                            }, c);
                        };
                    } else if (config.key === 'dispensingNumberFinish') {
                        config.customRender = (h, val) => {
                            return h('div', {
                                class: 'custom-medicine-apply-td',
                            }, val.dispensingNumberFinish?.filter((item) => {
                                return item.status !== dispenseOrderFormItemTypeEnum.UNDISPENSED && item.status !== dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE;
                            })?.map((item) => {
                                const {
                                    needShowTotal, totalNumber,
                                } = this.getTotalNumber(item);
                                return h('div',{
                                    class: 'custom-medicine-apply-td-content-item ellipsis',
                                    style: {
                                        color: !needShowTotal ? '#7a8794' : '#333333',
                                    },
                                }, [h('span',{}, totalNumber),
                                ]);
                            }));
                        };
                    } else if (config.key === 'sourceSheetType') {
                        config.customRender = (h, val) => {
                            return (
                                <div class="table-cell">
                                    { val.sourceSheetType === 60 ? '记账' : '医嘱' }
                                </div>
                            );
                        };
                    } else if (config.key === 'adviceCreatedTime') {
                        config.customRender = (h, val) => {
                            const { adviceView } = val;
                            const { created } = adviceView || {};
                            return (
                                <abc-table-cell title={created ? parseTime(created, 'm-d h:i', true) : ''}>
                                    <span>{ created ? parseTime(created, 'm-d h:i', true) : '' }</span>
                                </abc-table-cell>
                            );
                        };
                    }
                    return config;
                }),
            };
        }
        return {
            ...this.staticReturnConfig,
            list: this.staticReturnConfig.list.map((config) => {
                if (config.key === 'bed') {
                    config.dataFormatter = (val) => {
                        return val < 10 ? `0${val}` : val;
                    };
                } else if (config.key === 'status') {
                    config.customRender = (h, val) => {
                        // 判断中药和西药
                        const dispensingFormItems = val.dispensingForms?.[0]?.dispensingFormItems || [];
                        // 如果是中药
                        const isChinese = isChineseGoods(dispensingFormItems[0]);
                        const processUsageInfo = getProcessUsageInfo(val);
                        const requirement = getRequirement(val);
                        const dispensingFormItemInfo = getDispensingFormItemInfo(val.dispensingFormItem);

                        const newItem = {
                            processUsageInfo: `${processUsageInfo}${requirement}`, dispensingFormItemInfo,
                        };

                        // 西药、耗材
                        if (!isChinese) {
                            return (
                                <abc-flex vertical style={{
                                    width: '100%', minHeight: 'var(--abc-table-cell-height-default)',
                                }}>
                                    {
                                        ...dispensingFormItems.map((item, index) => {
                                            const {
                                                statusName, color,
                                            } = this.getStatusName(item.status);

                                            return (
                                                <abc-flex
                                                    align={'center'}
                                                    style={[
                                                        {
                                                            width: '100%',
                                                            height: 'var(--abc-table-cell-height-default)',
                                                            padding: '0 var(--abc-table-cell-padding-default)',
                                                            color,
                                                        },
                                                        index !== 0 ? { borderTop: '1px solid var(--abc-color-P6)' } : {},
                                                    ]}
                                                    class="ellipsis"
                                                    title={statusName || ''}
                                                >
                                                    { statusName || '' }
                                                </abc-flex>
                                            );
                                        })
                                    }
                                </abc-flex>
                            );
                        }

                        // 中药
                        return h('div', {
                            class: 'custom-medicine-apply-td chinese',
                        }, [
                            ...(dispensingFormItems || []).filter((item) => {
                                return item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE || item.status === dispenseOrderFormItemTypeEnum.UNDISPENSED || item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT;
                            })?.map((o) => {
                                const { status } = o;
                                const {
                                    statusName, color,
                                } = this.getStatusName(status);

                                return h('div', {
                                    class: 'custom-medicine-apply-td-content-item ellipsis',
                                }, [
                                    h('span', {
                                        style: {
                                            color,
                                        },
                                    },`${statusName}`),
                                ]);
                            }),
                            ...(isChinese && requirement ? [
                                h('div', {
                                    class: 'custom-medicine-apply-td-content-item ellipsis chinese',
                                    style: {
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                    },
                                }, [
                                    h('span', {}, `${newItem.processUsageInfo}`),
                                    h('span', {
                                        style: {
                                            marginLeft: '58px',
                                            color: '#8d9aa8',
                                        },
                                    }, `${newItem.dispensingFormItemInfo}`),
                                ]),
                            ] : []),
                        ]);
                    };
                } else if (config.key === 'planExecuteTime') {
                    config.customRender = (h, val) => {
                        const {
                            planExecuteTime,
                        } = val;
                        return (<div class="table-cell" style="width: 100%; line-height: 32px;">
                            { planExecuteTime ? parseTime(planExecuteTime, 'm-d h:i', true) : ''}
                        </div>);
                    };
                } else if (config.key === 'medicinalName') {
                    config.customRender = (h, val) => {
                        const showGroupLine = val.groupIds && val.groupIds.length > 1;

                        return h('div', {
                            class: 'custom-medicine-apply-td',
                        }, [
                            ...(val.medicinalName || []).filter((item) => {
                                return item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE || item.status === dispenseOrderFormItemTypeEnum.UNDISPENSED || item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT;
                            })?.map((item) => {
                                const name = item?.productInfo?.medicineCadn || item?.productInfo?.displayName || item?.name || '';
                                const displaySpec = item?.productInfo?.displaySpec;
                                const usage = item.usageInfo?.specialRequirement || '';

                                return h('div', {
                                    class: 'custom-medicine-apply-td-content-item ellipsis',
                                }, [
                                    h('span', {
                                        class: 'ellipsis',
                                        attrs: {
                                            title: name,
                                        },
                                    }, `${name}`),
                                    h('span', {
                                        class: 'ellipsis',
                                        style: {
                                            color: '#7a8794',
                                            marginLeft: '8px',
                                        },
                                        attrs: {
                                            title: displaySpec,
                                        },
                                    }, displaySpec),
                                    h('span', {
                                        class: 'ellipsis',
                                        style: {
                                            color: '#7a8794',
                                            marginLeft: '8px',
                                            fontSize: '10px',
                                        },
                                        attrs: {
                                            title: usage,
                                        },
                                    }, usage),
                                    showGroupLine ?
                                        h('div',{
                                            class: 'group-line',
                                            style: {
                                                height: `${((val?.groupIds.length - 1) * 40)}px`,
                                                right: '10px',
                                            },
                                        }) : '',
                                    h('span', {
                                        style: {
                                            color: '#1ec761',
                                            marginLeft: '8px',
                                        },
                                    }, item?.sourceItemType === TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE ? '出' : ''),
                                ]);
                            }),
                        ]);
                    };
                } else if (config.key === 'manufacturer') {
                    config.customRender = (h, val) => {
                        return h('div', {
                            class: 'custom-medicine-apply-td',
                        }, val.manufacturer?.filter((item) => {
                            return item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE || item.status === dispenseOrderFormItemTypeEnum.UNDISPENSED || item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT;
                        })?.map((item) => {
                            const manufacturer = item?.productInfo?.manufacturer || '暂无厂家';
                            return h('div',{
                                class: 'custom-medicine-apply-td-content-item ellipsis',
                                attrs: {
                                    title: manufacturer,
                                },
                            }, [h('span',{
                                class: 'ellipsis',
                            },`${manufacturer}`),
                            ]);
                        }));
                    };
                } else if (config.key === 'way') {
                    config.dataFormatter = (val) => {
                        return val?.usage || '';
                    };
                } else if (config.key === 'requirement') {
                    config.customRender = (h, val) => {
                        return h('div', {
                            class: 'custom-medicine-apply-td',
                        },[
                            h('div', {
                                class: 'custom-medicine-apply-td-content-item ellipsis',
                            }, [
                                val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.SUPPLEMENT;}) ? h('span',{
                                    class: 'advice-tags warn',
                                },'补') : '',
                                val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.URGENT;}) ? h('span',{
                                    class: 'advice-tags danger',
                                },'急') : '',
                                val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.OPERATE_ING;}) ? h('span',{
                                    class: 'advice-tags sign', style: { 'min-width': '30px' },
                                },'术中') : '',
                                val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.OPERATE_AFTER;}) ? h('span',{
                                    class: 'advice-tags sign', style: { 'min-width': '30px' },
                                },'术后') : '',
                                val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.JING_1;}) ? h('span',{
                                    class: 'advice-tags sign', style: { 'min-width': '26px' },
                                },'精1') : '',
                                val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.JING_2;}) ? h('span',{
                                    class: 'advice-tags sign', style: { 'min-width': '26px' },
                                },'精2') : '',
                                val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.MA_ZUI;}) ? h('span',{
                                    class: 'advice-tags sign',
                                },'麻') : '',
                                val.adviceTags?.find((it) => {return it.type === AdviceTagEnum.DU;}) ? h('span',{
                                    class: 'advice-tags sign',
                                },'毒') : '',

                                h('span',{},`${val?.requirement || ''}`),
                            ]),
                        ]);
                    };
                } else if (config.key === 'step') {
                    config.dataFormatter = (val) => {
                        return val?.freq || '';
                    };
                } else if (config.key === 'applyReason') {
                    config.customRender = (h, val) => {
                        const dispensingFormItems = val.dispensingForms?.[0]?.dispensingFormItems || [];
                        return (
                            <abc-flex vertical style={{
                                width: '100%', minHeight: 'var(--abc-table-cell-height-default)',
                            }}>
                                {
                                    ...dispensingFormItems.map((item, index) => {
                                        const applyDispenseRemark = [dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE, dispenseOrderFormItemTypeEnum.UNDISPENSED, dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT].includes(item.status) ?
                                            (item.usageInfo?.applyDispenseRemark || '') :
                                            '';
                                        return (
                                            <abc-flex
                                                align={'center'}
                                                style={[
                                                    {
                                                        width: '100%', height: 'var(--abc-table-cell-height-default)', padding: '0 var(--abc-table-cell-padding-default)',
                                                    },
                                                    index !== 0 ? { borderTop: '1px solid var(--abc-color-P6)' } : {},
                                                ]}
                                                class="ellipsis"
                                                title={applyDispenseRemark}
                                            >
                                                { applyDispenseRemark }
                                            </abc-flex>
                                        );
                                    })
                                }
                            </abc-flex>
                        );
                    };
                } else if (config.key === 'applyNumber') {
                    config.customRender = (h, val) => {
                        return h('div', {
                            class: 'custom-medicine-apply-td',
                        }, val.applyNumber?.filter((item) => {
                            return item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE || item.status === dispenseOrderFormItemTypeEnum.UNDISPENSED || item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT;
                        })?.map((item) => {
                            return h('div',{
                                class: 'custom-medicine-apply-td-content-item',
                            }, this.getCountRenderH(h, item));
                        }));
                    };
                } else if (config.key === 'dispensingReturnNumberFinish') {
                    config.customRender = (h, val) => {
                        return h('div', {
                            class: 'custom-medicine-apply-td',
                        }, val.dispensingNumber?.filter((item) => {
                            return item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE || item.status === dispenseOrderFormItemTypeEnum.UNDISPENSED || item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT;
                        })?.map((item) => {
                            const {
                                needShowTotal, totalNumber,
                            } = this.getTotalNumber(item);
                            return h('div',{
                                class: 'custom-medicine-apply-td-content-item',
                                style: {
                                    color: !needShowTotal ? '#7a8794' : '#333333',
                                },
                            }, [h('span',{},`${totalNumber}`),
                            ]);
                        }));
                    };
                } else if (config.key === 'dispensingTime') {
                    config.customRender = (h, val) => {
                        const dispensingFormItems = val.dispensingForms?.[0]?.dispensingFormItems || [];
                        // 判断是不是中药
                        const isChinese = isChineseGoods(dispensingFormItems[0]);

                        // 中药
                        if (isChinese) {
                            const lastModified = dispensingFormItems.find((item) => {
                                return item.status === dispenseOrderFormItemTypeEnum.UNDISPENSED;
                            })?.lastModified || '';
                            const unDispenseTime = lastModified ? parseTime(lastModified, 'm-d h:i', true) : '';
                            return (
                                <abc-table-cell class="ellipsis" title={unDispenseTime}>
                                    { unDispenseTime }
                                </abc-table-cell>
                            );
                        }

                        // 西药、耗材
                        return (
                            <abc-flex vertical style={{
                                width: '100%', minHeight: 'var(--abc-table-cell-height-default)',
                            }}>
                                {
                                    ...dispensingFormItems.map((item, index) => {
                                        const lastModified = item.status === dispenseOrderFormItemTypeEnum.UNDISPENSED ? parseTime(item.lastModified, 'm-d h:i', true) : '';
                                        return (
                                            <abc-flex
                                                align={'center'}
                                                style={[
                                                    {
                                                        width: '100%', height: 'var(--abc-table-cell-height-default)', padding: '0 var(--abc-table-cell-padding-default)',
                                                    },
                                                    index !== 0 ? { borderTop: '1px solid var(--abc-color-P6)' } : {},
                                                ]}
                                                class="ellipsis"
                                                title={lastModified}
                                            >
                                                { lastModified }
                                            </abc-flex>
                                        );
                                    })
                                }
                            </abc-flex>
                        );
                    };
                } else if (config.key === 'applyRemark') {
                    config.customRender = (h, val) => {
                        const jsx = val.applyRemark?.filter((item) => {
                            return item.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT;
                        })?.map((item) => {
                            const {
                                usageInfo,
                            } = item;
                            return h('div',{
                                class: 'custom-medicine-apply-td-content-item ellipsis',
                            }, [h('span',{}, usageInfo?.dispenseRemark || ''),
                            ]);
                        });

                        const c = jsx?.[0] ? [jsx[0]] : [];
                        return h('div', {
                            class: 'custom-medicine-apply-td',
                        }, c);
                    };
                } else if (config.key === 'sourceSheetType') {
                    config.customRender = (h, val) => {
                        return (
                            <div class="table-cell">
                                { val.sourceSheetType === 60 ? '记账' : '医嘱' }
                            </div>
                        );
                    };
                } else if (config.key === 'adviceCreatedTime') {
                    config.customRender = (h, val) => {
                        const { adviceView } = val;
                        const { created } = adviceView || {};
                        return (
                            <abc-table-cell title={created ? parseTime(created, 'm-d h:i', true) : ''}>
                                <span>{ created ? parseTime(created, 'm-d h:i', true) : '' }</span>
                            </abc-table-cell>
                        );
                    };
                }
                return config;
            }),
        };
    }
}
