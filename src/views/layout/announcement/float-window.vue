<template>
    <abc-notice-list
        class="announcement-float-window"
        :list="renderList"
        @close="handleDelete"
        @click="openDialog"
    >
        <template slot-scope="{ item }">
            <template v-if="settlementExceptionCheck(item.type)">
                {{ item.describe }}
                <abc-flex justify="end" style="margin-top: 16px;">
                    <abc-button variant="fill" theme="primary" @click="handleToSocial(item)">
                        前往处理
                    </abc-button>
                    <abc-button variant="ghost" theme="default" @click="handleReadMsg(item)">
                        稍后处理
                    </abc-button>
                </abc-flex>
            </template>
            <div v-else class="announcement-float-window-content">
                <span v-html="item.content"></span>
            </div>
        </template>
    </abc-notice-list>
</template>

<script>
    import { AnnouncementDialog } from './notice-dialog/index.js';
    import { AbcSocketMessageManager } from '@/service/message/manager.js';
    import { MessageIdEnum } from '@/service/message/constant.js';
    import { MFE_ROUTER_NAME } from 'abc-micro-frontend';
    import {
        navigateToModuleByCms,
        navigateToSocialAccountCheckingNew, removeLeadingSlash,
    } from '@/core/navigate-helper';
    import { mapGetters } from 'vuex';
    import { isNull } from '@/common/utils';

    export default {
        name: 'FloatWindow',
        props: {
            list: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MessageIdEnum,
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),
            renderList() {
                return this.list.map((item) => {
                    return {
                        originData: item,
                        title: item.messageBody?.data?.title,
                        content: item.messageBody?.data?.content,
                        describe: item.messageBody?.data?.describe,
                        type: item.messageBody?.data?.type,
                        id: item.id,
                        variant: this.settlementExceptionCheck(item.messageBody?.data?.type) ? 'outline' : undefined,
                        theme: this.settlementExceptionCheck(item.messageBody?.data?.type) ? 'warning' : undefined,
                        withDeleteIcon: !this.settlementExceptionCheck(item.messageBody?.data?.type),
                        hasLinkButton: this.settlementExceptionCheck(item.messageBody?.data?.type),
                        extendData: item.messageBody?.data?.extendData,
                    };
                });
            },
            mfeBasePath() {
                const moduleRoute = this.$router.options.routes.find((route) => route.name === MFE_ROUTER_NAME);
                return moduleRoute?.meta?.mfeBasePath || '/';
            },
        },
        created() {
            this.msgManager = AbcSocketMessageManager.getInstance();
        },
        methods: {
            navigate2Module(toPath) {
                this.$router.push(toPath, null, () => {
                    const route = this.$router.match(toPath);
                    this.$abcPlatform.notifyPlatformRouterChange(route);
                });
            },
            settlementExceptionCheck(type) {
                return [
                    MessageIdEnum.SHEBAO_DAILY_RECONCILIATION_EXCEPTION_NOTIFY,
                    MessageIdEnum.SHEBAO_SETTLEMENT_EXCEPTION_NOTIFY,
                    MessageIdEnum.TRACE_CODE_TODAY_REPORT_TIP,
                ].includes(type);
            },
            handleReadMsg(item) {
                this.readNotice(item.id);
            },
            handleToSocial(item) {
                const {
                    path,
                    module,
                    query,
                } = item.extendData || {};
                this.readNotice(item.id);

                if (!isNull(module)) {
                    navigateToModuleByCms(this.currentClinic, module, query);
                    return ;
                }

                let targetUrl = path ? path : this.$abcSocialSecurity.createCheckingPageUrl(item.extendData);
                targetUrl = removeLeadingSlash(targetUrl);

                navigateToSocialAccountCheckingNew(this.currentClinic, `/${targetUrl}`, () => {
                    this.navigate2Module(this.mfeBasePath + targetUrl);
                });
            },
            readNotice(id) {
                this.msgManager.readOneNotice(id);
            },
            handleDelete(item) {
                if (this.settlementExceptionCheck(item.type)) {
                    return;
                }
                // 标记消息已读
                // 关闭
                this.readNotice(item.id);
            },
            openDialog(item) {
                if (this.settlementExceptionCheck(item.type)) {
                    return;
                }
                this.readNotice(item.id);
                new AnnouncementDialog({
                    message: item.originData,
                }).generateDialog({ parent: this });
            },
        },
    };
</script>

<style lang="scss">
.announcement-float-window {
    .announcement-float-window-content {
        max-height: 66px;
        overflow: hidden;
    }
}
</style>
