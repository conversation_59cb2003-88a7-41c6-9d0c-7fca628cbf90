@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.prescription-table-wrapper.external-table {
    .prescription-title {
        h5 {
            margin-right: 12px;
        }

        .unit-select {
            background-color: transparent;
            outline: none;

            input {
                height: 26px;
                padding-right: 16px;
                line-height: 26px;
                cursor: pointer;
                border-radius: 16px;
            }

            .iconfont {
                right: 4px;
            }

            & + .unit-select {
                margin-left: 4px;
            }
        }
    }

    .prescription-table {
        .list-complete-leave-active {
            position: absolute;
        }

        .list-complete-move {
            transition: transform 0.15s;
        }

        .external-group:not(:first-child) {
            border-top: 1px solid $P6;
        }

        .external-item-wrapper {
            display: flex;
            align-items: center;
            padding-right: 12px;
            border-bottom: 1px solid $P6;

            .table-group-no {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 30px;
                height: 40px;
                border-right: 1px dashed $P6;

                .abc-input__inner {
                    height: 40px;
                    padding: 3px;
                    border-color: rgba(0, 0, 0, 0);
                    border-radius: 0;
                }

                .iconfont {
                    display: none;
                }
            }

            .abc-form-item {
                flex: 1;

                .abc-autocomplete-wrapper {
                    .abc-input__inner {
                        border-color: transparent;
                        border-radius: 0;
                    }
                }
            }

            .item-detail {
                display: flex;
                flex: 1;
                align-items: center;
                height: 40px;
                cursor: pointer;
                border-radius: var(--abc-border-radius-small);

                .item-index {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-width: 31px;
                    max-width: 31px;
                    height: 100%;
                    border-right: 1px dashed var(--abc-color-P6);
                }

                .item-name {
                    display: flex;
                    flex: 1;
                    align-items: center;
                    max-width: calc(100% - 12px);
                    padding: 0 6px;
                }

                .medical-fee-grade-td-wrapper + .item-name {
                    padding-left: 3px;
                }

                span:first-child {
                    font-size: 15px;
                }

                .abc-tipsy {
                    color: var(--abc-color-Y2);
                }
            }

            .unit-price {
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 84px;
                height: 40px;
                margin-left: 0;
                font-size: 14px;
                text-align: right;
                border-right: 1px dashed var(--abc-color-P6);
                border-left: 1px dashed var(--abc-color-P6);
            }

            .select-acupoint-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 70px;
                height: 40px;
                margin-left: 8px;
                color: var(--abc-color-T2);
                cursor: pointer;
                border: 1px solid var(--abc-color-P1);
                border-radius: var(--abc-border-radius-small);

                .iconfont {
                    margin-right: 8px;
                    font-size: 14px;
                    color: var(--abc-color-T2);
                }

                &:hover {
                    background-color: var(--abc-color-P5);
                }

                &:active {
                    background-color: var(--abc-color-P4);
                }
            }

            .btns {
                width: 108px;
                margin-left: auto;
            }

            .delete-group {
                display: none;
                color: var(--abc-color-R2);
            }

            .prescription-external-table-delete-button {
                visibility: hidden;
            }

            &:hover {
                .delete-group {
                    display: block;
                }

                .prescription-external-table-delete-button {
                    visibility: visible;
                }
            }
        }

        .external-acupoint-wrapper {
            padding: 12px 0 4px;
            border-bottom: 1px dashed var(--abc-color-P6);
        }

        .acupoint-item-wrapper {
            box-sizing: border-box;
            display: flex;
            flex-flow: row wrap;
            padding: 0 12px;

            .acupoint-item {
                position: relative;
                display: flex;
                width: 140px;
                margin-right: 8px;
                margin-bottom: 8px;
                background-color: #ffffff;

                .acupoint-position {
                    .abc-input__inner {
                        text-align: center;
                        border-right: 0;
                        border-radius: var(--abc-border-radius-small) 0 0 var(--abc-border-radius-small);

                        &:hover {
                            border-color: var(--abc-color-P7) !important;
                        }

                        &:focus {
                            border-color: #0270c9 !important;
                        }
                    }

                    &:hover {
                        background-color: var(--abc-color-P5);
                    }

                    &:active {
                        background-color: var(--abc-color-P4);
                    }
                }

                .acupoint-position + .abc-autocomplete-wrapper input {
                    border-radius: 0 var(--abc-border-radius-small) var(--abc-border-radius-small) 0;
                }

                input::-webkit-input-placeholder,
                input:-ms-input-placeholder,
                input::placeholder {
                    font-family: STZhongsong;
                    font-size: 14px;
                }

                .acupoint-name {
                    position: relative;
                    display: flex;
                    flex: 1;
                    align-items: center;
                    padding: 3px 6px 3px 8px;
                    font-family: STZhongsong;
                    border: 1px solid var(--abc-color-P7);
                    border-radius: 0 var(--abc-border-radius-small) var(--abc-border-radius-small) 0;

                    .delete-icon {
                        margin-left: auto;
                        visibility: hidden;
                    }

                    &:hover {
                        .delete-icon {
                            visibility: inherit;
                        }
                    }
                }
            }

            .select-acupoint-btn {
                position: absolute;
                top: 4px;
                right: 4px;
                z-index: 2;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                cursor: pointer;
                border-radius: 16px;

                .iconfont {
                    font-size: 14px;
                    color: var(--abc-color-P1);
                }

                &:hover,
                &:active {
                    background-color: var(--abc-color-P4);

                    .iconfont {
                        color: #96a4b3;
                    }
                }
            }
        }

        .external-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
            padding: 0 12px;

            .abc-input__inner {
                padding: 3px 6px;
                border-width: 0;
                border-bottom: 1px solid transparent;
                border-radius: 0;
                box-shadow: none !important;
            }

            .dose-count,
            .usages,
            .daily-dosage,
            .freq,
            .total-count,
            .usage-level {
                .abc-input__inner {
                    text-align: center;
                }
            }

            .total-count {
                display: flex;
                align-items: center;

                .abc-input__inner {
                    padding-left: 44px !important;
                    border-bottom: 1px solid var(--abc-color-P1);
                }

                .prepend-input,
                .append-inner-input {
                    width: 44px;
                    padding: 0;
                }
            }

            .abc-form-item.is-error .abc-form-item-content .abc-input__inner {
                border-bottom-width: 1px;
            }

            .dose-count .abc-input__inner {
                font-family: STZhongsong;
                font-size: 16px;
                border-bottom: 1px solid var(--abc-color-P1);
            }

            img {
                width: 41px;
                margin-right: 4px;
            }

            .abc-tag-wrapper {
                margin-right: 4px;
            }

            .input-append-unit {
                height: 28px;
            }

            .total-info {
                display: flex;
                align-items: center;
                margin-left: auto;

                > div.total-price {
                    min-width: 64px;
                    color: var(--abc-color-T2);
                    text-align: right;
                }
            }
        }
    }

    .is-disabled {
        cursor: not-allowed !important;
        background-color: var(--abc-color-bg-disabled);
        border-color: var(--abc-color-P6) !important;
    }

    .external-unit-count {
        display: flex;
        align-items: center;
        height: 100%;
        border-left: 1px dashed var(--abc-color-P6);

        span.unit {
            display: inline-block;
            width: 48px;
            overflow: hidden;
            text-align: center;
            white-space: nowrap;
        }

        .small-font {
            font-size: 12px;
        }
    }

    .input-select,
    .no-border-input {
        .abc-input__inner {
            height: 39px;
            padding: 3px 4px;
            background-color: #ffffff;
            border-color: transparent;
            border-radius: 0;
        }

        .append-input {
            min-width: 40px;
            border-color: var(--abc-color-P6);
            border-radius: 0;
        }

        &.is-disabled {
            .abc-input__inner {
                background-color: #f9fafc !important;
            }
        }
    }

    .input-select {
        .abc-input__inner {
            text-align: center;
            border-left: 1px solid rgba(0, 0, 0, 0);
        }

        .cis-icon-dropdown_triangle {
            display: none;
        }
    }
}
