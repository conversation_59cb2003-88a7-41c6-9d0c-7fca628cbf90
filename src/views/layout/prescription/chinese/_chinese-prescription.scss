@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

//中药处方样式
.prescription-table-wrapper.chinese-table {
    &.air-pharmacy {
        .prescription-header {
            background-color: #fcf7f5;
        }

        .air-pharmacy-vendors-popover-wrapper {
            width: 280px;

            .vendors-popper {
                flex: 1;

                .abc-input-wrapper {
                    width: 100%;
                }
            }
        }
    }

    .abc-form-item {
        margin-right: 0;
        margin-bottom: 0;

        &:hover,
        &:focus,
        &:active {
            z-index: 2;
        }
    }

    .operation {
        .abc-checkbox-button-wrapper {
            border-color: $P1;
        }
    }

    .prescription-title {
        h5 {
            margin-right: 12px;
        }

        .unit-select {
            margin-right: 6px;
            background-color: transparent;
            outline: none;

            input {
                border-radius: 16px;
            }

            .iconfont {
                right: 4px;
                font-size: 14px;
            }
        }
    }

    .total-weight-info {
        margin: 0 4px 0 2px;
        font-size: var(--abc-font-size-mini);
        color: var(--abc-color-T2);
    }

    .advertisement {
        padding: 4px 6px;
        font-size: 11px;
        font-weight: 400;
        color: #ff4b4b;
        background-color: transparent;
        border-radius: var(--abc-border-radius-small);

        &:hover {
            cursor: pointer;
            background-color: #ffeded;
        }
    }

    .prescription-table {
        min-height: 58px;

        .abc-autocomplete-wrapper {
            background-color: #ffffff;

            .prepend-input i {
                font-size: 12px;
                color: $P1;
            }
        }

        .table-body {
            position: relative;
        }

        .table-td {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 25%;
            height: 58px;
            padding: 0 8px 0 4px;
            vertical-align: top;
            background-color: #ffffff;

            .abc-input__inner {
                height: 24px;
                line-height: 1;
                border-width: 0;
                //border-bottom: 1px solid transparent;
                border-radius: 0;
                box-shadow: none !important;

                &:not([disabled]):not(.is-disabled):not([readonly]):not(.is-readonly):active,
                &:not([disabled]):not(.is-disabled):not([readonly]):not(.is-readonly):focus {
                    box-shadow: none !important;
                }

                .input-append-unit {
                    z-index: 2;
                    height: 24px;
                    padding: 0;
                }
            }

            &:not(.is-last-row) {
                border-bottom: 1px dashed $P6;
            }

            &:not(.is-last-col) {
                border-right: 1px dashed $P6;
            }

            &.is-last-col {
                border-right: 0;
            }

            &:hover {
                &:hover {
                    .delete-icon-wrapper {
                        visibility: inherit;
                    }
                }

                .abc-form-item-content .group-selector-wrapper {
                    visibility: visible;
                }
            }
        }

        .name-wrapper {
            position: relative;
            display: flex;
            flex: 1;
            align-items: center;
            width: 0;

            &:hover {
                .cant-select-pay-type {
                    display: none;
                }
            }

            .abc-autocomplete-wrapper {
                max-width: 168px;

                .abc-input__inner {
                    font-size: 14px;
                    -webkit-font-smoothing: initial;
                }

                input::-webkit-input-placeholder {
                    font-size: 15px;
                    color: $T3;
                }

                input:-ms-input-placeholder {
                    font-size: 15px;
                    color: $T3;
                }

                input::placeholder {
                    font-size: 15px;
                    color: $T3;
                }
            }

            .cadn {
                position: relative;
                display: flex;
                align-items: center;
                width: 100%;
                max-width: 176px;
                height: 24px;
                padding: 0 0 0 8px;
                line-height: 15px;
                cursor: pointer;
                border-width: 0;
                //border-bottom: 1px solid transparent;
                -webkit-font-smoothing: initial;

                // 不出省略号，但是不能截断字
                .name {
                    display: flex;
                    align-items: center;
                    height: 100%;
                    margin-right: 4px;
                    overflow: hidden;
                    font-size: 15px;
                    word-break: keep-all;
                    white-space: nowrap;
                }

                &.is-shortage {
                    color: $Y2;
                }

                .medical-fee-grade {
                    margin-left: auto;
                    font-size: 12px;
                    color: $T2;
                }

                .delete-icon {
                    &:hover {
                        color: #a4aeb9;
                        background-color: #d2d3d7;
                    }
                }

                .doctor-sign {
                    width: 44px;
                    min-width: 44px;
                    margin-left: auto;
                    font-size: 12px;
                    color: $T3;

                    img {
                        width: 100%;
                    }

                    &.is-float {
                        position: absolute;
                        top: -15px;
                        right: 0;
                    }
                }
            }

            .cadn-placeholder {
                width: 100%;
                padding-left: 9px;
                color: $T3;
            }

            .refund-icon-img {
                width: 30px;
                margin-left: auto;
            }

            .repeat-item {
                width: 30px;
                font-size: 12px;
                color: #ff9933;
                text-align: center;
            }
        }

        .delete-icon-wrapper {
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            visibility: hidden;

            &:hover {
                visibility: inherit;
            }
        }

        .show-index-wrapper {
            position: absolute;
            top: 3px;
            left: 12px;
            font-size: 13px;
            color: $T1;
        }

        .medical-fee-grade-td-wrapper {
            width: 26px;
            height: 30px;

            .pay-type-text {
                height: 30px;
                line-height: 30px;
            }

            &:hover {
                .abc-select-wrapper:not(.is-focus) {
                    background-color: $P4;

                    .abc-input__inner:not([disabled]):not(.is-disabled):hover {
                        border-color: transparent !important;
                    }
                }
            }
        }

        .count-wrapper {
            display: flex;
            align-items: center;
            width: 51px;
            font-size: 15px;

            .abc-input__inner {
                padding: 0 2px;
                font-family: STZhongsong;
                font-size: 15px;
                text-align: right;

                input::-webkit-input-placeholder {
                    font-size: 15px;
                    font-weight: 400;
                    color: $T3;
                }

                input:-ms-input-placeholder {
                    font-size: 15px;
                    font-weight: 400;
                    color: $T3;
                }

                input::placeholder {
                    font-size: 15px;
                    font-weight: 400;
                    color: $T3;
                }
            }

            .input-append-unit {
                width: 16px;
                font-family: STZhongsong;
                font-size: 15px;
            }
        }

        .chinese-special-requirement {
            position: absolute;
            bottom: 4px;
            left: 13px;

            .abc-form-item-content .group-selector-wrapper {
                position: relative;
                left: -1px;
                width: 60px;
                visibility: hidden;

                &.is-focus {
                    visibility: visible;
                }
            }

            .abc-input__inner {
                height: 12px;
                padding: 0;
                font-size: 12px;
                color: $T1;
                border-width: 0;
                box-shadow: none !important;
            }

            &.show-input {
                .abc-form-item-content .group-selector-wrapper {
                    visibility: visible;
                }
            }
        }

        .chinese-replace-medicine {
            position: absolute;
            bottom: 4px;
            left: 13px;
            width: calc(100% - 21px);

            .abc-form-item-content {
                overflow: hidden;
                font-size: 12px;
                color: #96a4b3;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .diff-spec {
            position: absolute;
            right: 12px;
            bottom: 2px;
            font-size: 12px;
            color: $T2;
        }

        .list-complete-leave-active {
            position: absolute;
        }
    }

    .prescription-footer {
        height: 41px;
        padding: 0 12px;

        .chinese-description {
            display: flex;
            align-items: center;

            .abc-form-item.is-error .abc-form-item-content .abc-input__inner {
                border-bottom-width: 1px;
            }

            .dose-count .abc-input__inner {
                font-size: 16px;
                border-bottom: 1px solid $P1;
            }
        }

        .chinese-description .abc-input__inner {
            padding: 3px 6px;
            border-width: 0;
            border-bottom: 1px solid transparent;
            box-shadow: none !important;
        }

        img {
            width: 41px;
            margin-right: 4px;
        }

        .abc-input__inner {
            border-radius: 0;
        }

        .dose-count,
        .usages,
        .daily-dosage,
        .freq,
        .usage-level {
            .abc-input__inner {
                text-align: center;
            }
        }

        .decoction {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 90px;
            height: 28px;
            padding-right: 12px;
            margin-left: auto;
            cursor: pointer;
            background: rgba(255, 255, 255, 1);

            i {
                margin-right: 4px;
                font-size: 14px;
                color: var(--abc-color-P3);
            }

            &.is-checked i {
                color: var(--abc-color-theme2);
            }
        }

        .pr-info-status {
            display: flex;
            align-items: center;

            img {
                margin-right: 0;
            }

            .abc-tag-wrapper {
                margin-right: 0;
            }

            .iconfont {
                margin-right: 4px;
                color: var(--abc-color-P3);
            }

            .external-price {
                font-size: 12px;
                line-height: 12px;
                text-align: left;
            }

            .total-price {
                min-width: auto;

                >span {
                    font-weight: normal;
                    color: var(--abc-color-T2);
                }
            }

            &.total-price {
                min-width: auto;
            }
        }
    }

    .prescription-external {
        //display: flex;
        //height: 41px;
        .external-info-item {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            height: 40px;
            padding: 0 12px;
            border-top: 1px solid var(--abc-color-P6);

            >div {
                display: flex;
                align-items: center;

                &.price {
                    margin-left: auto;
                    color: var(--abc-color-T2);
                }
            }

            label {
                width: 32px;
                color: var(--abc-color-T2);
            }

            .content {
                display: flex;
                align-items: center;
                height: 28px;
                padding: 0 8px;
                cursor: pointer;
                border-radius: var(--abc-border-radius-small);

                &:hover {
                    background-color: #eff3f6;
                }
            }

            .delivery-order-no {
                color: var(--abc-color-B2);
            }
        }
    }

    &:not(.is-disabled) {
        .prescription-table .abc-form-item:hover {
            cursor: pointer;

            .abc-input__inner:not(:focus) {
                cursor: pointer;
            }
        }
    }

    &:hover {
        .prescription-header {
            .operation i {
                display: inline-block;
            }

            .pr-list-length {
                display: inline-block;
            }
        }
    }
}

.abc-dialog-wrapper .abc-dialog-body .change-spec-confirm-dialog.dialog-content {
    > h5 {
        font-weight: bold;
        text-align: center;
    }

    > p {
        &:nth-child(2),
        &:last-child {
            color: var(--abc-color-T1);
        }

        &:nth-child(3) {
            margin-top: 0;

            @include ellipsis;
        }
    }
}

.chinese-autocomplete-suggestion {
    min-width: 924px;
    margin-left: -1px !important;

    .suggestion-title {
        display: flex;
        align-items: center;
        padding: 0 10px;
    }

    .abc-scrollbar-wrapper {
        overflow-y: scroll;

        .suggestions-item {
            padding: 0 0 0 10px;
        }
    }

    &.air-pharmacy {
        .suggestion-title {
            background-color: #fcf7f5;
        }
    }

    .suggestions-item > div {
        font-size: 13px;
        line-height: 18px;
        text-overflow: initial;
        white-space: nowrap;
    }

    .suggestion-title,
    .suggestions-item {
        .goods-code {
            flex: none !important;
            width: 70px;
            padding-right: 6px;
        }

        .medicine-name-group {
            display: flex;
            flex: 1;
            align-items: center;

            .abc-icon {
                margin-left: 6px;
            }

            &.support-mix {
                cursor: pointer;
            }
        }

        .medicine-cadn {
            font-size: 14px;
        }

        .alias-name {
            max-width: 68px;
            margin-left: 6px;
            font-size: 12px;
            color: var(--abc-color-T2);
        }

        .extend-spec {
            width: 70px;
        }

        .eq-coefficient {
            width: 40px;
        }

        .display-inventory {
            width: 64px;
            padding-left: 6px;
            text-align: right;

            &.coefficient {
                width: 84px;
            }
        }

        .display-price {
            width: 78px;
            padding-right: 12px;
            padding-left: 6px;
            text-align: right;

            &.coefficient {
                width: 98px;
            }
        }

        .medical-fee-grade {
            width: 100px;
            padding-left: 6px;
            text-align: left;
        }

        .medical-restriction {
            width: 150px;
        }

        .custom-type-name {
            width: 52px;
        }

        .manufacturer,
        .min-expiry-date {
            width: 100px;
            padding-left: 6px;
            text-overflow: initial;
            white-space: nowrap;
        }

        .remark {
            width: 76px;
            padding-left: 6px;
        }
    }

    .suggestions-item:not(.not-source).selected {
        .alias-name,
        .min-expiry-date {
            color: #ffffff;
        }
    }
}

.chinese-prescription-delivery-detail {
    width: 300px;
    color: var(--abc-color-T2);

    &-header {
        padding-bottom: 10px;
        border-bottom: 1px solid var(--abc-color-T3);
    }

    &-content {
        padding: 10px 0 10px 15px;

        li {
            list-style: disc;

            >p {
                font-size: 12px;
            }

            &.yellow {
                color: var(--abc-color-Y2);
            }
        }
    }
}

@media screen and (min-width: 1548px) {
    .abc-app-is-full-screen {
        .prescription-table-wrapper.chinese-table {
            .prescription-table {
                .table-body {
                    margin-bottom: -1px;
                }

                .table-td {
                    width: 20%;
                    border-right: 1px dashed var(--abc-color-P6) !important;
                    border-bottom: 1px dashed var(--abc-color-P6) !important;

                    &:nth-child(5n) {
                        border-right: none !important;
                    }
                }
            }
        }
    }
}
