<!--处方table-->
<template>
    <div class="infusion-prescription-group" data-cy="pr-table">
        <div class="prescription-table">
            <div class="table-group-no">
                <abc-select
                    v-model="currentValue.groupId"
                    :show-value="currentValue.groupId"
                    class="count-center"
                    custom-class="group-selector"
                    :width="30"
                    :inner-width="45"
                    :disabled="disabled"
                    data-cy="pr-infusion-group-no-select"
                    @change="selectGroupId"
                >
                    <abc-option data-cy="option-1" :label="1" :value="1"></abc-option>
                    <abc-option data-cy="option-2" :label="2" :value="2"></abc-option>
                    <abc-option data-cy="option-3" :label="3" :value="3"></abc-option>
                    <abc-option data-cy="option-4" :label="4" :value="4"></abc-option>
                    <abc-option data-cy="option-5" :label="5" :value="5"></abc-option>
                    <abc-option data-cy="option-6" :label="6" :value="6"></abc-option>
                    <abc-option data-cy="option-7" :label="7" :value="7"></abc-option>
                    <abc-option data-cy="option-8" :label="8" :value="8"></abc-option>
                    <abc-option data-cy="option-9" :label="9" :value="9"></abc-option>
                </abc-select>
            </div>
            <ul class="table-body">
                <li class="infusion-description" data-cy="infusion-description">
                    <!--用法-->
                    <abc-form-item :required="!!currentValue.list.length && !disabled">
                        <select-usage
                            v-model="currentValue.usage"
                            type="usages"
                            :readonly="false"
                            style="width: 72px;"
                            usage-type="zs"
                            placeholder="用法"
                            placement="bottom-start"
                            :disabled="disabled"
                            focus-show-options
                            data-cy="pr-infusion-group-usage-select"
                            @enter="enterEvent"
                            @change="selectUsage"
                            @input="changeUsage"
                        >
                        </select-usage>
                    </abc-form-item>

                    <!--用药频率-->
                    <div style="position: relative;">
                        <abc-form-item :required="!!currentValue.list.length && !disabled" class="prescription-select-freq-wrapper">
                            <abc-select
                                ref="freqSelector"
                                :show-value="currentValue.freq"
                                :value="currentValue.freq"
                                :width="60"
                                :inner-width="148"
                                size="small"
                                custom-class="prescription-select-freq-options"
                                placeholder="频率"
                                :disabled="disabled"
                                focus-show-options
                                data-cy="pr-infusion-group-freq-select"
                                @enter="enterEvent"
                                @change="selectFreq"
                            >
                                <abc-option
                                    v-for="it in freqList"
                                    :key="it.id"
                                    :label="it.en"
                                    :value="it.en"
                                    :data-cy="`option-freq-${it.en}`"
                                    :class="{
                                        'has-top-border': it.en === 'qnd',
                                    }"
                                >
                                    <div style="width: 46px;">
                                        {{ it.en }}
                                    </div>
                                    <div>{{ it.name }}</div>
                                </abc-option>
                            </abc-select>
                        </abc-form-item>
                        <div v-if="showCurrentEditCustomFreqType" style="position: absolute;">
                            <custom-freq-dialog
                                :custom-type="currentEditCustomFreqType"
                                @close="closeCustomFreqDialog"
                                @confirm="
                                    (val) => {
                                        confirmCustomFreqHandle(val);
                                    }
                                "
                            ></custom-freq-dialog>
                        </div>
                    </div>

                    <!--天数-->
                    <abc-form-item
                        :required="!!currentValue.list.length && !disabled"
                        :validate-event="validateInteger"
                    >
                        <abc-input
                            v-model.number="currentValue.days"
                            v-abc-focus-selected
                            :disabled="disabled"
                            :width="57"
                            :input-custom-style="{
                                padding: '3px 24px 3px 3px', fontSize: '16px'
                            }"
                            margin="0"
                            size="small"
                            type="number"
                            class="count-center"
                            :max-length="4"
                            data-cy="pr-infusion-group-days-input"
                            @enter="enterEvent"
                            @input="changeDay"
                        >
                        </abc-input>
                        <span class="input-append-unit">天</span>
                    </abc-form-item>

                    <ivgtt
                        v-model="currentValue"
                        :disabled="disabled"
                        @enter="enterEvent"
                        @change="changeIvgtt"
                    ></ivgtt>

                    <abc-flex style="position: absolute; right: 0; margin-right: 12px;">
                        <abc-button
                            v-if="listLength !== 1 && !disabled"
                            data-cy="pr-infusion-group-delete-btn"
                            variant="text"
                            theme="danger"
                            size="small"
                            class="prescription-infusion-table-delete-button"
                            @click="deleteGroup"
                        >
                            删除
                        </abc-button>

                        <abc-button
                            v-if="!disabled && !disabledAdd"
                            data-cy="pr-infusion-group-add-btn"
                            variant="text"
                            size="small"
                            @click="addGroup"
                        >
                            加一组
                        </abc-button>
                    </abc-flex>
                </li>

                <li
                    v-for="(item, index) in currentValue.list"
                    ref="tableTr"
                    :key="item.id || item.keyId || index"
                    class="table-tr"
                    :data-cy="`item-${index}`"
                >
                    <!--cadn-->
                    <div class="table-td cadn" @click="editCadn(index)">
                        <medicine-auto-complete
                            v-if="currentEditIndex === index"
                            :index="index"
                            :patient-info="patientInfo"
                            :show-detail-price="showDetailPrice"
                            :medical-record="medicalRecord"
                            :treat-online-clinic-id="treatOnlineClinicId"
                            :department-id="departmentId"
                            :pharmacy-type="item.pharmacyType"
                            :pharmacy-no="item.pharmacyNo"
                            :icon="false"
                            :is-infusion="true"
                            :placeholder="item.medicineCadn"
                            :prescription-form-items="currentValue.list"
                            :shebao-card-info="shebaoCardInfo"
                            :is-open-source="isOpenSource"
                            @select="
                                (newMedicine) => {
                                    changeMedicine(newMedicine, index);
                                }
                            "
                            @focus="currentEditIndex = index"
                            @closePanel="currentEditIndex = -1"
                            @dblclick.native="handleDoubleClick"
                        ></medicine-auto-complete>

                        <template v-if="currentEditIndex !== index">
                            <div
                                v-abc-goods-hover-popper:remote="{
                                    goods: item,
                                    onlyStock: true,
                                    pharmacyNo: item.pharmacyNo,
                                    openDelay: 500,
                                    showStock: needCheckStock,
                                    showPrice: showDetailPrice,
                                    showCostPrice: canViewCostPrice,
                                    showPass: showPass
                                }"
                                :class="{ 'abc-tipsy abc-tipsy--n': showWarnTips(item) }"
                                :data-tipsy="getWarnTips(item)"
                                style="flex: 1; width: 0;"
                            >
                                <!--药品cadn-->
                                <span class="in-block-cadn ellipsis" :class="{ 'no-stock': showWarnTips(item) }">
                                    {{ item.name }}
                                </span>
                                <div class="in-block-specification ellipsis">
                                    <div
                                        v-if="getSpec(item.productInfo)"
                                        class="ellipsis"
                                        :class="{ 'no-stock': showWarnTips(item) }"
                                    >
                                        {{ item.productInfo | getSpec }}
                                    </div>

                                    <!-- 精麻药品提示 | 库存不足提示 -->
                                    <div v-if="showWarnTips(item)" class="shortage-tips">
                                        <abc-icon
                                            icon="Attention"
                                            size="12"
                                            :color="$store.state.theme.style.Y2"
                                        ></abc-icon>
                                    </div>

                                    <form-item-status
                                        v-if="prescriptionForm.chargeStatus"
                                        :item="item"
                                        style="margin-left: 6px;"
                                    ></form-item-status>
                                </div>
                            </div>

                            <div v-if="compareRepeat(item)" class="repeat-item">
                                重复
                            </div>

                            <div
                                v-if="item.verifySignatures && item.verifySignatures.length"
                                class="doctor-sign"
                            >
                                <img v-if="doctorSignImgUrl" :src="doctorSignImgUrl" alt="" />
                                <template v-else>
                                    {{ doctorName }}
                                </template>
                            </div>
                        </template>
                    </div>

                    <pass-audit-dot
                        v-if="item.passAuditStatus"
                        :status="item.passAuditStatus"
                        :key-id="item.keyId"
                    ></pass-audit-dot>

                    <medical-fee-grade-td
                        v-if="displayMedicalFeeGrade(item)"
                        :item="item"
                        class="table-td"
                        :disabled="disabled"
                        :shebao-card-info="shebaoCardInfo"
                        :style="tableStyleConfig.feeGrade"
                        :show-pay-type-select="showPayTypeSelect"
                        :width="50"
                        @change-pay-type="val =>$emit('change-pay-type', val, item)"
                        @enter="enterEvent"
                    >
                    </medical-fee-grade-td>
                    <div v-else class="table-td medical-fee-grade-td-wrapper"></div>

                    <!--皮试 + 皮试结果-->
                    <ast-td
                        class="table-td ast"
                        :item="item"
                        :disabled="disabled"
                        @change="handleChangeAst"
                    ></ast-td>

                    <!--剂量-->
                    <div class="table-td dosage" :style="tableStyleConfig.dosage">
                        <dosage
                            :medicine.sync="currentValue.list[index]"
                            :disabled="disabled"
                            placeholder="单次"
                            :unit-tabindex="prescriptionFocusDosage ? -1 : 1"
                            @enter="enterEvent"
                            @change="$emit('queryVerify')"
                            @calCount="calWesternCountHandler"
                        >
                        </dosage>
                    </div>

                    <!--开药量-->
                    <div class="table-td count" :style="tableStyleConfig.count">
                        <count
                            :medicine.sync="currentValue.list[index]"
                            :need-check-stock="needCheckStock"
                            placeholder="总量"
                            :unit-tabindex="prescriptionFocusDosage ? -1 : 1"
                            :disabled="disabled"
                            @changeUnit="calWesternCountHandler"
                            @enter="enterEvent"
                            @input="handleChangeUnitCount(item)"
                        >
                        </count>
                    </div>

                    <!--折扣-->
                    <div
                        v-if="featureSupportRatioPrice && isOpenSource"
                        class="table-td"
                        :style="tableStyleConfig.ratio"
                    >
                        <abc-form-item>
                            <price-radio-popover
                                :disabled="disabled"
                                @change-price-radio="val => changePriceRadio(item, val)"
                            >
                                <abc-input
                                    v-abc-focus-selected
                                    :value="displayTotalPriceRatio(item)"
                                    placeholder="折扣"
                                    type="money"
                                    :disabled="disabled"
                                    :config="{
                                        supportZero: true,
                                        max: 100
                                    }"
                                    :readonly="readonlyItem(item)"
                                    @input="val => inputPriceRadioHandler(item, val)"
                                    @enter="enterEvent"
                                    @change="changeTotalPriceRatio(item)"
                                >
                                    <span slot="appendInner">%</span>
                                </abc-input>
                            </price-radio-popover>
                        </abc-form-item>
                    </div>

                    <!--金额-->
                    <div
                        v-if="featureSupportRatioPrice && isOpenSource"
                        class="table-td"
                        :style="tableStyleConfig.totalPrice"
                    >
                        <abc-form-item required>
                            <price-adjustment-popover :item="item">
                                <abc-input
                                    v-model="item.totalPrice"
                                    v-abc-focus-selected
                                    size="small"
                                    type="money"
                                    :tabindex="-1"
                                    :disabled="disabled"
                                    :input-custom-style="{ textAlign: 'right' }"
                                    :config="{
                                        formatLength: 2, supportZero: true, max: 9999999
                                    }"
                                    :readonly="readonlyItem(item)"
                                    @enter="enterEvent"
                                    @change="changeTotalPrice(item)"
                                >
                                </abc-input>
                            </price-adjustment-popover>
                        </abc-form-item>
                    </div>

                    <div
                        v-if="formHasSpecialRequirement"
                        class="table-td remarks"
                        :title="disabled ? item.specialRequirement : ''"
                        :style="tableStyleConfig.remark"
                    >
                        <goods-remark
                            ref="medicineRemarks"
                            v-model="item.specialRequirement"
                            placeholder="备注"
                            max-length="50"
                            placement="bottom-start"
                            :readonly="false"
                            :disabled="disabled"
                            support-tag
                            :pr-form-item="item"
                            :department-id="departmentId"
                            :tabindex="-1"
                            show-medicine-remark-options
                            :show-medicine-source-options="needCheckStock && item.pharmacyType !== PharmacyTypeEnum.COOPERATION_PHARMACY"
                            :show-no-charge="item.pharmacyType !== PharmacyTypeEnum.COOPERATION_PHARMACY"
                            @focus="() => handleFocusRemark(item)"
                            @blur="() => handleBlurRemark(item)"
                            @enter="enterEvent"
                            @change="outpatientFeeChange"
                        ></goods-remark>
                    </div>

                    <div class="delete-item">
                        <delete-icon
                            v-if="!disabled"
                            type="dark"
                            data-cy="pr-infusion-item-delete"
                            @delete="deleteWesternMedicine(index)"
                        >
                        </delete-icon>
                    </div>
                </li>
            </ul>
        </div>

        <div v-if="!disabled || showTotal" class="prescription-footer">
            <abc-form-item v-if="!disabled && !isCharged && !disabledAdd" style="flex: 1;">
                <medicine-auto-complete
                    ref="medicineAutoComplete"
                    :is-infusion="true"
                    :patient-info="patientInfo"
                    :repeat-index="repeatIndex"
                    :show-detail-price="showDetailPrice"
                    :medical-record="medicalRecord"
                    :treat-online-clinic-id="treatOnlineClinicId"
                    :department-id="departmentId"
                    :prescription-form-items="currentValue.list"
                    :pharmacy-type="prescriptionForm.pharmacyType"
                    :pharmacy-no="prescriptionForm.pharmacyNo"
                    :need-check-stock="needCheckStock"
                    :is-open-source="isOpenSource"
                    :shebao-card-info="shebaoCardInfo"
                    data-cy="pr-infusion-medicine-autocomplete"
                    placement="bottom-start"
                    @select="selectWesternMedicine"
                >
                </medicine-auto-complete>
            </abc-form-item>

            <abc-flex :gap="12" class="total-price">
                <template v-if="showTotal">
                    <form-status v-if="disabled" :forms="[prescriptionForm]"></form-status>
                    <span v-if="showTotalPrice" style=" font-weight: 400; color: #7a8794;">
                        <span v-if="!disabled"><abc-money
                            :value="prescriptionTotal"
                            is-show-space
                            data-cy="pr-infusion-total-price"
                        ></abc-money></span>
                        <biz-charge-stat-popover
                            v-else
                            :title="title"
                            :forms="[prescriptionForm]"
                        >
                            <abc-money
                                :value="prescriptionTotal"
                                is-show-space
                                data-cy="pr-infusion-total-price"
                            ></abc-money>
                        </biz-charge-stat-popover>
                    </span>
                </template>
            </abc-flex>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    // vue lib
    import { mapGetters } from 'vuex';

    // utils
    import {
        isShortage, isDisabledGoods,
    } from 'utils/validate';
    import { calWesternCount } from 'utils/calculation';
    import { getSpec } from 'src/filters/index';

    import MedicineAutoComplete from '../common/medicine-autocomplete.vue';
    import SelectUsage from '../../select-group/index.vue';
    import GoodsRemark from 'views/layout/goods-remark/index.vue';
    import Dosage from '../common/dosage.vue';
    import Count from '../common/count.vue';
    import Ivgtt from '../common/ivgtt.vue';
    import DeleteIcon from '../../delete-icon/delete-icon';
    import AstTd from '../common/ast-td.vue';
    import MedicalFeeGradeTd from '../common/medical-fee-grade-td.vue';
    import BizChargeStatPopover from '@/components-composite/biz-charge-stat-popover';
    import FormStatus from 'src/views/outpatient/common/form-status.vue';
    import FormItemStatus from 'src/views/outpatient/common/form-item-status.vue';

    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import {
        needExpandRemark, showPsychotropicNarcoticTips,
    } from 'views/layout/prescription/utils.js';
    import CustomFreqDialog from '../common/custom-freq-dialog.vue';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import { PharmacyTypeEnum } from '@abc/constants';
    import { isAllowAddByAntimicrobialDrugManagement } from 'views/outpatient/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import NeiMengPassPR from '@/pass-pharm-review';
    import PassAuditDot from '@/pass-pharm-review/pass-audit-dot.vue';

    // 自定义频率
    const CustomFreqOptions = [
        {
            en: 'qnd',
            name: 'N天1次',
        },
        {
            en: 'qnw',
            name: 'N周1次',
        },
        {
            en: 'qnh',
            name: 'N小时1次',
        },
        {
            en: 'wn',
            name: '每周N次',
        },
    ];

    export default {
        name: 'IvgttTable',
        components: {
            CustomFreqDialog,
            SelectUsage,
            GoodsRemark,
            MedicineAutoComplete,
            Ivgtt,
            Dosage,
            Count,
            DeleteIcon,
            AstTd,
            MedicalFeeGradeTd,
            BizChargeStatPopover,
            FormStatus,
            FormItemStatus,
            PassAuditDot,
        },
        inject: {
            outpatientEditForm: {
                default: null,
            },
        },
        props: {
            verifyOutpatient: {
                type: Object,
                default: () => ({}),
            },
            value: Object,
            listLength: Number,
            isCharged: {
                type: Boolean,
                default: false,
            },
            prescriptionForm: Object,
            formChargeStatus: {
                type: Number,
                default: 0,
            },
            showDetailPrice: {
                type: Boolean,
                default: true,
            },
            showTotalPrice: {
                type: Boolean,
                default: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            disabledAdd: Boolean,
            needCheckStock: {
                type: Boolean,
                default: true,
            },
            // 判断开出来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            prescriptionTotal: Number,
            PIndex: Number,
            patientInfo: Object,
            medicalRecord: Object,
            treatOnlineClinicId: [Number, String],
            departmentId: String,
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            /**
             是否展示医保登记
             */
            showMedicalFeeGrade: {
                type: Boolean,
                default: true,
            },
            title: String,
            doctorId: {
                type: String,
                default: '',
            },
            // 判断是否是处方模板
            isFromTemplate: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                PharmacyTypeEnum,
                currentEditIndex: -1,
                repeatIndex: -1,
                currentValue: {},
                // 自定义频率弹窗参数
                currentEditCustomFreqType: null,
                // 控制是否显示自定义频率弹窗
                showCurrentEditCustomFreqType: false,
            };
        },
        computed: {
            ...mapGetters([
                'westernMedicineConfig',
                'clinicConfig',
                'clinicBasic',
                'chainBasic',
                'isCanSeeGoodsCostPriceInOutpatient',
                'pharmacyRuleList',
                'userInfo',
                'chargeConfig',
            ]),
            ...mapGetters('viewDistribute',[
                'featureSupportRatioPrice',
                'viewDistributeConfig',
            ]),
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustOutpatientPrice',
            ]),

            showPass() {
                return NeiMengPassPR.needPass;
            },

            // 单项议价权限: 开启了医生可单项议价&&当前医生有权限
            canSingleBargain() {
                return !!this.chargeConfig.doctorSingleBargainSwitch && this.employeeCanAdjustOutpatientPrice;
            },
            outpatientVDConfig() {
                return this.viewDistributeConfig?.Outpatient || {};
            },

            tableStyleConfig() {
                return this.outpatientVDConfig.tableStyleConfig.infusionTable;
            },

            doctorSignImgUrl() {
                const { doctorSignImgUrl = '' } = this.outpatientEditForm || {};
                if (doctorSignImgUrl) return doctorSignImgUrl;
                const {
                    id: userId,
                    handSign,
                    handSignType,
                } = this.userInfo || {};
                if (handSignType === 1 && this.doctorId === userId && handSign) {
                    return handSign;
                }
                return '';
            },
            doctorName() {
                const { postData = {} } = this.outpatientEditForm || {};
                return postData.doctorName || '';
            },
            /**
             * @desc 能否选择 是否自费 payType
             * <AUTHOR>
             * @date 2021-07-19 18:23:14
             */
            showPayTypeSelect() {
                return (
                    this.$abcSocialSecurity.isOpenSocial &&
                    (
                        this.$abcSocialSecurity.config.isZhejiang ||
                        this.$abcSocialSecurity.config.isFujianFuzhou ||
                        this.$abcSocialSecurity.config.isLiaoningPanjin
                    )
                );
            },
            /**
             * 医生能否查看成本价
             */
            canViewCostPrice() {
                return this.isCanSeeGoodsCostPriceInOutpatient;
            },

            showTotal() {
                return this.listLength - 1 === this.PIndex;
            },

            formHasSpecialRequirement() {
                if (!this.disabled) return true;
                // 禁用状态下，没有填写过 备注 则影藏显示
                return this.currentValue.list.some((item) => {
                    const defaultPharmacy = getDefaultPharmacy(this.pharmacyRuleList, {
                        departmentId: this.departmentId,
                        goodsInfo: item.productInfo,
                    });
                    return needExpandRemark(item, defaultPharmacy);
                });
            },

            // 华蓥某诊所设置，回车跳过单位
            prescriptionFocusDosage() {
                if (!this.chainBasic) return false;
                if (!this.chainBasic.outpatient) return false;
                return !!this.chainBasic.outpatient.prescriptionFocusDosage;
            },
            // 默认频率加上自定义频率
            freqList() {
                return this.defineArray('freq').concat(CustomFreqOptions);
            },
        },
        watch: {
            value: {
                handler (val) {
                    this.currentValue = val;
                },
                deep: true,
                immediate: true,
            },
            currentValue: {
                handler (val) {
                    this.$emit('input', val);
                },
                deep: true,
            },
        },
        created() {
            this._filterUsage = [ '外用', '滴眼', '滴鼻', '滴耳', '眼科外用', '涂入眼睑', '含漱', '直肠给药', '直肠塞入', '肛门涂抹', '阴道给药', '阴道塞入', '阴道擦洗' ];
        },

        methods: {
            /**
             * @desc 选择组号
             * <AUTHOR>
             * @date 2018/07/10 15:35:12
             * @params groupId
             * @params index
             */
            selectGroupId() {
                this.$emit('changeGroupId');
            },

            /**
             * @desc 药品禁用 || 库存不足 || 无库存信息
             * <AUTHOR> Yang
             * @date 2021-03-30 15:46:13
             */

            showStockWarnTips(item) {
                // 自备不校验库存
                if (item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE) return false;
                if (this.isCharged || this.disabled || !this.needCheckStock) return false;
                return isDisabledGoods(item).flag || isShortage(item).flag || item.noStocks;
            },

            /**
             * @desc 药品禁用 || 库存不足 || 无库存信息 || 精麻药
             * <AUTHOR> Yang
             * @date 2021-03-30 15:46:13
             */
            showWarnTips(item) {
                // 自备不校验库存
                if (item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE) return false;
                if (this.isCharged || this.disabled || !this.needCheckStock) return false;

                const isDisabled = isDisabledGoods(item).flag;
                const isShortageFlag = isShortage(item).flag;
                const { noStocks } = item;

                const isPsychotropicNarcotic = showPsychotropicNarcoticTips(item, this.verifyOutpatient);

                // 当为-1不提示
                return isDisabled || isShortageFlag || noStocks || isPsychotropicNarcotic;
            },

            getStockWarnTips(item) {
                return isDisabledGoods(item).tips || isShortage(item).tips || '无库存';
            },

            getWarnTips(item) {
                const hasPsychotropicNarcotic = showPsychotropicNarcoticTips(item, this.verifyOutpatient);
                const hasStockWarn = this.showStockWarnTips(item);
                const psychotropicNarcoticTips = '该药物为麻醉药品，精神药品等特殊管制药品，在线复诊时禁止开出，请从处方中删除';

                if (hasPsychotropicNarcotic && hasStockWarn) {
                    return `${psychotropicNarcoticTips};${this.getStockWarnTips(item)}`;
                }

                if (hasPsychotropicNarcotic) {
                    return psychotropicNarcoticTips;
                }

                if (hasStockWarn) {
                    return this.getStockWarnTips(item);
                }

                return '';
            },

            getSpec,

            /**
             * @desc 点击 可以修改 cadn
             * <AUTHOR>
             * @date 2018/12/12 15:25:15
             */
            editCadn(index) {
                if (this.disabled) return false;
                this.currentEditIndex = index;
                this.$nextTick(() => {
                    $(this.$refs.tableTr[ index ]).find('.medicine-autocomplete input').focus();
                });
            },
            handleDoubleClick(event) {
                if (!event.target.value) return;
                event.target.selectionStart = 0;
                event.target.selectionEnd = event.target.value.length;
            },

            /**
             * @desc 换药
             * <AUTHOR>
             * @date 2018/12/06 17:47:55
             */
            changeMedicine(newMedicine, index) {
                // 通过模板开出时不进行校验
                if (!this.isFromTemplate) {
                    // 判断切换的中西成药的抗菌等级是否满足开出条件
                    const isSuccess = this.isAllowAdd(newMedicine);
                    if (!isSuccess) return;
                }

                this.currentEditIndex = -1;

                const oldMedicine = this.currentValue.list[ index ];

                newMedicine.usage = this.currentValue.usage;
                newMedicine.freq = this.currentValue.freq;
                newMedicine.days = this.currentValue.days;
                newMedicine.ivgtt = this.currentValue.ivgtt;
                newMedicine.ivgttUnit = this.currentValue.ivgttUnit;
                newMedicine.sort = oldMedicine.sort;
                newMedicine.groupId = oldMedicine.groupId;

                this.currentValue.list.splice(index, 1, newMedicine);

                this.calWesternCountHandler(newMedicine);

                this.$parent && this.$parent.$emit('queryVerify');
            },

            /**
             * 判断开出的中西成药的抗菌等级是否满足开出条件
             * @param {Object} medicine
             * @return {boolean}
             */
            isAllowAdd(medicine) {
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                const isSuccess = isAllowAddByAntimicrobialDrugManagement(medicine, this.doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                if (!isSuccess) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: [medicine] }).generateDialogAsync({ parent: this });
                    }, 100);
                }
                return isSuccess;
            },

            /** ----------------------------------------------------------------------
             * autocomplete 选择西药 || 通过回车录入西药
             * @param newMedicine
             */
            async selectWesternMedicine(newMedicine) {
                // 通过模板开出时不进行校验
                if (!this.isFromTemplate) {
                    const isSuccess = this.isAllowAdd(newMedicine);
                    if (!isSuccess) return;
                }

                this.repeatIndex = this.findRepeatIndex(newMedicine);
                // 有重复药品直接聚焦到该药品数量输入框
                if (this.repeatIndex > -1) {
                    this._fromAutocomple = true; // 设置快速回到药品搜索框 flag
                } else {
                    newMedicine.usage = this.currentValue.usage;
                    newMedicine.freq = this.currentValue.freq;
                    newMedicine.days = this.currentValue.days;
                    newMedicine.ivgtt = this.currentValue.ivgtt;
                    newMedicine.ivgttUnit = this.currentValue.ivgttUnit;

                    this.currentValue.list.push(newMedicine);

                    this.calWesternCountHandler(newMedicine);

                    this.$parent && this.$parent.$emit('queryVerify');
                }
            },

            /**
             * @desc 对西药数据进行默认值
             * <AUTHOR>
             * @date 2018/07/10 16:40:58
             */
            defineArray(key) {
                if (this.westernMedicineConfig) {
                    if (key === 'usage') {
                        return this.westernMedicineConfig[ key ].filter((it) => {
                            return it.type === 2;
                        });
                    }
                    return this.westernMedicineConfig[ key ];

                }
                return [];

            },

            /**
             * @desc 帮用户计算开药量
             * <AUTHOR>
             * @date 2018/04/13 12:14:23
             */
            calWesternCountHandler(medicine, useDismounting) {
                medicine.days = medicine.days || null;
                if (!medicine.goodsId) return false;
                const {
                    settings: outpatientCalcSettings,
                } = this.clinicBasic.outpatient;

                const forceUsePackageUnit = useDismounting === 0;
                const res = calWesternCount(medicine, outpatientCalcSettings, forceUsePackageUnit);
                medicine.unitCount = res.unitCount;
                if (res.unitCount) {
                    medicine.unit = res.unit;
                    medicine.useDismounting = res.useDismounting;
                    const {
                        piecePrice,
                        packagePrice,
                    } = medicine.productInfo;
                    medicine.unitPrice = medicine.useDismounting ? piecePrice : packagePrice;
                }
                this.$set(medicine, 'expectedTotalPriceRatio', null);
                this.$set(medicine, 'expectedTotalPrice', null);
                this.$set(medicine, 'unitAdjustmentFeeLastModifiedBy', null);
                this.outpatientFeeChange();
            },

            handleChangeUnitCount(medicine) {
                if (medicine.expectedTotalPrice) {
                    medicine.expectedTotalPrice = null;
                    if (medicine.sourceUnitPrice !== medicine.unitPrice) {
                        medicine.expectedUnitPrice = medicine.unitPrice;
                    }
                }
                this.$set(medicine, 'expectedTotalPriceRatio', null);
                this.$set(medicine, 'expectedTotalPrice', null);
                this.$set(medicine, 'unitAdjustmentFeeLastModifiedBy', null);
                this.outpatientFeeChange();
            },

            /**
             * @desc  改变分组用法 ,同组药品都需要修改
             * <AUTHOR>
             * @date 2018/04/16 15:10:29
             */
            selectUsage(usage) {
                this.currentValue.usage = usage;
                if (usage === '静脉滴注') {
                    this.currentValue.ivgtt = 60;
                    this.currentValue.ivgttUnit = '滴/分钟';
                } else {
                    this.currentValue.ivgtt = '';
                    this.currentValue.ivgttUnit = '';
                }
                this.changeIvgtt();
                this.currentValue.list.forEach((medicine) => {
                    medicine.usage = this.currentValue.usage;
                    this.calWesternCountHandler(medicine);
                });
            },

            /**
             * 打开自定义频率弹窗
             * @param freq
             */
            selectCustomFreq(freq) {
                this.$refs.freqSelector.showPopper = false;
                this.showCurrentEditCustomFreqType = true;
                this.currentEditCustomFreqType = freq;
            },
            /**
             * @desc 选择频率需要计算开药量
             * <AUTHOR>
             * @date 2018/04/14 19:18:55
             */
            selectFreq(freq) {
                // 如果选择了自定义频率,则单独处理
                if (['qnd', 'qnw', 'qnh', 'wn'].indexOf(freq) > -1) {
                    this.selectCustomFreq(freq);
                    return false;
                }

                this.currentValue.freq = freq;
                this.currentValue.list.forEach((medicine) => {
                    if (this.currentValue.freq !== medicine.freq) {
                        medicine.freq = this.currentValue.freq;
                        this.calWesternCountHandler(medicine);
                    }
                });
            },
            /**
             * 关闭自定义频率弹窗
             */
            closeCustomFreqDialog() {
                this.showCurrentEditCustomFreqType = false;
                this.currentEditCustomFreqType = null;
            },
            /**
             * 自定义频率弹窗确定事件
             * @param val
             */
            confirmCustomFreqHandle(val) {
                this.selectFreq(val);
            },

            /**
             * @desc 手动输入改变分组用法
             * <AUTHOR>
             * @date 2023-03-22 13:50:32
             */
            changeUsage() {
                this.currentValue.list.forEach((medicine) => {
                    medicine.usage = this.currentValue.usage;
                });
            },

            /**
             * @desc 改变分组天数
             * <AUTHOR>
             * @date 2018/04/16 15:03:16
             */
            changeDay() {
                this.currentValue.list.forEach((medicine) => {
                    if (+medicine.days !== +this.currentValue.days) {
                        medicine.days = this.currentValue.days || null;
                        this.calWesternCountHandler(medicine);
                    }
                });
            },

            /**
             * @desc 改变分组滴速/滴速单位
             * <AUTHOR>
             * @date 2018/04/16 15:08:11
             */
            changeIvgtt() {
                this.currentValue.list.forEach((medicine) => {
                    medicine.ivgtt = this.currentValue.ivgtt;
                    medicine.ivgttUnit = this.currentValue.ivgttUnit;
                });
            },

            /**
             * @desc 改变西药dosage
             * <AUTHOR>
             * @date 2018/04/13 12:39:59
             */
            changeDosage(medicine) {
                this.calWesternCountHandler(medicine);
            },

            /**
             * @desc 选择西药计量单位 如果选择"适量"需要清除dosage
             * <AUTHOR>
             * @date 2018/07/10 15:33:10
             */
            selectDosageUnit(dosageUnit, index) {
                this.currentValue.list[ index ].dosageUnit = dosageUnit;
                if (dosageUnit === '适量') {
                    this.currentValue.list[ index ].dosage = '';
                } else {
                    this.calWesternCountHandler(this.currentValue.list[ index ]);
                }
            },

            /** ----------------------------------------------------------------------
             * 删除西药
             * @param index
             */
            deleteWesternMedicine(index) {
                if (this.disabled) return false;
                this.currentValue.list.splice(index, 1);
                this.$parent && this.$parent.$emit('queryVerify');
                this.currentEditIndex = -1;
                this.outpatientFeeChange();
            },

            /**
             * @desc 输液处方添加一组
             * <AUTHOR>
             * @date 2018/04/12 14:25:20
             */
            addGroup() {
                this.$emit('addGroup', this.PIndex, this.currentValue.groupId);
                this.outpatientFeeChange();
            },

            /**
             * @desc 删除该分组
             * <AUTHOR>
             * @date 2018/04/12 15:56:45
             */
            deleteGroup() {
                this.$emit('deleteGroup', this.PIndex);
                this.outpatientFeeChange();
            },

            /**
             * @desc 找到已有药品的index
             * <AUTHOR>
             * @date 2018/07/10 15:45:07
             */
            findRepeatIndex(item) {
                return this.currentValue.list.findIndex((m) => {
                    return m.name &&
                        m.name === item.name &&
                        (m.goodsId || '') === (item.goodsId || '');
                });
            },
            /**
             * @desc 比较同组药品是否有重复项目
             * <AUTHOR>
             * @date 2018/07/10 15:45:07
             * @params
             * @return
             */
            compareRepeat(item) {
                if (this.disabled) return false;
                let count = 0;
                this.currentValue.list.forEach((m) => {
                    if (m.name && m.name === item.name && (m.goodsId || '') === (item.goodsId || '') && (m.groupId || '') === (item.groupId || '')) {
                        count++;
                    }
                });
                return count >= 2;
            },

            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                // 快速回到药品搜索input
                if (this._fromAutocomple) {
                    this.$nextTick(() => {
                        this.$el.querySelector('.medicine-autocomplete input').focus();
                        this._fromAutocomple = false;
                    });
                    return false;
                }

                // 找到所有的非disabled的input输入框
                const inputs = $(this.$el).find('.abc-input__inner').not(':disabled');
                const targetIndex = inputs.index(e.target);
                let nextInput = inputs[ targetIndex + 1 ];
                if (nextInput.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }
                // 碰到连续tabindex===-1的情况，再加一次
                if (nextInput.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 3];
                }
                nextInput && this.$nextTick(() => {
                    nextInput.select && nextInput.select();
                    nextInput.focus && nextInput.focus();

                    // magic code
                    this._timer = setTimeout(() => {
                        nextInput.selectionStart = 0;
                        nextInput.selectionEnd = nextInput.value ? nextInput.value.length : 0;
                        nextInput.select && nextInput.select();
                        nextInput.focus && nextInput.focus();
                    }, 50);
                });
            },

            handleFocusRemark(item) {
                this.$set(item, 'expandSpecialRequirement', true);
            },
            handleBlurRemark(item) {
                this.$set(item, 'expandSpecialRequirement', false);
            },
            handleChangeAst() {
                this.$parent && this.$parent.$emit('queryVerify');
                this.outpatientFeeChange();
            },

            handleRemarkInput(item) {
                if (item.specialRequirement && item.specialRequirement.indexOf('【自备】') > -1) {
                    this.$set(item, 'chargeType', OutpatientChargeTypeEnum.NO_CHARGE);
                    this.$Toast({
                        message: '备注为【自备】，该药品将不会纳入划价收费',
                        duration: 1500,
                        referenceEl: this.$refs.medicineRemarks[0].$el,
                    });
                } else {
                    this.$set(item, 'chargeType', OutpatientChargeTypeEnum.DEFAULT);
                }
            },

            /**
             * @desc 验证是否为整数
             * <AUTHOR>
             * @date 2025-02-17
             */
            validateInteger(value, callback) {
                const reg = /^[1-9]\d*$/;
                if (!reg.test(value)) {
                    callback({
                        validate: false,
                        message: '天数只支持整数',
                    });
                    return;
                }
                callback({
                    validate: true,
                });
            },

            /**
             * @desc 门诊单内容变更
             * <AUTHOR>
             * @date 2022/06/06 10:47:44
             */
            outpatientFeeChange() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '输注处方',
                    needCalcFee: true,
                });
            },

            displayMedicalFeeGrade(item) {
                return this.showMedicalFeeGrade &&
                    this.isOpenSource &&
                    item.productInfo &&
                    item.productInfo.medicalFeeGrade;
            },

            displayTotalPriceRatio(item) {
                const { totalPriceRatio } = item;
                if (!totalPriceRatio || totalPriceRatio > 1) return '';
                return Math.round((totalPriceRatio ?? 1) * 100);
            },
            inputPriceRadioHandler(item, val) {
                this.$set(item, 'totalPriceRatio', val / 100);
            },
            changeTotalPriceRatio(item) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPriceRatio', item.totalPriceRatio);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },
            changePriceRadio(item, val) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'totalPriceRatio', val);
                this.$set(item, 'expectedTotalPriceRatio', val);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },
            changeTotalPrice(item) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPriceRatio', null);
                this.$set(item, 'expectedTotalPrice', item.totalPrice);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },

            isItemCanAdjustment(item) {
                if (!this.canSingleBargain) return false;
                return !!item.canAdjustment;
            },

            readonlyItem(item) {
                return !this.isItemCanAdjustment(item);
            },
        },
    };
</script>

<style lang="scss">
.prescription-select-freq-wrapper {
    .abc-input__inner {
        padding: 3px 6px;
    }
}
</style>
