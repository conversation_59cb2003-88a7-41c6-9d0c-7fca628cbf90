<!--处方table-->
<template>
    <div class="prescription-table" data-cy="pr-table">
        <ul id="drag-list" class="table-body">
            <li
                v-for="(item, index) in formItems"
                ref="tableTr"
                :key="item.id || item.keyId || index"
                class="table-tr"
                :data-id="item.id || item.keyId"
                :draggable="!disabled"
                :data-cy="`item-${index}`"
            >
                <div :key="`groupId_${dragging}`" class="table-td group drag-handle">
                    <abc-form-item>
                        <abc-select
                            v-model="item.groupId"
                            class="count-center"
                            custom-class="group-selector"
                            :width="30"
                            :inner-width="45"
                            :index="index"
                            :disabled="disabled"
                            data-cy="select-group"
                            @enter="enterEvent"
                            @change="handleChangeGroupId(item, index)"
                        >
                            <abc-option
                                v-for="groupI in groupIdOptions"
                                :key="groupI.label"
                                :label="groupI.label"
                                :value="groupI.value"
                                :data-cy="`group-option-${groupI.label}`"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </div>

                <!--cadn-->
                <div :key="`cadn_${dragging}`" class="table-td cadn" @click="editCadn(index)">
                    <medicine-auto-complete
                        v-if="currentEditIndex === index"
                        :index="index"
                        :show-detail-price="showDetailPrice"
                        :patient-info="patientInfo"
                        :medical-record="medicalRecord"
                        :treat-online-clinic-id="treatOnlineClinicId"
                        :scene-type="SearchSceneTypeEnum.outpatient"
                        :department-id="departmentId"
                        :pharmacy-type="item.pharmacyType"
                        :pharmacy-no="item.pharmacyNo"
                        :icon="false"
                        :placeholder="item.medicineCadn || item.name"
                        :prescription-form-items="formItems"
                        :shebao-card-info="shebaoCardInfo"
                        :is-open-source="isOpenSource"
                        @select="
                            (newMedicine) => {
                                changeMedicine(newMedicine, index);
                            }
                        "
                        @closePanel="closePanel"
                        @dblclick.native="handleDoubleClick"
                    ></medicine-auto-complete>

                    <template v-if="currentEditIndex !== index">
                        <div
                            v-abc-goods-hover-popper:remote="{
                                goods: item,
                                onlyStock: true,
                                pharmacyNo: item.pharmacyNo,
                                openDelay: 500,
                                showStock: needCheckStock,
                                showPrice: showDetailPrice,
                                showCostPrice: canViewCostPrice,
                                showShebaoCode: true,
                                dragging: dragging,
                                showPass: showPass
                            }"
                            class="drag-handle"
                            :class="{ 'abc-tipsy abc-tipsy--n': showWarnTips(item) }"
                            :data-tipsy="getWarnTips(item)"
                            style="flex: 1; width: 0;"
                        >
                            <!--药品cadn-->
                            <span
                                class="in-block-cadn ellipsis"
                                :class="{
                                    'warn-tips': showWarnTips(item)
                                }"
                            >
                                {{ item.name }}
                            </span>
                            <div class="in-block-specification ellipsis">
                                <div
                                    v-if="getSpec(item.productInfo)"
                                    class="ellipsis"
                                    :class="{
                                        'warn-tips': showWarnTips(item)
                                    }"
                                >
                                    {{ item.productInfo | getSpec }}
                                </div>
                                <!-- 精麻药品提示 | 库存不足提示 -->
                                <div v-if="showWarnTips(item)" class="shortage-tips">
                                    <abc-icon
                                        icon="Attention"
                                        size="12"
                                        :color="$store.state.theme.style.Y2"
                                    ></abc-icon>
                                </div>

                                <form-item-status
                                    v-if="prescriptionForm.chargeStatus"
                                    :item="item"
                                    style="margin-left: 6px;"
                                ></form-item-status>
                            </div>
                        </div>

                        <div v-if="compareRepeat(item)" class="repeat-item">
                            重复
                        </div>

                        <div
                            v-if="item.verifySignatures && item.verifySignatures.length"
                            class="doctor-sign"
                        >
                            <img v-if="doctorSignImgUrl" :src="doctorSignImgUrl" alt="" />
                            <template v-else>
                                {{ doctorName }}
                            </template>
                        </div>
                    </template>
                </div>

                <pass-audit-dot
                    v-if="item.passAuditStatus"
                    :status="item.passAuditStatus"
                    :key-id="item.keyId"
                ></pass-audit-dot>

                <medical-fee-grade-td
                    v-if="displayMedicalFeeGrade(item)"
                    :item="item"
                    class="table-td"
                    :disabled="disabled"
                    :shebao-card-info="shebaoCardInfo"
                    :style="tableStyleConfig.feeGrade"
                    :show-pay-type-select="showPayTypeSelect"
                    :width="50"
                    @change-pay-type="val =>$emit('change-pay-type', val, item)"
                    @enter="enterEvent"
                >
                </medical-fee-grade-td>
                <div v-else class="table-td medical-fee-grade-td-wrapper"></div>
                <!--皮试 + 皮试结果-->
                <ast-td
                    v-if="historyHasAst || astPrescriptionSwitch.westernSwitch"
                    :key="`ast_${dragging}`"
                    class="table-td ast"
                    :item="item"
                    :disabled="disabled"
                    @change="outpatientFeeChange"
                ></ast-td>

                <!--用法-->
                <div
                    :key="`usage_${dragging}`"
                    class="table-td usage"
                    :title="item.usage && item.usage.length > 4 ? item.usage : ''"
                    :style="tableStyleConfig.usage"
                >
                    <abc-form-item :required="!disabled">
                        <select-usage
                            v-model="item.usage"
                            type="usages"
                            placeholder="用法"
                            placement="bottom-start"
                            :readonly="false"
                            max-length="8"
                            :index="index"
                            :disabled="disabled"
                            focus-show-options
                            @enter="enterEvent"
                            @change="selectUsage"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <!--频率-->
                <div
                    :key="`freq_${dragging}`"
                    class="table-td freq"
                    :style="tableStyleConfig.freq"
                >
                    <abc-form-item :required="!disabled">
                        <abc-select
                            ref="freqSelector"
                            :show-value="item.freq"
                            :value="item.freq"
                            adaptive-width
                            placeholder="频率"
                            custom-class="prescription-select-freq-options"
                            :index="index"
                            :disabled="disabled"
                            focus-show-options
                            data-cy="select-freq"
                            @enter="enterEvent"
                            @change="selectFreq"
                        >
                            <abc-option
                                v-for="it in freqList"
                                :key="it.id || it.en"
                                :label="it.en"
                                :value="it.en"
                                :class="{
                                    'has-top-border': it.en === 'qnd',
                                }"
                                :data-cy="`option-freq-${it.en}`"
                            >
                                <div style="width: 46px;">
                                    {{ it.en }}
                                </div>
                                <div class="gray">
                                    {{ it.name }}
                                </div>
                            </abc-option>
                        </abc-select>
                    </abc-form-item>
                    <custom-freq-dialog
                        v-if="index === currentEditCustomFreqIndex"
                        :custom-type="currentEditCustomFreqType"
                        @close="closeCustomFreqDialog"
                        @confirm="
                            (val) => {
                                confirmCustomFreqHandle(val, index);
                            }
                        "
                    >
                    </custom-freq-dialog>
                </div>

                <!--剂量-->
                <div
                    :key="`dosage_${dragging}`"
                    class="table-td dosage"
                    :style="tableStyleConfig.dosage"
                >
                    <dosage
                        :medicine="item"
                        :disabled="disabled"
                        placeholder="单次"
                        :unit-tabindex="prescriptionFocusDosage ? -1 : 1"
                        @enter="enterEvent"
                        @change="$emit('queryVerify')"
                        @calCount="calWesternCountHandler"
                    >
                    </dosage>
                </div>

                <!--天数-->
                <div class="table-td days" :style="tableStyleConfig.days">
                    <abc-form-item :validate-event="validateNumber">
                        <abc-input
                            v-model="item.days"
                            v-abc-focus-selected
                            :disabled="disabled"
                            :input-custom-style="{ padding: '3px 24px 3px 3px' }"
                            margin="0"
                            :max-length="4"
                            type="number"
                            class="count-center"
                            data-cy="input-days"
                            @enter="enterEvent"
                            @input="changeDays(item, index)"
                        >
                        </abc-input>
                        <span class="input-append-unit">天</span>
                    </abc-form-item>
                </div>

                <!--开药量-->
                <div
                    :key="`count_${dragging}`"
                    class="table-td count"
                    :style="tableStyleConfig.count"
                >
                    <count
                        :medicine="item"
                        :need-check-stock="needCheckStock"
                        placeholder="总量"
                        :disabled="disabled"
                        :unit-tabindex="prescriptionFocusDosage ? -1 : 1"
                        @changeUnit="handleChangeUnit"
                        @enter="enterEvent"
                        @input="handleChangeUnitCount(item)"
                    >
                    </count>
                </div>
                <!--折扣-->
                <div
                    v-if="featureSupportRatioPrice && isOpenSource"
                    class="table-td"
                    :style="tableStyleConfig.ratio"
                >
                    <abc-form-item>
                        <price-radio-popover
                            :disabled="disabled"
                            @change-price-radio="val => changePriceRadio(item, val)"
                        >
                            <abc-input
                                v-abc-focus-selected
                                :value="displayTotalPriceRatio(item)"
                                type="money"
                                placeholder="折扣"
                                :disabled="disabled"
                                :config="{
                                    supportZero: true,
                                    max: 100
                                }"
                                data-cy="input-ratio"
                                :readonly="readonlyItem(item)"
                                @input="val => inputPriceRadioHandler(item, val)"
                                @enter="enterEvent"
                                @change="changeTotalPriceRatio(item)"
                            >
                                <span slot="appendInner">%</span>
                            </abc-input>
                        </price-radio-popover>
                    </abc-form-item>
                </div>
                <!--总金额-->
                <div
                    v-if="featureSupportRatioPrice && isOpenSource"
                    class="table-td"
                    :style="tableStyleConfig.totalPrice"
                >
                    <abc-form-item required>
                        <price-adjustment-popover :item="item">
                            <abc-input
                                v-model="item.totalPrice"
                                v-abc-focus-selected
                                :disabled="disabled"
                                type="money"
                                :tabindex="-1"
                                :input-custom-style="{
                                    textAlign: 'right !important',
                                    paddingRight: '10px',
                                }"
                                :config="{
                                    formatLength: 2,
                                    supportZero: true,
                                    max: 9999999
                                }"
                                :readonly="readonlyItem(item)"
                                data-cy="input-total-price"
                                @enter="enterEvent"
                                @change="changeTotalPrice(item)"
                            ></abc-input>
                        </price-adjustment-popover>
                    </abc-form-item>
                </div>

                <div
                    v-if="formHasSpecialRequirement"
                    :key="`medicineRemarks_${dragging}`"
                    class="table-td remarks"
                    :style="tableStyleConfig.remark"
                    :title="disabled ? item.specialRequirement : ''"
                >
                    <goods-remark
                        ref="medicineRemarks"
                        v-model="item.specialRequirement"
                        placeholder="备注"
                        max-length="50"
                        placement="bottom-start"
                        :readonly="false"
                        :disabled="disabled"
                        support-tag
                        :pr-form-item="item"
                        :department-id="departmentId"
                        :tabindex="-1"
                        show-medicine-remark-options
                        :show-medicine-source-options="isOpenSource && item.pharmacyType !== PharmacyTypeEnum.COOPERATION_PHARMACY"
                        :show-no-charge="item.pharmacyType !== PharmacyTypeEnum.COOPERATION_PHARMACY"
                        @focus="() => handleFocusRemark(item)"
                        @blur="() => handleBlurRemark(item)"
                        @enter="enterEvent"
                        @changePharmacySource="outpatientFeeChange"
                        @change="outpatientFeeChange"
                    >
                    </goods-remark>
                </div>

                <div class="delete-item">
                    <delete-icon v-if="!disabled" type="dark" @delete="deleteWesternMedicine(index)"></delete-icon>
                </div>
            </li>
        </ul>
    </div>
</template>

<script type="text/ecmascript-6">
    // vue lib
    import { mapGetters } from 'vuex';
    import DeleteIcon from '../../delete-icon/delete-icon';
    import AbcSortable from '@abc/sortable';

    // utils
    import {
        validateNumber, isShortage, isDisabledGoods,
    } from 'utils/validate';
    import { calWesternCount } from 'utils/calculation';
    import { getSpec } from 'src/filters/index';

    import SelectUsage from '../../select-group/index.vue';
    import GoodsRemark from 'views/layout/goods-remark/index.vue';
    import Dosage from '../common/dosage.vue';
    import MedicineAutoComplete from '../common/medicine-autocomplete.vue';
    import Count from '../common/count.vue';
    import CustomFreqDialog from '../common/custom-freq-dialog';
    import MedicalFeeGradeTd from '../common/medical-fee-grade-td.vue';
    import AstTd from '../common/ast-td.vue';
    import FormItemStatus from 'src/views/outpatient/common/form-item-status.vue';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import {
        needExpandRemark, showPsychotropicNarcoticTips,
    } from 'views/layout/prescription/utils.js';
    import { SearchSceneTypeEnum } from 'views/common/enum.js';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import { PharmacyTypeEnum } from '@abc/constants';
    import { isAllowAddByAntimicrobialDrugManagement } from 'views/outpatient/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import NeiMengPassPR from '@/pass-pharm-review';
    import PassAuditDot from '@/pass-pharm-review/pass-audit-dot.vue';

    const CustomFreqOptions = [
        {
            en: 'qnd',
            name: 'N天1次',
        },
        {
            en: 'qnw',
            name: 'N周1次',
        },
        {
            en: 'qnh',
            name: 'N小时1次',
        },
        {
            en: 'wn',
            name: '每周N次',
        },
    ];

    export default {
        name: 'PrescriptionTable',
        components: {
            MedicineAutoComplete,
            SelectUsage,
            GoodsRemark,
            Dosage,
            Count,
            CustomFreqDialog,
            DeleteIcon,
            AstTd,
            MedicalFeeGradeTd,
            FormItemStatus,
            PassAuditDot,
        },
        inject: {
            outpatientEditForm: {
                default: null,
            },
        },
        props: {
            verifyOutpatient: {
                type: Object,
                default: () => ({}),
            },
            formId: {
                type: String,
                default: '',
            },
            prescriptionForm: Object,
            disabled: {
                type: Boolean,
                default: false,
            },
            needCheckStock: {
                type: Boolean,
                default: true,
            },
            showDetailPrice: {
                type: Boolean,
                default: true,
            },
            value: Array,
            patientInfo: Object,
            medicalRecord: Object,
            treatOnlineClinicId: [String, Number],
            departmentId: String,
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            // 判断开出来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            /**
             是否展示医保登记
             */
            showMedicalFeeGrade: {
                type: Boolean,
                default: true,
            },
            doctorId: {
                type: String,
                default: '',
            },
            // 判断是否是处方模板
            isFromTemplate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                PharmacyTypeEnum,
                SearchSceneTypeEnum,
                customFreqOptions: CustomFreqOptions,
                validateNumber,
                currentEditIndex: -1,
                currentEditCustomFreqIndex: -1,
                currentEditCustomFreqType: null,
                historyHasAst: false, // 之前有开过皮试开关并完成提交，查看的时候需要展示
                dragging: false,
            };
        },
        computed: {
            ...mapGetters([
                'westernMedicineConfig',
                'currentClinic',
                'clinicBasic',
                'chainBasic',
                'isCanSeeGoodsCostPriceInOutpatient',
                'pharmacyRuleList',
                'userInfo',
                'chargeConfig',
            ]),
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),
            ...mapGetters('viewDistribute', [
                'featureSupportRatioPrice',
                'viewDistributeConfig',
            ]),
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustOutpatientPrice',
            ]),

            showPass() {
                return NeiMengPassPR.needPass;
            },

            // 单项议价权限: 开启了医生可单项议价&&当前医生有权限
            canSingleBargain() {
                return !!this.chargeConfig.doctorSingleBargainSwitch && this.employeeCanAdjustOutpatientPrice;
            },
            outpatientVDConfig() {
                return this.viewDistributeConfig?.Outpatient || {};
            },

            tableStyleConfig() {
                return this.outpatientVDConfig.tableStyleConfig.westernTable;
            },

            doctorSignImgUrl() {
                const { doctorSignImgUrl = '' } = this.outpatientEditForm || {};
                if (doctorSignImgUrl) return doctorSignImgUrl;
                const {
                    id: userId,
                    handSign,
                    handSignType,
                } = this.userInfo || {};
                if (handSignType === 1 && this.doctorId === userId && handSign) {
                    return handSign;
                }
                return '';
            },
            doctorName() {
                const { postData = {} } = this.outpatientEditForm || {};
                return postData.doctorName || '';
            },

            /**
             * 医生能否查看成本价
             */
            canViewCostPrice() {
                return this.isCanSeeGoodsCostPriceInOutpatient;
            },
            /**
             * @desc 能否选择 是否自费 payType
             * <AUTHOR>
             * @date 2021-07-19 18:23:14
             */
            showPayTypeSelect() {
                return (
                    this.$abcSocialSecurity.isOpenSocial &&
                    (
                        this.$abcSocialSecurity.config.isZhejiang ||
                        this.$abcSocialSecurity.config.isFujianFuzhou ||
                        this.$abcSocialSecurity.config.isLiaoningPanjin
                    )
                );
            },
            groupIdOptions() {
                let maxGroup = 0;
                const arr = [
                    {
                        label: '-',
                        value: null,
                    },
                ];
                this.formItems.forEach((it) => {
                    if (it.groupId > maxGroup) {
                        maxGroup = it.groupId;
                    }
                });

                //至多9
                maxGroup = Math.min(maxGroup, 8);

                for (let i = 0; i <= maxGroup; i++) {
                    arr.push({
                        label: i + 1,
                        value: i + 1,
                    });
                }

                return arr;
            },

            formItems() {
                return this.value;
            },
            freqList() {
                return this.defineArray('freq').concat(CustomFreqOptions);
            },
            formHasSpecialRequirement() {
                if (!this.disabled) return true;
                // 禁用状态下，没有填写过 备注/自备/切换药房 则影藏显示
                return this.formItems.some((item) => {
                    const defaultPharmacy = getDefaultPharmacy(this.pharmacyRuleList,{
                        departmentId: this.departmentId,
                        goodsInfo: item.productInfo,
                    });
                    return needExpandRemark(item, defaultPharmacy);
                });
            },

            /**
             * @desc 是否展示皮试输入
             * <AUTHOR>
             * @date 2021-05-12 11:15:53
             */
            astPrescriptionSwitch() {
                const {
                    astPrescription,
                } = this.outpatientEmployeeConfig;
                return astPrescription || {
                    westernSwitch: 0,
                    infusionSwitch: 1,
                };
            },

            // 华蓥某诊所设置，回车跳过单位
            prescriptionFocusDosage() {
                if (!this.chainBasic) return false;
                if (!this.chainBasic.outpatient) return false;
                return !!this.chainBasic.outpatient.prescriptionFocusDosage;
            },
        },
        created() {
            if (this.formId) {
                this.historyHasAst = this.formItems.some((it) => it.ast);
            }
        },
        mounted() {
            this.sortInstance = new AbcSortable({
                $el: this.$el.querySelector('#drag-list'),
                handle: 'drag-handle',
                onDragStart: this.onDragStart,
                onDragOver: this.onDragOver,
                onDragEnd: this.onDragEnd,
            });
        },
        beforeDestroy() {
            this.sortInstance && this.sortInstance.stopSort();
        },
        methods: {
            onDragStart() {
                this.dragging = true;
                $('#medicine-hover-popover').remove();
            },
            onDragOver() {
                $('#medicine-hover-popover').remove();
            },
            onDragEnd(event, dragState) {
                this.dragging = false;
                const {
                    dragNode,
                    dropNode,
                    dropType,
                } = dragState;
                if (!dragNode) return;
                if (!dropNode) return;
                if (dropType === 'none') return;
                const dragNodeId = dragNode.dataset.id;
                const dropNodeId = dropNode.dataset.id;

                const dragNodeIndex = this.formItems.findIndex((it) => {
                    const keyId = it.id || it.keyId;
                    return keyId === dragNodeId;
                });
                const dragNodeData = this.formItems.splice(dragNodeIndex, 1)[0];

                let dropNodeIndex = this.formItems.findIndex((it) => {
                    const keyId = it.id || it.keyId;
                    return keyId === dropNodeId;
                });

                if (dropType === 'after') {
                    dropNodeIndex += 1;
                }

                const prevGroupId = this.formItems[dropNodeIndex - 1]?.groupId;
                const nextGroupId = this.formItems[dropNodeIndex]?.groupId;

                this.formItems.splice(dropNodeIndex, 0, dragNodeData);

                // 前后组号一致，代表进组，直接用组号
                if (prevGroupId === nextGroupId) {
                    this.selectGroupId(nextGroupId, dropNodeIndex, false);
                    return;
                }

                // 拖拽到最后一个 无组号 元素
                if (prevGroupId === null && nextGroupId === undefined) {
                    this.selectGroupId(prevGroupId, dropNodeIndex, false);
                    return;
                }
                // 拖拽到第一个 无组号 元素
                if (nextGroupId === null && prevGroupId === undefined) {
                    this.selectGroupId(nextGroupId, dropNodeIndex, false);
                    return;
                }

                // 拖拽排序，needPreSort = false，不需要提前sort，先重置组号
                if (dragNodeData.groupId === prevGroupId || dragNodeData.groupId === nextGroupId) {
                    // 被拖拽元素和挨着相同组号拖拽，组号不变
                    this.selectGroupId(dragNodeData.groupId, dropNodeIndex, false);
                } else {
                    this.selectGroupId(this.groupIdOptions.length - 1, dropNodeIndex, false);
                }

            },

            handleChangeGroupId(item, index) {
                this.selectGroupId(item.groupId, index);
            },

            /**
             * @desc 选择组号
             * <AUTHOR>
             * @date 2018/07/10 15:35:12
             * @params groupId
             * @params index
             */
            selectGroupId(groupId, index, needPreSort = true) {
                const medicineCache = this.formItems[ index ];

                medicineCache.groupId = groupId;
                this.formItems.map((item) => {
                    if (groupId && item.groupId === groupId) {
                        medicineCache.usage = item.usage;
                        medicineCache.freq = item.freq;
                        medicineCache.days = item.days || null;
                        this.calWesternCountHandler(medicineCache);
                        return false;
                    }
                });
                // 重排sort
                this.sortHandler(needPreSort);
            },

            /**
             * @desc 重置sort
             * <AUTHOR>
             * @date 2018/04/19 15:16:42
             * @param {Boolean} needPreSort 是否需要提前重排位置 例如选择组号需要先排序；拖拽顺序已经确定，所以只需要重置组号
             */
            sortHandler(needPreSort = false) {

                if (needPreSort) {
                    this.formItems.forEach((item, idx) => {
                        item.sort = (item.groupId || 10) * 1000 + idx;
                    });
                    this.formItems.sort((a, b) => {
                        return a.sort - b.sort;
                    });
                }

                let curGoodsIdCache = undefined; // 记录上一次被变化的组号，如果下一个组号和它一致，就同步成前一个变化后的组号
                const { length } = this.formItems;
                let curGroupId = 1;
                // 不相等，不连续，需要重置groupId
                for (let index = 0; index < length; index++) {
                    const curItem = this.formItems[index];

                    // 包含groupId 的情况下需要重排groupId
                    if (curItem.groupId) {
                        if (curItem.groupId === curGoodsIdCache) {
                            curItem.groupId = curGroupId - 1;
                        } else {
                            curGoodsIdCache = curItem.groupId;
                            curItem.groupId = curGroupId;
                            curGroupId++;
                        }
                    }

                    curItem.sort = (curItem.groupId || 10) * 1000 + index;
                }
            },

            /**
             * @desc 药品禁用 || 库存不足 || 无库存信息
             * <AUTHOR> Yang
             * @date 2021-03-30 15:46:13
             */
            showStockWarnTips(item) {
                // 自备不校验库存
                if (item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE) return false;
                if (this.disabled || !this.needCheckStock) return false;
                //当为-1不提示
                return isDisabledGoods(item).flag || isShortage(item).flag || item.noStocks;
            },

            // 药品禁用 || 库存不足 || 无库存信息 || 精麻药提示
            showWarnTips(item) {
                // 自备不校验库存
                if (item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE) return false;
                if (this.disabled || !this.needCheckStock) return false;

                const isDisabled = isDisabledGoods(item).flag;
                const isShortageFlag = isShortage(item).flag;
                const { noStocks } = item;

                const isPsychotropicNarcotic = showPsychotropicNarcoticTips(item, this.verifyOutpatient);

                // 当为-1不提示
                return isDisabled || isShortageFlag || noStocks || isPsychotropicNarcotic;
            },

            getWarnTips(item) {
                const isPsychotropicNarcotic = showPsychotropicNarcoticTips(item, this.verifyOutpatient);
                const hasStockWarn = this.showStockWarnTips(item);
                const psychotropicNarcoticTips = '该药物为麻醉药品，精神药品等特殊管制药品，在线复诊时禁止开出，请从处方中删除';

                if (isPsychotropicNarcotic && hasStockWarn) {
                    return `${psychotropicNarcoticTips};${this.getStockWarnTips(item)}`;
                }

                if (isPsychotropicNarcotic) {
                    return psychotropicNarcoticTips;
                }

                if (hasStockWarn) {
                    return this.getStockWarnTips(item);
                }

                return '';
            },

            getStockWarnTips(item) {
                return isDisabledGoods(item).tips || isShortage(item).tips || '无库存';
            },

            getSpec,

            /**
             * @desc 点击 可以修改 cadn
             * <AUTHOR>
             * @date 2018/12/12 15:25:15
             */
            editCadn(index) {
                if (this.disabled) return false;
                this.currentEditIndex = index;
                this.$nextTick(() => {
                    $(this.$el.querySelectorAll('.table-tr')[index]).find('.medicine-autocomplete input').focus();
                });
            },
            handleDoubleClick(event) {
                if (!event.target.value) return;
                event.target.selectionStart = 0;
                event.target.selectionEnd = event.target.value.length;
            },
            closePanel() {
                this.currentEditIndex = -1;
            },

            /**
             * 判断开出的中西成药的抗菌等级是否满足开出条件
             * @param {Object} medicine
             * @return {boolean}
             */
            isAllowAdd(medicine) {
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                const isSuccess = isAllowAddByAntimicrobialDrugManagement(medicine, this.doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                if (!isSuccess) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: [medicine] }).generateDialogAsync({ parent: this });
                    }, 100);
                }
                return isSuccess;
            },

            /**
             * @desc 换药
             * <AUTHOR>
             * @date 2018/12/06 17:47:55
             */
            changeMedicine(newMedicine, index) {
                // 通过模板开出时不进行校验
                if (!this.isFromTemplate) {
                    // 判断切换的中西成药的抗菌等级是否满足开出条件
                    const isSuccess = this.isAllowAdd(newMedicine);
                    if (!isSuccess) return;
                }

                this.currentEditIndex = -1;

                const oldMedicine = this.formItems[ index ];

                newMedicine.sort = oldMedicine.sort;
                newMedicine.groupId = oldMedicine.groupId;

                this.formItems.splice(index, 1, newMedicine);

                this.calWesternCountHandler(newMedicine);

                this.$parent && this.$parent.$emit('queryVerify');
            },

            /**
             * @desc 含有组号的药品更改 用法、频率、天数后 应该更新 相同组号的每一个药，并计算开药量
             * <AUTHOR>
             * @date 2018/04/19 14:51:15
             */
            groupChange(medicine) {
                this.formItems.forEach((it) => {
                    if (medicine.groupId === it.groupId) {
                        it.usage = medicine.usage;
                        it.freq = medicine.freq;
                        it.days = medicine.days || null;
                        this.calWesternCountHandler(it);
                    }
                });
            },

            handleChangeUnit(medicine, useDismounting) {
                // 切换单位时，有天数 才进行 用量重新计算
                if (medicine.days) {
                    this.calWesternCountHandler(medicine, useDismounting);
                } else {
                    this.outpatientFeeChange();
                }
            },

            handleChangeUnitCount(medicine) {
                if (medicine.expectedTotalPrice) {
                    medicine.expectedTotalPrice = null;
                    if (medicine.sourceUnitPrice !== medicine.unitPrice) {
                        medicine.expectedUnitPrice = medicine.unitPrice;
                    }
                }
                this.$set(medicine, 'expectedTotalPriceRatio', null);
                this.$set(medicine, 'expectedTotalPrice', null);
                this.$set(medicine, 'unitAdjustmentFeeLastModifiedBy', null);
                this.outpatientFeeChange();
            },


            /**
             * @desc 帮用户计算开药量
             * <AUTHOR>
             * @date 2018/04/13 12:14:23
             * @params useDismounting 指定是否使用大单位
             */
            calWesternCountHandler(medicine, useDismounting) {
                medicine.days = medicine.days || null;
                if (!medicine.goodsId) return false;
                if (medicine.abandonCalcCount) return false;

                const {
                    settings: outpatientCalcSettings,
                } = this.clinicBasic.outpatient;

                const forceUsePackageUnit = useDismounting === 0;
                const res = calWesternCount(medicine, outpatientCalcSettings, forceUsePackageUnit);

                medicine.unitCount = res.unitCount;
                if (res.unitCount) {
                    medicine.unit = res.unit;
                    medicine.useDismounting = res.useDismounting;
                    const {
                        piecePrice,
                        packagePrice,
                    } = medicine.productInfo;
                    medicine.unitPrice = medicine.useDismounting ? piecePrice : packagePrice;
                }

                this.$set(medicine, 'expectedTotalPriceRatio', null);
                this.$set(medicine, 'expectedTotalPrice', null);
                this.$set(medicine, 'unitAdjustmentFeeLastModifiedBy', null);
                this.outpatientFeeChange();
            },

            /**
             * @desc 对西药数据进行默认值
             * <AUTHOR>
             * @date 2018/07/10 16:40:58
             */
            defineArray(key) {
                return this.westernMedicineConfig[ key ];
            },


            /**
             * @desc 选择西药用法，如果是"口服" 需要默认填上天数并重排顺序
             * <AUTHOR>
             * @date 2018/07/10 15:37:20
             * @params usage
             * @params index
             * @return
             */
            selectUsage(usage, index) {
                const medicineCache = this.formItems[ index ];
                medicineCache.usage = usage;
                this.formItems.forEach((it) => {
                    if (it.usage === usage) {
                        medicineCache.days = it.days || null;
                    }
                });
                if (medicineCache.groupId) {
                    this.groupChange(medicineCache);
                }
                this.calWesternCountHandler(medicineCache);
            },

            /**
             * @desc 选择西药频率
             * <AUTHOR>
             * @date 2018/04/14 19:18:55
             */
            selectFreq(freq, index) {
                if (!freq) return false;
                if (['qnd', 'qnw', 'qnh', 'wn'].indexOf(freq) > -1) {
                    this.selectCustomFreq(freq, index);
                    return false;
                }

                const medicineCache = this.formItems[ index ];
                medicineCache.freq = freq;
                if (medicineCache.groupId) {
                    this.groupChange(medicineCache);
                } else {
                    this.calWesternCountHandler(medicineCache);
                }
            },

            /**
             * @desc 改变天数会计算开药量，但是如果该药是该处方第一个口服药改了后会影响后面口服药的天数
             * <AUTHOR>
             * @date 2018/04/19 11:16:49
             */
            changeDays(medicine) {
                if (medicine.groupId) {
                    this.groupChange(medicine);
                } else {
                    this.calWesternCountHandler(medicine);
                }
            },

            /**
             * @desc 删除西药
             * @param index
             */
            deleteWesternMedicine(index) {
                if (this.disabled) return false;
                this.formItems.splice(index, 1);
                this.$parent && this.$parent.$emit('queryVerify');
                this.currentEditIndex = -1;

                // 重排序号
                this.sortHandler();

                this.outpatientFeeChange();
            },

            /**
             * @desc 比较同组药品是否有重复项目
             * <AUTHOR>
             * @date 2018/07/10 15:45:07
             * @params
             * @return
             */
            compareRepeat(item) {
                if (this.disabled) return false;
                let count = 0;
                this.formItems.forEach((m) => {
                    if (m.name && m.name === item.name && (m.goodsId || '') === (item.goodsId || '') && (m.groupId || '') === (item.groupId || '')) {
                        count++;
                    }
                });
                return count >= 2;
            },

            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e, $el, value) {
                if ($el &&
                    value &&
                    $el.className.indexOf('abc-select-wrapper') > -1 &&
                    ['qnd', 'qnw', 'qnh'].indexOf(value) > -1) {
                    return false;
                }
                // 找到所有的非disabled的input输入框
                const inputs = $('.prescription-table-wrapper.western-table').find('.abc-input__inner').not(':disabled');
                const targetIndex = inputs.index(e.target);
                let nextInput = inputs[ targetIndex + 1 ];
                if (nextInput.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }
                // 碰到连续tabindex===-1的情况，再加一次
                if (nextInput.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 3];
                }
                nextInput && this.$nextTick(() => {
                    nextInput.select && nextInput.select();
                    nextInput.focus && nextInput.focus();
                    // magic code
                    this._timer = setTimeout(() => {
                        nextInput.selectionStart = 0;
                        nextInput.selectionEnd = nextInput.value ? nextInput.value.length : 0;
                        nextInput.select && nextInput.select();
                        nextInput.focus && nextInput.focus();
                    }, 50);
                });
            },

            selectCustomFreq(freq, index) {
                this.$refs.freqSelector[index].showPopper = false;
                this.currentEditCustomFreqIndex = index;
                this.currentEditCustomFreqType = freq;
            },
            confirmCustomFreqHandle(val, index) {
                this.selectFreq(val, index);
                this.$nextTick(() => {
                    $(this.$refs.tableTr[ index ])
                        .find('.table-td.dosage input')
                        .eq(0)
                        .focus();
                });
            },
            closeCustomFreqDialog() {
                this.currentEditCustomFreqIndex = -1;
                this.currentEditCustomFreqType = null;
            },

            handleFocusRemark(item) {
                this.$set(item, 'expandSpecialRequirement', true);
            },
            handleBlurRemark(item) {
                this.$set(item, 'expandSpecialRequirement', false);
            },

            /**
             * @desc 门诊单内容变更
             * <AUTHOR>
             * @date 2022/06/06 10:47:44
             */
            outpatientFeeChange() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '西药处方',
                    needCalcFee: true,
                });
            },

            displayMedicalFeeGrade(item) {
                return this.showMedicalFeeGrade &&
                    this.isOpenSource &&
                    item.productInfo &&
                    item.productInfo.medicalFeeGrade;
            },
            displayTotalPriceRatio(item) {
                const { totalPriceRatio } = item;
                if (!totalPriceRatio || totalPriceRatio > 1) return '';
                return Math.round((totalPriceRatio ?? 1) * 100);
            },
            inputPriceRadioHandler(item, val) {
                this.$set(item, 'totalPriceRatio', val / 100);
            },
            changeTotalPrice(item) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPriceRatio', null);
                this.$set(item, 'expectedTotalPrice', item.totalPrice);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },
            changeTotalPriceRatio(item) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPriceRatio', item.totalPriceRatio);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },
            changePriceRadio(item, val) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'totalPriceRatio', val);
                this.$set(item, 'expectedTotalPriceRatio', val);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },
            isItemCanAdjustment(item) {
                if (!this.canSingleBargain) return false;
                return !!item.canAdjustment;
            },

            readonlyItem(item) {
                return !this.isItemCanAdjustment(item);
            },
        },
    };
</script>
