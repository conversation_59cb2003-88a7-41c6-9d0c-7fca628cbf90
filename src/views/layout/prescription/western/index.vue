<template>
    <div
        class="prescription-table-wrapper western-table"
        :class="[
            `western${ formIndex }`,
            {
                'is-disabled': disabledForm || isEexcuted,
                'has-special-requirement': hasSpecialRequirement,
            },
        ]"
        data-cy="中西成药处方"
    >
        <div class="prescription-header">
            <div class="prescription-title">
                <img class="icon-wrapper" src="~assets/images/icon/pill.png" alt="" />
                <h5>{{ _westernPrescriptionNameText }}{{ translateH }}</h5>
                <cooperation-pharmacy-vendors
                    v-if="!isConsultation && showCooperationPharmacy && cooperationPharmacyList.length"
                    style="margin-left: 12px;"
                    :pharmacy-type.sync="form.pharmacyType"
                    :pharmacy-no.sync="form.pharmacyNo"
                    :pharmacy-name.sync="form.pharmacyName"
                    :form-items="form.prescriptionFormItems"
                    :disabled="disabledForm"
                    :department-id="departmentId"
                    data-cy="pr-western-table-vendors"
                ></cooperation-pharmacy-vendors>
            </div>
            <div class="operation">
                <abc-text
                    v-if="showBasicDrugReminder"
                    theme="black"
                    size="mini"
                    style="display: inline-flex; align-items: center; margin-right: 6px;"
                >
                    <abc-icon
                        icon="s-b-info-circle-line"
                        :size="14"
                        color="var(--abc-color-P10)"
                        style="margin-right: 4px;"
                    ></abc-icon>
                    请优先使用基药
                </abc-text>
                <abc-flex
                    v-if="isOpenSource && showPsychotropicNarcoticEmployee"
                    align="center"
                    :gap="4"
                >
                    <abc-text theme="black" size="mini">
                        代办人：{{ psychotropicNarcoticEmployee.name || '-' }}
                    </abc-text>
                    <abc-button
                        v-if="!disabledForm"
                        data-cy="agent-button"
                        icon="s-b-edited-line"
                        variant="text"
                        theme="default"
                        size="small"
                        @click="onClickPsychotropicNarcoticEmployee(form.psychotropicNarcoticType)"
                    >
                    </abc-button>
                </abc-flex>
                <jing-ma-dropdown
                    v-model="form.psychotropicNarcoticType"
                    class="btn"
                    data-cy="pr-western-table-jing-ma-dropdown"
                    :disabled="disabledForm"
                    @change="changePsychotropicNarcoticType"
                ></jing-ma-dropdown>
                <abc-button
                    v-if="!disabledForm && !disabledAdd && isOpenSource && !form.pharmacyType"
                    data-cy="pr-western-table-operation-search"
                    variant="text"
                    theme="default"
                    size="small"
                    icon="s-list-search-line"
                    @click="showGoodsSelectDialog = true"
                >
                </abc-button>
                <abc-check-access :permission-keys="[ROLE_DOCTOR_ID, ROLE_DOCTOR_ASSIST_ID]">
                    <abc-button
                        v-if="isOpenSource"
                        variant="text"
                        theme="default"
                        size="small"
                        icon="save"
                        data-cy="pr-western-table-save"
                        style="margin-left: 0;"
                        @click="saveCommon"
                    >
                    </abc-button>
                </abc-check-access>
                <trash-button-v2
                    v-if="!(disabledForm || isEexcuted)"
                    :is-confirm="deleteNeedConfirm"
                    data-cy="pr-western-table-trash"
                    @delete="deletePres"
                >
                </trash-button-v2>
            </div>
        </div>

        <common-table
            v-if="form.prescriptionFormItems.length"
            v-model="form.prescriptionFormItems"
            :form-id="form.id"
            :prescription-form="form"
            :patient-info="patientInfo"
            :treat-online-clinic-id="treatOnlineClinicId"
            :medical-record="medicalRecord"
            :need-check-stock="needCheckStock"
            :is-open-source="isOpenSource"
            :show-detail-price="showDetailPrice"
            :verify-outpatient="verifyOutpatient"
            :disabled="disabledForm || isEexcuted"
            :shebao-card-info="shebaoCardInfo"
            :department-id="departmentId"
            :show-medical-fee-grade="showMedicalFeeGrade"
            :doctor-id="doctorId"
            :is-from-template="isFromTemplate"
            @expandSpecialRequirement="expandSpecialRequirementHandle"
            @queryVerify="$emit('queryVerify')"
            @change-pay-type="(val, item) => $emit('change-pay-type', val, item, form)"
        >
        </common-table>

        <div class="prescription-footer">
            <abc-form-item v-if="!(disabledForm || isEexcuted || disabledAdd)" style="flex: 1;">
                <medicine-auto-complete
                    ref="medicineAutoComplete"
                    :patient-info="patientInfo"
                    :show-detail-price="showDetailPrice"
                    :medical-record="medicalRecord"
                    :scene-type="SearchSceneTypeEnum.outpatient"
                    :department-id="departmentId"
                    :treat-online-clinic-id="treatOnlineClinicId"
                    :prescription-form-items="form.prescriptionFormItems"
                    :pharmacy-type="form.pharmacyType"
                    :pharmacy-no="form.pharmacyNo"
                    :need-check-stock="needCheckStock"
                    :shebao-card-info="shebaoCardInfo"
                    :is-open-source="isOpenSource"
                    data-cy="pr-western-medicine-autocomplete"
                    placement="bottom-start"
                    @select="selectWesternMedicine"
                ></medicine-auto-complete>
            </abc-form-item>

            <abc-flex :gap="12" class="total-price">
                <form-status v-if="disabled" :forms="[form]"></form-status>
                <span v-if="showTotalPrice" style=" font-weight: 400; color: var(--abc-color-T2);">
                    <span v-if="!disabled"><abc-money
                        :value="prescriptionTotal"
                        is-show-space
                        data-cy="pr-western-total-price"
                    ></abc-money></span>
                    <biz-charge-stat-popover
                        v-else
                        :title="`${_westernPrescriptionNameText}${translateH}`"
                        :forms="[form]"
                    >
                        <abc-money
                            :value="prescriptionTotal"
                            is-show-space
                            data-cy="pr-western-total-price"
                        ></abc-money>
                    </biz-charge-stat-popover>
                </span>
            </abc-flex>
        </div>

        <component
            :is="curCommonDialog"
            v-if="dialogVisible"
            v-model="dialogVisible"
            type="western"
            :prescription-form="form"
        ></component>

        <goods-select-dialog
            v-if="showGoodsSelectDialog"
            v-model="showGoodsSelectDialog"
            :default-category-key="CATEGORY_TYPE_ENUM.MEDICINE_WESTERN"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="departmentId"
            :category-range="categoryRange"
            @onSelectGoods="quickSelect"
        ></goods-select-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    // vue lib
    import { mapGetters } from 'vuex';

    // utils
    import { numToChinese } from 'utils/index';
    import {
        itemCountPrice, calWesternCount, sum,
    } from 'utils/calculation';

    // components
    import MedicineAutoComplete from '../common/medicine-autocomplete.vue';
    import CommonTable from './table.vue';
    import TrashButton from '../common/trash-button';
    import JingMaDropdown from '../common/jing-ma-dropdown.vue';
    import CommonPrescriptionDialog from '../common/common-prescription-dialog';
    import CommonPrescriptionHospitalDialog from '../common/common-prescription-hospital-dialog.vue';
    import BizChargeStatPopover from '@/components-composite/biz-charge-stat-popover';
    import FormStatus from 'src/views/outpatient/common/form-status.vue';
    import CooperationPharmacyVendors from 'views/outpatient/common/cooperation-pharmacy-vendors/index.vue';
    import GoodsSelectDialog from 'views/layout/goods-select-dialog/index.vue';

    // constants
    import {
        OutpatientChargeTypeEnum,
        PsychotropicNarcoticTypeEnum,
    } from 'views/outpatient/constants.js';
    import {
        getItemUnitPrice, isAllowAddByAntimicrobialDrugManagement,
    } from 'views/outpatient/utils.js';
    import {
        ExecuteStatus,
        SearchSceneTypeEnum,
    } from 'views/common/enum';
    import { ChargeFormStatusEnum } from '@/service/charge/constants.js';
    import {
        ROLE_DOCTOR_ASSIST_ID,
        ROLE_DOCTOR_ID,
    } from 'utils/constants';
    import { CATEGORY_TYPE_ENUM } from 'src/views/common/goods-search/constants';
    import {
        completePrescriptionItem, getPrescriptionItemStruct,
    } from 'views/layout/prescription/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import TrashButtonV2 from 'views/layout/prescription/common/trash-button-v2.vue';

    const categoryRange = [
        CATEGORY_TYPE_ENUM.MEDICINE_WESTERN,
        CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_PATENT,
    ];
    export default {
        name: 'WesternPrescription',
        components: {
            TrashButtonV2,
            GoodsSelectDialog,
            MedicineAutoComplete,
            CommonTable,
            TrashButton,
            JingMaDropdown,
            BizChargeStatPopover,
            FormStatus,
            CooperationPharmacyVendors,
        },
        props: {
            verifyOutpatient: {
                type: Object,
                default: () => ({}),
            },
            showTotalPrice: {
                type: Boolean,
                default: true,
            },
            showDetailPrice: {
                type: Boolean,
                default: true,
            },
            form: {
                type: Object,
                required: true,
            },
            disabled: Boolean,
            disabledAdd: Boolean,
            // 是否需要验证库存
            needCheckStock: {
                type: Boolean,
                default: true,
            },
            formIndex: Number,
            formsLength: Number,
            patientInfo: Object,
            medicalRecord: Object,
            status: Number,
            treatOnlineClinicId: [String, Number],
            // 判断开出来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            /**
             是否展示医保登记
             */
            showMedicalFeeGrade: {
                type: Boolean,
                default: true,
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            templateManagerVersion: {
                type: Number,
                default: 0,
            },
            departmentId: String,
            psychotropicNarcoticEmployee: {
                type: Object,
                default() {
                    return {};
                },
            },
            showCooperationPharmacy: Boolean,
            isConsultation: Boolean,
            doctorId: {
                type: String,
                default: '',
            },
            // 判断是否是处方模板
            isFromTemplate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                CATEGORY_TYPE_ENUM,
                categoryRange,
                ROLE_DOCTOR_ID,
                ROLE_DOCTOR_ASSIST_ID,
                SearchSceneTypeEnum,
                dialogVisible: false,
                expandSpecialRequirement: false,
                showGoodsSelectDialog: false,
            };
        },
        computed: {
            ...mapGetters([
                'westernMedicineConfig',
                'clinicBasic',
                'cooperationPharmacyList',
            ]),
            ...mapGetters('outpatientConfig', [
                'outpatientEmployeeConfig',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            showPsychotropicNarcoticEmployee() {
                return [
                    PsychotropicNarcoticTypeEnum.JING_1,
                    PsychotropicNarcoticTypeEnum.JING_2,
                    PsychotropicNarcoticTypeEnum.MA_ZUI,
                    PsychotropicNarcoticTypeEnum.DU,
                ].includes(this.form.psychotropicNarcoticType);
            },

            curCommonDialog() {
                if (this.templateManagerVersion === 1) return CommonPrescriptionHospitalDialog;
                return CommonPrescriptionDialog;
            },

            translateH() {
                if (this.formsLength === 1) return '';
                return numToChinese(this.formIndex + 1);
            },

            // 空处方删除不需要确认弹窗
            deleteNeedConfirm() {
                return this.form.prescriptionFormItems.some((item) => {
                    return item.goodsId || item.name;
                });
            },

            prescriptionTotal() {
                const {
                    chargeStatus,
                    receivedPrice,
                    receivableFee,
                    refundedFee,
                } = this.form;

                // 全收（包含退单、退费、部分退）显示实收，未收、部分收显示原价
                // form 上已收、部分收、部分退状态都是1，如果(应收 === 实收 + 已退)就是全收了
                if (
                    chargeStatus > ChargeFormStatusEnum.CHARGED ||
                    (
                        chargeStatus === ChargeFormStatusEnum.CHARGED &&
                        sum(receivableFee) === sum(receivedPrice, Math.abs(refundedFee))
                    )
                ) {
                    return receivedPrice;
                }
                // 未收部分收显示原价
                let total = 0;
                const _arr = [];
                this.form.prescriptionFormItems.forEach((item) => {
                    if (item.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE) {
                        // 有议价情况直接用currentUnitPrice，没有用unitPrice 计算
                        const unitPrice = getItemUnitPrice(item);
                        _arr.push(itemCountPrice(unitPrice, item.unitCount));
                        _arr.push(item.fractionPrice || 0);
                    }
                });
                total = sum(..._arr);
                return total || 0;
            },
            disabledForm() {
                return this.disabled;
            },

            hasSpecialRequirement() {
                return this.form.prescriptionFormItems.some((item) => {
                    return item.specialRequirement || item.expandSpecialRequirement;
                });
            },
            // 处方是否执行过
            isEexcuted() {
                return this.form.prescriptionFormItems.some((x) => x.executeStatus === ExecuteStatus.FINISHED);
            },
            showBasicDrugReminder() {
                const { basicDrugReminder } = this.outpatientEmployeeConfig;
                const { westernSwitch } = basicDrugReminder || {};
                return this.$abcSocialSecurity.config.isGuizhou && westernSwitch === 1;
            },
        },
        created() {
            const {
                westernPrescriptionNameText,
            } = this.viewDistributeConfig.Outpatient;
            this._westernPrescriptionNameText = westernPrescriptionNameText;
            this.$store.dispatch('initDoctorWesternPRRemarks');
        },
        methods: {
            /**
             * @desc 帮用户计算开药量
             * <AUTHOR>
             * @date 2018/04/13 12:14:23
             */
            calWesternCountHandler(medicine) {
                if (!medicine.goodsId) return false;

                const {
                    settings: outpatientCalcSettings,
                } = this.clinicBasic.outpatient;

                const res = calWesternCount(medicine, outpatientCalcSettings);
                medicine.unitCount = res.unitCount;
                if (res.unitCount) {
                    medicine.unit = res.unit;
                    medicine.useDismounting = res.useDismounting;
                    const {
                        piecePrice,
                        packagePrice,
                    } = medicine.productInfo;
                    medicine.unitPrice = medicine.useDismounting ? piecePrice : packagePrice;
                }
                this.outpatientFeeChange();
            },

            /**
             * 判断开出的中西成药的抗菌等级是否满足开出条件
             * @param {Object} medicine
             * @return {boolean}
             */
            isAllowAdd(medicine) {
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                const isSuccess = isAllowAddByAntimicrobialDrugManagement(medicine, this.doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                if (!isSuccess) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: [medicine] }).generateDialogAsync({ parent: this });
                    }, 100);
                }
                return isSuccess;
            },

            /** ----------------------------------------------------------------------
             * autocomplete 选择西药 || 通过回车录入西药
             * @param newMedicine
             */
            async selectWesternMedicine(newMedicine) {
                // 通过模板开出时不进行校验
                if (!this.isFromTemplate) {
                    const isSuccess = this.isAllowAdd(newMedicine);
                    if (!isSuccess) return;
                }

                newMedicine.sort = this.form.prescriptionFormItems.length;
                newMedicine.groupId = null;

                this.form.prescriptionFormItems.push(newMedicine);

                this.calWesternCountHandler(newMedicine);

                this.$emit('queryVerify');
            },

            /** ----------------------------------------------------------------------
             *  删除西药药处方的按钮
             */
            deletePres() {
                this.$emit('close', this.formIndex);
            },

            // 保存模板
            saveCommon() {
                if (this.form.prescriptionFormItems.length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '处方药品为空，不能保存为模板',
                    });
                    return false;
                }
                this.dialogVisible = true;
            },

            expandSpecialRequirementHandle(val) {
                this.expandSpecialRequirement = val;
            },

            onClickPsychotropicNarcoticEmployee(value) {
                if (!this.isOpenSource) return;
                this.$abcEventBus.$emit('psychotropic-narcotic-info-input', value);
            },

            async quickSelect(goodsList) {
                goodsList.forEach(async (goods) => {
                    const prescriptionItem = getPrescriptionItemStruct(goods);
                    this.selectWesternMedicine(prescriptionItem);
                    await completePrescriptionItem({
                        goods,
                        prescriptionItem,
                        patientInfo: this.patientInfo,
                        physicalExamination: this.medicalRecord.physicalExamination,
                        prescriptionFormItems: this.form.prescriptionFormItems,
                        westernMedicineConfig: this.westernMedicineConfig,
                    });
                });
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.medicineAutoComplete.focusInput(this);
                }, 400);
            },
            outpatientFeeChange() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '西药处方',
                    needCalcFee: true,
                });
            },
            changePsychotropicNarcoticType() {
                this.$emit('queryVerify');
                this.outpatientFeeChange();
            },
        },
    };
</script>
<style module lang="scss" src="@/styles/theme.module.scss"></style>
