<template>
    <div class="count-input-select-wrapper" :class="{ 'is-disabled': disabled }">
        <abc-form-item
            :required="!disabled"
            style="width: calc(100% - 40px);"
            :style="tableStyleConfig.dosageCount"
            :validate-event="validateNumber"
        >
            <abc-input
                v-model="currentMedicine.unitCount"
                v-abc-focus-selected
                :disabled="disabled"
                :placeholder="placeholder"
                size="small"
                class="count-center"
                type="number"
                :config="countConfig(currentMedicine)"
                data-cy="input-count"
                @enter="enterEvent"
                @change="$emit('change')"
                @input="$emit('input')"
            >
            </abc-input>
        </abc-form-item>

        <abc-form-item
            :required="!disabled"
            style="width: 40px; margin-left: -1px;"
            :style="tableStyleConfig.dosageUnit"
        >
            <abc-input
                v-if="disabled"
                v-model="currentMedicine.unit"
                adaptive-width
                disabled
            >
            </abc-input>
            <template v-else>
                <!--诊所药品-->
                <abc-select
                    v-if="currentMedicine.goodsId && currentMedicine.productInfo"
                    v-model="currentMedicine.unit"
                    adaptive-width
                    custom-class="prescription-select-options"
                    :disabled="disabled"
                    focus-show-options
                    :tabindex="unitTabindex"
                    data-cy="select-count-unit"
                    @enter="enterEvent"
                    @change="selectUnit"
                >
                    <abc-option
                        v-for="it in unitArray(currentMedicine)"
                        :key="it.name"
                        :label="it.name"
                        :value="it.name"
                        :data-cy="`option-count-unit-${it.name}`"
                    >
                    </abc-option>
                </abc-select>

                <!--系统药品：显示domain表的所有单位-->
                <select-usage
                    v-else
                    v-model="currentMedicine.unit"
                    v-abc-focus-selected
                    type="dosageFormUnit"
                    style="width: 40px;"
                    :disabled="disabled"
                    focus-show-options
                    :tabindex="unitTabindex"
                    @enter="enterEvent"
                    @change="selectUnit"
                >
                </select-usage>
            </template>
        </abc-form-item>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import {
        validateStock, validateNumber,
    } from 'utils/validate';
    import SelectUsage from '../../select-group/index.vue';
    import { clearItemBargainHandler } from 'views/outpatient/utils.js';

    export default {
        name: 'AbcWMCount',
        components: {
            SelectUsage,
        },
        props: {
            medicine: Object,
            styleString: String,
            disabled: Boolean,
            needCheckStock: Boolean,
            placeholder: String,
            unitTabindex: {
                type: Number,
                default: 1,
            },
        },
        data() {
            return {
                validateNumber,
                validateStock,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            outpatientVDConfig() {
                return this.viewDistributeConfig?.Outpatient || {};
            },

            tableStyleConfig() {
                return this.outpatientVDConfig.tableStyleConfig.westernTable;
            },
            currentMedicine: {
                get() {
                    return this.medicine;
                },
                set(val) {
                    this.$emit('update:medicine', val);
                },
            },
        },
        watch: {
            'medicine.unit': function(oldVal, newVal) {
                if (oldVal && newVal && oldVal !== newVal) {
                    // 单位的变化会导致单价不同，需要清空议价
                    clearItemBargainHandler(this.medicine);
                }
            },
        },
        methods: {
            /**
             * @desc 开药量 是否允许输入 小数的判断
             * @desc 允许拆零的西药/中成药，当开药量单位选择 小单位 且 小单位是ml 时，可以输入 3位小数
             * <AUTHOR>
             * @date 2019/03/24 12:44:49
             */
            countConfig(item) {
                const result = {
                    max: 999999,
                    supportZero: true,
                    formatLength: 2,
                };

                if (!item.productInfo) {
                    return result;
                }
                const dismounting = !!item.productInfo.dismounting; // 是西药/中成药
                const isFormUnit = item.unit === item.productInfo.pieceUnit; // 开药量单位选择小单位
                const formUnitIsML = item.productInfo.pieceUnit === 'ml'; // 开药量单位选择小单位

                if (dismounting && isFormUnit && formUnitIsML) {
                    Object.assign(result, { formatLength: 3 });
                }

                return result;
            },

            /**
             * @desc 显示输入框，包装单位和制剂单位可选（盒、片），输入时允许用户自定义，提交时验证是否为包装单位或制剂单位，提示文案：需要填入“盒”或“片”
             * <AUTHOR>
             * @date 2018/04/13 11:01:33
             */
            unitArray(wm) {
                const res = [];
                if (!wm.productInfo) return res;

                const { dismounting } = wm.productInfo;
                const { pieceUnit } = wm.productInfo;
                const { packageUnit } = wm.productInfo;

                if (dismounting) {
                    if (pieceUnit) {
                        res.push({ 'name': pieceUnit });
                    }
                    if (packageUnit && packageUnit !== pieceUnit) {
                        res.push({ 'name': packageUnit });
                    }
                } else {
                    res.push({ 'name': packageUnit });
                }
                return res;
            },

            enterEvent(e) {
                this.$emit('enter', e);
            },

            /**
             * @desc 选择西药开药量单位 由单位决定该药是否使用拆零
             * <AUTHOR>
             * @date 2018/07/10 15:40:20
             * @param unit 新值
             * @param index
             * @param oldVal 老值
             */
            selectUnit(unit, index, oldVal) {
                this.currentMedicine.unit = unit;

                const {
                    pieceUnit,
                    packageUnit,
                    piecePrice,
                    packagePrice,
                    dismounting,
                } = this.currentMedicine.productInfo || {};

                // 当选择的不是小单位，如果数量是小数，则要清空数量
                if (this.currentMedicine.unit !== pieceUnit &&
                    ~~this.currentMedicine.unitCount !== +this.currentMedicine.unitCount) {
                    this.currentMedicine.unitCount = '';
                }

                this.currentMedicine.useDismounting = +(dismounting &&
                    this.currentMedicine.unit === pieceUnit &&
                    this.currentMedicine.unit !== packageUnit);

                this.currentMedicine.unitPrice = this.currentMedicine.useDismounting ? piecePrice : packagePrice;

                if (unit === oldVal) return;
                this.$emit('changeUnit', this.currentMedicine, this.currentMedicine.useDismounting);
            },

        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .count-input-select-wrapper {
        display: flex;
        align-items: center;

        &:not(.is-disabled):hover .abc-form-item {
            &:first-child .abc-input__inner {
                z-index: 1;
            }

            &:last-child .abc-input__inner {
                background-color: #e9f2fe;
            }

            .abc-input__inner {
                border-color: $theme3;
            }
        }

        .abc-form-item {
            &:first-child {
                .abc-input__inner {
                    padding: 3px 2px 3px 2px;
                    font-size: 16px;
                    text-align: right;
                }
            }

            &:last-child {
                .abc-input__inner {
                    padding-right: 0;
                    padding-left: 8px;
                    text-align: left;
                }

                .iconfont {
                    display: none;
                }
            }

            &:focus,
            &:active {
                z-index: 2;
            }

            &.is-error {
                .count-center .abc-input__inner {
                    border-color: $Y2;
                }
            }
        }
    }
</style>
