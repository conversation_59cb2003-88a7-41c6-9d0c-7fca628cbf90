<template>
    <div class="dosage-input-select-wrapper" :class="{ 'is-disabled': disabled }">
        <abc-form-item
            :required="!readonly && !disabled"
            :style="dosageCountStyle"
            :validate-event="validateNumber"
        >
            <div
                v-if="isFraction && !isEditing"
                tabindex="0"
                class="fraction-display abc-input__inner"
                @click="editFractionDosage"
                @focus="editFractionDosage"
                v-html="fractionDosageHtml"
            ></div>
            <abc-input
                v-else
                ref="dosageInput"
                v-model="currentMedicine.dosage"
                v-abc-focus-selected
                :disabled="disabled"
                :readonly="readonly"
                type="number"
                class="count-center"
                :placeholder="placeholder"
                :input-custom-style="readonly ? {
                    padding: 0, border: 0
                } : {}"
                :config="{
                    formatLength: 4, supportFraction: true
                }"
                data-cy="input-dosage"
                @focus="isEditing = true"
                @blur="handleBlur"
                @enter="enterEvent"
                @change="$emit('change')"
            >
            </abc-input>
        </abc-form-item>

        <abc-form-item
            :required="!disabled"
            :style="dosageUnitStyle"
        >
            <abc-input
                v-if="disabled"
                v-model="currentMedicine.dosageUnit"
                adaptive-width
                disabled
            >
            </abc-input>
            <template v-else>
                <!--诊所药品：只显示剂量单位（ml）和制剂单位（片），默认为剂量单位，
                其次为制剂单位，如果剂量单位为空，则只显示制剂单位-->
                <abc-select
                    v-if="hasDosageUnit"
                    v-model="currentMedicine.dosageUnit"
                    adaptive-width
                    custom-class="prescription-select-options"
                    :disabled="disabled"
                    :tabindex="unitTabindex"
                    focus-show-options
                    data-cy="select-dosage-unit"
                    @enter="enterEvent"
                    @change="selectDosageUnit"
                >
                    <abc-option
                        v-for="it in dosageUnitArray(currentMedicine)"
                        :key="it.name"
                        :label="it.name"
                        :value="it.name"
                        :data-cy="`dosage-option-${it.name}`"
                    >
                    </abc-option>
                </abc-select>

                <!--系统药品：显示domain表的所有单位-->
                <select-usage
                    v-else
                    v-model="currentMedicine.dosageUnit"
                    v-abc-focus-selected
                    type="allDosageUnit"
                    focus-show-options
                    :disabled="disabled"
                    :tabindex="unitTabindex"
                    @enter="enterEvent"
                    @change="selectDosageUnit"
                >
                </select-usage>
            </template>
        </abc-form-item>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import {
        validateStock, validateNumber,
    } from 'utils/validate';
    import SelectUsage from '../../select-group/index.vue';
    import { unique } from 'utils/lodash';

    export default {
        name: 'AbcWMDosage',
        components: {
            SelectUsage,
        },
        props: {
            medicine: Object,
            disabled: Boolean,
            placeholder: String,
            unitTabindex: {
                type: Number,
                default: 1,
            },
        },
        data() {
            return {
                validateNumber,
                validateStock,
                isEditing: false,
                isFraction: false,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            currentMedicine: {
                get() {
                    return this.medicine;
                },
                set(val) {
                    this.$emit('update:medicine', val);
                },
            },
            outpatientVDConfig() {
                return this.viewDistributeConfig?.Outpatient || {};
            },

            tableStyleConfig() {
                return this.outpatientVDConfig.tableStyleConfig.westernTable;
            },
            readonly() {
                return this.currentMedicine.dosageUnit === '适量';
            },
            fractionDosageHtml() {
                // 处理 1 1/3 这种形式
                const _dosageArr = this.currentMedicine.dosage.split(' ');
                let _arr = [];
                let intStr = '';
                if (_dosageArr.length === 2) {
                    intStr = _dosageArr[0];
                    _arr = _dosageArr[1].split('/');
                } else {
                    _arr = _dosageArr[0].split('/');
                }
                const numerator = _arr[0];
                const denominator = _arr[1];
                return `${intStr}<sup class="numerator">${numerator}</sup>&frasl;<sub class="denominator">${denominator}</sub>`;
            },

            hasDosageUnit() {
                const {
                    goodsId,
                    productInfo,
                } = this.currentMedicine;
                if (!goodsId) return false;
                if (!productInfo) return false;
                const {
                    componentContentUnit,
                    medicineDosageUnit,
                    pieceUnit,
                } = productInfo;
                return componentContentUnit || medicineDosageUnit || pieceUnit;
            },
            dosageUnitStyle() {
                if (this.readonly) {
                    return {
                        width: 'calc(100% - 1px)',
                    };
                }
                const { dosageUnit } = this.tableStyleConfig;
                return {
                    width: '40px',
                    marginLeft: '-1px',
                    ...dosageUnit,
                };
            },
            dosageCountStyle() {
                if (this.readonly) {
                    return {
                        width: 0,
                    };
                }
                const { dosageCount } = this.tableStyleConfig;

                return {
                    width: 'calc(100% - 40px)',
                    ...dosageCount,
                };
            },
        },
        watch: {
            'currentMedicine.dosage': function() {
                this.changeDosage();
                this.getIsFraction();
            },
        },
        created() {
            this._filterUsage = [
                '外用',
                '滴入',
                '滴眼',
                '滴鼻',
                '滴耳',
                '喷入',
                '口腔喷入',
                '鼻腔喷入',
                '直肠给药',
                '含漱',
                '涂抹',
                '塞肛用',
                '阴道用',
            ];
            this.getIsFraction();

            // 兼容sc/cdss/medicine/usage返回了单位滴
            if (this.currentMedicine.dosageUnit === '滴' && this.needAddUnitDi(this.currentMedicine)) {
                this.$set(this.currentMedicine, 'abandonCalcCount', true);
            }
        },
        methods: {
            editFractionDosage() {
                if (this.disabled || this.readonly) return false;
                this.isFraction = false;
                this.isEditing = true;
                this.$nextTick(() => {
                    this.$refs.dosageInput.$el.querySelector('input').focus();
                });
            },

            handleBlur() {
                this.isEditing = false;
                this.getIsFraction();
            },

            getIsFraction() {
                const fractionRegExp = new RegExp(/^([1-9]\s)?[1-9][/][1-9]$/);
                // 是分数 1 1/3 这种形式
                if (fractionRegExp.test(this.currentMedicine.dosage)) {
                    this.isFraction = true;
                    // 是用户change触发的才触发回车事件跳到下一个输入框
                    if (this.isEditing && this.$refs.dosageInput) {
                        const $input = this.$refs.dosageInput.$el.querySelector('input');
                        this.enterEvent({ target: $input });
                    }
                    this.isEditing = false;
                }
            },

            /**
             * @desc 对单位【滴】特殊处理
             * @desc 当选择用法为 [滴眼、滴鼻、滴耳] && goods本身不包含单位滴 需要添加单位【滴】 此时不计算
             * @param {Object} wm
             * @return {Boolean}
             */
            needAddUnitDi(wm) {
                const {
                    componentContentUnit,
                    medicineDosageUnit,
                    pieceUnit,
                } = wm.productInfo || {};
                return ['滴眼', '滴鼻', '滴耳'].indexOf(wm.usage) > -1 &&
                    [componentContentUnit, medicineDosageUnit, pieceUnit].indexOf('滴') === -1;
            },

            /**
             * @desc 成份含量单位 剂量单位（ml）和制剂单位（片）特殊用法需要展示 适量
             * <AUTHOR>
             * @date 2018/04/13 10:33:47
             */
            dosageUnitArray(wm) {
                const {
                    componentContentUnit,
                    medicineDosageUnit,
                    pieceUnit,
                } = wm.productInfo;
                let res = [componentContentUnit,medicineDosageUnit,pieceUnit];
                res = unique(res.filter((it) => it));
                res = res.map((it) => {
                    return {
                        name: it,
                    };
                });

                if (this.needAddUnitDi(wm)) {
                    res.push({ name: '滴' });
                }

                if (this._filterUsage.indexOf(wm.usage) > -1) {
                    res.push({ name: '适量' });
                }
                const dosageUnitUnitInRes = res.some((it) => it.name === wm.dosageUnit);
                if (!dosageUnitUnitInRes) {
                    wm.dosageUnit = res[0].name;
                    wm.abandonCalcCount = false;
                    this.$emit('calCount', this.currentMedicine);
                }
                return res;
            },

            enterEvent(e) {
                this.$emit('enter', e);
            },

            /**
             * @desc 改变西药dosage
             * <AUTHOR>
             * @date 2018/04/13 12:39:59
             */
            changeDosage() {
                this.$emit('calCount', this.currentMedicine);
            },

            /**
             * @desc 选择西药计量单位 如果选择"适量"需要清除dosage
             * <AUTHOR>
             * @date 2018/07/10 15:33:10
             */
            selectDosageUnit(dosageUnit) {
                this.currentMedicine.dosageUnit = dosageUnit;
                this.$set(this.currentMedicine, 'abandonCalcCount', false);
                if (dosageUnit === '适量') {
                    this.currentMedicine.dosage = '';
                } else if (dosageUnit === '滴') {
                    if (this.needAddUnitDi(this.currentMedicine)) {
                        // 当选中添加的单位【滴】，添加flag不计算
                        this.$set(this.currentMedicine, 'abandonCalcCount', true);
                    } else {
                        // goods自带的【滴】正常计算
                        this.$emit('calCount', this.currentMedicine);
                    }
                } else {
                    this.$emit('calCount', this.currentMedicine);
                }
                this.$emit('change');
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .dosage-input-select-wrapper {
        display: flex;
        align-items: center;

        &:not(.is-disabled):hover .abc-form-item {
            &:first-child .abc-input__inner {
                z-index: 1;
            }

            &:last-child .abc-input__inner {
                background-color: #e9f2fe;
            }

            .abc-input__inner {
                border-color: $theme3;
            }
        }

        .fraction-display {
            display: flex;
            align-items: center;
            justify-content: center;

            .numerator {
                top: -4px;
                margin-left: 2px;
            }

            .denominator {
                bottom: -4px;
            }
        }

        .abc-form-item {
            &:first-child {
                .abc-input__inner {
                    padding: 3px 5px 3px 3px;
                    font-size: 16px;
                    text-align: right;
                }
            }

            &:last-child {
                .abc-input__inner {
                    padding-right: 0;
                    padding-left: 8px;
                    text-align: left;
                }

                .iconfont {
                    display: none;
                }
            }

            &:focus,
            &:active {
                z-index: 2;
            }

            &.is-error {
                .count-center .abc-input__inner {
                    border-color: $Y2;
                }
            }
        }
    }
</style>
