<template>
    <div v-abc-click-outside="cancelHandle" class="custom-freq-dialog-wrapper">
        <div class="custom-freq-content">
            <!-- 每天n次 -->
            <template v-if="customType === 'nid'">
                每1天
                <abc-input
                    v-model="count"
                    v-abc-auto-focus
                    :width="50"
                    type="number"
                    size="small"
                    :max-length="2"
                    :config="{ max: 99 }"
                    :input-custom-style="{
                        'text-align': 'center',
                        'height': '32px',
                        'margin': '0 6px',
                        'border-color': $style.P1,
                    }"
                    tabindex="-1"
                    @enter="confirmHandle"
                    @input="handleFreqInput"
                ></abc-input>
                次
            </template>

            <template v-else-if="isWn">
                每周
                <abc-input
                    v-model="count"
                    v-abc-auto-focus
                    :width="50"
                    type="number"
                    size="small"
                    :max-length="2"
                    :config="{ max: 99 }"
                    :input-custom-style="{
                        'text-align': 'center',
                        'height': '32px',
                        'margin': '0 6px',
                        'border-color': $style.P1,
                    }"
                    tabindex="-1"
                    @enter="confirmHandle"
                ></abc-input>
                次
            </template>

            <template v-else>
                每
                <abc-input
                    v-model="count"
                    v-abc-auto-focus
                    :width="50"
                    type="number"
                    size="small"
                    :max-length="2"
                    :config="{ max: 99 }"
                    :input-custom-style="{
                        'text-align': 'center',
                        'height': '32px',
                        'margin': '0 6px',
                        'border-color': $style.P1,
                    }"
                    tabindex="-1"
                    @enter="confirmHandle"
                ></abc-input>
                {{ freqDescribe }}
            </template>
            <span>{{ freqStr }}</span>
        </div>
        <div>
            <abc-button @click="confirmHandle">
                确定
            </abc-button>
            <abc-button type="blank" @click="cancelHandle">
                取消
            </abc-button>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    export default {
        name: 'CustomFreqDialog',
        props: {
            customType: {
                type: String,
            },
        },
        data() {
            return {
                count: '',
            };
        },
        computed: {
            freqDescribe() {
                switch (this.customType) {
                    case 'qnd':
                        return '天1次';
                    case 'qnw':
                        return '周1次';
                    case 'qnh':
                        return '小时1次';
                    default:
                        return '';
                }
            },
            freqStr() {
                if (!this.count) return '';
                switch (this.customType) {
                    case 'qnd':
                        if (+this.count === 1) {
                            return 'qd';
                        } if (+this.count === 2) {
                            return 'qod';
                        }
                        return `q${this.count}d`;

                    case 'qnw':
                        if (+this.count === 1) {
                            return 'qw';
                        }
                        return `q${this.count}w`;

                    case 'qnh':
                        return `q${this.count}h`;
                    case 'nid':
                        return `${this.getNidFreqStr(this.count)}`;
                    case 'wn':
                        return `w${this.count}`;
                    default:
                        return '';
                }
            },
            isWn() {
                return this.customType === 'wn';
            },
        },
        methods: {
            confirmHandle() {
                this.$emit('confirm', this.freqStr);
                this.cancelHandle();
            },
            cancelHandle() {
                this.$emit('close');
            },

            getNidFreqStr(count) {
                const map = {
                    1: 'q',
                    2: 'bi',
                    3: 'ti',
                    4: 'qi',
                };
                console.debug(count,map[count]);
                return map[count] ? `${map[count]}d` : `${count}id`;
            },

            handleFreqInput(v) {
                const reg = /0+(?=[1-9])|^0+$/g;
                const _v = v.replace(reg, '');
                this.count = _v;
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss"></style>
<style lang="scss">
    @import 'src/styles/theme.scss';

    .custom-freq-dialog-wrapper {
        position: relative;
        z-index: 9;
        width: 196px;
        height: 100px;
        padding: 12px;
        background: #ffffff;
        border: 1px solid #b7b9c2;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

        .custom-freq-content {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            > span {
                margin-left: auto;
            }
        }

        button {
            min-width: 78px;
        }
    }
</style>
