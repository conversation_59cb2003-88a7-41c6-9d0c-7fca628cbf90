<template>
    <div
        v-abc-click-outside="
            () => {
                showPopper = false;
            }
        "
        data-cy="btn-trash"
        @click="handleClick"
    >
        <div ref="reference">
            <abc-button
                icon="trash"
                variant="text"
                theme="default"
                size="small"
            >
            </abc-button>
        </div>

        <div
            v-show="showPopper"
            ref="options"
            class="confirm-popper-over"
            :class="customPopoverClass"
        >
            <abc-button
                type="text"
                size="small"
                class="confirm-btn"
                @click="confirmDelete"
            >
                确认删除
            </abc-button>
            <div class="cut-line"></div>
            <abc-button
                type="text"
                size="small"
                class="cancel-btn"
                @click.stop="showPopper = false"
            >
                取消
            </abc-button>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import Popper from 'utils/vue-popper';

    export default {
        name: 'TrashButtonV2',
        mixins: [ Popper ],
        props: {
            isConfirm: {
                type: Boolean,
                default: true,
            },
            customPopoverClass: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                currentPlacement: 'right',
            };
        },
        watch: {
            showPopper(val) {
                this.$emit('show-confirm', val);
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.options;
            this.referenceElm = this.$refs.reference;
        },
        beforeDestroy() {
            this.destroyPopper();
        },
        methods: {
            handleClick() {
                if (this.isConfirm) {
                    this.showPopper = !this.showPopper;
                    return;
                }
                this.$emit('delete');
            },
            confirmDelete() {
                this.$emit('delete');
                this.showPopper = false;
            },
        },
    };
</script>

<style lang="scss" scoped>
@import 'src/styles/theme.scss';

.trash-btn {
    .iconfont {
        font-size: 14px;
        color: #96a4b3;
        cursor: pointer;

        &.expand,
        &:hover {
            color: $theme2;
        }
    }
}

.confirm-popper-over {
    position: absolute;
    top: -4px;
    left: -130px;
    z-index: 1992;
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 8px;
    margin-left: 10px;
    font-size: 12px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(206, 208, 218, 1);
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.24);

    .confirm-btn {
        color: $R2;
    }

    .cut-line {
        width: 1px;
        height: 18px;
        margin: 0 2px;
        background-color: $P6;
    }

    &::before {
        position: absolute;
        top: 12px;
        left: -6px;
        width: 10px;
        height: 10px;
        content: '';
        background: #ffffff;
        border: 1px solid #dadbe0;
        border-top: 0;
        border-right: 0;
        border-radius: 0;
        transform: rotate(45deg);
    }
}
</style>
