<template>
    <div>
        <!--常用处方-->
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            append-to-body
            title="保存为处方模板"
        >
            <div class="dialog-content clearfix">
                <abc-form
                    ref="prescriptionForm"
                    item-block
                    label-position="left"
                    :label-width="72"
                >
                    <abc-form-item label="模板名" required :validate-event="validateName">
                        <abc-input
                            v-model="postData.name"
                            trim
                            :width="300"
                            :max-length="20"
                        ></abc-input>
                    </abc-form-item>

                    <abc-form-item label="目录">
                        <abc-radio-group v-model="selectedCategory" @change="changeType">
                            <abc-radio :label="TemplateCategoryEnum.PERSONAL">
                                个人
                            </abc-radio>
                            <abc-radio :label="TemplateCategoryEnum.DEPARTMENT">
                                科室
                            </abc-radio>
                        </abc-radio-group>
                    </abc-form-item>


                    <abc-form-item label="上级目录" required style="margin: 0;">
                        <abc-popover
                            ref="catalogues-selector"
                            class="catalogues-selector"
                            trigger="click"
                            placement="bottom"
                            width="auto"
                            theme="white"
                            :visible-arrow="false"
                            :popper-style="{ padding: 0 }"
                        >
                            <div slot="reference" style="padding-bottom: 4px;">
                                <abc-input v-model="parentNodeName" :width="300" readonly></abc-input>
                            </div>
                            <div class="catalogues-folder-popover" style="width: 300px;">
                                <div class="catalogues-folder-content">
                                    <abc-tree
                                        :data="folder"
                                        :max-depth="2"
                                        :indent="18"
                                        style="padding: 0;"
                                        @node-click="selectFolder"
                                    >
                                        <template #default="{ node }">
                                            <div class="custom-node-wrapper" style="cursor: pointer;">
                                                <img src="~assets/images/<EMAIL>" alt="" />
                                                <span>{{ node.name }}</span>
                                            </div>
                                        </template>
                                    </abc-tree>
                                </div>
                                <div class="add-catalogues-footer">
                                    <abc-button icon="plus_thin" variant="text" @click.stop="handleClickAddFolderDialog">
                                        新增目录
                                    </abc-button>
                                </div>
                            </div>
                        </abc-popover>
                    </abc-form-item>
                </abc-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <abc-button
                    :loading="buttonLoading"
                    @click="saveCommonPrescription('prescriptionForm')"
                >
                    确定
                </abc-button>
                <abc-button variant="ghost" :plain="true" @click="showDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <hospital-add-dialog
            v-if="showAddFolderDialog"
            v-model="showAddFolderDialog"
            :max-depth="maxDepth"
            :add-root-title="addRootTitle"
            :template-scene-type="TemplateSceneTypeEnum.PRESCRIPTION"
            :default-category="addDefaultCategory"
            :category="selectedCategory"
            :is-folder="1"
            :catalogues="folder"
            @add-success="addSuccess"
        ></hospital-add-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import clone from 'utils/clone';
    import { mapGetters } from 'vuex';
    /**
     * @desc mixins
     * <AUTHOR>
     * @date 2020/05/11 09:44:27
     */
    import prescriptionHandle from 'src/views/outpatient/mixins/prescription-handle';
    import { PharmacyTypeEnum } from '@abc/constants';
    import { CategoryTemplateAPI } from 'api/catalogue-template.js';
    import HospitalAddDialog from 'src/views/layout/templates-manager/hospital/add-dialog.vue';
    import {
        CATALOGUE_FILE_OWNER_TYPE, TemplateCategoryEnum, TemplateSceneTypeEnum,
    } from 'utils/constants.js';

    export default {
        components: {
            HospitalAddDialog,
        },
        mixins: [
            prescriptionHandle,
        ],
        props: {
            type: {
                required: true,
            },
            prescriptionForm: {
                type: Object,
                required: true,
            },
            value: Boolean,
        },
        data() {
            return {
                TemplateCategoryEnum,
                TemplateSceneTypeEnum,
                commonPrescriptionName: '',
                personalCatalogues: [], // 个人
                departmentCatalogues: [], // 科室
                parentNodeName: '',
                buttonLoading: false,

                selectedCategory: TemplateCategoryEnum.PERSONAL,

                postData: {
                    id: null,
                    name: '',
                    category: TemplateCategoryEnum.PERSONAL,
                    parentId: null,
                    ownerId: null,
                    ownerType: CATALOGUE_FILE_OWNER_TYPE.PERSONAL,

                    majorDisease: '',
                    effect: '',
                    detail: {
                        prescriptionChineseForms: [],
                        prescriptionWesternForms: [],
                        prescriptionInfusionForms: [],
                        prescriptionExternalForms: [],
                        prescriptionGlassesForms: [],
                    },

                },
                showAddFolderDialog: false,
            };
        },
        computed: {
            ...mapGetters(['userInfo', 'clinicBasic']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            folder() {
                if (this.selectedCategory === TemplateCategoryEnum.PERSONAL) {
                    return this.personalCatalogues;
                }
                if (this.selectedCategory === TemplateCategoryEnum.DEPARTMENT) {
                    return this.departmentCatalogues;
                }
                return [];
            },
            maxDepth() {
                return this.clinicBasic.template.prescriptionCatalogTier || 3;
            },
            addRootTitle() {
                if (this.selectedCategory === TemplateCategoryEnum.DEPARTMENT) {
                    return '科室';
                }
                if (this.selectedCategory === TemplateCategoryEnum.PUBLIC_CLINIC) {
                    return '全院';
                }
                return '个人';
            },
            addDefaultCategory() {
                return {
                    id: this.postData.parentId,
                    name: this.parentNodeName,
                    ownerId: this.postData.ownerId,
                    ownerType: this.postData.ownerType,
                };
            },
        },
        created() {
            const formCaches = this.getPrescriptionForm([clone(this.prescriptionForm)]);
            const formCache = formCaches[0];
            if (this.type === 'chinese') {
                formCache.prescriptionFormItems = formCache.prescriptionFormItems.filter((item) => {
                    return item.goodsId || item.name;
                });
                /**
                 * @desc 中药处方模板保存不记录药房类型信息，都默认本地药房
                 * <AUTHOR>
                 * @date 2022-03-07 10:39:57
                 */
                formCache.pharmacyType = PharmacyTypeEnum.LOCAL_PHARMACY;
                formCache.pharmacyNo = '';
                formCache.pharmacyName = '';
                formCache.vendorId = null;
                formCache.vendorName = '';
                this.postData.detail.prescriptionChineseForms.push(formCache);
            } else if (this.type === 'western') {
                this.postData.detail.prescriptionWesternForms.push(formCache);
            } else if (this.type === 'infusion') {
                this.postData.detail.prescriptionInfusionForms.push(formCache);
            } else if (this.type === 'external') {
                formCache.prescriptionFormItems = formCache.prescriptionFormItems.filter((item) => {
                    item.acupoints = (item.acupoints || []).filter((acupoint) => {
                        return acupoint.id || acupoint.name;
                    });
                    item.externalGoodsItems = (item.externalGoodsItems || []).filter((it) => {
                        return it.goodsId || it.name;
                    });
                    return item.goodsId;
                });
                this.postData.detail.prescriptionExternalForms.push(formCache);
            }

            this.initFolder();
        },
        methods: {
            async changeType() {
                await this.initFolder();
                this.postData.parentId = this.folder[0]?.id;
                this.parentNodeName = this.folder[0]?.name;
                this.postData.ownerId = this.folder[0]?.ownerId;
                this.postData.ownerType = this.folder[0]?.ownerType;
            },
            async initFolder() {
                if (this.selectedCategory === TemplateCategoryEnum.PERSONAL && this._isInitPersonalCatalogues) return;
                if (this.selectedCategory === TemplateCategoryEnum.DEPARTMENT && this._isInitDepartmentCatalogues) return;

                const { data } = await CategoryTemplateAPI.getCategoryList({
                    type: TemplateSceneTypeEnum.PRESCRIPTION,
                    category: this.selectedCategory,
                });
                // 只要目录
                CategoryTemplateAPI.formatCatalogue(data);
                const { children } = data;
                const folder = children.map((item) => {
                    item.children = item.children.map((it) => {
                        it = Object.assign({}, it, {
                            expand: true,
                            isLeaf: false,
                        });
                        return it;
                    });
                    item = Object.assign({}, item, {
                        expand: true,
                        isLeaf: false,
                    });
                    return item;
                });

                if (this.selectedCategory === TemplateCategoryEnum.PERSONAL) {
                    this.personalCatalogues = folder;
                    this._isInitPersonalCatalogues = true;
                }
                if (this.selectedCategory === TemplateCategoryEnum.DEPARTMENT) {
                    this.departmentCatalogues = folder;
                    this._isInitDepartmentCatalogues = true;
                }
            },

            validateName(val, callback) {
                if (!val.trim()) {
                    this.commonPrescriptionName = val.trim();
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
            },

            selectFolder(node) {
                this.$refs['catalogues-selector'].showPopper = false;
                this.postData.parentId = node.id;
                this.postData.ownerId = node.ownerId;
                this.postData.ownerType = node.ownerType;
                this.parentNodeName = node.name;
            },

            /**
             * 显示新增目录弹窗
             */
            handleClickAddFolderDialog(e) {
                e.stopPropagation();
                this.showAddFolderDialog = true;
            },

            /**
             * 新增目录成功
             */
            addSuccess() {
                if (this.selectedCategory === TemplateCategoryEnum.PERSONAL) {
                    this._isInitPersonalCatalogues = false;
                } else if (this.selectedCategory === TemplateCategoryEnum.DEPARTMENT) {
                    this._isInitDepartmentCatalogues = false;
                }
                this.initFolder();
            },

            /** ----------------------------------------------------------------------
             * 保存处方模板
             * @param prescriptionForm
             */
            saveCommonPrescription(prescriptionForm) {
                if (this.buttonLoading) return false;
                this.$refs[ prescriptionForm ].validate(async (valid) => {
                    if (valid) {
                        this.createSubmit();
                    } else {
                        console.log('error submit!!');
                        this.confirmDialogVisible = false;
                        return false;
                    }
                });
            },

            async createSubmit() {
                try {
                    this.buttonLoading = true;
                    const { data } = await CategoryTemplateAPI.createCatalogue({
                        name: this.postData.name,
                        category: this.selectedCategory,
                        type: TemplateSceneTypeEnum.PRESCRIPTION,
                        parentId: this.postData.parentId,
                        ownerId: this.postData.ownerId,
                        ownerType: this.postData.ownerType,
                        isFolder: 0,
                        file: {
                            majorDisease: this.postData.majorDisease,
                            effect: this.postData.effect,
                            revision: this.postData.revision,
                            detail: this.postData.detail,
                        },
                    });
                    this.$Toast({
                        message: '创建成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.buttonLoading = false;
                    this.$emit('change-success', data, 'add');
                } catch (e) {
                    console.error(e);
                    this.buttonLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';

    .catalogues-folder-popover {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 300px;
        max-height: 264px;

        .custom-node-wrapper.is-disabled {
            color: var(--abc-color-T3);
        }

        .abc-tree-node-content:hover {
            background-color: var(--abc-color-cp-grey4);
        }

        .catalogues-folder-content {
            flex: 1;
            height: 0;
            padding: 4px;
            overflow-y: auto;
        }

        .add-catalogues-footer {
            display: flex;
            align-items: center;
            width: 100%;
            height: 40px;
            padding: 4px;
            border-top: 1px solid var(--abc-color-P6);
        }
    }
</style>
