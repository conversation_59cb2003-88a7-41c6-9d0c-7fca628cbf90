<template>
    <div>
        <!--常用处方-->
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            append-to-body
            title="保存为处方模板"
        >
            <div class="dialog-content clearfix">
                <abc-form
                    ref="prescriptionForm"
                    label-position="left"
                    item-block
                    :label-width="58"
                >
                    <abc-form-item
                        label="模板名"
                        required
                        :validate-event="validateName"
                        hidden-red-dot
                    >
                        <abc-input
                            v-model="postData.name"
                            trim
                            :width="300"
                            :max-length="20"
                        ></abc-input>
                    </abc-form-item>

                    <abc-form-item v-if="showCategory" label="类型">
                        <abc-select
                            v-model="postData.category"
                            :width="300"
                            @change="initFolder"
                        >
                            <abc-option
                                v-for="(it) in categoryList"
                                :key="it.value"
                                :value="it.value"
                                :label="it.name"
                            >
                                <span>{{ it.name }}</span>
                            </abc-option>
                        </abc-select>
                    </abc-form-item>

                    <abc-form-item
                        label="目录"
                        required
                        style="margin: 0;"
                        hidden-red-dot
                    >
                        <abc-popover
                            ref="catalogues-selector"
                            class="catalogues-selector"
                            trigger="click"
                            placement="bottom"
                            width="auto"
                            theme="white"
                            :visible-arrow="false"
                            :popper-style="{ padding: 0 }"
                        >
                            <div slot="reference" style=" display: flex; align-items: center; padding-bottom: 4px;">
                                <abc-input v-model="parentNodeName" :width="300" readonly></abc-input>
                            </div>
                            <div class="catalogues-folder-popover" style="width: 300px;">
                                <div class="catalogues-folder-content">
                                    <abc-tree
                                        :data="folder"
                                        :max-depth="3"
                                        :indent="18"
                                        @node-click="selectFolder"
                                    >
                                        <template #default="{ node }">
                                            <div class="custom-node-wrapper" style="cursor: pointer;">
                                                <img src="~assets/images/<EMAIL>" alt="" />
                                                <span>{{ node.name }}</span>
                                            </div>
                                        </template>
                                    </abc-tree>
                                </div>
                                <div class="add-catalogues-footer">
                                    <abc-button icon="plus_thin" variant="text" @click.stop="showAddFolderDialog">
                                        新增目录
                                    </abc-button>
                                </div>
                            </div>
                        </abc-popover>
                    </abc-form-item>
                </abc-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <abc-button
                    :loading="buttonLoading"
                    @click="saveCommonPrescription('prescriptionForm')"
                >
                    确定
                </abc-button>
                <abc-button variant="ghost" :plain="true" @click="showDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <add-template-dialog
            v-if="showAddFolder"
            v-model="showAddFolder"
            :max-depth="maxDepth"
            template-type="prescription"
            :add-root-title="rootTitle"
            :is-folder="1"
            :category="postData.category"
            :catalogues="folder"
            @add-success="initFolder"
        ></add-template-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import OutpatientAPI from 'api/outpatient';
    import clone from 'utils/clone';
    import AddTemplateDialog from '../../templates-manager/add-dialog.vue';
    /**
     * @desc mixins
     * <AUTHOR>
     * @date 2020/05/11 09:44:27
     */
    import prescriptionHandle from 'src/views/outpatient/mixins/prescription-handle';
    import { PharmacyTypeEnum } from '@abc/constants';
    import { mapGetters } from 'vuex';

    export default {
        name: 'CommonPrescriptionDialog',
        components: {
            AddTemplateDialog,
        },
        mixins: [
            prescriptionHandle,
        ],
        props: {
            type: {
                required: true,
            },

            prescriptionForm: {
                type: Object,
                required: true,
            },

            value: Boolean,

        },
        data() {
            return {
                commonPrescriptionName: '',
                folder: [],
                parentNodeName: '',
                buttonLoading: false,

                postData: {
                    name: '',
                    category: 2, // 1-公用模板 2-个人模板
                    parentId: null,

                    majorDisease: '',
                    effect: '',
                    detail: {
                        prescriptionChineseForms: [],
                        prescriptionWesternForms: [],
                        prescriptionInfusionForms: [],
                        prescriptionExternalForms: [],
                        prescriptionGlassesForms: [],
                    },

                },
                categoryList: [
                    {
                        name: '诊所公用', value: 1,
                    },
                    {
                        name: '个人专用', value: 2,
                    },
                ],
                showAddFolder: false,
            };
        },
        computed: {
            ...mapGetters(['userInfo', 'modulePermission', 'clinicBasic']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            showCategory() {
                return this.userInfo && (this.userInfo.isAdmin || this.modulePermission.hasSettingModule);
            },
            maxDepth() {
                return this.clinicBasic.template.prescriptionCatalogTier || 3;
            },
            rootTitle() {
                return this.postData.category === 1 ? '诊所公用' : '个人专用';
            },
        },
        created() {
            const formCaches = this.getPrescriptionForm([clone(this.prescriptionForm)]);
            const formCache = formCaches[0];
            if (this.type === 'chinese') {
                formCache.prescriptionFormItems = formCache.prescriptionFormItems.filter((item) => {
                    return item.goodsId || item.name;
                });
                /**
                 * @desc 中药处方模板保存不记录药房类型信息，都默认本地药房
                 * <AUTHOR>
                 * @date 2022-03-07 10:39:57
                 */
                formCache.pharmacyType = PharmacyTypeEnum.LOCAL_PHARMACY;
                formCache.pharmacyNo = '';
                formCache.pharmacyName = '';
                formCache.vendorId = null;
                formCache.vendorName = '';
                this.postData.detail.prescriptionChineseForms.push(formCache);
            } else if (this.type === 'western') {
                this.postData.detail.prescriptionWesternForms.push(formCache);
            } else if (this.type === 'infusion') {
                this.postData.detail.prescriptionInfusionForms.push(formCache);
            } else if (this.type === 'external') {
                formCache.prescriptionFormItems = formCache.prescriptionFormItems.filter((item) => {
                    item.acupoints = (item.acupoints || []).filter((acupoint) => {
                        return acupoint.id || acupoint.name;
                    });
                    item.externalGoodsItems = (item.externalGoodsItems || []).filter((it) => {
                        return it.goodsId || it.name;
                    });
                    return item.goodsId;
                });
                this.postData.detail.prescriptionExternalForms.push(formCache);
            }

            this.initFolder();
        },
        methods: {
            async initFolder() {
                const { data } = await OutpatientAPI.fetchTemplatesFolderTree('prescription', this.postData.category);
                const { children } = data;
                this.folder = children.map((item) => {
                    item.children = item.children.map((it) => {
                        it = Object.assign({}, it, {
                            expand: true,
                            isLeaf: false,
                        });
                        return it;
                    });
                    item = Object.assign({}, item, {
                        expand: true,
                        isLeaf: false,
                    });
                    return item;
                });
                this.postData.parentId = null;
                this.parentNodeName = '';
            },

            validateName(val, callback) {
                if (!val.trim()) {
                    this.commonPrescriptionName = val.trim();
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
            },

            selectFolder(node) {
                this.$refs['catalogues-selector'].showPopper = false;
                this.postData.parentId = node.id;
                this.parentNodeName = node.name;
            },

            showAddFolderDialog(e) {
                e.preventDefault();
                this.showAddFolder = true;
            },

            /** ----------------------------------------------------------------------
             * 保存处方模板
             * @param prescriptionForm
             */
            saveCommonPrescription(prescriptionForm) {
                if (this.buttonLoading) return false;
                this.$refs[ prescriptionForm ].validate(async (valid) => {
                    if (valid) {
                        this.createSubmit();
                    } else {
                        console.log('error submit!!');
                        this.confirmDialogVisible = false;
                        return false;
                    }
                });
            },

            async createSubmit() {
                try {
                    this.buttonLoading = true;
                    const { data } = await OutpatientAPI.createTemplate('prescription', this.postData);
                    this.$Toast({
                        message: '创建成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.buttonLoading = false;
                    this.$emit('change-success', data, 'add');
                } catch (e) {
                    console.error(e);
                    this.buttonLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';

    .catalogues-folder-popover {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 300px;
        max-height: 264px;

        .custom-node-wrapper.is-disabled {
            color: var(--abc-color-T3);
        }

        .abc-tree-node-content:hover {
            background-color: var(--abc-color-cp-grey4);
        }

        .catalogues-folder-content {
            flex: 1;
            height: 0;
            padding: 4px;
            overflow-y: auto;
        }

        .add-catalogues-footer {
            display: flex;
            align-items: center;
            width: 100%;
            height: 40px;
            padding: 4px;
            border-top: 1px solid var(--abc-color-P6);
        }
    }
</style>
