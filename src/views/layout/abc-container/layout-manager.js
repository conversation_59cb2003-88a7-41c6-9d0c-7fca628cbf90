import Vue from 'vue';
import {
    ResponsiveConfig, VerticalResponsiveConfig,
} from 'utils/responsive.js';

import Storage from 'utils/localStorage-handler';

export default class LayoutManager {
    static constants = {
        isFullScreen: false,
        isFixedLeftNav: false,
        // 左栏变宽的阈值，当 >= 该值时，左栏会变宽
        navMenuToWideThreshold: 1850,

        // 左栏响应式百分比
        leftContainerWidthPercent: ResponsiveConfig.leftContainerWidthPercent,
        // 左栏最大宽度
        leftContainerMaxWidth: ResponsiveConfig.leftContainerMaxWidth,
        // 左栏最小宽度
        leftContainerMinWidth: ResponsiveConfig.leftContainerMinWidth,

        // 右栏响应式百分比
        rightContainerWidthPercent: ResponsiveConfig.rightContainerWidthPercent,
        // 右栏最大宽度
        rightContainerMaxWidth: ResponsiveConfig.rightContainerMaxWidth,
        // 右栏最小宽度
        rightContainerMinWidth: ResponsiveConfig.rightContainerMinWidth,

        responsiveRightLeftWidth: VerticalResponsiveConfig.responsiveRightLeftWidth,

        // 左侧抽屉阈值，小于该值启用左侧抽屉
        leftDrawerThreshold: ResponsiveConfig.leftDrawerThreshold,

        drawerUsedStorageKey: 'drawer_used',
    };

    static state = Vue.observable({
        containerTop: 58,
        isVerticalNavMenu: false,
        navMenuHeight: undefined,

        navMenuWidth: undefined,

        isWideNav: false,

        containerWidth: undefined,
        headerContainerHeight: undefined,

        centerContainerWidth: undefined,

        containerTopHeadHeight: undefined,
        containerTopHeadBottomHeight: undefined,

        leftContainerWidth: 290,

        rightContainerWidth: 310,
        rightContainerRight: 0,

        containerOffsetLeft: 0, // abc-container 左偏移量

        offsetWidth: undefined,

        // 是否启用左侧抽屉
        isLeftDrawerEnable: false,
        // 左侧抽屉状态：-1 初始状态，0 关闭状态，1 打开状态
        leftDrawerStatus: -1,
    });

    static init(data) {
        const config = data.isFixedLeftNav ? VerticalResponsiveConfig : ResponsiveConfig;

        if (data.isFullScreen) {
            data.leftContainerMaxWidth = config.fullScreenLeftContainerMaxWidth;
            data.leftContainerMinWidth = config.leftContainerMinWidth;
            data.rightContainerMaxWidth = config.fullScreenRightContainerMaxWidth;
            data.rightContainerMinWidth = config.rightContainerMinWidth;
        } else {
            data.leftContainerMaxWidth = config.leftContainerMaxWidth;
            data.leftContainerMinWidth = config.leftContainerMinWidth;
            data.rightContainerMaxWidth = config.rightContainerMaxWidth;
            data.rightContainerMinWidth = config.rightContainerMinWidth;
        }
        Object.assign(this.constants, data);
        this.initNavMenu(data);
    }

    static initNavMenu() {
        let navMenuHeight = 58;
        let navMenuWidth = 0;
        let isWideNav = false;
        let isVerticalNavMenu = false;

        if (this.constants.isFullScreen) {
            navMenuHeight = 48;
        }

        isWideNav = this.state.offsetWidth > this.constants.navMenuToWideThreshold;
        if (this.constants.isFixedLeftNav) {
            navMenuHeight = 0;
            isVerticalNavMenu = true;
            if (isWideNav) {
                navMenuWidth = 148;
            } else {
                navMenuWidth = 60;
            }

        }
        Object.assign(this.state, {
            navMenuHeight,
            navMenuWidth,
            isVerticalNavMenu,
            isWideNav,
        });
    }

    static setState(state) {
        Object.assign(this.state, state);

    }

    /**
     * @desc 计算 Container 布局
     * <AUTHOR>
     * @date 2023-03-16 09:46:38
     * @param {object} params
     * @param {number} params.offsetWidth - 整体宽度
     * @param {number} params.containerTop - 容器top
     * @param {number} params.containerWidth - 容器宽度
     * @param {number} params.centerContainerWidth - 中间容器宽度
     * @param {object} params?.leftContainerWidthPercent // 配置来源于abc-container
     * @param {object} params?.rightContainerWidthPercent // 配置来源于abc-container
     * @param {object} params?.leftContainerMaxWidth // 配置来源于abc-container
     * @param {object} params?.leftContainerMinWidth // 配置来源于abc-container
     * @param {object} params?.rightContainerMaxWidth // 配置来源于abc-container
     * @param {object} params?.rightContainerMinWidth // 配置来源于abc-container
     */
    static calcContainer(params) {
        const {
            offsetWidth,
            containerTop,
            containerWidth,
            centerContainerWidth,
        } = params;
        this.setState({
            containerWidth,
            offsetWidth,
        });
        this.constants.isFixedLeftNav ? this.calcVerticalContainerWidth(params) : this.calcContainerWidth(params);
        this.calcContainerTop(containerTop);
        this.calcRightContainer({
            offsetWidth,
            containerWidth,
        });
        this.calcCenterContainer({
            centerContainerWidth,
        });
        this.calcContainerOffsetLeft({ offsetWidth });
        this.initNavMenu();
    }

    /**
     * @desc 计算 左右container宽度，中间是根据该宽度自适应
     * <AUTHOR>
     * @date 2023-03-16 17:59:01
     * @param {object} params - 屏幕宽度 document.body.offsetWidth
     * @param {number} params.offsetWidth - 屏幕宽度 document.body.offsetWidth
     */
    static calcContainerWidth(params) {
        const {
            offsetWidth,
            leftContainerWidthPercent = this.constants.leftContainerWidthPercent,
            rightContainerWidthPercent = this.constants.rightContainerWidthPercent,
            leftContainerMaxWidth = this.constants.leftContainerMaxWidth,
            leftContainerMinWidth = this.constants.leftContainerMinWidth,
            rightContainerMaxWidth = this.constants.rightContainerMaxWidth,
            rightContainerMinWidth = this.constants.rightContainerMinWidth,
        } = params;

        // 计算左侧容器宽度
        let leftContainerWidth = Math.ceil(offsetWidth * leftContainerWidthPercent);
        if (leftContainerWidth > leftContainerMaxWidth) {
            leftContainerWidth = leftContainerMaxWidth;
        } else if (leftContainerWidth < leftContainerMinWidth) {
            leftContainerWidth = leftContainerMinWidth;
        }

        // 计算右侧容器宽度
        let rightContainerWidth = Math.ceil(offsetWidth * rightContainerWidthPercent);
        if (rightContainerWidth > rightContainerMaxWidth) {
            rightContainerWidth = rightContainerMaxWidth;
        } else if (rightContainerWidth < rightContainerMinWidth) {
            rightContainerWidth = rightContainerMinWidth;
        }
        this.setState({
            leftContainerWidth,
            rightContainerWidth,
        });
    }


    static calcVerticalContainerWidth(params) {
        const {
            offsetWidth,
            rightContainerMinWidth = this.constants.rightContainerMinWidth,
            rightContainerMaxWidth = this.constants.rightContainerMaxWidth,
            leftContainerMaxWidth = this.constants.leftContainerMaxWidth,
            leftContainerMinWidth = this.constants.leftContainerMinWidth,
            leftContainerWidthFixed = false,
        } = params;
        const { responsiveRightLeftWidth } = this.constants;

        let leftWidth = 0;
        let rightWidth = 0;

        // 计算左侧容器宽度
        responsiveRightLeftWidth.forEach((item) => {
            if (offsetWidth <= item.maxScreenWidth && offsetWidth >= item.minScreenWidth) {
                leftWidth = item.width;
                rightWidth = item.width;
            }
        });

        if (leftContainerWidthFixed) {
            leftWidth = Math.min(Math.max(leftWidth, leftContainerMinWidth), leftContainerMaxWidth);
        }

        if (rightWidth < rightContainerMinWidth) {
            rightWidth = rightContainerMinWidth;
        }
        if (rightWidth > rightContainerMaxWidth) {
            rightWidth = rightContainerMaxWidth;
        }

        this.setState({
            leftContainerWidth: leftWidth,
            rightContainerWidth: rightWidth,
        });
    }

    /**
     * @desc 计算整个 container top 值
     * <AUTHOR>
     * @date 2023-03-16 17:59:01
     * @param {number} containerTop - #abc-container 的 offsetTop
     */
    static calcContainerTop(containerTop) {
        let top = this.state.navMenuHeight;
        top = Math.max(containerTop, top);

        this.setState({
            containerTop: top,
        });
    }

    /**
     * @desc 计算 right-container 相关布局信息
     * <AUTHOR>
     * @date 2023-03-16 17:59:01
     * @param {object} params -
     * @param {number} params.offsetWidth - 屏幕宽度document.body.offsetWidth
     * @param {number} params.containerWidth - #abc-container 的 width
     */
    static calcRightContainer(params) {
        const {
            offsetWidth,
            containerWidth,
        } = params;

        const rightContainerRight = (offsetWidth - containerWidth - (this.state.navMenuWidth || 0)) / 2;
        this.setState({
            rightContainerRight,
        });
    }

    /**
     * @desc 计算 center-container 相关布局信息
     * <AUTHOR>
     * @date 2023-03-16 17:59:01
     * @param {object} params -
     * @param {number} params.centerContainerWidth - #abc-container-center offsetWidth
     */
    static calcCenterContainer(params) {
        const { centerContainerWidth } = params;
        this.setState({
            centerContainerWidth,
        });
    }


    static calcContainerOffsetLeft(params) {
        const { offsetWidth } = params;
        const maxTotalWidth = 1920 + (this.state.navMenuWidth || 0);
        const left = offsetWidth > maxTotalWidth ? (offsetWidth - maxTotalWidth) / 2 : 0;
        this.setState({
            containerOffsetLeft: left,
        });
    }

    /**
     * @desc 获取左侧抽屉状态
     */
    static isDrawerUsed() {
        return Storage.getBoolean(this.constants.drawerUsedStorageKey);
    }

    static setDrawerUsed() {
        Storage.setBoolean(this.constants.drawerUsedStorageKey, 1);
    }


    /**
     * @desc 初始化左侧抽屉状态，根据屏幕宽度判断是否启用左侧抽屉
     * @param reset - 是否是初始化，如果是初始化，且抽屉未使用过，则默认打开抽屉
     */
    static initDrawer(reset = false) {
        if (this.state.offsetWidth < this.constants.leftDrawerThreshold) {
            let status = this.state.leftDrawerStatus;
            if (!this.isDrawerUsed()) {
                status = 1;
            } else {
                status = reset ? -1 : status;
            }
            this.setState({
                isLeftDrawerEnable: true,
                leftDrawerStatus: status,
            });
        } else {
            this.setState({
                isLeftDrawerEnable: false,
                leftDrawerStatus: reset ? -1 : this.state.leftDrawerStatus,
            });
        }
    }

    /**
     * @desc 销毁左侧抽屉
     */
    static destroyDrawer() {
        this.setState({
            isLeftDrawerEnable: false,
            leftDrawerStatus: -1,
        });
    }

    /**
     * @desc 切换左侧抽屉状态
     */
    static toggleDrawer() {
        this.setDrawerUsed();
        this.setState({
            leftDrawerStatus: this.state.leftDrawerStatus === 1 ? 0 : 1,
        });
    }

    /**
     * @desc 关闭左侧抽屉
     */
    static closeDrawer() {
        this.setDrawerUsed();
        if (this.state.leftDrawerStatus !== 1) {
            return;
        }
        this.setState({
            leftDrawerStatus: 0,
        });
    }

    /**
     * @desc 打开左侧抽屉
     */
    static openDrawer() {
        if (this.state.leftDrawerStatus === 1) {
            return;
        }
        this.setState({
            leftDrawerStatus: 1,
        });
    }
}


