<template>
    <div
        id="abc-container"
        class="app-container"
        :class="{
            'has-header-container': hasHeaderContainer,
            'has-left-container': hasLeftContainer,
            'has-right-container': hasRightContainer,
            'resident-right-container': residentRightContainer,
            'content-align-center': contentAlign<PERSON>enter,
            'has-left-min-width': leftContainer<PERSON>inWidth,
            'abc-container-vertical': isVertical,
            'is-left-drawer-enable': isLeftDrawerEnable,
            'is-left-drawer-expand': isLeftDrawerOpen,
            'start-drawer-animation': !isLeftDrawerInit,
            'is-support-center-scroll': isSupportCenterScroll,
        }"
    >
        <drawer-mask v-if="isLeftDrawerOpen"></drawer-mask>
        <div
            v-if="isLeftDrawerEnable"
            class="abc-container-drawer__toggle-wrapper"
            @click="handleDrawerToggle"
        >
            <abc-button class="abc-container-drawer__toggle-btn" variant="ghost" theme="primary">
                {{ drawerToggleText }}
            </abc-button>
        </div>
        <slot></slot>
    </div>
</template>
<script type="text/ecmascript-6">
    import ResizeObserver from 'resize-observer-polyfill';
    import LayoutManager from 'views/layout/abc-container/layout-manager.js';
    import DrawerMask from 'views/layout/abc-container/drawer-mask.vue';

    export default {
        name: 'AbcContainer',
        components: { DrawerMask },
        provide() {
            return {
                AbcContainer: this,
            };
        },
        props: {
            hasHeaderContainer: {
                type: Boolean,
                default: false,
            },
            hasLeftContainer: {
                type: Boolean,
                default: false,
            },
            hasRightContainer: {
                type: Boolean,
                default: false,
            },
            // 是否常驻右侧
            residentRightContainer: {
                type: Boolean,
                default: false,
            },

            // 左栏响应式百分比
            leftContainerWidthPercent: {
                type: Number,
                default: 0,
            },
            // 左栏最大宽度
            leftContainerMaxWidth: {
                type: Number,
                default: 0,
            },
            // 左栏最小宽度
            leftContainerMinWidth: {
                type: Number,
                default: 0,
            },

            // 左栏宽度是否固定
            leftContainerWidthFixed: {
                type: Boolean,
                default: false,
            },

            // 右栏响应式百分比
            rightContainerWidthPercent: {
                type: Number,
                default: 0,
            },
            // 右栏最大宽度
            rightContainerMaxWidth: {
                type: Number,
                default: 0,
            },
            // 右栏最小宽度
            rightContainerMinWidth: {
                type: Number,
                default: 0,
            },
            // abc-center-main-content区域是否居中
            contentAlignCenter: {
                type: Boolean,
                default: false,
            },
            // 是否支持抽屉
            isSupportDrawer: {
                type: Boolean,
                default: false,
            },
            // 抽屉toggle按钮文案，默认为'患者列表'
            drawerToggleText: {
                type: String,
                default: '患者列表',
            },
            // abc-container-center是否支持滚动
            isSupportCenterScroll: {
                type: Boolean,
                default: true,
            },
        },
        computed: {
            isVertical() {
                return LayoutManager.state.isVerticalNavMenu;
            },
            isLeftDrawerEnable() {
                return LayoutManager.state.isLeftDrawerEnable;
            },
            isLeftDrawerInit() {
                return LayoutManager.state.leftDrawerStatus === -1;
            },
            isLeftDrawerOpen() {
                return LayoutManager.state.leftDrawerStatus === 1;
            },
        },
        watch: {
            hasRightContainer() {
                this.calcContainerStyle();
            },
            hasLeftContainer() {
                this.calcContainerStyle();
            },
        },
        mounted() {
            this.calcContainerStyle();
            this.resizeObserver = new ResizeObserver(this.calcContainerStyle);

            const $app = document.getElementById('app');
            if ($app) {
                this.resizeObserver.observe($app);
            }

            const containerCenter = document.getElementById('abc-container-center');
            if (containerCenter) {
                this.resizeObserver.observe(containerCenter);
            }

            const containerRight = document.getElementById('abc-container-right');
            if (containerRight) {
                this.resizeObserver.observe(containerRight);
            }
            // if (this.isSupportDrawer) {
            //     LayoutManager.initDrawer(true);
            // }
        },

        beforeDestroy() {
            LayoutManager.destroyDrawer();
            this.resizeObserver?.disconnect();
        },

        methods: {
            calcContainerStyle() {
                this.$nextTick(() => {
                    const $abcContainer = document.querySelector('#abc-container');
                    const $abcContainerCenter = document.querySelector('#abc-container-center');

                    const params = {
                        offsetWidth: document.body?.offsetWidth ?? 0,
                        containerTop: $abcContainer?.getBoundingClientRect().top,
                        containerWidth: $abcContainer?.getBoundingClientRect().width,
                        containerOffsetLeft: $abcContainer?.getBoundingClientRect().left,
                        centerContainerWidth: $abcContainerCenter?.getBoundingClientRect().width,
                    };
                    if (this.leftContainerWidthPercent) {
                        params.leftContainerWidthPercent = this.leftContainerWidthPercent;
                    }
                    if (this.leftContainerMaxWidth) {
                        params.leftContainerMaxWidth = this.leftContainerMaxWidth;
                    }
                    if (this.leftContainerMinWidth) {
                        params.leftContainerMinWidth = this.leftContainerMinWidth;
                    }
                    if (this.rightContainerWidthPercent) {
                        params.rightContainerWidthPercent = this.rightContainerWidthPercent;
                    }
                    if (this.rightContainerMaxWidth) {
                        params.rightContainerMaxWidth = this.rightContainerMaxWidth;
                    }
                    if (this.rightContainerMinWidth) {
                        params.rightContainerMinWidth = this.rightContainerMinWidth;
                    }
                    if (this.leftContainerWidthFixed) {
                        params.leftContainerWidthFixed = this.leftContainerWidthFixed;
                    }
                    LayoutManager.calcContainer(params);
                    // if (this.isSupportDrawer) {
                    //     LayoutManager.initDrawer();
                    // }
                });
            },
            handleDrawerToggle() {
                LayoutManager.toggleDrawer();
            },
        },
    };
</script>
<style lang="scss">
    @import "src/styles/theme.scss";
    @import "src/styles/mixin.scss";

    $_1096LeftWidth: 250px;

    $_1097LeftWidth: 210px;
    $_1097RightWidth: 230px;

    $_1280LeftWidth: 230px;
    $_1280RightWidth: 260px;

    $_1366LeftWidth: 270px;
    $_1366RightWidth: 280px;

    $_1440RightWidth: 320px;

    $_1660RightWidth: 320px;

    $_1920RightWidth: 320px;

    #abc-container {
        position: relative;
        display: flex;
        width: auto;
        max-width: 1440px;
        height: 100%;
        padding: 0;
        margin: 0 auto !important;
        background-color: $abcBgMain;
        box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.16);

        @include clearfix;

        &.abc-container-vertical {
            $_1096LeftWidth: 226px !global;

            $_1097LeftWidth: 226px !global;
            $_1097RightWidth: 226px !global;

            $_1280LeftWidth: 226px !global;
            $_1280RightWidth: 226px !global;

            $_1366LeftWidth: 256px !global;
            $_1366RightWidth: 256px !global;

            $_1440RightWidth: 280px !global;

            $_1660RightWidth: 319px !global;

            $_1920RightWidth: 340px !global;
        }

        #abc-container-header {
            position: fixed;
            top: var(--containerTop);
            z-index: 9;
            width: var(--containerWidth);
            height: 56px;
            background-color: #ffffff;
            border-bottom: 1px solid $abcLayoutDividerColor;
            border-radius: $abcContainerBorderRadius $abcContainerBorderRadius 0 0;
        }

        #abc-container-left {
            position: fixed;
            top: calc(var(--containerTop) + var(--headerContainerHeight));
            z-index: 9;
            width: var(--leftContainerWidth);
            height: calc(100% - var(--containerTop) - var(--headerContainerHeight) - 20px);
            overflow: auto;
            background-color: #ffffff;
            border: none;
            border-right: 1px solid $abcLayoutDividerColor;
            border-radius: $abcContainerBorderRadius 0 0 $abcContainerBorderRadius;
        }

        #abc-container-right {
            position: fixed;
            top: calc(var(--containerTop) + var(--headerContainerHeight));
            right: var(--rightContainerRight);
            z-index: 9;
            width: var(--rightContainerWidth);
            height: calc(100% - var(--containerTop) - var(--headerContainerHeight) - 20px);
            overflow: hidden;
            background-color: #ffffff;
            border: none;
            border-left: 1px solid $abcLayoutDividerColor;
            border-radius: 0 $abcContainerBorderRadius $abcContainerBorderRadius 0;
        }

        #abc-container-center {
            flex-grow: 1;
            width: 0;
            height: 100%;
            min-height: 100%;
            padding-top: calc(var(--containerTopHeadHeight) + var(--headerContainerHeight) + var(--containerTopHeadBottomHeight));
            overflow: hidden;
            border: none;
            border-radius: $abcContainerBorderRadius;

            #abc-container-center__top-head {
                position: fixed;
                top: calc(var(--containerTop) + var(--headerContainerHeight));
                z-index: 10;
                display: flex;
                flex-direction: column;
                width: var(--centerContainerWidth);
                padding-right: 10px; //内容区域默认scrollbar-gutter: stable;有10px滚动条轨道
                background-color: #ffffff;
                border-top: none;
                border-bottom: 1px solid $abcLayoutDividerColor;
                border-radius: $abcContainerBorderRadius $abcContainerBorderRadius 0 0;

                .header-content {
                    position: relative;
                    display: flex;
                    align-items: center;
                    width: 100%;
                    height: 55px;
                    padding: 0 14px 0 24px;
                }

                .cut-line {
                    width: calc(100% + 10px) !important;
                    max-width: calc(100% + 10px) !important;
                    height: 1px;
                    background-color: $P6;
                }

                .bottom-extend {
                    width: 100%;
                }
            }
        }

        &.content-align-center {
            #abc-container-center #abc-container-center__top-head > div,
            .main-content {
                max-width: 1150px;
                margin: 0 auto;
            }
        }

        &.has-header-container {
            #abc-container-left {
                border-radius: 0 0 0 $abcContainerBorderRadius;
            }

            #abc-container-right {
                border-radius: 0 0 $abcContainerBorderRadius 0;
            }

            #abc-container-center {
                #abc-container-center__top-head {
                    border-top-right-radius: 0;
                }
            }
        }

        &.has-left-container {
            #abc-container-center {
                margin-left: var(--leftContainerWidth);
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;

                #abc-container-center__top-head {
                    border-top-left-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }
        }

        &.has-right-container {
            &.content-align-center {
                #abc-container-center #abc-container-center__top-head > div,
                .main-content {
                    max-width: 1020px;
                }
            }

            #abc-container-center {
                margin-right: var(--rightContainerWidth);
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;

                #abc-container-center__top-head {
                    border-top-right-radius: 0;
                    border-bottom-right-radius: 0;
                }
            }
        }

        &.is-support-center-scroll {
            #abc-container-center {
                overflow-y: auto;
                scrollbar-gutter: stable;

                @include scrollBar;
            }
        }
    }

    .is-expired {
        #abc-container {
            #abc-container-left,
            #abc-container-right,
            #abc-container-center #abc-container-center__top-head {
                top: 92px !important;
            }
        }

        &.is-vertical-nav {
            #abc-container {
                #abc-container-left,
                #abc-container-right,
                #abc-container-center #abc-container-center__top-head {
                    top: 44px !important;
                }
            }
        }
    }

    .abc-app-is-full-screen {
        #abc-container {
            max-width: 1920px;

            #abc-container-left {
                height: calc(100% - var(--containerTop) - var(--headerContainerHeight));
            }

            #abc-container-right {
                height: calc(100% - var(--containerTop) - var(--headerContainerHeight));
            }
        }

        .is-vertical-nav #abc-module-container {
            max-width: 1920px;
            margin: auto;
        }
    }

    @media screen and (max-width: 1096px) {
        #abc-container {
            &:not(.has-left-min-width) #abc-container-left {
                width: $_1096LeftWidth;
            }

            &:not(.has-left-min-width).has-left-container {
                #abc-container-center {
                    margin-left: $_1096LeftWidth;
                }
            }

            #abc-container-center {
                border-top-right-radius: $abcContainerBorderRadius;
                border-bottom-right-radius: $abcContainerBorderRadius;
            }

            &.has-right-container:not(.resident-right-container) {
                #abc-container-center {
                    margin-right: 0 !important;
                }
            }

            &:not(.resident-right-container) {
                #abc-container-right {
                    display: none !important;
                }
            }
        }
    }

    @media screen and (min-width: 1097px) and (max-width: 1279px) {
        #abc-container {
            &:not(.has-left-min-width):not(.is-left-drawer-enable) #abc-container-left {
                width: $_1097LeftWidth;

                .ql-item-describe > .abstract,
                .patient-name {
                    padding-left: 0;
                }

                .ql-item-content {
                    .doctor-name {
                        width: 42px;
                        min-width: 42px;
                    }
                }

                .status {
                    width: 30px;
                    min-width: 30px;
                }
            }

            &:not(.has-left-min-width).has-left-container {
                #abc-container-center {
                    margin-left: $_1097LeftWidth;
                }
            }

            #abc-container-center {
                .main-content {
                    padding: 16px 14px;
                }
            }

            &.has-right-container:not(.resident-right-container) {
                #abc-container-center {
                    margin-right: $_1097RightWidth;
                }
            }

            &:not(.resident-right-container) {
                #abc-container-right {
                    width: $_1097RightWidth;
                }
            }
        }
    }

    @media screen and (min-width: 1280px) and (max-width: 1365px) {
        #abc-container {
            &:not(.has-left-min-width):not(.is-left-drawer-enable) #abc-container-left {
                width: $_1280LeftWidth;

                .ql-item-describe > .abstract,
                .patient-name {
                    padding-left: 0;
                }

                .ql-item-content .doctor-name {
                    width: 42px;
                    min-width: 42px;
                }
            }

            &:not(.has-left-min-width).has-left-container {
                #abc-container-center {
                    margin-left: $_1280LeftWidth;
                }
            }

            &.has-right-container:not(.resident-right-container) {
                #abc-container-center {
                    margin-right: $_1280RightWidth;
                }
            }

            &:not(.resident-right-container) {
                #abc-container-right {
                    width: $_1280RightWidth;
                }
            }
        }
    }

    @media screen and (max-width: 1440px) {
        #abc-container {
            #abc-container-header {
                top: calc(var(--containerTop) - 10px);
            }

            #abc-container-left,
            #abc-container-right {
                top: calc(var(--containerTop) - 10px + var(--headerContainerHeight));
                height: calc(100% - var(--containerTop) + 10px - var(--headerContainerHeight));
            }

            #abc-container-center {
                #abc-container-center__top-head {
                    top: calc(var(--containerTop) - 10px + var(--headerContainerHeight));
                }
            }
        }

        .abc-app-is-full-screen {
            #abc-container {
                #abc-container-header {
                    top: var(--containerTop);
                }

                #abc-container-left,
                #abc-container-right {
                    top: calc(var(--containerTop) + var(--headerContainerHeight));
                    height: calc(100% - var(--containerTop) - var(--headerContainerHeight));
                }

                #abc-container-center {
                    #abc-container-center__top-head {
                        top: calc(var(--containerTop) + var(--headerContainerHeight));
                    }
                }
            }
        }
    }

    @media screen and (min-width: 1366px) and (max-width: 1439px) {
        #abc-container {
            &:not(.has-left-min-width) #abc-container-left {
                width: $_1366LeftWidth;
            }

            &:not(.has-left-min-width).has-left-container {
                #abc-container-center {
                    margin-left: $_1366LeftWidth;
                }
            }

            &.has-right-container:not(.resident-right-container) {
                #abc-container-center {
                    margin-right: $_1366RightWidth;
                }
            }

            &:not(.resident-right-container) {
                #abc-container-right {
                    width: $_1366RightWidth;
                }
            }
        }
    }

    @media screen and (min-width: 1440px) and (max-width: 1659px) {
        #abc-container.abc-container-vertical {
            &:not(.has-left-min-width) #abc-container-left {
                width: $_1440RightWidth;
            }

            &:not(.has-left-min-width).has-left-container {
                #abc-container-center {
                    margin-left: $_1440RightWidth;
                }
            }

            &.has-right-container:not(.resident-right-container) {
                #abc-container-center {
                    margin-right: $_1440RightWidth;
                }
            }

            &:not(.resident-right-container) {
                #abc-container-right {
                    width: $_1440RightWidth;
                }
            }
        }
    }

    @media screen and (min-width: 1660px) and (max-width: 1919px) {
        #abc-container.abc-container-vertical {
            &:not(.has-left-min-width) #abc-container-left {
                width: $_1660RightWidth;
            }

            &:not(.has-left-min-width).has-left-container {
                #abc-container-center {
                    margin-left: $_1660RightWidth;
                }
            }

            &.has-right-container:not(.resident-right-container) {
                #abc-container-center {
                    margin-right: $_1660RightWidth;
                }
            }

            &:not(.resident-right-container) {
                #abc-container-right {
                    width: $_1660RightWidth;
                }
            }
        }
    }

    @media screen and (min-width: 1920px) {
        #abc-container.abc-container-vertical {
            &:not(.has-left-min-width) #abc-container-left {
                width: $_1920RightWidth;
            }

            &:not(.has-left-min-width).has-left-container {
                #abc-container-center {
                    margin-left: $_1920RightWidth;
                }
            }

            &.has-right-container:not(.resident-right-container) {
                #abc-container-center {
                    margin-right: $_1920RightWidth;
                }
            }

            &:not(.resident-right-container) {
                #abc-container-right {
                    width: $_1920RightWidth;
                }
            }
        }
    }

    // 抽屉相关样式
    // 动画：左侧抽屉，toggle 按钮
    .abc-container-drawer__toggle-wrapper {
        position: fixed;
        top: 50%;
        left: 0;
        z-index: 11;
        transition: opacity 0.1s ease-in-out;
        transition-delay: 0.3s;
        transform: translateY(-50%);

        .abc-container-drawer__toggle-btn {
            width: 38px;
            min-width: 38px;
            height: auto;
            padding: var(--abc-paddingTB-xl) var(--abc-paddingLR-l);
            line-height: var(--abc-line-height-small);
            border-left: none;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            box-shadow: var(--abc-shadow-2);
        }
    }

    #abc-container.is-left-drawer-enable {
        &.start-drawer-animation {
            #abc-container-left {
                transition: transform 0.3s ease-in-out;
            }
        }

        #abc-container-left {
            // 提高层级，要比右边内容高
            z-index: 11;
            width: 320px;
            border-radius: 0 $abcContainerBorderRadius $abcContainerBorderRadius 0;
            transform: translateX(-100%) translateY(-50%);
        }

        // 抽屉隐藏时，显示 toggle 按钮
        &:not(.is-left-drawer-expand) {
            #abc-container-left {
                transform: translateX(-100%);
            }

            .abc-container-drawer__toggle-wrapper {
                opacity: 1;
            }
        }

        // 抽屉展开时，隐藏 toggle 按钮
        &.is-left-drawer-expand {
            #abc-container-left {
                // 增加右侧阴影
                box-shadow: 0 3px 24px 2px rgba(0, 0, 0, 0.08);
                transform: translateX(0);
            }

            .abc-container-drawer__toggle-wrapper {
                opacity: 0;
            }
        }

        // 启用抽屉时，中间内容区域占满，抽屉不占位，显示在上层
        &.has-left-container {
            #abc-container-center {
                margin-left: 0;
            }
        }
    }
</style>







