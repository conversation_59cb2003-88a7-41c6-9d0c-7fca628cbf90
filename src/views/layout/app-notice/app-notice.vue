<template>
    <div class="app-notice-wrapper app-header--round-btn">
        <abc-popover
            trigger="manual"
            :value="showPopover"
            :visible-arrow="visibleArrow"
            theme="white"
            :z-index="zIndex"
            :placement="placement"
            :offset="popperOffset"
            :popper-style="{
                padding: 0,
                ...popperStyle
            }"
        >
            <div
                ref="reference"
                slot="reference"
                class="app-notice-reference"
                @click="showPopover = !showPopover"
            >
                <component :is="viewComponents.AppNoticeReference" :icon-size="iconSize"></component>
                <span v-if="showNoticeText">消息</span>
                <template v-if="!isVertical">
                    <span
                        v-if="!useAbcUIV2"
                        class="app-notice-count"
                        :style="{
                            opacity: unreadCount === 0 ? '0' : '1'
                        }"
                    >
                        {{ unreadCount }}
                    </span>

                    <span v-else-if="unreadCount" class="abc-notice-count__v2">
                        <abc-badge
                            theme="danger"
                            variant="round"
                            :value="unreadCount"
                        ></abc-badge>
                    </span>
                </template>
                <span v-else-if="unreadCount" class="app-notice-count-variant">
                    <abc-badge
                        theme="danger"
                        variant="round"
                        :value="unreadCount"
                    ></abc-badge>
                </span>
            </div>

            <div
                v-if="showPopover"
                v-abc-click-outside="handleClickOutside"
                class="app-notice-popover"
            >
                <div v-if="notices.length || isUpdateCmsPush" :class="['read-all', { 'read-all-update': isUpdateCmsPush }]">
                    <abc-flex justify="space-between" align="center" style="height: 100%;">
                        <abc-select
                            v-if="isUpdateCmsPush"
                            v-model="msgType"
                            size="small"
                            :width="100"
                            reference-mode="text"
                            text-mode-plus
                            :custom-class="{ 'up-popover-options': true }"
                            @change="onChangeMegType"
                        >
                            <abc-option
                                v-for="(item, index) in messageTypeOptions"
                                :key="index"
                                :value="item.value"
                                :label="item.label"
                            ></abc-option>
                        </abc-select>

                        <abc-button
                            v-if="notices.length"
                            variant="text"
                            :size="isUpdateCmsPush ? 'small' : 'normal'"
                            @click="clickReadAll"
                        >
                            全部已读
                        </abc-button>
                    </abc-flex>
                </div>

                <div
                    v-if="notices.length"
                    v-abc-scroll-loader="{
                        methods: fetchNotice,
                        isLast
                    }"
                    class="app-notice-popover-content"
                >
                    <abc-list
                        :data-list="showNoticeList"
                        size="large"
                        show-divider
                        :divider-config="{
                            theme: 'light',
                            margin: 'none',
                        }"
                        style="padding: 4px 0 4px 10px;"
                        @click-item="(e,item)=>onClickMessage(item)"
                    >
                        <template
                            v-if="!isUpdateCmsPush"
                            #prepend="{
                                item
                            }"
                        >
                            <abc-flex
                                align="center"
                                justify="center"
                                style="width: 40px; height: 40px; background: #ffffff; border: 1px solid var(--abc-color-P8); border-radius: var(--abc-border-radius-small);"
                            >
                                <abc-icon
                                    v-if="item.type === 100001"
                                    icon="s-announcement-color"
                                    color="var(--abc-color-B2)"
                                    size="24"
                                ></abc-icon>

                                <abc-icon
                                    v-else-if="item.type === 100002"
                                    icon="n-capsule-fill"
                                    color="var(--abc-color-Y2)"
                                    size="24"
                                ></abc-icon>
                                <img
                                    v-else-if="item.icon"
                                    :src="item.icon"
                                    alt="icon"
                                    style="width: 100%; height: 100%;"
                                />
                                <abc-icon
                                    v-else
                                    icon="n-capsule-fill"
                                    color="var(--abc-color-Y2)"
                                    size="24"
                                ></abc-icon>
                            </abc-flex>
                        </template>

                        <template #default="{ item }">
                            <abc-flex
                                v-if="isUpdateCmsPush"
                                class="update-cms-item"
                                align="center"
                            >
                                <abc-flex
                                    class="update-item-content"
                                    justify="space-between"
                                    gap="8"
                                >
                                    <abc-text class="list-item-text" :theme="item.detailReadCount === 1 ? 'gray-light' : 'black'">
                                        <abc-text bold>
                                            [{{ item.categoryName }}]
                                        </abc-text>
                                        {{ item.title }}
                                    </abc-text>
                                    <abc-text class="list-item-time" size="mini" :theme="item.detailReadCount === 1 ? 'gray-light' : 'gray'">
                                        {{ formatCacheTime(item.created) }}
                                    </abc-text>
                                </abc-flex>
                            </abc-flex>
                            <abc-flex
                                v-else
                                vertical
                                flex="1"
                                class="ellipsis cms-item"
                            >
                                <abc-flex justify="space-between">
                                    <abc-text class="ellipsis" :theme="item.detailReadCount === 1 ? 'gray-light' : 'black'">
                                        {{ item.title }}
                                    </abc-text>
                                    <abc-text :theme="item.detailReadCount === 1 ? 'gray-light' : 'black'">
                                        {{ formatTime(item.created) }}
                                    </abc-text>
                                </abc-flex>
                                <div
                                    :class="['abstract', item.detailReadCount === 1 ? 'gray-light' : 'black']"
                                    v-html="item.abstract"
                                >
                                </div>
                            </abc-flex>
                        </template>
                    </abc-list>
                </div>
                <div v-else class="no-notice">
                    <abc-content-empty value="暂无通知"></abc-content-empty>
                </div>
            </div>
        </abc-popover>

        <inform-dialog
            v-if="showInformDialog"
            v-model="showInformDialog"
            :inform-id="informId"
            :inform-type="informType"
            :detail-data="currentNotice"
            :modify-items="modifyPriceOrderItemIds"
        >
        </inform-dialog>
        <app-notice-dialog
            v-if="showNoticeDialog"
            v-model="showNoticeDialog"
            :data="currentNotice"
        ></app-notice-dialog>
        <require-dialog
            v-if="showPurchaseOrderDialog"
            v-model="showPurchaseOrderDialog"
            :is-resubmit="isResubmit"
            :order-id="informId"
            :gsp-inst-id="gspInstId"
            @resubmit="handleResubmit"
        ></require-dialog>
        <trans-order-detail-dialog
            v-if="showTransOrderDialog"
            :visible="showTransOrderDialog"
            :order-id="informId"
            @close="showTransOrderDialog = false"
            @resubmit="handleTransOrderResubmit"
        ></trans-order-detail-dialog>
        <!--查看盘点-->
        <check-order-detail-dialog
            v-if="showCheckOrderDialog"
            v-model="showCheckOrderDialog"
            :order-id="informId"
            :show-reinitiate-button="false"
            @close="showCheckOrderDialog = false"
        ></check-order-detail-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import { formatTime } from '@/utils';
    import { formatCacheTime } from '@/filters';
    import AppNoticeDialog from './app-notice-dialog.vue';

    import { MFE_ROUTER_NAME } from 'abc-micro-frontend';

    import { useMessageStore } from '@/service/message/message-store.js';
    import { AbcSocketMessageManager } from '@/service/message/manager.js';
    import { productEnablementReportDialogService } from 'views/layout/product-enablement-report-dialog/product-enablement-report-dialog';
    import {
        navigateToModuleByCms,
        navigateToSocialAccountCheckingNew, removeLeadingSlash, windowOpen,
    } from '@/core/navigate-helper';
    import { PROTOCOL } from 'utils/constants';
    import { businessTypeConst } from '@/views-pharmacy/common/constants';
    import {
        MessageCategoryEnum,
        MessageCategoryName, MessageIdEnum,
    } from '@/service/message/constant';

    import * as tools from '@/views-pharmacy/common/tools';
    import InformDialog from 'views/dashboard/inform-dialog/index.vue';
    import RequireDialog from '@/views-pharmacy/inventory/frames/purchase/require-goods/order-dialog.vue';
    import DialogGoodsDetail from '@/views-pharmacy/gsp/frames/first-battalion/goods/dialog-goods-detail/index.vue';
    import DialogSuppliersDetail from '@/views-pharmacy/gsp/frames/first-battalion/supplier/dialog-supplier-detail/index.vue';
    import DestroyApplyDialog from '@/views-pharmacy/gsp/frames/destroy/destroy-apply-dialog.vue';
    import DialogLossGoodsDetail from 'views/inventory/goods-out/detail.vue';
    import SuspiciousQualityDialog from '@/views-pharmacy/gsp/frames/suspicious-quality/suspicious-quality-dialog.vue';
    import OrderDetailDialog from '@/views-pharmacy/inventory/frames/purchase/return-goods/detail.vue';
    import DialogGoodsCheckDetail from 'views/inventory/goods-check/detail.vue';
    import DialogPriceAdjustmentDetail from '@/views-pharmacy/inventory/frames/price-adjustment/detail.vue';
    import TransOrderDetailDialog from '@/views/inventory/goods-trans/detail.vue';
    import CheckOrderDetailDialog from '@/views/inventory/goods-check/detail.vue';
    import {
        ReTransInDialog, ReTransOutDialog,
    } from 'views/inventory/goods-trans/trans-dialog';
    import { AnnouncementDialog } from 'views/layout/announcement/notice-dialog';
    import { isNull } from '@/common/utils';

    export default {
        name: 'AppNotice',
        components: {
            InformDialog,
            AppNoticeDialog,
            RequireDialog,
            TransOrderDetailDialog,
            CheckOrderDetailDialog,
        },
        props: {
            iconSize: {
                type: Number,
                default: 12,
            },
            placement: {
                type: String,
                default: 'bottom',
            },
            showNoticeText: {
                type: Boolean,
                default: false,
            },
            visibleArrow: {
                type: Boolean,
                default: true,
            },
            popperOffset: {
                type: Number,
                default: 0,
            },
            popperStyle: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            isVertical: {
                type: Boolean,
                default: false,
            },
            zIndex: {
                type: Number,
                default: 999999,
            },
        },
        data() {
            return {
                showPopover: false,
                showNoticeDialog: false,
                currentNotice: null,

                messageStore: null,
                showInformDialog: false,
                informType: '',
                informId: '',
                modifyPriceOrderItemIds: null,

                gspInstId: '',
                isResubmit: false,
                showPurchaseOrderDialog: false,
                showTransOrderDialog: false,
                showCheckOrderDialog: false,
                messageTypeOptions: [
                    {
                        value: null, label: '全部信息',
                    },
                    {
                        value: 0, label: '未读信息',
                    },
                ],
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
            ]),
            ...mapGetters('viewDistribute', ['viewComponents', 'viewDistributeConfig']),
            useAbcUIV2() {
                return this.viewDistributeConfig.useAbcUIV2;
            },
            isUpdateCmsPush() {
                return this.viewDistributeConfig.Dashboard.isUpdateCmsPush;
            },
            msgType: {
                get() {
                    return this.messageStore?.state?.noticesParams?.readStatus;
                },
                set(val) {
                    this.messageStore.setNoticesParams({ readStatus: val });
                },
            },
            notices() {
                return this.messageStore?.state?.messages || [];
            },
            unreadCount() {
                return this.messageStore.state.unreadCount < 100 ? this.messageStore.state.unreadCount : 99;
            },
            isLast() {
                return this.messageStore.state.noticesParams.total <= this.messageStore.state.messages.length;
            },
            mfeBasePath() {
                const moduleRoute = this.$router.options.routes.find((route) => route.name === MFE_ROUTER_NAME);
                return moduleRoute?.meta?.mfeBasePath || '/';
            },
            showNoticeList() {
                return this.notices?.map((item) => {
                    const detail = {
                        id: item.id,
                        detailReadCount: item.detailReadCount, // 0-未读，1-已读
                        created: item.created,
                        icon: '',
                        type: 0, // 类型
                        title: '', // 标题
                        abstract: '', // 摘要
                        originData: null, // 原始数据
                        categoryName: MessageCategoryName[item.category],
                    };
                    switch (item.messageBody.type) {
                        case 100001:// 系统更新日志
                        case 100003:// 首页弹窗消息
                            detail.title = item.messageBody.data.title;
                            detail.abstract = this.settlementExceptionCheck(item.messageBody?.data?.type) ? '' : item.messageBody.data.describe;
                            break;
                        case 100002:// 药品信息变更提示
                            detail.title = item.messageBody.data.title;
                            detail.abstract = item.messageBody.data.abstract;
                            break;
                        default:
                            detail.icon = item.messageBody.icon;
                            detail.title = item.messageBody.title || item.messageBody.data.title;
                            detail.abstract = item.messageBody.content || item.messageBody.data.content;
                            break;
                    }
                    detail.type = item.messageBody.type;
                    detail.originData = item.messageBody.data;
                    if (detail.originData && !detail.originData.created) {
                        detail.originData.created = item.created;
                    }
                    return detail;
                });
            },
        },
        created() {
            // 拉取系统消息
            this.messageStore = useMessageStore();
            this.socketMsgManager = AbcSocketMessageManager.getInstance();
        },
        methods: {
            formatTime,
            formatCacheTime,
            /**
             * desc [点击全部已读]
             */
            clickReadAll() {
                if (this.unreadCount !== 0) {
                    this.socketMsgManager.readAllNotices();
                }
            },
            handleResubmit(orderId) {
                this._timer = setTimeout(() => {
                    this.isResubmit = true;
                    this.informId = orderId;
                    this.showPurchaseOrderDialog = true;
                    clearTimeout(this._timer);
                }, 0);
            },
            handleTransOrderResubmit({
                orderId, goodsId, type, transType,
            }) {
                this._timer = setTimeout(() => {
                    if (transType === 1 || (transType !== 1 && type === 1)) {
                        this._reTransOutDialogObj = new ReTransOutDialog({
                            orderId,
                            goodsId,
                            transType,
                            refresh: this.refreshHandler,
                            isReTrans: true,// 特殊标记为重新调拨，数据还是之前的orderId获取但是是新建入库单，不修改原单据。
                        });
                        this._reTransOutDialogObj.generateDialog({
                            parent: this,
                        });
                        this.$once('hook:beforeDestroy',() => {
                            this._reTransOutDialogObj.destroyDialog();
                        });
                    } else {
                        this._reTransInDialogObj = new ReTransInDialog({
                            orderId,
                            goodsId,
                            refresh: this.refreshHandler,
                            isReTrans: true,// 特殊标记为重新调拨，数据还是之前的orderId获取但是是新建入库单，不修改原单据。
                        });

                        this._reTransInDialogObj.generateDialog({
                            parent: this,
                        });
                        this.$once('hook:beforeDestroy',() => {
                            this._reTransInDialogObj.destroyDialog();
                        });
                    }
                }, 0);
            },
            refreshHandler(showToast) {
                if (showToast) {
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                }
                this.$store.dispatch('fetchInventoryTodo', this.pharmacyNo);
            },
            navigate2Module(toPath) {
                this.$router.push(toPath, null, () => {
                    const route = this.$router.match(toPath);
                    this.$abcPlatform.notifyPlatformRouterChange(route);
                });
            },
            /**
             * desc [点击消息]
             */
            async  onClickMessage(item) {
                if (!this.isUpdateCmsPush) this.showPopover = false;
                if (item.detailReadCount !== 1) {
                    await this.socketMsgManager.readOneNotice(item.id);
                }
                switch (item.type) {
                    case 100001:// 系统更新日志
                    case 100003:// 首页弹窗消息
                        if (item.originData.type === 101) {
                            new AnnouncementDialog({
                                message: this.notices.find((it) => it.id === item.id),
                            }).generateDialog({ parent: this });
                        } else if (item.originData.type === 111) {
                            // 客户产品报告开箱弹窗
                            if (item.originData.extendData && item.originData.extendData.staffSopSheet) {
                                delete item.originData.extendData.staffSopSheet;
                            }
                            productEnablementReportDialogService.updateSopConfig(item.originData);
                        } else if (
                            [
                                MessageIdEnum.SHEBAO_DAILY_RECONCILIATION_EXCEPTION_NOTIFY,
                                MessageIdEnum.SHEBAO_SETTLEMENT_EXCEPTION_NOTIFY,
                                MessageIdEnum.TRACE_CODE_TODAY_REPORT_TIP,
                                MessageIdEnum.ICPC_REPORT_TIP,
                            ].includes(item.originData.type)
                        ) {
                            // 医保异常结算通知
                            const {
                                path,
                                module,
                                query,
                            } = item.originData.extendData || {};


                            if (!isNull(module)) {
                                navigateToModuleByCms(this.currentClinic, module, query);
                                return ;
                            }

                            let targetUrl = path ? path : this.$abcSocialSecurity.createCheckingPageUrl(item.extendData);
                            targetUrl = removeLeadingSlash(targetUrl);

                            navigateToSocialAccountCheckingNew(this.currentClinic, `/${targetUrl}`, () => {
                                this.navigate2Module(this.mfeBasePath + targetUrl);
                            });

                        } else if (item.originData.link) {
                            windowOpen(item.originData.link, '_blank', '', PROTOCOL.HTTPS);
                        }
                        break;
                    case 100002:// 药品信息变更提示
                        this.showNoticeDialog = true;
                        this.currentNotice = item.originData;
                        break;
                    case 100005: // 商品单
                        this.showInformDialog = true;
                        this.informType = '1';
                        this.informId = item.originData.goodsId;
                        this.currentNotice = item;
                        break;
                    case 100006:// 供应商
                        this.showInformDialog = true;
                        this.informType = '2';
                        this.informId = item.originData.supplierId;
                        this.currentNotice = item;
                        break;
                    case 100007:// 调价单
                        this.showInformDialog = true;
                        this.informType = '3';
                        this.informId = item.originData.modifyPriceOrderId;
                        this.modifyPriceOrderItemIds = item.originData.modifyPriceOrderItemIds;
                        this.currentNotice = item;
                        break;
                    case 100004:// 要货单
                        this.isResubmit = false;
                        this.showPurchaseOrderDialog = true;
                        this.informId = item.originData.claimOrderId;
                        this.gspInstId = item.originData.gspInstId;
                        break;
                    case 100008:// 调剂单
                        this.showTransOrderDialog = true;
                        this.informId = item.originData.transOrderId;
                        break;
                    case MessageCategoryEnum.GoodsDelete:// 商品删除自动盘0
                        this.showCheckOrderDialog = true;
                        this.informId = item.originData.orderId;
                        break;
                    case 100012:// 政策公告提示
                        new AnnouncementDialog({
                            message: this.notices.find((it) => it.id === item.id),
                        }).generateDialog({ parent: this });
                        break;
                    case 800001: // 审批通知
                        if (item.originData.business === businessTypeConst.goodsFirstOperation ||
                            item.originData.business === businessTypeConst.goodsMedicalEquipmentFirstOperation ||
                            item.originData.business === businessTypeConst.goodsOtherGoodsFirstOperation ||
                            item.originData.business === businessTypeConst.goodsModify ||
                            item.originData.business === businessTypeConst.goodsMedicalEquipmentModify ||
                            item.originData.business === businessTypeConst.goodsOtherGoodsModify
                        ) {
                            tools.openDialog({
                                propsData: {
                                    goodsId: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                    // gspOrderNo: item.originData.gspOrderNo, // 审批单号
                                },
                                component: DialogGoodsDetail,
                            });
                        }
                        if (item.originData.business === businessTypeConst.goodsSupplierFirstOperation ||
                            item.originData.business === businessTypeConst.goodsSupplierModify
                        ) {
                            tools.openDialog({
                                propsData: {
                                    supplierId: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                    // gspOrderNo: item.originData.gspOrderNo, // 审批单号
                                },
                                component: DialogSuppliersDetail,
                            });
                        }
                        if (item.originData.business === businessTypeConst.goodsDestroy) {
                            tools.openDialog({
                                propsData: {
                                    value: true,
                                    id: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                },
                                component: DestroyApplyDialog,
                            });
                        }
                        if (item.originData.business === businessTypeConst.goodsProcurementCollective ||
                            item.originData.business === businessTypeConst.goodsProcurementSelf
                        ) {
                            tools.openDialog({
                                propsData: {
                                    value: true,
                                    orderId: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                },
                                component: RequireDialog,
                            });
                        }
                        if (item.originData.business === businessTypeConst.goodsReportingLosses) {
                            tools.openDialog({
                                propsData: {
                                    visible: true,
                                    orderId: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                    title: '报损申请',
                                },
                                component: DialogLossGoodsDetail,
                            });
                        }
                        if (item.originData.business === businessTypeConst.goodsSuspiciousReport) {
                            tools.openDialog({
                                propsData: {
                                    value: true,
                                    id: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                },
                                component: SuspiciousQualityDialog,
                            });
                        }
                        if (item.originData.business === businessTypeConst.goodsStockReturnOut) {
                            tools.openDialog({
                                propsData: {
                                    value: true,
                                    orderId: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                },
                                component: OrderDetailDialog,
                            });
                        }
                        if (item.originData.business === businessTypeConst.goodsStockCheck) {
                            tools.openDialog({
                                propsData: {
                                    value: true,
                                    orderId: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                },
                                component: DialogGoodsCheckDetail,
                            });
                        }
                        if (item.originData.business === businessTypeConst.marketingPriceAdjustment) {
                            tools.openDialog({
                                propsData: {
                                    value: true,
                                    orderId: item.originData.businessId, // 商品id
                                    gspInstId: item.originData.processInstId, // 审核任务id
                                    // gspOrderNo: item.originData.gspOrderNo, // 审批单号
                                },
                                component: DialogPriceAdjustmentDetail,
                            });
                        }
                        break;
                    case 300001:// 短信余量不足
                    case 300002:// 短信余量为零
                        if (this.isChainAdmin) {
                            this.$router.push({
                                name: 'messagepushOverView',
                            });
                        }
                        break;
                    default:
                        break;
                }
            },
            /**
             * desc [关闭系统消息面板，需要标记全部已读]
             */
            closeNotice() {
                if (this.unreadCount && this.showPopover && this.notices.length) {
                    this.socketMsgManager.readAllNotices();
                }
                if (!this.showNoticeDialog) {
                    this.showPopover = false;
                }
            },
            /**
             * @desc 滚动获取消息列表
             * <AUTHOR>
             * @date 2018/10/14 11:37:53
             */
            async fetchNotice() {
                const {
                    offset, limit, total,isLast,
                } = this.messageStore.state.noticesParams;
                if (!isLast) {
                    this.messageStore.setNoticesParams({
                        offset: offset + limit,
                        isLast: offset + limit >= total,
                    });
                    await this.socketMsgManager.getMessages();
                }
            },
            handleClickOutside(e) {
                if (this.$refs.reference.contains(e.target)) {
                    return;
                }
                if (e && e.path) {
                    const isInnerEl = e.path.some(
                        (el) => {
                            return el.classList && (Array.from(el.classList).includes('up-popover-options') || Array.from(el.classList).includes('abc-dialog-cover') || Array.from(el.classList).includes('abc-dialog'));
                        },
                    );
                    if (isInnerEl) {
                        return;
                    }
                }
                this.showPopover = false;
            },
            onChangeMegType() {
                this.socketMsgManager.initMessage();
            },
            settlementExceptionCheck(type) {
                return [MessageIdEnum.SHEBAO_DAILY_RECONCILIATION_EXCEPTION_NOTIFY, MessageIdEnum.SHEBAO_SETTLEMENT_EXCEPTION_NOTIFY].includes(type);
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';
    @import 'src/styles/mixin.scss';
    @import 'styles/abc-common.scss';

    .app-notice-wrapper {
        position: relative;

        > div {
            display: flex;
            width: 100%;
            height: 100%;
        }

        .app-notice-reference {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        i {
            position: inherit;
        }

        .app-notice-count {
            position: absolute;
            bottom: 14px;
            left: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 16px;
            height: 16px;
            padding: 0 4px;
            font-family: Roboto;
            font-size: 12px;
            font-weight: bold;
            color: $R2;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            transform: scale(0.9);
        }

        .abc-notice-count__v2,
        .app-notice-count-variant {
            position: absolute;
            bottom: 14px;
            left: 14px;
        }

        .abc-notice-count__v2 {
            display: flex;
        }
    }

    .app-notice-popover {
        width: 380px;

        .read-all {
            padding: var(--abc-paddingTB-s) 10px var(--abc-paddingTB-s) 12px;
            border-bottom: 1px solid var(--abc-color-P7);

            &.read-all-update {
                height: 44px;
            }
        }

        .app-notice-popover-content {
            height: 350px;
            overflow-y: scroll;

            @include scrollBar();
        }

        .update-cms-item {
            width: 100%;
            height: 44px;

            .update-item-content {
                align-items: baseline;
                width: 100%;

                .list-item-text {
                    flex: 1;
                    font-family: Roboto;
                    text-align: justify;

                    @include ellipsis(2);
                }

                .list-item-time {
                    text-align: right;
                }
            }
        }

        .cms-item {
            font-family: Roboto;
        }

        .abstract {
            display: -webkit-box;
            overflow: hidden;
            line-height: 22px;
            text-overflow: ellipsis;
            white-space: normal;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;

            &.gray-light {
                color: var(--abc-color-T3);
            }

            &.black {
                color: var(--abc-color-T1);
            }
        }

        .app-notice-item-wrap {
            position: relative;
            height: 350px;
            padding: var(--abc-paddingTB-s) 0;
            overflow-y: auto;
            overflow-y: overlay;

            @include scrollBar;
        }

        .no-notice {
            display: flex;
            justify-content: center;
            width: 100%;
            height: 350px;
        }
    }
</style>
