<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="data.title"
        content-styles="padding: 0;"
        :before-close="read"
        custom-class="app-notice-dialog"
        append-to-body
    >
        <div slot="title-append" class="created-time">
            {{ formatTime(data.created) }}
        </div>

        <div class="dialog-content clearfix" v-html="data.content"></div>

        <div slot="footer" class="dialog-footer">
            <abc-button type="primary" @click="read">
                知道了
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { formatTime } from 'utils/index';

    export default {
        name: 'AppNoticeDialog',
        props: {
            value: Boolean,
            data: Object,
        },
        data() {
            return {
                showDialog: this.value,
            };
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        methods: {
            formatTime,
            read() {
                this.showDialog = false;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import '~styles/theme';
    @import '~styles/mixin';

    .app-notice-dialog {
        width: 580px;

        .medicine-name {
            padding: 0 24px;
            margin-top: 24px;
            font-size: 16px;
            line-height: 20px;
        }

        .action {
            display: flex;
            align-items: center;
            padding: 0 24px;
            margin: 16px 0 24px;
            font-size: 20px;
            line-height: 20px;

            span {
                padding: 0 4px;
                margin-left: 4px;
                font-size: 16px;
                color: #687481;
            }
        }

        .effect-clinics {
            padding: 16px 24px;
            line-height: 1;
            background-color: #f5f7fb;

            span {
                line-height: 20px;
                color: #ff9933;
            }

            li {
                //display: inline-block;
                height: 20px;
                margin: 8px 0 0;
                line-height: 20px;
            }
        }

        .created-time {
            display: inline-block;
            margin-left: 20px;
            font-size: 14px;
            font-weight: normal;
            color: $T2;
        }
    }
</style>
