<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        custom-class="cms-notice-dialog-wrapper"
        content-styles="min-width: 400px; max-width: 1200px; min-height: 212px; padding: 24px;"
        :title="''"
        append-to-body
    >
        <div class="notice-desc" v-html="noticeInfo.desc"></div>
        <div v-if="noticeInfo.fileLink" class="file-link">
            相关文件：
            <a :href="noticeInfo.fileLink" target="_blank">{{ noticeInfo.fileName }}</a>
        </div>
        <!--        <div v-if="noticeInfo.link" class="file-link">-->
        <!--            详细信息：-->
        <!--            <a :href="noticeInfo.link" target="_blank">{{ noticeInfo.link }}</a>-->
        <!--        </div>-->
        <div class="btn-wrapper">
            <abc-button style="width: 142px;" @click="closeHandler">
                我知道了
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import AbcCmsManagerService from '@/service/cms/cms-manager-service.js';
    export default {
        name: 'CmsNoticeDialog',
        props: {
            value: {
                tyep: Boolean,
                required: true,
            },
            noticeInfo: {
                type: Object,
                required: true,
            },
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },
        created() {
            this.cmsService = AbcCmsManagerService.getCmsService();
        },
        methods: {
            closeHandler() {
                this.cmsService.reportClickPushItem(this.noticeInfo.id);
                this.$emit('close');
                this.showDialog = false;
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';

.cms-notice-dialog-wrapper {
    .notice-title-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .title {
            margin-top: 16px;
            font-size: 14px;
            font-weight: 500;
            line-height: 22px;
        }
    }

    .title-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        background-color: $Y5;
        border-radius: 56px;
    }

    .notice-desc {
        margin-top: 12px;
        line-height: 16px;

        h1 {
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: 32px; /* 150% */
        }

        h2 {
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; /* 150% */
        }

        p {
            font-size: 16px;
            line-height: 24px;
            text-align: justify;
        }

        strong {
            font-weight: bold;
        }

        img {
            max-width: 100%;
        }

        .ql-align-center {
            text-align: center;
        }

        a {
            color: var(--abc-color-Theme1, #005ed9);
            text-decoration: underline;
        }
    }

    .file-link {
        margin-top: 8px;
        color: $T2;

        >a {
            color: #005ed9;
        }
    }

    .btn-wrapper {
        display: flex;
        justify-content: center;
        padding-bottom: 16px;
        margin-top: 24px;
    }
}
</style>
