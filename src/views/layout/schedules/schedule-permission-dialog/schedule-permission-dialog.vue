<template>
    <abc-dialog
        v-model="showDialog"
        class="schedule-permission-dialog-wrapper"
        title="权限设置"
        content-styles="width:500px;padding: 24px; max-height: 360px"
    >
        <div class="dialog-content">
            <section class="setting-item-wrapper">
                <label>可设置排班人员</label>
                <div class="option-wrapper">
                    <abc-radio-group v-model="schedulePrivilege" item-block>
                        <template v-if="isUseScheduleSettingControlPermission">
                            <abc-radio :label="0" style="height: 32px;">
                                有排班设置权限
                            </abc-radio>
                        </template>

                        <template v-else>
                            <abc-radio :label="0" style="height: 32px;">
                                有执行站/挂号/管理权限
                            </abc-radio>
                        </template>
                        <abc-radio :label="1" style="height: 32px;">
                            自定义人员名单
                        </abc-radio>
                    </abc-radio-group>
                    <div class="employee-wrapper">
                        <div v-for="(employee, index) in scheduleWhiteList" :key="employee.id" class="employee-item">
                            <i class="iconfont cis-icon-patient"></i>
                            {{ employee.name }}
                            <div
                                v-if="schedulePrivilege === 1"
                                class="operation"
                                @click="handleEmployeeDelete(index, 'schedule')"
                            >
                                <i></i>
                            </div>
                        </div>
                        <abc-button
                            v-if="schedulePrivilege === 1"
                            type="text"
                            class="add-btn"
                            @click="openSelectEmployeeDialog('schedule')"
                        >
                            添加
                        </abc-button>
                    </div>
                </div>
            </section>

            <section class="setting-item-wrapper">
                <label>可设置班次人员</label>
                <div class="option-wrapper">
                    <abc-radio-group v-model="shiftPrivilege" item-block>
                        <template v-if="isUseScheduleSettingControlPermission">
                            <abc-radio :label="0" style="height: 32px;">
                                有排班设置权限
                            </abc-radio>
                        </template>
                        <template v-else>
                            <abc-radio :label="0" style="height: 32px;">
                                有管理权限
                            </abc-radio>
                        </template>
                        <abc-radio :label="1" style="height: 32px;">
                            自定义人员名单
                        </abc-radio>
                    </abc-radio-group>
                    <div class="employee-wrapper">
                        <div v-for="(employee, index) in shiftWhiteList" :key="employee.id" class="employee-item">
                            <i class="iconfont cis-icon-patient"></i>
                            {{ employee.name }}
                            <div
                                v-if="shiftPrivilege === 1"
                                class="operation"
                                @click="handleEmployeeDelete(index, 'shift')"
                            >
                                <i></i>
                            </div>
                        </div>
                        <abc-button
                            v-if="shiftPrivilege === 1"
                            type="text"
                            class="add-btn"
                            @click="openSelectEmployeeDialog('shift')"
                        >
                            添加
                        </abc-button>
                    </div>
                </div>
            </section>
        </div>

        <div slot="footer" class="dialog-footer permission-dialog-footer">
            <span class="desc-tips">管理员不受自定义名单限制</span>
            <abc-button :loading="isBtnLoading" @click="validate">
                确定
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>

        <abc-dialog
            v-if="isEmployeeDialogVisible"
            v-model="isEmployeeDialogVisible"
            :title="'选择人员'"
            content-styles="padding: 0 0 0 24px;height: 494px"
            append-to-body
        >
            <abc-transfer v-model="currentEditEmployees" :data="employeeOptions">
                <template #selected="{ item }">
                    <abc-icon
                        icon="patient"
                        size="12"
                        color="#58a0ff"
                        style="margin-right: 6px;"
                    ></abc-icon>
                    {{ item.name }}
                </template>
            </abc-transfer>

            <div slot="footer" class="dialog-footer">
                <abc-button @click="handleEmployeeSelectConfirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="isEmployeeDialogVisible = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import ClinicAPI from 'api/clinic';
    import Clone from 'utils/clone';
    import RegistrationAPI from 'api/registrations/index';

    export default {
        name: 'SchedulePermissionDialog',
        props: {
            value: Boolean,
        },
        data() {
            return {
                schedulePrivilege: 0,
                shiftPrivilege: 0,

                scheduleWhiteList: [],
                shiftWhiteList: [],

                employeeOptions: [],
                currentEditEmployees: [],
                currentEditType: '',

                isEmployeeDialogVisible: false,
                isBtnLoading: false,
            };
        },
        computed: {
            ...mapGetters(['consultingRoomSetting', 'isOpenCall']),

            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            // 是否使用排班设置模块控制排班权限
            isUseScheduleSettingControlPermission() {
                return this.viewDistributeConfig.Settings.schedule.isUseScheduleSettingControlPermission;
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        created() {
            this.fetchSchedulePermissionConfig();
        },
        methods: {
            validateScheduleEmpty() {
                if (this.schedulePrivilege === 0) {
                    return true;
                }
                return this.scheduleWhiteList.length > 0;
            },

            validateShiftEmpty() {
                if (this.shiftPrivilege === 0) {
                    return true;
                }
                return this.shiftWhiteList.length > 0;
            },

            async openSelectEmployeeDialog(type) {
                const { data } = await ClinicAPI.getClinicEmployee();
                this.employeeOptions = data.rows;
                this.currentEditType = type;

                this.currentEditEmployees =
                    type === 'schedule' ? Clone(this.scheduleWhiteList) : Clone(this.shiftWhiteList);

                this.currentEditEmployees.forEach((item) => {
                    this.employeeOptions.forEach((employee) => {
                        if (item.id === employee.id) {
                            employee.checked = true;
                        }
                    });
                });
                this.isEmployeeDialogVisible = true;
            },

            handleEmployeeSelectConfirm() {
                if (this.currentEditType === 'schedule') {
                    this.scheduleWhiteList = Clone(this.currentEditEmployees);
                } else {
                    this.shiftWhiteList = Clone(this.currentEditEmployees);
                }

                this.isEmployeeDialogVisible = false;
            },

            handleEmployeeDelete(index, type) {
                if (type === 'schedule') {
                    this.scheduleWhiteList.splice(index, 1);
                } else {
                    this.shiftWhiteList.splice(index, 1);
                }
            },

            async fetchSchedulePermissionConfig() {
                try {
                    const { data } = await RegistrationAPI.fetchSchedulePermissionConfig();
                    this.schedulePrivilege = data.schedulePrivilege;
                    this.shiftPrivilege = data.shiftPrivilege;
                    this.scheduleWhiteList = data.scheduleWhiteList || [];
                    this.shiftWhiteList = data.shiftWhiteList || [];
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: e.message || '拉取配置失败，请稍后重试',
                    });
                }
            },

            validate() {
                if (this.validateScheduleEmpty() && this.validateShiftEmpty()) {
                    this.submit();
                } else {
                    this.$Toast({
                        message: '请添加人员',
                        type: 'error',
                    });
                }
            },

            async submit() {
                try {
                    this.isBtnLoading = true;
                    await RegistrationAPI.updateSchedulePermissionConfig({
                        schedulePrivilege: this.schedulePrivilege,
                        shiftPrivilege: this.shiftPrivilege,
                        scheduleWhiteList: this.scheduleWhiteList.map((item) => item.id),
                        shiftWhiteList: this.shiftWhiteList.map((item) => item.id),
                    });
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (err) {
                    this.$Toast({
                        message: `保存失败${err.message}`,
                        type: 'error',
                    });
                }
                this.isBtnLoading = false;
                this.showDialog = false;
            },
        },
    };
</script>
<style lang="scss">
    @import 'schedule-permission-dialog';
</style>
