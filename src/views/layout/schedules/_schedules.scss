@import '~styles/theme.scss';

.schedules-content__item-popper-wrapper {
    background: rgba(255, 255, 255, 1);
    border: 1px solid var(--abc-color-P7);
    border-radius: var(--abc-border-radius-small);
    box-shadow: var(--abc-shadow-1);
}

.schedule-content__panel-header {
    height: 36px;
    padding-left: 16px;
    background: var(--abc-color-cp-grey3);
    border-bottom: 1px solid var(--abc-color-P8);
    border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;

    .shift {
        width: 108px;
    }

    .time {
        width: 108px;
    }

    .number {
        width: 58px;
    }

    .consulting-room {
        flex-grow: 1;
    }

    .reserve-period {
        width: 380px;
    }

    .old-reserve-period {
        width: 230px;
    }

    .show-registration-category {
        width: 352px;

        &.not-convenience {
            width: 160px;
        }

        &.only-normal-category {
            width: 140px !important;
        }

        &-flexible-time {
            width: 440px;

            &.not-convenience {
                width: 310px;
            }

            &.only-normal-category {
                width: 280px !important;
            }
        }
    }
}

.schedule-content__panel-body {
    min-height: 120px;
    max-height: 224px;
    padding: 0 6px 0  16px;
    overflow-x: hidden;
    overflow-y: scroll;

    @include scrollBar();

    .num-form-item {
        .show-registration-category {
            width: 344px;

            &.not-convenience {
                width: 150px;
            }

            &.only-normal-category {
                width: 130px !important;
            }
        }
    }

    .schedule-num-same-time {
        width: 230px;

        &.show-registration-category {
            width: 440px;

            &.not-convenience {
                width: 310px;
            }

            &.only-normal-category {
                width: 280px !important;
            }
        }
    }

    .schedule-reserve-period {
        width: 380px;
        padding-right: 12px;

        .abc-autocomplete-wrapper {
            .append-input {
                width: 40px;
            }
        }
    }
}

.schedules-wrapper {
    position: relative;
    border: 1px solid var(--abc-color-P7);
    border-radius: var(--abc-border-radius-small);

    .right-container {
        position: absolute;
        top: 7px;
        right: 12px;
    }

    .schedules-content__item-wrapper {
        width: 100%;
        height: 100%;
        padding: var(--abc-paddingTB-m) 0;

        .schedules-content__item + .schedules-content__item {
            margin-top: 2px;
        }
    }
}

.shifts-confirm-dialog {
    ol {
        padding-left: 16px;
        list-style: disc;

        li {
            margin-top: 12px;
            color: $T2;

            > div {
                display: flex;

                > div {
                    flex: 1;
                    width: 0;
                    line-height: 20px;
                }
            }
        }
    }
}

.consulting-room-selector {
    .option-item-wrapper {
        max-height: 210px;
    }

    .setting {
        position: fixed;
        bottom: -36px;
        left: -1px;
        width: calc(100% + 2px);
        height: 36px;
        text-align: right;
        cursor: pointer;
        background-color: #ffffff;
        border: 1px solid $P3;
        border-bottom-right-radius: 4px;
        border-bottom-left-radius: 4px;
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

        span.icon {
            position: relative;
            top: 50%;
            display: inline-block;
            margin-right: 16px;
            color: $T3;
            transform: translateY(-50%);
        }
    }
}

.abc-dialog.consulting-room-dialog {
    ul li {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 32px;
        padding: 0 16px;
        line-height: 32px;
        cursor: pointer;
        border-bottom: #e6eaee 1px solid;

        &:hover {
            background: $P4;
        }

        :last-child {
            border: none;
        }

        .abc-form-item {
            margin-bottom: 0;

            .edit-input {
                flex: 1;

                input.abc-input__inner {
                    padding: 0;

                    &:focus {
                        border: none !important;
                        box-shadow: none !important;
                    }

                    &:hover {
                        border: none !important;
                    }
                }
            }
        }

        .operation-wrapper {
            display: flex;
            margin-left: auto;

            button.abc-button-text {
                min-width: 28px;
            }

            button.btn-danger {
                color: $R2;
            }

            button.btn-cancel {
                color: $T2;
            }
        }

        &.btn-add {
            font-size: 14px;
            color: #8d9aa8;
            user-select: none;
            border-bottom: none;
            border-bottom-right-radius: 4px;
            border-bottom-left-radius: 4px;

            .cis-icon-plus_thin {
                margin-right: 8px;
                font-size: 12px;
            }

            &:hover {
                color: $B1;
            }
        }
    }

    .abc-dialog-footer {
        padding: 10px 16px;

        button.abc-button {
            min-width: 60px;
            height: 28px;
        }
    }
}
