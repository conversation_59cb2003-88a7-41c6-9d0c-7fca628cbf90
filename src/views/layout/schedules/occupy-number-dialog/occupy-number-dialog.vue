<template>
    <abc-dialog
        v-model="showDialog"
        class="occupy-number-dialog-wrapper"
        :content-styles="dialogContentStyles"
        :show-close="!isSelectMode"
        :custom-top="customTop"
        title="设置预留号"
        :append-to-body="true"
        @open="handleDialogOpen"
        @close-dialog="$emit('close')"
    >
        <div slot="title" :class="['occupy-number-dialog__content_header', { 'is-select-mode': isSelectMode }]">
            <div class="header-operation">
                <template v-if="isSelectMode">
                    <span class="doctor-name">{{ doctorName }}</span>
                    <span class="time">{{ timeDisplay }}</span>
                </template>
                <template v-else>
                    <template v-if="!isEdit">
                        <abc-button
                            v-if="isEnableLeaveForMember"
                            type="blank"
                            size="small"
                            @click.stop="handleEditClick(EDIT_TYPE.MEMBER)"
                        >
                            会员预留
                        </abc-button>
                        <abc-button
                            v-if="isEnableLeaveForPC"
                            type="blank"
                            size="small"
                            @click.stop="handleEditClick(EDIT_TYPE.LOCALE)"
                        >
                            现场预留
                        </abc-button>
                    </template>
                    <template v-else>
                        <abc-button
                            size="small"
                            :loading="buttonLoading"
                            @click.stop="handleSaveClick"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            type="blank"
                            size="small"
                            :loading="buttonLoading"
                            @click.stop="handleCancelClick"
                        >
                            取消
                        </abc-button>
                    </template>
                </template>
            </div>

            <div class="status-list">
                <!-- <span class="gray">已占用</span> -->
                <span class="white">可用号源</span>
                <span v-if="isEnableLeaveForMember" class="orange">会员预留</span>
                <span v-if="isEnableLeaveForPC" class="lightblue">现场预留</span>
            </div>
        </div>
        <div
            ref="selectionArea"
            v-abc-loading="dataLoading"
            :class="['occupy-number-dialog__content', {
                'is-select-mode': isSelectMode, editable: isEdit
            }]"
        >
            <!-- 自定义时间预约 -->
            <div v-if="customizeReservationVisible">
                <div
                    v-for="(o, index) in cGroupList"
                    :key="o.label"
                >
                    <div
                        class="title"
                        :style="{
                            marginTop: index > 0 ? '4px' : '24px'
                        }"
                    >
                        {{ o.label }}
                    </div>

                    <div
                        class="order-wrapper c-mode"
                    >
                        <div
                            v-for="(m, n) in o.value"
                            :key="n"
                            class="occupy-card"
                            :class="m.list.length < 15 ? 'c-mode' : ''"
                        >
                            <div class="card-header c-mode">
                                {{ isShowRegistrationCategory ? (RegistrationCategoryText[m.registrationCategory] || '') : '' }} {{ `${m.start }-${ m.end}` }}
                            </div>

                            <ul>
                                <li
                                    v-for="(item, i) in m.list"
                                    :key="item.orderNo + item.end + i"
                                    ref="selectOption"
                                    :class="{
                                        selected: isSelected(item),
                                        checked: isChecked(item),
                                        editable: isEdit,
                                    }"
                                    :disabled="isDisabled(item)"
                                    @click="handleItemCheck(item, m.start, m.end)"
                                >
                                    <span
                                        v-if="[EDIT_TYPE.MEMBER, EDIT_TYPE.LOCALE].includes(item.type)"
                                        class="line"
                                        :class="{
                                            'is-member': item.type === EDIT_TYPE.MEMBER,
                                            'is-locale': item.type === EDIT_TYPE.LOCALE,
                                        }"
                                    ></span>
                                    <span v-if="!isGenerateOrderNoOnSign" class="number">{{ item.orderNo }}</span>
                                    <i v-if="item.flag && item.flag === '下午'" class="icon iconfont cis-icon-pm1 icon-tip"></i>
                                    <i v-if="item.flag && item.flag === '晚上'" class="icon iconfont cis-icon-night icon-tip"></i>

                                    <div class="tips">
                                        <span v-if="item.type === EDIT_TYPE.MEMBER">
                                            号源已设置为会员预留
                                        </span>
                                        <span v-else-if="item.type === EDIT_TYPE.LOCALE">
                                            号源已设置为现场预留
                                        </span>
                                        <span v-else-if="item.type === EDIT_TYPE.NONE && item.scheduleSelfStatus === 10 && item.restCount">
                                            已停诊不可预留
                                        </span>
                                        <span v-else>
                                            号源已被约出
                                        </span>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div ref="selectionRect" class="selection-rect"></div>
            </div>

            <!-- 精准时间 or 上下午晚上 -->
            <div v-if="preciseAndSXWVisible" class="order-wrapper">
                <div v-for="(option, optionIndex) in optionList" :key="optionIndex" class="occupy-card">
                    <div v-for="(o, key) in option.scheduleIntervals" :key="key" class="item">
                        <div
                            class="card-header"
                            :style="{
                                paddingTop: key > 0 ? '4px' : '24px'
                            }"
                        >
                            {{ isShowRegistrationCategory ? (RegistrationCategoryText[option.registrationCategory] || '') : '' }} {{ o.timeOfDay }} {{ `${o.start }-${ o.end}` }}
                        </div>
                        <ul>
                            <li
                                v-for="(item, index) in o.list"
                                :key="index"
                                ref="selectOption"
                                :class="{
                                    selected: isSelected(item),
                                    checked: isChecked(item),
                                    editable: isEdit,
                                }"
                                :disabled="isDisabled(item)"
                                @click="handleItemCheck(item, o.start, o.end)"
                            >
                                <span
                                    v-if="[EDIT_TYPE.MEMBER, EDIT_TYPE.LOCALE].includes(item.type)"
                                    class="line"
                                    :class="{
                                        'is-member': item.type === EDIT_TYPE.MEMBER,
                                        'is-locale': item.type === EDIT_TYPE.LOCALE,
                                    }"
                                ></span>
                                <template v-if="!isGenerateOrderNoOnSign">
                                    <span class="number">{{ item.orderNo }}</span>
                                    <span class="time">{{ item.start }}</span>
                                </template>
                                <div class="tips">
                                    <span v-if="item.type === EDIT_TYPE.MEMBER">
                                        号源已设置为会员预留
                                    </span>
                                    <span v-else-if="item.type === EDIT_TYPE.LOCALE">
                                        号源已设置为现场预留
                                    </span>
                                    <span v-else-if="item.type === EDIT_TYPE.NONE && item.scheduleSelfStatus === 10 && item.restCount">
                                        已停诊不可预留
                                    </span>
                                    <span v-else>
                                        号源已被约出
                                    </span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div ref="selectionRect" class="selection-rect"></div>
            </div>

            <div v-if="emptyVisible" class="empty-wrapper">
                <abc-content-empty value="未设置医生号源数量，请在排班中设置">
                </abc-content-empty>
            </div>
        </div>
        <div v-if="isSelectMode" slot="footer" class="occupy-number-dialog__footer">
            <abc-button :disabled="!currentChecked" @click="handleSelectConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="handleCloseClick">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import {
        off, on,
    } from 'utils/dom';
    import Clone from 'utils/clone';
    import RegistrationAPI from 'api/registrations/index';
    import { mapGetters } from 'vuex';
    import {
        GENERATE_ORDER_NO_TIME_TYPE, RESERVATION_TIME_TYPE,
    } from 'views/settings/registered-reservation/constant';
    import {
        RegistrationCategory, RegistrationCategoryText,
    } from '@/views-hospital/registered-fee/constant';

    const EDIT_TYPE = {
        NONE: 0, // 没有编辑
        LOCALE: 1, // 现场预留
        MEMBER: 2, // 会员预留
    };
    // 该组件有三种形态 mode
    // 初始态：只能查看
    // 编辑态：排班时设置预留号使用
    // 选择态：预约时，选择对应的号
    export default {
        props: {
            value: Boolean,
            departmentId: String,
            doctorId: String,
            doctorName: String,
            workingDate: String,
            mode: String,
            regist: Number,
            registrationType: {
                type: Number,
                default: 0,
            },
        },

        data() {
            return {
                RegistrationCategory,
                RegistrationCategoryText,
                // 是否处于编辑态
                editType: 0, // 0 => 没有编辑，1 => 会员预留, 2 => 现场预留

                selectBegin: {
                    x: 0,
                    y: 0,
                    time: 0,
                },
                selectEnd: {
                    x: 0,
                    y: 0,
                    time: 0,
                },

                // 当前选中项，只在选择模式有效
                currentChecked: null,

                buttonLoading: false,

                dataLoading: false,

                isSelectActive: false,

                editData: {},

                data: {},

                // 预约设置
                reservationConfig: {},

                currentCheckPeriod: null,
            };
        },

        computed: {
            ...mapGetters(['registrationsConfig','therapyReservationConfig','isEnableRegUpgrade','appointmentConfig']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                async set(val) {
                    this.$emit('input', val);
                },
            },

            isEnableLeaveForMember() {
                return this.reservationConfig?.enableLeaveForMember;
            },

            isEnableLeaveForPC() {
                return this.reservationConfig?.enableLeaveForPC;
            },

            emptyVisible() {
                if (!this.data.registrationCategoryScheduleIntervals) return false;

                return this.data.registrationCategoryScheduleIntervals.length < 1;
            },

            customizeReservationVisible() {
                if (!this.data.registrationCategoryScheduleIntervals) return false;

                return this.reservationConfig?.serviceType === 2 && this.data.registrationCategoryScheduleIntervals.length > 0;
            },

            preciseAndSXWVisible() {
                if (!this.data.registrationCategoryScheduleIntervals) return false;

                return [0, 1].includes(this.reservationConfig?.serviceType) && this.data.registrationCategoryScheduleIntervals.length > 0;
            },
            // 门诊开启了签到取号 (分段模式&&开启签到取号)
            isGenerateOrderNoOnSign() {
                return this.reservationConfig?.fixedOrderDisplayServiceType === RESERVATION_TIME_TYPE.OTHER && this.reservationConfig?.generateOrderNoTime === GENERATE_ORDER_NO_TIME_TYPE.SIGN_IN;
            },

            isOpenOccupyNumber() {
                return this.isEnableLeaveForMember || this.isEnableLeaveForPC;
            },

            timeDisplay() {
                const workTime = this.workingDate;
                const date = new Date(workTime.replace(/-/g, '/'));
                let weekDay = '';
                if (date.getDay() === 0) {
                    weekDay = ' 周日';
                } else {
                    const dayOfWeek = date.getDay().toLocaleString('zh-hans-CN-u-nu-hanidec', { useGrouping: false });
                    weekDay = ` 周${dayOfWeek}`;
                }
                return workTime.substr(-5) + weekDay;
            },

            optionList() {
                return this.isEdit ? this.editData.registrationCategoryScheduleIntervals : this.data.registrationCategoryScheduleIntervals;
            },

            /**
             * @desc 按照 timeOfDay 分组
             * <AUTHOR>
             * @date 2020/01/03 13:15:07
             * @params
             * @return
             */
            groupList() {
                const group = {};
                if (this.optionList) {
                    this.optionList.forEach((item) => {
                        const groupKey = item.timeOfDay;

                        if (!group[groupKey]) {
                            group[groupKey] = [];
                        }
                        const list = group[groupKey];

                        list.push(item);
                    });
                }
                return group;
            },

            cGroupList() {
                if (this.reservationConfig?.serviceType !== 2) return [];

                const timeOptions = ['上午', '下午', '晚上'];
                const timeOfDayList = [];

                timeOptions.forEach((timeItem) => {
                    const allList = [];
                    this.optionList.forEach((optionItem) => {
                        optionItem.scheduleIntervals?.forEach((cur) => {
                            const {
                                timeOfDay, list, start, end,
                            } = cur;

                            // 目前只考虑会跨一个时间段
                            let flag;
                            list.forEach((o, key) => {
                                if (key === 0) {
                                    flag = o.timeOfDay;
                                } else {
                                    if (o.timeOfDay !== flag) o.flag = o.timeOfDay;
                                }
                                o.registrationCategory = optionItem.registrationCategory;
                            });
                            if (timeItem === timeOfDay) {
                                allList.push({
                                    start,
                                    end,
                                    list,
                                    registrationCategory: optionItem.registrationCategory,
                                });
                            }
                        });
                    });
                    timeOfDayList.push({
                        label: timeItem,
                        value: allList,
                    });
                });
                return timeOfDayList.filter((item) => item.value.length > 0);
            },

            EDIT_TYPE: () => EDIT_TYPE,

            isEdit() {
                return this.editType !== EDIT_TYPE.NONE;
            },

            isSelectMode() {
                return this.mode === 'select';
            },

            dialogContentStyles() {
                // if (this.isSelectMode) {
                //     return 'padding: 0px; width: 540px; max-height: 400px; min-height: 300px;';
                // }
                return 'padding: 0 14px 24px 24px; width: 784px;overflow-x:hidden;overflow-y: auto;max-height: 500px; min-height: 300px;';
            },

            customTop() {
                if (this.isSelectMode) {
                    return '79.9px';
                }
                return '';
            },
            isShowRegistrationCategory() {
                return this.viewDistributeConfig.Settings.schedule.isShowRegistrationCategory;
            },
        },

        watch: {
            /**
             * @desc 根据当前是否处于编辑态，绑定/解绑事件
             * <AUTHOR>
             * @date 2020/01/02 19:32:27
             * @params
             * @return
             */
            editType() {
                // 编辑态
                if (this.isEdit) {
                    this.bindListeners();
                } else {
                    this.unbindListeners();
                }
            },
        },

        created() {
            this.fetchReservationConfig();
            this.fetchData();
        },

        mounted() {},

        beforeDestroy() {
            this.unbindListeners();
        },

        methods: {
            async fetchData() {
                this.dataLoading = true;
                try {
                    const params = {
                        doctorId: this.doctorId,
                        departmentId: this.departmentId,
                        workingDate: this.workingDate,
                        forNormalRegistration: this.regist ? this.regist : 0,
                        registrationType: this.registrationType,
                    };
                    const { data } = await RegistrationAPI.fetchDoctorShiftsByDate(params);
                    if (data?.registrationCategoryScheduleIntervals) {
                        // 过滤出普通/专家门诊排班的数据
                        data.registrationCategoryScheduleIntervals = data.registrationCategoryScheduleIntervals.filter(
                            (item) => item.registrationCategory !== RegistrationCategory.CONVENIENCE,
                        ).map((scheduleIntervalItem) => {
                            scheduleIntervalItem?.scheduleIntervals?.forEach((item) => {
                                item.list.forEach((m) => {
                                    m.checked = false;
                                });
                            });

                            return scheduleIntervalItem;
                        });
                    }

                    this.data = data;

                } catch (e) {
                    console.error('fetchData', e);
                }
                this.dataLoading = false;
            },

            /**
             * @desc 获取预约设置
             * <AUTHOR>
             * @date 2020/01/04 14:26:51
             * @params
             * @return
             */
            async fetchReservationConfig() {
                if (this.viewDistributeConfig.Settings.reservation.fetchReservationNotFromAPI || this.isEnableRegUpgrade) {
                    this.reservationConfig = this.registrationType === 0 ? this.registrationsConfig : this.therapyReservationConfig;
                } else {
                    try {
                        await this.$store.dispatch('initAppointmentConfig');
                        this.reservationConfig = Clone(this.appointmentConfig);
                    } catch (error) {
                        console.log('fetchReservationConfig error', error);
                    }
                }
            },

            async updateOccupyNumber(list) {
                let res = null;
                try {
                    const workTime = this.workingDate;
                    const { data } = await RegistrationAPI.updateOccupyNumber(
                        this.doctorId,
                        this.departmentId,
                        workTime,
                        list,
                        this.registrationType,
                    );
                    if (data) {
                        res = data;
                    }
                } catch (e) {
                    console.error('updateOccupyNumber', e);
                }
                return res;
            },

            handleDialogOpen() {},

            /**
             * @desc 进入编辑态
             * <AUTHOR>
             * @date 2020/01/02 21:09:54
             * @params 编辑类型 @EDIT_TYPE
             * @return
             */
            handleEditClick(editType) {
                this.editData = Clone(this.data);
                this.editType = editType;
            },

            /**
             * @desc 点击保存
             * <AUTHOR>
             * @date 2020/01/02 22:19:53
             * @params
             * @return
             */
            async handleSaveClick() {
                this.buttonLoading = true;
                const postList = [];
                this.editData.registrationCategoryScheduleIntervals.forEach((scheduleIntervalItem) => {
                    scheduleIntervalItem.scheduleIntervals?.forEach((item) => {
                        // 签到取号模式且自定义时间预约取大时段
                        if (this.isGenerateOrderNoOnSign && this.customizeReservationVisible) {
                            item.list.forEach((o) => {
                                const {
                                    start, end,
                                } = item;

                                postList.push({
                                    orderNo: '',
                                    type: o.type,
                                    end,
                                    start,
                                    registrationCategory: scheduleIntervalItem.registrationCategory,
                                });
                            });
                        } else {
                            item.list.forEach((o) => {
                                const {
                                    orderNo, type, end, start,
                                } = o;

                                postList.push({
                                    orderNo: !this.isGenerateOrderNoOnSign ? orderNo : '',
                                    type,
                                    end,
                                    start,
                                    registrationCategory: scheduleIntervalItem.registrationCategory,
                                });
                            });
                        }
                    });
                });

                const res = await this.updateOccupyNumber(postList);

                if (res) {
                    this.data.registrationCategoryScheduleIntervals = res.registrationCategoryScheduleIntervals.filter(
                        (item) => item.registrationCategory !== RegistrationCategory.CONVENIENCE,
                    ).map((scheduleIntervalItem) => {
                        scheduleIntervalItem?.scheduleIntervals?.forEach((item) => {
                            item.list.forEach((m) => {
                                m.checked = false;
                            });
                        });

                        return scheduleIntervalItem;
                    });
                }
                this.editType = EDIT_TYPE.NONE;
                this.editData = null;
                this.buttonLoading = false;
            },

            /**
             * @desc 取消编辑态
             * <AUTHOR>
             * @date 2020/01/02 21:09:44
             * @params
             * @return
             */
            handleCancelClick() {
                this.editData = {};
                this.editType = EDIT_TYPE.NONE;
            },

            handleCloseClick() {
                this.showDialog = false;
                this.$emit('close');
            },

            /**
             * @desc 点击每个选项
             * <AUTHOR>
             * @date 2020/01/02 21:09:34
             * @params
             * @return
             */
            handleOptionClick(item) {
                if (this.isSelected(item)) {
                    this.selectItem(item, false);
                } else {
                    this.selectItem(item, true);
                }
            },

            /**
             * @desc 编辑模式，选择 item
             * <AUTHOR>
             * @date 2020/01/04 13:18:23
             * @params
             * @return
             */
            selectItem(item, selected) {
                // 处于非编辑态，无法修改
                if (!this.isEdit) {
                    return;
                }
                // 处于 disabled，无法修改
                if (this.isDisabled(item)) {
                    return;
                }
                if (selected) {
                    this.$set(item, 'type', this.editType);
                } else {
                    this.$set(item, 'type', EDIT_TYPE.NONE);
                }
            },

            /**
             * @desc 选择模式，才响应
             * <AUTHOR>
             * @date 2020/01/04 13:18:45
             * @params
             * @return
             */
            handleItemCheck(item, start, end) {
                if (!this.isSelectMode || this.isDisabled(item)) {
                    return;
                }
                this.currentChecked = item;

                this.currentCheckPeriod = {
                    start,
                    end,
                };
            },

            /**
             * @desc 选择模式，确认操作
             * <AUTHOR>
             * @date 2020/01/04 13:20:20
             * @params
             * @return
             */
            handleSelectConfirm() {
                if (!this.isSelectMode) {
                    return;
                }

                if (this.currentChecked) {
                    this.$emit('select', {
                        reserveTime: {
                            start: this.currentChecked.start,
                            end: this.currentChecked.end,
                        },

                        orderNo: this.currentChecked.orderNo,
                        period: this.reservationConfig?.serviceType === 1 ? {
                            start: this.currentChecked.start,
                            end: this.currentChecked.end,
                        } : this.currentCheckPeriod,
                        timeOfDay: this.currentChecked.timeOfDay,
                    });
                }

                this.showDialog = false;

                console.log('checked');
            },

            /**
             * @desc 判断 item 是否禁用：
             * 1. available === 0
             * 2. 进入会员预留号编辑态，但 type 是 现场预留号
             * 3. 进入现场预留号编辑态，但 type 是 会员预留号
             * <AUTHOR>
             * @date 2020/01/03 13:40:34
             * @params
             * @return
             */
            isDisabled(item) {
                if (!item.available) {
                    return true;
                }
                if (this.editType === EDIT_TYPE.MEMBER) {
                    return item.type === EDIT_TYPE.LOCALE;
                }
                if (this.editType === EDIT_TYPE.LOCALE) {
                    return item.type === EDIT_TYPE.MEMBER;
                }
                return false;
            },

            /**
             * @desc 判断是否选中：
             * item 的 type 和 当前的 editType 一致
             * <AUTHOR>
             * @date 2020/01/03 13:48:13
             * @params
             * @return
             */
            isSelected(item) {
                if (!this.isEdit) {
                    return false;
                }
                return item.type === this.editType;
            },

            /**
             * @desc 选择模式，是否选中
             * <AUTHOR>
             * @date 2020/01/04 13:14:16
             * @params
             * @return
             */
            isChecked(item) {
                // 非选择模式，直接返回
                if (!this.isSelectMode) {
                    return false;
                }
                if (this.currentChecked === null) {
                    return false;
                }
                return item === this.currentChecked;
            },

            bindListeners() {
                // const selectWrap = this.$refs[ 'selectWrap' ];
                const selectWrap = document.querySelector('.occupy-number-dialog-wrapper');

                on(selectWrap, 'mousedown', this.onMouseDown);
                on(selectWrap, 'mousemove', this.onMouseMove);
                on(selectWrap, 'mouseup', this.onMouseUp);
            },

            unbindListeners() {
                // const selectWrap = this.$refs[ 'selectWrap' ];
                const selectWrap = document.querySelector('.occupy-number-dialog-wrapper');
                off(selectWrap, 'mousedown', this.onMouseDown);
                off(selectWrap, 'mousemove', this.onMouseMove);
                off(selectWrap, 'mouseup', this.onMouseUp);
            },

            /** *****************************移动处理开始*******************************/
            onMouseDown(e) {
                // const { selectionArea } = this.$refs;
                const selectionArea = document.querySelector('.occupy-number-dialog-wrapper .abc-dialog-body');

                // 按键位置在选择区域外，不响应
                console.log('mouse-x:', e.clientX);
                console.log('mouse-y:', e.clientY);
                console.log('wrapper:', selectionArea.getBoundingClientRect());
                if (this.isPointerOutOfRect(e.clientX, e.clientY, selectionArea.getBoundingClientRect())) {
                    return;
                }

                e.preventDefault();
                this.isSelectActive = true;
                const { selectionRect } = this.$refs;
                selectionRect.style.display = 'block';

                this.selectBegin.x = this.selectEnd.x = e.clientX;
                this.selectBegin.y = this.selectEnd.y = e.clientY;
                this.selectBegin.time = new Date().getTime();
                this.setSelectionRectPosition(e);
            },

            onMouseMove(e) {
                // console.log('onMouseMove', e);
                if (!this.isSelectActive) {
                    return;
                }

                const { clientX } = e;
                const { clientY } = e;

                // const selectWrap = this.$refs[ 'selectWrap' ];
                const selectWrap = document.querySelector('.occupy-number-dialog-wrapper');
                const wrapRect = selectWrap.getBoundingClientRect();

                if (this.isPointerOutOfRect(clientX, clientY, wrapRect)) {
                    this.onMouseUp(e);
                }

                this.selectEnd.x = clientX;
                this.selectEnd.y = clientY;
                this.selectEnd.time = new Date().getTime();
                this.setSelectionRectPosition(e);
                // console.log( 'onMouseMove', e.clientX, e.clientY );
            },

            onMouseUp(e) {
                if (!this.isSelectActive) {
                    return;
                }
                e.preventDefault();
                this.selectEnd.x = e.clientX;
                this.selectEnd.y = e.clientY;
                this.selectEnd.time = new Date().getTime();

                // 判断是否在选中范围内
                const { selectionRect } = this.$refs;
                const selectionBounding = selectionRect.getBoundingClientRect();

                // const isClick = (this.selectEnd.time - this.selectBegin.time) <= 200;

                // 判断当前是否是点击事件，当 x 和 y 移动距离都 < 2 时，才算作点击
                const isClick = selectionBounding.width < 2 && selectionBounding.height < 2;

                const items = this.$refs.selectOption;

                items?.forEach((item, index) => {
                    const itemBounding = item.getBoundingClientRect();
                    const isOverlay = this.isIntersect(selectionBounding, itemBounding);
                    if (isOverlay) {
                        let loc = 0;
                        if (this.customizeReservationVisible) {
                            this.cGroupList.forEach((item) => {
                                item.value.forEach((it) => {
                                    it.list.forEach((m) => {
                                        if (loc === index) {
                                            if (isClick) {
                                                this.handleOptionClick(m);
                                            } else {
                                                this.selectItem(m, true);
                                            }
                                        }
                                        loc++;
                                    });
                                });
                            });
                        } else {
                            this.editData.registrationCategoryScheduleIntervals.forEach((scheduleIntervalItem) => {
                                scheduleIntervalItem?.scheduleIntervals?.forEach((o) => {
                                    o.list.forEach((m) => {
                                        if (loc === index) {
                                            if (isClick) {
                                                this.handleOptionClick(m);
                                            } else {
                                                this.selectItem(m, true);
                                            }
                                        }
                                        loc++;
                                    });
                                });
                            });
                        }
                    }
                });

                this.isSelectActive = false;
                this.selectBegin.x = 0;
                this.selectBegin.y = 0;
                this.selectBegin.time = 0;
                this.selectEnd.x = 0;
                this.selectEnd.y = 0;
                this.selectEnd.time = 0;
                selectionRect.style.display = 'none';
                // console.log( 'onMouseUp', e.clientX, e.clientY );
            },

            /**
             * @desc 判断两个矩形是否相交
             * <AUTHOR>
             * @date 2020/01/02 15:48:53
             * @params
             * @return
             */
            isIntersect(r1, r2) {
                return !(r1.left > r2.right || r1.top > r2.bottom || r2.left > r1.right || r2.top > r1.bottom);
            },

            /**
             * @desc 判断坐标点是否在矩形外
             * <AUTHOR>
             * @date 2020/01/03 11:10:11
             * @params
             * @return
             */
            isPointerOutOfRect(clientX, clientY, rect) {
                return (
                    clientX + 2 > rect.right ||
                    clientX - 2 < rect.left ||
                    clientY + 2 > rect.bottom ||
                    clientY - 2 < rect.top
                );
            },

            setSelectionRectPosition() {
                // 矩形选择框可操作的区域
                const { selectionArea } = this.$refs;
                // const selectWrap = document.querySelector('.occupy-number-dialog-wrapper');
                const { selectionRect } = this.$refs;

                const wrapperRect = selectionArea.getBoundingClientRect();
                // console.log( 'setSelectionRectPosition', wrapperRect );

                let width = this.selectEnd.x - this.selectBegin.x;
                let height = this.selectEnd.y - this.selectBegin.y;

                let left = this.selectBegin.x - wrapperRect.x;
                let top = this.selectBegin.y - wrapperRect.y;
                if (width < 0) {
                    left = left + width;
                }
                if (height < 0) {
                    top = top + height;
                }

                const scrollWrap = document.querySelector('.abc-dialog-body');
                console.log('onMouseMove', scrollWrap.scrollHeight, scrollWrap.clientHeight);

                if (scrollWrap.scrollHeight > scrollWrap.clientHeight) {
                    // 有滚动条，超出滚动区域部分，
                    const overflowBottomY = this.selectEnd.y - scrollWrap.getBoundingClientRect().bottom;
                    const overflowTopY = scrollWrap.getBoundingClientRect().top - this.selectEnd.y;
                    if (overflowBottomY > 0) {
                        // 需要向下滚动
                        scrollWrap.scrollTop = overflowBottomY;
                    } else if (overflowTopY > 0) {
                        // TODO 需要向上滚动
                        scrollWrap.scrollTop = scrollWrap.scrollTop - overflowTopY;
                    }
                }

                if (left + width > wrapperRect.width) {
                    width = wrapperRect.width - left;
                }
                if (top + height > wrapperRect.height) {
                    height = wrapperRect.height - top;
                }

                if (!selectionRect) {
                    return;
                }

                selectionRect.style.width = `${Math.abs(width)}px`;
                selectionRect.style.height = `${Math.abs(height)}px`;
                selectionRect.style.top = `${top}px`;
                selectionRect.style.left = `${left}px`;
            },
            /** *****************************移动处理结束*******************************/
        },
    };
</script>

<style lang="scss">
    @import './_occupy-number-dialog.scss';
</style>
