<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        size="medium"
        :show-footer="false"
    >
        <abc-layout>
            <abc-layout-header style="text-align: center;">
                <abc-icon icon="s-alert-fill1" color="#ff9933" size="40"></abc-icon>
            </abc-layout-header>
            <abc-layout-content>
                <abc-section v-if="title">
                    <abc-flex justify="center">
                        <abc-text bold>
                            {{ title }}
                        </abc-text>
                    </abc-flex>
                </abc-section>
                <abc-section v-for="(item, index) in curContents" :key="index">
                    <abc-flex justify="center">
                        <abc-text size="small" style="text-align: center;">
                            {{ item }}
                        </abc-text>
                    </abc-flex>
                </abc-section>
            </abc-layout-content>
            <abc-layout-footer>
                <abc-flex justify="center">
                    <abc-button @click="handleConfirm">
                        {{ confirmText }}
                    </abc-button>
                    <abc-button v-if="showCancel" variant="text" @click="visible = false">
                        {{ cancelText }}
                    </abc-button>
                </abc-flex>
            </abc-layout-footer>
        </abc-layout>
    </abc-modal>
</template>

<script>
    export default {
        props: {
            title: {
                type: String,
                default: '',
            },
            contents: {
                type: [String, Array],
                default: '',
            },
            onConfirm: Function,
            confirmText: {
                type: String,
                default: '确定',
            },
            cancelText: {
                type: String,
                default: '我知道了',
            },
            showCancel: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                visible: false,
            };
        },
        computed: {
            curContents() {
                if (typeof this.contents === 'string') return [this.contents];
                return this.contents || [];
            },
        },
        watch: {
            visible(newVal) {
                if (!newVal) {
                    this.destroyElement();
                }
            },
        },
        methods: {
            destroyElement() {
                this.$destroy();
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            handleConfirm() {
                this.onConfirm && this.onConfirm();
                this.visible = false;
            },
        },
    };
</script>
