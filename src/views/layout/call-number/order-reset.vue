<template>
    <div ref="origin" class="layout-module__call-number__order-reset">
        <slot></slot>
        <div
            v-show="showPopper"
            ref="popper"
            class="call-order-reset-box"
            :class="{ 'left-4': left4 }"
            @mouseenter="$emit('mouseenter')"
            @mouseleave="$emit('mouseleave')"
        >
            <abc-button type="text" :loading="loading" @click="$emit('reorder')">
                重新排队
            </abc-button>
        </div>
    </div>
</template>

<script>
    import Popper from 'utils/vue-popper';
    export default {
        name: 'OrderReset',
        mixins: [Popper],
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            loading: {
                type: Boolean,
                default: false,
            },
            left4: {
                type: Boolean,
                default: false,
            },
        },
        watch: {
            value(newValue) {
                this.showPopper = newValue;
            },
        },
        mounted() {
            this.referenceElm = this.$refs.origin;
            this.popperElm = this.$refs.popper;
            this.createPopper();
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .call-order-reset-box {
        z-index: 2002;
        width: 72px;
        height: 34px;
        background: #ffffff;
        border: 1px solid #b7b9c2;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

        @include flex(row, center, center);

        > button {
            height: 100%;
            font-size: 12px;
            line-height: 100%;
        }

        &.left-4 {
            left: -4px !important;
        }

        ::before {
            position: absolute;
            top: 12px;
            left: -5px;
            width: 8px;
            height: 8px;
            content: '';
            background: #ffffff;
            border: 1px solid #b7b9c2;
            border-top: 0;
            border-right: 0;
            border-radius: 1;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }
    }
</style>
