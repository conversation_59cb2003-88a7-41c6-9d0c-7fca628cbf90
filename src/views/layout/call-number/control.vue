<template>
    <div v-abc-loading="listLoading" class="call-number-control">
        <div v-if="showSidebarList" class="doctor-list">
            <item-doctor
                v-for="(item, index) in schedules"
                :key="index"
                :info="item"
                :active="selected && selected.doctorId === item.doctorId && selected.departmentId === item.departmentId"
                @click.native="onClickCheckoutDoctor(item)"
            ></item-doctor>
            <div v-if="!listLoading && schedules.length === 0" class="no-data">
                暂无排班
            </div>
        </div>
        <div class="call-control">
            <div
                v-show="!ordering"
                v-abc-loading="!listLoading && infoLoading"
                :class="{
                    'ctrl-box': true,
                    'had-list': showSidebarList,
                    open: showSidebarList || open,
                }"
            >
                <div class="top-box">
                    <template v-if="doctorInfo">
                        <img class="headimg" :src="doctorInfo.doctorHeadUrl" alt="" />
                        <span class="name">{{ doctorInfo.doctorName }}</span>
                        <span class="stanch"></span>
                    </template>
                    <span v-else>{{ showAllDoctor ? '暂无医生' : '暂无排班' }}</span>
                    <abc-delete-icon
                        class="close-box"
                        variant="outline-square"
                        theme="dark"
                        size="large"
                        @delete="$emit('close')"
                    ></abc-delete-icon>
                </div>
                <div class="current-box">
                    <horn-images v-if="calling"></horn-images>
                    <div v-if="calling" class="horn-imgs"></div>
                    <div v-if="hasNoConsultingRoom" class="no-consult">
                        <span class="iconfont cis-icon-jinggao1"></span>
                        <span>未设置坐诊诊室，无法叫号</span>
                    </div>
                    <template v-else>
                        <div class="cur-alert">
                            当前就诊
                        </div>
                        <div class="cur-conte">
                            {{ showCurrentName }}
                        </div>
                    </template>
                    <div class="btns-box">
                        <div :class="{ disabled: calling || !callingItemId }" @click="onClickRecall">
                            重呼
                        </div>
                        <div :class="{ disabled: passing || !callingItemId }" @click="onClickPassNo">
                            过号
                        </div>
                        <div
                            v-if="showFinishBtn"
                            :class="{ disabled: finishing || !callingItemId }"
                            @click="onClickFinish"
                        >
                            完诊
                        </div>
                    </div>
                </div>
                <div class="next-patient">
                    <abc-button
                        size="large"
                        class="next-btn"
                        style="font-weight: 500;"
                        :disabled="nextBtnDisabled || loadingNextBtn"
                        @click="onClickCallNext"
                    >
                        呼叫下一位
                    </abc-button>
                    <span
                        v-if="callNextItem"
                        class="name"
                        :class="{
                            disabled: nextBtnDisabled || loadingNextBtn,
                        }"
                    >{{ callNextItem.name }}</span>
                </div>
                <div v-show="open" class="list-box">
                    <div class="nav">
                        <div
                            :class="{
                                item: true, active: curTab === 1
                            }"
                            @click="curTab = 1"
                        >
                            候诊 {{ selected ? selected.waitingPatients.length : 0 }}
                        </div>
                        <div
                            :class="{
                                item: true, active: curTab === 2
                            }"
                            @click="curTab = 2"
                        >
                            已完成 {{ selected ? selected.diagnosedPatients.length : 0 }}
                        </div>
                        <div class="trach"></div>
                        <div class="ordering">
                            <abc-tooltip-info
                                placement="top-start"
                                :z-index="10000"
                                custom-popper-class="call-order-popper"
                            >
                                <div>
                                    <div>叫号顺序：默认按号数依次叫号。可手动调整叫号顺序</div>
                                    <div v-if="isOpenLatePunish" style="margin-top: 12px;">
                                        {{
                                            `预约迟到惩罚：超过${
                                                callConfig.latePunishInfo.delayMode === 1 ? '预计就诊开始时间' : '预计就诊结束时间'
                                            }${callConfig.latePunishInfo.lateMinutes}${
                                                callConfig.latePunishInfo.lateHours === 1 ? '小时' : '分钟'
                                            }仍未签到，`
                                        }}
                                        <template v-if="serviceType === 1">
                                            {{ `需等待${callConfig.latePunishInfo.delayNo}人完诊才可就诊` }}
                                        </template>
                                        <template v-else>
                                            {{ `叫号顺序延后至下一时段队尾 ` }}
                                        </template>
                                    </div>
                                </div>
                            </abc-tooltip-info>
                            <span :class="{ disabled: !canOrdering }" @click="onClickOrdering">调序</span>
                        </div>
                    </div>
                    <div class="patient-box">
                        <template v-if="showPatientList.length !== 0">
                            <order-reset
                                v-for="patient in showPatientList"
                                :key="patient.id"
                                :value="patient.isPassNumber && patient.id === mouseoverId"
                                :loading="loadingReorder"
                                placement="right"
                                :left4="!isScroll"
                                :class="{ 'bg-animation': continueDiagnosedPatient(patient) }"
                                @mouseenter="onMouseenter(patient.id)"
                                @mouseleave="onMouseleave(patient.id)"
                                @reorder="onClickReorder(patient.id)"
                            >
                                <abc-popover
                                    trigger="hover"
                                    placement="right"
                                    z-index="2021"
                                    :disabled="curTab === 2 || patient.isPassNumber && patient.id === mouseoverId"
                                    theme="yellow"
                                    :open-delay="300"
                                >
                                    <div
                                        slot="reference"
                                        :class="{
                                            item: true,
                                            await: patient.status === 1,
                                            disabled: patient.isCanBeAdjust === 0,
                                            'pr-no': showPatientList.length > 5,
                                        }"
                                        @mouseenter="onMouseenter(patient.id)"
                                        @mouseleave="onMouseleave(patient.id)"
                                    >
                                        <span v-if="isFixOrderMode" class="num">{{ patient.showOrderNo }}</span>
                                        <span v-else class="num">{{ patient.showTime }}</span>
                                        <span class="zaw">
                                            <span v-if="patient.showLabel" class="tag">
                                                {{ patient.showLabel }}
                                            </span>
                                        </span>
                                        <span class="name">
                                            {{ patient.name }}
                                            <abc-tag-v2
                                                v-if="patient.registrationCategory === RegistrationCategory.CONVENIENCE"
                                                style="margin-left: 8px;"
                                                variant="outline"
                                                theme="success"
                                                shape="square"
                                                size="mini"
                                                use-first-letter
                                            >便民门诊</abc-tag-v2>
                                        </span>

                                        <span class="status" :style="patient.showStyles">{{ patient.showStatus }}</span>
                                    </div>
                                    <div :key="mouseoverId">
                                        <div v-if="!canBeAdjust(patient) && !isLate(patient)">
                                            锁屏患者，不可调整顺序
                                        </div>
                                        <template v-else>
                                            <div>
                                                就诊时间：{{ patient.reserveStart }} ~ {{ patient.reserveEnd }}
                                            </div>
                                            <div>
                                                签到时间：{{ patient.signInTime | parseTime('h:i:s') }}
                                            </div>
                                            <div>
                                                {{ getWaitingTimeStr(patient.signInTime) }}
                                                <span v-if="isLate(patient)">
                                                    预计还需等待 {{ patient.leftLatePunishCount }} 人完诊后才可回归候诊队列
                                                </span>
                                            </div>
                                        </template>
                                    </div>
                                </abc-popover>
                            </order-reset>
                        </template>
                        <div v-else class="no-data">
                            暂无患者
                        </div>
                    </div>
                </div>
                <!-- 医生的遥控器暂时展开患者列表 -->
                <div v-if="!showSidebarList" class="open-btn" @click="open = !open"></div>
            </div>
            <template v-if="ordering">
                <ordering
                    :is-fix-order-mode="isFixOrderMode"
                    :selected="selected"
                    :get-waiting-time-str="getWaitingTimeStr"
                    @success="ordering = false"
                    @cancel="ordering = false"
                    @refresh="handleRefresh"
                ></ordering>
            </template>
        </div>
    </div>
</template>

<script>
    import ItemDoctor from './item-doctor.vue';
    import OrderReset from './order-reset.vue';
    import Ordering from './ordering.vue';
    import MixinSocket from './mixin-socket.js';
    import CallingAPI from 'api/call.js';
    import DEFAULT_HEADIMG from 'assets/images/user-w.png';
    import HornImages from '../horn-images.vue';
    import {
        mapGetters, mapState, mapActions,
    } from 'vuex';
    import { getTargetRemark } from 'assets/configure/call.js';
    import { RESERVATION_MODE_TYPE } from 'views/settings/registered-reservation/constant';
    import API from 'api/settings';
    import { RegistrationCategory } from '@/views-hospital/registered-fee/constant';
    export default {
        name: 'CallControl',
        components: {
            ItemDoctor,
            OrderReset,
            Ordering,
            HornImages,
        },
        mixins: [MixinSocket],
        props: {
            // 门诊传，挂号不传
            doctorId: {
                type: String,
                default: '',
            },
            // 上次选中缓存
            cacheSelected: {
                type: Object,
                default: null,
            },
            // 排班列表展示全部医生
            showAllDoctor: Boolean,
            // 显示左侧列表
            showSidebarList: Boolean,
            continueDiagnosedInfo: {
                type: Object,
                default: null,
            },
        },
        data() {
            return {
                RegistrationCategory,
                office: 1,
                open: true,
                curTab: 1,
                ordering: false,
                passing: false, // 当前正在过号
                finishing: false, // 当前正在完诊
                loadingNextBtn: false,
                loadingReorder: false,
                mouseoverId: '',
                timer: null,
                schedules: [],
                selected: {
                    doctorId: '',
                    departmentId: '',
                    consultingRoomId: '',
                    currentPatient: null,
                    waitingPatients: [],
                    diagnosedPatients: [],
                },
                listLoading: false,
                infoLoading: false,

                loading: null, // loading延迟关闭的回调函数，有值时，不能再次调用
                serviceType: 0, // 0：按上午/下午/晚上预约；1：按精确时间段预约；2：按自定义时间段预约
                borderAnimation: false,
                callingNextTips: false, // 下次不再提示
            };
        },
        computed: {
            ...mapState('call', ['callingId']),
            ...mapGetters(['callConfig','registrationsConfig','appointmentConfig','isEnableRegUpgrade', 'isCallingNextTips']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            /**
             * desc [展示的当前就诊号数+姓名]
             */
            showCurrentName() {
                if (this.selected && this.selected.currentPatient) {
                    const {
                        timeOfDay, orderNo,isAdditional, name,
                        reserveStart,
                    } = this.selected.currentPatient;
                    let countIcon = '';
                    let orderNoAfter = '';
                    if (isAdditional) {
                        countIcon = '+';
                        orderNoAfter = orderNo;
                    } else {
                        orderNoAfter = this.addZero(orderNo);
                    }
                    if (this.isFixOrderMode) {
                        return `${timeOfDay}${countIcon}${orderNoAfter} ${name}`;
                    }
                    return `${reserveStart} ${name}`;
                }
                return '暂无';
            },
            /**
             * desc [当前呼叫单id]
             */
            callingItemId() {
                return this.selected && !!this.selected.currentPatient && this.selected.currentPatient.id;
            },
            /**
             * desc [当前正在叫号（播报）]
             * true  => 显示喇叭
             * false => 不显示
             */
            calling() {
                return this.callingItemId === this.callingId;
            },
            /**
             * desc [可呼叫的下一位]
             */
            callNextItem() {
                return this.selected && !!this.selected.waitingPatients.length && this.selected.waitingPatients[0];
            },
            /**
             * desc [是否可呼叫下一位]
             * true  => 不可
             * fasle => 可以
             */
            nextBtnDisabled() {
                return !this.callNextItem || !!this.selected.currentPatient;
            },
            isFixOrderMode() {
                // modeType 0: 固定号源模式 1: 灵活时间模式
                return this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            /**
             * desc [显示患者列表]
             * curTab === 1 => 显示候诊
             * curTab === 2 => 显示完诊
             */
            showPatientList() {
                const {
                    waitingPatients = [], diagnosedPatients = [],
                } = this.selected || {};
                const list = this.curTab === 1 ? waitingPatients : diagnosedPatients;
                return list.map((item) => {
                    const {
                        timeOfDay,isAdditional, orderNo,reserveStart,
                    } = item;
                    let countIcon = '';
                    let orderNoAfter = '';
                    if (isAdditional) {
                        countIcon = '+';
                        orderNoAfter = orderNo;
                    } else {
                        orderNoAfter = this.addZero(orderNo);
                    }
                    if (this.isFixOrderMode) {
                        item.showOrderNo = `${timeOfDay}${countIcon}${orderNoAfter}号`;
                    } else {
                        item.showTime = reserveStart;
                    }
                    const {
                        showStyles, showStatus,
                    } = this.getStatus(item);
                    item.showStyles = showStyles;
                    item.showStatus = showStatus;
                    item.showLabel = getTargetRemark(item);
                    item.isPassNumber = item.showStatus === '过号'; // 是否过号
                    return item;
                });
            },
            // 是否滑动
            isScroll() {
                return this.showPatientList.length > 5;
            },
            /**
             * desc [医生信息：头像、姓名]
             */
            doctorInfo() {
                const target = this.schedules.find(
                    (item) =>
                        item.doctorId === this.selected.doctorId && item.departmentId === this.selected.departmentId,
                );
                if (target && !target.doctorHeadUrl) {
                    target.doctorHeadUrl = DEFAULT_HEADIMG;
                }
                return target;
            },
            /**
             * desc [未设置诊室]
             */
            hasNoConsultingRoom() {
                return (
                    this.doctorInfo &&
                    this.doctorInfo.doctorId &&
                    this.doctorInfo.departmentId &&
                    !this.doctorInfo.consultingRoomId
                );
            },
            /**
             * desc [是否开启迟到惩罚]
             * true  => 开启
             * false => 关闭
             */
            isOpenLatePunish() {
                return this.callConfig && this.callConfig.enableLatePunish === 1;
            },

            // 病历未填完时可呼叫下一位
            showFinishBtn() {
                return this.callConfig?.enableNext === 1;
            },
            /**
             * desc [是否可以点击调序 -当等待人数为零时，不允许点击调序]
             * true  => 可点击
             * false => 不可点击
             */
            canOrdering() {
                return this.selected && this.selected.waitingPatients.length !== 0;
            },
        },
        watch: {
            selected(newValue,oldValue) {
                // 避免一个医生多个科室，未选择对应科室下的医生
                if (newValue?.doctorId === oldValue?.doctorId && newValue?.departmentId === oldValue?.departmentId) return;
                this.$emit('save-cache', newValue);
            },
            doctorId() {
                this.fetchDoctorSchedules();
            },
        },
        async created() {
            this.open = this.showSidebarList;
            await Promise.all([
                this.fetchDoctorSchedules(),
                this.fetchReservation(),
                this.$store.dispatch('initCallingNextTips'),
            ]);

            this.$nextTick(() => {
                const el = document.querySelector('.bg-animation');
                if (el) {
                    el.scrollIntoView({
                        block: 'nearest', behavior: 'smooth' , inline: 'nearest',
                    });
                }
            });
            this.callingNextTips = this.isCallingNextTips;
        },
        mounted() {
            if (!this._escPressEvent) {
                this._escPressEvent = (e) => {
                    const ESC_KEYCODE = 27; // esc keycode
                    if (e.keyCode === ESC_KEYCODE) {
                        this.$emit('close');
                    }
                };
            }
            window.addEventListener('keydown', this._escPressEvent, false);
        },
        beforeDestroy() {
            window.removeEventListener('keydown', this._escPressEvent, false);
        },
        methods: {
            ...mapActions('call', ['delCurrentCalling']),
            /**
             * desc [拉取医生的排班信息]
             * 1、有doctorId时，拉取知道医生的排班列表
             * 2、没有时，拉取全部医生排班列表
             */
            async fetchDoctorSchedules() {
                this.listLoading = true;
                try {
                    const doctorId = this.showAllDoctor ? '' : this.doctorId;
                    const scene = this.$route.path.indexOf('outpatient') > -1 ? 'outpatient' : '';
                    const { data } = await CallingAPI.callSchedules(doctorId, scene); // 门诊处如果是医助，需要获取助理医生范围
                    if (data.schedules && data.schedules.length !== 0) {
                        this.schedules = data.schedules;

                        if (this.cacheSelected) {
                            // 有选择历史时，优选
                            const {
                                doctorId, departmentId,
                            } = this.cacheSelected;
                            const exit = this.schedules.find((item) => item.doctorId === doctorId && item.departmentId === departmentId);
                            if (exit) {
                                this.setCurrentSelected({
                                    doctorId: exit.doctorId,
                                    departmentId: exit.departmentId,
                                    consultingRoomId: exit.consultingRoomId,
                                });
                            }
                        }
                        if (!this.selected || !this.selected.doctorId) {
                            this.setCurrentSelected(this.schedules[0]);
                        }

                        await this.fetchCallList();
                    } else {
                        this.schedules = [];
                        this.setCurrentSelected();
                    }
                } catch (error) {
                    console.log('fetchDoctorSchedules error', error);
                }
                this.listLoading = false;
            },
            async fetchReservation() {
                if (this.viewDistributeConfig.Settings.reservation.fetchReservationNotFromAPI || this.isEnableRegUpgrade) {
                    this.serviceType = this.registrationsConfig.serviceType;
                } else {
                    try {
                        await this.$store.dispatch('initAppointmentConfig');
                        this.serviceType = this.appointmentConfig.serviceType;
                    } catch (e) {
                        console.log('fetchReservation error', e);
                    }
                }
            },
            /**
             * desc [拉取叫号排队数据]
             */
            async fetchCallList() {
                if (this.infoLoading || this.loading) return;
                const {
                    doctorId, departmentId,
                } = this.doctorInfo || {};
                if (doctorId && departmentId) {
                    this.loading = this.startLoading('infoLoading');
                    try {
                        const { data } = await CallingAPI.callPatientList({
                            doctorId,
                            departmentId,
                        });
                        this.setCurrentSelected(data);
                    } catch (error) {
                        console.log('fetchCallList error', error);
                    }
                    this.loading.finish();
                    this.loading = null;
                }
            },
            handleRefresh() {
                this.ordering = false;
                this.fetchCallList();
            },
            /**
             * desc [设置当前选中：医生+科室]
             */
            setCurrentSelected(item = null) {
                if (item) {
                    const {
                        doctorId = '',
                        departmentId = '',
                        currentPatient = null,
                        waitingPatients = [],
                        diagnosedPatients = [],
                    } = item;
                    this.selected = {
                        doctorId,
                        departmentId,
                        currentPatient,
                        waitingPatients,
                        diagnosedPatients,
                    };
                } else {
                    this.selected = null;
                }
            },
            /**
             * desc [点击重呼]
             */
            async onClickRecall() {
                if (this.callingItemId && !this.calling) {
                    try {
                        await CallingAPI.recallPatient(this.callingItemId);
                        await this.fetchCallList();
                    } catch (error) {
                        console.log('onClickRecall error', error);
                    }
                }
            },
            /**
             * desc [点击过号]
             */
            async onClickPassNo() {
                if (this.callingItemId && !this.passing) {
                    this.passing = true;
                    try {
                        await CallingAPI.passPatient(this.callingItemId);
                        await this.fetchCallList();
                        this.delCurrentCalling();
                    } catch (error) {
                        console.log('onClickPassNo error', error);
                    }
                    this.passing = false;
                }
            },
            /**
             * desc [点击完诊]
             */
            async onClickFinish() {
                if (this.callingItemId && !this.finishing) {
                    this.finishing = true;
                    try {
                        await CallingAPI.diagnosedPatient(this.callingItemId);
                        await this.fetchCallList(this.selected);
                        this.delCurrentCalling();
                    } catch (error) {
                        console.log('onClickFinish error', error);
                    }
                    this.finishing = false;
                }
            },
            /**
             * desc [点击呼叫下一位]
             */
            async onClickCallNext() {
                if (this.callNextItem && !this.loadingNextBtn) {
                    this.loadingNextBtn = true;
                    if (this.selected?.currentPatient && !this.isCallingNextTips) {
                        const h = this.$createElement;
                        this.$confirm({
                            type: 'warn',
                            preset: 'alert',
                            customClass: 'call-next-tips-confirm',
                            content: h('div',{
                                style: {
                                    width: '350px',
                                },
                            }, [
                                h('div', '当前患者还未完诊，是否确认呼叫下一位？'),
                                h('div', '呼叫下一位后当前患者将进入已完成队列，无法再呼叫。'),
                            ]),
                            footerPrepend: h('div', {
                                style: {
                                    marginRight: 'auto',
                                },
                            }, [h('abc-checkbox', {
                                on: {
                                    input: (val) => {
                                        this.callingNextTips = val;
                                    },
                                },
                            }, '下次不再提示')]),
                            onConfirm: () => {
                                this.onConfirmCallNext();
                                this.updateCallingNextTips(this.callingNextTips);
                            },
                            onCancel: () => {
                                this.loadingNextBtn = false;
                            },
                        });
                    } else {
                        await this.onConfirmCallNext();
                    }
                }
            },
            async onConfirmCallNext() {
                try {
                    const currentPatientItemId = this.callingItemId || '';
                    const { data } = await CallingAPI.callPatient(this.callNextItem.id, currentPatientItemId);
                    this.$emit('active-call', data.id);
                    await this.fetchCallList();
                } catch (error) {
                    console.log('callPatient error', error);
                }
                this.loadingNextBtn = false;
            },
            async updateCallingNextTips(callingNextTips) {
                try {
                    const { data } = await API.employee.updateCallingNextTips({
                        callingNextTips: +callingNextTips,
                    });
                    if (data) {
                        await this.$store.dispatch('updateCallingNextTips', data.callingNextTips);
                    }
                } catch (e) {
                    console.log(e);
                }
            },
            /**
             * 当点击重新排序
             * <AUTHOR>
             * @date 2020-10-28
             */
            async onClickReorder(patientId) {
                if (!this.loadingReorder) {
                    this.loadingReorder = true;
                    try {
                        await CallingAPI.callReorderItem(patientId);
                        await this.fetchCallList();
                    } catch (error) {
                        console.log('callPatient error', error);
                    }
                    this.loadingReorder = false;
                }
            },
            /**
             * desc [点击切换左侧排班]
             */
            onClickCheckoutDoctor(item) {
                this.ordering = false;
                this.setCurrentSelected(item);
                this.fetchCallList();
            },
            /**
             * desc [当点击调序时]
             */
            onClickOrdering() {
                if (this.canOrdering) {
                    this.ordering = true;
                }
            },
            /**
             * desc [当移动到]
             */
            onMouseenter(patientId) {
                if (this.timer) {
                    clearTimeout(this.timer);
                    this.timer = null;
                }
                this.mouseoverId = patientId;
            },
            /**
             * desc [当移动出]
             */
            onMouseleave() {
                this.timer = setTimeout(() => {
                    this.mouseoverId = '';
                }, 50);
            },
            /**
             * desc [号数，两位数字，不足前面加零]
             */
            addZero(num) {
                return num < 10 ? `0${num}` : `${num}`;
            },
            /**
             * desc [患者状态]
             * status    => 0 普通候诊；1 等待就诊；2 正在就诊；3 完诊
             * subStatus => 0 正常；1 迟到；2 过号 3 回诊
             */
            getStatus({
                status, subStatus,
            }) {
                const arr = [['候诊', '迟到', '过号','回诊'], ['候诊', '迟到', '过号', '回诊'], '', '完诊'];
                let showStatus = arr[status];
                showStatus = Array.isArray(showStatus) ? showStatus[subStatus] : showStatus;

                let showStyles = {};
                switch (showStatus) {
                    case '迟到':
                        if (status !== 1) {
                            showStyles = { color: '#FF3333' };
                        }
                        break;
                    case '过号':
                        showStyles = { color: '#FF3333' };
                        break;
                    case '完诊':
                        showStyles = { color: '#7A8794' };
                        break;
                    case '候诊':
                        break;
                    default:
                        break;
                }

                return {
                    showStyles, showStatus,
                };
            },
            /**
             * desc [注册loading预加载]
             * 发起请求300ms内返回不出loading
             * 超过300ms出loading（并且把之前的300也加上）
             */
            startLoading(loadingKey, delay = 600) {
                const startTimeStamp = Date.now();
                let timer = setTimeout(() => {
                    this[loadingKey] = true;
                    timer = null;
                }, delay);
                return {
                    finish: () => {
                        if (timer) {
                            clearTimeout(timer);
                            timer = null;
                        } else {
                            if (Date.now() - startTimeStamp < delay * 2) {
                                const _timer = setTimeout(() => {
                                    this[loadingKey] = false;
                                    clearTimeout(_timer);
                                }, delay);
                            } else {
                                this[loadingKey] = false;
                            }
                        }
                    },
                };
            },

            isLate({ subStatus }) {
                return subStatus === 1;
            },

            canBeAdjust({ isCanBeAdjust }) {
                return isCanBeAdjust === 1;
            },

            continueDiagnosedPatient(patient) {
                const {
                    patientId: pId,
                    timeOfDay: pTimeOfDay,
                    orderNo: pOrderNo,
                } = patient || {};

                const {
                    patientId = '',
                    timeOfDay = '',
                    orderNo = '',
                } = this.continueDiagnosedInfo || {};

                return patientId === pId && timeOfDay === pTimeOfDay && orderNo === pOrderNo;
            },

            getWaitingTimeStr(givenTime) {
                if (!givenTime) return '';
                // 将给定的时间字符串转换为Date对象
                const givenDate = new Date(givenTime);
                // 获取当前时间的Date对象
                const now = new Date();
                // 计算两个日期之间的时间差，以毫秒为单位
                const timeDifference = now - givenDate;

                // 将毫秒转换为天、小时和分钟
                const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));
                let str = '';

                if (days > 0) {
                    str += `${days}天`;
                }
                if (hours > 0) {
                    str += `${hours}小时`;
                }
                if (minutes > 0) {
                    str += `${minutes}分钟`;
                }
                return str ? `已等候${str}` : '';
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .call-number-control {
        @include flex(row, flex-start, stretch);

        .doctor-list {
            width: 122px;
            max-height: 482px;
            overflow-y: auto;
            border-right: 1px solid $abcCardBorderColor;

            .no-data {
                height: 40px;
                padding: 0 12px;
                font-size: 12px;
                line-height: 40px;
                color: $T2;
                border-bottom: 1px solid $abcCardDividerColor;
            }

            .call-item-doctor:first-child {
                border-radius: var(--abc-border-radius-small) 0 0 0;
            }
        }

        .call-control {
            width: 330px;
        }

        .ctrl-box {
            position: relative;
            width: 100%;
            height: 238px;
            padding: 12px 16px 16px;
            overflow: hidden;
            transition: all 0.15s ease-out;

            .top-box {
                @include flex(row, flex-start, center);

                position: relative;
                box-sizing: border-box;
                width: 100%;
                height: 30px;
                padding-right: 34px;

                .headimg {
                    width: 28px;
                    height: 28px;
                    margin-right: 8px;
                    border-radius: var(--abc-border-radius-mini);
                    box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.25);
                }

                .name {
                    width: 158px;
                    font-size: 14px;
                    color: $T1;

                    @include ellipsis(1);
                }

                .close-box {
                    position: absolute;
                    right: 0;
                }
            }

            .current-box {
                position: relative;
                box-sizing: border-box;
                width: 100%;
                height: 106px;
                padding-bottom: 34px;
                margin-top: 14px;
                background-color: #52c78c;
                border: 1px solid #3dac74;
                border-radius: var(--abc-border-radius-small);

                @include flex(column, center, center);

                .horn-images {
                    position: absolute;
                    top: 12px;
                    left: 14px;
                }

                .no-consult {
                    span {
                        font-size: 14px;
                        color: #ffffff;
                    }

                    span.iconfont {
                        margin-right: 4px;
                    }
                }

                .btns-box {
                    position: absolute;
                    bottom: 0;
                    width: 100%;
                    height: 30px;
                    border-top: 1px solid #49bd82;

                    @include flex(row, center, stretch);

                    > div {
                        flex: 1;
                        font-size: 14px;
                        line-height: 30px;
                        color: #02763c;
                        text-align: center;
                        cursor: pointer;
                        user-select: none;
                        background-color: #59d696;
                        border-right: 1px solid #49bd82;

                        &:not(.disabled):hover {
                            background-color: #5de09d;
                        }

                        &:not(.disabled):active {
                            background-color: #52c78c;
                        }
                    }

                    > div:first-child {
                        border-radius: 0 0 0 var(--abc-border-radius-small);
                    }

                    > div:last-child {
                        border-right: 1px solid rgba(0, 0, 0, 0);
                        border-radius: 0 0 var(--abc-border-radius-small) 0;
                    }

                    > div.disabled {
                        cursor: not-allowed;
                    }
                }

                .cur-alert {
                    margin-bottom: 6px;
                    font-size: 12px;
                    color: #ffffff;
                }

                .cur-conte {
                    padding: 0 24px;
                    font-size: 20px;
                    color: #ffffff;

                    @include ellipsis(1);
                }
            }

            .next-patient {
                position: relative;
                width: 100%;
                height: 40px;
                margin-top: 8px;

                .next-btn.abc-button-large {
                    width: 100%;
                    height: 40px;
                    font-size: 14px;
                }

                .next-btn.is-disabled {
                    border-color: $P1;
                }

                .name {
                    position: absolute;
                    top: 12px;
                    right: 20px;
                    display: inline-block;
                    width: 60px;
                    height: 18px;
                    overflow: hidden;
                    font-size: 12px;
                    line-height: 18px;
                    color: #ffffff;
                    text-align: center;
                    word-break: break-all;
                }

                .name.disabled {
                    color: $T2;
                }

                .toast-alert {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 1;
                    cursor: not-allowed;
                    opacity: 0;
                }
            }

            .list-box {
                height: 240px;
                margin-top: 16px;
                overflow: hidden;
                background-color: #ffffff;
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);

                @include flex(column, flex-start, stretch);

                .nav {
                    position: relative;
                    flex-shrink: 0;
                    width: 100%;
                    height: 34px;
                    background-color: $P5;

                    @include flex(row, flex-start, stretch);

                    .item {
                        flex-shrink: 0;
                        width: 72px;
                        font-size: 12px;
                        font-weight: 500;
                        line-height: 35px;
                        color: $T2;
                        text-align: center;
                        cursor: pointer;
                        user-select: none;
                        border-right: 1px solid rgba(0, 0, 0, 0);
                        border-bottom: 1px solid $P6;
                        border-left: 1px solid rgba(0, 0, 0, 0);

                        &:hover {
                            background-color: #ffffff;
                        }

                        &:first-child {
                            border-left: 1px solid rgba(0, 0, 0, 0);
                        }
                    }

                    .active {
                        color: var(--abc-color-T1);
                        background-color: #ffffff;
                        border-right: 1px solid var(--abc-color-P6);
                        border-bottom: 1px solid rgba(0, 0, 0, 0);
                        border-left: 1px solid var(--abc-color-P6);
                    }

                    .trach {
                        flex: 1;
                        border-bottom: 1px solid var(--abc-color-P6);
                    }

                    .ordering {
                        position: absolute;
                        top: 8px;
                        right: 12px;
                        width: 50px;
                        height: 20px;

                        @include flex(row, flex-start, center);

                        span {
                            margin-left: 4px;
                            font-size: 14px;
                            color: var(--abc-color-theme1);
                            cursor: pointer;
                            user-select: none;
                        }

                        span.disabled {
                            color: var(--abc-color-T3);
                            cursor: not-allowed;
                        }
                    }
                }

                .patient-box {
                    flex: 1;
                    overflow-x: hidden;
                    overflow-y: auto;

                    @keyframes bgBlink {
                        0%,
                        100% {
                            background-color: transparent;
                        }

                        20%,
                        80% {
                            background-color: #e1f0ff;
                        }
                    }

                    .bg-animation {
                        animation-name: bgBlink;
                        animation-duration: 3s;
                        animation-timing-function: linear;
                        animation-delay: 0s;
                        animation-fill-mode: forwards;
                    }

                    > div {
                        &:not(:last-child) {
                            border-bottom: 1px solid var(--abc-color-P7);
                        }
                    }

                    .item {
                        width: 100%;
                        height: 40px;
                        padding: 0 10px 0 12px;
                        cursor: pointer;

                        @include flex(row, center, center);

                        &:hover {
                            background-color: var(--abc-color-cp-grey4);
                        }

                        .num {
                            display: inline-block;
                            width: 70px;

                            @include ellipsis(1);
                        }

                        .zaw {
                            display: inline-block;
                            width: 50px;
                        }

                        .tag {
                            display: inline-block;
                            width: 20px;
                            height: 20px;
                            font-size: 14px;
                            line-height: 18px;
                            color: $Y2;
                            text-align: center;
                            border: 1px solid $Y2;
                            border-radius: 20px;
                        }

                        .name {
                            flex: 1;

                            @include ellipsis(1);
                        }

                        .status {
                            display: inline-block;
                            width: 30px;

                            @include ellipsis(1);
                        }
                    }

                    .await {
                        > span {
                            color: $G1;
                        }
                    }

                    .disabled {
                        background-color: var(--abc-color-cp-grey2);
                    }

                    .pr-no {
                        padding-right: 0;
                    }

                    .no-data {
                        height: 40px;
                        padding: 0 12px;
                        font-size: 12px;
                        line-height: 40px;
                        color: $T2;
                        border-bottom: 1px solid $P6;
                    }
                }
            }

            .open-btn {
                position: absolute;
                right: 0;
                bottom: 0;
                left: 0;
                z-index: 10;
                width: 100%;
                height: 22px;
                cursor: pointer;
                background-image: url('~assets/images/<EMAIL>');
                background-repeat: no-repeat;
                background-position: center center;
                background-size: 14px 14px;

                @include flex(row, center, center);

                &:hover {
                    background-color: #cfcfcf;
                }
            }
        }

        .open {
            height: 490px;

            .open-btn {
                transform: rotateZ(180deg);
            }
        }

        .had-list.open {
            height: 482px;
        }
    }
</style>
