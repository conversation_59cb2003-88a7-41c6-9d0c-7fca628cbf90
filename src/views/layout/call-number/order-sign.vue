<template>
    <div ref="origin" class="layout-module__call-number__order-sign">
        <slot></slot>
        <div
            v-show="showPopper"
            ref="popper"
            class="call-remark-box"
            @mouseenter="$emit('mouseenter')"
            @mouseleave="$emit('mouseleave')"
        >
            <p>优先叫号标记，可在叫号屏上显示</p>
            <div v-for="one in remarkOptions" :key="one.label" class="one" @click="onClickTag(one)">
                <label>{{ one.label }}</label>
                <span v-if="one.tag === tag" class="iconfont cis-icon-duigou"></span>
            </div>
        </div>
    </div>
</template>

<script>
    import Popper from 'utils/vue-popper';
    import { remarkOptions } from 'assets/configure/call.js';
    export default {
        name: 'OrderSign',
        remarkOptions,
        mixins: [Popper],
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            tag: {
                type: Number,
                required: true,
            },
        },
        data() {
            return {
                remarkOptions,
            };
        },
        watch: {
            value(newValue) {
                this.showPopper = newValue;
            },
        },
        mounted() {
            this.referenceElm = this.$refs['origin'];
            this.popperElm = this.$refs['popper'];
            this.createPopper();
        },
        methods: {
            /**
             * desc [选择一个tag]
             */
            onClickTag(one) {
                if (one.tag !== this.tag) {
                    this.$emit('change', one.tag);
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';
    .layout-module__call-number__order-sign {
    }
    .call-remark-box {
        transform: translate(2px, -4px);
        width: 204px;
        background: #fff;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
        border-radius: 2px;
        border: 1px solid #b7b9c2;
        z-index: 2001;
        > p {
            color: $T3;
            font-size: 12px;
            line-height: 24px;
            text-align: center;
        }
        > div {
            height: 32px;
            padding: 0px 12px;
            @include flex(row, space-between, center);
            cursor: pointer;
            &:hover {
                background-color: $P4;
            }
            label {
                color: $T1;
                font-size: 14px;
            }
            span {
                color: $theme2;
                font-size: 14px;
            }
        }
    }
</style>
