<template>
    <div class="app-header-fixed-left__user-setting">
        <abc-popover
            ref="userSettingPopover"
            class="user-dropdown"
            custom-class="user-header-dropdown"
            popper-class="user-setting-popper user-setting__right-popper"
            :popper-style="popperStyle"
            placement="right-start"
            trigger="click"
            :width="254"
            theme="white"
            :visible-arrow="false"
            :disabled="isEnableClinicSelector ? !isEnableClinicSelector() : false"
            @close="handleClose"
            @hide="handleClose"
        >
            <div class="user-setting__clinic-wrapper" @click.stop="headerDropdownChange('switch-clinic')">
                <div class="user-setting__clinic">
                    <abc-image
                        v-if="isDarkTheme"
                        :width="16"
                        :height="16"
                        :src="IconClinic"
                    ></abc-image>
                    <abc-image
                        v-else
                        :src="IconClinic"
                        :width="16"
                        :height="16"
                    ></abc-image>

                    <div class="user-setting__title ellipsis">
                        {{ isHideClinicName ? '*******************' : isChain<PERSON>dmin ? `【总部】${clinicName}` : clinicName }}
                    </div>
                    <abc-image :src="IconSwitch" :width="16" :height="16">
                    </abc-image>
                </div>
            </div>
            <span slot="reference" class="userinfo-inner">
                <abc-popover
                    placement="right"
                    trigger="hover"
                    theme="yellow"
                    :offset="18"
                    :open-delay="3000"
                    :close-delay="1000"
                    class="user-dropdown__popover"
                >
                    <div slot="reference" class="user-dropdown__reference">
                        <abc-image
                            v-if="userInfo?.headImgUrl"
                            class="avatar-img"
                            :width="headImageWidth"
                            :height="headImageWidth"
                            :src="userInfo?.headImgUrl"
                            oss-style-name="AvatorCompress"
                        ></abc-image>
                        <abc-image
                            v-else
                            class="avatar-img"
                            :width="headImageWidth"
                            :height="headImageWidth"
                            :src="avatarImg"
                            alt=""
                        ></abc-image>

                        <div v-if="showClinicName" class="user-clinic-name">
                            {{ clinicName }}
                        </div>
                    </div>
                    <div class="popover-national-info">
                        <span>
                            人员编码: {{ userInfo?.nationalCode || '未对码' }}
                        </span>
                        <span>
                            机构编码: {{ currentClinic?.nationalCode || '未对码' }}
                        </span>
                    </div>
                </abc-popover>
            </span>
            <abc-flex vertical gap="middle">
                <abc-space direction="vertical" gap="middle" :custom-style="{ padding: '12px 0 0' }">
                    <abc-space :size="12" align="normal">
                        <label
                            for="__file__"
                            style=" position: relative; display: block; width: 56px; height: 56px;"
                            @mouseenter="showCameraIcon = true"
                            @mouseleave="showCameraIcon = false"
                        >
                            <abc-image
                                class=" __avator__ avatar-img"
                                style="border-radius: var(--abc-border-radius-mini);"
                                :width="56"
                                :height="56"
                                :src="userInfo?.headImgUrl"
                                :default-img="require('@/assets/images/<EMAIL>')"
                                oss-style-name="AvatorCompress"
                            ></abc-image>
                            <span v-if="showCameraIcon" class="user-setting-popper_avatar_hover-mask">
                                <abc-icon
                                    icon="camera"
                                    style="position: absolute; top: 50%; left: 50%; color: #ffffff; cursor: pointer; transform: translate(-50%, -50%);"
                                    size="18"
                                ></abc-icon>
                            </span>

                            <input
                                id="__file__"
                                ref="imageInput"
                                type="file"
                                style="display: none;"
                                accept="image/*"
                                @change="fileChange"
                            />
                        </label>
                        <div style="width: 144px;">
                            <!--                            <abc-popover-->
                            <!--                                placement="right"-->
                            <!--                                trigger="hover"-->
                            <!--                                theme="yellow"-->
                            <!--                            >-->
                            <!--                                <abc-text slot="reference" size="normal" bold>-->
                            <!--                                    {{ userInfo?.name }}-->
                            <!--                                </abc-text>-->
                            <!--                                <div>请联系管理员，在「管理-成员管理」中修改</div>-->
                            <!--                            </abc-popover>-->
                            <!--                            <abc-popover-->
                            <!--                                placement="right"-->
                            <!--                                trigger="hover"-->
                            <!--                                theme="yellow"-->
                            <!--                            >-->
                            <!--                                <abc-space slot="reference" :size="8">-->
                            <!--                                    <abc-text size="mini" theme="gray" style="white-space: nowrap;">-->
                            <!--                                        {{ userInfoCo.handSignType ? '手写签名' : '电脑签名' }}-->
                            <!--                                    </abc-text>-->
                            <!--                                    <abc-text v-if="!userInfoCo.handSignType">-->
                            <!--                                        {{ userInfo?.name }}-->
                            <!--                                    </abc-text>-->
                            <!--                                    <img-->
                            <!--                                        v-if="userInfoCo.handSignType && userInfoCo.handSignPicUrl"-->
                            <!--                                        style=" width: auto; height: 16px; vertical-align: text-bottom;"-->
                            <!--                                        :src="userInfoCo.handSignPicUrl"-->
                            <!--                                    />-->
                            <!--                                </abc-space>-->
                            <!--                                <div>请联系管理员，在「管理-成员管理」中修改</div>-->
                            <!--                            </abc-popover>-->
                            <abc-text size="normal" bold tag="div">
                                {{ userInfo?.name }}
                            </abc-text>
                            <abc-text size="mini" theme="gray" tag="div">
                                {{ roleListMap }}
                            </abc-text>
                        </div>
                    </abc-space>
                    <abc-divider
                        margin="none"
                        size="normal"
                        theme="light"
                        variant="solid"
                        layout="horizontal"
                        style="width: 212px;"
                    ></abc-divider>
                </abc-space>
                <abc-list
                    :data-list="dropdownList"
                    :scrollable="false"
                    style="padding: 0;"
                >
                    <template #default="{ item }">
                        <abc-popover
                            v-if="item.desc === 'handSign'"
                            ref="signPopover"
                            v-abc-click-outside="
                                () => {
                                    $refs.signPopover.doClose()
                                }
                            "
                            placement="right"
                            trigger="click"
                            width="204px"
                            popper-class="user-sign-setting-popper user-sign-setting-popper-v2"
                            :visible-arrow="false"
                            theme="white"
                        >
                            <div slot="reference" style="width: 212px;">
                                <abc-flex
                                    flex="1"
                                    justify="space-between"
                                >
                                    <abc-text>{{ userInfoCo.handSignType ? '手写签名' : '电脑签名' }}</abc-text>
                                    <abc-space size="small">
                                        <abc-text v-if="userInfoCo.handSignType === 0" theme="gray">
                                            {{ userInfo?.name }}
                                        </abc-text>
                                        <img v-if="userInfoCo.handSignType === 1" style="width: auto; height: 16px; vertical-align: text-bottom;" :src="userInfoCo.handSignPicUrl" />
                                        <abc-icon
                                            icon="n-right-line-medium"
                                            color="var(--abc-color-T3)"
                                            :size="16"
                                            style="vertical-align: text-bottom;"
                                        ></abc-icon>
                                    </abc-space>
                                </abc-flex>
                                <div
                                    v-if="showQrCode"
                                    v-abc-click-outside="
                                        () => {
                                            showQrCode = false;
                                        }
                                    "
                                    class="img-qrcode"
                                >
                                    <img :src="qrCodeUrl" alt="" />
                                    <abc-text
                                        theme="gray"
                                        tag="div"
                                        size="mini"
                                        style="margin-top: -12px;"
                                    >
                                        扫码签名
                                    </abc-text>
                                </div>
                            </div>
                            <div>
                                <div
                                    class="hover-color"
                                    :class="{ 'choose-color': userInfoCo.handSignType === 0 }"
                                    @click="handleSignClick(0)"
                                >
                                    <div class="user-label-layout ">
                                        <div class="title">
                                            电脑签名
                                        </div>
                                        <div class="content no-sign-name">
                                            {{ userInfo?.name }}
                                            <abc-icon
                                                v-if="userInfoCo.handSignType === 0"
                                                icon="positive_1"
                                                style="position: absolute; top: 3px; right: -22px; color: #005ed9;"
                                            ></abc-icon>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="hover-color"
                                    :class="{ 'choose-color': userInfoCo.handSignType === 1 }"
                                    @click="handleSignClick(1)"
                                    @mouseenter="showEditIcon = true"
                                    @mouseleave="showEditIcon = false"
                                >
                                    <abc-tooltip placement="top-start" content="仅管理员可修改成员电子签名" :disabled="isSupportMemberUpdateName">
                                        <div class="user-label-layout ">
                                            <div class="title">
                                                手写签名
                                            </div>
                                            <div class="content">
                                                <div
                                                    v-if="userInfoCo.handSignPicUrl"
                                                    class="img-signature"
                                                >
                                                    <img :src="userInfoCo.handSignPicUrl" />
                                                    <abc-icon
                                                        v-if="showEditIcon && isSupportMemberUpdateName"
                                                        icon="Edit_Profile"
                                                        style="position: absolute; top: 3px; right: -22px;"
                                                        @click.stop="displayQRCode"
                                                    ></abc-icon>
                                                    <abc-icon
                                                        v-if="userInfoCo.handSignType === 1 && (!showEditIcon || !isSupportMemberUpdateName)"
                                                        icon="positive_1"
                                                        style="position: absolute; top: 3px; right: -22px; color: #005ed9;"
                                                    ></abc-icon>
                                                </div>
                                                <div
                                                    v-if="!userInfoCo.handSignPicUrl && isSupportMemberUpdateName"
                                                    class="set-signature"
                                                    @click.stop="displayQRCode"
                                                >
                                                    <div class="sign-setting">
                                                        设置
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </abc-tooltip>
                                </div>
                            </div>
                        </abc-popover>
                        <abc-popover
                            v-else-if="item.desc === 'zoom'"
                            ref="screenPopover"
                            v-abc-click-outside="
                                () => {
                                    $refs.screenPopover.doClose()
                                }
                            "
                            placement="right"
                            trigger="click"
                            width="118px"
                            popper-class="screen-zoom-setting-popper"
                            :popper-style="{ top: '68px' }"
                            :visible-arrow="false"
                            theme="white"
                        >
                            <abc-flex slot="reference" style="width: 212px;" justify="space-between">
                                <abc-text>{{ item.title }}</abc-text>
                                <abc-icon
                                    icon="n-right-line-medium"
                                    color="var(--abc-color-T3)"
                                    :size="16"
                                    style="vertical-align: text-bottom;"
                                ></abc-icon>
                            </abc-flex>
                            <div
                                v-for="$item in zoomList"
                                :key="$item.value"
                                class="zoom__item"
                                @click="handleChangeZoom($item.value)"
                            >
                                {{ $item.label }}
                            </div>
                        </abc-popover>
                        <abc-flex
                            v-else-if="item.desc === 'fullscreen'"
                            flex="1"
                            justify="space-between"
                            @click.stop="handleFullScreen"
                        >
                            <abc-text>{{ fullscreen ? "关闭全屏" : '全屏' }}</abc-text>
                            <abc-icon
                                icon="n-right-line-medium"
                                color="var(--abc-color-T3)"
                                :size="16"
                                style="vertical-align: text-bottom;"
                            ></abc-icon>
                        </abc-flex>
                        <abc-popover
                            v-else-if="['mobile', 'password'].includes(item.desc)"
                            width="204px"
                            placement="right"
                            trigger="hover"
                            theme="yellow"
                            :disabled="!isVirtualMobile"
                        >
                            <abc-flex
                                slot="reference"
                                flex="1"
                                style="width: 212px;"
                                justify="space-between"
                                @click="headerDropdownChange(item.desc)"
                            >
                                <abc-text>{{ item.title }}</abc-text>
                                <abc-space size="small">
                                    <abc-text v-if="item.desc === 'mobile'" theme="gray">
                                        {{ userInfo?.mobile }}
                                    </abc-text>
                                    <abc-text v-if="item.desc === 'password'" theme="gray">
                                        {{ userInfo?.hasPassword ? '已设置' : '未设置' }}
                                    </abc-text>
                                    <abc-icon
                                        icon="n-right-line-medium"
                                        color="var(--abc-color-T3)"
                                        :size="16"
                                        style="vertical-align: text-bottom;"
                                    ></abc-icon>
                                </abc-space>
                            </abc-flex>
                            <div v-if="item.desc === 'mobile'">
                                此账号为虚拟账号，无法修改
                            </div>
                            <div v-if="item.desc === 'password'">
                                如需修改此虚拟账号密码，请联系管理员在【成员管理】中修改
                            </div>
                        </abc-popover>
                        <abc-flex
                            v-else
                            flex="1"
                            justify="space-between"
                            @click="headerDropdownChange(item.desc)"
                        >
                            <abc-text>{{ item.title }}</abc-text>
                            <abc-icon
                                icon="n-right-line-medium"
                                color="var(--abc-color-T3)"
                                :size="16"
                                style="vertical-align: text-bottom;"
                            ></abc-icon>
                        </abc-flex>
                    </template>
                </abc-list>
            </abc-flex>
        </abc-popover>
        <abc-dialog
            v-if="errorDialog"
            v-model="errorDialog"
        >
            <div class="tips" style="font-size: 16px;">
                上传头像不能超过10M
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button @click="errorDialog = false">
                    关闭
                </abc-button>
            </div>
        </abc-dialog>

        <name-change-dialog
            v-if="showNameChangeDialog"
            v-model="showNameChangeDialog"
        >
        </name-change-dialog>
        <mobile-change-dialog
            v-if="showMobileChangeDialog"
            v-model="showMobileChangeDialog"
        >
        </mobile-change-dialog>
        <password-change-dialog
            v-if="showPasswordChangeDialog"
            v-model="showPasswordChangeDialog"
        >
        </password-change-dialog>

        <switch-clinic-dialog v-if="showClinicSwitchDialog" v-model="showClinicSwitchDialog"></switch-clinic-dialog>

        <theme-selector-dialog v-if="showThemeDialog" v-model="showThemeDialog"></theme-selector-dialog>
    </div>
</template>

<script>
    import NameChangeDialog from '../name-change.vue';
    import PasswordChangeDialog from '../password-change.vue';
    import MobileChangeDialog from '../mobile-change.vue';
    import SwitchClinicDialog from 'views/layout/switch-clinic-dialog/index.vue';
    import ThemeSelectorDialog from 'views/layout/theme-selector/theme-selector-dialog.vue';
    import {
        mapActions, mapGetters, mapState,
    } from 'vuex';
    import AbcSocket from 'views/common/single-socket.js';
    import QRCode from 'qrcode';
    import LoginAPI from 'api/login.js';
    import Api from 'api/settings.js';
    import _clone from 'utils/clone.js';
    import { NavigateHelper } from '@/core/index.js';
    import { getGlobalHost } from 'utils/host.js';
    import ShortUrlAPI from 'api/short-url.js';

    import IconClinic from '@/assets/images/icon-clinic-blue.png';
    import IconClinicDark from '@/assets/images/icon-clinic-black.png';
    import IconSwitch from '@/assets/images/<EMAIL>';

    import { isProd } from '@/assets/configure/build-env.js';
    import {
        off, on,
    } from 'utils/dom.js';
    import { isClientSupportHospital } from 'utils/electron.js';
    import { ZoomHelper } from '@/core/zoom-helper.js';
    import avatarImg from '@/assets/images/avatar.png';
    import { windowOpen } from '@/core/navigate-helper';
    import { PROTOCOL } from 'utils/constants';

    export default {
        name: 'UserSettingV2',
        components: {
            NameChangeDialog,
            MobileChangeDialog,
            PasswordChangeDialog,
            SwitchClinicDialog,
            ThemeSelectorDialog,
        },
        props: {
            showClinicName: {
                type: Boolean,
                default: false,
            },
            popperStyle: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            headImageWidth: {
                type: Number,
                default: 32,
            },
            isEnableClinicSelector: {
                type: Function,
            },
        },
        data() {
            return {
                avatarImg,
                IconClinicDark,
                IconClinic,
                IconSwitch,
                showNameChangeDialog: false,
                showMobileChangeDialog: false,
                showThemeDialog: false,
                errorDialog: false,
                userInfoCo: {
                    id: '',
                    mobile: '',
                    name: '',
                    headImgUrl: '',
                    handSign: '',
                    handSignPicUrl: '',
                    handSignType: 0,
                },
                showQrCode: false,
                showEditIcon: false,
                qrCodeUrl: '',
                handSignTypeTmp: 0,
                showCameraIcon: false,
                showNameEditIcon: false,
                showClinicSwitchDialog: false,

                fullscreen: false,

                currentZoomFactor: ZoomHelper.DEFAULT_ZOOM_FACTOR,
            };
        },
        computed: {
            ...mapState('globalModal', ['visibleModifyPasswordDialog']),
            ...mapGetters('theme', ['isDarkTheme']),
            ...mapGetters([
                'userInfo',
                'currentClinic',
                'isTestLogin',
                'isChainAdmin',
                'dataPermission',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isSupportMemberUpdateName() {
                //modifyEmployeeName 0:成员修改 1:管理员修改
                // return !this.dataPermission.settings.modifyEmployeeName;
                // 姓名资料下沉，取消修改签名限制
                return true;
            },
            // 是否隐藏门店名称
            isHideClinicName() {
                return isProd && this.isTestLogin;
            },
            clinicName() {
                return this.currentClinic && this.currentClinic.clinicName;
            },
            roleListMap() {
                const {
                    parentRoles,
                    roleNames,
                } = this.userInfo || {};
                if (parentRoles) {
                    return parentRoles.map((it) => it.name).join(',');
                }
                return roleNames?.join(',');
            },

            // 是否显示修改密码弹框
            showPasswordChangeDialog: {
                get() {
                    return this.visibleModifyPasswordDialog;
                },
                set(val) {
                    if (val) {
                        this.$store.commit('globalModal/SHOW_UPDATE_PASSWORD');
                    } else {
                        this.$store.commit('globalModal/HIDE_UPDATE_PASSWORD');
                    }
                },
            },
            visibleZoomBtn() {
                return isClientSupportHospital();
            },
            zoomList() {
                return [
                    {
                        label: '缩放至 80%',
                        value: 0.8,
                    },
                    {
                        label: '缩放至 90%',
                        value: 0.9,
                    },
                    {
                        label: '恢复至 100%',
                        value: 1,
                    },
                    {
                        label: '放大至 110%',
                        value: 1.1,
                    },
                    {
                        label: '放大至 120%',
                        value: 1.2,
                    },
                ];
            },
            dropdownList() {
                const list = [
                    {
                        id: 7, title: '签名', desc: 'handSign',
                    },
                    {
                        id: 1, title: '手机号', desc: 'mobile',
                    },
                    {
                        id: 2, title: '密码', desc: 'password',
                    },
                    {
                        id: 3, title: '更换皮肤', desc: 'theme',
                    },
                    {
                        id: 4, title: '屏幕缩放', desc: 'zoom',
                    },
                    {
                        id: 5, title: '全屏', desc: 'fullscreen',
                    },
                    {
                        id: 6, title: '退出登录', desc: 'logout',
                    },
                ];
                return this.visibleZoomBtn ? list : list.filter((v) => v.desc !== 'zoom');
            },
            isVirtualMobile() {
                return this.userInfo?.mobile?.startsWith('120');
            },
        },
        created() {
            this.fetchEmployeeInfo();
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('doctor-sign-success', this.signSuccess);
        },
        mounted() {
            this.$on('hook:beforeDestroy', () => {
                this._socket.off('doctor-sign-success', this.signSuccess);
            });

            on(document, 'keydown', this.toggleF11);
            document.onfullscreenchange = () => {
                this.fullscreen = !this.fullscreen;
            };

            if (this.visibleZoomBtn) {
                this.currentZoomFactor = ZoomHelper.getInstance().getZoomFactor();
            }
        },
        beforeDestroy() {
            off(document, 'keydown', this.toggleF11);
        },
        methods: {
            ...mapActions([
                'acFetchUserInfo',
                'updateDoctorSign',
            ]),

            signSuccess(data) {
                this.userInfoCo.handSignPicUrl = data.url;
                this.userInfoCo.handSignType = 1;
                this.showQrCode = false;

                this.updateDoctorSign({
                    handSignType: this.userInfoCo.handSignType,
                    handSign: data.url,
                    handSignPicUrl: data.url,
                });
            },

            async fetchEmployeeInfo() {
                this.userInfoCo = _clone(this.userInfo);
                this.handSignTypeTmp = this.userInfoCo.handSignType;
            },
            async updateBaseSign() {
                this.loadingInfo = true;
                try {
                    const handSign = this.userInfoCo.handSignType === 1 ? this.userInfoCo.handSignPicUrl : this.userInfoCo.name;
                    await LoginAPI.updateUserInfo({
                        handSignType: this.userInfoCo.handSignType,
                        handSign,
                        opType: 3,
                    });
                    this.$Toast({
                        type: 'success',
                        message: '修改成功',
                    });
                    this.handSignTypeTmp = this.userInfoCo.handSignType;
                    this.updateDoctorSign({
                        handSign,
                        handSignPicUrl: handSign,
                    });
                } catch (error) {
                    this.$Toast({
                        message: error.message,
                        type: 'error',
                    });
                }
                this.showEditIcon = false;
                this.loadingInfo = false;
            },
            async updateBaseHeadUrl(url) {
                this.loadingInfo = true;
                try {
                    await LoginAPI.updateUserInfo({
                        headImgUrl: url,
                        opType: 5,
                    });
                    this.$Toast({
                        type: 'success',
                        message: '修改成功',
                    });
                } catch (error) {
                    this.$Toast({
                        message: error.message,
                        type: 'error',
                    });
                }
                this.loadingInfo = false;
            },
            handleSignClick(value) {
                this.userInfoCo.handSignType = value;
                if (this.handSignTypeTmp !== this.userInfoCo.handSignType) {
                    this.updateBaseSign();
                }

            },
            /**
             * @desc 展示签名二维码
             * <AUTHOR>
             * @date 2018/08/29 15:40:47
             */
            async displayQRCode() {
                this.$refs.signPopover.doClose();
                // 在过期时间内，使用缓存中的url
                if (this.qrCodeUrl && Date.now() - this._createQrTime < 72e5) {
                    this.showQrCode = true;
                } else {
                    const {
                        status, data,
                    } = await Api.personal.fetchSignToken();
                    if (status.code === 200) {
                        const fullUrl = `${getGlobalHost()}/m/signature?token=${data.token}&region=${location.host}`;
                        const shortUrlRes = await ShortUrlAPI.createShortUrl({
                            fullUrl,
                        });
                        // 生成待参数的二维码
                        QRCode.toDataURL(shortUrlRes.data.shortUrl)
                            .then((url) => {
                                this._createQrTime = Date.now();
                                this.showQrCode = true;
                                this.qrCodeUrl = url;
                            })
                            .catch((err) => {
                                console.error(err);
                            });
                    }
                }
            },
            async headerDropdownChange(value) {
                if (value === 'logout') {
                    this.$store.dispatch('handleEmployeeLogout').then(() => {
                        NavigateHelper.navigateToLogin();
                    });
                } else if (value === 'update-log') {
                    windowOpen('/cms/view/loglist', '_blank', '', PROTOCOL.HTTPS);
                } else if (value === 'name') {
                    this.isSupportMemberUpdateName && (this.showNameChangeDialog = true);
                } else if (value === 'mobile' && !this.isVirtualMobile) {
                    this.showMobileChangeDialog = true;
                } else if (value === 'password' && !this.isVirtualMobile) {
                    this.showPasswordChangeDialog = true;
                } else if (value === 'switch-clinic') {
                    this.showClinicSwitchDialog = true;
                } else if (value === 'theme') {
                    this.showThemeDialog = true;
                }
                this.$refs.userSettingPopover.doClose();
            },
            handleClose() {
                this.showQrCode = false;
            },
            async fileChange(e) {
                const inputFile = e.target.files[0];
                if (!inputFile) {
                    return;
                }
                //生成头像预览
                if (inputFile.size > 1024 * 1024 * 10) {
                    //图片超过10M
                    this.errorDialog = true;
                    this.$refs.imageInput.value = '';
                    return 0;
                }
                const reader = new FileReader();
                reader.readAsDataURL(inputFile);
                reader.onload = (e) => {
                    $('.__avator__ img')[0].src = e.target.result;
                };

                //头像上传
                this.loading = true;

                try {
                    const imgRes = await this.$abcPlatform.service.oss.clinicUsageUpload(
                        { filePath: 'doctor' },
                        inputFile,
                    );

                    await this.updateBaseHeadUrl(imgRes.url);
                } catch (err) {
                    console.error('fileChange', err);
                    this.$Toast({
                        type: 'error',
                        message: '上传失败，请重试',
                    });
                } finally {
                    await this.acFetchUserInfo();
                    this.loading = false;
                }
            },

            toggleF11(event) {
                const KEY_F11 = 122;
                if (event.keyCode === KEY_F11) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                }
            },
            // 全屏事件
            handleFullScreen() {
                const element = document.documentElement;
                // 判断是否已经是全屏
                // 如果是全屏，退出
                if (this.fullscreen) {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitCancelFullScreen) {
                        document.webkitCancelFullScreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                    console.debug('已还原！');
                } else { // 否则，进入全屏
                    if (element.requestFullscreen) {
                        element.requestFullscreen();
                    } else if (element.webkitRequestFullScreen) {
                        element.webkitRequestFullScreen();
                    } else if (element.mozRequestFullScreen) {
                        element.mozRequestFullScreen();
                    } else if (element.msRequestFullscreen) {
                        // IE11
                        element.msRequestFullscreen();
                    }
                    console.debug('已全屏！');
                }
            },

            handleChangeZoom(value) {
                this.currentZoomFactor = ZoomHelper.getInstance().setZoomFactor(value);
            },
        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import "../../../styles/theme";
@import "../../../styles/mixin";

.app-header-fixed-left__user-setting {
    padding: 0 8px;
    margin-bottom: 8px;
    cursor: pointer;

    .user-dropdown__reference {
        display: flex;
        align-items: center;
        justify-content: start;
    }

    .avatar-img {
        border-radius: var(--abc-border-radius-mini);
    }

    .user-clinic-name {
        max-width: 80px;
        max-height: 28px;
        overflow: hidden;
        font-size: 12px;
        line-height: 14px;
        color: rgba(255, 255, 255, 0.8);
    }
}

.user-setting__right-popper.abc-popover__popper {
    margin-left: 18px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.15);

    .user-setting__clinic-wrapper {
        padding: 0;
        cursor: pointer;
    }

    .user-setting__clinic {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        background-color: #e5f2ff;
        border-radius: var(--abc-border-radius-small);
        opacity: 1;

        &:hover {
            background-color: $P4;
        }

        .user-setting__title {
            max-width: 184px;
            margin-right: 8px;
            margin-left: 8px;
            font-size: 14px;
            font-weight: 400;
            line-height: 24px;
        }
    }

    .img-qrcode {
        top: auto;
        right: -144px;
        bottom: 0;
        left: auto;
    }
}

.screen-zoom-setting-popper.abc-popover__popper {
    padding: 4px;

    .zoom__item {
        padding: 5px 8px;
        line-height: 22px;
        cursor: pointer;
        border-radius: var(--abc-border-radius-mini);

        &:hover {
            background: var(--abc-color-cp-grey4);
        }
    }
}
</style>
