<template>
    <div class="user-setting">
        <abc-popover
            ref="userSettingPopover"
            placement="bottom-end"
            trigger="click"
            :width="218"
            theme="white"
            size="large"
            :visible-arrow="false"
            :popper-style="{ padding: '16px 0' }"
            @close="handleClose"
            @hide="handleClose"
        >
            <span slot="reference">
                <abc-popover
                    placement="bottom-end"
                    trigger="hover"
                    theme="yellow"
                    :offset="-10"
                    :open-delay="3000"
                    :disabled="!viewDistributeConfig.AppHeader.showUserInfoNationalCode"
                    :close-delay="10000"
                >
                    <template slot="reference">
                        <abc-flex flex="1" align="center">
                            <abc-image
                                style="cursor: pointer; border-radius: var(--abc-border-radius-mini);"
                                class="avatar-img app-header--avator-btn"
                                :width="30"
                                :height="30"
                                :src="userInfo?.headImgUrl"
                                oss-style-name="AvatorCompress"
                                :default-img="require('@/assets/images/user-setting-default.png')"
                            ></abc-image>
                        </abc-flex>
                    </template>
                    <div class="popover-national-info">
                        <span>
                            人员编码: {{ userInfo?.nationalCode || '未对码' }}
                        </span>
                        <span>
                            机构编码: {{ currentClinic?.nationalCode || '未对码' }}
                        </span>
                    </div>
                </abc-popover>
            </span>
            <abc-flex vertical gap="middle">
                <abc-space direction="vertical" gap="middle" :custom-style="{ padding: '0 16px' }">
                    <abc-space :size="12" align="normal">
                        <label
                            for="__file__"
                            style=" position: relative; display: block; width: 56px; height: 56px;"
                            @mouseenter="showCameraIcon = true"
                            @mouseleave="showCameraIcon = false"
                        >
                            <abc-image
                                class=" __avator__ avatar-img"
                                style="border-radius: var(--abc-border-radius-mini);"
                                :width="56"
                                :height="56"
                                :src="userInfo?.headImgUrl"
                                :default-img="require('@/assets/images/user-setting-default.png')"
                                oss-style-name="AvatorCompress"
                            ></abc-image>
                            <span v-if="showCameraIcon" class="user-setting-popper_avatar_hover-mask">
                                <abc-icon
                                    icon="camera"
                                    style="position: absolute; top: 50%; left: 50%; color: #ffffff; transform: translate(-50%, -50%);"
                                    size="18"
                                ></abc-icon>
                            </span>

                            <input
                                id="__file__"
                                ref="imageInput"
                                type="file"
                                style="display: none;"
                                accept="image/*"
                                @change="fileChange"
                            />
                        </label>
                        <div style="width: 118px;">
                            <!--                            <abc-popover-->
                            <!--                                placement="left"-->
                            <!--                                trigger="hover"-->
                            <!--                                theme="yellow"-->
                            <!--                            >-->
                            <!--                                <abc-text slot="reference" size="normal" bold>-->
                            <!--                                    {{ userInfo?.name }}-->
                            <!--                                </abc-text>-->
                            <!--                                <div>请联系管理员，在「管理-成员管理」中修改</div>-->
                            <!--                            </abc-popover>-->
                            <!--                            <abc-popover-->
                            <!--                                placement="left"-->
                            <!--                                trigger="hover"-->
                            <!--                                theme="yellow"-->
                            <!--                            >-->
                            <!--                                <abc-space slot="reference" :size="8">-->
                            <!--                                    <abc-text size="mini" theme="gray" style="white-space: nowrap;">-->
                            <!--                                        {{ userInfoCo.handSignType ? '手写签名' : '电脑签名' }}-->
                            <!--                                    </abc-text>-->
                            <!--                                    <abc-text v-if="!userInfoCo.handSignType">-->
                            <!--                                        {{ userInfo?.name }}-->
                            <!--                                    </abc-text>-->
                            <!--                                    <img-->
                            <!--                                        v-if="userInfoCo.handSignType && userInfoCo.handSignPicUrl"-->
                            <!--                                        style=" width: auto; height: 16px; vertical-align: text-bottom;"-->
                            <!--                                        :src="userInfoCo.handSignPicUrl"-->
                            <!--                                    />-->
                            <!--                                </abc-space>-->
                            <!--                                <div>请联系管理员，在「管理-成员管理」中修改</div>-->
                            <!--                            </abc-popover>-->
                            <abc-text size="normal" tag="div" bold>
                                {{ userInfo?.name }}
                            </abc-text>
                            <abc-text size="mini" theme="gray" tag="div">
                                {{ roleListMap }}
                            </abc-text>
                        </div>
                    </abc-space>
                    <abc-divider
                        margin="none"
                        size="normal"
                        theme="light"
                        variant="solid"
                        layout="horizontal"
                        style="width: 186px;"
                    ></abc-divider>
                </abc-space>
                <abc-list
                    :data-list="dropdownList"
                    :scrollable="false"
                    style="padding: 0 6px;"
                >
                    <template #default="{ item }">
                        <abc-popover
                            v-if="item.desc === 'handSign'"
                            ref="signPopover"
                            v-abc-click-outside="
                                () => {
                                    $refs.signPopover.doClose()
                                }
                            "
                            placement="left"
                            trigger="click"
                            width="204px"
                            popper-class="user-sign-setting-popper"
                            :visible-arrow="false"
                            theme="white"
                        >
                            <div slot="reference" class="user-label-layout" style="width: 186px;">
                                <abc-flex
                                    flex="1"
                                    justify="space-between"
                                >
                                    <abc-text>{{ userInfoCo.handSignType ? '手写签名' : '电脑签名' }}</abc-text>
                                    <abc-space size="small">
                                        <abc-text v-if="userInfoCo.handSignType === 0" theme="gray">
                                            {{ userInfo?.name }}
                                        </abc-text>
                                        <img v-if="userInfoCo.handSignType === 1" style="width: auto; height: 16px; vertical-align: text-bottom;" :src="userInfoCo.handSignPicUrl" />
                                        <abc-icon
                                            icon="n-right-line-medium"
                                            color="var(--abc-color-T3)"
                                            :size="16"
                                            style="vertical-align: text-bottom;"
                                        ></abc-icon>
                                    </abc-space>
                                </abc-flex>
                                <div
                                    v-if="showQrCode"
                                    v-abc-click-outside="
                                        () => {
                                            showQrCode = false;
                                        }
                                    "
                                    class="img-qrcode"
                                >
                                    <img :src="qrCodeUrl" alt="" />
                                    <abc-text
                                        theme="gray"
                                        tag="div"
                                        size="mini"
                                        style="margin-top: -12px;"
                                    >
                                        扫码签名
                                    </abc-text>
                                </div>
                            </div>
                            <div>
                                <div
                                    class="hover-color"
                                    :class="{ 'choose-color': userInfoCo.handSignType === 0 }"
                                    @click="handleSignClick(0)"
                                >
                                    <div class="user-label-layout ">
                                        <div class="title">
                                            电脑签名
                                        </div>
                                        <div class="content no-sign-name">
                                            {{ userInfo?.name }}
                                            <abc-icon
                                                v-if="userInfoCo.handSignType === 0"
                                                icon="positive_1"
                                                style="position: absolute; top: 3px; right: -22px; color: #005ed9;"
                                            ></abc-icon>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="hover-color"
                                    :class="{ 'choose-color': userInfoCo.handSignType === 1 }"
                                    @click="handleSignClick(1)"
                                    @mouseenter="showEditIcon = true"
                                    @mouseleave="showEditIcon = false"
                                >
                                    <abc-tooltip placement="top-start" content="仅管理员可修改成员电子签名" :disabled="isSupportMemberUpdateName">
                                        <div class="user-label-layout ">
                                            <div class="title">
                                                手写签名
                                            </div>
                                            <div class="content">
                                                <div
                                                    v-if="userInfoCo.handSignPicUrl"
                                                    class="img-signature"
                                                >
                                                    <img :src="userInfoCo.handSignPicUrl" />
                                                    <abc-icon
                                                        v-if="showEditIcon && isSupportMemberUpdateName"
                                                        icon="Edit_Profile"
                                                        style="position: absolute; top: 3px; right: -22px;"
                                                        @click.stop="displayQRCode"
                                                    ></abc-icon>

                                                    <abc-icon
                                                        v-if="userInfoCo.handSignType === 1 && (!showEditIcon || !isSupportMemberUpdateName)"
                                                        icon="positive_1"
                                                        style="position: absolute; top: 3px; right: -22px; color: #005ed9;"
                                                    ></abc-icon>
                                                </div>
                                                <div
                                                    v-if="!userInfoCo.handSignPicUrl && isSupportMemberUpdateName"
                                                    class="set-signature"
                                                    @click.stop="displayQRCode"
                                                >
                                                    <div class="sign-setting">
                                                        设置
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </abc-tooltip>
                                </div>
                                <div v-if="isShandongQingdao" class="hover-color">
                                    <div class="user-label-layout" @click="openElectronicSignatureDialog">
                                        <div class="title">
                                            手写签名承诺书
                                        </div>
                                        <div class="electronic-signature">
                                            <template>
                                                <span
                                                    v-if="userInfoCo.handSignPicUrl && !electronicSignatureCommitmentUrl"
                                                    :style="{
                                                        color: userInfoCo.handSignPicUrl ? '#005ed9' : '#7a8794'
                                                    }"
                                                >未上传</span>
                                                <span v-if="electronicSignatureCommitmentUrl" style="color: #005ed9;">已上传</span>
                                            </template>
                                            <abc-icon
                                                icon="Arrow_Rgiht"
                                                :style="{
                                                    color: electronicSignatureCommitmentUrl ? '#005ed9' : '#aab4bf'
                                                }"
                                            ></abc-icon>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </abc-popover>
                        <abc-flex
                            v-if="item.desc === 'logout'"
                            flex="1"
                            justify="space-between"
                            @click="headerDropdownChange(item.desc)"
                        >
                            <abc-text>{{ item.title }}</abc-text>
                            <abc-icon
                                icon="n-right-line-medium"
                                color="var(--abc-color-T3)"
                                :size="16"
                                style="vertical-align: text-bottom;"
                            ></abc-icon>
                        </abc-flex>
                        <abc-popover
                            v-if="['mobile', 'password'].includes(item.desc)"
                            width="204px"
                            placement="left"
                            trigger="hover"
                            theme="yellow"
                            :disabled="!isVirtualMobile"
                        >
                            <abc-flex
                                slot="reference"
                                flex="1"
                                style="width: 186px;"
                                justify="space-between"
                                @click="headerDropdownChange(item.desc)"
                            >
                                <abc-text>{{ item.title }}</abc-text>
                                <abc-space size="small">
                                    <abc-text v-if="item.desc === 'mobile'" theme="gray">
                                        {{ userInfo?.mobile }}
                                    </abc-text>
                                    <abc-text v-if="item.desc === 'password'" theme="gray">
                                        {{ userInfo?.hasPassword ? '已设置' : '未设置' }}
                                    </abc-text>
                                    <abc-icon
                                        icon="n-right-line-medium"
                                        color="var(--abc-color-T3)"
                                        :size="16"
                                        style="vertical-align: text-bottom;"
                                    ></abc-icon>
                                </abc-space>
                            </abc-flex>
                            <div v-if="item.desc === 'mobile'">
                                此账号为虚拟账号，无法修改
                            </div>
                            <div v-if="item.desc === 'password'">
                                如需修改此虚拟账号密码，请联系管理员在【成员管理】中修改
                            </div>
                        </abc-popover>
                    </template>
                </abc-list>
            </abc-flex>
        </abc-popover>
        <abc-dialog
            v-if="errorDialog"
            v-model="errorDialog"
        >
            <div class="tips" style="font-size: 16px;">
                上传头像不能超过10M
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button @click="errorDialog = false">
                    关闭
                </abc-button>
            </div>
        </abc-dialog>

        <name-change-dialog
            v-if="showNameChangeDialog"
            v-model="showNameChangeDialog"
        >
        </name-change-dialog>
        <mobile-change-dialog
            v-if="showMobileChangeDialog"
            v-model="showMobileChangeDialog"
        >
        </mobile-change-dialog>
        <password-change-dialog
            v-if="showPasswordChangeDialog"
            v-model="showPasswordChangeDialog"
        >
        </password-change-dialog>
    </div>
</template>

<script>
    import NameChangeDialog from '../name-change.vue';
    import PasswordChangeDialog from '../password-change.vue';
    import MobileChangeDialog from '../mobile-change.vue';
    import { ElectronicSignatureDialog } from 'views/layout/dialog-electronic-signature';
    import {
        mapActions, mapGetters, mapState,
    } from 'vuex';
    import AbcSocket from 'views/common/single-socket.js';
    import QRCode from 'qrcode';
    import LoginAPI from 'api/login.js';
    import Api from 'api/settings.js';
    import _clone from 'utils/clone.js';
    import { NavigateHelper } from '@/core/index.js';
    import { getGlobalHost } from 'utils/host.js';
    import ShortUrlAPI from 'api/short-url.js';
    import { BusinessTypeEnum } from '@abc/constants';
    import SettingsAPI from 'api/settings';
    import { windowOpen } from '@/core/navigate-helper';
    import { PROTOCOL } from 'utils/constants';

    export default {
        name: 'UserSetting',
        components: {
            NameChangeDialog,
            MobileChangeDialog,
            PasswordChangeDialog,
        },
        data() {
            return {
                showNameChangeDialog: false,
                showMobileChangeDialog: false,
                errorDialog: false,
                userInfoCo: {
                    id: '',
                    mobile: '',
                    name: '',
                    headImgUrl: '',
                    handSign: '',
                    handSignPicUrl: '',
                    handSignType: 0,
                },
                showQrCode: false,
                showEditIcon: false,
                qrCodeUrl: '',
                handSignTypeTmp: 0,
                showCameraIcon: false,
                showNameEditIcon: false,
                dropdownList: [
                    {
                        id: 4, title: '签名', desc: 'handSign',
                    },
                    {
                        id: 1, title: '手机号', desc: 'mobile',
                    },
                    {
                        id: 2, title: '密码', desc: 'password',
                    },
                    {
                        id: 3, title: '退出登录', desc: 'logout',
                    },
                ],
            };
        },
        computed: {
            ...mapState('globalModal', ['visibleModifyPasswordDialog']),
            ...mapGetters([
                'userInfo',
                'currentClinic',
                'dataPermission',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isSupportMemberUpdateName() {
                //modifyEmployeeName 0:成员修改 1:管理员修改
                // return !this.dataPermission.settings.modifyEmployeeName;
                // 姓名资料下沉，取消修改签名限制
                return true;
            },
            roleListMap() {
                const {
                    parentRoles,
                    roleNames,
                } = this.userInfo || {};
                if (parentRoles) {
                    return parentRoles.map((it) => it.name).join(',');
                }
                return roleNames?.join(',');
            },
            isShandongQingdao() {
                return this.$abcSocialSecurity.config.isShandongQingdao;
            },
            electronicSignatureCommitmentUrl() {
                return this.userInfoCo?.electronicSignatureCommitmentUrl;
            },

            // 是否显示修改密码弹框
            showPasswordChangeDialog: {
                get() {
                    return this.visibleModifyPasswordDialog;
                },
                set(val) {
                    if (val) {
                        this.$store.commit('globalModal/SHOW_UPDATE_PASSWORD');
                    } else {
                        this.$store.commit('globalModal/HIDE_UPDATE_PASSWORD');
                    }
                },
            },

            isVirtualMobile() {
                return this.userInfo?.mobile?.startsWith('120');
            },
        },
        created() {
            this.fetchEmployeeInfo();
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('doctor-sign-success', this.signSuccess);
            this._socket.on('short-url.upload_attachment', this.handleImages);
        },
        mounted() {
            this.$on('hook:beforeDestroy', () => {
                this._socket.off('doctor-sign-success', this.signSuccess);
                this._socket?.off('short-url.upload_attachment', this.handleImages);
            });
        },
        methods: {
            ...mapActions([
                'acFetchUserInfo',
                'updateDoctorSign',
            ]),
            handleImages(data) {
                const {
                    businessType, attachments, businessId,
                } = data;
                if (businessType === BusinessTypeEnum.EMR_COMMITMENT_SIGNATURE && this.userInfo?.id === businessId && attachments && attachments.length) {
                    this.handleAddAttachments(attachments[0].url);
                }
            },
            async handleAddAttachments(electronicSignatureCommitmentUrl) {
                try {
                    const res = await SettingsAPI.employee.uploadElectronicSignature(electronicSignatureCommitmentUrl);
                    if (res) {
                        await this.acFetchUserInfo();
                        await this.fetchEmployeeInfo();
                    }
                } catch (e) {
                    console.log(e);
                }
            },

            signSuccess(data) {
                this.userInfoCo.handSignPicUrl = data.url;
                this.userInfoCo.handSignType = 1;
                this.showQrCode = false;

                this.updateDoctorSign({
                    handSignType: this.userInfoCo.handSignType,
                    handSign: data.url,
                    handSignPicUrl: data.url,
                });
            },

            async fetchEmployeeInfo() {
                this.userInfoCo = _clone(this.userInfo);
                this.handSignTypeTmp = this.userInfoCo.handSignType;
            },
            async updateBaseSign() {
                this.loadingInfo = true;
                try {
                    const handSign = this.userInfoCo.handSignType === 1 ? this.userInfoCo.handSignPicUrl : this.userInfoCo.name;
                    await LoginAPI.updateUserInfo({
                        handSignType: this.userInfoCo.handSignType,
                        handSign,
                        opType: 3,
                    });
                    this.$Toast({
                        type: 'success',
                        message: '修改成功',
                    });
                    this.handSignTypeTmp = this.userInfoCo.handSignType;
                    this.updateDoctorSign({
                        handSign,
                        handSignPicUrl: handSign,
                    });
                } catch (error) {
                    this.$Toast({
                        message: error.message,
                        type: 'error',
                    });
                }
                this.showEditIcon = false;
                this.loadingInfo = false;
            },
            async updateBaseHeadUrl(url) {
                this.loadingInfo = true;
                try {
                    await LoginAPI.updateUserInfo({
                        headImgUrl: url,
                        opType: 5,
                    });
                    this.$Toast({
                        type: 'success',
                        message: '修改成功',
                    });
                } catch (error) {
                    this.$Toast({
                        message: error.message,
                        type: 'error',
                    });
                }
                this.loadingInfo = false;
            },
            handleSignClick(value) {
                this.userInfoCo.handSignType = value;
                if (this.handSignTypeTmp !== this.userInfoCo.handSignType) {
                    this.updateBaseSign();
                }

            },
            /**
             * @desc 展示签名二维码
             * <AUTHOR>
             * @date 2018/08/29 15:40:47
             */
            async displayQRCode() {
                this.$refs.signPopover.doClose();
                // 在过期时间内，使用缓存中的url
                if (this.qrCodeUrl && Date.now() - this._createQrTime < 72e5) {
                    this.showQrCode = true;
                } else {
                    const {
                        status, data,
                    } = await Api.personal.fetchSignToken();
                    if (status.code === 200) {
                        const fullUrl = `${getGlobalHost()}/m/signature?token=${data.token}&region=${location.host}`;
                        const shortUrlRes = await ShortUrlAPI.createShortUrl({
                            fullUrl,
                        });
                        // 生成待参数的二维码
                        QRCode.toDataURL(shortUrlRes.data.shortUrl)
                            .then((url) => {
                                this._createQrTime = Date.now();
                                this.showQrCode = true;
                                this.qrCodeUrl = url;
                            })
                            .catch((err) => {
                                console.error(err);
                            });
                    }
                }
            },
            async headerDropdownChange(value) {
                if (value === 'logout') {
                    this.$store.dispatch('handleEmployeeLogout').then(() => {
                        NavigateHelper.navigateToLogin();
                    });
                } else if (value === 'update-log') {
                    windowOpen('/cms/view/loglist', '_blank', '', PROTOCOL.HTTPS);
                } else if (value === 'name') {
                    this.isSupportMemberUpdateName && (this.showNameChangeDialog = true);
                } else if (value === 'mobile' && !this.isVirtualMobile) {
                    this.showMobileChangeDialog = true;
                } else if (value === 'password' && !this.isVirtualMobile) {
                    this.showPasswordChangeDialog = true;
                }
                this.$refs.userSettingPopover.doClose();
            },
            handleClose() {
                this.showQrCode = false;
            },
            async fileChange(e) {
                const inputFile = e.target.files[0];
                if (!inputFile) {
                    return;
                }
                //生成头像预览
                if (inputFile.size > 1024 * 1024 * 10) {
                    //图片超过10M
                    this.errorDialog = true;
                    this.$refs.imageInput.value = '';
                    return 0;
                }
                const reader = new FileReader();
                reader.readAsDataURL(inputFile);
                reader.onload = (e) => {
                    $('.__avator__ img')[0].src = e.target.result;
                };

                //头像上传
                this.loading = true;

                try {
                    const imgRes = await this.$abcPlatform.service.oss.clinicUsageUpload(
                        { filePath: 'doctor' },
                        inputFile,
                    );

                    await this.updateBaseHeadUrl(imgRes.url);
                } catch (err) {
                    console.error('fileChange', err);
                    this.$Toast({
                        type: 'error',
                        message: '上传失败，请重试',
                    });
                } finally {
                    await this.acFetchUserInfo();
                    this.loading = false;
                }
            },

            openElectronicSignatureDialog() {
                new ElectronicSignatureDialog({
                    employeeId: this.userInfo?.id,
                    clinicId: this.currentClinic?.clinicId,
                    clinicName: this.currentClinic?.clinicName,
                    userInfo: this.userInfo,
                    uploadSuccessHandler: this.fetchEmployeeInfo,
                }, 'electronic-signature-dialog').generateDialog({ parent: this });
            },
        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import "../../../styles/theme";
@import "../../../styles/mixin";

.__avator_loading__ {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 28px;
    height: 28px;
    margin-top: -14px;
    margin-left: -14px;
    text-align: center;
    background-image: url("assets/images/upload-avatar.png");
    background-size: 28px 28px;
    animation: rotating 1.2s linear infinite;
}

@keyframes rotating {
    from {
        transform: rotate(0);
    }

    to {
        transform: rotate(360deg);
    }
}

.img-qrcode {
    // display: none;
    position: absolute;
    left: -140px;
    z-index: 1000;
    width: 136px;
    height: 158px;
    padding: 0 0 8px;
    overflow: hidden;
    text-align: center;
    background-color: #ffffff;
    border-radius: 5px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

    //&::after {
    //    position: absolute;
    //    top: 209px;
    //    left: 50%;
    //    display: block;
    //    margin-left: -2px;
    //    content: " ";
    //
    //    @include triangle(10px, 5px, #fff, "down");
    //}

    img {
        width: 136px;
        height: 136px;
    }

    span {
        position: absolute;
        bottom: 21px;
        left: 26px;
        display: inline-block;
        width: 140px;
        font-size: 12px;
        line-height: 1;
        color: #2e3439;
    }
}
</style>
