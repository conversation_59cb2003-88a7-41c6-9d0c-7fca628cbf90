<template>
    <div class="patient-form-wrapper patient-form" :class="{ 'is-block': block }">
        <div class="input-name-wrapper">
            <abc-form-item
                label="姓名"
                :required="patientInfoRequireConfig.name.required"
                class="input-name"
                :label-width="labelWidth"
            >
                <patient-autocomplete
                    ref="autoComplete"
                    v-model="patient.name"
                    :disabled="disabled || disabledName"
                    :is-member-module="isMemberModule"
                    :can-edit="canEdit"
                    :is-scan-code="isScanCode"
                    :max-length="40"
                    :distinguish-half-angle-length="true"
                    :is-can-see-patient-mobile="isCanSeePatientMobile"
                    @focus="focusBarcode"
                    @enter="enterEvent"
                    @selectPatient="selectPatient"
                    @blur="blurBarcode"
                    @change="fetchPatient"
                >
                    <template v-if="enableSocialMark">
                        <template v-if="!!shebaoCardInfo" slot="append">
                            <patient-social
                                v-model="visibleSocialInfo"
                                :shebao-card-info="shebaoCardInfo"
                                :style="{ 'margin-right': canEdit ? '22px' : '0px' }"
                                style="padding: 3px 0;"
                            >
                                <div class="social-mark-btn" @click="visibleSocialInfo = !visibleSocialInfo">
                                    医保
                                </div>
                            </patient-social>
                        </template>
                        <template
                            v-if="ReadCardService.isEnable() && !shebaoCardInfo && patient.id && !canEdit"
                            slot="append"
                        >
                            <abc-icon
                                size="14"
                                icon="read-card"
                                color="#96a4b3"
                                @click="onClickFillShebaoCardInfo"
                            ></abc-icon>
                        </template>
                    </template>
                </patient-autocomplete>
            </abc-form-item>

            <template v-if="!isMemberModule">
                <patient-card
                    v-if="!isMemberModule && patient.id"
                    ref="patient-card"
                    style="margin-left: 8px;"
                    :id-card-linkage-switch="idCardLinkageSwitch"
                    :patient-id="patient.id"
                    :is-can-see-patient-mobile="isCanSeePatientMobile"
                    @on-visible-base-card-change="onVisibleBaseCardChange"
                    @fetch-info="onFetchInfo"
                    @init-fetch-info="onChangePatientTags"
                    @change="onChangePatientTags"
                ></patient-card>
                <div
                    v-else-if="ReadCardService.isEnable()"
                    class="card-btn-wrapper"
                    @click="handleOpenReadPanel"
                >
                    <abc-icon size="16" icon="read-card"></abc-icon>
                </div>
            </template>
        </div>

        <abc-form-item
            v-if="!isMemberModule && !regsHiddenTags"
            label="标签"
            class="patient-tags-item"
            style="width: 100%;"
            :label-width="labelWidth"
            :custom-label-style="customLabelStyle"
        >
            <abc-tag-group>
                <div
                    v-for="tag in patient.tags"
                    :key="tag.tagId"
                >
                    <tag-item
                        :tag="tag"
                        @click-close-label="onClickHandleTag"
                    ></tag-item>
                </div>
                <abc-popover
                    ref="elpop"
                    placement="bottom-start"
                    trigger="manual"
                    :value="showPopover"
                    :visible-arrow="false"
                    popper-class="add-label-pop"
                >
                    <abc-tags-add slot="reference" @click.native="showPopover = !showPopover"></abc-tags-add>
                    <div v-abc-click-outside="outside" class="box">
                        <view-labels
                            :show-manage="false"
                            :selected-ids="selectedIds"
                            :filter="false"
                            @change="onClickHandleTag"
                        ></view-labels>
                    </div>
                </abc-popover>
            </abc-tag-group>
        </abc-form-item>

        <abc-form-item
            label="性别"
            class="sex-radio-group"
            style="width: 100%;"
            :required="patientInfoRequireConfig.sex.required"
            :label-width="labelWidth"
        >
            <abc-radio-group v-model="patient.sex">
                <abc-radio :disabled="disabled || disabledSex" label="男" style="height: 32px;">
                    男
                </abc-radio>
                <abc-radio :disabled="disabled || disabledSex" label="女" style="height: 32px;">
                    女
                </abc-radio>
            </abc-radio-group>
        </abc-form-item>

        <div class="patient-age-info">
            <!--没有月和天，就需要年-->
            <abc-form-item
                label="年龄"
                show-red-dot
                :required="patientInfoRequireConfig.age.required && !patient.age.month && (!patient.age.day && patient.age.day !== 0)"
                class="patient-age-year"
                :label-width="labelWidth"
            >
                <abc-input
                    v-model.number="patient.age.year"
                    v-abc-focus-selected
                    :width="45"
                    :disabled="disabled || disabledAge"
                    :input-custom-style="{
                        padding: '3px 6px', 'text-align': 'center'
                    }"
                    type="number"
                    :config="{
                        supportZero: true, max: 199
                    }"
                    @enter="enterEvent"
                    @change="changeAge"
                >
                    <span slot="append">岁</span>
                </abc-input>
            </abc-form-item>
            <!--没有年和天，就需要月-->
            <abc-form-item
                class="patient-age-month"
                style="margin-top: 0;"
                :required="patientInfoRequireConfig.age.required && (!patient.age.day && patient.age.day !== 0) && !patient.age.year"
                :validate-event="validateMonth"
            >
                <abc-input
                    v-model.number="patient.age.month"
                    v-abc-focus-selected
                    :width="45"
                    :disabled="disabled || disabledAge"
                    :input-custom-style="{ 'text-align': 'center' }"
                    type="number"
                    :config="{ supportZero: true }"
                    @enter="enterEvent"
                    @change="changeAge"
                >
                    <span slot="append">月</span>
                </abc-input>
            </abc-form-item>
            <!--没有月和年，就需要天-->
            <abc-form-item
                class="patient-age-day"
                style="margin-top: 0;"
                :required="patientInfoRequireConfig.age.required && !patient.age.month && !patient.age.year"
                :validate-event="validateDay"
            >
                <abc-input
                    v-model.number="patient.age.day"
                    v-abc-focus-selected
                    :width="45"
                    :disabled="disabled || disabledAge"
                    :input-custom-style="{ 'text-align': 'center' }"
                    type="number"
                    :config="{ supportZero: true }"
                    @enter="enterEvent"
                    @change="changeAge"
                >
                    <span slot="append">天</span>
                </abc-input>
            </abc-form-item>
        </div>

        <abc-form-item
            v-abc-focus-selected
            label="生日"
            class="patient-birthday"
            :label-width="labelWidth"
            :required="patientInfoRequireConfig.birthday.required"
        >
            <birthday-picker
                v-model="patient.birthday"
                :width="inputWidth"
                :disabled="disabled || disabledAge"
                @enter="enterEvent"
                @change="changeBirthday"
            ></birthday-picker>
        </abc-form-item>

        <abc-form-item
            v-abc-focus-selected
            label="手机"
            :required="patientInfoRequireConfig.mobile.required"
            :validate-event="handleValidateMobile"
            :label-width="labelWidth"
        >
            <abc-input
                v-model="patientMobile"
                type="phone"
                :disabled="disabled || disabledMobile"
                :width="inputWidth"
                @enter="enterEvent"
                @change="fetchPatient"
            ></abc-input>
        </abc-form-item>

        <template v-if="!isMemberModule">
            <abc-form-item
                v-if="!regsHiddenIdCard"
                label="证件"
                :validate-event="_validateIdCard"
                :required="patientInfoRequireConfig.certificates.required"
                :label-width="labelWidth"
            >
                <abc-certificates-type
                    ref="crm-id-card"
                    v-model.trim="patient.idCard"
                    :cert-type.sync="patient.idCardType"
                    :width="inputWidth"
                    :disabled="disabled || disabledIdCard"
                    :is-disabled-cert-type="disabled || disabledIdCard"
                    @input-change="handleIdCardInput"
                    @select-change="handleIdCardSelect"
                    @enter="enterEvent"
                ></abc-certificates-type>
            </abc-form-item>

            <abc-form-item
                v-if="!regsHiddenAddress"
                label="住址"
                style="margin-bottom: 8px;"
                :label-width="labelWidth"
                :required="patientInfoRequireConfig.address.required"
            >
                <abc-address-selector
                    v-model="patient"
                    :width="inputWidth"
                    :disabled="disabled || disabledAddress"
                    :inner-customer-style="{
                        width: '410px', top: '4px'
                    }"
                    @enter="enterEvent"
                ></abc-address-selector>
            </abc-form-item>

            <abc-form-item
                v-if="!regsHiddenAddress"
                label=" "
                :label-width="labelWidth"
                :required="patientInfoRequireConfig.address.required"
            >
                <abc-input
                    v-model="patient.addressDetail"
                    :width="inputWidth"
                    :disabled="disabled || disabledAddressDetail"
                    placeholder="详细地址"
                    @enter="enterEvent"
                ></abc-input>
            </abc-form-item>

            <abc-form-item
                v-if="!regsHiddenProfession"
                label="职业"
                :label-width="labelWidth"
                :required="patientInfoRequireConfig.profession.required"
            >
                <abc-input
                    v-if="disabled || disabledProfession"
                    v-model="patient.profession"
                    :width="240"
                    disabled
                ></abc-input>
                <abc-select
                    v-else
                    v-model="patient.profession"
                    :width="inputWidth"
                    placeholder="请选择患者职业"
                    @enter="enterEvent"
                >
                    <abc-option
                        v-for="item in professionOptions"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    ></abc-option>
                </abc-select>
            </abc-form-item>

            <abc-form-item
                v-if="!regsHiddenCompany"
                label="工作单位"
                :required="patientInfoRequireConfig.company.required"
                :label-width="labelWidth"
            >
                <abc-input
                    v-model="patient.company"
                    :width="inputWidth"
                    type="text"
                    :max-length="128"
                    :placeholder="'请输入工作单位'"
                    :disabled="disabled || disabledCompany"
                    @enter="enterEvent"
                ></abc-input>
            </abc-form-item>

            <abc-form-item
                v-if="!regsHiddenSn"
                label="档案号"
                :label-width="labelWidth"
                :required="patientInfoRequireConfig.sn.required"
            >
                <abc-input
                    v-model="patient.sn"
                    :width="inputWidth"
                    :disabled="disabled || disabledSn || !isCanModifySnInCrm"
                    type="number-en-char"
                    :ignore-composing="true"
                    :max-length="16"
                    :placeholder="snPlaceholder"
                    @enter="enterEvent"
                ></abc-input>
            </abc-form-item>

            <abc-form-item
                v-if="!regsHiddenRemark"
                label="患者备注"
                :label-width="labelWidth"
                :required="patientInfoRequireConfig.remark.required"
            >
                <abc-input
                    v-model="patient.remark"
                    :width="inputWidth"
                    :disabled="disabled || disabledRemark"
                    type="text"
                    :max-length="300"
                    :placeholder="remarkPlaceholder"
                    @enter="enterEvent"
                ></abc-input>
            </abc-form-item>
        </template>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';

    import PatientsAPI from 'api/patients';
    import CrmAPI from 'api/crm';
    import clone from 'utils/clone';
    import {
        age2birthday, birthday2age,
    } from 'utils/index';
    import {
        validateAge, validateMobile,
    } from 'utils/validate';
    import { professionOptions } from '@/views/crm/data/options';

    import ViewLabels from 'views/crm/common/package-label/view-labels.vue';
    import BirthdayPicker from '../../birthday-picker/birthday-picker';
    import PatientAutocomplete from '../patient-autocomplete/patient-autocomplete';
    import PatientSocial from 'views/crm/common/package-social/index.vue';
    import BarcodeScanner from 'src/utils/scanner-barcode-detector.js';
    import TagItem from 'views/crm/patient-files/card-patient-overview/tag-item';
    import AbcTagsAdd from 'views/crm/component/abc-tags-add';
    import IdCardReaderService from 'views/layout/read-card/id-card-reader/id-card-reader-service';
    import { DEFAULT_CERT_TYPE } from 'views/crm/constants';

    const PatientCard = () => import('views/crm/common/package-card/index.vue');
    import { encryptMobile } from 'utils/crm';
    export default {
        name: 'PatientForm',
        components: {
            ViewLabels,
            BirthdayPicker,
            PatientAutocomplete,
            PatientCard,
            PatientSocial,
            TagItem,
            AbcTagsAdd,
        },

        props: {
            isScanCode: {
                type: Boolean,
                default: false,
            },
            isOpenScanCode: {
                type: Boolean,
                default: false,
            },
            patientScanCodeInfoId: { // 流行病登记面板
                type: [Number,String],
                default: 0,
            },
            disabled: Boolean,
            value: {
                type: Object,
                required: true,
            },
            block: {
                type: Object,
                required: false,
            }, // 每一个formitem 是块 还是行内

            // 是否显示医保刷卡标志
            enableSocialMark: {
                type: Boolean,
                default: false,
            },

            isMemberModule: {
                type: Boolean,
                default: false,
            },
            requiredMobile: {
                type: Boolean,
                default: false,
            },
            requiredSource: {
                type: Boolean,
                default: false,
            },
            requiredIdCard: {
                type: Boolean,
                default: false,
            },
            requiredCompany: {
                type: Boolean,
                default: false,
            },
            requireVisitSource: {
                type: Boolean,
                default: false,
            },
            patientSourceType: {
                type: Array,
            },
            regsHiddenIdCard: {
                type: Boolean,
            },
            regsHiddenCompany: {
                type: Boolean,
            },
            regsHiddenSource: {
                type: Boolean,
            },
            regsHiddenAddress: {
                type: Boolean,
            },
            regsHiddenProfession: {
                type: Boolean,
            },
            regsHiddenSn: {
                type: Boolean,
            },
            regsHiddenTags: {
                type: Boolean,
            },
            regsHiddenRemark: {
                type: Boolean,
            },
            patientId: {
                type: String,
                default: '',
            },
            // 社保卡信息
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            //
            patientOrderId: {
                type: String,
                default: '',
            },
            snPlaceholder: {
                type: String,
                default: '',
            },
            remarkPlaceholder: {
                type: String,
                default: '',
            },
            // 科室编码 - 成都电子健康卡获取信息需要
            departmentId: {
                type: String,
                default: '',
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                professionOptions,
                disabledName: false,
                disabledSex: false,
                disabledAge: false,
                disabledMobile: false,
                disabledIdCard: false,
                disabledCompany: false,
                disabledSource: false,
                disabledAddress: false,
                disabledAddressDetail: false,
                disabledProfession: false,
                disabledSn: false,
                disabledRemark: false,
                visibleBaseCard: false,
                visibleSocialInfo: false, // 医保刷卡信息
                showPopover: false,
                customLabelStyle: {
                    alignSelf: 'flex-start',
                    marginTop: '4px',
                },
                barcodeScanner: null,
                isModifyPatientMobile: false,
            };
        },
        computed: {
            ReadCardService() {
                return IdCardReaderService;
            },
            ...mapGetters(['userConfig', 'isEnableIdCardReader', 'isCanModifyNameInCrm', 'isCanModifyIdCardInCrm', 'isCanModifySnInCrm']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters('crm', ['crmConfigList']),
            labelWidth() {
                /**
                 * @desc 兼容 济华就诊来源label宽度
                 * <AUTHOR>
                 * @date 2021/4/13 9:57 上午
                 */
                return 70;
            },
            patientMobile: {
                get() {
                    const { mobile = '' } = this.patient;
                    if (this.isModifyPatientMobile || this.isCanSeePatientMobile) {
                        return mobile;
                    }
                    return encryptMobile(mobile);
                },
                set(val) {
                    this.isModifyPatientMobile = true;
                    this.patient.mobile = val;
                },
            },
            // 是否开启身份证和生日年龄联动 1开启 0不开启
            idCardLinkageSwitch() {
                return this.viewDistributeConfig.Registration.idCardLinkageSwitch;
            },
            inputWidth() {
                return 240;
            },
            options() {
                return (
                    this.patientSourceType &&
                    this.patientSourceType.map((item) => {
                        return {
                            id: item.id, name: item.name, childs: item.childs || [],
                        };
                    })
                );
            },
            placeholder() {
                if (!this.patient.source.id) {
                    return '不指定';
                }
                return '';
            },
            patient: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            selectedSource: {
                get() {
                    if (this.value.source && this.value.source.id === undefined) {
                        return [];
                    }
                    if (!this.value.source.id) {
                        return [];
                    }
                    return [
                        {
                            id: this.value.source.id,
                            name: this.value.source.name,
                        },
                        this.value.source.sourceFrom && this.value.source.sourceFromName ?
                            {
                                id: this.value.source.sourceFrom,
                                name: this.value.source.sourceFromName,
                            } :
                            null,
                    ];
                },
                set(val) {
                    this.patient.source.id = val[0] ? val[0].id : '';
                    this.patient.source.name = val[0] ? val[0].name : '';
                    this.patient.source.sourceFrom = (val[1] && val[1].id) || '';
                    this.patient.source.sourceFromName = (val[1] && val[1].name) || '';
                },
            },
            canEdit() {
                return !this.disabled && this.disabledName;
            },
            // 选择患者标签id
            selectedIds() {
                const tags = this.patient.tags || [];
                return tags.map((item) => item.tagId);
            },
            patientInfoRequireConfig() {
                const model = {
                    name: {
                        required: true,
                    },
                    sex: {
                        required: true,
                    },
                    age: {
                        required: true,
                    },
                    mobile: {
                        required: false,
                    },
                    birthday: {
                        required: false,
                    },
                    certificates: {
                        required: false,
                    },
                    sourceInfo: {
                        required: false,
                    },
                    marital: {
                        required: false,
                    },
                    weight: {
                        required: false,
                    },
                    profession: {
                        required: false,
                    },
                    company: {
                        required: false,
                    },
                    sn: {
                        required: false,
                    },
                    ethnicity: {
                        required: false,
                    },
                    address: {
                        required: false,
                    },
                    remark: {
                        required: false,
                    },
                    visitReason: {
                        required: false,
                    },
                    pastHistory: {
                        required: false,
                    },
                };
                console.log('this.crmConfigList', this.crmConfigList);

                if (this.crmConfigList && Object.keys(this.crmConfigList).length) {
                    Object.keys(model).forEach((key) => {
                        if (this.crmConfigList[key]) {
                            model[key].required = !!this.crmConfigList[key].required;
                        }
                    });
                }

                return model;
            },
        },
        async created() {
            if (this.patientId) {
                await this.setPatientId(this.patientId);
            }

            await this.$store.dispatch('fetchClinicBasic');
        },
        mounted() {
            if (this.isOpenScanCode) {
                // 扫码填入就诊信息
                this.barcodeScanner = new BarcodeScanner((barcode) => {
                    console.log('病人就诊卡',barcode);
                    if (this.disabled) {
                        return false;
                    }
                    this.scanCodeSearch(barcode);
                }, (barcode) => {
                    console.log('病人就诊卡',barcode);
                    return true;
                });
            }

        },

        methods: {
            validateMobile,
            validateAge,
            handleValidateMobile(values, callback) {
                const mobile = `${this.patient.mobile || ''}` ;
                this.validateMobile(mobile, callback);
            },
            /**
             * @desc 验证年龄月份
             * <AUTHOR>
             * @date 2018/10/18 12:29:01
             */
            validateMonth(value, callback) {
                if (!this.patient.age.month) {
                    callback({ validate: true });
                    return;
                }
                if (!/^(0?[1-9]|1[0-1])$/.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            getIdBirthday(data, len) {
                let arr;
                //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
                if (len === 15) {
                    const reg15 = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
                    arr = data.match(reg15);
                } else {
                    //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
                    const reg18 = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
                    arr = data.match(reg18);
                }
                const year = arr[2];
                const month = arr[3];
                const day = arr[4];
                const idBirthday = len === 15 ? `${`19${year}-${month}-${day}`}` : `${`${year}-${month}-${day}`}`;
                return idBirthday;
            },
            handleIdCardInput(val) {
                if (this.disabled || this.disabledAge) return;
                if (val) {
                    this.correctIdCard(val);
                }
            },
            handleIdCardSelect() {
                this.correctIdCard(this.patient.idCard);
            },
            // 校验身份证是否正确
            correctIdCard(idCard) {
                const len = idCard?.length;
                if (len === 15 || len === 18) {
                    const certType = this.patient.idCardType || DEFAULT_CERT_TYPE;
                    const certNo = idCard;
                    const values = [certType, certNo];
                    this._validateIdCard(values, (res) => {
                        const { validate } = res;
                        if (validate && certType === DEFAULT_CERT_TYPE) {
                            const birthDay = this.getIdBirthday(idCard,len);
                            this.patient.birthday = birthDay;// 校验通过覆盖生日
                            this.changeBirthday(birthDay); // 校验通过覆盖年龄
                            this.handleSex(idCard);
                        }
                    });
                }
            },
            _validateIdCard(values, callback) {
                const [certType = '', certNo = ''] = values;
                if (!this.patientInfoRequireConfig.certificates.required && !certNo) {
                    return callback({ validate: true });
                }
                if (!certNo) {
                    return callback({ validate: false });
                }
                return this.$refs?.['crm-id-card']?.validateCertNo(certType, certNo, callback);
            },
            /**
             * @desc 条形码输入控件失去焦点后，启用barcode检测，检测到输入后，将值输入并重新获取焦点，同时触发搜索
             * <AUTHOR>
             * @date 2019-03-14 14:22:54
             */
            focusBarcode() {
                if (!this.isOpenScanCode) {
                    return false;
                }
                this.barcodeScanner.startDetect();
            },
            blurBarcode() {
                if (!this.isOpenScanCode) {
                    return false;
                }
                window.setTimeout(() => {
                    this.barcodeScanner.destoryDetect();
                }, 200);

            },
            onVisibleBaseCardChange(value) { //开启编辑弹窗的时候去掉这个功能
                this.visibleBaseCard = value;
            },
            async initPatientScanCodeInfo() {
                try {
                    const { data } = await CrmAPI.fetchPatientOverview(this.patientScanCodeInfoId);
                    if (data) {
                        await this.selectPatient(data);
                    }
                } catch (e) {
                    console.log(e);
                }
            },
            async scanCodeSearch(barcode) {
                if (this.visibleBaseCard) {
                    return false;
                }
                if (barcode.length > 16) { // 扫码超过16位截断
                    barcode = barcode.substring(0,16);
                }
                try {
                    await this.$refs.autoComplete.clearPatient();
                    const { data } = await PatientsAPI.fetchPatientsByName(barcode);
                    data.list = data.list || [];
                    const list = data.list.map((item) => {
                        item.disabled = this.isMemberModule && +item.isMember === 1;
                        return item;
                    });
                    const patientInformation = list.find((item) => {return item.sn === barcode;});
                    if (patientInformation) {
                        const patient = patientInformation;
                        await this.selectPatient(patient);

                    } else {
                        const updateObj = {
                            regsHiddenSn: false,
                        };
                        await this.$store.dispatch('updateUserConfig', Object.assign(this.userConfig, updateObj));
                        this.disabledSn = false;
                        this.$nextTick(() => {
                            this.$emit('modifySn',barcode);
                            window.setTimeout(() => {
                                this.$refs.autoComplete.focusInput();
                            },200);
                        });
                    }
                } catch (e) {
                    console.log(e);
                }

            },

            /**
             * @desc 验证年龄天数
             * <AUTHOR>
             * @date 2018/10/18 12:29:01
             */
            validateDay(value, callback) {
                if (!this.patient.age.day) {
                    callback({ validate: true });
                    return;
                }
                if (!/^(0?[1-9]|1[0-9]|2[0-9]|30)$/.test(value)) {
                    callback({
                        validate: false,
                        message: '最多30天',
                    });
                } else {
                    callback({ validate: true });
                }
            },

            /**
             * @desc 选择患者
             * <AUTHOR>
             * @date 2018/10/18 12:36:10
             * @params patient
             * @params markShebaoCardInfo 是否标记社保刷卡挂号
             */
            async selectPatient(patient, markShebaoCardInfo = false) {
                if (!patient) return false;

                this.$emit('startChangePatient', {
                    originId: this.patient.id,
                    id: patient.id,
                });

                if (patient.id) {
                    const { data } = await CrmAPI.fetchPatientOverview(patient.id);
                    if (data) {
                        patient.patientSource = data.patientSource;
                        patient.idCard = data.idCard;
                        patient.idCardType = data.idCardType;
                        patient.company = data.company;
                        patient.address = data.address;
                        patient.profession = data.profession;
                        patient.sn = data.sn;
                        patient.isMember = data.memberFlag;
                        patient.remark = data.remark;
                        patient.isMemberFamily = data.isMemberFamily;

                        if (this.patient.memberInfo && data.memberInfo) {
                            Object.assign(this.patient.memberInfo, data.memberInfo);
                        }
                    }
                }

                // 去除age中的0
                const { age } = patient;
                if (+age.year === 0) {
                    age.year = null;
                }

                if (+age.month === 0) {
                    age.month = null;
                }

                this.patient.id = patient.id;
                this.patient.name = patient.name;
                this.patient.age = age;
                this.patient.sex = patient.sex;
                this.patient.birthday = patient.birthday;
                this.patient.mobile = patient.mobile;
                this.patient.isMember = patient.isMember;
                this.patient.idCard = patient.idCard;
                this.patient.idCardType = patient.idCardType;
                this.patient.company = patient.company;
                this.patient.profession = patient.profession;
                this.patient.sn = patient.sn;
                this.patient.tags = clone(patient.tags || []);
                this.patient.remark = patient.remark;
                this.patient.isMemberFamily = patient.isMemberFamily;
                this.patient.shebaoCardInfo = markShebaoCardInfo ? patient.shebaoCardInfo : null;

                if (patient.address) {
                    const {
                        addressCityId,
                        addressCityName,
                        addressDistrictId,
                        addressDistrictName,
                        addressProvinceId,
                        addressProvinceName,
                        addressDetail,
                    } = patient.address;

                    this.patient.addressCityId = addressCityId;
                    this.patient.addressCityName = addressCityName;
                    this.patient.addressDistrictId = addressDistrictId;
                    this.patient.addressDistrictName = addressDistrictName;
                    this.patient.addressProvinceId = addressProvinceId;
                    this.patient.addressProvinceName = addressProvinceName;
                    this.patient.addressDetail = addressDetail;
                } else {
                    // 清空 address
                    this.patient.addressCityId = null;
                    this.patient.addressCityName = null;
                    this.patient.addressDistrictId = null;
                    this.patient.addressDistrictName = null;
                    this.patient.addressProvinceId = null;
                    this.patient.addressProvinceName = null;
                    this.patient.addressDetail = null;
                }
                this.patient.patientSource = patient.patientSource || null;
                /**
                 * @desc 选出来的患者，不能修改信息，但是如果没有数据的话，可以填
                 * <AUTHOR>
                 * @date 2019/08/26 16:58:41
                 */
                // 选出来的患者除了姓名不可更改，其他可更改（受字段设置二期必填项影响）
                this.disabledName = !!patient.id;

                if (!patient.id) this.mrCount = 0;
                this.$emit('changePatient', this.patient);
            },
            /**
             * @desc 姓名和手机号 失焦后请求patientId
             * <AUTHOR>
             * @date 2018/10/22 22:20:56
             */
            async fetchPatient() {
                const {
                    id, name, mobile,
                } = this.patient;

                if (id || !name || !mobile) return false;

                const { data } = await PatientsAPI.fetchPatientByNameMobile(name, mobile);
                if (data) {
                    await this.selectPatient(data);
                }
            },

            /**
             * @desc 选择生日时间后，需要计算年龄
             * <AUTHOR>
             * @date 2019/09/02 14:34:47
             */
            changeBirthday(birthday) {
                if (!birthday) {
                    // 清空操作
                    this.patient.age.year = '';
                    this.patient.age.month = '';
                    this.patient.age.day = '';
                    return;
                }
                const {
                    year, month, day,
                } = birthday2age(birthday);
                this.patient.age.year = year || '';
                this.patient.age.month = month || '';
                if (!year && !month) {
                    this.patient.age.day = day || 0;
                } else {
                    this.patient.age.day = day || '';
                }
            },
            handleSex(val) {
                let genderCode;
                if (val.length === 15) {
                    genderCode = val.charAt(val.length - 1);
                } else {
                    genderCode = val.charAt(val.length - 2);
                }
                this.patient.sex = genderCode % 2 === 0 ? '女' : '男';
            },
            outside() {
                if (this.showPopover) {
                    this.showPopover = false;
                }
            },
            /**
             * 处理标签
             * <AUTHOR>
             * @date 2021-03-01
             * @param {Object} tag 目标标签，存在时删除，不存在时加入
             */
            onClickHandleTag(tag) {
                const id = tag.id || tag.tagId;
                const name = tag.name || tag.tagName;
                const patientCardRef = this.$refs['patient-card'];
                if (patientCardRef) {
                    patientCardRef.onSwitchTag({
                        id, name,
                    });
                } else {
                    if (this.selectedIds.includes(id)) {
                        this.patient.tags = this.patient.tags.filter((item) => item.tagId !== id);
                    } else {
                        this.patient.tags.push({
                            ...tag, tagId: id, tagName: name, editPermit: 1,
                        });
                    }
                }
            },

            /**
             * @desc 填写年龄后，计算生日
             * <AUTHOR>
             * @date 2019/09/02 21:29:11
             */
            changeAge() {
                this.patient.birthday = age2birthday(this.patient.age);
            },
            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                this.$nextTick(() => {
                    let targetIndex = -1;
                    const inputs = $('.dialog-content.registration-form .abc-input__inner').not(
                        ':disabled,.is-disabled',
                    );
                    for (let i = 0; i < inputs.length; i++) {
                        if ($(inputs[i]).hasClass('tag')) {
                            targetIndex = i;
                        }
                    }
                    if (inputs.index(e.target) > -1) {
                        targetIndex = inputs.index(e.target);
                    }
                    const nextInput = inputs[targetIndex + 1];
                    nextInput &&
                        this.$nextTick(() => {
                            nextInput.focus();
                        });
                });
            },

            /**
             * @desc 修改患者信息控件拉取到患者信息后回调接口
             * <AUTHOR>
             * @date 2019/10/22 21:41:32
             * @params
             * @return
             */
            onFetchInfo(patientInfo) {
                this.$emit('onFetchPatientInfo', patientInfo);
                this.selectPatient({
                    ...patientInfo,
                    isMember: patientInfo.memberFlag,
                });
            },
            /**
             * 当更改患者tags
             * <AUTHOR>
             * @date 2021-03-01
             * @param {Object} patientInfo 患者信息
             */
            onChangePatientTags(patientInfo) {
                this.patient.tags = clone(patientInfo.tags);
            },

            async setPatientId(patientId, markShebaoCardInfo) {
                const { data } = await CrmAPI.fetchPatientOverview(patientId);
                await this.selectPatient(data, markShebaoCardInfo);
            },

            async handleOpenReadPanel() {
                const {
                    patient,
                    isShebaoCard,
                    shebaoCardInfo,
                } = await IdCardReaderService.read({
                    departmentId: this.departmentId,
                    confirmText: this.$route.name.includes('registration') ? '开始挂号' : '开始接诊',
                });
                const { originCardInfo } = shebaoCardInfo || {};
                this.patient.originCardInfo = originCardInfo;
                if (patient && patient.id) {
                    this.setPatientId(patient.id, isShebaoCard);
                }
            },

            /**
             * 当点击补刷社保卡
             * <AUTHOR>
             * @date 2020-08-13
             */
            async onClickFillShebaoCardInfo() {
                await IdCardReaderService.read({
                    patientName: this.patient.name,
                    patientOrderId: this.patientOrderId,
                    fillShebaoCardInfo: true,
                    confirmText: this.$route.name.includes('registration') ? '开始挂号' : '开始接诊',
                });
                this.$emit('fetchDetail'); // 刷新挂号单详情
                const patientCardRef = this.$refs['patient-card'];
                patientCardRef && patientCardRef.fetchPatientOverview();
            },
        },
    };
</script>
