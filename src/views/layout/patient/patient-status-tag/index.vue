<template>
    <div class="patient-status-tag-wrapper">
        <abc-tag-v2
            size="tiny"
            :variant="variant || 'dark'"
            :icon="icon"
            :disabled="disabled"
            :theme="tagTheme || 'warning'"
        >
            {{ icon ? '' : showTagName }}
        </abc-tag-v2>
    </div>
</template>

<script>
    import { PatientOrderHospitalTagEnum } from '@/views-hospital/beds/utils/constant';

    export default {
        name: 'PatientStatusTag',
        props: {
            tagId: {
                type: String,
                default: '',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                patientStatus: [{
                    tagId: PatientOrderHospitalTagEnum.PREMIUM_CARE,
                    tagName: '特',
                    tagType: '病情相关状态',
                    tagTheme: 'danger',
                },{
                    tagId: PatientOrderHospitalTagEnum.PRIMARY_CARE,
                    tagName: 'I',
                    icon: 's-b-romani-line',
                    tagType: '病情相关状态',
                    tagTheme: 'danger',
                },{
                    tagId: PatientOrderHospitalTagEnum.SECONDARY_CARE,
                    tagName: 'II',
                    icon: 's-b-romanii-line',
                    tagType: '病情相关状态',
                    tagTheme: 'warning',
                },{
                    tagId: PatientOrderHospitalTagEnum.TERTIARY_CARE,
                    tagName: 'III',
                    icon: 's-b-romaniii-line',
                    tagType: '病情相关状态',
                    tagTheme: 'success',
                },{
                    tagId: PatientOrderHospitalTagEnum.HIGH_FALL_RISK,
                    tagName: '跌',
                    tagType: '病情相关状态',
                },{
                    tagId: PatientOrderHospitalTagEnum.MODERATE_FALL_RISK,
                    tagName: '跌',
                    tagType: '病情相关状态',
                },{
                    tagId: PatientOrderHospitalTagEnum.RISK_OF_PIPELINE_SLIPPING,
                    tagName: '管',
                    tagType: '病情相关状态',
                },{
                    tagId: PatientOrderHospitalTagEnum.CONDITION_WEI,
                    tagName: '危',
                    tagType: '病情相关状态',
                }, {
                    tagId: PatientOrderHospitalTagEnum.CONDITION_ZHONG,
                    tagName: '重',
                    tagType: '病情相关状态',
                },{
                    tagId: PatientOrderHospitalTagEnum.NEW_ENTRANTS,
                    tagName: '新',
                    tagType: '非病情状态',
                    tagTheme: 'success',
                },{
                    tagId: PatientOrderHospitalTagEnum.LEAVE_HOSPITAL,
                    tagName: '出',
                    tagType: '非病情状态',
                    tagTheme: 'success',
                },{
                    tagId: PatientOrderHospitalTagEnum.ARREARS,
                    tagName: '欠',
                    tagType: '非病情状态',
                    tagTheme: 'danger',
                    variant: 'light-outline',
                },{
                    tagId: PatientOrderHospitalTagEnum.WAIT_CHECKED,
                    tagName: '核',
                    tagType: '代办',
                    tagTheme: 'primary',
                    variant: 'outline',
                },{
                    tagId: PatientOrderHospitalTagEnum.WAIT_EXECUTE,
                    tagName: '执',
                    tagType: '代办',
                    tagTheme: 'primary',
                    variant: 'outline',
                }],
            };
        },
        computed: {
            selectedTag() {
                return this.patientStatus.find((item) => item.tagId === this.tagId) || {};
            },
            showTagName() {
                return this.selectedTag.tagName || '';
            },
            tagTheme() {
                return this.selectedTag.tagTheme || '';
            },
            variant () {
                return this.selectedTag.variant || '';
            },
            icon() {
                return this.selectedTag.icon;
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.patient-status-tag-wrapper {
    & + .patient-status-tag-wrapper {
        margin-left: 4px;
    }

    .tag {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: $S2;
        background-color: #ff9a1e;
        border-radius: var(--abc-border-radius-small);

        &.new,
        &.out {
            background-color: #00a541;
        }

        &.level {
            padding-top: 3px;
            font-family: MySTZhongsong;
            font-weight: bold;
            background-color: #f04a3e;
        }

        &.level_2 {
            padding-top: 3px;
            font-family: MySTZhongsong;
            font-weight: bold;
            background-color: #ff9933;
        }

        &.level_3 {
            padding-top: 3px;
            font-family: MySTZhongsong;
            font-weight: bold;
            background-color: #1ec761;
        }

        &.level_best {
            background-color: #ff3366;
        }

        &.arrears {
            background-color: #ff3029;
        }

        &.unchecked,
        &.unexecuted {
            color: #008dff;
            background-color: $S2;
            border: 1px solid #b0dcff;
        }

        &.not-mark {
            background-color: $P6;
        }

        &.show-as-text {
            width: auto;
            min-width: 14px;
            height: auto;
            padding: 0;
            font-size: 14px;
            color: #ff9a1e;
            background: transparent;
            border: none;

            &.new,
            &.out {
                color: #00a541;
            }

            &.arrears {
                color: #ff3029;
            }

            &.unchecked,
            &.unexecuted {
                color: #008dff;
            }

            &.active {
                color: $S2;
            }
        }
    }
}
</style>
