@import "src/styles/theme";

.patient-outpatient-history {
    position: relative;
    height: 100%;
    padding: 6px 0 6px 10px;
    overflow-y: scroll;
    font-size: 14px;
    background-color: #ffffff;

    @include scrollBar;

    > ul {
        li {
            position: relative;
            display: flex;
            align-items: center;
            padding: 0 12px;
            cursor: pointer;
            border-bottom: 1px solid var(--abc-color-P4);
            border-radius: var(--abc-border-radius-small);

            &:hover {
                background-color: var(--abc-color-P4);
            }

            > div {
                width: 100%;
                color: $T2;
                outline: none;
            }

            .abc-popover__reference {
                display: flex;
                align-items: center;
                height: 44px;
                padding: 7px 0 8px 0;
                outline: none;

                .diagnosis {
                    flex: 1;

                    @include ellipsis;
                }

                .has-external-file {
                    margin-right: 12px;
                    color: $T2;
                }

                .name {
                    min-width: 36px;
                    margin-right: 8px;
                    font-size: 12px;
                }

                .time {
                    width: 32px;
                    min-width: 32px;
                    max-width: 60px;
                    font-size: 12px;
                    text-align: right;
                }
            }

            &.selected {
                background-color: $P4;
            }
        }
    }

    > .no-data {
        position: absolute;
        top: 30%;
        left: 50%;
        height: auto;
        font-size: 14px;
        line-height: 1;
        color: $T3;
        text-align: center;
        transform: translateX(-50%);
    }

    .scroll-loading {
        display: flex;
        align-items: center;
        height: 32px;
        padding-left: 126px;
        font-size: 12px;
        color: $T2;

        &::after {
            display: inline-block;
            width: 0;
            overflow: hidden;
            vertical-align: bottom;
            content: "\2026";
            -webkit-animation: ellipsis steps(4, end) 900ms infinite;
            animation: ellipsis steps(4, end) 900ms infinite;
        }
    }

    .abc-tabs.abc-tabs-middle {
        height: 100%;
        border-bottom: none;
    }

    .ql-item-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;

        .img-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 22px;
            min-width: 22px;
            max-width: 22px;
            height: 22px;
            background: $G2;

            i {
                font-size: 12px;
                color: $S2;
            }
        }

        .status {
            line-height: 20px;
        }

        i {
            font-size: 14px;
            color: $T3;
        }
    }
}

.patient-info-outpatient-history-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-size: 14px;
    background-color: #ffffff;

    .cut-bar {
        width: 100%;
        height: 8px;
        min-height: 8px;
        max-height: 8px;
        background-color: $P5;
        border-top: 1px solid $P6;
        border-bottom: 1px solid $P6;
    }

    .patient-info-outpatient-history {
        overflow-y: auto;
        overflow-y: overlay;

        @include scrollBar();
    }

    .patient-info-card {
        display: flex;
        align-items: center;
        padding: 12px 16px 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid $P6;

        .patient-info {
            flex: 1;

            > p {
                display: flex;
                align-items: center;
                font-weight: 500;
                line-height: 20px;
                cursor: pointer;

                .sex,
                .age,
                .history-count {
                    font-size: 12px;
                }

                .sex {
                    margin-left: 12px;
                }

                .age {
                    margin-left: 4px;
                }

                .history-count {
                    margin-left: 24px;
                    color: $T3;
                }

                .iconfont {
                    margin-left: auto;
                    color: $T3;
                }
            }

            .patient-tags-wrapper {
                margin-top: 8px;
            }
        }

        .patient-detail-info {
            width: 38px;
            height: 100%;
        }

        .view-detail-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 38px;

            .iconfont {
                font-size: 18px;
                color: $P1;
            }
        }
    }

    .patient-info-tab {
        padding: 0 12px;
        border-bottom: 1px solid $P6;

        .abc-tabs-item {
            display: inline-flex;
            align-items: center;
            font-size: 14px;
        }

        .abc-tabs-number-dot {
            position: relative;
            top: 0;
            right: 0;
            left: 0;
            padding: 2px 0 0 2px;
            color: $T2;
            background-color: transparent;
        }
    }

    .outpatient-history-list {
        flex: 1;
        padding: 0 16px 0 12px;

        > ul {
            li {
                position: relative;
                display: flex;
                align-items: center;
                cursor: pointer;
                border-bottom: 1px dashed $P6;

                > div {
                    width: 100%;
                    color: $T2;
                    outline: none;
                }

                .patient-time-info {
                    margin-bottom: 4px;
                    font-size: 12px;
                    line-height: 16px;
                    color: $T2;

                    span + span {
                        margin-left: 8px;
                    }
                }

                .abc-popover__reference {
                    display: flex;
                    align-items: center;
                    height: auto;
                    padding: 12px 0;
                    outline: none;

                    .diagnosis {
                        flex: 1;

                        @include ellipsis;
                    }

                    .doctor-name {
                        font-size: 12px;
                    }

                    .time {
                        width: 60px;
                        min-width: 60px;
                        max-width: 60px;
                        font-size: 12px;
                        text-align: right;
                    }
                }

                &.selected {
                    background-color: $P4;
                }

                &.no-data {
                    height: 40px;
                    font-size: 12px;
                    line-height: 40px;
                }
            }
        }

        .scroll-loading {
            display: flex;
            align-items: center;
            height: 32px;
            padding-left: 126px;
            font-size: 12px;
            color: $T2;

            &::after {
                display: inline-block;
                width: 0;
                overflow: hidden;
                vertical-align: bottom;
                content: "\2026";
                -webkit-animation: ellipsis steps(4, end) 900ms infinite;
                animation: ellipsis steps(4, end) 900ms infinite;
            }
        }
    }

    .family-doctor-wrapper {
        .no-sign-wrapper {
            margin-top: 15%;
            text-align: center;

            img {
                height: 66px;
            }

            .tips {
                margin-top: 10px;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                color: $T3;
            }

            .sign-button {
                width: 180px;
                margin-top: 24px;
            }
        }

        .family-doctor-sign-content {
            padding: 12px;

            .row-line {
                display: flex;
                margin-top: 6px;

                .describe {
                    width: 68px;
                    color: $T2;
                }

                .content {
                    flex: 1;
                    width: 0;
                }

                &.warn {
                    display: flex;
                    align-items: center;
                    color: $Y2;
                }

                .iconfont {
                    margin-right: 4px;
                    color: $Y2;
                }
            }
        }
    }

    .btn-group {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 12px;

        .abc-button {
            height: 28px;
        }
    }
}
