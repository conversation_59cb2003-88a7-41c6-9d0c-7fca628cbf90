<template>
    <div class="crm-patient-item-container">
        <div v-if="hasMemberModule" class="li-line">
            <div v-if="!isMember" class="item" @click="hasMemberModule ? onClickHandles('handleInsertMember', patientInfo) : ()=>{}">
                <div class="left">
                    <abc-icon icon="s-vip-color" size="18px"></abc-icon>
                    <span class="name" style="color: #005ed9;">办理会员</span>
                </div>
            </div>
            <div v-else class="item" @click="hasMemberModule ? onClickMemberHandle('handleMemberCharge') : ()=>{}">
                <div class="left">
                    <abc-icon icon="s-vip-color" size="18px"></abc-icon>
                    <span class="name">{{ memberName }}</span>
                </div>
                <div class="right">
                    <span class="description">
                        余<abc-money :value="(memberInfo.principal + memberInfo.present) || 0"></abc-money>
                    </span>
                    <span class="icon iconfont cis-icon-Arrow_Rgiht"></span>
                </div>
            </div>
        </div>
        <div class="li-line" @click="isCanUpdateMemberPointsInCrm ? onClickMemberHandle('handleExchangeChargePoint') : ()=>{}">
            <div class="item">
                <div class="left">
                    <abc-icon icon="s-integral-color" size="18px"></abc-icon>
                    <span class="name">
                        积分
                    </span>
                </div>
                <div class="right">
                    <abc-text
                        theme="gray-light"
                        size="small"
                        style="cursor: pointer;"
                        class="description"
                    >
                        {{ points }}
                        {{ points && pointsClearConfig.isNeedWarn ? `(${pointsClearConfig.pointsClearTime}清零)` : '' }}
                    </abc-text>
                    <span v-if="hasPatientModule" class="icon iconfont cis-icon-Arrow_Rgiht"></span>
                </div>
            </div>
        </div>
        <div class="li-line" @click="isCanUpdateMemberCouponInCrm ? (couponsList.length ? onClickCouponHandle('handleCouponList') : onClickCouponHandle('handleDistributeCoupon')) : ()=>{}">
            <div class="item">
                <div class="left">
                    <abc-icon icon="s-coupon-color" size="18px"></abc-icon>
                    <span class="name" style="display: flex; align-items: center;">
                        <abc-space>
                            优惠券
                        </abc-space>
                    </span>
                </div>
                <div class="right">
                    <abc-text v-if="couponsList.length" theme="gray-light" class="ellipsis description">
                        {{ couponsListString }}
                    </abc-text>
                    <abc-text v-else theme="gray-light">
                        暂无优惠券
                    </abc-text>
                    <span class="icon iconfont cis-icon-Arrow_Rgiht"></span>
                </div>
            </div>
        </div>
        <div v-if="isOpenFamilyDoctor" class="li-line">
            <div class="item" @click="handleVisible('visibleFamilyDoctor')">
                <div class="left">
                    <abc-icon icon="s-doctor-2-color" size="18px"></abc-icon>
                    <span class="name">家庭医生</span>
                </div>
                <div class="right">
                    <template>
                        <span v-if="servicePackage" class="description">服务包：{{ servicePackage }}</span>
                        <span v-else class="description">还未签约家庭医生</span>
                        <span class="icon iconfont cis-icon-Arrow_Rgiht"></span>
                    </template>
                </div>
            </div>
        </div>
        <div v-if="isAllowUseChildHealth" class="li-line">
            <template v-if="bindHealthFiles.length">
                <div
                    v-for="item in bindHealthFiles"
                    :key="item.title"
                    class="item"
                    @click="onClickFilesDetail(item)"
                >
                    <div class="left">
                        <abc-icon icon="s-child-color" size="18px"></abc-icon>
                        <span class="name">儿保档案</span>
                    </div>
                    <div class="right">
                        <span class="description">{{ childFileInfo }}</span>
                        <span class="icon iconfont cis-icon-Arrow_Rgiht"></span>
                    </div>
                </div>
            </template>
            <template v-else>
                <div class="item" @click="handleVisible('visibleBindFiles')">
                    <div class="left">
                        <abc-icon icon="s-child-color" size="18px"></abc-icon>
                        <span class="name">儿保档案</span>
                    </div>
                    <div class="right">
                        <span class="description">添加健康档案</span>
                        <span class="icon iconfont cis-icon-Arrow_Rgiht"></span>
                    </div>
                </div>
            </template>
        </div>
        <template v-if="cardList.length">
            <div v-for="(item,index) in cardList" :key="item.id" class="li-line">
                <div class="item" :class="`item${index}`" @click="onClickCard(item)">
                    <div class="left">
                        <abc-icon icon="s-card-color" size="18px"></abc-icon>
                        <span class="name">{{ item.cardName }}</span>
                    </div>
                    <div class="right">
                        <span v-if="item.isCanUseInCurrentClinic" class="description">余额 <abc-money :value="item.cardBalance" :is-format-money="false"></abc-money></span>
                        <span v-else class="description">当前门店不可用</span>
                        <span class="icon iconfont cis-icon-Arrow_Rgiht"></span>
                    </div>
                </div>
            </div>
        </template>
        <div v-if="hasMemberCardAdminModule" class="li-line">
            <div class="item" @click="onClickCardholderHandle">
                <div class="left">
                    <abc-icon icon="s-add-color" size="18px"></abc-icon>
                    <span class="name" style="color: #005ed9;">办理新卡</span>
                </div>
            </div>
        </div>

        <dialog-child-health
            v-if="visibleChildHealth"
            v-model="visibleChildHealth"
            :patient-info="patientInfo"
            @update="fetchPatientOverview"
        ></dialog-child-health>
        <view-cardholder-handles
            ref="cardholder-handles"
            :need-hover-popover="false"
            @refresh="handleUpdateCardInfo"
            @changeCardHolderHandles="changeCardHolderHandles"
        ></view-cardholder-handles>
        <view-member-handles
            ref="member-handles"
            @fetch-patient-info="handleRefresh"
            @changeMemberHandles="changeMemberHandles"
        ></view-member-handles>
        <view-coupon-handles
            ref="coupon-handles"
            :coupons-list="couponsList"
            @submit-success="fetchObtainedCoupons"
            @changeCouponHolders="changeCouponHolders"
            @handleDistributeCoupon="onClickCouponHandle('handleDistributeCoupon')"
        ></view-coupon-handles>
        <view-member-family-manage
            ref="member-family-manage"
            :need-hover-popover="false"
            :disabled-member="true"
            @fetch-patient-info="handleRefresh"
            @fetch-patient-track="handleRefresh"
            @fetch-family-member="handleRefresh"
            @changeIsMemberFamilyOpen="changeIsMemberFamilyOpen"
        ></view-member-family-manage>

        <!--  传送到外层父级区域展示 -->
        <teleport :to="teleportValue">
            <dialog-bind-files
                v-if="visibleBindFiles"
                :value="visibleBindFiles"
                :patient-info="patientInfo"
                @input="handleCloseTeleportDialog('visibleBindFiles')"
                @success="handleBindFilesSuccess"
            ></dialog-bind-files>
            <dialog-family-doctor
                v-if="visibleFamilyDoctor"
                :value="visibleFamilyDoctor"
                :patient-info="patientInfo"
                @input="handleCloseTeleportDialog('visibleFamilyDoctor')"
                @unbind-success="fetchPatientOverview"
                @success="fetchPatientOverview"
            ></dialog-family-doctor>
            <dialog-card-info
                v-if="visibleCardInfo"
                ref="card-info"
                :support-one-click-billing="supportOneClickBilling"
                :value="visibleCardInfo"
                :allow-recharge-card="allowRechargeCard"
                :card="card"
                :patient-info="patientInfo"
                :card-list="cardList"
                :max-height="maxHeight"
                @input="handleCloseTeleportDialog('visibleCardInfo')"
                @fetch-card-info="handleUpdateCardInfo"
            ></dialog-card-info>
        </teleport>
    </div>
</template>

<script>
    import MixinModulePermission from 'views/permission/module-permission';
    import AbcAccess from '@/access/utils';
    import { mapGetters } from 'vuex';
    import ViewMemberFamilyManage from 'views/crm/common/package-info/view-member-family-manage.vue';
    const ViewCardholderHandles = () => import('views/crm/common/package-cardholder/view-cardholder-handles.vue');
    import ViewMemberHandles from 'views/crm/common/package-member/view-member-handles.vue';
    import CrmAPI from 'api/crm';
    import Teleport from 'views/layout/patient/common/teleport.vue';
    import { groupBy } from '@/utils';

    export default {
        name: 'CrmPatientItemContainer',
        components: {
            Teleport,
            ViewMemberHandles,
            ViewCardholderHandles,
            ViewMemberFamilyManage,
            DialogBindFiles: () => import('views/crm/common/package-card/dialog-bind-files.vue'),
            DialogChildHealth: () => import('views/crm/common/package-card/dialog-child-health.vue'),
            ViewCouponHandles: () => import('views/crm/common/package-coupon/index.vue'),
            DialogFamilyDoctor: () => import('views/crm/common/package-card/dialog-family-doctor.vue'),
            DialogCardInfo: () => import('views/crm/common/package-card/dialog-card-info.vue'),
        },
        mixins: [
            MixinModulePermission,
        ],
        props: {
            patientId: {
                type: String,
                default: '',
            },
            patientBasicInfo: {
                type: Object,
                default: () => ({}),
            },
            showLiItem: {
                type: Boolean,
                default: false,
            },
            maxHeight: {
                type: Number,
            },
            // 是否支持一键开出卡项剩余项目功能
            supportOneClickBilling: {
                type: Boolean,
                default: false,
            },
            teleportValue: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                visibleBindFiles: false, // 可选健康档案列表
                visibleChildHealth: false, // 儿保健康档案详情
                visibleFamilyDoctor: false, // 家庭医生详情
                visibleCardInfo: false, // 卡详情
                cardList: [],
                card: null,
                currentCardItem: null,
                params: {
                    wx: 1,
                    childCareRecords: 1,
                    promotionCardList: 1,
                    showFamilyDoctor: 1,
                },
                couponsList: [],
                couponsArr: [],
            };
        },
        watch: {
            visibleFamilyDoctor(val) {
                this.$emit('visible-family-doctor', val);
            },
            visibleBindFiles(val) {
                this.$emit('visible-bind-files', val);
            },
            visibleCardInfo(val) {
                this.$emit('visible-card-info', val);
            },
            visibleChildHealth(val) {
                this.$emit('visible-child-health', val);
            },
        },
        computed: {
            ...mapGetters(['isOpenMp', 'isEnableChildHealth',
                           'isCanUpdateMemberPointsInCrm',
                           'isCanUpdateMemberCouponInCrm']),
            ...mapGetters('crm', [
                'pointsClearConfig',
            ]),
            patientInfo: {
                get() {
                    return this.patientBasicInfo || {};
                },
                set(val) {
                    this.$emit('update:patientBasicInfo', val);
                },
            },
            // 是否允许充值卡 需要有收费权限或者会员卡管理权限
            allowRechargeCard() {
                return this.hasChargeModule || this.hasMemberCardAdminModule;
            },

            isOpenFamilyDoctor() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.FAMILY_DOCTOR);
            },
            isAllowUseChildHealth() {
                return this.isEnableChildHealth === 1 && this.patientInfo?.age?.year < 18;
            },
            isMember() {
                return this.patientInfo?.isMember;
            },
            memberInfo() {
                return this.patientInfo?.memberInfo || {};
            },
            memberName() {
                return this.memberInfo.memberTypeInfo?.memberTypeName || '普通患者';
            },
            servicePackage() {
                return this.patientInfo?.familyDoctorAbstract?.servicePackName;
            },
            points() {
                return this.patientBasicInfo?.points || 0;
            },
            // 已经绑定的健康档案
            bindHealthFiles() {
                const healthFiles = [];
                const {
                    childCareArchives,
                    childCareRecords,
                } = this.patientInfo || {};
                if (childCareArchives === 1) {
                    // 已关联儿童健康档案
                    let growthCont = '无';
                    if (childCareRecords && childCareRecords.bodyGrowthData) {
                        // 生长记录
                        const {
                            bmi,
                            headSize,
                            height,
                            isBregmaClose,
                            lowerBodyLength,
                            teeth,
                            upperBodyLength,
                            weight,
                        } = childCareRecords.bodyGrowthData;
                        const strs = [];
                        height && strs.push(`身高${height}cm`);
                        weight && strs.push(`体重${weight}kg`);
                        bmi && strs.push(`BMI ${bmi}`);
                        headSize && strs.push(`头围${headSize}cm`);
                        upperBodyLength && strs.push(`上部量${upperBodyLength}cm`);
                        lowerBodyLength && strs.push(`下部量${lowerBodyLength}cm`);
                        isBregmaClose && strs.push(`前囟${isBregmaClose === 1 ? '开启' : '闭合'}`);
                        teeth && strs.push(`牙齿${teeth}颗`);

                        growthCont = strs.join('/');
                    }
                    let evaluationCont = '发育评测：无';
                    if (childCareRecords && childCareRecords.evaluationData) {
                        const {
                            evaluationName,
                            result,
                        } = childCareRecords.evaluationData;
                        const scores = result.map(
                            (item) => `${parseFloat(item.score)}分/${parseFloat(item.totalScore)}分`,
                        );
                        evaluationCont = `${evaluationName}：${scores.join(' ')}`;
                    }
                    const item = {
                        type: 'child',
                        title: '儿童健康档案',
                        contents: [`生长记录：${growthCont}`, evaluationCont],
                    };
                    healthFiles.push(item);
                }
                return healthFiles;
            },
            childFileInfo() {
                const {
                    childCareArchives,
                    childCareRecords,
                } = this.patientInfo || {};
                if (childCareArchives === 1) {
                    // 已关联儿童健康档案
                    if (childCareRecords?.bodyGrowthData) {
                        // 生长记录
                        const {
                            height,
                            weight,
                        } = childCareRecords.bodyGrowthData;
                        return `身高 ${height || 0}cm，体重 ${weight || 0}kg`;
                    }
                }
                return '暂无记录';
            },
            couponsListString() {
                const arr = groupBy(this.couponsList, (item) => item.name);
                return arr.map((item) => {
                    return `${item[0]?.name || ''}(${item.length})`;
                }).join('、');
            },
        },
        created() {
            this.fetchPatientCardsInfo();
            this.fetchObtainedCoupons();
        },
        methods: {
            async fetchPatientOverview() {
                try {
                    const patientId = this.patientInfo?.id || this.patientId;
                    if (patientId) {
                        const { data } = await CrmAPI.fetchPatientOverview(patientId, this.params);
                        this.patientInfo = Object.assign({}, this.patientInfo, data);
                    }
                } catch (error) {
                    console.log('fetchPatientOverview error', error);
                }
            },
            onClickCardholderHandle() {
                if (!AbcAccess.check(AbcAccess.accessMap.MARKET_CARD)) {
                    return;
                }
                const memberHandlesNode = this.$refs['cardholder-handles'];
                memberHandlesNode?.handleCardholderCreate(this.patientInfo, {}, this.patientInfo?.promotionCardPatientListView?.rows || []);
            },
            onClickHandles(handleFunc, ...args) {
                const memberFamilyManage = this.$refs['member-family-manage'];
                memberFamilyManage?.[handleFunc]?.(...args);
            },
            // 有收费权限或者会员管理权限：完成积分，会员相关操作
            // 有收费权限或者卡项管理权限：卡项充值操作
            // 有卡项管理权限：新增卡权限
            // 积分和会员操作 需要有会员卡权限或收费权限 产品：徐颖 2025年03月13日
            onClickMemberHandle(handleFunc) {
                if (['handleExchangeChargePoint', 'handleMemberCharge'].includes(handleFunc) && !this.hasMemberModule) {
                    return;
                }
                const memberHandlesNode = this.$refs['member-handles'];
                memberHandlesNode?.[handleFunc]?.(this.patientInfo);
            },
            onClickCouponHandle(handleFunc, type = 'add') {
                if (!AbcAccess.check(AbcAccess.accessMap.MARKET_COUPON)) {
                    return;
                }

                const couponHandlesNode = this.$refs['coupon-handles'];
                couponHandlesNode?.[handleFunc]?.(this.patientInfo, type);
            },
            async fetchObtainedCoupons() {
                this.couponsArr = [];
                const {
                    id,
                    chainId,
                } = this.patientInfo;
                try {
                    if (!id) return;
                    const { data = {} } = await CrmAPI.fetchObtainedCoupons(chainId, id, 0, 1000);
                    if (id !== this.patientInfo?.id) return;
                    this.couponsList = data?.rows || [];
                    const arr = groupBy(this.couponsList, (item) => item.name);
                    arr.forEach((item) => {
                        const obj = {
                            ...item[0], len: item.length,
                        };
                        this.couponsArr.push(obj);
                    });
                } catch (e) {
                    console.log(e);
                }
            },
            handleVisible(params) {
                this.$emit('handle-common');
                this.$nextTick(() => {
                    this.$data[params] = true;
                });
            },
            /**
             * 当点击查看健康档案详情
             * <AUTHOR>
             * @date 2020-07-01
             * @param {Object} item
             */
            onClickFilesDetail(item) {
                if (item.type === 'child') {
                    this.visibleChildHealth = true;
                }
            },
            async onClickCard(item) {
                if (!item.isCanUseInCurrentClinic) {
                    return;
                }
                await this.fetchPatientCardDetail(item);
                this.handleVisible('visibleCardInfo');
            },
            async fetchPatientCardDetail(item) {
                this.currentCardItem = item;
                if (!item?.id) return;
                try {
                    const { data } = await CrmAPI.fetchPatientCardDetail(item.id);
                    const {
                        cardInfo,
                        patientPresents,
                        cardBalance,
                        isOverdue = false,
                    } = data;

                    this.card = {
                        ...cardInfo,
                        id: item.id,
                    };

                    this.card.patientPresents = patientPresents;
                    this.card.cardBalance = cardBalance || 0;
                    this.card.isOverdue = isOverdue;
                } catch (e) {
                    console.log(e);
                }
            },
            handleCloseTeleportDialog(type) {
                this.$data[type] = false;
                this.$emit('update:showLiItem', false);
            },
            handleBindFilesSuccess() {
                this.visibleBindFiles = false;
                this.$emit('update:showLiItem', false);
                this.fetchPatientOverview();
            },
            async fetchPatientCardsInfo() {
                try {
                    const patientId = this.patientInfo?.id || this.patientId;
                    const { data } = await CrmAPI.fetchPatientCardsInfo(patientId) || {};
                    this.cardList = data?.rows || [];
                } catch (e) {
                    console.log(e);
                }
            },
            async handleUpdateCardInfo() {
                const patientId = this.patientInfo?.id || this.patientId;
                await Promise.all([
                    this.fetchPatientCardDetail(this.currentCardItem),
                    this.fetchPatientCardsInfo(patientId),
                ]);
            },
            changeCardHolderHandles(value) {
                this.$emit('change-card-holder-handles', value);
            },
            changeMemberHandles(value) {
                this.$emit('change-member-handles', value);
            },
            changeCouponHolders(value) {
                this.$emit('change-coupon-handles',value);
            },
            changeIsMemberFamilyOpen(value) {
                this.$emit('change-is-member-family-open',value);
            },
            handleRefresh() {
                this.$emit('handle-refresh');
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

.crm-patient-item-container {
    .li-line {
        padding: 0 6px;
        margin: 0 -6px;

        &:last-child {
            margin-bottom: 0 !important;
        }

        .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 36px;

            .left {
                display: inline-flex;
                align-items: center;

                img {
                    width: 20px;
                    height: 20px;
                }

                .name {
                    max-width: 200px;
                    height: 100%;
                    margin-left: 12px;
                    overflow: hidden;
                    font-size: 13px;
                    font-weight: 500;
                    line-height: 1;
                    color: $S1;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            .flex {
                display: flex;
                align-items: center;
                justify-content: space-around;
                width: 100%;
                height: 100%;
            }

            .right {
                display: inline-flex;
                align-items: center;
                justify-content: right;
                width: 300px;

                .description {
                    margin-right: 6px;
                    font-size: 13px;
                    font-weight: 400;
                    color: $T2;
                }

                .cis-icon-Arrow_Rgiht {
                    font-size: 18px;
                    color: $P1;
                }
            }
        }
    }

    .li-line:hover {
        cursor: pointer;
        background: $P4;
        border-radius: var(--abc-border-radius-mini);
        box-shadow: 0 -1px 0 0 $P4;
    }
}
</style>
