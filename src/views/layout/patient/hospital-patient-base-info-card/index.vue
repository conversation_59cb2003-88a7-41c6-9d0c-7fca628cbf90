<template>
    <div>
        <div
            v-if="showPatientHospitalCard"
            class="patient-hospital-info-card-popover"
            :class="{ 'show-animation': showAnimation }"
        >
            <div class="tags-desc-info">
                <abc-tag-group class="thr clearfix">
                    <div
                        v-for="tag in showTags"
                        :key="tag.tagId"
                    >
                        <tag-item
                            :tag="tag"
                            @click-close-label="clickCloseLabel"
                        ></tag-item>
                    </div>
                    <abc-popover
                        ref="abc-pop-tag"
                        trigger="manual"
                        :value="showTagPopover"
                        :visible-arrow="false"
                        popper-class="patient-label-tags__popper"
                        placement="bottom-start"
                        theme="white"
                        z-index="1993"
                    >
                        <abc-tags-add slot="reference" style="height: 20px;" @click.native="showTagPopover = !showTagPopover"></abc-tags-add>
                        <div v-abc-click-outside="outside" class="box">
                            <view-labels
                                :show-manage="false"
                                :selected-ids="selectedIds"
                                :filter="false"
                                :disabled-hover="true"
                                @change="clickHandleTag"
                            ></view-labels>
                        </div>
                    </abc-popover>
                </abc-tag-group>
                <div class="desc">
                    <span>{{ baseInfo && formatDate(baseInfo.inpatientTime,'YYYY-MM-DD HH:mm') }}入院</span>
                    <span>{{ baseInfo && baseInfo.inpatientDays ? `，在院${baseInfo.inpatientDays}天` : '' }}</span>
                    <span class="times"> {{ baseInfo && baseInfo.times ? `，${inpatientCount}` : '' }}</span>
                    <inpatient-history-popover v-if="inpatientHistory.length" :inpatient-history="inpatientHistory" :z-index="1993">
                        <span class="popover-reference">住院历史</span>
                    </inpatient-history-popover>
                </div>
                <div class="text-line"></div>
            </div>
            <div v-if="!editor" class="display-view">
                <div class="content-wrapper">
                    <div class="patient-info-wrapper" :class="{ 'patient-info-wrapper_large': isSupportTherapist }">
                        <div class="title-btn-wrapper">
                            <div class="title">
                                患者资料
                            </div>
                            <abc-button
                                class="operation-btn"
                                type="blank"
                                size="small"
                                @click="handleEdit"
                            >
                                编辑
                            </abc-button>
                        </div>
                        <div class="patient-info">
                            <div class="item single-item">
                                <label>证件</label>
                                <div class="value ellipsis">
                                    {{ patientBaseInfo.idCard ? `[${ patientBaseInfo.idCardType || DEFAULT_CERT_TYPE }]` : '' }} {{ patientBaseInfo.idCard }}
                                </div>
                            </div>
                            <div class="item">
                                <label>生日</label>
                                <div class="value">
                                    {{ patientBaseInfo.birthday }}
                                </div>
                            </div>
                            <div class="item">
                                <label>来源</label>
                                <div class="value ellipsis">
                                    <div v-abc-title.ellipsis="showPatientSource" class="value ellipsis"></div>
                                </div>
                            </div>
                            <div class="item">
                                <label>档案号</label>
                                <div class="value">
                                    {{ patientBaseInfo.sn }}
                                </div>
                            </div>
                            <div class="item">
                                <label>民族</label>
                                <div class="value">
                                    {{ patientBaseInfo.ethnicity }}
                                </div>
                            </div>
                            <div class="item">
                                <label>婚姻</label>
                                <div class="value">
                                    {{ getMarital(patientBaseInfo.marital) }}
                                </div>
                            </div>
                            <div class="item">
                                <label>国籍</label>
                                <div class="value">
                                    {{ nationalityText }}
                                </div>
                            </div>
                            <div class="item">
                                <label>身高</label>
                                <div class="value">
                                    {{ patientBaseInfo.height || '' }} {{ patientBaseInfo.height ? 'cm' : '' }}
                                </div>
                            </div>
                            <div class="item">
                                <label>体重</label>
                                <div class="value">
                                    {{ patientBaseInfo.weight || '' }} {{ patientBaseInfo.weight ? 'kg' : '' }}
                                </div>
                            </div>
                            <div class="item">
                                <label>邮箱</label>
                                <div class="value">
                                    {{ patientBaseInfo.email }}
                                </div>
                            </div>
                            <div class="item">
                                <label>人群分类</label>
                                <div class="value">
                                    {{ patientBaseInfo.crowdCategory }}{{ (patientBaseInfo.crowdCategoryRemark && patientBaseInfo.crowdCategory === '其他') ? `(${patientBaseInfo.crowdCategoryRemark})` : '' }}
                                </div>
                            </div>
                            <div class="item">
                                <label>职业</label>
                                <div class="value">
                                    {{ patientBaseInfo.profession }}
                                </div>
                            </div>
                            <div class="item">
                                <label>单位</label>
                                <div class="value ellipsis">
                                    {{ patientBaseInfo.company }}
                                </div>
                            </div>
                            <div class="item">
                                <label>家长名</label>
                                <div class="value">
                                    {{ patientBaseInfo.parentName || '' }}
                                </div>
                            </div>
                            <div class="item">
                                <label>到店</label>
                                <div class="value">
                                    {{ patientBaseInfo.visitReason || '' }}
                                </div>
                            </div>
                            <div class="item">
                                <label>建档</label>
                                <div class="value">
                                    {{ patientBaseInfo.created && parseTime(patientBaseInfo.created, 'y-m-d', true) || '' }}
                                </div>
                            </div>
                            <div class="item">
                                <label>医保号</label>
                                <div class="value">
                                    {{ patientBaseInfo.shebaoCardInfo && patientBaseInfo.shebaoCardInfo.cardNo }}
                                </div>
                            </div>

                            <template v-if="isSupportTherapist">
                                <div class="item">
                                    <label>首评治疗师</label>
                                    <div v-abc-title.ellipsis="patientBaseInfo.primaryTherapistName || ''" class="value ellipsis"></div>
                                </div>
                                <div class="item">
                                    <label>责任治疗师</label>
                                    <div v-abc-title.ellipsis="patientBaseInfo.dutyTherapistName || ''" class="value ellipsis"></div>
                                </div>
                            </template>
                            <div class="item">
                                <label>手机号</label>
                                <div class="value">
                                    {{ patientBaseInfo.mobile }}
                                </div>
                            </div>
                            <div class="item single-item">
                                <label>住址</label>
                                <div class="value address">
                                    {{ showAddress }}
                                </div>
                            </div>
                            <div class="item">
                                <label>备注</label>
                                <div v-abc-title.ellipsis="patientBaseInfo.remark" class="value ellipsis"></div>
                            </div>
                            <!-- <div class="item single-item">
                                <label>联系人</label>
                                <div class="value">
                                    {{ patientBaseInfo.contactName }}
                                    <span v-if="patientBaseInfo.contactRelation">({{ patientBaseInfo.contactRelation }})</span>
                                    <span style="margin-left: 16px;">{{ patientBaseInfo.contactMobile }}</span>
                                </div>
                            </div> -->
                        </div>
                        <div class="text-line"></div>
                    </div>
                    <div class="inpatient-info-wrapper">
                        <div class="inpatient-info-title">
                            住院信息
                        </div>
                        <div class="inpatient-info">
                            <div class="item">
                                <label>科室</label>
                                <div class="value">
                                    {{ baseInfo.departmentName }}
                                </div>
                            </div>
                            <div class="item">
                                <label>病区</label>
                                <div class="value ellipsis">
                                    {{ baseInfo.wardName }}
                                </div>
                            </div>
                            <div class="item">
                                <label>床位</label>
                                <div class="value bed-no">
                                    <span>{{ baseInfo.bedNo }}</span>
                                    <bed-transfer-record-popover
                                        v-if="bedTransferRecordList.length"
                                        :bed-transfer-record-list="bedTransferRecordList"
                                        :z-index="1993"
                                    >
                                        <span class="record">转移记录</span>
                                    </bed-transfer-record-popover>
                                </div>
                            </div>
                            <div class="item">
                                <label>入院途径</label>
                                <div class="value ellipsis">
                                    {{ baseInfo.inpatientSource && inpatientSourceObj[baseInfo.inpatientSource] || '' }}
                                </div>
                            </div>
                            <div class="item">
                                <label>入院诊断</label>
                                <div v-abc-title.ellipsis="inHospitalDiagnosisInfo" class="value ellipsis"></div>
                            </div>
                            <div class="item">
                                <label>入院病情</label>
                                <div class="value">
                                    {{ baseInfo.inpatientCondition && inpatientConditionObj[baseInfo.inpatientCondition] || '' }}
                                </div>
                            </div>
                            <div class="item">
                                <label>护理等级</label>
                                <div class="value">
                                    {{ nurseOfCareStr }}
                                </div>
                            </div>
                            <div class="item">
                                <label>门诊医生</label>
                                <div class="value">
                                    {{ baseInfo.outpatientDoctorName }}
                                </div>
                            </div>
                            <div class="item">
                                <label>住院医生</label>
                                <div class="value">
                                    {{ baseInfo.doctorName }}
                                </div>
                            </div>
                            <div class="item">
                                <label>主管护士</label>
                                <div class="value ellipsis">
                                    {{ baseInfo.nurseName }}
                                </div>
                            </div>
                            <div class="item last-item">
                                <label>住院号</label>
                                <div v-abc-title.ellipsis="baseInfo.no" class="value ellipsis"></div>
                            </div>
                            <div class="item last-item">
                                <label>费别</label>
                                <div class="value">
                                    {{ baseInfo.feeTypeName }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="showBtnFooter && !isDischarged" class="btn-wrapper">
                    <abc-space>
                        <abc-button
                            v-if="showNurseLevelBtn"
                            type="blank"
                            size="small"
                            @click="addNurseLevel()"
                        >
                            护理等级
                        </abc-button>
                        <abc-button
                            v-if="showTransferBtn"
                            type="blank"
                            size="small"
                            @click="addAdvice('transfer')"
                        >
                            转科
                        </abc-button>
                        <abc-button
                            v-if="showOutHospitalBtn"
                            type="blank"
                            size="small"
                            @click="addAdvice('leave')"
                        >
                            出院
                        </abc-button>
                        <abc-button
                            v-if="showOutHospitalBtn"
                            type="blank"
                            size="small"
                            @click="addAdvice('transfer-hospital')"
                        >
                            转院
                        </abc-button>
                    </abc-space>
                </div>
            </div>
            <div v-else class="edit-view">
                <div class="title-btn-wrapper">
                    <div class="title">
                        编辑患者
                    </div>
                    <div class="modify-btn">
                        <abc-button
                            type="primary"
                            class="save-btn"
                            size="small"
                            :disabled="noChangeData"
                            :loading="saveLoading"
                            @click="handleSave"
                        >
                            保存
                        </abc-button>
                        <abc-button type="blank" size="small" @click="handleCancel">
                            取消
                        </abc-button>
                    </div>
                </div>
                <abc-form
                    ref="postData"
                    label-position="left"
                    item-no-margin
                    :label-width="50"
                >
                    <div class="header-info">
                        <abc-space :size="4">
                            <div class="text">
                                <abc-form-item label="" required>
                                    <abc-input
                                        v-model.trim="postData.name"
                                        class="patient-name"
                                        :width="110"
                                        :max-length="40"
                                        :distinguish-half-angle-length="true"
                                        trim
                                        type="text"
                                        placeholder="新患者"
                                        :disabled="!isCanModifyNameInCrm"
                                        @enter="enterEvent"
                                    ></abc-input>
                                </abc-form-item>
                            </div>
                            <div class="text patient-sex">
                                <abc-form-item>
                                    <abc-select
                                        v-model="postData.sex"
                                        :width="52"
                                        custom-class="sex-select"
                                        size="tiny"
                                        @enter="enterEvent"
                                    >
                                        <abc-option value="男" label="男"></abc-option>
                                        <abc-option value="女" label="女"></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </div>
                            <div class="text patient-age-info">
                                <abc-space is-compact compact-block :border-style="false">
                                    <abc-form-item
                                        label=""
                                        show-red-dot
                                        :required="!secondInputAge && secondInputAge !== 0"
                                        :validate-event="firstUnit === '月' ? validateMonth : ()=>{}"
                                    >
                                        <abc-input
                                            v-model.number="firstInputAge"
                                            v-abc-focus-selected
                                            :width="29"
                                            :input-custom-style="{
                                                padding: '6px 0 6px 2px !important', 'text-align': 'center'
                                            }"
                                            type="number"
                                            :config="{
                                                supportZero: true, max: firstMax
                                            }"
                                            @enter="enterEvent"
                                            @input="changeAge"
                                        >
                                        </abc-input>
                                    </abc-form-item>
                                    <abc-form-item>
                                        <abc-select
                                            v-model="firstUnit"
                                            :width="22"
                                            size="tiny"
                                            custom-class="unit-select"
                                            class="unit-select"
                                            @change="handleUnit"
                                            @enter="enterEvent"
                                        >
                                            <abc-option
                                                v-for="it in firstUnitArray"
                                                :key="it.id"
                                                :label="it.name"
                                                :value="it.name"
                                            >
                                            </abc-option>
                                        </abc-select>
                                    </abc-form-item>
                                    <abc-form-item
                                        :required="!firstInputAge && firstInputAge !== 0"
                                        :validate-event="firstUnit === '岁' ? validateMonth : validateDay"
                                    >
                                        <abc-input
                                            v-model.number="secondInputAge"
                                            v-abc-focus-selected
                                            :width="26"
                                            class="age-input__wrapper"
                                            type="number"
                                            :config="{
                                                supportZero: true,max: secondMax
                                            }"
                                            @enter="enterEvent"
                                            @input="changeAge"
                                        >
                                            <span slot="append">{{ secondUnit }}</span>
                                        </abc-input>
                                    </abc-form-item>
                                </abc-space>
                            </div>
                            <div class="text mobile">
                                <abc-form-item label="" :required="requireConfig.mobile.required" :validate-event="validateMobile">
                                    <abc-input
                                        v-model.trim="postData.mobile"
                                        :width="139"
                                        :max-length="11"
                                        trim
                                        type="phone"
                                        placeholder="手机号"
                                        @enter="enterEvent"
                                    ></abc-input>
                                </abc-form-item>
                            </div>
                        </abc-space>
                    </div>
                    <div class="text-line split-line"></div>
                    <div class="info-box">
                        <abc-form-item label="证件" :required="requireConfig.certificates.required" :validate-event="_validateIdCard">
                            <abc-certificates-type
                                ref="crm-id-card"
                                v-model.trim="postData.idCard"
                                :cert-type.sync="postData.idCardType"
                                :disabled="!isCanModifyIdCardInCrm"
                                :is-disabled-cert-type="!isCanModifyIdCardInCrm"
                                :cert-type-width="78"
                                :width="367"
                                size="tiny"
                                :cert-type-adaptive-width="true"
                                :max-slice-len="7"
                                @enter="enterEvent"
                                @input="handleIdCardInput"
                            >
                            </abc-certificates-type>
                        </abc-form-item>
                        <abc-form-item label="生日" :required="requireConfig.birthday.required">
                            <birthday-picker
                                v-model="postData.birthday"
                                :width="144"
                                size="tiny"
                                :clearable="false"
                                :editable="true"
                                @change="changeBirthday"
                                @enter="enterEvent"
                            >
                            </birthday-picker>
                        </abc-form-item>
                        <abc-form-item label="来源" :required="requireConfig.sourceInfo.required">
                            <abc-cascader
                                ref="visit-source-cascader"
                                v-model="cascaderValue"
                                :props="{
                                    children: 'children',
                                    label: 'name',
                                    value: 'id'
                                }"
                                :width="144"
                                panel-width="154px"
                                :clearable="false"
                                size="tiny"
                                placeholder=""
                                separation="-"
                                :options="sourceList"
                                @enter="enterEvent"
                            >
                                <div class="visit-source-edit-wrapper">
                                    <abc-icon
                                        v-if="isClinicAdmin"
                                        icon="set"
                                        size="14"
                                        color="#94979B"
                                        class="icon"
                                        @click="handleVisitSourceEdit"
                                    ></abc-icon>

                                    <abc-popover
                                        v-else
                                        trigger="hover"
                                        placement="top-start"
                                        :popper-style="{
                                            zIndex: 99999
                                        }"
                                        theme="yellow"
                                    >
                                        <abc-icon
                                            slot="reference"
                                            icon="set"
                                            size="14"
                                            color="#94979B"
                                        ></abc-icon>

                                        <span>修改本次推荐请联系管理员</span>
                                    </abc-popover>
                                </div>
                            </abc-cascader>
                        </abc-form-item>
                        <abc-form-item label="档案号" :required="requireConfig.sn.required">
                            <abc-input
                                v-model="postData.sn"
                                :max-length="16"
                                :width="144"
                                type="input"
                                size="tiny"
                                :disabled="!isCanModifySnInCrm"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="民族" :required="requireConfig.ethnicity.required">
                            <abc-select
                                v-model="postData.ethnicity"
                                :width="144"
                                :max-height="234"
                                size="tiny"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in ethnicityOptions"
                                    :key="item"
                                    :value="item"
                                    :label="item"
                                    :width="144"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                        <abc-form-item label="婚姻" :required="requireConfig.marital.required">
                            <abc-select
                                v-model="postData.marital"
                                :width="144"
                                :max-height="234"
                                size="tiny"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in maritalOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                    :width="144"
                                    :panel-max-height="200"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                        <abc-form-item label="国籍" :required="requireConfig.nationality.required">
                            <abc-select
                                v-model="postData.nationality"
                                :width="144"
                                :max-height="234"
                                size="tiny"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in nationalityOptions"
                                    :key="item.label"
                                    :value="item.label"
                                    :label="item.label"
                                    :width="144"
                                    :panel-max-height="200"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                        <abc-form-item label="身高" :required="requireConfig.height.required">
                            <abc-input
                                v-model="postData.height"
                                type="number"
                                :config="{
                                    formatLength: 2,
                                    max: 300
                                }"
                                size="tiny"
                                :width="144"
                                @enter="enterEvent"
                            >
                                <span slot="appendInner">cm</span>
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="体重" :required="requireConfig.weight.required">
                            <abc-input
                                v-model="postData.weight"
                                type="number"
                                :config="{
                                    formatLength: 2,
                                    max: 1000
                                }"
                                size="tiny"
                                :width="144"
                                @enter="enterEvent"
                            >
                                <span slot="appendInner">kg</span>
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="人群<br/>分类" :show-red-dot="requireConfig.crowdCategory.required">
                            <abc-space
                                is-compact
                                compact-block
                                border-style="solid"
                            >
                                <abc-form-item :required="requireConfig.crowdCategory.required">
                                    <abc-select
                                        v-model="postData.crowdCategory"
                                        :width="postData.crowdCategory === '其他' ? 52 : 144"
                                        size="tiny"
                                        :max-height="234"
                                        :inner-width="144"
                                        @enter="enterEvent"
                                    >
                                        <abc-option
                                            v-for="item in populationAttributeOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                            :panel-max-height="200"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item v-if="postData.crowdCategory === '其他'" :required="requireConfig.crowdCategory.required">
                                    <abc-input
                                        v-model="postData.crowdCategoryRemark"
                                        size="tiny"
                                        :width="92"
                                        :max-length="60"
                                    ></abc-input>
                                </abc-form-item>
                            </abc-space>
                        </abc-form-item>
                        <abc-form-item label="职业" :required="requireConfig.profession.required">
                            <abc-select
                                v-model="postData.profession"
                                :width="144"
                                size="tiny"
                                :max-height="234"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in professionOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                    :width="144"
                                    :panel-max-height="200"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                        <abc-form-item label="邮箱" :validate-event="validateEmail" :required="requireConfig.email.required">
                            <abc-input
                                v-model="postData.email"
                                :width="144"
                                type="input"
                                size="tiny"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="单位" :required="requireConfig.company.required">
                            <abc-input
                                v-model="postData.company"
                                :max-length="128"
                                :width="144"
                                type="input"
                                size="tiny"
                                :config="{
                                    max: 1000,
                                    formatLength: 2,
                                }"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="家长名">
                            <abc-input
                                v-model="postData.parentName"
                                :width="144"
                                type="input"
                                size="tiny"
                                :max-length="40"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="到店" :required="requireConfig.visitReason.required">
                            <abc-input
                                v-model="postData.visitReason"
                                :width="144"
                                type="input"
                                size="tiny"
                            >
                            </abc-input>
                        </abc-form-item>


                        <abc-form-item label="建档">
                            <birthday-picker
                                v-model="postData.created"
                                :width="144"
                                placeholder=""
                                :disabled="true"
                                size="tiny"
                            >
                            </birthday-picker>
                        </abc-form-item>
                        <abc-form-item label="医保号">
                            <abc-input
                                v-model="postData.shebaoCardInfo.cardNo"
                                :width="144"
                                type="input"
                                disabled
                                size="tiny"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <template v-if="isSupportTherapist">
                            <abc-form-item
                                label="首评<br/>治疗师"
                                class="left-item"
                                :required="false"
                                label-wrap
                            >
                                <abc-select
                                    v-model="postData.primaryTherapistId"
                                    size="tiny"
                                    :width="144"
                                    with-search
                                    :fetch-suggestions="handleSearchEmployee"
                                    :show-value="postData.primaryTherapistName"
                                    @change="handlePrimaryTherapistChange"
                                >
                                    <abc-option
                                        v-for="item in currentEmployeeList"
                                        :key="item.employeeId"
                                        :value="item.employeeId"
                                        :label="item.employeeName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-form-item
                                label="责任<br/>治疗师"
                                class="right-item"
                                :required="false"
                                label-wrap
                            >
                                <abc-select
                                    v-model="postData.dutyTherapistId"
                                    size="tiny"
                                    :width="144"
                                    with-search
                                    :fetch-suggestions="handleSearchEmployee"
                                    :show-value="postData.dutyTherapistName"
                                    @change="handleDutyTherapistChange"
                                >
                                    <abc-option
                                        v-for="item in currentEmployeeList"
                                        :key="item.employeeId"
                                        :value="item.employeeId"
                                        :label="item.employeeName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </template>

                        <abc-form-item label="住址" :required="requireConfig.address.required">
                            <abc-address-selector
                                v-model="postData.address"
                                :width="367"
                                @enter="enterEvent"
                            ></abc-address-selector>
                        </abc-form-item>
                        <abc-form-item :label="''" class="address-detail-item" :required="requireConfig.address.required">
                            <abc-input
                                v-model="postData.addressDetail"
                                :width="367"
                                type="input"
                                size="tiny"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="备注" :required="requireConfig.remark.required">
                            <abc-input
                                v-model="postData.remark"
                                :max-length="300"
                                :width="367"
                                type="input"
                                size="tiny"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <!-- <abc-form-item label="联系人" hidden-red-dot required>
                            <abc-input
                                v-model="postData.contactName"
                                :width="144"
                                type="input"
                                size="tiny"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="关系" hidden-red-dot required>
                            <abc-select
                                v-model="postData.contactRelation"
                                :width="144"
                                size="tiny"
                                :max-height="234"
                                :fetch-suggestions="handleSearchRelationOptions"
                                with-search
                                class="select-contact-relation"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in currentRelationOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                        <abc-form-item
                            label="电话"
                            hidden-red-dot
                            required
                            class="last-form-item"
                            :validate-event="validateMobile"
                        >
                            <abc-input
                                v-model="postData.contactMobile"
                                :width="144"
                                :max-length="11"
                                trim
                                type="phone"
                                size="tiny"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item> -->
                    </div>
                </abc-form>
            </div>
        </div>
        <transfer-department-dialog
            v-if="isTransferDepartmentDialog"
            v-model="isTransferDepartmentDialog"
            :transfer-to-department="adviceCurrent.transferToDepartment"
            :remark="adviceCurrent.remark"
            @transfer="transfer"
        ></transfer-department-dialog>
        <transfer-hospital-dialog
            v-if="isShowTransferHospitalDialog"
            v-model="isShowTransferHospitalDialog"
            :advice.sync="adviceCurrent"
            :cur-start-time="advice.startTime"
            @discharge="discharge"
        >
        </transfer-hospital-dialog>
        <discharge-hospital-dialog
            v-if="isShowDischargeHospitalDialog"
            v-model="isShowDischargeHospitalDialog"
            :advice.sync="adviceCurrent"
            :cur-start-time="advice.startTime"
            @discharge="discharge"
        ></discharge-hospital-dialog>
        <add-medial-diagnosis-dialog
            v-if="isShowDiagnosisDialog"
            v-model="isShowDiagnosisDialog"
            :patient="patient"
            :patient-order-id="inPatientInfo.id"
            :doctor="doctor"
            :existed-diagnosis-list="diagnosisTableData"
            :default-diagnosis-type="defaultDiagnosisType"
        ></add-medial-diagnosis-dialog>
        <nurse-level-dialog
            v-if="isShowNurseLevelDialog"
            v-model="isShowNurseLevelDialog"
            :goods-list="adviceGoodsList"
            :nurse-level="nurseOfCareStr"
            @confirm="handleCreateNurseLevelAdvice"
        ></nurse-level-dialog>

        <!-- 就诊来源管理弹窗 -->
        <visit-source-dialog
            v-if="isShowVisitSourceDialog"
            :is-show.sync="isShowVisitSourceDialog"
            :patient-source-type="patientSourceType"
            @close="isShowVisitSourceDialog = false"
        ></visit-source-dialog>
    </div>
</template>

<script>
    import Clone from 'utils/clone';
    import { formatDate } from '@abc/utils-date';
    import { DEFAULT_CERT_TYPE } from '@/views/crm/constants';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { RecommendService } from '@/service/recommend';
    import InpatientHistoryPopover from '@/views-hospital/beds/components/inpatient-history-popover';
    import BedTransferRecordPopover from '@/views-hospital/beds/components/bed-transfer-record-popover';
    import {
        inpatientConditionObj,
        inpatientSourceObj,
        inpatientWayObj,
        PatientOrderHospitalTagEnum,
    } from '@/views-hospital/beds/utils/constant';
    import {
        mapGetters, mapState, mapActions,
    } from 'vuex';
    import CrmAPI from 'api/crm';
    import ViewLabels from 'views/crm/common/package-label/view-labels';
    import AbcTagsAdd from 'views/crm/component/abc-tags-add';
    import clone from 'utils/clone';
    import {
        age2birthday, birthday2age, getMarital, parseTime,
    } from '@/utils';
    import {
        validateMobile,
        validateEmail,
    } from 'utils/validate';
    import {
        contactRelationOptions, maritalOptions, professionOptions,populationAttributeOptions,
    } from 'views/crm/data/options';
    import ethnicityOptions from '@/assets/configure/nation-row.js';
    import nationalityOptions from '@/assets/configure/nationality.js';
    import BirthdayPicker from 'views/layout/birthday-picker/birthday-picker';
    import { isEqual } from 'utils/lodash';
    import TransferDepartmentDialog
        from '@/views-hospital/medical-prescription/components/transfer/transfer-department-dialog';
    import { MedicalPrescriptionService } from '@/views-hospital/medical-prescription/model';
    import { getFreqInfo } from '@/views-hospital/medical-prescription/utils/format-advice.js';
    import {
        DischargeTypeEnum, MedicalAdviceTypeEnum, CustomTypeIdEnum,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import CdssAPI from 'api/cdss';
    import {
        GoodsTypeEnum,
        GoodsTypeIdEnum,
        GoodsSubTypeEnum,
    } from '@abc/constants';
    import DischargeHospitalDialog
        from '@/views-hospital/medical-prescription/components/discharge/discharge-hospital-dialog';
    import TransferHospitalDialog
        from '@/views-hospital/medical-prescription/components/discharge/transfer-hospital-dialog';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import { HospitalStatusEnum } from '@/views-hospital/register/utils/constants';
    import { DiagnosisTypeEnum } from '@/views-hospital/medical-prescription/model/medical-diagnosis';
    import AddMedialDiagnosisDialog from '@/views-hospital/medical-prescription/components/add-medial-diagnosis-dialog';
    import NurseLevelDialog from '@/views-hospital/medical-prescription/components/nurse-level/nurse-level-dialog.vue';
    import GoodsV3API from 'api/goods/index-v3';
    import TagItem from 'views/crm/patient-files/card-patient-overview/tag-item';
    import MixinModulePermission from 'views/permission/module-permission';
    import VisitSourceDialog from 'views/registration/visit-source-dialog/index.vue';
    import { convertEmptyStringToNull } from '@/utils/convert-empty-string-to-null.js';
    import { filterEmployeeBySearchVal } from 'views/crm/common/filter';
    export default {
        name: 'PatientHospitalInfoCard',
        components: {
            VisitSourceDialog,
            ViewLabels,
            AbcTagsAdd,
            BirthdayPicker,
            InpatientHistoryPopover,
            BedTransferRecordPopover,
            TransferDepartmentDialog,
            DischargeHospitalDialog,
            AddMedialDiagnosisDialog,
            NurseLevelDialog,
            TagItem,
            TransferHospitalDialog,
        },
        mixins: [
            MixinModulePermission,
        ],
        provide() {
            return {
                main: this,
            };
        },
        props: {
            doctor: {
                type: Object,
                default: () => ({}),
            },
            patient: {
                type: Object,
                default: () => ({}),
            },
            status: {
                type: Number,
                default: () => HospitalStatusEnum.WAIT_DISCHARGE,
            },
            value: {
                type: Boolean,
                required: true,
            },
            inPatientInfo: {
                type: Object,
                default: () => ({}),
            },
            // 是否展示开转科医嘱btn
            showTransferBtn: {
                type: Boolean,
                required: false,
            },
            // 是否展示开出院医嘱btn
            showOutHospitalBtn: {
                type: Boolean,
                required: false,
            },
            // 是否能开护理等级
            showNurseLevelBtn: {
                type: Boolean,
                required: false,
            },
            showMask: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                DEFAULT_CERT_TYPE,
                populationAttributeOptions,
                HospitalStatusEnum,
                formatDate,
                maritalOptions,
                professionOptions,
                contactRelationOptions,
                ethnicityOptions,
                nationalityOptions,
                inpatientWayObj,
                inpatientSourceObj,
                inpatientConditionObj,
                editor: false,
                postData: {},
                showAnimation: false,
                baseInfo: {},
                inpatientHistory: [],
                bedTransferRecordList: [],
                selectOption: false,
                showTagPopover: false,
                firstInputAge: '',
                firstUnit: '岁',
                secondInputAge: '',
                secondUnit: '月',
                firstMax: 199,
                secondMax: 11,
                cascaderValue: [],
                sourceList: [], // 首诊来源数据
                saveLoading: false,
                adviceCurrent: null,
                advice: null,
                isTransferDepartmentDialog: false,
                isShowDischargeHospitalDialog: false,
                isShowTransferHospitalDialog: false,
                defaultDiagnosisType: null,
                isShowDiagnosisDialog: false,
                diagnosisTableData: [],
                adviceGoodsList: [],
                isShowNurseLevelDialog: false,
                isShowVisitSourceDialog: false,
                patientSourceType: [],
                searchRelationKey: '',
                employeeSearchValue: '',
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'employeeList',
                'isCanModifyNameInCrm',
                'isCanModifyIdCardInCrm',
                'isCanModifySnInCrm',
                'enableTherapist',
            ]),
            ...mapState('crm', [
                'originLabels', // 全部标签
            ]),
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters('crm', [
                'crmConfigList',
            ]),
            modifyCrmNameConfirmText() {
                return this.viewDistributeConfig.CRM.modifyCrmNameConfirmText;
            },
            nationalityText() {
                return this.patientBaseInfo.nationality || '';
            },
            requireConfig() {
                const model = {
                    name: {
                        required: true,
                    },
                    age: {
                        required: true,
                    },
                    sex: {
                        required: true,
                    },
                    mobile: {
                        required: true,
                    },
                    profession: {
                        required: false,
                    },
                    birthday: {
                        required: false,
                    },
                    address: {
                        required: false,
                    },
                    ethnicity: {
                        required: false,
                    },
                    weight: {
                        required: false,
                    },
                    remark: {
                        required: false,
                    },
                    visitReason: {
                        required: false,
                    },
                    marital: {
                        required: false,
                    },
                    sourceInfo: {
                        required: false,
                    },
                    certificates: {
                        required: false,
                    },
                    company: {
                        required: false,
                    },
                    sn: {
                        required: false,
                    },
                    pastHistory: {
                        required: false,
                    },
                    email: {
                        required: false,
                    },
                    crowdCategory: {
                        required: false,
                    },
                    height: {
                        required: false,
                    },
                    nationality: {
                        required: false,
                    },

                };
                for (const key in model) {
                    if (model.hasOwnProperty(key)) {
                        model[key].required = !!this.crmConfigList?.[key]?.required || false; // 修改 required 的值
                    }
                }
                return model;
            },
            isDischarged() {
                return this.status === this.HospitalStatusEnum.DISCHARGE;
            },
            showBtnFooter() {
                return this.showTransferBtn || this.showOutHospitalBtn;
            },
            cardInfoIsModified() {
                return this.editor && !this.noChangeData;
            },
            // 是否有修改信息
            noChangeData() {
                const postData = clone(this.postData);
                const cachePostData = clone(this.cachePostData);
                if (postData?.address) {
                    postData.address = convertEmptyStringToNull(postData?.address);
                }
                if (cachePostData?.address) {
                    cachePostData.address = convertEmptyStringToNull(cachePostData?.address);
                }
                if (['',null].includes(postData?.addressDetail)) {
                    postData.addressDetail = null;
                }
                if (['',null].includes(cachePostData?.addressDetail)) {
                    cachePostData.addressDetail = null;
                }
                return isEqual(postData, cachePostData);
            },
            firstUnitArray() {
                return [{
                    id: 1,
                    name: '岁',
                }, {
                    id: 2,
                    name: '月',
                }];
            },
            // 患者标签展示，首先处理标签数据方便查找，再兼容id、tagId这种后端问题，得到有效的患者标签
            showTags() {
                let tags = [];
                this.originLabels?.forEach((item) => {
                    if (item.tags) {
                        tags = [...tags, ...item.tags];
                    }
                });
                if (this.patientBaseInfo?.tags) {
                    return this.patientBaseInfo.tags
                        .map((item) => {
                            const target = tags.find((one) => one.id === item.tagId);
                            if (target) {
                                return {
                                    ...item,
                                    tagName: target.name,
                                };
                            }
                            return false;

                        })
                        .filter((item) => item !== false);
                }
                return [];

            },
            // 已经选中的标签ids
            selectedIds() {
                return this.showTags.map((item) => item.tagId);
            },
            showPatientHospitalCard: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input',val);
                },
            },
            patientBaseInfo() {
                return this.baseInfo?.patient || {};
            },
            showPatientSource() {
                if (!this.patientBaseInfo?.patientSource) return '';

                return this.initCascaderData(this.patientBaseInfo?.patientSource).map((o) => o.label).join(' - ');
            },
            showAddress() {
                const {
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                    addressDetail,
                } = this.patientBaseInfo?.address || {};
                const arr = [];
                if (addressProvinceName) arr.push(addressProvinceName);
                if (addressCityName) arr.push(addressCityName);
                if (addressDistrictName) arr.push(addressDistrictName);
                if (arr.length !== 0) {
                    return `${arr.join('/')}  ${addressDetail || ''}`;
                }
                return '';
            },
            nurseOfCareStr() {
                const {
                    PREMIUM_CARE,PRIMARY_CARE, SECONDARY_CARE, TERTIARY_CARE,
                } = PatientOrderHospitalTagEnum;
                const arr = [PREMIUM_CARE,PRIMARY_CARE, SECONDARY_CARE, TERTIARY_CARE];
                let str = '';
                this.baseInfo?.tags?.forEach((item) => {
                    if (arr.includes(item.id)) {
                        str = item.name;
                    }
                });
                return str || '';
            },
            inHospitalDiagnosisInfo() {
                let str = '';
                this.baseInfo?.primaryDiagnosisInfos?.[0]?.value?.forEach((item) => {
                    str += item.name;
                });
                return str;
            },
            currentRelationOptions() {
                if (!this.searchRelationKey) {
                    return this.contactRelationOptions;
                }
                return this.contactRelationOptions.filter((option) => option.label.includes(this.searchRelationKey));
            },
            inpatientCount() {
                let str = '入院：';
                const {
                    timesOfYear,
                    times,
                    inpatientYear,
                } = this.baseInfo || {};
                if (timesOfYear) {
                    str += `${inpatientYear}年第${timesOfYear}次/`;
                }
                return times > 0 ? `${str}累计第${times}次` : 0;
            },
            isSupportTherapist() {
                return this.viewDistributeConfig.CRM.isSupportTherapist && this.enableTherapist;
            },
            currentEmployeeList() {
                const { employeeSearchValue } = this;
                return filterEmployeeBySearchVal(employeeSearchValue, this.employeeList);
            },
        },
        watch: {
            cascaderValue(val) {
                if (val.length) {
                    if (val.length > 2) {
                        this.postData.sourceId = val[val.length - 2].value;
                        this.postData.sourceFrom = val[val.length - 1] ? val[val.length - 1].value : null;
                    } else if (['顾客推荐', '员工推荐', '医生推荐','转诊医生'].includes(val[0].label)) {
                        this.postData.sourceId = val[0].value;
                        this.postData.sourceFrom = val[1] ? val[1].value : null;
                    } else {
                        this.postData.sourceId = val[val.length - 1].value;
                        this.postData.sourceFrom = null;

                    }
                } else {
                    this.postData.sourceId = null;
                    this.postData.sourceFrom = null;
                }
            },
            showTagPopover: {
                handler(val) {
                    this.selectOption = val;
                },
                immediate: true,
                deep: true,
            },
            inPatientInfo: {
                handler(val) {
                    this.initData(val);
                },
                immediate: true,
                deep: true,
            },
            // 卡片信息修改
            cardInfoIsModified: {
                handler(val) {
                    this.$emit('update:showMask', val);
                },
                immediate: true,
            },
        },
        beforeDestroy() {
            this.showPatientHospitalCard = false;
            if (this._timer) {
                clearTimeout(this._timer);
                this._timer = null;
            }
        },
        created() {
            this.fetchInHospitalList(this.baseInfo?.patient?.id);
            this.fetchBedTransferRecords(this.inPatientInfo?.id);
            this.$store.dispatch('initCrmPermission');
            this.fetchCrmConfigList();
        },
        methods: {
            validateMobile,
            validateEmail,
            parseTime,
            getMarital,
            ...mapActions('crm', ['fetchCrmConfigList']),
            async initData(val) {
                await this.getListSource();
                this.baseInfo = Clone(val) || {};
                this.$nextTick(() => {
                    this.postData = this.getPostData();
                    this.cachePostData = Clone(this.postData);
                });
            },
            async getDiagnosisList() {
                try {
                    const { outpatientOrderId = '' } = this;
                    const list = await MedicalPrescriptionAPI.getDiagnosisList({
                        patientOrderId: this.inPatientInfo?.id,
                        outpatientOrderId,
                    });
                    if (list?.length) {
                        this.diagnosisTableData = list;
                    }

                } catch (err) {
                    console.log(err);
                }
            },
            clickCloseLabel(tag) {
                this.clickHandleTag({
                    id: tag.tagId,
                    name: tag.tagName,
                });
            },
            async batchCreate() {
                this.advice.advices[0].adviceRule = this.adviceCurrent;
                const postData = {
                    adviceGroups: [this.advice],
                    patientOrderId: this.inPatientInfo?.id,
                    examApplySheetReqs: [],//检验医嘱需要传
                };
                try {
                    const { data } = await MedicalPrescriptionAPI.batchCreate(postData);
                    if (data) {
                        this.showPatientHospitalCard = false;
                        this.$emit('create');
                    }

                } catch (e) {
                    if (e.code === 49998 || e.code === 49997) {
                        const title = e.code === 49998 ? '未下出院诊断' : '未下入院诊断';
                        this.defaultDiagnosisType = e.code === 49998 ? DiagnosisTypeEnum.OUT_HOSPITAL : DiagnosisTypeEnum.IN_HOSPITAL;
                        this.$alert({
                            type: 'warn',
                            title,
                            content: e.message,
                            onClose: async () => {
                                await this.getDiagnosisList();
                                this.isShowDiagnosisDialog = true;
                            },
                        });
                    }
                }
            },
            async addAdvice(type = 'transfer') {
                let goods = null;
                try {
                    const params = {
                        clinicId: this.currentClinic.clinicId,
                        key: '',
                        jsonType: type === 'transfer' ? [{
                            type: GoodsTypeEnum.LEAVE_HOSPITAL, subType: [GoodsSubTypeEnum[GoodsTypeEnum.LEAVE_HOSPITAL].TRANS],
                        }] : [{
                            type: GoodsTypeEnum.LEAVE_HOSPITAL, subType: [GoodsSubTypeEnum[GoodsTypeEnum.LEAVE_HOSPITAL].LEAVE],
                        }],
                        cMSpec: '',
                        sex: '',
                        age: {},
                        offset: 0,
                        limit: 10,
                        withDomainMedicine: 1,
                    };
                    const res = await GoodsV3API.searchGoods(params);

                    if (type === 'transfer') {
                        goods = res?.data?.list[0] || null;
                    } else if (type === 'transfer-hospital') {
                        goods = res?.data?.list.find((item) => {
                            return item.name === '转院出院' || item.displayName === '转院出院';
                        }) || null;
                    } else {
                        goods = res?.data?.list.find((item) => {
                            return item.name === '明日出院' || item.displayName === '明日出院';
                        }) || null;
                    }

                } catch (e) {
                    console.log(e);
                } finally {
                    await this.selectItem(goods, type);
                }

            },
            async addNurseLevel() {
                try {
                    const params = {
                        clinicId: this.currentClinic.clinicId,
                        keyword: '护理',
                        cMSpec: '',
                        sex: '',
                        age: {},
                        offset: 0,
                        limit: 100,
                        withDomainMedicine: 0,
                        jsonTypeWithCustomTypeList: [ {
                            customTypeId: CustomTypeIdEnum.NURSE_NURSE_LEVEL,
                        }],
                        searchSystemGoods: 1,
                    };
                    const { data } = await GoodsV3API.searchGoods(params);
                    this.adviceGoodsList = data?.list;
                    this.isShowNurseLevelDialog = true;
                } catch (e) {
                    this.adviceGoodsList = [];
                }

            },
            async fetchMedicineUsages(goods, patient) {
                try {
                    const { data } = await CdssAPI.fetchMedicineUsage({
                        medicineCadn: goods.medicineCadn,
                        goodsId: goods.goodsId,
                        type: goods.type,
                        patientInfo: patient,
                    });
                    return data;
                } catch (e) {
                    console.log('获取推荐用法失败', e);
                }
            },
            async selectItem(goods, type) {
                if (!goods) {
                    return;
                }
                const { typeId } = goods;
                const groupItem = MedicalPrescriptionService.getGroupItem(goods);

                if (goods.name === DischargeTypeEnum.TOMORROW || goods.displayName === DischargeTypeEnum.TOMORROW) {
                    groupItem.startTime = formatDate(new Date(groupItem.startTime).getTime() + (24 * 60 * 60 * 1000), 'YYYY-MM-DD HH:mm');
                    groupItem.advices[0].adviceRule.stopLongAdviceTime = groupItem.startTime;
                }
                if (typeId === GoodsTypeIdEnum.MEDICINE_WESTERN || typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT) {
                    const data = await this.fetchMedicineUsages(goods, this.patient);
                    if (data) {
                        groupItem.usage = data.usage;
                        groupItem.freq = data.freq;
                        groupItem.freqInfo = getFreqInfo(this.allExecuteTimeList, groupItem.freq);
                        groupItem.advices[0].adviceRule.dosageCount = data.unitCount;
                        groupItem.advices[0].adviceRule.dosageUnit = data.unit;
                        if (groupItem.type === MedicalAdviceTypeEnum.LONG_TIME) {
                            groupItem.advices[0].adviceRule.dosageCount = '';
                            groupItem.advices[0].adviceRule.dosageUnit = '';
                        }
                        groupItem.advices[0].adviceRule.singleDosageCount = data.dosage;
                        groupItem.advices[0].adviceRule.singleDosageUnit = data.dosageUnit;
                        groupItem.advices[0].adviceRule.ivgtt = data.ivgtt;
                        groupItem.advices[0].adviceRule.ivgttUnit = data.ivgttUnit;
                    }
                }
                this.advice = groupItem;
                this.adviceCurrent = groupItem.advices[0].adviceRule;
                if (type === 'transfer') {
                    this.isTransferDepartmentDialog = true;
                } else if (type === 'transfer-hospital') {
                    this.isShowTransferHospitalDialog = true;
                } else {
                    this.isShowDischargeHospitalDialog = true;
                }
            },
            async discharge(typeName, params, goodsList) {
                this.isShowDischargeHospitalDialog = false;
                this.adviceCurrent.dischargeHospitalTime = params.dischargeHospitalTime;
                this.advice.startTime = params.dischargeHospitalTime;
                if (typeName === 'transfer') {
                    this.adviceCurrent.stopLongAdviceTime = params.stopLongAdviceTime;
                    this.adviceCurrent.dischargeHospitalReason = params.dischargeHospitalReason;
                } else {
                    // 如果不是原本的typeName
                    if (this.adviceCurrent.name !== typeName) {
                        const item = goodsList?.find((i) => {
                            return i.name === typeName;
                        });
                        const advices = MedicalPrescriptionService.getAdviceItem(item, this.adviceCurrent?.keyId);
                        this.adviceCurrent = Object.assign(this.adviceCurrent, advices.adviceRule);
                    }
                    this.adviceCurrent = Object.assign(this.adviceCurrent, params);
                    if (typeName === DischargeTypeEnum.DEATH) {
                        this.adviceCurrent.stopLongAdviceTime = '';
                        this.adviceCurrent.dischargeHospitalReason = null;
                    } else {
                        this.adviceCurrent.deathTime = '';
                    }
                }
                await this.batchCreate();
            },
            async handleCreateNurseLevelAdvice(goods, startTime) {
                const adviceGroup = MedicalPrescriptionService.getGroupItem(goods);
                adviceGroup.startTime = startTime;

                const postData = {
                    adviceGroups: [adviceGroup],
                    patientOrderId: this.inPatientInfo?.id,
                    examApplySheetReqs: [],//检验医嘱需要传
                };
                try {
                    const { data } = await MedicalPrescriptionAPI.batchCreate(postData);
                    if (data) {
                        this.isShowNurseLevelDialog = false;
                        this.$emit('create');
                    }
                } catch (e) {
                    if (e.code === 49998 || e.code === 49997) {
                        const title = e.code === 49998 ? '未下出院诊断' : '未下入院诊断';
                        this.defaultDiagnosisType = e.code === 49998 ? DiagnosisTypeEnum.OUT_HOSPITAL : DiagnosisTypeEnum.IN_HOSPITAL;
                        this.$alert({
                            type: 'warn',
                            title,
                            content: e.message,
                            onClose: async () => {
                                await this.getDiagnosisList();
                                this.isShowDiagnosisDialog = true;
                            },
                        });
                    }
                }
            },
            async transfer(item) {
                const {
                    transferToDepartment, remark,
                } = item;
                const params = {
                    transferToDepartment,
                    remark,
                };
                this.adviceCurrent = Object.assign(this.adviceCurrent, params);
                console.log('转科', this.adviceCurrent);
                await this.batchCreate();
            },
            async clickHandleTag(tag) {
                try {
                    if (this.selectedIds.includes(tag.id)) {
                        // 存在-此时删除该标签
                        await CrmAPI.deletePatientTag(this.patientBaseInfo.id, tag.id);
                    } else {
                        // 不存在-此时打上标签
                        await CrmAPI.addPatientsTags({
                            patients: [this.patientBaseInfo.id],
                            tags: [
                                {
                                    tagId: tag.id,
                                    tagName: tag.name,
                                },
                            ],
                        });
                    }
                    const data = await this.getPatientTags();
                    this.$emit('change-patient', data);
                } catch (error) {
                    console.log('clickHandleTag error', error);
                }
            },
            async getPatientTags() {
                try {
                    const { data } = await CrmAPI.fetchPatientTags(this.patientBaseInfo.id);
                    return Object.assign({}, this.patientBaseInfo, {
                        tags: data,
                    });
                } catch (error) {
                    console.log('getPatientTags error', error);
                }
            },
            // 获取住院历史
            async fetchInHospitalList(patientId) {
                if (!patientId) return;
                try {
                    const { data } = await PatientOrderAPI.getInHospitalList({ patientId }) || {};
                    this.inpatientHistory = data?.rows || [];
                } catch (error) {
                    console.log('获取住院次数&历史失败', error);
                }
            },
            // 拉取床位转移记录
            async fetchBedTransferRecords(patientOrderId) {
                if (!patientOrderId) return;
                try {
                    const { data } = await PatientOrderAPI.getHospitalLog(patientOrderId);
                    this.bedTransferRecordList = data?.rows ?? [];
                } catch (e) {
                    console.log('fetchRecordList Error');
                }
            },
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();

                }
                this.patientSourceType = this.sourceList = RecommendService.getInstance().cascaderOptions;
            },
            async fetchAllDoctor() {
                await RecommendService.getInstance().structureOriginOptions();
                this.patientSourceType = this.sourceList = RecommendService.getInstance().cascaderOptions;
            },
            initCascaderData(patientSource) {
                if (!patientSource) return [];

                const {
                    id, sourceFromName, sourceFrom, name,
                } = patientSource;

                return RecommendService.getInstance().initCascaderValue({
                    visitSourceId: id,
                    visitSourceName: name,
                    visitSourceFrom: sourceFrom,
                    visitSourceFromName: sourceFromName,
                });
            },
            outside() {
                if (this.showTagPopover) {
                    this.showTagPopover = false;
                }
            },
            handleMask() {
                this.showAnimation = true;
                this._timer = setTimeout(() => {
                    this.showAnimation = false;
                },300);
            },
            getPostData() {
                const postData = {
                    id: null,
                    name: '',
                    mobile: '',
                    sex: '男',
                    birthday: '',
                    idCard: '',
                    idCardType: DEFAULT_CERT_TYPE,
                    company: '',
                    contactName: '',
                    contactMobile: '',
                    contactRelation: '',
                    marital: '',
                    weight: '',
                    profession: '',
                    sn: '',
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    sourceId: null,
                    sourceFrom: null,
                    wxBindStatus: 0,
                    wxNickName: '',
                    shebaoCardInfo: {
                        cardNo: '',
                    },
                    address: {
                        addressCityId: null,
                        addressCityName: null,
                        addressProvinceId: null,
                        addressProvinceName: null,
                        addressDistrictId: null,
                        addressDistrictName: null,
                        addressDetail: null,
                    },
                    addressGeo: '',
                    addressDetail: null,
                    created: '',
                    remark: '',
                    ethnicity: '',
                    nationality: '',
                    primaryTherapistId: '',
                    primaryTherapistName: '',
                    dutyTherapistId: '',
                    dutyTherapistName: '',
                    parentName: '',
                    visitReason: '',
                    crowdCategory: '',
                    crowdCategoryRemark: '',
                    height: '',
                    email: '',
                };
                if (this.patientBaseInfo) {
                    const {
                        id,
                        name,
                        mobile,
                        sex,
                        idCard,
                        idCardType,
                        company,
                        contactName,
                        contactMobile,
                        contactRelation,
                        marital,
                        weight,
                        profession,
                        sn,
                        age,
                        patientSource,
                        address,
                        created,
                        remark,
                        wxBindStatus = 0,
                        wxNickName = '',
                        nationality, // 国籍
                        ethnicity,
                        shebaoCardInfo,
                        primaryTherapistId,
                        primaryTherapistName,
                        dutyTherapistId,
                        dutyTherapistName,
                        parentName,
                        visitReason,
                        crowdCategory,
                        crowdCategoryRemark,
                        height,
                        email,
                    } = this.patientBaseInfo;
                    let { birthday } = this.patientBaseInfo;
                    if (!birthday && (age.year || age.month || age.day)) {
                        birthday = age2birthday(age);
                    }
                    postData.id = id || '';
                    postData.name = name || '';
                    postData.mobile = mobile || '';
                    postData.sex = sex || '男';
                    postData.birthday = birthday || '';
                    postData.idCard = idCard || '';
                    postData.idCardType = idCardType || DEFAULT_CERT_TYPE;
                    postData.company = company || '';
                    postData.contactName = contactName || '';
                    postData.contactMobile = contactMobile || '';
                    postData.contactRelation = contactRelation || '';
                    postData.nationality = nationality || '';
                    postData.marital = marital || '';
                    postData.weight = weight && `${weight}` || '';
                    postData.profession = profession || '';
                    postData.sn = sn || '';
                    postData.created = created || '';
                    postData.remark = remark || '';
                    postData.ethnicity = ethnicity || '';
                    postData.wxBindStatus = wxBindStatus || 0;
                    postData.wxNickName = wxNickName || '';
                    postData.shebaoCardInfo = shebaoCardInfo || {
                        cardNo: '',
                    };
                    postData.primaryTherapistId = primaryTherapistId || '';
                    postData.primaryTherapistName = primaryTherapistName || '';
                    postData.dutyTherapistId = dutyTherapistId || '';
                    postData.dutyTherapistName = dutyTherapistName || '';
                    postData.parentName = parentName || '';
                    postData.visitReason = visitReason || '';
                    postData.crowdCategory = crowdCategory || '';
                    postData.crowdCategoryRemark = crowdCategoryRemark || '';
                    postData.height = height || '';
                    postData.email = email || '';
                    if (age) {
                        postData.age.year = age.year || 0;
                        postData.age.month = age.month || 0;
                        postData.age.day = age.day || 0;
                    }
                    if (idCard) {
                        this.correctIdCard(idCard, idCardType);
                    }
                    if (patientSource) {
                        postData.sourceId = patientSource.id || null;
                        postData.sourceFrom = patientSource.sourceFrom || null;
                    }
                    if (address) {
                        postData.address.addressCityId = address.addressCityId || null;
                        postData.address.addressCityName = address.addressCityName || null;
                        postData.address.addressProvinceId = address.addressProvinceId || null;
                        postData.address.addressProvinceName = address.addressProvinceName || null;
                        postData.address.addressDistrictId = address.addressDistrictId || null;
                        postData.address.addressDistrictName = address.addressDistrictName || null;
                        postData.addressGeo = address.addressGeo || '';
                        postData.address.addressDetail = postData.addressDetail = address.addressDetail || null;
                    } else {
                        postData.address.addressCityId = '';
                        postData.address.addressCityName = '';
                        postData.address.addressProvinceId = '';
                        postData.address.addressProvinceName = '';
                        postData.address.addressDistrictId = '';
                        postData.address.addressDistrictName = '';
                        postData.addressGeo = '';
                        postData.address.addressDetail = postData.addressDetail = null;
                    }
                }

                return postData;
            },
            handleIdCardInput(val) {
                if (![15,18].includes(val?.length) || this.postData.idCardType !== DEFAULT_CERT_TYPE) return;
                if (val) {
                    this.correctIdCard(val, this.postData.idCardType);
                    this.$nextTick(() => {
                        if (this.isCorrectIdCard) {
                            this.handleBirthday(val,val?.length);
                            this.handleSex(val);
                        }
                    });
                }
            },
            correctIdCard(idCard, idCardType) {
                this._validateIdCard([idCardType, idCard], (res) => {
                    this.isCorrectIdCard = res.validate;
                });
            },
            handleBirthday(data,len) {
                const idBirthday = this.getIdBirthday(data, len);
                this.postData.birthday = idBirthday;
                this.changeBirthday(idBirthday);
            },
            handleSex(val) {
                let genderCode;
                if (val.length === 15) {
                    genderCode = val.charAt(val.length - 1);
                } else {
                    genderCode = val.charAt(val.length - 2);
                }
                this.postData.sex = genderCode % 2 === 0 ? '女' : '男';
            },
            getIdBirthday(data, len) {
                let arr;
                //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
                if (len === 15) {
                    const reg15 = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
                    arr = data.match(reg15);
                } else {
                    //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
                    const reg18 = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
                    arr = data.match(reg18);
                }
                const year = arr[2];
                const month = arr[3];
                const day = arr[4];
                const idBirthday = len === 15 ? `${`19${year}-${month}-${day}`}` : `${`${year}-${month}-${day}`}`;
                return idBirthday;
            },
            handleEdit() {
                this.editor = true;
                this.postData = this.getPostData();
                const {
                    year,
                    month,
                    day,
                } = birthday2age(this.postData.birthday);
                this.handleUnionAge(year, month, day);
                this.cascaderValue = this.initCascaderData(this.patientBaseInfo?.patientSource);
                this.cachePostData = Clone(this.postData);
            },
            validateMonth(value, callback) {
                if (!this.postData.age.month) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-1])$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            validateDay(value, callback) {
                if (!this.postData.age.day) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-9]|2[0-9]|30)$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多30天',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            handleUnit(val) {
                if (val === '岁') {
                    this.secondUnit = '月';
                    this.secondMax = 11;
                } else {
                    this.secondUnit = '天';
                    this.firstMax = 11;
                    this.secondMax = 30;
                }
                this.changeAge();
            },
            changeBirthday(birthday) {
                if (birthday) {
                    const {
                        year,
                        month,
                        day,
                    } = birthday2age(birthday);
                    this.handleUnionAge(year, month, day);
                } else {
                    // 清空操作
                    this.postData.age.year = 0;
                    this.postData.age.month = 0;
                    this.postData.age.day = 0;
                }
            },
            changeAge() {
                if (this.firstUnit === '岁') {
                    this.postData.age.year = this.firstInputAge;
                    this.postData.age.month = this.secondInputAge;
                } else {
                    this.postData.age.year = '';
                    this.postData.age.month = this.firstInputAge;
                    this.postData.age.day = this.secondInputAge || 0;
                }
            },
            handleUnionAge(year, month, day, postData = this.postData) {
                if (year) {
                    this.firstInputAge = postData.age.year = year || '';
                    this.secondInputAge = postData.age.month = month || '';
                    postData.age.day = day;
                    this.firstUnit = '岁';
                    this.secondUnit = '月';
                } else {
                    postData.age.year = '';
                    this.firstInputAge = postData.age.month = month || '';
                    if (!month) {
                        this.secondInputAge = postData.age.day = day || 0;
                    } else {
                        this.secondInputAge = postData.age.day = day || '';
                    }
                    this.firstUnit = '月';
                    this.secondUnit = '天';
                }
            },
            async saveCrmInfo() {
                this.saveLoading = true;
                try {
                    let params = clone(this.postData);
                    params = {
                        ...params,
                        ...params.address,
                        addressDetail: params.addressDetail,
                    };
                    delete params.address;

                    if (this.patientBaseInfo?.id) {
                        await CrmAPI.updatePatientInfo(this.patientBaseInfo.id, params);
                        this.$Toast({
                            message: '修改成功',
                            type: 'success',
                        });
                        const data = await this.fetchPatientOverview();
                        this.$emit('change-patient', data);
                        this.editor = false;
                    }
                } catch (error) {
                    console.log('onClickSave error', error);
                    if (error?.code === 10409 || error?.code === 409) {
                        // 存在已经被共享的会员
                        this.$Toast({
                            message: error.message,
                            type: 'error',
                        });
                    } else if (error?.code === 13993) {
                        // 身份证被注册
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: error.message,
                        });
                    } else if (error?.code === 13992) {
                        // 该患者信息（姓名和手机号）已经被注册
                        this.$Toast({
                            message: error.message,
                            type: 'error',
                        });
                    }
                } finally {
                    this.saveLoading = false;
                }
            },
            handleSave() {
                if (this.saveLoading) return;
                this.$refs.postData.validate(async (valid) => {
                    if (valid) {
                        // 判断名字有修改
                        if (this.postData?.id && this.postData?.name !== this.cachePostData?.name) {
                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                confirmText: '确定修改',
                                content: this.modifyCrmNameConfirmText,
                                onConfirm: async () => {
                                    await this.saveCrmInfo();
                                },
                            });
                            return;
                        }
                        await this.saveCrmInfo();
                    }
                });
            },
            async fetchPatientOverview() {
                try {
                    const patientId = this.patientBaseInfo?.id || '';
                    if (patientId) {
                        const { data } = await CrmAPI.fetchPatientOverview(patientId,{});
                        return data;
                    }
                } catch (error) {
                    console.log('fetchPatientOverview error', error);
                }
            },
            handleCancel() {
                this.postData = Clone(this.cachePostData);
                this.editor = false;
            },
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                const inputs = $('.edit-view .abc-input__inner').not(':disabled');
                let targetIndex = -1;
                if (e.target.className === 'select-inner-input') {
                    [...inputs].forEach((item, index) => {
                        if (item?.parentNode?.className?.includes('select-contact-relation')) {
                            targetIndex = index;
                        }
                    });
                } else {
                    targetIndex = inputs.index(e.target);
                }

                let nextInput = inputs[targetIndex + 1];

                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }

                nextInput && this.$nextTick(() => {
                    nextInput.focus();
                });
            },
            handleVisitSourceEdit() {
                this.$refs['visit-source-cascader'].outside();
                this.isShowVisitSourceDialog = true;
            },
            // 搜索联系人关系
            handleSearchRelationOptions(key) {
                this.searchRelationKey = key;
            },
            /**
             * 成员搜索
             */
            handleSearchEmployee(val) {
                this.employeeSearchValue = val;
            },
            handlePrimaryTherapistChange(val) {
                this.postData.primaryTherapistName = this.employeeList.find((it) => it.employeeId === val).name;
            },
            handleDutyTherapistChange(val) {
                this.postData.dutyTherapistName = this.employeeList.find((it) => it.employeeId === val).name;
            },
            _validateIdCard(values, callback) {
                const [certType = '', certNo = ''] = values;
                if (!certNo) {
                    return callback({ validate: true });
                }
                return this.$refs?.['crm-id-card']?.validateCertNo(certType, certNo, callback);
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/abc-common.scss';

.patient-hospital-info-card-popover {
    width: 451px;
    height: auto;
    max-height: 746px;
    overflow-y: auto;
    overflow-y: overlay;
    font-size: 13px;
    background-color: $S2;
    border: 1px solid $P1;
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

    &.show-animation {
        animation: left-right-dou 0.15s ease infinite;
    }

    .text-line {
        width: 100%;
        height: 1px;
        background-color: $P4;
    }

    .split-line {
        margin: 12px 0;
    }

    .tags-desc-info {
        padding: 16px 16px 0;

        .desc {
            display: inline-flex;
            height: 18px;
            margin-bottom: 8px;
            font-size: 0;
            line-height: 18px;

            span {
                font-size: 13px;

                &.times {
                    margin-right: 8px;
                }
            }

            .popover-reference {
                font-weight: 400;
                color: $theme1;
                cursor: pointer;
            }
        }
    }

    .title-btn-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 24px;
        margin: 12px 0;

        .title {
            font-size: 13px;
            font-weight: bold;
            line-height: 16px;
            color: $S1;
        }

        .operation-btn {
            width: 44px;
            min-width: 44px;
            height: 24px;
            padding: 0;
            font-size: 12px;
            border-radius: var(--abc-border-radius-small);
        }

        .modify-btn {
            display: inline-flex;

            .abc-button {
                @extend .operation-btn;

                &.save-btn {
                    min-width: 52px;
                }

                & + .abc-button {
                    margin-left: 4px;
                }
            }
        }
    }

    .display-view {
        font-weight: 400;
        line-height: 18px;
        color: $S1;

        .content-wrapper {
            padding: 0 16px;

            .patient-info-wrapper,
            .inpatient-info-wrapper {
                .inpatient-info-title {
                    margin: 12px 0;
                    font-weight: bold;
                    line-height: 16px;
                }

                .patient-info,
                .inpatient-info {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;

                    .item {
                        display: flex;
                        line-height: 18px;

                        &:not(.contact-info) {
                            margin-bottom: 10px;
                        }

                        &.contact-info {
                            .contact-name {
                                margin-right: 16px;
                            }
                        }

                        label {
                            width: 52px;
                            margin-right: 6px;
                            color: $T2;
                        }

                        .value {
                            width: 138px;

                            &.bed-no {
                                display: inline-flex;

                                .record {
                                    margin-left: 8px;
                                    font-size: 13px;
                                    font-weight: 400;
                                    color: $theme1;
                                    cursor: pointer;
                                }
                            }
                        }

                        &:nth-child(2n) {
                            .value {
                                width: 138px;
                            }
                        }

                        &.single-item {
                            width: 100% !important;

                            label {
                                min-width: 52px;
                                max-width: 52px;
                            }

                            .value {
                                width: 100% !important;

                                &.address {
                                    display: inline-block;
                                    word-break: break-all;
                                }
                            }
                        }

                        &.last-item {
                            margin-bottom: 0;
                        }
                    }
                }

                .inpatient-info {
                    margin-bottom: 12px;
                }
            }

            .patient-info-wrapper {
                &.patient-info-wrapper_large {
                    .patient-info {
                        .item {
                            label {
                                width: 66px;
                            }

                            .value {
                                width: 124px;
                            }

                            &.single-item {
                                label {
                                    min-width: 66px;
                                    max-width: 66px;
                                }
                            }
                        }
                    }
                }
            }
        }

        .btn-wrapper {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            height: 46px;
            padding: 0 10px;
            border-top: 1px solid $P4;

            .abc-button {
                width: 84px;
            }
        }
    }

    .edit-view {
        padding: 0 16px 16px;

        .abc-form {
            .header-info {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                height: 30px;
                margin-top: 12px;

                .text {
                    height: 30px;

                    &:not(:last-child) {
                        margin-right: 4px;
                    }

                    .abc-form-item {
                        .abc-input__inner {
                            height: 30px;
                            padding: 5px 6px 6px !important;
                            font-size: 13px;
                            line-height: 18px;
                        }

                        &.is-error {
                            .abc-input-wrapper .abc-input__inner {
                                border: 1px solid $Y2 !important;
                            }
                        }
                    }

                    &.patient-sex {
                        height: 30px;
                    }

                    &.patient-age-info {
                        display: inline-flex;

                        .abc-form-item {
                            .abc-form-item-content {
                                .abc-input-wrapper {
                                    font-size: 13px !important;

                                    .abc-input__inner {
                                        padding-right: 0;
                                        border-right: 0;
                                        border-top-right-radius: 0;
                                        border-bottom-right-radius: 0;
                                    }

                                    &.age-input__wrapper {
                                        .abc-input__inner {
                                            padding: 6px 0 !important;
                                            text-align: center;
                                            border-left: 0;
                                            border-top-left-radius: 0;
                                            border-bottom-left-radius: 0;
                                        }
                                    }

                                    &:not(.is-disabled) .abc-input__inner {
                                        &:hover,
                                        &:focus {
                                            border: 1px solid;
                                        }
                                    }

                                    .append-input {
                                        padding: 0 6px;
                                        font-size: 13px;
                                        line-height: 1;
                                        color: $T2;
                                        border-top-right-radius: 3px;
                                        border-bottom-right-radius: 3px;

                                        span {
                                            font-size: 13px;
                                        }
                                    }

                                    &:not(.is-disabled) {
                                        .append-input {
                                            padding: 0 6px;
                                            font-size: 13px;
                                            line-height: 1;
                                            background-color: $S2;
                                            border-top-right-radius: 3px;
                                            border-bottom-right-radius: 3px;
                                        }
                                    }
                                }
                            }
                        }

                        .abc-select-wrapper {
                            &.unit-select {
                                .abc-input__inner {
                                    padding: 4px 0 6px !important;
                                    font-size: 13px;
                                    line-height: 18px;
                                    color: $T2;
                                    text-align: center;
                                    border-right: 0;
                                    border-left: 0;
                                    border-radius: 0;
                                }

                                &:not(.is-disabled) .abc-input__inner {
                                    &:hover,
                                    &:focus {
                                        padding: 0;
                                        border: 1px solid;
                                    }
                                }

                                .cis-icon-dropdown_triangle {
                                    display: none;
                                }
                            }
                        }
                    }
                }
            }

            .info-box {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                margin-top: 11px;

                .abc-form-item {
                    margin: 0 0 7px 0;

                    &.last-form-item {
                        margin-bottom: 0;
                    }

                    &.address-detail-item {
                        padding-left: 50px;

                        .abc-form-item-content {
                            display: flex;
                        }
                    }

                    .abc-form-item-label {
                        .label-name {
                            font-size: 13px;
                        }
                    }

                    .address-selector {
                        .abc-input__inner {
                            height: 24px !important;
                            padding: 0 6px !important;
                            line-height: 24px !important;

                            span {
                                font-size: 13px !important;
                            }
                        }

                        .cis-icon-dropdown_triangle {
                            position: absolute;
                            top: 12px !important;
                        }
                    }

                    .birthday-picker {
                        .abc-input__inner {
                            padding: 0 6px;
                        }
                    }
                }
            }
        }
    }
}
</style>
