<template>
    <div class="crm-patient-basic_info" :class="{ 'crm-patient-basic_info-large': isSupportTherapist }">
        <div class="base-info-box">
            <abc-form
                ref="postData"
                label-position="left"
                item-no-margin
                :label-width="52"
            >
                <div v-if="canEditStatus" class="header-info">
                    <abc-space :size="4">
                        <div class="text">
                            <abc-form-item label="" required>
                                <abc-input
                                    v-model.trim="postData.name"
                                    class="patient-name"
                                    :width="87"
                                    :max-length="40"
                                    :distinguish-half-angle-length="true"
                                    trim
                                    type="text"
                                    placeholder="新患者"
                                    :disabled="!isAddPatient && !isCanModifyNameInCrm"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                        <div class="text patient-sex">
                            <abc-form-item>
                                <abc-select
                                    v-model="postData.sex"
                                    :width="42"
                                    custom-class="sex-select"
                                    size="tiny"
                                    @enter="enterEvent"
                                >
                                    <abc-option value="男" label="男"></abc-option>
                                    <abc-option value="女" label="女"></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </div>
                        <div class="text patient-age-info">
                            <abc-space is-compact compact-block :border-style="false">
                                <abc-form-item
                                    label=""
                                    show-red-dot
                                    :required="!secondInputAge && secondInputAge !== 0"
                                    :validate-event="firstUnit === '月' ? validateMonth : ()=>{}"
                                >
                                    <abc-input
                                        v-model.number="firstInputAge"
                                        v-abc-focus-selected
                                        :width="29"
                                        :input-custom-style="{
                                            padding: '6px 0 6px 6px !important', 'text-align': 'center'
                                        }"
                                        type="number"
                                        :config="{
                                            supportZero: true, max: firstMax
                                        }"
                                        @enter="enterEvent"
                                        @change="changeAge"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item>
                                    <abc-select
                                        v-model="firstUnit"
                                        :width="22"
                                        size="tiny"
                                        custom-class="unit-select"
                                        no-icon
                                        class="unit-select"
                                        @change="handleUnit"
                                        @enter="enterEvent"
                                    >
                                        <abc-option
                                            v-for="it in firstUnitArray"
                                            :key="it.id"
                                            :style="{ 'padding': '3px' }"
                                            :label="it.name"
                                            :value="it.name"
                                        >
                                        </abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item
                                    :required="!firstInputAge && firstInputAge !== 0"
                                    :validate-event="firstUnit === '岁' ? validateMonth : validateDay"
                                >
                                    <abc-input
                                        v-model.number="secondInputAge"
                                        v-abc-focus-selected
                                        :width="26"
                                        class="age-input__wrapper"
                                        type="number"
                                        :config="{
                                            supportZero: true,max: secondMax
                                        }"
                                        @enter="enterEvent"
                                        @change="changeAge"
                                    >
                                        <span slot="append">{{ secondUnit }}</span>
                                    </abc-input>
                                </abc-form-item>
                            </abc-space>
                        </div>
                        <div class="text mobile crm-patient_basic--mobile">
                            <abc-form-item
                                label=""
                                :validate-event="handleMobileValidate"
                                :required="requireConfig.mobile.required"
                            >
                                <input-mobile-encrypt
                                    v-model.trim="postData.mobile"
                                    :country-code.sync="postData.countryCode"
                                    auto-width
                                    placeholder="手机号"
                                    :enable-encrypt="!isCanSeePatientMobile"
                                    @enter="enterEvent"
                                >
                                </input-mobile-encrypt>
                            </abc-form-item>
                        </div>
                    </abc-space>
                </div>
                <template v-if="canEditStatus">
                    <abc-divider
                        size="normal"
                        theme="light"
                        margin="small"
                        variant="dashed"
                    ></abc-divider>
                    <div class="info-box">
                        <div
                            v-for="formItem in formConfig"
                            :key="formItem.key"
                            class="item"
                            :class="{ 'item-flex': ['remark', 'idCard', 'address', 'addressDetail', 'consultantId'].includes(formItem.key) }"
                        >
                            <div class="text">
                                <abc-form-item
                                    v-if="formItem.key === 'weight'"
                                    :required="requireConfig.weight.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :width="formItem.formWidth(formItem)"
                                        :type="formItem.InputType"
                                        :disabled="formItem.disabled"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                        <span slot="appendInner">kg</span>
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'company'"
                                    :required="requireConfig.company.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :width="formItem.formWidth(formItem)"
                                        :type="formItem.InputType"
                                        :disabled="formItem.disabled"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'sn'"
                                    :required="requireConfig.sn.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :width="formItem.formWidth(formItem)"
                                        :type="formItem.InputType"
                                        :disabled="formItem.disabled || !isCanModifySnInCrm"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'shebaoCardInfo'"
                                    :required="formItem.require"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :width="formItem.formWidth(formItem)"
                                        :type="formItem.InputType"
                                        :disabled="formItem.disabled"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'parentName'"
                                    :required="formItem.require"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :width="formItem.formWidth(formItem)"
                                        :type="formItem.InputType"
                                        :disabled="formItem.disabled"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'primaryTherapistId'"
                                    :required="formItem.require"
                                    label="首评<br/>治疗师"
                                    :validate-event="formItem.validate"
                                    label-wrap
                                >
                                    <abc-select
                                        v-model="formItem.value"
                                        :width="formItem.formWidth(formItem)"
                                        size="tiny"
                                        :max-height="234"
                                        custom-class="primaryTherapistId-select"
                                        with-search
                                        :fetch-suggestions="handleSearchEmployee"
                                        :show-value="postData.primaryTherapistName"
                                        @enter="enterEvent"
                                        @change="handlePrimaryTherapistChange"
                                    >
                                        <abc-option
                                            v-for="item in currentEmployeeList"
                                            :key="item.employeeId"
                                            :value="item.employeeId"
                                            :label="item.employeeName"
                                            :width="formItem.formWidth(formItem)"
                                            :panel-max-height="200"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'dutyTherapistId'"
                                    :required="formItem.require"
                                    label="责任<br/>治疗师"
                                    :validate-event="formItem.validate"
                                    label-wrap
                                >
                                    <abc-select
                                        v-model="formItem.value"
                                        :width="formItem.formWidth(formItem)"
                                        size="tiny"
                                        :max-height="234"
                                        custom-class="dutyTherapistId-select"
                                        with-search
                                        :fetch-suggestions="handleSearchEmployee"
                                        :show-value="postData.dutyTherapistName"
                                        @enter="enterEvent"
                                        @change="handleDutyTherapistChange"
                                    >
                                        <abc-option
                                            v-for="item in currentEmployeeList"
                                            :key="item.employeeId"
                                            :value="item.employeeId"
                                            :label="item.employeeName"
                                            :width="formItem.formWidth(formItem)"
                                            :panel-max-height="200"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>

                                <abc-form-item
                                    v-if="formItem.key === 'remark'"
                                    :required="requireConfig.remark.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                    class="crm-patient_basic--remark"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :type="formItem.InputType"
                                        class="crm-patient_basic--remark-input"
                                        :disabled="formItem.disabled"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'idCard'"
                                    :required="requireConfig.certificates.required"
                                    :label="formItem.label"
                                    :validate-event="_validateIdCard"
                                    class="crm-patient_basic--id-card"
                                >
                                    <abc-certificates-type
                                        ref="crm-id-card"
                                        v-model.trim="formItem.value"
                                        :cert-type-width="78"
                                        auto-width
                                        :cert-type-adaptive-width="true"
                                        :max-slice-len="7"
                                        class="crm-patient_basic--id-card-input"
                                        :disabled="formItem.disabled || (!isAddPatient && !isCanModifyIdCardInCrm)"
                                        :is-disabled-cert-type="formItem.disabled || (!isAddPatient && !isCanModifyIdCardInCrm)"
                                        :cert-type.sync="postData.idCardType"
                                        :placeholder="formItem.placeholder"
                                        @enter.prevent.stop="enterEvent"
                                    ></abc-certificates-type>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'addressDetail'"
                                    :required="requireConfig.address.required"
                                    :label="formItem.label"
                                    class="text_label--disabled  crm-patient_basic--address-detail"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :type="formItem.InputType"
                                        :disabled="formItem.disabled"
                                        class="crm-patient_basic--address-detail-input"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'visitReason'"
                                    :required="requireConfig.visitReason.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :width="formItem.formWidth(formItem)"
                                        :type="formItem.InputType"
                                        :disabled="formItem.disabled"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'scrmInfo'"
                                    :required="formItem.require"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :max-length="formItem.maxLength"
                                        :width="formItem.formWidth(formItem)"
                                        :type="formItem.InputType"
                                        :disabled="formItem.disabled"
                                        :config="formItem.config"
                                        :placeholder="formItem.placeholder"
                                        @enter="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'birthday'"
                                    :required="requireConfig.birthday.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <birthday-picker
                                        v-model="formItem.value"
                                        :width="formItem.formWidth(formItem)"
                                        :disabled="formItem.disabled"
                                        :clearable="formItem.clearable"
                                        :editable="formItem.editable"
                                        @change="changeBirthday"
                                        @enter="enterEvent"
                                    >
                                    </birthday-picker>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'created'"
                                    :required="formItem.require"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <birthday-picker
                                        v-model="formItem.value"
                                        :width="formItem.formWidth(formItem)"
                                        :disabled="formItem.disabled"
                                        :clearable="formItem.clearable"
                                        :editable="formItem.editable"
                                        @change="changeBirthday"
                                        @enter="enterEvent"
                                    >
                                    </birthday-picker>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'profession'"
                                    :required="requireConfig.profession.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-select
                                        v-model="formItem.value"
                                        :width="formItem.formWidth(formItem)"
                                        custom-class="profession-options"
                                        size="tiny"
                                        :max-height="234"
                                        @enter="enterEvent"
                                    >
                                        <abc-option
                                            v-for="item in formItem.options"
                                            :key="item.value"
                                            :value="item.value"
                                            :width="formItem.formWidth(formItem)"
                                            :label="item.label"
                                            :panel-max-height="200"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'marital'"
                                    :required="requireConfig.marital.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-select
                                        v-model="formItem.value"
                                        custom-class="profession-options"
                                        :width="formItem.formWidth(formItem)"
                                        :max-height="234"
                                        size="tiny"
                                        @enter="enterEvent"
                                    >
                                        <abc-option
                                            v-for="item in formItem.options"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                            :width="formItem.formWidth(formItem)"
                                            :panel-max-height="200"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'consultantId'"
                                    :required="requireConfig.consultantId.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                    class="crm-patient_basic--consultant"
                                >
                                    <abc-select
                                        v-model="formItem.value"
                                        :width="formItem.formWidth(formItem)"
                                        :max-height="234"
                                        size="tiny"
                                        :inner-width="380"
                                        with-search
                                        clearable
                                        :fetch-suggestions="handleConsultantSearch"
                                        @enter="enterEvent"
                                    >
                                        <abc-option
                                            v-for="item in consultantOptions"
                                            :key="item.employeeId"
                                            :label="item.employeeName"
                                            :value="item.employeeId"
                                            :width="formItem.formWidth(formItem)"
                                            :panel-max-height="200"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'ethnicity'"
                                    :required="requireConfig.ethnicity.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-select
                                        v-model="formItem.value"
                                        custom-class="profession-options"
                                        :width="formItem.formWidth(formItem)"
                                        :max-height="234"
                                        size="tiny"
                                        support-inverse
                                        @enter="enterEvent"
                                    >
                                        <abc-option
                                            v-for="item in formItem.options"
                                            :key="item"
                                            :value="item"
                                            :label="item"
                                            :width="formItem.formWidth(formItem)"
                                            :panel-max-height="200"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'height'"
                                    :required="requireConfig.height.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :width="formItem.formWidth(formItem)"
                                        type="number"
                                        :config="{
                                            max: 250, formatLength: 2,
                                        }"
                                        @enter.prevent.stop="enterEvent"
                                    >
                                        <span slot="appendInner">cm</span>
                                    </abc-input>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'nationality'"
                                    :required="requireConfig.nationality.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-select
                                        v-model="formItem.value"
                                        clearable
                                        custom-class="profession-options"
                                        :width="formItem.formWidth(formItem)"
                                    >
                                        <abc-option
                                            v-for="item in nationalityOptions"
                                            :key="item.label"
                                            :value="item.label"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'crowdCategory'"
                                    :show-red-dot="requireConfig.crowdCategory.required"
                                    label="人群<br/>分类"
                                >
                                    <abc-space
                                        is-compact
                                        compact-block
                                        border-style="solid"
                                    >
                                        <abc-form-item :required="requireConfig.crowdCategory.required">
                                            <abc-select
                                                v-model="formItem.value"
                                                clearable
                                                custom-class="profession-options"
                                                :width="formItem.value === '其他' ? 55 : formItem.formWidth(formItem)"
                                            >
                                                <abc-option
                                                    v-for="item in populationAttributeOptions"
                                                    :key="item.value"
                                                    :value="item.value"
                                                    :label="item.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-form-item v-if="formItem.value === '其他'" :required="requireConfig.crowdCategory.required">
                                            <abc-input
                                                v-model="postData.crowdCategoryRemark"
                                                :max-length="60"
                                                :width="95"
                                            ></abc-input>
                                        </abc-form-item>
                                    </abc-space>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'email'"
                                    :required="requireConfig.email.required"
                                    :label="formItem.label"
                                    :validate-event="validateEmail"
                                >
                                    <abc-input
                                        v-model="formItem.value"
                                        :width="formItem.formWidth(formItem)"
                                        @enter.prevent.stop="enterEvent"
                                    >
                                    </abc-input>
                                </abc-form-item>

                                <abc-form-item
                                    v-if="formItem.key === 'patientSource'"
                                    :required="requireConfig.sourceInfo.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                >
                                    <abc-popover
                                        v-model="showRecommendation"
                                        placement="top"
                                        trigger="manual"
                                        theme="yellow"
                                        :disabled="disabledCascaderPopover"
                                    >
                                        <abc-cascader
                                            slot="reference"
                                            ref="visit-source-cascader"
                                            :value="cascaderValue"
                                            :props="{
                                                children: 'children',
                                                label: 'name',
                                                value: 'id'
                                            }"
                                            separation="-"
                                            :clearable="formItem.clearable"
                                            :width="formItem.formWidth(formItem)"
                                            :options="sourceList"
                                            placeholder=""
                                            :disabled="disabledCascader"
                                            @change="handleCascaderValueChange"
                                            @panel-visible="handleCascaderVisible"
                                            @reference-mouse-enter="handleCascaderMouseEnter"
                                            @reference-mouse-leave="handleCascaderMouseLeave"
                                            @enter="enterEvent"
                                        >
                                            <div class="visit-source-edit-wrapper">
                                                <abc-icon
                                                    v-if="isClinicAdmin"
                                                    icon="set"
                                                    size="14"
                                                    color="#94979B"
                                                    class="icon"
                                                    @click="handleVisitSourceEdit"
                                                ></abc-icon>

                                                <abc-popover
                                                    v-else
                                                    trigger="hover"
                                                    placement="top-start"
                                                    :popper-style="{
                                                        zIndex: 99999
                                                    }"
                                                    theme="yellow"
                                                >
                                                    <abc-icon
                                                        slot="reference"
                                                        icon="set"
                                                        size="14"
                                                        color="#94979B"
                                                    ></abc-icon>

                                                    <span>修改本次推荐请联系管理员</span>
                                                </abc-popover>
                                            </div>
                                        </abc-cascader>
                                        <div v-if="isAddPatient">
                                            新客消费后，活动奖励将发放给此推荐人
                                        </div>
                                        <div v-else-if="disabledCascader">
                                            {{ disabledModifyFirstSourceText }}
                                        </div>
                                    </abc-popover>
                                </abc-form-item>
                                <abc-form-item
                                    v-if="formItem.key === 'address'"
                                    :required="requireConfig.address.required"
                                    :label="formItem.label"
                                    :validate-event="formItem.validate"
                                    class="crm-patient_basic--address"
                                >
                                    <abc-address-selector
                                        v-model="formItem.value"
                                        @enter="enterEvent"
                                    ></abc-address-selector>
                                </abc-form-item>
                                <abc-tooltip
                                    v-if="formItem.key === 'wxNickName'"
                                    placement="right"
                                    content="公众号未授权"
                                    :arrow-offset="10"
                                    :disabled="!!isOpenMp"
                                >
                                    <abc-form-item label="微信">
                                        <abc-input
                                            :width="formItem.formWidth(formItem)"
                                            readonly
                                            :value="isBindWX ? formItem.value || '已绑定' : ''"
                                            @click.native="handleClickWX"
                                        >
                                            <abc-icon
                                                slot="appendInner"
                                                icon="Code"
                                                color="#005ed9"
                                                size="12"
                                            ></abc-icon>
                                        </abc-input>
                                    </abc-form-item>
                                </abc-tooltip>
                            </div>
                        </div>
                    </div>
                </template>

                <div v-else class="info-box info-box_preview">
                    <div
                        v-for="formItem in formConfig"
                        :key="formItem.key"
                        class="item"
                        :class="formItem.formClass(formItem)"
                    >
                        <template v-if="formItem.isShowFormItem(formItem)">
                            <label class="label">{{ formItem.label }}</label>
                            <div
                                class="text"
                            >
                                <span
                                    v-abc-title.ellipsis="getFormItemValue(formItem)"
                                    class="text-content"
                                    :style="formItem.formStyle(formItem)"
                                >
                                </span>
                            </div>
                        </template>
                    </div>
                </div>
            </abc-form>
        </div>
        <!--        <teleport  :to="toElement">-->
        <teleport :to="teleportValue">
            <dialog-qr-code
                v-if="visibleQrCode"
                :value="visibleQrCode"
                :patient="postData"
                @input="visibleQrCode = false; $emit('update:showLiItem', false)"
                @update-info="handleUpdateInfo"
            ></dialog-qr-code>
            <dialog-unbind-wx
                v-if="visibleUnbindWX"
                :value="visibleUnbindWX"
                :patient-info="patientInfo"
                :patient-id="postData.id"
                @input="visibleUnbindWX = false; $emit('update:showLiItem', false)"
                @success="handleUpdatePostData"
            ></dialog-unbind-wx>
        </teleport>
        <!-- 就诊来源管理弹窗 -->
        <visit-source-dialog
            v-if="isShowVisitSourceDialogFlag"
            :is-show.sync="isShowVisitSourceDialogFlag"
            :patient-source-type="patientSourceType"
        ></visit-source-dialog>
    </div>
</template>

<script>
    import { parseTime } from 'utils/index';
    import {
        DEFAULT_CERT_TYPE, MaritalStatusEnum,
    } from 'views/crm/constants';
    import { RecommendService } from '@/service/recommend';
    import BirthdayPicker from 'views/layout/birthday-picker/birthday-picker';
    import {
        mapActions,
        mapGetters,
    } from 'vuex';
    import {
        validateEmail,
        validateMobile,
    } from 'utils/validate';
    import {
        age2birthday, birthday2age,
    } from '@/utils';
    import CrmAPI from 'api/crm';
    import clone from 'utils/clone';
    import {
        baseCardConfig, OptionsTypeEnum,
    } from 'views/crm/data/base-card-dentisty';
    import {
        populationAttributeOptions, professionOptions,
    } from 'views/crm/data/options';
    import { isEqual } from 'utils/lodash';
    import { defaultCountryCode } from '@/utils/country-codes.js';
    import { convertEmptyStringToNull } from '@/utils/convert-empty-string-to-null.js';
    import InputMobileEncrypt from 'views/crm/common/package-info/input-mobile-encrypt.vue';
    import handleReferrerPromotion from 'views/crm/mixin/handle-referrer-promotion';
    import { filterEmployeeBySearchVal } from 'views/crm/common/filter';
    import Teleport from 'views/layout/patient/common/teleport.vue';
    import nationalityOptions from '@/assets/configure/nationality';
    export default {
        name: 'CrmPatientBasicInfo',
        components: {
            Teleport,
            InputMobileEncrypt,
            BirthdayPicker,
            DialogUnbindWx: () => import('views/crm/common/package-card/dialog-unbind-wx.vue'),
            DialogQrCode: () => import('views/crm/common/package-card/dialog-qr-code.vue'),
            VisitSourceDialog: () => import('views/registration/visit-source-dialog'),
        },
        mixins: [
            handleReferrerPromotion,
        ],
        provide() {
            return {
                main: this,
            };
        },
        props: {
            toElement: {
                type: String,
                required: true,
            },
            defaultPatient: {
                type: Object,
                default: null,
            },
            notIdShowDetail: {
                type: Boolean,
                default: false,
            },
            addOldPostData: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            noChangeDataInfo: {
                type: Boolean,
                default: false,
            },
            editable: {
                type: Boolean,
                default: false,
            },
            postDataInfo: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            patientBasicInfo: {
                type: Object,
                default: () => {
                    return {

                    };
                },
            },
            coverTop: {
                type: Number,
                default: 0,
            },
            coverHeight: {
                type: Number,
                default: 0,
            },
            editor: {
                type: Boolean,
                default: false,
            },
            isAddPatient: {
                type: Boolean,
                default: false,
            },
            showLiItem: {
                type: Boolean,
                default: false,
            },
            isShowVisitSourceDialog: {
                type: Boolean,
                default: false,
            },
            visibleUnbindWXInfo: {
                type: Boolean,
                default: false,
            },
            visibleQrCodeInfo: {
                type: Boolean,
                default: false,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },

            // 校验身份证必填
            requireIdCard: {
                type: Boolean,
                default: false,
            },
            // 校验手机号必填
            requireMobile: {
                type: Boolean,
                default: false,
            },
            teleportValue: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                initCardConfig: clone(baseCardConfig),
                firstMax: 199,
                oldPostData: {},
                secondMax: 11,
                firstInputAge: '',
                firstUnit: '岁',
                secondInputAge: '',
                secondUnit: '月',
                cacheFormConfig: [],
                isCorrectIdCard: false, // 身份证是否校验成功
                isInitData: false,
                postData: {},
                cascaderValue: [],
                originCascaderValue: [],
                patientSourceType: [],
                sourceList: [], // 首诊来源数据
                visibleQrCode: false,
                visibleUnbindWX: false,
                isShowVisitSourceDialogFlag: false,
                params: {
                    wx: 1,
                    childCareRecords: 1,
                    promotionCardList: 1,
                    showFamilyDoctor: 1,
                },
                consultantSearchKey: '',
                employeeSearchValue: '',
                populationAttributeOptions,
                professionOptions,
                nationalityOptions,
            };
        },
        computed: {
            ...mapGetters([
                'isOpenMp',
                'isNeedAuthenticationBasicInfo',
                'clinicBasicConfig',
                'employeeList',
                'isCanModifyNameInCrm',
                'isCanModifyIdCardInCrm',
                'isCanModifySnInCrm',
                'chainBasic',
                'clinicBasic',
                'isIntranetUser',
            ]),
            ...mapGetters(['userInfo', 'disabledModifyFirstSourceText']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters(['enableTherapist']),
            ...mapGetters('consult', ['consultantList']),
            ...mapGetters('crm', [
                'crmConfigList',
            ]),
            modifyCrmNameConfirmText() {
                return this.viewDistributeConfig.CRM.modifyCrmNameConfirmText;
            },
            isRecommendAndSourceDisplay() {
                return this.clinicBasic.isRecommendAndSourceDisplay;
            },
            // 是否支持来源
            isSupportSource() {
                return this.viewDistributeConfig.CRM.isSupportSource;
            },
            requireConfig() {
                const model = {
                    consultantId: {
                        required: false,
                    },
                    mobile: {
                        required: true,
                    },
                    idCard: {
                        required: true,
                    },
                    profession: {
                        required: false,
                    },
                    birthday: {
                        required: false,
                    },
                    address: {
                        required: false,
                    },
                    ethnicity: {
                        required: false,
                    },
                    weight: {
                        required: false,
                    },
                    remark: {
                        required: false,
                    },
                    visitReason: {
                        required: false,
                    },
                    marital: {
                        required: false,
                    },
                    sourceInfo: {
                        required: false,
                    },
                    certificates: {
                        required: false,
                    },
                    company: {
                        required: false,
                    },
                    sn: {
                        required: false,
                    },
                    pastHistory: {
                        required: false,
                    },
                    height: {
                        required: false,
                    },
                    nationality: {
                        required: false,
                    },
                    crowdCategory: {
                        required: false,
                    },
                    email: {
                        required: false,
                    },
                };
                for (const key in model) {
                    if (model.hasOwnProperty(key)) {
                        model[key].required = !!this.crmConfigList?.[key]?.required || false; // 修改 required 的值
                    }
                }
                return model;
            },
            showConsultant() {
                const { showConsultant } = this.viewDistributeConfig.Registration;
                return showConsultant;
            },
            consultantOptions() {
                if (!this.consultantSearchKey) {
                    return this.consultantList;
                }
                return this.consultantList.filter((item) => {
                    return (
                        (item.employeeName && item.employeeName.indexOf(this.consultantSearchKey) > -1) ||
                        (item.employeeNamePy && item.employeeNamePy.toLocaleLowerCase().indexOf(this.consultantSearchKey) > -1) ||
                        (item.employeeNamePyFirst && item.employeeNamePyFirst.toLocaleLowerCase().indexOf(this.consultantSearchKey) > -1)
                    );
                });
            },
            patientInfo: {
                get() {
                    return this.patientBasicInfo;
                },
                set(val) {
                    this.$emit('update:patientBasicInfo', val);
                },

            },
            // 是否有修改信息
            noChangeData() {
                const postData = clone(this.postData);
                const oldPostData = clone(this.oldPostData);
                delete postData?.wxNickName;
                delete postData?.wxBindStatus;
                delete oldPostData?.wxNickName;
                delete oldPostData?.wxBindStatus;
                delete postData?.address?.addressPostcode;
                delete oldPostData?.address?.addressPostcode;
                delete postData?.address?.fullAddress;
                delete oldPostData?.address?.fullAddress;
                if (postData?.address) {
                    postData.address = convertEmptyStringToNull(postData?.address);
                }
                if (oldPostData?.address) {
                    oldPostData.address = convertEmptyStringToNull(oldPostData?.address);
                }
                if (['',null].includes(postData?.addressDetail)) {
                    postData.addressDetail = null;
                }
                if (['',null].includes(oldPostData?.addressDetail)) {
                    oldPostData.addressDetail = null;
                }

                return isEqual(postData, oldPostData);
            },
            formConfig: {
                get() {
                    const scrmInfoItem = this.initCardConfig.find((item) => item.key === 'scrmInfo') || { value: '' };
                    scrmInfoItem.value = this.getScrmInfo(scrmInfoItem.value || {});
                    let filterList = [];
                    if (this.isAddPatient) {
                        filterList = filterList.concat(['created', 'shebaoCardInfo']);
                    }
                    if (!this.showConsultant) {
                        filterList = filterList.concat(['consultantId']);
                    }

                    return this.initCardConfig.filter((item) => {
                        // 1. 部分门店应对监管局检查不显示来源 2. 云检版本门店不需要来源
                        if (
                            (!this.isRecommendAndSourceDisplay || !this.isSupportSource) &&
                            item.key === 'patientSource'
                        ) {
                            return false;
                        }
                        if (this.isIntranetUser && (item.key === 'scrmInfo' || item.key === 'wxNickName')) {
                            return false;
                        }

                        if (item.customOptionsType === OptionsTypeEnum.EMPLOYEE_LIST) {
                            return this.isSupportTherapist;
                        }
                        return !(filterList.includes(item.key));
                    });
                },
                set(val) {
                    this.initCardConfig = val;
                },
            },
            isShowLiItem() {
                return this.visibleQrCode || this.visibleUnbindWX;
            },
            // 门店管理员权限：1,管理员；2,普通成员；
            isClinicAdmin() {
                return this.userInfo?.roleId === 1;
            },
            isBindWX() {
                return this.postData?.wxBindStatus >= 2;
            },
            canEditStatus() {
                return this.editor || this.isAddPatient;
            },
            // 首诊来源展示
            showSourceName() {
                const patientSourceItem = this.formConfig?.find((item) => item.key === 'patientSource');
                if (!patientSourceItem?.value) return '';
                return this.initCascaderData().map((o) => o.label).join('-');
            },
            firstUnitArray() {
                return [{
                    id: 1,
                    name: '岁',
                }, {
                    id: 2,
                    name: '月',
                }];
            },
            disabledCascader() {
                return this.disabledModifyFirstSource && !this.isAddPatient;
            },
            disabledModifyFirstSource() {
                return !!this.disabledModifyFirstSourceText;
            },
            disabledCascaderPopover() {
                if (this.isAddPatient) {
                    return !this.isExistPromotionActivity;
                }
                return !this.disabledModifyFirstSource;
            },
            isSupportTherapist() {
                return this.viewDistributeConfig.CRM.isSupportTherapist && this.enableTherapist;
            },
            currentEmployeeList() {
                const { employeeSearchValue } = this;
                return filterEmployeeBySearchVal(employeeSearchValue, this.employeeList);
            },
        },
        watch: {
            isShowVisitSourceDialogFlag(val) {
                this.$emit('update:isShowVisitSourceDialog', val);
            },
            visibleUnbindWX(val) {
                this.$emit('update:visibleUnbindWXInfo', val);
            },
            visibleQrCode(val) {
                this.$emit('update:visibleQrCodeInfo', val);
            },
            noChangeData: {
                handler(val) {
                    this.$emit('update:noChangeDataInfo', val);
                },
                immediate: true,
            },
            patientSourceType: {
                handler(val) {
                    if (val) {
                        this.options = val.map((item) => {
                            if (item.name === '顾客推荐') {
                                return {
                                    ...item,
                                    slot: '新增患者档案',
                                };
                            }
                            return { ...item };
                        });
                    }
                },
                immediate: true,
                deep: true,
            },
            /**
             * desc [改变view-labels的位置]
             * 产品暂时希望不要动（天翔）
             */
            cascaderValue(val) {
                this.formConfig.map((item) => {
                    if (item.key === 'patientSource') {
                        if (val.length) {
                            if (val.length > 2) {
                                this.postData.sourceId = val[val.length - 2].value;
                                this.postData.sourceFrom = val[val.length - 1] ? val[val.length - 1].value : '';
                            } else if (['顾客推荐', '员工推荐', '医生推荐','转诊医生'].includes(val[0].label)) {
                                this.postData.sourceId = val[0].value;
                                this.postData.sourceFrom = val[1] ? val[1].value : '';
                            } else {
                                this.postData.sourceId = val[val.length - 1].value;
                                this.postData.sourceFrom = '';
                            }
                        }
                    }
                });
            },
            firstUnit(val) {
                if (val === '岁') {
                    this.firstMax = 199;
                    this.secondMax = 11;
                } else {
                    this.firstMax = 11;
                    this.secondMax = 30;
                }
            },
            'postData.idCard': function (val) {
                if (![15,18].includes(val?.length) || this.postData.idCardType !== '身份证') {
                    return;
                }
                if (this.canEditStatus) {
                    this.correctIdCard(this.postData.idCardType || DEFAULT_CERT_TYPE, val);
                    this.$nextTick(() => {
                        if (this.isCorrectIdCard) {
                            this.handleBirthday(val,val?.length);
                            this.handleSex(val);
                        }
                    });
                }
            },
            patientInfo: {
                async handler(val) {
                    await this.getListSource();
                    if (val?.id && !this.editor) {
                        this.postData = this.getPostData();
                        this.handlePostData({
                            ...val,addressDetail: this.postData?.addressDetail || '',
                        });
                        this.oldPostData = clone(this.postData);
                    } else if (this.notIdShowDetail) {
                        this.handleUnionPostData();
                    }
                },
                immediate: true,
                deep: true,
            },
            formConfig: {
                handler(val) {
                    if (val) {
                        for (const key in this.postData) {
                            if (Object.prototype.hasOwnProperty.call(this.postData, key)) {
                                this.formConfig.forEach((item) => {
                                    if (item.key === key && item.key !== 'patientSource') {
                                        this.postData[key] = item.value;
                                    }
                                });
                            }
                        }
                    }
                },
                immediate: true,
                deep: true,
            },
            postData: {
                handler(val) {
                    if (val) {
                        this.$emit('update:postDataInfo', val);
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        mounted() {
            this.initCachePostData();
        },
        created() {
            if (this.isAddPatient || this.notIdShowDetail) {
                this.handleUnionPostData();
            }
            this.getListSource();
            this.fetchCrmConfigList();
            if (this.showConsultant) {
                this.$store.dispatch('consult/updateConsultantList');
            }
            this.$store.dispatch('initCrmPermission');
        },
        methods: {
            ...mapActions('crm', ['fetchCrmConfigList']),
            parseTime,
            validateMobile,
            validateEmail,
            _validateIdCard(values, callback) {
                const [certType = '', certNo = ''] = values;
                if (!this.requireConfig?.certificates?.required && !certNo) {
                    return callback({ validate: true });
                }
                if (!certNo) {
                    return callback({ validate: false });
                }
                return this.$refs?.['crm-id-card']?.[0]?.validateCertNo(certType, certNo, callback);
            },
            handleConsultantSearch(key) {
                this.consultantSearchKey = key;
            },
            initCachePostData() {
                this.oldPostData = clone(this.postData);
            },
            async validate() {
                await this.$refs.postData.validate(async (valid) => {
                    if (valid) {
                        if (this.postData?.id && this.oldPostData?.name !== this.postData?.name) {
                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                confirmText: '确定修改',
                                customClass: 'confirm-save-validate',
                                content: this.modifyCrmNameConfirmText,
                                onConfirm: () => {
                                    this.$emit('confirm');
                                },
                            });
                            return;
                        }
                        this.$emit('confirm');
                    }
                });
            },
            async onlyValidate() {
                await this.$refs.postData.validate();
            },
            // 无id联动患者信息
            handleUnionPostData() {
                this.postData = this.getPostData();
                this.handlePostData(this.postData);
                this.$nextTick(() => {
                    this.$emit('update:addOldPostData', clone(this.postData));
                    this.isAddPatient && $(this.$el).find('.patient-name .abc-input__inner')[0].focus();
                    this.postData.name = this.defaultPatient?.name || '';
                    this.postData.sex = this.defaultPatient?.sex || '男';
                    const {
                        year, month, day,
                    } = this.defaultPatient?.age || {};
                    this.postData.age.year = year ?? '';
                    this.postData.age.month = month ?? '';
                    this.postData.age.day = day ?? '';
                    if (year || month || day) {
                        this.handleUnionAge(year, month, day);
                        const it = this.formConfig.find((item) => item.key === 'birthday');
                        it.value = age2birthday(this.postData.age);
                    }
                    this.postData.mobile = this.defaultPatient?.mobile || '';
                });
            },
            onClickEdit() {
                this.$emit('update:editor', true);
                this.postData = this.getPostData();
                this.handlePostData(this.postData);
                const { wxNickName } = this.postData;
                const it = this.formConfig.find((item) => item.key === 'wxNickName') || {};
                it.value = wxNickName;
                this.cacheFormConfig = clone(this.formConfig);
                this.cascaderValue = this.initCascaderData();
                this.originCascaderValue = clone(this.cascaderValue);
            },
            onClickCancel() {
                const {
                    wxNickName,wxBindStatus,
                } = this.postData;
                this.postData = clone(this.oldPostData);
                this.postData.wxBindStatus = wxBindStatus;
                this.formConfig = clone(this.cacheFormConfig);
                const it = this.formConfig.find((item) => item.key === 'wxNickName') || {};
                it.value = wxNickName;
                this.$emit('update:editor', false);
            },
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();
                }
                this.patientSourceType = this.sourceList = RecommendService.getInstance().cascaderOptions;
            },
            async fetchAllDoctor() {
                await RecommendService.getInstance().structureOriginOptions();
                this.patientSourceType = this.sourceList = RecommendService.getInstance().cascaderOptions;
            },
            async handleUpdatePostData() {
                this.visibleUnbindWX = false;
                if (this.isAddPatient) {
                    this.postData.wxBindStatus = 0;
                    this.postData.wxNickName = '';
                } else {
                    const { data } = await CrmAPI.fetchPatientOverview(this.patientInfo.id, this.params);
                    this.patientInfo.wxBindStatus = this.postData.wxBindStatus = data.wxBindStatus;
                    const it = this.formConfig.find((item) => item.key === 'wxNickName');
                    it.value = '';
                    this.patientInfo.wxNickName = this.postData.wxNickName = data.wxNickName;
                }
                this.handlePostData(this.postData);

                this.$nextTick(() => {
                    this.visibleQrCode = true;
                });
            },
            async handleUpdateInfo(val) {
                if (this.isAddPatient) {
                    this.postData.id = val.id;
                    this.postData.wxNickName = val.wxBindStatus >= 2 ? val.wxNickName || '已绑定' : '';
                } else {
                    const { data } = await CrmAPI.fetchPatientOverview(this.patientInfo.id, this.params);
                    this.patientInfo.wxNickName = this.postData.wxNickName = data.wxNickName;
                    this.patientInfo.wxBindStatus = data.wxBindStatus;
                    const it = this.formConfig.find((item) => item.key === 'wxNickName');
                    it.value = data.wxNickName;
                }
                this.postData.wxBindStatus = val.wxBindStatus;
                this.postData.name = val.name;
                this.postData.sex = val.sex;
                this.postData.mobile = val.mobile;
                // 若新建没填sn，则扫码会自动生成，覆盖，用户可继续修改
                this.postData.sn = this.postData.sn ? this.postData.sn : val.sn;
                this.postData.birthday = val.birthday;
                this.changeBirthday(this.postData.birthday);
                this.handlePostData(this.postData);
            },
            handlePostData(postData) {
                for (const key in postData) {
                    if (Object.prototype.hasOwnProperty.call(postData, key)) {
                        this.formConfig.forEach((item) => {
                            if (item.key === key) {
                                if (item.key === 'address' && !postData[key]) {
                                    item.value = Object.assign({}, {
                                        addressCityId: '',
                                        addressCityName: '',
                                        addressProvinceId: '',
                                        addressProvinceName: '',
                                        addressDistrictId: '',
                                        addressDistrictName: '',
                                        addressDetail: null,
                                    });
                                } else if (item.key === 'shebaoCardInfo') {
                                    item.value = postData[key]?.cardNo || '';
                                } else if (item.key === 'weight') {
                                    item.value = postData[key] && `${postData[key]}` || '';
                                } else {
                                    item.value = postData[key] || '';
                                    if (item.key === 'birthday' && item.value) {
                                        const {
                                            year,
                                            month,
                                            day,
                                        } = birthday2age(item.value);
                                        this.handleUnionAge(year, month, day);
                                    }
                                }
                            }
                        });
                    }
                }
            },
            handleVisitSourceEdit() {
                this.$refs['visit-source-cascader'][0].outside();
                this.isShowVisitSourceDialogFlag = true;
            },
            async handleClickWX() {
                if (!this.isOpenMp) {
                    return;
                }
                if (this.isAddPatient && !this.postData?.id) {
                    try {
                        const { data } = await CrmAPI.fetchMatchedPatients({
                            name: this.postData.name,
                            mobile: this.postData.mobile,
                            idCard: this.postData.idCard,
                        });
                        if (data?.rows?.length) {
                            let msg = '';
                            if (this.postData.name && this.postData.mobile) {
                                msg = '该患者信息（姓名和手机号或）已经被注册';
                            }
                            if (this.postData.idCard) {
                                msg = '该患者信息（身份证）已经被注册';
                            }
                            this.$Toast({
                                message: msg,
                                type: 'error',
                            });
                            return;
                        }
                    } catch (e) {
                        console.error('fetchMatchedPatients error', e);
                    }
                }
                !this.isBindWX ? this.handleShowQrCode() : this.handleVisible();
            },
            handleVisible() {
                this.$emit('handleCommon');
                this.$nextTick(() => {
                    this.visibleUnbindWX = true;
                });
            },
            handleShowQrCode() {
                if (!this.postData.name.trim()) {
                    this.$Toast({
                        message: '患者姓名不能为空',
                        type: 'error',
                    });
                    return false;
                }
                this.$emit('handleCommon');
                this.$nextTick(() => {
                    this.visibleQrCode = true;
                });
            },
            handleBirthday(data,len) {
                const idBirthday = this.getIdBirthday(data, len);
                this.changeBirthday(idBirthday);
                const it = this.formConfig.find((item) => item.key === 'birthday');
                it.value = idBirthday;
            },
            handleSex(val) {
                let genderCode;
                if (val.length === 15) {
                    genderCode = val.charAt(val.length - 1);
                } else {
                    genderCode = val.charAt(val.length - 2);
                }
                this.postData.sex = genderCode % 2 === 0 ? '女' : '男';
            },
            getIdBirthday(data, len) {
                let arr;
                //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
                if (len === 15) {
                    const reg15 = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
                    arr = data.match(reg15);
                } else {
                    //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
                    const reg18 = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
                    arr = data.match(reg18);
                }
                const year = arr[2];
                const month = arr[3];
                const day = arr[4];
                const idBirthday = len === 15 ? `${`19${year}-${month}-${day}`}` : `${`${year}-${month}-${day}`}`;
                return idBirthday;
            },
            getPostData() {
                const postData = {
                    id: null,
                    name: '',
                    mobile: '',
                    countryCode: defaultCountryCode,
                    sex: '男',
                    birthday: '',
                    idCard: '',
                    company: '',
                    marital: '',
                    weight: '',
                    profession: '',
                    sn: '',
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    visitReason: '',
                    sourceId: '',
                    sourceFrom: '',
                    wxBindStatus: 0,
                    wxNickName: '',
                    address: {
                        addressCityId: null,
                        addressCityName: null,
                        addressProvinceId: null,
                        addressProvinceName: null,
                        addressDistrictId: null,
                        addressDistrictName: null,
                        addressDetail: null,
                    },
                    addressGeo: '',
                    addressDetail: null,
                    created: '',
                    remark: '',
                    ethnicity: '',
                    consultantId: '',
                    pastHistory: '',
                    parentName: '',
                    primaryTherapistId: '',
                    primaryTherapistName: '',
                    dutyTherapistId: '',
                    dutyTherapistName: '',
                    idCardType: DEFAULT_CERT_TYPE,
                    crowdCategory: '',
                    crowdCategoryRemark: '',
                    height: '',
                    email: '',
                    nationality: '',
                };
                if (this.patientInfo && !this.isAddPatient || this.notIdShowDetail) {
                    const {
                        id,
                        name,
                        mobile,
                        countryCode,
                        sex,
                        idCard,
                        company,
                        marital,
                        weight,
                        profession,
                        sn,
                        age,
                        patientSource,
                        address,
                        created,
                        remark,
                        ethnicity,
                        consultantId,
                        wxBindStatus = 0,
                        wxNickName = '',
                        pastHistory = '',
                        visitReason = '',
                        parentName = '',
                        primaryTherapistId = '',
                        primaryTherapistName = '',
                        dutyTherapistId = '',
                        dutyTherapistName = '',
                        idCardType = '',
                        crowdCategory = '',
                        crowdCategoryRemark = '',
                        height = '',
                        email = '',
                        nationality = '',
                    } = this.patientInfo;
                    let { birthday } = this.patientInfo;
                    if (!birthday && (age?.year || age?.month || age?.day)) {
                        birthday = age2birthday(age);
                    }
                    postData.idCardType = idCardType || DEFAULT_CERT_TYPE;
                    postData.id = id || '';
                    postData.name = name || '';
                    postData.mobile = mobile || '';
                    postData.countryCode = countryCode || defaultCountryCode;
                    postData.sex = sex || '男';
                    postData.birthday = birthday || '';
                    postData.idCard = idCard || '';
                    postData.company = company || '';
                    postData.marital = marital || '';
                    postData.weight = weight && `${weight}` || '';
                    postData.profession = profession || '';
                    postData.sn = sn || '';
                    postData.created = created || '';
                    postData.remark = remark || '';
                    postData.ethnicity = ethnicity || '';
                    postData.consultantId = consultantId || '';
                    postData.wxBindStatus = wxBindStatus || 0;
                    postData.wxNickName = wxNickName || '';
                    postData.pastHistory = pastHistory || '';
                    postData.visitReason = visitReason || '';
                    postData.parentName = parentName || '';
                    postData.primaryTherapistId = primaryTherapistId || '';
                    postData.primaryTherapistName = primaryTherapistName || '';
                    postData.dutyTherapistId = dutyTherapistId || '';
                    postData.dutyTherapistName = dutyTherapistName || '';
                    postData.crowdCategory = crowdCategory || '';
                    postData.crowdCategoryRemark = crowdCategoryRemark || '';
                    postData.height = height || '';
                    postData.email = email || '';
                    postData.nationality = nationality || '';
                    if (age) {
                        postData.age.year = age.year || 0;
                        postData.age.month = age.month || 0;
                        postData.age.day = age.day || 0;
                    }
                    if (idCard) {
                        this.correctIdCard(idCardType || DEFAULT_CERT_TYPE, idCard);
                    }
                    if (patientSource) {
                        postData.sourceId = patientSource.id || '';
                        postData.sourceFrom = patientSource.sourceFrom || '';
                    }
                    if (address) {
                        postData.address.addressCityId = address.addressCityId || null;
                        postData.address.addressCityName = address.addressCityName || null;
                        postData.address.addressProvinceId = address.addressProvinceId || null;
                        postData.address.addressProvinceName = address.addressProvinceName || null;
                        postData.address.addressDistrictId = address.addressDistrictId || null;
                        postData.address.addressDistrictName = address.addressDistrictName || null;
                        postData.addressGeo = address.addressGeo || '';
                        postData.address.addressDetail = postData.addressDetail = address.addressDetail || null;
                    } else {
                        postData.address.addressCityId = '';
                        postData.address.addressCityName = '';
                        postData.address.addressProvinceId = '';
                        postData.address.addressProvinceName = '';
                        postData.address.addressDistrictId = '';
                        postData.address.addressDistrictName = '';
                        postData.addressGeo = '';
                        postData.address.addressDetail = postData.addressDetail = null;
                    }
                }

                if (this.isAddPatient) {
                    const {
                        addressProvinceId,
                        addressProvinceName,
                        addressCityId,
                        addressCityName,
                        addressDistrictId,
                        addressDistrictName,
                        addressGeo,
                    } = this.clinicBasicConfig;

                    postData.address.addressCityId = addressCityId || null;
                    postData.address.addressCityName = addressCityName || null;
                    postData.address.addressProvinceId = addressProvinceId || null;
                    postData.address.addressProvinceName = addressProvinceName || null;
                    postData.address.addressDistrictId = addressDistrictId || null;
                    postData.address.addressDistrictName = addressDistrictName || null;
                    postData.addressGeo = addressGeo || '';
                    postData.address.addressDetail = postData.addressDetail = null;

                    if (this.defaultPatient?.idCard) {
                        postData.idCard = this.defaultPatient.idCard;
                        postData.idCardType = this.defaultPatient.idCardType || DEFAULT_CERT_TYPE;
                    }
                }
                return postData;
            },
            correctIdCard(idCardType, idCard) {
                this._validateIdCard([idCardType, idCard],(res) => {
                    this.isCorrectIdCard = res.validate;
                });
            },
            handleUnit(value) {
                if (value === '岁') {
                    this.secondUnit = '月';
                    this.secondMax = 11;
                } else {
                    this.secondUnit = '天';
                    this.firstMax = 11;
                    this.secondMax = 30;
                }
                this.changeAge();
            },
            changeBirthday(birthday) {
                if (birthday) {
                    const {
                        year,
                        month,
                        day,
                    } = birthday2age(birthday);
                    this.handleUnionAge(year, month, day);
                } else {
                    // 清空操作
                    this.postData.age.year = 0;
                    this.postData.age.month = 0;
                    this.postData.age.day = 0;
                }
            },
            handleUnionAge(year, month, day, postData = this.postData) {
                if (year) {
                    this.firstInputAge = postData.age.year = year || '';
                    this.secondInputAge = postData.age.month = month || '';
                    postData.age.day = day;
                    this.firstUnit = '岁';
                    this.secondUnit = '月';
                } else {
                    postData.age.year = '';
                    this.firstInputAge = postData.age.month = month || '';
                    if (!month) {
                        this.secondInputAge = postData.age.day = day || 0;
                    } else {
                        this.secondInputAge = postData.age.day = day || '';
                    }
                    this.firstUnit = '月';
                    this.secondUnit = '天';
                }
            },
            changeAge() {
                if (this.firstUnit === '岁') {
                    this.postData.age.year = this.firstInputAge;
                    this.postData.age.month = this.secondInputAge;
                } else {
                    this.postData.age.year = '';
                    this.postData.age.month = this.firstInputAge;
                    this.postData.age.day = this.secondInputAge || 0;
                }
                const {
                    year,month,day,
                } = this.postData.age;
                // day为0的校验要放开
                if (!year && !month && !day && day !== 0) return;
                const it = this.formConfig.find((item) => item.key === 'birthday');
                it.value = age2birthday(this.postData.age);
            },
            handleMobileValidate(values, callback) {
                const [countryCode = '', mobile = ''] = values;
                if (!countryCode) {
                    return callback({
                        validate: false, message: '无法确认手机号所在地区，请联系ABC客服！',
                    });
                }

                // 若是加密手机号则使用对应的明文进行校验
                const mobileStr = this.isCanSeePatientMobile ? mobile : `${this.postData.mobile || ''}` ;
                this.validateMobile(mobileStr, callback, this.postData.countryCode);
            },
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                const inputs = $(`${this.toElement} .abc-input__inner`).not(':disabled');
                const targetIndex = inputs.index(e.target);
                let nextInput = inputs[targetIndex + 1];

                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }

                nextInput && this.$nextTick(() => {
                    nextInput.focus();
                });
            },
            validateMonth(value, callback) {
                if (!this.postData.age.month) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-1])$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            validateDay(value, callback) {
                if (!this.postData.age.day) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-9]|2[0-9]|30)$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多30天',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            getMarital(marital) {
                switch (marital) {
                    case MaritalStatusEnum.single:
                        return '未婚';
                    case MaritalStatusEnum.married:
                        return '已婚';
                    case MaritalStatusEnum.divorced:
                        return '离异';
                    case MaritalStatusEnum.widow:
                        return '丧偶';
                    default:
                        return '';
                }
            },
            getFormItemValue(formItem) {
                if (formItem.key === 'patientSource') {
                    return this.showSourceName || '';
                }
                if (formItem.key === 'marital') {
                    return this.getMarital(formItem.value) || '';
                }
                if (formItem.key === 'weight') {
                    return formItem.value ? `${formItem.value}kg` : '';
                }
                if (formItem.key === 'height') {
                    return formItem.value ? `${formItem.value}cm` : '';
                }
                if (formItem.key === 'idCard') {
                    return formItem.value ? `[${this.patientInfo?.idCardType || DEFAULT_CERT_TYPE}] ${formItem.value}` : '';
                }
                if (formItem.key === 'crowdCategory') {
                    return formItem.value ? `${this.patientInfo?.crowdCategory}${(this.patientInfo?.crowdCategory === '其他' && this.patientInfo?.crowdCategoryRemark) ? `(${this.postData.crowdCategoryRemark})` : ''}` : '';
                }
                if (formItem.key === 'consultantId') {
                    return this.patientInfo?.consultantName || this.defaultPatient?.consultantName || '';
                }
                if (formItem.key === 'created' && formItem.value) {
                    return parseTime(formItem.value, 'y-m-d', true) || '';
                }
                if (formItem.key === 'sn' && formItem.value && formItem.value.length > 18) {
                    return formItem.value.slice(0, 18);
                }
                if (formItem.key === 'wxNickName') {
                    return this.isBindWX ? formItem.value || '已绑定' : '';
                }
                if (formItem.key === 'address') {
                    if (formItem.value && this.getAddress(formItem.value) !== '-') {
                        return this.getAddress(formItem.value);
                    }
                    return '';
                }
                if (formItem.key === 'scrmInfo') {
                    return this.getScrmInfo(formItem.value) || '';
                }
                if (formItem.key === 'primaryTherapistId') {
                    return this.patientInfo?.primaryTherapistName || '';
                }
                if (formItem.key === 'dutyTherapistId') {
                    return this.patientInfo?.dutyTherapistName || '';
                }
                return formItem.value || '';
            },
            initCascaderData() {
                const patientSourceItem = this.formConfig?.find((item) => item.key === 'patientSource');
                if (!patientSourceItem?.value) return [];
                const {
                    id,
                    sourceFromName,
                    sourceFrom,
                    name,
                } = patientSourceItem?.value ?? {};
                return RecommendService.getInstance().initCascaderValue({
                    visitSourceId: id,
                    visitSourceName: name,
                    visitSourceFrom: sourceFrom,
                    visitSourceFromName: sourceFromName,
                });
            },
            getScrmInfo(value) {
                return value?.customerCorpUserRelates?.map((item) => {
                    return item.employeeName || '未知员工';
                })?.join(',') || '';
            },
            getAddress(address) {
                const {
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                } = address || {};
                const arr = [];
                if (addressProvinceName) arr.push(addressProvinceName);
                if (addressCityName) arr.push(addressCityName);
                if (addressDistrictName) arr.push(addressDistrictName);
                if (arr.length !== 0) {
                    return arr.join(' / ');
                }
                return '-';
            },
            handleCascaderValueChange(val) {
                const lastCascaderValue = clone(this.cascaderValue);
                this.cascaderValue = val;
                const isEditor = !this.isAddPatient;
                this.isExistPromotionActivity = false;

                if (isEqual(val, lastCascaderValue)) {
                    return;
                }

                if (isEditor) {
                    // 修改来源时原推荐人不是符合老带新的来源
                    if (!this.referrerList.includes(this.originCascaderValue[0]?.label)) {
                        return;
                    }
                    const cancelFun = () => {
                        this.cascaderValue = clone(lastCascaderValue);
                    };

                    this.handleReferrer(val, this.patientInfo, null, cancelFun);
                } else {
                    // 新增时推荐人不是符合老带新的来源
                    if (!this.referrerList.includes(val[0]?.label)) {
                        return;
                    }

                    const {
                        referrerId,
                        referrerType,
                    } = this.getReferrerData(val);

                    this.fetchReferrerPromotionExist(val, referrerId, referrerType);
                }
            },

            handleSearchEmployee(val) {
                this.employeeSearchValue = val;
            },
            handlePrimaryTherapistChange(val) {
                this.postData.primaryTherapistName = this.employeeList.find((it) => it.employeeId === val).name;
            },
            handleDutyTherapistChange(val) {
                this.postData.dutyTherapistName = this.employeeList.find((it) => it.employeeId === val).name;
            },
        },
    };
</script>

<style lang="scss">
.crm-base-card-cover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    height: 100vh;
    background: $T1;
    opacity: 0.3;
}

.crm-patient-basic_info {
    .patient-head-info {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 32px;
        padding: 0 6px 0 12px;
        font-size: 14px;
        font-weight: 400;
        color: $S1;
        background: $S2;

        .name {
            margin-right: 12px;
            font-weight: bold;
        }

        .sex,
        .age,
        .mobile {
            margin-right: 8px;
        }

        .crm-patient_basic--mobile {
            .abc-input__inner {
                flex: 1;
                width: auto !important;
            }
        }

        .delete-icon-wrapper {
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 100%;
            margin-left: auto;
        }
    }

    .patient-head-info + .content {
        border-top: 1px solid $P4;
    }

    .base-info-box {
        .abc-form {
            .header-info {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                height: 30px;

                .text {
                    height: 30px;

                    .abc-form-item {
                        .abc-input__inner {
                            height: 30px;
                            padding: 5px 6px 6px !important;
                            font-size: 13px;
                            line-height: 18px;
                        }

                        &.is-error {
                            .abc-input-wrapper .abc-input__inner {
                                border: 1px solid $Y2 !important;
                            }
                        }
                    }

                    &.patient-sex {
                        height: 30px;
                    }

                    &.patient-age-info {
                        display: inline-flex;

                        .abc-form-item {
                            .abc-form-item-content {
                                .abc-input-wrapper {
                                    font-size: 13px !important;

                                    .abc-input__inner {
                                        padding-right: 0;
                                        border-right: 0;
                                        border-top-right-radius: 0;
                                        border-bottom-right-radius: 0;
                                    }

                                    &.age-input__wrapper {
                                        .abc-input__inner {
                                            padding: 6px 0 !important;
                                            text-align: center;
                                            border-left: 0;
                                            border-top-left-radius: 0;
                                            border-bottom-left-radius: 0;
                                        }
                                    }

                                    &:not(.is-disabled) .abc-input__inner {
                                        &:hover,
                                        &:focus {
                                            border: 1px solid;
                                        }
                                    }

                                    .append-input {
                                        padding: 0 6px;
                                        font-size: 13px;
                                        line-height: 1;
                                        color: $T2;

                                        span {
                                            font-size: 13px;
                                        }
                                    }

                                    &:not(.is-disabled) {
                                        .append-input {
                                            padding: 0 6px;
                                            font-size: 13px;
                                            line-height: 1;
                                            background-color: $S2;
                                        }
                                    }
                                }
                            }
                        }

                        .abc-select-wrapper {
                            &.unit-select {
                                .abc-input__inner {
                                    padding: 4px 0 6px !important;
                                    font-size: 13px;
                                    line-height: 18px;
                                    color: $T2;
                                    text-align: center;
                                    border-right: 0;
                                    border-left: 0;
                                    border-radius: 0;
                                }

                                &:not(.is-disabled) .abc-input__inner {
                                    &:hover,
                                    &:focus {
                                        padding: 0;
                                        border: 1px solid;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .lint-top {
                margin-top: 12px;
                margin-bottom: 11px;
            }

            .text-line {
                width: 100%;
                height: 1px;
                background-color: $P4;
            }

            .info-box {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;

                .item {
                    display: inline-flex;
                    justify-content: start;
                    margin-bottom: 7px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    &.item-h26 {
                        height: 26px;
                    }

                    > label {
                        width: 52px;
                        font-size: 13px;
                        font-weight: 400;
                        color: $T2;
                        text-align: left;
                    }

                    .text {
                        .text_label--disabled {
                            padding-left: 52px;
                        }

                        .text-content {
                            display: inherit;
                            width: 146px;
                            padding-left: 6px;
                            overflow: hidden;
                        }

                        .abc-form-item {
                            .abc-input__inner {
                                height: 26px;
                                padding: 5px 6px 6px !important;
                                font-size: 13px !important;
                            }

                            .abc-form-item-label {
                                .label-name {
                                    position: relative;
                                    font-size: 13px !important;
                                }
                            }

                            .abc-form-item-content {
                                .abc-input-wrapper {
                                    &.border-right-none {
                                        .abc-input__inner {
                                            border-right: 0;

                                            &:focus,
                                            &:hover {
                                                border-right: 1px solid #0270c9;
                                            }
                                        }
                                    }

                                    .append-input {
                                        color: $T2;
                                        background-color: $S2;
                                        border-top-right-radius: 3px;
                                        border-bottom-right-radius: 3px;
                                    }
                                }
                            }
                        }

                        .wx_box {
                            display: flex;

                            &--label {
                                width: 52px;
                                font-size: 13px;
                                font-weight: 400;
                                color: $T2;
                                text-align: left;
                            }

                            &--input {
                                display: flex;
                                justify-content: space-between;
                                width: 120px;
                                height: 26px;
                                padding: 4px 6px;
                                cursor: pointer;
                                border: 1px solid $P1;
                                border-radius: var(--abc-border-radius-small);

                                span {
                                    display: inline-block;
                                    width: 80px;
                                    overflow: hidden;
                                    font-size: 13px;
                                    line-height: 18px;
                                }

                                .abc-icon {
                                    display: inline-block;
                                    height: 14px;
                                    margin-top: 2px;
                                    color: $S3;
                                }
                            }
                        }

                        .address-selector {
                            .abc-input__inner {
                                padding: 0 6px !important;
                                line-height: 24px !important;

                                span {
                                    font-size: 13px !important;
                                }
                            }
                        }

                        .abc-date-picker {
                            .abc-input__inner {
                                padding: 4px 6px !important;
                            }
                        }
                    }

                    .abc-date-picker {
                        height: 26px;
                    }
                }

                .item-flex {
                    width: 100%;

                    .text {
                        width: 100%;

                        .crm-patient_basic--consultant {
                            .abc-form-item-content {
                                flex: 1 !important;

                                .abc-select-wrapper {
                                    width: 100% !important;
                                }
                            }
                        }

                        .crm-patient_basic--address {
                            .abc-form-item-content {
                                flex: 1 !important;

                                .abc-select-wrapper {
                                    width: 100% !important;
                                }
                            }
                        }

                        .crm-patient_basic--id-card,
                        .crm-patient_basic--remark {
                            &-input {
                                width: 100% !important;
                            }

                            .abc-form-item-content {
                                flex: 1 !important;
                            }
                        }

                        .crm-patient_basic--address-detail {
                            &-input {
                                width: 100% !important;
                            }

                            .abc-form-item-content {
                                flex: 1 !important;
                            }
                        }
                    }
                }

                .address-detail {
                    margin-top: -5px !important;
                }

                &_preview {
                    .item {
                        height: 18px;
                        margin: 5px 0;
                        overflow: hidden;
                        line-height: 18px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .text {
                            .abc-form-item {
                                .abc-input__inner {
                                    padding: 6px !important;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    &.crm-patient-basic_info-large {
        .base-info-box {
            .abc-form {
                .info-box {
                    .item {
                        > label {
                            width: 68px;
                        }
                    }
                }
            }
        }
    }
}
</style>


