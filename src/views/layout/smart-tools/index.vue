<template>
    <div class="smart-tools">
        <div class="smart-tools-content">
            <abc-popover
                v-if="waitAppointmentVisible"
                v-model="isShowWaitAppointmentPatientList"
                v-abc-click-outside="closeWaitAppointmentPatientList"
                width="416px"
                placement="right-end"
                trigger="manual"
                popper-class="wait-appointment_patient-list"
                theme="white"
            >
                <register-appointment-patient-list :wait-register-appointment-patient-list="waitRegisterAppointmentPatientList" @goToAppointment="goToAppointment" @click.native.prevent.stop="isShowWaitAppointmentPatientList = true"></register-appointment-patient-list>
                <div
                    v-if="waitAppointmentVisible"
                    slot="reference"
                    class="entry-item"
                    @click="handleClickEntryItem('registration')"
                >
                    <abc-icon icon="s-reservation-color" :size="20"></abc-icon>
                    <div class="content">
                        待定预约
                    </div>
                    <div class="describe">
                        <div v-if="waitRegisterAppointmentTotal" class="wait-appointment-total">
                            {{ waitRegisterAppointmentTotal }}
                        </div>
                        <abc-icon icon="s-b-right-line-medium" size="16" color="var(--abc-color-T3)"></abc-icon>
                    </div>
                </div>
            </abc-popover>

            <call-number
                v-if="!isTherapy && (type === SmartToolsType.REGISTRATION || type === SmartToolsType.OUTPATIENT)"
                v-show="visibleCallControl"
                v-model="openCtrlView"
                placement="right-end"
                :doctor-id="doctorId === undefined ? (showAllDoctor ? '' : userInfo?.id) : doctorId"
                :continue-diagnosed-info="continueDiagnosedInfo"
                @active-call="handleActiveCall"
            >
                <template #btn>
                    <abc-check-access>
                        <div
                            class="entry-item"
                            @click="handleOpenCtrlView"
                        >
                            <abc-icon icon="s-volume-color" :size="20"></abc-icon>
                            <div class="content">
                                叫号
                            </div>
                            <div class="describe">
                                下一位 {{ nextCallingText }}
                                <abc-icon icon="s-b-right-line-medium" size="16" color="var(--abc-color-T3)"></abc-icon>
                            </div>
                        </div>
                    </abc-check-access>
                </template>
            </call-number>

            <abc-flex
                align="center"
                class="registered-board-wrapper"
                @click="handleOpenRegisteredBoardDialog"
            >
                <abc-icon icon="s-calendar-color" :size="20"></abc-icon>
                <abc-text tag="div" size="normal" class="registered-board-content">
                    挂号看板
                </abc-text>
                <abc-flex align="center">
                    <abc-text tag="div" size="mini" theme="gray">
                        挂号工作汇总
                    </abc-text>
                    <abc-icon
                        icon="s-b-right-line-medium"
                        size="16"
                        color="var(--abc-color-T3)"
                        style="margin-left: 4px;"
                    >
                    </abc-icon>
                </abc-flex>
            </abc-flex>

            <slot></slot>
        </div>
        <abc-video v-show="false" ref="abcVideo" url="https://cd-cis-static-common.oss-cn-chengdu.aliyuncs.com/media/waiting-appointment.mp3"></abc-video>
    </div>
</template>

<script>
    import {
        RegisteredBoardDialog,
    } from '@/views-dentistry/registration/components/registered-board-dialog/registered-board-dialog';
    const CallNumber = () => import('views/layout/call-number');
    import RegisterAppointmentPatientList from '../../layout/register-appointment-patient-list/index';
    import ModulePermission from 'views/permission/module-permission';
    import {
        mapActions, mapGetters, mapMutations, mapState,
    } from 'vuex';
    import AbcSocket from 'views/common/single-socket';
    import AbcVideo from 'views/layout/abc-video/index';
    import { BusinessType } from 'views/registration/common/constants';
    import { RESERVATION_MODE_TYPE } from 'views/settings/registered-reservation/constant';
    import ClinicAPI from 'api/clinic';
    import { MODULE_ID_MAP } from 'utils/constants';
    export const SmartToolsType = {
        REGISTRATION: 0,
        OUTPATIENT: 1,
        CASHIER: 2,
        PHARMACY: 3,
    };

    export default {
        name: 'SmartTools',
        components: {
            CallNumber,
            RegisterAppointmentPatientList, // 挂号预约组件
            AbcVideo,
        },
        mixins: [
            ModulePermission,
        ],
        props: {
            visibleCallControl: {
                type: Boolean,
                default: false,
            },
            doctorId: {
                type: String,
                default: undefined,
            },
            type: {
                type: Number,
                default: SmartToolsType.OUTPATIENT,
            },
            businessType: {
                type: Number,
                default: BusinessType.REGISTRATION,
            },
        },
        data() {
            return {
                openCtrlView: false,
                isShowWaitAppointmentPatientList: false, // 是否展示待预约患者列表
                SmartToolsType,
                continueDiagnosedInfo: null, // 回诊患者信息
                registrationEmployeeList: [],
            };
        },
        computed: {
            ...mapState('call', [
                'nextCalling',
            ]),
            ...mapGetters([
                'userInfo',
                'registrationsConfig',
                'therapyReservationConfig',
                'isOpenMp',
            ]),
            ...mapState('crm', [
                'unreadMessageCount',
            ]),
            ...mapGetters(['isOpenRegistrationAppointmentVoice', 'waitRegisterAppointmentPatientList']),
            // 等待预约人数
            waitRegisterAppointmentTotal() {
                return this.waitRegisterAppointmentPatientList.filter((item) => {return item.applyAuditStatus === 1;})?.length;
            },
            nextCallingText() {
                const {
                    patientName,
                    orderNo,
                    timeOfDay,
                    isAdditional,
                } = this.nextCalling || {};
                if (!patientName) {
                    return '无';
                }
                let orderNoStr = '';
                if (isAdditional) {
                    orderNoStr = `+${orderNo}`;
                } else {
                    orderNoStr = orderNo > 9 ? orderNo : `0${orderNo}`;
                }
                if (this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER) {
                    return `${timeOfDay}${orderNoStr}号 ${patientName}`;
                }
                return patientName;
            },
            showAllDoctor() {
                return !!(this.isClinicAdmin || this.hasDoctorHelperModule);
            },
            // 理疗预约
            isTherapy() {
                return this.businessType === BusinessType.THERAPY;
            },
            isFixOrderMode() {
                if (BusinessType.THERAPY === this.businessType) {
                    return this.therapyReservationConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
                }
                // modeType 0: 固定号源模式 1: 灵活预约模式
                return this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            isOpenWechatReserveNeedApply() {
                if (BusinessType.THERAPY === this.businessType) {
                    return this.therapyReservationConfig.wechatReserveNeedApply === 1;
                }
                return this.registrationsConfig.wechatReserveNeedApply === 1;
            },
            waitAppointmentVisible() {
                // 灵活模式 && 挂号 && 开启微信预约 && 开启在线沟通权限 && 开启微信预约需要审核
                return !this.isFixOrderMode && this.type === SmartToolsType.REGISTRATION && this.isOpenMp && this.hasOnlineCommunicationModule && this.isOpenWechatReserveNeedApply;
            },
        },
        async created() {
            if (this.type === SmartToolsType.REGISTRATION) {
                const { socket } = AbcSocket.getSocket();
                this._socket = socket;
                this._socket.on('registration.apply_audit_number', this.hasNewWaitAppointmentMsg);
                this.$on('hook:beforeDestroy', () => {
                    this._socket.off('registration.apply_audit_number', this.hasNewWaitAppointmentMsg);
                });
                await Promise.all([
                    this.$store.dispatch('fetchRegistrationAppointmentVoiceSetting'),
                    this.fetchWaitRegisterAppointmentPatientList(),
                ]);
                this.$abcEventBus.$on('continue-diagnosed-open-ctr-view',(data) => {
                    this.continueDiagnosedInfo = data;
                    if (this.visibleCallControl && !this.isTherapy && this.type === SmartToolsType.REGISTRATION) {
                        this.openCtrlView = true;
                    }
                }, this);

                await this.fetchEmployeeByModuleId();
            }
        },
        methods: {
            ...mapActions(['fetchWaitRegisterAppointmentPatientList']),
            ...mapMutations('crm', [
                'muOpenPatientCommunicate',
            ]),
            ...mapActions('crm', ['setLastCommunicatePatient']),
            async hasNewWaitAppointmentMsg(data) {
                console.log('newMsg=', data);
                // 待定预约数量
                if (data?.applyAuditNumber) {
                    // 开启预约声音提醒，并且数据量大于现有待定预约数量则视为是新增
                    if (this.isOpenRegistrationAppointmentVoice && data?.applyAuditNumber > this.waitRegisterAppointmentTotal) {
                        this.$refs?.abcVideo?.playVoice();
                    }

                    await this.fetchWaitRegisterAppointmentPatientList();
                }
            },
            async goToAppointment(item) {
                // 如果待预约 打开微信聊天弹窗
                if (item.applyAuditStatus === 1) {
                    await this.$nextTick();
                    const timer = window.setTimeout(() => {
                        this.closeWaitAppointmentPatientList();
                        // 存入信息 设置waitAppointmentFlag
                        item.waitAppointmentFlag = true;
                        this.setLastCommunicatePatient(item);
                        this.muOpenPatientCommunicate();
                        clearTimeout(timer);
                    },300);

                }
            },
            closeWaitAppointmentPatientList() {
                if (this.isShowWaitAppointmentPatientList) {
                    this.isShowWaitAppointmentPatientList = false;
                }
            },
            handleClickEntryItem(type) {
                this.isShowWaitAppointmentPatientList = !this.isShowWaitAppointmentPatientList;
                this.$emit('click-entry-item', type);
            },
            handleActiveCall(id) {
                this.$emit('active-call', id);
            },
            handleOpenCtrlView() {
                this.continueDiagnosedInfo = null;
                this.openCtrlView = !this.openCtrlView;
            },
            async fetchEmployeeByModuleId() {
                const { data } = await ClinicAPI.fetchEmployeeByModuleId({
                    moduleId: [MODULE_ID_MAP.registration],
                });
                this.registrationEmployeeList = data || [];
            },
            handleOpenRegisteredBoardDialog() {
                new RegisteredBoardDialog({
                    registrationEmployeeList: this.registrationEmployeeList,
                }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.smart-tools {
    .smart-tools-title {
        display: flex;
        align-items: center;
        height: 32px;
        padding: 0 11px;
        overflow: hidden;
        font-size: 12px;
        line-height: 32px;
        border-top: 1px solid $P6;
        border-bottom: 1px solid $P6;

        > h5 {
            font-size: 12px;
            font-weight: bold;
        }
    }

    .smart-tools-content {
        .call-open-btn {
            width: 100%;
        }

        .wait-pay-order-count {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 16px;
            height: 16px;
            padding: 0 4px;
            font-size: 12px;
            color: #ffffff;
            background-color: $R2;
            border-radius: 8px;
        }

        .entry-item {
            display: flex;
            align-items: center;
            height: 44px;
            padding: 0 var(--abc-paddingLR-ml);
            cursor: pointer;
            border-bottom: 1px solid var(--abc-color-P6);

            &.entry-item-no-border {
                border-bottom: 1px solid transparent;
            }

            &:hover {
                background-color: var(--abc-color-cp-grey4);
                border-bottom: 1px solid transparent;
                border-radius: var(--abc-border-radius-small);
            }

            img {
                width: 22px;
                height: 22px;
            }

            .content {
                flex: 1;
                margin-left: 6px;

                span {
                    margin-left: 12px;
                    font-size: 12px;
                    color: var(--abc-color-T2);
                }
            }

            .describe {
                display: flex;
                align-items: center;
                max-width: 180px;
                height: 20px;
                font-size: 12px;
                color: var(--abc-color-T2);

                .wait-appointment-total {
                    min-width: 16px;
                    height: 16px;
                    padding: 0 4px;
                    font-size: 12px;
                    line-height: 16px;
                    color: var(--abc-color-S2);
                    text-align: center;
                    background: #ff3333;
                    border-radius: 8px;
                }

                .iconfont {
                    margin-left: 4px;
                }
            }

            .iconfont {
                color: $P1;
            }
        }

        .registered-board-wrapper {
            width: 100%;
            height: 44px;
            padding: 0 var(--abc-paddingLR-ml);
            cursor: pointer;
            border-bottom: 1px solid var(--abc-color-P6);

            &:hover {
                background-color: var(--abc-color-cp-grey4);
                border-bottom: 1px solid rgba(0, 0, 0, 0);
                border-radius: var(--abc-border-radius-small);
            }

            .registered-board-content {
                flex: 1;
                margin-left: 6px;
            }
        }
    }
}

.wait-appointment_patient-list {
    padding: 0 0 !important;
}
</style>
