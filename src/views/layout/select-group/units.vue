<template>
    <div ref="suggestions" class="unit-suggestion-wrapper">
        <div class="unit-li-wrapper">
            <ul>
                <template v-for="(unit, index) in units">
                    <li
                        :key="index"
                        :class="[
                            { 'is-selected': currentValue && currentValue === unit.name },
                            { 'is-hover': hoverIndex === index },
                            { 'last-item': index % 4 === 3 },
                        ]"
                        @click="selectUnit(unit.name)"
                    >
                        {{ unit.name }}
                    </li>
                </template>
            </ul>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import Popper from 'utils/vue-popper';
    import { mapGetters } from 'vuex';

    export default {
        name: 'AbcUnitSelect',
        mixins: [ Popper ],
        props: {
            visible: Boolean,
            currentValue: [String, Number],
            type: String,
            extraPieceUnit: String,
            extraPackageUnit: String,
        },

        data() {
            return {
                hoverIndex: null,
            };
        },
        computed: {
            ...mapGetters([
                'westernMedicineConfig',
            ]),
            GroupSelect() {
                let parent = this.$parent;
                while (parent && parent.$options && parent.$options.name !== 'AbcGroupSelect') {
                    parent = parent.$parent;
                }
                return parent;
            },
            units() {
                const units = this.westernMedicineConfig[ this.type ].slice();
                if (this.extraPieceUnit && !units.find((item) => item.name === this.extraPieceUnit)) {
                    units.push({
                        id: units.length,
                        name: this.extraPieceUnit,
                    });
                }
                if (this.extraPackageUnit && !units.find((item) => item.name === this.extraPackageUnit)) {
                    units.push({
                        id: units.length,
                        name: this.extraPackageUnit,
                    });
                }
                if (this.currentValue && !units.find((item) => item.name === this.currentValue)) {
                    units.unshift({
                        'id': 0,
                        'name': this.currentValue,
                    });
                }
                return units;
            },
        },
        watch: {
            visible: {
                immediate: true,
                handler(val) {
                    this.showPopper = val;
                },
            },
            currentValue: {
                immediate: true,
                handler(val) {
                    this.units.forEach((item, index) => {
                        if (val && val === item.name) {
                            this.hoverIndex = index;
                        }
                    });
                },
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.suggestions;
            this.referenceElm = this.$parent.$refs.abcinput;
        },

        beforeDestroy() {
            this.doDestroy();
        },
        methods: {
            selectUnit(val) {
                this.GroupSelect.$emit('handleOptionClick', val);
            },

            /**
             * @desc 父组件按 下
             * <AUTHOR>
             * @date 2018/08/06 13:59:50
             */
            down() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                if (this.hoverIndex + 4 <= this.units.length - 1) {
                    this.hoverIndex += 4;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 上
             * <AUTHOR>
             * @date 2018/08/06 14:00:30
             */
            up() {
                if (this.hoverIndex > 3) {
                    this.hoverIndex -= 4;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 左
             * <AUTHOR>
             * @date 2018/08/06 14:00:54
             */
            left() {
                if (this.hoverIndex > 0) {
                    this.hoverIndex -= 1;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 右
             * <AUTHOR>
             * @date 2018/08/06 14:01:12
             */
            right() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                if (this.hoverIndex + 1 <= this.units.length - 1) {
                    this.hoverIndex += 1;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 回车
             * <AUTHOR>
             * @date 2018/08/06 14:01:35
             */
            enter() {
                if (this.hoverIndex === null) return false;
                this.selectUnit(this.units[this.hoverIndex].name);
            },

            /**
             * @desc 在suggestions中高亮选中项目
             * <AUTHOR>
             * @date 2018/05/28 15:58:09
             */
            changeIndex() {
                this.$nextTick(() => {
                    const suggestions = this.$refs.suggestions.querySelector('.unit-li-wrapper');
                    const highlightItem = suggestions.querySelector('.is-hover');

                    if (!highlightItem) return false;
                    const { scrollTop } = suggestions;
                    const { offsetTop } = highlightItem;

                    if (offsetTop + highlightItem.scrollHeight > (scrollTop + suggestions.clientHeight)) {
                        suggestions.scrollTop += (offsetTop + 48);
                    }
                    if (offsetTop < scrollTop) {
                        suggestions.scrollTop -= (offsetTop + 48);
                    }
                });
            },
        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .unit-suggestion-wrapper {
        position: absolute;
        top: 24px;
        left: 0;
        z-index: 9999;
        box-sizing: border-box;
        max-width: 322px;
        padding: 0;
        margin-top: 2px;
        overflow: hidden;
        background-color: #ffffff;
        border: 1px solid var(--abc-color-P7);
        border-radius: var(--abc-border-radius-small);
        box-shadow: var(--abc-shadow-1);

        .unit-li-wrapper {
            overflow-y: auto;
        }

        ul {
            width: 100%;
            min-width: 320px;
            font-size: 0;
        }

        li:not(.title) {
            box-sizing: border-box;
            display: inline-block;
            width: 80px;
            min-width: 80px;
            max-width: 80px;
            height: 36px;
            font-size: 14px;
            line-height: 36px;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            border-right: 1px solid $P6;
            border-bottom: 1px solid $P6;

            &:hover {
                background-color: $P4;
            }

            &.is-hover {
                background-color: $P4;
            }

            &.is-selected {
                color: #ffffff;

                /* <!--background-color: $theme2;--> */
                background-color: #00ace9;
            }

            &.last-item {
                border-right: 0;
            }
        }

        .title {
            display: block;
            width: 100%;
            height: 36px;
            padding-left: 16px;
            font-size: 14px;
            line-height: 36px;
            color: $T2;
            text-align: left;
            cursor: default;
            border-right: 0;
            border-bottom: 1px solid $P3;
        }
    }
</style>
