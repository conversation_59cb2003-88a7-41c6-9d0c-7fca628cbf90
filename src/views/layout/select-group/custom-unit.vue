<template>
    <div ref="suggestions" :style="wrapperWidth" class="custom-unit-options">
        <div class="treatment-unit-li-wrapper">
            <ul>
                <template v-for="(unit, index) in options">
                    <li
                        :key="index"
                        :class="[
                            { 'is-selected': currentValue && currentValue === unit.name },
                            { 'is-hover': hoverIndex === index },
                            { 'last-item': index % 4 === 3 },
                        ]"
                        :style="{ width: `${liWidth }px` }"
                        :title="unit.name"
                        @click="selectUnit(unit.name)"
                    >
                        {{ unit.name }}
                    </li>
                </template>
            </ul>


            <abc-flex
                v-if="isAdmin"
                justify="flex-end"
                align="center"
                class="custom-unit-options-footer"
            >
                <abc-button
                    variant="text"
                    icon="n-settings-line"
                    size="small"
                    theme="default"
                    @click.stop="modifyOptions"
                ></abc-button>
            </abc-flex>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import Popper from 'utils/vue-popper';
    import { mapGetters } from 'vuex';

    export default {
        name: 'AbcCustomUnitSelect',
        mixins: [Popper],
        props: {
            visible: Boolean,
            currentValue: String,
            type: String,
            options: Array,
            liWidth: {
                type: [String, Number],
                default: 80,
            },
        },

        data() {
            return {
                hoverIndex: null,
            };
        },
        computed: {
            ...mapGetters(['isAdmin']),
            GroupSelect() {
                let parent = this.$parent;
                while (parent && parent.$options && parent.$options.name !== 'AbcGroupSelect') {
                    parent = parent.$parent;
                }
                return parent;
            },
            wrapperWidth() {
                return `max-width: ${this.liWidth * 4 + 2 + (this.options.length > 40 ? 10 : 0)}px`;
            },
        },
        watch: {
            visible: {
                immediate: true,
                handler(val) {
                    this.showPopper = val;
                },
            },
            currentValue: {
                immediate: true,
                handler(val) {
                    this.options.forEach((item, index) => {
                        if (val && val === item.name) {
                            this.hoverIndex = index;
                        }
                    });
                },
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.suggestions;
            this.referenceElm = this.$parent.$refs.abcinput;
        },

        beforeDestroy() {
            this.doDestroy();
        },
        methods: {
            modifyOptions() {
                this.$emit('modify-options');
                this.GroupSelect.closePopper();
            },
            selectUnit(val) {
                this.GroupSelect.$emit('handleOptionClick', val);
            },

            /**
             * @desc 父组件按 下
             * <AUTHOR>
             * @date 2018/08/06 13:59:50
             */
            down() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                if (this.hoverIndex + 4 <= this.options.length - 1) {
                    this.hoverIndex += 4;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 上
             * <AUTHOR>
             * @date 2018/08/06 14:00:30
             */
            up() {
                if (this.hoverIndex > 3) {
                    this.hoverIndex -= 4;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 左
             * <AUTHOR>
             * @date 2018/08/06 14:00:54
             */
            left() {
                if (this.hoverIndex > 0) {
                    this.hoverIndex -= 1;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 右
             * <AUTHOR>
             * @date 2018/08/06 14:01:12
             */
            right() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                if (this.hoverIndex + 1 <= this.options.length - 1) {
                    this.hoverIndex += 1;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 回车
             * <AUTHOR>
             * @date 2018/08/06 14:01:35
             */
            enter() {
                if (this.hoverIndex === null) return false;
                this.selectUnit(this.options[this.hoverIndex].name);
            },

            /**
             * @desc 在suggestions中高亮选中项目
             * <AUTHOR>
             * @date 2018/05/28 15:58:09
             */
            changeIndex() {
                this.$nextTick(() => {
                    const suggestions = this.$refs.suggestions.querySelector('.treatment-unit-li-wrapper');
                    const highlightItem = suggestions.querySelector('.is-hover');

                    if (!highlightItem) return false;
                    const { scrollTop } = suggestions;
                    const { offsetTop } = highlightItem;

                    if (offsetTop + highlightItem.scrollHeight > (scrollTop + suggestions.clientHeight)) {
                        suggestions.scrollTop += (offsetTop + 48);
                    }
                    if (offsetTop < scrollTop) {
                        suggestions.scrollTop -= (offsetTop + 48);
                    }
                });
            },
        },

    };
</script>

<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';

    .custom-unit-options {
        position: absolute;
        top: 24px;
        left: 0;
        z-index: 9999;
        box-sizing: border-box;
        max-width: 322px;
        padding: 0 0 31px 0;
        margin-top: 2px;
        background-color: #ffffff;
        border: 1px solid $P3;
        border-radius: var(--abc-border-radius-small);
        box-shadow: $boxShadowContent;

        .treatment-unit-li-wrapper {
            max-height: 378px;
            overflow-y: auto;
        }

        ul {
            width: 100%;
            font-size: 0;
        }

        li:not(.title) {
            box-sizing: border-box;
            display: inline-block;
            width: 80px;
            height: 36px;
            padding: 6px;
            overflow: hidden;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            border-right: 1px solid $P6;
            border-bottom: 1px solid $P6;

            &:hover {
                background-color: $P4;
            }

            &.is-hover {
                background-color: $P4;
            }

            &.is-selected {
                color: #ffffff;

                /* <!--background-color: $theme2;--> */
                background-color: #00ace9;
            }

            &.last-item {
                border-right: 0;
            }
        }

        .title {
            display: block;
            width: 100%;
            height: 36px;
            padding-left: 16px;
            font-size: 14px;
            line-height: 36px;
            color: $T2;
            text-align: left;
            cursor: default;
            border-right: 0;
            border-bottom: 1px solid $P3;
        }

        .custom-unit-options-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 32px;
            padding: 0 4px;
            background: var(--abc-color-cp-grey2);
            border-top: 1px solid var(--abc-color-P8);
            border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);
        }
    }
</style>
