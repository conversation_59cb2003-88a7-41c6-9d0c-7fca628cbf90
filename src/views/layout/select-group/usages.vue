<template>
    <div ref="suggestions" class="usage-suggestion-wrapper">
        <ul>
            <template v-for="(usage, index) in usages">
                <li v-if="index === zsIndex || index === qtIndex" :key="index" class="title"></li>
                <li
                    :key="usage.name + index"
                    :class="[
                        { 'is-selected': currentValue && currentValue === usage.name },
                        { 'is-hover': hoverIndex === index },
                        { 'last-item': index % 4 === 3 },
                        { 'is-disabled': !usage.name },
                    ]"
                    @click="selectUsage(usage.name)"
                >
                    {{ usage.name }}
                </li>
            </template>
        </ul>
    </div>
</template>

<script type="text/ecmascript-6">
    import Popper from 'utils/vue-popper';
    import { mapGetters } from 'vuex';

    export default {
        name: 'AbcUsagesSelect',
        mixins: [Popper],
        props: {
            visible: Boolean,
            usageType: String,
            currentValue: String,
        },

        data() {
            return {
                hoverIndex: null,
                zsIndex: 0,
                qtIndex: 0,
            };
        },
        computed: {
            ...mapGetters([
                'westernMedicineConfig',
            ]),
            GroupSelect() {
                let parent = this.$parent;
                while (parent && parent.$options && parent.$options.name !== 'AbcGroupSelect') {
                    parent = parent.$parent;
                }
                return parent;
            },

            usages() {
                const options = this.westernMedicineConfig.usage;

                const cy = {
                    type: 1, list: [],
                };
                const zs = {
                    type: 2, list: [],
                };
                const qt = {
                    type: 3, list: [],
                };

                options.forEach((o) => {
                    if (o.type === cy.type) {
                        cy.list.push(o);
                    } else if (o.type === zs.type) {
                        zs.list.push(o);
                    } else {
                        qt.list.push(o);
                    }
                });
                const cyLen = cy.list.length;
                const zsLen = zs.list.length;
                const qtLen = qt.list.length;

                if (cyLen % 4 !== 0) {
                    for (let i = 0; i < 4 - zsLen % 4; i++) {
                        cy.list.push({ name: '' });
                    }
                }

                if (zsLen % 4 !== 0) {
                    for (let i = 0; i < 4 - zsLen % 4; i++) {
                        zs.list.push({ name: '' });
                    }
                }

                if (qtLen % 4 !== 0) {
                    for (let i = 0; i < 4 - qtLen % 4; i++) {
                        qt.list.push({ name: '' });
                    }
                }

                if (this.usageType === 'zs') {
                    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                    this.zsIndex = -1;
                    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                    this.qtIndex = -1;
                    return [
                        ...zs.list,
                    ];
                }
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.zsIndex = cy.list.length;
                // eslint-disable-next-line vue/no-side-effects-in-computed-properties
                this.qtIndex = zs.list.length + cy.list.length;
                return [
                    ...cy.list,
                    ...zs.list,
                    ...qt.list];

            },
        },
        watch: {
            visible: {
                immediate: true,
                handler(val) {
                    this.showPopper = val;
                },
            },
            currentValue: {
                immediate: true,
                handler(val) {
                    this.hoverIndex = null;
                    this.usages.forEach((item, index) => {
                        if (val && val === item.name) {
                            this.hoverIndex = index;
                        }
                    });
                },
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.suggestions;
            this.referenceElm = this.$parent.$refs.abcinput;
        },

        beforeDestroy() {
            this.doDestroy();
        },
        methods: {
            selectUsage(val) {
                if (val) {
                    this.GroupSelect.$emit('handleOptionClick', val);
                }
            },

            /**
             * @desc 父组件按 下
             * <AUTHOR>
             * @date 2018/08/06 13:59:50
             */
            down() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }

                if (this.hoverIndex + 4 <= this.usages.length - 1 && this.usages[this.hoverIndex + 4].name) {
                    this.hoverIndex += 4;
                }

                this.changeIndex();
            },

            /**
             * @desc 父组件按 上
             * <AUTHOR>
             * @date 2018/08/06 14:00:30
             */
            up() {
                if (this.hoverIndex > 3 && this.usages[this.hoverIndex - 4].name) {
                    this.hoverIndex -= 4;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 左
             * <AUTHOR>
             * @date 2018/08/06 14:00:54
             */
            left() {
                if (this.hoverIndex > 0 && this.usages[this.hoverIndex - 1].name) {
                    this.hoverIndex -= 1;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 右
             * <AUTHOR>
             * @date 2018/08/06 14:01:12
             */
            right() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                if (this.hoverIndex + 1 <= this.usages.length - 1 && this.usages[this.hoverIndex + 1].name) {
                    this.hoverIndex += 1;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 回车
             * <AUTHOR>
             * @date 2018/08/06 14:01:35
             */
            enter() {
                if (this.hoverIndex === null) return false;
                if (this.usages[this.hoverIndex]) {
                    this.selectUsage(this.usages[this.hoverIndex].name);
                }
            },

            /**
             * @desc 在suggestions中高亮选中项目
             * <AUTHOR>
             * @date 2018/05/28 15:58:09
             */
            changeIndex() {
                //                this.$nextTick(_ => {
                //                    const suggestions = this.$refs.suggestions.querySelector('.abc-scrollbar-wrapper');
                //                    const highlightItem = suggestions.querySelector('.is-hover');
                //
                //                    if(!highlightItem) return false;
                //                    let scrollTop = suggestions.scrollTop;
                //                    let offsetTop = highlightItem.offsetTop;
                //
                //                    if (offsetTop + highlightItem.scrollHeight > (scrollTop + suggestions.clientHeight)) {
                //                        suggestions.scrollTop += (offsetTop + 48);
                //                    }
                //                    if (offsetTop < scrollTop) {
                //                        suggestions.scrollTop -= (offsetTop + 48);
                //                    }
                //                })
            },
        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .usage-suggestion-wrapper {
        position: absolute;
        top: 24px;
        left: 0;
        z-index: 9999;
        box-sizing: border-box;
        width: 322px;
        padding: 0;
        margin-top: 2px;
        overflow: hidden;
        background-color: #ffffff;
        border: 1px solid var(--abc-color-P3);
        border-radius: var(--abc-border-radius-small);
        box-shadow: var(--abc-box-shadow-content);

        ul {
            width: 100%;
            font-size: 0;
        }

        li:not(.title) {
            box-sizing: border-box;
            display: inline-block;
            width: 80px;
            height: 36px;
            font-size: 14px;
            line-height: 36px;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            border-right: 1px solid var(--abc-color-P6);
            border-bottom: 1px solid var(--abc-color-P6);

            &:hover {
                background-color: var(--abc-color-P4);
            }

            &.is-hover {
                background-color: var(--abc-color-P4);
            }

            &.is-selected {
                color: #ffffff;
                background-color: var(--abc-color-B3);
            }

            &.is-disabled {
                cursor: default;

                &:hover {
                    background-color: #ffffff;
                }
            }

            &.last-item {
                border-right: 0;
            }
        }

        .title {
            display: block;
            width: 100%;
            height: 12px;
            font-size: 14px;
            cursor: default;
            border-right: 0;
            border-bottom: 1px solid $P6;
        }
    }
</style>
