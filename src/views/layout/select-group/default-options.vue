<template>
    <div ref="suggestions" class="default-suggestion-wrapper" :style="wrapperWidth">
        <div class="default-li-wrapper">
            <ul>
                <template v-for="(unit, index) in options">
                    <li
                        :key="index"
                        :class="[
                            { 'is-selected': currentValue && currentValue === unit.name },
                            { 'is-hover': hoverIndex === index },
                            { 'last-item': index % divisorIndex === resultIndex },
                        ]"
                        :style="{ width: `${liWidth }px` }"
                        @click="selectUnit(unit.name)"
                    >
                        {{ unit.name }}
                    </li>
                </template>
            </ul>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import Popper from 'utils/vue-popper';
    import { mapGetters } from 'vuex';

    export default {
        name: 'AbcUnitSelect',
        mixins: [Popper],
        props: {
            visible: Boolean,
            currentValue: String,
            type: String,
            options: Array,
            liWidth: {
                type: [String, Number],
                default: 80,
            },
            divisorIndex: {
                type: Number,
                default: 4,
            },
            resultIndex: {
                type: Number,
                default: 3,
            },
        },

        data() {
            return {
                hoverIndex: null,
            };
        },
        computed: {
            ...mapGetters([
                'westernMedicineConfig',
            ]),
            GroupSelect() {
                let parent = this.$parent;
                while (parent && parent.$options && parent.$options.name !== 'AbcGroupSelect') {
                    parent = parent.$parent;
                }
                return parent;
            },
            wrapperWidth() {
                return `max-width: ${this.liWidth * this.divisorIndex + 2}px`;
            },
        },
        watch: {
            visible: {
                immediate: true,
                handler(val) {
                    this.showPopper = val;
                },
            },
            currentValue: {
                immediate: true,
                handler(val) {
                    this.options.forEach((item, index) => {
                        if (val && val === item.name) {
                            this.hoverIndex = index;
                        }
                    });
                },
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.suggestions;
            this.referenceElm = this.$parent.$refs.abcinput;
        },

        beforeDestroy() {
            this.doDestroy();
        },
        methods: {
            selectUnit(val) {
                this.GroupSelect.$emit('handleOptionClick', val);
            },

            /**
             * @desc 父组件按 下
             * <AUTHOR>
             * @date 2018/08/06 13:59:50
             */
            down() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                if (this.hoverIndex + 4 <= this.options.length - 1) {
                    this.hoverIndex += 4;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 上
             * <AUTHOR>
             * @date 2018/08/06 14:00:30
             */
            up() {
                if (this.hoverIndex > 3) {
                    this.hoverIndex -= 4;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 左
             * <AUTHOR>
             * @date 2018/08/06 14:00:54
             */
            left() {
                if (this.hoverIndex > 0) {
                    this.hoverIndex -= 1;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 右
             * <AUTHOR>
             * @date 2018/08/06 14:01:12
             */
            right() {
                if (this.hoverIndex === null) {
                    this.hoverIndex = 0;
                    return;
                }
                if (this.hoverIndex + 1 <= this.options.length - 1) {
                    this.hoverIndex += 1;
                }
                this.changeIndex();
            },

            /**
             * @desc 父组件按 回车
             * <AUTHOR>
             * @date 2018/08/06 14:01:35
             */
            enter() {
                if (this.hoverIndex === null) return false;
                this.selectUnit(this.options[this.hoverIndex].name);
            },

            /**
             * @desc 在suggestions中高亮选中项目
             * <AUTHOR>
             * @date 2018/05/28 15:58:09
             */
            changeIndex() {
                this.$nextTick(() => {
                    const suggestions = this.$refs.suggestions.querySelector('.default-li-wrapper');
                    const highlightItem = suggestions.querySelector('.is-hover');

                    if (!highlightItem) return false;
                    const { scrollTop } = suggestions;
                    const { offsetTop } = highlightItem;

                    if (offsetTop + highlightItem.scrollHeight > (scrollTop + suggestions.clientHeight)) {
                        suggestions.scrollTop += (offsetTop + 48);
                    }
                    if (offsetTop < scrollTop) {
                        suggestions.scrollTop -= (offsetTop + 48);
                    }
                });
            },
        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.default-suggestion-wrapper {
    position: absolute;
    top: 24px;
    left: 0;
    z-index: 9999;
    box-sizing: border-box;
    max-width: 322px;
    padding: 0;
    margin-top: 2px;
    overflow: hidden;
    background-color: $S2;
    border: 1px solid var(--abc-color-P7);
    border-radius: var(--abc-border-radius-small);
    box-shadow: var(--abc-shadow-1);

    .default-li-wrapper {
        overflow-y: auto;
    }

    ul {
        width: 100%;
        font-size: 0;
    }

    li:not(.title) {
        box-sizing: border-box;
        display: inline-block;
        width: 80px;
        height: 36px;
        font-size: 14px;
        line-height: 36px;
        text-align: center;
        vertical-align: middle;
        cursor: pointer;
        border-right: 1px solid var(--abc-color-P6);
        border-bottom: 1px solid var(--abc-color-P6);

        &:hover {
            background-color: var(--abc-color-P4);
        }

        &.is-hover {
            background-color: var(--abc-color-P4);
        }

        &.is-selected {
            color: var(--abc-color-S2);
            background-color: var(--abc-color-B3);
        }

        &.last-item {
            border-right: 0;
        }
    }

    .title {
        display: block;
        width: 100%;
        height: 36px;
        padding-left: 16px;
        font-size: 14px;
        line-height: 36px;
        color: var(--abc-color-T2);
        text-align: left;
        cursor: default;
        border-right: 0;
        border-bottom: 1px solid var(--abc-color-P3);
    }
}
</style>
