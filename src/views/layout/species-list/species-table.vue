<template>
    <abc-table
        ref="table-fixed"
        :data-list="data"
        :empty-opt="{ label: `暂无数据` }"
        :has-inner-border="false"
        :loading="loading"
        :max-height="707"
        :min-height="388"
        :render-config="renderConfig"
        :sort-config="sortConfig"
        :disabled-item-func="disabledItemFunc"
        :custom-all-checked-func="customAllCheckedFunc"
        class="inventory-abc-table"
        :tr-click-trigger-checked="true"
        :need-selected="false"
        v-on="$listeners"
        @handleClickTr="rowClickHandler"
        @sortChange="sortChange"
    >
        <template #topHeader>
            <slot name="topHeader"></slot>
        </template>
    </abc-table>
</template>

<script>
    import PopoverStockCard from 'views/inventory/components/popover-stock-card.vue';
    import MultipleCodePopover from '@/service/trace-code/components/multiple-code-popover.vue';

    import {
        isChineseMedicine, percent, count1,
    } from '@/filters/index.js';
    import {
        moneyDigit, paddingMoney,
    } from 'utils/index.js';
    import { isNull } from 'utils/lodash.js';
    import { mapGetters } from 'vuex';
    import { parseTime } from 'utils/index.js';

    import InventoryDataPermission from 'views/inventory/mixins/inventory-data-permission.js';
    import { PriceType } from 'views/common/inventory/constants';
    import GoodsArchivesTable from 'views/inventory/goods/table-config';
    import OverflowFlexTagsWrapper from 'views/registration/components/overflow-flex-tags-wrapper.vue';
    import { InventoryViewMode } from '@/views-pharmacy/inventory/constant';
    import TraceCode, { TraceableCodeTypeEnum } from '@/service/trace-code/service';
    import {
        GoodsTypeIdEnum,
    } from '@abc/constants';
    import { LabelManager } from 'views/common/inventory/LabelManager';

    export default {
        name: 'SpeciesTable',
        mixins: [InventoryDataPermission],
        props: {
            header: {
                type: Array,
                required: true,
            },
            data: {
                type: Array,
                required: true,
            },
            loading: Boolean,
            sortConfig: {
                type: Object,
                required: true,
            },
            topTabText: String,
            selectedClinicId: String,
            fetchParams: {
                type: Object,
                required: true,
            },
            panelData: {
                type: Object,
                required: true,
            },
            rowClickHandler: Function,
            shebaoCount: Object,
            viewMode: {
                type: String,
                default: InventoryViewMode.NORMAL,
            },
            disabledItemFunc: {
                type: Function,
                default: () => {
                    return false;
                },
            },
            customAllCheckedFunc: {
                type: Function,
            },
            usableTraceCodeGoodsTypeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                showCardName: '',
                currentHoverId: '',
                currentWarningKeys: [],
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
            ]),
            showAllCheckbox() {
                return this.viewMode !== InventoryViewMode.BATCH_SET_NO_TRACE_CODE;
            },
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isSupportConvertArchiveFieldName() {
                return this.viewDistributeConfig.Inventory.isSupportConvertArchiveFieldName;
            },
            isNanningRegion() {
                return this.$abcSocialSecurity.region === 'guangxi_nanning';
            },
            renderConfig() {
                const goodsArchivesTable = new GoodsArchivesTable();
                const renderConfig = goodsArchivesTable.createRenderConfig(this.header, this.renderTypeList, this.viewMode);
                if (this.isNanningRegion && this.isSupportConvertArchiveFieldName) {
                    renderConfig.list = renderConfig.list.map((item) => {
                        return {
                            ...item,
                            label: LabelManager.transformMap['goods.stocks.goodsBasicInfo']?.[item.label] || item.label,
                        };
                    });
                }
                if (this.viewMode === InventoryViewMode.BATCH_SET_NO_TRACE_CODE) {
                    renderConfig.list = renderConfig.list.map((item) => {
                        if (item.key === 'displayName') {
                            return {
                                ...item,
                                style: {
                                    'flex': 1,
                                    'width': '326px',
                                    'maxWidth': '',
                                    'minWidth': '326px',
                                    'paddingLeft': '',
                                    'paddingRight': '',
                                    'textAlign': 'left',
                                },
                            };
                        }
                        if (item.key === 'goodsTypeName') {
                            return {
                                ...item,
                                style: {
                                    'width': '88px',
                                    'maxWidth': '88px',
                                    'minWidth': '88px',
                                    'paddingLeft': '',
                                    'paddingRight': '',
                                    'textAlign': 'left',
                                },
                            };
                        }
                        return {
                            ...item,
                        };
                    });
                }
                return renderConfig;
            },
            needWarning() {
                // 总部spu不展示预警
                if (this.isChainAdmin && this.fetchParams.isSpu) {
                    return false;
                }
                return true;
            },
            renderTypeList() {
                return {
                    displayNameRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <abc-flex
                                    title={row.displayName}
                                    align="center"
                                    justify="space-between"
                                    gap="small"
                                    style={{
                                        position: 'relative',
                                        'min-height': '20px',
                                        width: '100%',
                                    }}
                                >
                                    <abc-text class="ellipsis" theme="primary">{row.displayName}</abc-text>
                                    {
                                        row.v2DisableStatus || row.disable ? (
                                            <abc-tag-v2
                                                shape="square"
                                                size="small"
                                                variant="ghost"
                                                min-width={68}
                                                style={{ 'white-space': 'nowrap' }}
                                            >
                                                {row.v2DisableStatus === 20 || row.disable ? '禁售' : '可售'}
                                            </abc-tag-v2>
                                        ) : (row.prohibitPackageCount + row.prohibitPieceCount) ? (
                                            <abc-tag-v2
                                                shape="square"
                                                size="small"
                                                variant="ghost"
                                                min-width={68}
                                                style={{ 'white-space': 'nowrap' }}
                                            >
                                                部分停售
                                            </abc-tag-v2>
                                        ) : ''
                                    }
                                </abc-flex>
                            </abc-table-cell>
                        );
                    },
                    typeRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div class={{ 'is-disabled': row.v2DisableStatus }}
                                     title={row.goodsTypeName}>{row.goodsTypeName}</div>
                            </abc-table-cell>
                        );
                    },
                    specRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <span class={{ 'is-disabled': row.v2DisableStatus }}
                                     title={row.displaySpec}>{row.displaySpec}</span>
                            </abc-table-cell>
                        );
                    },
                    priceTypeRender: (h, row) => {
                        const value = row.priceType === PriceType.PKG_PRICE_MAKEUP ? '进价加成' : '固定售价';
                        return (
                            <abc-table-cell>
                                <div
                                    class={{ 'is-disabled': row.v2DisableStatus }}
                                    title={value}
                                >{value}</div>
                            </abc-table-cell>
                        );
                    },
                    outTaxRatRender: (h, row) => {
                        const displayValue = row?.outTaxRat ? `${row.outTaxRat}%` : '';
                        return (
                            <abc-table-cell>
                                <div class={{ 'is-disabled': row.v2DisableStatus }}
                                     title={displayValue}>{displayValue}</div>
                            </abc-table-cell>
                        );
                    },
                    inTaxRatRender: (h, row) => {
                        const displayValue = row?.inTaxRat ? `${row.inTaxRat}%` : '';
                        return (
                            <abc-table-cell>
                                <div class={{ 'is-disabled': row.v2DisableStatus }}
                                     title={displayValue}>{displayValue}</div>
                            </abc-table-cell>
                        );
                    },
                    manufacturerRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <span class={{ 'is-disabled': row.v2DisableStatus }}
                                     title={row.manufacturer}>{row.manufacturer}</span>
                            </abc-table-cell>
                        );
                    },
                    packagePriceRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div
                                    class={{ 'is-disabled': row.v2DisableStatus }}
                                    title={row.chainPackagePrice}
                                >{row.chainPackagePrice}</div>
                            </abc-table-cell>
                        );
                    },
                    lastPackageCostPriceRender: (h, row) => {
                        if (this.showGoodsCost) {
                            return (
                                <abc-table-cell>
                                    <div class={{ 'is-disabled': row.v2DisableStatus }}>{
                                        isNull(row.lastPackageCostPrice) ? '' : paddingMoney(row.lastPackageCostPrice)
                                    }</div>
                                </abc-table-cell>
                            );
                        }
                    },
                    positionRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div class={{ 'is-disabled': row.v2DisableStatus }}>
                                    {row.position}
                                </div>
                            </abc-table-cell>
                        );
                    },
                    priceRender: (h, row, col) => {
                        const displayValue = this.fetchParams.isSpu ? row.clinicPackagePrice : row[col.prop];
                        const isAdditionMode = row.priceType === PriceType.PKG_PRICE_MAKEUP;

                        let content = (<span class={{ 'is-disabled': row.v2DisableStatus }}>{ isNull(displayValue) ? '' : paddingMoney(displayValue) }</span>);

                        if (this.fetchParams.isSpu) {
                            content = (<span class={{ 'is-disabled': row.v2DisableStatus }}>{ displayValue }</span>);
                        }
                        // 进价加成的药品售价与库存强相关，没有库存就没有售价
                        if (isAdditionMode && !row.stockPackageCount && !row.stockPieceCount) {
                            content = <span class={{ 'is-disabled': row.v2DisableStatus }}>-</span>;
                        }
                        if (isAdditionMode && col.prop === 'chainPackagePrice') {
                            content = (<span class={{ 'is-disabled': row.v2DisableStatus }} title={row.chainPackagePrice || ''}>{ row.chainPackagePrice || '' }</span>);
                        }
                        if (isAdditionMode && col.prop === 'chainPiecePrice') {
                            content = (<span class={{ 'is-disabled': row.v2DisableStatus }} title={row.chainPiecePrice || ''}>{ row.chainPiecePrice || '' }</span>);
                        }
                        if (isAdditionMode && col.prop === 'packagePrice') {
                            content = (<span class={{ 'is-disabled': row.v2DisableStatus }} title={row.clinicPackagePrice || ''}>{ row.clinicPackagePrice || '' }</span>);
                        }
                        if (isAdditionMode && col.prop === 'piecePrice') {
                            content = (<span class={{ 'is-disabled': row.v2DisableStatus }} title={row.clinicPiecePrice || ''}>{ row.clinicPiecePrice || '' }</span>);
                        }
                        return (
                            <abc-table-cell>
                                {content}
                            </abc-table-cell>
                        );
                    },
                    listingPriceRender: (h, row) => {
                        const { shebaoNationalView } = row;
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                        'warn-status': shebaoNationalView?.overListingPriceWarnFlag && this.needWarning,
                                    }}
                                    title={shebaoNationalView?.listingPrice ?? ''}
                                >{shebaoNationalView?.listingPrice ?? ''}</div>
                            </abc-table-cell>
                        );
                    },
                    minExpiryDateRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div class={{
                                    'is-disabled': row.v2DisableStatus,
                                    'warn-status': row.expiredWarnFlag && this.needWarning,
                                }}
                                     title={row.minExpiryDate}>
                                    {row.minExpiryDate}
                                </div>
                            </abc-table-cell>
                        );
                    },
                    profitRatRender: (h, row) => {
                        if (this.showGoodsProfit) {
                            return (
                                <abc-table-cell>
                                    <div class={{
                                        'is-disabled': row.v2DisableStatus,
                                        'warn-status': row.negativeProfitWarnFlag && this.needWarning,
                                    }} title={percent(row.profitRat)}>
                                        {isNull(row.profitRat) ? '' : percent(row.profitRat)}</div>
                                </abc-table-cell>
                            );
                        }
                    },
                    // 当前库存（总库存）
                    currentCountRender: (h, row) => {
                        const key = this.fetchParams.isSpu ? 'spuGoodsId' : 'goodsId';
                        const val = this.fetchParams.isSpu ? row.spuGoodsId : row.goodsId;
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                        'warn-status': this.needWarning && row.shortageWarnFlag,
                                        'current-stock-td': true,
                                    }}
                                    style="align-items: center;"
                                >
                                    <abc-popover
                                        trigger="hover"
                                        ref="currentCountPopover"
                                        placement="bottom-start"
                                        theme="yellow"
                                        popper-style={{
                                            padding: '0px', 'min-width': '177px',
                                        }}
                                        open-delay={500}
                                        onShow={() => {
                                            this.currentHoverId = row.goodsId;
                                            this.showCardName = 'current';
                                        }}
                                        onHide={() => {
                                            this.currentHoverId = '';
                                        }}
                                        disabled={!!this.fetchParams.isSpu}
                                    >

                                    <span slot="reference" style="padding-right:2px;">
                                        {row.dispGoodsCount}
                                    </span>

                                        {this.showCardName === 'current' ? <PopoverStockCard
                                            row={row}
                                            rowKey={key}
                                            currentHoverId={this.currentHoverId}
                                            fetchParams={{
                                                [key]: val,
                                                clinicId: this.selectedClinicId,
                                                goodsId: row.goodsId,
                                                pharmacyNo: this.fetchParams.pharmacyNo,
                                            }}
                                        /> : null}

                                    </abc-popover>
                                </div>
                            </abc-table-cell>
                        );
                    },
                    // 可售数量
                    stockCountRender: (h, row) => {
                        // const colors = this.$store.state.theme.style;
                        // const detailHandler = () => {
                        //     this.openBatchDetail(row, index);
                        // };
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                        'warn-status': this.needWarning && row.shortageWarnFlag,
                                        'current-stock-td': true,
                                    }}
                                    style="align-items: center;"
                                >
                                    <abc-popover
                                        trigger="hover"
                                        ref="stockCountPopover"
                                        placement="bottom-start"
                                        theme="yellow"
                                        popper-style={{
                                            padding: '0px', 'min-width': '177px',
                                        }}
                                        onShow={() => {
                                            this.currentHoverId = row.goodsId;
                                            this.showCardName = 'stock';
                                        }}
                                        onHide={() => {
                                            this.currentHoverId = '';
                                        }}

                                    >

                                    <span slot="reference" style="padding-right:2px;">
                                        {row.dispStockGoodsCount}
                                    </span>

                                        {this.showCardName === 'stock' ? <PopoverStockCard
                                            row={row}
                                            rowKey="goodsId"
                                            isStockCount
                                            currentHoverId={this.currentHoverId}
                                            fetchParams={{
                                                clinicId: this.selectedClinicId,
                                                goodsId: row.goodsId,
                                                pharmacyNo: this.fetchParams.pharmacyNo,
                                            }}
                                        /> : null}

                                    </abc-popover>
                                </div>
                            </abc-table-cell>
                        );
                    },
                    totalCostRender: (h, row) => {
                        if (this.showGoodsCost) {
                            return (
                                <abc-table-cell>
                                    <div class={{ 'is-disabled': row.v2DisableStatus }}
                                         title={moneyDigit(row.totalCost, 5)}>
                                        {moneyDigit(row.totalCost, 5)}</div>
                                </abc-table-cell>
                            );
                        }
                    },
                    recentAvgSellRender: (h, row) => {
                        const formatHandler = this.formatRecentAvgSell;
                        return (
                            <abc-table-cell>
                                <div class={{ 'is-disabled': row.v2DisableStatus }} title={formatHandler(row)}>
                                    {formatHandler(row)}</div>
                            </abc-table-cell>
                        );
                    },
                    turnoverDaysRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                        'warn-status': this.needWarning && row.turnoverDaysWarnFlag,
                                    }}>
                                    {
                                        isNull(row.turnoverDays) ? '-' : `${row.turnoverDays}天`
                                    }</div>
                            </abc-table-cell>
                        );
                    },

                    shortIdRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div class={{ 'is-disabled': row.v2DisableStatus }}> {row.shortId}</div>
                            </abc-table-cell>
                        );
                    },

                    medicineNmpnRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div class={{ 'is-disabled': row.v2DisableStatus }}> {row.medicineNmpn || row.medcineNpmn}</div>
                            </abc-table-cell>
                        );
                    },

                    lastSupplierNameRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <span class={{ 'is-disabled': row.v2DisableStatus }} title={row.lastSupplierName}> {row.lastSupplierName}</span>
                            </abc-table-cell>
                        );

                    },

                    brandRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div class={{ 'is-disabled': row.v2DisableStatus }}
                                     title={row.brandName}>{row.brandName}</div>
                            </abc-table-cell>
                        );
                    },
                    clinicPriceRender: (h, row) => {
                        const price = this.fetchParams.isSpu ? row.clinicPackagePrice : row.packagePrice;
                        return (
                            <abc-table-cell>
                                <div
                                    class={{ 'is-disabled': row.v2DisableStatus }}
                                    title={price}
                                >{price}</div>
                            </abc-table-cell>
                        );
                    },
                    shebaoCodeRender: (h, row) => {
                        const { shebaoNationalView } = row;
                        let icon = '';
                        if (shebaoNationalView?.isExpired) {
                            icon = <abc-icon icon="jinggao1" color="#ff9933" style="margin-left:8px;"></abc-icon>;
                        }
                        let isWarning = false;
                        if (shebaoNationalView?.specificationMatchStatus || shebaoNationalView?.medicineNmpnMatchStatus) {
                            isWarning = true;
                        }
                        return (
                            <abc-table-cell class="shebao-code-cell">
                                <abc-popover
                                    width="260px"
                                    trigger="hover"
                                    theme="yellow"
                                    placement="top"
                                    disabled={!isWarning}
                                >
                                    <abc-flex align="center" style="max-width: 180px" slot="reference">
                                        <div
                                            class={{
                                                'is-disabled': row.v2DisableStatus,
                                                'warn-status': shebaoNationalView?.shebaoCodeWarnFlag,
                                            }}
                                            style={{
                                                flex: 1,
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                whiteSpace: 'nowrap',
                                                color: isWarning ? 'var(--abc-color-Y2)' : 'var(--abc-color-T1)',
                                            }}
                                            title={shebaoNationalView?.shebaoCode === '不允许医保支付' ? '无医保码' : shebaoNationalView?.shebaoCode}
                                        >{shebaoNationalView?.shebaoCode === '不允许医保支付' ? '无医保码' : shebaoNationalView?.shebaoCode}</div>
                                        {icon}
                                    </abc-flex>
                                    <abc-flex align="baseline" gap="small">
                                        <abc-icon icon="n-alert-fill" size="12" color="var(--abc-color-OR1)"></abc-icon>
                                        <abc-flex vertical gap="small">
                                            <abc-text size="mini" style={{ color: 'var(--abc-color-OR1)' }}>合规风险</abc-text>
                                            {shebaoNationalView?.medicineNmpnMatchStatus === 1 && <abc-text size="mini" style={{ color: 'var(--abc-color-Y6)' }}>ABC 系统档案与医保目录准字不一致</abc-text>}
                                            {shebaoNationalView?.specificationMatchStatus === 1 && <abc-text size="mini" style={{ color: 'var(--abc-color-Y6)' }}>ABC 系统档案与医保目录规格无法对应</abc-text>}
                                        </abc-flex>
                                    </abc-flex>
                                </abc-popover>
                            </abc-table-cell>
                        );
                    },
                    standardCodeRender: (h, row) => {
                        const { shebaoNationalView } = row;
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                    }}
                                    title={shebaoNationalView?.standardCode ?? ''}
                                >{shebaoNationalView?.standardCode ?? ''}</div>
                            </abc-table-cell>
                        );
                    },
                    medicalFeeGradeNameRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                        'warn-status': row.shebaoCodeWarnFlag,
                                    }}
                                    title={shebaoInfo?.medicalFeeGradeName}
                                >{shebaoInfo?.medicalFeeGradeName}</div>
                            </abc-table-cell>
                        );
                    },
                    barCodeRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div
                                    class={{ 'is-disabled': row.v2DisableStatus }}
                                    title={row?.barCode}
                                >{row?.barCode}</div>
                            </abc-table-cell>
                        );
                    },
                    // 限制说明
                    restrictionRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        return (
                            <abc-table-cell>
                                <span
                                    class={{ 'is-disabled': row.v2DisableStatus }}
                                    title={shebaoInfo?.restriction}
                                >{shebaoInfo?.restriction}</span>
                            </abc-table-cell>
                        );
                    },

                    // 限价
                    shebaoCodePriceLimitedRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        return (
                            <abc-table-cell>
                                <span
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                        'warn-status ': shebaoInfo?.shebaoOverPriceWarnFlag,
                                    }}
                                    title={shebaoInfo?.shebaoCodeCurrentPriceLimited}
                                >{shebaoInfo?.shebaoCodeCurrentPriceLimited}</span>
                            </abc-table-cell>
                        );
                    },
                    // 到期时间
                    shebaoCodeEndDateRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        const content = shebaoInfo?.shebaoCodeCurrentEndDate ? parseTime(shebaoInfo?.shebaoCodeCurrentEndDate, 'y-m-d', true) : '';
                        return (
                            <abc-table-cell>
                                <span
                                    class={{ 'is-disabled': row.v2DisableStatus }}
                                    title={content}
                                >{content}</span>
                            </abc-table-cell>
                        );
                    },
                    // 医保支付
                    shebaoCodePayModeRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <span
                                    class={{ 'is-disabled': row.v2DisableStatus }}
                                    title={row?.shebaoPayModeName}
                                >{row?.shebaoPayModeName}</span>
                            </abc-table-cell>
                        );
                    },
                    // 产品标识码
                    drugIdentificationCodeRender: (h, row) => {
                        if (!row.drugIdentificationCode) return '';

                        return (
                            <abc-table-cell>
                                <MultipleCodePopover
                                    goodsIdentificationCodeList={row.drugIdentificationCode?.split(',')}
                                    style="width:100%;"
                                >
                                </MultipleCodePopover>
                            </abc-table-cell>
                        );
                    },
                    // 有无追溯码
                    traceableCodeTypeRender: (h, row) => {
                        let hasCode = (row?.traceableCodeNoInfoList?.[0]?.type === TraceableCodeTypeEnum.HAS_CODE || false);
                        let showEmpty = false;
                        if (!row.traceableCodeNoInfoList?.[0]) {
                            // 西药 中成药 器械等支持追溯码都可以勾选
                            if (TraceCode.isSupportTraceCode(row.typeId)) {
                                hasCode = true;
                            }
                            // 中药饮片、颗粒，需要看后台配置，开启功能的地区可支持勾选，未开启功能的地区不可勾选 TODO
                            // eslint-disable-next-line no-constant-condition
                            if ([
                                GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                                GoodsTypeIdEnum.CHINESE_MEDICINE, // 中药
                                GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES, //非配方饮片
                                GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE, //中药颗粒
                            ].includes(row.typeId)) {
                                if (TraceCode.isSupportTraceCode(row.typeId)) {
                                    hasCode = true;
                                } else {
                                    showEmpty = true;
                                }
                            }
                        }
                        if (showEmpty) {
                            return (
                                <abc-table-cell>
                                    <span className={{ 'is-disabled': row.v2DisableStatus }}></span>
                                </abc-table-cell>
                            );
                        }
                        return (
                            <abc-table-cell>
                                <span className={{ 'is-disabled': row.v2DisableStatus }}
                                      title={!hasCode ? (row?.traceableCodeNoInfoList?.[0]?.value || '无码') : '有码'}> {!hasCode ? (row?.traceableCodeNoInfoList?.[0]?.value || '无码') : '有码'}</span>
                            </abc-table-cell>
                        );
                    },
                    goodsTagRender: (h, row) => {
                        const goodsTagList = (row.goodsTagList || []).map((item, index) => ({
                            tagId: index,
                            tagName: item.name,
                            viewMode: 0,
                        }));
                        return goodsTagList.length ?
                            <abc-table-cell>
                                <OverflowFlexTagsWrapper tags={goodsTagList} variant="outline" size="tiny"/>
                            </abc-table-cell> : '';
                    },
                    stockWarnRuleRender: (h, row) => {
                        const {
                            shortageWarnEnable, warnStockCount, configTurnoverDays, shortageWarnCountSmallUnit, pieceUnit = '', packageUnit = '',
                        } = row;
                        let str = '';
                        if (shortageWarnEnable) {
                            str += `库存不足${warnStockCount}${shortageWarnCountSmallUnit ? pieceUnit : packageUnit}时`;
                        } else {
                            str += `周转天数不足${configTurnoverDays}天时`;
                        }
                        return (
                            <abc-table-cell class="ellipsis" title={str}>
                                <div class="ellipsis">{ str }</div>
                            </abc-table-cell>
                        );
                    },
                };
            },
        },
        methods: {
            sortChange({
                orderBy,
                orderType,
            }) {
                this.$emit('change-sort', {
                    orderBy,
                    orderType,
                });
            },
            warningSort(el, orderBy, sortType = 'asc') {
                el.stopPropagation();
                el.preventDefault();
                this.sortChange({
                    orderBy, orderType: sortType,
                });
            },
            formatRecentAvgSell(item) {
                if (!item) return '';
                const unit = isChineseMedicine(item) ? item.pieceUnit : item.packageUnit;
                return count1(item.recentAvgSell, 2) + unit;
            },
        },
    };
</script>

<style lang="scss">
@import "@/styles/mixin.scss";

.inventory-abc-table {
    .abc-table-cell {
        .warn-status {
            color: var(--abc-color-Y2);
        }

        .is-disabled {
            color: var(--abc-color-T2);
        }

        &.shebao-code-cell {
            > div {
                width: 100%;
            }
        }
    }

    .sort-bar {
        width: 8px;
    }

    .table-fixed-header thead tr {
        th:first-child {
            padding-left: 12px;
        }
    }

    .table-scroll-header thead tr {
        th:last-child {
            padding-right: 12px;
        }
    }

    .abc-table__fixed-left-body-wrapper .table-tbody tr {
        td:first-child {
            padding-left: 12px;
        }
    }
    // 解决药品物资打开弹窗，滚动条正好和弹窗底部按钮重合的问题，导致按钮无法点击
    .abc-table__body-wrapper {
        overflow-y: auto;
    }

    tbody td {
        border-right: none;
        border-left: none;

        > div {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    thead th {
        height: 36px !important;
        color: $T2;
        border-right: none;
        border-left: none;
    }

    .warning-info-wrapper {
        position: relative;
        display: inline-flex;
        cursor: pointer;

        .warn-popover {
            position: absolute;
            left: -30px;
            width: 30px;
            text-align: right;
        }
    }

    .warning-count {
        position: relative;
        display: inline-flex;
        padding: 0 3px;
        margin-right: 6px;
        font-size: 12px;
        font-style: italic;
        line-height: 20px;
        color: white;
        cursor: pointer;
        background-color: #ff9933;
        border-radius: var(--abc-border-radius-small);

        &::after {
            position: absolute;
            top: 7px;
            right: -4px;
            display: inline-block;
            content: ' ';

            /* box-shadow: 0px 2px 8px 0px #d9dbe3; */
            @include triangle(6px, 6px, #ff9933, 'right');
        }
    }
}
</style>
