<template>
    <abc-flex class="species-filter-handler-bar" justify="space-between">
        <abc-space>
            <goods-auto-complete
                ref="autoComplete"
                :auto-focus-first="false"
                :clear-search-key="false"
                :clinic-id="
                    isChainAdmin ? selectedClinicId : currentClinicInfo.clinicId
                "
                :custom-type-id-list="customTypeId"
                :enable-barcode-detector="!isDialogShowing"
                :is-spu="isEyeglass && !!viewType"
                :only-stock="onlyStock"
                :pharmacy-no="pharmacyNo"
                :placeholder="searchHint"
                :search.sync="searchKey"
                :type-id-list="typeIdList"
                :width="180"
                :with-stock="false"
                class="abc-autocomplete-search"
                enable-local-search
                focus-show
                @searchGoods="searchGoods"
                @selectGoods="selectGoods"
            >
                <abc-search-icon slot="prepend"></abc-search-icon>
                <abc-delete-icon v-if="searchKey" slot="append" @delete="clearSearch"></abc-delete-icon>
            </goods-auto-complete>

            <!--门店筛选-->
            <clinic-select
                v-if="showClinicSelect"
                v-model="selectedClinicId"
                @change="handleChangeClinic"
            ></clinic-select>
            <abc-tabs-v2
                v-model="curGoodsType"
                :option="goodsTypesTabList"
                type="outline"
                @change="handleChangeTabGoodsType"
            >
            </abc-tabs-v2>
            <!--类型筛选-->
            <biz-goods-type-cascader
                ref="goodsTypeCascader"
                v-model="selectedTypes"
                :goods-type-options="goodsTypeOptions"
                @change="handleChangeType"
            >
            </biz-goods-type-cascader>

            <abc-select
                v-if="showPriceTypeSelect"
                v-model="priceType"
                :width="90"
                clearable
                placeholder="定价模式"
                @change="handlerPriceTypeChange"
            >
                <abc-option :key="PriceType.PRICE" :label="PriceTypeName[PriceType.PRICE]" :value="PriceType.PRICE">
                </abc-option>
                <abc-option :key="PriceType.PKG_PRICE_MAKEUP" :label="PriceTypeName[PriceType.PKG_PRICE_MAKEUP]" :value="PriceType.PKG_PRICE_MAKEUP">
                </abc-option>
            </abc-select>

            <goods-filter-popover
                :include-disable-stock="includeDisableStock"
                :include-zero-stock="includeZeroStock"
                :item-keys="itemKeys"
                :params="params"
                :pharmacy-type="pharmacyType"
                :shebao-count="shebaoCount"
                :show-eye-glasses-filter="showEyeGlassesFilter"
                :show-goods-filter="!isEyeglass"
                :show-shebao-filter="!isEyeglass"
                :is-support-drug-identification-code="isSupportCodelessArea"
                :is-support-position="!isChainAdmin"
                is-stock-entry-stat
                is-stock-goods
                :show-goods-basic-filter="true"
                :is-show-shebao-pay-mode="isChainAdmin ? selectedClinicId !== currentClinicInfo.chainId : true"
                :need-tranfer-no-use-shebao="true"
                @change="handleChangeSocialStatus"
            >
                <template #brandName="{ formData }">
                    <abc-autocomplete
                        v-model.trim="formData.brandName"
                        :async-fetch="true"
                        :auto-focus-first="false"
                        :delay-time="0"
                        :fetch-suggestions="fetchOptions('brands')"
                        :width="100"
                        clearable
                        focus-show
                        inner-width="100px"
                        @clear="formData.brandName = ''"
                        @enterEvent="handleSelect('brandName', $event, formData)"
                    >
                        <template #suggestions="props">
                            <dt
                                :class="{ selected: props.index === props.currentIndex }"
                                class="suggestions-item"
                                @click="handleSelect('brandName', props.suggestion, formData)"
                            >
                                {{ props.suggestion.value }}
                            </dt>
                        </template>
                    </abc-autocomplete>
                </template>

                <template #material="{ formData }">
                    <abc-autocomplete
                        v-model.trim="formData.material"
                        :async-fetch="true"
                        :auto-focus-first="false"
                        :delay-time="0"
                        :fetch-suggestions="fetchOptions('materials')"
                        :width="100"
                        clearable
                        focus-show
                        inner-width="100px"
                        @clear="formData.material = ''"
                        @enterEvent="handleSelect('material', $event, formData)"
                    >
                        <template #suggestions="props">
                            <dt
                                :class="{ selected: props.index === props.currentIndex }"
                                class="suggestions-item"
                                @click="handleSelect('material', props.suggestion, formData)"
                            >
                                {{ props.suggestion.value }}
                            </dt>
                        </template>
                    </abc-autocomplete>
                </template>

                <template #spec="{ formData }">
                    <abc-autocomplete
                        v-model.trim="formData.spec"
                        :async-fetch="true"
                        :auto-focus-first="false"
                        :delay-time="0"
                        :fetch-suggestions="fetchOptions('specs')"
                        :width="100"
                        clearable
                        focus-show
                        inner-width="100px"
                        @clear="formData.spec = ''"
                        @enterEvent="handleSelect('spec', $event, formData)"
                    >
                        <template #suggestions="props">
                            <dt
                                :class="{ selected: props.index === props.currentIndex }"
                                class="suggestions-item"
                                @click="handleSelect('spec', props.suggestion, formData)"
                            >
                                {{ props.suggestion.value }}
                            </dt>
                        </template>
                    </abc-autocomplete>
                </template>

                <template #color="{ formData }">
                    <abc-autocomplete
                        v-model.trim="formData.color"
                        :async-fetch="true"
                        :auto-focus-first="false"
                        :delay-time="0"
                        :fetch-suggestions="fetchOptions('colors')"
                        :width="100"
                        clearable
                        focus-show
                        inner-width="100px"
                        @clear="formData.color = ''"
                        @enterEvent="handleSelect('color', $event, formData)"
                    >
                        <template #suggestions="props">
                            <dt
                                :class="{ selected: props.index === props.currentIndex }"
                                class="suggestions-item"
                                @click="handleSelect('color', props.suggestion, formData)"
                            >
                                {{ props.suggestion.value }}
                            </dt>
                        </template>
                    </abc-autocomplete>
                </template>
            </goods-filter-popover>

            <abc-tabs
                v-if="isEyeglass"
                v-model="viewType"
                :option="viewTypeOptions"
                size="middle"
                type="outline"
                @change="changeViewTypeHandler"
            >
            </abc-tabs>
        </abc-space>

        <abc-space v-if="!isBatchUpdateView">
            <abc-dropdown
                v-if="showCreateBtn"
                placement="bottom-end"
                style="width: auto;"
                data-cy="inventory-stock-add-dropdown"
                @change="handleCreate"
            >
                <template #reference>
                    <abc-check-access>
                        <abc-button
                            theme="success"
                            icon="s-b-add-line-medium"
                            data-cy="inventory-stock-add-archives"
                        >
                            新建档案
                        </abc-button>
                    </abc-check-access>
                </template>


                <abc-dropdown-item :value="1" label="新建药品" data-cy="inventory-stock-add-medicine">
                </abc-dropdown-item>

                <abc-dropdown-item :value="2" label="新建物资" data-cy="inventory-stock-add-materials">
                </abc-dropdown-item>

                <abc-dropdown-item :value="7" label="新建商品" data-cy="inventory-stock-add-goods">
                </abc-dropdown-item>

                <abc-dropdown-item
                    v-if="showCreateEyeGlassesBtn"
                    :value="GoodsTypeEnum.EYEGLASSES"
                    label="新建眼镜"
                    data-cy="inventory-stock-add-eyeglass"
                >
                </abc-dropdown-item>
            </abc-dropdown>

            <abc-check-access v-if="!isForbiddenExport">
                <abc-dropdown v-if="isChainAdmin && !selectedClinicId" placement="bottom-end" style="width: auto; height: 32px; margin-right: 0; font-size: 14px;">
                    <div slot="reference">
                        <abc-popover
                            v-if="isExportLoading && isExportClinic"
                            ref="exportPopover"
                            :disabled="!isExportingLongTime"
                            placement="bottom"
                            style="display: inline-flex; vertical-align: top;"
                            theme="yellow"
                            trigger="hover"
                        >
                            <div slot="reference">
                                <abc-button
                                    class="export-btn"
                                    variant="ghost"
                                    @click="exportGoodsClinic"
                                >
                                    <div class="export-btn-content">
                                        <abc-loading no-cover small></abc-loading>
                                        <span style=" margin-left: 18px; color: #7a8794;">{{ exportWording }}</span>
                                    </div>
                                </abc-button>
                            </div>
                            <div v-if="isExportingLongTime" style="margin-top: 8px;">
                                <p>可能等待时间较长，你可以稍后回来下载</p>
                            </div>
                        </abc-popover>
                        <abc-button
                            v-else
                            :loading="loading"
                            icon="n-upload-line"
                            variant="ghost"
                        >
                            {{ loading ? '导出中' : '导出' }}
                        </abc-button>
                    </div>
                    <abc-dropdown-item>
                        <span @click="exportExcel"> 药品物资档案</span>
                    </abc-dropdown-item>
                    <abc-dropdown-item>
                        <span @click="exportGoodsClinic">药品物资门店分布</span>
                    </abc-dropdown-item>
                </abc-dropdown>
                <abc-button
                    v-else
                    :loading="loading"
                    icon="n-upload-line"
                    variant="ghost"
                    @click="exportExcel"
                >
                    {{ loading ? '导出中' : '导出' }}
                </abc-button>
            </abc-check-access>

            <abc-dropdown
                v-if="showPriceAdjustmentConfig"
                placement="bottom-start"
            >
                <abc-button
                    slot="reference"
                    shape="square"
                    variant="ghost"
                >
                    批量操作
                </abc-button>
                <abc-dropdown-item style="padding: 0;">
                    <abc-tooltip
                        :disabled="!disabledPriceAdjustment"
                        content="该门店暂无自主定价权，请联系总部开启"
                        placement="top"
                    >
                        <div style="padding: 6px 12px;" @click="handlePriceAdjustment">
                            批量调价
                        </div>
                    </abc-tooltip>
                </abc-dropdown-item>

                <abc-dropdown-item v-if="isShowPrintPriceTagButton" style="padding: 0;">
                    <div style="padding: 6px 12px;" @click="handleClickEnterPrintPriceTagMode">
                        批量打印价签
                    </div>
                </abc-dropdown-item>

                <abc-dropdown-item v-if="isSupportCodelessArea" style="padding: 0;">
                    <div style="padding: 6px 12px;" @click="handleSetNoTraceCode">
                        批量标记无追溯码
                    </div>
                </abc-dropdown-item>
            </abc-dropdown>

            <abc-button
                v-else-if="isShowPrintPriceTagButton"
                slot="reference"
                shape="square"
                variant="ghost"
                @click="handleClickEnterPrintPriceTagMode"
            >
                批量打印价签
            </abc-button>

            <abc-dropdown
                v-if="showCustomHeaderSettingEntry"
                placement="bottom-end"
                style="width: 32px;"
            >
                <abc-button slot="reference" icon="n-more-line-medium" variant="ghost"></abc-button>

                <abc-dropdown-item v-if="showGuanbiaoStat" label="贯标统计" style="padding: 0;">
                    <div style="padding: 6px 12px;" @click="handleGuanbiao">
                        <span>贯标统计</span>
                    </div>
                </abc-dropdown-item>

                <abc-dropdown-item label="设置展示字段" style="padding: 0;">
                    <div style="padding: 6px 12px;" @click="handleCustomDisplay">
                        <span>设置展示字段</span>
                    </div>
                </abc-dropdown-item>
                <abc-dropdown-item v-if="isEyeglass && isChainSubStore" style="padding: 0;">
                    <div style="padding: 6px 12px;" @click="handleSyncAdminGoods">
                        <span>同步总部档案</span>
                    </div>
                </abc-dropdown-item>
            </abc-dropdown>
        </abc-space>

        <template v-else>
            <abc-space v-if="viewMode === InventoryViewMode.BATCH_PRINT_PRICE_TAG">
                <abc-button :count="selectedCount" :disabled="!selectedCount" @click="handleClickPrintPriceTagMode">
                    {{ selectedCount ? '打印价签' : '未选择打印商品' }}
                </abc-button>
                <abc-button variant="ghost" @click="handleCancelBatchPrintPriceTag">
                    取消
                </abc-button>
            </abc-space>
            <abc-space v-else-if="viewMode === InventoryViewMode.BATCH_SET_NO_TRACE_CODE">
                <abc-button
                    :disabled="!batchNoSetTraceCodeNumber"
                    :count="batchNoSetTraceCodeNumber"
                    :loading="traceBtnLoading"
                    @click="handleClickBatchNoSetTraceCode"
                >
                    {{ batchNoSetTraceCodeNumber ? '批量标记无码' : '未选择无码商品' }}
                </abc-button>
                <abc-button variant="ghost" @click="handleCancelBatchNoSetTraceCode">
                    取消
                </abc-button>
            </abc-space>
        </template>
        <abc-modal
            v-if="showProcessDialog"
            v-model="showProcessDialog"
            append-to-body
            size="large"
            :show-footer="false"
            custom-class="batch-update-goods-process-modal"
            :content-styles="{
                height: '416px'
            }"
            :on-close="closeProgressDialog"
        >
            <div>
                <h4 style="font-size: 16px; font-weight: bold; line-height: 24px; text-align: center;">
                    批量修改档案
                </h4>
                <process-loading
                    :total-count="progressData.totalCount"
                    :current-count="progressData.currentCount"
                    :is-finish="progressData.isFinished"
                    process-text="修改中"
                    finish-text="档案修改完成"
                    style="margin-top: 88px;"
                >
                    <template #finishTips>
                        <span v-if="progressData.currentCount >= progressData.totalCount ">{{ progressData.currentCount }}种商品档案修改成功</span>
                        <span v-else>{{ progressData.currentCount }}种商品档案修改成功，{{ progressData.totalCount - progressData.currentCount }}种商品无需修改</span>
                    </template>
                </process-loading>
            </div>
        </abc-modal>
    </abc-flex>
</template>

<script>
    import {
        PriceType, PriceTypeName,
    } from 'views/common/inventory/constants';

    const GoodsAutoComplete = () => import('views/inventory/common/goods-auto-complete');

    import ClinicSelect from 'views/layout/clinic-select/clinic-select';
    import GoodsFilterPopover from 'views/inventory/components/goods-filter-popover.vue';

    import {
        GoodsSubTypeEnum,
        GoodsTypeEnum,
        PharmacyTypeEnum,
        GoodsTypeIdEnum,
    } from '@abc/constants';

    import { mapGetters } from 'vuex';
    import { goodsFullName } from '@/filters/index.js';
    import { EyeglassAPI } from 'api/goods/eyeglass.js';
    import { ExportStatus } from 'views/statistics/core/entities/export';
    import {
        BatchModifyEnum, InventoryViewMode,
    } from '@/views-pharmacy/inventory/constant';
    import GoodsAPI from 'api/goods';
    import { isNull } from 'utils/lodash';
    import { filterEmptyValue } from '@/utils';
    import ProcessLoading from 'views/inventory/goods-in/components/import-excel-dialog/process-loading.vue';
    import clone from 'utils/clone';

    const BizGoodsTypeCascader = () => import('@/components-composite/biz-goods-type-cascader');
    export default {
        name: 'SpeciesFilter',
        components: {
            ProcessLoading,
            BizGoodsTypeCascader,
            GoodsAutoComplete,
            ClinicSelect,
            GoodsFilterPopover,
        },
        props: {
            searchHint: String,
            exportClinicStatus: Number,
            pharmacyNo: {
                type: Number,
                default: 0,
            },
            showPriceAdjustmentConfig: Boolean,
            /**
             * 药房类型
             */
            pharmacyType: {
                type: Number,
                default: PharmacyTypeEnum.LOCAL_PHARMACY,
            },
            showCreateEyeGlassesBtn: Boolean,

            isDialogShowing: Boolean,
            goodsAllTypes: {
                type: Array,
                default: () => [],
            },
            shebaoCount: {
                type: Object,
                required: true,
            },
            disabledPriceAdjustment: {
                type: Boolean,
                default: false,
            },
            showGuanbiaoStat: {
                type: Boolean,
                default: false,
            },
            showClinicSelect: {
                type: Boolean,
                default: false,
            },
            showCreateBtn: {
                type: Boolean,
                default: false,
            },
            showPriceTypeSelect: {
                type: Boolean,
                default: false,
            },
            //搜索关键字
            keyword: String,
            // 筛选门店idd
            clinicId: String,
            // 药品类型
            customTypeId: String,
            typeIdList: {
                type: Array,
                default: () => [],
            },
            params: Object,
            filterOnlyStock: Boolean, // 筛选入过库的品种
            exportLoading: {
                type: Boolean,
                default: false,
            }, // 筛选入过库的品种
            isBatchUpdateView: {
                type: Boolean,
                default: false,
            },
            viewMode: {
                type: String,
                default: InventoryViewMode.NORMAL,
            },
            selectedCount: {
                type: Number,
                default: 0,
            },
            batchNoSetTraceCodeNumber: {
                type: Number,
                default: 0,
            },
            warningKeys: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            goodsIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            isAllChecked: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            const viewTypeOptions = Object.freeze([{
                label: 'SPU',
                value: 1,
            }, {
                label: 'SKU',
                value: 0,
            }]);
            return {
                PriceType,
                PriceTypeName,
                GoodsTypeEnum,
                viewTypeOptions,
                searchKey: '',
                selectedClinicId: '',
                selectedTypes: [],
                includeZeroStock: 1,
                includeDisableStock: 1,
                curGoodsType: '全部',
                viewType: 1,
                priceType: '',
                options: {
                    brands: [],
                    colors: [],
                    materials: [],
                    specs: [],
                },
                loading: false,
                isExportClinic: false,
                exportStatus: -1,
                progressData: {
                    totalCount: 0,
                    currentCount: 0,
                    isFinished: false,
                },
                traceBtnLoading: false,
                showProcessDialog: false,
            };
        },
        computed: {
            InventoryViewMode() {
                return InventoryViewMode;
            },
            ...mapGetters([
                'isChainAdmin',
                'isChain',
                'currentClinic',
                'isChainSubStore',
                'inventoryTodo',
                'isForbiddenExport',
            ]),
            ...mapGetters('viewDistribute',['viewDistributeConfig']),
            isSupportPrintPriceTag() {
                return this.viewDistributeConfig.Inventory.isSupportPrintPriceTag;
            },
            isShowPrintPriceTagButton() {
                return this.isSupportPrintPriceTag && (this.isChainAdmin ? !!this.selectedClinicId : true);
            },
            isSupportCodelessArea() {
                return !!this.$abcSocialSecurity.defaultDrugTraceCode?.isSupportDefaultTraceCode;
            },
            // 选择了门店显示对应的门店入过库的商品
            onlyStock() {
                if (this.isVirtualPharmacy) return false;
                if (this.filterOnlyStock) return true;

                return !!this.selectedClinicId;
            },
            currentClinicInfo() {
                return this.currentClinic || {};
            },
            isVirtualPharmacy() {
                return this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            showCustomHeaderSettingEntry() {
                return this.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY;
            },
            goodsTypeOptions() {
                let typeOptions = this.goodsAllTypes;
                if (this.isVirtualPharmacy) {
                    typeOptions = this.goodsAllTypes.filter((item) => {
                        return item.goodsType === GoodsTypeEnum.MEDICINE &&
                            item.goodsSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine;
                    }) || [];
                }
                const curTypeTabObj = this.goodsTypesTabList.find((item) => {
                    return item.value === this.curGoodsType;
                });
                if (curTypeTabObj?.params) {
                    typeOptions = typeOptions.filter((item) => {
                        return (curTypeTabObj?.params?.typeIdList?.includes(Number(item.id)) || curTypeTabObj.isAllType);
                    }) || [];
                }
                return typeOptions;
            },
            isEyeglass() {
                return this.curGoodsType === '眼镜';
            },
            hasEyeglasses() {
                return this.viewDistributeConfig.Inventory.hasEyeglasses;
            },
            showEyeGlassesFilter() {
                return this.hasEyeglasses && (this.isEyeglass || this.curGoodsType === '全部');
            },
            goodsTypesTabList() {
                return [
                    {
                        label: '全部',
                        value: '全部',
                        params: {},
                        isAllType: true,
                        isShow: true,
                    },
                    {
                        label: '成药',
                        value: '成药',
                        params: {
                            typeIdList: [ GoodsTypeIdEnum.MEDICINE_WESTERN, GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT ],
                        },
                        isShow: this.inventoryTodo.westCount > 0,
                    },
                    {
                        label: '饮片',
                        value: '饮片',
                        params: {
                            typeIdList: [ GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES ],
                        },
                        isShow: this.inventoryTodo.chinesePiecesCount > 0,
                    },
                    {
                        label: '颗粒',
                        value: '颗粒',
                        params: {
                            typeIdList: [ GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE ],
                        },
                        isShow: this.inventoryTodo.chineseGranuleCount > 0,
                    },
                    {
                        label: '眼镜',
                        value: '眼镜',
                        params: {
                            typeIdList: [
                                GoodsTypeIdEnum.EYEGLASSES,
                                GoodsTypeIdEnum.LENS,
                                GoodsTypeIdEnum.BRACKET,
                                GoodsTypeIdEnum.OK_LENS,
                                GoodsTypeIdEnum.RIGID_LENS,
                                GoodsTypeIdEnum.SUNGLASSES,
                                GoodsTypeIdEnum.SOFT_LENS,
                            ],
                        },
                        isShow: this.hasEyeglasses && this.inventoryTodo.eyeCount > 0,
                    },
                    {
                        label: '器械',
                        value: '器械',
                        params: {
                            typeIdList: [ GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL ],
                        },
                        isShow: this.inventoryTodo.medicalMaterialCount > 0,
                    },
                    {
                        label: '物资',
                        value: '物资',
                        params: {
                            typeIdList: [
                                GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL,
                                GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS,
                            ],
                        },
                        isShow: this.inventoryTodo.materialCount > 0,
                    },
                    {
                        label: '商品',
                        value: '商品',
                        params: {
                            typeIdList: [
                                GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT,
                                GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE,
                                GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD,
                                GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT,
                            ],
                        },
                        isShow: this.inventoryTodo.additionalCount > 0,
                    },
                ].filter((item) => {
                    if (this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                        if (['全部', '饮片', '颗粒'].includes(item.label)) {
                            return item.isShow;
                        }
                        return false;
                    }
                    return item.isShow;
                });
            },
            itemKeys() {
                if (!this.selectedTypes.length) {
                    return ['brandName', 'refractiveIndex', 'customType', 'addLight', 'wearCycle', 'material', 'spherical', 'focalLength', 'lenticular', 'spec', 'color'];
                }
                let keys = ['brandName'];
                const map = {
                    [GoodsTypeIdEnum.LENS]: ['refractiveIndex', 'customType', 'addLight', 'spherical', 'lenticular' ],
                    [GoodsTypeIdEnum.BRACKET]: ['material', 'color'],
                    [GoodsTypeIdEnum.OK_LENS]: ['spec'],
                    [GoodsTypeIdEnum.SOFT_LENS]: ['focalLength', 'wearCycle', 'color'],
                    [GoodsTypeIdEnum.RIGID_LENS]: ['wearCycle', 'spherical', 'lenticular'],
                    [GoodsTypeIdEnum.SUNGLASSES]: ['spec'],
                };

                this.selectedTypes.forEach(([item]) => {
                    keys = keys.concat(map[item.id] || []);
                });

                return [...new Set(keys)];
            },
            canExportDownload() {
                return this.exportStatus === ExportStatus.GENERATED;
            },

            isExportLoading() {
                return (
                    this.exportStatus === ExportStatus.GENERATING ||
                    this.exportStatus === ExportStatus.GENERATING_LONG_TIME
                );
            },

            isExportingLongTime() {
                return this.exportStatus === ExportStatus.GENERATING_LONG_TIME;
            },
            exportWording() {
                let wording = '导出';
                switch (this.exportStatus) {
                    case ExportStatus.NONE:
                    case ExportStatus.DOWNLOADED:
                        wording = '导出';
                        break;

                    case ExportStatus.GENERATING:
                    case ExportStatus.GENERATING_LONG_TIME:
                        wording = '导出中';
                        break;
                    case ExportStatus.GENERATED:
                        wording = '点击下载';
                        break;
                    case ExportStatus.FAILED:
                        wording = '导出失败';
                        break;
                    default:
                        break;
                }
                return wording;
            },
        },

        watch: {
            searchKey(val) {
                if (!val) {
                    this.clearSearch();
                }
            },
            exportLoading(val) {
                this.loading = val;
            },
            exportClinicStatus(val) {
                this.exportStatus = val;
            },
        },
        beforeDestroy() {
            clearInterval(this.timer);
        },
        methods: {
            closeProgressDialog() {
                clearInterval(this.timer);
                this.showProcessDialog = false;
            },
            selectGoods(goods) {
                if (!goods) return;
                this.searchKey = goodsFullName(goods);
                this.handleChangeFetchParams({
                    goodsId: goods.id,
                    spuGoodsId: goods.spuGoodsId,
                    keyword: '',
                });
            },
            searchGoods(val) {
                this.handleChangeFetchParams({
                    keyword: val.trim(),
                    goodsId: '',
                });
            },
            clearSearch() {
                this.searchKey = '';
                this.handleChangeFetchParams({
                    keyword: '',
                    goodsId: '',
                    spuGoodsId: '',
                });
            },
            handleChangeClinic() {
                this.handleChangeFetchParams({
                    clinicId: this.selectedClinicId,
                });
            },
            handleChangeTabGoodsType(value, typeObj) {
                this.viewType = 1;
                if (this.$refs.goodsTypeCascader) {
                    this.$refs.goodsTypeCascader.clear();
                }

                this.handleChangeFetchParams({
                    typeId: typeObj?.params?.typeIdList || [],
                    customTypeId: [],
                    isSpu: value === '眼镜' ? 1 : undefined,
                });
            },
            handleChangeType(typeIdList, customTypeIdList) {

                // 受分类筛选的限制
                if (typeIdList?.length || customTypeIdList?.length) {
                    this.handleChangeFetchParams({
                        typeId: typeIdList,
                        customTypeId: customTypeIdList,
                    });
                } else {
                    // 获取分类的筛选
                    const curTypeTabObj = this.goodsTypesTabList.find((item) => {
                        return item.value === this.curGoodsType;
                    });
                    const curTypeIdList = curTypeTabObj?.params?.typeIdList || [];
                    this.handleChangeFetchParams({
                        typeId: curTypeIdList,
                        customTypeId: [],
                    });
                }
            },
            handlerPriceTypeChange() {
                this.handleChangeFetchParams({
                    priceType: this.priceType,
                });
            },
            changeViewTypeHandler() {
                const isSpu = this.viewType;
                this.handleChangeFetchParams({
                    isSpu,
                });
            },
            handleChangeSocialStatus(params) {
                this.handleChangeFetchParams({
                    ...params,
                });
            },
            exportExcel() {
                this.$emit('export');
                this.isExportClinic = false;
            },
            exportGoodsClinic() {
                this.$emit('export-clinic');
                this.isExportClinic = true;
            },
            handleCreate(val) {
                this.$emit('create', val);
            },
            handlePriceAdjustment() {
                this.$emit('change-price-adjustment');
            },
            handleSetNoTraceCode() {
                this.$emit('update:viewMode', InventoryViewMode.BATCH_SET_NO_TRACE_CODE);
            },
            handleGuanbiao() {
                this.$emit('change-guanbiao');
            },
            handleCustomDisplay() {
                this.$emit('change-custom-display');
            },
            handleChangeFetchParams(params) {
                this.$emit('refresh-list', params);
            },
            async handleSyncAdminGoods() {
                // 同步连锁总部的档案
                this.$emit('update-chain-admin');
            },

            async getSpuSearchCondition() {
                const { data } = await EyeglassAPI.getSpuSearchCondition();
                if (data) {
                    const {
                        brands,colors,materials,specs,
                    } = data;
                    this.options.brands = brands.map((e) => ({
                        label: e,value: e,
                    })) || [];
                    this.options.materials = materials.map((e) => ({
                        label: e,value: e,
                    })) || [];
                    this.options.colors = colors.map((e) => ({
                        label: e,value: e,
                    })) || [];
                    this.options.specs = specs.map((e) => ({
                        label: e,value: e,
                    })) || [];
                }
            },
            fetchOptions(key) {
                const options = this.options[key];
                return (val, callback) => callback(options.filter((item) => item.value.includes(val)));
            },
            handleClickEnterPrintPriceTagMode() {
                this.$emit('update:viewMode', InventoryViewMode.BATCH_PRINT_PRICE_TAG);
            },
            handleClickPrintPriceTagMode() {
                this.$emit('batch-print-price-tag');
            },
            getParams(needMoreParam = false, isPriceAdjust = false) {
                const params = {};
                const fetchParams = clone(this.params);
                if (needMoreParam) {
                    if (fetchParams.keyword) {
                        params.keyword = fetchParams.keyword;
                    }
                    if (fetchParams.clinicId) {
                        params.clinicId = fetchParams.clinicId;
                    }
                    if (fetchParams.goodsId) {
                        params.goodsId = fetchParams.goodsId;
                    }
                    if (fetchParams.unGspWarn) {
                        params.unGspWarn = fetchParams.unGspWarn;
                    }
                }

                if (fetchParams.typeId.length) {
                    params.typeId = fetchParams.typeId;
                }
                if (fetchParams.customTypeId.length) {
                    params.customTypeId = fetchParams.customTypeId;
                }
                if (fetchParams.minProfitRat) {
                    params.minProfitRat = fetchParams.minProfitRat;
                }
                if (fetchParams.maxProfitRat) {
                    params.maxProfitRat = fetchParams.maxProfitRat;
                }
                if (fetchParams.stockWarn) {
                    params.stockWarn = fetchParams.stockWarn;
                }
                if (fetchParams.expiredWarn) {
                    params.expiredWarn = fetchParams.expiredWarn;
                }
                if (fetchParams.profitWarn) {
                    params.profitWarn = fetchParams.profitWarn;
                }
                if (fetchParams.unsalableWarn) {
                    params.unsalableWarn = fetchParams.unsalableWarn;
                }
                if (fetchParams.costPriceWarn) {
                    params.costPriceWarn = fetchParams.costPriceWarn;
                }
                if (fetchParams.unPriceWarn) {
                    params.unPriceWarn = fetchParams.unPriceWarn;
                }
                if (fetchParams.profitCategoryTypeList?.length) {
                    params.profitCategoryTypeList = fetchParams.profitCategoryTypeList;
                }
                if (fetchParams.sbNationalMatched) {
                    params.sbNationalMatched = fetchParams.sbNationalMatched;
                }
                if (fetchParams.sbNationalNotMatched) {
                    params.sbNationalNotMatched = fetchParams.sbNationalNotMatched;
                }
                if (fetchParams.sbNationalNotPermit) {
                    params.sbNationalNotPermit = fetchParams.sbNationalNotPermit;
                }
                if (fetchParams.sbGoingOverPrice) {
                    params.sbGoingOverPrice = fetchParams.sbGoingOverPrice;
                }
                if (fetchParams.sbNotOverPrice) {
                    params.sbNotOverPrice = fetchParams.sbNotOverPrice;
                }
                if (fetchParams.sbOverPrice) {
                    params.sbOverPrice = fetchParams.sbOverPrice;
                }
                if (fetchParams.medicalFeeGrade?.length) {
                    params.medicalFeeGrade = fetchParams.medicalFeeGrade;
                }
                if (fetchParams.shebaoPayMode?.length) {
                    params.shebaoPayMode = fetchParams.shebaoPayMode;
                }
                if (fetchParams.remark) {
                    params.remark = fetchParams.remark;
                }


                if (isPriceAdjust) {
                    if (fetchParams.hasBarCode !== undefined) {
                        params.hasBarCode = `${fetchParams.hasBarCode}`?.length === 1 ? [Number(fetchParams.hasBarCode)] : (fetchParams.hasBarCode?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                    }
                    if (fetchParams.onlyStock !== undefined && fetchParams.onlyStock !== 0 && fetchParams.onlyStock !== '0') {
                        params.onlyStock = `${fetchParams.onlyStock}`?.length === 1 ? [Number(fetchParams.onlyStock)] : (fetchParams.onlyStock?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                    }
                    if (fetchParams.disable !== undefined) {
                        params.disable = `${fetchParams.disable}`?.length === 1 ? [Number(fetchParams.disable)] : (fetchParams.disable?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                    }
                    if (fetchParams.memberPriceFlag !== undefined) {
                        params.memberPriceFlag = `${fetchParams.memberPriceFlag}`?.length === 1 ? [Number(fetchParams.memberPriceFlag)] : (fetchParams.memberPriceFlag?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                    }
                    if (fetchParams.otcType !== undefined) {
                        params.otcType = this.fetchParams.otcType;
                    }
                } else {
                    if (fetchParams.hasBarCode !== undefined) {
                        params.hasBarCode = fetchParams.hasBarCode;
                    }
                    if (fetchParams.onlyStock) {
                        params.onlyStock = fetchParams.onlyStock;
                    }
                    if (fetchParams.disable !== undefined) {
                        params.disable = fetchParams.disable;
                    }
                    if (fetchParams.memberPriceFlag !== undefined) {
                        params.memberPriceFlag = fetchParams.memberPriceFlag;
                    }
                }

                if (fetchParams.position) {
                    params.position = fetchParams.position;
                }
                if (fetchParams.storage) {
                    params.storage = fetchParams.storage;
                }
                if (fetchParams.hasDrugIdentificationCode !== undefined) {
                    params.hasDrugIdentificationCode = fetchParams.hasDrugIdentificationCode;
                }
                if (fetchParams.unPrintPriceWarn) {
                    params.unPrintPriceWarn = fetchParams.unPrintPriceWarn;
                }
                return params;
            },
            createPostParams() {
                let modifyField = 0;
                modifyField |= BatchModifyEnum.TRACE_CODE;
                const handleParams = this.getParams();
                const params = {
                    ...handleParams,
                    fullSelect: Number(this.isAllChecked),
                    batchGoodsIdList: this.goodsIdList,
                    modifyField,
                    // hasDrugIdentificationCode: -1, // type = 0 代表勾选全部，需要传入1, -1 目前报错，不勾选全部，目前传-1 TODO
                    // modifyTraceableCodeNoInfoList: [{
                    //     drugIdentificationCode: '', type: 10,
                    // }],
                    onlyStock: (handleParams?.onlyStock === undefined || handleParams?.onlyStock === 0) ? [] : `${handleParams.onlyStock}`?.length === 1 ? [Number(handleParams.onlyStock)] : (handleParams.onlyStock?.split(',')?.map((i) => {
                        return Number(i);
                    }) || []),
                    disable: handleParams?.disable === undefined ? [] : `${handleParams.disable}`?.length === 1 ? [Number(handleParams.disable)] : (handleParams?.disable?.split(',')?.map((i) => {
                        return Number(i);
                    }) || []),
                    // 字符串转数组
                    hasBarCode: handleParams?.hasBarCode === undefined ? [] : `${handleParams.hasBarCode}`?.length === 1 ? [Number(handleParams.hasBarCode)] : (handleParams.hasBarCode?.split(',')?.map((i) => {
                        return Number(i);
                    }) || []),
                    memberPriceFlag: handleParams?.memberPriceFlag === undefined ? [] : `${handleParams?.memberPriceFlag}`?.length === 1 ? [Number(handleParams.memberPriceFlag)] : (handleParams.memberPriceFlag?.split(',')?.map((i) => {
                        return Number(i);
                    }) || []),
                };
                if (params.modifyBusinessScopeList?.length === 0) {
                    delete params.modifyBusinessScopeList;
                }
                if (params.modifyTagIdList?.length === 0) {
                    delete params.modifyTagIdList;
                }
                return filterEmptyValue(params);
            },
            async submit() {
                try {
                    const params = this.createPostParams();
                    const res = await GoodsAPI.batchModifyGoods(params);
                    if (res?.data?.modifyTaskId) {
                        this.showProcessDialog = true;
                        this.progressData.totalCount = res.data.total || 0;
                        this.progressData.isFinished = false;
                        this.startProcess(res.data.modifyTaskId);
                    } else {
                        this.$Toast.error('修改失败');
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.traceBtnLoading = false;
                }
            },
            startProcess(taskId) {
                this.timer = setInterval(async () => {
                    try {
                        const res = await GoodsAPI.batchModifyGoodsProgress(taskId);
                        this.progressData.currentCount = res?.data?.success;
                        this.progressData.totalCount = res?.data?.total;
                        if (res?.data?.status === 20) {
                            this.progressData.isFinished = true;
                            clearInterval(this.timer);
                            if (this.progressData.currentCount === 0) {
                                this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: '已选档案中不存在需要修改的追溯码',
                                });
                                this.showProcessDialog = false;
                                return;
                            }
                            // eslint-disable-next-line abc/no-timer-id
                            const timer = setTimeout(() => {
                                this.$emit('on-success');
                                this.handleCancelBatchNoSetTraceCode();
                                this.showProcessDialog = false;
                                clearTimeout(timer);
                            }, 1000);
                        }
                    } catch (e) {
                        console.error(e);
                    }
                }, 1000);
            },
            createListParams() {
                let spuGoodsCondition = '';
                try {
                    spuGoodsCondition = JSON.stringify(Object.fromEntries(Object.entries(this.params.spuGoodsCondition).filter(([, v]) => !isNull(v))));
                } catch (e) {
                    spuGoodsCondition = '';
                    console.error(e);
                }
                const params = {
                    ...this.params,
                    spuGoodsCondition,
                    profitWarn: this.warningKeys.includes('profit') ? 1 : undefined,
                    stockWarn: this.warningKeys.includes('stock') ? 1 : undefined,
                    expiredWarn: this.warningKeys.includes('expired') ? 1 : undefined,
                    costPriceWarn: this.warningKeys.includes('costPrice') ? 1 : undefined,
                    unPriceWarn: this.warningKeys.includes('unPrice') ? 1 : undefined,
                    unsalableWarn: this.warningKeys.includes('unsalable') ? 1 : undefined,
                    sbNationalInvalid: this.warningKeys.includes('sbNationalInvalid') ? 1 : undefined,
                    sbNationalWarn: this.warningKeys.includes('sbNationalWarn') ? 1 : undefined,
                    sbOverPrice: this.warningKeys.includes('sbOverPrice') ? 1 : undefined,
                    sbNotMatched: this.warningKeys.includes('sbNotMatched') ? 1 : undefined,
                    sbOverListingPrice: this.warningKeys.includes('sbOverListingPrice') ? 1 : undefined,
                    unPrintPriceWarn: this.warningKeys.includes('unPrintPriceWarn') ? 1 : undefined,
                };
                if (params.isSpu) {
                    delete params.goodsId;
                } else {
                    delete params.spuGoodsId;
                }
                return params;
            },
            async handleClickBatchNoSetTraceCode() {
                this.traceBtnLoading = true;
                try {
                    await this.submit();
                    // const queryParams = this.createListParams();
                    // const params = {
                    //     ...queryParams,
                    //     fullSelect: Number(this.isAllChecked),
                    //     hasDrugIdentificationCode: queryParams?.hasDrugIdentificationCode ?? 1,
                    //     batchGoodsIdList: this.goodsIdList,
                    //     offset: 0,
                    //     limit: 10,
                    // };
                    // const { data } = await GoodsAPI.goodsList(params);
                    // const item = data?.rows?.[0];
                    // if (item?.traceableCodeNoInfoList?.[0]?.type === TraceableCodeTypeEnum.HAS_CODE) {
                    //     this.$confirm({
                    //         type: 'warn',
                    //         title: (Number(this.isAllChecked) || this.goodsIdList.length > 1) ? '批量操作风险提示' : '确认商品无追溯码？',
                    //         content: (Number(this.isAllChecked) || this.goodsIdList.length > 1) ? '已选择的商品档案中包含已经绑定标识码的档案，</br>确认标记无码后将清空所有已绑定的标识码' : '该商品已经绑定了标识码，标记无码后将清空所</br>有已绑定的标识码，是否确认勾选？',
                    //         confirmText: '确定勾选',
                    //         onConfirm: async () => {
                    //             await this.submit();
                    //         },
                    //         onCancel: () => {
                    //             this.traceBtnLoading = false;
                    //         },
                    //     });
                    // } else {
                    //     await this.submit();
                    // }
                    // const {
                    //     data: {
                    //         total = 0,
                    //     },
                    // } = await GoodsAPI.goodsList(params);
                    // // 有有码信息 需要弹出交互
                    // if (total) {
                    //     if (total === 1) {
                    //         this.$confirm({
                    //             type: 'warn',
                    //             title: '确认商品无追溯码？',
                    //             content: '该商品已经绑定了标识码，标记无码后将清空所</br>有已绑定的标识码，是否确认勾选？',
                    //             confirmText: '确定勾选',
                    //             onConfirm: async () => {
                    //                 await this.submit();
                    //             },
                    //             onCancel: () => {
                    //
                    //             },
                    //         });
                    //         this.traceBtnLoading = false;
                    //     } else {
                    //         this.$emit('batch-no-set-trace-code');
                    //         this.traceBtnLoading = false;
                    //     }
                    // } else {
                    //     // 没有有码的信息所以直接触发修改
                    //     await this.submit();
                    // }
                } catch (e) {
                    console.log(e);
                    this.traceBtnLoading = false;
                }
            },
            handleCancelBatchPrintPriceTag() {
                this.$emit('batch-cancel');
            },
            handleCancelBatchNoSetTraceCode() {
                this.$emit('batch-cancel-trace-code');
            },
        },
    };
</script>

<style lang="scss">
.species-filter-handler-bar {
    .abc-select-wrapper {
        vertical-align: top;
    }

    .goods-all-count {
        position: absolute;
        top: 24px;
        left: 82px;
    }

    .select-label {
        display: inline-block;
        height: 32px;
        margin-left: 8px;
        font-size: 14px;
        line-height: 32px;
    }

    .export-btn {
        min-width: 32px;
        font-size: 14px;

        .export-btn-content {
            position: relative;
            width: 60px;

            .loading-spinner {
                position: absolute;
                top: 0;
                left: -9px;
                width: 30px;
                text-align: center;
            }
        }
    }
}
</style>
