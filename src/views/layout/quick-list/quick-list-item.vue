<template>
    <li
        class="quick-list-item-wrapper"
        :class="{
            'is-done': isDone,
            'is-active': isActive,
        }"
        :data-cy="`quick-list-item-${patientName}`"
        @click="select"
    >
        <div class="ql-item-info">
            <slot name="prepend"></slot>

            <abc-avatar
                class="img-wrapper"
                :url="patientHeadImgUrl"
                :is-male="isMale"
                :border-visible="!isActive"
                border-hidden-color="#40acff"
                :is-vip="isVIP"
                :icon-name="iconName"
            ></abc-avatar>

            <div class="patient-name">
                <slot name="patient-name">
                    {{ patientName }}
                </slot>

                <span v-if="$slots" class="patient-name-append">
                    <slot name="patient-name-append"></slot>
                </span>
            </div>

            <div
                class="ql-item-content"
                :class="contentClass"
            >
                <slot name="content"></slot>
            </div>

            <div v-if="showStatus" class="status" :class="statusClass">
                <slot v-if="$slots.status" name="status"></slot>
                <template v-else>
                    {{ quickItem.statusName }}
                </template>
            </div>
        </div>

        <div v-if="withDescribe" class="ql-item-describe">
            <div class="abstract">
                <slot name="abstract"></slot>
            </div>
            <div class="date">
                <slot name="date"></slot>
            </div>
        </div>
    </li>
</template>

<script type="text/ecmascript-6">
    import AbcAvatar from 'src/views/layout/abc-avatar/index.vue';
    import { getImgWrapperClass } from 'views/common/patient';

    export default {
        name: 'QuickListItem',
        components: {
            AbcAvatar,
        },
        props: {
            quickItem: {
                type: Object,
                required: true,
            },

            // ql草稿，用与展示 患者信息
            draftList: {
                type: Array,
                default: () => [],
            },

            // 传入用户头像
            patientHeadImgUrl: {
                type: String,
                default: '',
            },

            // ql内容class
            contentClass: {
                type: [Array,Object,String],
                default: () => '',
            },

            // ql状态class
            showStatus: {
                type: Boolean,
                default: true,
            },

            // ql状态class
            statusClass: {
                type: [Array,Object,String],
                default: () => '',
            },

            // ql item 完成状态
            isDone: {
                type: Boolean,
                default: false,
            },
            // ql item 选中状态
            isActive: {
                type: Boolean,
                default: false,
            },

            // 是否展示ql下方描述
            withDescribe: {
                type: Boolean,
                default: false,
            },
            iconName: String,
        },
        data() {
            return {

            };
        },
        computed: {

            quickListSmallStyle() {
                return this.selectedTab === 2 ?
                    'height:calc(100% - 68px); height:-webkit-calc(100% - 68px);' :
                    'height:-webkit-calc(100% - 36px);';
            },

            patientInfo() {
                const {
                    patient,
                    patientInfo,
                } = this.quickItem;
                return patient || patientInfo || {};
            },

            draftItem() {
                return this.draftList?.find((it) => it.draftId === this.quickItem.id);
            },

            isVIP() {
                if (!this.patientInfo) return false;
                const {
                    name,
                    isMember,
                } = this.patientInfo;

                if (!name) return false;


                if (this.draftItem) {
                    return !!this.draftItem.patient?.isMember;
                }

                return !!isMember;
            },


            patientName() {
                if (this.draftItem) {
                    return this.draftItem.patient?.name || '匿名患者';
                }
                return this.patientInfo?.name || '匿名患者';
            },

            patientSex() {
                if (this.draftItem) {
                    return this.draftItem.patient?.sex;
                }
                return this.patientInfo?.sex || '男';
            },

            imgWrapperClass() {
                return getImgWrapperClass(this.isVIP, this.patientSex);
            },

            isMale() {
                return this.patientSex === '男';
            },
        },

        methods: {
            select() {
                this.$emit('select');
            },

        },
    };
</script>

<style lang="scss">
    @import "src/styles/theme.scss";
    @import "src/styles/mixin.scss";

    .quick-list-item-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 44px;
        padding: 8px 10px;
        cursor: pointer;
        background-color: #ffffff;
        border-radius: var(--abc-border-radius-small);

        .ql-item-info {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .ql-item-describe {
            display: flex;
            align-items: center;
            width: 100%;
            font-size: 12px;
            line-height: 18px;
            color: var(--abc-color-T2);

            .abstract {
                flex: 1;
                padding-left: 26px;

                @include ellipsis;
            }

            .date {
                min-width: 42px;
                text-align: right;
            }
        }

        .img-wrapper {
            margin-right: 4px;
        }

        .patient-name {
            position: relative;
            display: flex;
            flex: 1;
            align-items: center;
            width: 0;
            padding: 0 8px 0 0;
            overflow: hidden;
            font-size: 14px;
            line-height: 20px;
            color: var(--abc-color-T1);
            text-overflow: ellipsis;
            word-break: break-all;
            white-space: nowrap;

            .icon-reserve,
            .icon-referral {
                width: 14px;
                height: 14px;
                margin-left: 6px;
            }

            .red-dot {
                width: 6px;
                height: 6px;
                margin-left: 6px;
                background-color: var(--abc-color-R2);
                border-radius: var(--abc-border-radius-small);
            }
        }

        .patient-name-append {
            color: var(--abc-color-T2);
        }

        .charge-type {
            margin: 0 4px;
            font-size: 12px;
            color: var(--abc-color-T2);
        }

        .ql-invoice-wrapper {
            display: flex;
            margin-right: 6px;
        }

        .printed-icon {
            min-width: 12px;
            margin: 0 2px 0 4px;
            color: var(--abc-color-P1);
        }

        .ql-item-content {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            max-width: 116px;
            overflow: hidden;
            font-size: 12px;
            line-height: 18px;
            color: var(--abc-color-T1);
            text-align: right;
            text-overflow: ellipsis;
            white-space: nowrap;

            .doctor-name {
                width: 54px;
                min-width: 54px;
                margin-left: 6px;
                overflow: hidden;
                text-align: left;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .text {
                min-width: 38px;
            }

            &.green {
                //color: var(--abc-color-G1);
            }

            .send-icon {
                .iconfont {
                    margin-right: 4px;
                    font-size: 12px;
                }

                .cis-icon-send {
                    color: var(--abc-color-P1);
                }
            }
        }

        .ql-item-content.num-tag {
            min-width: 70px;
        }

        .ql-item-content.calling {
            min-width: 112px;
        }

        .status {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 44px;
            min-width: 44px;
            max-width: 44px;
            font-size: 12px;
            font-weight: bold;
            color: var(--abc-color-B2);
            text-align: right;

            &.width-54 {
                width: 54px;
                min-width: 54px;
                max-width: 54px;
            }

            &.green {
                color: var(--abc-color-G1);
            }

            &.yellow {
                color: var(--abc-color-Y2);
            }

            &.yellow-2 {
                color: var(--abc-color-Y1);
            }

            &.purple {
                color: #0001c6;
            }

            &.green-2 {
                color: var(--abc-color-G2);
            }

            &.gray {
                color: var(--abc-color-T2);
            }

            &.blue {
                color: var(--abc-color-B2);
            }

            &.blue-2 {
                color: var(--abc-color-B2);
            }

            &.red {
                color: #ff3333;
            }

            &.gray-2 {
                color: var(--abc-color-T3);
            }
        }

        &:not(.is-active):hover {
            background-color: #eff3f6 !important;
        }

        &.is-active {
            background-color: var(--abc-color-B3) !important;

            .delivery-icon,
            .patient-name,
            .patient-name-append,
            .charge-type,
            .status,
            .list-desc,
            .status.green,
            .ql-item-content,
            .ql-item-content .send-icon .cis-icon-send,
            .printed-icon,
            .ql-item-describe {
                color: #ffffff !important;
            }

            &::after {
                display: none;
            }
        }

        &.is-done {
            .status {
                font-weight: normal;
                color: var(--abc-color-T3);

                &.green {
                    color: var(--abc-color-G1);
                }
            }
        }

        & + li {
            margin-top: 4px;
        }
    }

    .invoice-status-title {
        padding: 8px 16px;
        font-size: 12px;
        color: #ffffff;
        background-color: #000000;
        border-radius: var(--abc-border-radius-small);
    }
</style>
