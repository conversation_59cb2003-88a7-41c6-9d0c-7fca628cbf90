<template>
    <abc-dialog
        title="表头设置-核拨统计-汇总"
        class="statistics-module__social-stat__national-social-approve__setting"
        :value="true"
        content-styles="width: 830px; height: 560px; padding: 0 0 0 24px;"
        @input="onClickCancle"
    >
        <div class="content-wrapper">
            <div class="right-content-wrapper">
                <div
                    v-for="(item, index) in selectOptions"
                    :key="index"
                    class="item"
                >
                    <div class="title">
                        {{ item.title }}
                    </div>
                    <abc-checkbox
                        v-for="it in item.child"
                        :key="it.key"
                        v-model="it.value"
                        :disabled="item.type === 'dim' && it.disabled === true"
                        @change="checkIsSelectOne(item)"
                    >
                        {{ it.name }}
                    </abc-checkbox>
                </div>
            </div>
            <div class="left-content-wrapper">
                <div class="stat-dim">
                    <div class="title">
                        统计维度
                    </div>
                    <div
                        v-for="item in selectedDim"
                        :key="item.key"
                        class="item"
                    >
                        {{ item.name }}
                    </div>
                </div>
                <div class="stat-quota">
                    <div class="title">
                        统计指标
                    </div>
                    <div
                        v-for="item in selectedQuota"
                        :key="item.key"
                        class="item"
                    >
                        {{ item.name }}
                    </div>
                </div>
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button
                type="blank"
                :loading="loadingDefaultBtn"
                @click="onClickDefault"
            >
                恢复默认
            </abc-button>
            <div class="track"></div>
            <abc-button
                :loading="loadingConfirmBtn"
                :disabled="disableConfirmBtn"
                @click="onClickConfirm"
            >
                确定
            </abc-button>
            <abc-button
                type="blank"
                @click="onClickCancle"
            >
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>
<script>
    import { isEqual } from 'utils/lodash';
    import { mapGetters } from 'vuex';
    import StatSocialApi from 'api/stat/social';
    export default {
        name: 'NationalSocialStatApproveSetting',
        data() {
            return {
                selectOptions: null, // 选项数据
                selectedDim: [], // 选中的统计维度
                selectedQuota: [], // 选中的统计指标
                socialConfig: null, // 医保信息

                loadingDefaultBtn: false, // 恢复默认按钮状态
                loadingConfirmBtn: false, // 确定按钮状态
            };
        },
        computed: {
            ...mapGetters(['currentClinic' ]),
            // 是否为连锁店总部
            isChainAdmin() {
                return this.currentClinic?.chainAdmin === 1;
            },
            // 是否有更新
            isUpdated() {
                return !isEqual(this.selectOptions, this.createSelectOptions());
            },
            // 是否可以保存
            disableConfirmBtn() {
                return !this.isUpdated;
            },
            // 是否山东-青岛
            isShandongQingdao() {
                return this.$abcSocialSecurity.config.isShandongQingdao || this.$abcSocialSecurity.region === 'shandong_qingdao';
            },
            // 是否甘肃-陇南
            isGansuLongnan() {
                return this.$abcSocialSecurity.config.isGansuLongnan || this.$abcSocialSecurity.region === 'gansu_longnan';
            },
            // 是否山东-济南
            isShandongJinan() {
                return this.$abcSocialSecurity.config.isShandongJinan || this.$abcSocialSecurity.region === 'shandong_jinan';
            },
        },
        watch: {
            selectOptions: {
                handler(newValue) {
                    this.selectedDim = [];
                    this.selectedQuota = [];
                    const dimChilds = (newValue || []).filter((item) => item.type === 'dim');
                    const quotaChilds = (newValue || []).filter((item) => item.type === 'quota');
                    dimChilds.forEach((item) => {
                        item.child.forEach((it) => {
                            if (it.value === true) {
                                this.selectedDim.push(it);
                            }
                        });
                    });
                    quotaChilds.forEach((item) => {
                        item.child.forEach((it) => {
                            if (it.value === true) {
                                this.selectedQuota.push(it);
                            }
                        });
                    });
                },
                immediate: true,
                deep: true,
            },
        },
        async mounted() {
            await this.fetchSocialConfig();
            this.selectOptions = this.createSelectOptions();
        },
        methods: {
            /**
             * @desc 查询医保信息
             * <AUTHOR>
             * @date 2024-04-10
             */
            async fetchSocialConfig() {
                const response = await StatSocialApi.fetchSocialConfig();
                this.socialConfig = response.data;
            },
            /**
             * @desc 创建设置项
             * <AUTHOR>
             * @date 2024-04-10
             */
            createDefaultSelectOptions() {
                const options = [
                    {
                        title: '统计维度',
                        type: 'dim',
                        show: true,
                        child: [
                            {
                                key: 'insutype',
                                name: '险种类型',
                                disabled: false,
                                show: true,
                            },
                            {
                                key: 'psnType',
                                name: '人员类别',
                                disabled: false,
                                show: true,
                            },
                            {
                                key: 'medType',
                                name: '医疗类别',
                                disabled: false,
                                show: true,
                            },
                            {
                                key: 'insuranceRegionType',
                                name: '区划类型',
                                disabled: false,
                                show: true,
                            },
                            {
                                key: 'insuranceRegion',
                                name: '参保区划',
                                disabled: false,
                                show: true,
                            },
                        ],
                    },
                    {
                        title: '统计指标-签约规模',
                        type: 'quota',
                        show: this.isShandongQingdao,
                        child: [
                            {
                                key: 'signNumber',
                                name: '签约人数',
                                show: this.isShandongQingdao,
                            },
                        ],
                    },
                    {
                        title: '统计指标-结算规模',
                        type: 'quota',
                        show: true,
                        child: [
                            {
                                key: 'totalPersonCount',
                                name: '结算人头数',
                                show: true,
                            },
                            {
                                key: 'totalPersonTimes',
                                name: '结算人次',
                                show: true,
                            },
                            {
                                key: 'timesRatioCount',
                                name: '人次人头比',
                                show: true,
                            },
                            {
                                key: 'medfeeSumamtFee',
                                name: '医疗费用总额',
                                show: true,
                            },
                            {
                                key: 'avageCost',
                                name: '医疗费用次均',
                                show: true,
                            },
                            {
                                key: 'avagePer',
                                name: '医疗费用人均',
                                show: true,
                            },
                            {
                                key: 'reservedAmount',
                                name: '预留金',
                                show: this.isGansuLongnan,
                            },
                        ],
                    },
                    {
                        title: '统计指标-费用构成-非个人支付',
                        type: 'quota',
                        show: true,
                        child: [
                            {
                                key: 'fundPaySumamt',
                                name: '基金支付总额',
                                show: true,
                            },
                            {
                                key: 'hifpPay',
                                name: '统筹基金支付',
                                show: true,
                            },
                            {
                                key: 'cvlservPayFee',
                                name: '公务员补助',
                                show: true,
                            },
                            {
                                key: 'hifesPayFee',
                                name: '企业补充医疗',
                                show: !this.isShandongJinan,
                            },
                            {
                                key: 'hifobPay',
                                name: '职工大额补助',
                                show: true,
                            },
                            {
                                key: 'hifmiPay',
                                name: '居民大病保险',
                                show: true,
                            },
                            {
                                key: 'mafPay',
                                name: '医疗救助',
                                show: true,
                            },
                            {
                                key: 'othPay',
                                name: '其他支出',
                                show: true,
                            },
                        ],
                    },
                    {
                        title: '统计指标-费用构成-个人支付',
                        type: 'quota',
                        show: true,
                        child: [
                            {
                                key: 'psnPartAmt',
                                name: '个人支付总额',
                                show: true,
                            },
                            {
                                key: 'acctPay',
                                name: '个账支付',
                                show: true,
                            },
                            {
                                key: 'acctMulaidPay',
                                name: '共济支付',
                                show: true,
                            },
                            {
                                key: 'psnCashPay',
                                name: '现金支付',
                                show: true,
                            },
                        ],
                    },
                ];
                const list = [];
                options.forEach((item) => {
                    const child = item.child.filter((it) => it.show === true);
                    if (item.show === true) {
                        item.child = child;
                        list.push(item);
                    }
                });
                return list;
            },
            /**
             * @desc 检查唯独是否只选择一个
             * <AUTHOR>
             * @date 2024-04-16
             */
            checkIsSelectOne(item) {
                if (item.type !== 'dim') {
                    return false;
                }
                const isSelectArr = item.child.filter((it) => it.value === true);
                item.child.forEach((it) => {
                    if (it.value === true && isSelectArr.length === 1) {
                        it.disabled = true;
                    } else {
                        it.disabled = false;
                    }
                });
            },
            /**
             * @desc 创建渲染数据
             * <AUTHOR>
             * @date 2024-04-10
             */
            createSelectOptions() {
                const options = this.createDefaultSelectOptions();
                const { nationalSocialStatDefaultSetting } = this.$abcSocialSecurity.config;
                options.forEach((item) => {
                    item.child.forEach((it) => {
                        const itVlaue = this.socialConfig?.basicInfo?.nationalSocialStatSetting?.[it.key];
                        if (itVlaue !== undefined && itVlaue !== null && itVlaue !== '') {
                            it.value = itVlaue;
                        } else {
                            it.value = nationalSocialStatDefaultSetting[it.key];
                        }
                    });
                    this.checkIsSelectOne(item);
                });
                return options;
            },
            /**
             * @desc 点击恢复默认按钮触发
             * <AUTHOR>
             * @date 2024-04-09
             */
            async onClickDefault() {
                this.loadingDefaultBtn = true;
                Object.assign(this.socialConfig.basicInfo, {
                    nationalSocialStatSetting: this.$abcSocialSecurity.config.nationalSocialStatDefaultSetting,
                });
                await StatSocialApi.updateSocialConfig(this.socialConfig);
                this.loadingDefaultBtn = false;
                this.$Toast({
                    message: '保存成功',
                    type: 'success',
                });
                this.$emit('confirm');
            },
            /**
             * @desc 点击确定按钮触发
             * <AUTHOR>
             * @date 2024-04-09
             */
            async onClickConfirm() {
                this.loadingConfirmBtn = true;
                const nationalSocialStatSetting = {};
                this.selectOptions.forEach((item) => {
                    item.child.forEach((it) => {
                        nationalSocialStatSetting[it.key] = it.value;
                    });
                });
                if (this.isChainAdmin) {
                    Object.assign(this.socialConfig, {
                        apiVersion: '1.0',
                        basicInfo: {
                            ...(this.socialConfig.basicInfo || {}),
                            nationalSocialStatSetting,
                        },
                    });
                } else {
                    Object.assign(this.socialConfig.basicInfo, {
                        nationalSocialStatSetting,
                    });
                }

                await StatSocialApi.updateSocialConfig(this.socialConfig);
                this.loadingConfirmBtn = false;
                this.$Toast({
                    message: '保存成功',
                    type: 'success',
                });
                this.$emit('confirm');
            },
            /**
             * @desc 点击取消按钮触发
             * <AUTHOR>
             * @date 2024-04-09
             */
            onClickCancle() {
                this.$emit('input', false);
                this.$emit('cancel');
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';
    @import 'src/styles/mixin.scss';

    .statistics-module__social-stat__national-social-approve__setting {
        .content-wrapper {
            display: flex;
            justify-content: flex-start;
            width: 100%;
            height: 100%;

            .right-content-wrapper {
                width: 626px;
                height: 100%;
                border-right: 1px solid #efefef;

                .title {
                    margin-top: 20px;
                    margin-bottom: 13px;
                    color: #7a8794;
                }

                .abc-checkbox-wrapper {
                    width: 150px;
                    margin-bottom: 13px;
                }
            }

            .left-content-wrapper {
                width: 180px;
                height: 100%;
                padding-left: 10px;
                overflow-y: auto;

                @include scrollBar();

                .stat-dim {
                    margin-top: 20px;
                    margin-bottom: 20px;
                }

                .title {
                    margin-bottom: 11px;
                    color: #7a8794;
                }

                .item {
                    margin-bottom: 8px;
                }
            }
        }

        .dialog-footer {
            display: flex;
            justify-content: flex-start;

            .track {
                flex: 1;
            }
        }
    }
</style>
