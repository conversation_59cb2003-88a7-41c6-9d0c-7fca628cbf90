<template>
    <div class="business-manage-overview">
        <abc-space>
            <abc-date-picker-bar
                v-model="params.currentBasicInfoFilter"
                :options="dateOptions"
                :picker-options="pickerOptions"
                value-format="YYYY-MM-DD"
                :clearable="false"
                @change="handleDateChange"
            ></abc-date-picker-bar>

            <clinic-select
                v-if="isChainAdmin"
                v-model="params.clinicIdFilter"
                :clinic-list="clinicList"
                width="150"
                @change="handleClinicChange"
            ></clinic-select>
        </abc-space>

        <biz-statistic-card-board
            :data-list="statisticData"
            min-col-width="370px"
            :gutter="[16, 16]"
            class="statistic-card-board-wrapper"
        >
        </biz-statistic-card-board>

        <biz-statistic-card-board
            :data-list="renderOverviewData"
            min-col-width="740px"
            :gutter="[16, 16]"
            type="custom"
            class="statistic-card-board-wrapper"
        >
            <template #bar-chart="{ item }">
                <stat-card class="card">
                    <abc-space direction="vertical" align="start" :size="4">
                        <abc-text bold>
                            {{ item.label }}
                        </abc-text>
                        <abc-text theme="gray" size="mini">
                            {{ displayDateRange }}
                        </abc-text>
                    </abc-space>
                    <div v-if="item.isEmpty" class="pie-chart-empty">
                        <abc-content-empty></abc-content-empty>
                    </div>
                    <abc-bar-chart
                        v-else
                        :id="`group-bar-graph-${item.label }`"
                        :category="item.category"
                        :x-axis-data="item.category"
                        :data="item.data"
                        height="250px"
                        class="pie-chart-style"
                        width="100%"
                        :stack="true"
                        :bar-width="32"
                        trigger="axis"
                        a-axis-type="category"
                        y-axis-type="value"
                        :grid="{
                            left: '1%',
                            right: '0',
                            top: '10%',
                            bottom: '20%',
                            containLabel: true,
                            width: '100%',
                        }"
                        :legend="{
                            data: ['初诊人次', '复诊人次'],
                            bottom: '5%',
                        }"
                        :name-list="['初诊人次', '复诊人次']"
                        :bar-colors="['#2680F7', '#40C6C2']"
                        :tooltip-formatter="tooltipFormatter"
                        :y-axis="{
                            type: 'value',
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: ['#E6EAEE'],
                                },
                            },
                        }"
                    ></abc-bar-chart>
                </stat-card>
            </template>
        </biz-statistic-card-board>

        <section class="card-container" style="margin-top: 16px;">
            <stat-card class="card">
                <abc-flex justify="space-between">
                    <div>
                        <h3 class="business-manage-overview-label">
                            客流趋势
                        </h3>
                        <div class="date-range">
                            {{ newCustomersTrendDate }}
                        </div>
                    </div>
                    <abc-date-picker-bar
                        v-model="params.currentNewCustomersTrendFilter"
                        :options="trendDateOptions"
                        :picker-options="pickerOptions"
                        value-format="YYYY-MM-DD"
                        :clearable="false"
                        @change="handleNewCustomersTrendDateChange"
                    ></abc-date-picker-bar>
                </abc-flex>
                <div v-if="isCustomerEmpty & !newCustomersTrendLoading" class="pie-chart-empty">
                    <abc-content-empty></abc-content-empty>
                </div>
                <abc-line-chart
                    v-else
                    id="new-customers-trend-chart"
                    v-abc-loading="newCustomersTrendLoading"
                    class="new-customers-trend-chart"
                    :date="newCustomersTrendDates"
                    :data="newCustomersTrendData"
                    :legend="{
                        data: ['到店人数', '新客'],
                        bottom: -3,
                        left: '45%',
                    }"
                    series-name="到店人数,新客"
                    :grid="grid"
                    height="218px"
                    :data-zoom-conf="null"
                    line-color="#2680F7"
                    item-color="#2680F7"
                    x-axis-label-count="10"
                    :x-axis-label-rotate="0"
                    width="100%"
                ></abc-line-chart>
            </stat-card>
        </section>
    </div>
</template>

<script>
    import ClinicSelect from 'views/statistics/common/clinic-select/clinic-select';
    import StatCard from 'views/statistics/common/stat-card/stat-card';
    import { mapGetters } from 'vuex';
    import { dateRangeFormat } from 'views/statistics/common/util';
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import Api from '../core/api/business-manage';
    import SummaryModel from './adapter/summary';
    import { AbcDatePickerBar } from '@abc/ui-pc'; const { DatePickerBarOptions } = AbcDatePickerBar;
    import { formatDate } from '@abc/utils-date';
    import BizStatisticCardBoard from '@/components-composite/biz-statistic-card-board';

    export default {
        name: 'BusinessManageOverview',
        components: {
            StatCard,
            ClinicSelect,
            BizStatisticCardBoard,
        },
        mixins: [ClinicTypeJudger],
        props: {},
        data() {
            return {
                params: {
                    // 基础信息筛选：包含概要、费用分布、支付方式分布
                    clinicIdFilter: '',
                    filterStart: '',
                    filterEnd: '',
                    currentBasicInfoFilter: DatePickerBarOptions.DAY.label,

                    // 新客趋势
                    newCustomersTrendFilterStart: '',
                    newCustomersTrendFilterEnd: '',
                    currentNewCustomersTrendFilter: DatePickerBarOptions.LATEST_WEEK.label,
                },
                summaryData: SummaryModel.defaultData,
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                newCustomersTrendDates: [],
                newCustomersTrendData: [],
                newCustomersTrendLoading: false,
                grid: {
                    show: true,
                    borderWidth: 0,
                    borderColor: '#E6E9EC',
                    containLabel: true,
                    top: '3%',
                    left: '3%',
                    bottom: '16%',
                    right: '4%',
                },
                dateOptions: [
                    {
                        label: DatePickerBarOptions.DAY.label,
                        name: DatePickerBarOptions.DAY.name,
                        getValue() {
                            return [new Date(), new Date()];
                        },
                    },
                    DatePickerBarOptions.WEEK,
                    DatePickerBarOptions.MONTH,
                    DatePickerBarOptions.YEAR,
                ],
                trendDateOptions: [
                    DatePickerBarOptions.LATEST_WEEK,
                    DatePickerBarOptions.LATEST_MONTH,
                    DatePickerBarOptions.LATEST_THREE_MONTH,
                    DatePickerBarOptions.LATEST_HALF_YEAR,
                    DatePickerBarOptions.MONTHLY,
                ],

                visitDepartmentSeriesData: [],
                visitDepartmentCategory: [],
                visitDoctorSeriesData: [],
                visitDoctorCategory: [],
            };
        },
        computed: {
            ...mapGetters(['subClinics']),
            clinicList() {
                return this.subClinics
                    .map((clinic) => {
                        return {
                            ...clinic,
                            shortName: this.chainId === clinic.id ? '总部' : clinic.shortName,
                        };
                    })
                    .filter((clinic) => clinic.name !== '总部');
            },
            isHeadOffice() {
                const status = this.subClinics.some(
                    (clinic) =>
                        (clinic.id === this.params.clinicIdFilter && clinic.name === '总部') ||
                        !this.params.clinicIdFilter,
                );
                if (this.isChainAdmin && status) return true;
                return false;
            },
            isCustomerEmpty() {
                return this.newCustomersTrendDates.length === 0;
            },

            displayDateRange() {
                return dateRangeFormat(this.params.filterStart, this.params.filterEnd);
            },

            newCustomersTrendDate() {
                return dateRangeFormat(
                    this.params.newCustomersTrendFilterStart,
                    this.params.newCustomersTrendFilterEnd,
                );
            },

            renderOverviewData() {
                return [
                    {
                        slotName: 'bar-chart',
                        label: '科室接诊',
                        category: this.visitDepartmentCategory,
                        data: this.visitDepartmentSeriesData,
                        isEmpty: this.visitDepartmentSeriesData.every((item) => !item.length),
                    },
                    {
                        slotName: 'bar-chart',
                        label: '医生接诊',
                        category: this.visitDoctorCategory,
                        data: this.visitDoctorSeriesData,
                        isEmpty: this.visitDoctorSeriesData.every((item) => !item.length),
                    },
                ];
            },

            statisticData() {
                return [
                    {
                        topTitle: '客流量',
                        labelTips: '门店门诊人次+零售人次',
                        topContent: this.displayDateRange,
                        title: `${this.summaryData.arrivalPeopleCount || 0}`,
                    },
                    {
                        topTitle: this.isHeadOffice ? '连锁新客/老客' : '门店新客/老客',
                        labelTips: this.isHeadOffice ? '首次到连锁就诊/消费则为连锁新客' : '首次到门店就诊/消费则为门店新客',
                        topContent: this.displayDateRange,
                        title: this.isHeadOffice ?
                            `${this.summaryData.chainClinicNewCustomers}/${this.summaryData.chainClinicOldCustomers}` :
                            `${this.summaryData.clinicNewCustomers}/${this.summaryData.clinicOldCustomers}`,
                    },
                    {
                        topTitle: '零售人次',
                        topContent: this.displayDateRange,
                        title: `${this.summaryData.retailTraffic || 0}`,
                    },
                    {
                        topTitle: '门诊收费人次/门诊人次',
                        topContent: this.displayDateRange,
                        title: `${this.summaryData.patientCompletePayCount}/${this.summaryData.patientCompleteCount}`,
                    },
                    {
                        topTitle: '初诊患者/复诊患者',
                        labelTips: '首次就诊某医生则为该医生的初诊患者',
                        topContent: this.displayDateRange,
                        title: `${this.summaryData.firstVisitPatientCount}/${this.summaryData.returnVisitPatientCount}`,
                    },
                ];
            },
        },
        watch: {},
        created() {
            // 概览
            this.params.filterStart = formatDate(new Date());
            this.params.filterEnd = formatDate(new Date());

            const [start, end] = DatePickerBarOptions.LATEST_WEEK.getValue();

            this.params.newCustomersTrendFilterStart = formatDate(start);
            this.params.newCustomersTrendFilterEnd = formatDate(end);

            this.fetchSummary();
            this.fetchVisitDepartment();
            this.fetchDoctorDepartment();
            this.fetchNewCustomersTrend();
        },
        methods: {
            tooltipFormatter(params) {
                let str = `${params[0].name}<br/>`;
                params.forEach((item) => {
                    str += `<div style="display: flex;justify-content: space-between"><span>${item.marker} ${item.seriesName}</span><span style="text-align: right;min-width: 40px;display: inline-block">${item.value}</span></div>`;
                });
                str += `<div style="display: flex;justify-content: space-between"><span>合计</span><span style="text-align: right;min-width: 40px;display: inline-block">${params.reduce((acc, item) => acc + Number(item.value), 0)}</span></div>`;
                return str;
            },
            async fetchSummary() {
                const begin = this.params.filterStart;
                const end = this.params.filterEnd;
                const clinicId = this.params.clinicIdFilter;
                try {
                    const { data } = await Api.overview.summary(begin, end, clinicId);
                    if (data) {
                        const summaryModel = new SummaryModel(data);

                        this.summaryData = summaryModel.data;
                    } else {
                        this.summaryData = SummaryModel.defaultData;
                    }
                } catch (e) {
                    console.error('fetchSummary', e);
                }
            },

            async fetchVisitDepartment() {
                const begin = this.params.filterStart;
                const end = this.params.filterEnd;
                const clinicId = this.params.clinicIdFilter;
                try {
                    const { data = [] } = await Api.overview.visitDepartment(begin, end, clinicId);
                    this.visitDepartmentCategory = data.map((item) => item.name);
                    this.visitDepartmentSeriesData = [data.map((item) => item.firstCount ?? 0), data.map((item) => item.followCount ?? 0)];
                } catch (e) {
                    console.error('fetchVisitDepartment', e);
                }
            },

            async fetchDoctorDepartment() {
                const begin = this.params.filterStart;
                const end = this.params.filterEnd;
                const clinicId = this.params.clinicIdFilter;
                try {
                    const { data = [] } = await Api.overview.visitDoctor(begin, end, clinicId);
                    this.visitDoctorCategory = data.map((item) => item.name);
                    this.visitDoctorSeriesData = [data.map((item) => item.firstCount ?? 0), data.map((item) => item.followCount ?? 0)];
                } catch (e) {
                    console.error('fetchDoctorDepartment', e);
                }
            },

            async fetchNewCustomersTrend() {
                const begin = this.params.newCustomersTrendFilterStart;
                const end = this.params.newCustomersTrendFilterEnd;
                let group = 'day';
                const clinicId = this.params.clinicIdFilter;
                this.params.currentNewCustomersTrendFilter === DatePickerBarOptions.MONTHLY.label && (group = 'month');
                try {
                    this.newCustomersTrendLoading = true;
                    const { data } = await Api.overview.dailyPatient(begin, end, group, clinicId);
                    const {
                        newCustomers, arriveCustomers,
                    } = data;
                    this.newCustomersTrendDates = newCustomers.dates;
                    this.newCustomersTrendData = [
                        {
                            name: '新客',
                            type: 'line',
                            data: newCustomers.counts,
                            itemStyle: {
                                normal: {
                                    color: '#55BFC0',
                                },
                            },
                            lineStyle: {
                                normal: {
                                    color: '#55BFC0',
                                    width: 1,
                                },
                            },
                        },
                        {
                            name: '到店人数',
                            type: 'line',
                            data: arriveCustomers.counts,
                        },
                    ];
                } catch (e) {
                    console.error('fetchNewCustomersTrend', e);
                } finally {
                    this.newCustomersTrendLoading = false;
                }
            },

            handleClinicChange() {
                this.fetchSummary();
                this.fetchVisitDepartment();
                this.fetchDoctorDepartment();
                this.fetchNewCustomersTrend();
            },

            handleDateChange(date) {
                this.params.filterStart = date[0];
                this.params.filterEnd = date[1];

                this.fetchSummary();
                this.fetchVisitDepartment();
                this.fetchDoctorDepartment();
            },

            handleNewCustomersTrendDateChange(date) {
                this.params.newCustomersTrendFilterStart = date[0];
                this.params.newCustomersTrendFilterEnd = date[1];

                this.fetchNewCustomersTrend();
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';

    .business-manage-overview {
        padding: 20px 20px 0;

        .new-customers-trend-chart {
            width: 100%;
            margin-top: 84px;
        }

        .statistic-card-board-wrapper {
            margin-top: 16px;

            .card {
                height: 100%;
                padding: 24px;

                .pie-chart-empty {
                    height: 200px;
                }
            }
        }
    }

    .business-manage-overview-label {
        font-size: 14px;
        font-weight: 700;
        color: #000000;
    }
</style>
