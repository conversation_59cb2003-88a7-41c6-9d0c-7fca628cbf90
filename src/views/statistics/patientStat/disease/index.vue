<template>
    <div class="stat-ms-disease-wrapper">
        <abc-space>
            <abc-date-picker-bar
                v-model="dateParams.currentBasicInfoFilter"
                :options="dateOptions"
                enable-date-picker
                :picker-options="pickerOptions"
                value-format="YYYY-MM-DD"
                :clearable="false"
                @change="handleDateChange"
            >
            </abc-date-picker-bar>
            <clinic-select
                v-if="isChainAdmin"
                v-model="paramsClinicId"
                placeholder="门店"
                :show-all-clinic="false"
                width="120"
                :clinic-list="clinicList"
                @change="handleChangeClinic"
            >
            </clinic-select>
        </abc-space>

        <biz-statistic-card-board
            :data-list="[
                { slotName: 'pie-chart' },
                { slotName: 'bar-chart' }
            ]"
            min-col-width="760px"
            :gutter="[16, 16]"
            type="custom"
            class="statistic-card-board-wrapper"
        >
            <template #pie-chart>
                <stat-card v-abc-loading="pirChartLoading" class="card">
                    <abc-flex justify="space-between" style="line-height: 32px;">
                        <abc-text bold>
                            病种占比
                        </abc-text>
                        <abc-button type="text" @click="handleOpenDetailDialog('DiseaseProportion')">
                            查看详情
                        </abc-button>
                    </abc-flex>
                    <abc-text theme="gray" size="mini">
                        {{ displayDateRange }}
                    </abc-text>
                    <div v-if="!diseaseProportionData.data || !diseaseProportionData.data.length" class="pie-chart-empty">
                        <abc-content-empty></abc-content-empty>
                    </div>
                    <abc-pie-chart
                        v-else
                        id="pie-chart"
                        class="pie-chart-style"
                        :data="diseaseProportionData.data"
                        :legend="diseaseProportionData.legend"
                        :tooltip="diseaseProportionData.tooltip"
                        :center="['100', '125']"
                        height="240px"
                        width="540px"
                    >
                    </abc-pie-chart>
                </stat-card>
            </template>
            <template #bar-chart>
                <stat-card v-abc-loading="barChartLoading" class="card">
                    <abc-flex justify="space-between" style="margin-bottom: 16px;">
                        <abc-space>
                            <abc-text bold>
                                医生病种诊断排序
                            </abc-text>
                            <filter-select
                                v-model="doctorDiagnosisSortParams.category"
                                :width="120"
                                :inner-width="120"
                                placeholder="疾病类型"
                                :options="categoryList"
                                :normalize-id="'category'"
                                :normalize-key="'category'"
                                @change="handleChangeCategory"
                            >
                            </filter-select>
                        </abc-space>
                        <abc-button type="text" @click="handleOpenDetailDialog('DoctorDiagnose')">
                            查看全部
                        </abc-button>
                    </abc-flex>
                    <div
                        v-if="!doctorDiagnosisSortData.data || !doctorDiagnosisSortData.data.length"
                        class="pie-chart-empty"
                    >
                        <abc-content-empty></abc-content-empty>
                    </div>
                    <abc-bar-chart
                        v-else
                        id="bar-chart"
                        height="250px"
                        class="pie-chart-style"
                        width="100%"
                        :grid="{
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        }"
                        :category="doctorDiagnosisSortData.category"
                        :legend="doctorDiagnosisSortData.legend"
                        :data="doctorDiagnosisSortData.data"
                        :x-axis="{
                            minInterval: 1,
                        }"
                    >
                    </abc-bar-chart>
                </stat-card>
            </template>
        </biz-statistic-card-board>

        <section class="card-container">
            <stat-card v-abc-loading="tableLoading" class="card">
                <abc-flex justify="space-between">
                    <abc-space>
                        <abc-text bold>
                            病种诊断明细
                        </abc-text>
                        <filter-select
                            v-model="doctorDiagnosisDetailParams.doctorId"
                            :width="120"
                            :inner-width="120"
                            placeholder="医生"
                            :options="doctorList"
                            :normalize-id="'doctorId'"
                            :normalize-key="'doctorName'"
                            id-with-name
                            @change="handleChangeDoctor"
                        >
                        </filter-select>
                    </abc-space>
                    <abc-button
                        style="margin-left: auto;"
                        type="blank"
                        @click="exportDiseaseDetail"
                    >
                        导出
                    </abc-button>
                </abc-flex>
                <abc-table
                    :render-config="doctorDiagnosisTableConfig"
                    :data-list="doctorDiagnosisDetailData.list"
                    :empty-opt="{
                        label: '暂无数据',
                    }"
                    style="margin-top: 16px;"
                >
                </abc-table>
                <abc-pagination
                    show-total-page
                    :pagination-params="pageParams"
                    :count="doctorDiagnosisDetailData.totalCount"
                    @current-change="pageTo"
                >
                </abc-pagination>
            </stat-card>
        </section>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';

    import StatDiseaseAPI from 'api/stat/disease';

    import { dateRangeFormat } from 'views/statistics/common/util';
    import { getOperationPieChartColor } from 'views/statistics/common/util';

    import ClinicSelect from 'views/statistics/common/clinic-select/clinic-select';
    import StatCard from '../../common/stat-card/stat-card';
    import DiseaseDetailDialog from './disease-detail-dialog';
    import { AbcDatePickerBar } from '@abc/ui-pc'; const { DatePickerBarOptions } = AbcDatePickerBar;
    import { formatDate } from '@abc/utils-date';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import BizStatisticCardBoard from '@/components-composite/biz-statistic-card-board';

    export default {
        name: 'DiseaseAnalysis',
        components: {
            BizStatisticCardBoard,
            ClinicSelect,
            StatCard,
            FilterSelect,
        },
        data() {
            return {
                dateParams: {
                    currentBasicInfoFilter: DatePickerBarOptions.DAY.label,
                    beginDate: '',
                    endDate: '',
                },
                paramsClinicId: '',

                pirChartLoading: false,
                barChartLoading: false,
                tableLoading: false,

                categoryList: [],
                doctorList: [],

                diseaseCategoryParams: {
                    clinicId: '',
                },
                diseaseProportionData: {
                    legend: {
                        type: 'scroll',
                        orient: 'vertical',
                        top: 18,
                        left: 200,
                        data: [],
                        textStyle: {
                            fontSize: 14,
                        },
                    },
                    data: [],
                },

                // 医生病种诊断排序
                doctorDiagnosisSortParams: {
                    category: '',
                },
                doctorDiagnosisSortData: {
                    legend: {
                        data: [],
                    },
                    category: [],
                    data: [],
                },

                doctorDiagnosisDetailParams: {
                    offset: 0,
                    limit: 10,
                    doctorId: '',
                },
                doctorDiagnosisDetailData: {
                    totalCount: 0,
                    list: [],
                },

                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                dateOptions: [
                    {
                        label: DatePickerBarOptions.DAY.label,
                        name: DatePickerBarOptions.DAY.name,
                        getValue() {
                            return [new Date(), new Date()];
                        },
                    },
                    DatePickerBarOptions.WEEK,
                    DatePickerBarOptions.MONTH,
                    DatePickerBarOptions.YEAR,
                ],
                doctorDiagnosisTableConfig: {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: '医生',
                            key: 'doctorName',
                        },
                        {
                            label: '科室',
                            key: 'departmentName',
                        },
                        {
                            label: '疾病',
                            key: 'category',
                        },
                        {
                            label: '诊断次数',
                            key: 'summary',
                            style: {
                                textAlign: 'right',
                            },
                        },
                    ],
                },
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'isChainAdmin', 'subClinics']),
            clinicList() {
                return this.subClinics
                    .map((clinic) => {
                        return {
                            ...clinic,
                            shortName: this.chainId === clinic.id ? '总部' : clinic.shortName,
                        };
                    })
                    .filter((clinic) => clinic.name !== '总部');
            },
            pageParams() {
                const {
                    limit: pageSize, offset,
                } = this.doctorDiagnosisDetailParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
            displayDateRange() {
                return dateRangeFormat(this.dateParams.beginDate, this.dateParams.endDate);
            },
        },

        async created() {
            if (this.isChainAdmin) {
                this.paramsClinicId = '';
            } else {
                this.paramsClinicId = this.currentClinic.clinicId;
            }
            this.dateParams.beginDate = formatDate(new Date());
            this.dateParams.endDate = formatDate(new Date());

            // 获取病种类型
            this.fetchDiagnoseDoctors();
            await this.fetchDiagnoseCategory();

            this.fetchData();

            this.fetchDiseaseDetailTotal();
        },
        methods: {
            fetchData() {
                // 病种占比
                this.fetchDiseaseProportion();
                // 病种排序
                this.fetchDoctorDiagnosisSort();
                // 诊断详情
                this.fetchDoctorDiagnosisDetail();
            },
            async handleDateChange(date) {
                this.dateParams.beginDate = date[0];
                this.dateParams.endDate = date[1];
                this.doctorDiagnosisDetailParams.offset = 0;

                this.fetchDiagnoseDoctors();
                await this.fetchDiagnoseCategory();
                this.fetchData();
                this.fetchDiseaseDetailTotal();
            },
            async handleChangeClinic() {
                this.doctorDiagnosisDetailParams.offset = 0;
                this.fetchDiagnoseDoctors();
                await this.fetchDiagnoseCategory();
                this.fetchData();
                this.fetchDiseaseDetailTotal();
            },

            handleOpenDetailDialog(type) {
                this.diseaseDetailDialog = new DiseaseDetailDialog({
                    value: true,
                    type,
                    beginDate: this.dateParams.beginDate,
                    endDate: this.dateParams.endDate,
                    clinicId: this.paramsClinicId,
                    onClose: () => {
                        this.diseaseDetailDialog?.destroyDialog();
                    },
                });
                this.diseaseDetailDialog.generateDialog({ parent: this });
            },
            // 获取医生列表
            async fetchDiagnoseDoctors() {
                try {
                    const params = {
                        beginDate: this.dateParams.beginDate,
                        endDate: this.dateParams.endDate,
                        clinicId: this.paramsClinicId,
                    };
                    const { data } = await StatDiseaseAPI.fetchDiagnoseDoctors(params);
                    this.doctorList = data.list;
                } catch (e) {
                    console.warn('获取诊断医生类型失败', e);
                }
            },
            // 获取病种类型
            async fetchDiagnoseCategory() {
                try {
                    const params = {
                        beginDate: this.dateParams.beginDate,
                        endDate: this.dateParams.endDate,
                        clinicId: this.paramsClinicId,
                    };

                    const { data } = await StatDiseaseAPI.fetchDiagnoseCategory(params);
                    this.categoryList = data.list;
                    this.doctorDiagnosisSortParams.category = '';
                    if (this.categoryList && this.categoryList.length) {
                        this.doctorDiagnosisSortParams.category = this.categoryList[0].category;
                    }
                } catch (e) {
                    console.warn('获取病种类型失败', e);
                }
            },

            /** *********** 病种占比明细 ************* **/
            async fetchDiseaseProportion() {
                try {
                    this.pirChartLoading = true;
                    const params = {
                        beginDate: this.dateParams.beginDate,
                        endDate: this.dateParams.endDate,
                        clinicId: this.paramsClinicId,
                    };
                    const { data } = await StatDiseaseAPI.fetchDiseaseProportion(params);
                    this.diseaseProportionData = this.initRenderDiseaseProportion(data.list);
                    this.pirChartLoading = false;
                } catch (e) {
                    console.warn('获取病种占比失败', e);
                    this.pirChartLoading = false;
                }
            },
            initRenderDiseaseProportion(data) {
                const legendData = [];
                const renderData = [];
                const curData = [];
                data.forEach((item) => {
                    if (item.percent) {
                        curData.push(this.formatPieData(item));
                    }
                });
                curData.forEach((item, index) => {
                    legendData.push({
                        name: item.name,
                    });
                    renderData.push({
                        ...item,
                        itemStyle: {
                            normal: {
                                type: 'solid',
                                color: getOperationPieChartColor(index),
                            },
                        },
                    });
                });
                return {
                    tooltip: {
                        formatter: '{b}',
                    },
                    legend: {
                        type: 'scroll',
                        orient: 'vertical',
                        top: 18,
                        left: 200,
                        data: legendData,
                        textStyle: {
                            fontSize: 14,
                        },
                    },
                    data: renderData,
                };
            },
            formatPieData(item) {
                let value = '';
                let name = '';
                value = (item.percent * 100).toFixed(2);
                name = `${item.category}(${value}%, ${item.summary}次)`;
                return {
                    value,
                    name,
                };
            },

            /** *********** 医生病种诊断排序 ************* **/
            async fetchDoctorDiagnosisSort() {
                try {
                    this.barChartLoading = true;
                    this.doctorDiagnosisSortData.category = [];
                    this.doctorDiagnosisSortData.data = [];
                    const params = {
                        beginDate: this.dateParams.beginDate,
                        endDate: this.dateParams.endDate,
                        clinicId: this.paramsClinicId,
                        category: this.doctorDiagnosisSortParams.category,
                    };
                    const { data } = await StatDiseaseAPI.fetchDoctorDiagnosisSort(params);
                    this.doctorDiagnosisSortData.category = data.list.map((item) => {
                        return item.doctorName;
                    });
                    this.doctorDiagnosisSortData.data = data.list.map((item) => {
                        return item.summary;
                    });

                    this.barChartLoading = false;
                } catch (e) {
                    this.barChartLoading = false;
                    console.warn('医生病种诊断排序', e);
                }
            },
            async handleChangeCategory() {
                await this.fetchDoctorDiagnosisSort();
            },

            /** *********** 病种诊断明细 ************* **/
            /**
             * @desc 获取医生诊断明细
             */
            async fetchDoctorDiagnosisDetail() {
                try {
                    this.tableLoading = true;
                    const params = {
                        beginDate: this.dateParams.beginDate,
                        endDate: this.dateParams.endDate,
                        clinicId: this.paramsClinicId,
                        ...this.doctorDiagnosisDetailParams,
                    };
                    const { doctorId } = this.doctorDiagnosisDetailParams;
                    const employees = doctorId ? [
                        {
                            id: doctorId.split('-idWithName-')[0],
                            name: doctorId.split('-idWithName-')[1],
                        },
                    ] : [];
                    params.employees = JSON.stringify(employees);
                    const { data } = await StatDiseaseAPI.fetchDoctorDiagnosisDetail(params);
                    this.doctorDiagnosisDetailData.list = data.list;
                    this.tableLoading = false;
                } catch (e) {
                    console.warn('获取医生诊断明细失败', e);
                    this.tableLoading = false;
                }
            },
            exportDiseaseDetail() {
                const params = {
                    beginDate: this.dateParams.beginDate,
                    endDate: this.dateParams.endDate,
                    ...this.doctorDiagnosisDetailParams,
                };
                const { doctorId } = this.doctorDiagnosisDetailParams;
                const employees = doctorId ? [
                    {
                        id: doctorId.split('-idWithName-')[0],
                        name: doctorId.split('-idWithName-')[1],
                    },
                ] : [];
                params.employees = JSON.stringify(employees);
                StatDiseaseAPI.exportDiagnoseDetail(params);
            },
            async fetchDiseaseDetailTotal() {
                try {
                    const params = {
                        beginDate: this.dateParams.beginDate,
                        endDate: this.dateParams.endDate,
                        clinicId: this.paramsClinicId,
                        ...this.doctorDiagnosisDetailParams,
                    };
                    const { doctorId } = this.doctorDiagnosisDetailParams;
                    const employees = doctorId ? [
                        {
                            id: doctorId.split('-idWithName-')[0],
                            name: doctorId.split('-idWithName-')[1],
                        },
                    ] : [];
                    params.employees = JSON.stringify(employees);
                    const { data } = await StatDiseaseAPI.fetchDiagnoseDetailTotal(params);
                    this.doctorDiagnosisDetailData.totalCount = data.dataTotal;
                } catch (e) {
                    console.warn(e);
                }
            },
            handleChangeDoctor() {
                this.doctorDiagnosisDetailParams.offset = 0;
                this.fetchDiseaseDetailTotal();
                this.fetchDoctorDiagnosisDetail();
            },
            async pageTo(page) {
                this.doctorDiagnosisDetailParams.offset = (page - 1) * this.doctorDiagnosisDetailParams.limit;
                await this.fetchDoctorDiagnosisDetail();
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';

    .stat-ms-disease-wrapper {
        padding: 20px 20px 0;

        .stat-card .abc-fixed-table .table-empty {
            .label {
                font-weight: normal;
                color: $T2;
            }
        }

        .statistic-card-board-wrapper {
            margin: 16px 0;

            .card {
                height: 100%;
                padding: 24px;

                .pie-chart-empty {
                    height: 200px;
                }
            }
        }
    }
</style>
