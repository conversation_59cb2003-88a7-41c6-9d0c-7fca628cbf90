<template>
    <div class="statistics__patient-source-container" style="padding: 20px 20px 0;">
        <abc-space :size="8">
            <abc-date-picker-bar
                v-model="params.currentDateRangeFilter"
                :options="dateOptions"
                :picker-options="pickerOptions"
                value-format="YYYY-MM-DD"
                :clearable="false"
                @change="handleDateChange"
            ></abc-date-picker-bar>
            <clinic-select
                v-if="isChainAdmin"
                v-model="params.clinicIdFilter"
                placeholder="门店"
                :show-all-clinic="false"
                width="120"
            >
            </clinic-select>
        </abc-space>

        <biz-statistic-card-board
            :data-list="[
                { slotName: 'pie-chart' },
                { slotName: 'bar-chart' }
            ]"
            min-col-width="760px"
            :gutter="[16, 16]"
            type="custom"
            class="statistic-card-board-wrapper"
        >
            <template #pie-chart>
                <stat-card v-abc-loading="pieChartLoading" class="card">
                    <div class="source-card-handler" style="height: auto;">
                        <abc-text bold>
                            首诊来源客量
                        </abc-text>
                        <abc-radio-group v-model="pieTabType" @change="fetchSourceAnalysisCount('pie')">
                            <abc-radio-button
                                v-for="tab in tabOptions"
                                :key="tab.value"
                                :label="tab.value"
                            >
                                {{ tab.label }}
                            </abc-radio-button>
                        </abc-radio-group>
                    </div>
                    <abc-text theme="gray" size="mini">
                        {{ displayDateRange }}
                    </abc-text>
                    <div v-if="!typePieData.data || !typePieData.data.length" class="pie-chart-empty">
                        <abc-content-empty></abc-content-empty>
                    </div>
                    <abc-pie-chart
                        v-else
                        id="pie-chart"
                        class="pie-chart-style"
                        :data="typePieData.data"
                        :legend="typePieData.legend"
                        :tooltip="typePieData.tooltip"
                        :center="['100', '125']"
                        height="250px"
                        width="540px"
                    >
                    </abc-pie-chart>
                </stat-card>
            </template>
            <template #bar-chart>
                <stat-card v-abc-loading="barChartLoading" class="card">
                    <div class="source-card-handler" style="height: auto;">
                        <abc-text bold>
                            首诊来源消费金额前十
                        </abc-text>
                        <abc-radio-group v-model="barTabType" @change="fetchSourceAnalysisCount('bar')">
                            <abc-radio-button
                                v-for="tab in tabOptions"
                                :key="tab.value"
                                :label="tab.value"
                            >
                                {{ tab.label }}
                            </abc-radio-button>
                        </abc-radio-group>
                    </div>
                    <abc-text theme="gray" size="mini">
                        {{ displayDateRange }}
                    </abc-text>
                    <div
                        v-if="!typeBarData.data || !typeBarData.data.length"
                        class="pie-chart-empty"
                    >
                        <abc-content-empty></abc-content-empty>
                    </div>
                    <abc-bar-chart
                        v-else
                        id="bar-chart"
                        height="250px"
                        class="pie-chart-style"
                        width="100%"
                        :grid="{
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        }"
                        :category="typeBarData.category"
                        :legend="typeBarData.legend"
                        :data="typeBarData.data"
                    >
                    </abc-bar-chart>
                </stat-card>
            </template>
        </biz-statistic-card-board>

        <section class="card-container">
            <stat-card v-abc-loading="tableLoading" class="card" style="width: 100%;">
                <div class="source-card-handler">
                    <abc-flex vertical="vertical" style="margin-right: 16px;">
                        <span class="label">首诊来源明细</span>
                        <div class="date-range">
                            {{ displayDateRange }}
                        </div>
                    </abc-flex>
                    <abc-cascader
                        ref="visitSourceRef"
                        v-model="tableParams.visitSourceIdList"
                        :props="{
                            children: 'children',
                            label: 'name',
                            value: 'newId',
                        }"
                        placeholder="首诊来源"
                        multiple
                        mutually-exclusive
                        separation="-"
                        :width="120"
                        :options="visitSourceFilterOptions"
                        @change="handleVisitSourceChange"
                    >
                    </abc-cascader>
                    <div style="margin-left: auto;">
                        <stat-toolbar
                            ref="statToolbarRef"
                            :enable-features="['__EXPORT__']"
                            :export-task-type="exportTaskType"
                            :handle-export="handleExport"
                        >
                        </stat-toolbar>
                    </div>
                </div>
                <abc-table
                    :render-config="renderTableHeader"
                    :data-list="tableData"
                    :empty-opt="{
                        label: '暂无数据',
                    }"
                    style="margin-top: 16px;"
                >
                </abc-table>
                <abc-pagination
                    show-total-page
                    :pagination-params="pageParams"
                    :count="totalCount"
                    @current-change="handlePageIndexChange"
                >
                </abc-pagination>
            </stat-card>
        </section>
    </div>
</template>

<script>
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    import { AbcDatePickerBar } from '@abc/ui-pc';
    import { mapGetters } from 'vuex';
    import {
        dateRangeFormat, getOperationPieChartColor,
    } from 'views/statistics/common/util';
    import StatCard from 'views/statistics/common/stat-card/stat-card.vue';
    import { formatDate } from '@abc/utils-date';
    import StatAPI from 'api/stat/patientSource';
    import TableUtilsMixin from 'views/statistics/mixins/table-utils-mixin';
    import { RecommendService } from '@/service/recommend';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar.vue';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import BizStatisticCardBoard from '@/components-composite/biz-statistic-card-board';
    import { resolveHeader } from 'utils/table';

    const { DatePickerBarOptions } = AbcDatePickerBar;

    export default {
        name: 'PatientSource',
        components: {
            BizStatisticCardBoard,
            StatToolbar,
            StatCard,
            ClinicSelect,
        },
        mixins: [TableUtilsMixin],
        data() {
            return {
                params: {
                    beginDate: '',
                    endDate: '',
                    currentDateRangeFilter: DatePickerBarOptions.DAY.label,
                    clinicIdFilter: '',
                },
                tabOptions: [
                    {
                        label: '一级来源',
                        value: 1,
                    },
                    {
                        label: '二级来源',
                        value: 2,
                    },
                    {
                        label: '三级来源',
                        value: 3,
                    },
                ],
                pieTabType: 1,
                barTabType: 1,
                dateOptions: [
                    {
                        label: DatePickerBarOptions.DAY.label,
                        name: DatePickerBarOptions.DAY.name,
                        getValue() {
                            return [new Date(), new Date()];
                        },
                    },
                    DatePickerBarOptions.WEEK,
                    DatePickerBarOptions.MONTH,
                    DatePickerBarOptions.YEAR,
                ],
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                pieChartLoading: false,
                typePieData: {
                    legend: {
                        type: 'scroll',
                        orient: 'vertical',
                        top: 18,
                        left: 200,
                        data: [],
                        textStyle: {
                            fontSize: 14,
                        },
                    },
                    data: [],
                },
                typeBarData: {
                    legend: {
                        data: [],
                    },
                    category: [],
                    data: [],
                },
                barChartLoading: false,

                tableLoading: false,
                tableParams: {
                    sourceTypeOne: '',
                    sourceTypeTwo: '',
                    sourceTypeThree: '',
                    pageIndex: 0,
                    pageSize: 10,
                    visitSourceIdList: [],
                },
                tableData: [],
                tableHeader: [],
                totalCount: 0,

                visitSourceFilterOptions: [],
                exportTaskType: 'source-analysis',
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin','currentClinic']),
            displayDateRange() {
                return dateRangeFormat(this.params.beginDate, this.params.endDate);
            },
            pageParams() {
                const {
                    pageSize, pageIndex,
                } = this.tableParams;
                return {
                    pageIndex,
                    pageSize,
                };
            },
            renderTableHeader() {
                const list = resolveHeader(this.tableHeader, {}, true).map((item) => {
                    if (item.children) {
                        item.children.forEach((child) => {
                            child.style = Object.assign({}, child.style, {
                                textAlign: 'right',
                            });
                        });
                    }
                    return item;
                });
                return {
                    hasInnerBorder: true,
                    list,
                };
            },
        },
        created() {
            this.exportService = new ExportService();
            if (this.isChainAdmin) {
                this.params.clinicIdFilter = '';
            } else {
                this.params.clinicIdFilter = this.currentClinic.clinicId;
            }
            this.params.beginDate = formatDate(new Date());
            this.params.endDate = formatDate(new Date());

            this.fetchData();
            this.getListSource();
        },
        beforeDestroy() {
            this.exportService.destroy();
        },
        methods: {
            handleDateChange(date) {
                this.params.beginDate = date[0];
                this.params.endDate = date[1];
                this.fetchData();
            },
            createFetchParams(mode) {
                const modeParamsMapping = {
                    'pie': {
                        type: this.pieTabType,
                    },
                    'bar': {
                        type: this.barTabType,
                    },
                    'default': {},
                };
                const modeParams = modeParamsMapping[mode] ?? modeParamsMapping.default;
                return {
                    beginDate: this.params.beginDate,
                    endDate: this.params.endDate,
                    clinicId: this.params.clinicIdFilter,
                    ...modeParams,
                };
            },
            fetchData() {
                this.fetchSourceAnalysisCount();
                this.fetchSourceAnalysisItem();
            },
            async fetchSourceAnalysisCount(mode) {
                const {
                    beginDate,endDate,clinicId,type,
                } = this.createFetchParams(mode);
                if (mode === 'pie') {
                    this.pieChartLoading = true;
                } else if (mode === 'bar') {
                    this.barChartLoading = true;
                } else {
                    this.pieChartLoading = true;
                    this.barChartLoading = true;
                }
                try {
                    const { data } = await StatAPI.fetchSourceAnalysisCount({
                        beginDate,
                        endDate,
                        clinicId,
                        type,
                    });
                    if (mode === 'pie') {
                        this.typePieData = this.initTypePieData(data ?? []);
                    } else if (mode === 'bar') {
                        this.typeBarData = this.initTypeBarData(data ?? []);
                    } else {
                        this.typePieData = this.initTypePieData(data ?? []);
                        this.typeBarData = this.initTypeBarData(data ?? []);
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.pieChartLoading = false;
                    this.barChartLoading = false;
                }
            },
            async fetchSourceAnalysisItem(resetPageParams = true) {
                this.tableLoading = true;
                const {
                    beginDate,endDate,clinicId,
                } = this.createFetchParams();
                if (resetPageParams) {
                    this.tableParams.pageIndex = 0;
                }
                const {
                    pageIndex,pageSize,sourceTypeOne,sourceTypeTwo,sourceTypeThree,
                } = this.tableParams;
                const params = {
                    offset: pageIndex * pageSize,
                    limit: pageSize,
                    sourceTypeOne,
                    sourceTypeTwo,
                    sourceTypeThree,
                };
                try {
                    const { data } = await StatAPI.fetchSourceAnalysisItem({
                        beginDate,
                        endDate,
                        clinicId,
                        ...params,
                    });
                    this.setTableData(false, data, resetPageParams);
                } catch (e) {
                    this.setTableData(true);
                    console.log(e);
                } finally {
                    this.tableLoading = false;
                }
            },
            createSourceLabel(type,item) {
                const sourceName = item.sourceName ?? '';
                const sourceTwoName = item.sourceTwoName ?? '';
                const formName = item.formName ?? '';
                const mapping = {
                    1: sourceName,
                    2: `${sourceName}/${sourceTwoName}`,
                    3: `${sourceName}/${sourceTwoName}/${formName}`,
                };
                return mapping[type] ?? '';
            },
            initTypePieData(data) {

                const legendData = [];
                const renderData = [];
                const curData = data.map((item) => {
                    const name = this.createSourceLabel(this.pieTabType,item);
                    return {
                        name: `${name} (${item.count})`,
                        value: item.count,
                    };
                });
                curData.forEach((item, index) => {
                    legendData.push({
                        name: item.name,
                    });
                    renderData.push({
                        ...item,
                        itemStyle: {
                            normal: {
                                type: 'solid',
                                color: getOperationPieChartColor(index),
                            },
                        },
                    });
                });
                return {
                    // tooltip: {
                    //     formatter: '{b}',
                    // },
                    legend: {
                        type: 'scroll',
                        orient: 'vertical',
                        top: 18,
                        left: 200,
                        data: legendData,
                        textStyle: {
                            fontSize: 14,
                        },
                    },
                    data: renderData,
                };
            },
            initTypeBarData(data) {
                const _data = data.sort((a, b) => a.amount - b.amount).slice(-10);
                const category = _data.map((item) => {
                    return this.createSourceLabel(this.barTabType,item) ;
                });
                const dataArr = _data.map((item) => {
                    return item.amount;
                });
                return {
                    ...this.typeBarData,
                    category,
                    data: dataArr,
                };
            },
            handlePageIndexChange(index) {
                this.tableParams.pageIndex = index - 1;
                this.fetchSourceAnalysisItem(false);
            },
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();
                }
                this.visitSourceFilterOptions = RecommendService.getInstance().cascaderOptions;
                this.visitSourceFilterOptions.unshift({
                    id: '00000000000000000000000000000000',
                    name: '未指定',
                });
                this.visitSourceFilterOptions = RecommendService.getInstance().processIds(this.visitSourceFilterOptions);
            },
            handleVisitSourceChange() {
                this.generateVisitSource2Params(this.$refs.visitSourceRef.getOriValue(this.tableParams.visitSourceIdList));
            },
            generateVisitSource2Params(list) {
                // 第一级是1， 2，下面要拼 null
                // 第一级是0，下面是3， 4要拼 null
                // 其余该怎么处理，就怎么处理
                const newList = list.map((item) => item || []);
                const medicalRecordType1 = newList.length >= 1 && newList[0]?.length ? [...new Set(newList[0].map((item) => item.id))] : [];
                const medicalRecordType2 = [];
                let medicalRecordType3 = [];
                if (newList.length >= 2 && newList[1]?.length) {
                    const arr = [...new Set(newList[1].map((item) => item.newId))];
                    arr.forEach((item) => {
                        if (item.split('/').length === 3) {
                            medicalRecordType3.push(item);
                        } else {
                            medicalRecordType2.push(item);
                        }
                    });
                }

                if (newList.length >= 3 && newList[2]?.length) {
                    medicalRecordType3 = [...medicalRecordType3, ...new Set(newList[2].map((item) => item.newId))];
                }
                this.tableParams.sourceTypeOne = medicalRecordType1.join(',');
                this.tableParams.sourceTypeTwo = medicalRecordType2.join(',');
                this.tableParams.sourceTypeThree = medicalRecordType3.join(',');

                this.fetchSourceAnalysisItem();
            },
            async handleExport() {
                const {
                    beginDate,endDate,clinicId,
                } = this.createFetchParams();
                const {
                    pageIndex,pageSize,sourceTypeOne,sourceTypeTwo,sourceTypeThree,
                } = this.tableParams;
                const params = {
                    offset: pageIndex * pageSize,
                    limit: pageSize,
                    sourceTypeOne,
                    sourceTypeTwo,
                    sourceTypeThree,
                    beginDate,
                    endDate,
                    clinicId,
                };
                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        ...params,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },
        },
    };
</script>

<style lang="scss">
.statistics__patient-source-container {
    .statistic-card-board-wrapper {
        margin: 16px 0;

        .card {
            height: 100%;
            padding: 24px;

            .pie-chart-empty {
                height: 200px;
            }
        }
    }
}

.source-card-handler {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
}
</style>
