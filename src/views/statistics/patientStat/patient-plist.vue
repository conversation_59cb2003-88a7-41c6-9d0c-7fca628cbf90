<template>
    <abc-layout class="common-padding-container" preset="page-table">
        <abc-layout-header>
            <stat-toolbar
                ref="statToolbarRef"
                :enable-features="toolbarFeatures"
                :patient-id-filter.sync="params.patientId"
                :clinic-id-filter.sync="params.clinicId"
                :date-filter.sync="params.dateFilter$"
                :handle-export="exports"
                :setting-options="settingOptions"
                :clinic-list="clinicList"
                :export-task-type="exportTaskType"
                :custom-clinic-employee="true"
                date-label="建档日期"
                custom-clinic-placeholder="活跃门店"
                :limit-export-date="false"
                @change-date="handleDateChange"
                @setting-item-click="handleOpenCustomHeaderDialog"
                @change-clinic="handleClinicChange"
            >
                <abc-button
                    :type="filterCrmParamsCount ? 'blank' : 'ghost'"
                    :style="{
                        maxWidth: '200px',
                    }"
                    icon="n-filter-line"
                    @click="visibleCrmFilter = true"
                >
                    <span
                        v-abc-title.ellipsis="filterCrmParamsCount ? `已筛选${filterCrmParamsCount}项` : '筛选'"
                        :style="{
                            display: 'inline-block',
                            color: filterCrmParamsCount ? '#000' : '#7a8794',
                            maxWidth: '150px',
                        }"
                    >
                    </span>
                </abc-button>
            </stat-toolbar>
        </abc-layout-header>

        <!--表格-->
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :loading="loading"
                :data-list="list"
                :render-config="renderTableHeader"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="currentPageParams"
                :count="tableCount"
                :class="{ 'show-total': true }"
                @current-change="changePageIndex"
            >
                <ul slot="tipsContent">
                    <li>
                        共 <span>{{ tableCount }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>

        <dialog-filter
            v-if="visibleCrmFilter"
            v-model="visibleCrmFilter"
            is-from-stat
            :filter-config="initFilterCrmConfig"
            @change="onChangeFilterConfig"
        ></dialog-filter>
    </abc-layout>
</template>

<script>
    import StatApi from 'api/stat';

    import {
        formatDate,
    } from '@abc/utils-date';
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import { resolveHeaderV2 } from 'views/statistics/utils';
    import { mapGetters } from 'vuex';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import DialogFilter from 'views/crm/common/package-filter/index.vue';
    import MixinFilter from 'views/crm/common/package-filter/mixin-filter';
    import { isNotNull } from '@/lis/common/tools';
    import { BizCustomHeader } from '@/components-composite/biz-custom-header';
    import TableHeaderAPI from 'views/statistics/core/api/table-header';
    import { tryJSONParse } from '@/utils';
    import DecodeService from '@/service/decode';
    const { patientStat } = getViewDistributeConfig().Statistics;

    export default {
        name: 'PatientPlist',
        components: {
            DialogFilter, StatToolbar,
        },
        mixins: [ClinicTypeJudger, DateParamsMixins,MixinFilter],
        data() {
            return {
                params: {
                    patientId: '',
                    clinicId: '',
                    doctorIdFilter: '',
                    pageIndex: 0,
                    pageSize: 0,
                    dateFilter: {
                        begin: '',
                        end: '',
                        dateRange: [],
                    },
                },
                loading: false,
                tableCount: 0,
                list: [],
                postData: {
                    patientName: '',
                    patientId: '',
                },
                exportTaskType: 'patient-stat',
                calcStatTableInfo: {},
                tableHeader: [],
                settingOptions: [
                    {
                        text: '设置展示字段',
                        value: '',
                        isOpen: false,
                        groupName: '',
                    },
                ],
                tagOptions: [],
                visibleCrmFilter: false,
                filterCrmConfig: null,
                staticConfig: {
                    hasInnerBorder: true,
                    list: [
                        {
                            'label': '创建日期',
                            'key': 'created',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '姓名',
                            'key': 'name',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '性别',
                            'key': 'sex',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '年龄',
                            'key': 'age',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '手机号',
                            'key': 'mobile',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '档案号',
                            'key': 'sn',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '150px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '证件号',
                            'key': 'idCard',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '180px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '婚否',
                            'key': 'marital',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '职业',
                            'key': 'profession',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '民族',
                            'key': 'ethnicity',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '家庭住址',
                            'key': 'address',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '150px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '首诊来源',
                            'key': 'visitSource',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '120px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '到店原因',
                            'key': 'visitReason',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '患者标签',
                            'key': 'patientTag',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '最近就诊',
                            'key': 'lastOutpatientDate',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'label': '门诊就诊次数合计',
                            'key': 'visitNum',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '140px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'right',
                            },
                        },
                        {
                            'label': '门诊费用合计',
                            'key': 'allCumulativeAmount',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '120px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'right',
                            },
                        },
                        {
                            'label': '既往史',
                            'key': 'pastHistory',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '120px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '过敏史',
                            'key': 'allergicHistory',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '120px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '体重',
                            'key': 'weight',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'right',
                            },
                        },
                        {
                            'label': '备注',
                            'key': 'remark',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '90px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '生日',
                            'key': 'birthday',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '工作单位',
                            'key': 'company',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '微信绑定',
                            'key': 'wxBind',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'center',
                            },
                        },
                        {
                            'label': '会员类型',
                            'key': 'memberType',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'center',
                            },
                        },
                        {
                            'label': '门店新老客',
                            'key': 'clinicCustomers',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'center',
                            },
                        },
                        {
                            'label': '门诊类型',
                            'key': 'outpatientType',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'center',
                            },
                        },
                        {
                            'label': '最近预约时间',
                            'key': 'lastReserveDate',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'center',
                            },
                        },
                        {
                            'label': '最近执行时间',
                            'key': 'lastExecuteTime',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'center',
                            },
                        },
                        {
                            'label': '活跃门店',
                            'key': 'archiveClinic',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                            },
                        },
                        {
                            'label': '就诊医生/初复诊',
                            'key': 'outpatientInfo',
                            'sortable': 0,
                            'style': {
                                'flex': '1',
                                'width': '160px',
                                'minWidth': '',
                                'maxWidth': '',
                                textAlign: 'center',
                            },
                        },
                    ],
                },
                notCrm: true,
            };
        },

        computed: {
            ...mapGetters(['currentClinic','enablePatientMobileInStatistics','isForbiddenExport']),
            ...mapGetters('theme', ['curTypeId']),

            toolbarFeatures() {
                const features = [
                    StatToolbar.Feature.DATE,
                    StatToolbar.Feature.CLINIC,
                    StatToolbar.Feature.PATIENT,
                    StatToolbar.Feature.SETTING,
                ];
                return this.isForbiddenExport ? features : [
                    StatToolbar.Feature.EXPORT,
                    ...features,
                ];
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.currentClinic?.clinicId;
            },
            renderTableHeader() {
                const config = resolveHeaderV2({
                    header: this.tableHeader,
                    staticConfig: this.staticConfig,
                    renderTypeList: this.renderTypeList,
                    renderTypeMap: {
                        patientName: 'patientNameRender',
                    },
                });
                config.list.forEach((item) => {
                    if (item.children) {
                        item.children.forEach((child) => {
                            child.autoSort = child.sortable;
                        });
                    }
                    item.autoSort = item.sortable;
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    ...config,
                };
            },
            currentPageParams() {
                return {
                    showTotalPage: false,
                    pageIndex: this.params.pageIndex,
                    pageSize: this.params.pageSize,
                    count: this.tableCount,
                };
            },

            renderTypeList() {
                return {
                    patientNameRender: (h, row) => {
                        const value = row.name;
                        let className = 'cell';
                        if (row.isMember) {
                            className += ' vip';
                        }
                        return (
                            <div class={className} title={value}>
                                {value}
                            </div>
                        );
                    },
                };
            },

            clinicList() {
                return this.subClinics.map((clinic) => {
                    return {
                        ...clinic,
                        shortName: this.chainId === clinic.id ? '总部' : clinic.shortName,
                    };
                }).filter((item) => item.shortName !== '总部');
            },

            plistCustomStatHeaderTableKey() {
                return patientStat.plistCustomStatHeaderTableKey;
            },

            filterCrmInfo() {
                return this.createFilterParams(this.filterCrmConfig);
            },
            filterCrmParamsCount() {
                return this. filterCrmInfo.labels?.length ?? 0;
            },
            initFilterCrmConfig() {
                return {
                    ...this.filterCrmConfig,
                    createDate: {
                        startDate: this.params.dateFilter$.begin,
                        endDate: this.params.dateFilter$.end,
                    },
                    activeClinicId: this.queryClinicId,
                };
            },
        },
        watch: {
            'params.patientId': {
                handler() {
                    this.queryPatientList(true);
                },
                deep: true,
            },
        },
        created() {
            this.params.clinicId = '';
            const date = formatDate(new Date());
            this.params.dateFilter = {
                begin: date,
                end: date,
                dateRange: [date, date],
            };
            this.exportService = new ExportService();
            this.$abcEventBus.$on('amount-setting-success', () => this.getTableData(), this);
        },
        beforeDestroy() {
            this.exportService.destroy();
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            async handleMounted(data) {
                this.params.pageSize = (data.paginationLimit - 1) || 10;
                await this.getTableData();
            },
            handleOpenCustomHeaderDialog() {
                new BizCustomHeader({
                    value: true,
                    tableKey: this.plistCustomStatHeaderTableKey,
                    titleName: '运营分析 - 患者清单',
                    mode: 'draggle',
                    showFixed: false,
                    finishFunc: this.getTableData,
                    tableHeaderApi: TableHeaderAPI,
                }).generateDialog({ parent: this });
            },
            async getTableData() {
                await this.$nextTick();
                this.loading = true;
                const {
                    pageIndex, pageSize, patientId, dateFilter$,
                } = this.params;
                const {
                    begin: beginDate, end: endDate,
                } = dateFilter$;
                const baseParams = {
                    limit: pageSize,
                    offset: pageSize * pageIndex,
                    beginCreatedDate: beginDate,
                    endCreatedDate: endDate,
                    activeClinicId: this.queryClinicId,
                    patientId,
                    enablePatientMobile: this.enablePatientMobileInStatistics,
                };
                const crmParams = this.handleFilterCrmInfoParams();
                const params = this.notCrm ? {
                    ...crmParams,
                    ...baseParams,
                } : {
                    ...baseParams,
                    ...crmParams,
                };
                params.activeClinicId = this.isChainAdmin ? params.activeClinicId : this.queryClinicId;
                StatApi.getPatientStat(params,{ e: 1 }).then((val) => {
                    this.handleTableList(val?.data || []);
                    this.tableHeader = val?.header || [];
                    this.tableCount = val?.total?.count || 0;
                }).finally(() => {
                    this.loading = false;
                });
            },
            async handleTableList(list = []) {
                if (!list || list.length === 0) {
                    this.list = [];
                    return;
                }
                const decryptKeys = [
                    'name', 'mobile', 'sn', 'idCard', 'email', 'address', 'company',
                ];
                // 处理所有需要解密的字段
                const decryptAllFields = async () => {
                    const decryptedData = {};

                    // 为每个需要解密的字段创建解密 Promise
                    for (const key of decryptKeys) {
                        const decryptPromises = list.map((item) => {
                            // 检查字段是否存在且有值
                            if (isNotNull(item[key])) {
                                return DecodeService.aecSignForDec(item[key]);
                            }
                            // 如果字段不存在或为空，返回原值或空字符串
                            return Promise.resolve(item[key] || '');
                        }) || [];

                        // 等待当前字段的所有解密操作完成
                        decryptedData[key] = await Promise.all(decryptPromises);
                    }

                    return decryptedData;
                };

                // 等待所有字段的解密操作完成
                const allDecryptedData = await decryptAllFields();

                this.list = list.map((item, index) => {
                    // 为当前项应用所有解密后的数据
                    const decryptedFields = {};
                    decryptKeys.forEach((key) => {
                        if (allDecryptedData[key] && allDecryptedData[key][index] !== undefined) {
                            decryptedFields[key] = tryJSONParse(allDecryptedData[key][index]);
                        }
                    });

                    return {
                        ...item,
                        ...decryptedFields,
                    };
                }) || [];
            },
            handleDateChange() {
                this.notCrm = true;
                this.queryPatientList();
            },
            handleClinicChange(val) {
                this.notCrm = true;
                this.params.clinicId = val;
                this.queryPatientList();
            },
            // 分页
            changePageIndex(index) {
                this.params.pageIndex = index - 1;
                this.queryPatientList(false);
            },
            queryPatientList(resetPageParams = true) {
                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }
                this.getTableData();
            },
            // 导出患者清单
            async exports() {
                const {
                    patientId, dateFilter$,
                } = this.params;
                const {
                    begin: beginDate, end: endDate,
                } = dateFilter$;
                const baseParams = {
                    pageIndex: 0,
                    pageSize: 0,
                    beginCreatedDate: beginDate,
                    endCreatedDate: endDate,
                    activeClinicId: this.queryClinicId,
                    patientId,
                    enablePatientMobile: this.enablePatientMobileInStatistics,
                };
                const crmParams = this.handleFilterCrmInfoParams();
                const params = this.notCrm ? {
                    ...crmParams,
                    ...baseParams,
                } : {
                    ...baseParams,
                    ...crmParams,
                };
                params.activeClinicId = this.isChainAdmin ? params.activeClinicId : this.queryClinicId;
                try {
                    await this.exportService.startExport(this.exportTaskType,params);
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            /**
             * desc [修改筛选条件]
             */
            onChangeFilterConfig(filterConfig) {
                this.filterCrmConfig = filterConfig;
                const {
                    createDate = {},activeClinicId = '',
                } = this.filterCrmConfig;
                const {
                    startDate,endDate,
                } = createDate;
                this.params.dateFilter$ = {
                    begin: startDate,
                    end: endDate,
                    dateRange: [ startDate, endDate ],
                };
                this.params.clinicId = activeClinicId;
                this.notCrm = false;
                this.visibleCrmFilter = false;
                this.getTableData();
            },

            handleFilterCrmInfoParams() {
                const {
                    minAge,maxAge,wxPatientQuery,sourceFrom,sourceId,consultantId,medicalPlanStatus,medicalPlanType,dutyTherapistId,primaryTherapistId,
                } = this.filterCrmInfo.params;
                const params = {
                    ...this.filterCrmInfo.params,
                    consultantId: isNotNull(consultantId) ? [consultantId] : consultantId,
                    medicalPlanStatus: isNotNull(medicalPlanStatus) ? [medicalPlanStatus] : medicalPlanStatus,
                    medicalPlanType: isNotNull(medicalPlanType) ? [medicalPlanType] : medicalPlanType,
                    dutyTherapistId: isNotNull(dutyTherapistId) ? [dutyTherapistId] : dutyTherapistId,
                    primaryTherapistId: isNotNull(primaryTherapistId) ? [primaryTherapistId] : primaryTherapistId,
                    minAge: Number(minAge) ?? undefined,
                    maxAge: Number(maxAge) ?? undefined,
                    wxBind: wxPatientQuery === true ? 1 : (wxPatientQuery === false ? 0 : undefined),
                    sourceIdFroms: (sourceFrom || sourceId) ? [
                        {
                            sourceFrom,
                            sourceId,
                        },
                    ] : [],
                };
                delete params.wxPatientQuery;
                delete params.sourceFrom;
                delete params.sourceId;
                return params;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .common-padding-container {
        .table-container {
            .vip::after {
                display: inline-block;
                width: 12px;
                height: 12px;
                vertical-align: middle;
                content: '';
                background: url('~assets/images/vip.png') no-repeat;
                background-size: 12px 12px;
            }
        }
    }
</style>
