<template>
    <div class="stat-patient-rebuy-wrapper">
        <abc-space class="inline-filter">
            <abc-date-picker-bar
                v-model="params.currentDateRangeFilter"
                :options="dateOptions"
                :picker-options="pickerOptions"
                value-format="YYYY-MM-DD"
                :clearable="false"
                @change="changeDate"
            ></abc-date-picker-bar>
            <clinic-select
                v-if="isChainAdmin"
                v-model="params.clinicIdFilter"
                placeholder="门店"
                width="120"
                :show-all-clinic="false"
                @change="linePartCaller"
            >
            </clinic-select>
        </abc-space>
        <biz-statistic-card-board
            :data-list="chartConfig"
            min-col-width="760px"
            :gutter="[16, 16]"
            type="custom"
            class="statistic-card-board-wrapper"
        >
            <template #pie-chart>
                <abc-card padding-size="large" class="card">
                    <abc-text size="large" bold>
                        复诊率
                    </abc-text>
                    <abc-tooltip-info>第二次就诊医生则为该医生的复诊患者</abc-tooltip-info>
                    <div v-if="!revisitRateData?.length" style="height: 250px;">
                        <abc-content-empty></abc-content-empty>
                    </div>
                    <abc-pie-chart
                        v-else
                        v-abc-loading="loading"
                        :data="revisitRateData"
                        :legend="{
                            orient: 'vertical',
                            top: 18,
                            left: 200,
                            data: revisitRateLegend,
                            textStyle: {
                                fontSize: 14,
                            }
                        }"
                        :tooltip="{
                            formatter: '{b}',
                        }"
                        :center="['100', '125']"
                        height="250px"
                        width="100%"
                    ></abc-pie-chart>
                </abc-card>
            </template>
            <template #bar-chart="{ item }">
                <abc-card padding-size="large" class="card">
                    <abc-flex justify="space-between">
                        <abc-text size="large" bold>
                            {{ item.title }}
                            <abc-text theme="gray">
                                {{ item.subtitle }}
                            </abc-text>
                        </abc-text>
                        <abc-button
                            v-if="item.menu"
                            type="text"
                            @click="menuHandle(item.menu)"
                        >
                            查看全部
                        </abc-button>
                    </abc-flex>
                    <abc-bar-chart
                        :id="item.chartId"
                        v-abc-loading="doctorLoading"
                        class="bar-v-chart-wrapper"
                        :name-list="item.seriesName"
                        :category="item.yAxisData"
                        :data="item.xAxisData"
                        :bar-width="item.width"
                        :tooltip-formatter="item.tooltipFormatter"
                        height="250px"
                        width="100%"
                        :x-axis="{
                            minInterval: 1,
                        }"
                        :grid="{
                            containLabel: true,
                        }"
                    >
                    </abc-bar-chart>
                </abc-card>
            </template>
            <template #basic>
                <abc-card padding-size="large" class="card">
                    <abc-text size="large" bold>
                        会员/非会员对比
                    </abc-text>
                    <div
                        v-abc-loading="loading"
                        style="width: 100%; height: 250px;"
                    >
                        <abc-flex
                            v-for="(member, index) in repurchaseCompare"
                            :key="index"
                            align="center"
                            gap="large"
                            style="width: 100%; padding: 36px 42px 0;"
                        >
                            <abc-text size="mini" theme="gray" style="width: 36px;">
                                {{ member.type ? '会员' : '非会员' }}
                            </abc-text>
                            <abc-flex vertical style="flex: 1;">
                                <abc-text size="xxxlarge">
                                    {{ member.revisitRate }}
                                </abc-text>
                                <abc-text theme="gray">
                                    复诊率
                                </abc-text>
                            </abc-flex>
                            <abc-flex vertical style="flex: 1;">
                                <abc-text size="xxxlarge">
                                    {{ member.avgTimes }}
                                </abc-text>
                                <abc-text theme="gray">
                                    平均复诊次数
                                </abc-text>
                            </abc-flex>
                        </abc-flex>
                    </div>
                </abc-card>
            </template>
        </biz-statistic-card-board>
    </div>
</template>

<script>
    import clinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import DoctorRankDialog from '@/views/statistics/patientStat/patient-revisit-doctor-rank.js';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';

    import PatientRevisitApi from 'views/statistics/core/api/patient-revisit.js';
    import { AbcDatePickerBar } from '@abc/ui-pc'; const { DatePickerBarOptions } = AbcDatePickerBar;
    import {
        formatDate, getLastNDaysDate,
    } from '@abc/utils-date';
    import BizStatisticCardBoard from '@/components-composite/biz-statistic-card-board';

    export default {
        name: 'PatientRevisit',
        components: {
            ClinicSelect,
            BizStatisticCardBoard,
        },
        mixins: [clinicTypeJudger],
        data() {
            return {
                params: {
                    filterStart: '',
                    filterEnd: '',
                    currentDateRangeFilter: DatePickerBarOptions.LATEST_MONTH.label,
                    clinicIdFilter: '',
                    iClinicIdFilter: '',
                },
                dateOptions: [
                    DatePickerBarOptions.LATEST_MONTH,
                    DatePickerBarOptions.LATEST_THREE_MONTH,
                    DatePickerBarOptions.LATEST_HALF_YEAR,
                ],
                // config
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                center: ['220', '120'],
                // loading
                doctorLoading: false,
                loading: false,
                // barData
                chartData: {
                    count: {
                        subtitle: '',
                        xAxisData: [],
                        yAxisData: [],
                    },
                    doctor: {
                        xAxisData: [],
                        yAxisData: [],
                        repurchaseRate: [],
                        tooltipFormatter: null,
                    },
                },
                revisitRateData: [],
                revisitRateLegend: [],
                repurchaseCompare: [],
            };
        },
        computed: {
            chartConfig() {
                return [
                    { slotName: 'pie-chart' },
                    {
                        slotName: 'bar-chart',
                        title: '复诊次数',
                        seriesName: '患者数量',
                        chartId: 'count',
                        width: 14,
                        subtitle: this.chartData.count.subtitle,
                        xAxisData: this.chartData.count.xAxisData,
                        yAxisData: this.chartData.count.yAxisData,
                    },
                    { slotName: 'basic' },
                    {
                        slotName: 'bar-chart',
                        title: '医生复诊次数排行',
                        seriesName: '复诊次数',
                        chartId: 'doctor',
                        width: 10,
                        menu: {
                            id: 0,
                            title: '查看全部',
                        },
                        xAxisData: this.chartData.doctor.xAxisData,
                        yAxisData: this.chartData.doctor.yAxisData,
                        tooltipFormatter: this.chartData.doctor.tooltipFormatter,
                    },
                ];
            },
        },

        created() {
            const start = formatDate(getLastNDaysDate(30));
            const end = formatDate(new Date());
            this.changeDate([
                start,
                end,
            ]);
        },
        methods: {
            changeDate(picker) {
                if (picker.length !== 2) return false;
                this.params.filterStart = picker[0];
                this.params.filterEnd = picker[1];
                this.getRepurchaseInfo();
                this.getRepurchaseDoctor();
            },
            linePartCaller() {
                this.getRepurchaseInfo();
                this.getRepurchaseDoctor();
            },

            async getRepurchaseInfo() {
                try {
                    this.loading = true;
                    const {
                        filterStart: beginDate, filterEnd: endDate, clinicIdFilter: clinicId,
                    } = this.params;
                    const { data } = await PatientRevisitApi.repurchase({
                        clinicId,
                        beginDate,
                        endDate,
                    });
                    if (data) {
                        this.revisitRateData = this.generateRevisitRatePieData(data?.repurchaseRate);
                        this.revisitRateLegend = this.revisitRateData.map((item) => item.name);
                        this.repurchaseCompare = data?.repurchaseCompare?.map((item) => {
                            return {
                                ...item,
                                avgTimes: parseFloat(item.avgTimes).toFixed(2) || 0,
                            };
                        });
                        this.chartData.count.xAxisData = [];
                        this.chartData.count.yAxisData = [];
                        const temp = [];
                        const keys = {
                            repurchaseTime10: '复诊10次以上',
                            repurchaseTime510: '复诊5～10次',
                            repurchaseTime34: '复诊3～4次',
                            repurchaseTime2: '复诊2次',
                            repurchaseTime1: '复诊1次',
                        };
                        for (const x in keys) {
                            if (x !== 'avg') {
                                temp.push({
                                    key: keys[x],
                                    value: data?.repurchaseTimes[x] || 0,
                                });
                            }
                        }
                        temp.forEach((val) => {
                            this.chartData.count.xAxisData.push(val.value);
                            this.chartData.count.yAxisData.push(val.key);
                        });
                        this.chartData.count.subtitle = ` 平均次数：${(Math.round(data?.repurchaseTimes.avg * 100) / 100).toFixed(2) || 0}`;
                    }

                } catch (err) {
                    console.error(err);
                } finally {
                    this.loading = false;
                }
            },
            generateRevisitRatePieData(repurchaseRate = []) {
                const totalCount = repurchaseRate.reduce((total, item) => total + item.value, 0);
                if (totalCount) {
                    return repurchaseRate.map((item) => {
                        const rate = ((item.value / totalCount) * 100).toFixed(2);
                        return {
                            value: rate,
                            name: `${item.key}: ${item.value} (${rate}%)`,
                        };
                    });
                }
                return [];
            },

            async getRepurchaseDoctor() {
                const {
                    filterStart: beginDate, filterEnd: endDate, clinicIdFilter: clinicId,
                } = this.params;
                try {
                    this.doctorLoading = true;
                    const { data } = await PatientRevisitApi.repurchaseDoctor({
                        clinicId,
                        beginDate,
                        endDate,
                        offset: 0,
                        size: 10,
                    });
                    if (data) {
                        // 展示前五个医生的数据
                        const list = (data.data || []).slice(0, 5) ;
                        if (list.length > 0) {
                            this.chartData.doctor.xAxisData = [];
                            this.chartData.doctor.yAxisData = [];
                        } else {
                            this.chartData.doctor.xAxisData = [0];
                            this.chartData.doctor.yAxisData = ['暂无数据'];
                        }
                        this.chartData.doctor.repurchaseRate = [];
                        list.sort((a, b) => {
                            return (
                                parseFloat(a.repurchaseCount) -
                                parseFloat(b.repurchaseCount)
                            );
                        }).forEach((val) => {
                            this.chartData.doctor.xAxisData.push(
                                parseFloat(val.repurchaseCount) || 0,
                            );
                            this.chartData.doctor.yAxisData.push(
                                val.doctorName || '',
                            );
                            this.chartData.doctor.repurchaseRate.push(
                                val.repurchaseRate || 0,
                            );
                        });
                        this.chartData.doctor.tooltipFormatter = (params) => {
                            const rate =
                                this.chartData.doctor.repurchaseRate[
                                    params.dataIndex
                                ];
                            return `
                                <p>${params.name}</p>
                                <p>${params.marker}${
                                params.seriesName
                            }：${params.data}</p>
                                <p>${params.marker}复诊率：${rate}</p>
                                `;
                        };
                    }
                } catch (err) {
                    console.error(err);
                } finally {
                    this.doctorLoading = false;
                }
            },

            menuHandle(menu) {
                if (menu.id === 0) {
                    new DoctorRankDialog({
                        visible: true,
                        params: {
                            clinicIdFilter: this.params.clinicIdFilter,
                        },
                        dateParams: {
                            beginDate: this.params.filterStart,
                            endDate: this.params.filterEnd,
                            dateRange: [this.params.filterStart, this.params.filterEnd],
                        },
                    }).generateDialog({ parent: this });
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';

    .stat-patient-rebuy-wrapper {
        padding: 20px 20px 0;

        .statistic-card-board-wrapper {
            margin-top: 16px;
        }
    }
</style>
