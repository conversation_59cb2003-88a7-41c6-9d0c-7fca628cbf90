<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="选择提成人员"
        class="select-commission-employee-dialog"
        content-styles="padding: 0"
        append-to-body
    >
        <abc-transfer-v2
            lite
            :result-list="selectedEmployees"
            @del="deleteItem"
            @confirm="confirm"
            @cancel="showDialog = false"
        >
            <div class="clinic-col">
                <label class="full-check">
                    <abc-checkbox
                        v-model="allChainChecked"
                        type="number"
                        :checked="allChainChecked"
                    ></abc-checkbox>
                    <abc-icon icon="s-module-color" size="15"></abc-icon>
                    <abc-text class="clinic-name">
                        {{ chainName }}
                    </abc-text>
                </label>

                <div class="clinic-list-wrapper">
                    <ul v-for="clinic in clinics" :key="clinic.clinicId" class="clinic-wrapper">
                        <template v-if="isSingleClinic">
                            <label v-for="employee in clinic.employees" :key="employee.employeeId">
                                <li style="padding-left: 24px;">
                                    <abc-checkbox
                                        v-model="employee.checked"
                                        type="number"
                                        :value="employee.checked"
                                        @change="checkEmployee(employee)"
                                    ></abc-checkbox>
                                    <abc-icon icon="s-commodity-color" size="15" style="margin-left: 24px;"></abc-icon>
                                    {{ employee.employeeName }}
                                </li>
                            </label>
                        </template>

                        <template v-else>
                            <li style="padding-left: 0;">
                                <abc-checkbox
                                    v-model="clinic.checked"
                                    type="number"
                                    style="margin-left: auto;"
                                    :value="clinic.checked"
                                    :disabled="clinic.disabled"
                                    @change="checkClinic(clinic)"
                                ></abc-checkbox>
                                <div class="check-box" @click="clinic.expand = !clinic.expand">
                                    <i v-if="clinic.expand" class="iconfont cis-icon-dropdown_triangle"></i>
                                    <i v-else class="iconfont cis-icon-dropdown_secondary_triangle"></i>
                                </div>

                                <label class="full-checked">
                                    <abc-icon icon="s-folder-color" size="15"></abc-icon>
                                    <abc-text class="clinic-name">{{ isChainAdminClinic(clinic) ? '总部' : clinic.clinicName }}</abc-text>
                                </label>
                            </li>
                            <template v-if="clinic.expand">
                                <label v-for="employee in clinic.employees" :key="employee.employeeId">
                                    <li>
                                        <abc-checkbox
                                            v-model="employee.checked"
                                            type="number"
                                            :value="employee.checked"
                                            :disabled="employee.disabled"
                                            @change="checkEmployee(employee)"
                                        ></abc-checkbox>
                                        <abc-icon icon="s-commodity-color" size="15" style="margin-left: 24px;"></abc-icon>
                                        {{ employee.employeeName }}
                                    </li>
                                </label>
                            </template>
                        </template>
                    </ul>
                </div>
            </div>

            <template #lite-result-node-label="{ node }">
                {{ node.employees ? `${node.clinicName}(${node.employees.length}人)` : node.employeeName }}
            </template>
        </abc-transfer-v2>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import ClinicAPI from 'src/api/clinic';
    import {
        isChainAdminClinic,
        isSingleClinic,
        isAdminClinic,
    } from 'src/views/common/clinic.js';
    import { mapGetters } from 'vuex';
    import AbcAccess from '@/access/utils.js';

    export default {
        name: 'SelectEmployeeDialog',

        props: {
            value: Boolean,
            employees: Array,
            autoFold: {
                type: Boolean,
                default: true,
            },
            hideChainAdmin: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                accessKey: AbcAccess.accessMap.STAT_COMMISSION_REPORT,
                checkedList: [],
                clinics: [],
                isSingleClinic: 0,
            };
        },

        computed: {
            ...mapGetters(['currentClinic']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },

            chainName() {
                const chain = this.clinics.find((item) => {
                    return isAdminClinic(item);
                });
                return chain ? chain.name : '';
            },

            allChainChecked: {
                get() {
                    let checked = true;
                    this.clinics.every((clinic) => {
                        if (!clinic.checked) {
                            checked = false;
                        } else {
                            clinic.employees.every((item) => {
                                if (!item.checked) {
                                    checked = false;
                                    return false;
                                }
                            });
                        }
                        return checked;
                    });
                    return checked;
                },
                set(val) {
                    this.clinics.forEach((clinic) => {
                        if (!clinic.disabled) {
                            clinic.checked = val;
                            this.checkClinic(clinic);
                        }
                    });
                },
            },

            selectedEmployees() {
                let result = [];
                const mapObj = new Map();

                this.checkedList.forEach((employee) => {
                    employee.chainId = this.currentClinic?.chainId;
                    const obj = mapObj.get(employee.clinicId);
                    if (obj) {
                        obj.employees.push(employee);
                    } else {
                        mapObj.set(employee.clinicId, {
                            clinicId: employee.clinicId,
                            nodeType: employee.nodeType,
                            viewMode: employee.viewMode,
                            clinicName: employee.clinicShortName || employee.clinicName || '',
                            employees: [ employee ],
                        });
                    }
                });

                // 单店,只用一层结构
                if (this.isSingleClinic || !this.autoFold) {
                    mapObj.forEach((item) => {
                        result = result.concat(item.employees.map((it) => {
                            it.selectType = 'employee';
                            return it;
                        }));
                    });
                    return result;
                }

                mapObj.forEach((item, key) => {
                    const len = this.findEmployeeLength(key);
                    if (item.employees && item.employees.length >= len) {
                        item.selectType = 'clinic';
                        result.push(item);
                    } else {
                        result = result.concat(item.employees.map((it) => {
                            it.selectType = 'employee';
                            return it;
                        }));
                    }
                });

                return result;
            },
        },

        async created() {
            this.checkedList = [ ...this.employees ];
            const { data } = await ClinicAPI.fetchChainEmployeesNew();
            const dataList = data.rows || [];

            if (dataList.length === 1 && isSingleClinic(dataList[0])) {
                // 单店的情况下,只有一层结构
                this.isSingleClinic = 1;
            }

            this.clinics = dataList.map((clinic) => {
                clinic.expand = false;
                clinic.clinicId = clinic.id;
                clinic.clinicName = clinic.shortName || clinic.name;
                clinic.disabled = !AbcAccess.checkAvailableByEdition(this.accessKey, clinic.edition);
                clinic.employees = (clinic.employees || []).map((employee) => {
                    employee.clinicId = clinic.clinicId;
                    employee.clinicName = clinic.clinicName;
                    employee.nodeType = clinic.nodeType;
                    employee.viewMode = clinic.viewMode;
                    employee.checked = !!this.isInSelected(employee);
                    employee.disabled = clinic.disabled;
                    return employee;
                });
                // 如果传入的employeeType 是 医生，则需要过滤出isDoctor=true的员工
                // if (this.employeeType === SelectEmployeeConst.TYPE_DOCTOR) {
                //     clinic.employees = clinic.employees.filter( employee => {
                //         return employee.isDoctor;
                //     } );
                // }

                const len = clinic.employees.filter((employee) => {
                    return employee.checked;
                }).length;

                if (len > 0) {
                    clinic.expand = len !== clinic.employees.length;
                }

                clinic.checked = len === clinic.employees.length;
                return clinic;
            });
            if (this.hideChainAdmin) {
                // 隐藏总部
                this.clinics = this.clinics.filter((clinic) => {
                    return !isChainAdminClinic(clinic);
                });
            }
        },

        methods: {
            isChainAdminClinic,
            findEmployeeLength(clinicId) {
                let len = 0;
                this.clinics.forEach((clinic) => {
                    if (clinic.clinicId === clinicId) {
                        len = clinic.employees.length;
                    }
                });
                return len;
            },

            isInSelected(employee) {
                return this.employees.find((item) => {
                    if (this.isSingleClinic) {
                        return item.employeeId === employee.employeeId;
                    }
                    return item.clinicId === employee.clinicId && item.employeeId === employee.employeeId;
                });
            },

            /**
             * @desc 选择整个诊所
             * <AUTHOR>
             * @date 2019/05/19 14:35:28
             */
            checkClinic(clinic) {
                if (this.isSingleClinic) {
                    this.checkedList = [];
                } else {
                    this.checkedList = this.checkedList.filter((item) => {
                        return item.clinicId !== clinic.clinicId;
                    });
                }


                if (clinic.checked) {
                    this.checkedList = this.checkedList.concat(clinic.employees);
                    $('.employee-list').scrollTop($('.employee-list li').height() * this.checkedList.length + 40);
                }

                this.clinics.forEach((item) => {
                    if (item.clinicId === clinic.clinicId) {
                        clinic.employees.forEach((employee) => {
                            employee.checked = clinic.checked;
                        });
                    }
                });
            },

            /**
             * @desc 选择某一个用户
             * <AUTHOR>
             * @date 2019/05/19 14:35:19
             */
            checkEmployee(employee) {
                if (employee.checked) {
                    this.checkedList.push(employee);
                    $('.employee-list').scrollTop($('.employee-list').height());
                } else {
                    const index = this.checkedList.findIndex((item) => {
                        if (this.isSingleClinic) {
                            return item.employeeId === employee.employeeId;
                        }
                        return item.clinicId === employee.clinicId && item.employeeId === employee.employeeId;
                    });
                    index !== -1 && this.checkedList.splice(index, 1);
                }

                // 当一个门店下所有医生被选择,则门店checkbox全选
                this.clinics.forEach((clinic) => {
                    clinic.checked = clinic.employees.length === clinic.employees.filter((item) => { return item.checked; }).length;
                });
            },

            /**
             * @desc 删除已选择的 店 | 人
             * <AUTHOR>
             * @date 2019/05/19 14:39:50
             */
            deleteItem(item) {
                const { clinicId } = item;
                const { employeeId } = item;

                if (item.selectType === 'clinic') {
                    this.checkedList = this.checkedList.filter((item) => {
                        return item.clinicId !== clinicId;
                    });

                    this.clinics.forEach((clinic) => {
                        if (clinicId === clinic.clinicId) {
                            clinic.checked = false;
                            clinic.employees.forEach((employee) => {
                                employee.checked = false;
                            });
                        }
                    });
                } else {
                    const index = this.checkedList.findIndex((item) => {
                        return item.clinicId === clinicId && item.employeeId === employeeId;
                    });
                    this.checkedList.splice(index, 1);
                    this.clinics.forEach((clinic) => {
                        clinic.employees.forEach((employee) => {
                            if (this.isSingleClinic) {
                                if (employeeId === employee.employeeId) {
                                    employee.checked = false;
                                    clinic.checked = false;
                                }
                            } else {
                                if (clinicId === employee.clinicId && employeeId === employee.employeeId) {
                                    employee.checked = false;
                                    clinic.checked = false;
                                }
                            }
                        });
                    });
                }
            },

            /**
             * @desc 确认选择的提成人员
             * <AUTHOR>
             * @date 2019/05/20 11:30:11
             */
            confirm() {
                this.$emit('change', this.checkedList);
                this.showDialog = false;
            },
        },

    };
</script>

<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';
    @import 'src/styles/abc-common.scss';

    .select-commission-employee-dialog {
        .clinic-col {
            height: 100%;
            padding: 24px 0 0 16px;
            background: var(--abc-color-cp-grey2);

            .full-check {
                display: flex;
                gap: 8px;
                align-items: center;
                height: 40px;
                padding-right: 8px;
                font-weight: bold;
                cursor: pointer;

                .clinic-name {
                    @include ellipsis;

                    flex: 1;
                }
            }

            .clinic-list-wrapper {
                height: calc(100% - 40px);
                overflow-y: auto;
                overflow-y: overlay;

                .check-box {
                    width: 16px;
                    height: 40px;
                    line-height: 40px;
                }

                .full-checked {
                    display: flex;
                    flex: 1;
                    gap: 8px;
                    align-items: center;
                    width: 0;
                    height: 40px;

                    .clinic-name {
                        @include ellipsis;

                        flex: 1;
                    }
                }
            }

            .iconfont {
                font-size: 16px;
                color: $T3;
            }

            ul li {
                position: relative;
                display: flex;
                gap: 8px;
                align-items: center;
                height: 40px;
                padding-right: 8px;
                padding-left: 16px;
                cursor: pointer;

                &.is-selected,
                &:hover {
                    background-color: $P4;
                }
            }
        }
    }
</style>
