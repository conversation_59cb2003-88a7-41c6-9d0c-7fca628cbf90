<template>
    <abc-popover
        ref="popover"
        placement="bottom-end"
        trigger="click"
        theme="white"
        style="margin-left: auto;"
        :popper-style="{
            padding: '0px'
        }"

        @show="handleShow"
    >
        <div slot="reference">
            <abc-button
                variant="text"
                shape="square"
                theme="default"
                size="small"
                :icon="type === 'daterange' ? 'n-calendar-line' : 'n-search-line'"
                :icon-color="iconColor"
            >
            </abc-button>
        </div>

        <div>
            <div ref="focusInput" style="padding: 16px;">
                <template v-if="type === 'daterange'">
                    <abc-date-picker
                        v-model="dateRange"
                        type="daterange"
                        clearable
                        :describe-list="describeList"
                        :picker-options="pickerOptions"
                        :width="240"
                    ></abc-date-picker>
                </template>

                <template v-else-if="type === 'input'">
                    <abc-input
                        v-model="inputValue"
                        placeholder="请输入"
                        clearable
                        :width="240"
                    ></abc-input>
                </template>
            </div>

            <abc-flex justify="flex-end" style="padding: 8px 16px; border-top: 1px solid var(--abc-color-P8);">
                <abc-button
                    variant="fill"
                    @click="handleConfirm"
                >
                    {{ confirmText }}
                </abc-button>
                <abc-button
                    variant="ghost"
                    @click="handleCancel"
                >
                    取消
                </abc-button>
            </abc-flex>
        </div>
    </abc-popover>
</template>

<script>
    import useDateRangePicker from '@/hooks/abc-ui/use-date-range-picker';

    export default {
        name: 'FilterPopover',
        props: {
            type: {
                type: String,
                default: 'daterange',
                validator: (value) => ['daterange', 'input'].includes(value),
            },
            confirm: {
                type: Function,
                default: () => {},
            },
        },
        setup() {
            // 使用日期范围选择器 hooks
            const {
                describeList,
                pickerOptions,
            } = useDateRangePicker({
                customDisabledDate: () => false,
            });

            return {
                describeList,
                pickerOptions,
            };
        },
        data() {
            return {
                dateRange: [],
                inputValue: '',
            };
        },
        computed: {
            iconColor() {
                if (this.type === 'daterange') {
                    return this.dateRange.length === 2 ? 'var(--abc-color-theme1)' : 'var(--abc-color-T2)';
                }
                return this.inputValue ? 'var(--abc-color-theme1)' : 'var(--abc-color-T2)';
            },
            confirmText() {
                return this.type === 'daterange' ? '筛选' : '搜索';
            },
        },
        methods: {
            handleConfirm() {
                const val = this.type === 'daterange' ? this.dateRange : this.inputValue;
                this.confirm(val);
                this.handleCancel();
            },
            handleCancel() {
                this.$refs.popover.doClose();
            },
            handleShow() {
                console.log(this.$refs.focusInput);
                // this.$refs.focusInput.querySelector('input').focus();
            },
        },
    };
</script>
