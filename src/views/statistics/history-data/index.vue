<template>
    <abc-flex
        v-abc-loading.coverOpaque="pageLoading"
        class="history-data-wrapper"
        vertical
        style="position: relative; height: 100%;"
    >
        <template v-if="renderOptions.length">
            <abc-tabs-v2
                :value="currentRecordId"
                :option="renderOptions"
                class="statistics-card-tabs"
                size="large"
                type="border-card"
                :item-max-length="7"
                :adaptation="true"
                @change="onChangeTab"
            ></abc-tabs-v2>

            <abc-layout preset="page-table" style="background: white;">
                <abc-layout-header>
                    <abc-flex
                        align="center"
                        style="height: 40px;"
                        @mouseenter="showEditBtn = true"
                        @mouseleave="showEditBtn = false"
                    >
                        <template v-if="isEditingRemark">
                            <abc-text
                                theme="black"
                                size="large"
                                bold
                            >
                                数据来源：
                            </abc-text>
                            <abc-input
                                v-model="sourceRemarkTemp"
                                v-abc-auto-focus
                                placeholder="请输入"
                                :width="302"
                            ></abc-input>
                            <abc-flex inline align="center" style="padding: 0 8px;">
                                <abc-button
                                    variant="text"
                                    size="small"
                                    :loading="btnLoading"
                                    @click="saveSourceRemark"
                                >
                                    保存
                                </abc-button>
                                <abc-button
                                    variant="text"
                                    size="small"
                                    style="margin-left: 0;"
                                    :disabled="btnLoading"
                                    @click="cancelEditRemark"
                                >
                                    取消
                                </abc-button>
                            </abc-flex>
                        </template>
                        <template v-else>
                            <abc-text
                                theme="black"
                                size="large"
                                bold
                            >
                                <span @click="startEditRemark">
                                    数据来源：{{ currentRecordItem.sourceRemark || '-' }}
                                </span>
                            </abc-text>
                            <abc-button
                                v-if="showEditBtn"
                                variant="text"
                                size="small"
                                style="margin-left: 8px;"
                                @click="startEditRemark"
                            >
                                修改
                            </abc-button>
                        </template>
                    </abc-flex>
                </abc-layout-header>
                <abc-layout-content @layout-mounted="handleMounted">
                    <abc-table
                        ref="abcTable"
                        type="pro"
                        :data-list="tableData"
                        :loading="tableLoading"
                        :need-selected="false"
                        :render-config="renderConfig"
                        empty-content="暂无数据"
                    ></abc-table>
                </abc-layout-content>
                <abc-layout-footer>
                    <abc-pagination
                        :key="pageSizeList.join('-')"
                        :count="pageParams.total"
                        :page-sizes="pageSizeList"
                        :page-sizes-width="120"
                        :pagination-params="pageParams"
                        :show-total-page="true"
                        show-size
                        @size-change="pageSizeChange"
                        @current-change="pageTo"
                    >
                    </abc-pagination>
                </abc-layout-footer>
            </abc-layout>
        </template>
        <abc-content-empty v-else></abc-content-empty>
    </abc-flex>
</template>

<script type="text/ecmascript-6">
    import { debounce } from 'utils/lodash';
    import useClinicStorage, {
        CustomKeys, TableKeys,
    } from '@/hooks/business/use-clinic-storage';
    import usePagination from '@/hooks/abc-ui/use-table-pagination';

    import StatAPI from 'api/stat';
    import { mapGetters } from 'vuex';
    import {
        createGUID, isNotNull,
    } from '@/utils';
    import FilterPopover from 'views/statistics/history-data/components/filter-popover.vue';

    export default {
        name: 'HistoryData',
        setup() {
            const {
                getStorage,
                setStorage,
            } = useClinicStorage();

            const {
                pageParams,
                setPageSize,
                setPageTotal,
                changePageIndex,
            } = usePagination({
                pageSize: getStorage(CustomKeys.CLINIC_INVENTORY_PAGE_SIZE, TableKeys.statisticsHistoryDataTable) || 10,
                pageIndex: 0,
                total: 0,
            });

            return {
                getStorage,
                setStorage,
                changePageIndex,
                setPageSize,
                setPageTotal,
                pageParams,
            };
        },
        data() {
            return {
                pageLoading: true,
                tableLoading: false,
                btnLoading: false,
                currentRecordId: '',
                currentRecordItem: {},
                sourceRemarkTemp: '',
                isEditingRemark: false,
                showEditBtn: false,
                renderOptions: [{
                    label: '数据测试',
                    value: 1,
                }],
                pageSizeList: [10, 50, 100],
                tableHeader: [
                    // {
                    //     prop: 'date',
                    //     label: '日期',
                    //     align: 'center',
                    //     search_type: 'range',
                    // },
                    // {
                    //     prop: 'goodsName',
                    //     label: '商品名称',
                    //     align: 'center',
                    //     search_type: 'term',
                    // },
                ],
                tableData: [
                    // {
                    //     date: '123',
                    //     goodsName: '123',
                    // },
                ],
                rangeItemList: [],// [{"name": "", "lt": "", "gt": ""}]
                termItemList: [],// [{"name": "",  "value": ""}]
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },
            renderConfig() {
                return {
                    hasInnerBorder: true,
                    list: this.tableHeader.map((header) => {
                        const item = {
                            key: header.prop,
                            label: header.label,
                            colType: header.search_type || '',
                            style: {
                                flex: 1,
                                width: '150px',
                                minWidth: '150px',
                                textAlign: header.align || 'left',
                            },
                        };
                        if (item.colType === 'range') {
                            item.headerAppendRender = () => {
                                return <FilterPopover type="daterange" confirm={(v) => this.handleDateConfirm(item.key, v)}></FilterPopover>;
                            };
                        }
                        if (item.colType === 'term') {
                            item.headerAppendRender = () => {
                                return <FilterPopover type="input" confirm={(v) => this.handleInputConfirm(item.key, v)}></FilterPopover>;
                            };
                        }
                        return item;
                    }),
                };
            },
        },
        async created() {
            this.pageLoading = true;
            try {
                await this.fetchTabs();
                await this.fetchData();
            } catch (e) {
                console.error(e);
            } finally {
                this.pageLoading = false;
            }

            this._fetchData = debounce(this.fetchData, 500, true);
        },
        methods: {
            async fetchTabs() {
                try {
                    const result = await StatAPI.getExternalReportList();
                    if (result && result.data && Array.isArray(result.data)) {
                        this.renderOptions = result.data.map((item) => ({
                            ...item,
                            label: item.tableHiddenName || '-',
                            value: item.mappingId,
                        }));
                    }
                    if (this.renderOptions.length > 0) {
                        this.currentRecordItem = this.renderOptions[0];
                        this.currentRecordId = this.currentRecordItem.value;
                    }
                } catch (error) {
                    console.error('获取报表列表失败:', error);
                }
            },
            async fetchData() {
                if (!this.currentRecordId) return;

                this.tableLoading = true;

                try {
                    // 首先获取报表信息，包含表头结构
                    const reportInfoResult = await StatAPI.getExternalReportInfo({
                        client: this.currentRecordItem.client || '',
                        clinicId: this.clinicId || '',
                        mappingId: this.currentRecordId,
                        rangeItemList: this.rangeItemList,
                        termItemList: this.termItemList,
                        limit: this.pageParams.limit,
                        offset: this.pageParams.offset,
                    });

                    if (reportInfoResult && reportInfoResult.data) {
                        // 处理表头数据
                        if (reportInfoResult.data.headers && Array.isArray(reportInfoResult.data.headers)) {
                            this.tableHeader = reportInfoResult.data.headers;
                        }

                        // 处理表格数据
                        if (reportInfoResult.data.data && Array.isArray(reportInfoResult.data.data)) {
                            this.tableData = reportInfoResult.data.data.map((e) => {
                                e.keyId = createGUID();
                                return e;
                            });
                            this.setPageTotal(reportInfoResult.data.count || reportInfoResult.data.data.length);
                        }
                    } else {
                        this.tableHeader = [];
                        this.tableData = [];
                    }
                } catch (error) {
                    console.error('获取报表数据失败:', error);
                } finally {
                    this.tableLoading = false;
                }
            },
            onChangeTab(value, item) {
                console.log('onChangeTab',value,item);
                this.currentRecordId = value;
                this.currentRecordItem = item;
                this.changePageIndex(0);
                // 切换标签时清空筛选参数
                this.rangeItemList = [];
                this.termItemList = [];
                this._fetchData();
            },

            handleMounted(data) {
                console.log('handleMounted', data);
                const limit = data.paginationLimit - 1;
                if (!this.pageSizeList.includes(limit)) {
                    this.pageSizeList.push({
                        label: '推荐',
                        value: limit,
                    });
                    this.pageSizeList.sort((a, b) => {
                        return (a.value || a) - (b.value || b);
                    });
                }

                // 优先用上次的
                let preferPageSize = this.getStorage(CustomKeys.CLINIC_INVENTORY_PAGE_SIZE, TableKeys.statisticsHistoryDataTable);
                if (!this.pageSizeList.includes(preferPageSize)) {
                    // 没有上次的或者上次使用的分页条数不存在，优先用本次的
                    preferPageSize = limit;
                }

                this.setPageSize(preferPageSize);
            },
            // 每页显示数量
            pageSizeChange(pageSize) {
                this.changePageIndex(0);
                this.setPageSize(pageSize);
                this.setStorage(CustomKeys.CLINIC_INVENTORY_PAGE_SIZE, TableKeys.statisticsHistoryDataTable, pageSize);
                this._fetchData();
            },
            pageTo(page) {
                console.log('pageTo', page);
                this.changePageIndex(page - 1);
                this._fetchData();
            },
            // 日期范围确认
            handleDateConfirm(key, dateRange) {
                if (dateRange && dateRange.length === 2) {
                    const rangeItem = this.rangeItemList.find((item) => item.name === key);
                    if (rangeItem) {
                        rangeItem.gt = dateRange[0];
                        rangeItem.lt = dateRange[1];
                    } else {
                        this.rangeItemList.push({
                            name: key,
                            gt: dateRange[0],
                            lt: dateRange[1],
                        });
                    }
                } else {
                    this.rangeItemList = this.rangeItemList.filter((item) => item.name !== key);
                }
                this.changePageIndex(0);
                this._fetchData();
            },

            // 输入框确认
            handleInputConfirm(key, inputValue) {
                if (isNotNull(inputValue)) {
                    const termItem = this.termItemList.find((item) => item.name === key);

                    if (termItem) {
                        termItem.value = inputValue || '';
                    } else {
                        this.termItemList.push({
                            name: key,
                            value: inputValue,
                        });
                    }
                } else {
                    this.termItemList = this.termItemList.filter((item) => item.name !== key);
                }
                this.changePageIndex(0);
                this._fetchData();
            },

            /**
             * 更新报表数据源
             */
            async updateReportSource(sourceRemark) {
                try {
                    this.btnLoading = true;
                    await StatAPI.updateExternalReportSource({
                        mappingId: this.currentRecordId,
                        sourceRemark,
                    });
                    this.isEditingRemark = false;
                    this.currentRecordItem.sourceRemark = sourceRemark;
                    // 更新数据源后重新加载数据
                    await this.fetchData();
                    this.$Toast.success('更新成功');
                } catch (error) {
                    console.error('更新数据源失败:', error);
                    this.$Toast.error('更新失败');
                } finally {
                    this.btnLoading = false;
                }
            },

            // 开始编辑数据来源描述
            startEditRemark() {
                this.sourceRemarkTemp = this.currentRecordItem.sourceRemark || '';
                this.isEditingRemark = true;
            },

            // 保存数据来源描述
            async saveSourceRemark() {
                if (this.sourceRemarkTemp !== this.currentRecordItem.sourceRemark) {
                    try {
                        await this.updateReportSource(this.sourceRemarkTemp);
                    } catch (error) {
                        console.error('保存数据来源失败:', error);
                    }
                }
            },

            // 取消编辑数据来源描述
            cancelEditRemark() {
                this.sourceRemarkTemp = '';
                this.isEditingRemark = false;
            },
        },
    };
</script>

<style lang="scss">
.history-data-wrapper {
    .statistics-card-tabs {
        flex: 1;
        border-top: 0;
        border-top-left-radius: 0;
        border-top-right-radius: 0;

        .abc-tabs-v2-item:first-child {
            border-top-left-radius: 0;
        }
    }
}
</style>
