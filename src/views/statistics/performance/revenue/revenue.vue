<template>
    <abc-layout class="common-padding-container statistics-module__revenue-wrapper" preset="page-table">
        <abc-layout-header>
            <stat-toolbar
                ref="statToolbarRef"
                :enable-features="toolbarFeatures"
                :clinic-id-filter.sync="params.clinicIdFilter"
                :date-filter.sync="params.dateFilter$"
                :is-year-limit-date="isYearLimitDate"
                :fee-type-filter.sync="params.feeTypeFilter$"
                :fee-type-options="feeTypeFilterOptions"
                :handle-export="handleExport"
                :clinic-width="120"
                :fee-type-width="120"
                multiple-visit-source
                :fee-type-placeholder="params.dimension === 'item' && supportAddNursing ? '医嘱分类' : '项目分类'"
                :fee-type-selector-sticky="feeTypeSticky$"
                :export-task-type="exportTaskType"
                :setting-options="settingOptions"
                @change-date="handleDateChange"
                @change-clinic="handleClinicChange"
                @change-fee-type="handleFeeTypeChange"
                @setting-item-click="handleOpenStatDimensionSettingDialog"
            >
                <abc-select
                    v-if="showRevenuePersonType"
                    slot="employeeId"
                    v-model="params.personType"
                    width="120"
                    place-holder="类型"
                    @change="handlePersonTypeChange"
                >
                    <abc-option
                        v-for="item in personTypeOptions"
                        :key="item.value"
                        :label="item.key"
                        :value="item.value"
                    ></abc-option>
                </abc-select>
                <abc-cascader
                    v-if="params.dimension === 'detail' && showRevenueVisitSourceSelect"
                    ref="visitSourceRef"
                    v-model="params.visitSourceIdList"
                    :props="{
                        children: 'children',
                        label: 'name',
                        value: 'newId',
                    }"
                    :placeholder="recommendVisitPlaceHolder"
                    multiple
                    mutually-exclusive
                    separation="-"
                    :width="120"
                    :options="visitSourceFilterOptions"
                    @change="handleVisitSourceChange"
                >
                </abc-cascader>
                <filter-select
                    v-if="params.dimension !== 'department'"
                    key="employeeId"
                    slot="employeeId"
                    v-model="params.doctorIdFilter"
                    :width="120"
                    :placeholder="doctorPlaceHolder"
                    :options="employeeList"
                    id-with-name
                    @change="handleDepartmentChange"
                >
                </filter-select>
                <filter-select
                    v-if="(!showEmployeeTable) && queryClinicId && !isNurse && showRevenueDepartmentSelect"
                    key="departmentId"
                    slot="employeeId"
                    v-model="params.departmentId"
                    :width="120"
                    placeholder="开单科室"
                    :options="departmentOptions"
                    @change="handleDepartmentChange"
                >
                </filter-select>
                <abc-select
                    v-if="supportAddNursing && showProjectTable && showRevenueFeeTypeSelect"
                    slot="patient"
                    :key="feeTypeKey"
                    v-model="feeTypeIdList"
                    placeholder="费用类型"
                    :width="120"
                    clearable
                    multiple
                    multi-label-mode="text"
                    :max-tag="1"
                    @change="handleAdviceFeeTypeChange"
                >
                    <abc-option
                        v-for="(item, index) in adviceFeeTypeOptions"
                        :key="index"
                        :value="item.id"
                        :label="item.name"
                    ></abc-option>
                </abc-select>
                <profit-rate-filter
                    v-if="showProjectTable"
                    :show-profit-rate-range="false"
                    :options="{
                        beginDate: params.dateFilter$.begin,
                        endDate: params.dateFilter$.end
                    }"
                    @change-profit-rate="handelProfitRateChange"
                ></profit-rate-filter>
                <stat-dimension-picker
                    slot="right"
                    v-model="params.dimension"
                    :options="dimensionOptions"
                    @change="handleDimensionChange"
                >
                </stat-dimension-picker>
                <abc-popover
                    v-if="showRevenueStatPopover"
                    placement="bottom-start"
                    trigger="click"
                    theme="white"
                    width="370px"
                    :visible-arrow="false"
                    style="display: inline-block;"
                    custom-class="revenue-stat__rule-popover"
                    @hide="revenueRulePopoverVisible = false"
                >
                    <ul
                        slot="reference"
                        class="revenue-stat__input-filter-selector"
                        :class="{ 'is-show': revenueRulePopoverVisible }"
                        @click="revenueRulePopoverVisible = !revenueRulePopoverVisible"
                    >
                        <li>
                            <abc-icon icon="positive_1" :color="isIncludingRegistration ? '#1EC761' : '#aab4bf'"></abc-icon>
                            <span>{{ $t('registrationFeeName') }}</span>
                        </li>
                        <li>
                            <abc-icon icon="positive_1" :color="isContainOthersWriterAchievement ? '#1EC761' : '#aab4bf'"></abc-icon>
                            <span>{{ `开单业绩 (${isContainOthersWriterAchievement ? '' : '不'}含代录)` }}</span>
                        </li>
                        <li>
                            <abc-icon icon="positive_1" :color="isIncludingWriter ? '#1EC761' : '#aab4bf'"></abc-icon>
                            <span>代录业绩</span>
                        </li>
                    </ul>
                    <abc-form
                        item-block
                        :label-width="70"
                        label-position="left"
                    >
                        <div class="revenue-stat__rule-list-wrapper">
                            <abc-form-item
                                label="业绩范围"
                                :custom-label-style="{
                                    color: '#7a8794',
                                }"
                                style="margin-bottom: 12px;"
                            >
                                <abc-checkbox v-model="isIncludingRegistration" @change="handleIncludingRegistrationChange">
                                    包含{{ $t('registrationFeeName') }}
                                </abc-checkbox>
                            </abc-form-item>
                            <abc-form-item
                                label="开单业绩"
                                style="display: flex; align-items: flex-start; margin-bottom: 12px;"
                            >
                                <abc-radio-group v-model="isContainOthersWriterAchievement" style="margin-top: -2px;" @change="handleRevenueChange">
                                    <abc-radio :label="1">
                                        <span class="rule-item-text">包含他人代录产生的业绩</span>
                                    </abc-radio>
                                    <p style=" margin: 6px 0 12px 0; font-size: 12px; color: #7a8794;">
                                        统计A的开单业绩时，B为A产生的代录业绩会计入
                                    </p>
                                    <abc-radio :label="0">
                                        <span class="rule-item-text">不含他人代录产生的业绩</span>
                                    </abc-radio>
                                    <p style="margin: 6px 0 0 0; font-size: 12px; color: #7a8794;">
                                        统计A的开单业绩时，B为A产生的代录业绩不会计入
                                    </p>
                                </abc-radio-group>
                            </abc-form-item>
                            <abc-form-item label="代录业绩" style="margin-bottom: 12px;">
                                <abc-checkbox v-model="isIncludingWriter" @change="handleIncludingWriterChange">
                                    展示代录人的业绩
                                </abc-checkbox>
                            </abc-form-item>

                            <div v-if="!isContainOthersWriterAchievement && !isIncludingWriter" class="revenue-stat__rule-err-tips">
                                <abc-icon icon="Attention" color="#fd9800" size="12"></abc-icon>
                                未统计代录产生的业绩，合计可能少于门店实际开单金额
                            </div>
                        </div>
                    </abc-form>
                </abc-popover>
            </stat-toolbar>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :key="params.dimension"
                :loading="loading"
                :data-list="tableData"
                :summary="displaySummaryData"
                :summary-render-keys="summaryRenderKeys"
                :render-config="tableRenderHeader"
                @change-sort="handleSortChange"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="currentPageParams"
                :count="totalCount"
                :class="{ 'show-total': showEmployeeTable || showDepartmentTable }"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li v-if="showEmployeeTable || showDepartmentTable">
                        共 <span>{{ totalCount }}</span> 条数据
                    </li>
                    <li v-else v-html="totalInfo"></li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import {
        formatMoney, formatNumber,
    } from '@/utils';
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import FeeTypeMixins from 'views/statistics/mixins/fee-type-mixin';
    import StatisticsDataPermission from 'views/statistics/mixins/statistics-data-permission';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import StatDimensionPicker from 'views/statistics/common/stat-dimension-picker/stat-dimension-picker';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import PayModeMixins from 'views/statistics/mixins/pay-modes-mixin';
    import { AmountDialog } from 'views/statistics/common/amount-dialog/index.js';
    import { RecommendService } from '@/service/recommend';
    import { Popover as AbcPopover } from '@abc/ui-pc';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import RevenueAPI from '@/views/statistics/core/api/revenue.js';
    import TransactionService from 'views/statistics/core/services/operation/transaction-service';
    import ChargeAPI from 'views/statistics/core/api/charge.js';
    import { createGUID } from '@/utils';
    import { resolveHeaderV2 } from 'src/views/statistics/utils.js';
    import {
        getSummaryRenderKeys,resolveHeader,
    } from 'utils/table';
    import {
        DepartmentDescriptionMap,
        DepartmentTableConfig,
        EmployeeTableConfig, RevenueRenderType,EmployeeDescriptionMap,
    } from 'views/statistics/performance/revenue/constants';
    import ProfitRateFilter from 'views/statistics/common/profit-rate-filter';
    import { BizCustomHeader } from '@/components-composite/biz-custom-header';
    import TableHeaderAPI from 'views/statistics/core/api/table-header';

    export default {
        components: {
            StatToolbar,
            StatDimensionPicker,
            FilterSelect,
            ProfitRateFilter,
        },
        mixins: [ClinicTypeJudger, PickerOptions, DateParamsMixins, FeeTypeMixins, StatisticsDataPermission, PayModeMixins],
        data() {
            return {
                visitSourceFilterOptions: [],
                params: {
                    clinicIdFilter: '',
                    doctorIdFilter: '',
                    dimension: 'employee',
                    sortConfig: {
                        orderBy: '',
                        orderType: '',
                    },
                    visitSourceIdList: [],
                    visitSourceLevel1Ids: '',
                    visitSourceLevel2Ids: '',
                    pageIndex: 0,
                    pageSize: 0,
                    departmentId: '',
                    personType: 'SELLER',
                    feeTypeIds: '',
                    profitCategoryTypeId: [],
                },
                feeTypeIdList: [],
                feeTypeFilterOptions: [],
                employeeList: [],
                loading: false,
                exportTaskType: 'charge',
                departmentOptions: [],
                personTypeOptions: [],
                settingOptions: [
                    {
                        text: '设置展示字段',
                        value: 'customer',
                        isOpen: false,
                        groupName: '',
                    },
                ],
                tableHeader: [],
                tableData: [],
                tableRenderHeader: {},
                totalCount: 0,
                summaryData: {},
                totalInfo: '',
                revenueRulePopoverVisible: false,
                chargeSheetData: {
                    list: [],
                },
                adviceFeeTypeOptions: [],
                feeTypeKey: createGUID(),
                isYearLimitDate: true,
            };
        },

        computed: {
            ...mapGetters([
                'clinicConfig',
                'statisticsIsRevenueKpiIncludingRegistration',
                'statisticsIsRevenueKpiIncludingWriter',
                'statisticsIsContainOthersWriterAchievement',
                'hiddenAchievement',
                'currentStatSettingRule',
                'currentClinic',
                'enableCostInStatistics',
                'enableGrossInStatistics',
                'enablePatientMobileInStatistics',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            recommendVisitPlaceHolder() {
                return this.viewDistributeConfig.Statistics.operationStat.recommendVisitPlaceHolder;
            },

            showRevenuePersonType() {
                return this.viewDistributeConfig.Statistics.performanceStat.showRevenuePersonType;
            },
            doctorPlaceHolder() {
                return this.viewDistributeConfig.Statistics.performanceStat.doctorPlaceHolder;
            },
            showRevenueStatPopover() {
                return this.viewDistributeConfig.Statistics.performanceStat.showRevenueStatPopover;
            },
            productTabName() {
                return this.viewDistributeConfig.Statistics.performanceStat.productTabName;
            },
            supportAddNursing() {
                return this.viewDistributeConfig?.Outpatient?.supportAddNursing;
            },
            showRevenueFeeTypeSelect() {
                return this.viewDistributeConfig.Statistics.performanceStat.showRevenueFeeTypeSelect;
            },
            showRevenueDepartmentSelect() {
                return this.viewDistributeConfig.Statistics.performanceStat.showRevenueDepartmentSelect;
            },
            showRevenueVisitSourceSelect() {
                return this.viewDistributeConfig.Statistics.performanceStat.showRevenueVisitSourceSelect;
            },
            revenueDimensionOptionsConfig() {
                return this.viewDistributeConfig.Statistics.performanceStat.revenueDimensionOptionsConfig;
            },
            dimensionOptions() {
                const config = this.revenueDimensionOptionsConfig;
                const options = [
                    {
                        label: 'employee', name: '人员',
                    },
                    {
                        label: 'department', name: '科室',
                    },
                    {
                        label: 'item', name: this.supportAddNursing ? '医嘱' : this.productTabName,
                    },
                    {
                        label: 'detail', name: this.chargeItemSupportDoctorNurse ? '明细' : '单据',
                    },
                ];

                return options.filter((item) => {
                    const configItem = config[item.label];
                    return configItem && configItem.visible;
                }).sort((a, b) => {
                    return config[a.label].order - config[b.label].order;
                });
            },
            isNurse() {
                return this.params.personType === 'NURSE';
            },
            idDoctor() {
                return this.params.personType === 'DOCTOR';
            },
            isSeller() {
                return this.params.personType === 'SELLER';
            },
            displaySummaryData() {
                if (this.showEmployeeTable || this.showDepartmentTable) {
                    return this.tableData.length ? this.summaryData : null;
                }
                return null;

            },
            summaryRenderKeys() {
                if (this.showEmployeeTable || this.showDepartmentTable) {
                    return this.summaryData ? Object.keys(this.summaryData) : [];
                }
                return getSummaryRenderKeys(this.tableHeader);

            },
            renderTypeList() {
                return {
                    noRender: (h, row) => {
                        const srcStr = '00000000';
                        return !row.no ? (
                            <abc-table-cell title={srcStr}>{srcStr}</abc-table-cell>
                            ) : (<abc-table-cell title={`${srcStr}${row.no}`.slice(-8)}>{`${srcStr}${row.no}`.slice(-8)}</abc-table-cell>);
                    },
                    costRender: (h, row, col) => {
                        const displayValue = row[col.key];
                        const showCostText = row.hoverCode & 1;
                        const showAmounText = row.hoverCode & 2;
                        return <abc-table-cell>
                            <abc-flex justify="flex-end" align="center">
                                <span class="ellipsis" title={formatMoney(displayValue)}>{formatMoney(displayValue) || '-'}</span>
                                {
                                    row.hoverCode ? <abc-tooltip-info style="margin-left:4px">
                                        <p>成本特殊情况说明：</p>
                                        {showAmounText ? <p>包含在筛选时间外收费的药品，因此仅有成本没有收费金额。</p> : ''}
                                        {showCostText ? <p>包含因修改入库单导致的成本修正</p> : ''}
                                    </abc-tooltip-info> : ''
                                }

                            </abc-flex>
                        </abc-table-cell>;
                    },
                    commissionAmountDescription: () => {
                        return (
                            <div>
                                <div>计提金额：用来评估员工业绩，为计算提成的基础。提成金额 = 计提金额 * 提成比例</div>
                                <div style="margin-top: 8px">统计口径： { this.currentStatSettingRule.length === 1 ? this.currentStatSettingRule[0] : '' }</div>
                                <div>
                                    {
                                         this.currentStatSettingRule.length > 1 ?
                                        this.currentStatSettingRule.map((item, index) => (
                                            <div style="display: flex; align-items: center">
                                                <span>{index + 1}、 {item}</span>
                                            </div>
                                        )) :
                                        ''
                                    }
                                </div>
                                <div style="color: #005ED9; cursor: pointer; margin-top: 8px" onClick={this.handleClick}>去设置</div>
                            </div>
                        );
                    },
                    prescriptionRender: (h, row, col) => {
                        const displayValue = row[col.key];
                        return <abc-table-cell>{displayValue || '-'}</abc-table-cell>;

                    },

                    visitSourceTypeRender: (h, row) => {
                        const visitSourceContent = row?.visitSourceType || '-';
                        const maxDisplayWords = 5;
                        const showPrPopper = visitSourceContent.length > maxDisplayWords ? true : false;
                        return showPrPopper ? (
                            <AbcPopover
                                visibleArrow={false}
                                theme="yellow"
                                trigger="click"
                                placement="top-start"
                                >
                                <div> {visitSourceContent}</div>
                                <div slot='reference' class="cell" style="max-width: 160px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">  {visitSourceContent.slice(0, 9)}</div>
                            </AbcPopover>
                            ) : <div style="max-width: 160px; padding: 0 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{visitSourceContent}</div>;
                    },
                    patientSourceTypeRender: (h, row) => {
                        const {
                            patientSourceFrom, patientSourceType1, patientSourceType2,
                        } = row;
                        const visitSourceContent = this.concatenateFields(patientSourceType1, patientSourceType2, patientSourceFrom);
                        const maxDisplayWords = 10;
                        const showPrPopper = visitSourceContent.length > maxDisplayWords ? true : false;
                        return showPrPopper ? (
                                <AbcPopover
                                    width='auto'
                                    theme="yellow"
                                    placement="top-start"
                                    visibleArrow={false}
                                    style='padding: 0 10px'>
                                    <div style="max-width: 160px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;padding-left: 10px"> {visitSourceContent}</div>
                                    <div slot='reference' class="cell" style="max-width: 160px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{visitSourceContent}</div>
                                </AbcPopover>
                            ) : <div style="max-width: 160px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;padding-left: 10px" title={visitSourceContent}>{ visitSourceContent}</div>;
                    },
                    sheetMoneyRender: (h, row) => {
                        let icon = '';
                        const {
                            receivableFee, actualFee, chargeSheetId, goodsId, v2ChargeFormItemId,
                            type,
                        } = row;
                        let clickHandler = null;
                        clickHandler = () => {
                            this.querySheet(chargeSheetId, goodsId, v2ChargeFormItemId, type);
                        };
                        if (+receivableFee !== +actualFee) {
                            icon = <abc-icon size="12" icon="info" color="#e6eaee" onClick={clickHandler} style="cursor: pointer;"></abc-icon>;
                            return (
                                <AbcPopover
                                    trigger="click"
                                    z-index="10000"
                                    popperClass="charge-sheet-info-popper"
                                    placement="bottom-start"
                                    theme="yellow"
                                >
                                    <div class="charge-sheet-header">
                                        <span class="charge-sheet-header-left">
                                            <span> 患者: { this.chargeSheetData?.patientName}</span>
                                            <span>诊号: { this.chargeSheetData?.orderNo}</span>
                                        </span>
                                        <span class="charge-sheet-header-right">
                                        应收：{parseFloat(this.chargeSheetData?.shouldReceivePrice || 0).toFixed(2)}
                                        </span>
                                    </div>
                                    <div class="charge-sheet-content">
                                        {this.chargeSheetData.list &&
                                        this.chargeSheetData.list.map(
                                            (item) => (
                                                <div class="charge-sheet-item">
                                                    <span class="charge-sheet-item-left">
                                                        <span>
                                                            {
                                                                item.receiveTime
                                                            }
                                                        </span>
                                                        <span>
                                                            {item.type}
                                                        </span>
                                                        <span style="display: inline-block; width: 60px; overflow: hidden;text-overflow:ellipsis;white-space: nowrap;" title={item.cashierName}>
                                                            {item.cashierName}
                                                        </span>
                                                        <span>
                                                            {item.payType}
                                                        </span>
                                                    </span>
                                                    <span class="charge-sheet-item-right">
                                                        {parseFloat(item.amount || 0).toFixed(2)}
                                                    </span>
                                                </div>
                                            ),
                                        )}
                                    </div>
                                    <div slot="reference">
                                        <div class="cell" style="display: flex; justify-content:space-between;align-items: center" title={row.actualFee}>
                                            {icon}
                                            <span onClick={ (e) => { e.stopPropagation();}}> {row.actualFee}</span>
                                        </div>
                                    </div>
                                </AbcPopover>
                            );
                        }
                        return (
                            <abc-table-cell title={row.amount}>
                                {parseFloat(row.amount).toFixed(2)}
                            </abc-table-cell>
                        );
                    },
                    patientCountRenter: () => {
                        return (<div>用于评估机构的营收情况<br/>计算产生付费行为的患者人次（同一患者不去重）</div>);
                    },
                    transFormPatientCountRender: () => {
                        return (<div>用于评估医生的转化付费能力<br/>计算门诊后产生诊疗付费行为（当天同一患者不去重）＋零售付费（当天同一患者不去重） 的患者人数</div>);
                    },
                };
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicIdFilter;
                }
                return this.currentClinic?.clinicId;
            },

            isIncludingRegistration: {
                get() {
                    return this.statisticsIsRevenueKpiIncludingRegistration;
                },

                set(val) {
                    this.$store.dispatch('setIsRevenueKpiIncludingRegistration', val);
                },
            },

            isIncludingWriter: {
                get() {
                    return this.statisticsIsRevenueKpiIncludingWriter;
                },

                set(val) {
                    this.$store.dispatch('setIsRevenueKpiIncludingWriter', val);
                },
            },
            isContainOthersWriterAchievement: {
                get() {
                    return +this.statisticsIsContainOthersWriterAchievement;
                },

                set(val) {
                    this.$store.dispatch('setIsContainOthersWriterAchievement', val);
                },
            },

            isFeeTypeHeaderAffectedByFilter$() {
                return false;
            },

            toolbarFeatures() {
                const features = [
                    StatToolbar.Feature.DATE,
                    StatToolbar.Feature.CLINIC,
                    StatToolbar.Feature.FEE_TYPE,
                    StatToolbar.Feature.EXPORT,
                    StatToolbar.Feature.SETTING,
                ];

                if (this.supportAddNursing && !this.showProjectTable) {
                    const index = features.findIndex((item) => item === StatToolbar.Feature.FEE_TYPE);
                    if (index !== -1) {
                        features.splice(index, 1);
                    }
                }
                return features;
            },

            showProjectTable() {
                return this.params.dimension === 'item';
            },

            showEmployeeTable() {
                return this.params.dimension === 'employee';
            },

            showSheetTable() {
                return this.params.dimension === 'detail';
            },
            showDepartmentTable() {
                return this.params.dimension === 'department';
            },
            chargeItemSupportDoctorNurse() {
                return this.viewDistributeConfig.chargeItemSupportDoctorNurse;
            },

            feeTypeOptions() {
                return [
                    {
                        id: 'feeRegistration',
                        name: `${this.$t('registrationFeeName')}`,
                    },
                ];
            },

            hasCopyWriter() {
                return this.tableData.filter((val) => val.isCW).length > 0;
            },

            currentPageParams() {
                if (this.showProjectTable || this.showSheetTable) {
                    return {
                        ...this.params,
                    };
                }
                return {
                    ...this.params,
                    pageSize: 0,
                };
            },
            doctorId() {
                if (this.params.doctorIdFilter) {
                    return this.params.doctorIdFilter.split('-idWithName-')[0];
                }
                return '';
            },
            tableKeyEnum() {
                const {
                    showRevenueDetail,
                    performanceStat: {
                        revenueStatPersonalTableKey,
                        revenueStatItemTableKey,
                        revenueStatSheetTableKey,
                    },
                } = this.viewDistributeConfig.Statistics;
                return {
                    'employee': {
                        key: revenueStatPersonalTableKey,
                        label: '人员',
                        mode: 'fixed',
                    },
                    'department': {
                        key: 'achievement.charge.department',
                        label: '科室',
                        mode: 'fixed',
                    },
                    'item': {
                        key: revenueStatItemTableKey,
                        label: '项目',
                        mode: 'draggle',
                    },
                    'detail': {
                        key: showRevenueDetail ? 'achievement.charge.detail' : revenueStatSheetTableKey,
                        label: showRevenueDetail ? '明细' : '单据',
                        mode: 'draggle',
                    },
                };
            },
            tableHeaderConfigKey() {
                return this.tableKeyEnum[this.params.dimension].key;
            },
        },

        created() {
            if (this.isYearLimitDate) {
                this.formatDateToOneYear();
            }
            // 由于医院没有开单业绩代录面板 默认勾选挂号费
            if (!this.showRevenueStatPopover) {
                this.isIncludingRegistration = true;
            }

            if (this.supportAddNursing && this.showProjectTable) {
                this.getAdviceTypeOptions();
            }
            this.exportService = new ExportService();

            // 设置 费用分类无法排序
            this.feeTypeSortable$ = false;
            this.feeTypeSticky$ = false;
            this.getPersonOptions();
            this.getSelectOptions();

        },
        mounted() {
            this.$abcEventBus.$on('amount-setting-success', () => {
                this.getSelectOptions();
                this.getTableData();
            }, this);
            this.getListSource();
            this.params.dimension = this.viewDistributeConfig.Statistics.performanceStat.defaultRevenueDimension;
        },

        beforeDestroy() {
            this.exportService.destroy();
            this.$abcEventBus.$offVmEvent(this._uid);
        },

        methods: {
            async handleMounted(data) {
                const { paginationLimit } = data;
                this.params.pageSize = paginationLimit;
                await this.getTableData();
            },
            createStaticConfig() {
                if (this.showEmployeeTable) {
                    return EmployeeTableConfig;
                }
                if (this.showDepartmentTable) {
                    return DepartmentTableConfig;
                }
            },
            createDescriptionMap() {
                if (this.showDepartmentTable) {
                    return DepartmentDescriptionMap;
                }
                if (this.showEmployeeTable) {
                    return EmployeeDescriptionMap;
                }
                return {};
            },
            createRenderTypeMap() {
                return RevenueRenderType;
            },
            ...mapActions(['getAchievementInfo']),
            concatenateFields(a, b, c) {
                a = a || '-';
                b = b || '-';
                c = c || '-';

                if (a === '-' && b === '-' && c === '-') {
                    return '-';
                } if (b === '-' && c === '-') {
                    return a;
                } if (c === '-') {
                    return `${a}-${b}`;
                }
                return `${a}-${b}-${c}`;
            },
            handleSortChange() {},
            handleRevenueRulePopperHide() {
                this.revenueRulePopoverVisible = false;
            },
            async querySheet(chargeSheetId, productId = '', v2ChargeFormItemId = '', type) {
                try {
                    const res = await TransactionService.queryChargeSheet({
                        chargeSheetId,
                        productId,
                        v2ChargeFormItemId,
                        action: type,
                    });
                    if (res) {
                        this.chargeSheetData = res;
                    }
                } catch (err) {
                    console.log(err);
                    this.chargeSheetData = {};
                }
            },
            handleAdviceFeeTypeChange(list = []) {
                this.params.feeTypeIds = list?.length ? list?.join(',') : '';
                this.getTableData();
            },
            handlePersonTypeChange() {
                this.params.doctorIdFilter = '';
                this.getSelectOptions();
                if (this.supportAddNursing) {
                    this.getAdviceTypeOptions();
                }
                this.getTableData();
            },
            handleOpenStatDimensionSettingDialog(val) {
                if (val) {
                    new BizCustomHeader({
                        value: true,
                        tableKey: this.tableHeaderConfigKey,
                        titleName: `开单业绩 - ${this.tableKeyEnum[this.params.dimension].label}`,
                        mode: this.tableKeyEnum[this.params.dimension].mode,
                        finishFunc: this.getTableData,
                        tableHeaderApi: TableHeaderAPI,
                    }).generateDialog({ parent: this });
                } else {
                    this.formDialogVisible = true;
                }
            },

            async getPersonOptions() {
                const haveNurse = this.params.dimension === 'department' ? false : true;
                if (this.chargeItemSupportDoctorNurse) {
                    try {
                        const res = await RevenueAPI.getRevenueSelectPersonType({
                            haveNurse,
                        });
                        if (res) {
                            this.personTypeOptions = res.data || [];
                        }
                    } catch (e) {
                        console.log(e);
                    }
                } else {
                    try {
                        const res = await RevenueAPI.achievement.getAchievementEmployeeType();
                        if (res) {
                            this.personTypeOptions = res?.data?.data || [];
                        }
                    } catch (e) {
                        console.log(e);
                    }
                }
            },
            handleClick() {
                new AmountDialog({
                    successHandle: () => {
                        this.getTableData();
                    },
                    clinicId: this.currentClinic.clinicId,
                }, 'amount-dialog').generateDialog({ parent: this });
            },
            async getAdviceTypeOptions() {
                try {
                    const {
                        begin: beginDate, end: endDate,
                    } = this.params.dateFilter$;
                    const res = await ChargeAPI.getAdviceTypeSelect({
                        beginDate,
                        endDate,
                        clinicId: this.currentClinic.clinicId,
                        employeeTypeEnum: this.params.personType,
                    });
                    this.adviceFeeTypeOptions = res?.data?.data || [];
                    this.feeTypeKey = createGUID();
                } catch (err) {
                    console.log(err);
                }
            },

            handleVisitSourceChange() {
                this.generateVisitSource2Params(this.$refs.visitSourceRef.getOriValue(this.params.visitSourceIdList));
            },

            /**
             * 拉取就诊来源数据
             * <AUTHOR>
             * @date 2020-06-29
             */
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();
                }
                this.visitSourceFilterOptions = RecommendService.getInstance().cascaderOptions;
                this.visitSourceFilterOptions.unshift({
                    id: '00000000000000000000000000000000',
                    name: '未指定',
                });

                this.visitSourceFilterOptions = RecommendService.getInstance().processIds(this.visitSourceFilterOptions);
            },
            generateVisitSource2Params(list) {
                // 第一级是1， 2，下面要拼 null
                // 第一级是0，下面是3， 4要拼 null
                // 其余该怎么处理，就怎么处理
                const newList = list.map((item) => item || []);
                const medicalRecordType1 = newList.length >= 1 && newList[0]?.length ? [...new Set(newList[0].map((item) => item.id))] : [];
                const medicalRecordType2 = [];
                let medicalRecordType3 = [];
                if (newList.length >= 2 && newList[1]?.length) {
                    const arr = [...new Set(newList[1].map((item) => item.newId))];
                    arr.forEach((item) => {
                        if (item.split('/').length === 3) {
                            medicalRecordType3.push(item);
                        } else {
                            medicalRecordType2.push(item);
                        }
                    });
                }

                if (newList.length >= 3 && newList[2]?.length) {
                    medicalRecordType3 = [...medicalRecordType3, ...new Set(newList[2].map((item) => item.newId))];
                }
                this.params.visitSourceLevel1Ids = medicalRecordType1.join(',');
                this.params.visitSourceLevel2Ids = medicalRecordType2.join(',');
                this.params.visitSourceLevel3Ids = medicalRecordType3.join(',');

                this.getTableData();
            },
            handleDimensionChange() {
                this.params.pageIndex = 0;
                this.getPersonOptions();
                if (this.showDepartmentTable) {
                    this.params.doctorIdFilter = '';
                    if (this.params.personType === 'NURSE') {
                        this.params.personType = this.personTypeOption?.[0]?.value || 'SELLER';
                    }
                }

                if (this.supportAddNursing && this.showProjectTable) {
                    this.getAdviceTypeOptions();
                }
                this.getTableData();
            },

            handleFeeTypeChange() {
                this.getTableData();
            },

            handleDoctorChange() {
                this.getTableData();
            },

            handleDepartmentChange() {
                this.getTableData();
            },

            handleDateChange() {
                this.params.feeTypeFilter$ = [];
                this.feeTypeIdList = [];
                this.params.feeTypeIds = '';
                if (this.supportAddNursing && this.showProjectTable) {
                    this.getAdviceTypeOptions();
                }
                this.getSelectOptions();
                this.getTableData();
            },

            handleClinicChange() {
                if (this.supportAddNursing && this.showProjectTable) {
                    this.getAdviceTypeOptions();
                }
                this.params.departmentId = '';
                this.getSelectOptions();
                this.getTableData();
            },

            handleIncludingRegistrationChange() {
                this.getSelectOptions();
                this.getTableData();
            },
            handleRevenueChange() {
                this.getTableData();
            },

            handleIncludingWriterChange() {
                this.getSelectOptions();
                this.getTableData();
            },

            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            formatNumber(prop) {
                return formatNumber(prop);
            },
            formatMoney(prop) {
                return formatMoney(prop);
            },
            /**
             * @desc 总计都要把代录的去掉
             * <AUTHOR>
             * @date 2019/04/21 23:07:06
             * @params
             * @return
             */
            calTotalPrice(src, prop, fn) {
                let total = 0;
                src.forEach((it) => {
                    if (!it.isCW) {
                        total += fn ? fn(it) * 10000 : (it[prop] ? it[prop] : 0) * 10000;
                    }
                });

                return total / 10000;
            },
            async handleExport(_, keys) {
                const {
                    begin, end,
                } = this.params.dateFilter$;
                const {
                    clinicIdFilter, personType, departmentId,visitSourceLevel1Ids, feeTypeIds,
                    visitSourceLevel2Ids, visitSourceLevel3Ids, doctorIdFilter,
                } = this.params;
                const employees = doctorIdFilter ? [
                    {
                        id: doctorIdFilter.split('-idWithName-')[0],
                        name: doctorIdFilter.split('-idWithName-')[1],
                    },
                ] : [];
                // 2020-12-31 16:37:34
                // 超过三个月，导出的数据不包含单据维度，需要提示用户，用户确认后继续导出。
                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        beginDate: begin,
                        endDate: end,
                        employeeId: this.chargeItemSupportDoctorNurse ? '' : personType !== 'AGENT' ? this.doctorId : '' ,
                        doctorId: this.chargeItemSupportDoctorNurse ? personType !== 'AGENT' ? this.doctorId : '' : '',
                        employees: personType !== 'AGENT' ? employees : [],
                        clinicId: clinicIdFilter,
                        feeType1: this.feeType1FilterForRequest$,
                        feeType2: this.feeType2FilterForRequest$,
                        payModes: this.payModeFilterForRequest$,
                        enableCost: this.enableCostInStatistics,
                        enableGross: this.enableGrossInStatistics,
                        enablePatientMobile: this.enablePatientMobileInStatistics,
                        includeReg: +this.isIncludingRegistration,
                        includeWriter: +this.isIncludingWriter,
                        medicalRecordType1: visitSourceLevel1Ids,
                        medicalRecordType2: visitSourceLevel2Ids,
                        medicalRecordType3: visitSourceLevel3Ids,
                        isContainOthersWriterAchievement: this.isContainOthersWriterAchievement,
                        employeeTypeEnum: personType,
                        copyWriterId: this.params.personType === 'AGENT' ? this.doctorId : '',
                        departmentId,
                        keyWordList: keys,
                        feeTypeIds,
                        profitCategoryTypeId: this.params.profitCategoryTypeId,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            getTableParams() {
                const {
                    begin: beginDate, end: endDate,
                } = this.params.dateFilter$;
                const {
                    dimension, pageIndex, pageSize, departmentId, personType, doctorIdFilter, visitSourceLevel1Ids,
                    visitSourceLevel2Ids, visitSourceLevel3Ids, feeTypeIds, profitCategoryTypeId,
                } = this.params;
                const { queryClinicId: clinicId } = this;
                const offset = pageIndex * pageSize;
                const isNotNeedFeeTypeParams = this.supportAddNursing && !this.showProjectTable;
                const feeTypeParams = {
                    feeType1: this.feeType1FilterForRequest$,
                    feeType2: this.feeType2FilterForRequest$,
                };
                const baseParams = {
                    beginDate,
                    endDate,
                    clinicId,
                    includeReg: +this.isIncludingRegistration,
                    isContainOthersWriterAchievement: this.isContainOthersWriterAchievement,
                    includeWriter: +this.isIncludingWriter,
                    employeeTypeEnum: personType,
                    enableCost: this.enableCostInStatistics,
                    enableGross: this.enableGrossInStatistics,
                    enablePatientMobile: this.enablePatientMobileInStatistics,
                };
                if (!isNotNeedFeeTypeParams) {
                    Object.assign(baseParams,feeTypeParams);
                }
                const employees = doctorIdFilter ? [
                    {
                        id: doctorIdFilter.split('-idWithName-')[0],
                        name: doctorIdFilter.split('-idWithName-')[1],
                    },
                ] : [];
                if (dimension === 'item') {
                    return {
                        ...baseParams,
                        employees: personType !== 'AGENT' ? JSON.stringify(employees) : '',
                        offset,
                        size: pageSize,
                        payModes: this.payModeFilterForRequest$,
                        departmentId,
                        feeTypeIds,
                        copyWriterId: personType === 'AGENT' ? this.doctorId : '',
                        profitCategoryTypeId: JSON.stringify(profitCategoryTypeId),
                    };
                } if (dimension === 'employee') {
                    return {
                        ...baseParams,
                        employees: personType !== 'AGENT' ? JSON.stringify(employees) : '',
                        payModes: this.payModeFilterForRequest$,
                        copyWriterId: personType === 'AGENT' ? this.doctorId : '',
                    };
                } if (dimension === 'detail') {
                    return {
                        ...baseParams,
                        offset,
                        size: pageSize,
                        departmentId,
                        employees: personType !== 'AGENT' ? employees : [],
                        medicalRecordType1: visitSourceLevel1Ids,
                        medicalRecordType2: visitSourceLevel2Ids,
                        medicalRecordType3: visitSourceLevel3Ids,
                        copyWriterId: personType === 'AGENT' ? this.doctorId : '',
                    };
                }
                return {
                    ...baseParams,
                    departmentId,
                };

            },

            updateTableData(isClear = false, tableData = {}, resetPageParams) {
                if (isClear) {
                    this.tableHeader = [];
                    this.tableData = [];
                    if (resetPageParams) {
                        this.totalCount = 0;
                    }
                } else {
                    const {
                        header = [], data = [], total = {}, keyData = [], summary = {},
                    } = tableData || {};
                    this.tableHeader = header || [];
                    this.tableData = data || [];
                    if (resetPageParams) {
                        this.totalCount = total?.count || 0;
                    }
                    this.tableKeyData = keyData || [];
                    this.summaryData = summary || {};
                    this.totalInfo = this.transTemplateToTotalInfo(total?.data, total?.template);
                    if (!this.showSheetTable) {
                        this.tableData?.forEach((item) => {
                            if (item.isWriter) {
                                item.trClass = 'td-copywriter';
                            }
                        });
                    }
                }
            },
            transTemplateToTotalInfo (data = [], template = '') {
                if (!template) return '';
                let index = 0;
                const result = template.replace(
                    RegExp('%s', 'g'),
                    () => `<span style="color: #000; font-weight: bold">${index === 0 ? data[index++] : parseFloat(data[index++] || 0).toFixed(2)}</span>`,
                );
                return result;
            },

            /**
             * @desc
             * 获取表格列表，限制条件只有 begin end
             * <AUTHOR>
             * @date 2018/11/24 6:41 PM
             * @params
             * @return
             */
            async getTableData(resetPageParams = true) {
                this.loading = true;
                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }
                this.getAchievementInfo();
                const { dimension } = this.params;
                const params = this.getTableParams();

                if (dimension === 'item') {
                    try {
                        const { data } = await RevenueAPI.achievement.getAchievementGoods({
                            ...params,
                        });
                        this.updateTableData(false, data, resetPageParams);
                    } catch (err) {
                        console.error(err);
                        this.updateTableData(true);
                    }
                } else if (dimension === 'employee') {
                    try {
                        const { data } = await RevenueAPI.achievement.getAchievementPersonnel({
                            ...params,
                        });
                        const headers = data.header;
                        const isPrescription = headers?.find((item) => item.prop === 'prescriptions');
                        const options = headers?.filter((item) => item.prop !== 'prescriptions');

                        if (isPrescription) {
                            options.push({
                                ...isPrescription,
                            });
                        }

                        const payload = {
                            ...data,
                            header: options,

                        };
                        this.updateTableData(false, payload, resetPageParams);
                    } catch (err) {
                        console.error(err);
                        this.updateTableData(true);
                    }
                } else if (dimension === 'detail') {
                    if (this.chargeItemSupportDoctorNurse) {
                        try {
                            const { data } = await RevenueAPI.achievement.getAchievementDetail({
                                ...params,
                            });
                            this.updateTableData(false, data, resetPageParams);
                        } catch (err) {
                            console.error(err);
                            this.updateTableData(true);
                        }
                    } else {
                        try {
                            const { data } = await RevenueAPI.achievement.getAchievementSheet({
                                ...params,
                            });
                            this.updateTableData(false, data, resetPageParams);
                        } catch (err) {
                            console.error(err);
                            this.updateTableData(true);
                        }
                    }
                } else {
                    try {
                        const { data } = await RevenueAPI.achievement.getAchievementDepartment({
                            ...params,
                        });
                        this.updateTableData(false, data);
                    } catch (err) {
                        console.error(err);
                        this.updateTableData(true);
                    }
                }
                this.tableRenderHeader = this.createTableRenderHeader();
                this.loading = false;
            },
            async getSelectOptions() {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                    personType: employeeTypeEnum,
                } = this.params;
                try {
                    const res = await RevenueAPI.selectOptions({
                        beginDate,
                        endDate,
                        clinicId: this.queryClinicId,
                        includeReg: +this.isIncludingRegistration,
                        includeWriter: +this.isIncludingWriter,
                        employeeTypeEnum,
                    });
                    this.feeTypeFilterOptions = res?.feeTypeList;
                    this.departmentOptions = res?.departments;
                    this.employeeList = res?.employees;
                } catch (e) {
                    console.log(e);
                    this.feeTypeFilterOptions = [];
                }
            },
            createTableRenderHeader() {
                if (this.showEmployeeTable || this.showDepartmentTable) {
                    const config = resolveHeaderV2({
                        header: this.tableHeader,
                        renderTypeList: this.renderTypeList,
                        staticConfig: this.createStaticConfig(),
                        renderTypeMap: this.createRenderTypeMap(),
                        descriptionMap: this.createDescriptionMap(),
                    });
                    return {
                        hasHeaderBorder: true,
                        hasInnerBorder: true,
                        ...config,
                    };
                }
                const list = resolveHeader(
                    this.tableHeader,
                    this.renderTypeList,
                    true,
                );
                list.forEach((item) => {
                    item.children.forEach((child) => {
                        child.autoSort = child.sortable;
                    });
                    item.autoSort = item.sortable;
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list,
                };
            },

            handelProfitRateChange(val) {
                this.params.profitCategoryTypeId = val.profitCategoryList;
                this.getTableData();
            },
        },
    };
</script>
<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.revenue-stat__input-filter-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 32px;
    padding: 0 6px;
    margin-right: 8px;
    cursor: pointer;
    border: 1px solid $P1;
    border-radius: var(--abc-border-radius-small);

    &:hover {
        border: 1px solid $theme3;
    }

    &.is-show {
        border: 1px solid #0270c9;
        box-shadow: 0 0 0 2px #c3e0fe;
    }

    .abc-icon {
        width: 22px;
        margin-right: 0;
        line-height: 1;
        text-align: center;
    }

    li {
        margin-right: 16px;

        span {
            margin-left: -4px;
        }
    }
}

.statistics-module__revenue-wrapper {
    .date-label {
        display: inline-block;
        height: 32px;
        margin-right: 0;
        line-height: 32px;
    }

    .td-copywriter {
        color: $T2;
    }

    .revenue-stat__rule-popover {
        display: inline-block;

        .revenue-stat__rule-list-wrapper {
            margin-bottom: 30px;

            .abc-radio {
                height: 48px;
            }

            .rule-item-text {
                color: #000000;
            }
        }
    }
}

.revenue-stat__rule-err-tips {
    width: 344px;
    height: 28px;
    padding-left: 10px;
    font-size: 12px;
    line-height: 28px;
    color: #ff9933;
    background: #fff4ea;
    border: 1px solid #ffebd6;
    border-radius: var(--abc-border-radius-small);
}

.charge-sheet-info-popper {
    min-width: 420px;
    max-width: 450px;
    min-height: 160px;
    max-height: 180px;
    padding: 12px;
    overflow-y: auto;
    overflow-y: overlay;

    @include scrollBar;

    .charge-sheet-header {
        display: flex;
        justify-content: space-between;
        padding-bottom: 8px;
        color: $S1;
        border-bottom: 1px dashed $P6;

        .charge-sheet-header-left {
            span {
                margin-right: 24px;
            }
        }
    }

    .charge-sheet-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 8px;
        padding-bottom: 3px;
        color: $T2;

        .charge-sheet-item-left {
            display: flex;
            align-items: center;

            span {
                max-width: 140px;
                margin-right: 12px;

                @include ellipsis;
            }
        }
    }
}
</style>
