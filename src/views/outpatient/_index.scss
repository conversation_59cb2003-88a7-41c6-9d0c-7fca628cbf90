@import "diagnosis-treatment/diagnosis-treatment";
@import "./common/doctor-advice-dialog/doctor-advice-dialog";
@import "./common/review-charge-dialog/review-charge-dialog";
@import "./common/outpatient-setting-popover/outpatient-setting-popover";
@import "common/medical-record/index";
@import "prescription-group/prescription-group";
@import "src/styles/abc-common.scss";

.outpatient-container {
    .medical-history-wrapper {
        position: relative;

        .abc-form-item {
            width: 100%;
        }
    }

    #abc-container-center__top-head .header-content {
        padding: 0 20px 0 16px !important;
    }

    .main-content.outpatient-main-content {
        padding-right: 20px;
    }

    .tips-summary {
        margin-top: 16px;
        border: 1px solid var(--abc-color-card-border-color);
        border-radius: var(--abc-border-radius-small);
    }

    .edit-mr-btn {
        position: absolute;
        top: -80px;
        right: 40px;
        width: 48px;
        height: 48px;
        line-height: 48px;
        color: $B2;
        text-align: center;
        cursor: pointer;
    }

    .outpatient .main-content .outpatient-form-wrapper .abc-form .button-wrapper {
        margin-top: 40px;
    }

    .split-line {
        margin: 16px 0;
        border-top: 1px dashed $P6;
    }

    .prescription-wrapper {
        margin-top: 16px;
    }

    .outpatient-form-tab-wrapper {
        display: flex;
        align-items: center;
    }

    .outpatient-record-count {
        margin-right: 2px;
        margin-left: 16px;
        font-weight: normal;
        color: $G2;
    }

    .main-content {
        .outpatient-bottom-info {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-top: 16px;

            > div + div {
                margin-left: 16px;
            }

            .patient-order-number {
                height: 20px;
                color: $T2;
            }

            .default-visit-time {
                display: flex;
                align-items: center;
                margin-left: 12px;
                color: $T2;

                .label {
                    height: 20px;
                }

                .abc-button-text {
                    height: 20px;
                    color: $T2;
                }

                &.custom-date .abc-button-text {
                    color: $T1;
                }
            }
        }

        .pay-exception-tips-card {
            margin-bottom: 16px;
        }

        .flex {
            @include flex(row, space-between, center);
        }
    }

    &.crm-module {
        .main-content {
            max-width: 1020px;
            margin: 0 auto;
        }
    }
}

.quick-list-wrapper {
    .quick-content-wrapper {
        .treat-online-status {
            position: relative;
            z-index: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            font-size: 12px;
            color: $Y2;
            cursor: pointer;
            background-color: $Y4;

            &.online {
                color: $G1;
                background-color: $G4;
            }

            .treat-online-setting-btn {
                position: absolute;
                top: 0;
                right: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 36px;
                height: 100%;

                .iconfont {
                    font-size: 12px;
                    color: $T3;
                }

                &:hover {
                    .iconfont {
                        color: $theme2;
                    }
                }
            }

            .change-treat-online-dialog {
                position: absolute;
                top: 36px;
                z-index: 10;
                width: 100px;
                padding: 8px 0;
                background-color: #ffffff;
                border: 1px solid rgba(183, 185, 194, 1);
                border-radius: 2px;
                box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

                > div {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 32px;
                    padding: 0 12px;
                    font-size: 14px;
                    color: $T1;
                    cursor: pointer;
                }

                i {
                    color: $B2;
                }
            }
        }
    }

    .quick-list-small {
        .patient-name .patient-tag {
            width: 20px;
            height: 20px;
            margin-left: 6px;
            font-size: 14px;
            line-height: 18px;
            color: #ff9933;
            text-align: center;
            border: 1px solid #ff9933;
            border-radius: 20px;
        }

        .time .tag-box {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin: 0 6px;
            background-repeat: no-repeat;
            background-size: 100% 100%;

            &.pass-num {
                background-image: url('~assets/images/call-number/<EMAIL>');
            }

            &.late-num {
                background-image: url('~assets/images/call-number/<EMAIL>');
            }
        }

        .time .text {
            display: inline-block;
            min-width: 38px;
            text-align: right;
        }

        .active .tag-box {
            &.pass-num {
                background-image: url('~assets/images/call-number/<EMAIL>');
            }

            &.late-num {
                background-image: url('~assets/images/call-number/<EMAIL>');
            }
        }
    }
}

.outpatient-doctor-select {
    top: -6px !important;
    width: 92px !important;
}

.outpatient-date-picker {
    transform: translateX(36px) !important;
}

@media screen and (min-width: 1734px) {
    .outpatient-diagnosis-treatment .goods-item-list .delete-item,
    .prescription-table-wrapper .prescription-table .table-tr .delete-item {
        right: -28px;
        justify-content: flex-end;
        width: 28px;
    }
}
