<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        content-styles="width: 700px;padding: 0;min-height: 500px;overflow-x: hidden;"
        title="发起转诊"
        custom-class="outpatient-referral-dialog"
        append-to-body
        :auto-focus="false"
    >
        <div class="dialog-content clearfix">
            <div class="header-wrapper">
                <!--患者信息-->
                <patient-section
                    v-model="postData.patient"
                    :is-required="true"
                    :disabled="true"
                    :allow-show-tags="false"
                ></patient-section>
                <div class="refer-ral-info">
                    <span>{{ postData.doctorName || '不指定' }}<template v-if="postData.departmentName">{{ `-${ postData.departmentName}` }}</template></span>
                    <span class="time">{{ `${todayDate}转出` }}</span>
                </div>
            </div>
            <abc-form ref="outpatientReferralForm">
                <component
                    :is="currentMedicalRecord"
                    ref="medicalRecord"
                    v-model="simpleMedicalRecord"
                    :post-data="postData"
                    fixed
                    :switch-setting="switchSetting"
                    diagnosis-label="初步诊断"
                    :show-doctor-advice="false"
                    :required-chief-complaint="false"
                    :required-diagnosis="false"
                    style="margin-top: 16px;"
                >
                    <div class="medical-record-item">
                        <label>转诊原因/备注</label>
                        <abc-form-item>
                            <abc-edit-div
                                v-model="remark"
                                fixed
                                :maxlength="300"
                                spellcheck="false"
                            ></abc-edit-div>
                        </abc-form-item>
                    </div>
                </component>
                <!--预约信息-->
                <div class="reservation-info">
                    <h4>转入挂号/预约</h4>
                    <div class="filter-wrap">
                        <abc-date-picker
                            v-model="selectDate"
                            :width="165"
                            value-format="YYYY-MM-DD"
                            :clearable="false"
                            :describe-list="describeList"
                            :picker-options="pickerOptions"
                            @change="onDateChange"
                        >
                        </abc-date-picker>

                        <abc-select
                            v-if="isShowRegistrationCategory"
                            v-model.number="selectRegistrationCategory"
                            :width="88"
                            style="margin-left: 8px;"
                            @change="changeRegistrationCategory"
                        >
                            <abc-option
                                v-for="item in registrationCategoryOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </abc-option>
                        </abc-select>

                        <abc-input
                            v-model.trim="keywords"
                            type="text"
                            placeholder="搜索医生"
                            style="margin-left: 8px;"
                            :width="200"
                            @input="clearSelected"
                        >
                            <abc-search-icon slot="prepend"></abc-search-icon>
                        </abc-input>
                    </div>
                    <abc-radio-group v-model="selectedDoctorId">
                        <ul v-if="doctors.length" v-abc-loading="loading" class="doctor-list">
                            <li
                                v-for="item in doctors"
                                :key="item.doctorId + item.departmentId"
                                :class="{
                                    'selected': selectedDoctorId === item.doctorId + item.departmentId,
                                    'disable': disabledRadio(item)
                                }"
                                @click="selectDoctor(item)"
                            >
                                <abc-radio
                                    class="doctor-name"
                                    :class="{ gray: !item.onSchedule }"
                                    :label="item.doctorId + item.departmentId"
                                    :disabled="disabledRadio(item)"
                                    @click.native.prevent=""
                                >
                                    {{ item.doctorName }}<span v-if="item.departmentName">-{{ item.departmentName }}</span>
                                </abc-radio>
                                <span v-if="item.onSchedule" class="schedule-periods">{{ item.schedulePeriods.join() }}</span>
                                <span v-else class="schedule-periods">未排班</span>
                                <div
                                    v-if="selectedDoctorId && selectedDoctor.doctorId === item.doctorId && selectedDoctor.departmentId === item.departmentId"
                                    class="select-order-no"
                                    @click.prevent.stop=""
                                >
                                    <template v-if="isFixOrderMode">
                                        <abc-form-item required>
                                            <abc-select
                                                v-model="timeOptionId"
                                                size="tiny"
                                                class="time-of-day-wrapper"
                                                :width="44"
                                                :inner-width="54"
                                                no-icon
                                                @change="changeTimeOfDay"
                                            >
                                                <abc-option
                                                    v-for="t in timeOptions"
                                                    :key="t.id"
                                                    :label="t.time"
                                                    :value="t.id"
                                                >
                                                </abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-form-item required>
                                            <abc-select
                                                v-if="!isStopDiagnose"
                                                v-model="orderNoAndTime"
                                                size="tiny"
                                                :width="150"
                                                :class="['order-no-and-time-wrapper',{ 'show-description': showDescription }]"
                                                no-icon
                                                :placeholder="orderNoPlaceholderStr"
                                            >
                                                <template v-if="showDescription" #description>
                                                    <div
                                                        style="position: absolute;
                                                        top: 50%;
                                                        left: -142px;
                                                        display: flex;
                                                        align-items: center;
                                                        justify-content: space-between;
                                                        width: 30px;
                                                        font-size: 14px;
                                                        transform: translateY(-50%);"
                                                    >
                                                        {{ isGenerateOrderNoOnSignDisplayNoStr }}
                                                    </div>
                                                </template>
                                                <abc-option
                                                    v-for="item in orderNoAndTimeOptions"
                                                    :key="`${item.orderNo}-${item.start}-${item.end}-${item.type}-${item.timeOfDay}`"
                                                    style="padding-right: 0; padding-left: 6px;"
                                                    :label="getLabelStr(item)"
                                                    :value="`${item.orderNo || ''}-${item.start}-${item.end}-${item.type}-${item.timeOfDay}`"
                                                    @click="clickOrderNoAndTimeOption(item)"
                                                >
                                                    <span>
                                                        <span v-if="!isGenerateOrderNoOnSign">{{ (`${item.orderNo}`).padStart(2,'0') }}号</span>
                                                        <span>{{ item.start }} ~ {{ item.end }}</span>
                                                        <span v-if="!!item.type">
                                                            <img
                                                                v-if="item.type === 1"
                                                                style="width: 14px; height: 14px; margin-top: -2px; vertical-align: middle;"
                                                                src="~assets/images/<EMAIL>"
                                                                alt="xian"
                                                            />
                                                            <img
                                                                v-else
                                                                style="width: 14px; height: 14px; margin-top: -2px; vertical-align: middle;"
                                                                src="~assets/images/<EMAIL>"
                                                                alt="hui"
                                                            />
                                                        </span>
                                                    </span>
                                                </abc-option>
                                            </abc-select>
                                            <abc-input
                                                v-else
                                                size="tiny"
                                                :class="['order-no-and-time-wrapper',{ 'show-description': showDescription }]"
                                                :width="150"
                                                readonly
                                            >
                                                <abc-text
                                                    slot="prepend"
                                                    style="padding-left: 7px;"
                                                    size="normal"
                                                    theme="warning-light"
                                                >
                                                    停诊
                                                </abc-text>
                                            </abc-input>
                                        </abc-form-item>
                                    </template>
                                    <abc-form-item v-else required>
                                        <div class="date-time-range-wrapper">
                                            <abc-time-range-picker
                                                v-model="reserveTimeRange"
                                                :picker-options="timeRangePickerOptions"
                                                :width="150"
                                                size="tiny"
                                                :focus-show-options="false"
                                                :clearable="false"
                                                class="time-range-picker"
                                                @change="handleChangeReserveTimeRange"
                                            >
                                                <template slot="option-describe" slot-scope="{ option }">
                                                    <div style="display: inline-flex; align-items: center; margin-right: 1px;">
                                                        <abc-icon icon="people"></abc-icon>
                                                        <span style="width: 7px;">{{ getRegistrationCount(option) }}</span>
                                                    </div>
                                                </template>
                                            </abc-time-range-picker>
                                        </div>
                                    </abc-form-item>
                                </div>
                                <span v-else class="rest-count">
                                    <template v-if="isFixOrderMode">
                                        <template v-if="!item.onSchedule"></template>
                                        <template v-else>余号<span :class="{ yellow: item.restCount === 0 }">{{ item.restCount }}</span></template>
                                    </template>
                                    <template v-else>
                                        <template v-if="item.onSchedule">
                                            已约<span>{{ item.notRefundedCount }}</span>
                                        </template>
                                    </template>
                                </span>
                            </li>
                        </ul>
                        <abc-content-empty v-else top="50px" value="暂无数据"></abc-content-empty>
                    </abc-radio-group>
                </div>
            </abc-form>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="buttonLoading" @click="confirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="visible = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import PatientSection from 'src/views/layout/patient/patient-section';
    import ChiefComplaint from 'src/views/outpatient/common/medical-record/chief-complaint.vue';
    import ExtendDiagnosisInfos from 'src/views/outpatient/common/medical-record/extend-diagnosis-infos.vue';
    import RegistrationsAPI from 'api/registrations/index';
    import { formatDate } from '@abc/utils-date';
    import { mapGetters } from 'vuex';
    import BoardApi from 'api/registrations/board';
    import { parseTime } from '@/utils';
    import OutpatientAPI from 'api/outpatient';
    import clone from 'utils/clone';
    import { getMedicalRecordStruct } from 'views/outpatient/common/medical-record/utils.js';
    import {
        GENERATE_ORDER_NO_TIME_TYPE,
        RESERVATION_MODE_TYPE,
        RESERVATION_TIME_TYPE,
        SERVICE_TYPE_ENUM,
    } from 'views/settings/registered-reservation/constant';
    import Clone from 'utils/clone';
    import { RegistrationCategory } from '@/views-hospital/registered-fee/constant';

    export default {
        name: 'OutpatientReferralDialog',
        components: {
            PatientSection,
            ChiefComplaint,
            ExtendDiagnosisInfos,
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            postData: {
                type: Object,
                required: true,
            },
        },

        data() {
            return {
                remark: '',
                selectDate: formatDate(new Date(), 'YYYY-MM-DD'),
                keywords: '',
                selectedDoctor: '',
                selectedDoctorId: '',
                selectRegistrationCategory: RegistrationCategory.ORDINARY,
                loading: false,
                doctorList: [],
                describeList: [],
                pickerOptions: {
                    shortcuts: [{
                        text: '今天',
                        onClick(cb) {
                            const start = new Date();
                            cb(start);
                        },
                    }],
                },

                calculateOrderNo: null,
                orderNoPlaceholderStr: '',
                orderNoAndTimeStr: '',
                orderNo: '',
                orderNoType: 0,
                selectedOrderNoTimeOfDay: '',
                orderNoAndTimeOptions: [],
                doctorNoInfoList: [],
                timeOptionId: '',
                timeOptions: [
                    {
                        id: 1,
                        date: '今天',
                        time: '上午',
                        value: {
                            start: '00:00',
                            end: '12:00',
                        },
                    },
                    {
                        id: 2,
                        date: '今天',
                        time: '下午',
                        value: {
                            start: '12:00',
                            end: '18:00',
                        },
                    },
                    {
                        id: 3,
                        date: '今天',
                        time: '晚上',
                        value: {
                            start: '18:00',
                            end: '24:00',
                        },
                    },
                ],
                timeOfDay: '',
                reserveTime: {
                    start: '',
                    end: '',
                },
                reserveTimeRange: ['', ''],
                timeRangePickerOptions: {
                    disabledMinTime(time) {
                        return time <= '06:00';
                    },

                    disabledMaxTime(time) {
                        return time <= '06:00';
                    },

                    timeRange: {
                        begin: '06:00',
                        end: '23:00',
                    },
                },
                serviceType: 0, // 预约时间段类型 固定号源预约模式服务类型；0：按上午/下午/晚上; 1:按精确时间段预约; 2:按自定义时段
                registrationCount: [],
                buttonLoading: false,
                revisitStatus: 1, // 初诊: 1, 复诊: 2
                simpleMedicalRecord: {},
                isGenerateOrderNoOnSignDisplayNoStr: '',
                isStopDiagnose: false,
            };
        },
        watch: {
            selectedDoctorId: {
                handler(val) {
                    if (!val) {
                        this.selectedDoctorId = '';
                        this.selectedDoctor = '';
                    }
                },
            },
        },
        computed: {
            ...mapGetters([
                'isEnableRegUpgrade',
                'registrationsConfig',
                'appointmentConfig',
            ]),
            ...mapGetters('viewDistribute',[
                'viewComponents',
                'viewDistributeConfig',
            ]),
            // 未排班是否可以挂号
            unscheduledCanRegistration() {
                return !this.registrationsConfig.noneScheduleDisableRegistration;
            },
            currentMedicalRecord() {
                return this.viewComponents.medicalRecord;
            },
            switchSetting() {
                const medicalRecord = {
                    chiefComplaint: '1',
                    diagnosis: '1',
                };
                return getMedicalRecordStruct(medicalRecord);
            },
            todayTime() {
                return formatDate(new Date(), 'YYYY-MM-DD');
            },
            orderNoAndTime: {
                get() {
                    return this.orderNoAndTimeStr;
                },
                set(val) {
                    if (!val) {
                        this.orderNoAndTimeStr = '';
                        return;
                    }
                    this.orderNoAndTimeStr = val;
                    const arr = val.split('-') || [];
                    if (!this?.reserveTime) {
                        this.reserveTime = {
                            start: '',
                            end: '',
                        };
                    }
                    this.reserveTime = {
                        start: arr[1] || '',
                        end: arr[2] || '',
                    };
                    this.orderNo = +arr[0] || '';
                    this.orderNoType = +arr[3] || 0;
                    this.selectedOrderNoTimeOfDay = arr[4] || '';
                },
            },
            // 升级的诊所管家/口腔管家
            isRegUpgrade() {
                return this.isEnableRegUpgrade || this.viewDistributeConfig.Settings.reservation.fetchReservationNotFromAPI;
            },
            isFixOrderMode() {
                if (this.isRegUpgrade) {
                    // modeType 0: 固定号源模式 1: 灵活时间模式
                    return this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
                }
                // 普通诊所管家及眼科只有固定号源模式
                return true;
            },
            // 固定模式 - 分段&开启签到取号
            isGenerateOrderNoOnSign() {
                const {
                    fixedOrderDisplayServiceType, generateOrderNoTime,
                } = this.registrationsConfig || {};
                return this.isFixOrderMode && fixedOrderDisplayServiceType === RESERVATION_TIME_TYPE.OTHER && generateOrderNoTime === GENERATE_ORDER_NO_TIME_TYPE.SIGN_IN;
            },
            // 创建时挂号是否展示号数
            showDescription() {
                return this.isGenerateOrderNoOnSign && this.isRegistration;
            },
            isAccurateTime() {
                return this.serviceType === 1;
            },
            doctors() {
                if (!this.keywords) {
                    return this.doctorList;
                }
                return this.doctorList.filter((item) => {
                    return (
                        (item.doctorName && item.doctorName.indexOf(this.keywords) > -1) ||
                        (item.doctorName && item.doctorName.toLocaleLowerCase().indexOf(this.keywords) > -1) ||
                        (item.doctorNamePy && item.doctorNamePy.toLocaleLowerCase().indexOf(this.keywords) > -1) ||
                        (item.doctorNamePyFirst && item.doctorNamePyFirst.toLocaleLowerCase().indexOf(this.keywords) > -1)
                    );
                });
            },
            todayDate() {
                return formatDate(new Date());
            },
            // 当天是挂号类型, 未来日期是预约类型
            isRegistration() {
                return this.todayDate === this.selectDate;
            },
            visible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            visitSourceRemark() {
                const arr = [];
                let chiefComplaintStr = this.simpleMedicalRecord.chiefComplaint?.trim();
                if (chiefComplaintStr) {
                    chiefComplaintStr = chiefComplaintStr.replace(/<br>/g,'');
                    arr.push(`[主诉] ${chiefComplaintStr}`);
                }

                const extendDiagnosisInfos = this.simpleMedicalRecord.extendDiagnosisInfos?.[0]?.value;
                if (extendDiagnosisInfos?.length) {
                    let diagnosisStr = extendDiagnosisInfos?.map((item) => item?.name).join(',');
                    if (diagnosisStr) {
                        diagnosisStr = diagnosisStr.replace(/<br>/g,'');
                        arr.push(` [初步诊断] ${diagnosisStr}`);
                    }
                }

                let remark = this.remark?.trim();
                if (remark) {
                    remark = remark.replace(/<br>/g,'');
                    arr.push(` [转诊原因/备注] ${remark}`);
                }
                return arr.join(' ');
            },
            isShowRegistrationCategory() {
                return this.viewDistributeConfig.Settings.schedule.isShowRegistrationCategory;
            },
            registrationCategoryOptions() {
                const arr = [{
                    label: '普通门诊',
                    value: RegistrationCategory.ORDINARY,
                },{
                    label: '专家门诊',
                    value: RegistrationCategory.SPECIALIST,
                }];
                // 预约无便民门诊
                if (this.isRegistration) {
                    arr.push({
                        label: '便民门诊',
                        value: RegistrationCategory.CONVENIENCE,
                    });
                }
                return arr;
            },
        },

        created() {
            this.fetchReservation();
            this.getDoctorSchedule(this.selectDate);
            const postData = clone(this.postData.medicalRecord);
            this.simpleMedicalRecord = {
                chiefComplaint: postData.chiefComplaint,
                extendDiagnosisInfos: postData.extendDiagnosisInfos,
            };
        },

        methods: {
            async fetchReservation() {
                if (this.isRegUpgrade) {
                    this.serviceType = this.registrationsConfig.serviceType;
                } else {
                    try {
                        await this.$store.dispatch('initAppointmentConfig');
                        const data = Clone(this.appointmentConfig);
                        this.serviceType = data.serviceType;
                    } catch (e) {
                        console.error(e);
                    }
                }
            },
            async getDoctorSchedule(date) {
                try {
                    this.loading = true;
                    const { data } = await RegistrationsAPI.getDoctorsSchedules({
                        date,
                        registrationCategory: this.isShowRegistrationCategory ? this.selectRegistrationCategory : RegistrationCategory.ORDINARY,
                    });
                    this.loading = false;
                    this.doctorList = data.doctors;
                } catch (e) {
                    console.error(e);
                }
            },
            clearSelected() {
                this.resetReserveDate();
                this.selectedDoctor = '';
                this.selectedDoctorId = '';
            },
            onDateChange(val) {
                this.clearSelected();
                this.getDoctorSchedule(val);
            },
            disabledRadio(item) {
                // 预约类型 - 未排班/完诊医生不可选
                /**
                 * 固定号源模式
                 */
                if (this.isFixOrderMode) {
                    // 未排班可以挂号
                    if (this.unscheduledCanRegistration) {
                        return !this.isRegistration && (!item.onSchedule || !item.restCount);
                    }
                    // 未排班不可以挂号
                    // 挂号有排班没有号源了则可以算号
                    if (this.isRegistration) {
                        return !item.onSchedule;
                    }
                    // 预约没有排班或者没有号源了都不可以
                    return !item.onSchedule || !item.restCount;
                }
                /**
                 * 灵活时间模式
                 */
                // 未排班可以挂号预约
                if (this.unscheduledCanRegistration) {
                    return !this.isRegistration && !item.canReserve;
                }
                // 未排班不可以挂号
                return !item.onSchedule || !item.canReserve;
            },
            resetReserveDate() {
                if (this.isFixOrderMode) {
                    this.orderNoAndTime = '';
                    this.orderNoAndTimeOptions = [];
                    if (!this.isRegistration) {
                        // 预约
                        this.reserveTime = {
                            start: '',
                            end: '',
                        };
                    } else {
                        // 挂号
                        const { timeOptionId } = this;
                        for (let i = 0; i < this.timeOptions.length; i++) {
                            const option = this.timeOptions[i];
                            if (option.id === timeOptionId) {
                                this.timeOfDay = option.time;
                                this.reserveTime.start = option.value.start;
                                this.reserveTime.end = option.value.end;
                                break;
                            }
                        }
                        this.selectDate = formatDate(new Date(), 'YYYY-MM-DD');
                    }
                    // 计算的号数也要置空
                    this.calculateOrderNo = null;
                }
            },
            handleChangeReserveTimeRange(val) {
                this.reserveTime.start = val[0];
                this.reserveTime.end = val[1];
            },
            async selectDoctor(item) {
                if (this.disabledRadio(item)) {
                    return;
                }
                if (this.selectedDoctorId === item.doctorId + item.departmentId) {
                    this.selectedDoctor = '';
                    this.selectedDoctorId = '';
                    return;
                }
                this.resetReserveDate();
                this.selectedDoctor = item;
                this.selectedDoctorId = item.doctorId + item.departmentId;
                if (this.isFixOrderMode) {
                    // 拉取医生排班
                    this.handleRegistrationTimeOfDay();
                    await this.fetchDoctorShifts();
                    await this.getOrderNoAndTimeOptions();
                } else {
                    await this.getVisitStatus();
                    this.handleTimeRangePickerOptions();
                }
                await this.getDailyReserveStatus();
            },
            async getVisitStatus() {
                try {
                    const params = {
                        doctorId: this.selectedDoctor.doctorId,
                        patientId: this.postData.patient.id,
                    };
                    const { data } = await OutpatientAPI.getPatientVisitStatus(params);
                    this.revisitStatus = data.revisitStatus;
                } catch (e) {
                    console.log('getVisitStatus Error');
                }
            },
            async handleTimeRangePickerOptions() {
                const {
                    hour: visitServiceHour ,min: visitServiceMin,
                } = this.registrationsConfig?.serviceDuration?.visitServiceDuration || {};
                const {
                    hour: revisitedServiceHour ,min: revisitedServiceMin,
                } = this.registrationsConfig?.serviceDuration?.revisitedServiceDuration || {};
                const visitServiceDuration = visitServiceHour * 60 + visitServiceMin; // 初诊时长
                const revisitedServiceDuration = revisitedServiceHour * 60 + revisitedServiceMin; // 复诊时长
                const timeRangeEnd = this.revisitStatus === 1 ? visitServiceDuration : revisitedServiceDuration;
                await this.getFlexibleTimeCount();
                this.setReserveTime(timeRangeEnd);
            },
            async getFlexibleTimeCount() {
                try {
                    if (!this.selectedDoctor.departmentId) return;
                    const data = await BoardApi.getFlexibleTimeCount({
                        departmentId: this.selectedDoctor.departmentId,
                        doctorId: this.selectedDoctor.doctorId,
                        start: '06:00',
                        end: '23:00',
                        // 0 门诊  1 治疗理疗
                        registrationType: 0 ,
                        reserveDate: this.selectDate,
                    });
                    this.registrationCount = data || [];
                } catch (e) {
                    console.log('fetch getFlexibleTimeCount error');
                }
            },
            setReserveTime(init,timeRangeEnd) {
                const timeRange = ['06:00', '23:00'];
                const isToday = this.selectDate === this.todayTime;
                let reserveTimeRange = [];
                if (isToday) {
                    reserveTimeRange = this.handleReserveTimeRange(timeRangeEnd);
                } else {
                    const endTime = this.getReserveEndTime('08:00',timeRangeEnd);
                    reserveTimeRange = ['08:00',endTime];
                }
                const disabledTimeList = [];
                this.registrationCount.forEach((item) => {
                    if (item.isStopDiagnose) disabledTimeList.push(item.localTime);
                });
                this.timeRangePickerOptions = isToday ? ({
                    disabledMinTime(time) {
                        if (reserveTimeRange[0] === '23:00') {
                            return time <= reserveTimeRange[0];
                        }
                        return time < reserveTimeRange[0] || disabledTimeList.includes(time);
                    },

                    disabledMaxTime(time) {
                        return time <= reserveTimeRange[0] || disabledTimeList.includes(time);
                    },

                    timeRange: {
                        begin: timeRange[0],
                        end: timeRange[1],
                    },
                }) : ({
                    disabledMinTime(time) {
                        return disabledTimeList.includes(time);
                    },

                    disabledMaxTime(time) {
                        return disabledTimeList.includes(time);
                    },

                    timeRange: {
                        begin: timeRange[0],
                        end: timeRange[1],
                    },
                });
                this.reserveTimeRange = reserveTimeRange;
                // 计算后的时间若已停诊则置空
                if (disabledTimeList.some((disTime) => this.getMinutesByTime(reserveTimeRange[0]) <= this.getMinutesByTime(disTime) && this.getMinutesByTime(disTime) <= this.getMinutesByTime(reserveTimeRange[1]))) this.reserveTimeRange = ['', ''];
                this.reserveTime.start = this.reserveTimeRange[0];
                this.reserveTime.end = this.reserveTimeRange[1];
            },
            getMinutesByTime(str = '') {
                if (!str && typeof str !== 'string') return;
                const timeArr = str.split(':');
                return +timeArr[0] * 60 + +timeArr[1];
            },
            handleReserveTimeRange(endTime) {
                const date = new Date();
                const hours = new Date().getHours();
                const minutes = new Date().getMinutes();
                let start = '';
                let end = '';
                const arr = [0,15,30,45];
                if (hours < 6) {
                    date.setHours(6);
                    date.setMinutes(0);
                    start = parseTime(date,'h:i',true);
                    end = this.getReserveEndTime(start,endTime);
                } else if (hours < 23) {
                    if (minutes > 45) {
                        date.setHours(hours + 1);
                        date.setMinutes(arr[0]);
                        start = parseTime(date,'h:i',true);
                        end = this.getReserveEndTime(start,endTime);
                    } else if (minutes > 30) {
                        date.setMinutes(arr[arr.length - 1]);
                        start = parseTime(date,'h:i',true);
                        date.setHours(hours + 1);
                        end = this.getReserveEndTime(start,endTime);
                    } else {
                        for (const i in arr) {
                            if (minutes <= arr[i]) {
                                date.setMinutes(arr[i]);
                                start = parseTime(date,'h:i',true);
                                end = this.getReserveEndTime(start,endTime);
                                break;
                            }
                        }
                    }
                } else {
                    date.setHours(23);
                    date.setMinutes(0);
                    start = parseTime(date,'h:i',true);
                    end = this.getReserveEndTime(start,endTime);
                }
                return [start,end];
            },
            getReserveEndTime(start,end) {
                // 最大时间不超过23:00
                const endTime = this.baseTimeAddMinutes(start,end);
                const endTimeArr = endTime.split(':');
                const maxSeconds = 23 * 60 * 60;
                const endTimeSeconds = endTimeArr[0] * 60 * 60 + endTimeArr[1] * 60;
                return endTimeSeconds <= maxSeconds ? endTime : '23:00';
            },
            baseTimeAddMinutes(baseTime = '8:00', plusMinutes = 30) {
                const baseDate = new Date();
                const [hours, minutes] = baseTime.split(':');
                baseDate.setHours(+hours);
                baseDate.setMinutes(+minutes);
                const newDate = new Date(baseDate.getTime() + plusMinutes * 60 * 1000);
                if (newDate.getHours() < baseDate.getHours()) {
                    newDate.setHours(23);
                    newDate.setMinutes(0);
                }
                return parseTime(newDate, 'h:i',true);
            },
            getRegistrationCount(option) {
                const item = this.registrationCount.find((item) => item.localTime === option.label);
                return item?.count || 0;
            },
            handleRegistrationTimeOfDay() {
                const hours = new Date().getHours();
                if (hours >= 0 && hours < 12) {
                    this.timeOptionId = this.timeOptions[0].id;
                    this.timeOfDay = '上午';
                } else if (hours >= 12 && hours < 18) {
                    this.timeOptionId = this.timeOptions[1].id;
                    this.timeOfDay = '下午';
                } else {
                    this.timeOptionId = this.timeOptions[2].id;
                    this.timeOfDay = '晚上';
                }
            },
            async changeTimeOfDay(val) {
                this.orderNoAndTime = '';
                this.orderNoAndTimeOptions = [];
                for (let i = 0; i < this.timeOptions.length; i++) {
                    const option = this.timeOptions[i];
                    if (option.id === val) {
                        this.timeOfDay = option.time;
                        this.reserveTime.start = option.value.start;
                        this.reserveTime.end = option.value.end;
                        break;
                    }
                }
                await this.getOrderNoAndTimeOptions();
            },
            // 获取科室医生排班表
            async fetchDoctorShifts() {
                try {
                    const params = {
                        doctorId: this.selectedDoctor.doctorId,
                        departmentId: this.selectedDoctor.departmentId,
                        workingDate: this.selectDate,
                        registrationCategory: this.isShowRegistrationCategory ? this.selectRegistrationCategory : RegistrationCategory.ORDINARY,
                    };
                    const { data } = await RegistrationsAPI.fetchDoctorShiftsByDate(params);
                    const registrationCategoryScheduleIntervals = data?.registrationCategoryScheduleIntervals || [];
                    this.doctorNoInfoList = registrationCategoryScheduleIntervals.find((item) => item.registrationCategory === 0)?.scheduleIntervals || [];
                    return this.doctorNoInfoList;
                } catch (e) {
                    console.error('fetchData', e);
                }
            },
            async getOrderNoAndTimeOptions() {
                let timeOfDayList = [];
                let currentTimeList = [];
                const timeOptions = ['上午', '下午', '晚上'];
                timeOfDayList = timeOptions.map((timeOfDay) => {
                    const doctorNoInfoList = Clone(this.doctorNoInfoList);
                    const timeOfDayArr = doctorNoInfoList?.filter((item) => item.timeOfDay === timeOfDay) || [];
                    // 根据当前选择时段 判断是否停诊 处理展示已停诊信息
                    if (timeOfDay === this.timeOfDay) {
                        if (timeOfDayArr.length > 0) {
                            this.isStopDiagnose = timeOfDayArr.every((item) => item.isStopDiagnose === 1);
                        } else {
                            this.isStopDiagnose = false;
                        }
                    }
                    let allNoList = [];
                    if (this.isGenerateOrderNoOnSign) {
                        timeOfDayArr.forEach((item) => {
                            if (item.availableCellTypes?.length) {
                                const availableList = item.list.filter((it) => it.available) ;
                                const normalNoCount = availableList.filter((it) => !it.type).length;
                                const xianNoCount = availableList.filter((it) => it.type === 1).length;
                                const vipNoCount = availableList.filter((it) => it.type === 2).length;

                                item.availableCellTypes.forEach((type) => {
                                    allNoList.push({
                                        orderNo: '',
                                        start: item.start,
                                        end: item.end,
                                        timeOfDay: item.timeOfDay,
                                        type,
                                        signInTimeFirstEnableUseOrderNo: item.signInTimeFirstEnableUseOrderNo,
                                        residueCount: [normalNoCount, xianNoCount, vipNoCount][type],
                                    });
                                });
                            }
                        });
                        return allNoList;
                    }
                    if (this.isAccurateTime) {
                        allNoList = timeOfDayArr.map((item) => item.list).flat();
                    } else {
                        allNoList = timeOfDayArr.map((item) => {
                            item.list?.forEach((it) => {
                                it.orderNoStart = it.start;
                                it.orderNoEnd = it.end;
                                it.start = item.start;
                                it.end = item.end;
                            });
                            return item.list;
                        }).flat();
                    }
                    return allNoList?.filter((item) => item.available) || [];
                });

                if (!this.isRegistration && !this.timeOfDay) {
                    // 预约类型
                    let index = 0;
                    for (let i = 0; i < timeOfDayList.length; i++) {
                        if (timeOfDayList[i].length) {
                            index = i;
                            break;
                        }
                    }
                    this.timeOptionId = this.timeOptions[index].id;
                    this.timeOfDay = timeOptions[index];
                    currentTimeList = timeOfDayList[index];
                } else {
                    // 挂号类型
                    const index = timeOptions.findIndex((item) => item === this.timeOfDay);
                    currentTimeList = timeOfDayList[index] ?? [];
                }

                this.orderNoPlaceholderStr = '';

                if (currentTimeList.length) {
                    this.orderNoAndTime = `${currentTimeList[0].orderNo}-${currentTimeList[0].start}-${currentTimeList[0].end}-${currentTimeList[0].type || 0}-${currentTimeList[0].timeOfDay}`;
                    this.orderNoAndTimeOptions = currentTimeList;
                } else {
                    // 没有选择上午、下午、晚上 return
                    if (!this.timeOfDay) return;
                    if (this.reserveTime.start === '' || this.reserveTime.end === '') {
                        for (let i = 0; i < this.timeOptions.length; i++) {
                            const option = this.timeOptions[i];
                            if (option.time === this.timeOfDay) {
                                this.reserveTime.start = option.value.start;
                                this.reserveTime.end = option.value.end;
                                break;
                            }
                        }
                    }
                    const cacheDoctorNoInfoList = Clone(this.doctorNoInfoList);
                    const listByDay = cacheDoctorNoInfoList.find((item) => item.timeOfDay === this.timeOfDay)?.list || [];
                    if (!this.unscheduledCanRegistration && !listByDay.length) {
                        // 如果未排班不可挂号, 且当前时间段内未排班, 则不算号
                        return;
                    }
                    await this.calcOrderNo();
                    this.$nextTick(() => {
                        if (this.calculateOrderNo) {
                            const {
                                orderNo = '',
                                reserveTime = {
                                    start: '',
                                    end: '',
                                    orderNoStart: '',
                                    orderNoEnd: '',
                                },
                                type = 0,
                                timeOfDay = '',
                            } = this.calculateOrderNo;

                            const orderNoNum = !this.isGenerateOrderNoOnSign ? orderNo : '';
                            if (this.selectDate) {
                                this.orderNoAndTime = `${orderNoNum}-${reserveTime.start}-${reserveTime.end}-${type}-${timeOfDay}`;
                            }
                            this.orderNoAndTimeOptions = [{
                                orderNo: orderNoNum,
                                start: reserveTime.start,
                                end: reserveTime.end,
                                orderNoStart: reserveTime.orderNoStart,
                                orderNoEnd: reserveTime.orderNoEnd,
                                type: 0,
                                timeOfDay,
                                signInTimeFirstEnableUseOrderNo: orderNo,
                            }];
                        }
                    });
                }

                if (this.isRegistration) {
                    this.$nextTick(() => {
                        if (this.showDescription) {
                            const arr = this.orderNoAndTime?.split('-') || [];
                            const selectedItem = this.orderNoAndTimeOptions.find((item) => item.start === arr[1] && item.end === arr[2] && +item.type === +arr[3]);

                            if (selectedItem) {
                                this.isGenerateOrderNoOnSignDisplayNoStr = selectedItem.signInTimeFirstEnableUseOrderNo ? `${(`${selectedItem.signInTimeFirstEnableUseOrderNo}`).padStart(2, '0')}号` : '';
                            }
                        }
                    });
                }
            },
            // 无排班，挂号类型算号
            async calcOrderNo() {
                try {
                    // 预约不去算号
                    if (!this.isRegistration) {
                        this.calculateOrderNo = null;
                        this.orderNoPlaceholderStr = '无可用号源';
                        return;
                    }
                    const { data } = await RegistrationsAPI.calcOrderNo({
                        registrationFormItem: {
                            departmentId: this.selectedDoctor.departmentId,
                            doctorId: this.selectedDoctor.doctorId,
                            reserveTime: this.reserveTime,
                            reserveDate: this.selectDate,
                            isReserved: 0,
                            registrationCategory: this.selectRegistrationCategory,
                        },
                        registrationType: this.registrationType,
                    });
                    this.calculateOrderNo = data;
                } catch (err) {
                    this.calculateOrderNo = null;
                    // 无可用号源
                    if (err?.code === 15125) {
                        this.orderNoPlaceholderStr = '无可用号源';
                    } else {
                        this.$Toast({
                            type: 'error',
                            message: err.message,
                        });
                        this.orderNoPlaceholderStr = '无可用号源';
                    }
                }
            },
            async getDailyReserveStatus() {
                try {
                    if (!this.selectedDoctor.departmentId) return;
                    const arr = this.selectDate.split('-');
                    const year = +arr[0];
                    const month = +arr[1] - 1;
                    const monthStartDate = new Date(year,month,1);
                    const monthEndDate = new Date(year,month + 1,0);
                    const data = await BoardApi.getDailyReserveStatus({
                        departmentId: this.selectedDoctor.departmentId,
                        doctorId: this.selectedDoctor.doctorId,
                        start: formatDate(monthStartDate, 'YYYY-MM-DD'),
                        end: formatDate(monthEndDate, 'YYYY-MM-DD'),
                        registrationType: 0 ,
                        isRevisited: 0,
                    });
                    this.describeList = data?.map((item) => {
                        const describeClass = item.status === 6 ? 'describe-stop-work' : ''; // 停诊
                        if (item.status !== 5 && item.date >= this.todayTime) {
                            return ({
                                date: item.date,
                                describe: item.statusName,
                                describeClass,
                            });
                        }
                    })?.filter(Boolean);
                } catch (e) {
                    console.log('fetch getDailyReserveStatus error');
                }
            },
            getLabelStr(item) {
                if (this.isGenerateOrderNoOnSign) {
                    return `${item.start} ~ ${item.end}`;
                }
                return `${(`${item.orderNo}` + '').padStart(2,'0')}号 ${item.start} ~ ${item.end}`;
            },
            clickOrderNoAndTimeOption(item) {
                const { signInTimeFirstEnableUseOrderNo } = item;
                this.isGenerateOrderNoOnSignDisplayNoStr = signInTimeFirstEnableUseOrderNo ? `${(`${signInTimeFirstEnableUseOrderNo}`).padStart(2, '0')}号` : '';
            },
            async confirm() {
                if (!this.selectedDoctor.doctorId) {
                    this.$Toast({
                        message: '请选择要转诊的医生',
                        type: 'error',
                    });
                    return;
                }
                this.$refs.outpatientReferralForm.validate(async (valid) => {
                    const {
                        patient,
                        patientOrderId,
                    } = this.postData;
                    const {
                        departmentId,
                        departmentName,
                        doctorId,
                        doctorName,
                    } = this.selectedDoctor;
                    // isReserved: 0-挂号， 1-预约
                    const isReserved = this.isRegistration ? 0 : 1;
                    const isCalculateNo = +this.calculateOrderNo?.orderNo === +this.orderNo;
                    const postDataOrderNo = (isCalculateNo || this.isGenerateOrderNoOnSign) ? '' : +this.orderNo;

                    const params = {
                        departmentId,
                        departmentName,
                        doctorId,
                        doctorName,
                        isReserved,
                        ...(this.isFixOrderMode && { orderNo: postDataOrderNo }),
                        orderNoType: this.orderNoType,
                        patientId: patient.id,
                        referralPatientOrderId: patientOrderId,
                        reserveDate: this.selectDate,
                        reserveTime: this.reserveTime,
                        visitSourceRemark: this.visitSourceRemark,
                        registrationCategory: this.selectRegistrationCategory,
                    };
                    // 固定模式-自定义时段&非签到取号
                    if (this.isFixOrderMode && this.registrationsConfig?.serviceType !== SERVICE_TYPE_ENUM.ACCURATE_TIME && !this.isGenerateOrderNoOnSign) {
                        const selectedItem = this.orderNoAndTimeOptions.find((item) => {
                            const {
                                orderNo,
                                start,
                                end,
                                type,
                                timeOfDay,
                            } = item;
                            const {
                                orderNo: postDataOrderNo,
                                reserveTime,
                                orderNoType,
                            } = this;

                            return +orderNo === +postDataOrderNo && start === reserveTime.start && end === reserveTime.end && +type === +orderNoType && timeOfDay === this.selectedOrderNoTimeOfDay;
                        });
                        params.reserveTime.orderNoStart = selectedItem?.orderNoStart || '';
                        params.reserveTime.orderNoEnd = selectedItem?.orderNoEnd || '';
                    }
                    if (valid) {
                        this.buttonLoading = true;
                        try {
                            await RegistrationsAPI.referralManage(params);
                            this.buttonLoading = false;
                            this.visible = false;
                            this.$Toast({
                                message: '转诊成功',
                                type: 'success',
                            });
                        } catch (e) {
                            console.error(e);
                        } finally {
                            this.buttonLoading = false;
                        }
                    } else {
                        console.log('校验失败');
                    }
                });

            },
            changeRegistrationCategory(value,index, oldVal) {
                if (value === oldVal) return;
                this.clearSelected();
                this.getDoctorSchedule(this.selectDate);
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/theme";
    @import "src/styles/mixin";

    .outpatient-referral-dialog {
        .dialog-content {
            padding: 24px;

            .header-wrapper {
                display: flex;
                align-items: center;

                .refer-ral-info {
                    margin-left: 40px;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 16px;
                    color: $T2;

                    .time {
                        margin-left: 12px;
                    }
                }
            }

            .medical-record-wrapper .medical-record-item {
                label:not(.abc-checkbox-wrapper) {
                    width: 114px;
                }

                &:first-child {
                    .abc-input__inner {
                        border-radius: 0 var(--abc-border-radius-small) 0 0;
                    }
                }

                &:last-child {
                    .abc-input__inner {
                        border-radius: 0 0 var(--abc-border-radius-small) 0;
                    }
                }
            }

            .reservation-info {
                margin-top: 16px;

                >h4 {
                    font-size: 14px;
                    font-weight: bold;
                }

                .filter-wrap {
                    display: flex;
                    margin: 12px 0;
                }

                .doctor-list {
                    max-height: 270px;
                    overflow-y: auto;
                    overflow-y: overlay;
                    border: 1px solid #ced0da;
                    border-radius: var(--abc-border-radius-small);

                    @include scrollBar(true);

                    > li {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        height: 36px;
                        padding: 0 12px;
                        cursor: pointer;

                        &.disable {
                            cursor: not-allowed;
                        }

                        & + li {
                            border-top: 1px solid #e6eaee;
                        }

                        &:hover,
                        &.selected {
                            background: #d4e7fd;

                            .select-order-no {
                                .time-of-day-wrapper,
                                .order-no-and-time-wrapper,
                                .date-time-range-wrapper {
                                    font-size: 13px;
                                    background-color: $S2;
                                    border-radius: var(--abc-border-radius-small);
                                }

                                .time-of-day-wrapper {
                                    border-radius: 4px 0 0 4px;
                                }
                            }
                        }

                        .doctor-name {
                            .abc-radio-label {
                                width: 100px;

                                @include ellipsis;
                            }

                            &.gray {
                                .abc-radio-label {
                                    color: $T2;
                                }
                            }
                        }

                        .schedule-periods {
                            flex: 1;
                            margin-left: 44px;
                            font-size: 12px;
                            color: $T2;
                        }

                        .select-order-no {
                            display: flex;
                            align-items: center;
                            justify-content: end;
                            width: 189px;
                            font-size: 0;

                            .abc-form-item {
                                margin: 0;

                                .time-of-day-wrapper {
                                    .abc-input__inner {
                                        border-right: transparent;
                                        border-top-right-radius: 0;
                                        border-bottom-right-radius: 0;
                                    }
                                }

                                .order-no-and-time-wrapper {
                                    .abc-input__inner {
                                        padding: 4px 8px;
                                        border-top-left-radius: 0;
                                        border-bottom-left-radius: 0;
                                    }
                                }

                                .abc-form-item-content {
                                    .show-description {
                                        .abc-input__inner {
                                            padding-left: 42px;
                                        }
                                    }
                                }
                            }
                        }

                        .rest-count {
                            width: 189px;
                            text-align: right;

                            >span {
                                display: inline-block;
                                width: 20px;
                                margin-left: 8px;
                                text-align: left;
                            }

                            .yellow {
                                color: $Y2;
                            }
                        }
                    }
                }
            }
        }
    }

    .describe-stop-work {
        color: var(--abc-color-Y2) !important;
    }
</style>
