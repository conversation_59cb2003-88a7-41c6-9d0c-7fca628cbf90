<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        :content-styles="contentStyles"
        custom-class="registration-board-dialog-wrapper"
        :auto-focus="false"
        title="预约看板"
        disabled-keyboard
    >
        <template #title>
            <div class="registartion-board-handler">
                <abc-dropdown
                    placement="bottom-start"
                    class="dropdown-item"
                >
                    <div slot="reference" class="view-mode-reference">
                        <span class="view-mode">
                            {{ isBoardViewMode ? '预约看板' : '预约列表' }}
                        </span>
                        <abc-icon icon="dropdown_triangle" style="color: #aab4bf;" size="12"></abc-icon>
                    </div>
                    <div>
                        <abc-dropdown-item style="padding: 0;">
                            <div style="display: flex; align-items: center; justify-content: space-between; padding: 6px 12px;" @click="handleChangeViewMode(VIEW_MODE_ENUM.BOARD)">
                                <span>预约看板</span>
                            </div>
                        </abc-dropdown-item>

                        <abc-dropdown-item style="padding: 0;">
                            <div style="display: flex; align-items: center; justify-content: space-between; padding: 6px 12px;" @click="handleChangeViewMode(VIEW_MODE_ENUM.LIST)">
                                <span>预约列表</span>
                            </div>
                        </abc-dropdown-item>
                    </div>
                </abc-dropdown>
                <abc-space>
                    <div v-if="isBoardViewMode" @click="handleClickBoardView">
                        <abc-tabs-v2
                            v-model="boardViewMode"
                            :option="boardViewModeOptions"
                            size="middle"
                            type="outline"
                            :item-min-width="40"
                        >
                        </abc-tabs-v2>
                    </div>

                    <abc-date-picker
                        v-model="cursorDate"
                        type="date"
                        placeholder="预约日期"
                        :width="132"
                        :clearable="false"
                    ></abc-date-picker>

                    <abc-select
                        v-if="selectedDoctor"
                        :value="selectedDoctor.doctor.id"
                        :width="110"
                        placeholder="全部医生"
                        @change="handleChangeDoctor"
                    >
                        <abc-option
                            v-for="it in doctorList"
                            :key="it.doctor.id"
                            :value="it.doctor.id"
                            :label="it.doctor.name"
                        ></abc-option>
                    </abc-select>

                    <registration-board-search
                        v-if="isBoardViewMode"
                        :width="260"
                        :fetch-board-view-suggestions="fetchBoardViewSuggestions"
                        :handle-board-view-autocomplete-input-select="handleBoardViewAutocompleteInputSelect"
                    ></registration-board-search>
                    <abc-input
                        v-if="isListViewMode"
                        v-model.trim="params.keyword"
                        type="text"
                        :width="200"
                        :placeholder="searchInputPlaceholder"
                        :icon="isInSearch ? 'cis-icon-cross_small' : ''"
                        @focus="handleQLSearchFocus"
                        @blur="handleQLSearchBlur"
                        @icon-click="clearInput"
                        @input="inputHandler"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                    </abc-input>

                    <abc-button
                        v-if="isCanModifyOutpatientRegistration"
                        class="add-registration-btn"
                        type="success"
                        @click="() => { handleClickNewRegistrationButton() }"
                    >
                        新增预约
                    </abc-button>

                    <abc-dropdown
                        v-if="isListViewMode"
                        style="width: 32px;"
                        :min-width="130"
                        :max-width="150"
                        placement="bottom-end"
                    >
                        <div slot="reference" class="view-setting-box">
                            <abc-button type="blank">
                                <abc-icon icon="set" size="14" color="#007AFF"></abc-icon>
                            </abc-button>
                        </div>
                        <div>
                            <abc-dropdown-item style="padding: 0;">
                                <div
                                    style="display: flex; align-items: center; justify-content: space-between; padding: 6px 12px;"
                                    @click="handleOpenCustomHeaderDialog"
                                >
                                    <span>设置展示字段</span>
                                    <abc-icon icon="Arrow_Rgiht"></abc-icon>
                                </div>
                            </abc-dropdown-item>
                        </div>
                    </abc-dropdown>
                </abc-space>
            </div>
        </template>

        <div class="registartion-board-amount">
            <div class="registartion-board-amount-item">
                <div class="registartion-board-amount-label">
                    总数
                </div>
                <div class="registartion-board-amount-value">
                    {{ statistics.notRefundedCount || 0 }}
                </div>
            </div>
            <div class="registartion-board-amount-item">
                <div class="registartion-board-amount-label">
                    预约
                </div>
                <div class="registartion-board-amount-value">
                    {{ statistics.reserveTotalCount || 0 }}
                </div>
            </div>
            <div class="registartion-board-amount-item">
                <div class="registartion-board-amount-label">
                    签到
                </div>
                <div class="registartion-board-amount-value">
                    {{ statistics.hadSignInCount || 0 }}
                </div>
            </div>
            <div class="registartion-board-amount-item">
                <div class="registartion-board-amount-label">
                    未签
                </div>
                <div class="registartion-board-amount-value">
                    {{ statistics.waitingSignInCount || 0 }}
                </div>
            </div>
            <div class="registartion-board-amount-item">
                <div class="registartion-board-amount-label">
                    取消
                </div>
                <div class="registartion-board-amount-value">
                    {{ statistics.refundedCount || 0 }}
                </div>
            </div>

            <div v-if="isBoardWeekViewMode" class="registartion-board-amount-time">
                {{ parseTime(timeRange[0], 'y-m-d', true) }} ~ {{ parseTime(timeRange[1], 'y-m-d', true) }}
            </div>
        </div>

        <!--board-view需要预知高度才能计算 style设置为calc(100vh - 234px)-->
        <div v-if="isBoardViewMode && boardDoctorList.length" class="registration-board-view" style="height: calc(633px - 36px);">
            <board-view
                ref="borderView"
                form-source="registration-board-dialog"
                :is-board-day-view-mode="isBoardDayViewMode"
                :is-board-week-view-mode="isBoardWeekViewMode"
                :is-single-doctor="true"
                :board-view-source="boardViewSource"
                :doctor-list="boardDoctorList"
                :time-range="timeRange"
                :has-departments="hasDepartments"
                :departments="selectedDoctorDepartments"
                hide-single-doctor-header
                style="height: 100%;"
                :is-can-modify-registration-info="isCanModifyOutpatientRegistration"
                @finish-registration="handleFinishRegistrationInfo"
                @board-view-registration-update="handleBoardViewRegistrationUpdate"
                @new-add="handleNewAdd"
                @refresh="refreshRegistrations"
                @board-view-statistics-update="handleBoardViewStatisticsUpdate"
            ></board-view>
        </div>

        <div v-if="isListViewMode" class="registration-board-view">
            <div class="table-wrapper">
                <table-list
                    :height="tableHeight"
                    :table-list-loading="loading"
                    :has-departments="hasDepartments"
                    :departments="selectedDoctorDepartments"
                    :table-header="tableHeader"
                    :table-list-data="tableData"
                    :is-can-modify-outpatient-registration="isCanModifyOutpatientRegistration"
                    @refresh="fetchTableList"
                ></table-list>
            </div>
        </div>
    </abc-dialog>
</template>

<script>
    import { boardViewModeOptions } from '@/views-dentistry/registration/table';
    import {
        BOARD_VIEW_MODE, BOARD_VIEW_SOURCE, VIEW_MODE_ENUM,
    } from '@/views-dentistry/registration/constants';
    import {
        getOneWeekStartAndEndDate,
        getTodayStartAndEndDate,
        showRegistrationProducts,
    } from '@/views-dentistry/registration/drag-grid-v1/common';
    import RegistrationBoardSearch from '@/views-dentistry/registration/components/registration-board-search.vue';
    import RegistrationsAPI from 'api/registrations/index';
    import { StatusV2 } from 'views/registration/common/constants';
    const BoardView = () => import('@/views-dentistry/registration/board-view');
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { parseTime } from '@/filters';
    import RegistrationListModel from 'views/registration/adapter/registration-list-model';
    import { flatObjectByPath } from 'views/statistics/common/custom-stat-header-dialog/util';
    import Clone from 'utils/clone';
    import {
        debounce, isEqual,
    } from 'utils/lodash';
    import { resolveHeader } from 'views/statistics/utils';
    import { outpatientRenderTypeList } from 'views/registration/outpatient-header-config';
    import TableList from '@/views-dentistry/outpatient/table-list';
    import { filterDepartmentsByDoctorId } from '@/views-dentistry/registration/common';
    import PatientsAPI from 'api/patients';
    import AppointmentCardDialog from '@/views-dentistry/registration/appointment-card-dialog';
    import { RESERVATION_MODE_TYPE } from 'views/settings/registered-reservation/constant';
    import AbcSocket from 'views/common/single-socket';
    import { CustomStatHeaderDialog } from 'views/statistics/common/custom-stat-header-dialog';

    export default {
        name: 'RegistrationBoardDialog',
        components: {
            RegistrationBoardSearch,
            BoardView,
            TableList,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                boardViewModeOptions,
                VIEW_MODE_ENUM,
                viewMode: VIEW_MODE_ENUM.BOARD,
                boardViewMode: BOARD_VIEW_MODE.DAY,
                cursorDate: new Date(),
                departments: [],
                selectedDoctor: null,
                timeRange: getTodayStartAndEndDate(),
                statistics: {
                    notRefundedCount: 0,
                    reserveTotalCount: 0,
                    hadSignInCount: 0,
                    waitingSignInCount: 0,
                    waitingDiagnoseCount: 0,
                    diagnosedCount: 0,
                    refundedCount: 0,
                },
                doctorList: [],
                loading: false,
                registrationListModel: RegistrationListModel.defaultFirstItem,
                tableHeader: [],
                tableData: [],
                qlSearchHint: '姓名 / 手机 / 诊号',
                params: {
                    offset: 0,
                    limit: 99999,
                    departmentId: '',
                    doctorId: '',
                    date: parseTime(new Date(), 'y-m-d', true),
                    keyword: '',
                    displayStatus: '',
                    patientId: '', // 健康卡扫描之后，使用 patientId 进行搜索
                    registrationType: 0,
                    isForOutpatientRegKanBan: 1,
                },
                isSearching: false,
            };
        },
        watch: {
            cursorDate: {
                async handler(newValue) {
                    this.handleChangeDate(newValue);
                    if (this.isListViewMode) {
                        await this.fetchTableList();
                    } else {
                        await this.updateBoardView();
                    }
                },
            },
        },
        async created() {
            this._fetch = debounce(this.fetchTableList, 200, true);
            this._socketFetchRegistrations = debounce((loading) => this.fetchTableList(loading), 2000, true);
            await Promise.all([
                this.$store.dispatch('fetchClinicBasic'),
                this.$store.dispatch('invoice/initInvoiceConfig'),
                this.fetchRegistrationFee(),
            ]);
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            if (this.isListViewMode) {
                this._socket.on('m1', this.handleSocketMsg);
            }
        },
        mounted() {
            this.$abcEventBus.$on('save-patient-info-success',(data) => {
                if (this.isListViewMode) {
                    this.fetchTableList(false);
                } else {
                    this.handleBoardViewRegistrationUpdate(data.registrationData);
                }
            }, this);
            this.fetchDepartmentsDoctors();
            this.fetchRegistrationsAssistDoctors();
        },
        computed: {
            ...mapGetters([
                'registrationsConfig',
                'registrationsConfigIsInit',
                'userInfo',
                'isCanModifyOutpatientRegistration',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            isBoardDayViewMode() {
                return this.boardViewMode === BOARD_VIEW_MODE.DAY;
            },
            isBoardWeekViewMode() {
                return this.boardViewMode === BOARD_VIEW_MODE.WEEK;
            },
            isListViewMode() {
                return this.viewMode === VIEW_MODE_ENUM.LIST;
            },
            isBoardViewMode() {
                return this.viewMode === VIEW_MODE_ENUM.BOARD;
            },
            boardViewSource() {
                return this.isFixOrderMode ? BOARD_VIEW_SOURCE.CODE : BOARD_VIEW_SOURCE.TIME;
            },
            isFixOrderMode() {
                // modeType 0: 固定号源模式 1: 灵活预约模式
                return this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            boardDoctorList() {
                return this.selectedDoctor ? [this.selectedDoctor] : [];
            },
            outpatientRenderTableHeader() {
                const options = resolveHeader(this.tableHeader, outpatientRenderTypeList);
                options.push({
                    label: '',
                    prop: 'chargeSheetQueryExceptionType',
                    width: 30,
                    render: (h, row) => {
                        return this.showExceptionIcon(row) ? (<abc-icon icon="Attention"></abc-icon>) : null;
                    },
                });
                return options;
            },
            searchInputPlaceholder() {
                return this.qlSearchHint;
            },
            isInSearch() {
                return this.params.keyword || this.params.patientId;
            },
            selectedDoctorDepartments() {
                return filterDepartmentsByDoctorId(this.departments, this.selectedDoctor?.doctor?.id) || [];
            },
            hasDepartments() {
                return !!this.selectedDoctorDepartments.filter((d) => {
                    return d.isDefault !== 1;
                }).length;
            },
            tableHeight() {
                // (633 - 116 - 48 - 17)
                return 452;
            },
            contentStyles() {
                return 'width: 1100px;height: 633px;padding: 0; borderRadius: 0 0 var(--abc-dialog-border-radius) var(--abc-dialog-border-radius)';
            },
        },
        methods: {
            ...mapActions(['fetchRegistrationFee']),
            parseTime,
            showRegistrationProducts,
            handleChangeViewMode(viewMode) {
                this.viewMode = viewMode;
                if (this.isBoardViewMode) {
                    this.boardViewMode = BOARD_VIEW_MODE.WEEK;
                    this.timeRange = getOneWeekStartAndEndDate(this.cursorDate);
                    this.handleClickBoardView();
                } else {
                    this.fetchTableList();
                }
            },
            async fetchRegistrationsAssistDoctors() {
                try {
                    let { data: { rows } } = await RegistrationsAPI.fetchRegistrationsAssistDoctors({
                        registrationType: 0,
                    });
                    if (rows.length === 0) {
                        rows.push ({
                            id: this.userInfo.id,
                            name: this.userInfo.name,
                        });
                    }
                    // 把医生本人放在最开始
                    const currentDoctor = rows.filter((doctor) => doctor.id === this.userInfo.id);
                    const otherDoctors = rows.filter((doctor) => doctor.id !== this.userInfo.id);
                    rows = currentDoctor.concat(otherDoctors);
                    this.doctorList = rows.map((it) => {
                        return {
                            doctor: it,
                        };
                    });
                    if (this.doctorList.length) {
                        this.selectedDoctor = this.doctorList[0];
                    }
                } catch (e) {
                    console.error(e);
                }
            },
            async fetchBoardViewSuggestions(key, callback) {
                try {
                    if (!key) return;
                    const { data: { rows: registrationList = [] } } = await RegistrationsAPI.registrationsQuickList({
                        keyword: key,
                        limit: 100,
                        offset: 0,
                        registrationType: 0,
                    });
                    callback(registrationList.filter(({ registrationFormItem: { doctorId } }) => {
                        return doctorId === this.selectedDoctor.doctor.id;
                    }).filter((it) => it.registrationFormItem.statusV2 < StatusV2.REFUNED));
                } catch (e) {
                    console.error(e);
                }
            },
            handleBoardViewAutocompleteInputSelect(registrationData, selectTriggerCardOpen = true) {
                if (this.$refs.borderView) {
                    this.$refs.borderView.handleBoardViewSearch(registrationData ?? {}, async (searchOptions, next) => {
                        if (searchOptions.changeDate) {
                            this.cursorDate = new Date(searchOptions.changeDate);
                            this.handleChangeDate(this.cursorDate);
                        }

                        if (searchOptions.doctorId) {
                            const findedDoctor = this.doctorList.find((item) => item.doctor.id === searchOptions.doctorId);
                            if (findedDoctor) {
                                this.selectedDoctor = findedDoctor;
                            }
                        }

                        // 视图切换完成并且搜索完成后设置激活的元素id
                        await this.$nextTick();
                        next(selectTriggerCardOpen);
                    });
                }
            },
            handleFinishRegistrationInfo(registrationInfo) {
                this.$Toast({
                    type: 'success', message: registrationInfo.message,
                });
                this.handleBoardViewRegistrationUpdate(registrationInfo);
            },
            handleBoardViewRegistrationUpdate(registrationInfo) {
                this.handleBoardViewAutocompleteInputSelect(registrationInfo, false);
            },
            refreshRegistrations() {
                if (this.isListViewMode) {
                    this.fetchTableList();
                }
            },
            handleClickBoardView() {
                this.handleChangeDate(this.cursorDate);
                if (this.isBoardViewMode) {
                    this.updateBoardView();
                } else {
                    this.fetchTableList();
                }
            },
            async updateBoardView() {
                if (this.isBoardViewMode && this.$refs.borderView) {
                    console.warn('更新看板视图');
                    await this.$nextTick();
                    this.$refs.borderView.updateBoardView();
                }
            },
            handleChangeDate(date) {
                if (this.isBoardWeekViewMode) {
                    this.timeRange = getOneWeekStartAndEndDate(date);
                }
                if (this.isBoardDayViewMode) {
                    const startDate = parseTime(date, 'y-m-d', true);
                    this.timeRange = [startDate, startDate];
                }
            },
            handleQLSearchFocus() {
                this.qlSearchHint = '姓名 / 手机 / 诊号';
            },
            handleQLSearchBlur() {
                this.qlSearchHint = '搜索挂号单';
            },
            getParams() {
                let params = {};
                // 有搜索条件的，只根据搜索条件查询
                if (this.isInSearch) {
                    params = {
                        offset: 0,
                        limit: 100,
                        departmentId: '',
                        doctorId: '',
                        date: '',
                        keyword: this.params.keyword,
                        patientId: this.params.patientId,
                        registrationType: 0,
                        isForOutpatientRegKanBan: 1,
                    };
                } else {
                    this.params.doctorId = this.selectedDoctor.doctor.id || '';
                    this.params.date = parseTime(this.cursorDate, 'y-m-d', true);
                    params = Clone(this.params);
                }

                return params;
            },

            async fetchTableList(loading = true) {
                if (!this.isListViewMode) {
                    return;
                }
                this.loading = loading;
                const params = this.getParams();
                try {
                    const data = await RegistrationsAPI.registrationsQuickList({ ...params });
                    const {
                        notRefundedCount,
                        reserveTotalCount = 0,
                        hadSignInCount,
                        waitingSignInCount,
                        refundedCount,
                    } = data.data;
                    this.statistics.notRefundedCount = notRefundedCount;
                    this.statistics.reserveTotalCount = reserveTotalCount;
                    this.statistics.hadSignInCount = hadSignInCount;
                    this.statistics.waitingSignInCount = waitingSignInCount;
                    this.statistics.refundedCount = refundedCount;
                    if (isEqual(params, this.getParams())) {
                        this.registrationListModel = new RegistrationListModel(data, this.businessType);
                        this.registrations = this.registrationListModel.list;

                        this.tableHeader = data.data.header;
                        this.tableHeader.splice(0, 0, {
                            prop: '',
                            label: '',
                            width: 36,
                            renderType: 'reserveRender',
                        });
                        this.tableData = this.registrations.map((item) => flatObjectByPath(item));
                    }
                } catch (e) {
                    console.error(e);
                    if (isEqual(params, this.getParams())) {
                        this.registrationListModel = new RegistrationListModel({}, this.businessType);
                        this.registrations = this.registrationListModel.list;
                    }
                }
                this.loading = false;
                this.isSearching = false;
            },
            async fetchDepartmentsDoctors() {
                const { data } = await RegistrationsAPI.fetchDepartmentsDoctors({ type: 1 });
                this.departments = data.departments;
            },
            handleBoardViewStatisticsUpdate(statistics) {
                this.statistics = statistics;
            },
            handleChangeDoctor(id) {
                this.selectedDoctor = this.doctorList.find((it) => it.doctor.id === id);
                if (this.isBoardViewMode) {
                    this.updateBoardView();
                } else {
                    this.fetchTableList();
                }
            },
            async handleNewAdd(patient) {
                try {
                    let pastHistory = '', allergicHistory = '';
                    if (patient?.id) {
                        const history = await PatientsAPI.fetchPastHistory(patient.id);
                        pastHistory = history.pastHistory;
                        allergicHistory = history.allergicHistory;
                    }

                    this.handleClickNewRegistrationButton(patient,'registration-board-dialog-card-dropdown', {
                        registrationId: undefined,
                        isReserved: 1,
                        date: undefined,
                        pastHistory,
                        allergicHistory,
                    });
                } catch (e) {
                    console.log(e);
                }
            },
            async handleClickNewRegistrationButton(patient = {}, formSource = 'registration-board-dialog' , extendCardProps = {}) {
                if (this._appointmentCardDialogInstance) {
                    this._appointmentCardDialogInstance.destroyDialog();
                    this._appointmentCardDialogInstance = null;
                }
                this._appointmentCardDialogInstance = new AppointmentCardDialog(Object.assign({
                    value: true,
                    formSource: formSource || '',
                    doctorId: this.selectedDoctor.doctor.id,
                    doctorName: this.selectedDoctor.doctor.name,
                    hasDepartments: this.hasDepartments,
                    departments: this.selectedDoctorDepartments,
                    date: parseTime(this.cursorDate, 'y-m-d', true),
                    isReserved: 1,
                    itemPatient: patient,
                    refresh: (...props) => {
                        this.refreshRegistrations();
                        this.handleBoardViewRegistrationUpdate(...props);
                    },
                    close: () => {},
                    finishRegistration: (...props) => {
                        this.handleFinishRegistrationInfo(...props);
                    },
                }, extendCardProps));
                this._appointmentCardDialogInstance.generateDialog({
                    parent: this,
                });
            },
            inputHandler() {
                this.params.offset = 0;
                this.params.displayStatus = '';
                this.isSearching = true;
                this._fetch();
            },
            clearInput() {
                this.params.keyword = '';
                this.params.patientId = '';
                this.fetchTableList();
            },
            handleSocketMsg(data) {
                const dateFilterStr = parseTime(this.cursorDate, 'y-m-d', true) === parseTime(new Date(), 'y-m-d', true) ? 'today' : 'datePicker';

                if (!this.params.departmentId &&
                    !this.isInSearch &&
                    data.sc > 0 &&
                    this.userInfo.id !== data.operatorId &&
                    dateFilterStr === 'today'
                ) {
                    this._socketFetchRegistrations(false);
                }
            },
            handleOpenCustomHeaderDialog() {
                const tableKey = 'registration.itemListForOutpatientRegKanBan';

                new CustomStatHeaderDialog({
                    customHeaderConfig: {
                        tableKey,
                        tableName: '门诊预约看板挂号',
                        updateCustomHeaderSuccessHandler: this.fetchTableList,
                        showFixed: false,
                        mode: 'draggle',
                    },
                }).generateDialog({ parent: this });
            },
        },
        beforeDestroy() {
            this._socket && this._socket.off('m1', this.handleSocketMsg);
        },
    };
</script>

<style lang="scss">
@import '~styles/theme.scss';
@import "src/styles/mixin";

.registration-board-dialog-wrapper {
    .close-wrapper {
        position: absolute;
        top: 8px;
        right: 8px;
    }

    .registartion-board-handler {
        display: flex;
        align-items: center;

        .dropdown-item {
            width: auto;
            margin-right: 16px;
        }

        .view-mode-reference {
            .view-mode {
                font-size: 16px;
                font-weight: 500;
                line-height: 16px;
                color: $T1;
                letter-spacing: 0;
            }
        }
    }

    .registartion-board-amount {
        display: flex;
        align-items: center;
        height: 36px;
        padding: 0 24px;
        background: #f0f7ff;

        .registartion-board-amount-item {
            display: flex;
            align-items: center;
            margin-right: 40px;
            color: $theme2;

            .registartion-board-amount-label {
                font-size: 12px;
            }

            .registartion-board-amount-value {
                margin-left: 6px;
                font-size: 16px;
            }
        }
    }

    .registartion-board-amount-time {
        font-size: 12px;
        color: #a3c5e9;
    }

    .table-wrapper {
        display: flex;
        width: 100%;
        height: 100%;

        .registration-table-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            overflow-y: overlay;

            @include scrollBar;

            .abc-fixed-table.registration-table-wrapper {
                .detail-table-container::after {
                    border-bottom: none;
                }
            }

            .registration-table-wrapper {
                .sort-bar {
                    width: 8px;
                }

                tbody td {
                    border-right: none;
                    border-left: none;

                    > div {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }

                .table-tbody td {
                    padding: 0 12px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                    &:last-child {
                        border-right: none;
                    }
                }

                thead th {
                    padding: 0 12px;
                    color: $T2;
                    background-color: $S2;
                    border-top: none;
                    border-right: none;
                    border-left: none;
                }
            }
        }
    }
}
</style>
