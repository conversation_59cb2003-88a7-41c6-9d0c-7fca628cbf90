<template>
    <div v-if="interactions.length" class="verify-detail-wrapper">
        <div
            v-for="(it, index) in interactions"
            :key="index || it.medicineA + it.medicineB"
            class="interaction-item"
            :class="`level-${ it.level || 5}`"
        >
            <h5 class="influence" :class="{ 'has-mechanism': it.mechanism }">
                {{ judge(it) }}{{ levelStr(it) }}
            </h5>

            <div v-if="it.mechanism" class="mechanism">
                <span>风险详情：</span>
                {{ it.mechanism }}
            </div>

            <template v-if="it.measures">
                <div class="measures">
                    <span>建议措施：</span>
                    {{ measuresStr(it.measures) }}
                </div>
            </template>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    export default {
        name: 'Interaction',
        props: {
            interactions: Array,
        },
        data() {
            return {
                currentId: '',
            };
        },
        computed: {},
        watch: {

        },
        methods: {
            judge(it) {
                if (it.influence) {
                    it.influence = it.influence.replace(/{A}/g, `${it.medicineA} `);
                    it.influence = it.influence.replace(/{B}/g, ` ${it.medicineB} `);
                    return it.influence;
                }
                return it.reason || '';

            },
            levelStr(it) {
                switch (it.level) {
                    case 2:
                        return '（风险分级：轻度）';
                    case 3:
                        return '（风险分级：中度）';
                    case 4:
                        return '（风险分级：严重）';
                    case 5:
                        return '（风险分级：禁忌）';
                    default:
                        return '';
                }
            },
            measuresStr(measures) {
                return `措施：${measures.replace(/<br\/>/g, '')}`;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .verify-detail-wrapper {
        .interaction-item {
            position: relative;
            margin-bottom: 0;

            &:last-child {
                margin-bottom: 0;
            }

            .cis-icon-zhuyi {
                font-size: 14px;
            }

            .influence {
                position: relative;
                font-size: 12px;
                font-weight: bold;
                line-height: 18px;

                &.show-abstract::after {
                    position: absolute;
                    top: 26px;
                    left: 50%;
                    z-index: 2;
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    margin-left: -6px;
                    content: ' ';
                    background-color: #fff4bd;
                    border: 1px solid #ffcd00;
                    border-right: none;
                    border-bottom: none;
                    transform: rotate(45deg);
                }

                /* &.has-mechanism { */

                /*    &:hover { */

                /*        text-decoration: underline dashed; */

                /*    } */

                /* } */
            }

            .measures-h5 {
                margin-top: 8px;
                margin-bottom: 4px;
            }

            .measures,
            .mechanism {
                margin-top: 4px;
                font-size: 12px;
                color: $T2;

                > span {
                    font-weight: bold;
                }
            }

            .mechanism {
                display: -webkit-box;
            }

            &.level-3,
            &.level-2 {
                border-left-color: $Y2;

                .influence,
                .cis-icon-zhuyi {
                    color: $Y2;
                }
            }

            &.level-5,
            &.level-4 {
                border-left-color: #ff3333;

                .influence,
                .cis-icon-zhuyi {
                    color: #ff3333;
                }
            }

            .mechanism-popper {
                position: absolute;

                /* top: 40px; */
                left: 0;
                z-index: 5;
                width: 100%;
                padding: 10px;
                line-height: 20px;
                color: $T1;
                background-color: #fff4bd;
                border: 1px solid #ffcd00;
                border-radius: var(--abc-border-radius-small);
                box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.1);
            }
        }

        .interaction-item + .interaction-item {
            margin-top: 12px;
        }
    }
</style>
