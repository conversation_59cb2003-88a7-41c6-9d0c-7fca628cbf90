<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="手术单"
        size="huge"
        :auto-focus="false"
        content-styles="min-height: 552px; max-height: 552px;"
        custom-class="outpatient-surgery-detail-dialog"
        append-to-body
        @open="handleOpen"
    >
        <abc-flex vertical gap="large">
            <abc-form ref="outpatient-surgery-apply-form-ref" item-no-margin is-excel>
                <abc-flex vertical gap="large">
                    <abc-descriptions
                        :label-width="88"
                        :column="3"
                        size="large"
                        grid
                        :disabled="disabled"
                        :custom-title-style="{ 'background-color': '#f5f7fb' }"
                    >
                        <div slot="title">
                            手术安排
                        </div>

                        <abc-descriptions-item label="手术日期" content-padding="0">
                            <abc-form-item required>
                                <abc-date-picker
                                    v-model="surgeryDetail.surgeryArrangement.surgeryDate"
                                    format="YYYY-MM-DD"
                                    :disabled="disabled"
                                    placeholder=""
                                    :show-icon="!disabled"
                                    :clearable="false"
                                ></abc-date-picker>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="手术室" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-if="!disabled"
                                    v-model="surgeryDetail.surgeryArrangement.operatingRoomId"
                                    size="medium"
                                    @change="handleOperatingRoomName"
                                >
                                    <abc-option
                                        v-for="room in surgeryRoomList"
                                        :key="`surgery-room-${room.id}`"
                                        :value="room.id"
                                        :label="room.name"
                                    ></abc-option>
                                </abc-select>

                                <abc-select
                                    v-else
                                    :show-value="surgeryDetail.surgeryArrangement.operatingRoomName"
                                    disabled
                                    no-icon
                                    size="medium"
                                ></abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="麻醉医生" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="surgeryDetail.surgeryArrangement.anesthesiaDoctorId"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                >
                                    <abc-option
                                        v-for="doctorByDepartment in doctorsByDepartment"
                                        :key="`anesthesia-doctor-${doctorByDepartment.employeeId}`"
                                        :value="doctorByDepartment.employeeId"
                                        :label="doctorByDepartment.employeeName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="麻醉助手" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="anesthesiaAssistantEmployeeIds"
                                    multiple
                                    :multi-label-mode="!disabled ? 'tag' : 'text'"
                                    with-search
                                    :max-tag="!disabled ? multiSelectTagNum : 99"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                    :fetch-suggestions="handeFetchAnesthesiaEmployee"
                                >
                                    <abc-option
                                        v-for="assistantEmployee in anesthesiaEmployeesByDepartment"
                                        :key="`anesthesia-assistant-employee-${assistantEmployee.employeeId}`"
                                        :value="assistantEmployee.employeeId"
                                        :label="assistantEmployee.employeeName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="手术护士" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="surgeryNurseEmployeeIds"
                                    multiple
                                    :multi-label-mode="!disabled ? 'tag' : 'text'"
                                    with-search
                                    :max-tag="!disabled ? multiSelectTagNum : 99"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                    :fetch-suggestions="handeFetchSurgeryNurseEmployee"
                                >
                                    <abc-option
                                        v-for="assistantEmployee in surgeryNurseEmployeesByDepartment"
                                        :key="`surgery-nurse-employee-${assistantEmployee.employeeId}`"
                                        :value="assistantEmployee.employeeId"
                                        :label="assistantEmployee.employeeName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="巡回护士" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="circulatingNurseEmployeeIds"
                                    multiple
                                    :multi-label-mode="!disabled ? 'tag' : 'text'"
                                    with-search
                                    :max-tag="!disabled ? multiSelectTagNum : 99"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                    :fetch-suggestions="handeFetchCirculatingNurseEmployee"
                                >
                                    <abc-option
                                        v-for="assistantEmployee in circulatingNurseEmployeesByDepartment"
                                        :key="`circulating-nurse-employee-${assistantEmployee.employeeId}`"
                                        :value="assistantEmployee.employeeId"
                                        :label="assistantEmployee.employeeName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>
                    </abc-descriptions>

                    <abc-descriptions
                        :label-width="88"
                        :column="3"
                        size="large"
                        grid
                        :disabled="disabled"
                        :custom-title-style="{ 'background-color': '#f5f7fb' }"
                    >
                        <div slot="title">
                            手术信息
                        </div>

                        <abc-descriptions-item label="科室" content-padding="0">
                            <abc-form-item required>
                                <abc-select
                                    v-model="surgeryDetail.surgeryDepartmentId"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                    @change="(val) => handeChangeDepartment(val, true)"
                                >
                                    <abc-option
                                        v-for="department in departments"
                                        :key="department.id"
                                        :value="department.id"
                                        :label="department.name"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="手术医生" content-padding="0">
                            <abc-form-item required>
                                <abc-select
                                    v-model="surgeryDetail.surgeryDoctorId"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                >
                                    <abc-option
                                        v-for="doctorByDepartment in doctorsByDepartment"
                                        :key="doctorByDepartment.employeeId"
                                        :value="doctorByDepartment.employeeId"
                                        :label="doctorByDepartment.employeeName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="手术助手" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="assistantEmployeeIds"
                                    multiple
                                    :multi-label-mode="!disabled ? 'tag' : 'text'"
                                    with-search
                                    :max-tag="!disabled ? multiSelectTagNum : 99"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                    :fetch-suggestions="handeFetchAssistantEmployee"
                                >
                                    <abc-option
                                        v-for="assistantEmployee in assistantEmployeesByDepartment"
                                        :key="assistantEmployee.employeeId"
                                        :value="assistantEmployee.employeeId"
                                        :label="assistantEmployee.employeeName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="术前诊断" content-padding="0" :span="3">
                            <abc-form-item>
                                <abc-select
                                    v-model="cachePreoperativeDiagnosis"
                                    multiple
                                    :multi-label-mode="!disabled ? 'tag' : 'text'"
                                    :max-tag="!disabled ? 4 : 99"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                >
                                    <abc-option
                                        v-for="diagnosis in curExtendDiagnosisInfos"
                                        :key="diagnosis.diseaseCode"
                                        :value="diagnosis.diseaseCode"
                                        :label="diagnosis.diseaseName"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="拟施手术" content-padding="0" :span="3">
                            <abc-space
                                v-for="(intendedSurgery, index) in surgeryDetail.intendedSurgeries"
                                :key="`intended-surgery-${index}`"
                                is-compact
                                class="intended-surgeries-wrapper"
                            >
                                <abc-flex
                                    align="center"
                                    justify="space-between"
                                    class="intended-surgeries-name"
                                >
                                    <span class="intended-surgeries-name-title" :title="intendedSurgery.name">{{ intendedSurgery.name }}</span>
                                    <span class="intended-surgeries-code">{{ intendedSurgery.code }}</span>
                                </abc-flex>

                                <!-- 手术等级 -->
                                <abc-form-item>
                                    <abc-select
                                        v-model="intendedSurgery.level"
                                        :width="124.5"
                                        :placeholder="!disabled ? '级别' : ''"
                                        :disabled="disabled"
                                        :no-icon="disabled"
                                        size="medium"
                                        class="intended-surgeries-form-operate-assistant"
                                    >
                                        <abc-option
                                            v-for="surgeryLevel in surgeryLevelList"
                                            :key="surgeryLevel.code"
                                            :value="surgeryLevel.code"
                                            :label="surgeryLevel.name"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>

                                <!-- 手术切口 -->
                                <abc-form-item>
                                    <abc-select
                                        v-model="intendedSurgery.healingIncisionLevel"
                                        :width="124.5"
                                        :placeholder="!disabled ? '切口' : ''"
                                        :disabled="disabled"
                                        :no-icon="disabled"
                                        size="medium"
                                        class="intended-surgeries-form-operate-assistant"
                                    >
                                        <abc-option
                                            v-for="surgeryIncisionHealingLevel in surgeryIncisionHealingLevelList"
                                            :key="surgeryIncisionHealingLevel.code"
                                            :value="surgeryIncisionHealingLevel.code"
                                            :label="surgeryIncisionHealingLevel.shortName"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>

                                <!-- 手术部位 -->
                                <abc-form-item>
                                    <abc-select
                                        v-model="intendedSurgery.site"
                                        :width="124.5"
                                        :placeholder="!disabled ? '部位' : ''"
                                        :disabled="disabled"
                                        :no-icon="disabled"
                                        size="medium"
                                        class="intended-surgeries-form-operate-assistant"
                                    >
                                        <abc-option
                                            v-for="surgerySite in surgerySiteList"
                                            :key="surgerySite.code"
                                            :value="surgerySite.code"
                                            :label="surgerySite.name"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>

                                <abc-flex
                                    v-if="!disabled"
                                    align="center"
                                    justify="center"
                                    class="intended-surgeries-delete"
                                >
                                    <abc-delete-icon @delete="handeDeleteIntendedSurgeries(intendedSurgery.name)"></abc-delete-icon>
                                </abc-flex>
                            </abc-space>

                            <intended-surgery-autocomplete v-if="!disabled" @selected="handleSelectedIntendedSurgery"></intended-surgery-autocomplete>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="手术类型" content-padding="0">
                            <abc-space style="width: 100%; height: 100%;" class="surgery-apply-form-operate-type-wrapper">
                                <abc-form-item class="surgery-apply-form-operate-type">
                                    <abc-select
                                        v-model="surgeryDetail.type"
                                        adaptive-width
                                        :disabled="disabled"
                                        :no-icon="disabled"
                                        size="medium"
                                    >
                                        <abc-option :value="0" label="择期手术"></abc-option>
                                        <abc-option :value="1" label="急诊手术"></abc-option>
                                        <abc-option :value="2" label="非手术患者"></abc-option>
                                    </abc-select>
                                </abc-form-item>

                                <abc-form-item>
                                    <abc-checkbox
                                        v-model="surgeryDetail.isDaytimeSurgery"
                                        type="number"
                                        :disabled="disabled"
                                        style="padding-right: 8px;"
                                    >
                                        日间
                                    </abc-checkbox>
                                </abc-form-item>
                            </abc-space>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="手术体位" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="surgeryDetail.surgeryPosture"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                >
                                    <abc-option :value="0" label="仰卧"></abc-option>
                                    <abc-option :value="1" label="侧卧"></abc-option>
                                    <abc-option :value="2" label="半侧卧"></abc-option>
                                    <abc-option :value="3" label="俯卧"></abc-option>
                                    <abc-option :value="4" label="截石位"></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="预计时长" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="surgeryDetail.estimateMinutes"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                >
                                    <abc-option
                                        v-for="estimateMinutes in estimateMinutesList"
                                        :key="`intended-surgeries-estimate-minutes-${estimateMinutes}`"
                                        :value="estimateMinutes"
                                        :label="`${estimateMinutes}分钟`"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="麻醉方式" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="surgeryDetail.surgeryArrangement.anesthesiaMethod"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                >
                                    <abc-option
                                        v-for="anesthesiaMethod in surgeryAnesthesiaMethodList"
                                        :key="`anesthesia-method-${anesthesiaMethod.code}`"
                                        :value="anesthesiaMethod.code"
                                        :label="anesthesiaMethod.name"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="ASA分级" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="surgeryDetail.surgeryArrangement.asaLevel"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                >
                                    <abc-option
                                        v-for="asaLevel in asaLevelList"
                                        :key="`asa-level-${asaLevel.value}`"
                                        :value="asaLevel.value"
                                        :label="asaLevel.label"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="术前进食" content-padding="0">
                            <abc-form-item>
                                <abc-select
                                    v-model="surgeryDetail.surgeryArrangement.preoperativeFastingFlag"
                                    :disabled="disabled"
                                    :no-icon="disabled"
                                    size="medium"
                                >
                                    <abc-option :value="1" label="是"></abc-option>
                                    <abc-option :value="0" label="否"></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>

                        <abc-descriptions-item label="手术需求" content-padding="0" :span="3">
                            <abc-form-item>
                                <abc-edit-div
                                    v-model="surgeryDetail.surgicalRequirements"
                                    :maxlength="500"
                                    :disabled="disabled"
                                    class="surgery-apply-form-requirements-edit-div"
                                ></abc-edit-div>
                            </abc-form-item>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-flex>
            </abc-form>
        </abc-flex>

        <div slot="footer" style="text-align: right;">
            <abc-button @click="handleSubmit">
                确定
            </abc-button>
            <abc-button variant="ghost" @click="visible = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import SurgeryApi from 'api/hospital/surgery';
    import settings from 'api/settings';
    import clone from 'utils/clone';
    import SettingAPI from 'api/settings';
    import IntendedSurgeryAutocomplete
        from '@/views-hospital/medical-prescription/components/surgery/components/intended-surgery-autocomplete.vue';
    import { mapGetters } from 'vuex';

    export default {
        name: 'SurgeryDetailDialog',
        components: { IntendedSurgeryAutocomplete },
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            item: {
                type: Object,
                default() {
                    return {};
                },
                require: true,
            },
            multiSelectTagNum: {
                type: Number,
                default: 2,
            },
            extendDiagnosisInfos: {
                type: Array,
                default() {
                    return [];
                },
            },
            submitCallback: {
                type: Function,
                default() {},
            },
        },
        data() {
            const cacheEstimateMinutesList = [];
            for (let i = 15; i <= 240; i += 15) {
                cacheEstimateMinutesList.push(i);
            }

            const cacheAsaLevelList = [
                {
                    value: 0, label: '一级',
                },
                {
                    value: 1, label: '二级',
                },
                {
                    value: 2, label: '三级',
                },
                {
                    value: 3, label: '四级',
                },
                {
                    value: 4, label: '五级',
                },
                {
                    value: 5, label: '六级',
                },
            ];

            return {
                visible: false,
                surgeryDetail: {},
                departments: [], // 科室列表
                surgeryRoomList: [], // 手术室列表
                doctorsByDepartment: [], // 对应科室下的医生列表
                anesthesiaEmployeesByDepartment: [], // 对应科室下的麻醉助手列表
                employeesByDepartment: [], // 对应科室下的成员列表
                surgeryNurseEmployeesByDepartment: [], // 对应科室下的手术护士列表
                circulatingNurseEmployeesByDepartment: [], // 对应科室下的巡回护士列表
                assistantEmployeesByDepartment: [], // 对应科室下的手术助手列表
                estimateMinutesList: cacheEstimateMinutesList, // 预计时长分钟数列表
                asaLevelList: cacheAsaLevelList, // asa等级列表
            };
        },
        computed: {
            ...mapGetters([
                'surgeryLevelList', // 手术等级
                'surgeryIncisionHealingLevelList', // 手术切口愈合等级
                'surgerySiteList', // 手术操作部位
                'surgeryAnesthesiaMethodList', // 麻醉方式列表
            ]),
            curExtendDiagnosisInfos() {
                return this.extendDiagnosisInfos.reduce((acc, current) => {
                    if (current.code && !acc.some((item) => item.code === current.code)) {
                        acc.push(current);
                    }
                    return acc;
                }, []).map((it) => ({
                    diseaseCode: it.code, diseaseName: it.name,
                }));
            },
            anesthesiaAssistantEmployeeIds: {
                get() {
                    return this.surgeryDetail.surgeryArrangement.anesthesiaAssistantEmployeeIds || [];
                },
                set(v) {
                    this.surgeryDetail.surgeryArrangement.anesthesiaAssistantEmployeeIds = clone(v);
                },
            },
            surgeryNurseEmployeeIds: {
                get() {
                    return this.surgeryDetail.surgeryArrangement.surgeryNurseEmployeeIds || [];
                },
                set(v) {
                    this.surgeryDetail.surgeryArrangement.surgeryNurseEmployeeIds = clone(v);
                },
            },
            circulatingNurseEmployeeIds: {
                get() {
                    return this.surgeryDetail.surgeryArrangement.circulatingNurseEmployeeIds || [];
                },
                set(v) {
                    this.surgeryDetail.surgeryArrangement.circulatingNurseEmployeeIds = clone(v);
                },
            },
            assistantEmployeeIds: {
                get() {
                    return this.surgeryDetail.assistantEmployeeIds || [];
                },
                set(v) {
                    this.surgeryDetail.assistantEmployeeIds = clone(v);
                },
            },
            // 术前诊断id列表
            cachePreoperativeDiagnosis: {
                get() {
                    return (this.surgeryDetail.preoperativeDiagnosis || []).map((it) => it.diseaseCode);
                },
                set(val) {
                    this.surgeryDetail.preoperativeDiagnosis = this.curExtendDiagnosisInfos.filter((it) => val.includes(it.diseaseCode));
                },
            },
        },
        created() {
            this.surgeryDetail = clone(this.item.surgeryDetail) || {};

            // 获取科室列表
            this.fetchDepartments();
            // 获取手术室列表
            this.fetchSurgeryRooms();

            this.$store.dispatch('fetchHisSurgery');

            this.$nextTick(() => {
                // 将手术goods上的拟施手术加载到surgeryDetail上
                this.handleSurgeryOperation();

                // 获取选择的科室下的成员
                if (this.surgeryDetail.surgeryDepartmentId) {
                    this.handeChangeDepartment(this.surgeryDetail.surgeryDepartmentId);
                }
            });
        },
        methods: {
            handleOpen() {
            },
            async fetchDepartments() {
                try {
                    const res = await SettingAPI.clinic.fetchClinicDepartments();
                    const { data } = res.data;
                    this.departments = data?.rows || [];
                } catch (e) {
                    console.error('科室信息获取错误\n', e);
                }
            },
            async fetchSurgeryRooms() {
                try {
                    const roomList = await SurgeryApi.fetchSurgeryRoomList();
                    this.surgeryRoomList = roomList.filter((it) => !!it.status);
                } catch (e) {
                    console.error(e);
                }
            },
            handleOperatingRoomName(roomId) {
                const room = this.surgeryRoomList.find((it) => it.id === roomId);
                if (room) {
                    this.surgeryDetail.surgeryArrangement.operatingRoomName = room.name;
                } else {
                    this.surgeryDetail.surgeryArrangement.operatingRoomName = '';
                }
            },
            async handeChangeDepartment(departmentId, isResetEmployee = false) {
                const { data } = await settings.clinic.fetchDepartmentById(departmentId);
                const employees = data?.employees || [];
                this.employeesByDepartment = clone(employees);
                this.doctorsByDepartment = clone(employees).filter((it) => (it.roles || []).includes(1));
                this.assistantEmployeesByDepartment = clone(employees);
                this.anesthesiaEmployeesByDepartment = clone(employees);
                this.surgeryNurseEmployeesByDepartment = clone(employees);
                this.circulatingNurseEmployeesByDepartment = clone(employees);

                if (isResetEmployee) {
                    this.surgeryDetail.surgeryDoctorId = '';
                    this.surgeryDetail.surgeryArrangement.anesthesiaAssistantEmployeeIds = [];
                    this.surgeryDetail.surgeryArrangement.surgeryNurseEmployeeIds = [];
                    this.surgeryDetail.surgeryArrangement.circulatingNurseEmployeeIds = [];
                    this.surgeryDetail.assistantEmployeeIds = [];
                    this.surgeryDetail.surgeryArrangement.anesthesiaDoctorId = '';
                }
            },
            handleSurgeryOperation() {
                if (this.disabled || !this.surgeryDetail.isInit) return;
                const surgeryOperationList = this.item.children || [];
                const cacheIntendedSurgeries = [];
                surgeryOperationList.forEach((it) => {
                    cacheIntendedSurgeries.push({
                        name: it.name,
                        code: it.operationCode,
                        level: it.surgeryGradeCode,
                        site: it.surgerySiteCode,
                        healingIncisionLevel: it.surgeryIncisionHealingCode,
                    });
                });
                this.surgeryDetail.intendedSurgeries = clone(cacheIntendedSurgeries);
            },
            handeFetchAnesthesiaEmployee(key) {
                key = key.trim();
                this.anesthesiaEmployeesByDepartment = this.employeesByDepartment.filter((it) => it.employeeName?.includes(key));
            },
            handeFetchSurgeryNurseEmployee(key) {
                key = key.trim();
                this.surgeryNurseEmployeesByDepartment = this.employeesByDepartment.filter((it) => it.employeeName?.includes(key));
            },
            handeFetchCirculatingNurseEmployee(key) {
                key = key.trim();
                this.circulatingNurseEmployeesByDepartment = this.employeesByDepartment.filter((it) => it.employeeName?.includes(key));
            },
            handeFetchAssistantEmployee(key) {
                key = key.trim();
                this.assistantEmployeesByDepartment = this.employeesByDepartment.filter((it) => it.employeeName?.includes(key));
            },
            handeDeleteIntendedSurgeries(name) {
                this.surgeryDetail.intendedSurgeries = this.surgeryDetail.intendedSurgeries.filter((it) => it.name !== name);
            },
            handleSelectedIntendedSurgery(item) {
                // 如果已经选择过了, 则不再添加到列表中
                if (this.surgeryDetail.intendedSurgeries.find((it) => it.name === item.name)) return;
                item = Object.assign({}, item, {
                    level: '',
                    site: '',
                    healingIncisionLevel: '',
                });
                this.surgeryDetail.intendedSurgeries = this.surgeryDetail.intendedSurgeries.concat([item]);
            },
            handleSubmit() {
                if (this.disabled) {
                    this.visible = false;
                } else {
                    this.$refs['outpatient-surgery-apply-form-ref'].validate((valid) => {
                        if (valid) {
                            typeof this.submitCallback === 'function' && this.submitCallback(this.surgeryDetail);
                            this.visible = false;
                        }
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/mixin.scss";

.outpatient-surgery-detail-dialog {
    .patient-sub-info {
        color: var(--abc-color-T2);
    }

    .intended-surgeries-wrapper {
        width: 100%;
        height: auto !important;

        &:not(:last-child) {
            border-bottom: 1px solid var(--abc-color-P7);
        }

        .abc-space-compact-item:first-child {
            flex: 1;
            max-width: calc(100% - 413.5px);
        }

        input {
            border-left: 1px dashed var(--abc-color-P7) !important;
        }
    }

    .intended-surgeries-name {
        flex: 1;
        height: 100%;
        padding: 0 12px;

        .intended-surgeries-code {
            font-size: 12px;
            color: var(--abc-color-T3);
        }
    }

    .intended-surgeries-name-title {
        @include ellipsis;
    }

    .intended-surgeries-form-operate-assistant {
        min-width: 124.5px;
        max-width: 124.5px;
    }

    .surgery-apply-form-operate-type-wrapper {
        .abc-space-item:first-child {
            flex: 1;
        }
    }

    .surgery-apply-form-operate-type {
        border-right: 1px solid var(--abc-color-P7) !important;
    }

    .surgery-apply-form-requirements-edit-div {
        padding: 9px 11px !important;
    }

    .intended-surgeries-delete {
        width: 40px;
        height: 100%;
        border-left: 1px dashed var(--abc-color-P7);
    }
}
</style>
