<template>
    <abc-dialog
        v-if="showDialog"
        ref="dialog"
        v-model="showDialog"
        append-to-body
        title="号源看板"
        size="hugely"
        responsive
        custom-class="appointment-kanban-dialog-wrapper"
        :auto-focus="false"
    >
        <abc-flex
            vertical
            :gap="24"
        >
            <abc-flex vertical :gap="16">
                <abc-date-picker-bar
                    v-model="currentDateLabel"
                    :options="datePickerBarOptions"
                    value-format="YYYY-MM-DD"
                    date-picker-type="date"
                    @change="changeDate"
                >
                </abc-date-picker-bar>

                <abc-flex justify="space-between" :gap="12">
                    <abc-statistic
                        style="flex: 1;"
                        variant="colorful"
                        theme="C1"
                        :title="summaryData.totalCount"
                        content="总号数"
                        icon="s-order-1-fill"
                    >
                    </abc-statistic>
                    <abc-statistic
                        style="flex: 1;"
                        variant="colorful"
                        theme="C2"
                        :title="summaryData.reserveTotalCount"
                        content="预约量"
                        icon="s-group-fill"
                    >
                    </abc-statistic>
                    <abc-statistic
                        style="flex: 1;"
                        variant="colorful"
                        theme="C3"
                        :title="summaryData.hadSignInCount"
                        content="签到"
                        icon="s-check-circle-small-fill"
                    >
                    </abc-statistic>
                    <abc-statistic
                        style="flex: 1;"
                        variant="colorful"
                        theme="C4"
                        :title="summaryData.waitingSignInCount"
                        content="未签"
                        icon="s-time-fill"
                    >
                    </abc-statistic>
                    <abc-statistic
                        style="flex: 1;"
                        variant="colorful"
                        theme="C5"
                        :title="summaryData.refundedCount"
                        content="退号"
                        icon="s-transfer-out-fill"
                    >
                    </abc-statistic>
                </abc-flex>
            </abc-flex>
            <abc-flex vertical :gap="16">
                <h5>号源列表</h5>
                <abc-table
                    :style="{ height: tableHeight }"
                    :render-config="renderConfig"
                    :data-list="dataList"
                    :loading="contentLoading"
                    :pagination="{
                        ...pageParams,
                        count: summaryData.notRefundedCount,
                        showTotalPage: true,
                    }"
                    @pageChange="changePageIndex"
                >
                </abc-table>
            </abc-flex>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    import RegistrationsAPI from 'api/registrations/index';
    import { parseTime } from 'src/filters';
    import { mapGetters } from 'vuex';
    import {
        BusinessType,
        PayStatusV2,
        StatusV2,
    } from '@/views/registration/common/constants';
    import { formatAge } from 'utils/index';
    import { AbcDatePickerBar } from '@abc/ui-pc';
    import KanBanTable from './table';

    const { DatePickerBarOptions } = AbcDatePickerBar;

    export default {
        name: 'AppointmentKanbanDialog',
        components: {
        },
        filters: {
            formatDateStr(date) {
                return date.substr(5, date.length);
            },
            formatReserveDate(item, today) {
                const {
                    reserveDate,
                    reserveStart,
                    reserveEnd,
                } = item;

                const date = new Date(reserveDate.replace(/-/g, '/'));
                const todayDate = new Date(today.replace(/-/g, '/'));
                const diff = (todayDate.getTime() - date.getTime()) / 1000;

                let str = '';

                if (diff > 0 && diff <= 86400) {
                    str = '昨天';
                } else if (diff < 0 && diff >= -86400) {
                    str = '明天';
                } else if (diff === 0) {
                    str = '今天';
                } else {
                    str = reserveDate.substr(5, reserveDate.length);
                }

                return `${str} ${reserveStart}~${reserveEnd}`;
            },

            formatReserveInfo({
                reserveDate, reserveStart,
            } = {}) {
                return `${reserveDate} ${reserveStart}`;
            },

            formatReserveInfoType(item) {
                const { reason } = item.oldReserveInfo || {};
                let str = '';
                switch (reason) {
                    case 1:
                        str = '提前签到';
                        break;
                    case 2:
                        str = '延后签到';
                        break;
                    case 3:
                        str = '提前就诊';
                        break;
                    case 4:
                        str = '延后就诊';
                        break;
                    default:
                }
                return str;
            },

            formatDepartmentName(name) {
                if (!name) {
                    return '其他';
                }
                if (name === '默认') {
                    return '--';
                }
                return name;
            },

            formatTypeName(type) {
                if (type === 3) {
                    return '微信';
                }
                return '现场';
            },
        },
        props: {
            value: Boolean,
        },
        data() {
            return {
                StatusV2,
                PayStatusV2,
                BusinessType,
                today: parseTime(new Date(), 'y-m-d', true),

                contentLoading: false,
                currentDateLabel: DatePickerBarOptions.DAY.label,

                summaryData: {
                    dayAfterTomorrowTotalCount: 0,// 后天未退号总数
                    tomorrowTotalCount: 0,// 明天未退号总数
                    todayTotalCount: 0, // 今天未退号总数
                    notRefundedCount: 0, // 未退号总数
                    reserveTotalCount: 0, // 预约总数
                    hadSignInCount: 0, // 已签总数
                    waitingSignInCount: 0, // 待签总数
                    refundedCount: 0, // 退号总数
                },

                params: {
                    offset: 0,
                    limit: window.screen.width > 1366 ? 10 : 8,
                    doctorId: '',
                    date: parseTime(new Date(), 'y-m-d', true),
                },
                tableMinHeight: window.screen.width > 1366 ? 360 : 280,
                dataList: [],
                tableHeight: '',
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'dataPermission',
            ]),

            renderConfig() {
                return KanBanTable.getRenderConfig({
                    formatTypeName: this.$options.filters.formatTypeName,
                    patientStatus: this.patientStatus,
                    formatDepartmentName: this.$options.filters.formatDepartmentName,
                    formatReserveDate: this.$options.filters.formatReserveDate,
                    formatReserveInfo: this.$options.filters.formatReserveInfo,
                    formatReserveInfoType: this.$options.filters.formatReserveInfoType,
                    today: this.today,
                    hasDoctorRegistrationFeePermission: this.hasDoctorRegistrationFeePermission,
                    PayStatusV2,
                    StatusV2,
                });
            },

            hasDoctorRegistrationFeePermission() {
                return this.dataPermission.dashboard.doctorRegistrationFee;
            },

            pageParams() {
                const {
                    limit: pageSize, offset,
                } = this.params;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            datePickerBarOptions() {
                const {
                    todayTotalCount,
                    tomorrowTotalCount,
                    dayAfterTomorrowTotalCount,
                } = this.summaryData;
                let todayLabelStr = '';
                let tomorrowLabelStr = '';
                let dayAfterTomorrowLabelStr = '';
                if (todayTotalCount) {
                    todayLabelStr = `<span>${todayTotalCount}</span>`;
                }
                if (tomorrowTotalCount) {
                    tomorrowLabelStr = `<span>${tomorrowTotalCount}</span>`;
                }
                if (dayAfterTomorrowTotalCount) {
                    dayAfterTomorrowLabelStr = `<span>${dayAfterTomorrowTotalCount}</span>`;
                }
                return [
                    {
                        label: 'day',
                        name: `今天${todayLabelStr}`,
                        getValue() {
                            return new Date();
                        },
                    },
                    {
                        label: 'tomorrow',
                        name: `明天${tomorrowLabelStr}`,
                        getValue() {
                            return new Date(Date.now() + 24 * 60 * 60 * 1000);
                        },
                    },

                    {
                        label: 'afterTomorrow',
                        name: `后天${dayAfterTomorrowLabelStr}`,
                        getValue() {
                            return new Date(Date.now() + 48 * 60 * 60 * 1000);
                        },
                    },
                ];
            },
        },
        created() {
            this.params.doctorId = this.userInfo.id;
            this.fetchTableList();
        },
        mounted() {
            this._timer = setTimeout(() => {
                this.tableHeight = `${this.getTableHeight()}px`;
            });
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            formatAge,

            getTableHeight() {
                const $dialog = this.$refs.dialog;
                const body = $dialog.$el.querySelector('.abc-dialog-body');
                return body.offsetHeight - 258;
            },

            changeDate(picker) {
                this.params.offset = 0;
                this.params.date = picker || this.today;
                if (!picker) {
                    this.currentDateLabel = DatePickerBarOptions.DAY.label;
                }
                this.fetchTableList(true);
            },

            changePageIndex(page) {
                this.params.offset = (page - 1) * this.params.limit;
                this.fetchTableList();
            },

            async fetchTableList(loading = true) {
                this.contentLoading = loading;
                try {
                    const { data } = await RegistrationsAPI.fetchRegistrationsKanban(this.params);
                    if (this.params.offset !== data.offset) return;
                    this.dataList = data.items;
                    const {
                        dayAfterTomorrowTotalCount = 0,
                        tomorrowTotalCount = 0,
                        todayTotalCount = 0,
                        notRefundedCount = 0,
                        reserveTotalCount = 0,
                        hadSignInCount = 0,
                        waitingSignInCount = 0,
                        refundedCount = 0,
                        totalCount = 0,
                    } = data;
                    Object.assign(this.summaryData, {
                        dayAfterTomorrowTotalCount,
                        tomorrowTotalCount,
                        todayTotalCount,
                        notRefundedCount, // 未退号总数
                        reserveTotalCount, // 预约总数
                        hadSignInCount, // 已签总数
                        waitingSignInCount, // 待签总数
                        refundedCount, // 退号总数
                        totalCount, // 总数
                    });

                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            // 患者状态
            patientStatus(patient) {
                let status = {
                    color: '#005ED9',
                };
                switch (patient.statusV2) {
                    case StatusV2.WAITING_SIGN_IN:
                        status = {
                            color: '#FF9933',
                        };
                        break;
                    case StatusV2.WAITING_DIAGNOSE:
                        status = {
                            color: '#005ED9',
                        };
                        break;
                    case StatusV2.DIAGNOSED:
                        status = {
                            color: '#626D77',
                        };
                        break;
                    case StatusV2.EXPIRED:
                    case StatusV2.REFUNED:
                    case StatusV2.CANCELED:
                        status = {
                            color: '#626D77',
                        };
                        break;
                    default:
                }
                return status;
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/theme.scss';

.appointment-kanban-dialog-wrapper {
    .kanban-handler {
        display: flex;
        align-items: center;

        .title {
            font-size: 16px;
            font-weight: bold;
        }

        .quick-select-date-bar {
            display: flex;
            align-items: center;
            margin-left: 16px;
        }
    }

    .kanban-content {
        position: relative;

        h5 {
            margin-bottom: 8px;
            font-weight: bold;
        }

        .abc-table-wrapper.registration-table {
            overflow: hidden;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);

            .table-title {
                border-top: none;
            }

            .table-body {
                margin-bottom: -1px;
            }

            .table-tr {
                height: 40px;
                line-height: 40px;
            }

            .table-td {
                padding: 0 2px;

                img {
                    vertical-align: middle;
                }
            }

            .table-empty {
                top: 50%;

                .label {
                    margin-top: 0;
                    font-size: 14px;
                    color: $T3;
                }

                .icon {
                    display: none;
                }
            }
        }
    }

    .close-wrapper {
        position: absolute;
        top: 8px;
        right: 8px;
    }
}
</style>
