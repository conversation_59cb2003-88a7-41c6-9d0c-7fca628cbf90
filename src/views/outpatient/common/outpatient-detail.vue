<template>
    <div v-abc-loading="contentLoading" class="outpatient-detail-readonly-wrapper">
        <template v-if="outpatientData">
            <div class="title-bar">
                <h4>门诊记录</h4>
                <div class="describe">
                    <span>医生：{{ outpatientData.doctorName || '' }}</span>
                    <span>时间：{{ outpatientData.diagnosedDate | formatCacheTime }}</span>
                </div>
            </div>
            <div class="outpatient-detail">
                <!--主诉-->
                <template v-if="outpatientData.medicalRecord">
                    <div v-for="item in medicalRecordStructList" :key="item.key" class="detail-row">
                        <label>{{ item.label }}</label>
                        <span :class="item.key" v-html="item.htmlText"></span>
                    </div>
                </template>

                <div v-if="outpatientData.questionSheets && outpatientData.questionSheets.length" class="detail-row">
                    <label>问诊单</label>
                    <div
                        v-for="(item, index) in outpatientData.questionSheets"
                        :key="item.id || index"
                        class="question-form-item"
                        @click="viewQuestionForm(item)"
                    >
                        <i class="iconfont cis-icon-update_log"></i>
                        {{ item.name }}
                    </div>
                </div>

                <div
                    v-if="outpatientData.medicalRecord.attachments && outpatientData.medicalRecord.attachments.length"
                    class="detail-row"
                >
                    <label>图片附件</label>
                    <div
                        v-for="(item, index) in outpatientData.medicalRecord.attachments"
                        :key="item.uuid || item.id || index"
                        class="attachments-item"
                        @click.stop="previewIt(item)"
                    >
                        <abc-file-view :file="item" width="40px" height="40px"></abc-file-view>
                    </div>
                </div>

                <div v-if="outpatientData.productForms && outpatientData.productForms.length" class="product">
                    <h5>项目</h5>
                    <div class="content">
                        <ul>
                            <template v-for="form in outpatientData.productForms">
                                <li v-for="(item, index) in form.productFormItems" :key="`${item.id }${ index}`">
                                    <div class="name ellipsis">
                                        {{ item.name }}
                                    </div>

                                    <div
                                        v-if="hasResultExaminations(item.examinationResult).length"
                                        style="margin-right: 20px;"
                                    >
                                        <span
                                            v-if="hasResultExaminations(item.examinationResult).length === 1"
                                            style="color: #2680f7; cursor: pointer;"
                                            @click="
                                                viewReport(
                                                    hasResultExaminations(item.examinationResult)[0].examinationSheetId,
                                                    hasResultExaminations(item.examinationResult)[0].type
                                                )
                                            "
                                        >
                                            查看报告
                                        </span>
                                        <abc-popover
                                            v-else
                                            trigger="click"
                                            placement="bottom-start"
                                            theme="white"
                                            :visible-arrow="false"
                                        >
                                            <span slot="reference" style="color: #2680f7; cursor: pointer;">
                                                查看报告
                                            </span>
                                            <div class="view-examination-result-wrapper">
                                                <div
                                                    v-for="eResult in hasResultExaminations(item.examinationResult)"
                                                    :key="eResult.examinationSheetId"
                                                    @click="viewReport(eResult.examinationSheetId, eResult.type)"
                                                >
                                                    {{ eResult.name }}
                                                </div>
                                            </div>
                                        </abc-popover>
                                    </div>

                                    <div class="unitCount">
                                        {{ item.unitCount }}{{ item.unit || '次' }}
                                    </div>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>

                <div v-if="canViewHistoryPr" class="prescription">
                    <template
                        v-if="
                            outpatientData &&
                                outpatientData.prescriptionWesternForms &&
                                outpatientData.prescriptionWesternForms.length
                        "
                    >
                        <div v-for="(form, index) in outpatientData.prescriptionWesternForms" :key="form.id">
                            <h5 v-if="outpatientData.prescriptionWesternForms.length > 1">
                                西药{{ (index + 1) | numToChinese }}
                            </h5>
                            <h5 v-else>
                                西药
                            </h5>
                            <ul>
                                <li v-for="(item, index) in form.prescriptionFormItems" :key="`${item.id }${ index}`">
                                    <div class="name ellipsis">
                                        {{ item.name }} <span>{{ item.productInfo | getSpec }}</span>
                                    </div>

                                    <div class="unitCount">
                                        {{ item.unitCount }}{{ item.unit }}
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </template>

                    <template
                        v-if="
                            outpatientData &&
                                outpatientData.prescriptionInfusionForms &&
                                outpatientData.prescriptionInfusionForms.length
                        "
                    >
                        <div v-for="(form, pIndex) in outpatientData.prescriptionInfusionForms" :key="form.id">
                            <h5 v-if="outpatientData.prescriptionInfusionForms.length > 1">
                                注射{{ (pIndex + 1) | numToChinese }}
                            </h5>
                            <h5 v-else>
                                注射
                            </h5>
                            <ul
                                v-for="(group, index) in transGroup(form)"
                                :key="`${form.id }${ index}`"
                                class="infusion"
                            >
                                <li>
                                    {{ group[0].groupId ? `组${ group[0].groupId}` : '' }}
                                    <div class="row-line"></div>
                                </li>
                                <li v-for="item in group" :key="item.id">
                                    <div class="name ellipsis">
                                        {{ item.name }} <span>{{ item.productInfo | getSpec }}</span>
                                    </div>

                                    <div class="unitCount">
                                        {{ item.unitCount }}{{ item.unit }}
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </template>

                    <template
                        v-if="
                            outpatientData &&
                                outpatientData.prescriptionChineseForms &&
                                outpatientData.prescriptionChineseForms.length
                        "
                    >
                        <div v-for="(form, index) in outpatientData.prescriptionChineseForms" :key="form.id">
                            <h5 v-if="outpatientData.prescriptionChineseForms.length > 1">
                                中药{{ (index + 1) | numToChinese }}
                            </h5>
                            <h5 v-else>
                                中药
                            </h5>
                            <ul class="chinese" style="margin: 0;">
                                <li v-for="(item, index) in form.prescriptionFormItems" :key="`${item.id }${ index}`">
                                    <div class="name ellipsis">
                                        {{ item.name }} {{ item.unitCount }}{{ item.unit }}
                                    </div>
                                </li>
                            </ul>
                            <ul style="margin: 0; margin-top: 6px; color: #626d77;">
                                {{
                                    form.doseCount
                                }}剂
                            </ul>
                        </div>
                    </template>

                    <template
                        v-if="
                            outpatientData &&
                                outpatientData.prescriptionExternalForms &&
                                outpatientData.prescriptionExternalForms.length
                        "
                    >
                        <div v-for="(form, pIndex) in outpatientData.prescriptionExternalForms" :key="form.id">
                            <h5 v-if="outpatientData.prescriptionExternalForms.length > 1">
                                外治{{ (pIndex + 1) | numToChinese }}
                            </h5>
                            <h5 v-else>
                                外治
                            </h5>
                            <ul
                                v-for="(item, index) in form.prescriptionFormItems"
                                :key="`${item.id }${ index}`"
                                class="external"
                            >
                                <li>
                                    <div>
                                        {{ index + 1 }}.{{ item.name }} {{ item.unitCount }}{{ item.unit || '贴' }}
                                    </div>
                                    <div class="row-line"></div>
                                </li>
                                <li v-if="item.acupoints" class="acupoints">
                                    <div
                                        v-for="acupoint in item.acupoints"
                                        :key="acupoint.id || acupoint.name"
                                        class="name"
                                    >
                                        {{ acupoint.position }}{{ acupoint.name }}
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </template>

                    <div v-if="noETPR" class="no-et-pr">
                        没有开具处方
                    </div>

                    <div v-if="outpatientData.medicalRecord.doctorAdvice" class="doctor-advise-wrapper">
                        <h5>医嘱事项</h5>
                        <div class="content" v-html="outpatientData.medicalRecord.doctorAdvice"></div>
                    </div>
                </div>

                <div v-else class="not-allow-view-pr">
                    管理员已关闭处方共享查看
                </div>
            </div>
        </template>

        <dialog-detail-examination
            v-if="showReport"
            :id="examinationSheetId"
            v-model="showReport"
            :type="examType"
        ></dialog-detail-examination>
    </div>
</template>

<script>
    import OutpatientAPI from 'src/api/outpatient';
    import DialogDetailExamination from '@/views/crm/common/package-detail/dialog-detail-examination.vue';

    import { mapGetters } from 'vuex';
    import GenerateFormDialog from '../../chronic-care/form-dialog';
    import { ChronicCareFormPrintType } from 'views/chronic-care/constants';
    import { ANONYMOUS_ID } from '@abc/constants';
    import { medicalImagingViewerDialogService } from '@/medical-imaging-viewer/store/medical-imaging-viewer-dialog';
    import { getMRStructList } from 'views/outpatient/common/medical-record/utils.js';

    export default {
        name: 'RecordHistoryCard',
        components: {
            DialogDetailExamination,
        },
        props: {
            outpatientSheetId: {
                type: String,
                required: true,
            },
        },

        data() {
            return {
                outpatientData: null,
                contentLoading: false,
                currentIndex: -1,
                examinationSheetId: null,
                showReport: false,
                examType: '',
            };
        },
        async created() {
            await this.$store.dispatch('initAssistDoctors');
        },
        computed: {
            ...mapGetters(['userInfo', 'historyPrescriptionInOutpatient','isAssistDoctorHisCase']),
            ...mapGetters('viewDistribute',['viewDistributeConfig']),

            medicalRecordStructList() {
                return getMRStructList(this.outpatientData.medicalRecord);
            },

            // 没有开具 检查治疗 || 处方
            noETPR() {
                return (
                    this.outpatientData &&
                    this.outpatientData.prescriptionChineseForms.length === 0 &&
                    this.outpatientData.prescriptionWesternForms.length === 0 &&
                    this.outpatientData.prescriptionInfusionForms.length === 0 &&
                    this.outpatientData.prescriptionExternalForms.length === 0
                );
            },
            /**
             * @desc 能否看到处方,只在门诊模块生效
             * 对于32位0的doctorId不受权限控制
             * <AUTHOR>
             * @date 2020/01/17 16:35:32
             */
            canViewHistoryPr() {
                if (!this.outpatientData) return false;
                if (this.outpatientData.doctorId === ANONYMOUS_ID) return true;

                const {
                    id, moduleIds,
                } = this.userInfo;
                return this.historyPrescriptionInOutpatient || id === this.outpatientData.doctorId || moduleIds === '0' || this.isAssistDoctorHisCase(this.outpatientData.doctorId) ;
            },
        },

        watch: {
            outpatientSheetId: {
                handler (val) {
                    if (val) {
                        this.fetchDetail();
                    } else {
                        this.outpatientData = null;
                    }
                },
                immediate: true,
            },
        },

        methods: {
            async fetchDetail() {
                this.contentLoading = true;
                const { data } = await OutpatientAPI.fetch(this.outpatientSheetId);
                this.outpatientData = data;
                this.contentLoading = false;
            },

            /**
             * @desc 能够查看检查检验报告
             * <AUTHOR> Yang
             * @date 2020-10-22 09:22:22
             */
            hasResultExaminations(result) {
                if (Array.isArray(result)) {
                    return result.filter((item) => {
                        return item.status === 1;
                    });
                }
                return [];
            },
            /**
             * @desc 查看检验报告
             * <AUTHOR>
             * @date 2019/12/19 16:01:55
             */
            viewReport(id, type) {
                this.showReport = true;
                this.examinationSheetId = id;
                this.examType = type;
            },

            previewIt(item) {
                medicalImagingViewerDialogService.previewImageAttachment({
                    attachments: this.outpatientData.medicalRecord.attachments,
                    attachment: item,
                });
            },

            /**
             * @desc 转换输液处方分组
             * <AUTHOR>
             * @date 2019/05/09 15:17:23
             */
            transGroup(form) {
                const groupMap = new Map();
                const list = [];
                const noGroupList = [];
                form.prescriptionFormItems.forEach((item) => {
                    if (item.groupId) {
                        const groupItem = groupMap.get(item.groupId);
                        if (groupItem) {
                            groupItem.push(item);
                        } else {
                            groupMap.set(item.groupId, [item]);
                        }
                    } else {
                        noGroupList.push(item);
                    }
                });
                groupMap.forEach((item) => {
                    list.push(item);
                });
                return list.concat(noGroupList);
            },

            async viewQuestionForm({ id }) {
                const { data } = await OutpatientAPI.fetchOutpatientQuestionForm(id);
                GenerateFormDialog({
                    formTemplateData: data.form,
                    readonly: true,
                    disabled: true,
                    printFormType: ChronicCareFormPrintType.CONSULTATION,
                });
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';
    @import 'src/styles/mixin';

    .outpatient-detail-readonly-wrapper {
        .title-bar {
            position: relative;
            color: $T1;

            h4 {
                height: 20px;
                font-size: 16px;
                font-weight: bold;
                line-height: 20px;
            }

            .describe {
                display: flex;
                align-items: center;
                height: 26px;
                margin-top: 4px;

                > span {
                    margin-right: 16px;
                    color: $T2;
                }
            }
        }

        .outpatient-detail {
            position: relative;
            padding: 24px 12px;

            > .detail-row {
                display: flex;
                align-items: flex-start;
                margin-bottom: 12px;

                span {
                    flex: 1;
                    line-height: 20px;
                    color: $T2;
                }

                .question-form-item {
                    display: flex;
                    align-items: center;
                    width: 146px;
                    height: 50px;
                    padding: 0 12px;
                    margin-right: 8px;
                    cursor: pointer;
                    border: 1px solid $P6;

                    @include ellipsis;

                    .iconfont {
                        margin-right: 4px;
                    }
                }
            }

            > div:last-child {
                margin-bottom: 0;
            }

            label {
                min-width: 80px;
                margin-right: 8px;
                font-weight: 400;
                line-height: 20px;
            }

            .small {
                margin-right: 8px;
                font-size: 12px;
                line-height: 12px;
            }

            .oralExamination {
                > span {
                    display: inline-block;
                    padding: 4px;
                    font-family: Roboto;
                    font-size: 12px;
                    line-height: 12px;
                }

                span.top-left {
                    border-right: 1px solid #878c92;
                    border-bottom: 1px solid #878c92;
                }

                span.top-right {
                    border-bottom: 1px solid #878c92;
                    border-left: 1px solid #878c92;
                }

                span.bottom-left {
                    border-top: 1px solid #878c92;
                    border-right: 1px solid #878c92;
                }

                span.bottom-right {
                    border-top: 1px solid #878c92;
                    border-left: 1px solid #878c92;
                }
            }

            .obstetricalHistory {
                .menstruation {
                    display: inline-flex;
                    align-items: center;

                    > span {
                        display: inline-flex;
                        flex-direction: column;
                        margin: 0 8px;
                        text-align: center;

                        span {
                            height: 12px;
                            font-size: 12px;
                            line-height: 12px;
                        }

                        .frasl {
                            width: 100%;
                            height: 1px;
                            border-bottom: 1px solid $P1;
                        }
                    }
                }
            }

            .line-title {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                font-size: 12px;
                color: $T2;

                &::before {
                    width: 110px;
                    height: 1px;
                    margin-top: 8px;
                    content: ' ';
                    background-color: $P6;
                }

                &::after {
                    width: 110px;
                    height: 1px;
                    margin-top: 8px;
                    content: ' ';
                    background-color: $P6;
                }
            }

            .cutline {
                width: 100%;
                height: 1px;
                background-color: $P6;
            }

            .history-title {
                padding: 16px;
                border-bottom: 1px solid $P6;

                > div {
                    display: flex;
                    margin-bottom: 6px;
                }

                > div:last-child {
                    margin-bottom: 0;
                }

                span {
                    flex: 1;
                    line-height: 20px;
                    color: $T2;
                }

                label {
                    min-width: 56px;
                    margin-right: 8px;
                    font-weight: 400;
                    line-height: 20px;
                }

                .small {
                    margin-right: 8px;
                    font-size: 12px;
                    line-height: 12px;
                }

                .line-title {
                    margin-bottom: 10px;
                }

                .oral-examination {
                    > span {
                        display: inline-block;
                        padding: 4px;
                        font-family: Roboto;
                        font-size: 12px;
                        line-height: 12px;
                    }

                    span.top-left {
                        border-right: 1px solid #878c92;
                        border-bottom: 1px solid #878c92;
                    }

                    span.top-right {
                        border-bottom: 1px solid #878c92;
                        border-left: 1px solid #878c92;
                    }

                    span.bottom-left {
                        border-top: 1px solid #878c92;
                        border-right: 1px solid #878c92;
                    }

                    span.bottom-right {
                        border-top: 1px solid #878c92;
                        border-left: 1px solid #878c92;
                    }
                }
            }

            h5 {
                height: 20px;
                margin-top: 24px;
                font-size: 14px;
                font-weight: normal;
                line-height: 20px;
                color: $T1;
            }

            .product {
                padding-bottom: 12px;
                border-bottom: 1px solid $P6;
            }

            .not-allow-view-pr {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                min-height: 120px;
                color: #c9c6a8;
            }

            ul:not(.chinese) li {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 6px;

                .abc-button {
                    margin: 0 8px;
                }

                .name {
                    flex: 1;
                    color: $T2;
                }

                .name span {
                    color: $T3;
                }

                .unitCount {
                    min-width: 40px;
                    color: $T2;
                    text-align: right;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .infusion {
                margin-bottom: 4px;
            }

            .infusion li:first-child {
                position: relative;
                display: flex;
                align-items: center;
                font-size: 12px;
                color: $T3;

                .row-line {
                    flex: 1;
                    height: 1px;
                    margin-left: 8px;
                    background-color: $P6;
                }
            }

            .external li:first-child {
                position: relative;
                display: flex;
                align-items: center;
                font-size: 14px;
                color: $T2;

                > div {
                    z-index: 1;
                    padding-right: 8px;
                }

                .row-line {
                    flex: 1;
                    height: 1px;
                    margin-left: 8px;
                    background-color: $P6;
                }
            }

            .external .acupoints {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;

                > .name {
                    flex: none;
                    min-width: 44px;
                    margin-right: 8px;
                }
            }

            ul.chinese {
                font-size: 0;

                li {
                    display: inline-block;
                    min-width: 100px;
                    margin-top: 6px;
                    margin-right: 4px;
                    font-size: 14px;
                    color: $T2;
                }
            }

            .no-et-pr {
                padding: 40px 0 30px;
                color: $T3;
                text-align: center;
            }

            .doctor-advise-wrapper {
                margin-top: 16px;

                .content {
                    margin-top: 8px;
                    color: $T2;
                }
            }

            .attachments-item {
                margin-right: 8px;
                cursor: pointer;
            }

            .abc-file-viewer .view-box img {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                max-width: 40px;
                max-height: 40px;
                margin: auto;
            }

            .abc-file-viewer .view-box .box {
                background-position: center center;
            }
        }
    }
</style>
