<template>
    <abc-dialog
        v-if="visibleDialog"
        :value="visibleDialog"
        title="查看报告"
        data-cy="exam-inspect-report-list-dialog"
        :append-to-body="appendToBody"
        full
        custom-class="report-list-dialog"
        content-styles="padding: 0"
        @input="val => switchDialog(val)"
    >
        <div class="content-wrap">
            <div class="content-left">
                <div v-if="showTab" class="content-left-tab">
                    <abc-tabs
                        v-model="curType"
                        :option="examOptions"
                        size="middle"
                        type="outline"
                        @change="handleTabChange"
                    ></abc-tabs>
                </div>

                <div
                    v-abc-scroll-loader="{
                        methods: fetchReportList,
                        isLast,
                    }"
                    v-abc-loading="loading"
                    class="report-list-wrapper"
                    :style="reportListWrapperStyle"
                >
                    <div>
                        <div
                            v-for="item in reportTabs"
                            :key="item.id"
                            class="report-item"
                            :class="{
                                'active': item.id === currentId,
                                'disabled': item.status !== InspectStatus.EXMAINATED
                            }"
                            @click="changeReport(item)"
                        >
                            <div class="item-name ellipsis">
                                {{ item.examinationName }}
                            </div>
                            <div
                                class="abnormal-info"
                                :class="{
                                    blue: item.isAbnormal === 0,
                                    red: abnormalStr(item) === '阳性'
                                }"
                            >
                                {{ abnormalStr(item) }}
                            </div>
                            <div class="item-date">
                                {{ formatDate(item.created) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content-right">
                <!-- 检验历史报告 -->
                <template v-if="examType === EXAMINATION_TYPE.EXAMINATION">
                    <examination-report
                        :id="currentId"
                        :allow-exam-report-print="allowExamReportPrint"
                    ></examination-report>
                </template>

                <!-- 检查历史报告 -->
                <hospital-report-content
                    v-if="examType === EXAMINATION_TYPE.INSPECT"
                    :id="currentId"
                    :allow-exam-report-print="allowExamReportPrint"
                ></hospital-report-content>
            </div>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { InspectStatus } from '@/views-ophthalmology/constants';
    import {
        isSameDay, parseTime,
    } from '@abc/utils-date';
    import { ANONYMOUS_ID } from '@abc/constants';
    import CrmAPI from 'api/crm';
    import ExaminationReport from '@/views/examination/readonly-report.vue';
    import { EXAMINATION_TYPE } from '@/utils/constants';
    import ExaminationAPI from 'api/examination';
    import {
        GoodsTypeEnum,
        GoodsSubTypeEnum,
    } from '@abc/constants';
    import HospitalReportContent from '@/views-hospital/inspect-diagnosis/common/hospital-inspect-report.vue';

    export default {
        name: 'ReportListDialog',
        components: {
            HospitalReportContent,
            ExaminationReport,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            patientId: {
                type: String,
                default: '',
            },
            examinationId: {
                type: String,
                default: '',
            },
            type: {
                type: Number,
                default: undefined,
            },
            outReportList: {
                type: Array,
                default: () => ([]),
            },
            appendToBody: {
                type: Boolean,
                default: true,
            },
            isFunctionDialog: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                visible: false,
                loading: false,

                EXAMINATION_TYPE,
                InspectStatus,
                currentId: this.examinationId,
                tableConfig: [],
                writable: false,
                formData: {},
                reportVersion: null, //1眼科检查
                pageParams: {
                    status: 1,
                    offset: 0,
                    limit: 100,
                },
                reportList: [],
                examType: '',
                isLast: false,

                curType: this.type || GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test,
                curSelectedItem: null,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewComponents']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            allowExamReportPrint() {
                return this.viewDistributeConfig.Print.allowExamReportPrint;
            },
            isSupportViewInspectReport() {
                return this.viewDistributeConfig.Outpatient.isSupportViewInspectReport;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(bool) {
                    this.$emit('input', bool);
                },
            },
            visibleDialog() {
                if (this.isFunctionDialog) {
                    return this.visible;
                }
                return this.showDialog;
            },

            reportTabs() {
                return this.outReportList.length ? this.outReportList : this.reportList;
            },
            examOptions() {
                return [
                    {
                        label: '检查',
                        value: GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test,
                    },
                    {
                        label: '检验',
                        value: GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect,
                    },
                ];
            },

            reportListWrapperStyle() {
                return {
                    height: this.outReportList.length ? '100%' : 'calc(100% - 56px)',
                };
            },

            showTab() {
                return this.outReportList.length === 0 && this.isSupportViewInspectReport;
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
            patientId: {
                async handler() {
                    if (this.outReportList.length !== 0) {
                        return;
                    }
                    this.curSelectedItem = {
                        id: this.examinationId,
                        type: this.type,
                    };
                    await this.fetchReportList();
                },
                immediate: true,
            },
        },
        async mounted() {
            if (this.outReportList.length !== 0) {
                this.currentId = this.outReportList[0].id;
                this.examType = this.outReportList[0].type;
            }
        },
        methods: {
            switchDialog(val) {
                if (this.isFunctionDialog) {
                    this.visible = val;
                } else {
                    this.showDialog = val;
                }
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            async fetchReportList() {
                if (!this.patientId || this.patientId === ANONYMOUS_ID) {
                    this.reportList = [];
                    this.loading = false;
                    return;
                }
                this.loading = true;
                try {
                    const { data } = await CrmAPI.fetchExamination(this.patientId, {
                        ...this.pageParams,
                        type: this.curType,
                    });
                    const {
                        examinationSheetPatients = [],
                        totalCount,
                    } = data;
                    const pushListData = this.getPushReportList(this.reportList, examinationSheetPatients);
                    this.reportList.push(...pushListData);
                    this.isLast = this.reportList.length === totalCount;
                    if (!this.isLast) {
                        this.pageParams.offset += this.pageParams.limit;
                    }

                    /**
                     * 构造数据的情况：
                     * 1. 初始化列表中不存在该条数据
                     * 2. 切换 tab 再切回来，列表刷新构造的数据丢失，需要重新构造
                     */
                    if (this.curSelectedItem.type === this.curType) {
                        const currentObject = this.reportList.find((item) => item.id === this.curSelectedItem.id);

                        if (currentObject) {
                            this.examType = currentObject?.type;
                        } else {
                            // 分页数据，当前报告不在list中，构造一条，滚动加载拉到的时候排除当前这条
                            this.createCurrentExaminationItem();
                        }
                    }
                } catch (error) {
                    console.error(error);
                } finally {
                    this.loading = false;
                }
            },
            async createCurrentExaminationItem() {
                if (!this.examinationId) return;
                try {
                    this.loading = true;
                    const { data } = await ExaminationAPI.getExaminationDetail(this.examinationId);
                    const {
                        id,
                        status,
                        examinationName,
                        isAbnormal,
                        created,
                        type,
                    } = data;
                    this.reportList.push({
                        id,
                        status,
                        examinationName,
                        isAbnormal,
                        created,
                        type,
                    });
                    this.examType = type;
                } catch (e) {
                    console.error(e);
                }
            },
            changeReport(item) {
                if (item.status === InspectStatus.WAITING) return;
                const {
                    id, type,
                } = item;
                this.currentId = id;
                this.examType = type;
                this.curSelectedItem = item;
            },

            formatDate(string) {
                const createdTime = new Date(string);
                const now = new Date();
                if (isSameDay(createdTime, now)) return '今天';
                return parseTime(createdTime, 'm-d');
            },

            abnormalStr(item) {
                if (!item.abnormalInfo) return '';
                const {
                    abnormalCount,
                    simpleInfo,
                } = item.abnormalInfo || {};
                if (simpleInfo) return simpleInfo;
                if (abnormalCount) return `${abnormalCount}项异常`;
                return '';
            },
            getPushReportList(currentList, rows) {
                const currentInCurrentList = currentList.findIndex((item) => item.id === this.examinationId) > -1;
                const currentInRows = rows.findIndex((item) => item.id === this.examinationId) > -1;
                // 当前id同时在list 和返回数据中，说明之前已经构造过，这次push要过滤掉
                if (currentInCurrentList && currentInRows) {
                    return rows.filter((x) => x.id !== this.examinationId);
                }
                return rows;
            },
            resetFetchParams(type) {
                this.pageParams = {
                    status: 1,
                    offset: 0,
                    limit: 100,
                };
                this.isLast = false;
                this.reportList = [];
                this.curType = type;
            },
            handleTabChange(v) {
                this.resetFetchParams(v);
                this.fetchReportList();
            },
        },
    };
</script>

<style lang="scss">
@import "styles/abc-common.scss";

.report-list-dialog {
    width: auto;
    min-width: 1300px;
    padding: 0;

    .abc-dialog-wrapper .abc-dialog-body {
        padding: 0;
    }

    .content-wrap {
        height: 100%;

        .content-left {
            position: fixed;
            min-width: 270px;
            height: calc(100% - var(--abc-dialog-header-min-height));
            border-right: 1px solid $P6;

            .content-left-tab {
                padding: 12px 16px;
                border-bottom: 1px solid $P6;
            }

            .report-list-wrapper {
                height: calc(100% - 56px);
                padding: 12px 2px 12px 12px;
                overflow-y: scroll;

                @include scrollBar();

                .report-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 246px;
                    height: 44px;
                    padding: 9px;
                    margin-bottom: 12px;
                    font-size: 14px;
                    cursor: pointer;
                    border-radius: var(--abc-border-radius-small);

                    .item-name {
                        max-width: 120px;

                        >span {
                            margin-left: 9px;
                        }
                    }

                    .abnormal-info {
                        width: 55px;
                        margin: 0 4px;
                        font-size: 12px;
                        color: $Y2;
                        text-align: right;

                        &.blue {
                            color: #004c97;
                        }

                        &.red {
                            color: #ff0000;
                        }
                    }

                    .item-date {
                        width: 46px;
                        font-size: 12px;
                        color: $T2;
                        text-align: right;
                    }

                    &.active {
                        color: white;
                        background-color: #40acff;

                        .item-date {
                            color: white;
                        }

                        .normal {
                            color: white;
                        }

                        .abnormal-info {
                            color: white;
                        }
                    }

                    &.disabled {
                        cursor: not-allowed;
                        background: $P4;
                    }
                }
            }
        }

        .content-right {
            width: 1150px;
            height: 100%;
            padding: 24px 14px 24px 24px;
            margin-left: 270px;
            overflow-y: scroll;

            @include scrollBar();
        }
    }
}
</style>
