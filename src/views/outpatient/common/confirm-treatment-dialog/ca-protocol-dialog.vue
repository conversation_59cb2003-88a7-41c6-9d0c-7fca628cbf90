<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        :show-close="scene === 'setting'"
        title="使用电子签名开具处方"
        append-to-body
        custom-class="ca-protocol-dialog"
        content-styles="width: 920px; height: 700px; padding: 24px 14px 24px 24px;"
    >
        <div class="ca-protocol-dialog-content">
            <strong class="title">《数字证书服务协议》</strong>

            <p>
                四川省数字证书认证管理中心有限公司（简称“四川CA”）是工业和信息化部批准的电子认证服务机构和国家密码管理局批准的电子政务电子认证服务机构，遵照《中华人民共和国电子签名法》为用户提供数字证书相关的电子认证服务。
            </p>
            <p>
                本协议中的“用户”指数字证书持有人以及申请使用数字证书的实体；“数字证书”是指包含数字证书使用者身份信息和公开密钥的电子文件，能够标识用户的身份，用于网络系统登录身份认证及确保用户使用数字证书进行签名的电子数据的真实性、完整性、机密性和抗抵赖性。为明确各方权利和义务，四川CA和用户就数字证书的申请和使用等事项达成本协议，本协议自用户或其授权代表/经办人在线下完成签章或在线上点击、勾选、确认等表示同意的动作后生效，由双方共同遵守执行。
            </p>
            <p>
                （一）用户知晓数字证书用于在网络上标识用户身份及电子签名，应按照申请时预定的目的进行使用，不作其他任何用途。用户应正确、规范使用数字证书，所有使用数字证书在网上作业中的活动均视为用户所为，用户对使用数字证书的行为负责，因此而产生的相关后果由用户自行承担。
            </p>
            <p>
                （二）数字证书申请者及其授权代表/经办人同意授权四川CA合法收集相关资料和信息用于电子认证业务，并按照相关法律法规及监管要求妥善使用、存储和披露此类资料信息。四川CA收集的信息因证书类型（对于企业/单位用户，四川CA可能收集单位证照、法定代表人身份信息、授权代表/经办人身份信息等；对于个人用户，四川CA可能收集个人身份证明、姓名、实名手机号码、生物特征等信息。）不同而有所差异。
            </p>
            <p>
                四川CA 对于个人信息的收集、使用、存储与披露将严格遵守国家法律法规，并符合《四川CA电子认证业务规则》（CPS，详见https://www.scca.com.cn）中个人信息保护的相关政策。
            </p>
            <p>
                若用户勾选个人敏感信息弹窗，则视为用户认可、同意并授权四川CA合法收集相关资料和信息用于电子认证业务，并按照相关法律法规及监管要求妥善使用、存储和披露此类资料信息。
            </p>
            <p>
                （三）用户申请数字证书时应提供真实、完整、准确的资料和信息。如用户故意或过失提供不真实资料和信息而导致四川CA签发数字证书错误的，造成相关各方损失的，由用户承担因此所造成的相关责任和损失。
            </p>
            <p>
                （四）非四川CA过错而导致签发数字证书延迟、中断或者无法签发的，四川CA不承担赔偿责任。
            </p>
            <p>
                （五）用户提供的资料信息(如机构名称、统一社会信用代码、个人用户的姓名、身份证号等)在数字证书有效期内变更的，应当及时书面告知四川CA，并终止使用该数字证书。
            </p>
            <p>
                （六）数字证书一律不得转让、转借或转用。因转让、转借或转用而产生的相关后果应当由用户自行承担。如因用户原因致使数字证书私钥泄露、损毁或者丢失的，损失由用户自行承担。如证书私钥在数字证书有效期内泄露、损毁、丢失或可能泄露、损毁、丢失的，用户应及时向四川CA申请办理吊销手续。数字证书吊销申请受理后24小时内生效，期间用户自行承担使用数字证书造成的一切责任。
            </p>
            <p>
                （七）用户应确保其应用系统能为数字证书提供安全的应用环境，若因网络、主机、操作系统或其他软硬件环境等存在安全漏洞，由此导致的安全事故及相关后果，四川CA不承担责任。
            </p>
            <p>
                （八）用户必须在数字证书有效期内使用该数字证书，数字证书在有效期届满或已吊销后自动失效；用户不得使用已失效、已失密/可能失密、已过期、被中止的数字证书。
            </p>
            <p>
                （九）用户终止使用数字证书时，应当立即申请吊销数字证书；单位证书用户单位解散、注销或个人证书用户失去民事行为能力、死亡，法定责任人需要携带相关证明文件及原证书，向四川CA请求吊销用户数字证书。相关责任人应当承担其数字证书在吊销生效前相关行为所产生的责任。数字证书吊销申请受理后24小时内生效，期间用户自行承担使用数字证书造成的一切责任。
            </p>
            <p>
                （十）下列情形之一，四川CA有权吊销数字证书并不承担任何责任。由此给四川CA造成损失的，用户应当向四川CA承担赔偿责任：
            </p>
            <p>
                1、提供的资料或信息不真实、不完整或不准确的；
            </p>
            <p>
                2、数字证书相关信息有变更或数字证书私钥已经丢失或可能丢失，未终止使用该数字证书并通知四川CA的；
            </p>
            <p>
                3、用户未履行本协议所规定的责任和义务；
            </p>
            <p>
                4、法律、法规规定的其他情形。
            </p>
            <p>
                （十一）四川CA承诺，由于四川CA过错导致数字证书签发错误或数字证书私钥被破译并对用户造成损失的，四川CA按照《四川CA电子认证业务规则》（CPS，详见https://www.scca.com.cn）承担相应的赔偿责任。
            </p>
            <p>
                （十二）<span class="special-text">用户在勾选“同意”时起，本协议生效。</span>本协议生效后，四川CA及用户均应依诚实守信之原则履行协议项之全部义务，若因本协议的履行、解释、赔偿所产生的一切争议，经双方协商不能解决，任何一方均可将争议提交四川CA所在地有管辖权的人民法院诉讼解决。
            </p>
        </div>

        <div slot="footer" class="dialog-footer">
            <template v-if="scene === 'setting'">
                <abc-button @click="visible = false">
                    确定
                </abc-button>
            </template>
            <template v-else>
                <abc-button type="blank" @click="cancel">
                    不同意
                </abc-button>
                <abc-button @click="submit">
                    同意
                </abc-button>
            </template>
        </div>
    </abc-dialog>
</template>

<script>
    export default {
        name: 'CaProtocolDialog',
        props: {
            callback: {
                type: Function,
                default: () => {},
            },
            scene: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                visible: false,
            };
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        methods: {
            submit() {
                this.callback && this.callback(true);
                this.visible = false;
            },
            cancel() {
                this.callback && this.callback(false);
                this.visible = false;
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';
@import 'src/styles/mixin';

.ca-protocol-dialog {
    .abc-dialog-body {
        overflow: scroll;

        @include scrollBar(true);

        .ca-protocol-dialog-content {
            display: flex;
            flex-direction: column;
            font-size: 14px;

            .title {
                width: 100%;
                margin-bottom: 20px;
                font-weight: bold;
                text-align: center;
            }

            p {
                width: 100%;
                line-height: 30px;
                color: #626d77;
                text-indent: 2em;
            }

            .special-text {
                color: red;
            }
        }
    }
}
</style>
