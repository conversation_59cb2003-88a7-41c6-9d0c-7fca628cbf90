<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        content-styles="width: 498px;padding: 24px; overflow: visible;"
        :title="title"
        :show-close="false"
        custom-class="preview-fee-dialog"
        :shadow="showQrCodeDialog"
        tabindex="-1"
        append-to-body
        @open="getPrintOpt"
    >
        <template v-if="!!patient.name">
            <abc-button
                v-if="canChargeReview && !featureSupportRatioPrice"
                slot="title-append"
                variant="ghost"
                style="margin-left: auto;"
                @click="reviewChargeHandle"
            >
                费用预览
            </abc-button>

            <div class="dialog-content preview-fee-dialog clearfix">
                <template v-if="chargeStatus">
                    <template v-if="canChargeReview">
                        <div class="preview-item">
                            添加了医嘱，有新增费用
                        </div>
                        <div v-if="outpatientShowTotalPrice" class="preview-item">
                            <label>新增费用：</label>
                            <div>
                                <p v-if="hiddenOutpatientTotalPrice" class="hidden-icon">
                                    ******
                                    <i class="iconfont cis-icon-eye_disable" style="margin-left: 10px;" @click="changeShowPrice"></i>
                                </p>
                                <p v-else>
                                    <abc-money :value="outpatientTotalPrice"></abc-money>
                                    <i class="iconfont cis-icon-eye" style="margin-left: 10px;" @click="changeShowPrice"></i>
                                </p>
                                <p v-if="autoSendOrderInfoSwitch" class="tips">
                                    <wechat-push
                                        :patient="newPatient"
                                        :chain-id="chainId"
                                        @update-patient="newPatient = $event"
                                    >
                                        <template
                                            #default="{
                                                isPush, wxBindStatus,wxStatus
                                            }"
                                        >
                                            <push-text
                                                :is-push="isPush"
                                                :wx-bind-status="wxBindStatus"
                                                :wx-status="wxStatus"
                                                :patient="newPatient"
                                                :is-consultation="isConsultation"
                                                @update-info="handleUpdateInfo"
                                                @changeDialogVisible="visible => { showQrCodeDialog = visible }"
                                            ></push-text>
                                        </template>
                                    </wechat-push>
                                </p>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="preview-item">
                            未添加医嘱，无新增费用
                        </div>
                        <div v-if="outpatientShowTotalPrice" class="preview-item">
                            <label>新增费用：</label>
                            <abc-money :value="0" is-show-space></abc-money>
                        </div>
                    </template>
                </template>

                <template v-else>
                    <div class="preview-item">
                        <label>患者：</label>
                        <div>{{ patient.name }}</div>
                    </div>
                    <div class="preview-item">
                        <label>诊断：</label>
                        <div class="diagnosis-html" v-html="diagnosisHtml"></div>
                    </div>
                    <div v-if="outpatientShowTotalPrice" class="preview-item">
                        <label>费用：</label>
                        <div class="fee-wrapper">
                            <template v-if="outpatientShowTotalPrice">
                                <p v-if="hiddenOutpatientTotalPrice" class="hidden-icon">
                                    ******
                                    <i class="iconfont cis-icon-eye_disable" style="margin-left: 10px;" @click="changeShowPrice"></i>
                                </p>
                                <p v-else>
                                    <abc-money :value="outpatientTotalPrice"></abc-money>
                                    <i class="iconfont cis-icon-eye" style="margin-left: 10px;" @click="changeShowPrice"></i>
                                </p>
                            </template>

                            <p v-if="autoSendOrderInfoSwitch" class="tips">
                                <wechat-push
                                    :patient="newPatient"
                                    :chain-id="chainId"
                                    @update-patient="newPatient = $event"
                                >
                                    <template
                                        #default="{
                                            isPush, wxBindStatus,wxStatus
                                        }"
                                    >
                                        <push-text
                                            :is-push="isPush"
                                            :wx-bind-status="wxBindStatus"
                                            :wx-status="wxStatus"
                                            :patient="newPatient"
                                            :is-consultation="isConsultation"
                                            @update-info="handleUpdateInfo"
                                            @changeDialogVisible="visible => { showQrCodeDialog = visible }"
                                        ></push-text>
                                    </template>
                                </wechat-push>
                            </p>
                        </div>
                    </div>
                </template>

                <div class="preview-item">
                    <label>费别：</label>
                    <div>{{ shebaoChargeTypeDesc }}</div>
                </div>

                <div v-if="featureSupportPatchOrder" class="preview-item" style="align-items: center;">
                    <label>时间：</label>
                    <abc-date-picker
                        v-model="diagnosedDateStr"
                        value-format="YYYY-MM-DD"
                        :picker-options="pickerOptions"
                        style=" flex: 0 0 auto; margin-left: -6px;"
                        @change="onDiagnosedDateChange"
                    >
                        <abc-button
                            style="color: var(--abc-color-T1);"
                            variant="text"
                            theme="default"
                            size="small"
                        >
                            {{ diagnosedDateStr }}
                        </abc-button>
                    </abc-date-picker>
                </div>

                <div v-if="shebaoAuditData" class="preview-item">
                    <label>
                        <abc-icon icon="Attention" :color="$style.Y2" size="16"></abc-icon>
                        医保审核存在风险：</label>
                    <abc-button
                        type="text"
                        style="font-size: 16px;"
                        @click="showAuditDetail"
                    >
                        查看
                    </abc-button>
                </div>

                <template v-if="isConsultation && isShowCaSignature">
                    <div class="ca-divide-line"></div>
                    <div class="ca-signature-wrapper">
                        <!-- 未申请CA签名 -->
                        <template v-if="isNotCaSignature">
                            <abc-icon icon="Attention" color="#ff9933"></abc-icon>
                            <span class="not-valid-ca-text">
                                未绑定电子签名，开具处方无法使用电子签名
                            </span>
                        </template>
                        <!-- CA签名生效中 -->
                        <template v-else-if="isValidCaSignature">
                            <abc-checkbox
                                :value="isSelectCaProtocol"
                                class="ca-checkbox"
                                control
                                @click="changeSelectCaProtocol"
                            >
                                使用电子签名开具处方，需阅读并同意
                            </abc-checkbox>
                            <abc-button type="text" @click="openCaProtocolDialog">
                                《数字证书服务协议》
                            </abc-button>
                        </template>
                        <!-- CA签名已过期/已吊销 -->
                        <template v-else-if="isNotValidCaSignature">
                            <abc-icon icon="Attention" color="#ff9933"></abc-icon>
                            <span class="not-valid-ca-text">
                                电子签名已失效，开具处方无法使用电子签名
                            </span>
                        </template>
                    </div>
                </template>
            </div>
            <div
                v-if="bottomExpand && isSupportMeanwhilePrint"
                slot="bottom-extend"
                class="confirm-treatment-dialog-bottom-extend-outpatient"
            >
                <p class="tip-text">
                    设置每次完诊时需要同时自动打印的医疗文书
                </p>
                <abc-checkbox-group v-model="printOpt.finishSelect" class="disabled-no-checkbox print-checkbox-group">
                    <abc-checkbox class="check-box-item" :label="printOptions.MEDICAL.label">
                        {{ printOptions.MEDICAL.label }}
                    </abc-checkbox>
                    <abc-checkbox class="check-box-item" :label="printOptions.PRESCRIPTION.label">
                        {{ printOptions.PRESCRIPTION.label }}
                    </abc-checkbox>

                    <abc-checkbox class="check-box-item" :label="printOptions.INFUSION_EXECUTE.label">
                        输注单
                    </abc-checkbox>

                    <abc-checkbox class="check-box-item" :label="printOptions.TREATMENT_EXECUTE.label">
                        治疗单
                    </abc-checkbox>
                    <abc-checkbox
                        v-for="(c,i) in examPrintOptions"
                        :key="i"
                        class="check-box-item"
                        :label="c.value"
                    >
                        {{ c.value }}
                    </abc-checkbox>
                </abc-checkbox-group>
            </div>

            <div slot="footer" class="dialog-footer">
                <div v-if="isSupportMeanwhilePrint" class="print-meanwhile-set">
                    <abc-checkbox v-model="isOutpatientMeanwhilePrint" @change="changeMeanwhilePrintHandler"></abc-checkbox>
                    <span class="label-text" @click="bottomExpand = !bottomExpand">
                        同时打印
                        <i class="iconfont cis-icon-dropdown_triangle"></i>
                    </span>
                </div>

                <abc-button :loading="saveLoading" @click="beforeOk">
                    确定
                </abc-button>
                <abc-button :disabled="saveLoading" type="blank" @click="no">
                    取消
                </abc-button>
            </div>
        </template>
    </abc-dialog>
</template>

<script type="text/babel">
    import {
        on, off,
    } from 'utils/dom';
    import localStorage from 'utils/localStorage-handler';
    import Store from 'src/store';

    import Printer from 'views/print';
    import clone from 'utils/clone';
    import { WxBindStatusEnum } from '@abc/constants';
    import { loosePlainText } from 'utils/xss-filter';
    import WechatPush from '@/views/crm/wechat-push/index.vue';
    import PushText from '@/views/crm/wechat-push/push-text.vue';
    import { getWarnDiagnosisHtml } from '../medical-record/utils.js';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import CAAPI from 'api/ca-signature';
    import CaProtocolDialog from 'views/outpatient/common/confirm-treatment-dialog/ca-protocol-dialog';
    import { mapGetters } from 'vuex';
    import { parseTime } from '@/utils';

    export default {
        name: 'ConfirmTreatmentDialog',
        components: {
            WechatPush,
            PushText,
        },
        props: {
            chainId: String,
            patient: Object,
            medicalRecord: Object,
            title: String,
            chargeStatus: Number,
            canChargeReview: [Boolean,Number],
            outpatientShowTotalPrice: [Boolean,Number],
            outpatientTotalPrice: [Number,String],
            autoSendOrderInfoSwitch: [Boolean,Number],
            isConsultation: [Boolean,Number],
            submit: Function,
            updatePatientInfo: Function,
            reviewCharge: Function,
            postData: {
                type: Object,
                default() {
                    return {};
                },
            },
            disabledPrintExamination: {
                type: Boolean,
                default: false,
            },
            disabledPrintInspect: {
                type: Boolean,
                default: false,
            },
            shebaoChargeTypeDesc: { // 1自费 2 普通门诊 3 慢特病门诊
                type: String,
                default: '自费',
            },
            doctorId: {
                type: String,
                default: '',
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            // 是否显示CA电子签名
            isShowCaSignature: {
                type: Number,
                default: 0,
            },
            // 是否默认勾选使用CA电子签名开具处方
            isDefaultSelectCaProtocol: {
                type: Boolean,
                default: false,
            },
            caCert: {
                type: Object,
                default: () => ({
                    doctorId: '',
                    status: '', // 状态 0-正常 1-注销
                    cardNumber: '', // 身份证号
                    caBeginDate: '', // 证书申请日期
                    caEndDate: '', // 证书有效期至
                    validDays: 0, // 剩余有效天数
                }),
            },
        },
        data() {
            return {
                newPatient: this.patient,
                visible: false,
                closed: false,
                saveLoading: false,
                hiddenOutpatientTotalPrice: false,
                printOpt: {
                    printSelect: '处方',
                    finishSelect: [],
                },

                showQrCodeDialog: false,

                isOutpatientMeanwhilePrint: false, // 是否需要同时打印
                bottomExpand: false,

                isSelectCaProtocol: false, // 是否勾选电子签名协议
                isFirstCaDone: false, // 是否是医生第一次完诊
                isFirstClickConfirm: true,

                shebaoAuditData: null,
            };
        },

        computed: {
            ...mapGetters('viewDistribute', [
                'featureSupportRatioPrice',
                'featureSupportPatchOrder',
                'viewDistributeConfig',
            ]),
            isSupportMeanwhilePrint() {
                return this.viewDistributeConfig.Outpatient.isSupportMeanwhilePrint;
            },
            diagnosedDateStr: {
                get() {
                    const {
                        diagnosedDateStr, diagnosedDate,
                    } = this.postData;
                    const date = diagnosedDateStr || diagnosedDate;
                    if (date) {
                        return parseTime(new Date(date), 'y-m-d', true);
                    }
                    return parseTime(new Date(), 'y-m-d', true);
                },
                set(val) {
                    this.postData.diagnosedDateStr = val;
                },
            },
            pickerOptions() {
                return {
                    disabledDate: (date) => {
                        const { firstChargedTime } = this.postData;
                        if (firstChargedTime) {
                            return date > new Date(firstChargedTime);
                        }
                        return date > new Date();
                    },
                };
            },
            printOptions() {
                return getViewDistributeConfig().Print.printOptions;
            },

            patientSubscribeBind() {
                // return this.patient.wxBindStatus === WxBindStatusEnum.SUBSCRIBE_AND_BIND;
                return this.patient.wxStatus === WxBindStatusEnum.SUBSCRIBE_AND_BIND;
            },
            patientBindNoSubscribe() {
                // return this.patient.wxBindStatus === WxBindStatusEnum.BIND_AND_NO_SUBSCRIBE;
                return this.patient.wxStatus === WxBindStatusEnum.BIND_AND_NO_SUBSCRIBE;
            },

            diagnosis() {
                return this.medicalRecord.diagnosis;
            },

            diagnosisHtml() {
                const {
                    extendDiagnosisInfos,
                } = clone(this.medicalRecord);
                if (extendDiagnosisInfos && extendDiagnosisInfos.filter((it) => it.value.length).length) {
                    const _arr = [];
                    extendDiagnosisInfos.forEach((item) => {
                        item.value.forEach((it) => {
                            it.name && _arr.push(it);
                        });
                    });
                    const _html = getWarnDiagnosisHtml(_arr, this.$abcSocialSecurity.isSupportMatchDiseaseCode);
                    return loosePlainText(_html, true, {
                        a: ['class', 'data-tipsy'],
                        i: ['class', 'contenteditable'],
                    });
                }

                return '';
            },
            // 是否为检验申请单打印
            isPrintExamApplySheet() {
                return getViewDistributeConfig().Outpatient.isPrintExamApplySheet;
            },
            examPrintOptions() {
                const clinicExamPrintOptions = [
                    {
                        value: this.printOptions.EXAMINATION.label,
                    },
                    {
                        value: this.printOptions.INSPECT.label,
                    },
                ];
                const hospitalExamPrintOptions = [
                    {
                        value: this.printOptions.EXAMINATION_APPLY_SHEET.label,
                    },
                    {
                        value: this.printOptions.INSPECT_APPLY_SHEET.label,
                    },
                ];

                return this.isPrintExamApplySheet ? hospitalExamPrintOptions : clinicExamPrintOptions;
            },
            // 未申请签名
            isNotCaSignature() {
                return !this.caCert.doctorId;
            },
            // 签名生效中
            isValidCaSignature() {
                return this.caCert.status === '0' && this.caCert.validDays > 0;
            },
            // 签名已失效(过期或吊销)
            isNotValidCaSignature() {
                return !(this.isNotCaSignature || this.isValidCaSignature);
            },
        },

        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        async created() {
            const { currentClinic } = Store.getters;
            const { userInfo } = Store.getters;
            this._key = `${currentClinic.clinicId}_${userInfo.id}`;
            this.hiddenOutpatientTotalPrice = localStorage.getObj('hidden_outpatient_total_price', this._key, true);

            // 初始化同时打印的数据, outpatientNeedPrint 判断是否需要同时打印
            const { cache } = Printer;
            const {
                outpatientMeanwhilePrint, outpatient,
            } = cache.get();

            // 兼容之前勾选打印处方等同时打印的情况
            this.isOutpatientMeanwhilePrint =
                outpatientMeanwhilePrint === undefined ? outpatient.length > 0 : outpatientMeanwhilePrint;

            if (this.isOutpatientMeanwhilePrint && this.printOpt.finishSelect.length === 0) {
                this.bottomExpand = true;
            }

            if (this.isConsultation && this.isShowCaSignature) {
                this.isSelectCaProtocol = this.isDefaultSelectCaProtocol;

                // 如果CA电子签名处于有效期内,再去拉去相关数据
                if (this.isValidCaSignature) {
                    this.fetchIsReadCaProtocol();
                }
            }

            this.getShebaoAudit();

        },
        mounted() {
            on(document, 'keydown', this.keydownHandle);
        },
        beforeDestroy() {
            off(document, 'keydown', this.keydownHandle);
        },
        methods: {
            /**  打印相关
             * @desc 打开完成就诊弹窗，初始化打印 checkbox
             * <AUTHOR>
             * @date 2018/10/10 12:27:03
             */
            getPrintOpt() {
                const { cache } = Printer;
                const { outpatient } = cache.get();
                const index = outpatient.findIndex((item) => {
                    return item === this.printOptions.INFUSION_TREATMENT.label;
                });
                if (index > -1) {
                    outpatient.splice(index, 1);
                    if (!outpatient.includes(this.printOptions.INFUSION_EXECUTE)) {
                        outpatient.push(this.printOptions.INFUSION_EXECUTE);
                    }
                    if (!outpatient.includes(this.printOptions.TREATMENT_EXECUTE)) {
                        outpatient.push(this.printOptions.TREATMENT_EXECUTE);
                    }
                }
                this.printOpt.finishSelect = clone(outpatient);
            },
            changeMeanwhilePrintHandler() {
                if (!this.printOpt.finishSelect || !this.printOpt.finishSelect.length) {
                    this.bottomExpand = true;
                }
            },

            /**
             * @desc 点击完成接诊弹窗中，是否显示总价格
             * <AUTHOR>
             * @date 2020/01/10 14:37:21
             */
            changeShowPrice() {
                this.hiddenOutpatientTotalPrice = !this.hiddenOutpatientTotalPrice;
                localStorage.setObj('hidden_outpatient_total_price', this._key, this.hiddenOutpatientTotalPrice);
            },

            keydownHandle(e) {
                const KEY_ENTER = 13;
                const KEY_ESC = 27;
                if (e.keyCode === KEY_ENTER) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.beforeOk();
                } else if (e.keyCode === KEY_ESC) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.no();
                }
            },

            handleUpdateInfo(e) {
                this.newPatient = e;
                this.updatePatientInfo(e);
            },

            async beforeOk() {
                // 如果是网诊,且开启了使用电子签名开关,则在完诊前做预处理
                if (this.isConsultation && this.isShowCaSignature) {
                    // 签名处于有效期中
                    if (this.isValidCaSignature) {
                        // 第一次完诊且未勾选使用电子签名
                        if (this.isFirstCaDone && !this.isSelectCaProtocol && this.isFirstClickConfirm) {
                            this.isFirstClickConfirm = false;
                            this.openCaProtocolDialog();
                        } else {
                            // 如果是第一次完诊
                            if (this.isFirstCaDone) {
                                CAAPI.caSignatureRead({ employeeId: this.doctorId });
                            }
                            CAAPI.changeUseCaSignature({
                                prescriptionUseCa: +this.isSelectCaProtocol, doctorId: this.doctorId,
                            });
                            // 完诊成功后调用处方电子签名
                            const successCallback = () => {
                                CAAPI.doctorCertSign({
                                    doctorId: this.doctorId,
                                    patientOrderId: this.patientOrderId,
                                });
                            };
                            this.ok(successCallback);
                        }
                    } else {
                        // 如果未申请签名、签名过期、签名吊销,则正常完诊
                        this.ok();
                    }
                } else {
                    this.ok();
                }
            },

            async ok(successCallback) {
                if (typeof this.submit === 'function') {
                    try {
                        this.saveLoading = true;
                        console.log(this.printOpt.finishSelect);
                        await this.submit(null, this.printOpt.finishSelect, successCallback);
                        // 打印
                        const { cache } = Printer;
                        cache.set({
                            outpatient: this.printOpt.finishSelect,
                            outpatientMeanwhilePrint: this.isOutpatientMeanwhilePrint,
                            outpatientNeedPrint: this.isOutpatientMeanwhilePrint,
                        });
                        this.saveLoading = false;
                    } catch (e) {
                        this.saveLoading = false;
                    }
                }
                this.close();
            },

            async no() {
                if (typeof this.cancel === 'function') {
                    this.cancel();
                }
                this.close();
            },

            reviewChargeHandle() {
                if (typeof this.reviewCharge === 'function') {
                    this.reviewCharge();
                }
            },

            close() {
                this.closed = true;
                if (typeof this.onClose === 'function') {
                    this.onClose(this);
                }
            },

            destroyElement() {
                off(document, 'keydown', this.keydownHandle);
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            // 点击电子签名checkbox的回调
            changeSelectCaProtocol() {
                const cacheIsSelectCaProtocol = !this.isSelectCaProtocol;
                if (cacheIsSelectCaProtocol) {
                    this.openCaProtocolDialog();
                } else {
                    this.isSelectCaProtocol = cacheIsSelectCaProtocol;
                }
            },
            /**
             * 获取是否首次网诊完诊
             * @returns {Promise<void>}
             */
            async fetchIsReadCaProtocol() {
                try {
                    const isReadCaProtocolResp = await CAAPI.isCaSignatureRead({ employeeId: this.doctorId });
                    this.isFirstCaDone = !isReadCaProtocolResp.data?.created;
                } catch (e) {
                    console.error(e);
                    this.isFirstCaDone = false;
                }
            },
            dialogConfirmCallback(val) {
                this.isSelectCaProtocol = val;
            },
            openCaProtocolDialog() {
                new CaProtocolDialog({
                    callback: this.dialogConfirmCallback,
                }).generateDialog({ parent: this });
            },

            /**
             * @desc 医保审核明细
             * <AUTHOR>
             * @date 2023/09/11 11:15:07
             */
            async getShebaoAudit() {
                try {
                    if (!this.$abcSocialSecurity.isEnableHisCallDetailAnalysis) return;
                    if (!this.patientOrderId) return;
                    const { shebaoCardInfo } = this.postData;
                    const {
                        status,
                        data,
                    } = await this.$abcSocialSecurity.detailAnalysis({
                        patientOrderId: this.patientOrderId,
                        shebaoCardInfo,
                        outpatientInfo: this.postData,
                    });
                    if (!status) return;
                    const { result } = data;
                    this.shebaoAuditData = result;
                } catch (e) {
                    console.error(e);
                }
            },
            /**
             * @desc 展示医保明细结果
             * <AUTHOR>
             * @date 2023/09/12 16:43:55
             */
            async showAuditDetail() {
                try {
                    if (!this.shebaoAuditData) return;
                    await this.$abcSocialSecurity.showDetailAnalysisResult({
                        result: this.shebaoAuditData,
                    });
                } catch (e) {
                    console.error(e);
                }
            },

            onDiagnosedDateChange(val) {
                this.postData.diagnosedDateStr = val;
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
<style lang="scss">
    @import 'src/styles/theme';

    .preview-fee-dialog {
        text-align: center;

        .abc-dialog-header {
            display: flex;
            align-items: center;
        }

        h5 {
            margin-bottom: 24px;
            line-height: 16px;
        }

        .preview-item {
            display: flex;
            margin-bottom: 16px;
            font-size: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            label {
                min-width: 48px;
                margin-right: 6px;
                color: $T2;
            }

            > div {
                display: flex;
                flex: 1;
                flex-direction: column;
                text-align: left;

                > p {
                    display: flex;
                    align-items: center;
                    font-size: 18px;
                    color: $Y2;
                }

                .tips {
                    margin-top: 4px;
                    font-size: 12px;
                    line-height: 28px;
                    color: $T2;
                }

                button {
                    font-size: 16px;
                }
            }

            .fee-wrapper .iconfont {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                color: $T2;
                cursor: pointer;

                &:hover {
                    color: $T1;
                }
            }

            .diagnosis-html {
                display: block;

                > div {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                }

                .global-tooth-selected-quadrant > div {
                    color: $T1;
                }

                // .abc-tipsy {
                //     flex-direction: inherit;
                //     align-items: center;
                //     padding-top: 2px;
                //     margin-left: 2px;
                //     color: $Y2;
                //     vertical-align: baseline;
                // }

                .shortage-tips {
                    position: relative;
                    color: $Y2;

                    i {
                        font-style: normal;
                        color: $Y2;
                    }

                    .abc-tipsy--n {
                        i {
                            font-style: normal;
                            color: $Y2;
                        }
                    }

                    &.abc-tipsy--n::before {
                        border-top-color: #000000;
                    }

                    &.abc-tipsy--n::after {
                        width: 216px;
                        padding: 10px 12px;
                        white-space: normal;
                        background-color: #000000;
                    }
                }

                // .abc-tipsy--n::after {
                //     left: 0;
                //     transform: translateX(-90px);
                // }
            }
        }

        .print-meanwhile-set {
            margin-right: auto;
            font-size: 14px;

            .abc-checkbox-wrapper {
                margin-right: 0;
            }

            .iconfont {
                color: $T2;
                cursor: pointer;
            }

            .cis-icon-dropdown_triangle {
                margin-left: -4px;
                font-size: 14px;
            }

            > .label-text {
                margin-left: 4px;
                cursor: pointer;
            }

            .label-tips {
                font-size: 12px;
                color: #ff9933;
            }
        }

        .confirm-treatment-dialog-bottom-extend-outpatient {
            position: absolute;
            left: 0;
            width: 100%;
            padding: 12px 24px;
            background-color: $P4;
            border-top: 1px solid $P6;
            border-radius: 0 0 var(--abc-border-radius-medium) var(--abc-border-radius-medium);
            transform: translateY(52px);

            .tip-text {
                margin-bottom: 6px;
                font-size: 12px;
                color: $T2;
                text-align: left;
            }

            .print-checkbox-group {
                display: flex;
                flex-wrap: wrap;
                gap: 0;

                .check-box-item {
                    width: 20%;
                    margin-right: 8px;
                    margin-left: 0;
                }
            }

            .abc-checkbox-wrapper {
                margin: 0 !important;

                .abc-checkbox__label {
                    color: $T1;
                }
            }
        }

        .ca-divide-line {
            height: 0;
            margin: 16px 0;
            border-top: 1px dashed $P6;
        }

        .ca-signature-wrapper {
            display: flex;
            align-items: center;

            .ca-checkbox {
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                color: $T2;
            }

            .not-valid-ca-text {
                margin-left: 9px;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                color: $Y2;
            }
        }
    }
</style>
