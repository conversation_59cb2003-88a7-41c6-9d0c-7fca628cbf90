<template>
    <div v-abc-click-outside="outside" class="oral-examination-wrapper">
        <abc-edit-div
            ref="ref-target"
            v-model="currentValue"
            :class="{ 'is-focus': showSuggestions }"
            :disabled="disabled"
            :maxlength="1000"
            :xss-options="{ span: ['class'] }"
            data-cy="abc-mr-口腔检查"
            @click="handleClick"
            @tab="handleTab"
            @keydown.down.prevent="handleDown"
            @keydown.up.prevent="handleUp"
        >
        </abc-edit-div>

        <input
            ref="abcinput"
            type="text"
            style=" position: absolute; top: 0; left: 0; width: 0; opacity: 0;"
            tabindex="-1"
            :value="value"
        />

        <div
            v-if="showSuggestions && oralExaminations.length"
            ref="popper-target"
            class="medical-record-suggestions-wrapper no-shadow"
            :class="{ fixed: fixed }"
            style="width: 624px;"
            :style="suggestionsStyle"
            data-cy="abc-mr-popover-口腔检查"
        >
            <biz-quick-options-panel
                :tabs-options="panelTabOptions"
                :tabs-value="activeTabKey"
                :list="currentPanelOptions"
                close-data-cy="abc-mr-口腔检查-close"
                @changeTabs="handleChangeTabs"
                @select="item => selectOralExaminations(item.value)"
                @close="showSuggestions = false"
            >
                <template #tooth>
                    <div class="examination-content">
                        <div
                            ref="patchSelectWrapper"
                            class="select-tooth-wrapper"
                        >
                            <div
                                v-show="showMask"
                                ref="ctrlSelectMask"
                                class="ctrl-select-mask"
                                :style="selectMaskStyle"
                            ></div>
                            <div>
                                <div class="top-tooth">
                                    <abc-popover
                                        v-for="tooth in topToothList"
                                        :key="tooth.position + tooth.no"
                                        v-model="tooth.showPopover"
                                        placement="right-start"
                                        trigger="manual"
                                    >
                                        <div
                                            slot="reference"
                                            class="tooth-item"
                                            :class="[
                                                tooth.position,
                                                `${tooth.position }-${ tooth.no}`,
                                                {
                                                    'is-disabled': isChildTeeth && !tooth.childNo,
                                                    'is-hover': tooth.hover || tooth.showPopover,
                                                    'is-selected': isSelectedTooth(tooth),
                                                },
                                            ]"
                                            :data-cy="`abc-mr-口腔检查-${tooth.position}-${ tooth.no}`"
                                            @click.prevent.stop="clickToothHandle(tooth)"
                                        >
                                            <div class="tooth-img" :class="[tooth.position, `top-tooth-${ tooth.no}`]"></div>
                                            <div class="tooth-no">
                                                {{ isChildTeeth ? tooth.childNo || '' : tooth.no }}
                                            </div>
                                        </div>
                                        <tooth-examination-popover
                                            :tooth="tooth"
                                            :selected-teeth="findSelectedTeeth(tooth)"
                                            @closeHandle="closeHandle"
                                            @addTooth="addTooth"
                                        ></tooth-examination-popover>
                                    </abc-popover>
                                </div>
                                <div class="bottom-tooth">
                                    <abc-popover
                                        v-for="tooth in bottomToothList"
                                        :key="tooth.position + tooth.no"
                                        v-model="tooth.showPopover"
                                        placement="right"
                                        trigger="manual"
                                    >
                                        <div
                                            slot="reference"
                                            class="tooth-item"
                                            :class="[
                                                tooth.position,
                                                `${tooth.position }-${ tooth.no}`,
                                                {
                                                    'is-disabled': isChildTeeth && !tooth.childNo,
                                                    'is-hover': tooth.hover || tooth.showPopover,
                                                    'is-selected': isSelectedTooth(tooth),
                                                },
                                            ]"
                                            :data-cy="`abc-mr-口腔检查-${tooth.position}-${ tooth.no}`"
                                            @click.prevent.stop="clickToothHandle(tooth)"
                                        >
                                            <div class="tooth-no">
                                                {{ isChildTeeth ? tooth.childNo || '' : tooth.no }}
                                            </div>
                                            <div class="tooth-img" :class="[tooth.position, `bottom-tooth-${ tooth.no}`]"></div>
                                        </div>
                                        <tooth-examination-popover
                                            :tooth="tooth"
                                            :selected-teeth="findSelectedTeeth(tooth)"
                                            @closeHandle="closeHandle"
                                            @addTooth="addTooth"
                                        ></tooth-examination-popover>
                                    </abc-popover>
                                </div>
                            </div>
                            <div class="wisdom-teeth">
                                <abc-popover
                                    v-for="tooth in wisdomTeeth"
                                    :key="tooth.position + tooth.no"
                                    v-model="tooth.showPopover"
                                    placement="right"
                                    trigger="manual"
                                >
                                    <div
                                        slot="reference"
                                        class="tooth-no"
                                        :class="[
                                            tooth.position,
                                            `${tooth.position }-${ tooth.no}`,
                                            {
                                                'is-disabled': isChildTeeth && !tooth.childNo,
                                                'is-hover': tooth.hover || tooth.showPopover,
                                                'is-selected': isSelectedTooth(tooth),
                                            },
                                        ]"
                                        :data-cy="`abc-mr-口腔检查-${tooth.position}-${ tooth.no}`"
                                        @click.prevent.stop="clickToothHandle(tooth)"
                                    >
                                        {{ tooth.noStr }}
                                    </div>
                                    <tooth-examination-popover
                                        :tooth="tooth"
                                        :selected-teeth="findSelectedTeeth(tooth)"
                                        @closeHandle="closeHandle"
                                        @addTooth="addTooth"
                                    ></tooth-examination-popover>
                                </abc-popover>
                            </div>
                        </div>

                        <div class="examination-footer">
                            <abc-checkbox v-model="isChildTeeth">
                                乳牙
                            </abc-checkbox>
                            <div class="tips">
                                按住{{ isMac ? 'command' : 'Ctrl' }}可多选
                            </div>
                        </div>

                        <div v-if="currentPopoverTooth && currentPopoverTooth.showPopover" class="cover"></div>
                    </div>
                </template>
            </biz-quick-options-panel>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import { loosePlainText } from 'utils/xss-filter';
    import popper from './popper';
    import {
        off, on, keepLastIndex,
    } from 'utils/dom';
    import { isJSON } from 'utils/index';
    import ToothExaminationPopover from './tooth-examination-popover';
    import { BizQuickOptionsPanel } from '@/components-composite/biz-quick-options-panel/index';
    import { clone } from '@abc/utils';
    import { formatMedicalRecordQuickOptions } from 'views/outpatient/common/medical-record/utils';

    const isMac = navigator.userAgent.indexOf('Mac OS') > -1;
    export default {
        components: {
            ToothExaminationPopover,
            BizQuickOptionsPanel,
        },
        mixins: [popper],
        props: {
            value: String,
            type: Number,
            medicalRecordType: Number, // 病历类型0：西医；1：中医
            disabled: Boolean,
            isChildcare: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                isMac,
                showSuggestions: false,
                showMask: false,
                oralExaminations: [
                    {
                        key: 'tooth',
                        label: '牙位',
                        groupList: [
                            {
                                groupSlotName: 'tooth',
                                list: [],
                            },
                        ],
                    },
                    {
                        'key': 'periodontium',
                        'label': '牙周',
                        'groupList': [
                            {
                                list: [
                                    ['牙龈苍白', '牙龈暗红', '牙龈鲜红', '扪痛（-）', '扪痛（+）'],
                                    ['牙龈红肿', '牙龈脓肿', '牙龈糜烂', '牙龈出血', '牙龈增生', '牙龈萎缩', '点彩消失'],
                                    ['无龈袋', '有龈袋', '牙周袋≤4mm', '牙周袋≤6mm', '牙周袋＞6mm'],
                                    ['牙结石（-）', '牙结石（+）', '牙结石（++）', '牙结石（+++）'],
                                    ['牙周袋附着丧失1~2mm', '牙周袋附着丧失3~4mm', '牙周袋附着丧失≥5mm'],
                                ],
                            },
                            {
                                list: [
                                    ['口腔黏膜苍白', '口腔黏膜暗红', '口腔黏膜鲜红'],
                                    ['口腔黏膜疱疹', '口腔黏膜丘疹', '口腔黏膜溃疡', '口腔黏膜瘢痕', '口腔黏膜角化'],
                                ],
                            },
                        ],
                    },

                    {
                        'key': 'tongues',
                        'label': '舌腭腺',
                        'groupList': [
                            {
                                list: [
                                    ['舌体裂纹', '舌体溃疡', '舌体肿胀', '舌乳头异常'],
                                    ['苔黄', '苔灰黑', '苔厚', '苔少', '无苔'],
                                ],
                            },
                            {
                                list: [
                                    ['软腭充血', '软腭角化', '软腭溃疡', '软腭瘘管', '软腭运动障碍'],
                                    ['硬腭充血', '硬腭角化', '硬腭溃疡', '硬腭瘘管'],
                                ],
                            },
                            {
                                list: [
                                    ['腮腺导管口肿胀', '腮腺导管口压痛', '腮腺导管口阻塞', '腮腺导管口充血', '腮腺导管口溢脓'],
                                    ['下颌下腺导管口肿胀', '下颌下腺导管口压痛', '下颌下腺导管口阻塞', '下颌下腺导管口溢脓'],
                                ],
                            },
                        ],
                    },

                    {
                        'key': 'face',
                        'label': '颞颌面',
                        'groupList': [
                            {
                                list: [
                                    ['面部突出', '面部凹陷', '面部缺损', '面部肿胀', '面部瘘管', '面部不对称'],
                                    ['面色萎黄', '面色苍白', '面色晦暗', '面部瘢痕', '面肌弛缓'],
                                    ['面部麻木', '面部疼痛', '口角歪斜', '眼角歪斜', '眼睑下垂', '眼球突出', '鼻部畸形'],
                                ],
                            },{
                                list: [
                                    ['上颌囊肿', '上颌前突', '上颌后缩', '上颌压痛', '上颌缺损', '上颌骨折', '上颌不对称'],
                                    ['下颌囊肿', '下颌前突', '下颌后缩', '下颌压痛', '下颌缺损', '下颌骨折', '下颌不对称'],
                                ],
                            },{
                                list: [
                                    ['左颞颌关节肿胀', '左颞颌关节疼痛', '左颞颌关节压痛', '左颞颌关节弹响'],
                                    ['右颞颌关节肿胀', '右颞颌关节疼痛', '右颞颌关节压痛', '右颞颌关节弹响'],
                                ],
                            },
                        ],
                    },

                    {
                        'key': 'lymphaden',
                        'label': '淋巴结',
                        'groupList': [
                            {
                                list: [
                                    ['耳前淋巴结肿大', '耳前淋巴结压痛', '耳前淋巴结增多', '耳前淋巴结粘连'],
                                    ['耳后淋巴结肿大', '耳后淋巴结压痛', '耳后淋巴结增多', '耳后淋巴结粘连'],
                                    ['枕后淋巴结肿大', '枕后淋巴结压痛', '枕后淋巴结增多', '枕后淋巴结粘连'],
                                    ['颌下淋巴结肿大', '颌下淋巴结压痛', '颌下淋巴结增多', '颌下淋巴结粘连'],
                                    ['颏下淋巴结肿大', '颏下淋巴结压痛', '颏下淋巴结增多', '颏下淋巴结粘连'],
                                    ['颈前淋巴结肿大', '颈前淋巴结压痛', '颈前淋巴结增多', '颈前淋巴结粘连'],
                                    ['颈后淋巴结肿大', '颈后淋巴结压痛', '颈后淋巴结增多', '颈后淋巴结粘连'],
                                    ['锁骨上淋巴结肿大', '锁骨上淋巴结压痛', '锁骨上淋巴结增多', '锁骨上淋巴结粘连'],
                                ],
                            },
                        ],
                    },
                ],
                selectExaminations: [],

                suggestionsStyle: {
                    top: '40px',
                },

                topToothList: [
                    {
                        no: '8',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '7',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '6',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '5',
                        childNo: 'E',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '4',
                        childNo: 'D',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '3',
                        childNo: 'C',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '2',
                        childNo: 'B',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '1',
                        childNo: 'A',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '1',
                        childNo: 'A',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '2',
                        childNo: 'B',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '3',
                        childNo: 'C',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '4',
                        childNo: 'D',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '5',
                        childNo: 'E',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '6',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '7',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '8',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },

                ],
                bottomToothList: [
                    {
                        no: '8',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '7',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '6',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '5',
                        childNo: 'E',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '4',
                        childNo: 'D',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '3',
                        childNo: 'C',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '2',
                        childNo: 'B',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '1',
                        childNo: 'A',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '1',
                        childNo: 'A',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '2',
                        childNo: 'B',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '3',
                        childNo: 'C',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '4',
                        childNo: 'D',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '5',
                        childNo: 'E',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '6',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '7',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '8',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },

                ],
                wisdomTeeth: [
                    {
                        no: '9',
                        noStr: '19',
                        position: 'top-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '9',
                        noStr: '29',
                        position: 'top-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '9',
                        noStr: '39',
                        position: 'bottom-right',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },
                    {
                        no: '9',
                        noStr: '49',
                        position: 'bottom-left',
                        selected: false,
                        hover: false,
                        showPopover: false,
                    },

                ],

                currentList: [],
                isPressCtrl: false,
                isChildTeeth: false,
                currentValue: '',
                currentPopoverTooth: null,

                boxScreenLeft: 0,
                boxScreenTop: 0,
                startX: 0,
                startY: 0,
                endX: 0,
                endY: 0,

                activeTabKey: 'tooth',
            };
        },

        computed: {
            selectMaskStyle() {
                return {
                    'top': `${Math.min(this.startY, this.endY) - this.boxScreenTop}px`,
                    'left': `${Math.min(this.startX, this.endX) - this.boxScreenLeft}px`,
                    'width': `${Math.abs(this.endX - this.startX)}px`,
                    'height': `${Math.abs(this.endY - this.startY)}px`,
                };
            },

            panelTabOptions() {
                return this.oralExaminations.map((item) => ({
                    label: item.label,
                    value: item.key,
                }));
            },

            currentPanelOptions() {
                const current = clone(this.oralExaminations.find((item) => item.key === this.activeTabKey) || {});
                return current.groupList || [];
            },
        },
        watch: {
            showSuggestions(val) {
                if (val) {
                    on(document, 'keydown', this.ctrlKeydownHandle);
                    on(document, 'keyup', this.ctrlKeyupHandle);

                    this.$nextTick(() => {
                        this._domBox = this.$refs.patchSelectWrapper;
                        if (this._domBox) {
                            this.boxScreenLeft = this._domBox.getBoundingClientRect().left;
                            this.boxScreenTop = this._domBox.getBoundingClientRect().top;
                            on(this._domBox, 'mousedown', this.handleMouseDown);
                        }
                    });
                } else {
                    this.isPressCtrl = false;
                    this.showMask = false;
                    this._selectedTeethList.forEach((item) => {
                        item.hover = false;
                    });
                    this.topToothList.forEach((item) => {
                        item.hover = false;
                    });
                    this.bottomToothList.forEach((item) => {
                        item.hover = false;
                    });
                    this._selectedTeethList = [];
                    off(document, 'keydown', this.ctrlKeydownHandle);
                    off(document, 'keyup', this.ctrlKeyupHandle);
                    this._domBox && off(this._domBox, 'mousedown', this.handleMouseDown);
                }
            },
            value: {
                handler(val) {
                    if (isJSON(val)) {
                        this.currentList = JSON.parse(val);
                        this.trans2Str();
                    } else {
                        const _list = val ? val.split(/,|，/) : [];
                        this.trans2Json(_list);
                        this.trans2Str();
                    }
                    this.updateStyle();
                },
                immediate: true,
            },
            currentValue: {
                handler(val) {
                    const _list = val ? val.trim().split(/,|，/) : [];
                    this.trans2Json(_list);
                    if (this.currentList && this.currentList.length) {
                        this.$emit('input', JSON.stringify(this.currentList));
                    } else {
                        this.$emit('input', '');
                    }
                },
            },
            isPressCtrl(val) {
                if (val) {
                    this._domBox && off(this._domBox, 'mousedown', this.handleMouseDown);
                    this._domBox && off(this._domBox, 'mousemove', this.handleMouseMove);
                    this._domBox && off(this._domBox, 'mouseup', this.handleMouseUp);
                } else {
                    this._domBox && on(this._domBox, 'mousedown', this.handleMouseDown);
                }
            },
        },

        created() {
            this.formatOralExamination();
            this.handleChangeTabs(this.oralExaminations[0].key);
            this._selectedTeethList = [];
            // 初始化 dataNo中包含 ABCDE 的视为选择乳牙
            this.currentList.forEach((item) => {
                if (typeof item === 'object') {
                    item.positions.forEach((pos) => {
                        pos.dataNo.forEach((it) => {
                            if (['A', 'B', 'C', 'D', 'E'].indexOf(it) > -1) {
                                this.isChildTeeth = true;
                            }
                        });
                    });
                }
            });
        },
        mounted() {
            this.updateStyle();
        },
        beforeDestroy() {
            this.isPressCtrl = false;
            this.showMask = false;
            off(document, 'keydown', this.ctrlKeydownHandle);
            off(document, 'keyup', this.ctrlKeyupHandle);
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            formatOralExamination() {
                this.oralExaminations.forEach((item) => {
                    item.groupList = formatMedicalRecordQuickOptions(item.groupList, 'abc-mr-口腔检查');
                });
            },
            handleChangeTabs(value) {
                this.activeTabKey = value;
            },

            handleClick() {
                if (this.disabled) return false;
                this.showSuggestions = true;
            },
            handleTab() {
                this.showSuggestions = false;
            },
            updateStyle() {
                if (this.$children[0]) {
                    this.suggestionsStyle = {
                        top: this.fixed ? '0' : `${this.$children[0].$refs.abcinput.offsetHeight + 4}px`,
                    };
                }
            },

            getValue(event) {
                this.$emit('input', loosePlainText(event.currentTarget.innerHTML, false));
                this.$emit('blur', event);
            },
            handleDown() {
                this.showSuggestions = false;
            },
            handleUp() {
                this.showSuggestions = false;
            },

            /**
             * @desc 将牙科门诊中牙齿位置诊断信息json格式化
             * <AUTHOR> Yang
             * @date 2020-11-09 14:53:08
             */
            trans2Json(list) {
                this.currentList = list.map((item) => {
                    const oDiv = document.createElement('div');
                    oDiv.innerHTML = item.toString();
                    const positions = [];
                    const describes = [];
                    Array.prototype.forEach.call(oDiv.children, (child) => {
                        if (child) {
                            // 光标在span象限中。如果填入的不是牙齿数字或智齿数字，则分割出来
                            const _list = child.textContent.split(' ').filter((it) => it);
                            const _dataNo = [];
                            _list.forEach((it) => {
                                if (['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E'].indexOf(it) > -1) {
                                    _dataNo.push(it);
                                } else {
                                    describes.push(it);
                                }
                            });
                            if (_dataNo.length) {
                                positions.push({
                                    position: child.className,
                                    dataNo: _dataNo,
                                });
                            }
                        }
                    });
                    if (oDiv.lastChild && oDiv.lastChild.nodeName === '#text') {
                        describes.push(
                            ...oDiv.lastChild.textContent.trim().split(' '),
                        );
                    }
                    return {
                        positions,
                        describes,
                    };
                }).filter((item) => {
                    return item.positions.length || item.describes.length;
                });
            },

            trans2Str() {
                const _list = this.currentList.map((item) => {
                    let str = item;
                    if (typeof item === 'object') {
                        str = '';
                        item.positions.forEach((pos) => {
                            // 这里span后面加了一个空格，是为了聚焦能在 span外
                            str += `<span class="${pos.position}">${pos.dataNo.join(' ')}</span>`;
                        });
                        str += `${(str ? ' ' : '') + item.describes.join(' ')} `;
                    }
                    return str;
                });
                this.currentValue = _list.join('，');
            },

            selectOralExaminations(examination) {
                const _index = this.currentList.findIndex((it) => {
                    return it.positions.length === 0 && it.describes.indexOf(examination) > -1;
                });
                if (_index === -1) {
                    this.currentList.push(examination);
                    this.trans2Str();
                }
            },

            findSelectedTeeth(tooth) {
                if (!tooth) return [];
                const no = this.isChildTeeth ? (tooth.childNo || '') : tooth.no;
                return this.currentList.filter((item) => {
                    return !!item.positions.find((pos) => {
                        return pos.position === tooth.position && pos.dataNo.indexOf(no) > -1;
                    });
                });
            },

            isSelectedTooth(tooth) {
                return this.findSelectedTeeth(tooth).length;
            },

            outside() {
                this.showSuggestions = false;
            },
            closeHandle(tooth) {
                if (tooth.showPopover) {
                    this.$nextTick(() => {
                        tooth.showPopover = false;
                        this._selectedTeethList.forEach((item) => {
                            item.hover = false;
                        });
                        this._selectedTeethList = [];
                    });
                }
            },

            clickToothHandle(tooth) {
                if (!tooth) return false;
                this.currentPopoverTooth = tooth;
                if (this.isChildTeeth && !tooth.childNo) return false;
                if (this.isPressCtrl) {
                    tooth.hover = !tooth.hover;
                    const existIndex = this._selectedTeethList.findIndex((item) => {
                        return item.position === tooth.position && item.no === tooth.no;
                    });
                    if (existIndex > -1) {
                        this._selectedTeethList.splice(existIndex, 1);
                    } else {
                        this._selectedTeethList.push(tooth);
                    }
                } else {
                    this._timer = setTimeout(() => {
                        tooth.showPopover = true;
                    }, 100);
                }
            },

            addTooth(tooth, describe) {
                if (this._selectedTeethList.length > 1) {
                    this.patchHandle(tooth, describe);
                } else {
                    this.singleHandle(tooth, describe);
                }
                this.$nextTick(() => {
                    if (!describe) {
                        this.closeHandle(tooth);
                        const { $el } = this.$refs['ref-target'];
                        if ($el) {
                            $el.focus();
                            keepLastIndex($el);
                        }
                    }
                });
            },
            // 批量选择
            patchHandle(tooth, describe) {
                const no = this.isChildTeeth ? (tooth.childNo || '') : tooth.no;

                const _positionNoObj = new Map();
                // 控制象限顺序
                _positionNoObj.set('top-left', []);
                _positionNoObj.set('top-right', []);
                _positionNoObj.set('bottom-left', []);
                _positionNoObj.set('bottom-right', []);
                this._selectedTeethList.forEach((it) => {
                    const _obj = _positionNoObj.get(it.position);
                    _obj.push(it);
                });
                const existItem = this.currentList.find((item) => {
                    return !!item.positions.find((pos) => {
                        return pos.position === tooth.position && pos.dataNo.indexOf(no) > -1;
                    });
                });

                if (existItem) {
                    const _index = existItem.describes.indexOf(describe);
                    if (_index > -1) {
                        existItem.describes.splice(_index, 1);
                    } else {
                        existItem.describes.push(describe);
                    }
                } else {
                    const positions = [];
                    _positionNoObj.forEach((item, index) => {
                        if (item.length) {
                            positions.push({
                                position: index,
                                dataNo: item.map((it) => (this.isChildTeeth ? it.childNo : it.no)),
                            });
                        }
                    });
                    this.currentList.push({
                        positions,
                        describes: [describe],
                    });
                }
                this.trans2Str();
            },
            /**
             * @desc 单选情况较特殊，可能会存在批量选择后反选的过程，需要从批量的选择中提出来单独显示；
             * <AUTHOR> Yang
             * @date 2020-11-09 16:41:18
             */
            singleHandle(tooth, describe) {
                const no = this.isChildTeeth ? (tooth.childNo || '') : tooth.no;

                const existPatchItem = this.currentList.find((item) => {
                    return !!item.positions.find((pos) => {
                        return pos.position === tooth.position &&
                            pos.dataNo.length > 1 &&
                            pos.dataNo.indexOf(no) > -1;
                    });
                });
                let describes = [];
                // 存在批量操作中的，需要提取出来单独操作；
                if (existPatchItem) {
                    const _index = existPatchItem.describes.indexOf(describe);
                    // 单选在批量中是新增，不做处理，
                    // 如果是删除，需要将批量选择中的提取出来
                    if (_index > -1) {
                        existPatchItem.positions = existPatchItem.positions.filter((pos) => {
                            if (pos.position === tooth.position) {
                                pos.dataNo = pos.dataNo.filter((it) => {
                                    return it !== no;
                                });
                            }
                            return pos.dataNo.length;
                        });
                        describes = existPatchItem.describes.slice();
                    }
                }

                // 单选需要完全匹配
                const existItem = this.currentList.find((item) => {
                    return item.positions.length === 1 &&
                        item.positions[0].position === tooth.position &&
                        item.positions[0].dataNo.length === 1 &&
                        item.positions[0].dataNo.indexOf(no) > -1;
                });

                if (existItem) {
                    existItem.describes = existItem.describes.concat(describes);
                    const _index = existItem.describes.indexOf(describe);
                    if (_index > -1) {
                        existItem.describes.splice(_index, 1);
                    } else {
                        existItem.describes.push(describe);
                    }
                } else {
                    const _index = describes.indexOf(describe);
                    if (_index > -1) {
                        describes.splice(_index, 1);
                    } else {
                        describes.push(describe);
                    }
                    this.currentList.push({
                        positions: [{
                            position: tooth.position,
                            dataNo: [no],
                        }],
                        describes,
                    });
                }
                this.trans2Str();
            },

            ctrlKeydownHandle(event) {
                const CTRL = 17;
                const COMMAND = 91;
                if (event.keyCode === CTRL || event.keyCode === COMMAND) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.isPressCtrl = true;
                    return false;
                }
            },
            ctrlKeyupHandle(event) {
                const CTRL = 17;
                const COMMAND = 91;
                if (event.keyCode === CTRL || event.keyCode === COMMAND) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.isPressCtrl = false;
                    this.clickToothHandle(this._selectedTeethList[this._selectedTeethList.length - 1]);
                    return false;
                }
            },
            handleMouseDown(event) {
                this.startX = event.clientX;
                this.startY = event.clientY;
                this.endX = event.clientX;
                this.endY = event.clientY;

                this._domBox && on(this._domBox, 'mousemove', this.handleMouseMove);
                this._domBox && on(this._domBox, 'mouseup', this.handleMouseUp);
            },
            handleMouseMove(event) {
                this.endX = event.clientX;
                this.endY = event.clientY;

                if (this.endX - this.startX > 10 || this.endY - this.startY > 10) {
                    this.showMask = true;
                }

                const domMask = this.$refs.ctrlSelectMask;
                if (!domMask) return false;
                const rectSelect = domMask.getClientRects()[0];
                if (!rectSelect) return false;
                document.querySelectorAll('.tooth-item:not(.is-disabled)').forEach((node) => {
                    const rects = node.getClientRects()[0];
                    if (node.className.indexOf('top-right') > -1 || node.className.indexOf('top-left') > -1) {
                        this.topToothList.forEach((tooth) => {
                            const _posStr = `${tooth.position}-${tooth.no}`;
                            if (node.className.indexOf(_posStr) > -1) {
                                tooth.hover = this.collide(rects, rectSelect);
                            }
                        });
                    } else {
                        this.bottomToothList.forEach((tooth) => {
                            const _posStr = `${tooth.position}-${tooth.no}`;
                            if (node.className.indexOf(_posStr) > -1) {
                                tooth.hover = this.collide(rects, rectSelect);
                            }
                        });
                    }
                });
            },
            handleMouseUp() {
                this._domBox && off(this._domBox, 'mousemove', this.handleMouseMove);
                this._domBox && off(this._domBox, 'mouseup', this.handleMouseUp);
                this.showMask = false;
                if (this.endX - this.startX > 20 || this.endY - this.startY > 20) {
                    this.handleDomSelect();
                }
                this.resSetXY();
            },
            collide(rect1, rect2) {
                const maxX = Math.max(rect1.x + rect1.width, rect2.x + rect2.width);
                const maxY = Math.max(rect1.y + rect1.height, rect2.y + rect2.height);
                const minX = Math.min(rect1.x, rect2.x);
                const minY = Math.min(rect1.y, rect2.y);
                if (maxX - minX <= rect1.width + rect2.width && maxY - minY <= rect1.height + rect2.height) {
                    return true;
                }
                return false;

            },
            handleDomSelect() {
                const domMask = this.$refs.ctrlSelectMask;
                const rectSelect = domMask.getClientRects()[0];
                this._selectedTeethList = [];
                document.querySelectorAll('.tooth-item:not(.is-disabled)').forEach((node) => {
                    const rects = node.getClientRects()[0];
                    if (this.collide(rects, rectSelect)) {
                        if (node.className.indexOf('top-right') > -1 || node.className.indexOf('top-left') > -1) {
                            this.topToothList.forEach((tooth) => {
                                const _posStr = `${tooth.position}-${tooth.no}`;
                                if (node.className.indexOf(_posStr) > -1) {
                                    this._selectedTeethList.push(tooth);
                                }
                            });
                        } else {
                            this.bottomToothList.forEach((tooth) => {
                                const _posStr = `${tooth.position}-${tooth.no}`;
                                if (node.className.indexOf(_posStr) > -1) {
                                    this._selectedTeethList.push(tooth);
                                }
                            });
                        }
                    }
                });
                this.clickToothHandle(this._selectedTeethList[this._selectedTeethList.length - 1]);
            },
            resSetXY() {
                this.startX = 0;
                this.startY = 0;
                this.endX = 0;
                this.endY = 0;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .oral-examination-wrapper {
        .medical-record-suggestions-wrapper {
            .cover {
                position: absolute;
                top: 39px;
                left: 0;
                z-index: 1;
                width: 100%;
                height: 270px;
                background-color: rgba(0, 0, 0, 0.05);
            }
        }

        .select-tooth-wrapper {
            position: relative;
            display: flex;
            align-items: center;

            .top-tooth,
            .bottom-tooth {
                display: flex;
                align-items: center;

                > div {
                    display: flex;
                    align-items: center;
                }
            }

            .tooth-no {
                width: 16px;
                height: 16px;
                margin: 8px 0;
                font-size: 12px;
                line-height: 15px;
                color: #96a4b3;
                text-align: center;
                user-select: none;
                border: 1px solid $P4;
                border-radius: 16px;
            }

            .top-tooth {
                border-bottom: 1px solid $P6;

                .tooth-img {
                    background: transparent no-repeat center bottom;
                    background-size: 80%;
                }
            }

            .bottom-tooth {
                .tooth-img {
                    background: transparent no-repeat center top;
                    background-size: 80%;
                }
            }

            .tooth-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 35px;
                height: 112px;
                cursor: pointer;
                outline: none;

                .tooth-img {
                    flex: 1;
                    width: 100%;

                    &.top-right,
                    &.bottom-right {
                        transform: scaleX(-1);
                    }
                }

                .tooth-no {
                    margin: 10px 0;
                }

                .top-tooth-1 {
                    background-image: url('~assets/images/tooth/tooth_top_1.png');
                }

                .top-tooth-2 {
                    background-image: url('~assets/images/tooth/tooth_top_2.png');
                }

                .top-tooth-3 {
                    background-image: url('~assets/images/tooth/tooth_top_3.png');
                }

                .top-tooth-4 {
                    background-image: url('~assets/images/tooth/tooth_top_4.png');
                }

                .top-tooth-5 {
                    background-image: url('~assets/images/tooth/tooth_top_5.png');
                }

                .top-tooth-6 {
                    background-image: url('~assets/images/tooth/tooth_top_6.png');
                }

                .top-tooth-7 {
                    background-image: url('~assets/images/tooth/tooth_top_7.png');
                }

                .top-tooth-8 {
                    background-image: url('~assets/images/tooth/tooth_top_8.png');
                }

                .bottom-tooth-1 {
                    background-image: url('~assets/images/tooth/tooth_bottom_1.png');
                }

                .bottom-tooth-2 {
                    background-image: url('~assets/images/tooth/tooth_bottom_2.png');
                }

                .bottom-tooth-3 {
                    background-image: url('~assets/images/tooth/tooth_bottom_3.png');
                }

                .bottom-tooth-4 {
                    background-image: url('~assets/images/tooth/tooth_bottom_4.png');
                }

                .bottom-tooth-5 {
                    background-image: url('~assets/images/tooth/tooth_bottom_5.png');
                }

                .bottom-tooth-6 {
                    background-image: url('~assets/images/tooth/tooth_bottom_6.png');
                }

                .bottom-tooth-7 {
                    background-image: url('~assets/images/tooth/tooth_bottom_7.png');
                }

                .bottom-tooth-8 {
                    background-image: url('~assets/images/tooth/tooth_bottom_8.png');
                }

                &:not(.is-disabled).is-hover,
                &:not(.is-disabled):hover {
                    .top-tooth-1 {
                        background-image: url('~assets/images/tooth/tooth_top_1_hover.png');
                    }

                    .top-tooth-2 {
                        background-image: url('~assets/images/tooth/tooth_top_2_hover.png');
                    }

                    .top-tooth-3 {
                        background-image: url('~assets/images/tooth/tooth_top_3_hover.png');
                    }

                    .top-tooth-4 {
                        background-image: url('~assets/images/tooth/tooth_top_4_hover.png');
                    }

                    .top-tooth-5 {
                        background-image: url('~assets/images/tooth/tooth_top_5_hover.png');
                    }

                    .top-tooth-6 {
                        background-image: url('~assets/images/tooth/tooth_top_6_hover.png');
                    }

                    .top-tooth-7 {
                        background-image: url('~assets/images/tooth/tooth_top_7_hover.png');
                    }

                    .top-tooth-8 {
                        background-image: url('~assets/images/tooth/tooth_top_8_hover.png');
                    }

                    .bottom-tooth-1 {
                        background-image: url('~assets/images/tooth/tooth_bottom_1_hover.png');
                    }

                    .bottom-tooth-2 {
                        background-image: url('~assets/images/tooth/tooth_bottom_2_hover.png');
                    }

                    .bottom-tooth-3 {
                        background-image: url('~assets/images/tooth/tooth_bottom_3_hover.png');
                    }

                    .bottom-tooth-4 {
                        background-image: url('~assets/images/tooth/tooth_bottom_4_hover.png');
                    }

                    .bottom-tooth-5 {
                        background-image: url('~assets/images/tooth/tooth_bottom_5_hover.png');
                    }

                    .bottom-tooth-6 {
                        background-image: url('~assets/images/tooth/tooth_bottom_6_hover.png');
                    }

                    .bottom-tooth-7 {
                        background-image: url('~assets/images/tooth/tooth_bottom_7_hover.png');
                    }

                    .bottom-tooth-8 {
                        background-image: url('~assets/images/tooth/tooth_bottom_8_hover.png');
                    }

                    .tooth-no {
                        border-color: $B2;
                    }
                }

                &:not(.is-disabled).is-hover {
                    position: relative;
                    z-index: 2;
                }

                &.is-selected {
                    .tooth-no {
                        color: $S2;
                        background-color: $B2;
                    }
                }

                &.top-left-1,
                &.bottom-left-1 {
                    position: relative;

                    &::after {
                        position: absolute;
                        right: 0;
                        bottom: 0;
                        width: 1px;
                        height: 30px;
                        content: ' ';
                        background-color: $P6;
                    }
                }

                &.bottom-left-1 {
                    &::after {
                        top: 0;
                    }
                }
            }

            .wisdom-teeth {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-left: 20px;

                .tooth-no {
                    cursor: pointer;
                    user-select: none;
                    outline: none;

                    &:not(.is-disabled):hover,
                    &:not(.is-disabled).is-hover {
                        border: 1px solid $B2;
                    }

                    &.is-selected {
                        color: $S2;
                        background-color: $B2;
                        border: 1px solid $B2;
                    }
                }
            }
        }

        .abc-edit-div {
            word-wrap: break-word;
            white-space: pre-wrap;

            > span {
                display: inline-block;
                padding: 2px 4px;
                font-family: Roboto;
                font-size: 12px;
                line-height: 12px;
            }

            span.top-left {
                border-right: 1px solid #878c92;
                border-bottom: 1px solid #878c92;
            }

            span.top-right {
                border-bottom: 1px solid #878c92;
                border-left: 1px solid #878c92;
            }

            span.bottom-left {
                border-top: 1px solid #878c92;
                border-right: 1px solid #878c92;
            }

            span.bottom-right {
                border-top: 1px solid #878c92;
                border-left: 1px solid #878c92;
            }
        }

        .ctrl-select-mask {
            position: absolute;
            background: rgba(64, 158, 255, 0.3);
            border: 1px solid #409eff;
        }

        .examination-footer {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;

            .abc-checkbox-wrapper {
                position: absolute;
                top: 0;
                left: 12px;
                width: auto;
                text-indent: 0;
                border-right: 0;
            }

            .tips {
                font-size: 12px;
                color: $T3;
            }
        }
    }
</style>
