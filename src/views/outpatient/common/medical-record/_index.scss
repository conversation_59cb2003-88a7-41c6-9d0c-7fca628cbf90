@import "../../../../styles/theme";
@import "../doctor-advice-dialog/doctor-advice-dialog";
@import "src/styles/mixin.scss";

.fade-enter-active {
    transition: all 0.2s ease-in;
}

.fade-leave-active {
    transition-duration: 0s;
}

.fade-enter,
.fade-leave-to {
    /* opacity: 0; */
}

.medical-record-suggestions-wrapper {
    position: absolute;
    top: 40px;
    left: 40px;
    z-index: 1009;
    width: calc(100% - 40px);
    min-width: 400px;
    font-size: 14px;
    background-color: var(--abc-color-cp-white);
    border: 1px solid var(--abc-color-P7);
    border-radius: var(--abc-border-radius-small);
    box-shadow: var(--abc-shadow-1);

    &.no-shadow {
        background-color: transparent;
        border: none;
        border-radius: 0;
        box-shadow: none;
    }

    &.fixed {
        position: fixed;
        top: unset;
        left: 0;
    }

    .close-suggestions {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 40px;
        font-weight: 500;
        line-height: 40px;
        color: $theme1;
        cursor: pointer;
        background: #f7f7f7;
        border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

        .iconfont {
            margin-right: 8px;
        }

        .setting-btn {
            position: absolute;
            top: 0;
            right: 0;
            width: 40px;
            color: $T3;
            text-align: center;
            cursor: pointer;

            &:hover {
                color: $B2;
            }
        }
    }

    .left {
        flex: 1;
    }

    .symptom-list,
    .extent-list {
        position: relative;
        padding: 0 7px;

        h5 {
            padding-left: 8px;
            margin-top: 12px;
            font-size: 14px;
            font-weight: bold;
        }

        ul {
            margin: 10px 0;

            li:first-child {
                margin-bottom: 2px;
            }
        }

        h5 + ul {
            margin: 8px 0 10px 0;
        }

        .cut-line {
            height: 1px;
            margin: 0 8px;
            font-size: 0;
            background-color: var(--abc-color-P6);
        }

        li {
            >div {
                display: inline-block;
                padding: 5px 8px;
                line-height: 1;
                color: $T2;
                cursor: pointer;
                user-select: none;
                border-radius: var(--abc-border-radius-small);

                &:hover {
                    color: $theme2;
                    background-color: $theme4;
                }
            }
        }

        &::after {
            display: block;
            height: 0;
            clear: both;
            font-size: 0;
            visibility: hidden;
            content: " ";
        }
    }
}

.registration-container .medical-record-wrapper .medical-record-suggestions-wrapper {
    width: 606px !important;
}

.medical-record-wrapper {
    background-color: var(--abc-color-LY4);
    border: 1px solid var(--abc-color-card-border-color);
    border-radius: var(--abc-border-radius-small);

    //jason在处理不允许复制选择处方时加的，但ios上影响了可编辑div的输入，
    // user-select: none;
    .medical-record-item {
        display: flex;

        label:not(.abc-checkbox-wrapper) {
            width: 98px;
            line-height: 36px;
            color: #626d77;
            text-indent: 12px;
            border-right: 1px solid $abcCardDividerColor;
        }

        .label-name::after {
            margin-left: 4px;
            font-family: SimSun;
            font-size: 14px;
            line-height: 1;
            color: var(--abc-color-Y2);
            content: "*";
        }

        & + .medical-record-item {
            border-top: 1px solid $abcCardDividerColor;
        }

        .patient-guardian-first-label {
            display: inline-block;
            width: 110px;
            height: 36px;
            line-height: 36px;
            color: #626d77;
            text-indent: 12px;
            border-right: 1px solid $abcCardDividerColor;
        }

        .label-bold {
            font-weight: bold;
        }

        .second-label {
            border-left: 1px solid $abcCardDividerColor;
        }

        .abc-form-item {
            flex: 1;
            margin: 0;
        }

        .abc-edit-div {
            display: block;
        }

        .abc-input__inner {
            height: auto;
            min-height: 36px;
            padding: 7px 10px;
            font-size: 14px;
            line-height: 20px;
            word-break: break-word;
            background-color: #fffdec !important;
            border-color: transparent;
            border-radius: 0;

            &:focus {
                position: relative;
                z-index: 2;
                border-color: $theme1;
            }
        }

        &:nth-last-child(3) {
            .abc-form-item:last-child .abc-input__inner {
                border-radius: 0 0 var(--abc-border-radius-small) 0;
            }
        }

        &.is-first-item {
            .abc-input__inner {
                border-radius: 0 var(--abc-border-radius-small) 0 0;
            }
        }

        &:last-child,
        &.is-last-item {
            .abc-input__inner {
                border-radius: 0 0 var(--abc-border-radius-small) 0;
            }
        }

        .upload-img {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 2;
            display: none;
            width: 76px;
            height: 36px;
            font-size: 14px;
            line-height: 36px;
            color: $B2;
            text-align: center;
            cursor: pointer;
        }

        &:hover {
            .upload-img {
                display: block;
            }
        }

        .tcm-syndrome {
            .abc-form-item-content {
                height: 100%;
            }

            .aide-diagnosis-wrapper {
                height: 100%;
            }

            .abc-input__inner {
                height: 100%;
            }
        }
    }

    .medical-record-tips {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid $P6;

        .tips {
            height: 36px;
            line-height: 36px;
            color: $T3;
            text-indent: 12px;
        }
    }

    &.is-disabled {
        background-color: var(--abc-color-bg-disabled);

        .abc-input__inner {
            background-color: var(--abc-color-cp-grey2) !important;
        }
    }
}

.aide-diagnosis-wrapper {
    .diagnosis-social-info-wrapper {
        position: relative;

        .diagnosis-social-info-popover {
            position: absolute;
            bottom: 40px;
            left: 0;
            box-sizing: border-box;
            display: none;
            max-height: 320px;
            padding: 16px;
            overflow-y: auto;
            overflow-y: overlay;
            font-size: 14px;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

            .social-code {
                display: flex;
                align-items: center;
                justify-content: space-between;

                span + span {
                    margin-left: 12px;
                }
            }
        }

        &:hover {
            .diagnosis-social-info-popover {
                display: block;
            }
        }
    }

    .abc-edit-div {
        i {
            color: $T3;
            -webkit-user-modify: read-only;
        }

        i.name {
            font-style: normal;
            color: $Y2;
        }

        .shortage-tips {
            position: relative;
            display: inline-flex;

            i {
                font-style: normal;
                color: $Y2;
            }

            .abc-tipsy--n {
                i {
                    font-style: normal;
                    color: $Y2;
                }
            }

            &.abc-tipsy--n::before {
                border-top-color: #000000;
            }

            &.abc-tipsy--n::after {
                width: 216px;
                padding: 10px 12px;
                white-space: normal;
                background-color: #000000;
            }
        }

        .abc-tipsy {
            margin-left: 2px;
            vertical-align: baseline;

            &.abc-tipsy--n::before {
                border-top-color: #000000;
            }

            &.abc-tipsy--n::after {
                width: 216px;
                padding: 10px 12px;
                white-space: normal;
                background-color: #000000;
            }
        }
    }

    .medical-record-suggestions-wrapper {
        >.item {
            padding: 14px 8px 16px;

            h5 {
                display: flex;
                padding: 12px 8px 8px;
                font-weight: bold;

                i {
                    margin-left: 4px;
                    font-size: 14px;
                    font-weight: normal;
                    color: $P3;
                    cursor: pointer;
                }

                .popper__pr-content {
                    margin-left: 6px;
                }
            }

            &:first-child h5 {
                color: $B1;
            }
        }

        li {
            display: inline-block;
            padding: 5px 8px;
            margin-right: 2px;
            line-height: 1;
            color: $T2;
            cursor: pointer;
            -moz-user-select: none;

            /* 火狐 */
            -webkit-user-select: none;

            /* webkit浏览器 */
            -ms-user-select: none;

            /* IE10 */
            user-select: none;
            border-radius: var(--abc-border-radius-small);

            &:hover {
                color: $theme2;
                background-color: $theme4;
            }
        }
    }

    .medical-record-suggestions-wrapper.search-results {
        left: 0;
        width: auto;
        min-width: 284px;
        max-height: 260px;
        padding: 4px 0 4px 4px;
        margin: 0;
        overflow-y: scroll;

        @include scrollBar;

        &.is-diagnosis {
            width: 100%;
            min-width: 556px;

            .suggestions-item {
                overflow: initial;

                .diagnosis-name {
                    flex: 1;
                    width: 0;

                    @include ellipsis;

                    .clinical-disease-simple {
                        font-size: 12px;
                    }
                }

                .diagnosis-type {
                    width: 80px;
                    margin: 0 10px;
                }

                .diagnosis-social-type {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    width: 90px;
                    padding-right: 8px;
                    color: $T2;

                    >span {
                        font-size: 13px;
                    }
                }

                .diagnosis-social-code {
                    display: flex;
                    align-items: center;
                    width: 100px;
                    margin-left: 12px;
                    color: $T2;

                    >span {
                        font-size: 13px;
                    }

                    .iconfont {
                        margin-right: 4px;
                        color: $P1;
                    }

                    &.abc-tipsy--n::before {
                        left: 8px;
                    }

                    &.abc-tipsy--n::after {
                        left: 0;
                        transform: translateX(-10%);
                    }
                }

                &.selected {
                    .diagnosis-social-type,
                    .diagnosis-social-code {
                        color: #ffffff;

                        .iconfont {
                            color: #ffffff;
                        }
                    }

                    .diagnosis-name {
                        .clinical-disease-simple {
                            color: #ffffff;
                        }
                    }
                }
            }
        }

        &.fixed {
            max-width: 608px;
            margin-left: 0 !important;
        }

        &.is-only-name {
            min-width: 288px !important;
        }
    }
}

.present-history-wrapper,
.physical-examination-wrapper,
.oral-examination-wrapper,
.histories-wrapper {
    .medical-record-suggestions-wrapper {
        .examination-title {
            display: flex;
            padding: 0 16px;
            margin: 8px 0 4px;
            border-bottom: 1px solid $P6;

            h5 {
                height: 30px;
                margin-top: 0;
                margin-right: 32px;
                font-size: 14px;
                font-weight: bold;
                line-height: 30px;
                color: $T2;
                cursor: pointer;

                &.selected {
                    color: $T1;
                    border-bottom: 2px solid $theme2;
                }

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        .examination-content {
            .content-tabs {
                display: flex;
                flex-direction: column;
                gap: var(--abc-space-s);
                margin-right: 2px;

                >li {
                    display: flex;
                    align-items: center;
                    width: 126px;
                    height: 32px;
                    padding: 0 var(--abc-space-m);
                    cursor: pointer;
                    border-radius: var(--abc-border-radius-mini);

                    &.is-selected {
                        color: var(--abc-color-T1);
                        background-color: var(--abc-color-B4);
                    }

                    &:hover {
                        background: var(--abc-color-cp-grey4);
                    }
                }
            }

            .examination-list-wrapper {
                flex: 1;
            }

            .unit {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                min-width: 72px;
                padding: 0 18px;
                font-size: 24px;
                color: var(--abc-color-T3);
            }
        }

        .examination-list {
            display: flex;

            .type {
                width: 60px;
                padding: 5px 8px 0 0;
                color: $T2;
                text-align: right;
            }

            ul {
                flex: 1;
            }

            ul.is-line {
                height: 1px;
                background-color: $P6;
            }
        }

        .suggestion-examination-list {
            h5 {
                display: inline-flex;
                align-items: center;
                height: 32px;
                padding: 0 8px;
                font-weight: normal;
                color: $B1;
                outline: none;

                i {
                    margin-left: 4px;
                    font-size: 14px;
                    color: $P3;
                }
            }

            ul.chinese-suggestion {
                max-height: 26px;
                overflow: hidden;
            }

            .ai-blank-tips {
                padding-top: 20px;
                padding-left: 10px;
                color: $T3;
            }
        }

        .examination-list:not(.chinese-examination) li,
        .suggestion-examination-list li {
            display: inline-block;
            min-width: 42px;
            padding: 5px 6px;
            margin-bottom: 4px;
            line-height: 1;
            color: $T2;
            text-align: center;
            cursor: pointer;
            -moz-user-select: none;

            /* 火狐 */
            -webkit-user-select: none;

            /* webkit浏览器 */
            -ms-user-select: none;

            /* IE10 */
            user-select: none;
            border-radius: var(--abc-border-radius-small);

            &:hover {
                color: $theme2;
                background-color: $theme4;
            }
        }

        .chinese-examination {
            flex-direction: column;
            padding: 0 8px;

            ul {
                flex: none;
                padding: 6px 0;
                border-bottom: 1px solid $P6;

                &:last-child {
                    border-bottom: 0;
                }
            }

            li {
                >div {
                    display: inline-block;
                    padding: 5px 8px;
                    line-height: 1;
                    color: $T2;
                    cursor: pointer;
                    user-select: none;
                    border-radius: var(--abc-border-radius-small);

                    &:not(.vertical-line) {
                        min-width: 44px;
                    }

                    &:hover {
                        color: $theme2;
                        background-color: $theme4;
                    }
                }

                .vertical-line {
                    width: 1px;
                    height: 20px;
                    padding: 0;
                    margin: 0 10px;
                    vertical-align: text-bottom;
                    background-color: $P6;
                }

                &:first-child {
                    margin-bottom: 2px;
                }
            }
        }

        .ai-tips {
            position: absolute;
            right: 12px;
            bottom: 46px;
            display: flex;
            align-items: center;
            font-size: 12px;
            color: $T3;

            i {
                margin-right: 4px;
                font-size: 14px;
                color: $T3;
            }
        }
    }
}

.doctor-advice-wrapper {
    .medical-record-suggestions-wrapper {
        ul {
            max-height: 176px;
            padding: 10px;
            overflow-y: auto;
            overflow-y: overlay;

            @include scrollBar;
        }

        li {
            margin-right: 2px;
            color: $T2;
            cursor: pointer;
            -moz-user-select: none;

            /* 火狐 */
            -webkit-user-select: none;

            /* webkit浏览器 */
            -ms-user-select: none;

            /* IE10 */
            user-select: none;
            border-radius: var(--abc-border-radius-small);

            >div {
                display: inline-block;
                max-width: 100%;
                padding: 6px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                border-radius: 0 var(--abc-border-radius-small);
            }

            &:hover {
                >div {
                    color: $theme2;
                    background-color: $theme4;
                }
            }
        }

        .no-data {
            color: $T3;
            text-align: center;

            li {
                height: 64px;
                line-height: 64px;
                cursor: default;
            }
        }
    }
}
