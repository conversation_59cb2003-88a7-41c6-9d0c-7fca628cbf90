<template>
    <div v-abc-click-outside="outside" class="physical-examination-wrapper">
        <abc-edit-div
            ref="ref-target"
            :value="displayValue"
            :class="{ 'is-focus': showSuggestions }"
            :disabled="disabled"
            :maxlength="1000"
            :data-cy="`abc-mr-${label}`"
            :xss-options="{
                a: ['class', 'data-tipsy'],
                i: ['class', 'contenteditable']
            }"
            @click="handleClick"
            @tab="handleTab"
            @input="handleInput"
            @keydown.down.prevent="handleDown"
            @keydown.up.prevent="handleUp"
        >
        </abc-edit-div>

        <input
            ref="abcinput"
            type="text"
            style=" position: absolute; top: 0; left: 0; width: 0; opacity: 0;"
            tabindex="-1"
            :value="value"
        />

        <div
            v-if="showSuggestions"
            ref="popper-target"
            class="medical-record-suggestions-wrapper no-shadow"
            :class="{ fixed: fixed }"
            :style="suggestionsStyle"
            :data-cy="`abc-mr-popover-${label}`"
        >
            <biz-quick-options-panel
                :close-data-cy="`abc-mr-${label}-close`"
                :tabs-value="currentTabActiveKey"
                :tabs-options="panelTabOptions"
                :custom-header-style="{
                    paddingLeft: '8px',
                    marginBottom: '8px'
                }"
                @close="showSuggestions = false"
                @changeTabs="handleChangeTabs"
            >
                <!--自定义头部内容-->
                <template v-if="(hasChineseSuggestion || hasWesternSuggestion) && type === medicalRecordType" #header>
                    <div class="suggestion-examination-list">
                        <!--体格检查-->
                        <template v-if="type === 0">
                            <biz-quick-options-panel-group
                                :list="renderSuggestionExaminations"
                                @select="(item) => selectSuggestionExamination(item.value)"
                            ></biz-quick-options-panel-group>
                        </template>

                        <!--望闻切诊-->
                        <template v-else-if="type === 1">
                            <biz-quick-options-panel-group
                                :list="renderChineseSuggestion"
                                @select="(item) => selectSuggestionExamination(item.value)"
                            ></biz-quick-options-panel-group>
                        </template>
                    </div>
                </template>

                <!--儿保内容-->
                <template v-if="isChildcare" #default>
                    <biz-quick-options-panel-group
                        v-for="(group, idx) in renderChildHealth"
                        :key="`options-panel-group-${idx}`"
                        :label="group.label"
                        :list="group.list"
                        :label-width="52"
                        label-position="left"
                        :label-style="{ textAlign: 'right' }"
                        @select="(item) => selectChildHealthPE(item.value)"
                    >
                    </biz-quick-options-panel-group>
                </template>

                <template v-else #default>
                    <abc-flex class="examination-content">
                        <!--tab-->
                        <ul v-if="currentTabActiveKey === 'weight'" class="content-tabs">
                            <li
                                v-for="tab in curPhysicalExamination.curTabs"
                                :key="tab.value"
                                :class="{
                                    'is-selected': currentWeightTabActiveKey === tab.value,
                                }"
                                :data-cy="`abc-mr-${label}-tab-${tab.name}`"
                                @click="currentWeightTabActiveKey = tab.value"
                            >
                                {{ tab.label }}
                            </li>
                        </ul>

                        <ul v-if="currentTabActiveKey === 'height'" class="content-tabs">
                            <li
                                v-for="tab in curPhysicalExamination.curTabs"
                                :key="tab.value"
                                :class="{
                                    'is-selected': currentHeightTabActiveKey === tab.value,
                                }"
                                :data-cy="`abc-mr-${label}-tab-${tab.name}`"
                                @click="currentHeightTabActiveKey = tab.value"
                            >
                                {{ tab.label }}
                            </li>
                        </ul>

                        <!--内容列表-->
                        <div class="examination-list-wrapper">
                            <abc-flex vertical :gap="8">
                                <template v-for="(group, idx) in curPhysicalExamination.groupList">
                                    <biz-quick-options-panel-group
                                        :key="`chinese-options-panel-group-${idx}`"
                                        :label="group.label"
                                        label-position="left"
                                        :list="group.list"
                                        @select="item => handleSelectItem(item)"
                                    ></biz-quick-options-panel-group>

                                    <div
                                        v-if="idx < curPhysicalExamination.groupList.length - 1"
                                        :key="`options-panel-group-divider-${idx}`"
                                        style=" margin-right: var(--abc-paddingLR-m); margin-left: var(--abc-paddingLR-m);"
                                    >
                                        <abc-divider
                                            margin="none"
                                            variant="dashed"
                                        ></abc-divider>
                                    </div>
                                </template>
                            </abc-flex>
                        </div>

                        <!--单位-->
                        <div v-if="curPhysicalExamination.unit" class="unit">
                            {{ curPhysicalExamination.unit }}
                        </div>
                    </abc-flex>
                </template>

                <template #extra>
                    <abc-flex
                        v-if="(hasChineseSuggestion || hasWesternSuggestion) && type === medicalRecordType"
                        class="ai-footer-tips"
                        :gap="4"
                        align="center"
                    >
                        <abc-icon icon="s-abcai-color" :size="14"></abc-icon>
                        <abc-text size="mini" theme="gray-light" style="font-size: 12px;">
                            智能{{ type === 1 ? '望闻切诊' : '体征' }}提示
                        </abc-text>
                        <abc-tooltip-info
                            placement="top-start"
                            :max-width="300"
                            :icon-size="12"
                            content="AI辅助诊断系统可根据患者主诉、病史等输入信息智能推断出相关体征信息提示，方便医生进一步关注和循证。同时，AI辅助诊断系统会根据中西医病历类型，做出不同的体征提示或望闻切诊提示。"
                        >
                        </abc-tooltip-info>
                    </abc-flex>
                </template>
            </biz-quick-options-panel>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
// API
    import { loosePlainText } from 'utils/xss-filter';
    import popper from './popper';
    import { mapGetters } from 'vuex';
    import common from 'components/common/form';
    import {
        BizQuickOptionsPanel,
        BizQuickOptionsPanelGroup,
    } from '@/components-composite/biz-quick-options-panel/index';
    import { physicalExaminations } from 'views/outpatient/common/medical-record/constants';
    import { clone } from '@abc/utils';
    import {
        keepLastIndex,findNodeAndOffsetForCharIndex,
    } from 'utils/dom';

    export default {
        components: {
            BizQuickOptionsPanel,
            BizQuickOptionsPanelGroup,
        },
        mixins: [common, popper],
        props: {
            value: String,
            type: Number,
            medicalRecordType: Number, // 病历类型0：西医；1：中医
            disabled: Boolean,
            isChildcare: {
                type: Boolean,
                default: false,
            },
            label: String,
        },
        data() {
            return {
                pos: null,
                cursorPosition: null, // 添加一个变量来存储光标位置
                isFocus: false,
                showSuggestions: false,
                displayValue: '',
                selectExaminations: [],
                pulses: [],
                tongues: [],
                moss: [],
                suggestionsStyle: {
                    top: '40px',
                },
                currentTabActiveKey: '',
                currentWeightTabActiveKey: 0,
                currentHeightTabActiveKey: 0,
            };
        },

        computed: {
            ...mapGetters('aiDiagnosis', [
                'diseases',
                'sickness',
                'chinesePhysical',
            ]),

            suggestionExaminations() {
                const data = this.diseases;
                let list = [];
                data.forEach((item, index) => {
                    if (index < 3) {
                        list = list.concat(item.physical_exam.map((item) => { return item.symptomName; }));
                    }
                });
                // 去重
                const setArr = new Set(list);
                return new Array(...setArr);
            },
            renderSuggestionExaminations() {
                return this.suggestionExaminations.map(this.formatPanelListItem);
            },

            chineseSuggestion() {
                // 中医望闻切诊
                const {
                    lichen = [],
                    observe = [],
                    pulse = [],
                    tongue = [],
                } = this.chinesePhysical || {};
                lichen.length = lichen.length > 5 ? 5 : lichen.length;
                observe.length = observe.length > 5 ? 5 : observe.length;
                pulse.length = pulse.length > 5 ? 5 : pulse.length;
                tongue.length = tongue.length > 5 ? 5 : tongue.length;
                return {
                    lichen,
                    observe,
                    pulse,
                    tongue,
                };
            },
            renderChineseSuggestion() {
                return [
                    ...[...this.chineseSuggestion.observe, ...this.chineseSuggestion.tongue].map((item, idx) => ({
                        label: item,
                        value: item,
                        dataCy: `abc-mr-${this.label}-${item}`,
                        isBreak: idx === this.chineseSuggestion.observe.length + this.chineseSuggestion.tongue.length - 1,
                    })),
                    ...[...this.chineseSuggestion.lichen, ...this.chineseSuggestion.pulse].map((item) => ({
                        label: item,
                        value: item,
                        dataCy: `abc-mr-${this.label}-${item}`,
                    })),
                ];
            },

            hasWesternSuggestion() {
                return this.suggestionExaminations.length;
            },
            hasChineseSuggestion() {
                const {
                    lichen,
                    observe,
                    pulse,
                    tongue,
                } = this.chineseSuggestion;
                let length = 0;
                length += lichen.length;
                length += observe.length;
                length += pulse.length;
                length += tongue.length;
                return length;
            },

            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.formItem && this.formItem.$emit('formFieldInput', val);
                    this.$emit('input', val);
                },
            },

            renderChildHealth() {
                return [
                    {
                        'label': '眼',
                        'value': ['眼袋发青', '眼袋发红', '眼袋发紫', '眼皮浮肿', '眼皮下垂', '结膜充血', '鼻梁山根青筋'],
                    },
                    {
                        'label': '耳',
                        'value': ['耳外道有渗出物', '耳外道发炎'],
                    },
                    {
                        'label': '唇齿',
                        'value': ['嘴唇偏白', '嘴唇干、起皮', '有龋齿', '牙龈红肿', '牙龈出血', '扁桃体肿大'],
                    },
                    {
                        'label': '面&皮肤',
                        'value': ['脸有红斑', '脸有白斑', '脸有皮疹', '肤色蜡黄', '色素沉着', '水肿'],
                    },
                    {
                        'label': '腹',
                        'value': ['腹部肿大', '腹部硬', '易打嗝', '口臭', '食量过大', '食量过小', '口味偏重'],
                    },
                    {
                        'label': '大小便',
                        'value': ['大便干燥', '大便不成形', '大便呈颗粒状', '大便夹泡沫', '大便夹奶瓣', '大便夹食物残渣'],
                    },
                    {
                        'label': '胸肺',
                        'value': ['鸡胸', '漏斗胸', '桶装胸', '串珠肋', '肋缘外翻', '呼吸困难', '呼吸有浊音'],
                    },
                    {
                        'label': '出汗',
                        'value': ['头部出汗', '浑身出汗', '颈部以上出汗', '白天出汗', '晚上出汗', '鼻头出汗'],
                    },
                ].map((item) => ({
                    label: item.label,
                    list: item.value.map(this.formatPanelListItem),
                }));
            },

            panelTabOptions() {
                // 儿保
                if (this.isChildcare) {
                    return [];
                }

                let tabOptions = physicalExaminations.map((item) => ({
                    label: item.label,
                    value: item.key,
                }));

                // 体格检查过滤掉望闻切
                if (this.type === 0) {
                    tabOptions = tabOptions.filter((item) => item.value !== 'chineseExamination');
                }

                // 望闻切诊过滤体征
                if (this.type === 1) {
                    tabOptions = tabOptions.filter((item) => item.value !== 'sign');
                }

                return tabOptions;
            },

            curPhysicalExamination() {
                let physicalExamination = physicalExaminations.find((item) => item.key === this.currentTabActiveKey);

                if (physicalExamination) {
                    physicalExamination = clone(physicalExamination);

                    if (physicalExamination.curTabs?.length) {
                        let selectedTab = this.currentHeightTabActiveKey;
                        if (this.currentTabActiveKey === 'weight') {
                            selectedTab = this.currentWeightTabActiveKey;
                        }
                        physicalExamination.groupList = [ physicalExamination.groupList[selectedTab] ];
                    }

                    // 转换数据为 biz-quick-options-panel 的参数格式
                    physicalExamination.groupList.forEach((group) => {
                        const item = group.list[0];
                        if (Array.isArray(item)) {
                            group.list = group.list.reduce((res, listItem) => {
                                const formatList = listItem.map(this.formatPanelListItem);
                                formatList[formatList.length - 1].isBreak = true;
                                return [...res, ...formatList];
                            }, []);
                        } else if (typeof item === 'object') {
                            group.list = group.list.reduce((res, rowObj) => {
                                const row = rowObj.list.reduce((_res, l, idx) => {
                                    // 脉的单选多选
                                    const isSingleSelect = idx === rowObj.list.length - 1 && rowObj.type === 'pulse';
                                    const formatList = l.map(this.formatPanelListItem).map((_item) => ({
                                        ..._item,
                                        type: rowObj.type,
                                        isSingleSelect,
                                    }));
                                    formatList[formatList.length - 1].isSplit = true;
                                    return [..._res, ...formatList];
                                }, []);

                                row[row.length - 1].isBreak = true;
                                delete row[row.length - 1].isSplit;

                                return [...res, ...row];
                            }, []);
                        } else {
                            group.list = group.list.map(this.formatPanelListItem);
                        }
                    });
                }

                return physicalExamination;
            },
        },

        watch: {
            medicalRecordType() {
                this.changeType();
            },
            currentValue: {
                handler() {
                    if (!this.showSuggestions) {
                        this.handleDisplayValue();
                    }
                    this.updateStyle();
                },
                immediate: true,
            },
            disabled() {
                this.handleDisplayValue();
            },
            showSuggestions(val) {
                if (val) {
                    this.handleDisplayValue();
                    // 恢复到用户点击时的光标位置，而不是强制移到末尾
                    this.timer = setTimeout(() => {
                        this.restoreCursorPosition();
                    }, 1);
                } else {
                    this.handleDisplayValue();
                }
            },
        },

        async created() {
            this.changeType();
        },
        mounted() {
            this.updateStyle();
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            handleSelectItem(item) {
                switch (item.type) {
                    case 'tongue':
                        this.selectTongue(item.value.replace('舌', ''));
                        break;
                    case 'moss':
                        this.selectMoss(item.value.replace('苔', ''));
                        break;
                    case 'pulse':
                        this.selectPulse(item.value.replace(item.isSingleSelect ? '' : '脉', ''), item.isSingleSelect);
                        break;
                    default:
                        this.selectPhysicalExamination(item.value, this.curPhysicalExamination);
                        break;
                }
            },
            /**
             * @desc 不同病历类型，展示的体格检查望闻切诊不一样
             * <AUTHOR>
             * @date 2020/05/09 19:10:53
             */
            changeType() {
                this.currentTabActiveKey = this.panelTabOptions[0]?.value || '';
            },

            handleChangeTabs(val) {
                this.currentTabActiveKey = val;
                if (this._selectFirst) {
                    this._selectFirst = false;
                }
            },

            handleClick() {
                if (this.disabled) return false;
                // 使用nextTick确保DOM已更新后再保存光标位置
                this.$nextTick(() => {
                    this.saveCursorPosition();
                });
                this.showSuggestions = true;
            },
            handleTab() {
                this.showSuggestions = false;
            },

            /**
             * @desc 用户有输入，需要清空选择数组
             * <AUTHOR>
             * @date 2019/08/12 11:13:49
             */
            handleInput(val) {
                this.pulses = [];
                this.tongues = [];
                this.moss = [];
                this.currentValue = val;
            },

            outside() {
                this.showSuggestions = false;
            },

            updateStyle() {
                this._timer = setTimeout(() => {
                    this.suggestionsStyle = {
                        top: this.fixed ? '0' : `${this.$children[0].$refs?.abcinput?.offsetHeight + 4}px`,
                    };
                });
            },

            getValue(event) {
                this.$emit('input', loosePlainText(event.currentTarget.innerHTML, false));
                this.$emit('blur', event);
            },
            handleDown() {
                this.showSuggestions = false;
            },
            handleUp() {
                this.showSuggestions = false;
            },

            // 保存光标位置
            saveCursorPosition() {
                const editDiv = this.$refs['ref-target'].$el;
                if (!editDiv) return;

                try {
                    const selection = window.getSelection();
                    if (selection.rangeCount > 0) {
                        const range = selection.getRangeAt(0);

                        // 检查选区是否在编辑区域内
                        if (editDiv.contains(range.startContainer)) {
                            // 创建一个临时范围来计算偏移量
                            const preCaretRange = range.cloneRange();
                            preCaretRange.selectNodeContents(editDiv);
                            preCaretRange.setEnd(range.startContainer, range.startOffset);

                            // 计算光标前面的文本长度作为偏移量
                            const startOffset = preCaretRange.toString().length;

                            // 如果是选择了一段文本，还需要计算结束位置
                            preCaretRange.setEnd(range.endContainer, range.endOffset);
                            const endOffset = preCaretRange.toString().length;

                            // 保存偏移量和当前文本内容，而不是DOM节点引用
                            this.cursorPosition = {
                                start: startOffset,
                                end: endOffset,
                                text: editDiv.textContent || '',
                            };
                        }
                    }
                } catch (e) {
                    console.error('保存光标位置失败', e);
                }
            },

            // 恢复光标位置
            restoreCursorPosition() {
                const editDiv = this.$refs['ref-target'].$el;
                if (!editDiv || !this.cursorPosition) {
                    // 如果没有有效的光标位置，则默认移到末尾
                    keepLastIndex(editDiv);
                    return;
                }

                try {
                    // 检查文本内容是否发生了变化
                    const currentText = editDiv.textContent || '';
                    if (currentText !== this.cursorPosition.text) {
                        keepLastIndex(editDiv);
                        return;
                    }

                    // 遍历节点找到对应的位置
                    const charIndex = findNodeAndOffsetForCharIndex(editDiv, this.cursorPosition.start);
                    if (!charIndex) {
                        keepLastIndex(editDiv);
                        return;
                    }

                    const selection = window.getSelection();
                    const range = document.createRange();

                    // 设置光标位置
                    range.setStart(charIndex.node, charIndex.offset);

                    // 如果是选择了一段文本
                    if (this.cursorPosition.start !== this.cursorPosition.end) {
                        const endCharIndex = findNodeAndOffsetForCharIndex(editDiv, this.cursorPosition.end);
                        if (endCharIndex) {
                            range.setEnd(endCharIndex.node, endCharIndex.offset);
                        }
                    }

                    selection.removeAllRanges();
                    selection.addRange(range);
                } catch (e) {
                    console.error('恢复光标位置失败', e);
                    // 如果恢复失败，退回到将光标移到末尾的方案
                    keepLastIndex(editDiv);
                }
            },

            selectPhysicalExamination(examination, extraInfo) {
                this.pulses = [];
                this.tongues = [];
                this.moss = [];

                if (this.currentTabActiveKey === 'examination' ||
                    this.currentTabActiveKey === 'chineseExamination' ||
                    this.currentTabActiveKey === 'sign') {
                    if (this.currentValue) {
                        this.currentValue += `，${examination}`;
                    } else {
                        this.currentValue += examination;
                    }
                } else if (this.currentTabActiveKey === 'bloodPressure') {
                    if (this._selectFirst) {
                        this._selectFirst = false;
                        this.currentValue += ` / ${examination} ${extraInfo.unit}`;
                    } else {
                        this._selectFirst = true;
                        if (this.currentValue) {
                            this.currentValue += `，${extraInfo.label} ${examination}`;
                        } else {
                            this.currentValue += `${extraInfo.label} ${examination}`;
                        }
                    }
                } else {
                    const val = `${extraInfo.label} ${examination} ${extraInfo.unit}`;
                    if (this.currentValue) {
                        this.currentValue += `，${val}`;
                    } else {
                        this.currentValue += `${val}`;
                    }
                }
                this.$nextTick(() => {
                    this.handleDisplayValue();
                });
                this.focusInput();
            },

            selectChildHealthPE(val) {
                if (this.currentValue.indexOf(val) > -1) return false;

                if (this.currentValue) {
                    this.currentValue += `，${val}`;
                } else {
                    this.currentValue += `${val}`;
                }
                this.$nextTick(() => {
                    this.handleDisplayValue();
                });
                this.focusInput();
            },

            /**
             * @desc 选择舌色 舌形
             * <AUTHOR>
             * @date 2019/08/08 19:46:52
             */
            selectTongue(val) {
                this.pulses = [];
                this.moss = [];

                if (this.tongues.length === 0) {
                    if (this.currentValue) {
                        this.currentValue += `，舌${val}`;
                    } else {
                        this.currentValue += `舌${val}`;
                    }
                } else if (this.tongues.length === 1) {
                    this.currentValue += `而${val}`;
                }

                this.tongues.push(val);

                if (this.tongues.length === 2) {
                    this.tongues = [];
                }
                this.$nextTick(() => {
                    this.handleDisplayValue();
                });
                this.focusInput();
            },

            /**
             * @desc 选择苔色 苔质
             * <AUTHOR>
             * @date 2019/08/08 19:47:53
             */
            selectMoss(val) {
                this.pulses = [];
                this.tongues = [];

                if (this.moss.length === 0) {
                    if (this.currentValue) {
                        this.currentValue += `，苔${val}`;
                    } else {
                        this.currentValue += `苔${val}`;
                    }
                } else if (this.moss.length === 1) {
                    this.currentValue += `而${val}`;
                }

                this.moss.push(val);

                if (this.moss.length === 2) {
                    this.moss = [];
                }
                this.$nextTick(() => {
                    this.handleDisplayValue();
                });
                this.focusInput();
            },

            /**
             * @desc 选择脉象
             * <AUTHOR>
             * @date 2019/08/08 19:50:57
             */
            selectPulse(val, singleSelect = false) {
                this.tongues = [];
                this.moss = [];

                if (singleSelect) {
                    if (this.currentValue) {
                        this.currentValue += `，${val}`;
                    } else {
                        this.currentValue += `${val}`;
                    }
                } else {
                    if (this.pulses.length === 0) {
                        if (this.currentValue) {
                            this.currentValue += `，脉${val}`;
                        } else {
                            this.currentValue += `脉${val}`;
                        }
                    } else {
                        this.currentValue += `${val}`;
                    }

                    this.pulses.push(val);

                    if (this.pulses.length === 3) {
                        this.pulses = [];
                    }
                }
                this.$nextTick(() => {
                    this.handleDisplayValue();
                });
                this.focusInput();
            },

            selectSuggestionExamination(symptomName) {
                this.pulses = [];
                this.tongues = [];
                this.moss = [];

                this.selectExaminations.push(symptomName);
                if (this.currentValue) {
                    this.currentValue += `，${symptomName}`;
                } else {
                    this.currentValue += `${symptomName}`;
                }
                this.$nextTick(() => {
                    this.handleDisplayValue();
                });
                this.focusInput();
            },

            handleDisplayValue() {
                if (!this.currentValue) {
                    this.displayValue = '';
                    return;
                }
                this.displayValue = this.currentValue.replace(/血压\s*(\d+)\s*\/\s*(\d+)\s*mmHg/g, (match, systolic, diastolic) => {
                    if (Number(systolic) < Number(diastolic) && !this.showSuggestions && !this.disabled) {
                        return `血压 <a data-tipsy="数据异常，收缩压不能小于舒张压（收缩压/舒张压）" class="abc-tipsy--n shortage-tips"><i style="color: #ff9933;font-style: normal;">${systolic} / ${diastolic}</i></a> mmHg`;
                    }
                    return match;
                });
                this.$nextTick(() => {
                    const {
                        forceUpdate,
                    } = this.$refs['ref-target'] || {};
                    if (typeof forceUpdate === 'function' && this.showSuggestions) {
                        forceUpdate();
                    }
                });
            },
            focusInput() {
                this._timer = setTimeout(() => {
                    const { $el } = this.$refs['ref-target'] || {};
                    if ($el) {
                        $el.focus();
                        keepLastIndex($el);
                    }
                }, 250);
            },

            formatPanelListItem(item) {
                return {
                    label: item,
                    value: item,
                    dataCy: `abc-mr-${this.label}-${item}`,
                };
            },
        },
    };
</script>

<style lang="scss">
    @import '~styles/abc-common.scss';

    .physical-examination-wrapper {
        .abc-edit-div {
            .shortage-tips {
                &.abc-tipsy--n::after {
                    width: 204px;
                    padding: 10px 12px;
                    text-align: center;
                    white-space: normal;
                    background-color: #000000;
                }
            }
        }

        .medical-record-suggestions-wrapper {
            .close-suggestions {
                .ai-footer-tips {
                    position: absolute;
                    left: 16px;
                    color: var(--abc-color-T3);

                    .iconfont {
                        margin-right: 0;
                    }
                }
            }
        }
    }
</style>
