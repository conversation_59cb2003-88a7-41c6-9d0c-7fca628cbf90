<template>
    <div v-abc-click-outside="outside" class="histories-wrapper">
        <abc-edit-div
            ref="ref-target"
            v-model="currentValue"
            :class="{ 'is-focus': showSuggestions }"
            :disabled="disabled"
            :maxlength="1000"
            data-cy="abc-mr-流行病史"
            @click="handleClick"
            @blur="handleBlur"
            @tab="handleTab"
            @keydown.down.prevent="handleDown"
            @keydown.up.prevent="handleUp"
        >
        </abc-edit-div>

        <abc-text
            v-if="showSuggestions"
            ref="popper-target"
            class="medical-record-suggestions-wrapper no-shadow epidemiological-history"
            :class="{ fixed: fixed }"
            :style="suggestionsStyle"
            data-cy="abc-mr-popover-流行病史"
        >
            <biz-quick-options-panel
                close-data-cy="abc-mr-流行病史-close"
                @close="showSuggestions = false"
            >
                <abc-flex justify="space-between" align="center" class="patient-selector-wrap">
                    <abc-flex :gap="24" align="center">
                        <abc-flex :gap="4" align="center">
                            <abc-text theme="gray">
                                流调病症：
                            </abc-text>
                            <abc-select
                                v-model="currentObj.diseaseType"
                                :width="90"
                                :inner-width="100"
                                class="epidemiological-history-select"
                                reference-mode="text"
                                data-cy="abc-mr-流行病史-疾病类型"
                                @change="changeDiseaseType"
                            >
                                <abc-option
                                    data-cy="abc-mr-流行病史-新冠"
                                    label="新冠"
                                    :value="DiseaseTypeEnum.COVID_19"
                                ></abc-option>
                                <abc-option
                                    data-cy="abc-mr-流行病史-基孔肯亚热"
                                    label="基孔肯亚热"
                                    :value="DiseaseTypeEnum.CHIKUNGUNYA_FEVER"
                                ></abc-option>
                            </abc-select>
                        </abc-flex>
                        <abc-flex :gap="4" align="center">
                            <abc-text theme="gray">
                                流调对象：
                            </abc-text>
                            <abc-flex :gap="4" align="center">
                                <abc-checkbox
                                    v-model="currentObj.patientChecked"
                                    @change="changeCheckbox"
                                >
                                    患者
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="currentObj.attendantChecked"
                                    style="margin-left: 16px;"
                                    @change="changeCheckbox"
                                >
                                    陪同者
                                </abc-checkbox>
                            </abc-flex>
                        </abc-flex>
                        <abc-flex :gap="4" align="center">
                            <abc-text theme="gray">
                                流调时间：
                            </abc-text>
                            <abc-select
                                :width="60"
                                :value="curWarnDays"
                                :show-value="`${curWarnDays}天`"
                                reference-mode="text"
                                class="epidemiological-history-select"
                                data-cy="abc-mr-流行病史-流调时间"
                                @change="handleChangeWarnDay"
                            >
                                <abc-option data-cy="abc-mr-流行病史-流调时间-7天" label="7天" :value="7"></abc-option>
                                <abc-option data-cy="abc-mr-流行病史-流调时间-14天" label="14天" :value="14"></abc-option>
                                <abc-option data-cy="abc-mr-流行病史-流调时间-21天" label="21天" :value="21"></abc-option>
                                <abc-option data-cy="abc-mr-流行病史-流调时间-28天" label="28天" :value="28"></abc-option>
                            </abc-select>
                        </abc-flex>
                    </abc-flex>
                    <abc-button
                        type="blank"
                        size="small"
                        @click="selectAllNo"
                    >
                        确认全否
                    </abc-button>
                </abc-flex>

                <div class="epidemiological-history-list">
                    <ul>
                        <li v-for="(sym, index) in symptomList" :key="sym.label">
                            <div class="content">
                                {{ sym.label }}
                            </div>
                            <div class="radio-wrapper">
                                <div
                                    v-if="!sym.hasPopover"
                                    class="radio-item"
                                    :class="{ 'is-checked': getCheckedValue(sym.label) === '是' }"
                                    :data-cy="`abc-mr-流行病史-item${index}-是`"
                                    @click="selectSymptom(sym.label, '是')"
                                >
                                    是
                                </div>
                                <abc-popover
                                    v-else
                                    :key="sym.label"
                                    class="popver-wrap"
                                    trigger="click"
                                    placement="bottom-start"
                                    theme="white"
                                    :value="getCheckedValue(sym.label) !== '是' && popoverValue"
                                    :open-delay="200"
                                    :visible-arrow="false"
                                    :popper-style="{ padding: 0 }"
                                    :popper-options="{ label: sym.label }"
                                >
                                    <div
                                        slot="reference"
                                        class="radio-item"
                                        :class="{ 'is-checked': getCheckedValue(sym.label) === '是' }"
                                        @click="selectSymptom(sym.label, '是')"
                                    >
                                        是
                                    </div>
                                    <div
                                        v-abc-click-outside="() => {popoverValue = false;}"
                                        class="epidemiological-popover-wrapper"
                                        @mousedown.stop=""
                                    >
                                        <ul v-for="(line, index) in sym.options" :key="index">
                                            <abc-flex justify="space-between" wrap="wrap" :gap="4">
                                                <abc-tag-v2
                                                    v-for="item in line"
                                                    :key="item"
                                                    shape="round"
                                                    size="mini"
                                                    theme="success"
                                                    variant="outline"
                                                    selectable
                                                    :selected="isActive(sym.label, item)"
                                                    @click="selectSymItem(sym.label, item)"
                                                >
                                                    {{ item }}
                                                </abc-tag-v2>
                                            </abc-flex>
                                        </ul>
                                    </div>
                                </abc-popover>

                                <div
                                    class="radio-item"
                                    :class="{ 'is-checked': getCheckedValue(sym.label) === '否' }"
                                    :data-cy="`abc-mr-流行病史-item${index}-否`"
                                    @click="selectSymptom(sym.label, '否')"
                                >
                                    否
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </biz-quick-options-panel>
        </abc-text>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import common from 'components/common/form';
    import popper from './popper';
    import { isJSON } from 'utils/index';
    import {
        getEpidemiologicalHistoryStr, getEpidemiologicalHistoryObj,
    } from './utils.js';
    import { BizQuickOptionsPanel } from '@/components-composite/biz-quick-options-panel/index';

    const DiseaseTypeEnum = Object.freeze({
        COVID_19: 0, // 新冠
        CHIKUNGUNYA_FEVER: 1, // 基孔肯雅热
    });

    export default {
        name: 'EpidemiologicalHistory',
        components: {
            BizQuickOptionsPanel,
        },
        mixins: [common, popper],
        props: {
            value: String,
            disabled: Boolean,
            warnDays: {
                type: Number,
                default: 14,
            },
        },

        data() {
            return {
                DiseaseTypeEnum,
                showSuggestions: false,

                lock: false,
                currentValue: '',

                currentObj: {
                    patientChecked: true,
                    attendantChecked: false,
                    intervalDays: 14,
                    symptomList: [],
                    diseaseType: DiseaseTypeEnum.COVID_19,
                },
                offsetHeight: '44px',

                curWarnDays: this.warnDays,
                popoverValue: false,
            };
        },
        computed: {
            suggestionsStyle() {
                return {
                    top: this.fixed ? '0' : this.offsetHeight,
                };
            },
            symptomList() {
                if (this.currentObj.diseaseType === DiseaseTypeEnum.CHIKUNGUNYA_FEVER) {
                    return [
                        {
                            label: `${this.curWarnDays}天内是否到过基孔肯雅热/登革热疫区（如佛山、云南西双版纳、东南亚/南亚/非洲/美洲热带地区等）`,
                            hasPopover: false,
                        },
                        {
                            label: `${this.curWarnDays}天内是否被蚊虫叮咬（尤其白昼时段伊蚊叮咬史）`,
                            hasPopover: false,
                        },
                        {
                            label: `${this.curWarnDays}天内是否出现发热（≥37.3°C）伴关节痛（小关节对称性肿痛）与皮疹（充血性皮疹与出血点）`,
                            hasPopover: false,
                        },
                        {
                            label: `${this.curWarnDays}天内是否出现头痛/眼眶痛/肌肉痛/乏力/恶心等登革热交叉症`,
                            hasPopover: false,
                        },
                        {
                            label: `${this.curWarnDays}天内是否未使用驱蚊产品（避蚊胺≥20%浓度）`,
                            hasPopover: false,
                        },
                        {
                            label: '居住/工作场所周围是否有基孔肯雅热/登革热病例',
                            hasPopover: false,
                        },
                    ];
                }
                return [
                    {
                        label: `${this.curWarnDays}天内是否有发热、干咳、乏力、鼻塞、流涕、咽痛、肌痛、腹泻、结膜炎、嗅觉和味觉减退等症状（有任一症状即选择“是”）`,
                        hasPopover: true,
                        options: [
                            ['发热','干咳','乏力'],
                            ['鼻塞','流涕','咽痛'],
                            ['肌痛','腹泻','结膜炎'],
                            ['嗅觉和味觉减退'],
                        ],
                    },
                    {
                        label: `${this.curWarnDays}天内是否有高/中风险地区，境外或其他有病例报告社区的旅行史或居住史`,
                        hasPopover: true,
                        options: [
                            ['国内病例社区','境外'],
                        ],
                    },
                    {
                        label: `${this.curWarnDays}天内是否接触过来自高/中风险地区，或来自有病例报告社区的疑似症状患者`,
                        hasPopover: false,
                    },
                    {
                        label: `${this.curWarnDays}天内是否有与新冠病毒感染者（核酸检测阳性）的接触史`,
                        hasPopover: true,
                        options: [
                            ['国内','境外'],
                        ],
                    },
                    {
                        label: `${this.curWarnDays}天内您生活或工作的地方是否存在聚集性发病（2例或以上）`,
                        hasPopover: false,
                    },
                    {
                        label: '是否从事高风险医务工作、呼吸道标本采集、国际运输、登临外籍船舶、移民及海关、进口冷链食品、口岸货物进口、隔离医学观察、接驳转运、快捷通道等岗位？（有任一项既选“是”）',
                        hasPopover: true,
                        options: [
                            ['高风险医务工作','国际运输'],
                            ['呼吸道标本采集','登临外籍船舶'],
                            ['移民及海关','进口冷链食品'],
                            ['口岸货物进口','隔离医学观察'],
                            ['接驳转运','快捷通道'],
                        ],
                    },
                ];
            },
        },
        watch: {
            value: {
                handler(val) {
                    if (isJSON(val)) {
                        const valObj = JSON.parse(val);
                        if (Array.isArray(valObj)) {
                            this.currentObj = {
                                patientChecked: true,
                                attendantChecked: false,
                                diseaseType: DiseaseTypeEnum.COVID_19,
                                symptomList: valObj,
                            };
                        } else {
                            this.currentObj = valObj;
                        }
                        this.trans2Str();
                    } else {
                        this.currentObj.patientChecked = true;
                        this.currentObj.attendantChecked = false;
                        this.trans2Json(val);
                        this.trans2Str();
                    }
                },
                immediate: true,
            },
            currentValue: {
                handler() {
                    this.updateStyle();
                },
            },
        },

        mounted() {
            if (this.$children[0]) {
                this.offsetHeight = `${this.$children[0].$refs.abcinput.offsetHeight + 4}px`;
            }
        },

        methods: {
            handleChangeWarnDay(val) {
                const oldVal = this.curWarnDays;
                this.curWarnDays = val;
                const {
                    symptomList,
                } = this.currentObj;
                symptomList.forEach((it) => {
                    it.label = it.label.replaceAll(`${oldVal}天内`, `${this.curWarnDays}天内`);
                });
                this.trans2Str();
                this.emitDataHandler();
            },

            getCheckedValue(label) {
                const {
                    symptomList,
                } = this.currentObj;
                const _item = symptomList.find((item) => item.label === label);
                const value = _item && _item.value || '';
                if (typeof value === 'number') {
                    return value === 1 ? '是' : '否';
                }
                return value;

            },

            getSuspiciousLabel() {
                const {
                    patientChecked,
                    attendantChecked,
                } = this.currentObj;
                let str = '';
                if (patientChecked && attendantChecked) {
                    str = '患者（及陪同者）';
                } else if (attendantChecked) {
                    str = '陪同者';
                }
                return str;
            },

            changeCheckbox() {
                const {
                    symptomList,
                } = this.currentObj;
                const _obj = symptomList.find((it) => it.isSuspicious);
                if (_obj) {
                    _obj.label = this.getSuspiciousLabel();
                    this.trans2Str();
                }
                this.emitDataHandler();
            },

            trans2Json(str) {
                const {
                    symptomList, suspiciousList,
                } = getEpidemiologicalHistoryObj(str);
                const { symptomList: orinigSymptomList } = this.currentObj;
                symptomList.forEach((item) => {
                    orinigSymptomList.forEach(((x) => {
                        if (item.label === x.label) {
                            if (x.selectedOptions && x.selectedOptions.length > 0) {
                                item.selectedOptions = x.selectedOptions;
                                item.value = '是';
                            }
                            item.isHealthCode = x.isHealthCode;
                            item.isTripCode = x.isTripCode;
                        }
                    }));
                });
                Object.assign(this.currentObj, {
                    symptomList,
                    suspiciousList,
                    intervalDays: this.curWarnDays,
                });
            },

            trans2Str() {
                this.currentValue = getEpidemiologicalHistoryStr(this.currentObj);

                const matchObj = this.currentValue.match(/(\d+)天内/);
                if (matchObj) {
                    this.curWarnDays = +matchObj[1] || this.warnDays;
                }

                this.$nextTick(() => {
                    this.updateStyle();
                });
            },
            handleClick() {
                if (this.disabled) return false;
                this.updateStyle();
                this.showSuggestions = true;
            },
            handleTab() {
                this.showSuggestions = false;
            },

            // click outside 回调方法
            outside(e) {
                const isTarget = e.path.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className.includes('option-item-wrapper');
                });
                if (!isTarget && this.showSuggestions) {
                    this.showSuggestions = false;
                }
            },

            updateStyle() {
                if (this.$children[0]) {
                    this.offsetHeight = `${this.$children[0].$refs.abcinput.offsetHeight + 4}px`;
                }
            },

            selectSymptom(symptom, value) {
                this.popoverValue = true;
                this.currentObj.suspiciousList = [];

                const {
                    symptomList,
                } = this.currentObj;

                // 补充主语
                const _exist = symptomList.find((it) => it.isSuspicious);
                if (!_exist) {
                    symptomList.push({
                        label: this.getSuspiciousLabel(),
                        isSuspicious: true,
                    });
                }

                const _item = symptomList.find((item) => {
                    return item.label === symptom;
                });
                if (_item) {
                    if (_item.value === value) {
                        symptomList.splice(symptomList.findIndex((it) => it.label === _item.label), 1);
                    } else {
                        _item.value = value;
                    }
                    if (_item.selectedOptions) {
                        _item.selectedOptions = [];
                    }
                } else {
                    symptomList.push({
                        label: symptom,
                        value,
                    });
                }

                this.trans2Str();
                this.updateStyle();
                this.emitDataHandler();
            },

            selectSymItem(label, item) {
                const { symptomList } = this.currentObj;
                const _item = symptomList.find((item) => {
                    return item.label === label;
                });
                if (_item.selectedOptions) {
                    if (_item.selectedOptions.includes(item)) {
                        const index = _item.selectedOptions.findIndex((x) => x === item);
                        _item.selectedOptions.splice(index, 1);
                    } else {
                        _item.selectedOptions.push(item);
                    }
                } else {
                    this.$set(_item, 'selectedOptions', [item]);
                }
                this.trans2Str();
                this.updateStyle();
                this.emitDataHandler();
            },

            isActive(label, item) {
                const { symptomList } = this.currentObj;
                const _item = symptomList.find((item) => item.label === label);
                const { selectedOptions = [] } = _item || {};
                return selectedOptions.includes(item);
            },

            selectAllNo() {
                this.currentObj.suspiciousList = [];
                const isExist = this.currentObj.symptomList.find((it) => it.isSuspicious);
                if (!isExist) {
                    this.currentObj.symptomList.push({
                        label: this.getSuspiciousLabel(),
                        isSuspicious: true,
                    });
                }

                this.symptomList.forEach(({ label }) => {
                    const _exist = this.currentObj.symptomList.find((it) => it.label === label);
                    if (_exist) {
                        _exist.selectedOptions = [];
                        _exist.value = '否';
                    } else {
                        this.currentObj.symptomList.push({
                            label,
                            value: '否',
                        });
                    }
                });
                this.trans2Str();
                this.updateStyle();
                this.emitDataHandler();

                this.showSuggestions = false;
            },

            handleBlur() {
                this.emitDataHandler();
            },

            emitDataHandler() {
                let _value = '';
                if (this.currentValue) {
                    this.trans2Json(this.currentValue);
                    _value = JSON.stringify(this.currentObj);
                } else {
                    _value = this.currentValue;
                }
                this.$emit('input', _value);
                this.formItem && this.formItem.$emit('formFieldInput', _value);
            },

            handleDown() {
                this.showSuggestions = false;
            },
            handleUp() {
                this.showSuggestions = false;
            },

            changeDiseaseType(val, index, old) {
                if (val === old) return;
                this.popoverValue = false;
                this.currentObj.symptomList = [];
                this.trans2Str();
                this.emitDataHandler();
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .medical-record-suggestions-wrapper.epidemiological-history {
        .patient-selector-wrap {
            height: 50px;
            padding: 0 8px;
            border-bottom: 1px dashed var(--abc-color-P6);

            .epidemiological-history-select {
                .abc-input__inner {
                    font-size: 0 !important;
                    background: transparent !important;
                }
            }
        }

        .epidemiological-history-list {
            padding: 10px 0;

            li {
                display: flex;
                padding: 0 8px;
                margin-bottom: 10px;
                line-height: 20px;
                color: var(--abc-color-T2);

                &:last-child {
                    justify-content: flex-end;
                    width: 100%;
                    margin-right: 24px;
                    margin-bottom: 0;
                }

                .content {
                    margin-right: 22px;
                }
            }

            .radio-wrapper {
                display: flex;
                margin-left: auto;

                .radio-item {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 40px;
                    height: 24px;
                    font-size: 12px;
                    color: $T1;
                    cursor: pointer;
                    background: $S2;
                    border: 1px solid #c1c9d0;
                    border-radius: var(--abc-border-radius-small);

                    &:hover {
                        background: var(--abc-color-cp-grey4);
                    }

                    &.is-checked {
                        color: var(--abc-color-theme1);
                        background: var(--abc-color-B7);
                        border: 1px solid var(--abc-color-theme2);
                    }
                }

                .radio-item + .radio-item {
                    margin-left: 4px;
                }

                .popver-wrap + .radio-item {
                    margin-left: 4px;
                }
            }

            &.is-suspicious-type {
                ul {
                    display: flex;
                    flex-wrap: wrap;
                }

                .sym-item {
                    width: auto;
                    min-width: 44px;
                    color: $T2;
                    text-align: right;
                }

                li {
                    flex: 1;
                    width: 33%;
                    padding: 0;
                    margin-bottom: 16px;

                    &:last-child {
                        justify-content: flex-end;
                        width: 100%;
                        margin-right: 24px;
                        margin-bottom: 0;
                    }
                }

                .radio-wrapper {
                    margin-left: 12px;
                }
            }
        }
    }

    .epidemiological-popover-wrapper {
        z-index: 2;
        box-sizing: border-box;
        padding: 12px;

        ul {
            display: flex;

            & + ul {
                margin-top: 6px;
            }

            li {
                padding: 2px 6px;
                color: $G2;
                cursor: pointer;
                border: 1px solid #e6eaed;
                border-radius: 10px;

                & + li {
                    margin-left: 6px;
                }

                &:hover {
                    background: $P4;
                }

                &.active {
                    color: white;
                    background: $G2;
                }
            }
        }
    }
</style>

