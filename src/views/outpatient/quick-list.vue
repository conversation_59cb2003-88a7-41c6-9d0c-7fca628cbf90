<template>
    <div class="outpatient-quick-list-wrapper">
        <biz-quick-list
            :tools="toolList"
            :is-last="isLast"
            :loading="loading"
            :quick-footer-tabs="quickFooterTabsOption"
            :default-tool-tab="defaultToolTab"
            :show-empty="curQuickList.length === 0"
            enable-virtual
            :quick-list-data="curQuickList"
            :quick-list-total="qlTotal"
            data-cy="outpatient-quick-list"
            :scroll-load-func="fetchOutpatientQuickList"
            @change-footer-tab="changeFooterTab"
        >
            <template #search>
                <abc-search
                    v-model.trim="scrollParamsKey"
                    placeholder="搜索门诊单"
                    focus-placeholder="姓名 / 手机尾号 / 诊号 / 诊断"
                    style="width: 100%;"
                    data-cy="outpatient-quick-list-search"
                    @search="handlerInputKey"
                    @clear="clearKey"
                >
                </abc-search>
                <abc-check-access v-if="quickDiagnosis">
                    <abc-button
                        type="success"
                        title="快速接诊"
                        class="open-order-btn"
                        icon="a-plus13px"
                        @click="() => addNew()"
                    >
                        接诊
                    </abc-button>
                </abc-check-access>
            </template>

            <template #operate>
                <div v-if="totalCount > -1" class="search-tips">
                    <div>
                        搜索到<span style=" margin: 0 4px; font-size: 12px; color: #000000;">{{ totalCount }}</span> 条信息
                    </div>
                </div>
                <template v-else>
                    <abc-tabs-v2
                        v-model="selectedTab"
                        size="middle"
                        :option="tabsOption"
                        :border-style="{ borderBottom: 'none' }"
                        :before-change="handleBeforeChangeTab"
                        @change="changeTab"
                    ></abc-tabs-v2>
                    <abc-date-picker
                        v-if="!scrollParamsKey"
                        v-model="datePickerValue"
                        class="date-picker"
                        :width="56"
                        type="date"
                        :picker-options="{
                            disabledDate(time) {
                                return time.getTime() > Date.now();
                            },
                        }"
                        custom-class="outpatient-date-picker"
                        value-format="YYYY-MM-DD"
                        :shortcut-name.sync="datePickerStr"
                        @change="changeDate"
                    >
                        <abc-button
                            variant="text"
                            theme="default"
                            size="small"
                            icon="s-select-fill"
                            icon-position="right"
                        >
                            {{ datePickerStr }}
                        </abc-button>
                    </abc-date-picker>
                </template>
            </template>

            <template #quickListTop>
                <div
                    v-if="isTreatOnline && !scrollParamsKey && isOnlineDoctor"
                    v-abc-click-outside="
                        () => {
                            showChangeTreatOnline = false;
                        }
                    "
                    class="treat-online-status"
                    :class="{ online: outpatient.treatOnline }"
                    @click="showChangeTreatOnline = !showChangeTreatOnline"
                >
                    {{ outpatient.treatOnline ? '在线' : '离线' }}

                    <div class="treat-online-setting-btn" @click.stop="openTreatOnlineSettingDialog">
                        <abc-icon icon="n-settings-line" size="14"></abc-icon>
                    </div>

                    <div v-if="showChangeTreatOnline" class="change-treat-online-dialog">
                        <div @click.stop="changeTreatOnlineStatus(true)">
                            上线
                            <i v-if="outpatient.treatOnline" class="iconfont cis-icon-positive_"></i>
                        </div>
                        <div @click.stop="changeTreatOnlineStatus(false)">
                            下线
                            <i v-if="!outpatient.treatOnline" class="iconfont cis-icon-positive_"></i>
                        </div>
                    </div>
                </div>
            </template>

            <template #quickListItem="{ item }">
                <biz-quick-list-item
                    v-if="item.todayDraft"
                    :key="item.draftId || `draft${index}`"
                    :quick-item="item"
                    :is-active="selectedItem && selectedItem.draftId === item.draftId"
                    content-class="green"
                    status-class="green"
                    @select="select(item)"
                >
                    <template v-if="item.patient" #patient-name>
                        {{ item.patient.name || '新患者' }}
                    </template>

                    <template #status>
                        草稿
                    </template>
                </biz-quick-list-item>

                <biz-quick-list-item
                    v-else-if="item.isOnline"
                    :key="item.id || `item${index}`"
                    :quick-item="item"
                    :is-active="selectedItem && item.id === selectedItem.id"
                    :is-done="doneStatus(item)"
                    :status-class="{ green: item.statusName === '问诊中' }"
                    :patient-head-img-url="item.consultation.patientHeadImgUrl"
                    @select="select(item)"
                >
                    <template v-if="item.consultation.patientName" #patient-name>
                        {{ item.consultation.patientName }}
                    </template>
                    <template v-if="item.consultation.unReadCount" #patient-name-append>
                        <div class="red-dot"></div>
                    </template>
                    <template #content>
                        <span v-if="isClinicAdmin || hasDoctorHelperModule" class="doctor-name">{{
                            item.doctorName
                        }}
                        </span>

                        <span class="text">{{ transTime(item) }}</span>
                    </template>
                    <template #status>
                        {{ item.consultation.statusName }}
                    </template>
                </biz-quick-list-item>

                <biz-quick-list-item
                    v-else
                    :key="`${item.id}_${scrollParams.offset}` || `item${ index}`"
                    :quick-item="item"
                    :draft-list="draftOutpatients"
                    :is-active="selectedItem && item.id === selectedItem.id"
                    :is-done="doneStatus(item)"
                    :content-class="{
                        calling: item.callingPatientItem && item.callingPatientItem.id === callingId,
                        'num-tag': handleTargetStatus(item).isPass || handleTargetStatus(item).isLate,
                    }"
                    :status-class="{ green: item.isDraft }"
                    @select="select(item)"
                >
                    <template #patient-name-append>
                        <abc-flex :gap="6" inline align="center">
                            <template v-if="item.registration && item.registration.isReserved">
                                <abc-icon icon="s-reserve-tag-color"></abc-icon>
                            </template>
                            <template v-if="item.referralFlag">
                                <abc-icon
                                    v-if="item.referralFlag === ReferralFlagEnum.REFER"
                                    icon="s-change-tag-color"
                                ></abc-icon>
                                <abc-icon
                                    v-else-if="item.referralFlag === ReferralFlagEnum.REVISIT_IN_THREE_DAYS"
                                    icon="s-followup-tag-color"
                                ></abc-icon>
                            </template>
                            <template v-if="item.registration && item.registration.registrationCategory === RegistrationCategory?.CONVENIENCE">
                                <abc-icon icon="s-convenience-tag-color"></abc-icon>
                            </template>
                        </abc-flex>
                    </template>
                    <template v-if="!item.isDraft" #content>
                        <horn-images
                            v-if="item.callingPatientItem && item.callingPatientItem.id === callingId"
                        ></horn-images>

                        <template v-else>
                            <abc-icon
                                v-if="!!item.isAirPharmacyOrder || !!item.sourceFromOpenApi"
                                :size="12"
                                icon="pharmacy-2"
                                color="#e0e2eb"
                            ></abc-icon>

                            <span
                                :class="{
                                    'tag-box': true,
                                    'pass-num': handleTargetStatus(item).isPass,
                                    'late-num': handleTargetStatus(item).isLate,
                                }"
                            ></span>

                            <!--管理员&医助需要显示医生姓名-->
                            <span v-if="isClinicAdmin || hasDoctorHelperModule" class="doctor-name">{{
                                item.doctorName
                            }}</span>

                            <span class="text">{{ transTime(item) }}</span>
                        </template>
                    </template>
                    <template #status>
                        {{ patientStatus(item) }}
                    </template>
                </biz-quick-list-item>
            </template>

            <template #customToolsContent="{ tabValue }">
                <outpatient-history
                    v-if="hasOutpatientHistory"
                    v-show="tabValue === 1"
                    :id="selectedPatient ? selectedPatient.id : ''"
                    from-module="outpatient"
                    has-copy
                    has-copy-mr
                    @copy="copyHandler"
                    @history-list-change="onHistoryListChange"
                ></outpatient-history>
            </template>

            <template v-if="visibleCallControl" #customTools>
                <call-number
                    v-model="openCtrlView"
                    placement="right-end"
                    class="entry-item-group"
                    :doctor-id="showAllDoctor ? '' : userInfo?.id"
                    @active-call="(id) => (activeCallId = id)"
                >
                    <template #btn>
                        <abc-check-access>
                            <div
                                class="entry-item"
                                @click="openCtrlView = !openCtrlView"
                            >
                                <abc-icon icon="s-volume-color" :size="20"></abc-icon>
                                <div class="content">
                                    叫号
                                </div>
                                <div class="describe">
                                    下一位 {{ nextCallingText }}
                                    <abc-icon
                                        icon="s-b-right-line-medium"
                                        style="margin-left: 6px;"
                                    ></abc-icon>
                                </div>
                            </div>
                        </abc-check-access>
                    </template>
                </call-number>
            </template>
        </biz-quick-list>

        <outpatient-work-kanban-dialog v-if="showSummeryDialog" v-model="showSummeryDialog"></outpatient-work-kanban-dialog>
        <appointment-kanban-dialog v-if="showBulletinBoard" v-model="showBulletinBoard"></appointment-kanban-dialog>
        <registration-board-dialog v-if="showRegistrationBoard" v-model="showRegistrationBoard"></registration-board-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        mapGetters, mapState,
    } from 'vuex';
    import dayjs from 'dayjs';
    import fecha from '../../utils/fecha';
    import { debounce } from 'utils/lodash';
    import CrmAPI from 'api/crm';
    import SocialAPI from 'src/api/social';
    import AbcSocket from 'views/common/single-socket';
    import ModulePermission from 'views/permission/module-permission';

    import quickListCommon from '@/views/common/quick-list-common.js';
    import OutpatientHistory from '../layout/patient/outpatient-history/patient-outpatient-history';
    import BizQuickListItem from '@/components-composite/biz-quick-list-item/index.js';
    import BizQuickList from '@/components-composite/biz-quick-list/index.js';

    const CallNumber = () => import('@/views/layout/call-number/index.vue');
    import HornImages from '@/views/layout/horn-images.vue';

    import { createPostData } from '@/views/outpatient/constants.js';
    import { ReferralFlagEnum } from '@/common/constants/registration';
    import { parseTime } from '@/utils/index';
    import { getTargetStatus } from 'assets/configure/call.js';
    import BarcodeScanner from 'src/utils/scanner-barcode-detector.js';
    import TreatOnlineSettingDialog from '@/views/outpatient/consulting-online/treat-online-dialog/index.js';
    import MedicineInstructionsDialog from '@/views/layout/medicine-instructions/index.js';
    import Logger from 'utils/logger';

    import SocialHistoryPrescription from '@/views/layout/social-history-prescription/index';
    import SocialSettleListReport from '@/views/layout/social-report/index';
    import OutpatientWorkKanbanDialog from '@/views/outpatient/common/outpatient-work-summary-dialog/index.vue';
    import AppointmentKanbanDialog from '@/views/outpatient/common/appointment-kanban-dialog/index.vue';
    import RegistrationBoardDialog from '@/views/outpatient/common/registration-board-dialog';
    import LocalStorage from 'utils/localStorage-handler.js';
    import AbcAccess from '@/access/utils.js';
    import { RESERVATION_MODE_TYPE } from 'views/settings/registered-reservation/constant';
    import { RegistrationCategory } from '@/views-hospital/registered-fee/constant';
    import AbcSearch from 'components/abc-search/index.vue';

    export default {
        name: 'OutpatientQuickList',
        components: {
            AbcSearch,
            CallNumber,
            HornImages,
            AppointmentKanbanDialog,
            RegistrationBoardDialog,
            OutpatientWorkKanbanDialog,
            OutpatientHistory,
            BizQuickListItem,
            BizQuickList,
        },
        mixins: [ quickListCommon, ModulePermission ],
        data() {
            this._todayStr = '';
            const tabValue = LocalStorage.get('outpatient_ql_tab', true) || 0;
            return {
                ReferralFlagEnum,
                RegistrationCategory,
                showChangeTreatOnline: false,
                loading: false,
                scrollParamsKey: '',
                showWaiting: true,
                cancelDialogVisible: false,
                showDialog: false,
                fecha,
                totalCount: -1,

                openCtrlView: false,
                datePickerValue: this._todayStr,
                selectedTab: 1,

                doctorId: '',
                doctorList: [],
                keyword: '', // 医生筛选关键词
                activeCallId: '', // 主动呼叫id
                width: 92,
                isScanBarcode: false,

                showSummeryDialog: false,
                showBulletinBoard: false,
                showRegistrationBoard: false,

                defaultToolTab: +tabValue,
                patientHistoryCount: 0,
                qlTotal: 0,

                icpcNearExpiryCount: 0, // icpc待上报即将到期的数量
            };
        },
        computed: {
            ...mapState('call', [
                'callingId',
                'nextCalling',
            ]),
            ...mapState('socialPc', [
                'basicInfo',
            ]),
            ...mapGetters([
                'clientWidth',
                'outpatient',
                'currentClinic',
                'userInfo',
                'isOpenCall',
                'draftOutpatientNews',
                'draftOutpatients',
                'outpatientQLCurrentTreatItem', // 当前就诊
                'todayDraftOutpatientNews',
                'registrationsConfig',
                'clinicBasic',
                'isEnableRegUpgrade',
                'isIntranetUser',
                'clinicBasic',
            ]),
            ...mapGetters('outpatientConfig', [
                'outpatientEmployeeConfig',
            ]),
            ...mapGetters('viewDistribute',[
                'featureTreatOnline',
                'viewDistributeConfig',
            ]),
            toolList() {
                const arr = [];
                arr.push({
                    label: '门诊看板',
                    describe: '医生工作汇总',
                    icon: 's-presentatio-color',
                    handler: () => {
                        this.handleClickEntryItem('work');
                    },
                });
                if (this.isShowICPCReport) {
                    let describe = 'ICPC上报医保';
                    if (this.icpcNearExpiryCount > 0) {
                        describe = `有${this.icpcNearExpiryCount}个病历即将到期`;
                    }
                    arr.push(...[
                        {
                            label: 'ICPC上报',
                            describe,
                            customDescribeStyle: {
                                color: this.icpcNearExpiryCount > 0 ? 'var(--abc-color-Y2)' : 'var(--abc-color-T2)',
                            },
                            icon: 's-upload-color',
                            handler: () => {
                                this.handleClickEntryItem('report');
                            },
                        },
                    ]);
                }
                arr.push({
                    label: this.isSupportNewRegistrationBoard ? '预约看板' : '号源看板',
                    describe: '挂号预约情况',
                    icon: 's-calendar-color',
                    handler: () => {
                        const paramStr = this.isSupportNewRegistrationBoard ? 'board' : 'date';
                        this.handleClickEntryItem(paramStr);
                    },
                });
                arr.push({
                    label: '用药助手',
                    describe: '安全合理用药',
                    icon: 's-capsule-color',
                    handler: () => {
                        this.handleClickEntryItem('pill');
                    },
                });
                if (this.isShowHistoryPrescriptionTool) {
                    arr.push(...[
                        {
                            label: '就医查询',
                            describe: '医保历史处方',
                            icon: 's-chs-color',
                            handler: () => {
                                this.handleClickEntryItem('history');
                            },
                        },
                    ]);
                }
                return arr;
            },
            curQuickList() {
                let _arr = [];
                if (this.showWaiting && this.selectedTab === 1) {
                    _arr = _arr.concat(this.todayDraftOutpatientNews);
                }
                return _arr.concat(this.outpatient.quickList).filter((item) => !!item);
            },
            isLast() {
                return this.outpatient.isLast;
            },
            scrollParams() {
                return this.outpatient.scrollParams;
            },
            nextCallingText() {
                const {
                    patientName,
                    orderNo,
                    timeOfDay,
                    isAdditional,
                } = this.nextCalling || {};
                if (!patientName) {
                    return '无';
                }
                let orderNoStr = '';
                if (isAdditional) {
                    orderNoStr = `+${orderNo}`;
                } else {
                    orderNoStr = orderNo > 9 ? orderNo : `0${orderNo}`;
                }
                if (this.isFixOrderMode) {
                    return `${timeOfDay}${orderNoStr}号 ${patientName}`;
                }
                return patientName;
            },

            tabsOption() {
                const {
                    outpatientTotalCount,
                } = this.outpatient.summary || {};
                const _arr = [
                    {
                        label: '门诊',
                        value: 1,
                        noticeNumber: outpatientTotalCount,
                        maxNoticeNumber: 99,
                    },
                ];
                if (this.featureTreatOnline && !this.isIntranetUser) {
                    _arr.push({
                        label: '网诊',
                        value: 2,
                        noticeNumber: this.outpatient.consultations.filter((item) => {
                            return item.consultation && item.consultation.unReadCount;
                        }).length,
                        maxNoticeNumber: 99,
                    });
                }
                return _arr;
            },

            hasOutpatientHistory () {
                return this.clientWidth <= 1096;
            },

            quickFooterTabsOption() {
                const _arr = [];
                if (this.hasOutpatientHistory) {
                    _arr.push({
                        label: '就诊历史',
                        value: 1,
                        statisticsNumber: this.patientHistoryCount,
                    });
                }
                return _arr;
            },

            selectedItem() {
                return this.outpatient.selectedItem || {
                    patient: {},
                };
            },
            selectedPatient() {
                return this.outpatient.selectedPatient;
            },
            datePickerStr() {
                if (!this.datePickerValue) return '今天';
                return parseTime(new Date(this.datePickerValue.replace(/-/g, '/')), 'm-d');
            },

            clinicTreatOnline() {
                return this.outpatient.treatOnline !== null;
            },
            doctorTreatOnline() {
                return this.outpatient.treatOnline;
            },
            isTreatOnline() {
                return this.selectedTab === 2 && this.clinicTreatOnline;
            },
            isOnlineDoctor() {
                return this.selectedTab === 2 && this.outpatient.isOnlineDoctor;
            },

            // 叫号器显示与否
            visibleCallControl() {
                if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.CALLING_NUM)) return false;
                if (!this.isOpenCall) return false;
                if (this.selectedTab === 2) return false;
                // 门店管理员、医生权限、医助  均显示叫号器
                return this.isClinicAdmin || this.canDiagnosis || this.permissionHasAssistant;
            },
            // 展示排班列表，显示全部医生
            showAllDoctor() {
                if (this.isClinicAdmin || this.hasDoctorHelperModule) {
                    return true;
                }
                return false;
            },
            // 管理员、医助可查看多个医生的QL，因此需要展示下拉筛选器
            visibleCheckoutDoctor() {
                return false; // quicklist暂不展示医生筛选
                // if (this.selectedTab !== 2) {
                //     return this.isClinicAdmin || this.hasDoctorHelperModule
                // } else {
                //     return false
                // }
            },
            // 医生下拉菜单列表，支持关键词过滤
            showDoctorList() {
                return this.doctorList.filter((item) => {
                    if (this.keyword) {
                        const keyword = this.keyword.toLocaleUpperCase();
                        const namePyUpper = item.namePy.toLocaleUpperCase();
                        const namePyFirstUpper = item.namePyFirst.toLocaleUpperCase();
                        return (
                            item.name.indexOf(keyword) !== -1 ||
                            item.mobile.indexOf(keyword) !== -1 ||
                            namePyUpper.indexOf(keyword) !== -1 ||
                            namePyFirstUpper.indexOf(keyword) !== -1
                        );
                    }
                    return true;

                });
            },
            isFixOrderMode() {
                // modeType 0: 固定号源模式 1: 灵活时间模式
                return this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            isSupportNewRegistrationBoard() {
                return this.viewDistributeConfig?.Outpatient?.supportNewRegistrationBoard || this.isEnableRegUpgrade;
            },
            // 能否快速接诊
            quickDiagnosis() {
                return this.clinicBasic.outpatient.settings.quickDiagnosis;
            },
            isShowShenyangSocialBusiness() {
                return this.$abcSocialSecurity.isOpenSocial &&
                    this.$abcSocialSecurity.isElectron &&
                    this.$abcSocialSecurity.config.isLiaoningShenyang;
            },
            isShowHistoryPrescriptionTool() {
                return this.isShowShenyangSocialBusiness && this.basicInfo.isOpenHistoryPrescription;
            },
            isShowICPCReport() {
                return this.isShowShenyangSocialBusiness &&
                    this.basicInfo.isOpenUploadOutpatientICPC;
            },
        },
        watch: {
            // 如果路由去index，清空ql
            '$route': function (to) {
                const { routeBasePath } = this.viewDistributeConfig.Outpatient;
                if (to.path === `${routeBasePath}outpatient`) {
                    this.selectFirst();
                }
            },
            hasOutpatientHistory: {
                handler(val) {
                    if (!val) {
                        this.defaultToolTab = 0;
                    }
                },
                immediate: true,
            },
            outpatientQLCurrentTreatItem(newValue, oldValue) {
                if (newValue && (!oldValue || oldValue.id !== newValue.id)) {
                    const { id } = newValue.callingPatientItem || {};
                    if (id === this.activeCallId) {
                        this.select(newValue);
                    }
                }
            },
        },
        mounted() {
            const barcodeScanner = new BarcodeScanner((barCode, event) => {
                const { $el } = this.$refs.searchInput || {};
                /**
                 * @desc target是body || 是搜索框才触发ql栏的扫码搜索
                 * <AUTHOR> Yang
                 * @date 2024-08-15 14:58:05
                */
                if (event.target === $el || event.target === document.body) {
                    this.scrollParamsKey = barCode;
                    this.isScanBarcode = true;
                }
            }, (barcode) => {
                if (barcode.length !== 8) {
                    return false;
                }
                return true;
            });

            barcodeScanner.startDetect();
            this.$on('hook:beforeDestroy', () => {
                barcodeScanner.destoryDetect();
                window.removeEventListener('resize', this._resizeHandler);
            });

        },
        async created() {
            this.selectedTab = +this.$route.query.tab || 1;

            this.loading = true;

            this.initQuickList();

            // 注册防抖search函数
            this._debounceSearch = debounce(async () => {
                this.loading = true;
                this.$store.dispatch('clearQuickList', 'outpatient');
                this.$store.dispatch('setScrollParams', {
                    type: 'outpatient',
                    scrollParams: {
                        keyword: this.scrollParamsKey,
                        offset: 0,
                        beginDate: this.scrollParamsKey ? '' : this.datePickerValue,
                        endDate: this.scrollParamsKey ? '' : this.datePickerValue,
                    },
                });
                this.$store.dispatch('updateOutpatientIsLast', false);
                await this.fetchOutpatientQuickList();
                this.$nextTick(() => {
                    this.selectFirst();
                });
            }, 250, true);

            this.$store.dispatch('fetchOnlineDoctorStatus');

            /**
             * @desc 监听门诊im消息，当有新的im消息过来，需要根据
             * <AUTHOR>
             * @date 2020/02/17 14:15:48
             */
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('im.msg', this.updateRedDot);

            this.$store.dispatch('call/initNextCalling');

            this.$abcEventBus.$on('add-patient-outpatient-draft', this.addNew, this);
        },

        beforeDestroy() {
            this.$store.dispatch('clearQuickList', 'outpatient');
            this.$store.dispatch('setScrollParams', {
                type: 'outpatient',
                scrollParams: {
                    keyword: '',
                    offset: 0,
                },
            });
            this.$store.dispatch('setSelectedItem', {
                type: 'outpatient',
                selectedItem: null,
            });
            this._socket.off('im.msg', this.updateRedDot);
            this.$abcEventBus.$offVmEvent(this._uid);
            this._timer && clearTimeout(this._timer);
        },

        methods: {
            onHistoryListChange({ totalCount = 0 }) {
                this.patientHistoryCount = totalCount;
            },

            changeFooterTab(tabValue) {
                LocalStorage.set('outpatient_ql_tab', tabValue);
            },

            getTargetStatus,
            handleClickEntryItem(type) {
                if (type === 'work') {
                    this.showSummeryDialog = true;
                } else if (type === 'date') {
                    this.showBulletinBoard = true;
                } else if (type === 'pill') {
                    new MedicineInstructionsDialog({
                        store: this.$store,
                        value: true,
                    }).generateDialog({ parent: this });
                } else if (type === 'board') {
                    this.showRegistrationBoard = true;
                } else if (type === 'history') {
                    new SocialHistoryPrescription({
                        store: this.$store,
                        sheetId: this.$route.params.id,
                    }).generateDialog({ parent: this });
                } else if (type === 'report') {
                    new SocialSettleListReport({
                        store: this.$store,
                    }).generateDialog({ parent: this });
                }
            },
            /**
             * desc [处理过号、迟到标记显示（已完诊的，不显示）]
             */
            handleTargetStatus(item) {
                if (item.status === 0) {
                    return getTargetStatus(item.callingPatientItem);
                }
                return {};
            },
            /**
             * desc [打开叫号器]
             */
            handleOpenCaller() {
                if (this.visibleCallControl) {
                    this.openCtrlView = true;
                }
            },
            /**
             * desc [变更医生]
             */
            handleChangeDoctor(data) {
                this.doctorInfo = data;
            },
            /**
             * desc [医生下拉支持搜索]
             */
            fetchSuggestionDoctors(keyword) {
                this.keyword = keyword;
            },
            // 更新红点
            updateRedDot(res) {
                //只有当前连锁下消息可以收到（处理一个医生多个连锁的情况）
                if (this.currentClinic && this.currentClinic.chainId === res.chainId && res.sceneType === 0) {
                    if (this.selectedItem &&
                        this.selectedItem.consultation &&
                        this.selectedItem.consultation.conversationId === res.conversationId) {
                        //当前正在会话（患者消息置顶，但不加红点）
                        const patientId = this.selectedItem.patient.id;
                        if (res.fromUserId === patientId) {
                            this.$store.commit('add_unread_consultations_item_by_id', {
                                conversationId: res.conversationId,
                                addRedDot: false,
                            });
                        }
                    } else {
                        //不在当前会话，置顶（患者消息 => 加上红点）
                        this.$store.commit('add_unread_consultations_item_by_id', {
                            conversationId: res.conversationId,
                            addRedDot: res.fromUserType === 1,
                        });
                    }
                }
            },

            /**
             * desc [拉取医生列表]
             */
            async getDoctorList() {
                try {
                    const { data } = await CrmAPI.fetchDoctorList();
                    this.doctorList = data.list || [];
                } catch (error) {
                    console.log('getDoctorList error', error);
                }
            },

            /**
             * desc [计算当前选中医生的width，左对齐，好看些]
             */
            handleActiveDoctorWidth() {
                const target = this.doctorList.find((item) => item.id === this.doctorId);
                if (target) {
                    const el = document.createElement('span');
                    el.innerHTML = target.name;
                    document.body.appendChild(el);
                    const width = Number(el.offsetWidth);
                    this.width = isNaN(width) ? 92 : (width + 28);
                    el.remove();
                }
            },

            changeTreatOnlineStatus(status) {
                this.showChangeTreatOnline = false;
                this.$store.dispatch('switchTreatOnlineStatus', status);
            },

            /**
             * @desc 打开网诊自定义设置弹窗
             * <AUTHOR>
             * @date 2021-07-16 10:42:53
             */
            openTreatOnlineSettingDialog() {
                new TreatOnlineSettingDialog({
                    autoOnlineSetting: this.outpatient.autoOnlineSetting,
                    confirmFunc: async (data, callback) => {
                        await this.$store.dispatch('updateTreatOnlineCustomConfig', data);
                        callback();
                    },
                }).generateDialog({ parent: this });
            },


            /**
             * @desc 根据条件初始化quicklist
             * <AUTHOR>
             * @date 2020/02/16 16:48:49
             */
            async initQuickList() {
                this.loading = true;
                this.$store.dispatch('updateOutpatientIsLast', false);
                // 1.清空quicklist
                this.$store.dispatch('clearQuickList', 'outpatient');
                // 2.设置 查询 quicklist 参数
                this.$store.dispatch('setScrollParams', {
                    type: 'outpatient',
                    scrollParams: {
                        keyword: this.scrollParamsKey,
                        beginDate: this.scrollParamsKey ? '' : this.datePickerValue,
                        endDate: this.scrollParamsKey ? '' : this.datePickerValue,
                        offset: 0,
                        tab: this.selectedTab,
                        loading: true,
                        doctorId: this.doctorId,
                    },
                });
                // 3.拉取quicklist
                await this.fetchOutpatientQuickList();
                // 4.默认选择
                this.defaultSelectItem(this.outpatient.quickList);

                this.loading = false;

                this.$store.dispatch('setScrollParams', {
                    type: 'outpatient',
                    scrollParams: {
                        loading: false,
                    },
                });

                this._timer = setTimeout(() => {
                    // 初始化拉取一次网诊列表用于红点展示
                    if (this.selectedTab === 1 && this.clinicTreatOnline) {
                        this.$store.dispatch('initOutpatientConsultations');
                    }
                    this.fetchIcpcInfo();
                }, 0);
            },


            /**
             * @desc 切换tab
             * <AUTHOR>
             * @date 2019/11/18 15:35:19
             */
            async changeTab(index, item) {
                item.value === 2 && this.$store.dispatch('fetchOnlineDoctorStatus');
                this.selectedTab = item.value;
                this.scrollParamsKey = '';
                // if(this.selectedTab === 2) {
                //    this._socket.off('im.msg', this.updateRedDot)
                // } else {
                //    this._socket.on('im.msg', this.updateRedDot)
                // }

                // 当选择网诊tab，但是并没有开网诊权限的时候不拉取网诊列表,并清空quicklist
                if (this.selectedTab === 2 && !this.clinicTreatOnline) {
                    this.$store.dispatch('clearQuickList', 'outpatient');
                    // this.$router.replace( '/outpatient' );
                    return false;
                }
                this.initQuickList();
            },

            /**
             * @desc 切换quicklist日期,重新拉取数据
             * <AUTHOR>
             * @date 2019/11/18 17:32:34
             */
            changeDate(date) {
                this.datePickerValue = date;
                this.initQuickList();
            },

            /**
             * @desc 格式化时间
             * <AUTHOR>
             * @date 2019/03/26 15:56:20
             */
            transTime(item) {
                const today = fecha.format(Date.now(), 'YYYY-MM-DD');
                // 创建时间
                const created = item.created && fecha.format(item.created, 'YYYY-MM-DD') || '';
                // 有挂号信息
                let { reserveDate } = item.registration || {};
                const {
                    orderNoStr, doctorId,reserveStart,
                } = item.registration || {};
                reserveDate = reserveDate && fecha.format(reserveDate, 'YYYY-MM-DD') || '';

                // 当天 显示号数，以前显示日期
                if (reserveDate === today || created === today) {
                    if (this.isFixOrderMode) {
                        return this.toReverseInfo(doctorId, orderNoStr);
                    }
                    // 灵活时间预约模式返回开始 （eric说的）
                    return reserveStart;
                }
                return this.formatCreated(item.created);

            },

            toReverseInfo (doctorId, orderNoStr) {
                // 不指定医生 不显示时间
                if (!doctorId) return '';
                return orderNoStr || '';
            },

            doneStatus(item) {
                if (!item) return false;
                if (item.isOnline) {
                    return item.consultation && item.consultation.status > 30;
                }
                if (item.status === 1 && item.subStatus === 1) {
                    return false;
                }
                return item.status > 0;

            },

            patientStatus(item) {
                if (item.isDraft) {
                    return '草稿';
                }
                return item.statusName;
            },

            /** ------------------------------------------------------------------------------------------
             * 获取挂号quicklist （滚动加载）
             */
            async fetchOutpatientQuickList() {
                if (this.isLast) return false;
                const data = await this.$store.dispatch('fetchOutpatientQuickList');
                this.qlTotal = data.totalCount;
                this.totalCount = this.scrollParamsKey ? data.totalCount : -1;
                this.showWaiting = !this.scrollParamsKey;
                this.loading = false;
            },

            /**
             * @desc 默认选中第一个元素
             * <AUTHOR>
             * @date 2019/02/27 17:58:03
             */
            selectFirst() {
                let selectedItem;

                if (this.todayDraftOutpatientNews.length && this.showWaiting && this.selectedTab === 1) {
                    selectedItem = this.todayDraftOutpatientNews[ 0 ];
                } else {
                    if (this.outpatient.quickList.length > 0) {
                        selectedItem = this.outpatient.quickList[ 0 ];
                    } else {
                        selectedItem = null;
                    }
                }
                this.select(selectedItem);
            },

            /**
             * @desc quicklist 选择 item
             * <AUTHOR>
             * @date 2019/03/29 11:56:11
             * @params
             * @return
             */
            select(selectedItem) {
                if (selectedItem && selectedItem.status < 1 && this.draftOutpatients) {
                    const draftObj = this.draftOutpatients.find((it) => {
                        return it.draftId === selectedItem.id;
                    });
                    if (draftObj) {
                        Object.assign(selectedItem, {
                            patient: draftObj.patient,
                        });
                    }
                }

                this.$store.dispatch('setSelectedItem', {
                    type: 'outpatient',
                    selectedItem,
                });

                if (this.$route.path.indexOf('outpatient') === -1) {
                    console.log('快速切换head tab 会导致在非当前tab下执行该方法', this.$route.path, 'outpatient');
                    return;
                }

                if (!selectedItem) {
                    const { routeBasePath } = this.viewDistributeConfig.Outpatient;
                    this.$router.push(`${routeBasePath}outpatient`);
                    return false;
                }

                // 快速接诊创建云端草稿，patient为null，补充一个空结构
                if (selectedItem && !selectedItem.patient) {
                    selectedItem.patient = {};
                }

                const { draftId } = selectedItem;
                const { id } = selectedItem;
                if (draftId) {
                    this.replaceRouter(`outpatient/add/${draftId}`);
                } else if (id) {
                    this.replaceRouter(`outpatient/${id}`);
                } else {
                    this.replaceRouter('outpatient');
                }
            },

            replaceRouter(path) {
                const { routeBasePath } = this.viewDistributeConfig.Outpatient;
                this.$router.replace({
                    path: routeBasePath + path,
                    query: {
                        ...this.$route.query,
                        tab: this.selectedTab,
                    },
                });
            },

            /**
             * @desc 清除quicklist 搜素条件
             * <AUTHOR>
             * @date 2019/03/29 11:56:30
             */
            async clearKey() {
                this.scrollParamsKey = '';
                this.loading = true;
                this.$store.dispatch('updateOutpatientIsLast', false);
                await this.initQuickList();
            },

            /**
             * @desc 快速接诊直接创建草稿
             * <AUTHOR>
             * @date 2019/03/27 18:08:55
             */
            async addNew(patient) {
                if (this._isAdding) return;
                // 不在当天，有搜索值，选择网诊tab时点新增，需要跳到初始化
                if (this.datePickerValue !== this._todayStr || this.scrollParamsKey || this.selectedTab === 2) {
                    this.datePickerValue = this._todayStr;
                    this.selectedTab = 1;
                    await this.clearKey();
                }
                this._isAdding = true;
                let data = await this.$store.dispatch('addOutpatientDraft', patient?.id);

                /**
                 * @desc testlogin登录 创建一个本地的，方便调试
                 * <AUTHOR> Yang
                 * @date 2021-02-02 11:53:39
                 */
                if (data === 'testLoginAddDraft') {
                    data = createPostData();
                    data.patient = patient || data.patient;
                    this.$store.dispatch('SetDraft', {
                        key: 'outpatientNews',
                        record: data,
                    });
                }
                if (data) {
                    // 添加完后 选中该挂单
                    data.isDraft = 1;
                    this.select(data);
                }
                // 点击新增的时候判断下如果有搜索条件则需要清空重置
                this.scrollParamsKey = '';
                const $quickList = document.querySelector('.quick-list-small');
                if ($quickList) {
                    $quickList.scrollTop = 0;
                }
                this._isAdding = false;
                if (this.clinicBasic.isEnableAIExperience) {
                    Logger.report({
                        scene: 'outpatientQuickAdd',
                    });
                }
            },

            copyHandler(mr, type) {
                this.$emit('copy', mr, type);
            },

            handlerInputKey() {
                this.isScanBarcode = false;
                this._debounceSearch();
            },

            /**
             * @desc icpc即将过期数据
             */
            async fetchIcpcInfo() {
                try {
                    if (!this.isShowICPCReport) return;
                    const { data } = await SocialAPI.fetchShebaoTaskList({
                        icpcExpireFlag: 1,
                        // 接口调整
                        icpcUploadFlag: 1,
                        beginDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
                        endDate: dayjs().format('YYYY-MM-DD'),
                    });
                    this.icpcNearExpiryCount = data.icpcNearExpiryCount || 0;
                } catch (err) {
                    this.icpcNearExpiryCount = 0;
                    Logger.error({
                        scene: 'fetchIcpcInfo error',
                        err,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
    .outpatient-quick-list-wrapper {
        height: 100%;

        .quick-content-wrapper {
            .treat-online-status {
                position: relative;
                z-index: auto;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 32px;
                font-size: 12px;
                color: var(--abc-color-Y2);
                cursor: pointer;
                background-color: var(--abc-color-Y4);

                &.online {
                    color: var(--abc-color-G1);
                    background-color: var(--abc-color-G4);
                }

                .treat-online-setting-btn {
                    position: absolute;
                    top: 0;
                    right: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 36px;
                    height: 100%;

                    .iconfont {
                        font-size: 12px;
                        color: var(--abc-color-T3);
                    }

                    &:hover {
                        .iconfont {
                            color: var(--abc-color-theme2);
                        }
                    }
                }

                .change-treat-online-dialog {
                    position: absolute;
                    top: 36px;
                    z-index: 10;
                    width: 100px;
                    padding: 8px 0;
                    background-color: #ffffff;
                    border: 1px solid var(--abc-color-card-border-color);
                    border-radius: var(--abc-border-radius-small);
                    box-shadow: var(--abc-box-shadow-content);

                    > div {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        height: 32px;
                        padding: 0 12px;
                        font-size: 14px;
                        color: var(--abc-color-T1);
                        cursor: pointer;
                    }

                    i {
                        color: var(--abc-color-B2);
                    }
                }
            }
        }

        .patient-name-append {
            display: flex;
            align-items: center;
            margin-left: 6px;
        }
    }
</style>
