<template>
    <div class="c13-report__wrapper">
        <!-- 标题 -->
        <abc-flex align="center" justify="center" class="c13-report__title">
            ¹³C 呼气试验 Hp 检测报告
        </abc-flex>

        <!-- 头部信息 -->
        <abc-flex vertical class="c13-report__header">
            <abc-flex
                gap="6"
                align="center"
                justify="space-between"
                class="with-bottom-border"
            >
                <div class="c13-report__header-item">
                    <span>姓名：</span>
                    <span>{{ patient }}</span>
                </div>

                <div class="c13-report__header-item">
                    <span>检测日期：</span>
                    <span>{{ examInfo.time || '-' }}</span>
                </div>
            </abc-flex>

            <abc-divider margin="small" style="border-bottom-color: var(--abc-color-S1);"></abc-divider>

            <abc-flex align="center" justify="space-between">
                <div class="c13-report__header-item">
                    <span>试剂名称：</span>
                    <span>{{ examInfo.reagent }}</span>
                </div>

                <div class="c13-report__header-item">
                    <span>病历号：</span>
                    <span>{{ examInfo.caseNo || '-' }}</span>
                </div>

                <div class="c13-report__header-item">
                    <span>检测编号：</span>
                    <span>{{ examInfo.examNo || '-' }}</span>
                </div>
            </abc-flex>

            <abc-flex align="center" justify="space-between" style="margin-top: 6px;">
                <div class="c13-report__header-item">
                    <span>诊断：</span>
                    <span>{{ examInfo.diagnosis || '' }}</span>
                </div>

                <div class="c13-report__header-item">
                    <span>检测机构：</span>
                    <span>{{ examInfo.org || '' }}</span>
                </div>
            </abc-flex>
        </abc-flex>

        <abc-divider margin="small" variant="dashed"></abc-divider>

        <!-- 检测结果摘要 -->
        <div class="c13-report__result">
            <abc-flex gap="8">
                <abc-text style="font-weight: 400;">
                    检测结果：
                </abc-text>

                <abc-flex vertical style="flex: 1;">
                    <abc-tips
                        v-if="result.isValid"
                        icon
                        theme="warning"
                    >
                        呼气浓度偏低，请重测
                    </abc-tips>

                    <abc-space v-else>
                        <abc-icon
                            v-if="result.positive"
                            icon="n-alert-fill"
                            style="color: var(--abc-color-Y2);"
                            size="14"
                        ></abc-icon>

                        <abc-text>{{ `DOB=${result.value}` }}</abc-text>

                        <abc-text :theme="result.positive ? 'danger' : 'success'" bold>
                            {{ result.positive ? '阳性（+）' : '阴性（-）' }}
                        </abc-text>
                    </abc-space>

                    <abc-text size="mini" style="margin: 9px 0 32px;">
                        判读标准：阳性 DOB≥4.0，阴性 DOB&lt;4.0
                    </abc-text>

                    <abc-flex
                        v-if="!result.isValid"
                        vertical
                        :gap="40"
                        class="c13-report__chat-wrapper"
                    >
                        <div class="c13-report__border"></div>

                        <abc-flex
                            v-for="(tick, idx) in yTicks"
                            :key="`tick-${idx}`"
                            class="c13-report__chat-line-item"
                            :class="{ 'no-border': idx === 0 || idx === yTicks.length - 1 }"
                            align="center"
                            :gap="12"
                        >
                            <span class="c13-report__chat-line-label">
                                {{ tick }}
                            </span>

                            <div class="c13-report__chat-line"></div>
                        </abc-flex>

                        <!-- 阈值线（固定为4） -->
                        <div
                            class="c13-report__threshold"
                            :style="{
                                bottom: `${(4 / yMax) * 56 * 7 + 7}px`,
                            }"
                        ></div>

                        <!-- 柱 -->
                        <div class="c13-report__bar" :style="barStyle">
                            <abc-text
                                class="c13-report__bar-label"
                                :theme="result.positive ? 'danger' : 'success'"
                            >
                                {{ `DOB=${result.value}` }}
                            </abc-text>
                        </div>
                    </abc-flex>
                </abc-flex>
            </abc-flex>
        </div>


        <!-- 温馨提示 -->
        <abc-card class="c13-report__tips" :bordered="true">
            <abc-flex>
                <abc-flex vertical style="flex: 1;" gap="6">
                    <div class="tips-title">
                        温馨提示：
                    </div>
                    <div class="tips-item">
                        1. 幽门螺杆菌（Hp） 感染是胃病的致病主要原因之一，也是世界卫生组织（WHO）认定的 I 类致癌源，及时诊断并根除 Hp 感染是治愈胃病，防止复发，有效阻断向胃癌发展的重要前提。
                    </div>
                    <div class="tips-item">
                        2. 阳性患者请接受医生的正规治疗，并在治疗结束 1 个月后进行复查根除。
                    </div>
                </abc-flex>
                <div class="tips-figure">
                    <img :src="require('@/assets/images/examination/c13-example.png')" />
                </div>
            </abc-flex>
        </abc-card>

        <!-- 医师签名与落款 -->
        <div class="c13-report__footer">
            <abc-flex justify="flex-end" class="footer-sign">
                <span>检测医生：</span>
                <span>{{ doctor || '-' }}</span>
            </abc-flex>

            <abc-divider margin="small" style="border-bottom-color: var(--abc-color-S1);"></abc-divider>

            <div class="footer-note">
                本报告仅对本次检测负责，临床在引用检测结果时，应结合病历、临床表现等因素综合分析
            </div>
        </div>
    </div>
</template>

<script>
    import { formatAge } from '@/utils';
    import { formatDate } from '@tool/date';
    import { formatDentistry2Text } from '@/common/utils/format-diagnosis';
    import { mapGetters } from 'vuex';

    export default {
        name: 'C13CloudExamReport',
        props: {
            postData: {
                type: Object, default: () => ({}),
            },
        },
        computed: {
            ...mapGetters(['currentClinic']),

            patient() {
                const p = this.postData?.patient || {};

                return [
                    p.name,
                    p.sex,
                    formatAge(p.age),
                ].filter(Boolean).join(' ');
            },

            diagnosisText() {
                return formatDentistry2Text(this.postData.extendDiagnosisInfos || []);
            },

            examInfo() {
                return {
                    time: formatDate(this.postData.testTime, 'YYYY-MM-DD HH:mm'),
                    reagent: '¹³C 尿素',
                    caseNo: this.to8(this.postData.patientOrderNumber),
                    examNo: this.postData.orderNo,
                    diagnosis: this.diagnosisText,
                    org: this.currentClinic.name,
                };
            },
            result() {
                const r = this.postData?.itemsValue?.[0] || {};
                const value = Number(r.value);
                return {
                    value,
                    positive: value >= 4,
                    isValid: value === -1,
                };
            },
            doctor() {
                return this.postData?.testerName || '-';
            },

            // 计算友好的步长（1/2/5*10^n），共7个刻度（6个区间）
            yStep() {
                const baseMax = Math.max(4, Number(this.result.value || 0));
                const target = baseMax / 7; // 6个区间
                if (target <= 0) return 1;
                const pow10 = Math.pow(10, Math.floor(Math.log10(target)));
                const n = target / pow10;
                let step;
                if (n <= 1) step = 1;
                else if (n <= 2) step = 2;
                else if (n <= 5) step = 5;
                else step = 10;
                return step * pow10;
            },

            // 最大值=步长*6，确保包含结果值与阈值4
            yMax() {
                return this.yStep * 7;
            },

            // 刻度数组：0..6 共7个
            yTicks() {
                return Array.from({ length: 8 }, (_, i) => {
                    const v = +(i * this.yStep).toFixed(2);
                    return Number.isInteger(this.yStep) ? Math.round(v) : v;
                }).reverse();
            },

            barStyle() {
                const v = Math.max(0, Math.min(this.yMax, Number(this.result.value || 0)));
                const h = (v / this.yMax) * 56 * 7;
                return {
                    height: `${h}px`,
                    background: this.result.positive ? 'var(--abc-color-R1)' : 'var(--abc-color-G2)',
                };
            },
        },

        methods: {
            to8(value = '') {
                value === null && (value = '');
                const srcStr = '00000000';
                return srcStr.slice(0, -`${value}`.length).concat(value);
            },
        },
    };
</script>

<style lang="scss" scoped>
.c13-report__wrapper {
    padding: 40px;
    background: #ffffff;
}

.c13-report__title {
    margin-bottom: 24px;
    font-family: SimSun, STSong, serif;
    font-size: 21px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px; /* 133.333% */
    letter-spacing: 0.84px;
}

.c13-report__header {
    font-size: 13px;
    font-weight: 300;

    .c13-report__header-item {
        width: 33%;
    }
}

.c13-report__result {
    margin: 12px 0 32px;
    font-size: 13px;
    font-weight: 300;

    .c13-report__chat-wrapper {
        position: relative;

        .c13-report__border {
            position: absolute;
            top: 8px;
            left: 34px;
            width: calc(100% - 34px);
            height: calc(100% - 16px);
            border: 1px solid var(--abc-color-S1);
        }

        .c13-report__chat-line-item {
            &.no-border {
                .c13-report__chat-line {
                    border-bottom: none;
                }
            }

            .c13-report__chat-line-label {
                flex-shrink: 0;
                width: 22px;
                height: 16px;
                text-align: right;
            }

            .c13-report__chat-line {
                flex: 1;
                border-bottom: 1px dashed var(--abc-color-S1);
            }
        }

        .c13-report__threshold {
            position: absolute;
            left: 34px;
            width: calc(100% - 34px);
            height: 2px;
            background: var(--abc-color-R1);
        }

        .c13-report__bar {
            position: absolute;
            bottom: 8px;
            left: 50%;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            width: 40px;
            background: var(--abc-color-R1); /* 红柱 */
            transform: translateX(-50%);

            .c13-report__bar-label {
                position: absolute;
                top: -18px;
                font-size: 13px;
                font-weight: 700;
            }
        }
    }
}

.c13-report__tips {
    padding: 16px;
    margin-top: 300px;
    font-size: 13px;
    font-weight: 300;
    border-radius: 8px;

    .tips-title {
        margin-bottom: 6px;
        font-weight: 400;
    }

    .tips-item {
        font-size: 12px;
        line-height: 20px;
        color: var(--abc-color-T1);
    }

    .tips-figure {
        width: 227px;
        height: 137px;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }
}

.c13-report__footer {
    margin-top: 16px;
    font-size: 13px;
    font-weight: 300;

    .footer-sign {
        padding-bottom: 6px;
    }
}
</style>
