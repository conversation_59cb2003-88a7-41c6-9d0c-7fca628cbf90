<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        :auto-focus="false"
        custom-class="invoice-modal-wrapper"
        append-to-body
        dialog-content-styles="background: #F4F4F4; border-radius: var(--abc-dialog-border-radius)"
        content-styles="padding: 0; max-height: 604px"
        size="xlarge"
        :show-confirm="false"
        :hide-footer="true"
        :show-cancel="false"
        :need-high-level="false"
        disabled-keyboard
        @close="handleClose"
    >
        <abc-tips-card-v2
            v-if="lastFailedErrorMessage && !listLoading"
            slot="top-extend"
            theme="warning"
        >
            {{ lastFailedErrorMessage }}
            <abc-button
                v-if="lastFailedErrorCode"
                slot="operate"
                style="margin-left: auto;"
                variant="text"
                size="small"
                @click="handleCloseError"
            >
                {{ lastFailedErrorCode === `${TaxFullyInvoiceErrorCode.NEED_LOGIN_FAIL}` ? '立即登录' : '前往认证' }}
            </abc-button>
        </abc-tips-card-v2>
        <abc-tips-card-v2
            v-if="disabledInvoiceFeature"
            slot="top-extend"
            theme="warning"
        >
            门店未开通发票功能，请先前往【{{ invoiceConfigModuleName }}】开通
            <abc-button
                slot="operate"
                style="margin-left: auto;"
                variant="text"
                size="small"
                @click="navigateToInvoiceConfig"
            >
                去开通
            </abc-button>
        </abc-tips-card-v2>
        <abc-tips-card-v2
            v-if="isUploadInvoice && !isSupportEInvoice"
            slot="top-extend"
            theme="primary"
        >
            该发票为补传发票，建议开通 ABC 电子发票功能，可在 ABC 系统内直接开票上传医保中心，无需补传。
            <abc-button
                slot="operate"
                style="margin-left: auto;"
                variant="text"
                size="small"
                @click="navigateToInvoiceConfig"
            >
                去开通
            </abc-button>
        </abc-tips-card-v2>

        <abc-card
            v-if="invoiceRecordList.length && !listLoading"
            slot="left-extend"
            style="position: absolute; top: 0; left: -264px; width: 260px; padding: 8px 0  8px 10px;"
            radius-size="large"
            :shadow="false"
            :border="false"
            :divider="false"
            class="invoice-list-left-wrapper"
        >
            <abc-scrollbar padding-size="none">
                <template v-if="pendingInvoiceList.length || processingInvoiceList.length">
                    <abc-flex
                        vertical
                        justify="flex-start"
                        align="flex-start"
                        style="padding: var(--abc-paddingLR-ml) var(--abc-paddingLR-ml) 0 var(--abc-paddingLR-ml);"
                    >
                        <abc-text size="normal" theme="gray">
                            待开票
                        </abc-text>
                    </abc-flex>

                    <abc-list
                        ref="pendingInvoiceListRef"
                        :data-list="renderPendingList"
                        size="large"
                        :scroll-config="{ paddingSize: 'none' }"
                        need-selected
                        :scrollable="false"
                        custom-item-class="invoice-list-left-item"
                        :create-key="(item) => item.goodsFeeTypeId"
                        @click-item="handleClickPreviewPendingInvoice"
                    >
                        <template
                            #default="{
                                isSelected
                            }"
                        >
                            <abc-flex
                                justify="flex-start"
                                align="center"
                                gap="6"
                            >
                                <abc-space
                                    :size="6"
                                    class="is-blue-invoice"
                                >
                                    <abc-icon icon="s-invoice-fill" :color="isSelected ? '#fff' : ''"></abc-icon>
                                    <abc-text
                                        size="normal"
                                        :style="{ color: isSelected ? '#fff' : '' }"
                                    >
                                        <abc-text :style="{ opacity: 0 }">
                                            -
                                        </abc-text>
                                        <abc-money symbol-icon-size="12px" :value="totalInvoiceFee"></abc-money>
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-list>
                    <abc-list
                        ref="processingInvoiceListRef"
                        :data-list="processingInvoiceList"
                        size="large"
                        :scroll-config="{ paddingSize: 'none' }"
                        need-selected
                        :scrollable="false"
                        custom-item-class="invoice-list-left-item"
                        :create-key="(item) => item.goodsFeeTypeId"
                        @click-item="handleClickPreviewProcessingInvoice"
                    >
                        <template
                            #default="{
                                item,
                                isSelected
                            }"
                        >
                            <abc-flex
                                justify="flex-start"
                                align="center"
                                gap="6"
                            >
                                <abc-space
                                    :size="6"
                                    :class="{
                                        'is-red-invoice': item.type === InvoiceViewType.RED, 'is-blue-invoice': item.type === InvoiceViewType.BLUE
                                    }"
                                >
                                    <abc-icon icon="s-invoice-fill" :color="isSelected ? '#fff' : ''"></abc-icon>
                                    <abc-text
                                        size="normal"
                                        :style="{ color: isSelected ? '#fff' : '' }"
                                    >
                                        <abc-text :style="{ opacity: item.invoiceFee < 0 ? 1 : 0 }">
                                            -
                                        </abc-text>
                                        <abc-money symbol-icon-size="12px" :value="Math.abs(item.invoiceFee)"></abc-money>
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-list>
                </template>

                <template v-if="invoiceRecordList.length">
                    <abc-flex
                        vertical
                        justify="flex-start"
                        align="flex-start"
                        style="padding: var(--abc-paddingLR-ml) var(--abc-paddingLR-ml) 0 var(--abc-paddingLR-ml);"
                    >
                        <abc-text size="normal" theme="gray">
                            已开票
                        </abc-text>
                    </abc-flex>
                    <abc-list
                        ref="invoiceRecordListRef"
                        :data-list="invoiceRecordList"
                        size="large"
                        :scroll-config="{ paddingSize: 'none' }"
                        need-selected
                        :scrollable="false"
                        custom-item-class="invoice-list-left-item"
                        :create-key="(item) => item.actionId"
                        @click-item="handleClickPreviewRecordInvoice"
                    >
                        <template
                            #default="{
                                item,
                                isSelected
                            }"
                        >
                            <abc-flex gap="4" align="center">
                                <abc-space
                                    :size="6"
                                    :class="{
                                        'is-red-invoice': item.type === InvoiceViewType.RED,
                                        'is-blue-invoice': item.type === InvoiceViewType.BLUE,
                                        'is-refund-invoice': item.status === InvoiceStatus.REFUND && item.invoiceCategory === InvoiceCategory.PAPER
                                    }"
                                >
                                    <abc-icon icon="s-invoice-fill" :color="isSelected ? '#fff' : ''"></abc-icon>
                                    <abc-text
                                        size="normal"
                                        style="width: 72px; max-width: 72px;"
                                        tag="div"
                                        class="ellipsis"
                                        :style="{ color: isSelected ? '#fff' : '' }"
                                    >
                                        <abc-text :style="{ opacity: item.invoiceFee < 0 ? 1 : 0 }">
                                            -
                                        </abc-text>
                                        <abc-money symbol-icon-size="12px" :value="Math.abs(item.invoiceFee)" :style="{ 'text-decoration-line': item.status === InvoiceStatus.REFUND && item.invoiceCategory === InvoiceCategory.PAPER ? 'line-through' : 'none' }"></abc-money>
                                    </abc-text>
                                </abc-space>
                                <abc-text
                                    size="mini"
                                    theme="gray"
                                    :style="{ color: isSelected ? '#fff' : '' }"
                                    style="width: 70px; min-width: 70px; max-width: 70px;"
                                    class="ellipsis"
                                >
                                    {{ item.goodsFeeTypeName }}
                                </abc-text>
                                <abc-text
                                    size="mini"
                                    theme="gray"
                                    :style="{ color: isSelected ? '#fff' : '' }"
                                    style="width: 44px; min-width: 44px; max-width: 44px; text-align: right;"
                                    class="ellipsis"
                                >
                                    {{ InvoiceCategoryTextShort[item.invoiceCategory] }}
                                </abc-text>
                            </abc-flex>
                        </template>
                    </abc-list>
                </template>
            </abc-scrollbar>
        </abc-card>

        <abc-flex
            v-abc-loading="loading"
            vertical
            style="position: relative; min-height: 352px;"
            class="invoice-page-wrapper"
        >
            <abc-content-empty v-if="!businessItemDetails && !disablePaperAndMedicalInvoice" style="position: absolute; left: 50%; transform: translate3d(-50%, -50%, 0);" value="暂无可开发票"></abc-content-empty>

            <div v-show="!loading" class="invoice-page-header">
                <abc-select
                    v-if="(isNewInvoice && !hasInvoiceRecords) || disabledInvoiceFeature"
                    v-model="selectedInvoiceCategory"
                    :width="120"
                    @change="handleChangeInvoiceCategory"
                >
                    <abc-option
                        v-for="item in invoiceTabOptions"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    >
                    </abc-option>
                </abc-select>

                <abc-flex
                    v-if="disablePaperAndMedicalInvoice"
                    vertical
                    gap="6"
                    align="center"
                    style="position: absolute; top: 50%; left: 50%; width: 100%; transform: translate3d(-50%, -50%, 0);"
                >
                    <abc-text theme="gray" size="normal">
                        门店未开通发票功能，请先前往【{{ invoiceConfigModuleName }}】开通
                    </abc-text>
                </abc-flex>

                <abc-flex v-if="isBeingInvoice" gap="10" align="center">
                    <abc-text size="large" theme="warning-light" bold>
                        {{ currentPreviewItem.type === InvoiceViewType.BLUE ? '开票中' : '冲红待确认' }}
                    </abc-text>
                    <abc-text theme="gray" size="normal">
                        <template v-if="currentPreviewItem.type === InvoiceViewType.BLUE">
                            {{ parseTime(currentPreviewItem.invoiceDate, 'y-m-d h:i:s', true) }} 已提交开票申请至电子税局，预计将在 24 小时内完成开票
                        </template>
                        <template v-else>
                            {{ parseTime(currentPreviewItem.invoiceDate, 'y-m-d h:i:s', true) }} 购方已将发票用作报账，冲红申请需等待购方确认
                        </template>
                    </abc-text>
                </abc-flex>
                <abc-flex v-if="isInvoiceRecord" justify="space-between">
                    <abc-text
                        :theme="currentPreviewItem.status === InvoiceStatus.REFUND && currentPreviewItem.invoiceCategory === InvoiceCategory.PAPER ? 'gray' : (currentPreviewItem.type === InvoiceViewType.BLUE ? 'primary-light' : 'danger-light')"
                        size="large"
                        bold
                        tag="div"
                    >
                        <abc-space :size="8">
                            <abc-text>{{ getInvoiceType(currentPreviewItem) }}</abc-text>
                            <abc-text>
                                <template v-if="currentPreviewItem.invoiceFee < 0">
                                    -
                                </template>
                                <abc-money :value="Math.abs(currentPreviewItem.invoiceFee)" symbol-icon-size="14px" :style="{ 'text-decoration-line': currentPreviewItem.status === InvoiceStatus.REFUND && currentPreviewItem.invoiceCategory === InvoiceCategory.PAPER ? 'line-through' : 'none' }"></abc-money>
                            </abc-text>
                        </abc-space>
                    </abc-text>

                    <abc-space v-if="digitalInvoice.invoiceUrl">
                        <abc-button
                            v-if="isShuzu"
                            v-abc-check-electron="{
                                disableTips: '请下载客户端打印发票 ',
                                customStyle: {
                                    width: '238px',
                                }
                            }"
                            icon="s-b-print-line"
                            variant="ghost"
                            shape="round"
                            theme="default"
                            :disabled="printISVLoading"
                            size="small"
                            class="invoice-download-btn"
                            @click="handlePrintElectron(digitalInvoice.invoiceUrl)"
                        >
                            打印
                        </abc-button>

                        <abc-button
                            icon="s-b-download2-line"
                            variant="ghost"
                            shape="round"
                            theme="default"
                            size="small"
                            class="invoice-download-btn"
                            @click="open(digitalInvoice.invoiceUrl)"
                        >
                            下载
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </div>

            <abc-scrollbar v-show="!loading && businessItemDetails" class="invoice-page-preview">
                <invoice-preview
                    ref="invoicePreview"
                    :show-refund="isRefundInvoice && !loading"
                    :is-refund-invoice="isRefundInvoice"
                    :is-upload-invoice="isUploadInvoice"
                    :print-data="printPreviewData"
                    :digital-invoice="isNewInvoice ? digitalInvoice : null"
                    :normal-invoice="normalInvoice"
                    :print-bill-config="printBillConfig"
                    :invoice-category="selectedInvoiceCategory"
                    :paper-invoice-scene="businessType"
                    :show-paper-invoice="showPaperInvoice"
                    :invoice-supplier-id="invoiceSupplierId"
                    :is-open-invoice="isOpenInvoice"
                    :is-open-medical-invoice="isOpenMedicalInvoice"
                    :mac-address="macAddress"
                    @devToolsPreviewInvoice="handleDevToolsPreviewInvoice"
                    @to-invoice-config="navigateToInvoiceConfig"
                    @getBillPages="handleGetBillPages"
                    @rendering="changePrintDisabled"
                    @rendered="changePrintEnabled"
                    @close="writeCompleteHandler"
                ></invoice-preview>
            </abc-scrollbar>

            <div v-show="!loading && !disablePaperAndMedicalInvoice" class="invoice-page-form-footer">
                <abc-form ref="invoiceFormRef" label-position="left" label-width="72">
                    <abc-form-item-group grid :grid-column-count="2">
                        <template v-if="isPaperInvoice">
                            <abc-form-item v-if="showInvoiceFeeType && isNewInvoice" grid-column="span 2">
                                <abc-form-item label="开票科目">
                                    <abc-flex
                                        align="center"
                                        wrap="wrap"
                                        gap="8"
                                    >
                                        <abc-checkbox
                                            v-for="item in pendingInvoiceList"
                                            :key="item.goodsFeeTypeId"
                                            v-model="item.checked"
                                            @change="handleChangeInvoiceChecked"
                                        >
                                            {{ item.goodsFeeTypeName }}
                                        </abc-checkbox>

                                        <abc-tooltip v-for="item in completedInvoiceList" :key="item.goodsFeeTypeId" content="已开票">
                                            <abc-checkbox disabled>
                                                {{ item.goodsFeeTypeName }}
                                            </abc-checkbox>
                                        </abc-tooltip>
                                    </abc-flex>
                                </abc-form-item>
                            </abc-form-item>
                            <abc-form-item label="发票代码">
                                <abc-input
                                    v-if="isInvoiceRecord"
                                    v-model="normalInvoice.invoiceCode"
                                    :width="282"
                                    disabled
                                >
                                </abc-input>
                                <abc-select
                                    v-else
                                    v-model="normalInvoice.invoiceManagementId"
                                    :width="282"
                                    inner-width="auto"
                                    :with-search="invoiceCodeList?.length > 8"
                                    :fetch-suggestions="querySearchInvoiceCode"
                                    :disabled="disabledEdit"
                                    @change="handleChangeCode"
                                >
                                    <abc-option
                                        v-for="item in filterInvoiceCodeList"
                                        :key="item.id"
                                        :value="item.id"
                                        :label="item.code"
                                    >
                                        <abc-space :size="12">
                                            {{ item.code }}
                                            <abc-text size="normal">
                                                号码 {{ item.startNumber }}~{{ item.endNumber }}
                                            </abc-text>
                                        </abc-space>
                                    </abc-option>
                                </abc-select>
                                <abc-flex
                                    v-if="invoicePageCount && isNewInvoice"
                                    style="position: absolute; top: 36px; left: 0; margin-top: 2px;"
                                    align="center"
                                    wrap="wrap"
                                >
                                    <abc-text
                                        size="mini"
                                        theme="warning"
                                        tag="div"
                                    >
                                        将{{ invoicePageCount > 1 ? '连续' : '' }}使用 {{ invoicePageCount }}  张发票
                                        <template v-if="invoicePageCount > normalInvoice.invoiceNumbers.length">
                                            ，当前票号不足请先
                                        </template>
                                        <abc-link size="small" @click="handleCreateNumber">
                                            新增票号
                                        </abc-link>
                                    </abc-text>
                                </abc-flex>
                            </abc-form-item>
                            <abc-form-item
                                label="发票号码"
                                required
                                hidden-red-dot
                            >
                                <abc-space v-if="isInvoiceRecord" :size="8">
                                    <abc-input
                                        v-model="normalInvoice.invoiceNumber"
                                        :width="invoicePageCount > 1 ? 130 : 282"
                                        disabled
                                    >
                                    </abc-input>
                                    <template v-if="invoicePageCount > 1">
                                        <abc-text size="normal" theme="gray">
                                            -
                                        </abc-text>
                                        <abc-input
                                            v-model="normalInvoice.invoiceNumbers[normalInvoice.invoiceNumbers.length - 1]"
                                            :width="130"
                                            disabled
                                        ></abc-input>
                                    </template>
                                </abc-space>

                                <abc-space v-else :size="8">
                                    <abc-select
                                        v-model="normalInvoice.invoiceNumber"
                                        :width="invoicePageCount > 1 ? 130 : 282"
                                        no-icon
                                        :disabled="disabledEdit"
                                        :adaptive-width="false"
                                        :with-search="invoiceNumberList.length > 8"
                                        :fetch-suggestions="querySearchInvoiceNumber"
                                        @change="calcInvoiceNo"
                                    >
                                        <abc-option
                                            v-for="item in filterInvoiceNumberList"
                                            :key="item.id"
                                            :value="item.invoiceNumber"
                                            :label="item.invoiceNumber"
                                        ></abc-option>
                                    </abc-select>
                                    <template v-if="invoicePageCount > 1">
                                        <abc-text size="normal" theme="gray">
                                            -
                                        </abc-text>
                                        <abc-input
                                            :width="130"
                                            disabled
                                            :value="normalInvoice.invoiceNumbers[normalInvoice.invoiceNumbers.length - 1]"
                                        ></abc-input>
                                    </template>
                                </abc-space>
                            </abc-form-item>
                        </template>
                        <template v-else>
                            <abc-form-item
                                label="抬头类型"
                            >
                                <abc-radio-group v-model="digitalInvoice.buyerType" @change="handleChangeEInvoice">
                                    <abc-radio v-if="!isPeGroupInvoice" :label="InvoiceHeaderType.PERSONAL" :disabled="disabledEdit">
                                        个人
                                    </abc-radio>
                                    <abc-radio v-if="!isPaperInvoice" :label="InvoiceHeaderType.ENTERPRISE" :disabled="disabledEdit">
                                        企业
                                    </abc-radio>
                                </abc-radio-group>
                            </abc-form-item>

                            <abc-form-item
                                v-if="digitalInvoice.buyerType === InvoiceHeaderType.PERSONAL"
                                label="身份证"
                            >
                                <abc-input
                                    v-model="digitalInvoice.idCard"
                                    :disabled="disabledEdit"
                                    :width="282"
                                    @change="handleChangeEInvoice"
                                ></abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-else
                                label="税号"
                            >
                                <abc-input
                                    v-model="digitalInvoice.buyerTaxNum"
                                    :disabled="disabledEdit"
                                    :width="282"
                                    @change="handleChangeEInvoice"
                                ></abc-input>
                            </abc-form-item>

                            <abc-form-item
                                label="抬头名称"
                                required
                                hidden-red-dot
                            >
                                <abc-input
                                    v-model="digitalInvoice.buyerName"
                                    :disabled="disabledEdit"
                                    :width="282"
                                    @change="handleChangeEInvoice"
                                ></abc-input>
                            </abc-form-item>

                            <abc-form-item
                                label="发送至"
                                hidden-red-dot
                            >
                                <abc-space is-compact compact-block>
                                    <div>
                                        <abc-select
                                            v-model="digitalInvoice.pushMode"
                                            :width="90"
                                            :disabled="disabledEdit"
                                        >
                                            <abc-option :value="EInvoicePushMode.SMS" label="手机号"></abc-option>
                                            <abc-option :value="EInvoicePushMode.EMAIL" label="邮箱"></abc-option>
                                        </abc-select>
                                    </div>
                                    <abc-input
                                        v-if="digitalInvoice.pushMode === EInvoicePushMode.SMS"
                                        v-model="digitalInvoice.buyerPhone"
                                        :disabled="disabledEdit || disabledEditSms"
                                        :width="192"
                                    >
                                    </abc-input>
                                    <abc-input
                                        v-else
                                        v-model="digitalInvoice.email"
                                        :disabled="disabledEdit"
                                        :width="192"
                                    ></abc-input>
                                </abc-space>
                                <abc-text
                                    v-if="disabledEditSms"
                                    style="position: absolute; top: 36px; left: 0;"
                                    size="mini"
                                    theme="warning"
                                    tag="div"
                                >
                                    {{ sendSmsMessageTips }}
                                </abc-text>
                            </abc-form-item>
                        </template>
                    </abc-form-item-group>
                </abc-form>
            </div>
        </abc-flex>

        <abc-flex slot="footerPrepend" justify="space-between" style="width: 100%; min-height: 32px;">
            <abc-flex>
                <abc-button v-if="showUploadBtn" variant="text" @click="handleOpenUploadESettlementDialog">
                    上传电子发票
                </abc-button>
                <abc-button v-if="isPaperInvoice" variant="text" @click="handleCreateNumber">
                    票号管理
                </abc-button>

                <abc-space v-if="isElectronicInvoice && currentPreviewItem" size="large">
                    <abc-text v-if="currentPreviewItem.created || currentPreviewItem.invoiceDate" size="normal" theme="gray">
                        开票时间：{{ parseTime((currentPreviewItem.created || currentPreviewItem.invoiceDate) ,'y-m-d h:i', true) }}
                    </abc-text>
                    <abc-text v-if="currentPreviewItem.invoiceNumbers" size="normal" theme="gray">
                        票号：{{ currentPreviewItem.invoiceNumbers }}
                    </abc-text>
                    <abc-text v-if="currentPreviewItem.type === InvoiceViewType.RED && currentPreviewItem.redConfirmId" size="normal" theme="gray">
                        红票确认单：{{ currentPreviewItem.redConfirmId }}
                    </abc-text>
                </abc-space>
            </abc-flex>

            <abc-flex v-if="isElectronicInvoice" align="center" gap="8">
                <abc-flex v-if="showInvoiceOperator" align="center">
                    <abc-text theme="gray" size="normal">
                        开票员
                    </abc-text>
                    <abc-select
                        v-model="invoiceOperatorId"
                        reference-mode="text"
                        :width="80"
                        reference-text-justify="end"
                        :inner-width="200"
                        @change="handleChangeInvoiceOperator"
                    >
                        <abc-option
                            v-for="item in operatorList"
                            :key="item.value"
                            :value="item.value"
                            :label="item.employeeName"
                        >
                            {{ item.employeeName }}({{ item.label }})
                        </abc-option>
                    </abc-select>
                </abc-flex>
                <abc-tooltip v-if="isCanWriteEInvoice" :disabled="!unableWriteInvoice && !disabledInvoiceFeature" :content="digitalInvoiceTips">
                    <div>
                        <abc-button :loading="buttonLoading" :disabled="loading || disabledInvoiceFeature" @click="handleSubmitEInvoicePreCheck">
                            开票
                            <abc-money :value="totalInvoiceFee"></abc-money>
                        </abc-button>
                    </div>
                </abc-tooltip>
                <abc-button
                    v-if="canChonghongEInvoice && !isUploadInvoice"
                    variant="ghost"
                    theme="danger"
                    :disabled="loading"
                    :loading="buttonLoading"
                    @click="handleChonghongEInvoice"
                >
                    红冲
                </abc-button>
                <abc-tooltip
                    v-if="isUploadInvoice && isInvoiceRecord"
                    content="该发票已上传至医保中心，无法删除"
                    placement="top"
                    :disabled="!disabledDelete"
                >
                    <div>
                        <abc-button
                            :disabled="disabledDelete"
                            variant="ghost"
                            theme="danger"
                            @click="handleDeleteInvoice"
                        >
                            删除
                        </abc-button>
                    </div>
                </abc-tooltip>
                <abc-button
                    v-if="isRedInvoiceOpening"
                    variant="ghost"
                    theme="danger"
                    :loading="buttonLoading"
                    @click="handleRevokeRedInvoice"
                >
                    撤销冲红
                </abc-button>
            </abc-flex>
            <template v-else>
                <abc-flex v-if="macAddress">
                    <template v-if="showInvoiceOperationButtons">
                        <abc-button
                            variant="ghost"
                            theme="danger"
                            :disabled="loading"
                            @click="handleClickDestroy"
                        >
                            作废
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            theme="danger"
                            :disabled="loading"
                            @click="handleClickChongHong"
                        >
                            红冲
                        </abc-button>
                    </template>
                    <abc-tooltip v-else-if="(isCanWriteInvoice && businessItemDetails?.length) || disabledInvoiceFeature" :content="disabledPrint.tips" :disabled="!disabledPrint.flag">
                        <div>
                            <abc-button
                                :loading="buttonLoading"
                                :disabled="disabledPrint.flag || loading || initDataLoading"
                                @click="handleClickPrint"
                            >
                                {{ canWriteInvoiceList.length > 1 ? '批量开票' : '开票' }}
                            </abc-button>
                        </div>
                    </abc-tooltip>


                    <template v-if="!isRefundInvoice && isInvoiceRecord">
                        <abc-button variant="ghost" @click="handleClickRePrint">
                            补打
                        </abc-button>
                    </template>
                </abc-flex>
            </template>
        </abc-flex>
        <num-manager-dialog
            v-if="showNumInvoice"
            v-model="showNumInvoice"
            :write-invoice-config="writeInvoiceConfig"
            @update="updateHandleUpdateInvoiceNumber"
        ></num-manager-dialog>
        <template slot="bottom-extend">
            <div v-if="showLoading" class="invoice-loading-modal">
                <div class="invoice-loading-content">
                    <abc-flex class="invoice-loading-animation-wrapper" justify="space-between" align="center">
                        <abc-image :src="AbcLogo" :width="40" :height="40"></abc-image>
                        <abc-flex vertical style="position: relative;">
                            <div class="invoice-loading-animation">
                                <div class="loader-dot"></div>
                            </div>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="48"
                                height="4"
                                viewBox="0 0 48 4"
                                fill="none"
                            >
                                <path
                                    d="M1 3H45L42.5024 1"
                                    stroke="#0090FF"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                />
                            </svg>
                        </abc-flex>
                        <abc-image :src="InvoiceTaxImg" :width="40" :height="40"></abc-image>
                    </abc-flex>
                    <abc-text
                        bold
                        theme="black"
                        size="normal"
                        tag="div"
                        style="text-align: center;"
                    >
                        {{ showLoadingTitle }}
                    </abc-text>
                </div>
            </div>
        </template>

        <operator-form-dialog
            v-if="showOpenPasswordDialog"
            :id="passwordOperatorId"
            v-model="showOpenPasswordDialog"
            show-tips
        ></operator-form-dialog>
    </abc-modal>
</template>

<script type="text/babel">
    import AbcLogo from '@/assets/images/logo/abc-logo.png';
    import InvoiceTaxImg from '@/assets/images/invoice/invoice-tax.png';
    import InvoiceAPI from 'api/invoice';
    const NumManagerDialog = () => import('views/cashier/invoice/invoice-number-manager-dialog.vue');

    import {
        InvoiceBusinessScene,
        InvoiceCategory,
        InvoiceCategoryText,
        InvoiceHeaderType,
        TaxFullyInvoiceErrorCode,
        InvoiceViewType,
        PaperInvoiceType,
        InvoiceStatus,
        EInvoicePushMode,
        SupportInvoiceMethodsEnum,
        UploadElectronicSettlementInvoiceStatus,
        InvoiceSupplierId,
        InvoiceBusinessSceneIncludeInvoiceTypes,
        InvoiceCategoryTextShort, ReimbursementElectronicSocialCenterStatus,
    } from './constants.js';

    import { noop } from 'utils/index.js';
    import Logger from 'utils/logger';
    import { parseTime } from '@/utils';

    import AccountValidateDialog from 'views/cashier/invoice/tax-fully-digital-dialog/account-validate-dialog';
    import FaceValidateDialog from 'views/cashier/invoice/tax-fully-digital-dialog/face-validate-dialog';
    import OperatorFormDialog from 'views/settings/charge-setting/invoice-setting/components/operator-form-dialog.vue';
    import InvoicePreview from 'views/cashier/invoice/invoice-preview-v2/index.vue';
    import AbcPrinter from '@/printer';
    import { ABCPrintConfigKeyMap } from '@/printer/constants';

    import {
        matchPageSize, matchTemplate,
    } from 'views/settings/print-config/medical-bills/bill';
    import AbcElectronicSettlementUploadDialog from 'components/electronic-settlement-upload-dialog';
    import LocalInvoice from 'views/cashier/invoice/write-invoice-core-v2/local-invoice';
    import { sleep } from 'utils/delay';
    import useInvoiceNumber from 'views/cashier/invoice/hooks/use-invoice-number';

    import { ANONYMOUS_ID } from '@abc/constants';


    import {
        ChargeStatusEnum,
    } from '@/service/charge/constants.js';
    import { isEqual } from 'utils/lodash.js';
    import clone from 'utils/clone.js';
    import { HospitalChargeStatusEnum } from '@/views-hospital/charge-hospital/utils';
    import { PEPayStatusEnum } from 'views/physical-examination/constants';
    import { windowOpen } from '@/core/navigate-helper';
    import useCheckElectronicSettlement from 'views/cashier/invoice/hooks/check-electronic-settlement';
    import useInvoiceOperator from 'views/cashier/invoice/hooks/use-invoice-operator';
    import ChonghongDialog from 'views/cashier/invoice/chonghong-dialog';
    import { getMacAddress } from 'views/print/tools';
    import InvoiceService from 'views/cashier/invoice/write-invoice-core-v2/invoice-service';
    import { useGetInvoiceMacAddress } from './hooks/use-get-invoice-macaddress.js';
    import { navigate } from '@storybook/addon-links';
    import {
        off, on,
    } from 'utils/dom';
    import {
        useMessageConfigItem, useMessageConfig,
    } from 'views/settings/message-notification-item/use-message-config';
    import useDigitalInvoiceOpenStatus from 'views/settings/charge-setting/invoice-setting/hooks/use-digital-invoice-open-status';
    import { pdfLodopPrint } from '@/printer/utils';
    // import { pdfUrlToBase64 } from '@/printer/print-api/examination';


    export default {
        name: 'InvoiceDialog',
        components: {
            InvoicePreview,
            NumManagerDialog,
            OperatorFormDialog,
        },
        props: {
            chargeStatus: Number,
            payStatus: Number,
            chargeSheetId: {
                type: String,
            },
            businessType: {
                type: String,
            },
            patientInfo: {
                type: Object,
            },
            printBillConfig: {
                type: Object,
                required: true,
            },
            printMedicalListConfig: {
                type: Object,
                required: true,
            },
            medicalElectronicAPIConfig: {
                type: Object,
                required: true,
            },
            writeInvoiceConfig: {
                type: Object,
                required: true,
            },
            isOpenInvoice: {
                type: Boolean,
                default: false,
            },
            isOpenMedicalInvoice: {
                type: Boolean,
                default: false,
            },
            invoiceType: {
                type: Number,
                default: PaperInvoiceType.OUTPATIENT,
            },
            invoiceStatus: {
                type: Number,
                default: 0,
            },
            toBillPrintSetting: {
                type: Function,
                default: noop,
            },
            submit: Function,
            cancel: Function,
            updateInvoiceStatus: {
                type: Function,
                default: noop,
            },
            updateInvoice: {
                type: Function,
                default: noop,
            },

            onClose: {
                type: Function,
                default: noop,
            },
            resolvePromise: {
                type: Object,
                default() {
                    return {};
                },
            },

            // 是否展示纸质发票
            showPaperInvoice: {
                type: Boolean,
                default: true,
            },
            // 是否展示发票类型
            showInvoiceType: {
                type: Boolean,
                default: true,
            },
            isOpenElecSetlCertUpload: {
                type: Number,
                default: 0,
            },
            invoiceConfigList: {
                type: Array,
                default: () => [],
            },
            userInfo: {
                type: Object,
            },
            /*
            @desc: 药店业务
            @author: ff
            @time: 2025/3/12
            */
            isBizPharmacy: {
                type: Boolean,
                default: false,
            },
        },
        setup(props) {
            const {
                loading: invoiceNumberLoading,
                invoiceCodeList,
                invoiceNumberList,
                filterInvoiceCodeList,
                filterInvoiceNumberList,
                fetchInvoiceCodeList,
                fetchInvoiceNumberList,
                recommendInvoiceNumber,
                calculateInvoiceNumbers,
                querySearchInvoiceCode,
                querySearchInvoiceNumber,
                invoiceCodeFilterStr,
                invoiceNumberFilterStr,
                queryPatientLastInvoiceInfo,
            } = useInvoiceNumber({
                businessType: props.businessType,
            });

            const invoiceSmsMsgTemplate = useMessageConfigItem('invoice.report');
            const { messageConfig } = useMessageConfig();

            const { digitalInvoiceOpenStatus } = useDigitalInvoiceOpenStatus();

            const {
                operatorList,
                recommendedOperator,
                fetchOperators,
            } = useInvoiceOperator();
            return {
                // 票号相关
                invoiceNumberLoading,
                invoiceCodeList,
                invoiceNumberList,
                filterInvoiceCodeList,
                filterInvoiceNumberList,
                fetchInvoiceCodeList,
                fetchInvoiceNumberList,
                recommendInvoiceNumber,
                calculateInvoiceNumbers,
                querySearchInvoiceCode,
                querySearchInvoiceNumber,
                invoiceCodeFilterStr,
                invoiceNumberFilterStr,
                queryPatientLastInvoiceInfo,

                // 发送短信的配置
                invoiceSmsMsgTemplate,
                messageConfig,
                digitalInvoiceOpenStatus,

                // 开票员数据
                operatorList,
                recommendedOperator,
                fetchOperators,
            };
        },
        data() {
            return {
                AbcLogo,
                InvoiceTaxImg,
                InvoiceViewType,
                InvoiceHeaderType,
                EInvoicePushMode,
                InvoiceCategoryTextShort,
                InvoiceStatus,
                InvoiceCategory,
                TaxFullyInvoiceErrorCode,

                showLoading: false,
                showLoadingTitle: '正在提交开票申请',
                listLoading: true,

                hasInvoiceRecords: 0,
                normalInvoiceType: '',
                macAddress: '',
                visible: false,
                loading: false,
                initDataLoading: false,
                showNumInvoice: false,
                lastFailedErrorMessage: '',
                lastFailedErrorCode: '',
                selectedItemId: '',

                selectedInvoiceCategory: null,
                invoiceMethod: 0, // 支持开票的类型

                allInvoiceList: [],
                pendingInvoiceList: [],
                processingInvoiceList: [],
                invoiceRecordList: [],

                businessItemDetails: null,
                eMedicalInvoicePreviewData: null,
                eInvoicePreviewData: null,

                printPreviewData: null,
                invoicePageCount: 0,
                digitalInvoice: {
                    buyerName: this.patientInfo.buyerName,
                    pushMode: EInvoicePushMode.SMS,
                    buyerPhone: this.patientInfo.buyerPhone,
                    email: this.patientInfo.email || '',
                    idCard: this.patientInfo.idCard,
                    buyerType: InvoiceHeaderType.PERSONAL,
                    buyerAccount: '',
                    buyerAddress: '',
                    buyerTaxNum: '',
                    invoiceNumber: '',
                    invoiceCodeNum: '',
                    invoiceUrl: '',
                },
                lastDigitalInvoiceInfo: {},

                normalInvoice: {
                    invoiceManagementId: '',
                    invoiceNumber: '', // 记录多张发票的起始发票号
                    invoiceNumbers: [],
                    invoiceCode: '',
                    // 对应的正数发票
                    blueInvoiceCode: '',
                    blueInvoiceNumbers: '',
                },

                showElectronicSettlementDialog: false,
                buttonLoading: false,

                currentItemId: null,
                currentPreviewItem: null,
                accountValidateDialog: null,
                faceValidateDialog: null,

                invoiceOperatorId: '', //开票员
                invoiceOperatorInfo: '', //开票员详情
                showOpenPasswordDialog: false,

                printISVLoading: false,
            };
        },

        computed: {
            // 没有开通发票，并且是 纸质发票或者财政发票，不允许预览
            disablePaperAndMedicalInvoice() {
                return this.disabledInvoiceFeature && [InvoiceCategory.PAPER, InvoiceCategory.MEDICAL_ELECTRONIC].includes(this.selectedInvoiceCategory);
            },
            // 允许请求数电发票
            allowTaxFullyDigitalInvoice() {
                return this.disabledInvoiceFeature && this.selectedInvoiceCategory === InvoiceCategory.TAX_FULLY_DIGITAL && (this.isCharged || this.isPartRefund);
            },
            // 没有设置纸质发票模版，并且没有开通任何电子发票
            disabledInvoiceFeature() {
                return !this.invoiceConfigList.length && !this.printBillConfig.format;
            },
            showInvoiceFeeType() {
                if (this.selectedInvoiceCategory !== InvoiceCategory.PAPER) {
                    return false;
                }
                return this.printBillConfig[this.printBillConfig.format]?.oneChargeTypePerPage || false;
            },
            newInvoiceGoodsFeeTypeId() {
                return '-999';
            },
            showUploadBtn() {
                return this.isOpenElecSetlCertUpload === 1 &&
                    SupportInvoiceMethodsEnum.ABC !== this.invoiceMethod;
            },
            // 获取当前业务类型下面支持的发票类型
            invoiceTypes() {
                return InvoiceBusinessSceneIncludeInvoiceTypes[this.businessType];
            },
            renderPendingList() {
                if (this.pendingInvoiceList.length) {
                    return [
                        {
                            goodsFeeTypeId: this.newInvoiceGoodsFeeTypeId,
                        },
                    ];
                }
                return [];
            },
            // 已开票列表
            completedInvoiceList() {
                return this.allInvoiceList.filter((item) => {
                    return item.status === InvoiceStatus.SUCCESS;
                });
            }, //是否是挂号发票
            isRegInvoiceType() {
                return this.businessType === InvoiceBusinessScene.REGISTRATION;
            },
            isZhejiangRegInvoiceType() {
                return this.isRegInvoiceType && this.$abcSocialSecurity.config.isZhejiangHangzhou;
            },
            isGuangdongRegInvoice() {
                return this.isRegInvoiceType && this.$abcSocialSecurity.config.isGuangdongZhanjiang;
            },
            isTianjinRegInvoice() {
                return this.isRegInvoiceType && this.$abcSocialSecurity.config.isTianjin;
            },
            /**
             * @desc 挂号发票特殊结构
             * <AUTHOR>
             * @date 2023-10-11 14:25:13
             */
            registrationInvoiceType() {
                return Number(!!this.isZhejiangRegInvoiceType || !!this.isTianjinRegInvoice);
            },
            chargedCanPrint() {
                if (this.isRegInvoiceType) return this.isCharged;
                return this.isCharged || this.isPartRefund;
            },

            invoiceConfigModuleName() {
                return this.isBizPharmacy ? '设置-零售设置-发票设置' : '管理-收费设置-发票设置';
            },
            disabledPrint() {
                if (this.disabledInvoiceFeature) {
                    return {
                        flag: true,
                        tips: `门店未开通发票功能，请先前往【${this.invoiceConfigModuleName}】开通`,
                    };
                }
                if (this.canWriteInvoiceList.length && this.chargedCanPrint && !this.isAnonymousPatient) {
                    if (this.writeInvoiceConfig.normalInvoiceBillStrategy) {
                        if (!this.normalInvoice.invoiceCode || !this.normalInvoice.invoiceNumber || this.invoicePageCount > this.invoiceNumberList?.length) {
                            return {
                                flag: true,
                                tips: '票号不足',
                            };
                        }
                    }
                    return {
                        flag: false,
                        tips: '',
                    };
                }
                return {
                    flag: true,
                    tips: this.isAnonymousPatient ? '匿名患者不可开票' : '重新收费后可开票',
                };

            },
            isNewInvoice() {
                return this.currentItemId === this.newInvoiceGoodsFeeTypeId;
            },
            // 发票记录
            isInvoiceRecord() {
                return !!this.currentPreviewItem?.actionId;
            },
            canChonghongEInvoice() {
                if (this.isLocalWriteInvoice) {
                    return window.electronFlag && this.isFinishedInvoice && this.showInvoiceOperationButtons;
                }
                return this.isFinishedInvoice && this.showInvoiceOperationButtons;
            },
            // 显示作废和冲红按钮
            showInvoiceOperationButtons() {
                return this.currentPreviewItem?.isValid && !this.currentPreviewItem?.type;
            },
            unableWriteInvoice() {
                if (this.isAnonymousPatient) {
                    return true;
                }
                return this.eMedicalInvoicePreviewData?.unablePrintFlag || this.eInvoicePreviewData?.unablePrintFlag;
            },
            // 已收费
            isCharged() {
                if (this.isPeInvoice) {
                    return this.chargeStatus >= PEPayStatusEnum.CHARGED;
                }
                return this.chargeStatus === ChargeStatusEnum.CHARGED || this.payStatus === HospitalChargeStatusEnum.CHARGED;
            },
            // 部分退费
            isPartRefund() {
                return this.chargeStatus === ChargeStatusEnum.PART_REFUND || this.payStatus === HospitalChargeStatusEnum.PART_REFUND;
            },
            // 已收费才能开电子发票
            isCanWriteEInvoice() {
                return (this.isCharged || this.isPartRefund) && this.isCanWriteInvoice;
            },
            /**
             * 显示开票按钮
             */
            isCanWriteInvoice() {
                if (!this.isNewInvoice) return false;
                if (SupportInvoiceMethodsEnum.UPLOAD === this.invoiceMethod) return false;
                if (this.selectedInvoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC && this.isLocalWriteInvoice) {
                    return window.electronFlag && this.pendingInvoiceList.length;
                }
                return this.pendingInvoiceList.length;
            },
            // 当前预览的发票开票成功，能够作废或者冲红
            isFinishedInvoice() {
                return this.currentPreviewItem?.status === InvoiceStatus.SUCCESS;
            },
            digitalInvoiceTips() {
                if (this.disabledInvoiceFeature) {
                    return `门店未开通发票功能，请先前往【${this.invoiceConfigModuleName}】开通`;
                }
                if (this.isAnonymousPatient) {
                    return '匿名患者不可开票';
                }
                if (this.unableWriteInvoice) {
                    return '本单医保收费项目退费未退医保，存在合规风险';
                }
                if (!this.isCharged) {
                    return '重新收费后开票';
                }
                if (this.isLocalWriteInvoice && !window.electronFlag) {
                    return '请在客户端开票';
                }
                return '';
            },
            isAnonymousPatient() {
                return this.patientInfo?.patientId === ANONYMOUS_ID;
            },
            canWriteInvoiceList() {
                return this.pendingInvoiceList.filter((item) => {return item.checked;});
            },
            disabledWriteInvoice() {
                if (this.isAnonymousPatient) {
                    return true;
                }
                if (!this.canWriteInvoiceList.length) {
                    return true;
                }
                return false;
            },
            isEMedicalJiangsuInvoice() {
                const invoiceAreaCode = ['jiangsu', 'jiangsujianye'];
                return invoiceAreaCode.includes(this.eMedicalInvoicePreviewData?.invoiceAreaCode);
            },
            // 内网电子发票
            isLocalNetworkInvoice() {
                return this.selectedInvoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC && this.medicalElectronicAPIConfig.networkMode;
            },
            // 由前端触发开票
            isLocalWriteInvoice() {
                return this.isEMedicalJiangsuInvoice || this.isLocalNetworkInvoice;
            },
            totalInvoiceFee() {
                return this.pendingInvoiceList.filter((item) => {
                    return item.checked;
                }).reduce((acc, item) => {
                    return acc + item.receivedFee;
                }, 0);
            },
            disabledEdit() {
                return !this.isNewInvoice || this.disabledInvoiceFeature;
            },
            // 支持的发票类型
            invoiceTabOptions() {
                let options = [];


                if (this.showPaperInvoice) {
                    options.push({
                        value: InvoiceCategory.PAPER,
                        label: InvoiceCategoryText[InvoiceCategory.PAPER],
                    });
                }

                if (this.isPeInvoice) {
                    options = [];
                }
                if (this.disabledInvoiceFeature) {
                    options.push({
                        label: InvoiceCategoryText[InvoiceCategory.TAX_FULLY_DIGITAL],
                        value: InvoiceCategory.TAX_FULLY_DIGITAL,
                    });
                    if (!this.isBizPharmacy) {
                        options.push({
                            label: InvoiceCategoryText[InvoiceCategory.MEDICAL_ELECTRONIC],
                            value: InvoiceCategory.MEDICAL_ELECTRONIC,
                        });
                    }
                } else {
                    options.push(...this.invoiceConfigList.map((item) => {
                        return {
                            label: InvoiceCategoryText[item.invoiceCategory],
                            value: item.invoiceCategory,
                            ...item,
                        };
                    }));
                    if (this.isBizPharmacy && this.digitalInvoiceOpenStatus) {
                        options = options.filter((item) => item.invoiceCategory === InvoiceCategory.TAX_FULLY_DIGITAL);
                    }
                }
                return options;
            },
            invoiceSupplierId() {
                return this.eMedicalInvoicePreviewData?.invoiceSupplierId || this.eInvoicePreviewData?.invoiceSupplierId;
            },

            // 选择的是纸质发票，如果需要展示纸质发票，没有设置 format 需要去手动设置
            isSetBillPrinter() {
                if (!this.isElectronicInvoice) {
                    return !this.showPaperInvoice || !!this.printBillConfig.format;
                }
                return true;
            },
            // 体检开票
            isPeInvoice() {
                return [InvoiceBusinessScene.PE_CHARGE, InvoiceBusinessScene.PE_CHARGE_GROUP].includes(this.businessType);
            },
            // 团检开票
            isPeGroupInvoice() {
                return this.businessType === InvoiceBusinessScene.PE_CHARGE_GROUP;
            },

            // 已作废
            isRefundInvoice() {
                return this.currentPreviewItem?.displayStatus === InvoiceStatus.REFUND;
            },
            disabledDelete() {
                return [UploadElectronicSettlementInvoiceStatus.REPORTING, UploadElectronicSettlementInvoiceStatus.SUCCESS].includes(this.currentPreviewItem?.exportStatus);
            },
            // 上传电子发票凭证
            isUploadInvoice() {
                return this.currentPreviewItem?.isUploadInvoice;
            },
            // 电子发票开票中
            isBeingInvoice() {
                return this.currentPreviewItem?.status === InvoiceStatus.CREATING;
            },
            isPaperInvoice() {
                return this.selectedInvoiceCategory === InvoiceCategory.PAPER;
            },
            defaultInvoiceCategory() {
                return this.writeInvoiceConfig.invoiceCategory;
            },
            //  电子发票
            isElectronicInvoice() {
                return !this.isPaperInvoice;
            },
            isMedicalElInvoice() {
                return this.selectedInvoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC;
            },
            // 医院住院发票
            isHisHospitalInvoice() {
                return this.businessType === InvoiceBusinessScene.HIS_HOSPITAL;
            },

            // 是否支持电子发票
            isSupportEInvoice() {
                return this.invoiceTabOptions.find((item) => {
                    return [InvoiceCategory.MEDICAL_ELECTRONIC, InvoiceCategory.ELECTRONIC, InvoiceCategory.TAX_FULLY_DIGITAL].includes(item.value);
                });
            },
            /**
             * @description: 发票预览需要显示发票号的地区
             * @author: ff
             * @date: 2024/4/22
             * @params
             * @return
             */
            needShowInvoiceNumber() {
                return ['qingdao'].includes(this.printBillConfig?.format);
            },
            // 数族
            isShuzu() {
                return this.invoiceSupplierId === InvoiceSupplierId.ISV;
            },
            /**
             * @desc: 是否展示开票员下拉
             * @author: ff
             * @time: 2025/5/12
             */
            showInvoiceOperator() {
                return this.isShuzu && !this.disabledInvoiceFeature && (this.canChonghongEInvoice || this.isRedInvoiceOpening || this.isCanWriteEInvoice);
            },
            supportShuzu() {
                return this.invoiceConfigList.find((item) => {
                    return item.invoiceCategory === InvoiceCategory.TAX_FULLY_DIGITAL && item.invoiceSupplierId === InvoiceSupplierId.ISV;
                });
            },
            /*
            @desc: 冲红确认中
            @author: ff
            @time: 2025/3/2
            */
            isRedInvoiceOpening() {
                return this.currentPreviewItem && this.currentPreviewItem.type === InvoiceViewType.RED && this.isBeingInvoice && this.isShuzu;
            },
            /*
            @desc: 是否支持发送短信
            @author: ff
            @time: 2025/3/17
            */
            isSupportSendSms() {
                return this.invoiceSmsMsgTemplate?.smsSwitch;
            },
            disabledEditSms() {
                return this.digitalInvoice.pushMode === EInvoicePushMode.SMS && (!this.isSupportSendSms || !this.isSmsEnoughBalance) && this.isShuzu;
            },
            isSmsEnoughBalance() {
                return this.messageConfig?.smsBalance >= this.messageConfig?.priceEachSms;
            },
            sendSmsMessageTips() {
                if (!this.isNewInvoice) {
                    return '';
                }
                if (!this.isSupportSendSms) {
                    return '未开通短信通知，请前往总部【营销-消息推送】设置';
                }
                if (!this.isSmsEnoughBalance) {
                    return '短信余额不足，请前往总部【营销-消息推送】充值';
                }
                return '';
            },
            passwordOperatorId() {
                return this.invoiceOperatorInfo?.id;
            },
        },

        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },

        async mounted() {
            this.invoiceService = new InvoiceService();
            this.normalInvoiceType = this.invoiceTypes[0];

            await this.initMacAddress();

            //  查询开票上次异常数据
            this.queryInvoiceResultByBusinessId();

            this.initInvoiceCategory();
            this.init();

            // 支持电子发票获取上次开票抬头
            if (this.isSupportEInvoice && !this.disabledInvoiceFeature) {
                this.fetchPatientLastInvoiceInfo();
            }
            on(document, 'keydown', this.handleEnterEvent);

            this.initTaxElectronInvoiceOpenerList();
        },

        beforeDestroy() {
            off(document, 'keydown', this.handleEnterEvent);
            if (this.isUpdateInvoiceStatus) {
                this.updateInvoice();
            }
            clearInterval(this._timer);
        },
        methods: {
            navigate,
            parseTime,
            async init() {
                this.loading = true;
                if (!this.disabledInvoiceFeature || this.allowTaxFullyDigitalInvoice) {
                    await this.initInvoiceList();
                    await this.initPreviewData();
                }
                this.loading = false;
                await this.$nextTick();
                this.initListSelected();
            },

            async handleLodopInstall() {
                getMacAddress(true).then(async () => {
                    await this.initMacAddress();
                    if (this.selectedInvoiceCategory === InvoiceCategory.PAPER) {
                        this.getSuggestInvoiceNumber();
                    }
                });
            },
            async handleChangeInvoiceCategory(val, index, oldVal) {
                if (val !== oldVal) {
                    this.loading = true;
                    this.businessItemDetails = null;
                    await this.init();
                    this.loading = false;
                }
            },
            async initMacAddress() {
                const { getMacAddressValue } = useGetInvoiceMacAddress();
                this.macAddress = await getMacAddressValue();
                if (!this.macAddress && !this.isElectronicInvoice) {
                    this.handleLodopInstall();
                }
            },
            async initPaperInvoice() {
                if (this.isElectronicInvoice) return;
                await this.initMacAddress();
                if (this.macAddress) {
                    await this.queryInvoiceCodeList();
                    await this.getSuggestInvoiceNumber();
                }
            },

            async handleChangeInvoiceChecked() {
                this.initPreviewData();
            },

            getInvoiceType(invoiceItem) {
                if (invoiceItem.status === InvoiceStatus.REFUND && invoiceItem.invoiceCategory === InvoiceCategory.PAPER) {
                    return '已作废';
                }
                if (invoiceItem.type === InvoiceViewType.BLUE) {
                    return '已开票';
                }
                if (invoiceItem.type === InvoiceViewType.RED) {
                    return '已冲红';
                }
                return '';
            },
            /*
            @desc: 获取最近开出的电子发票的抬头信息
            @author: ff
            @time: 2025/2/26
            */
            async fetchPatientLastInvoiceInfo() {
                if (!this.patientInfo?.patientId || this.patientInfo?.patientId === ANONYMOUS_ID) return;
                const data = await this.queryPatientLastInvoiceInfo(this.patientInfo.patientId);

                if (data) {
                    this.lastDigitalInvoiceInfo = data;

                }
            },
            initDigitalInvoice(data) {
                Object.assign(this.digitalInvoice, data);

                if (this.disabledEditSms) {
                    this.digitalInvoice.buyerPhone = '';
                }
            },
            //  初始化开票类型
            initInvoiceCategory() {
                if (this.disabledInvoiceFeature) {
                    this.selectedInvoiceCategory = InvoiceCategory.TAX_FULLY_DIGITAL;
                    return;
                }
                if (this.isPeInvoice) {
                    this.digitalInvoice.buyerType = this.businessType === InvoiceBusinessScene.PE_CHARGE_GROUP ? InvoiceHeaderType.ENTERPRISE : InvoiceHeaderType.PERSONAL;
                    // 体检只开 诺诺发票
                    this.selectedInvoiceCategory = InvoiceCategory.ELECTRONIC;
                } else {
                    this.selectedInvoiceCategory = this.defaultInvoiceCategory;
                }
                if (this.isPeInvoice || !this.showPaperInvoice) {
                    this.selectedInvoiceCategory = this.invoiceTabOptions[0]?.value || null;
                }
            },
            /*
            @desc: 获取代开发票列表
            @author: ff
            @time: 2025/2/26
            */
            async initInvoiceList() {
                try {
                    this.listLoading = true;
                    this.pendingInvoiceList = [];
                    const { data } = await InvoiceAPI.fetchInvoiceItemList(this.chargeSheetId, this.businessType, this.selectedInvoiceCategory, this.registrationInvoiceType);

                    this.selectedInvoiceCategory = data.invoiceCategory;
                    this.hasInvoiceRecords = data.hasInvoiceRecords;
                    this.invoiceMethod = data.invoiceMethod;

                    this.allInvoiceList = data.rows || [];

                    this.pendingInvoiceList = data.rows?.filter((item) => {
                        return item.status === InvoiceStatus.WAITING;
                    }).map((item) => {
                        return {
                            ...item,
                            checked: true,
                        };
                    }) || [];
                    this.processingInvoiceList = data.rows?.filter((item) => {
                        return item.status === InvoiceStatus.CREATING;
                    }) || [];

                    if (!this.disabledInvoiceFeature) {
                        await this.fetchInvoiceActions();
                    }

                    let previewItem = null;
                    // 只有开票记录选中开票记录第一个选择
                    if (this.invoiceRecordList?.length) {
                        this.currentItemId = this.invoiceRecordList[0].actionId;
                        previewItem = this.invoiceRecordList[0];
                    }

                    // 选中开票中的数据
                    if (this.processingInvoiceList.length) {
                        this.currentItemId = this.processingInvoiceList[0].goodsFeeTypeId;
                        previewItem = this.processingInvoiceList[0];
                    }

                    // 选中新开发票
                    if (this.pendingInvoiceList.length) {
                        this.currentItemId = this.newInvoiceGoodsFeeTypeId;
                        previewItem = null;
                    }
                    this.currentPreviewItem = previewItem;

                } catch (e) {
                    console.error('获取开票项目数据失败',e);
                    this.pendingInvoiceList = [];
                    this.processingInvoiceList = [];
                } finally {
                    this.listLoading = false;
                }
            },
            initListSelected() {
                // 只有开票记录选中开票记录第一个选择
                if (this.invoiceRecordList?.length) {
                    this.$refs.invoiceRecordListRef?.setSelectedKey(this.currentItemId);
                    this.$refs.processingInvoiceListRef?.setSelectedKey(null);
                    this.$refs.pendingInvoiceListRef?.setSelectedKey(null);
                }

                // 选中开票中的数据
                if (this.processingInvoiceList.length) {
                    this.$refs.processingInvoiceListRef?.setSelectedKey(this.currentItemId);
                    this.$refs.invoiceRecordListRef?.setSelectedKey(null);
                    this.$refs.pendingInvoiceListRef?.setSelectedKey(null);
                }

                // 选中新开发票
                if (this.pendingInvoiceList.length) {
                    this.$refs.pendingInvoiceListRef?.setSelectedKey(this.currentItemId);
                    this.$refs.invoiceRecordListRef?.setSelectedKey(null);
                    this.$refs.processingInvoiceListRef?.setSelectedKey(null);
                }
            },

            async fetchInvoiceActions() {
                try {
                    const { data } = await InvoiceAPI.getInvoiceActions(this.chargeSheetId, this.businessType);
                    this.invoiceRecordList = data?.rows || [];
                } catch (e) {
                    console.error('获取开票记录失败', e);
                    this.invoiceRecordList = [];
                }
            },
            async initPreviewData() {
                let previewList = [];
                this.businessItemDetails = null;
                if (!this.currentPreviewItem && !this.canWriteInvoiceList.length) {
                    this.initDataLoading = false;
                    this.loading = false;
                    return;
                }

                // 开票记录
                if (this.currentPreviewItem?.actionId) {
                    this.handleResetEInvoice();
                    // 当前查看的是开票记录里面的票面，调用另外一个接口请求预览数据
                    await this.fetchPrintPreviewByRecordId();
                    return;
                }

                // 新开发票
                if (this.currentItemId === this.newInvoiceGoodsFeeTypeId) {
                    if (this.canWriteInvoiceList.length) {
                        this.initDataLoading = true;
                        previewList = this.canWriteInvoiceList;
                        await this.fetchPrintPreview(previewList);
                        if (this.isPaperInvoice) {
                            await this.initPaperInvoice();
                        } else {
                            this.initDigitalInvoice(this.lastDigitalInvoiceInfo);
                        }
                        this.initDataLoading = false;
                        return;
                    }
                }
                // 开票中
                this.handleResetEInvoice();
                previewList = [this.currentPreviewItem];
                await this.fetchPrintPreview(previewList);
                if (this.businessItemDetails && this.businessItemDetails.length) {
                    this.initInvoiceData(this.businessItemDetails[0]);
                }
            },
            handleResetEInvoice() {
                this.digitalInvoice.buyerPhone = this.patientInfo.buyerPhone;
                this.digitalInvoice.buyerName = this.patientInfo.buyerName;
                this.digitalInvoice.email = '';
                this.digitalInvoice.pushMode = EInvoicePushMode.SMS;
                if (this.isLocalWriteInvoice) {
                    this.digitalInvoice.pushMode = 0;
                } else if (this.selectedInvoiceCategory === InvoiceCategory.ELECTRONIC && this.recommendDigitalInvoice) {
                    Object.assign(this.digitalInvoice, this.recommendDigitalInvoice);
                }
            },
            createPostData(list, type) {
                const goodsFeeTypeIds = list.map((item) => {
                    return item.goodsFeeTypeId;
                });
                return {
                    businessScene: this.businessType,
                    goodsFeeTypeIds: clone(goodsFeeTypeIds),
                    invoiceCategory: this.selectedInvoiceCategory,
                    opType: type,
                    registrationInvoiceType: this.registrationInvoiceType,
                };
            },
            async fetchPrintPreview(list, type = InvoiceViewType.BLUE) {
                try {
                    const postData = this.createPostData(list, type);
                    const preWaitInvoiceList = clone(this.canWriteInvoiceList);
                    const preCurrentPreviewItem = clone(this.currentPreviewItem);

                    // 开票，触发打印，上报数据
                    Logger.report({
                        scene: 'write-paper-invoice-print',
                        data: {
                            info: '获取打印预览数据',
                            postData: JSON.stringify(postData),
                            preWaitInvoiceList: JSON.stringify(preWaitInvoiceList),
                            preCurrentPreviewItem: JSON.stringify(preCurrentPreviewItem),
                            chargeSheetId: this.chargeSheetId,
                        },
                    });

                    const { businessItemDetails } = await this.invoiceService.fetchPrintDataPreview(this.chargeSheetId, postData);
                    const curPostData = this.createPostData(list, type);

                    if (isEqual(postData, curPostData) && isEqual(this.canWriteInvoiceList, preWaitInvoiceList) && isEqual(preCurrentPreviewItem, this.currentPreviewItem)) {
                        this.businessItemDetails = businessItemDetails;
                        this.eMedicalInvoicePreviewData = this.businessItemDetails[0].medicalDigitalInvoicePreview;
                        this.eInvoicePreviewData = this.businessItemDetails[0].digitalInvoicePreview;
                        this.printPreviewData = this.getRenderPreviewData();
                        Logger.report({
                            scene: 'write-paper-invoice-print',
                            data: {
                                info: '预览数据更新',
                                chargeSheetId: this.chargeSheetId,
                                printPreviewData: JSON.stringify(this.printPreviewData),
                            },
                        });
                    }
                } catch (e) {
                    console.error('获取预览数据失败',e);
                }
            },


            /**
             * @desc 预览开票记录的发票
             * <AUTHOR>
             * @date 2023-10-18 16:16:14
             * @params
             * @return
             */
            async fetchPrintPreviewByRecordId() {
                try {
                    this.loading = true;
                    const { data } = await InvoiceAPI.fetchInvoiceByRecordId(this.currentPreviewItem.invoiceRecordId, this.registrationInvoiceType);
                    this.businessItemDetails = data.businessItemDetails;
                    this.eMedicalInvoicePreviewData = this.businessItemDetails[0].medicalDigitalInvoicePreview;
                    this.eInvoicePreviewData = this.businessItemDetails[0].digitalInvoicePreview;

                    this.initInvoiceData(this.businessItemDetails[0]);

                    this.printPreviewData = this.getRenderPreviewData();

                    this.$nextTick(() => {
                        if (this.isPaperInvoice) {
                            this.initMacAddress();
                        }
                    });
                } catch (e) {
                    console.warn('获取发票开票记录失败', e);
                } finally {
                    this.loading = false;
                }
            },
            initInvoiceData(invoice) {
                const {
                    digitalInvoicePreview,
                    medicalDigitalInvoicePreview,
                } = invoice;

                let tempDigitalInvoice = null;
                if ([InvoiceCategory.ELECTRONIC, InvoiceCategory.TAX_FULLY_DIGITAL].includes(invoice.invoiceCategory)) {
                    tempDigitalInvoice = digitalInvoicePreview;
                }
                if (invoice.invoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC) {
                    tempDigitalInvoice = medicalDigitalInvoicePreview;
                }
                if (tempDigitalInvoice) {
                    const {
                        buyerName,
                        pushMode,
                        buyerPhone,
                        email,
                        buyerType,
                        buyerAccount,
                        buyerAddress,
                        buyerTaxNum,
                        invoiceNumber,
                        invoiceUrl,
                        invoiceCodeNum,
                    } = tempDigitalInvoice;

                    Object.assign(this.digitalInvoice, {
                        buyerName,
                        pushMode,
                        buyerType,
                        buyerPhone,
                        email,
                        buyerAccount,
                        buyerAddress,
                        buyerTaxNum,
                        invoiceNumber,
                        invoiceUrl,
                        invoiceCodeNum,
                    });
                }

                this.invoiceCategory = invoice.invoiceCategory;
                this.selectedInvoiceCategory = invoice.invoiceCategory;
                this.normalInvoice.invoiceNumbers = invoice.invoiceNumbers?.split(',') || [];
                this.normalInvoice.invoiceNumber = this.normalInvoice.invoiceNumbers[0];
                this.normalInvoice.invoiceCode = invoice.invoiceCode;
            },

            getRenderPreviewData() {
                if (this.isMedicalElInvoice) {
                    return {
                        ...this.eMedicalInvoicePreviewData,
                        renderInvoiceData: {
                            // 只有南京电子发票使用到了这两个字段
                            invoiceCodeNum: this.digitalInvoice.invoiceCodeNum,
                            invoiceNumber: this.digitalInvoice.invoiceNumber,
                        },
                        ...{
                            IS_HOSPITAL: this.isHisHospitalInvoice,
                        },
                    };
                }
                if (this.isElectronicInvoice) {
                    return this.eInvoicePreviewData;
                }
                let medicalBills = [];
                const printData = this.businessItemDetails[0]?.normalInvoicePreviews[0] || {};
                this.businessItemDetails.forEach((businessItem) => {
                    businessItem.normalInvoicePreviews.forEach((item) => {
                        medicalBills = medicalBills.concat(item.medicalBills);
                    });
                });
                return {
                    ...printData,
                    medicalBills,
                };
            },
            handleClickDestroy() {
                this.$confirm({
                    type: 'warn',
                    title: '是否确认作废此发票？',
                    content: ['作废后，该发票号将在系统中标记作废，请回收纸质发票，并标记作废。',
                              `作废发票代码：${this.normalInvoice.invoiceCode || '-'}`,
                              `作废发票号码：${this.normalInvoice.invoiceNumbers.join('，') || '-'}`],
                    onConfirm: async () => {

                        await this.invoiceService.destroyInvoice(this.chargeSheetId, {
                            businessScene: this.businessType,
                            goodsFeeTypeId: this.currentPreviewItem.goodsFeeTypeId,
                        });
                        this.init();

                        this.isUpdateInvoiceStatus = true;

                        this.$Toast({
                            message: '发票已作废',
                            type: 'success',
                        });
                    },
                });
            },
            handleEnterEvent(e) {
                if (e.target.tagName === 'INPUT') return;
                if (this.showElectronicSettlementDialog) {
                    return;
                }
                const KEY_ENTER = 13;
                const KEY_ESC = 27;
                if (e.keyCode === KEY_ENTER) {
                    e.preventDefault();
                    e.stopPropagation();
                    if (this.disabledInvoiceFeature ||
                        !this.invoicePageCount ||
                        this.loading ||
                        !this.isCanWriteInvoice ||
                        this.buttonLoading ||
                        this.showLoading ||
                        this._message ||
                        !!this.accountValidateDialog ||
                        !!this.faceValidateDialog) return;
                    if (this.isPaperInvoice) {
                        if (!this.disabledPrint.flag) {
                            this.handleClickPrint();
                        }
                    } else {
                        if (this.isCanWriteEInvoice) {
                            this.handleSubmitEInvoicePreCheck();
                        }
                    }

                }
                if (e.keyCode === KEY_ESC) {
                    if (!!this.accountValidateDialog ||
                        !!this.faceValidateDialog) {
                        return;
                    }
                    this.visible = false;
                }
            },

            open(url) {
                if (window.require?.('electron')) {
                    window.require('electron').shell.openExternal(url);
                } else {
                    windowOpen(url);
                }
            },


            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },

            writeCompleteHandler() {
                this.visible = false;
            },

            handleClose() {
                this.visible = false;
                if (typeof this.resolvePromise.resolve === 'function') {
                    this.resolvePromise.resolve();
                }
                if (typeof this.onClose === 'function') {
                    this.onClose();
                }
            },

            handleCreateNumber() {
                this.showNumInvoice = true;
            },

            async handleOpenUploadESettlementDialog() {
                if (this.showElectronicSettlementDialog) return;
                this.showElectronicSettlementDialog = true;
                const invoiceItems = [];
                if (this.isPaperInvoice) {
                    this.businessItemDetails?.forEach((item) => {
                        item.normalInvoicePreviews.forEach((normalInvoice) => {
                            invoiceItems.push({
                                goodsFeeTypeId: item.goodsFeeTypeId,
                                invoiceFee: normalInvoice.invoiceFee,
                            });
                        });
                    });
                } else {
                    this.businessItemDetails?.forEach((item) => {
                        invoiceItems.push({
                            goodsFeeTypeId: item.goodsFeeTypeId,
                            invoiceFee: this.isMedicalElInvoice ? item.medicalDigitalInvoicePreview?.invoiceFee : item.digitalInvoicePreview?.invoiceFee,
                        });
                    });
                }

                await new AbcElectronicSettlementUploadDialog({
                    chargeSheetId: this.chargeSheetId,
                    businessScene: this.businessType,
                    patient: this.patientInfo,
                    invoiceItems,
                    isOpenInvoice: this.isOpenInvoice,
                    isOpenMedicalInvoice: this.isOpenMedicalInvoice,
                    onSuccess: async () => {
                        this.init();
                        this.showElectronicSettlementDialog = false;
                    },
                    onCancel: () => {
                        this.showElectronicSettlementDialog = false;
                    },
                }).generateDialogAsync();

            },

            async queryInvoiceResultByBusinessId() {
                try {
                    const { data } = await InvoiceAPI.queryInvoiceResultByBusinessId(this.chargeSheetId,{
                        businessScene: this.businessType,
                    });
                    if (data) {
                        this.lastFailedErrorMessage = data.invoiceFailReason;
                        this.lastFailedErrorCode = data.invoiceFailReasonCode;
                        if (![ `${TaxFullyInvoiceErrorCode.NEED_LOGIN_FAIL}`, `${TaxFullyInvoiceErrorCode.FACE_TIMEOUT}`].includes(`${this.lastFailedErrorCode}`)) {
                            this.lastFailedErrorCode = '';
                        }

                    }
                    return data || {};
                } catch (e) {
                    Logger.report({
                        scene: 'OPEN_INVOICE',
                        data: {
                            info: '获取上次开票接口报错',
                            err: e,
                        },
                    });
                    return null;
                }
            },
            async handleCloseError() {
                try {
                    if (`${this.lastFailedErrorCode}` === `${TaxFullyInvoiceErrorCode.NEED_LOGIN_FAIL}`) {
                        this.accountValidateDialog = new AccountValidateDialog({
                            employeeId: this.userInfo?.id,
                            showTips: false,
                            employee: this.invoiceOperatorInfo,
                            onFinish: () => {
                                this.accountValidateDialog = null;
                            },
                        }).generateDialogAsync();
                    } else if (`${this.lastFailedErrorCode}` === `${TaxFullyInvoiceErrorCode.FACE_TIMEOUT}`) {
                        this.faceValidateDialog = new FaceValidateDialog({
                            showTips: false,
                            employee: this.invoiceOperatorInfo,
                            onFinish: () => {
                                this.faceValidateDialog = null;
                            },
                        }).generateDialogAsync();
                    }
                } catch (e) {
                    console.error('关闭错误失败', e);
                }
            },
            changePrintDisabled() {
                this.loading = true;
            },
            changePrintEnabled() {
                this.loading = false;
            },
            /**
             * @desc 获取本次打印需要的发票张数
             */
            async handleGetBillPages(numbers) {
                this.invoicePageCount = numbers;
                if (this.isCanWriteInvoice) {
                    await this.$nextTick();
                    this.calcInvoiceNo();
                    await this.updatePreview();
                    Logger.report({
                        scene: 'write-paper-invoice-print',
                        data: {
                            info: 'handleGetBillPages/计算票号',
                            chargeSheetId: this.chargeSheetId,
                            normalInvoice: JSON.stringify(this.normalInvoice),
                        },
                    });
                }
            },
            async updatePreview() {
                if (!this.needShowInvoiceNumber) return;
                this.loading = true;
                await this.$nextTick();
                await this.$refs.invoicePreview?.updatePreview();
                this.loading = false;
            },
            getTemplateName() {
                const { format } = this.printBillConfig;
                if (!format) {
                    return console.error('没有format，无法挂载AbcPint');
                }
                let templateName = `medicalBill${format[0].toUpperCase()}${format.slice(1)}`;
                if (this.isEInvoice) {
                    templateName = 'eInvoice';
                }
                if (this.isHisHospitalInvoice) {
                    templateName = `medicalHospitalBill${format[0].toUpperCase()}${format.slice(1)}`;
                }
                if (this.isZhejiangRegInvoiceType) {
                    templateName = 'zhejiangInvoice';
                }
                if (this.isGuangdongRegInvoice) {
                    templateName = 'guangdongRegInvoice';
                }
                if (this.isTianjinRegInvoice) {
                    templateName = 'tianjinRegInvoice';
                }

                return templateName;
            },

            async handleDevToolsPreviewInvoice() {
                await AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates[this.getTemplateName()],
                    printConfigKey: ABCPrintConfigKeyMap.bill,
                    data: {
                        ...this.printPreviewData,
                        normalInvoice: this.normalInvoice || {},
                    },
                    extra: {
                        $abcSocialSecurity: this.$abcSocialSecurity,
                    },
                    isDevTools: true,
                    matchTemplateCallback: ({ template: { templates = [] } }) => {
                        return matchTemplate(this.printBillConfig.format, templates, this.printBillConfig);
                    },
                    matchRecommendPagesizeCallback: (pageSizeList) => {
                        return matchPageSize(pageSizeList, this.printBillConfig, this.printBillConfig.format);
                    },
                });
            },
            async handleCloseLastError(forceClose = false) {
                if (this.lastFailedErrorMessage || forceClose) {
                    try {
                        await InvoiceAPI.closeInvoiceError({
                            businessScene: this.businessType,
                            businessId: this.chargeSheetId,
                        });
                        this.lastFailedErrorMessage = '';
                    } catch (e) {
                        console.error('关闭上次开票错误报错', e);
                    }
                }
            },
            handleSubmitEInvoicePreCheck() {
                this.$refs.invoiceFormRef.validate(async (valid) => {
                    if (valid) {
                        await this.submitElectronicInvoice();
                    }
                });
            },
            /**
             * @desc 提交电子发票
             */
            async submitElectronicInvoice() {
                try {
                    this.buttonLoading = true;
                    this.handleCloseLastError();

                    if (this.isShuzu) {
                        this.showLoading = true;
                        this.showLoadingTitle = '正在提交开票申请';
                    } else {
                        this._message = this.$message({
                            referenceEl: this.$parent.$el.querySelector('.invoice-modal-wrapper'),
                            type: 'loading',
                            title: '正在申请开票',
                            dialogType: 'tiny',
                            dialogContentStyles: 'width: 240px; min-width: 240px; min-height: auto',
                            cancelText: '关闭',
                            onCancel: this.completeHandler,
                            showConfirm: false,
                            showClose: false,
                            noDialogAnimation: true,
                        });
                    }
                    const invoiceItems = [];
                    this.businessItemDetails.forEach((item) => {
                        invoiceItems.push({
                            goodsFeeTypeId: item.goodsFeeTypeId,
                            invoiceFee: this.isMedicalElInvoice ? item.medicalDigitalInvoicePreview.invoiceFee : item.digitalInvoicePreview.invoiceFee,
                        });
                    });
                    const digitalInvoice = {
                        ...this.digitalInvoice,
                    };
                    const res = await this.invoiceService.writeInvoice(this.chargeSheetId, {
                        invoiceType: this.normalInvoiceType,
                        businessScene: this.businessType,
                        invoiceCategory: this.selectedInvoiceCategory,
                        digitalInvoice,
                        buyerName: this.digitalInvoice.buyerName,
                        buyerTaxNum: this.digitalInvoice.buyerTaxNum,
                        buyerType: this.digitalInvoice.buyerType,
                        patientId: this.patientInfo?.patientId,
                        clerkId: this.invoiceOperatorId,
                        invoiceItems,
                    });
                    // 前端触发开票
                    if (this.isLocalWriteInvoice) {
                        const invoiceData = await this.invoiceService.fetchInvoiceData(res.businessId, this.businessType);
                        const localInvoice = new LocalInvoice.getInstance(this.eMedicalInvoicePreviewData.invoiceSupplierId, this.medicalElectronicAPIConfig);
                        const { createInvoiceReqToVendor } = invoiceData;

                        const invoiceInfo = await localInvoice.writeInvoice(createInvoiceReqToVendor);
                        if (invoiceInfo?.success) {
                            await this.updateInvoiceResult(res.businessId, invoiceInfo.data);
                        } else {
                            throw new Error(invoiceInfo.message);
                        }

                        if (localInvoice.queryInvoice) {
                            // 查询 HIS 后台返回的查询参数
                            // 调三方接口 获取票据详情
                            // 保存结果
                            await sleep(1000);
                            const queryInvoiceData = await this.invoiceService.fetchInvoiceData(res.businessId, this.businessType);
                            const { queryInvoiceReqToVendor } = queryInvoiceData;
                            const queryInvoiceInfo = await localInvoice.queryInvoice(queryInvoiceReqToVendor);
                            if (queryInvoiceInfo?.success) {
                                await this.updateInvoiceResult(res.businessId, queryInvoiceInfo.data);
                            } else {
                                throw new Error(queryInvoiceInfo.message);
                            }
                        }
                    }

                    this.isUpdateInvoiceStatus = true;
                    if (!this.isLocalWriteInvoice) {
                        if (!this.isShuzu) {
                            const timer = setTimeout(() => {
                                if (this._message.messageType === 'loading') {
                                    this._message.title = '请稍后查看开票结果';
                                }
                                clearTimeout(timer);
                            }, 5000);
                        }
                        this.queryInvoiceStatus(false);
                    } else {
                        this._message?.close();
                        this.completeHandler();
                    }
                } catch (e) {
                    if (this.isShuzu) {
                        this.handleCloseLastError();
                    }
                    const { message } = e;
                    if (this._message) {
                        this._message.messageType = 'warn';
                        this._message.title = '开票失败';
                        this._message.content = [message || ''];
                        this._message.confirmFunc = this.completeHandler;
                        this._message.cancelFunc = null;
                    }
                    this.isUpdateInvoiceStatus = false;
                    this.showLoading = false;
                } finally {
                    this.buttonLoading = false;
                }
            },
            async handleChonghongEInvoice() {

                if (this.$abcSocialSecurity.config.isCheckElecBillStatus) {
                    const { checkElectronicSettlementStatus } = useCheckElectronicSettlement();
                    const {
                        status: isEnableRefund, reimFlag,
                    } = await checkElectronicSettlementStatus(this.chargeSheetId, 'invoice');
                    if (!isEnableRefund) {
                        return;
                    }
                    if (isEnableRefund && reimFlag === ReimbursementElectronicSocialCenterStatus.REPORTING) {
                        this.$confirm({
                            type: 'warn',
                            title: '医保中心提示',
                            content: '当前发票状态为「报销中」，建议红冲前先与患者确认，如果医保已经报销，请勿红冲。',
                            confirmText: '继续红冲',
                            cancelText: '暂不红冲',
                            size: 'small',
                            onConfirm: async () => {
                                await this.chonghongEInvoice();
                            },
                        });
                        return;
                    }
                }
                await this.chonghongEInvoice();
            },
            async chonghongEInvoice() {
                this.$confirm({
                    type: 'warn',
                    title: '冲红发票',
                    content: '发票冲红后，已开发票将失去报账作用，收费单可以重新开票，是否确定冲红？',
                    onConfirm: async () => {
                        try {
                            if (this.isShuzu) {
                                this.showLoading = true;
                                this.showLoadingTitle = '正在提交红冲申请';
                            } else {
                                this._message = this.$message({
                                    referenceEl: this.$parent.$el.querySelector('.invoice-modal-wrapper'),
                                    type: 'loading',
                                    title: '正在申请开票',
                                    dialogType: 'tiny',
                                    dialogContentStyles: 'width: 240px; min-width: 240px; min-height: auto',
                                    cancelText: '关闭',
                                    onCancel: this.completeHandler,
                                    showConfirm: false,
                                    showClose: false,
                                    noDialogAnimation: true,
                                });
                            }
                            const invoiceItems = [];
                            this.businessItemDetails.forEach((item) => {
                                invoiceItems.push({
                                    goodsFeeTypeId: item.goodsFeeTypeId,
                                    invoiceFee: this.printPreviewData.invoiceFee,
                                });
                            });

                            const data = await this.invoiceService.chonghongInvoice(
                                this.chargeSheetId, {
                                    invoiceCode: this.digitalInvoice.invoiceCodeNum,
                                    invoiceNumber: this.digitalInvoice.invoiceNumber,
                                    patientId: this.patientInfo.patientId,
                                    invoiceItems,
                                    businessScene: this.businessType,
                                    buyerName: this.digitalInvoice.buyerName,
                                    buyerTaxNum: this.digitalInvoice.buyerTaxNum,
                                    buyerType: this.digitalInvoice.buyerType,
                                    invoiceCategory: this.selectedInvoiceCategory,
                                    clerkId: this.invoiceOperatorId,
                                    registrationInvoiceType: Number(this.isZhejiangRegInvoiceType || this.isTianjinRegInvoice),
                                },
                            );
                            if (this.isLocalWriteInvoice) {
                                const invoiceData = await this.invoiceService.fetchInvoiceData(data.businessId, this.businessType);
                                const localInvoice = new LocalInvoice.getInstance(this.eMedicalInvoicePreviewData.invoiceSupplierId, this.medicalElectronicAPIConfig);

                                const { chonghongInvoiceReqToVendor } = invoiceData;
                                const invoiceInfo = await localInvoice.chonghongInvoice(chonghongInvoiceReqToVendor);
                                if (invoiceInfo?.success) {
                                    await this.updateInvoiceResult(data.businessId, invoiceInfo.data,1);
                                    this.init();
                                } else {
                                    throw new Error(invoiceInfo.message);
                                }

                                if (localInvoice.queryInvoice) {
                                    // 查询 HIS 后台返回的查询参数
                                    // 调三方接口 获取票据详情
                                    // 保存结果
                                    await sleep(1000);
                                    const queryInvoiceData = await this.invoiceService.fetchInvoiceData(data.businessId, this.businessType);
                                    const { queryInvoiceReqToVendor } = queryInvoiceData;
                                    const queryInvoiceInfo = await localInvoice.queryInvoice(queryInvoiceReqToVendor);
                                    if (queryInvoiceInfo?.success) {
                                        await this.updateInvoiceResult(data.businessId, queryInvoiceInfo.data, 1);
                                    } else {
                                        throw new Error(queryInvoiceInfo.message);
                                    }
                                }

                                this.isUpdateInvoiceStatus = true;
                                this.$Toast({
                                    message: '发票已冲红',
                                    type: 'success',
                                });
                                this._message?.close();
                                this.showLoading = false;
                            } else {
                                const timer = setTimeout(() => {
                                    if (this._message?.messageType === 'loading') {
                                        this._message.title = '请稍后查看开票结果';
                                    }
                                    clearTimeout(timer);
                                }, 5000);
                                this.queryInvoiceStatus(true);
                            }
                        } catch (e) {
                            console.error('冲红报错', e);
                            if (this.isLocalWriteInvoice) {
                                this.$confirm({
                                    title: '冲红失败',
                                    type: 'warn',
                                    content: e?.message,
                                    showClose: true,
                                });
                                this._message.close();
                            }
                        }
                    },
                });
            },
            async updateInvoiceResult(businessId, invoiceInfo,type = 0) {
                await this.invoiceService.updateInvoiceResult({
                    businessId,
                    type, // 0 蓝票  1 红票
                    businessScene: this.businessType,
                    invoiceCategory: this.selectedInvoiceCategory,
                    invoiceCreateResult: invoiceInfo, // 兼容老字段
                    response: this.eMedicalInvoicePreviewData.invoiceSupplierId === InvoiceSupplierId.HEBEI_XINHE ? invoiceInfo : '',
                    invoiceSupplierId: this.eMedicalInvoicePreviewData.invoiceSupplierId,
                });
            },

            async handleDeleteInvoice() {
                try {
                    this.$confirm({
                        type: 'warn',
                        title: '删除确认',
                        content: '是否确认删除该发票？（关联的正常票/冲红票也将一并删除）',
                        onConfirm: async () => {
                            const checkData = {
                                businessScene: this.businessType,
                                invoiceRecordId: this.currentPreviewItem.recordId || this.currentPreviewItem.invoiceRecordId,
                            };
                            const flag = await this.checkDeleteInvoice(checkData);
                            if (flag) {
                                await InvoiceAPI.deleteInvoice(this.chargeSheetId, checkData);
                                this.init();
                            }
                        },
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            async checkDeleteInvoice(checkData) {
                try {
                    await InvoiceAPI.checkDeleteInvoice(this.chargeSheetId, checkData);
                    return true;
                } catch (e) {
                    return false;
                }
            },

            queryInvoiceStatus(isChonghong = false) {
                let totalCount = 0;
                this._timer = setInterval(async () => {
                    const { data } = await InvoiceAPI.fetchInvoiceStatus(this.chargeSheetId, this.businessType);
                    totalCount++;
                    const { invoiceStatusItems } = data;
                    if (invoiceStatusItems?.[0].status === InvoiceStatus.SUCCESS) {
                        clearInterval(this._timer);
                        this.showLoading = false;
                        this._message?.close();
                        this.completeHandler();
                        this.$Toast({
                            message: isChonghong ? '发票已冲红' : '开票成功',
                            type: 'success',
                        });
                        this.isUpdateInvoiceStatus = true;
                        return;
                    }
                    if (invoiceStatusItems?.[0].status === InvoiceStatus.FAIL) {
                        this.showLoading = false;
                        if (this.isShuzu) {
                            this.handleCloseLastError(true);
                        }
                        clearInterval(this._timer);
                        const {
                            invoiceFailReasonCode,
                            invoiceFailReason,
                        } = invoiceStatusItems[0];


                        if (invoiceFailReasonCode === `${TaxFullyInvoiceErrorCode.NEED_LOGIN_FAIL}`) {
                            this.accountValidateDialog = new AccountValidateDialog({
                                employeeId: this.invoiceOperatorId,
                                employee: this.invoiceOperatorInfo,
                                supplierId: this.invoiceSupplierId,
                                onFinish: () => {
                                    this.accountValidateDialog = null;
                                },
                            }).generateDialogAsync();
                            this._message?.close();

                        } else if (invoiceFailReasonCode === `${TaxFullyInvoiceErrorCode.FACE_TIMEOUT}`) {
                            this.faceValidateDialog = new FaceValidateDialog({
                                invoiceOperatorId: this.invoiceOperatorId,
                                onFinish: () => {
                                    this.faceValidateDialog = null;
                                },
                                employee: this.invoiceOperatorInfo,
                            }).generateDialogAsync();
                            this._message?.close();

                        } else if (invoiceFailReasonCode === `${TaxFullyInvoiceErrorCode.RESET_PASSWORD}`) {
                            this.showOpenPasswordDialog = true;
                            this._message?.close();

                        } else {
                            this._message?.close();
                            this._message = this.$message({
                                referenceEl: this.$parent.$el.querySelector('.invoice-modal-wrapper'),
                                type: 'warn',
                                title: '开票失败',
                                dialogType: 'tiny',
                                content: [invoiceFailReason || ''],
                                dialogContentStyles: 'width: 240px; min-width: 240px; min-height: auto',
                                confirmFunc: this.completeHandler,
                                showConfirm: false,
                                showClose: false,
                                noDialogAnimation: true,
                            });
                            this.$Toast({
                                message: '开票失败',
                                type: 'error',
                            });
                            // this._message?.close();
                            // this.completeHandler();
                            this.isUpdateInvoiceStatus = false;
                        }
                        return;
                    }
                    if (totalCount >= 5 && invoiceStatusItems?.[0].status !== InvoiceStatus.FAIL) {
                        clearInterval(this._timer);
                        this.showLoading = false;
                        this.isUpdateInvoiceStatus = false;
                        this._message?.close();
                        this.$alert({
                            type: 'success',
                            title: isChonghong ? '冲红申请已提交' : '开票申请已提交',
                            content: isChonghong ? `购方已将发票用作报账，冲红申请需等待购方确认，冲红确认单编号：${invoiceStatusItems?.[0].redConfirmId}` : '开票信息已提交至电子税局，预计将在24小时内完成，请耐心等待',
                            onClose: () => {
                                this.init();
                                this.queryInvoiceResultByBusinessId();
                            },
                        });

                    }
                }, 1500);
            },
            /**
             * @desc 打印普通发票
             * <AUTHOR>
             * @date 2021-08-16 17:15:44
             */
            async  handleClickPrint() {
                // 强校验票号
                this.submitNormalInvoice();
                // 打印代码
                this.printHandler();
            },
            handleClickRePrint() {
                this.$confirm({
                    type: 'warn',
                    title: '此发票已打印过，如需再次打印，请先确认当前打印机中的发票信息是否正确',
                    content: [`发票代码：${this.normalInvoice.invoiceCode || ''}`, `发票号码：${this.normalInvoice.invoiceNumbers.join('，')}`],
                    onConfirm: () => {
                        // 打印代码
                        this.printHandler();
                    },
                });
            },

            /**
             * @desc 提交普通发票，点了打印===开票
             * <AUTHOR>
             * @date 2021-08-16 16:39:23
             */
            async submitNormalInvoice() {
                this.normalInvoice.mac = this.macAddress;
                if (!this.normalInvoice.mac || !this.isCanWriteInvoice) {
                    return;
                }
                this.handleCloseLastError();
                this.buttonLoading = true;

                try {
                    const invoiceItems = [] ;
                    let count = 0;
                    // 没有按照类型拆分的发票，但是是自动分页，要构造多个items给后台
                    if (this.businessItemDetails?.length === 1) {
                        const invoiceItem = this.businessItemDetails[0];
                        const invoiceNumberList = this.normalInvoice.invoiceNumbers;
                        for (let i = 0; i < this.invoicePageCount; i++) {
                            invoiceItems.push({
                                goodsFeeTypeId: invoiceItem.goodsFeeTypeId,
                                invoiceFee: invoiceItem.invoiceFee,
                                invoiceNumber: invoiceNumberList?.[i] || '',
                            });
                        }
                    } else {
                        this.businessItemDetails.forEach((item) => {
                            item.normalInvoicePreviews.forEach((normalInvoice) => {
                                invoiceItems.push({
                                    goodsFeeTypeId: item.goodsFeeTypeId,
                                    invoiceFee: normalInvoice.invoiceFee,
                                    invoiceNumber: this.normalInvoice.invoiceNumbers?.[count] || '',
                                });
                                count++;
                            });
                        });
                    }
                    Logger.report({
                        scene: 'write-invoice-submit',
                        data: {
                            info: '构造发票数据',
                            chargeSheetId: this.chargeSheetId,
                            invoiceItems: JSON.stringify(invoiceItems),
                            invoicePageCount: JSON.stringify(this.invoicePageCount),
                            normalInvoice: JSON.stringify(this.normalInvoice),
                            businessItemDetails: JSON.stringify(this.businessItemDetails),
                        },
                    });
                    await this.invoiceService.writeInvoice(this.chargeSheetId, {
                        invoiceType: this.normalInvoiceType,
                        businessScene: this.businessType,
                        invoiceCategory: this.selectedInvoiceCategory,
                        patientId: this.patientInfo.patientId,
                        buyerType: this.digitalInvoice.buyerType,
                        buyerName: this.digitalInvoice.buyerName,
                        normalInvoice: {
                            mac: this.normalInvoice.mac,
                            invoiceCode: this.normalInvoice.invoiceCode,
                            registrationInvoiceType: Number(this.isZhejiangRegInvoiceType || this.isTianjinRegInvoice),
                        },
                        invoiceItems,
                    });
                    await this.init();
                    this.isUpdateInvoiceStatus = true;
                } catch (e) {
                    Logger.report({
                        scene: 'write-invoice-submit',
                        data: {
                            info: '开票异常',
                            chargeSheetId: this.chargeSheetId,
                            error: e,
                            invoicePageCount: JSON.stringify(this.invoicePageCount),
                            normalInvoice: JSON.stringify(this.normalInvoice),
                            businessItemDetails: JSON.stringify(this.businessItemDetails),
                        },
                    });
                } finally {
                    this.buttonLoading = false;
                }
            },
            handleClickChongHong() {
                new ChonghongDialog({
                    businessId: this.chargeSheetId,
                    businessType: this.businessType,
                    macAddress: this.macAddress,
                    invoicePageCount: this.invoicePageCount,
                    writeInvoiceConfig: this.writeInvoiceConfig,
                    confirmFn: async ({
                        invoiceCode,
                        invoiceNumber,
                        invoiceNumbers,
                    }) => {

                        // 提交冲红数据
                        this.submitChonghong({
                            invoiceCode, invoiceNumbers,
                        });

                        const data = await this.fetchRedPaperInvoiceData();

                        this.printHandler({
                            ...data,
                            invoiceNumber, // 记录多张发票的起始发票号
                            invoiceNumbers,
                            invoiceCode,
                            // 对应的正数发票
                            blueInvoiceCode: this.currentPreviewItem.invoiceCode,
                            blueInvoiceNumbers: this.currentPreviewItem.invoiceNumbers,
                        });
                        this.init();

                    },
                    addInvoiceNumberFn: () => {
                        this.handleCreateNumber();
                    },
                }).generateDialogAsync();
            },
            async submitChonghong({
                invoiceCode, invoiceNumbers,
            }) {
                // 不选择发票号也可以提交发票
                try {
                    this.buttonLoading = true;
                    const invoiceItems = [] ;
                    let count = 0;
                    // 没有按照类型拆分的发票，但是是自动分页，要构造多个items给后台
                    if (this.businessItemDetails?.length === 1) {
                        const invoiceItem = this.businessItemDetails[0];
                        for (let i = 0; i < this.invoicePageCount; i++) {
                            invoiceItems.push({
                                goodsFeeTypeId: invoiceItem.goodsFeeTypeId,
                                invoiceFee: invoiceItem.invoiceFee,
                                invoiceNumber: invoiceNumbers?.[i] || '',
                            });
                        }
                    } else {
                        this.businessItemDetails.forEach((item) => {
                            item.normalInvoicePreviews.forEach((normalInvoice) => {
                                invoiceItems.push({
                                    goodsFeeTypeId: item.goodsFeeTypeId,
                                    invoiceFee: normalInvoice.invoiceFee,
                                    invoiceNumber: invoiceNumbers?.[count] || '',
                                });
                                count++;
                            });
                        });
                    }
                    await this.invoiceService.chonghongInvoice(
                        this.chargeSheetId, {
                            invoiceType: this.normalInvoiceType,
                            businessScene: this.businessType,
                            invoiceCategory: this.selectedInvoiceCategory,
                            patientId: this.patientInfo.patientId,
                            invoiceItems,
                            normalInvoice: {
                                mac: this.macAddress,
                                invoiceCode,
                                registrationInvoiceType: Number(this.isZhejiangRegInvoiceType || this.isTianjinRegInvoice),
                            },
                        },
                    );
                    this.init();

                    this.normalInvoice.invoiceNumbers = [];
                    this.isUpdateInvoiceStatus = true;

                    this.$Toast({
                        message: '发票已冲红',
                        type: 'success',
                    });
                } catch (e) {
                    const { message } = e;
                    this._message = this.$message({
                        referenceEl: this.$parent.$el.querySelector('.invoice-modal-wrapper'),
                        type: 'warn',
                        title: message,
                        dialogContentStyles: 'width: 240px; min-width: 240px; min-height: auto',
                        showConfirm: true,
                        showCancel: false,
                        showClose: false,
                        dialogType: 'tiny',
                        noDialogAnimation: true,
                    });
                    const _timer = setTimeout(() => {
                        this._message?.close();
                        clearTimeout(_timer);
                    }, 1000);
                } finally {
                    this.buttonLoading = false;
                }
            },
            /*
           @desc: 获取红票的打印数据
           @author: ff
           @time: 2025/2/17
           */
            async fetchRedPaperInvoiceData() {
                const postData = this.createPostData([this.currentPreviewItem], InvoiceViewType.RED);

                const { businessItemDetails } = await this.invoiceService.fetchPrintDataPreview(this.chargeSheetId, postData);

                let medicalBills = [];
                const printData = businessItemDetails[0]?.normalInvoicePreviews[0] || {};
                businessItemDetails.forEach((businessItem) => {
                    businessItem.normalInvoicePreviews.forEach((item) => {
                        medicalBills = medicalBills.concat(item.medicalBills);
                    });
                });
                return {
                    ...printData,
                    medicalBills,
                };
            },
            // 票据的打印
            async printHandler(printInfo) {
                let printData = null;
                if (printInfo) {
                    printData = clone(printInfo);
                } else {
                    printData = clone({
                        ...this.printPreviewData,
                        normalInvoice: this.normalInvoice,
                    });
                }

                // 开票，触发打印，上报数据
                Logger.report({
                    scene: 'write-paper-invoice-print',
                    data: {
                        info: '触发打印',
                        printData,
                        chargeSheetId: this.chargeSheetId,
                    },
                });

                this._message = this.$message({
                    $el: this.$parent.$el.querySelector('.invoice-modal-wrapper'),
                    type: 'loading',
                    title: '正在打印',
                    dialogContentStyles: 'width: 240px; min-width: 240px; min-height: auto',
                    onCancel: this.completeHandler,
                    cancelText: '关闭',
                    dialogType: 'tiny',
                    showConfirm: false,
                    showClose: false,
                    noDialogAnimation: true,
                });
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    if (this._message.type === 'loading') {
                        this._message?.close();
                    }
                }, 1000);


                await AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates[this.getTemplateName()],
                    printConfigKey: ABCPrintConfigKeyMap.bill,
                    data: printData,
                    extra: {
                        $abcSocialSecurity: this.$abcSocialSecurity,
                    },
                    matchTemplateCallback: ({ template: { templates = [] } }) => {
                        return matchTemplate(this.printBillConfig.format, templates, this.printBillConfig);
                    },
                    matchRecommendPagesizeCallback: (pageSizeList) => {
                        return matchPageSize(pageSizeList, this.printBillConfig, this.printBillConfig.format);
                    },
                    needReportLog: true,
                    reportLogData: {
                        scene: 'write-paper-invoice-print',
                        keyId: this.chargeSheetId,
                        data: {
                            info: '发票打印HTML上传',
                            printData,
                        },
                    },
                });
            },
            calcInvoiceNo() {
                this.normalInvoice.invoiceNumbers = this.calculateInvoiceNumbers(this.invoicePageCount, this.invoiceNumberList, this.normalInvoice.invoiceNumber);
            },

            async handleChangeCode() {
                this.invoiceCodeList.forEach((item) => {
                    if (this.normalInvoice.invoiceManagementId === item.id) {
                        this.normalInvoice.invoiceCode = item.code;
                        this.normalInvoiceType = item.type;
                    }
                });
                this.normalInvoice.invoiceNumber = '';
                await this.queryInvoiceNumberList(true);
            },
            /*
           @desc: 获取发票号码列表
           @author: ff
           @time: 2025/2/10
           */
            async queryInvoiceNumberList(selectFirst = false) {
                this.initDataLoading = true;
                await this.fetchInvoiceNumberList(this.normalInvoice.invoiceManagementId, this.macAddress);

                if (this.invoiceNumberList.length && selectFirst) {
                    const {
                        invoiceNumber,
                        invoiceCode,
                        invoiceManagementId,
                    } = this.invoiceNumberList[0];
                    Object.assign(this.normalInvoice, {
                        invoiceNumber,
                        invoiceCode,
                        invoiceManagementId,
                    });
                }
                this.calcInvoiceNo();
                Logger.report({
                    scene: 'write-paper-invoice-print',
                    data: {
                        info: 'queryInvoiceNumberList/计算票号',
                        chargeSheetId: this.chargeSheetId,
                        normalInvoice: JSON.stringify(this.normalInvoice),
                    },
                });
                this.initDataLoading = false;
            },
            async updateHandleUpdateInvoiceNumber() {
                if (this.isNewInvoice) {
                    await this.queryInvoiceCodeList();
                    await this.getSuggestInvoiceNumber();
                }

            },
            /*
           @desc: 获取发票代码列表
           @author: ff
           @time: 2025/2/10
           */
            async queryInvoiceCodeList() {
                await this.fetchInvoiceCodeList(this.macAddress);

                if (this.invoiceCodeList?.length) {
                    // 默认选中第一个支持的发票类型
                    this.normalInvoiceType = this.invoiceCodeList[0].type;
                }
            },
            /*
           @desc: 推荐使用的发票代码号码
           @author: ff
           @time: 2025/2/10
           */
            async getSuggestInvoiceNumber() {
                const data = await this.recommendInvoiceNumber(this.normalInvoiceType, this.macAddress);

                if (data) {
                    this.normalInvoice.invoiceNumber = data.invoiceNumber;
                    this.normalInvoice.invoiceCode = data.invoiceCode;
                    this.normalInvoice.invoiceManagementId = data.invoiceManagementId;
                    await this.queryInvoiceNumberList();
                } else {
                    this.normalInvoice.invoiceNumber = '';
                    this.normalInvoice.invoiceCode = '';
                    this.normalInvoice.invoiceManagementId = '';
                }
            },
            async handleClickPreviewRecordInvoice(e, item) {
                this.loading = true;
                this.$refs.pendingInvoiceListRef?.setSelectedKey(null);
                this.$refs.processingInvoiceListRef?.setSelectedKey(null);
                this.currentItemId = item.actionId;
                this.currentPreviewItem = item;
                await this.initPreviewData();
                this.loading = false;
            },
            async handleClickPreviewPendingInvoice() {
                this.loading = true;
                this.$refs.invoiceRecordListRef?.setSelectedKey(null);
                this.$refs.pendingInvoiceListRef?.setSelectedKey(null);
                this.currentItemId = this.newInvoiceGoodsFeeTypeId;
                this.currentPreviewItem = null;
                await this.initPreviewData();
                this.loading = false;
            },
            async handleClickPreviewProcessingInvoice(e, item) {
                this.loading = true;
                this.$refs.invoiceRecordListRef?.setSelectedKey(null);
                this.$refs.pendingInvoiceListRef?.setSelectedKey(null);
                this.currentItemId = item.goodsFeeTypeId;
                this.currentPreviewItem = item;
                await this.initPreviewData();
                this.loading = false;
            },
            completeHandler() {
                this.visible = false;
            },
            /*
            @desc: 撤销冲红
            @author: ff
            @time: 2025/3/2
            */
            async handleRevokeRedInvoice() {
                try {
                    this.buttonLoading = true;
                    this.$confirm({
                        type: 'warn',
                        title: '撒销冲红',
                        content: '撤销后，患者可继续使用原发票，是否确定撤销？',
                        onConfirm: async () => {
                            await InvoiceAPI.revokeRedInvoice({
                                supplierId: this.invoiceSupplierId,
                                businessId: this.chargeSheetId,
                                businessScene: this.businessType,
                                clerkId: this.invoiceOperatorId,
                            });
                            this.init();
                            this.$Toast({
                                message: '红冲申请已撤销',
                                type: 'success',
                            });
                        },
                    });
                } catch (e) {
                    console.error('撤销红冲报错',e);
                } finally {
                    this.buttonLoading = false;
                }
            },
            navigateToInvoiceConfig() {
                if (this.toBillPrintSetting && typeof this.toBillPrintSetting === 'function') {
                    this.toBillPrintSetting();
                }
            },
            handleChangeEInvoice() {
                this.$refs.invoicePreview?.mountPrintInstance(false);
            },
            /**
             * @desc: 初始化开票员和默认开票员
             * @author: ff
             * @time: 2025/5/12
             */
            async initTaxElectronInvoiceOpenerList() {
                if (this.supportShuzu) {
                    await this.fetchOperators(this.userInfo.id);
                    this.invoiceOperatorId = this.recommendedOperator?.employeeId;
                    this.invoiceOperatorInfo = this.recommendedOperator;
                }
            },
            handleChangeInvoiceOperator() {
                this.operatorList.find((item) => {
                    if (item.employeeId === this.invoiceOperatorId) {
                        this.invoiceOperatorInfo = item;
                    }
                });
            },

            async downloadPdfToBase64(url, headers = {}) {
                return new Promise((resolve, reject) => {
                    // 设置超时处理
                    const timeout = setTimeout(() => {
                        reject(new Error('请求超时'));
                    }, 10000); // 10秒超时

                    const request = window.electron.remote.net.request({
                        method: 'GET', url,
                    });

                    // 默认请求头
                    const defaultHeaders = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Referer': 'https://dppt.qingdao.chinatax.gov.cn/',
                        'Accept': 'application/pdf',
                        'Connection': 'keep-alive',
                    };

                    // 合并 headers（传入的 headers 会覆盖默认值）
                    const finalHeaders = {
                        ...defaultHeaders, ...headers,
                    };
                    for (const [key, value] of Object.entries(finalHeaders)) {
                        request.setHeader(key, value);
                    }


                    const chunks = [];

                    request.on('response', (response) => {
                        // 检查HTTP状态码
                        if (response.statusCode < 200 || response.statusCode >= 300) {
                            clearTimeout(timeout);
                            reject(new Error(`HTTP错误: ${response.statusCode}`));
                            return;
                        }

                        response.on('data', (chunk) => chunks.push(chunk));

                        response.on('end', () => {
                            clearTimeout(timeout);

                            try {
                                const buffer = Buffer.concat(chunks);

                                // 验证是否为PDF
                                if (buffer.length === 0) {
                                    reject(new Error('接收到空数据'));
                                    return;
                                }

                                if (buffer.slice(0, 4).toString() !== '%PDF') {
                                    reject(new Error('无效的PDF文件'));
                                    return;
                                }

                                resolve({
                                    base64: buffer.toString('base64'),
                                    size: buffer.length,
                                    headers: response.headers,
                                });
                            } catch (error) {
                                reject(new Error(`处理PDF数据时出错: ${error.message}`));
                            }
                        });

                        response.on('error', (error) => {
                            clearTimeout(timeout);
                            reject(new Error(`响应错误: ${error.message}`));
                        });
                    });

                    // 添加请求错误处理
                    request.on('error', (error) => {
                        clearTimeout(timeout);
                        reject(new Error(`请求错误: ${error.message}`));
                    });

                    request.end(); // 必须调用
                });
            },

            async handlePrintElectron(url) {
                this.printISVLoading = true;
                try {
                    const response = await this.downloadPdfToBase64(url);
                    console.log('response', response);
                    const { base64 } = response;
                    if (base64) {
                        pdfLodopPrint({
                            pdfBase64: base64,
                            pdfOptions: {
                                filename: `数电发票-${this.digitalInvoice.buyerName}`,
                            },
                        });
                    }
                } catch (e) {
                    this.$Toast.error('打印失败，请重试');
                    console.log('数电发票打印报错', e);
                    Logger.report({
                        scene: 'ISV_PRINT_ERROR',
                        data: {
                            error: e,
                            info: '数电发票打印报错',
                        },
                    });
                } finally {
                    this.printISVLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';

    .invoice-modal-wrapper {
        .invoice-list-left-wrapper {
            height: 316px;
            border-radius: var(--abc-dialog-border-radius);

            .invoice-list-left-item {
                margin-top: 2px;
            }

            .is-red-invoice {
                color: var(--abc-color-R6);
            }

            .is-blue-invoice {
                color: var(--abc-color-theme2);
            }

            .is-refund-invoice {
                color: var(--abc-color-T2);
            }
        }

        .invoice-page-wrapper {
            height: 604px;

            .invoice-page-header {
                width: 100%;
                height: 80px;
                min-height: 80px;
                max-height: 80px;
                padding: 44px 40px 12px;

                .invoice-download-btn {
                    font-size: 12px;

                    &:hover {
                        background-color: var(--abc-color-T6);
                    }

                    &:active {
                        background-color: #020a1a0a;
                    }
                }
            }

            .invoice-page-preview {
                flex: 1;
                width: 780px;
                padding-top: 0;
                padding-right: 30px;
                padding-left: 40px;
            }

            .invoice-page-form-footer {
                width: 100%;
                height: 128px;
                min-height: 128px;
                max-height: 128px;
                padding: 24px;
                margin-top: auto;
                background: #ffffff;
                border-top: 1px solid var(--abc-color-P8, #eaedf1);
            }
        }

        .invoice-loading-modal {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 4;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: var(--abc-border-radius-medium, 8px);

            .invoice-loading-content {
                width: 244px;
                height: 198px;
                padding: 56px 24px;
                background: #ffffff;
                border-radius: var(--abc-border-radius-medium, 8px);
                box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.1);
            }

            .invoice-loading-animation-wrapper {
                padding: 0 20px;
                margin-bottom: 24px;
            }

            .invoice-loading-animation {
                /* HTML: <div class="loader"></div> */
                position: absolute;
                bottom: 10px;
                left: 0;
                width: 44px;
                height: 10px;

                .loader-dot {
                    width: 6px;
                    aspect-ratio: 1;
                    margin: 0 auto 6px;
                    border-radius: 50%;
                    animation: l5 2s infinite linear  backwards;
                }

                @keyframes l5 {
                    0% {
                        background: #0090ff02;
                        box-shadow: 12px 0 #0090ff02, -12px 0 #0090ff;
                    }

                    50% {
                        background: #0090ff;
                        box-shadow: 12px 0 #0090ff, -12px 0 #0090ff02;
                    }

                    90% {
                        background: #0090ff02;
                        box-shadow: 12px 0 #0090ff, -12px 0 #0090ff02;
                    }

                    100% {
                        background: #0090ff02;
                        box-shadow: 12px 0 #0090ff, -12px 0 #0090ff;
                    }
                }
            }
        }
    }
</style>
