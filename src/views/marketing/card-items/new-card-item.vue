<template>
    <abc-layout preset="page-table">
        <introduce
            v-if="!isPurchased"
            :access-key="accessMap.MARKET_CARD"
            :hide-title="true"
        >
        </introduce>
        <template v-else>
            <abc-layout-header>
                <abc-flex justify="space-between">
                    <abc-search
                        v-model="params.keyword"
                        placeholder="搜索卡名"
                        clearable
                        :focus-when-clear="false"
                        :width="300"
                        @input="handleSearchCard"
                        @clear="handleSearchCard"
                    >
                    </abc-search>
                    <abc-check-access>
                        <abc-button
                            icon="s-b-add-line-medium"
                            theme="success"
                            @click="handleOpenFormDialog({ id: null })"
                        >
                            新增卡项
                        </abc-button>
                    </abc-check-access>
                </abc-flex>
            </abc-layout-header>
            <abc-layout-content @layout-mounted="handleMounted">
                <abc-table
                    :empty-opt="{ label: '暂无卡项数据' }"
                    :render-config="tableHeaderConfig"
                    :loading="loading"
                    :data-list="cardList"
                    @handleClickTr="handleOpenFormDialog"
                >
                    <template
                        #name="{ trData: item }"
                    >
                        <abc-table-cell>
                            <abc-text theme="primary">
                                {{ item.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>
                    <template
                        #status="{ trData: item }"
                    >
                        <abc-table-cell>
                            <abc-tag-v2
                                :variant="item.status === CardStatusEnum.STOPPED ? 'light-outline' : 'outline'"
                                :theme="item.status === CardStatusEnum.USING ? 'success' : 'default'"
                            >
                                {{ item.status ? '已终止' : '使用中' }}
                            </abc-tag-v2>
                        </abc-table-cell>
                    </template>
                    <template
                        #isNeedCardFee="{ trData: item }"
                    >
                        <abc-table-cell>
                            <abc-text>
                                {{ item.isNeedCardFee ? '付费开卡' : '免费开卡' }}
                            </abc-text>
                        </abc-table-cell>
                    </template>
                    <template
                        #cardFee="{ trData: item }"
                    >
                        <abc-table-cell>
                            <abc-text>
                                {{ item.cardFee ? item.cardFee : '--' }}
                            </abc-text>
                        </abc-table-cell>
                    </template>
                    <template
                        #validityPeriod="{ trData: item }"
                    >
                        <abc-table-cell>
                            <abc-text>
                                {{ item.validityPeriod }}
                            </abc-text>
                        </abc-table-cell>
                    </template>
                    <template
                        #ownRights="{ trData: item }"
                    >
                        <abc-table-cell>
                            <abc-text>
                                {{ item.ownRights }}
                            </abc-text>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-layout-content>
            <abc-layout-footer>
                <abc-pagination
                    :show-total-page="true"
                    :pagination-params="params"
                    :count="count"
                    @current-change="handlePageIndexChange"
                ></abc-pagination>
            </abc-layout-footer>


            <form-dialog
                v-if="formDialogVisible"
                :id="currentId"
                v-model="formDialogVisible"
                @input="handleRefresh"
            ></form-dialog>
        </template>
    </abc-layout>
</template>

<script>
    import FormDialog from './dialog/form-dialog.vue';
    import MarketingAPI from 'api/marketing';
    import Introduce from 'views/edition/introduce/index.vue';
    import AbcAccess from '@/access/utils.js';
    import { debounce } from 'utils/lodash';
    import AbcSearch from 'components/abc-search/index.vue';

    const CardStatusEnum = {
        USING: 0,
        STOPPED: 1,
    };
    export default {
        components: {
            AbcSearch,
            FormDialog,
            Introduce,
        },
        data() {
            return {
                accessMap: AbcAccess.accessMap,
                formDialogVisible: false,
                loading: false,
                cardList: [],
                params: {
                    pageIndex: 0,
                    pageSize: 10,
                    keyword: '',
                },
                count: 0,
                currentId: null,
                CardStatusEnum,
            };
        },

        computed: {
            isPurchased() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.MARKET_CARD);
            },

            tableHeaderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            label: '卡名',
                            key: 'name',
                            style: {
                                width: '248px',
                                maxWidth: '248px',
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '状态',
                            key: 'status',
                            style: {
                                width: '100px',
                                maxWidth: '100px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '获取方式',
                            key: 'isNeedCardFee',
                            style: {
                                width: '100px',
                                maxWidth: '100px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '卡费',
                            key: 'cardFee',
                            style: {
                                width: '100px',
                                maxWidth: '100px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '有效期',
                            key: 'validityPeriod',
                            style: {
                                width: '104px',
                                maxWidth: '104px',
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '权益类型',
                            key: 'ownRights',
                            style: {
                                flex: 1,
                                textAlign: 'left',
                            },
                        },
                    ],
                };
            },
        },
        created() {
            this._debounceSearch = debounce(
                async () => {
                    await this.getTableData();
                },
                250,
                true,
            );
        },
        methods: {
            handleMounted(data) {
                this.params.pageSize = data.paginationLimit;
                this.getTableData();
            },
            handleRefresh() {
                this.getTableData(false);
            },
            handleOpenFormDialog(item) {
                this.currentId = item.id;
                this.formDialogVisible = true;
                this.$router.replace({
                    query: {
                        rights: 'base',
                    },
                });
            },

            async getTableData(resetPageParams = true) {
                if (!this.isPurchased) return;

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }
                const {
                    pageIndex, pageSize, keyword,
                } = this.params;
                const offset = pageIndex * pageSize;
                this.loading = true;
                try {
                    const { data } = await MarketingAPI.promotions.list(offset, pageSize, keyword);
                    this.cardList = data.rows || [];
                    this.count = data.total || 0;

                } catch (err) {
                    console.error(err);
                    this.loading = false;
                } finally {
                    this.loading = false;
                }
            },

            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            handleSearchCard() {
                this._debounceSearch();
            },
        },

    };
</script>

<style lang="scss" scoped>
    @import '~styles/theme.scss';

    .promotion-module-card-item-list {
        background: $S2;

        .promotion-module-new-card-item {
            padding: 20px 20px 16px 20px;
        }

        .promotions-table-wrapper {
            padding: 0 20px 20px 20px;

            .active {
                color: $T1;
            }

            .disable {
                color: $T3;
            }
        }

        .promotion-module-card-name {
            color: $B1;
        }
    }
</style>
