
<template>
    <abc-dialog
        v-if="formDialogVisible"
        v-model="formDialogVisible"
        :auto-focus="!id"
        size="hugely"
        :content-styles="contentStyle"
        :title="dialogTitle"
    >
        <div class="card-item-form-dialog-container">
            <div class="card-item-form-dialog_left">
                <div class="card-item-form-dialog_left-content">
                    <card-view :card-info="baseSettings" :unit-options="unitOptions" :disable-switch-rights="disableSwitchRights"></card-view>
                    <div class="rights-card-group">
                        <rights-item
                            v-for="(item, index) in rightsData"
                            :key="index"
                            :disable-switch-rights="disableSwitchRights"
                            :rights-item="item"
                        ></rights-item>
                    </div>
                </div>
            </div>
            <div class="card-item-form-dialog_right" :class="{ 'has-card-obtained-tips': hasStopped || presentRightsEditUnable }">
                <div v-if="hasStopped" class="card-obtained-tips">
                    该卡项已终止
                </div>
                <div v-if="presentRightsEditUnable" class="card-obtained-tips">
                    该卡项已有患者办理，不可修改抵扣权益
                </div>
                <template
                    v-for="(form,key) in rightsFormMap"
                >
                    <component
                        :is="form.component"
                        v-show="$route.query.rights === key"
                        :ref="`${key}RightsForm`"
                        :key="key"
                        v-model="form.model"
                        :is-need-card-fee="baseSettings.isNeedCardFee"
                        :card-fee="baseSettings.cardFee"
                        :disable-card-edit="disableCardEdit"
                        :show-discount-error="showDiscountError"
                        :show-service-error="showServiceError"
                        :show-recharge-error="showRechargeError"
                        :unit-options="unitOptions"
                        @input="handleFormInput"
                        @disable-switch-rights="handleChangeSwitchStatus"
                        @change="modelChangeHandler"
                        @handleOptTracker="handleOptTracker"
                    ></component>
                </template>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button
                v-if="!!id"
                variant="ghost"
                theme="danger"
                :disabled="cardHasObtained && normalObtained"
                @click="handleDelete"
            >
                删除
            </abc-button>
            <abc-button
                v-if="!!id"
                variant="ghost"
                theme="danger"
                :disabled="!!hasStopped"
                @click="handleStopCard"
            >
                终止
            </abc-button>
            <div style="flex: 1;"></div>
            <abc-button :loading="loading" @click="handleSave">
                保存
            </abc-button>
            <abc-button variant="ghost" @click="handleCancel">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import RightsItem from './rights-card.vue';
    import BaseForm from './form/base-form.vue';
    import RechargeForm from './form/recharge-form.vue';
    import DiscountForm from './form/discount-form.vue';
    import ServiceForm from './form/service-form.vue';
    import OtherForm from './form/other-form.vue';
    import CardView from './card-view.vue';
    import MarketingAPI from 'api/marketing';
    import { mapGetters } from 'vuex';
    import {
        enlargeDiscount, reduceDiscount,
    } from 'views/marketing/util.js';
    import clone from 'utils/clone';
    import {
        isNotNull, isNull,
    } from '@/utils';
    import useDataOperationTracker, { OPT_TYPES } from 'views/marketing/hooks/use-data-operation-tracker';

    export default {
        components: {
            RightsItem,
            BaseForm,
            RechargeForm,
            DiscountForm,
            ServiceForm,
            OtherForm,
            CardView,
        },
        props: {
            id: {
                type: String,
                default: '',
            },
            value: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const discountGoodsListTracker = useDataOperationTracker();
            const serviceGoodsListTracker = useDataOperationTracker();
            const rechargeGoodsListTracker = useDataOperationTracker();
            return {
                discountGoodsListTracker,
                serviceGoodsListTracker,
                rechargeGoodsListTracker,
            };
        },
        data() {
            return {
                isEditedCard: false, // 是否编辑了卡信息
                cardHasObtained: false, // 卡项是否被领取
                normalObtained: false, // 卡项是否过期
                showDiscountError: false,
                showServiceError: false,
                showRechargeError: false,
                activeRights: 'base',
                baseSettings: {
                    name: '',
                    cardFee: '',
                    validityType: 0,
                    isShowInWeClinic: 0,
                    isNeedCardFee: 0,
                    isShare: 0,
                    isShowAgreement: 0,
                    agreement: '',
                    validityPeriodUnit: 2,
                    validateCardValidityPeriod: '',
                    validityPeriodStr: '',
                    isAllClinics: 1,
                    clinicIds: [],
                },
                customRights: {
                    isOpen: 0,
                    detail: '',
                },
                discountRights: {
                    isOpen: 0,
                    detail: {
                        goodsList: [],
                    },
                },
                presentRights: {
                    isOpen: 0,
                    presentAppointGoodsSwitch: 0,
                    presentScopeGoodsSwitch: 0,
                    detail: {
                        singlePresentList: [],
                        packagePresentList: [],
                    },
                },
                selfGoodsPackageListTracker: [], // 抵扣权益-自选项目追踪器
                rechargeRights: {
                    isOpen: 0,
                    detail: {
                        balanceUsingScope: 1,
                        goodsPayWithBalance: [],
                        id: '',
                        isRechargeInWeClinic: 0,
                        rechargeRules: [{
                            pay: '',
                            present: '',
                        }],
                    },
                },
                disableSwitchRights: false,
                hasStopped: false,
                unitOptions: [
                    {
                        label: '天',
                        value: 0,
                    },
                    {
                        label: '周',
                        value: 1,
                    },
                    {
                        label: '月',
                        value: 2,
                    },
                    {
                        label: '年',
                        value: 3,
                    },
                ],
                loading: false,
            };

        },

        computed: {
            ...mapGetters(['currentClinic']),

            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            contentStyle() {
                const height = window.screen.width > 1440 ? 730 : 615;
                return `padding: 0; height: ${height}px; overflow-y: auto;`;
            },
            presentRightsEditUnable () {
                return !this.hasStopped && this.cardHasObtained && this.$route.query?.rights === 'service';
            },
            disableCardEdit() {
                const { rights } = this.$route.query || {};
                return rights === 'service' ? !!this.hasStopped || !!this.cardHasObtained : !!this.hasStopped;
            },

            switchComponent() {
                const { rights } = this.$route.query;
                return this.rightsFormMap[rights].component || BaseForm;
            },

            rightsFormMap() {
                return {
                    'base': {
                        model: this.baseSettings,
                        component: BaseForm,
                    },
                    'recharge': {
                        model: this.rechargeRights,
                        component: RechargeForm,
                    },
                    'service': {
                        model: this.presentRights,
                        component: ServiceForm,
                    },
                    'discount': {
                        model: this.discountRights,
                        component: DiscountForm,
                    },
                    'other': {
                        model: this.customRights,
                        component: OtherForm,
                    },
                };
            },

            dialogTitle() {
                return this.id ? '编辑卡项' : '新增卡项';
            },

            chainId() {
                return this.currentClinic && this.currentClinic.chainId;
            },

            formDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            rechargeRightsData() {
                const {
                    detail = {}, isOpen,
                } = this.rechargeRights || {};
                let _rules = [];
                if (!isOpen) {
                    return [];
                }

                if (detail.rechargeRules) {
                    _rules = detail.rechargeRules.map((item) => {
                        return `充${this.$t('currencySymbol')} ${item.pay || 0} 送${this.$t('currencySymbol')} ${item.present || 0}`;
                    });

                    const {
                        balanceUsingScope, goodsPayWithBalance = [],
                    } = detail;
                    if (balanceUsingScope) {
                        _rules.push('使用范围：全部');
                    } else {
                        const _arr = goodsPayWithBalance.map((item) => {
                            if (item.type === 1) {
                                return item.name;
                            }
                            const {
                                name, medicineCadn,
                            } = item.goods || {};
                            return `${medicineCadn ? medicineCadn : ''}${name ? `（${name}）` : ''}`;

                        });
                        _rules.push(`使用范围： ${_arr.join()}`);

                    }
                }
                return _rules;
            },

            discountRightsData() {
                const {
                    detail = {}, isOpen,
                } = this.discountRights || {};

                let _rules = [];
                if (!isOpen) {
                    return _rules;
                }
                if (detail.goodsList) {
                    _rules = detail.goodsList.map(((item) => {
                        const formatDiscount = enlargeDiscount(item.discount, 1, 2);
                        if (item.type === 1) {
                            return `${item.name} ${formatDiscount} 折`;
                        }
                        const {
                            name,
                            medicineCadn,
                        } = item.goods || {};
                        return item.discountType ? ` ${medicineCadn || name} ${item.discount || 0} 元` : `${medicineCadn || name} ${formatDiscount} 折`;
                    }));
                }

                return _rules;
            },

            // 抵扣权益 - 单选
            serviceSinglePresentList() {
                const {
                    detail = {}, isOpen,
                } = this.presentRights || {};
                let _rules = [];
                if (!isOpen) {
                    return _rules;
                }

                if (detail.singlePresentList) {
                    _rules = detail.singlePresentList.map((item) => {
                        return item.isGivingLimit ? `${item?.goods?.name || ''} ${item.givingCount || 0} 次` : `${item?.goods?.name || ''}不限`;
                    });
                }
                return _rules;
            },

            // 抵扣权益 - 多选
            servicePackagePresentList () {
                const {
                    detail = {}, isOpen,
                } = this.presentRights || {};
                let _rules = [];
                if (!isOpen) {
                    return _rules;
                }

                if (detail.packagePresentList) {

                    _rules = detail.packagePresentList.filter((item) => {
                        return !item.type;
                    });
                }
                return _rules;
            },
            // 抵扣权益，自选范围
            serviceScopePackagePresentList () {
                const {
                    detail = {}, isOpen,
                } = this.presentRights || {};
                let _rules = [];
                if (!isOpen) {
                    return _rules;
                }

                if (detail.packagePresentList) {

                    _rules = detail.packagePresentList.filter((item) => {
                        return item.type;
                    });
                }
                return _rules;
            },

            customRightsData() {
                const {
                    detail, isOpen,
                } = this.customRights || {};
                let _rules = [];
                if (!isOpen) {
                    return _rules;
                }

                if (detail) {
                    _rules = detail.split();
                }
                return _rules;
            },

            rightsData() {
                return [
                    {
                        title: '充送权益',
                        name: 'recharge',
                        group: this.rechargeRightsData || [],
                    },
                    {
                        title: '抵扣权益',
                        name: 'service',
                        group: this.serviceSinglePresentList || [],
                        extraData: this.servicePackagePresentList || [],
                        scopePackagePresent: this.serviceScopePackagePresentList || [],
                    },
                    {
                        title: '折扣权益',
                        name: 'discount',
                        group: this.discountRightsData || [],
                    },
                    {
                        title: '其他权益',
                        name: 'other',
                        group: this.customRightsData || [],
                    },
                ];
            },
        },
        watch: {
            '$route.query': {
                handler() {
                    const { rights } = this.$route.query;
                    this.activeRights = rights || 'base';
                },
                immediate: true,
            },
        },

        created() {
            this.fetchCardDetail();
        },

        methods: {
            handleStopCard() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '终止后无法再办理该卡项，是否确认？',
                    onConfirm: () => {
                        this.stop();
                    },
                });
            },
            async stop() {
                try {
                    await MarketingAPI.promotions.stopCard(this.id);
                    this.$Toast({
                        message: '终止成功',
                        type: 'success',
                    });
                    this.formDialogVisible = false;
                    this.$emit('refresh');
                } catch (e) {
                    console.error(e);
                }
            },
            modelChangeHandler(value) {
                const routerKey = this.$route.query.rights || 'base';
                const key = value?.key || routerKey;
                if (key !== 'base') {
                    if (value.isOpen) {
                        this.$set(this.rightsFormMap[key].model,'detail', value.detail);
                    }

                    if (isNotNull(value.isOpen)) {
                        this.rightsFormMap[key].model.isOpen = value.isOpen;
                    }

                    if (key === 'service') {
                        this.$set(this.rightsFormMap[key].model,'presentAppointGoodsSwitch', value.presentAppointGoodsSwitch);
                        this.$set(this.rightsFormMap[key].model,'presentScopeGoodsSwitch', value.presentScopeGoodsSwitch);
                    }
                } else {
                    Object.assign(this.rightsFormMap[key].model, value);
                }
            },
            handleChangeSwitchStatus(val) {
                this.disableSwitchRights = val;
            },
            handleFormInput(value) {
                this.isEditedCard = true;
                console.log('value', value);
            },
            async fetchCardDetail() {
                if (!this.id) {
                    return;
                }
                const { data } = await MarketingAPI.promotions.card(this.id);
                // init data
                this.baseSettings = {
                    name: data.name,
                    cardFee: data.cardFee,
                    validityType: data.validityType,
                    validityPeriod: data.validityType ? data.validityPeriod : '',
                    validityPeriodUnit: data.validityPeriodUnit === null ? 2 : data.validityPeriodUnit,
                    isShowInWeClinic: data.isShowInWeClinic,
                    isNeedCardFee: data.isNeedCardFee,
                    isShare: data.isShare,
                    isShowAgreement: data.isShowAgreement,
                    agreement: data.agreement,
                    validityPeriodStr: data.validityPeriodStr,
                    isAllClinics: data.isAllClinics,
                    clinicIds: data.clinicIds || [],
                };

                this.rechargeRights = { ...data.rechargeRights };

                this.presentRights = {
                    ...data.presentRights,
                    presentScopeGoodsSwitch: data.presentScopeGoodsSwitch,
                    presentAppointGoodsSwitch: data.presentAppointGoodsSwitch,
                };

                this.discountRights = { 
                    ...data.discountRights,
                    originGoodsList: data?.discountRights?.detail?.goodsList || [],
                };
                this.customRights = { ...data.customRights };
                this.cardHasObtained = !!data.obtainedCount;
                this.normalObtained = !!data.normalObtained;
                this.hasStopped = data.status;
                this.discountRights?.detail?.goodsList?.forEach((item) => {
                    const formatDiscount = enlargeDiscount(item.discount, 10, 2);
                    item.discount = item.discountType ? item.discount : formatDiscount;
                    item.isOriginData = true;
                });
                this.presentRights?.detail?.singlePresentList?.forEach((item) => {
                    item.isOriginData = true;
                });
                this.presentRights?.detail?.packagePresentList?.forEach((item) => {
                    item.isOriginData = true;
                    item.originGoodsList = clone(item.goodsList);
                });
                this.rechargeRights?.detail?.goodsPayWithBalance?.forEach((item) => {
                    item.isOriginData = true;
                });
            },

            async handleSave() {
                this.loading = true;
                try {
                    // 校验表单的必填项， 校验失败定位到第一个表单出问题的位置
                    let passValidate = true;
                    Object.keys(this.rightsFormMap).forEach((key) => {
                        const abcFormVm = this.$refs[`${key}RightsForm`][0].$refs.abcForm;
                        if (passValidate) {
                            abcFormVm.validate((valid) => {
                                if (!valid) {
                                    passValidate = false;
                                    // 路由一致，不进行跳转
                                    if (key === this.$route.query.rights) {
                                        return;
                                    }
                                    this.$router.push({
                                        query: {
                                            rights: key,
                                        },
                                    });
                                }
                            });
                        }
                    });

                    const {
                        chainId ,rechargeRights , presentRights, discountRights, customRights,
                    } = this;

                    if (!this.baseSettings?.name) {
                        return;
                    }

                    const newParams = {
                        chainId,
                        ...this.baseSettings,
                        equityTotalPrice: this.baseSettings.isNeedCardFee ? this.baseSettings.cardFee : '',
                        rechargeRights,
                        presentRights,
                        discountRights,
                        customRights,
                    };

                    const params = clone(newParams);

                    if (params.isShowAgreement && !params.agreement.trim()) {
                        this.$Toast({
                            message: '协议内容不能为空',
                            type: 'error',
                        });
                        return;
                    }

                    // 至少选择一种权益校验
                    if (!params.rechargeRights.isOpen && !params.presentRights.isOpen && !params.discountRights.isOpen && !params.customRights.isOpen) {
                        this.$Toast({
                            message: '至少选择一种权益',
                            type: 'error',
                        });
                        return;
                    }

                    // 充送权益
                    if (params.rechargeRights.isOpen) {
                        const {
                            detail: {
                                rechargeRules, goodsPayWithBalance,
                            },
                        } = params.rechargeRights;
                        // 充值规则不能为空
                        if (rechargeRules) {
                            const hasEmpty = rechargeRules.some((item) => isNull(item.pay));
                            if (hasEmpty) {
                                this.$Toast({
                                    message: '充送权益有必填项未录入',
                                    type: 'error',
                                });
                                return;
                            }
                        }

                        // 充值规则不能重复
                        const pay = rechargeRules && rechargeRules.map((item) => item.pay);
                        const paySet = new Set(pay);
                        if (paySet.size !== pay.length) {
                            this.$Toast({
                                message: '充值规则不能重复',
                                type: 'error',
                            });
                            return;
                        }

                        // 删除多余参数
                        goodsPayWithBalance && goodsPayWithBalance.forEach((item) => {
                            // delete item.goods;
                            delete item.productInfo;
                        });
                    }

                    // 抵扣权益
                    if (params.presentRights.isOpen) {
                        const {
                            detail: {
                                singlePresentList, packagePresentList,
                            },
                            presentAppointGoodsSwitch,
                            presentScopeGoodsSwitch,
                        } = params.presentRights;

                        // 赠送商品不能重复
                        for (let i = 0; i < singlePresentList && singlePresentList.length; i++) {
                            for (let j = 0; j < packagePresentList && packagePresentList.length; j++) {
                                const haRepeatGood = packagePresentList && packagePresentList[j].goodsList.find((item) =>
                                    item.id === singlePresentList[i].id,
                                );
                                if (haRepeatGood) {
                                    this.$Toast({
                                        message: '赠送商品不能重复',
                                        type: 'error',
                                    });
                                    return;
                                }
                            }
                        }

                        // 删除多余参数
                        singlePresentList && singlePresentList.forEach((item) => {
                            // delete item.goods;
                            delete item.productInfo;
                        });

                        packagePresentList && packagePresentList.forEach((item) => {
                            item.goodsList && item.goodsList.forEach((good) => {
                                // delete good.goods;
                                delete good.productInfo;
                            });
                        });

                        // 权益总价值
                        params.presentScopeGoodsSwitch = presentScopeGoodsSwitch;
                        // 没有支持自选范围的营销默认单项
                        params.presentAppointGoodsSwitch = this.viewDistributeConfig.Marketing.card.isSupportDeductionBenefitsRangeRule ? presentAppointGoodsSwitch : 1;
                    }

                    // 折扣权益
                    const { isOpen } = params.discountRights ;
                    if (isOpen) {
                        const { goodsList } = params.discountRights.detail;

                        // 单品折扣特价是否大于原价
                        const discountGtOriginalPrice = goodsList && goodsList.length && goodsList.some((item) => {
                            if (item.discountType === 1) {
                                return item.discount > item.goods && item.goods.packagePrice;
                            }
                        });
                        if (discountGtOriginalPrice) {
                            return;
                        }

                        goodsList && goodsList.length && goodsList.forEach((item) => {
                            delete item.goods;
                            delete item.productInfo;
                            const formatDiscount = reduceDiscount(item.discount, 10, 3);
                            item.discount = item.discountType ? item.discount : formatDiscount;
                        });
                    }
                    // 如果没有开通权益，不传 detail字段
                    if (rechargeRights.isOpen === 0) {
                        delete params.rechargeRights.detail;
                        this.showRechargeError = false;
                    }

                    if (presentRights.isOpen === 0) {
                        delete params.presentRights.detail;
                        this.showServiceError = false;
                    }

                    if (discountRights.isOpen === 0) {
                        delete params.discountRights.detail;
                        this.showDiscountError = false;
                    }

                    if (customRights.isOpen === 0) {
                        delete params.customRights.detail;
                    }

                    if (this.discountRights.isOpen) {
                        const { goodsList } = this.discountRights.detail;
                        this.showDiscountError = goodsList.length === 0;

                        // 处理折扣权益goodsList入参
                        const discountGoodsList = clone(this.discountGoodsListTracker?.operationData?.value || []);
                        discountGoodsList && discountGoodsList.length && discountGoodsList.forEach((item) => {
                            delete item.goods;
                            delete item.productInfo;
                            const formatDiscount = reduceDiscount(item.discount, 10, 3);
                            item.discount = item.discountType ? item.discount : formatDiscount;
                            if (item.optType === OPT_TYPES.DELETE && item.exceptItems?.length) {
                                item.exceptItems = item.exceptItems.map((it) => {
                                    return {
                                        ...it,
                                        optType: OPT_TYPES.DELETE,
                                    };
                                });
                            } else {
                                if (item?.exceptOperationTrackerItems?.length) {
                                    item.exceptItems = item.exceptOperationTrackerItems;
                                    delete item.exceptOperationTrackerItems;
                                    delete item.originExceptItems;
                                }
                            }
                        });
                        params.discountRights.detail.goodsList = discountGoodsList;
                        delete params.discountRights.originGoodsList;
                    } else {
                        if (params.discountRights.originGoodsList && params.discountRights.originGoodsList.length) {
                            if (!params.discountRights.detail) {
                                params.discountRights.detail = {
                                    goodsList: [],
                                };
                            }
                            params.discountRights.detail.goodsList = params.discountRights.originGoodsList.map((item) => {
                                return {
                                    ...item,
                                    optType: OPT_TYPES.DELETE,
                                };
                            });
                        }
                        delete params.discountRights.originGoodsList;
                    }

                    if (this.presentRights.isOpen) {
                        const {
                            singlePresentList, packagePresentList,
                        } = this.presentRights.detail;
                        this.showServiceError = (singlePresentList && singlePresentList.length === 0) && (packagePresentList && packagePresentList.length === 0);

                        // 处理抵扣权益入参singlePresentList
                        const serviceSingleGoodsList = clone(this.serviceGoodsListTracker?.operationData?.value || []);
                        serviceSingleGoodsList && serviceSingleGoodsList.length && serviceSingleGoodsList.forEach((item) => {
                            delete item.productInfo;
                        });
                        params.presentRights.detail.singlePresentList = serviceSingleGoodsList;
                        params.presentRights.detail.packagePresentList = clone(this.selfGoodsPackageListTracker).map((item) => {
                            item.goodsList = item.expectedGoodsList;
                            delete item.expectedGoodsList;
                            delete item.originGoodsList;
                            return item;
                        });
                    }

                    if (this.rechargeRights.isOpen) {
                        const {
                            goodsPayWithBalance, balanceUsingScope,
                        } = this.rechargeRights.detail;
                        this.showRechargeError = goodsPayWithBalance.length === 0 && !balanceUsingScope;

                        // 处理充送权益入参goodsPayWithBalance
                        const rechargeGoodsList = clone(this.rechargeGoodsListTracker?.operationData?.value || []);
                        rechargeGoodsList && rechargeGoodsList.length && rechargeGoodsList.forEach((item) => {
                            if (item.optType === OPT_TYPES.DELETE && item.exceptItems?.length) {
                                item.exceptItems = item.exceptItems.map((it) => {
                                    return {
                                        ...it,
                                        optType: OPT_TYPES.DELETE,
                                    };
                                });
                            } else {
                                if (item?.exceptOperationTrackerItems?.length) {
                                    item.exceptItems = item.exceptOperationTrackerItems;
                                    delete item.exceptOperationTrackerItems;
                                    delete item.originExceptItems;
                                }
                            }
                            delete item.productInfo;
                        });
                        params.rechargeRights.detail.goodsPayWithBalance = rechargeGoodsList;
                    }

                    if (!this.baseSettings.name) {
                        return;
                    }

                    if (this.showDiscountError || this.showServiceError || this.showRechargeError) {
                        return;
                    }

                    if (customRights.isOpen === 1 && !customRights.detail) {
                        return;
                    }

                    if (!passValidate) {
                        return;
                    }

                    if (this.id) {
                        try {
                            const res = await MarketingAPI.promotions.updateCard(this.id, params);
                            if (res.data) {
                                this.formDialogVisible = false;
                                this.$Toast({
                                    message: '修改成功',
                                    type: 'success',
                                });
                            }
                        } catch (error) {
                            const { message } = error;
                            this.$Toast({
                                message: message || '更新失败!',
                                type: 'danger',
                            });
                        }
                    } else {
                        try {
                            const res = await MarketingAPI.promotions.addCard(params);
                            if (res.data) {
                                this.formDialogVisible = false;
                                this.$Toast({
                                    message: '保存成功',
                                    type: 'success',
                                });
                            }
                        } catch (error) {
                            const { message } = error;
                            this.$Toast({
                                message: message || '保存失败!',
                                type: 'error',
                            });
                        }
                    }
                } catch (error) {
                    console.log(error);
                } finally {
                    this.loading = false;
                }
            },

            handleDelete() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除该卡？',
                    onConfirm: () => {
                        this.deleteSubmit();
                    },
                });
            },

            async deleteSubmit() {
                const res = await MarketingAPI.promotions.delete(this.id);
                if (res.data === 200) {
                    this.formDialogVisible = false;
                } else {
                    this.$Toast({
                        message: res?.message || '删除失败!',
                        type: 'error',
                    });
                }
            },

            handleCancel() {
                this.formDialogVisible = false;
            },

            // 商品列表操作追踪
            handleOptTracker(goodsItem, optType, trackerType) {
                if (!goodsItem) return;
                if (optType === 'selfGoodsPackageListTracker') {
                    this.selfGoodsPackageListTracker = goodsItem;
                    return;
                }
                if (optType === OPT_TYPES.DELETE) {
                    this[`${trackerType}GoodsListTracker`].saveDeleteItem(goodsItem);
                    return;
                }
                if (optType === OPT_TYPES.ADD) {
                    this[`${trackerType}GoodsListTracker`].saveAddItem(goodsItem);
                    return;
                }
                if (optType === OPT_TYPES.UPDATE) {
                    this[`${trackerType}GoodsListTracker`].saveUpdateItem(goodsItem);
                }
            },
        },
    };
</script>

<style lang="scss" scoped>
    @import 'src/styles/theme';
    @import 'src/styles/mixin';

    .card-item-form-dialog-container {
        display: flex;
        width: 100%;
        height: 100%;

        .card-item-form-dialog_left {
            width: 375px;
            max-height: 100%;
            padding-top: 24px;
            padding-left: 24px;
            overflow-y: auto;
            border-right: 1px solid $P6;

            .card-item-form-dialog_left-content {
                max-height: 100%;
                padding-right: 4px;
                padding-bottom: 24px;
                overflow-y: scroll;

                @include scrollBar();
            }

            .rights-card-group {
                margin-top: 8px;
                background: $S2;
                border-radius: var(--abc-border-radius-small);
            }
        }

        .card-item-form-dialog_right {
            flex: 1;
            padding: 24px 24px 24px 20px;
            overflow-y: auto;
            background: $S2;

            &.has-card-obtained-tips {
                .promotion-card-form-content {
                    height: calc(100% - 40px);
                }
            }

            .card-obtained-tips {
                height: 40px;
                padding: 10px 24px;
                margin-top: -24px;
                margin-right: -24px;
                margin-bottom: 20px;
                margin-left: -20px;
                font-size: 14px;
                color: $Y2;
                text-align: center;
                background-color: $Y4;
            }

            .promotion-card-form-content {
                height: 100%;
                padding: 0 0 24px 20px;
                overflow-y: auto;
                overflow-y: overlay;

                @include scrollBar;

                .form-title {
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 14px;
                    color: $T1;
                }

                .dashed-line {
                    margin-top: 8px;
                    margin-bottom: 12px;
                    border-bottom: 1px dashed $P6;
                }
            }

            .card-form-footer {
                display: flex;
                justify-content: flex-end;
                margin-top: 15px;
            }
        }
    }
</style>
