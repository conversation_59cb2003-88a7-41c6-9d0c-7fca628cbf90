@import 'src/styles/theme.scss';

.promotion-base-form-card-fee {
    display: flex;
    align-items: center;

    .small-input {
        margin: 0 8px;
    }
}

.abc-form-item .abc-form-item-label .label-name {
    color: $T2;
}

.reduction-coupon-item {
    margin-top: -5px;

    .small-input {
        .abc-input__inner {
            height: 24px;
        }
    }

    .abc-form-item-label {
        align-self: flex-start;
        height: 24px;
        line-height: 24px;
    }

    .abc-form-item-content {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .activity-date-range {
        display: flex;
        align-items: center;
        margin-left: 4px;

        .abc-date-picker {
            height: 24px;
            margin: 0 4px;

            .abc-input__inner {
                height: 24px;
            }
        }
    }

    .abc-radio {
        height: 24px;

        & + .abc-radio {
            margin-left: 24px;
        }
    }

    .delete-btn {
        color: #ff3333;
    }

    .select-promotion-range {
        flex: 1;

        .range-bar {
            display: flex;
            align-items: center;
            height: 24px;
            font-size: 12px;
            font-weight: 400;
            color: rgba(150, 164, 179, 1);

            .original-price {
                margin-left: auto;
                text-align: right;
            }
        }
    }

    .preferential-settings {
        flex: 1;

        .preferential-rules {
            margin-bottom: 10px;
            border-bottom: 1px dashed $P6;

            .preferential-level {
                padding-bottom: 10px;

                .title {
                    height: 24px;
                    margin-bottom: 16px;
                    font-size: 14px;
                    font-weight: bold;
                    line-height: 24px;
                }

                .content {
                    display: flex;
                    align-items: center;
                }

                .abc-input__inner {
                    height: 24px;
                }
            }

            .gift-rules-content {
                display: flex;
                align-items: center;
                padding: 10px 6px;
                border-top: 1px dashed #e6eaee;

                .abc-form-item {
                    margin-bottom: 0;
                }

                .abc-input__inner {
                    height: 24px;
                }

                .rule-title {
                    flex: 0 0 auto;
                    padding: 4px 8px;
                    font-size: 12px;
                    line-height: 12px;
                    color: #ff3333;
                    border: 1px solid #ff3333;
                    border-radius: 10px;
                }

                .item-type {
                    align-self: flex-start;
                    width: 42px;
                    min-width: 42px;
                    max-width: 42px;
                    padding: 4px 8px;
                    margin-top: 2px;
                    margin-right: 12px;
                    font-size: 12px;
                    line-height: 12px;
                    color: #ff3333;
                    border: 1px solid #ff3333;
                    border-radius: 10px;
                }

                .item-detail {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;

                    .label-input {
                        margin-right: 8px;
                    }
                }
            }
        }

        .add-preferential-level {
            .tips-info {
                margin-left: 16px;
                font-size: 12px;
                color: $T3;
            }
        }
    }

    .error {
        margin-left: 8px;
        font-size: 12px;
        color: $R2;
    }
}

.promotion-card-form-content {
    padding: 20px;

    .card-form-item-text {
        color: #000000;
    }

    .form-title {
        margin-bottom: 20px;
        font-size: 14px;
        font-weight: 500;
        line-height: 14px;
        color: #000000;

        .comment {
            margin-left: 24px;
            font-size: 12px;
            line-height: 14px;
            color: #aab4bf;
        }
    }

    .rights-form-item-wrapper {
        margin-bottom: 20px;

        .card-form-item-text {
            color: #000000;
        }

        .explain {
            margin-top: -15px;
            margin-left: 110px;
            font-size: 12px;
            color: #aab4bf;
        }
    }

    .dashed-line {
        margin-top: 8px;
        margin-bottom: 12px;
        border-bottom: 1px dashed #e6eaee;
    }

    .rights-form-item {
        .rule-item-wrapper {
            position: relative;
            height: 24px;
            margin-top: 8px;
            line-height: 24px;

            &:first-child {
                margin-top: 3px;
            }

            .delete-icon {
                position: absolute;
                display: inline-block;
                width: 24px;
                height: 24px;
            }
        }

        .is-edit,
        .is-edit input {
            color: $Y2;
        }

        .recharge-amount {
            position: relative;
            margin-top: -2px;

            .recharge-amount-repeat-alert {
                position: absolute;
                left: 0;
                z-index: 5;
                padding: 6px;
                margin-top: -7px;
                font-size: 14px;
                line-height: 14px;
                color: $T1;
                white-space: nowrap;
                background: rgba(255, 253, 236, 1);
                border: 1px solid rgba(203, 184, 22, 1);
                box-shadow: 2px 2px 0 0 rgba(0, 0, 0, 0.1);
            }
        }

        .small-input {
            .abc-input__inner {
                height: 24px;
            }
        }

        .abc-form-item-label {
            align-self: flex-start;
            height: 24px;
            line-height: 24px;
        }

        .abc-form-item-content {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-items: center;
        }

        .abc-radio {
            height: 24px;

            & + .abc-radio {
                margin-left: 24px;
            }
        }

        .error {
            margin-left: 8px;
            font-size: 12px;
            color: $R2;
        }
    }

    .add-product-wrapper {
        display: flex;
        align-items: center;
        margin: -14px 0 20px 110px;

        .add-product-rule {
            margin-left: 5px;
            font-size: 12px;
            color: #aab4bf;
        }
    }

    .rights-threshold-radio {
        .abc-radio-original {
            position: absolute;
            top: 0;
            left: 136px;
            display: block;
            width: 80px;
            height: 24px;
            border-color: transparent;
        }
    }

    .customer-power-limit {
        padding-top: 1px;
        overflow-y: auto;
    }

    label {
        font-size: 14px;
        color: $T2;
    }

    .set-tips {
        margin-left: 24px;
        color: $Y2;
    }

    .separator-line {
        border-bottom: 1px solid $P6;
    }

    .discount-item-spacing {
        margin-right: 4px;
        margin-bottom: 4px;
    }

    .close-img {
        width: 28px;
        height: 28px;
        margin-left: 14px;
        cursor: pointer;
    }
}

.input-line-hove {
    display: flex;
    align-items: center;
    width: 100%;
}

.customer-power-wrapper {
    margin-bottom: 9px;
}

.action-line {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.abc-radio .abc-radio-input .abc-radio-inner {
    background-color: #ffffff;
}
