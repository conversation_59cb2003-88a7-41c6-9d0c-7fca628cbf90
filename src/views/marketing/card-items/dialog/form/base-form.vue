<template>
    <biz-setting-form :label-width="110" style="padding-bottom: 24px;">
        <biz-setting-form-group title="基础设置">
            <abc-form
                ref="abcForm"
                label-position="left"
                item-no-margin
            >
                <biz-setting-form-item label-line-height-size="medium" label="卡名称">
                    <abc-form-item :validate-event="validateName">
                        <abc-input
                            v-model="localValue.name"
                            v-abc-focus-selected
                            :disabled="disableCardEdit"
                            :width="240"
                            :max-length="20"
                            :input-custom-style="{
                                backgroundColor: '#ffffff'
                            }"
                        ></abc-input>
                    </abc-form-item>
                </biz-setting-form-item>
                <biz-setting-form-item label="卡费用">
                    <abc-form-item>
                        <abc-radio-group
                            v-model="localValue.isNeedCardFee"
                            :item-block="true"
                            :disabled="disableCardEdit"
                        >
                            <abc-radio :label="0" :disabled="disableCardEdit">
                                免费
                            </abc-radio>
                            <abc-radio :label="1" :disabled="disableCardEdit">
                                <abc-flex :gap="8" style="line-height: 32px;">
                                    <abc-text>
                                        付费
                                    </abc-text>
                                    <abc-form-item v-if="localValue.isNeedCardFee === 1" :validate-event="validateCardFee">
                                        <abc-input
                                            v-model="localValue.cardFee"
                                            v-abc-focus-selected
                                            :disabled="!localValue.isNeedCardFee || disableCardEdit"
                                            margin="0 4px"
                                            :width="76"
                                            type="number"
                                            :config="{
                                                formatLength: 2, max: 9999999
                                            }"
                                            :input-custom-style="{
                                                textAlign: 'start',
                                                backgroundColor: '#ffffff',
                                                marginRight: 0,
                                            }"
                                        >
                                            <abc-text slot="append">
                                                元
                                            </abc-text>
                                        </abc-input>
                                    </abc-form-item>
                                </abc-flex>
                            </abc-radio>
                        </abc-radio-group>
                    </abc-form-item>
                </biz-setting-form-item>
                <biz-setting-form-item label="有效期">
                    <abc-form-item>
                        <abc-radio-group
                            v-model="localValue.validityType"
                            :item-block="true"
                            @change="handleValidityTypeChange"
                        >
                            <abc-radio :label="0" :disabled="disableCardEdit">
                                永久有效
                            </abc-radio>
                            <abc-radio :label="1" :disabled="disableCardEdit">
                                <abc-flex :gap="8" style="line-height: 32px;" align="center">
                                    自定义效期
                                    <abc-form-item
                                        v-if="localValue.validityType"

                                        :validate-event="validateCardValidityPeriod"
                                        :validate-params="localValue"
                                    >
                                        <abc-input
                                            v-model="localValue.validityPeriod"
                                            class="small-input"
                                            :width="67"
                                            margin="0 4px"
                                            type="number"
                                            :config="{ max: 999999 }"
                                            :input-custom-style="{
                                                textAlign: 'start',
                                                backgroundColor: '#ffffff',
                                                marginRight: 0,
                                                marginLeft: '5px',
                                                borderTopRightRadius: 0,
                                                borderBottomRightRadius: 0,
                                            }"
                                        >
                                        </abc-input>
                                        <abc-select
                                            v-model="localValue.validityPeriodUnit"
                                            width="42"
                                            class="small-input"
                                            :input-style="{
                                                borderLeft: 'none',
                                                borderTopLeftRadius: 0,
                                                borderBottomLeftRadius: 0,
                                            }"
                                        >
                                            <abc-option
                                                v-for="item in unitOptions"
                                                :key="item.label"
                                                :label="item.label"
                                                :value="item.value"
                                            ></abc-option>
                                        </abc-select>
                                    </abc-form-item>
                                </abc-flex>
                            </abc-radio>
                        </abc-radio-group>
                    </abc-form-item>
                </biz-setting-form-item>
                <biz-setting-form-item v-if="isOpenWeChatPay" :label="`微${$app.institutionTypeWording}自助办理`">
                    <biz-setting-form-item-tip :tip="`患者可在微${ $app.institutionTypeWording }里看到该卡项目并自助开卡`">
                        <abc-form-item>
                            <abc-checkbox
                                v-model="localValue.isShowInWeClinic"
                                :disabled="openWeChatPayDisabled"
                                type="number"
                            >
                                允许
                            </abc-checkbox>
                        </abc-form-item>
                    </biz-setting-form-item-tip>
                </biz-setting-form-item>

                <biz-setting-form-item label="开卡协议">
                    <biz-setting-form-item-indent>
                        <biz-setting-form-item-tip :tip="`开启后，用户在微${ $app.institutionTypeWording }开卡时可查看开卡协议`">
                            <abc-form-item>
                                <abc-checkbox
                                    v-model="localValue.isShowAgreement"
                                    :disabled="disableCardEdit"
                                    type="number"
                                >
                                    <abc-flex :gap="8" justify="center">
                                        开启
                                        <p class="explain">
                                            <abc-button
                                                size="small"
                                                class="view-case-btn"
                                                type="text"
                                                @click="handleShowAgreementCase"
                                            >
                                                查看案例
                                            </abc-button>
                                        </p>
                                    </abc-flex>
                                </abc-checkbox>
                            </abc-form-item>
                        </biz-setting-form-item-tip>
                        <template #content>
                            <div v-if="localValue.isShowAgreement" style="margin-top: 12px;">
                                <abc-textarea
                                    v-model.trim="localValue.agreement"
                                    :height="281"
                                    placeholder="请输入内容"
                                    :maxlength="maxLength"
                                    show-max-length-tips
                                    @input="emojiFilter"
                                >
                                </abc-textarea>
                            </div>
                        </template>
                    </biz-setting-form-item-indent>
                </biz-setting-form-item>
                <biz-setting-form-item label="卡项共享">
                    <biz-setting-form-item-tip tip="开启后，该卡项可共享给其他患者">
                        <abc-form-item>
                            <abc-checkbox
                                v-model="localValue.isShare"
                                :disabled="disableCardEdit"
                                type="number"
                            >
                                允许
                            </abc-checkbox>
                        </abc-form-item>
                    </biz-setting-form-item-tip>
                </biz-setting-form-item>
                <biz-setting-form-item v-if="!isSingleStore" label="门店限制">
                    <biz-setting-form-item-indent>
                        <abc-radio-group v-model="localValue.isAllClinics" @change="handleLimitStoreTypeChange">
                            <abc-radio :label="1">
                                全部门店可用
                            </abc-radio>
                            <abc-radio :label="0">
                                部分门店可用
                            </abc-radio>
                        </abc-radio-group>
                        <template v-if="localValue.isAllClinics === 0" #content>
                            <biz-employee-panel-selector
                                v-model="limitStoreList"
                                add-text="选择门店"
                                result-title="选择门店"
                                search-placeholder="搜索门店"
                                :employees="chainSubClinicList"
                                :default-checked-keys="defaultLimitStoreList"
                                leaf-icon="s-organization-color"
                                :max-name-text-length="8"
                                :can-delete-all="true"
                                :confirm-button-disabled="false"
                                @confirm="handleLimitStoreConfirm"
                            ></biz-employee-panel-selector>
                        </template>
                    </biz-setting-form-item-indent>
                </biz-setting-form-item>
            </abc-form>
        </biz-setting-form-group>
        <abc-dialog
            v-if="showCardAgreementDialog"
            v-model="showCardAgreementDialog"
            title="开卡协议案例说明"
            append-to-body
            size="huge"
        >
            <abc-flex vertical :gap="24">
                <abc-flex vertical :gap="12">
                    <abc-text bold>
                        展示位置
                    </abc-text>
                    <abc-flex :gap="8">
                        <img style="width: 300px; height: 650px;" src="~assets/images/marketing/<EMAIL>" alt="" />
                        <img style="width: 300px; height: 650px;" src="~assets/images/marketing/<EMAIL>" alt="" />
                    </abc-flex>
                </abc-flex>
                <abc-flex vertical :gap="12">
                    <div>
                        <abc-text bold>
                            内容示例
                        </abc-text>
                        <abc-button type="text" @click="copyCardAgreement">
                            复制内容
                        </abc-button>
                    </div>
                    <div class="text-wrapper">
                        <div class="text" v-html="agreementContentHtml"></div>
                    </div>
                </abc-flex>
            </abc-flex>
        </abc-dialog>
    </biz-setting-form>
</template>

<script>
    import EmojiFilter from 'utils/emoji-filter';
    import { mapGetters } from 'vuex';
    import { decodeTextareaStringToHTML } from 'utils/xss-filter';
    import AbcAccess from '@/access/utils.js';
    import { isChainSubClinic } from 'views/common/clinic.js';
    import ClinicAPI from 'api/clinic';
    import BizSettingFormItem from '@/components-composite/setting-form/src/views/item.vue';
    import BizSettingForm from '@/components-composite/setting-form/src/views/index.vue';
    import BizSettingFormGroup from '@/components-composite/setting-form/src/views/group.vue';
    import BizSettingFormItemTip from '@/components-composite/setting-form/src/views/tip.vue';
    import BizSettingFormItemIndent from '@/components-composite/setting-form/src/views/indent.vue';
    import BizEmployeePanelSelector from '@/components-composite/biz-employee-panel-selector/src/views/index.vue';

    export default {
        components: {
            BizSettingFormItemIndent,
            BizSettingFormItemTip,
            BizSettingFormGroup,
            BizSettingForm,
            BizSettingFormItem,
            BizEmployeePanelSelector,
        },
        props: {
            value: {
                type: Object,
                default: () => ({
                    name: '',
                    cardFee: '',
                    validityType: 0,
                    validityPeriod: '',
                    validityPeriodUnit: 2,
                    isNeedCardFee: 0,
                    isShowInWeClinic: 0,
                    isShare: 0,
                    isShowAgreement: 0,
                    agreement: '',
                    isAllClinics: 1,
                    clinicIds: [],
                }),
            },
            disableCardEdit: {
                type: Boolean,
                default: false,
            },
            unitOptions: {
                type: Array,
                default: () => [],
            },
            maxLength: {
                type: Number,
                default: 2000,
            },
        },
        data() {
            return {
                localValue: {
                    name: '',
                    cardFee: '',
                    validityType: 0,
                    isNeedCardFee: 0,
                    isShowInWeClinic: 0,
                    isShowAgreement: 0,
                    agreement: '',
                    isShare: 0,
                    validityPeriodUnit: 2,
                    validityPeriod: '',
                    isAllClinics: 1, // 门店限制
                    clinicIds: [], // 限制的门店ids
                },
                showCardAgreementDialog: false,
                agreementContent: '一、协议目的\n' +
                    '\n' +
                    '理疗馆是一家专业提供理疗服务的机构，旨在提升客户的健康水平和康复效果。\n' +
                    '客户希望购买理疗馆提供的理疗服务，并在此协议中确认其了解和接受相关条款。\n' +
                    '\n' +
                    '二、理疗卡内容及服务范围\n' +
                    '\n' +
                    '客户购买的理疗卡包括具体的理疗项目和相应的服务次数。\n' +
                    '理疗馆将根据客户需求和身体状况为其提供相应的理疗服务。\n' +
                    '理疗卡仅限于客户个人使用，不可转让给他人或以任何形式进行交易。\n' +
                    '\n' +
                    '三、费用及支付方式\n' +
                    '\n' +
                    '客户购买理疗卡时，应按照理疗馆规定的价格进行支付。\n' +
                    '支付方式可以选择现金、银行卡或其他线上支付方式。\n' +
                    '一经购买，理疗卡费用不可退款。\n' +
                    '\n' +
                    '四、使用有效期限\n' +
                    '\n' +
                    '理疗卡的使用期限为购买之日起至有效期结束。\n' +
                    '客户应在有效期内使用理疗卡，逾期将视为自动放弃未使用次数。\n' +
                    '\n' +
                    '五、预约及取消政策\n' +
                    '\n' +
                    '客户在使用理疗卡时，需提前预约具体服务时间。\n' +
                    '如客户无法如约前来理疗馆接受服务，请提前24小时通知理疗馆进行取消或调整。\n' +
                    '若客户未提前通知取消或多次爽约，理疗馆有权暂停或终止对其提供服务。\n' +
                    '\n' +
                    '六、免责条款\n' +
                    '\n' +
                    '理疗馆将按照专业标准和合理技术提供理疗服务，但不能保证服务结果完全符合客户预期。\n' +
                    '客户在接受理疗服务期间，理疗馆将尽力确保安全，并提供必要的监护和指导，但客户个人的身体状况和行为也会影响服务效果和结果。\n' +
                    '如因不可抗力导致理疗服务无法正常进行，理疗馆将与客户协商重新安排服务时间。\n' +
                    '\n' +
                    '七、争议解决\n' +
                    '\n' +
                    '对于因履行本协议所引起的争议，双方应通过友好协商解决。\n' +
                    '如协商无法达成一致，任何一方均可向有管辖权的法院提起诉讼。\n' +
                    '\n' +
                    '八、其他条款\n' +
                    '\n' +
                    '本协议不得违反相关法律法规，如有违反，以法律法规为准。\n' +
                    '双方同意在签署日期后具有法律效力。',

                chainSubClinicList: [], // 连锁下子店列表
                limitStoreList: [], // 限制的门店
                limitStoreListLength: 0,
                accessKey: AbcAccess.accessMap.MARKET_CARD,
            };
        },
        computed: {
            ...mapGetters(['weChatPayConfig', 'isOpenMp', 'isSingleStore']),

            // 是否开通了微诊所和微信自助支付
            isOpenWeChatPay() {
                return this.weChatPayConfig.weChatPaySwitch === 2 &&
                    this.weChatPayConfig.jsapiPayStatus === 2 &&
                    this.isOpenMp;
            },
            openWeChatPayDisabled() {
                return this.disableCardEdit || !!(this.localValue.isAllClinics === 0 && this.limitStoreListLength);
            },
            agreementContentLen() { // 内容的长度
                return (this.localValue.agreement && this.localValue.agreement.length) || 0;
            },
            agreementContentHtml() {
                return decodeTextareaStringToHTML(this.agreementContent);
            },
            defaultLimitStoreList() {
                return this.limitStoreList.map((item) => item.id);
            },
        },

        watch: {
            value: {
                handler(value) {
                    Object.assign(this.localValue, this.getInitData(), { ...value });
                    this.localValue.cardFee = value.cardFee ? value.cardFee : '';
                    this.limitStoreListLength = value?.clinicIds?.length || 0;
                    if (!this.isSingleStore) {
                        this.fetchChainClinics(value);
                    }
                },
                immediate: true,
            },

            localValue: {
                handler(val) {
                    const payload = {
                        ...val,
                        name: EmojiFilter(val.name.trim()),
                    };
                    this.$emit('change', payload);
                },
                deep: true,
            },

            limitStoreList: {
                handler(val) {
                    this.limitStoreListLength = val?.length || 0;
                    this.localValue.clinicIds = val?.map((item) => item.id) || [];
                },
                deep: true,
            },
        },

        created() {
            this.$store.dispatch('initWeChatPayConfig');
        },

        methods: {
            getInitData() {
                return {
                    name: '',
                    cardFee: '',
                    isNeedCardFee: 0,
                    isShowInWeClinic: 0,
                    isShowAgreement: 0,
                    agreement: '',
                    isShare: 0,
                    validityType: 0,
                    validityPeriod: '',
                    validityPeriodUnit: 2,
                };
            },
            handleValidityTypeChange() {
                if (!this.localValue.validityType) {
                    this.localValue.validityPeriod = '';
                    this.localValue.validityPeriodUnit = 2;
                }
            },
            validateName(value, callback) {
                if (!value && !this.disableCardEdit) {
                    callback({
                        validate: false,
                        message: '名称不能为空',
                    });
                }
            },
            validateCardValidityPeriod(value, callback, params) {
                if (params.validityPeriod) {
                    if (!value) {
                        callback({
                            validate: false,
                            message: '请填写效期',
                        });
                    }
                }
            },

            validateCardFee(value, callback) {
                if (this.localValue.isNeedCardFee && !value) {
                    callback({
                        validate: false,
                        message: '请填写卡费',
                    });
                }
            },

            handleShowAgreementCase() {
                this.showCardAgreementDialog = true;
            },

            emojiFilter(val) {
                this.localValue.agreement = EmojiFilter(val);
            },

            copyCardAgreement() {
                const textarea = document.createElement('textarea');
                textarea.value = this.agreementContent;
                textarea.style.position = 'fixed';
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
            },

            async fetchChainClinics(value) {
                try {
                    if (!this.chainSubClinicList?.length) {
                        const { data } = await ClinicAPI.chainClinicV3();
                        this.chainSubClinicList = (data.rows || []).filter((item) => isChainSubClinic(item)).map((item) => {
                            item.disabled = !AbcAccess.checkAvailableByEdition(this.accessKey, item.edition);
                            return item;
                        });
                    }
                    if (value?.clinicIds?.length) {
                        this.limitStoreList = this.chainSubClinicList.filter((item) => value.clinicIds.includes(item.id));
                    }
                } catch (error) {
                    console.log('fetchChainClinics error', error);
                }
            },

            handleLimitStoreConfirm(payload) {
                if (this.isOpenWeChatPay && this.limitStoreListLength === 0 && payload?.length) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '限制门店消费后，将无法在微诊所场景中自助开卡',
                    });
                    this.localValue.isShowInWeClinic = 0;
                }
            },

            handleLimitStoreTypeChange(val) {
                if (this.isOpenWeChatPay && this.limitStoreList.length && val === 0) {
                    this.localValue.isShowInWeClinic = 0;
                }
            },
        },
    };
</script>

<style lang="scss">
.text-wrapper {
    padding: 6px 8px 8px;
    overflow: auto;
    font-size: 14px;
    background: #f7f7f7;
    border: 1px solid $P3;
    border-radius: var(--abc-border-radius-small);
}
</style>
