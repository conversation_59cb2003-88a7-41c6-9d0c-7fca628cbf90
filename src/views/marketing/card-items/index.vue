<template>
    <abc-layout
        style="height: 100%;"
        :class="{
            'market-crm-module-table': hiddenScroll,
        }"
    >
        <abc-layout-header style="padding: 0;">
            <abc-tabs-v2
                :option="displayTabOptions"
                style="padding: 0 24px 0 16px;"
                size="huge"
                @change="handleTabsChange"
            ></abc-tabs-v2>
        </abc-layout-header>
        <router-view></router-view>
    </abc-layout>
</template>

<script type="text/ecmascript-6">
    import { getRouterChildren } from 'router/filter-router.js';

    export default {
        computed: {
            displayTabOptions () {
                const { routes } = this.$router.options;
                return getRouterChildren(routes, '卡项')
                    .filter((item) => !item.meta.hidden)
                    .map((item) => ({
                        label: item.meta.name,
                        value: item.name,
                        path: item.path,
                    }));
            },
            hiddenScroll() {
                if (this.$route?.path?.indexOf('card-holder-manage') > -1) {
                    return true;
                }
                return false;
            },
        },
        methods: {
            handleTabsChange(index, item) {
                this.$router.push({
                    name: item.value,
                });
            },
        },
    };
</script>

<style lang="scss">
// TODO基础组件替换，医院样式兼容 12.17 此处杨哥休完产假需要再商量
.market-crm-module-table {
    .crm-module {
        .cardholder-manage {
            height: 100% !important;
            margin-top: 0 !important;
            border-top: none !important;
        }
    }
}
</style>
