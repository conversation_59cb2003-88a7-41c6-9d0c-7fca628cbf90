@import "../../styles/theme";
@import "./coupon/index";

.marketing-container {
    background-color: #ffffff;
    border-radius: 0 var(--abc-border-radius-small) var(--abc-border-radius-small) 0;
    // 满减和优惠券 form-dialog
    .full-reduction-coupon-wrapper {
        .full-reduction-coupon-form {
            .reduction-coupon-name {
                .abc-input__inner {
                    padding-right: 44px;
                }
            }

            .reduction-coupon-item {
                &.reduction-coupon-item_label {
                    margin-bottom: 12px;
                }

                .small-input {
                    .abc-input__inner {
                        height: 24px;
                    }
                }

                .abc-form-item-label {
                    align-self: flex-start;
                    height: 24px;
                    line-height: 24px;
                }

                .abc-form-item-content {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                }

                .activity-date-range {
                    display: flex;
                    align-items: center;
                    margin-left: 4px;

                    .abc-date-picker {
                        height: 24px;
                        margin: 0 4px;

                        .abc-input__inner {
                            height: 24px;
                        }
                    }
                }

                .abc-radio {
                    height: 24px;

                    & + .abc-radio {
                        margin-left: 24px;
                    }
                }

                .delete-btn {
                    color: #ff3333;
                }

                .tips-info {
                    color: #7a8794;
                }

                .select-promotion-range {
                    flex: 1;

                    .range-bar {
                        display: flex;
                        align-items: center;
                        height: 24px;
                        font-size: 12px;
                        font-weight: 400;
                        color: rgba(150, 164, 179, 1);

                        .tips-info {
                            font-size: 12px;
                            color: $T3;
                        }

                        .original-price {
                            margin-left: auto;
                            text-align: right;
                        }
                    }
                }

                .preferential-settings {
                    flex: 1;

                    .preferential-rules {
                        margin-bottom: 10px;
                        border-bottom: 1px dashed $P6;

                        .preferential-level {
                            padding-bottom: 10px;

                            .title {
                                height: 24px;
                                margin-bottom: 16px;
                                font-size: 14px;
                                font-weight: bold;
                                line-height: 24px;
                            }

                            .content {
                                display: flex;
                                align-items: center;
                            }

                            .abc-input__inner {
                                height: 24px;
                            }
                        }

                        .gift-rules-content {
                            display: flex;
                            align-items: center;
                            padding: 10px  6px;
                            border-top: 1px dashed #e6eaee;

                            .abc-form-item {
                                margin-bottom: 0;
                            }

                            .abc-input__inner {
                                height: 24px;
                            }

                            .rule-title {
                                flex: 0 0 auto;
                                padding: 4px 8px;
                                font-size: 12px;
                                line-height: 12px;
                                color: #ff3333;
                                border: 1px solid #ff3333;
                                border-radius: 10px;
                            }

                            .item-type {
                                align-self: flex-start;
                                width: 42px;
                                min-width: 42px;
                                max-width: 42px;
                                padding: 4px 8px;
                                margin-top: 2px;
                                margin-right: 12px;
                                font-size: 12px;
                                line-height: 12px;
                                color: #ff3333;
                                border: 1px solid #ff3333;
                                border-radius: 10px;
                            }

                            .item-detail {
                                display: flex;
                                flex-wrap: wrap;
                                align-items: center;

                                .label-input {
                                    margin-right: 8px;
                                }
                            }
                        }
                    }

                    .add-preferential-level {
                        .tips-info {
                            margin-left: 16px;
                            font-size: 12px;
                            color: $T3;
                        }
                    }
                }

                .error {
                    margin-left: 8px;
                    font-size: 12px;
                    color: $R2;
                }
            }

            .add-product {
                flex-shrink: 0;
            }
        }
    }

    .table-box {
        .table-body {
            .table-tr {
                .copy-btn {
                    display: none;
                }

                &:hover {
                    .copy-btn {
                        display: inline-block;
                    }
                }
            }
        }
    }

    .abc-tabs {
        .abc-tabs-item-active {
            color: $T1;
        }
    }

    .abc-checkbox-wrapper {
        > .abc-checkbox__label {
            margin-right: 8px;
        }
    }

    .clearfix::after {
        display: block;
        height: 0;
        clear: both;
        font-size: 0;
        visibility: hidden;
        content: ".";
    }

    .clearfix {
        zoom: 1;
    }
}
