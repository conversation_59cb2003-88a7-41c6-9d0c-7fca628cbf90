
<template>
    <abc-space is-compact class="marketing-module__label-input-new">
        <abc-form-label :width="138">
            <abc-flex
                slot="reference"
                align="center"
                gap="small"
                class="label-box"
            >
                <abc-icon size="14" :icon="icon"></abc-icon>
                <abc-text class="name" theme="black" :data-cy="`${dataCy}-${name}`">
                    {{ name }}
                </abc-text>
                <abc-text v-if="statusName" class="status-name" theme="warning-light">
                    {{ statusName }}
                </abc-text>
            </abc-flex>
        </abc-form-label>
        <abc-form-item
            required
            class="coupon-item"
            :validate-event="validate(value)"
        >
            <abc-input
                type="number"
                :width="76"
                :config="config"
                :disabled="disabled"
                :value="value"
                :data-cy="`${dataCy}-input-${name}`"
                @input="handleChange"
            >
                <abc-text slot="appendInner" theme="gray-light">
                    {{ unit }}
                </abc-text>
            </abc-input>
        </abc-form-item>
        <abc-delete-icon
            v-if="!disabled"
            class="coupon-delete-icon"
            theme="dark"
            variant="fill"
            @delete="handleDelete"
        ></abc-delete-icon>
    </abc-space>
</template>

<script>
    export default {
        model: {
            prop: 'value',
            event: 'change',
        },
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            icon: {
                type: String,
                default: '',
            },
            name: {
                type: String,
                default: '',
            },
            dataCy: {
                type: String,
                default: '',
            },
            value: {
                type: [ String, Number ],
                default: '',
            },
            unit: {
                type: String,
            },
            statusName: String,
            isTakeOff: Boolean,
            config: {
                type: Object,
                default () {
                    return {
                        max: null, // 最多输入多少位
                        formatLength: 0, // 支持小数点后几位
                        supportZero: false, // 是否支持零
                        supportFraction: false, // 是否支持分数
                        supportNegative: false, // 是否支持负数
                    };
                },
            },
        },
        data() {
            return {};
        },
        methods: {
            handleChange(val) {
                this.$emit('change', val);
            },
            validate(val) {
                if (Number(val) <= 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能填0',
                        });
                    };
                }
            },
            handleDelete() {
                this.$emit('delete');
            },
        },
    };
</script>

<style lang="scss">
    .marketing-module__label-input-new {
        position: relative;

        .label-box {
            width: 100%;
            height: 100%;

            .name {
                // width: 48px;
                flex: 1 0 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .status-name {
                flex-shrink: 0;
            }
        }

        .coupon-item {
            .abc-input__inner {
                border-top-right-radius: var(--abc-border-radius-small) !important;
                border-bottom-right-radius: var(--abc-border-radius-small) !important;
            }
        }

        &:hover {
            .coupon-delete-icon {
                display: block;
            }
        }

        .coupon-delete-icon {
            position: absolute;
            top: -8px;
            right: -8px;
            z-index: 10;
            display: none;
            cursor: pointer;

            img {
                width: 16px;
                height: 16px;
            }
        }
    }
</style>
