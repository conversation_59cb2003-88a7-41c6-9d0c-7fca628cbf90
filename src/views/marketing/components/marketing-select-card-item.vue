<template>
    <biz-setting-form-item
        class="marketing-select-card-item"
        :has-divider="hasDivider"
        vertical
        :label="label"
        :data-cy="cardItemCy"
    >
        <abc-flex align="flex-end" :gap="8">
            <slot name="radio-group"></slot>
            <abc-flex v-if="isShowCard" style="width: 100%; height: 20px;" justify="space-between">
                <abc-space>
                    <abc-button
                        :disabled="disabled"
                        variant="text"
                        size="small"
                        :data-cy="editBtnCy"
                        @click="openDialog"
                    >
                        {{ editBtnText }}
                    </abc-button>
                    <slot name="tips"></slot>
                </abc-space>
                <abc-button
                    v-if="!isDelete"
                    variant="text"
                    size="small"
                    :disabled="!tagData.length || disabled"
                    :data-cy="deleteBtnCy"
                    @click="isDelete = true"
                >
                    删除
                </abc-button>
                <abc-button
                    v-else
                    variant="text"
                    size="small"
                    :data-cy="quitDeleteBtnCy"
                    @click="isDelete = false"
                >
                    退出删除
                </abc-button>
            </abc-flex>
        </abc-flex>
        <abc-popover
            :disabled="disabledHover"
            trigger="hover"
            placement="top-start"
            theme="yellow"
        >
            <div slot="reference">
                <abc-card v-if="isShowCard">
                    <div
                        class="safe-login-card"
                        :class="{ 'has-config-item': tagData.length }"
                        :style="{ 'grid-template-columns': `repeat(auto-fill, ${ tagWidth }px)` }"
                    >
                        <template v-if="tagData.length">
                            <abc-tag-v2
                                v-for="(item, tIndex) in tagData"
                                :key="tIndex"
                                :style="{ 'width': `${tagWidth }px` }"
                                shape="square"
                                size="huge"
                                theme="default"
                                variant="outline"
                                :closable="isDelete"
                                close-resident
                                :min-width="tagWidth"
                                @close="handleDelete(tIndex)"
                            >
                                <abc-flex
                                    :gap="4"
                                    justify="space-between"
                                    align="center"
                                    style="width: 100%;"
                                >
                                    <abc-icon :icon="getIconFunction(item)" :size="14">
                                    </abc-icon>
                                    <abc-text
                                        siz="normal"
                                        tag="div"
                                        class="ellipsis"
                                        style="flex: 1;"
                                        :title="item.name"
                                    >
                                        {{ itemDisplayName(item) ? itemDisplayName(item) : item.name }}
                                    </abc-text>
                                </abc-flex>
                            </abc-tag-v2>
                        </template>
                        <abc-content-empty
                            v-else
                            top="32px"
                            value="暂无数据"
                            :show-icon="false"
                        ></abc-content-empty>
                    </div>
                </abc-card>
            </div>
            <div>{{ disabledHoverText }}</div>
        </abc-popover>
    </biz-setting-form-item>
</template>

<script>
    import clone from 'utils/clone';
    import {
        BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';

    export default {
        components: {
            BizSettingFormItem,
        },
        props: {
            dataCy: {
                type: String,
                default: '',
            },
            label: {
                type: String,
                required: true,
            },
            tagData: {
                type: Array,
                default: () => [],
                required: true,
            },
            getIconFunction: {
                type: Function,
                default: () => {},
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            disabledHover: {
                type: Boolean,
                default: true,
            },
            disabledHoverText: {
                type: String,
                default: '',
            },
            hasDivider: {
                type: Boolean,
                default: false,
            },
            isShowCard: {
                type: Boolean,
                default: true,
            },
            itemDisplayName: {
                type: Function,
                default: () => '',
            },
            editBtnText: {
                type: String,
                default: '修改',
            },
            tagWidth: {
                type: Number,
                default: 120,
            },
        },
        data() {
            return {
                isDelete: false,
            };
        },
        computed: {
            cardItemCy() {
                return this.dataCy ? `marketing-select-card-${this.dataCy}-card-item` : 'marketing-select-card-item';
            },
            editBtnCy() {
                return this.dataCy ? `marketing-select-card-${this.dataCy}-edit-btn` : 'marketing-select-card-item-edit-btn';
            },
            deleteBtnCy() {
                return this.dataCy ? `marketing-select-card-${this.dataCy}-delete-btn` : 'marketing-select-card-item-delete-btn';
            },
            quitDeleteBtnCy() {
                return this.dataCy ? `marketing-select-card-${this.dataCy}-quit-delete-btn` : 'marketing-select-card-item-quit-delete-btn';
            },
        },
        methods: {
            openDialog() {
                this.$emit('openDialog');
            },
            handleDelete(index) {
                const tempData = clone(this.tagData);
                tempData.splice(index, 1);
                this.$emit('update:tagData', tempData);
            },
        },

    };
</script>

<style lang="scss">
@import "src/styles/mixin.scss";

.marketing-select-card-item {
    .safe-login-card {
        width: 100%;
        min-height: 106px;
        max-height: 210px;
        padding: 10px 0 10px 10px;
        overflow-y: scroll;

        @include scrollBar(true);

        &.has-config-item {
            display: grid;
            grid-auto-flow: row dense;
            grid-gap: 8px 8px;
        }
    }
}
</style>
