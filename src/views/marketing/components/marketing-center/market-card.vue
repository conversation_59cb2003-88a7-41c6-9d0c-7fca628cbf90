<template>
    <div class="marketing-module__market-card-wrapper">
        <template v-if="item.isOpen && !isSpecialCard">
            <div class="analysis-header">
                <div class="left">
                    <div class="name">
                        {{ item.name }}
                    </div>
                    <div class="date-range">
                        2021-01-12~2021-12-12
                    </div>
                </div>
                <div class="right">
                    <abc-date-picker-bar
                        v-model="params.currentBasicInfoFilter"
                        :options="dateOptions"
                        :picker-options="pickerOptions"
                        value-format="YYYY-MM-DD"
                        :clearable="false"
                        @change="handleDateChange"
                    ></abc-date-picker-bar>
                </div>
            </div>
            <market-summary-card></market-summary-card>
            <div class="analysis-table">
                <abc-table-fixed2
                    :loading="loading"
                    :header="tableHeader"
                    :data="list"
                    style="width: 100%;"
                    :empty-opt="{ label: '暂无数据' }"
                >
                </abc-table-fixed2>
                <div class="analysis-table-footer">
                    <abc-button type="text" @click="handleViewAll">
                        查看全部
                    </abc-button>
                </div>
            </div>
        </template>
        <template v-if="item.isOpen && isSpecialCard">
            <div class="analysis-header-wrapper">
                <span class="name">{{ item.name }}</span>
                <span class="setting-text" @click="handleClick(item)">设置</span>
            </div>
            <div class="analysis-body">
                <div class="summary-item">
                    <span class="count">21280.00</span>
                    <span class="text">累计积分</span>
                </div>
                <div class="summary-item">
                    <span class="count">21280.00</span>
                    <span class="text">可用积分</span>
                </div>
                <div class="summary-item">
                    <span class="count">21280.00</span>
                    <span class="text">抵扣金额</span>
                </div>
            </div>
        </template>
        <template v-if="!item.isOpen">
            <div class="card-header">
                <div class="name" :class="{ 'is-special-card': isSpecialCard }">
                    {{ item.name }}
                </div>
            </div>
            <div class="card-body" :class="{ 'is-special-card': isSpecialCard }">
                <img :src="item.icon" alt="" />
                <div class="desc">
                    {{ item.desc }}
                </div>
                <div class="add-action" @click="handleClick(item)">
                    {{ item.actionText }}
                </div>
            </div>
        </template>
        <card-items-info-dialog
            v-if="cardItemsInfoDialogVisible"
            :visible.sync="cardItemsInfoDialogVisible"
        ></card-items-info-dialog>
    </div>
</template>

<script>
    import CardItemsInfoDialog from 'views/marketing/components/marketing-center/dialog/card-items-info-dialog.vue';
    import MarketSummaryCard from 'views/marketing/components/marketing-center/marketing-summary-card.vue';
    import { AbcDatePickerBar } from '@abc/ui-pc';
    const { DatePickerBarOptions } = AbcDatePickerBar;

    export default {
        components: {
            MarketSummaryCard,
            CardItemsInfoDialog,
        },
        props: {
            item: {
                type: Object,
                default: () => ({}),
            },
        },

        data() {
            return {
                dateOptions: [
                    DatePickerBarOptions.MONTH,
                    DatePickerBarOptions.LATEST_THREE_MONTH,
                    DatePickerBarOptions.LATEST_HALF_YEAR,
                    DatePickerBarOptions.YEAR,
                ],
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                params: {
                    filterStart: '',
                    filterEnd: '',
                    currentBasicInfoFilter: DatePickerBarOptions.MONTH.label,
                },
                list: [
                    {
                        name: '测试',
                        type: '测试',
                        count: 10,
                        user: '测试',
                    },
                    {
                        name: '测试',
                        type: '测试',
                        count: 10,
                        user: '测试',
                    },
                ],
                loading: false,
                cardItemsInfoDialogVisible: false,
            };
        },

        computed: {
            isSpecialCard() {
                return ['积分' , '消息推送'].includes(this.item.name);
            },
            tableHeader() {
                return [
                    {
                        label: '卡名(8)',
                        prop: 'name',
                        width: 100,
                    },
                    {
                        label: '类型',
                        prop: 'type',
                        width: 100,
                        titleStyle: {
                            textAlign: 'center',
                        },
                        bodyStyle: {
                            textAlign: 'center',
                        },
                    },
                    {
                        label: '开卡数',
                        prop: 'count',
                        width: 100,
                    },
                    {
                        label: '持卡人',
                        prop: 'user',
                        width: 100,
                    },
                ];
            },
        },

        methods: {
            handleViewAll() {
                this.cardItemsInfoDialogVisible = true;

            },
            handleDateChange(date) {
                this.params.filterStart = date[ 0 ];
                this.params.filterEnd = date[ 1 ];
            },

            handleClick(item) {
                const { value } = item;
                console.log('item', item);
                // this.showCardItemFormVisible = true;
                this.$router.push({
                    path: `/chain/marketing/${value}`,
                });
            },
        },

    };
</script>

<style lang="scss">
.marketing-module__market-card-wrapper {
    width: 528px;
    margin-bottom: 24px;
    background: #ffffff;
    border: 1px solid #e6eaee;
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);

    &:nth-child(2n + 2) {
        margin-left: 24px;
    }

    .analysis-header {
        display: flex;
        justify-content: space-between;
        padding: 12px;

        .left {
            .name {
                font-size: 16px;
                font-weight: 500;
            }

            .date-range {
                margin-top: 8px;
                font-size: 12px;
                line-height: 12px;
                color: #8d9aa8;
            }
        }

        .abc-radio-button .abc-radio-button-label {
            margin-right: -5px;
            color: #000000;
        }

        .abc-radio-button.is-checked .abc-radio-button-label {
            margin-right: -5px;
            color: #000000;
            background-color: #eff3f6;
        }

        .abc-date-picker .date-picker-reference {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 32px;
            margin-left: 0;
            cursor: pointer;
            border: 1px solid $P1;
            border-radius: 0 2px 2px 0;
        }
    }

    .analysis-summary-wrapper {
        display: flex;
        padding: 12px;

        .summary-item {
            margin-right: 27px;
            color: #000000;

            .text {
                display: block;
                margin-bottom: 9px;
                font-size: 12px;
                font-weight: 600;
            }

            .count {
                height: 24px;
                font-size: 20px;
                font-weight: 400;
            }
        }
    }

    .analysis-table {
        padding: 12px;

        .analysis-table-footer {
            padding-top: 12px;
            text-align: end;
        }

        // .abc-table-wrapper .table-body {
        //     height: 0;
        // }

        // .abc-table-wrapper .table-empty {
        //     top: 50px;
        // }
    }

    .analysis-header-wrapper {
        display: flex;
        justify-content: space-between;
        padding: 12px;
        margin-bottom: 10px;

        .name {
            font-size: 16px;
            font-weight: 500;
            color: #000000;
        }

        .setting-text {
            font-size: 14px;
            color: #005ed9;
        }
    }

    .analysis-body {
        display: flex;
        padding: 12px;

        .summary-item {
            flex: 1;
            text-align: center;

            &:nth-child(1) {
                text-align: start;
            }

            &:last-child {
                text-align: end;
            }

            .count {
                display: block;
                margin-bottom: 5px;
            }

            .text {
                font-size: 12px;
                color: #7a8794;
            }
        }
    }

    .card-header {
        padding: 12px;

        &.is-special-card {
            padding-top: 12px;
            padding-bottom: 0;
            padding-left: 12px;
        }

        .name {
            font-size: 16px;
            font-weight: 500;
        }
    }

    .card-body {
        margin: 76px auto;
        text-align: center;

        &.is-special-card {
            margin: 0;
            margin-top: -54px;
            margin-bottom: 12px;
        }

        img {
            display: block;
            width: 104px;
            margin: 16px auto;
        }

        .desc {
            margin-bottom: 4px;
            font-size: 14px;
            color: #7a8794;
        }

        .add-action {
            color: #005ed9;
            cursor: pointer;
        }
    }
}
</style>
