<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="批量选择"
        append-to-body
        content-styles="padding: 0; width:1034px; height: 496px; overflow: hidden;"
    >
        <div v-abc-loading="loading" class="products-types-wrapper">
            <div class="products-col">
                <!-- tabbar -->
                <div class="tabs-bar">
                    <!-- tab切换 -->
                    <ul v-if="showTypes" class="tabs">
                        <li
                            v-for="item in tabOptions"
                            :key="item"
                            :class="item === currentTab ? 'active' : ''"
                            @click="handleChangeActive(item)"
                        >
                            {{ item }}
                        </li>
                    </ul>

                    <!-- 单品搜索栏 -->
                    <div v-if="currentTab === '单品' || !showTypes" class="search-bar">
                        <abc-space>
                            <abc-cascader
                                v-if="!isExpect"
                                ref="goodsTypesRef"
                                v-model="selectedTypes"
                                :options="goodsTypeOptions"
                                placeholder="选择分类"
                                multiple
                                :collapse="true"
                                :mutually-exclusive="exclusive"
                                :width="128"
                                :props="{
                                    label: 'name',
                                    value: 'id',
                                    children: 'children'
                                }"
                                :value-props="{
                                    _label: 'name',
                                    _value: 'id'
                                }"
                                @change="handleChangeType"
                            >
                            </abc-cascader>
                            <goods-tag-filter-item
                                v-model="goodsTag"
                                :can-delete="false"
                                @change-tag="handleTagChange"
                            >
                            </goods-tag-filter-item>
                            <abc-input
                                v-model="scrollParams.key"
                                v-abc-focus-selected
                                :width="300"
                                :placeholder="placeholderStr"
                                @input="_debounceSearch"
                            >
                                <abc-search-icon slot="prepend"></abc-search-icon>
                            </abc-input>
                        </abc-space>
                    </div>
                </div>

                <!--分类显示全选-->
                <div v-if="currentTab === '分类'" class="selected-all-wrapper" @click="handleSelectedAllClick">
                    <span>全选</span>
                    <abc-checkbox v-model="selectedAll" no-border @change="handleSelectedAll">
                    </abc-checkbox>
                </div>

                <!--分类tab-->
                <div v-if="currentTab === '分类'" class="products-types">
                    <abc-tree
                        :data="typeList"
                        class="product-types-tree"
                        :indent="20"
                        :max-depth="10"
                    >
                        <template #default="{ node }">
                            <label>
                                <div class="product-types-tree">
                                    {{ node.name }}
                                    <abc-checkbox
                                        v-model="node.checked"
                                        style="margin-left: auto;"
                                        no-border
                                        @change="handleChangeSelected($event, node)"
                                    ></abc-checkbox>
                                </div>
                            </label>
                        </template>
                    </abc-tree>
                </div>
                <!--单品tab-->
                <div v-if="!showTypes || currentTab === '单品'" ref="tableContent" class="table-box">
                    <template v-if="config && config.length">
                        <!-- 单品title传递 -->
                        <div class="table-title">
                            <div style="width: 32px;"></div>
                            <div
                                v-for="cf in config"
                                :key="cf.prop"
                                :style="[
                                    cf.titleStyle,
                                    {
                                        flex: !cf.width ? 1 : '', width: cf.width && `${cf.width }px`
                                    },
                                ]"
                            >
                                <div>{{ cf.label }}</div>
                            </div>
                        </div>
                        <!-- 单品content -->
                        <div
                            v-abc-scroll-loader="{
                                methods: fetchData, isLast: isLast
                            }"
                            class="table-content"
                        >
                            <!-- 单品content传递 -->

                            <ul>
                                <label v-for="product in list" :key="product.goodsId">
                                    <li>
                                        <abc-checkbox
                                            v-model="product.checked"
                                            style="width: 32px; padding-left: 8px; margin-right: 0;"
                                            :disabled="disabled"
                                            @change="(checked) => {
                                                changeCheck(checked, product);
                                            }"
                                        ></abc-checkbox>
                                        <div
                                            v-for="cf in config"
                                            :key="cf.prop"
                                            :style="[
                                                cf.bodyStyle,
                                                {
                                                    flex: !cf.width ? 1 : '', width: cf.width && `${cf.width }px`
                                                },
                                            ]"
                                        >
                                            <slot :name="cf.prop" :item="product"></slot>
                                        </div>
                                    </li>
                                </label>
                            </ul>
                        </div>
                    </template>
                    <template v-else>
                        <abc-table
                            :key="loading"
                            fill-height
                            enable-virtual-list
                            :custom-tr-key="itemKey"
                            :show-all-checkbox="false"
                            :virtual-list-config="virtualListConfig"
                            :scroll-load-config="scrollLoadConfig"
                            style="height: 410px; margin-top: 16px; overflow: hidden;"
                            :render-config="tableRenderConfig"
                            :data-list="list"
                            :disabled-item-func="disabledItemFunc"
                            @changeChecked="handleChangeChecked"
                            @sortChange="handleConfirmTableSortChange"
                        >
                        </abc-table>
                    </template>
                </div>
            </div>

            <!-- 右边已选框 -->
            <div class="selected-col">
                <div class="selected">
                    已选 <span>{{ selectedCount }}</span> 项
                </div>

                <ul class="selected-goods-list">
                    <li v-for="(item, index) in selectedType" :key="item.id || item.goodsId || index">
                        <abc-flex justify="space-between" align="center">
                            <abc-icon icon="s-commodity-color" size="16"></abc-icon>
                            <div class="name ellipsis">
                                {{ item.name || item.goodsCMSpec || item.displayName }}
                            </div>
                            <abc-delete-icon
                                v-if="!disabled"
                                size="small"
                                theme="dark"
                                class="selected-delete-icon"
                                @delete="deleteTypeItem(index)"
                            ></abc-delete-icon>
                        </abc-flex>
                    </li>
                    <li v-for="(item, index) in selected" :key="item.goodsId || index">
                        <abc-flex justify="space-between" align="center">
                            <abc-icon icon="s-commodity-color" size="16"></abc-icon>
                            <div v-if="item.goods" class="name ellipsis">
                                {{ item.goods.type === 1 ? item.goods.medicineCadn : item.goods.name }}
                            </div>
                            <div v-else class="name ellipsis">
                                {{ item.name || item.goodsName }}
                            </div>
                            <div
                                v-if="item.goods && (item.goods.type === 1 || item.goods.type === 2)"
                                class="trade-name ellipsis"
                            >
                                {{ item.goods.type === 1 ? item.goods.name : '' }}
                            </div>
                            <abc-delete-icon
                                v-if="!disabled && !disabledItemFunc(item)"
                                size="small"
                                theme="dark"
                                class="selected-delete-icon"
                                @delete="deleteItem(item)"
                            ></abc-delete-icon>
                        </abc-flex>
                        <abc-space v-if="goodsSpec(item.goods)">
                            <abc-text theme="gray" size="mini" style="margin-left: 24px;">
                                {{ goodsSpec(item.goods) }}
                            </abc-text>
                        </abc-space>
                    </li>
                </ul>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <template v-if="disabled">
                <abc-button type="blank" @click="showDialog = false">
                    关闭
                </abc-button>
            </template>
            <template v-else>
                <abc-button @click="confirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="showDialog = false">
                    取消
                </abc-button>
            </template>
        </div>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import GoodsApi from 'api/goods';
    import GoodsV3API from 'api/goods/index-v3';
    import MarketingAPI from 'api/marketing';
    import {
        debounce, isNull,
    } from 'utils/lodash';
    import TableConfig from './table-config/table-select-goods-dialog';
    import TableTypeConfig from './table-config/table-select-goods-dialog-type';
    import {
        goodsFullName, goodsSpec, goodsTypeName, isChineseMedicine,
    } from '@/filters/goods.js';
    import clone from 'utils/clone';
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import {
        number, toMoney,
    } from '@/filters';
    import { mapGetters } from 'vuex';
    import OverflowFlexTagsWrapper from 'views/registration/components/overflow-flex-tags-wrapper.vue';
    import GoodsTagFilterItem from 'views/statistics/common/pro-stat-toolbar/good-tag-filter-item.vue';

    export default {
        name: 'SelectGoodsDialog',
        components: { GoodsTagFilterItem },
        filters: {
            price(product) {
                if (isChineseMedicine(product)) {
                    return product.piecePrice;
                }
                return product.packagePrice;

            },
        },
        props: {
            // 是否是老带新
            fromReferrer: {
                type: Boolean,
                default: false,
            },
            // 是否是抵扣权益
            isPresentRightsGoods: {
                type: Boolean,
                default: false,
            },
            // 是否是抵扣权益多选(自选服务)
            isCustomService: {
                type: Boolean,
                default: false,
            },
            needGoodsCount: {
                type: Boolean,
                default: false,
            },
            data: [],
            showTypes: { // 是否有分类
                type: Boolean,
                default: false,
            },
            isTabs: {
                type: Boolean,
                default: false,
            },

            subType: {
                type: [ String, Number ],
                default: '',
            },
            cMSpec: {
                type: [ String, Number ],
                default: '',
            },
            selectGoodsList: Array,
            productTypes: Array,
            value: Boolean,
            config: Array,
            placeholder: String,
            paramsDisable: {
                // 是否查询可用的项目，检查治疗可以设置启用停用
                type: [ Number, String ],
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            isExpect: {
                type: Boolean,
                default: false,
            },
            // 是否是积分活动
            showPoints: {
                type: Boolean,
                default: false,
            },
            customTypeIdList: {
                type: Array,
                default: () => [],
            },
            feeTypeIdList: {
                type: Array,
                default: () => [],
            },
            // 禁止操作的 goods
            disabledGoodsIdList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                loading: false,
                active: 0,
                tabOptions: [ '分类', '单品' ],
                currentTab: this.showTypes ? '分类' : '单品',
                list: [],
                typeList: [],
                uncheckeds: [],
                allChecked: false,
                isLast: false,
                scrollParams: {
                    key: '',
                    offset: 0,
                    limit: 30,
                    withCostPrice: 1,
                    jsonTypeWithCustomTypeList: [ ],
                    typeId: '',
                    goodsTagIdList: [],
                },
                selected: [],
                selectedType: [],
                timer: null,
                goodsAllTypes: [],
                selectedTypes: [], // 选中的药品分类
                exclusive: false,
                selectedAll: false,
                deduplicationSet: new Set(),

                // 表格配置
                renderConfig: {
                    hasInnerBorder: false,
                    list: [],
                },
                total: 0,
                goodsTag: [],
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters([
                'isChainAdmin',
                'isSingleStore',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            isCheckAll: {
                get() {
                    return (this.list && this.list.filter((item) => item.checked).length === this.list.length && this.list.length > 0);
                },
                set(val) {
                    this.checkAll(val);
                },
            },
            goodsTypeOptions() {
                return this.goodsAllTypes.map((item) => {
                    return {
                        ...item,
                        childrenLength: item.children?.length || 0,
                    };
                }) || [];
            },
            placeholderStr() {
                if (this.placeholder) {
                    return this.placeholder;
                }
                return Number(this.scrollParams.type) === 4 ? '输入治疗 / 理疗项' : '输入项目名称';

            },
            selectedCount() {
                if (this.showTypes) {
                    return (this.selected && this.selected.length || 0) + (this.selectedType && this.selectedType.length || 0);
                }
                return this.selected && this.selected.length;
            },
            virtualListConfig() {
                return {
                    visibleCount: 10,
                    bufferSize: 10,
                    scrollendTime: 300,
                    bufferLoad: false,
                };
            },
            scrollLoadConfig() {
                return {
                    fetchData: this.fetchData,
                    total: this.total,
                };
            },
            tableRenderConfig() {
                return this.getTableRenderConfig();
            },

            adaptRegType() {
                return this.viewDistributeConfig.Marketing.adaptRegType;
            },
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
        },
        async created() {
            const {
                productTypes, cMSpec = '', subType,feeTypeIdList,
            } = this;
            if (productTypes.length === 1) {
                this.scrollParams.jsonTypeWithCustomTypeList = [
                    {
                        type: '',
                    },
                ];
                this.scrollParams.jsonTypeWithCustomTypeList[0].type = productTypes[0];
                if (feeTypeIdList && feeTypeIdList.length > 0) {
                    this.scrollParams.feeTypeIdList = feeTypeIdList;
                }
                if (subType) {
                    this.scrollParams.jsonTypeWithCustomTypeList[0].subType = subType;
                }
                if (cMSpec) {
                    this.scrollParams.jsonTypeWithCustomTypeList[0].CMSpec = cMSpec;
                }
                if (this.customTypeIdList?.length) {
                    this.scrollParams.jsonTypeWithCustomTypeList[0].customTypeIdList = this.customTypeIdList;
                }
            } else {
                this.scrollParams.type = this.productTypes;
            }
            if (this.showTypes) {
                this.selectGoodsList.forEach((item) => {
                    if (!item.goodsIdKey) {
                        item.goodsIdKey = `${item.goodsId}-${item.pharmacyType}`;
                    }
                });
                this.selectedType = this.selectGoodsList && this.selectGoodsList.filter((item) => {
                    return item.type === 1;
                });
                this.selected = this.selectGoodsList && this.selectGoodsList.filter((item) => {
                    return item.type === 2;
                });
            } else {
                if (this.isCustomService) {
                    this.selected = clone(this.selectGoodsList && this.selectGoodsList[0] && this.selectGoodsList[0].goodsList) || [];
                } else {
                    this.selected = clone(this.selectGoodsList);
                }
            }
            this.loading = true;
            this.fetchData();
            this.uncheckeds = [];
            this._debounceSearch = debounce(() => {
                this.isLast = false;
                this.list = [];
                this.scrollParams.offset = 0;
                this.loading = true;
                this.fetchData();
            }, 250, true);
            this.fetchTypes();
            this.fetchAllGoodsTypes();
        },
        beforeDestroy() {
            clearTimeout(this.timer);
        },
        methods: {
            goodsSpec,
            disabledItemFunc(item) {
                return this.disabledGoodsIdList?.indexOf(item.goodsId) > -1;
            },
            transformData(data) {
                const result = [];
                const map = new Map();

                for (let i = 0; i < data.length; i++) {
                    const obj = data[i];
                    const typeId = obj[0].id;
                    let customTypeId = '';
                    const count = obj[0]?.children?.length || 0;
                    if (obj?.[1]?.id) {
                        customTypeId = obj[1].id;
                    }

                    if (map.has(typeId)) {
                        map.get(typeId).customTypeIds.push(customTypeId);
                    } else {
                        map.set(typeId, {
                            customTypeIds: [customTypeId], length: 0, count,
                        });
                    }
                }

                for (const [key, value] of map) {
                    const entry = { typeId: key };
                    if (value.customTypeIds[0]) {
                        entry.customTypeIdList = value.customTypeIds;
                    }
                    entry.length = value.customTypeIds.length;
                    entry.count = value.count;
                    const {
                        count, length, customTypeIdList, typeId,
                    } = entry;
                    if (count > length) {
                        result.push({
                            customTypeIdList,
                        });
                    } else {
                        result.push({
                            typeId,
                            customTypeIdList,
                        });
                    }
                }
                return result;
            },

            handleChangeType() {
                const arr = this.transformData(this.$refs.goodsTypesRef.getOriValue(this.selectedTypes));
                if (arr?.length) {
                    this.scrollParams.jsonTypeWithCustomTypeList = arr;
                    this.scrollParams.type = [];
                } else {
                    // 清空选择的类型，要把当前渲染列表数据清空，
                    this.scrollParams.type = this.productTypes;
                    this.scrollParams.jsonTypeWithCustomTypeList = [];
                    this.list = [];
                }
                this.scrollParams.offset = 0;
                this.isLast = false;
                this.fetchData();
            },
            async fetchAllGoodsTypes() {
                const { data } = await GoodsApi.fetchGoodsClassificationV3({
                    needCustomType: 1,
                });
                const types = [
                    GoodsTypeIdEnum.EXAMINATION_ASSAY,
                    GoodsTypeIdEnum.EXAMINATION_INSPECTION,
                    GoodsTypeIdEnum.TREATMENT_TREATMENT,
                    GoodsTypeIdEnum.TREATMENT_PHYSIOTHERAPY,
                    GoodsTypeIdEnum.OTHER_GOODS,
                ];
                const list = data?.list?.map((item) => {
                    const children = item.customTypes || [];
                    if (children.length) {
                        children.push({
                            id: -item.id,
                            name: '未指定',
                            sort: 999,
                            typeId: +item.id,
                        });
                    }
                    return {
                        ...item,
                        children,
                    };
                }) || [];
                this.goodsAllTypes = this.isPresentRightsGoods ? list?.filter((goodsItem) => types.includes(+goodsItem.id)) : list;
            },
            hasProductInfo(type) {
                return [
                    GoodsTypeEnum.MEDICINE,
                    GoodsTypeEnum.MATERIAL,
                    GoodsTypeEnum.GOODS,
                ].indexOf(type) > -1;
            },
            initTypes(modules, initExpand = false) {
                return modules.map((item) => {
                    // 初始化选中态
                    const index = this.selectedType.findIndex((selectItem) => {
                        return selectItem.goodsIdKey === item.goodsIdKey;
                    });
                    item.checked = index > -1;
                    if (Array.isArray(item.children) && item.children.length) {
                        item.isLeaf = false;
                        item.children = this.initTypes(item.children);
                        if (initExpand) {
                            item.expand = !!item.children.find((it) => it.checked);
                        }
                        // 父节点的count 目前没有返回，暂时手动计算
                        item.count = 0;
                        item.count = item.children.reduce((acc, cur) => {
                            return (acc || 0) + (cur.count || 0);
                        }, 0);
                    } else {
                        item.isLeaf = true;
                        if (item.goodsType === 13 || item.goodsType === 14) {
                            item.count = 1;
                        }
                        if (initExpand) {
                            item.expand = false;
                        }
                    }
                    return Object.assign({}, item, {
                        checked: item.checked || false,
                        isLeaf: item.isLeaf,
                        expand: item.expand,
                        checkStatus: false,
                        count: item.count,
                    });
                });
            },

            initSelectedAll(typeList = []) {
                const flatTypeList = [];
                const processItem = (item) => {
                    const newItem = {
                        name: item.name,
                        goodsType: item.goodsType,
                        goodsSubType: item.goodsSubType,
                        goodsCMSpec: item.goodsCMSpec,
                        goodsId: item.id,
                        pharmacyType: item?.pharmacyType || item?.isAirPharmacy || 0,
                        isAirPharmacy: item?.pharmacyType || item?.isAirPharmacy || 0,
                        type: 1,
                        count: item.count,
                        exceptItems: [],
                        goodsIdKey: `${item.id}-${item.pharmacyType}`,
                        customTypeId: item.customTypeId,
                    };

                    flatTypeList.push(newItem);

                    if (item.children?.length) {
                        item.children.forEach((child) => processItem(child));
                    }
                };

                typeList.forEach(processItem);

                this.selectedAll = flatTypeList.length === this.selectedType.length;
            },

            handleSelectedAllClick() {
                this.selectedAll = !this.selectedAll;
                this.handleSelectedAll(this.selectedAll);
            },
            handleSelectedAll(val) {
                const typeItem = (item, checked = true) => {
                    const newItem = {
                        ...item,
                        checked,
                    };

                    if (item.expand && item.children?.length) {
                        newItem.children = item.children.map((child) => typeItem(child, checked));
                    }
                    return newItem;
                };

                const processItem = (item) => {
                    const newItem = {
                        name: item.name,
                        goodsType: item.goodsType,
                        goodsSubType: item.goodsSubType,
                        goodsCMSpec: item.goodsCMSpec,
                        goodsId: item.id,
                        pharmacyType: item?.pharmacyType || item?.isAirPharmacy || 0,
                        isAirPharmacy: item?.pharmacyType || item?.isAirPharmacy || 0,
                        type: 1,
                        count: item.count,
                        exceptItems: [],
                        goodsIdKey: `${item.id}-${item.pharmacyType}`,
                        customTypeId: item.customTypeId,
                        expand: item.expand || false,
                    };

                    const isExist = this.selectedType.find((it) => it.goodsId === newItem.goodsId);
                    if (!isExist) {
                        this.selectedType.push(newItem);
                    }

                    if (item.expand && item.children?.length) {
                        item.children.forEach((child) => processItem(child));
                    }
                };

                if (val) {
                    this.typeList = this.typeList.map((item) => typeItem(item));
                    this.typeList.forEach(processItem);
                } else {
                    this.typeList = this.typeList.map((item) => typeItem(item, false));
                    this.selectedType = [];
                }
            },

            /**
             * @desc 选中项目添加到右侧选中栏
             * <AUTHOR>
             * @date 2020/4/17
             */
            addProductToSelected(item) {
                const index = this.selectedType.findIndex((temp) => {
                    return item.goodsIdKey === temp.goodsIdKey;
                });
                if (index === -1) {
                    this.selectedType && this.selectedType.push({
                        name: item.name,
                        goodsType: item.goodsType,
                        goodsSubType: item.goodsSubType,
                        goodsCMSpec: item.goodsCMSpec,
                        goodsId: item.id,
                        pharmacyType: item?.pharmacyType || item?.isAirPharmacy || 0,
                        isAirPharmacy: item?.pharmacyType || item?.isAirPharmacy || 0,
                        type: 1,
                        count: item.count,
                        exceptItems: [],
                        goodsIdKey: `${item.id}-${item.pharmacyType}`,
                        customTypeId: item.customTypeId,
                    });
                }
            },
            /**
             * @desc 从右侧选中栏删除项目
             * <AUTHOR>
             * @date 2020/4/17
             */
            deleteProductFromSelected(item) {
                const index = this.selectedType.findIndex((temp) => {
                    return `${item.id}` === `${temp.goodsId}`;
                });
                if (index > -1) {
                    this.selectedType.splice(index, 1);
                }
            },
            handleChangeSelected(val, item) {
                if (this.disabled) return false;
                if (val) {
                    this.addProductToSelected(item);
                } else {
                    this.deleteProductFromSelected(item);
                }
                this.typeList = this.initTypes(this.typeList);
                this.initSelectedAll(this.typeList);
            },
            clearKey() {
                this.isLast = false;
                this.list = [];
                this.scrollParams.offset = 0;
                this.fetchData();
            },
            unique(arr) {
                const result = [];
                const obj = {};
                for (let i = 0; i < arr.length; i++) {
                    if (!obj[ arr[ i ].goodsId && arr[ i ].goods.goodsId ]) {
                        result.push(arr[ i ]);
                        obj[ arr[ i ].goodsId && arr[ i ].goods.goodsId ] = true;
                    }
                }
                return result;
            },
            checkAll(checked) {
                if (checked) {
                    this.list.forEach((item) => {
                        item.checked = true;
                        // for (let i=0;i<this.noDiscountGoodsList.length;i++) {
                        //     if (item.goods && this.noDiscountGoodsList[i].goodsId === item.goods.goodsId) {
                        //         item.checked = false
                        //     }
                        // }
                    });
                    const data = this.selected.concat(this.list && this.list.filter((item) => item.checked === true));
                    this.selected = this.unique(data);
                } else {
                    this.selected.forEach((check) => {
                        this.list.forEach((item) => {
                            item.checked = false;
                            if (item.goods && item.goods.goodsId === check.goods.goodsId) {
                                check.checked = false;
                            }
                        });
                    });
                    this.selected = this.selected && this.selected.filter((item) => item.checked === true);
                }
            },

            changeCheck(checked, item) {
                if (checked) {
                    if (this.showTypes) {
                        this.selected.unshift({
                            ...item,
                            type: 2,
                            pharmacyType: 0,
                        });
                    } else {
                        this.selected && this.selected.unshift(item);
                    }

                    this.timer = setTimeout(() => {
                        $('.selected-goods-list').scrollTop(($('.selected-goods-list li').height() + 1) * this.selected && this.selected.length);
                    }, 1);
                } else {
                    this.selected = this.selected && this.selected.filter((product) => product.goodsId !== item.goodsId);
                }
            },
            itemKey(item) {
                const {
                    type,
                    goodsId,
                    goodsType,
                    goodsSubType,
                } = item;
                return `${type}_${goodsId}_${goodsType}_${goodsSubType}`;
            },
            // abcTable选择框
            handleChangeChecked(item) {
                if (this.disabledItemFunc(item)) return;
                this.changeCheck(item.checked, item);
            },


            deleteTypeItem(index) {
                this.selectedType.splice(index, 1);
                this.typeList = this.initTypes(this.typeList);
            },
            deleteItem(item) {
                this.list.forEach((it) => {
                    if (it.goodsId === item.goodsId) {
                        it.checked = false;
                    }
                });
                this.changeCheck(false, item);
            },
            handleChangeActive(val) {
                this.currentTab = val;
                this.active = val;
            },
            confirm() {
                let data;
                if (this.showTypes) {
                    data = [...this.selectedType].concat([...this.selected]);
                } else {
                    if (this.isCustomService) {
                        const customSelected = this.selected && this.selected.filter((item) => !item.goodsList);
                        data = customSelected && customSelected.length ? [...customSelected] : [];
                    } else {
                        data = [...this.selected];
                    }

                }
                this.$emit('change', data, this.uncheckeds);
                this.showDialog = false;
            },

            // 更改CustomTypes字段名为Children字段名兼容abc-tree组件展示子级
            changeCustomTypesToChildren(list = []) {
                return list.map((item) => {
                    // 先转换一次类型名称
                    item.name = this.transGoodsClassificationName(item.name);
                    if (Array.isArray(item?.customTypes) && item?.customTypes?.length) {
                        item.customTypes.forEach((it) => {
                            it.goodsCMSpec = item.goodsCMSpec;
                            it.goodsType = item.goodsType;
                            it.goodsSubType = item.goodsSubType;
                            it.pharmacyType = item.pharmacyType;
                            it.isAirPharmacy = item.isAirPharmacy;
                            // 转换二级分类id
                            it.customTypeId = it.id;
                        });
                        item.children = item.customTypes;
                        delete item.customTypes;
                    }
                    if (Array.isArray(item?.children) && item?.children?.length) {
                        this.changeCustomTypesToChildren(item.children);
                    }

                    return {
                        ...item,
                    };
                });
            },

            generateGoodsIdkey(list = []) {
                const newList = list;
                return newList.map((item) => {
                    if (!item.goodsIdKey) {
                        item.goodsIdKey = `${item.id}-${item.pharmacyType}`;
                    }

                    if (Array.isArray(item?.children) && item?.children?.length) {
                        item.children = this.generateGoodsIdkey(item.children);
                    }

                    return {
                        ...item,
                        goodsIdKey: item.goodsIdKey,
                    };
                });
            },
            /**
             * @desc 获取系统分类数据
             * <AUTHOR>
             * @date 2020/4/17
             */
            async fetchTypes() {
                try {
                    this.loading = true;
                    const { data } = await MarketingAPI.getTypeList({
                        needGoodsCount: this.needGoodsCount ? 1 : 0,
                        needCustomType: 1,
                    });

                    let types = data.types && data.types.map((item) => {
                        return this.adaptRegType(item);
                    });

                    // 数据处理
                    types = this.changeCustomTypesToChildren(types);

                    let resolveData = this.generateGoodsIdkey(types);

                    // 老带新过滤掉空中药房， 代煎代配
                    if (this.fromReferrer) {
                        const filters = ['1003', '0'];
                        const namesToExclude = ['空中药房', '代煎代配药房'];

                        resolveData = resolveData.filter((item) => {
                            return !filters.includes(item.id) && !namesToExclude.includes(item.name);
                        });
                    }

                    this.typeList = this.initTypes(resolveData);
                    this.initSelectedAll(this.typeList);
                    this.loading = false;
                } catch (err) {
                    this.loading = false;
                }
            },
            async fetchData() {
                try {
                    if (this.isLast) return false;
                    /**
                     * @desc 有搜索条件,不分页,@天翔
                     * <AUTHOR>
                     * @date 2019/05/20 22:59:13
                     */
                    this.scrollParams.limit = this.scrollParams.key || this.selectedTypes?.length || this.scrollParams.goodsTagIdList?.length ? 999 : 30;
                    this.scrollParams.disable = 0;
                    if (this.paramsDisable !== undefined) {
                        this.scrollParams.disable = +this.paramsDisable;
                    }
                    // https://www.tapd.cn/22044681/bugtrace/bugs/view/1122044681001056137
                    // https://www.tapd.cn/43780818/bugtrace/bugs/view?bug_id=1143780818001060317&qy_private_corpid=
                    // 总部为汇总，不然总部没有入过库售价都是0
                    if (this.isChainAdmin || this.isSingleStore) {
                        this.scrollParams.queryStockOrderType = 40;
                    }
                    const { data } = await GoodsV3API.searchGoods(this.scrollParams);
                    const {
                        list, query,
                    } = data;
                    if (query.keyword !== this.scrollParams.key && this.scrollParams.offset !== query.offset) {
                        return;
                    }

                    if (list.length < this.scrollParams.limit && this.list.length > 0) {
                        this.isLast = true;
                    }

                    let newList = list.map((item) => {
                        return {
                            checked: !!this.selected && !!this.selected.find((it) => {
                                return it.goodsId === item.goodsId;
                            }) ,
                            goodsId: item.goodsId,
                            goodsType: item.type,
                            goodsSubType: item.subType,
                            goodsCMSpec: item.CMSpec,
                            goods: item,
                            productInfo: item,
                            type: 2,
                            pharmacyType: 0,
                        };
                    });
                    if (this.scrollParams.key || this.selectedTypes?.length || this.scrollParams.goodsTagIdList?.length) {
                        this.isLast = true;
                        this.deduplicationSet = new Set();
                        this.list = newList;
                    } else {
                        this.scrollParams.offset = this.scrollParams.offset + this.scrollParams.limit;
                        newList = newList.filter((item) => {
                            if (!this.deduplicationSet.has(item.goodsId)) {
                                this.deduplicationSet.add(item.goodsId);
                                return true;
                            }
                            return false;
                        });
                        this.list = this.list.concat(newList);
                    }
                    this.total = data.total || 0;
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },

            getTableRenderConfig() {
                const baseConfig = {
                    'displayName': {
                        customRender: (_, row) => {
                            if (this.hasProductInfo(row.goods.type)) {
                                return (
                                    <div
                                        v-abc-goods-hover-popper={{
                                            goods: row.goods,
                                            openDelay: 300,
                                            showStock: false,
                                        }}
                                        class="name ellipsis"
                                    >
                                        {goodsFullName(row.goods)}
                                        <span class="ellipsis">
                                            {goodsSpec(row.goods)}
                                        </span>
                                    </div>
                                );
                            }
                            return (
                                <div
                                    class="name ellipsis"
                                >
                                    {goodsFullName(row.goods)}
                                    <span class="ellipsis">
                                        {goodsSpec(row.goods)}
                                    </span>
                                </div>
                            );
                        },
                    },
                    'packagePrice': {
                        customRender: (_, row) => {
                            if (isNull(row.goods?.packagePrice)) {
                                return (<span class="ellipsis price un-fix-price">
                                        未定价
                                    </span>);
                            }
                            return (<span class="ellipsis price">
                                        {this.$t('currencySymbol') + toMoney(this.$options.filters.price(row.goods))}
                                    </span>);
                        },
                    },
                    'profitRat': {
                        dataFormatter: (_, row) => {
                            if (row.goods.profitRat === null || row.goods.profitRat === undefined) {return '--';}
                            return `${(row.goods.profitRat) | number}%`;
                        },
                    },
                    'stockPieceCount': {
                        dataFormatter: (_, row) => {
                            const { goods } = row;
                            let totalStock,stockUnit,pieceStock = '',pieceUnit = '';
                            if (goods.subType === 2 && goods.type === 1) {
                                totalStock = goods.stockPieceCount;
                                stockUnit = goods.pieceUnit;
                            } else {
                                totalStock = goods.stockPackageCount;
                                stockUnit = goods.packageUnit;
                                if (goods.stockPieceCount !== null && goods.pieceUnit !== null && goods.stockPieceCount !== 0) {
                                    pieceStock = goods.stockPieceCount;
                                    pieceUnit = goods.pieceUnit;
                                }
                            }
                            if (totalStock === null || stockUnit === null) {return '--';}

                            return `${totalStock}${stockUnit}${pieceStock}${pieceUnit}`;
                        },
                    },
                    'goodsTagList': {
                        customRender: (_, row) => {
                            const goodsTagList = (row.goods?.goodsTagList || [])
                                .filter((item) => item.name)
                                .map((item) => ({
                                    tagId: item.tagId,
                                    tagName: item.name,
                                    viewMode: 0,
                                }));
                            return goodsTagList.length ?
                                <abc-table-cell>
                                    <OverflowFlexTagsWrapper tags={goodsTagList} variant="outline" size="tiny"/>
                                </abc-table-cell> :
                                <abc-table-cell>--</abc-table-cell>;
                        },
                    },
                };

                if (this.isPresentRightsGoods) {
                    return TableTypeConfig.extendConfig({
                        ...baseConfig,
                        'typeName': {
                            dataFormatter: (_, row) => goodsTypeName(row.goods),
                        },
                    });
                }
                return TableConfig.extendConfig({
                    ...baseConfig,
                    'name': {
                        dataFormatter: (_, row) => (row.goods.type === 1 ? row.goods.name : '--'),
                    },
                    'manufacturer': {
                        dataFormatter: (_, row) => (row.goods.manufacturer || '--'),
                    },
                });
            },

            handleConfirmTableSortChange({
                orderBy, orderType,
            }) {
                if (orderBy) {
                    this.sortList(this.list, orderBy, orderType);
                }
            },

            sortList(list, orderBy, orderType) {
                return list.sort((a, b) => {
                    if (typeof a.goods[orderBy] === 'number' && typeof b.goods[orderBy] !== 'number') {
                        return -1;
                    }
                    if (typeof a.goods[orderBy] !== 'number' && typeof b.goods[orderBy] === 'number') {
                        return 1;
                    }
                    if (a.goods[orderBy] === undefined || a.goods[orderBy] === null) return 1;
                    if (b.goods[orderBy] === undefined || b.goods[orderBy] === null) return -1;
                    if (orderType === 'asc') {
                        return a.goods[orderBy] - b.goods[orderBy];
                    }
                    return b.goods[orderBy] - a.goods[orderBy];
                });

            },

            handleTagChange(goodsTag, tagIds) {
                this.scrollParams.offset = 0;
                this.isLast = false;
                this.goodsTag = goodsTag;
                this.scrollParams.goodsTagIdList = tagIds;
                this.fetchData();
            },
        },
    };
</script>

<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';
    @import 'src/styles/mixin.scss';

    .products-types-wrapper {
        display: flex;
        height: 100%;

        .products-col {
            display: flex;
            flex: 1;
            flex-direction: column;
            width: 0;
            height: 100%;
            padding-left: 24px;
            border-right: 1px solid $P6;

            .table-box {
                width: 734px;
                height: 448px;
                padding-top: 0;
                margin-right: 24px;
                overflow: hidden;
            }

            .tabs-bar {
                display: flex;
                align-items: center;
                padding-top: 16px;

                .search-bar {
                    display: flex;
                    align-items: center;
                }

                .tabs {
                    display: flex;
                    margin-right: 8px;
                    color: $T2;
                    background: $S2;

                    li {
                        padding: 5px 12px;
                        cursor: pointer;
                        border: 1px solid $P1;

                        &:first-child {
                            border-right: none;
                        }

                        &:last-child {
                            border-left: none;
                        }

                        &.active {
                            color: $theme1;
                            border: 1px solid $theme1;
                        }
                    }
                }
            }

            .selected-all-wrapper {
                display: flex;
                justify-content: space-between;
                height: 40px;
                padding: 0 24px 0 10px;
                margin-top: 16px;
                line-height: 40px;
                border-bottom: 1px solid $P6;

                &:hover {
                    background-color: $P4;
                }
            }

            .products-types {
                flex: 1;
                margin-top: 8px;
                overflow-y: scroll;

                @include scrollBar;

                .product-types-tree {
                    .abc-tree-node-content {
                        height: 40px;
                        padding-right: 14px;
                        cursor: pointer;
                        border-bottom: 1px solid $P6;

                        &:hover {
                            background: $P4;
                        }

                        .product-types-tree {
                            display: flex;
                            flex: 1;
                            align-items: center;
                            cursor: pointer;
                        }
                    }
                }

                .item-row {
                    display: flex;
                    align-items: center;
                    padding: 13px 12px;
                    border-bottom: 1px solid $P6;

                    .cis-icon-dropdown_secondary_triangle,
                    .cis-icon-dropdown_triangle {
                        flex: 0 0 18px;
                        margin-right: 5px;
                        font-size: 18px;
                        color: #979da2;
                    }

                    .drugs-name {
                        flex: 1;
                    }
                }

                .item-row-container {
                    .item-row-container {
                        .item-row {
                            padding-left: 48px;
                        }

                        .item-row-container {
                            .item-row {
                                padding-left: 84px;
                            }
                        }
                    }
                }
            }

            .table-title {
                display: flex;
                align-items: center;
                height: 32px;
                margin-top: 16px;
                font-size: 14px;
                font-weight: bold;
                color: $T2;
                background-color: $P5;
                border-top: 1px solid $P6;
                border-bottom: 1px solid $P6;

                > div {
                    padding-right: 12px;
                }
            }

            .table-content {
                position: relative;
                height: calc(100% - 48px);
                overflow-x: hidden;
                overflow-y: auto;

                @include scrollBar;
            }

            .table-content li {
                display: flex;
                align-items: center;
                height: 40px;
                cursor: pointer;
                border-bottom: 1px solid $P6;

                &:hover {
                    background-color: $P4;
                }

                > div {
                    padding-right: 12px;
                }

                &.scroll-loading {
                    justify-content: center;
                    color: $T3;
                    text-align: center;
                }
            }

            .name,
            .price {
                height: 100%;
                line-height: 40px;

                span {
                    margin-left: 8px;
                    color: $T3;
                }
            }

            .un-fix-price {
                color: var(--abc-color-O2);
            }
        }

        .selected-col {
            width: 252px;
            height: 100%;

            .selected {
                width: calc(100% - 16px);
                height: 40px;
                padding-left: 24px;
                font-weight: bold;
                line-height: 40px;

                span {
                    margin: 0 2px;
                }
            }

            .selected-goods-list {
                height: calc(100% - 40px);
                padding: 0 6px 0 16px;
                overflow-y: scroll;

                @include scrollBar;

                .name {
                    flex: 1;
                    width: 0;
                    margin-left: 8px;
                }

                .trade-name {
                    width: 60px;
                    margin-left: 8px;
                }
            }

            ul li {
                display: flex;
                flex-direction: column;
                justify-content: center;
                width: 100%;
                height: 40px;
                padding-left: 8px;

                &:hover {
                    background-color: $P4;
                }

                .selected-delete-icon {
                    margin-right: 8px;
                }
            }
        }
    }

    .select-goods-popper {
        .popper-row {
            display: flex;

            span {
                flex: 1;
            }

            span:last-child {
                flex: 0 0 auto;
            }
        }
    }
</style>
