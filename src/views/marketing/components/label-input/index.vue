
<template>
    <div
        class="marketing-module__label-input"
        :class="[disabled ? 'is-disabled' : '']"
    >
        <div :class="['name-box']">
            <i :class="['iconfont', icon]"></i>
            <span class="name" :class="{ 'take-off-status': isTakeOff }" :title="name">{{ name }}</span>
            <span v-if="statusName" class="status-name">{{ statusName }}</span>
        </div>

        <div class="count-box">
            <abc-form-item
                required
                class="coupon-item"
                :validate-event="validate(value)"
            >
                <abc-input
                    type="number"
                    :width="56"
                    :config="config"
                    class="coupon-count"
                    :disabled="disabled"
                    :value="value"
                    @input="handleChange"
                ></abc-input>
                <span class="coupon-unit">{{ unit }}</span>
            </abc-form-item>

            <div v-if="!disabled" class="coupon-delete-icon" @click="handleDelete()">
                <img src="~assets/images/<EMAIL>" alt="close" />
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        model: {
            prop: 'value',
            event: 'change',
        },
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            icon: {
                type: String,
                default: '',
            },
            name: {
                type: String,
                default: '',
            },
            value: {
                type: [ String, Number ],
                default: '',
            },
            unit: {
                type: String,
            },
            statusName: String,
            isTakeOff: Boolean,
            config: {
                type: Object,
                default () {
                    return {
                        max: null, // 最多输入多少位
                        formatLength: 0, // 支持小数点后几位
                        supportZero: false, // 是否支持零
                        supportFraction: false, // 是否支持分数
                        supportNegative: false, // 是否支持负数
                    };
                },
            },
        },
        data() {
            return {};
        },
        methods: {
            handleChange(val) {
                this.$emit('change', val);
            },
            validate(val) {
                if (Number(val) <= 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能填0',
                        });
                    };
                }
            },
            handleDelete() {
                this.$emit('delete');
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/theme.scss";

    .marketing-module__label-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 28px;
        margin-right: 5px;
        margin-bottom: 4px;
        font-size: 12px;
        border: 1px solid #dadbe0;

        .name-box {
            display: flex;
            flex: 1;
            align-items: center;
            padding: 6px 8px;
            border-right: 1px solid #dadbe0;

            &.no-border {
                border-right: none;
            }

            .iconfont {
                flex: 0 0 auto;
                margin-right: 6px;
                font-size: 12px;
                color: rgba(151, 194, 252, 1);
            }

            .status-name {
                margin-left: 2px;
                font-size: 12px;
                color: #ff3333;
            }
        }

        .count-box {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;

            &:hover {
                .coupon-delete-icon {
                    display: block;
                }
            }

            .coupon-delete-icon {
                position: absolute;
                top: -8px;
                right: -8px;
                z-index: 10;
                display: none;
                cursor: pointer;

                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }

        .name {
            flex: 0 0 auto;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &.take-off-status {
                color: $T2;
            }
        }

        .coupon-item {
            position: relative;

            .coupon-count {
                height: 100%;
                margin: 0;
                font-size: 14px;
                text-align: center;
                background: transparent;
                border: none;
                outline: none;

                .abc-input__inner {
                    height: 28px;
                    padding-right: 24px;
                    border-color: transparent;
                    border-radius: 0;
                }

                .append-input {
                    min-width: 22px;
                    padding: 0 4px 0 4px;
                    font-size: 12px;
                    color: #7a8794;
                    background-color: transparent;
                    border-color: transparent;
                }
            }

            .coupon-unit {
                position: absolute;
                top: 0;
                right: 0;
                z-index: 4;
                width: 24px;
                height: 24px;
                font-size: 12px;
                line-height: 24px;
                color: #7a8794;
                text-align: center;
            }
        }
    }

    .is-disabled {
        .coupon {
            color: #c0c4cc;
            background: $abcBgDisabled;
        }

        .iconfont,
        .count {
            color: #c0c4cc;
        }
    }
</style>
