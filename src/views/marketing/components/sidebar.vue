<template>
    <div class="sidebar-wrapper-marketing">
        <div class="sidebar">
            <ul>
                <li
                    v-for="item in displayMenuList"
                    :key="item.name"
                    :class="{ active: isActive(item) }"
                    :data-cy="`marketing-menu-${item.label}`"
                    @click="onClickMenuItem(item)"
                >
                    <abc-icon class="icon" :icon="item.icon" size="14"></abc-icon>
                    <span class="text">{{ item.label }}</span>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
    import { getRouterChildren } from 'router/filter-router.js';
    import { mapGetters } from 'vuex';

    export default {
        name: 'SideBar',
        data() {
            return {
                currentRouteName: '',
            };
        },
        computed: {
            ...mapGetters(['isIntranetUser']),
            // 当前路由name
            routeName() {
                return this.$route.name;
            },
            // 菜单展示列表
            displayMenuList() {
                const { routes } = this.$router.options;
                return getRouterChildren(routes, 'marketing')
                    .filter((item) => {
                        if (item.path === 'messagepush') {
                            return !item.meta.hidden && !this.isIntranetUser;
                        }
                        return !item.meta.hidden;
                    })
                    .map((item) => ({
                        label: item.name,
                        value: item.name,
                        path: item.path,
                        icon: item.meta.icon,
                        children: item.children,
                    }));
            },
        },
        methods: {
            isActive(item) {
                return item.children.findIndex((item) => {
                    return item.name === this.$route.name;
                }) > -1;
            },
            /**
             * 当点击一项菜单时
             * <AUTHOR>
             * @date 2021-03-08
             * @param {Object} item
             */
            onClickMenuItem(item) {
                if (this.routeName === item.value) return;
                this.$router.push({ name: item.value });
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import '~styles/theme.scss';
    @import '~styles/mixin.scss';

    @mixin boxStyleBase {
        -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
        -moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    }

    @media screen and (max-width: 1365px) {
        .app-container .sidebar-wrapper-marketing {
            .sidebar > ul > li .red-dot {
                right: 110px;
            }
        }
    }

    .sidebar-wrapper-marketing {
        height: 100%;
        padding: 8px 10px;
        overflow-x: hidden;
        overflow-y: auto;
        overflow-y: overlay;
        background-color: #f9fafc;
        border-radius: 4px 0 0 4px;

        @include scrollBar;

        .sidebar > ul > li {
            position: relative;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            height: 40px;
            font-size: 0;
            line-height: 40px;
            cursor: pointer;
            border-radius: var(--abc-border-radius-small);

            & + li {
                margin-top: 4px;
            }

            .text {
                display: inline-block;
                font-size: 14px;
            }

            .icon {
                width: 14px;
                margin: 12px;
                color: $T2;
            }

            &:hover {
                background: $P8;
            }
        }

        .sidebar ul .active {
            position: relative;
            color: $S2;
            background: $theme3;

            .icon {
                color: $S2;
            }

            &:hover {
                background: $theme3;
            }
        }
    }
</style>
