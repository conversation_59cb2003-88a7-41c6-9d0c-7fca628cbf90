<template>
    <div style="height: 100%;">
        <abc-layout preset="page-table">
            <abc-layout-header v-if="!isFromWeShop && !isChainSubStore">
                <abc-space split align="flex-end" style="justify-content: flex-start; width: 100%;">
                    <abc-check-access>
                        <abc-button icon="s-b-add-line-medium" theme="success" @click="showMemberFrom()">
                            会员等级
                        </abc-button>
                    </abc-check-access>
                </abc-space>
            </abc-layout-header>
            <abc-layout-content @layout-mounted="handleMounted">
                <abc-table
                    empty-content="暂无会员卡类别"
                    :render-config="tableRenderConfig"
                    :loading="loading"
                    :data-list="memberCardList"
                    tr-clickable
                    @handleClickTr="showMemberFrom"
                >
                    <template #memberCard="{ trData: item }">
                        <abc-table-cell :title="item.name">
                            <abc-text theme="primary">
                                {{ item.name }}
                            </abc-text>
                        </abc-table-cell>
                    </template>

                    <template #getRequirement="{ trData: item }">
                        <abc-table-cell :title="item.pointsAutoUpdate">
                            {{ item.pointsAutoUpdate ? `累计积分${ item.pointsAutoUpdate}` : '未设置' }}
                        </abc-table-cell>
                    </template>

                    <template #discountRight="{ trData: item }">
                        <div v-abc-title.ellipsis="item.discountBenefits || '无'" :title="item.discountBenefits" style="padding: 0 8px; line-height: 40px;">
                        </div>
                    </template>

                    <template #mallDiscountBenefits="{ trData: item }">
                        <div v-abc-title.ellipsis="item.mallDiscountBenefits || ''" :title="item.mallDiscountBenefits" style="padding: 0 8px; line-height: 40px;">
                        </div>
                    </template>

                    <template #memberNum="{ trData: item }">
                        <abc-table-cell :title="item.memberCount">
                            {{ item.memberCount }}
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-layout-content>
            <abc-layout-footer>
                <abc-pagination
                    :show-total-page="false"
                    :pagination-params="paginationParams"
                    :count="paginationParams.count"
                    @current-change="changePageIndex"
                >
                    <ul slot="tipsContent">
                        <li>
                            共 <span>{{ paginationParams.count }}</span> 项
                        </li>
                    </ul>
                </abc-pagination>
            </abc-layout-footer>
        </abc-layout>
        <member-form-view
            v-if="isShowMemberForm"
            :visible="isShowMemberForm"
            :member-type-id="currentMemberCard.id"
            :member-count="currentMemberCard.memberCount"
            :is-from-we-shop="isFromWeShop"
            @visible="(val) => (isShowMemberForm = val)"
            @refresh="refresh"
        ></member-form-view>
        <point-rule-view
            v-if="marketingMemberSettingStatus"
            :visible="marketingMemberSettingStatus"
            v-bind="{
                enablePaymentPassword, enableMemberRecharge, memberRechargeMinAmount , enableAutoUpgradeMember, autoUpgradeMemberType, enableMemberStepRecharge, memberStepRechargeRules
            }"
            :show-payment-password="true"
            :show-member-recharge="true"
            @visible="setMarketingMemberSetting"
            @change="updatePointRule"
        ></point-rule-view>
    </div>
</template>

<script>
    import MemberFormView from './member-form.vue';
    import PointRuleView from './point-rule.vue';
    import MarketingAPI from 'api/marketing';
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { MemberTypeTableConfig } from 'views/marketing/member/table-config';
    import useMarketingMemberSetting from 'views/marketing/hooks/memberHook';

    export default {
        components: {
            MemberFormView,
            PointRuleView,
        },
        setup() {
            const {
                setMarketingMemberSetting,
                marketingMemberSettingStatus,
            } = useMarketingMemberSetting();
            return {
                marketingMemberSettingStatus,
                setMarketingMemberSetting,
            };
        },
        data() {
            return {
                loading: false,
                memberCardList: [],
                selectMemberItem: {},
                isShowMemberForm: false,
                enablePaymentPassword: false,
                enableMemberRecharge: false,
                memberRechargeMinAmount: null,
                enableAutoUpgradeMember: false,
                enableMemberStepRecharge: 0,
                memberStepRechargeRules: [{
                    principal: '',
                    present: '',
                }],
                autoUpgradeMemberType: null,
                currentMemberCard: {
                    id: null,
                    memberCount: 0,
                },
                currentMemberItem: 0,
                paginationParams: {
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
                fromSource: '',
            };
        },
        computed: {
            ...mapGetters(['crmPermission', 'currentClinic']),
            ...mapGetters('weShop', ['enableWeShop']),
            ...mapGetters(['isChainSubStore']),
            tableRenderConfig() {
                return new MemberTypeTableConfig(this.currentClinic, this.enableWeShop).getTableConfig();
            },
            memberSettingTips() {
                const tips = [];
                if (!this.enablePaymentPassword) {
                    tips.push('未开启会员余额密码支付');
                } else {
                    tips.push('已开启会员余额密码支付');
                }
                return tips.join('，');
            },
            isFromWeShop() {
                return this.fromSource === 'we-shop-member';
            },
        },
        watch: {
            '$route': {
                handler(val) {
                    this.fromSource = val?.name || '';
                },
                immediate: true,
            },
        },
        async created() {
            await this.fetchCrmPermission();
        },
        methods: {
            ...mapActions(['fetchCrmPermission']),
            handleMounted(data) {
                this.paginationParams.pageSize = data.paginationLimit;
                this.refresh();
            },
            showMemberFrom(item) {
                if (item) {
                    this.$data.currentMemberCard = item;
                } else {
                    this.$data.currentMemberCard = {
                        id: null,
                        memberCount: 0,
                    };
                }
                this.$data.isShowMemberForm = true;
            },
            updatePointRule(data) {
                if (data) {
                    this.enablePaymentPassword = data.enablePaymentPassword;
                    this.enableMemberRecharge = data.enableMemberRecharge;
                    this.memberRechargeMinAmount = data.memberRechargeMinAmount;
                    this.autoUpgradeMemberType = data.autoUpgradeMemberType;
                    this.enableAutoUpgradeMember = data.enableAutoUpgradeMember;
                    this.enableMemberStepRecharge = data.enableMemberStepRecharge;
                    this.memberStepRechargeRules = data.memberStepRechargeRules;
                }
            },
            changePageIndex(index) {
                this.paginationParams.pageIndex = index - 1;
                this.refresh();
            },
            async refresh() {
                try {
                    this.loading = true;
                    const res = await MarketingAPI.getMemberActivities(
                        this.paginationParams.pageIndex,
                        this.paginationParams.pageSize,
                        1,
                    );
                    const {
                        total, rows,
                    } = res.data;
                    if (rows.length === 0 && this.paginationParams.pageIndex !== 0) {
                        this.paginationParams.pageIndex--;
                        this.refresh();
                    } else {
                        this.memberCardList = rows;
                        this.paginationParams.count = total;
                        this.pageTotal = Math.ceil(total / this.paginationParams.pageSize);
                    }
                    this.enablePaymentPassword = this.crmPermission.patient.enableMemberPassword;
                    this.enableMemberRecharge = this.crmPermission.patient.enableMemberRecharge;
                    this.memberRechargeMinAmount = this.crmPermission.patient.memberRechargeMinAmount;
                    this.autoUpgradeMemberType = this.crmPermission.patient.autoUpgradeMemberType;
                    this.enableAutoUpgradeMember = this.crmPermission.patient.enableAutoUpgradeMember;
                    this.enableMemberStepRecharge = this.crmPermission.patient.enableMemberStepRecharge;
                    this.memberStepRechargeRules = this.crmPermission.patient.memberStepRechargeRules;
                    this.loading = false;
                } catch (error) {
                    console.log('refresh error', error);
                    this.loading = false;
                }
            },
        },
    };
</script>
