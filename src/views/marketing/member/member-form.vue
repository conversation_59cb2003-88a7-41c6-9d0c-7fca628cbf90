<template>
    <div class="member-form">
        <abc-dialog
            v-if="visible"
            :title="'会员'"
            :value="visible"
            size="huge"
            content-styles="height: 600px;"
            append-to-body
            @input="(val) => $emit('visible', val)"
        >
            <biz-setting-form v-abc-loading="pageLoading" :label-width="105">
                <abc-flex :gap="24" vertical>
                    <biz-setting-form-item label-line-height-size="medium" label="会员等级">
                        <abc-input
                            v-model="postData.memberCard.name"
                            :disabled="postData.memberCard.innerFlag === 1 || isFromWeShop || isChainSubStore"
                            :max-length="10"
                            :width="240"
                            type="text"
                        ></abc-input>
                    </biz-setting-form-item>
                    <biz-setting-form-item
                        v-if="postData.memberCard.innerFlag !== 1"
                        label-line-height-size="medium"
                        label="获取条件"
                    >
                        <biz-setting-form-item-tip tip="前往营销-积分设置，设置获取规则">
                            <abc-space>
                                <abc-text>累计积分达</abc-text>
                                <abc-input
                                    v-model="postData.memberCard.pointsAutoUpdate"
                                    :disabled="isFromWeShop || isChainSubStore"
                                    :config="{
                                        max: 99999, formatLength: 0
                                    }"
                                    :max-length="7"
                                    :width="136"
                                    type="number"
                                >
                                    <template #append>
                                        分
                                    </template>
                                </abc-input>
                                <abc-text>自动升级为该会员</abc-text>
                            </abc-space>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                    <biz-setting-form-item v-if="isShowMemberPriceGoods" label="单品会员价">
                        <biz-setting-form-item-tip :tip="memberPriceGoodsTip">
                            <abc-table
                                :data-list="goodsList"
                                :hidden-table-header="true"
                                :render-config="tableRenderConfig"
                                style="max-height: 400px;"
                            >
                                <template
                                    #name="{
                                        trData, cellRowSpan
                                    }"
                                >
                                    <abc-table-cell align="center" :cell-row-span="cellRowSpan">
                                        <span>{{ goodsFullName(trData.goods) }}</span>
                                    </abc-table-cell>
                                </template>
                            </abc-table>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                    <biz-setting-form-item v-if="isPharmacy" label="商品组折扣/特价" label-line-height-size="small">
                        <biz-setting-form-item-tip tip="结算时，商品组特价/折扣优于分类折扣计算">
                            <abc-form ref="goodsGroupListForm">
                                <abc-flex vertical gap="middle" align="start">
                                    <abc-button size="small" variant="text" @click="openGroupDialog">
                                        修改商品组
                                    </abc-button>
                                    <template v-if="goodsGroupList.length">
                                        <abc-table
                                            type="excel"
                                            :data-list="curGoodsGroupList"
                                            :hidden-table-header="true"
                                            :render-config="goodsGroupTableRenderConfig"
                                            support-delete-tr
                                            class="member-form-goods-group-table"
                                            style=" width: 100%; min-height: 40px; max-height: 456px;"
                                            @delete-tr="handleDeleteGoodsGroup"
                                        >
                                            <template #name="{ trData }">
                                                <abc-table-cell style="justify-content: space-between; width: 100%;">
                                                    <abc-text>{{ trData.name }}</abc-text>
                                                    <abc-button variant="text" theme="primary" @click="handleViewGoodsGroupList(trData)">
                                                        查看
                                                    </abc-button>
                                                </abc-table-cell>
                                            </template>
                                            <template #discountType="{ trData }">
                                                <abc-select
                                                    v-model="trData.discountType"
                                                    style="width: 100%;"
                                                    :input-style="{
                                                        textAlign: 'center'
                                                    }"
                                                    :inner-width="88"
                                                    size="small"
                                                    @change="handlePromotionTypeChange(trData)"
                                                >
                                                    <abc-option
                                                        v-for="(label, value) in PromotionTypeStrFilterGift"
                                                        :key="value"
                                                        :label="label"
                                                        :value="Number(value)"
                                                    ></abc-option>
                                                </abc-select>
                                            </template>
                                            <template #discount="{ trData }">
                                                <abc-form-item
                                                    :validate-event="validateDiscount"
                                                    :validate-params="trData"
                                                    style="width: 100%;"
                                                >
                                                    <abc-input
                                                        v-abc-focus-selected
                                                        :value="trData.discountType === PromotionTypeEnum.special ? trData.specialValue : trData.discountValue"
                                                        data-cy="abc-input-折扣"
                                                        :max-length="5"
                                                        type="number"
                                                        adaptive-width
                                                        :input-custom-style="{
                                                            textAlign: 'center'
                                                        }"
                                                        :config="{
                                                            max: trData.discountType === PromotionTypeEnum.discount ? 9.9 : 9999,
                                                            min: 0,
                                                            supportZero: false,
                                                            formatLength: trData.discountType === PromotionTypeEnum.discount ? 1 : 2,
                                                        }"
                                                        @input="handleDiscountInput($event, trData)"
                                                        @blur="handleDiscountChange(trData)"
                                                    >
                                                        <span slot="appendInner">{{ trData.discountType === PromotionTypeEnum.discount ? '折' : '元' }}</span>
                                                    </abc-input>
                                                </abc-form-item>
                                            </template>
                                            <template v-if="goodsGroupList?.length > goodsGroupListParams.pageSize" #footer>
                                                <abc-pagination
                                                    :count="goodsGroupList.length"
                                                    :pagination-params="goodsGroupListParams"
                                                    show-total-page
                                                    size="small"
                                                    :current-page="goodsGroupListParams.pageIndex"
                                                    @current-change="handleGoodsGroupPageChange"
                                                ></abc-pagination>
                                            </template>
                                        </abc-table>
                                    </template>
                                    <abc-card
                                        v-else
                                        padding-size="huge"
                                        border
                                        radius-size="small"
                                        style="width: 100%;"
                                    >
                                        <abc-content-empty top="0"></abc-content-empty>
                                    </abc-card>
                                </abc-flex>
                            </abc-form>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                    <biz-setting-form-item
                        :has-divider="!isShowMemberPriceGoods"
                        label-line-height-size="small"
                        :label="isPharmacy ? '分类折扣' : '折扣权益'"
                        :use-inner-layout="false"
                    >
                        <abc-form class="member-range-select-wrapper">
                            <select-goods-type-list
                                :show-goods-select="!isShowMemberPriceGoods"
                                :disabled="isFromWeShop || isChainSubStore"
                                :goods-type-list.sync="postData.memberCard.discountList"
                                @handleOptTracker="handleOptTracker"
                            >
                                <template v-if="!isShowMemberPriceGoods && postData.memberCard.discountList.length" #search>
                                    <abc-autocomplete
                                        v-model.trim="keyword"
                                        :width="220"
                                        :inner-width="560"
                                        placeholder="商品名/首字母"
                                        clearable
                                        :fetch-suggestions.sync="searchGoodsInfo"
                                        search-goods-info
                                        :async-fetch="true"
                                        @enterEvent="selectGoods"
                                        @clear="clearKeyword"
                                        @blur="handleBlur"
                                    >
                                        <abc-icon slot="prepend" icon="search"></abc-icon>
                                        <template slot="suggestions" slot-scope="{ suggestion }">
                                            <dt
                                                class="suggestions-item"
                                                @click="selectGoods(suggestion)"
                                            >
                                                <div
                                                    style="width: 240px; min-width: 240px; max-width: 240px; padding-right: 10px;"
                                                    class="ellipsis"
                                                    :title="suggestion | goodsFullName"
                                                >
                                                    {{ suggestion | goodsFullName }}
                                                </div>
                                                <div style="width: 140px; padding-right: 10px;" class="ellipsis" :title="suggestion | goodsDisplaySpec">
                                                    {{ suggestion | goodsDisplaySpec }}
                                                </div>
                                                <div style="flex: 1; padding-right: 10px;" class="ellipsis" :title="suggestion.manufacturer">
                                                    {{ suggestion.manufacturer }}
                                                </div>
                                            </dt>
                                        </template>
                                    </abc-autocomplete>
                                </template>
                                <template #table>
                                    <select-goods-type-table
                                        :goods-list.sync="postData.memberCard.discountList"
                                        :show-discount-radio="!isShowMemberPriceGoods"
                                        :show-error="false"
                                        :show-labe-input="true"
                                        :disabled="isFromWeShop || isChainSubStore"
                                        :tips="'配置参与活动的项目范围'"
                                        :hide-goods-list="isShowMemberPriceGoods"
                                        :show-except-items-popover="isChainSubStore"
                                        is-member-or-discount
                                        show-except-items
                                        :only-show-exception="isShowMemberPriceGoods"
                                        :show-empty="!!postData.memberCard.discountList.length"
                                        :filter-params="filterParams"
                                        :filter-func="filterList"
                                        @handleOptTracker="handleOptTracker"
                                    >
                                    </select-goods-type-table>
                                </template>
                            </select-goods-type-list>
                        </abc-form>
                    </biz-setting-form-item>
                    <biz-setting-form-item
                        v-if="isWeShopReady"
                        has-divider
                        label="微商城折扣权益"
                        class="line-space line-space-padding activity-range"
                        style="margin-bottom: 0;"
                        label-line-height-size="small"
                        :use-inner-layout="false"
                    >
                        <abc-form class="member-range-select-wrapper">
                            <select-we-shop-goods-type
                                :type-list="activityTypeList"
                                :goods-list="activityGoodsList"
                                :show-type-tips="false"
                                :show-goods-tips="false"
                                @change-goods-list="handleChangeGoodsList"
                                @change-type-list="handleChangeTypeList"
                            >
                                <template #table>
                                    <select-we-shop-promotion-goods-list
                                        :show-label-input="true"
                                        show-except-items
                                        :show-discount-radio="true"
                                        is-member-or-discount
                                        :tips="'配置参与活动的项目范围'"
                                        :table-data-list.sync="postData.memberCard.mallDiscountList"
                                    ></select-we-shop-promotion-goods-list>
                                </template>
                            </select-we-shop-goods-type>
                        </abc-form>
                    </biz-setting-form-item>
                    <biz-setting-form-item label="自定义权益">
                        <biz-setting-form-item-tip :tip="`仅用于顾客展示，权益由${ $app.institutionTypeWording }发放`">
                            <abc-form item-no-margin :item-block="true">
                                <abc-flex vertical gap="12">
                                    <div
                                        v-for="(item, index) in postData.memberCard.benefits"
                                        :key="index"
                                    >
                                        <abc-form-item
                                            :validate-event="validateRightsCount"
                                            required
                                        >
                                            <abc-textarea
                                                :key="index"
                                                ref="customInput"
                                                v-model="item.value"
                                                show-max-length-tips
                                                :input-custom-style="{
                                                    width: '100%',
                                                    height: '76px'
                                                }"
                                                :disabled="isFromWeShop || isChainSubStore"
                                                :placeholder="'输入权益内容'"
                                                type="text"
                                                @enter="insertNewDefault(index)"
                                            ></abc-textarea>
                                        </abc-form-item>
                                    </div>
                                </abc-flex>
                            </abc-form>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                </abc-flex>
            </biz-setting-form>
            <abc-flex slot="footer" justify="space-between">
                <div>
                    <abc-button
                        v-if="(memberTypeId && postData.memberCard.innerFlag !== 1) && !isChainSubStore"
                        :disabled="loading || isFromWeShop"
                        theme="danger"
                        variant="ghost"
                        @click="deleteCard"
                    >
                        删除
                    </abc-button>
                </div>
                <div>
                    <abc-button v-if="!isChainSubStore" :disabled="loading" @click="save">
                        保存
                    </abc-button>
                    <abc-button variant="ghost" @click="cancel">
                        取消
                    </abc-button>
                </div>
            </abc-flex>
        </abc-dialog>
    </div>
</template>

<script>
    import MarketingAPI from 'api/marketing';
    import {
        emojiExits, filterStrSpace,
    } from 'utils/validate';
    import {
        enlargeDiscount, reduceDiscount,
    } from 'views/marketing/util.js';
    import BizSettingForm from '@/components-composite/setting-form/src/views/index.vue';
    import BizSettingFormItem from '@/components-composite/setting-form/src/views/item.vue';
    import BizSettingFormItemTip from '@/components-composite/setting-form/src/views/tip.vue';
    import SelectGoodsTypeList from 'views/marketing/components/select-goods-type-list/select-goods-type-list.vue';
    import SelectGoodsTypeTable from 'views/marketing/components/select-goods-type-list/select-goods-type-table.vue';
    import SelectWeShopGoodsType
        from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-type.vue';
    import {
        GOODS_DISCOUNT_TYPE,
    } from 'views/we-clinic/frames/shop/marketing-activities/constants';
    import Clone from 'utils/clone';
    import SelectWeShopPromotionGoodsList from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-promotion-goods-list.vue';
    import { mapGetters } from 'vuex';
    import { MemberGoodsPriceTableConfig } from 'views/marketing/data/member-form-goods-price-table-data';
    import {
        goodsFullName,
    } from '@/filters';
    import GoodsV3API from 'api/goods/index-v3';
    import useMemberLevelList from '@/hooks/business/use-member-levels-list';
    import {
        PromotionTypeEnum,
        PromotionTypeStrFilterGift,
        SalesPromotionDiscountTypeEnum,
    } from 'views/marketing/sales-promotion/constants.js';
    import GoodsGroupTransferDialog from 'components/goods-group-transfer/index.js';
    import GoodsGroupGoodsDialog from 'components/goods-group-goods-table/index.js';
    import usePaginationSerial from 'views/marketing/hooks/use-pagination-serial';
    import useDataOperationTracker, { OPT_TYPES } from 'views/marketing/hooks/use-data-operation-tracker';

    export default {
        components: {
            SelectGoodsTypeTable,
            SelectGoodsTypeList,
            BizSettingFormItemTip,
            BizSettingFormItem,
            BizSettingForm,
            SelectWeShopGoodsType,
            SelectWeShopPromotionGoodsList,
        },
        props: {
            memberTypeId: {
                type: String,
                default: '',
            },
            visible: Boolean,
            memberCount: {
                type: Number,
                default: 0,
            },
            isFromWeShop: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const { refresh } = useMemberLevelList({ MarketingAPI });
            const goodsTracker = useDataOperationTracker();
            const goodsGroupTracker = useDataOperationTracker();
            return {
                refresh,
                goodsTracker,
                goodsGroupTracker,
            };
        },
        data() {
            return {
                isForever: 1,
                patientType: 0,
                organType: 0,

                postData: {
                    memberCard: {
                        id: '',
                        name: '',
                        pointUpdate: '',
                        benefits: [],
                        discountList: [],
                        mallDiscountList: [],
                    },
                },
                originalDiscountList: [], // 保存原始的折扣列表
                loading: false,
                pageLoading: false,
                isCreated: false,
                pickerOptions: {
                    disabledDate: (date) => {
                        return date < new Date(new Date().getTime() - 60 * 60 * 1000 * 24);
                    },
                },
                goodsList: [],
                typeList: [],

                keyword: '',
                filterParams: {
                    id: '',
                },

                PromotionTypeEnum,
                PromotionTypeStrFilterGift,
                goodsGroupList: [],
                goodsGroupListParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
            };
        },
        computed: {
            ...mapGetters('weShop', ['isWeShopReady']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters(['isAdmin', 'isChainAdmin', 'isChainSubStore', 'currentClinic', 'isPharmacy']),
            isShowMemberPriceGoods() {
                return this.viewDistributeConfig.Marketing.member.isShowMemberPriceGoods;
            },
            activityGoodsList() {
                return this.postData.memberCard.mallDiscountList.filter((item) => item.goodsDiscountType === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT);
            },
            activityTypeList() {
                return this.postData.memberCard.mallDiscountList.filter((item) => item.goodsDiscountType === GOODS_DISCOUNT_TYPE.CATEGORY);
            },
            tableRenderConfig() {
                return new MemberGoodsPriceTableConfig(this.currentClinic).extendConfig({
                    'name': {
                        rowSpan: (trData) => {
                            return trData.rowSpan;
                        },
                    },
                    'chainSubName': {
                        dataFormatter: (_,trData) => {
                            return trData.clinicName;
                        },
                    },
                    'originPrice': {
                        customRender: (h, item) => {
                            return (
                            <abc-table-cell>
                                <abc-text theme="gray">
                                    原价：<abc-money value={ this.getGoodsPackagePrice(item, false) }></abc-money>
                                </abc-text>
                            </abc-table-cell>
                            );
                        },
                    },
                    'discount': {
                        dataFormatter: (_,trData) => {
                            return trData.discountType === 1 ? '特价' : '折扣';
                        },
                    },
                    'memberPrice': {
                        customRender: (h, item) => {
                            return (
                            <abc-table-cell>
                                {item.discountType === 1 ?
                                    <abc-text>
                                        <abc-money value={ item.discount?.toFixed(2) }></abc-money>
                                        /{ item.goods?.packageUnit || item.goods?.pieceUnit}
                                    </abc-text> :
                                    <abc-text>
                                        {item.discount}折(折后
                                        <abc-money value={ this.getDiscountPrice(item) }></abc-money>)
                                    </abc-text>
                                }
                            </abc-table-cell>
                            );
                        },
                    },
                });
            },
            memberPriceGoodsTip() {
                return this.isPharmacy ? '结算时，商品会员价优于商品组和分类折扣，商品会员价请前往商品档案设置' : '结算时，商品的会员价将优先于会员折扣计算，商品会员价请前往商品档案设置';
            },
            goodsGroupTableRenderConfig() {
                return {
                    list: [
                        {
                            key: 'name',
                            label: '商品名称',
                            style: { minWidth: '180px' },
                        },
                        {
                            key: 'discountType',
                            label: '折扣类型',
                            style: {
                                minWidth: '88px', maxWidth: '88px',
                            },
                        },
                        {
                            key: 'discount',
                            label: '折扣',
                            style: {
                                minWidth: '150px', maxWidth: '150px',
                            },
                        },
                    ],
                };
            },
            curGoodsGroupList() {
                let startIndex = this.goodsGroupListParams.pageIndex * this.goodsGroupListParams.pageSize;
                let endIndex = startIndex + this.goodsGroupListParams.pageSize;
                const len = this.goodsGroupList.length;
                if (endIndex > len) {
                    endIndex = len;
                }
                if (startIndex < 0 || startIndex > len) {
                    startIndex = 0;
                }
                return this.goodsGroupList.slice(startIndex, endIndex);
            },
        },
        async created() {
            this.loading = true;
            this.pageLoading = true;
            try {
                if (this.memberTypeId) {
                    const memberInitData = await MarketingAPI.getMemberTypeDetail(this.memberTypeId);
                    await this.fetchAllMemberTypeDetails(memberInitData);
                    this.$nextTick(() => {
                        this.pageLoading = false;
                    });
                } else {
                    if (this.postData.memberCard.benefits.length < 1) {
                        this.postData.memberCard.benefits.push({
                            default: true, value: '',
                        });
                    }
                }
            } catch (error) {
                console.log('created error', error);
            } finally {
                this.loading = false;
                this.pageLoading = false;
            }
            this.isCreated = true;
        },
        methods: {
            goodsFullName,
            init(memberData) {
                try {
                    try {
                        memberData.goodsGroupList =
                            (memberData &&
                                memberData.discountList &&
                                memberData.discountList.filter((v) => v.type === 4).map((item) => {
                                    return {
                                        ...item,
                                        discountValue: item.discountType === PromotionTypeEnum.discount ? enlargeDiscount(item.discount, 10, 2) : '',
                                        specialValue: item.discountType === PromotionTypeEnum.special ? item.discount : '',
                                        discountType: item.discountType || 0,
                                        isOriginData: true,
                                    };
                                })) ||
                            [];
                        memberData.discountList =
                            (memberData &&
                                memberData.discountList &&
                                memberData.discountList.filter((v) => v.type !== 4).map((item) => {
                                    const formatDiscount = enlargeDiscount(item.discount, 10, 2);
                                    return {
                                        ...item,
                                        cloneExceptItems: item.exceptItems?.map((it) => ({
                                            ...it,
                                            goods: null,
                                        })),
                                        discount: item.discountType ? item.discount : formatDiscount,
                                        discountType: item.discountType || 0,
                                    };
                                })) ||
                            [];
                        memberData.mallDiscountList = memberData?.mallDiscountList?.map((item) => {
                            const formatDiscount = enlargeDiscount(item.discount, 10, 2);
                            return {
                                ...item,
                                name: item.relatedName || '',
                                ...(item.type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT && { goodsId: item.relatedValue || '' }),
                                ...(item.type === GOODS_DISCOUNT_TYPE.CATEGORY && { typeId: item.relatedValue || '' }),
                                goodsDiscountType: item.type,
                                discount: item.discountType ? item.discount : formatDiscount,
                                discountType: item.discountType || 0,
                            };
                        }) || [];
                        memberData.benefits = JSON.parse(memberData.benefits);
                        if (memberData.benefits.map) {
                            memberData.benefits = memberData.benefits.map((item) => {
                                return { value: item };
                            });
                        }
                    } catch (e) {
                        memberData.benefits = [{ value: memberData.benefits }];
                    }
                    // 给每一项 goods 添加 isOriginData 作为源数据标识
                    memberData.discountList.forEach((item) => {
                        item.isOriginData = true;
                    });
                    this.postData.memberCard = {
                        ...memberData,
                        discountList: [...this.postData.memberCard.discountList, ...memberData.discountList],
                    };
                    this.goodsGroupList = memberData.goodsGroupList || [];
                    this.filterGoodsList();
                    if (this.postData.memberCard.benefits.length < 1) {
                        this.postData.memberCard.benefits.push({
                            default: true, value: '',
                        });
                    }
                } catch (error) {
                    console.log('created error', error);
                }
            },
            async fetchAllMemberTypeDetails(initData) {
                const handlePageData = (pageData) => {
                    // 这里可以处理每一页的数据
                    this.init({
                        ...initData,
                        discountList: pageData.rows ?? [],
                    });
                    // 如果需要，可以在这里执行其他操作
                };
                const {
                    run,
                } = usePaginationSerial(
                    async (params) => {
                        const pageData = await MarketingAPI.getMemberTypePageListDetail({
                            ...params, memberTypeId: this.memberTypeId,
                        });
                        // 每次请求完成后立即处理数据
                        handlePageData(pageData);
                        return pageData;
                    },
                    {
                        limit: 1000,
                    },
                );
                await run();
            },
            update(index, value) {
                this.discountTypeList[index].discount = value;
            },
            validateRightsCount(val, callback) {
                if (val?.length > 140) {
                    callback({
                        validate: false,
                        message: `已达到字数上限${`${val.length}/140`}`,
                    });
                }
            },
            insertNewDefault(index) {
                if (
                    this.postData.memberCard.benefits.length >= 10 ||
                    filterStrSpace(this.postData.memberCard.benefits[index].value).length === 0
                ) {
                    return;
                }
                this.postData.memberCard.benefits[
                    this.postData.memberCard.benefits.length - 1
                ].default = false;
                this.postData.memberCard.benefits.push({ default: true });
                this.$nextTick(() => {
                    this.$refs.customInput[this.$refs.customInput.length - 1].$refs.abcinput.focus();
                });
            },
            removeCustomPower(index) {
                this.postData.memberCard.benefits.splice(index, 1);
            },
            findErrorGoodsGroupIndex() {
                const list = this.goodsGroupList || [];
                return list?.findIndex((item) => {
                    const value = item.discountType === PromotionTypeEnum.special ? item.specialValue : item.discountValue;
                    if (typeof value === 'string') {
                        return !value || !value.trim();
                    }
                    return false;
                });
            },
            async save() {
                const errorIndex = this.findErrorGoodsGroupIndex();
                if (errorIndex !== -1) {
                    const pageIndex = Math.ceil((errorIndex + 1) / this.goodsGroupListParams?.pageSize);
                    if (pageIndex) {
                        this.handleGoodsGroupPageChange(pageIndex);
                    }
                    this.$nextTick(() => {
                        this.$refs?.goodsGroupListForm?.validate();
                    });
                    return;
                }
                this.loading = true;
                // 处理折扣列表和例外商品
                const discountListData = (this.goodsTracker.operationData.value || []).map((item) => {
                    let exceptItems = undefined;
                    if (item.optType === OPT_TYPES.DELETE && item.cloneExceptItems?.length) {
                        exceptItems = item.cloneExceptItems.map((it) => {
                            return {
                                ...it,
                                optType: OPT_TYPES.DELETE,
                            };
                        });
                    } else {
                        exceptItems = item.exceptOperationTrackerItems;
                    }
                    return {
                        ...item,
                        exceptItems,
                        cloneExceptItems: undefined,
                        exceptOperationTrackerItems: undefined,
                        originExceptItems: undefined,
                        discount: item.discountType ? item.discount : reduceDiscount(item.discount, 10, 3),
                    };
                });
                let goodsGroupListData = [];
                if (this.goodsGroupTracker.operationData.value?.length) {
                    goodsGroupListData = this.goodsGroupTracker.operationData.value.map((item) => {
                        item.discount = item.discountType === PromotionTypeEnum.special ? item.specialValue : reduceDiscount(item.discountValue, 10, 3);
                        return item;
                    });
                }
                const discountList = [...discountListData, ...goodsGroupListData];
                const newCardInfo = {
                    name: filterStrSpace(this.postData.memberCard.name),
                    benefits: this.postData.memberCard.benefits,
                    discountList,
                    mallDiscountList: this.postData.memberCard.mallDiscountList.map((item) => {
                        const formatDiscount = reduceDiscount(item.discount, 10, 3);
                        const exceptGoodsIds = item.exceptItems?.map((it) => it.goodsId || item.id);
                        const { type } = item;
                        let relatedValue = '';
                        if (item.id) {
                            relatedValue = item.relatedValue;
                        } else if (type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT) {
                            relatedValue = item.goodsId || '';
                        } else {
                            relatedValue = item.typeId || '';
                        }

                        return {
                            id: item.id || '',
                            type,
                            relatedValue,
                            discount: item.discountType ? item.discount : formatDiscount,
                            discountType: item.discountType || 0,
                            exceptGoodsIds,
                        };
                    }) || [],
                    pointsAutoUpdate: this.postData.memberCard.pointsAutoUpdate,
                    innerFlag: this.postData.memberCard.innerFlag,
                };
                if (!newCardInfo.name || newCardInfo.name.length < 1) {
                    this.$Toast({
                        message: '会员卡名称不能为空',
                        type: 'error',
                    });
                    this.loading = false;
                    return;
                }
                if (this.postData.memberCard?.benefits?.some((item) => item.value?.length > 140)) {
                    this.$Toast({
                        message: '权益内容超出字数限制',
                        type: 'error',
                    });
                    this.loading = false;
                    return;
                }
                if (emojiExits(newCardInfo.name)) {
                    this.$Toast({
                        message: '会员卡名称存在非法字符',
                        type: 'error',
                    });
                    this.loading = false;
                    return;
                }
                if (newCardInfo.pointsAutoUpdate) {
                    if (newCardInfo.pointsAutoUpdate === 0) {
                        this.$Toast({
                            message: '会员卡升级积分标准不能为0',
                            type: 'error',
                        });
                        this.loading = false;
                        return;
                    }
                    if (isNaN(parseInt(newCardInfo.pointsAutoUpdate))) {
                        this.$Toast({
                            message: '会员卡升级积分标准含有非数字字符',
                            type: 'error',
                        });
                        this.loading = false;
                        return;
                    }
                }

                if (!this.isShowMemberPriceGoods) {
                    // 只有非内置会员可以为空
                    const { discountList: _discountList } = this.postData.memberCard;
                    if (!_discountList.length && newCardInfo.innerFlag !== 1) {
                        this.$Toast({
                            message: '折扣权益不能为空',
                            type: 'error',
                        });
                        this.loading = false;
                        return;
                    }
                }

                if (this.isWeShopReady && !newCardInfo.mallDiscountList.length && newCardInfo.innerFlag !== 1) {
                    this.$Toast({
                        message: '微商城折扣权益不能为空',
                        type: 'error',
                    });
                    this.loading = false;
                    return;
                }
                let benefitsArray = [];
                if (this.postData.memberCard.benefits.length) {
                    const filterArray = this.postData.memberCard.benefits.filter((item) => {
                        return filterStrSpace(item.value);
                    });
                    benefitsArray = filterArray.map((item) => {
                        return filterStrSpace(item.value);
                    });
                    for (let i = 0; i < benefitsArray.length; i++) {
                        if (emojiExits(benefitsArray[i])) {
                            this.$Toast({
                                message: '自定义权益中含有非法字符',
                                type: 'error',
                            });
                            this.loading = false;
                            return;
                        }
                    }
                }
                newCardInfo.benefits = JSON.stringify(benefitsArray);
                try {
                    if (this.memberTypeId) {
                        this.$emit('refresh');
                        await MarketingAPI.updateMemberCard(newCardInfo, this.memberTypeId);
                        this.$parent.refresh();
                        this.$Toast({
                            message: '会员卡更新成功',
                            type: 'success',
                        });
                    } else {
                        await MarketingAPI.addMemberCard(newCardInfo);
                        this.$parent.refresh();
                        this.$Toast({
                            message: '会员卡创建成功',
                            type: 'success',
                        });
                    }
                    this.refresh(); // 更新会员等级列表
                    this.$emit('visible', false);
                } catch (e) {
                    if (e && e.code === 28012) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    }
                    this.loading = false;
                }
                this.loading = false;
            },
            cancel() {
                this.$emit('visible', false);
            },
            deleteCard() {
                if (this.memberCount > 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '已有患者开通该会员卡，无法删除',
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '会员卡删除后无法恢复，是否确认删除？',
                        onConfirm: () => {
                            this.remove();
                        },
                    });
                }
            },
            async remove() {
                try {
                    this.loading = true;
                    await MarketingAPI.deleteMemberCard(this.memberTypeId);
                    this.$Toast({
                        message: '删除会员卡成功',
                        type: 'success',
                    });

                    this.refresh(); // 更新会员等级列表
                } catch (e) {
                    this.loading = false;
                    return;
                }
                this.loading = false;
                this.$emit('visible', false);
                this.$emit('refresh');
            },
            handleChangeGoodsList(selectedList) {
                const goodsList = selectedList.map((item) => {
                    return {
                        ...item,
                        type: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                        relatedValue: item.goodsId,
                        discountType: item.discountType || 0,
                    };
                });
                this.postData.memberCard.mallDiscountList = [...this.activityTypeList, ...goodsList];
            },
            handleChangeTypeList(selectedList) {
                const typeList = Clone(this.activityTypeList);
                selectedList.forEach((item) => {
                    if (!typeList.find((it) => it.typeId === item.typeId)) {
                        this.$set(item, 'exceptItems', []);
                        this.$set(item, 'type', GOODS_DISCOUNT_TYPE.CATEGORY);
                        this.$set(item, 'relatedValue', item.typeId);
                        this.$set(item, 'discountType', 0);
                        delete item.id;
                        typeList.push(item);
                    }
                });

                typeList.forEach((item, index) => {
                    if (!selectedList.some((it) => it.typeId === item.typeId)) {
                        typeList.splice(index, 1);
                    }
                });
                this.postData.memberCard.mallDiscountList = [...typeList, ...this.activityGoodsList];
            },
            filterGoodsList() {
                // 先过滤出商品列表
                let filteredList = this.postData.memberCard.discountList.filter((item) => item.type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT);

                // 子店只显示当前门店商品
                if (this.isChainSubStore) {
                    filteredList = filteredList.filter((item) => item.clinicId === this.currentClinic.clinicId);
                }

                // 按 displayName 排序
                filteredList.sort((a, b) => {
                    const nameA = a.goods?.displayName || '';
                    const nameB = b.goods?.displayName || '';
                    return nameA.localeCompare(nameB);
                });

                // 计算相同 displayName 的数量并添加 rowSpan
                let currentName = null;
                let count = 0;
                let firstIndex = 0;

                filteredList.forEach((item, index) => {
                    const displayName = item.goods?.displayName || '';

                    if (displayName !== currentName) {
                        // 为上一组的第一个元素添加 rowSpan
                        if (count > 0) {
                            filteredList[firstIndex].rowSpan = count;
                        }
                        // 开始新的一组
                        currentName = displayName;
                        count = 1;
                        firstIndex = index;
                    } else {
                        count++;
                    }

                    // 处理最后一组
                    if (index === filteredList.length - 1) {
                        filteredList[firstIndex].rowSpan = count;
                    }
                });

                this.goodsList = filteredList;
            },
            getGoodsPackagePrice(goodItem, needFormat = false) {
                let packagePrice = goodItem?.goods?.packagePrice || 0;
                if (needFormat) {
                    packagePrice = packagePrice.toFixed(2);
                }
                return packagePrice;
            },
            getDiscountPrice(goodItem) {
                const {
                    goods = {}, discount = 0,
                } = goodItem;
                const { packagePrice = 0 } = goods;
                return `${Number((discount * (10 * packagePrice)) / 100).toFixed(2)}`;
            },
            filterList(goodsList, filterParams) {
                const list = goodsList;
                if (filterParams.id) {
                    return list.filter((item) => item.goodsId === filterParams.id || item.id === filterParams.id);
                }
                return list;
            },
            async searchGoodsInfo(keyword, callback) {
                keyword = keyword.trim();
                let dataList = [];
                if (keyword) {
                    try {
                        const { data } = await GoodsV3API.searchGoods({
                            keyword,
                            offset: 0,
                            limit: 50,
                        });
                        dataList = data.list || [];

                        // 分类查询
                        const goodsList = this.postData.memberCard.discountList.filter((item) => item.name?.includes(keyword));
                        goodsList.forEach((item) => {
                            const target = dataList.find((it) => it.goodsId === item.goodsId);
                            if (!target) dataList.push(item);
                        });
                    } catch (error) {
                        console.log('querySearchAsync error', error);
                    }
                }
                return callback(dataList);
            },
            selectGoods(item) {
                this.keyword = item.medicineCadn || item.name;
                this.filterParams.id = item.id || item.goodsId;
            },
            clearKeyword() {
                this.keyword = '';
                this.filterParams.id = '';
            },
            handleBlur() {
                if (!this.keyword.trim()) {
                    this.clearKeyword();
                }
            },
            handleOptTracker(goodsItem,optType) {
                if (!goodsItem) return;
                if (optType === OPT_TYPES.DELETE) {
                    this.goodsTracker.saveDeleteItem(goodsItem);
                }
                if (optType === OPT_TYPES.ADD) {
                    this.goodsTracker.saveAddItem(goodsItem);
                }
                if (optType === OPT_TYPES.UPDATE) {
                    this.goodsTracker.saveUpdateItem(goodsItem);
                }
            },
            handleGoodsGroupOptTracker(goodsItem, optType) {
                if (!goodsItem) return;
                if (optType === OPT_TYPES.DELETE) {
                    this.goodsGroupTracker.saveDeleteItem(goodsItem);
                }
                if (optType === OPT_TYPES.ADD) {
                    this.goodsGroupTracker.saveAddItem(goodsItem);
                }
                if (optType === OPT_TYPES.UPDATE) {
                    this.goodsGroupTracker.saveUpdateItem(goodsItem);
                }
            },
            openGroupDialog() {
                new GoodsGroupTransferDialog({
                    onConfirm: this.handleSelectedGoodsGroupChange,
                    selectedGroupList: this.goodsGroupList,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            handleViewGoodsGroupList(data) {
                new GoodsGroupGoodsDialog({
                    groupId: data.goodsId,
                    title: data.name,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            handleGoodsGroupPageChange(pageIndex) {
                this.goodsGroupListParams.pageIndex = pageIndex - 1;
            },
            handleSelectedGoodsGroupChange(list) {
                const newList = list.map((item) => {
                    item.discountType = PromotionTypeEnum.discount; // 默认为折扣
                    return {
                        ...item,
                        specialValue: '',
                        discountValue: '',
                        type: SalesPromotionDiscountTypeEnum.goodsGroup,
                        goodsType: 40,
                        goodsTypeName: '商品组',
                        goodsSubType: 0,
                        goodsCMSpec: '',
                        isAirPharmacy: 0,
                        customTypeId: 0,
                        pharmacyType: 0,
                    };
                });

                // 找出要被移除的项
                const itemsToRemove = this.goodsGroupList.filter(
                    (goods) => !newList.some((item) => (item.goodsId === goods.goodsId)),
                );

                // 为每个要移除的项触发删除操作跟踪并从列表中移除
                itemsToRemove.forEach((removeItem) => {
                    this.handleGoodsGroupOptTracker(removeItem, OPT_TYPES.DELETE);
                    const indexToRemove = this.goodsGroupList.findIndex((item) => item.goodsId === removeItem.goodsId);
                    if (indexToRemove !== -1) {
                        this.goodsGroupList.splice(indexToRemove, 1);
                    }
                });

                // 找出新增的项
                const itemsToAdd = newList.filter(
                    (goods) => !this.goodsGroupList.some((item) => item.goodsId === goods.goodsId),
                );

                // 为每个新增的项触发添加操作跟踪并添加到列表中
                itemsToAdd.forEach((addItem) => {
                    // 查找 operationData 中是否有与 addItem 匹配的项
                    const operationDataArray = this.goodsGroupTracker.operationData.value;
                    const matchedItem = operationDataArray.find((item) => item.goodsId === addItem.goodsId);

                    // 如果找到匹配项，并且有 isOriginData 和 originData 属性，则赋值给 addItem
                    if (matchedItem && matchedItem.isOriginData && matchedItem.originData) {
                        addItem.isOriginData = matchedItem.isOriginData;
                        addItem.originData = { ...matchedItem.originData };
                        console.log('找到匹配项并赋值', matchedItem, addItem);
                    }

                    this.handleGoodsGroupOptTracker(addItem, OPT_TYPES.ADD);
                    // 添加到 goodsGroup 中
                    this.goodsGroupList.push(addItem);
                });
            },
            handleDeleteGoodsGroup(index) {
                const deleteItem = this.goodsGroupList[this.goodsGroupListParams.pageIndex * this.goodsGroupListParams.pageSize + index];
                this.handleGoodsGroupOptTracker(deleteItem, OPT_TYPES.DELETE);
                this.goodsGroupList.splice(this.goodsGroupListParams.pageIndex * this.goodsGroupListParams.pageSize + index, 1);
                if (this.goodsGroupList.length === 0 && this.goodsGroupListParams.pageIndex > 0) {
                    this.goodsGroupListParams.pageIndex--;
                }
            },
            handlePromotionTypeChange(trData) {
                if (trData.discountType === PromotionTypeEnum.special) {
                    trData.discount = trData.specialValue || '';
                } else {
                    trData.discount = trData.discountValue || '';
                }
                this.handleDiscountChange(trData);
            },
            handleDiscountInput(value, trData) {
                if (trData.discountType === PromotionTypeEnum.special) {
                    trData.specialValue = value;
                } else {
                    trData.discountValue = value;
                }
            },
            handleDiscountChange(updatedItem) {
                // 检查operationData中是否已存在对应项
                const operationDataArray = this.goodsGroupTracker.operationData.value;
                const existingItemIndex = operationDataArray.findIndex((item) => item.id === updatedItem.id);

                if (existingItemIndex === -1) {
                    // 如果不存在，才调用handleOptTracker追踪更新操作
                    this.handleGoodsGroupOptTracker(updatedItem, OPT_TYPES.UPDATE);
                } else {
                    // 如果已存在，直接更新对应项的数据，但不改变optType
                    const existingItem = operationDataArray[existingItemIndex];
                    existingItem.discountType = updatedItem.discountType;
                    existingItem.specialValue = updatedItem.specialValue;
                    existingItem.discountValue = updatedItem.discountValue;
                }
            },
            validateDiscount(value, callback, trData) {
                if (value && value.trim()) {
                    callback({ validate: true });
                    return;
                }
                const str = trData.discountType === PromotionTypeEnum.special ? '特价' : '折扣';
                callback({
                    validate: false,
                    message: `请填写${str}`,
                });
            },
        },
    };
</script>

<style lang="scss">
    .member-form-goods-group-table {
        .abc-table-body {
            .abc-table-tr:last-child {
                border-bottom: none;
            }
        }

        .abc-table-footer {
            padding-left: 12px;
        }
    }
</style>
