import BaseClinicTypeTable from 'views/layout/tables/base-clinic-type-table';

export class MemberTypeTableConfig extends BaseClinicTypeTable {
    constructor(props, enableWeShop) {
        super(props);
        this.chainTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '会员等级',
                'slot': true,
                'key': 'memberCard',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '会员数',
                'slot': true,
                'key': 'memberNum',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '16px','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '获取条件',
                'slot': true,
                'key': 'getRequirement',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','paddingLeft': '16px','paddingRight': '16px','textAlign': 'left',
                },
            },{
                'label': '折扣权益',
                'slot': true,
                'key': 'discountRight',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }, enableWeShop && {
                'label': '微商城折扣权益',
                'slot': true,
                'key': 'mallDiscountBenefits',
                'style': {
                    'flex': 1,'width': '200px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }].filter(Boolean),
        };

        this.singleTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '会员等级',
                'slot': true,
                'key': 'memberCard',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '会员数',
                'slot': true,
                'key': 'memberNum',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '16px','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '获取条件',
                'slot': true,
                'key': 'getRequirement',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','paddingLeft': '16px','paddingRight': '16px','textAlign': 'left',
                },
            },{
                'label': '折扣权益',
                'slot': true,
                'key': 'discountRight',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }, enableWeShop && {
                'label': '微商城折扣权益',
                'slot': true,
                'key': 'mallDiscountBenefits',
                'style': {
                    'flex': 1,'width': '200px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }].filter(Boolean),
        };

        this.chainSubTableConfig = {
            'hasInnerBorder': false,
            'list': [{
                'label': '会员等级',
                'slot': true,
                'key': 'memberCard',
                'style': {
                    'flex': 1,'width': '248px','maxWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '会员数',
                'slot': true,
                'key': 'memberNum',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '100px','paddingLeft': '16px','paddingRight': '16px','textAlign': 'right',
                },
            },{
                'label': '获取条件',
                'slot': true,
                'key': 'getRequirement',
                'style': {
                    'flex': 1,'width': '148px','maxWidth': '148px','paddingLeft': '16px','paddingRight': '16px','textAlign': 'left',
                },
            },{
                'label': '折扣权益',
                'slot': true,
                'key': 'discountRight',
                'style': {
                    'flex': 1,'width': '100px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }, enableWeShop && {
                'label': '微商城折扣权益',
                'slot': true,
                'key': 'mallDiscountBenefits',
                'style': {
                    'flex': 1,'width': '200px','maxWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }].filter(Boolean),
        };
    }
}
