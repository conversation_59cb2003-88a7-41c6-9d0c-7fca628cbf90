<template>
    <abc-layout
        style="height: 100%;"
        :class="{
            'market-crm-module-table': hiddenScroll,
        }"
    >
        <abc-layout-header style="padding: 0;">
            <abc-tabs-v2
                :option="displayTabOptions"
                size="huge"
                style="padding: 0 24px 0 8px;"
                :disable-indicator="displayTabOptions.length <= 1"
                @change="handleTabsChange"
            >
            </abc-tabs-v2>
            <abc-button
                v-if="!isChainSubStore"
                icon="s-b-settings-line"
                variant="text"
                size="small"
                style="position: absolute; top: 16px; right: 16px;"
                @click="showPointRuleView"
            >
                设置
            </abc-button>
        </abc-layout-header>
        <router-view></router-view>
    </abc-layout>
</template>

<script type="text/ecmascript-6">
    import { getRouterChildren } from 'router/filter-router.js';
    import useMarketingMemberSetting from 'views/marketing/hooks/memberHook';
    import { mapGetters } from 'vuex';
    export default {
        setup() {
            const {
                setMarketingMemberSetting,
            } = useMarketingMemberSetting();
            return {
                setMarketingMemberSetting,
            };
        },
        computed: {
            ...mapGetters([
                'isChainSubStore',
            ]),
            displayTabOptions () {
                const { routes } = this.$router.options;
                return getRouterChildren(routes, '会员')
                    .filter((item) => !item.meta.hidden)
                    .map((item) => ({
                        label: item.meta.name,
                        value: item.name,
                        path: item.path,
                    }));
            },
            hiddenScroll() {
                if (this.$route?.path?.indexOf('member-manage') > -1) {
                    return true;
                }
                return false;
            },
        },
        methods: {
            handleTabsChange(index, item) {
                this.$router.push({
                    name: item.value,
                });
            },
            showPointRuleView() {
                this.setMarketingMemberSetting(true);
            },
        },
    };
</script>

<style lang="scss">
// TODO基础组件替换，医院样式兼容 12.17 此处杨哥休完产假需要再商量
.market-crm-module-table {
    .crm-module {
        .member-manage {
            height: 100% !important;
            margin-top: 0 !important;
            border-top: none !important;
        }
    }
}
</style>
