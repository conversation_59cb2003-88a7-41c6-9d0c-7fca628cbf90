<template>
    <div class="point-rule">
        <abc-dialog
            v-if="visible"
            v-abc-loading="isLoading"
            :value="visible"
            title="会员设置"
            size="xlarge"
            show-header-border-bottom
            content-styles="max-height: 736px"
            @input="(val) => $emit('visible', val)"
        >
            <biz-setting-form :no-limit-width="true" :label-width="84">
                <biz-setting-form-group title="会员余额设置">
                    <biz-setting-form-item v-if="showPaymentPassword" label="密码支付">
                        <biz-setting-form-item-tip tip="默认密码为会员手机号后6位，输入时推荐使用外置密码器">
                            <abc-checkbox v-model="postData.enablePaymentPassword" class="chkbox">
                                开启
                            </abc-checkbox>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                    <biz-setting-form-item v-if="showMemberRecharge && isShowMemberWxCharge" label="微信自助充值">
                        <biz-setting-form-item-tip :tip="isOpenMp ? `开启后患者可使用微${$app.institutionTypeWording}进行会员充值` : `未开通微${$app.institutionTypeWording}`">
                            <abc-checkbox
                                v-model="postData.enableMemberRecharge"
                                class="chkbox"
                                :disabled="!isOpenMp"
                                @change="handleMemberRecharge"
                            >
                                开启
                            </abc-checkbox>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                    <biz-setting-form-item v-if="postData.enableMemberRecharge && isShowWxChargeRule" label="微信充值规则">
                        <abc-radio-group v-model="postData.enableMemberStepRecharge" @change="handleMemberStepChangeClick">
                            <biz-setting-form-item-tip tip="通过设置充值阶梯限制充值金额">
                                <abc-radio :label="1">
                                    阶梯充值
                                </abc-radio>
                            </biz-setting-form-item-tip>

                            <div v-if="postData.enableMemberStepRecharge" class="recharge-rules-list">
                                <abc-form
                                    label-position="left"
                                    item-block
                                    :label-width="40"
                                >
                                    <div
                                        v-for="(item, index) in postData.memberStepRechargeRules"
                                        :key="index"
                                        class="recharge-rule-item"
                                    >
                                        <abc-form-item>
                                            <abc-space>
                                                <abc-text>充值</abc-text>
                                                <abc-popover
                                                    placement="bottom-start"
                                                    :value="activeErrorRuleIndex === index"
                                                    trigger="manual"
                                                    theme="yellow"
                                                >
                                                    <div slot="reference">
                                                        <abc-input
                                                            v-model.number="item.principal"
                                                            v-abc-focus-selected
                                                            :input-custom-style="{
                                                                textAlign: 'start',
                                                                backgroundColor: '#ffffff'
                                                            }"
                                                            :class="{
                                                                'is-edit':
                                                                    activeErrorRuleIndex === index
                                                            }"
                                                            :width="76"
                                                            type="number"
                                                            :config="{
                                                                supportZero: true,
                                                                formatLength: 2,
                                                                max: 9999999,
                                                                marginRight: 0
                                                            }"
                                                            @input="onPayInput(item, index, postData.memberStepRechargeRules) "
                                                            @focus="onPayFocus(item, index, postData.memberStepRechargeRules) "
                                                            @blur="onPayBlur(item.principal)"
                                                        >
                                                            <label slot="append" class="recharge-rule-append-input">元</label>
                                                        </abc-input>
                                                    </div>
                                                    <abc-text>
                                                        {{ `充值金额为${ item.principal }的规则填写重复` }}
                                                    </abc-text>
                                                </abc-popover>
                                            </abc-space>
                                        </abc-form-item>
                                        <abc-text style="margin: 0 8px;">
                                            送
                                        </abc-text>
                                        <abc-form-item class="recharge-form-item">
                                            <abc-input
                                                v-model.number="item.present"
                                                v-abc-focus-selected
                                                class="small-input"
                                                type="number"
                                                :input-custom-style="{
                                                    textAlign: 'start',
                                                    backgroundColor: '#ffffff'
                                                }"
                                                :width="76"
                                                :config="{
                                                    supportZero: true,
                                                    formatLength: 2,
                                                    max: 9999999
                                                }"
                                                @input="onPayInput(item, index, postData.memberStepRechargeRules)"
                                            >
                                                <label slot="append" class="recharge-rule-append-input">元</label>
                                            </abc-input>
                                        </abc-form-item>
                                        <button class="recharge-rule-item-delete-button">
                                            <img
                                                v-if="postData.memberStepRechargeRules.length > 1"
                                                src="~assets/images/marketing/Icon-delete-normal.svg"
                                                class="delete-icon"
                                                @click="handleDeleteRules(item)"
                                            />
                                        </button>
                                    </div>
                                </abc-form>
                                <div v-if="postData.enableMemberStepRecharge" class="add-rules-btn">
                                    <abc-button
                                        variant="text"
                                        size="small"
                                        @click="handleAddRule"
                                    >
                                        添加规则
                                    </abc-button>
                                    <abc-text
                                        theme="gray"
                                        size="mini"
                                    >
                                        设置规则后，充值时将可按照规则进行赠金发送
                                    </abc-text>
                                </div>
                            </div>
                            <abc-divider variant="dashed"></abc-divider>
                            <biz-setting-form-item-tip tip="通过设置充值最小值限制充值金额">
                                <abc-radio :label="0">
                                    最小值充值
                                </abc-radio>
                            </biz-setting-form-item-tip>

                            <div v-if="!postData.enableMemberStepRecharge" class="line-top">
                                <abc-text>用户在微{{ $app.institutionTypeWording }}充值时充值金额不可小于</abc-text>
                                <abc-input
                                    v-model.number="postData.memberRechargeMinAmount"
                                    type="number"
                                    :width="70"
                                    style="margin: 0 5px;"
                                    :max-length="5"
                                    :config="{
                                        supportZero: true,
                                        formatLength: 2,
                                        max: 99999,
                                        marginRight: 0
                                    }"
                                >
                                    <template #append>
                                        元
                                    </template>
                                </abc-input>
                            </div>
                        </abc-radio-group>
                    </biz-setting-form-item>
                </biz-setting-form-group>
                <biz-setting-form-group v-if="isShowUpgradeSetting" title="升级设置">
                    <biz-setting-form-item label="患者自动升级">
                        <biz-setting-form-item-tip tip="开启后，当患者填写手机和姓名后将自动升级为会员">
                            <abc-checkbox v-model="postData.enableAutoUpgradeMember" class="chkbox">
                                开启
                            </abc-checkbox>
                        </biz-setting-form-item-tip>
                    </biz-setting-form-item>
                    <biz-setting-form-item v-if="postData.enableAutoUpgradeMember" label="患者升级为">
                        <abc-space>
                            <abc-select v-model="postData.autoUpgradeMemberType" :width="160">
                                <abc-option
                                    v-for="item in memberTypes"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.name"
                                ></abc-option>
                            </abc-select>
                            <abc-button variant="text" size="small" @click="visibleMemberType = true">
                                查看会员卡类型
                            </abc-button>
                        </abc-space>
                    </biz-setting-form-item>
                </biz-setting-form-group>
            </biz-setting-form>
            <div slot="footer" class="dialog-footer">
                <abc-button :disabled="!isDataChange" @click="save">
                    完成
                </abc-button>
                <abc-button class="abc-button-blank" @click="cancel">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
        <dialog-member-type v-if="visibleMemberType" v-model="visibleMemberType"></dialog-member-type>
    </div>
</template>

<script>
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import MarketingAPI from 'api/marketing';
    import DialogMemberType from 'views/crm/common/package-member/member-type.vue';
    import clone from 'utils/clone';
    import BizSettingForm from '@/components-composite/setting-form/src/views/index.vue';
    import BizSettingFormGroup from '@/components-composite/setting-form/src/views/group.vue';
    import BizSettingFormItem from '@/components-composite/setting-form/src/views/item.vue';
    import BizSettingFormItemTip from '@/components-composite/setting-form/src/views/tip.vue';
    import { isNotNull } from '@/utils';

    export default {
        components: {
            BizSettingFormItemTip,
            BizSettingFormItem,
            BizSettingFormGroup,
            BizSettingForm,
            DialogMemberType,
        },
        props: {
            showPaymentPassword: {
                type: [Boolean, Number],
                default: true,
            },
            enablePaymentPassword: {
                type: [Boolean, Number],
                default: 0,
            },
            enableAutoUpgradeMember: {
                type: [Boolean, Number],
                default: 0,
            },
            enableMemberStepRecharge: {
                type: [Boolean, Number, String],
                default: 0,
            },
            showMemberRecharge: {
                type: [Boolean, Number],
                default: true,
            },
            enableMemberRecharge: {
                type: [Boolean, Number],
                default: 1,
            },
            memberStepRechargeRules: {
                type: Array,
                default: () => [ {
                    principal: '',
                    present: '',
                }],
            },
            memberRechargeMinAmount: {
                type: String,
                default: '',
            },
            autoUpgradeMemberType: {
                type: String,
                default: null,
            },
            visible: Boolean,
        },
        data() {
            return {
                postData: {
                    enablePaymentPassword: !!this.enablePaymentPassword,
                    enableMemberRecharge: !!this.enableMemberRecharge,
                    memberRechargeMinAmount: this.enableMemberStepRecharge ? '' : (this.memberRechargeMinAmount || 0),
                    autoUpgradeMemberType: this.autoUpgradeMemberType,
                    enableAutoUpgradeMember: !!this.enableAutoUpgradeMember,
                    enableMemberStepRecharge: this.enableMemberStepRecharge,
                    memberStepRechargeRules: this.memberStepRechargeRules || [],
                },
                cacheMemberStepRechargeRules: null,
                isLoading: false,
                // 标识当前有错误的 rule 的索引，若值不为 -1，则表示在对应 rule 下方显示错误提示
                activeErrorRuleIndex: -1,
                memberTypes: [],
                loading: false,
                visibleMemberType: false, // 会员类型拉取
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters(['isOpenMp']),
            isShowUpgradeSetting() {
                return this.viewDistributeConfig.Marketing.member.isShowUpgradeSetting;
            },
            isShowMemberWxCharge() {
                return this.viewDistributeConfig.Marketing.member.isShowMemberWxCharge;
            },
            isShowWxChargeRule() {
                return this.viewDistributeConfig.Marketing.member.isShowWxChargeRule;
            },
            isDataChange() {
                if (
                    this.enablePaymentPassword !== this.postData.enablePaymentPassword ||
                    this.enableMemberRecharge !== this.postData.enableMemberRecharge ||
                    this.memberRechargeMinAmount !== this.postData.memberRechargeMinAmount ||
                    this.enableAutoUpgradeMember !== this.postData.enableAutoUpgradeMember ||
                    this.enableMemberStepRecharge !== this.postData.enableMemberStepRecharge ||
                    this.cacheMemberStepRechargeRules !== this.postData.memberStepRechargeRules
                ) {
                    return true;
                }
                return false;
            },
        },
        created() {
            this.cacheMemberStepRechargeRules = clone(this.postData.memberStepRechargeRules);
            this.fetchMemberTypes();
        },
        methods: {
            ...mapActions(['updateCrmPermission']),

            onPayInput(item, index, rules) {
                const hasDuplicateRule = rules.some(
                    (rule, i) => i !== index && rule.principal === item.principal,
                );
                this.activeErrorRuleIndex = hasDuplicateRule ? index : -1;
            },

            onPayFocus(item, index, rules) {
                const hasDuplicateRule = rules.some(
                    (rule, i) => i !== index && rule.principal === item.principal && isNotNull(rule.principal),
                );
                this.activeErrorRuleIndex = hasDuplicateRule ? index : -1;
            },

            onPayBlur() {
                this.activeErrorRuleIndex = -1;
            },
            handleAddRule() {
                this.postData.memberStepRechargeRules.push({
                    principal: '',
                    present: '',
                });
            },
            handleMemberStepChangeClick(val) {
                if (+val) {
                    this.postData.memberRechargeMinAmount = '';
                } else {
                    this.postData.memberStepRechargeRules = [];
                    this.postData.memberRechargeMinAmount = '0';
                }
            },

            handleMemberRecharge(val) {
                if (!val) {
                    this.postData.memberStepRechargeRules = [];
                    this.postData.enableMemberStepRecharge = 0;
                    this.postData.memberRechargeMinAmount = '0';
                }

            },
            handleDeleteRules(item) {
                const targetIndex = this.postData.memberStepRechargeRules.findIndex(
                    (rule) => rule.principal === item.principal && rule.present === item.present,
                );
                this.postData.memberStepRechargeRules.splice(targetIndex, 1);
            },
            /**
             * desc [拉取会员类型列表]
             */
            async fetchMemberTypes() {
                this.loading = true;
                try {
                    if (this.fetch && this.patientId) {
                        await this.fetchMemberInfo();
                    }
                    const { rows } = await MarketingAPI.getAllMemberCardByChainId(0, 1000);
                    this.memberTypes = rows || [];
                } catch (error) {
                    console.log('fetchMemberTypes error', error);
                }
                this.loading = false;
            },
            async save() {
                this.loading = true;
                this.postData.memberRechargeMinAmount = `${this.postData.memberRechargeMinAmount}`;
                try {
                    if (this.postData.memberRechargeMinAmount && this.postData.memberRechargeMinAmount > 50000) {
                        this.$Toast({
                            message: '充值金额不能超过 50000 元',
                            type: 'error',
                        });
                        return;
                    }
                    if (this.postData.enableMemberStepRecharge && !this.postData.memberStepRechargeRules?.length) {
                        this.$Toast({
                            message: '充值规则不能为空',
                            type: 'error',
                        });
                        return;
                    }

                    // 充值规则不能重复
                    if (this.postData.enableMemberStepRecharge && this.postData.memberStepRechargeRules?.length) {
                        const payList = this.postData.memberStepRechargeRules.map((item) => item.principal);
                        if (payList.includes('')) {
                            this.$Toast({
                                message: '充值规则不能为空',
                                type: 'error',
                            });
                            return;
                        }
                        const paySet = new Set(payList);
                        if (paySet.size !== payList.length) {
                            this.$Toast({
                                message: '充值规则不能重复',
                                type: 'error',
                            });
                            return;
                        }
                    }

                    if (!this.postData.enableMemberStepRecharge && !this.postData.memberRechargeMinAmount) {
                        this.$Toast({
                            message: '请输入充值金额',
                            type: 'error',
                        });
                        return;
                    }
                    if (this.showPaymentPassword) {
                        await this.updateCrmPermission({
                            patient: {
                                enableMemberPassword: this.postData.enablePaymentPassword ? 1 : 0,
                                enableMemberRecharge: this.postData.enableMemberRecharge ? 1 : 0,
                                memberRechargeMinAmount: this.postData.memberRechargeMinAmount,
                                enableMemberStepRecharge: this.postData.enableMemberStepRecharge ? 1 : 0,
                                memberStepRechargeRules: this.postData.memberStepRechargeRules,
                                autoUpgradeMemberType: this.postData.autoUpgradeMemberType,
                                enableAutoUpgradeMember: this.postData.enableAutoUpgradeMember ? 1 : 0,
                            },
                        });
                    }
                    this.$Toast({
                        message: '会员设置成功',
                        type: 'success',
                    });
                } catch (error) {
                    console.log('save error', error);
                    this.loading = false;
                    return;
                }
                this.$emit('change', {
                    enablePaymentPassword: this.showPaymentPassword ? this.postData.enablePaymentPassword : undefined,
                    enableMemberRecharge: this.showMemberRecharge ? this.postData.enableMemberRecharge : undefined,
                    memberRechargeMinAmount: this.postData.memberRechargeMinAmount,
                    enableMemberStepRecharge: this.postData.enableMemberStepRecharge,
                    memberStepRechargeRules: this.postData.memberStepRechargeRules,
                    enableAutoUpgradeMember: this.postData.enableAutoUpgradeMember,
                    autoUpgradeMemberType: this.postData.autoUpgradeMemberType,
                });
                this.$emit('visible', false);
                this.loading = false;
            },
            cancel() {
                this.$emit('visible', false);
            },
        },
    };
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
    @import 'src/styles/theme.scss';
    $defaultSpace: 24px;

    .section-header {
        .title {
            margin-right: 20px;
            font-weight: 500;
            color: black;
        }

        .chkbox {
            color: #000000;

            span {
                color: #000000;
            }
        }

        padding-bottom: 8px;
        margin-bottom: 0;
        border-bottom: 1px dashed #e6eaee;

        &:last-child {
            border-bottom: none;

            .title {
                margin-right: 21px;
            }
        }
    }

    .section-content {
        padding: 12px 0;
        padding-bottom: 0;

        label {
            color: black;
        }

        .tip {
            font-size: 12px;
            color: #96a4b3;

            &.yellow {
                color: $Y2;
            }
        }
    }

    .recharge-rules-list {
        .recharge-rule-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }

            .abc-form-item {
                align-items: center !important;
                margin-bottom: 0;

                .abc-form-item-label {
                    width: unset !important;
                    padding-right: 5px;

                    .label-name {
                        color: #000000 !important;
                    }
                }

                .recharge-form-item {
                    position: relative;
                }

                .recharge-rule-append-input {
                    color: #000000;
                }

                .is-edit,
                .is-edit input {
                    color: $Y2;
                }
            }

            .recharge-rule-item-delimiter {
                display: inline-block;
                margin: 0 5px;
                color: #000000;
            }

            .recharge-rule-item-delete-button {
                background-color: transparent;
                border: 0;
                outline: 0;
            }
        }

        .add-rules-btn {
            margin-top: 5px;
            margin-left: -5px;

            .add-rules-text {
                font-size: 12px;
                color: #aab4bf;
            }
        }
    }

    .recharge-amount-repeat-tips {
        position: absolute;
        left: 0;
        z-index: 5;
        padding: 6px;
        margin-top: 20px;
        font-size: 14px;
        line-height: 14px;
        color: $T1;
        white-space: nowrap;
        background: rgba(255, 253, 236, 1);
        border: 1px solid rgba(203, 184, 22, 1);
        box-shadow: 2px 2px 0 0 rgba(0, 0, 0, 0.1);
    }

    .point-rule {
        .line-space {
            display: flex;
            align-items: center;
            margin-bottom: $defaultSpace;
            color: $T2;

            .line-title {
                display: inline-block;
                width: 96px;
                color: #7a8794;
            }
        }

        .step-recharge-rule {
            display: flex;
            margin-bottom: $defaultSpace;

            .line-title {
                display: inline-block;
                width: 96px;
                color: $T2;
            }
        }

        .line-top {
            display: inline-flex;
            align-items: baseline;
            margin-top: 5px;
        }

        .date-selector-place-holder {
            position: relative;
            left: -98px;
            z-index: 1000;
            display: inline-block;
            width: 94px;
            height: 32px;
            line-height: 32px;
            text-align: right;
            vertical-align: middle;
            pointer-events: none;
            background-color: #ffffff;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);
        }

        .date-icon {
            display: inline-block;
            margin: 0 5px;
            font-size: 14px;
        }
    }
</style>
