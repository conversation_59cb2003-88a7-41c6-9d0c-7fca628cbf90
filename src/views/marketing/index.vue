<template>
    <abc-container has-left-container :is-support-center-scroll="false" class="abc-marketing-setting-container">
        <abc-container-left>
            <side-bar></side-bar>
        </abc-container-left>
        <abc-container-center class="content-container marketing-container">
            <router-view></router-view>
        </abc-container-center>
    </abc-container>
</template>

<script>
    import SideBar from './components/sidebar';
    import AbcUiThemeMixin from 'views/common/abc-ui-theme-mixin';
    export default {
        components: {
            SideBar,
        },
        mixins: [AbcUiThemeMixin],
        data() {
            return {};
        },
    };
</script>

<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';
</style>
