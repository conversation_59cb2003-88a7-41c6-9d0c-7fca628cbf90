<template>
    <abc-layout style="height: 100%;">
        <abc-layout-header style="padding: 0;">
            <abc-tabs-v2
                :option="tabsOption"
                size="huge"
                style="padding: 0 24px 0 8px;"
                @change="handleTabsChange"
            ></abc-tabs-v2>
            <abc-button
                v-if="showCouponSettingBtn"
                icon="s-b-settings-line"
                variant="text"
                size="small"
                style="position: absolute; top: 16px; right: 16px;"
                @click="setMarketingCouponSetting(true)"
            >
                设置
            </abc-button>
        </abc-layout-header>
        <router-view></router-view>
    </abc-layout>
</template>

<script type="text/ecmascript-6">
    import useMarketingCouponSetting from 'views/marketing/hooks/couponHook';

    const tabsOption = [
        {
            label: '优惠券', value: 'coupon-home',
        },
        {
            label: '服务介绍', value: 'coupon-introduce',
        },
    ];

    export default {
        setup() {
            const {
                setMarketingCouponSetting,
            } = useMarketingCouponSetting();
            return {
                setMarketingCouponSetting,
            };
        },
        data() {
            return {
                tabsOption,
                showCouponSettingBtn: true,
            };
        },
        methods: {
            handleTabsChange(index, item) {
                this.showCouponSettingBtn = item?.value === 'coupon-home';
                this.$router.push({
                    name: item.value,
                });
            },
        },
    };
</script>
