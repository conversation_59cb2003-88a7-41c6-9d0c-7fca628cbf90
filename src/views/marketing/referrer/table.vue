<template>
    <biz-setting-layout class="marketing__referrer-page" data-cy="marketing-referrer-page">
        <setting-form-content>
            <!-- 活动列表 -->
            <div class="marketing__referrer-page--content" data-cy="marketing-referrer-content">
                <div class="feature-desc-wrapper">
                    <div v-for="item in featureDescList" :key="item.title" class="feature-item">
                        <img :src="item.img" alt="" />
                        <div class="right">
                            <div class="title">
                                {{ item.title }}
                            </div>
                            <div class="desc">
                                {{ item.desc }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="activity-result">
                    <abc-text size="large" bold>
                        活动效果
                    </abc-text>
                    <div class="activity-result__toolbar">
                        <abc-date-picker-bar
                            v-model="params.currentBasicInfoFilter"
                            :options="dateOptions"
                            value-format="YYYY-MM-DD"
                            :picker-options="pickerOptions"
                            :clearable="false"
                            data-cy="activity-result-date-picker"
                            @change="handleDateChange"
                        >
                        </abc-date-picker-bar>
                        <abc-link data-cy="go-to-referrer-stat-link" @click="goToReferrerStat">
                            查看详细统计数据
                        </abc-link>
                    </div>
                    <section v-abc-loading="overviewLoading" class="activity-result__list">
                        <template v-for="item in activityResultList">
                            <activity-stat-card
                                :key="item.text"
                                class="activity-card-item"
                                :item="item"
                            ></activity-stat-card>
                        </template>
                    </section>
                </div>
                <div class="toolbar">
                    <abc-flex justify="space-between" style="width: 100%;">
                        <abc-space
                            :size="8"
                        >
                            <abc-input
                                v-model="params.keyword"
                                placeholder="输入活动名称"
                                :width="300"
                                data-cy="activity-name-input"
                                @change="getTableData"
                            >
                            </abc-input>
                            <abc-select
                                v-model="params.status"
                                :width="120"
                                placeholder="状态"
                                data-cy="activity-status-select"
                                clearable
                                @change="handleRefresh"
                            >
                                <abc-option
                                    v-for="item in statusOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></abc-option>
                            </abc-select>
                        </abc-space>
                        <abc-flex :gap="8" align="center" justify="center">
                            <div v-if="!isOpenMp" class="alert-desc">
                                <span>创建老带新活动，需开通微{{ $app.institutionTypeWording }}</span>
                                <abc-button v-if="hasWxNav" type="text" @click="handleNavigateToMpSetting">
                                    去开通
                                </abc-button>
                            </div>
                            <abc-button
                                icon="s-b-add-line-medium"
                                :disabled="!isOpenMp"
                                theme="success"
                                data-cy="activity-add-button"
                                @click="handleOpenFormDialog({ id: null })"
                            >
                                新增活动
                            </abc-button>
                        </abc-flex>
                    </abc-flex>
                </div>
                <div class="marketing__referrer-table">
                    <abc-table
                        empty-content="暂无老带新活动"
                        :render-config="tableHeaderConfig"
                        :loading="loading"
                        :data-list="referrerList"
                        data-cy="marketing-referrer-table"
                        @handleClickTr="(item) => isOpenMp ? handleOpenFormDialog(item) : undefined"
                    >
                        <template
                            #name="{
                                trData: item
                            }"
                        >
                            <abc-table-cell>
                                <abc-text theme="primary">
                                    {{ item.name }}
                                </abc-text>
                            </abc-table-cell>
                        </template>
                        <template
                            #validityPeriod="{
                                trData: item
                            }"
                        >
                            <abc-table-cell
                                :title="item.validityPeriod"
                                @click=" handleOpenFormDialog(item)"
                            >
                                {{ item.beginDate ? `${item.beginDate }至${ item.endDate}` : '永久有效' }}
                            </abc-table-cell>
                        </template>
                        <template
                            #referrerList="{
                                trData: item
                            }"
                        >
                            <abc-table-cell
                                :title="calcReferrerList(item)"
                                @click=" handleOpenFormDialog(item)"
                            >
                                {{ calcReferrerList(item) }}
                            </abc-table-cell>
                        </template>
                        <template
                            #statusStr="{
                                trData: item
                            }"
                        >
                            <abc-table-cell
                                :title="item.statusStr"
                            >
                                <abc-tag-v2 :variant="item.realStatus === 90 ? 'light-outline' : 'outline'" :theme="setTagTheme(item)">
                                    {{ item.statusStr }}
                                </abc-tag-v2>
                            </abc-table-cell>
                        </template>
                        <template
                            #newCustomerCount="{
                                trData: item
                            }"
                        >
                            <abc-table-cell :title="item.newCustomerCount">
                                {{ item.newCustomerCount }}
                            </abc-table-cell>
                        </template>
                        <template
                            #operation="{
                                trData: item
                            }"
                        >
                            <abc-table-cell :title="item.operation">
                                <div style="display: inline-block;">
                                    <abc-button
                                        variant="text"
                                        class="copy-btn"
                                        :disabled="item.realStatus === 0"
                                        data-cy="open-statistics-dialog-btn"
                                        @click.stop="handleOpenStatisticsDialog(item)"
                                    >
                                        数据
                                    </abc-button>
                                </div>
                                <div style="display: inline-block;">
                                    <abc-button
                                        variant="text"
                                        class="copy-btn"
                                        data-cy="open-spread-dialog-btn"
                                        :disabled="!(item.realStatus !== 90 && item.realStatus !== 0) || !isOpenMp"
                                        @click.stop="handleSpread(item)"
                                    >
                                        推广
                                    </abc-button>
                                </div>
                                <div style="display: inline-block;">
                                    <abc-button
                                        variant="text"
                                        class="copy-btn"
                                        data-cy="copy-btn"
                                        :disabled="!isOpenMp"
                                        @click.stop="handleCopy(item)"
                                    >
                                        复制
                                    </abc-button>
                                </div>
                            </abc-table-cell>
                        </template>
                    </abc-table>
                </div>
                <abc-pagination
                    :pagination-params="params"
                    :count="count"
                    show-total-page
                    @current-change="handlePageIndexChange"
                >
                    <ul slot="tipsContent">
                        <li>
                            共 <span>{{ count }}</span> 条数据，
                            新增客户 <span>{{ newAddPatientCount }}</span>
                        </li>
                    </ul>
                </abc-pagination>
            </div>
        </setting-form-content>

        <form-dialog
            v-if="formDialogVisible"
            :id="currentId"
            v-model="formDialogVisible"
            :is-copy="isCopy"
            :status="currentStatus"
            @close="isCopy = false"
            @refresh="handleRefresh"
            @open-activity-spread="handleSpread"
        ></form-dialog>
        <activity-spread-dialog
            v-if="activitySpreadDialogVisible"
            :id="currentId"
            v-model="activitySpreadDialogVisible"
            :activity-name="activityName"
            :referral-short-url="referralShortUrl"
        ></activity-spread-dialog>
        <data-statistics-dialog
            v-if="dataStatisticsDialogVisible"
            v-model="dataStatisticsDialogVisible"
            :referral-id="currentId"
            :referrer-type="currentReferrerType"
        ></data-statistics-dialog>
        <!-- 活动引导页面 -->
        <activity-guide-dialog
            v-if="activityGuideDialogVisible"
            v-model="activityGuideDialogVisible"
            @create-activity="handleCreate({ id: null })"
            @refresh="getRemindFlag"
        ></activity-guide-dialog>
    </biz-setting-layout>
</template>
<script>
    import { mapGetters } from 'vuex';
    import FormDialog from './form-dialog/index';
    import ReferrerCombo1 from 'src/assets/images/marketing/referrer-combo1.png';
    import ReferrerCombo2 from 'src/assets/images/marketing/referrer-combo2.png';
    import ReferrerCombo3 from 'src/assets/images/marketing/referrer-combo3.png';
    import ReferrerCombo4 from 'src/assets/images/marketing/referrer-combo4.png';
    import MarketingAPI from 'api/marketing';
    import { dateRangeFormat } from 'views/statistics/common/util';
    import ActivityStatCard from 'views/marketing/referrer/form-dialog/activity-card.vue';
    import ActivitySpreadDialog from './form-dialog/activity-spread-dialog';
    import DataStatisticsDialog from './form-dialog/data-statistics-dialog';
    import ActivityGuideDialog from 'views/marketing/referrer/form-dialog/activity-guide-dialog.vue';

    import { AbcDatePickerBar } from '@abc/ui-pc';
    import AbcAccess from '@/access/utils.js';
    import {
        navigateToMpSetting, navigateToReferrerStat, windowOpen,
    } from '@/core/navigate-helper.js';
    const { DatePickerBarOptions } = AbcDatePickerBar;
    import PromotionAPI from 'views/statistics/core/api/promotion.js';
    import { formatDate } from '@abc/utils-date';
    import SettingFormContent from '@/components-composite/setting-form-layout/src/views/content.vue';
    import BizSettingLayout from '@/components-composite/setting-form-layout/src/views/index.vue';

    export default {
        components: {
            BizSettingLayout,
            SettingFormContent,
            FormDialog,
            ActivityStatCard,
            ActivitySpreadDialog,
            DataStatisticsDialog,
            ActivityGuideDialog,
        },
        data() {
            return {
                statusOptions: [
                    {
                        label: '草稿中',
                        value: 0,
                    },
                    {
                        label: '未开始',
                        value: 1,
                    },
                    {
                        label: '进行中',
                        value: 2,
                    },
                    {
                        label: '已结束',
                        value: 3,
                    },
                ],
                accessMap: AbcAccess.accessMap,
                overviewLoading: false,
                params: {
                    keyword: '',
                    status: '',
                    pageSize: 10,
                    pageIndex: 0,
                    beginDate: formatDate(new Date()),
                    endDate: formatDate(new Date()),
                    currentBasicInfoFilter: DatePickerBarOptions.DAY.label,
                },
                loading: false,
                currentId: '',
                currentReferrerType: '',
                currentStatus: '',
                formDialogVisible: false,
                activitySpreadDialogVisible: false,
                featureDescList: [
                    {
                        title: '1.低成本获客',
                        desc: '发布老带新活动，激励患者/员工推荐新客',
                        img: ReferrerCombo1,
                    },
                    {
                        title: '2.高效便捷',
                        desc: '推荐人可一键分享给微信好友，传播便捷且高效',
                        img: ReferrerCombo2,
                    },
                    {
                        title: '3.自动绑定',
                        desc: '自动绑定推荐关系，奖励发放简单透明',
                        img: ReferrerCombo3,
                    },
                    {
                        title: '4.效果可视化',
                        desc: '全链路跟踪，转化数据一目了然',
                        img: ReferrerCombo4,
                    },
                ],
                referrerList: [],
                count: 0,
                newAddPatientCount: 0,
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                dateOptions: [
                    {
                        label: DatePickerBarOptions.DAY.label,
                        name: DatePickerBarOptions.DAY.name,
                        getValue() {
                            return [new Date(), new Date()];
                        },
                    },
                    DatePickerBarOptions.WEEK,
                    DatePickerBarOptions.MONTH,
                    DatePickerBarOptions.YEAR,
                ],
                isCopy: false,
                referralShortUrl: '',
                activityName: '',
                dataStatisticsDialogVisible: false,
                overviewData: {},
                activityGuideDialogVisible: false,
                remindFlag: false,
            };
        },
        computed: {
            ...mapGetters([
                'isOpenMp',
                'currentClinic',
            ]),
            isPurchased() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.MARKET_REFERRER);
            },
            ...mapGetters(['modulePermission']),
            hasWxClinic() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.WE_CLINIC);
            },
            hasWxNav() {
                if (this.modulePermission.hasWeClinicModule) {
                    // 管理员
                    return this.hasWxClinic;
                }
                return false;
            },
            tableHeaderConfig() {
                return {
                    list: [
                        {
                            label: '活动名称',
                            key: 'name',
                            style: {
                                width: '200px',
                            },
                        },
                        {
                            label: '有效期',
                            key: 'validityPeriod',
                            style: {
                                width: '180px',
                            },
                        },
                        {
                            label: '推荐人',
                            key: 'referrerList',
                            style: {
                                width: '160px',
                            },
                        },
                        {
                            label: '状态',
                            key: 'statusStr',
                            style: {
                                width: '100px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '新增客户',
                            width: 100,
                            key: 'newCustomerCount',
                            style: {
                                width: '100px',
                                textAlign: 'right',
                            },
                        },
                        {
                            label: '操作',
                            key: 'operation',
                            style: {
                                width: '160px',
                                minWidth: '160px',
                                textAlign: 'center',
                            },
                        },
                    ],
                };

            },
            displayDateRange() {
                return dateRangeFormat(this.params.beginDate, this.params.endDate);
            },
            activityResultList() {
                const {
                    newAddPatientCount = 0,
                    payPeopleNumber = 0,
                    patientPayAmount = 0,
                } = this.overviewData || {};
                return [
                    {
                        text: '新增客户数',
                        tips: '成功建档人数总和',
                        dateRange: this.displayDateRange,
                        amount: newAddPatientCount,
                    },
                    {
                        text: '消费人数',
                        tips: '活动带来新客至少完成1次消费的人数总和（去重）',
                        dateRange: this.displayDateRange,
                        amount: payPeopleNumber,
                    },
                    {
                        text: '消费金额',
                        tips: '活动带来新客消费金额总和',
                        dateRange: this.displayDateRange,
                        amount: patientPayAmount,
                    },
                ];

            },
        },
        created() {
            if (this.isPurchased) {
                this.getSummary();
                this.handleRefresh();
                this.getRemindFlag();
            }
        },
        methods: {
            goToReferrerStat() {
                navigateToReferrerStat(this.currentClinic);
            },
            handleReferrerDescClick() {
                windowOpen('https://mp.weixin.qq.com/s/hcm7-s1OEpHUacsI-sp_2Q', '_blank');
            },
            async getRemindFlag() {
                try {
                    const { data } = await MarketingAPI.referrer.getRemindReadStatus('promotion_referral');
                    this.remindFlag = data.created;

                } catch (err) {
                    console.log(err);
                }
            },
            async getSummary() {
                try {
                    this.overviewLoading = true;
                    const {
                        beginDate,
                        endDate,
                    } = this.params;
                    const { data } = await PromotionAPI.referrer.overviewSummary({
                        beginDate,
                        endDate,
                    });
                    if (data) {
                        this.overviewData = data;
                    }
                } catch (err) {
                    console.error(err);
                } finally {
                    this.overviewLoading = false;
                }
            },
            handleOpenStatisticsDialog(item) {
                this.currentId = item.id;
                this.currentReferrerType = item.referrerType;
                this.dataStatisticsDialogVisible = true;
            },
            handleNavigateToMpSetting() {
                navigateToMpSetting(this.currentClinic);
            },
            handleSpread(item) {
                this.activityName = item.name;
                this.currentId = item.id;
                this.activitySpreadDialogVisible = true;
            },
            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
                this.getReferrerTotalData();
            },
            handleCopy(item) {
                this.isCopy = true;
                this.currentId = item.id;
                this.formDialogVisible = true;
            },
            handleDateChange(date) {
                this.params.beginDate = date[ 0 ];
                this.params.endDate = date[ 1 ];
                this.getSummary();
            },
            calcReferrerList(item) {
                const {
                    referrerList, isAllReferrer, referrerType,
                } = item;
                if (isAllReferrer) {
                    return referrerType ? '全部员工' : '全部客户';
                }
                return referrerList?.length > 2 ? `${referrerList.slice(0, 2).map((it) => it.name).join('、')}等${referrerList.length}人` : referrerList?.map((it) => it.name).join('、');
            },

            handleOpenFormDialog(item) {
                if (!this.isOpenMp) {
                    return;
                }
                if (!this.remindFlag && !item.id) {
                    this.activityGuideDialogVisible = true;
                    return;
                }
                this.currentId = item.id;
                this.currentStatus = item.realStatus;
                this.formDialogVisible = true;
            },
            handleCreate(item) {
                this.currentId = item.id;
                this.currentStatus = item.realStatus;
                this.formDialogVisible = true;
            },
            handleRefresh() {
                this.getTableData();
                this.getReferrerTotalData();
            },
            async getTableData(resetPageParams = true) {

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                    this.params.pageSize = 10;
                }
                const {
                    pageIndex, pageSize, status, keyword,
                } = this.params;
                const offset = pageIndex * pageSize;
                this.loading = true;
                try {
                    const { data } = await MarketingAPI.referrer.referrerList({
                        keyword,
                        status,
                        offset,
                        limit: pageSize,
                    });
                    this.referrerList = data.rows || [];
                    this.count = data.total || 0;

                } catch (err) {
                    console.error(err);
                    this.loading = false;
                } finally {
                    this.loading = false;
                }
            },

            async getReferrerTotalData() {
                const {
                    status, keyword,
                } = this.params;
                try {
                    const { data } = await MarketingAPI.referrer.referrerTotal({
                        keyword,
                        status,
                    });
                    if (data) {
                        this.newAddPatientCount = data.newAddPatientCount || 0;
                    }
                } catch (err) {
                    console.error(err);
                }
            },

            setTagTheme(item) {
                if (item.realStatus === 0) {
                    return 'warning';
                }
                if (item.realStatus === 11) {
                    return 'warning';
                }
                if (item.realStatus === 12) {
                    return 'success';
                }
                if (item.realStatus === 90) {
                    return 'default';
                }
            },
        },

    };
</script>
<style lang="scss">
@import '~styles/theme.scss';

.marketing__referrer-page {
    .marketing__referrer-page--content {
        .feature-desc-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            grid-gap: 20px;
            height: 86px;
            padding: 14px 16px 24px 16px;
            background: linear-gradient(#57b3ff, #378ef6);
            border-radius: 5px;

            .feature-item {
                display: flex;
                width: 200px;

                img {
                    display: inline-block;
                    width: 56px;
                    height: 56px;
                    border-radius: 50%;
                }

                .right {
                    margin-top: 2px;
                    margin-left: 10px;
                    color: $S2;

                    .title {
                        font-weight: bold;
                    }

                    .desc {
                        font-size: 12px;
                    }
                }
            }
        }

        .activity-result {
            margin-top: 24px;

            .activity-result__toolbar {
                display: flex;
                justify-content: space-between;
                margin-top: 16px;

                .link {
                    color: $theme1;
                    cursor: pointer;
                }
            }

            .activity-result__list {
                display: flex;
                flex-wrap: nowrap;

                .activity-card-item {
                    border: 1px solid $P6;
                    border-radius: var(--abc-border-radius-small);
                }

                &::after {
                    flex: auto;
                    content: '';
                }
            }
        }

        .toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 24px;

            .toolbar__right {
                display: flex;
                align-items: center;

                img {
                    margin-right: 4px;
                }
            }

            .alert-desc {
                display: inline-block;
                margin-left: 8px;

                span {
                    color: #ff9933;
                }
            }
        }
    }

    .marketing__referrer-table {
        margin-top: 16px;

        .table-body {
            .table-tr {
                &:nth-child(10n) {
                    border-bottom: none;
                }
            }
        }

        .status-name {
            &.is-draft {
                color: #ff9933;
            }

            &.is-not-start {
                color: #ff9933;
            }

            &.is-processing {
                color: #1ec761;
            }

            &.is-end {
                color: $T2;
            }
        }
    }
}
</style>
