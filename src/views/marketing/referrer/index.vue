<template>
    <biz-fill-remain-height style="height: 100%; padding: 0;">
        <template #header>
            <introduce :access-key="accessMap.MARKET_REFERRER" style="height: auto;">
                <abc-tabs-v2
                    :option="tabsOption"
                    style="padding: 0 24px 0 16px;"
                    size="huge"
                    @change="handleTabsChange"
                ></abc-tabs-v2>
            </introduce>
        </template>
        <router-view></router-view>
    </biz-fill-remain-height>
</template>
<script>
    import Introduce from 'views/edition/introduce/index.vue';
    import AbcAccess from '@/access/utils.js';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';
    const tabsOption = [
        {
            label: '老带新', value: 'referrer',
        },
        {
            label: '服务介绍', value: 'referrer-introduce',
        },
    ];
    export default {
        components: {
            BizFillRemainHeight,
            Introduce,
        },
        data() {
            return {
                accessMap: AbcAccess.accessMap,
                tabsOption,
            };
        },
        methods: {
            handleTabsChange(index, item) {
                this.$router.push({
                    name: item.value,
                });
            },
        },
    };
</script>
