<template>
    <abc-dialog
        v-if="selectedEmployeeDialogVisible"
        v-model="selectedEmployeeDialogVisible"
        :title="referrerType ? '指定员工' : '指定客户'"
        append-to-body
        class="referrer__selected-employee-dialog"
        content-styles="width:600px; padding:24px;height:608px "
        @close-dialog="handleCloseDialog"
    >
        <div class="referrer__selected-employee-dialog--header">
            <abc-space
                :size="8"
            >
                <abc-input placeholder="输入客户名称" :width="198" size="tiny">
                </abc-input>
                <abc-button class="abc-button-success" size="small" @click="onAddBtnClick">
                    添加
                </abc-button>
            </abc-space>

            <div v-if="totalCount" class="selected-count">
                已选 {{ totalCount }}人
            </div>
        </div>
        <ul class="referrer__selected-employee-dialog--content">
            <li v-for="item in currentPageData" :key="item.id" class="employee-item">
                <div class="name-box">
                    <span class="name">
                        {{ item.name }}
                    </span>
                    <img
                        v-if="item.isMember === 1"
                        class="vip-icon"
                        width="14"
                        height="14"
                        src="~assets/images/icon-yellow.png"
                        alt="vip"
                    />
                </div>
                <span class="sex">{{ item.sex }}</span>
                <span class="age">{{ item.age | formatAge }}</span>
                <span class="mobile">{{ item.mobile }}</span>
                <span class="delete" @click="handleDelete(item)">删除</span>
            </li>
        </ul>
        <div class="referrer__selected-employee-dialog--footer">
            <abc-pagination
                :pagination-params="params"
                :count="totalCount"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li>
                        共 <span>{{ totalCount }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </div>
        <patient-dialog
            v-if="patientDialogVisible"
            ref="patients"
            v-model="patientDialogVisible"
            :filter-config="filterConfig"
            :selected-patients="localSelectedEmployeeList"
            @open-fileter="visibleFilter = true"
            @update-config="updateFilterConfig"
            @confirm="onConfirmPatients"
        ></patient-dialog>
        <dialog-filter
            v-if="visibleFilter"
            v-model="visibleFilter"
            last-outpatient
            :filter-config="filterConfig"
            @change="onChangeFilterConfig"
        ></dialog-filter>
        <selected-staff-dialog
            v-if="selectedStaffDialogVisible"
            v-model="selectedStaffDialogVisible"
            :employee-list="selectedEmployeeList"
            @select-staff="handleChangeEmployee"
        ></selected-staff-dialog>
    </abc-dialog>
</template>
<script>
    import PatientDialog from 'views/crm/common/package-visit/dialog-patients.vue';
    const DialogFilter = () => import('views/crm/common/package-filter/index.vue');
    import SelectedStaffDialog from './selected-staff-dialog.vue';
    export default {
        components: {
            PatientDialog,
            DialogFilter,
            SelectedStaffDialog,
        },
        props: {
            sampleInfo: {
                type: Object,
                default: () => ({}),
            },
            value: {
                type: Boolean,
                default: false,
            },
            referrerType: {
                type: Number,
                default: 0,
            },
            selectedEmployeeList: {
                type: Array,
                default: () => [],
            },

        },
        data() {
            return {
                localSelectedEmployeeList: [],
                params: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                patientDialogVisible: false,
                visibleFilter: false,
                selectedStaffDialogVisible: false,
            };
        },
        computed: {
            maxPage() {
                return Math.ceil(this.localSelectedEmployeeList.length / this.params.pageSize);
            },
            currentPageData() {
                let startIndex = this.params.pageIndex * this.params.pageSize;
                let endIndex = startIndex + this.params.pageSize;
                const len =
                    (this.localSelectedEmployeeList && this.localSelectedEmployeeList.length) || 0;
                if (endIndex > len) {
                    endIndex = len;
                }
                if (startIndex < 0) {
                    startIndex = 0;
                }
                return this.localSelectedEmployeeList.slice(startIndex, endIndex);
            },
            selectedEmployeeDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            totalCount() {
                return this.localSelectedEmployeeList?.length;
            },
        },
        watch: {
            selectedEmployeeList: {
                handler(val) {
                    this.localSelectedEmployeeList = val;
                },
                deep: true,
                immediate: true,
            },

        },
        created() {
        },
        methods: {
            onAddBtnClick() {
                if (this.referrerType) {
                    this.selectedStaffDialogVisible = true;
                } else {
                    this.patientDialogVisible = true;
                    this.filterConfig = null;
                    this.updateSelectPatients(this.localSelectedEmployeeList);
                }

            },
            onChangeFilterConfig(filterConfig) {
                this.visibleFilter = false;
                this.updateFilterConfig(filterConfig);
                const patientsRef = this.$refs.patients;
                patientsRef && patientsRef.changeFilterConfig();
            },
            updateFilterConfig(filterConfig) {
                this.filterConfig = filterConfig;
            },
            onConfirmPatients(selectedPatients = []) {
                this.patientDialogVisible = false;
                this.updateSelectPatients(selectedPatients);
                this.$nextTick(() => {
                    this.$refs?.visitBox?.refreshUpdateSelectPatients();
                });
            },
            updateSelectPatients(selectedPatients = []) {
                selectedPatients.forEach((item) => {
                    const isExist = this.localSelectedEmployeeList?.find((v) => v.id === item.id);
                    if (!isExist) {
                        this.localSelectedEmployeeList = this.localSelectedEmployeeList.push({
                            ...item,
                        });
                    }
                });
                this.selectedEmployeeDialogVisible = true;
            },

            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
            },
            handleCloseDialog() {
                this.$emit('change-employee', this.localSelectedEmployeeList);
            },
            handleDelete(item) {
                this.localSelectedEmployeeList = this.localSelectedEmployeeList.filter((v) => v.id !== item.id);
            },
            handleChangeEmployee(list) {
                this.localSelectedEmployeeList = list;
            },
        },
    };
</script>
<style lang="scss">
@import '~styles/theme.scss';
@import "src/styles/mixin.scss";

.referrer__selected-employee-dialog {
    &--header {
        margin-bottom: 10px;

        .selected-count {
            margin: 13px 0;
            font-weight: bold;
        }
    }

    &--content {
        min-height: 400px;
        max-height: 552px;
        border-top: 1px solid $P6;

        .employee-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            grid-gap: 6px;
            height: 40px;
            padding: 0 16px;
            line-height: 40px;
            cursor: pointer;
            border-bottom: 1px solid $P6;

            &:hover {
                background: #ebf2fe;
            }

            .name-box {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                max-width: 110px;

                .name {
                    display: inline-block;
                    max-width: 60px;

                    @include ellipsis;
                }

                .vip-icon {
                    width: 14px;
                    height: 14px;
                    margin-left: 4px;
                }
            }

            .sex {
                width: 50px;
            }

            .age {
                width: 80px;
            }

            .mobile {
                width: 130px;
            }

            .delete {
                display: inline-block;
                color: #ff3333;
                text-align: right;
            }
        }
    }
}
</style>
