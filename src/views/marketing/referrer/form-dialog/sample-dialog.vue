<template>
    <abc-dialog
        v-if="sampleDialogVisible"
        v-model="sampleDialogVisible"
        :title="sampleInfo.title"
        append-to-body
        class="referrer-sample-dialog"
        :content-styles="contentStyle"
    >
        <div class="sample-content">
            <div v-for="item in sampleInfo.list" :key="item.title" class="sample-content-item">
                <div v-if="item.title" class="sample-tips-title">
                    {{ item.title }}
                </div>
                <div class="sample-ways-list">
                    <div v-for="(img, index) in item.imgs" :key="index" class="sample-way-wrapper">
                        <div
                            class="sample-way-card"
                            :style="{
                                width: calImgCardWidth(item, img),
                                height: item.id === '1-1' || item.id === '1-2' ? '428px' : '600px'
                            }"
                        >
                            <div
                                class="sample-way-title"
                                :style="{
                                    fontSize: item.id === '1-1' || item.id === '1-2' ? '12px' : '16px' ,
                                    height: item.id === '1-1' || item.id === '1-2' ? '38px' : '52px'
                                }"
                            >
                                {{ img.title }}
                            </div>
                            <div class="sample-way-content">
                                <img
                                    :src="img.img"
                                    alt=""
                                    :width="calImgWidth(item, img)"
                                    :height="item.id === '1-1' || item.id === '1-2' ? '342' : '479'"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </abc-dialog>
</template>
<script>
    export default {
        props: {
            sampleInfo: {
                type: Object,
                default: () => ({}),
            },
            value: {
                type: Boolean,
                default: false,
            },

        },
        computed: {
            sampleDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            contentStyle() {
                return this.sampleInfo.list.length > 1 ? 'width:1110px; padding:24px;height:520px ' : 'width:709px; padding:24px;height:650px ';
            },
        },
        methods: {
            calImgWidth(item, child) {
                if (item.id === '1-1') return 207;
                if (child.id === '1-2-1') return 403;
                if (item.id === '2-1') return 610;
                if (item.id === '3-1') return 610;
            },
            calImgCardWidth(item, child) {
                if (child.id === '1-1-1') return '286px';
                if (child.id === '1-1-2') return '244px';
                if (child.id === '1-2-1') return '471px';
                if (item.id === '2-1' || item.id === '3-1') return '660px';
            },
        },

    };
</script>

<style lang="scss">
@import '~styles/theme.scss';
@import 'src/styles/mixin';

.referrer-sample-dialog {
    .sample-content {
        display: flex;

        .sample-content-item {
            .sample-tips-title {
                padding-bottom: 24px;
                font-weight: bold;
                color: $T1;
                text-align: center;
            }

            .sample-ways-list {
                display: flex;
                justify-content: flex-start;

                .sample-way-wrapper {
                    display: flex;
                    justify-content: space-between;

                    .sample-way-card {
                        margin-right: 18px;
                        background: #f5f7fb;
                        border-radius: var(--abc-border-radius-small);

                        & + .sample-way-card {
                            margin-left: 8px;
                        }

                        .sample-way-title {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 0 10px;
                            font-weight: bold;
                            color: #3070b5;
                            background-color: #d9e3f1;
                            border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;
                        }

                        .sample-way-content {
                            display: flex;
                            justify-content: center;
                            padding-top: 22px;
                        }
                    }
                }
            }
        }
    }
}
</style>

