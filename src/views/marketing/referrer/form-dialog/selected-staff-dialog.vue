<!--<template>-->
<!--    <abc-dialog-->
<!--        v-if="staffDialogVisible"-->
<!--        v-model="staffDialogVisible"-->
<!--        title=""-->
<!--        append-to-body-->
<!--        class="select-referrer-staff-dialog"-->
<!--        content-styles="padding:0;"-->
<!--    >-->
<!--        <abc-transfer-v2-->
<!--            v-abc-loading="loading"-->
<!--            show-check-all-->
<!--            :data="employees"-->
<!--            :props="{-->
<!--                label: 'name',-->
<!--                value: 'id',-->
<!--            }"-->
<!--            node-key="id"-->
<!--            show-search-->
<!--            search-placeholder="搜索姓名"-->
<!--            leaf-icon="s-user-color"-->
<!--            result-title="选择员工"-->
<!--            :search-node-method="handleSearch"-->
<!--            :default-checked-keys="selectedEmployee"-->
<!--            @cancel="staffDialogVisible = false"-->
<!--            @confirm="confirmSelect"-->
<!--        >-->
<!--            &lt;!&ndash;            <div slot="pre-extend">&ndash;&gt;-->
<!--            &lt;!&ndash;                <abc-space :size="3" class="toolbar">&ndash;&gt;-->
<!--            &lt;!&ndash;                    <clinic-select&ndash;&gt;-->
<!--            &lt;!&ndash;                        v-model="clinicId"&ndash;&gt;-->
<!--            &lt;!&ndash;                        :width="100"&ndash;&gt;-->
<!--            &lt;!&ndash;                        placeholder="门店"&ndash;&gt;-->
<!--            &lt;!&ndash;                        :clinic-list="clinics"&ndash;&gt;-->
<!--            &lt;!&ndash;                        @change="handleClinicChange"&ndash;&gt;-->
<!--            &lt;!&ndash;                    >&ndash;&gt;-->
<!--            &lt;!&ndash;                    </clinic-select>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <abc-input v-model="queryKeyword" :width="150">&ndash;&gt;-->
<!--            &lt;!&ndash;                        <abc-search-icon slot="prepend"></abc-search-icon>&ndash;&gt;-->
<!--            &lt;!&ndash;                    </abc-input>&ndash;&gt;-->
<!--            &lt;!&ndash;                </abc-space>&ndash;&gt;-->
<!--            &lt;!&ndash;            </div>&ndash;&gt;-->
<!--        </abc-transfer-v2>-->
<!--    </abc-dialog>-->
<!--</template>-->
<template>
    <abc-dialog
        v-if="staffDialogVisible"
        v-model="staffDialogVisible"
        title="选择员工"
        append-to-body
        class="select-referrer-staff-dialog"
        data-cy="select-referrer-staff-dialog"
        content-styles="padding:0; height: 480px"
    >
        <abc-transfer
            v-model="selectedEmployee"
            v-abc-loading="loading"
            :data="employees"
        >
            <div slot="pre-extend">
                <abc-space class="toolbar">
                    <clinic-select
                        v-model="clinicId"
                        :width="100"
                        placeholder="门店"
                        :clinic-list="clinics"
                        data-cy="clinic-select"
                        @change="handleClinicChange"
                    >
                    </clinic-select>
                    <abc-input v-model="queryKeyword" :width="182" data-cy="search-input">
                        <abc-search-icon slot="prepend"></abc-search-icon>
                    </abc-input>
                </abc-space>
                <div class="check-all">
                    <span>
                        全选
                    </span>
                    <abc-checkbox
                        :value="isSelectedAll"
                        data-cy="check-all"
                        @change="handleSelectAllClick"
                    ></abc-checkbox>
                </div>
            </div>
            <template slot="extend">
            </template>
            <template #selected="{ item }">
                <abc-icon
                    icon="patient"
                    size="12"
                    color="#58a0ff"
                    style="margin-right: 6px;"
                ></abc-icon>
                {{ item.name }}
            </template>
        </abc-transfer>

        <div slot="footer" class="dialog-footer">
            <abc-button data-cy="confirm-btn" @click="confirmSelect">
                确定
            </abc-button>
            <abc-button type="blank" data-cy="cancel-btn" @click="staffDialogVisible = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>
<script>
    import MarketingAPI from 'api/marketing';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    export default {
        components: {
            ClinicSelect,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            employeeList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                selectedEmployee: [], // 选中操作暂存数组
                loading: false,
                postData: {
                    employeeIds: [],
                    employees: [],
                },
                queryKeyword: '',
                employees: [],
                clinics: [],
                clinicId: '',
            };
        },
        computed: {
            staffDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            isSelectedAll() {
                return this.selectedEmployee.length && this.selectedEmployee.length === this.employees.length;
            },
        },
        watch: {
            queryKeyword(newValue) {
                this.getStaffList(newValue);
            },
            value: {
                handler(val) {
                    if (val) {
                        this.getStaffList();
                        this.selectedEmployee = this.employeeList.slice();
                        this.postData.employees = this.employeeList.slice();
                        this.getClinics();
                    }
                },
                immediate: true,
            },
        },
        methods: {
            handleSelectAllClick(isSelect) {
                if (isSelect) {
                    this.selectedEmployee = [...this.employees];
                    this.employees = this.employees.map((item) => {
                        return {
                            ...item,
                            checked: true,
                        };
                    });
                } else {
                    this.selectedEmployee = [];
                    this.employees = this.employees.map((item) => {
                        return {
                            ...item,
                            checked: false,
                        };
                    });
                }
            },
            handleSearch(searchValue) {
                return {
                    key: searchValue,
                    list: this.employees.filter((o) => o.name.includes(searchValue)),
                };
            },
            handleClinicChange() {
                this.getStaffList();
            },
            async getStaffList(val) {
                val = typeof val === 'object' ? '' : val;
                this.loading = true;
                try {
                    this.selectedEmployee = this.postData.employees.slice();
                    const { data } = await MarketingAPI.referrer.getClinicStaff({
                        keyword: val,
                        clinicid: this.clinicId,
                    });
                    this.employees = data.rows;

                    this.postData.employees.forEach((item) => {
                        this.employees.forEach((employee) => {
                            if (item.id === employee.id) {
                                employee.checked = true;
                            }
                        });
                    });
                    this.staffDialogVisible = true;
                } catch (error) {
                    console.log('fetchEmployeeList error', error);
                }
                this.loading = false;
            },
            confirmSelect() {
                this.selectedEmployee = this.selectedEmployee.map((item) => {
                    return Object.assign(item,{
                        id: item.id,
                        employeeName: item.name,
                    });
                });
                this.postData.employees = this.selectedEmployee;
                this.$emit('select-staff', this.postData.employees);
                this.staffDialogVisible = false;

                // this.selectedEmployee = result;
                // this.postData.employees = this.selectedEmployee;
                // this.$emit('select-staff', this.postData.employees);
                // this.staffDialogVisible = false;
            },

            async getClinics() {
                try {
                    const { data } = await MarketingAPI.referrer.clinicList();
                    this.clinics = data.children;
                } catch (error) {
                    console.log('fetchClinics error', error);
                }
            },
        },
    };
</script>
<style lang="scss">
@import '~styles/theme.scss';

.select-referrer-staff-dialog {
    .toolbar {
        padding-top: 24px;
        padding-right: 30px;
        margin-left: 12px;
    }

    .check-all {
        display: flex;
        justify-content: space-between;
        padding: 12px 12px 12px 16px;
        border-bottom: 1px solid $P6;
    }
}
</style>
