<template>
    <abc-dialog
        v-if="patientDialogVisible"
        v-model="patientDialogVisible"
        title=""
        append-to-body
        size="hugely"
        class="referrer-selected-patient-dialog"
        data-cy="referrer-selected-patient-dialog"
        custom-class="goods-transfer-dialog"
        content-styles="height:680px;padding: 0"
        :auto-focus="false"
    >
        <abc-transfer-v2
            lite
            :show-icon="false"
            :result-list="checkedList"
            size="large"
            :width="1200"
            @del="deleteGoodsSyncCheckedList"
            @confirm="handleConfirm"
            @cancel="patientDialogVisible = false"
        >
            <template #lite-result-node-label="{ node }">
                <abc-icon
                    icon="patient"
                    size="12"
                    :color=" node.sex === '男' ? '#92c1ff' : '#ff5b84'"
                ></abc-icon>
                <span class="nodeName">{{ node.name }}</span>
                <span class="nodeName">{{ node.mobile }}</span>
            </template>
            <abc-layout preset="dialog-table" class="left-content">
                <abc-layout-header>
                    <abc-flex justify="space-between">
                        <abc-space>
                            <abc-space class="tools">
                                <div
                                    :class="{
                                        filter: true,
                                        active: !pageParams.searchText && filterInfo.labels.length !== 0,
                                    }"
                                    data-cy="filter-btn"
                                    @click="visibleFilter = true"
                                >
                                    <abc-icon icon="filtrate" size="14" style="margin-right: 4px;"></abc-icon>
                                    <span>筛选</span>
                                </div>
                                <abc-search
                                    v-model="pageParams.searchText"
                                    placeholder="搜索姓名 / 手机，患者最近诊断"
                                    data-cy="search-input"
                                    :width="300"
                                    @search="fetchData(true)"
                                    @clear="fetchData(true)"
                                ></abc-search>
                            </abc-space>

                            <div v-if="filterInfo.labels.length !== 0" class="section">
                                <transition-group
                                    name="filter-box"
                                    tag="div"
                                    class="filter-list clearfix"
                                >
                                    <span key="-1" class="jiao"></span>
                                    <item-filter
                                        v-for="item in filterInfo.labels"
                                        :key="item.id || item.key"
                                        :label="item.label"
                                        @close="clickDeleteItem(item)"
                                    ></item-filter>
                                </transition-group>
                            </div>
                        </abc-space>
                    </abc-flex>
                </abc-layout-header>
                <abc-layout-content v-abc-loading="loading">
                    <abc-table
                        style="height: 100%;"
                        enable-virtual-list
                        :render-config="tableRenderConfig"
                        :data-list="dataList"
                        :virtual-list-config="virtualListConfig"
                        :scroll-load-config="scrollLoadConfig"
                        data-cy="patient-table"
                        @handleClickTr="rowClickHandler"
                        @changeChecked="rowClickHandler"
                        @changeAllChecked="changeSelectAll"
                    >
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-transfer-v2>
        <dialog-filter
            v-if="visibleFilter"
            v-model="visibleFilter"
            last-outpatient
            :filter-config="filterConfig"
            @change="onChangeFilterConfig"
        ></dialog-filter>
    </abc-dialog>
</template>
<script>
    import MixinFilter from 'views/crm/common/package-filter/mixin-filter.js';
    import AbcSearch from '@/components/abc-search/index.vue';
    import ItemFilter from 'views/crm/common/package-visit/item-filter.vue';
    import { delayPromise } from '@/social-security/common/tools';
    const DialogFilter = () => import('views/crm/common/package-filter/index.vue');
    import clone from 'utils/clone';
    import CrmAPI from 'api/crm';
    import {
        formatAge,
    } from '@/utils';
    import { isEqual } from 'utils/lodash';
    export default {
        components: {
            AbcSearch,

            ItemFilter,
            DialogFilter,
        },
        mixins: [
            MixinFilter,
        ],
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            selectedPatients: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                formatAge,
                pageParams: {
                    searchText: '',
                    orderBy: 'lastOutpatientDate',
                    orderType: 'desc',
                    offset: 0,
                    limit: 15,
                    count: 0,
                },
                loading: false,
                dataList: [],
                checkedList: [], // 选择患者
                filterConfig: null,
                visibleFilter: false,
                isLast: false,
                totalCount: 0,

                tableRenderConfig: {
                    hasInnerBorder: false,
                    list: [
                        {
                            'label': ' ',
                            'isCheckbox': true,
                            'style': {
                                'flex': 'none',
                                'width': '40px',
                                minWidth: '40px',
                            },
                            pinned: true,
                        },
                        {
                            label: '患者',
                            key: 'name',
                            pinned: true,
                            style: {
                                flex: '1',
                                minWidth: '100px',
                            },
                            dataFormatter: (val) => {
                                return val || '匿名患者';
                            },
                        },
                        {
                            label: '性别',
                            key: 'sex',
                            style: {
                                flex: '1',
                                minWidth: '40px',
                            },
                            dataFormatter: (val) => {
                                return val || '-';
                            },
                        },
                        {
                            label: '年龄',
                            key: 'age',
                            style: {
                                minWidth: '80px',
                            },
                            dataFormatter: (val) => {
                                return formatAge(val, {
                                    monthYear: 150, dayYear: 1 , defaultValue: '-',
                                });
                            },
                        },
                        {
                            label: '手机',
                            key: 'mobile',
                            style: {
                                textAlign: 'left',
                                flex: '1',
                                width: '80px',
                                minWidth: '120px',
                                maxWidth: '120px',
                            },
                            dataFormatter: (val) => {
                                return val || '-';
                            },
                        },
                        {
                            key: 'wxNickName',
                            label: '微信',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                maxWidth: '120px',
                                textAlign: 'left',
                            },
                            dataFormatter: (val) => {
                                return val || '-';
                            },
                        },
                        {
                            label: '会员卡',
                            key: 'memberTypeName',
                            style: {
                                flex: '1',
                                minWidth: '76px',
                            },
                            dataFormatter: (val) => {
                                return val || '-';
                            },
                        },
                        {
                            label: '付费次数',
                            key: 'profitRat',
                            style: {
                                textAlign: 'right',
                                flex: '1',
                            },
                            dataFormatter: (val) => {
                                return val || '0';
                            },
                        },
                    ].filter((it) => it.show !== false),
                },
            };
        },
        computed: {
            checkedIds() {
                return this.checkedList.map((item) => item.id);
            },
            patientDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            filterInfo() {
                return this.createFilterParams(this.filterConfig);
            },

            dialogTitle() {
                return this.selectedPatients?.length ? '选择患者' : '添加患者';
            },
            isUpdate() {
                return !isEqual(this.checkedList, this.selectedPatients);
            },
            virtualListConfig() {
                return {
                    visibleCount: 10,
                    bufferSize: 10,
                    scrollendTime: 300,
                    bufferLoad: false,
                };
            },
            scrollLoadConfig() {
                return {
                    fetchData: this.fetchData,
                    total: this.total,
                };
            },
            tablePagination() {
                const {
                    limit,
                    offset,
                } = this.pageParams;
                return {
                    showTotalPage: true,
                    pageIndex: (offset / limit),
                    pageSize: limit,
                    pageSizes: [10, 20, 50, 100],
                    count: this.pageParams.count,
                };
            },
        },
        watch: {
            selectedPatients: {
                handler(list) {
                    this.checkedList = clone(list);
                },
                immediate: true,
                deep: true,
            },
        },

        mounted() {
            this.getPatients();
        },
        methods: {
            deleteGoodsSyncCheckedList(item) {
                const index = this.checkedList.findIndex((t) => t.id === item.id);
                if (index > -1) {
                    this.checkedList.splice(index, 1);
                }
                this.dataList?.forEach((dataItem) => {
                    dataItem.checked = !!this.checkedList.find((it) => it.id === dataItem.id);
                });
            },
            onChangeFilterConfig(filterConfig) {
                this.visibleFilter = false;
                this.filterConfig = filterConfig;
                this.updateFilterConfig(filterConfig);
                this.updatePageParams({ offset: 0 });
                this.getPatients();
            },
            updateFilterConfig(filterConfig) {
                this.filterConfig = filterConfig;
            },
            /**
             * desc [点击删除筛选条件]
             */
            clickDeleteItem(item) {
                const filterConfig = this.handleFilterDelete(this.filterConfig, item.key);
                this.updateFilterConfig(filterConfig);
                this.updatePageParams({ offset: 0 });
                this.isLast = false;
                this.getPatients();
            },
            /**
             * desc [更新筛选参数]
             */
            updateConfig(config) {
                this.$emit('update-config', config);
            },
            changeSelectItem(selected, item) {
                if (selected) {
                    this.checkedList.push({
                        ...item,
                    });
                } else {
                    // 此时，去除
                    this.checkedList = this.checkedList.filter(({ id }) => id !== item.id);
                }
            },

            rowClickHandler(item) {
                this.changeSelectItem(item.checked, item);
            },
            handleConfirm() {
                this.$emit('confirm', this.checkedList);
                this.patientDialogVisible = false;
            },
            changeSelectAll() {
                this.dataList.forEach((item) => {
                    this.changeSelectItem(item.checked, item);
                });
            },
            async getPatients() {
                if (this.isLast) return false;
                this.loading = true;
                try {
                    const params = await this.getParams();
                    const { data } = await CrmAPI.fetchPatients({
                        ...params,
                        patientBasicQuery: false,
                    });

                    if (data?.result?.length < this.pageParams.limit && this.dataList.length > 0) {
                        this.isLast = true;
                    }

                    if (data.offset === this.pageParams.offset) {
                        this.pageParams.offset = this.pageParams.offset + this.pageParams.limit;
                        if (data.offset === 0) {
                            this.dataList = data.result || [];
                        } else {
                            this.dataList = this.dataList.concat(data.result) || [];
                        }
                        this.dataList = clone(this.dataList?.map((item) => {
                            const selected = this.checkedIds.includes(item.id);
                            return {
                                ...item,
                                checked: selected,
                                selected,
                            };
                        }));
                    }
                } catch (error) {
                    console.log('getPatients error', error);
                }
                this.loading = false;
            },
            async getParams() {
                await delayPromise(50);
                const {
                    limit, offset, searchText, orderBy, orderType,
                } = this.pageParams;
                const params = {
                    limit,
                    offset,
                    orderBy,
                    orderType,
                    key: searchText,
                    ...clone(this.filterInfo.params),
                };
                return params;
            },
            updatePageParams(params) {
                this.pageParams = {
                    ...this.pageParams,
                    ...params,
                };
            },
            fetchData(reset = false) {
                if (reset) {
                    this.updatePageParams({
                        offset: 0,
                    });
                    this.isLast = false;
                }
                this.getPatients();
            },
        },
    };
</script>


<style lang="scss">
    @import 'src/styles/abc-common.scss';
    @import "src/styles/theme.scss";
    @import "src/styles/mixin.scss";

    .referrer-selected-patient-dialog {
        .nodeName {
            margin-left: 8px;
        }

        .goods-transfer-dialog {
            .left-content {
                padding: 24px;

                .filter {
                    @include flex(row, center, center);

                    width: 80px;
                    height: 32px;
                    font-size: 14px;
                    color: $T2;
                    cursor: pointer;
                    border: 1px solid $P1;
                    border-radius: var(--abc-border-radius-small);
                }

                .active {
                    color: $B1;
                }
            }

            .selected-sidebar {
                height: 100%;
                border-left: 1px solid var(--abc-color-P6);

                .selected {
                    height: 52px;
                    padding-left: 12px;
                    font-weight: bold;
                    line-height: 52px;
                    border-bottom: 1px solid var(--abc-color-P6);

                    span {
                        margin: 0 2px;
                    }
                }

                .selected-goods-list {
                    height: calc(100% - 52px);
                    padding-right: 34px;
                    overflow-y: auto;

                    @include scrollBar;

                    li {
                        display: flex;
                        align-items: center;
                    }

                    .name {
                        flex: 1;
                    }
                }

                ul li {
                    position: relative;
                    display: flex;
                    align-items: center;
                    height: 40px;
                    padding-left: 12px;
                    border-bottom: 1px solid var(--abc-color-P6);

                    .iconfont {
                        margin-right: 8px;
                    }

                    &:hover {
                        background-color: var(--abc-color-P4);
                    }

                    .selected-delete-icon {
                        position: absolute;
                        top: 10px;
                        right: -24px;
                        width: 20px;
                        color: var(--abc-color-T3);
                        text-align: center;

                        i {
                            margin-right: 0;
                        }
                    }
                }
            }
        }
    }
</style>
