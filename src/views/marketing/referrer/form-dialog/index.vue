<template>
    <div>
        <abc-modal
            v-if="formDialogVisible"
            v-model="formDialogVisible"
            :need-high-level="false"
            :disabled-keyboard="true"
            preset="step"
            size="hugely"
            append-to-body
            :title="dialogTitle"
            :show-cancel="false"
            :show-confirm="false"
            :content-styles="{
                width: '100%',
                height: '100%',
                overflow: 'hidden',
            }"
            class="marketing-referrer-activity-dialog"
            data-cy="marketing-referrer-activity-dialog"
        >
            <!-- 设置步骤-->
            <template #step>
                <div style="height: 20px;">
                    <div v-if="!id">
                        <abc-steps :active="activeStep" active-color="#005ED9">
                            <abc-step :index="0">
                                规则设置
                            </abc-step>
                            <abc-step :index="1">
                                推荐引导设置
                            </abc-step>
                        </abc-steps>
                    </div>

                    <abc-tabs-v2
                        v-if="isDraft || isEditMode"
                        v-model="activeTab"
                        :option="tabOptions"
                        :disable-indicator="tabOptions.length === 1"
                        :item-min-width="192"
                        size="middle"
                        type="outline"
                        @change="changeTab"
                    ></abc-tabs-v2>
                </div>
            </template>
            <template #default>
                <!-- 冲突信息 -->
                <abc-tips-card-v2
                    v-if="!isCopy && ruleFormData.conflicts && ruleFormData.conflicts.length"
                    align="center"
                    theme="warning"
                    style="position: fixed; top: -48px; left: 50%; width: 1152px; transform: translateX(-50%);"
                >
                    当前活动与其他活动有冲突
                    <template #operate>
                        <abc-button type="text" @click="conflictDialogVisible = true">
                            查看冲突
                        </abc-button>
                    </template>
                </abc-tips-card-v2>
                <div ref="settingLeft" class="setting-left">
                    <!-- 基础设置 -->
                    <biz-setting-form v-show="!activeStep" :no-limit-width="true">
                        <abc-form ref="ruleFormRef" item-no-margin>
                            <biz-setting-form-group title="基础设置">
                                <biz-setting-form-item label-line-height-size="medium" label="活动名称">
                                    <abc-form-item required>
                                        <abc-input
                                            v-model="ruleFormData.name"
                                            show-max-length-tips
                                            :disabled="isEnd && !isCopy"
                                            :max-length="20"
                                            adaptive-width
                                            data-cy="activity-name-input"
                                            @change="handleChange"
                                        ></abc-input>
                                    </abc-form-item>
                                </biz-setting-form-item>
                                <biz-setting-form-item label="活动说明">
                                    <abc-form-item>
                                        <abc-textarea
                                            v-model="ruleFormData.description"
                                            :maxlength="200"
                                            show-max-length-tips
                                            :input-custom-style="{ background: '#fff' }"
                                            :height="76"
                                            :disabled="isEnd && !isCopy"
                                            placeholder="简要说明活动背景目的及奖励规则，用于活动详情页展示"
                                            data-cy="activity-description-textarea"
                                            @change="handleChange"
                                        ></abc-textarea>
                                    </abc-form-item>
                                </biz-setting-form-item>
                                <biz-setting-form-item has-divider label="有效期">
                                    <abc-form-item>
                                        <abc-radio-group
                                            v-model="ruleFormData.isForever"
                                            @change="handleDateTypeRange"
                                        >
                                            <abc-radio :label="1" :disabled="!isDraft && isEditMode && !isCopy" data-cy="marketing-referrer-activity-valid-forever">
                                                永久有效
                                            </abc-radio>
                                            <abc-radio :label="0" :disabled="!isDraft && isEditMode && !isCopy" data-cy="marketing-referrer-activity-valid-time">
                                                <abc-flex style="line-height: 32px;" :gap="8">
                                                    限定时间
                                                    <abc-date-picker
                                                        v-if="ruleFormData.isForever === 0"
                                                        v-model="selectDateRange"
                                                        :disabled="!isDraft && isEditMode && !isCopy"
                                                        placeholder="请选择时间"
                                                        data-cy="marketing-referrer-activity-valid-time-picker"
                                                        type="daterange"
                                                        :picker-options="pickerOptions"
                                                        @change="handleDateChange"
                                                    >
                                                    </abc-date-picker>
                                                    <abc-text v-if="showDateError" theme="danger">
                                                        未选择指定时间
                                                    </abc-text>
                                                </abc-flex>
                                            </abc-radio>
                                        </abc-radio-group>
                                    </abc-form-item>
                                </biz-setting-form-item>
                                <marketing-select-card-item
                                    label="适用门店"
                                    has-divider
                                    :get-icon-function="() => 's-organization-color'"
                                    :is-show-card="ruleFormData.isAllClinic === 0"
                                    :item-display-name="(item) => item.organ ? item.organ?.name : item.name"
                                    :disabled="!!ruleFormData.isAllClinic || (!isDraft && isEditMode && !isCopy)"
                                    :tag-data.sync="ruleFormData.clinicIdList"
                                    :edit-btn-text="'选择门店'"
                                    :tag-width="174"
                                    data-cy="clinic-select"
                                    @openDialog="showSelectClinicsTransfer = true"
                                >
                                    <template v-if="showClinicError" #tips>
                                        <abc-text theme="danger">
                                            未选择指定门店
                                        </abc-text>
                                    </template>
                                    <template #radio-group>
                                        <abc-radio-group
                                            v-model="ruleFormData.isAllClinic"
                                            @change="handleClinicChange"
                                        >
                                            <abc-radio :label="1" :disabled=" (!isDraft && isEditMode) && !isCopy" data-cy="marketing-referrer-activity-all-clinic">
                                                全部门店
                                            </abc-radio>
                                            <abc-radio :label="0" :disabled="!isDraft && isEditMode && !isCopy" data-cy="marketing-referrer-activity-specify-clinic">
                                                指定门店
                                            </abc-radio>
                                        </abc-radio-group>
                                    </template>
                                </marketing-select-card-item>
                                <biz-setting-form-item label-line-height-size="medium" label="推荐人">
                                    <abc-radio-group v-model="activeReferrerType">
                                        <abc-space>
                                            <abc-radio-button
                                                v-for="(item) in referrerOptions"
                                                :key="item.value"
                                                :disabled="!isDraft && !isCopy && !!id"
                                                variant="card"
                                                :width="64"
                                                :value="activeReferrerType"
                                                :label="item.value"
                                                :data-cy="`${item.label}-radio-button`"
                                                @click="handleChangeReferrerTab(item)"
                                            >
                                                {{ item.label }}
                                            </abc-radio-button>
                                        </abc-space>
                                    </abc-radio-group>
                                </biz-setting-form-item>
                                <marketing-select-card-item
                                    :get-icon-function="() => 's-user-color'"
                                    :is-show-card="ruleFormData.isAllReferrer === 0"
                                    :item-display-name="(item) => item.name"
                                    :disabled="!!ruleFormData.isAllReferrer || (isEnd && !isCopy)"
                                    :tag-data.sync="selectedEmployeeList"
                                    label=""
                                    :edit-btn-text="activeReferrerType ? '选择员工' : '选择客户'"
                                    :tag-width="78"
                                    data-cy="pepole-select"
                                    @openDialog="handleOpenReferrerDialog"
                                >
                                    <template v-if="showReferrerError" #tips>
                                        <abc-text theme="danger">
                                            未选择指定{{ activeReferrerType ? '员工' : '客户' }}
                                        </abc-text>
                                    </template>
                                    <template #radio-group>
                                        <abc-flex vertical :gap="16">
                                            <abc-radio-group
                                                v-if="activeReferrerType !== -1"
                                                v-model="ruleFormData.isAllReferrer"
                                                style="display: flex; min-height: 24px;"
                                                @change="handleReferrerTypeChange"
                                            >
                                                <abc-radio :label="1" :disabled="isEnd && !isCopy" :data-cy="`all-${activeReferrerType ? 'employee' : 'customer'}-radio`">
                                                    <span style="color: #000000;"> 全部{{ activeReferrerType ? '员工' : '客户' }}</span>
                                                </abc-radio>
                                                <abc-radio :label="0" :disabled="isEnd && !isCopy" :data-cy="`specify-${activeReferrerType ? 'employee' : 'customer'}-radio`">
                                                    <span style="color: #000000;">指定{{ activeReferrerType ? '员工' : '客户' }}</span>
                                                </abc-radio>
                                            </abc-radio-group>
                                        </abc-flex>
                                    </template>
                                </marketing-select-card-item>
                            </biz-setting-form-group>

                            <biz-setting-form-group title="奖励设置">
                                <biz-setting-form-item
                                    v-if="!activeReferrerType"
                                    label-line-height-size="medium"
                                    :has-divider="!ruleFormData.isRewardReferrerCoupon"
                                    label="推荐人奖励"
                                >
                                    <abc-flex align="center" :gap="8">
                                        <abc-checkbox
                                            v-model="ruleFormData.isRewardReferrerCoupon"
                                            :disabled="!isDraft && isEditMode && !isCopy"
                                            type="number"
                                            data-cy="is-reward-referrer-coupon-checkbox"
                                            @change="handleReferrerRewardChange"
                                        >
                                            新客建档后
                                        </abc-checkbox>
                                        <abc-flex align="center">
                                            <abc-form-item :validate-event="validateCouponPeriod">
                                                <abc-input
                                                    v-model="ruleFormData.rewardReferrerCouponPeriod"
                                                    :disabled="!isDraft && isEditMode && !isCopy"
                                                    :width="67"
                                                    type="number"
                                                    data-cy="reward-referrer-coupon-period-input"
                                                    :config="{ max: 99999 }"
                                                    :input-custom-style="{
                                                        textAlign: 'start',
                                                        backgroundColor: '#ffffff',
                                                        marginRight: 0,
                                                        marginLeft: '5px',
                                                        borderTopRightRadius: 0,
                                                        borderBottomRightRadius: 0,
                                                    }"
                                                    @change="handleChange"
                                                ></abc-input>
                                            </abc-form-item>
                                            <abc-select
                                                v-model="ruleFormData.rewardReferrerCouponPeriodUnit"
                                                width="42"
                                                data-cy="reward-referrer-coupon-period-unit-select"
                                                :disabled="!isDraft && isEditMode && !isCopy"
                                                :input-style="{
                                                    borderLeft: 'none',
                                                    borderTopLeftRadius: 0,
                                                    borderBottomLeftRadius: 0,
                                                }"
                                                @change="handleChange"
                                            >
                                                <abc-option
                                                    v-for="item in periodOptions"
                                                    :key="item.label"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-flex>
                                        <abc-text>内首次消费立即获得优惠券</abc-text>
                                    </abc-flex>
                                </biz-setting-form-item>

                                <marketing-select-card-item
                                    v-if="!!ruleFormData.isRewardReferrerCoupon"
                                    style="padding-top: 16px;"
                                    :label="!activeReferrerType ? '' : '推荐人奖励'"
                                    has-divider
                                    :get-icon-function="() => 's-organization-color'"
                                    :is-show-card="!!ruleFormData.isRewardReferrerCoupon"
                                    :item-display-name="(item) => `${ item.name }${ item.rewardCount }张`"
                                    :disabled="!ruleFormData.isRewardReferrerCoupon || (!isDraft && isEditMode && !isCopy)"
                                    :tag-data.sync="referrerCouponList"
                                    :edit-btn-text=" `${ referrerCouponList && referrerCouponList.length ? '修改' : '选择' }优惠券`"
                                    :tag-width="174"
                                    data-cy="reward-referrer-coupon"
                                    @openDialog="addReferrerCouponBtnClick"
                                >
                                    <template v-if="showReferrerCouponRewardError" #tips>
                                        <abc-text theme="warning-light">
                                            未选择优惠券
                                        </abc-text>
                                    </template>
                                </marketing-select-card-item>

                                <biz-setting-form-item has-divider :label="!activeReferrerType ? '' : '推荐人奖励'">
                                    <abc-flex align="center" :gap="8">
                                        <abc-checkbox
                                            v-model="ruleFormData.isRewardReferrer"
                                            :disabled="!isDraft && isEditMode && !isCopy"
                                            type="number"
                                            data-cy="reward-referrer-point-checkbox"
                                            @change="handleRewardChange"
                                        >
                                            新客建档后
                                        </abc-checkbox>
                                        <div style="display: flex; align-items: center;">
                                            <abc-form-item :validate-event="validatePeriod">
                                                <abc-input
                                                    v-model="ruleFormData.rewardReferrerPeriod"
                                                    :disabled="!isDraft && isEditMode && !isCopy"
                                                    class="small-input"
                                                    :width="67"
                                                    type="number"
                                                    data-cy="reward-referrer-period-input"
                                                    :config="{ max: 99999 }"
                                                    :input-custom-style="{
                                                        textAlign: 'start',
                                                        backgroundColor: '#ffffff',
                                                        marginRight: 0,
                                                        marginLeft: '5px',
                                                        borderTopRightRadius: 0,
                                                        borderBottomRightRadius: 0,
                                                    }"
                                                    @change="handleChange"
                                                ></abc-input>
                                            </abc-form-item>
                                            <abc-select
                                                v-model="ruleFormData.rewardReferrerPeriodUnit"
                                                width="42"
                                                data-cy="reward-referrer-period-unit-select"
                                                :disabled="!isDraft && isEditMode && !isCopy"
                                                :input-style="{
                                                    borderLeft: 'none',
                                                    borderTopLeftRadius: 0,
                                                    borderBottomLeftRadius: 0,
                                                }"
                                                @change="handleChange"
                                            >
                                                <abc-option
                                                    v-for="item in periodOptions"
                                                    :key="item.label"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></abc-option>
                                            </abc-select>
                                        </div>
                                        <abc-text>内消费指定项目获得{{ activeReferrerType ? '提成' : '积分' }}奖励</abc-text>
                                    </abc-flex>
                                    <select-goods-type-list
                                        :goods-type-list.sync="ruleFormData.referralGoods"
                                        :disabled="!isDraft && isEditMode && !isCopy"
                                        @handleOptTracker="handleOptTracker"
                                        @change="handleGoodsListChange"
                                    >
                                        <template v-if="showReferrerGoodsError" #tips>
                                            <abc-text theme="warning-light">
                                                未选择活动范围
                                            </abc-text>
                                        </template>
                                        <template #table>
                                            <select-referrer-reward-goods-table
                                                v-show="ruleFormData.isRewardReferrer && ruleFormData.referralGoods.length"
                                                ref="referrerRewardGoodsTableRef"
                                                v-model="allRewardValue"
                                                :rule-form-data="ruleFormData"
                                                :is-draft="isDraft"
                                                :is-edit-mode="isEditMode"
                                                :is-copy="isCopy"
                                                :active-referrer-type="activeReferrerType"
                                                :get-except-info-number="getExceptInfoNumber"
                                                @handleChange="handleChange"
                                                @handleBatchSettingAward="handleBatchSettingAward"
                                                @openExceptGoodsListDialog="openExceptGoodsListDialog"
                                                @deleteItem="deleteItem"
                                                @handleOptTracker="handleOptTracker"
                                            >
                                            </select-referrer-reward-goods-table>
                                        </template>
                                    </select-goods-type-list>
                                </biz-setting-form-item>

                                <biz-setting-form-item label="新客奖励">
                                    <abc-flex vertical :gap="8">
                                        <abc-checkbox
                                            v-model="ruleFormData.isRewardNewPatient"
                                            :disabled="!isDraft && isEditMode && !isCopy"
                                            type="number"
                                            data-cy="reward-new-patient-checkbox"
                                            :customer-style="{
                                                marginRight: '4px',
                                            }"
                                            @change="handleCouponChange"
                                        >
                                            新客被推荐后，首次建档立即获得优惠券
                                        </abc-checkbox>
                                    </abc-flex>
                                </biz-setting-form-item>
                                <marketing-select-card-item
                                    style="padding-top: 12px;"
                                    label=""
                                    has-divider
                                    :get-icon-function="() => 's-organization-color'"
                                    :is-show-card="!!ruleFormData.isRewardNewPatient"
                                    :item-display-name="(item) => `${ item.name }${ item.rewardCount }张`"
                                    :disabled="!ruleFormData.isRewardNewPatient || (!isDraft && isEditMode && !isCopy)"
                                    :tag-data.sync="couponList"
                                    :edit-btn-text=" `${ couponList && couponList.length ? '修改' : '选择' }优惠券`"
                                    :tag-width="174"
                                    data-cy="new-patient-referrer-coupon"
                                    @openDialog="addCouponBtnClick"
                                >
                                    <template v-if="showReferrerCouponError" #tips>
                                        <abc-text theme="warning-light">
                                            未选择优惠券
                                        </abc-text>
                                    </template>
                                </marketing-select-card-item>

                                <biz-setting-form-item label="退款处理">
                                    <biz-setting-form-item-tip tip="系统根据退费前后应发奖励的差额，计算应退奖励并自动退回，若积分已使用，则需要门店手动处理">
                                        <abc-checkbox
                                            v-model="ruleFormData.isRefundReferrerRewardAuto"
                                            :disabled="!isDraft && isEditMode && !isCopy"
                                            type="number"
                                            data-cy="refund-referrer-reward-auto-checkbox"
                                            :customer-style="{
                                                marginRight: '4px',
                                            }"
                                            @change="handleChange"
                                        >
                                            新客退费后，自动退回奖励
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>
                            </biz-setting-form-group>
                        </abc-form>
                    </biz-setting-form>
                    <!-- 分享规则设置 -->
                    <biz-setting-form v-if="activeStep" :no-limit-width="true" :label-width="110">
                        <biz-setting-form-group>
                            <biz-setting-form-item label="推荐引导文案">
                                <biz-setting-form-item-tip tip="推荐人分享医生/项目链接给新客好友时，新客可看到此文案">
                                    <abc-textarea
                                        v-model="ruleFormData.referralMessaging"
                                        :disabled="isEnd && !isCopy"
                                        :maxlength="18"
                                        show-max-length-tips
                                        :input-custom-style="{
                                            background: '#fff'
                                        }"
                                        :height="76"
                                        placeholder="请根据营销需要，写一句引导患者点击的话"
                                        data-cy="referrer-messaging-textarea"
                                        @change="handleChange"
                                    ></abc-textarea>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>
                        </biz-setting-form-group>
                    </biz-setting-form>
                </div>

                <!-- 预览 -->
                <div class="setting-content__rule-preview" :style="{ 'height': `${calcContentHeight}px` }">
                    <div class="setting-content__preview-frame" :style="{ 'height': `${calcContentHeight}px` }">
                        <abc-flex justify="center">
                            <abc-text bold class="preview-title">
                                手机预览
                            </abc-text>
                        </abc-flex>
                        <img src="~assets/images/<EMAIL>" alt="" class="preview-img" />
                        <div class="phone-view">
                            <template v-if="activeStep">
                                <img class="preview-shell" :src="previewImg" alt="" />
                                <img
                                    :src="sharePreviewImg"
                                    class="referrer-preview"
                                    alt="close"
                                />
                                <abc-text
                                    class="referrer-text"
                                    :title="ruleFormData.referralMessaging"
                                    :class="{ 'is-h5': !isOpenMicroClinicWechat }"
                                    :size="!isOpenMicroClinicWechat ? 'mini' : 'tiny'"
                                >
                                    {{ ruleFormData.referralMessaging || '' }}
                                </abc-text>
                            </template>
                            <template v-else>
                                <img
                                    class="rule-preview-shell"
                                    :src="previewImg"
                                    alt=""
                                />
                                <iframe
                                    ref="previewFrame"
                                    frameborder="none"
                                    width="375px"
                                    wmode="transparent"
                                    :class="{
                                        'is-conflict': !isCopy && ruleFormData.conflicts && ruleFormData.conflicts.length
                                    }"
                                    class="decorate-preview__iframe"
                                    :src="previewUrl"
                                ></iframe>
                            </template>
                        </div>
                    </div>
                </div>
            </template>

            <template #footerPrepend>
                <abc-button
                    v-if="showDeleteBtn"
                    type="danger"
                    data-cy="referrer-delete-btn"
                    @click="handleDelete"
                >
                    删除
                </abc-button>
                <abc-button
                    v-if="showStopBtn"
                    type="danger"
                    data-cy="referrer-stop-btn"
                    @click="handleStop"
                >
                    终止
                </abc-button>
                <div style="flex: 1;"></div>
                <abc-button
                    v-if="showPreStepBtn"
                    type="blank"
                    data-cy="referrer-pre-step-btn"
                    @click="handleStepClick"
                >
                    上一步
                </abc-button>
                <abc-button
                    v-if="showNextStepBtn"
                    type="blank"
                    data-cy="referrer-next-step-btn"
                    @click="handleNextStepClick"
                >
                    下一步
                </abc-button>
                <abc-button
                    v-if="showSaveDraftBtn"
                    type="blank"
                    data-cy="referrer-save-draft-btn"
                    @click="handleSaveDraft"
                >
                    保存为草稿
                </abc-button>
                <abc-button v-if="showOpenActivityBth" data-cy="referrer-open-activity-btn" @click="handleStart">
                    开启活动
                </abc-button>
                <abc-button
                    v-if="showSaveBtn"
                    type="blank"
                    data-cy="referrer-save-btn"
                    @click="handleSave"
                >
                    保存
                </abc-button>
            </template>
        </abc-modal>
        <clinic-transfer
            v-if="showSelectClinicsTransfer"
            v-model="showSelectClinicsTransfer"
            :clinics="ruleFormData.clinicIdList"
            title="指定门店"
            :access-key="accessKey"
            @confirm="changeClinics"
        ></clinic-transfer>
        <!-- 添加患者 -->
        <selected-patient-dialog
            v-if="patientDialogVisible"
            v-model="patientDialogVisible"
            :selected-patients="selectedPatients"
            @confirm="onConfirmPatients"
        ></selected-patient-dialog>
        <selected-employee-dialog
            v-if="selectedEmployeeDialogVisible"
            v-model="selectedEmployeeDialogVisible"
            :selected-employee-list="selectedEmployeeList"
            :referrer-type="ruleFormData.referrerType"
            @change-employee="handleChangeEmployee"
        ></selected-employee-dialog>
        <selected-staff-dialog
            v-if="selectedStaffDialogVisible"
            v-model="selectedStaffDialogVisible"
            :employee-list="selectedEmployeeList"
            @select-staff="handleChangeEmployee"
        ></selected-staff-dialog>
        <selected-coupon-dialog
            v-if="visibleCouponDialog"
            v-model="visibleCouponDialog"
            :selected-coupon-list="couponList"
            :clinic-id-list="ruleFormData.clinicIdList"
            @selected-coupon-change="handleSelectedCouponChange"
        ></selected-coupon-dialog>
        <selected-coupon-dialog
            v-if="visibleReferrerCouponDialog"
            v-model="visibleReferrerCouponDialog"
            :selected-coupon-list="referrerCouponList"
            :clinic-id-list="ruleFormData.clinicIdList"
            @selected-coupon-change="handleSelectedReferrerCouponChange"
        ></selected-coupon-dialog>

        <!--选择例外的商品-->
        <select-goods-item-list
            v-if="showExceptGoodsListDialog"
            v-model="showExceptGoodsListDialog"
            from-referrer
            :is-expect="showExceptGoodsListDialog"
            :product-types="exceptGoodsType"
            :sub-type="exceptGoodsSubType"
            :c-m-spec="exceptGoodsCMSpec"
            :custom-type-id-list="exceptGoodsCustomTypeIdList"
            :select-goods-list="selectedExceptGoodsList"
            :registration-category="registrationCategory"
            @change="changeExceptGoodsList"
        ></select-goods-item-list>
        <!--选择例外的医生-->
        <select-doctors-dialog
            v-if="showExceptDoctorsListDialog"
            v-model="showExceptDoctorsListDialog"
            :goods-id="goodsId"
            :select-goods-list="selectedExceptGoodsList"
            @change="changeExceptGoodsList"
        >
        </select-doctors-dialog>
        <conflict-detail-dialog
            v-if="conflictDialogVisible"
            v-model="conflictDialogVisible"
            :conflicts="ruleFormData.conflicts"
        ></conflict-detail-dialog>
        <!-- 活动引导页面 -->
        <activity-guide-dialog
            v-if="activityGuideDialogVisible"
            v-model="activityGuideDialogVisible"
        ></activity-guide-dialog>

        <!-- 二次确认弹框 -->
        <confirm-activity-dialog
            v-if="activityConfirmDialogVisible"
            v-model="activityConfirmDialogVisible"
            :confirm-data="reviewData"
            :period-options="periodOptions"
            @confirm-start="submit(false, true)"
        ></confirm-activity-dialog>
    </div>
</template>
<script>
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import CreateDocSampleImg from 'src/assets/images/marketing/create-doc-sample.png';
    import ObtainCouponSampleImg from 'src/assets/images/marketing/obtain-coupon-sample.png';
    import ReferrerSampleDialog from 'src/assets/images/marketing/referrer-sample-dialog.png';
    import SelectedEmployeeDialog from './selected-employee-dialog.vue';
    import SelectedStaffDialog from './selected-staff-dialog.vue';
    import SelectDoctorsDialog from 'views/marketing/components/select-doctors-dlalog';
    import SelectedCouponDialog from 'views/marketing/referrer/form-dialog/selected-coupon-dialog.vue';
    import SelectedPatientDialog from 'views/marketing/referrer/form-dialog/selected-patient-dialog.vue';
    import ConfirmActivityDialog from 'views/marketing/referrer/form-dialog/confirm-activity-dialog.vue';

    import ActivityGuideDialog from 'views/marketing/referrer/form-dialog/activity-guide-dialog.vue';
    import ConflictDetailDialog from './conflict-detail-dialog.vue';
    import AbcAccess from '@/access/utils.js';
    import MarketingAPI from 'api/marketing';
    import ClinicAPI from 'api/clinic';
    import previewShellImg from 'src/assets/images/marketing/rule-preview-shell.png';

    import { isChainSubClinic } from 'views/common/clinic.js';
    import { getPreviewBaseUrl } from 'views/settings/micro-clinic/decoration/config';
    import { AbcPostMessage } from 'views/we-clinic/frames/decoration/components/decoration/post-message';
    import {
        PAGE_LOADED, PREVIEW_PROJECT_ID, REFRESH_DATA,
    } from 'views/settings/micro-clinic/decoration/constant';
    import { debounce } from 'utils/lodash';
    import { mapGetters } from 'vuex';
    import BizSettingForm from '@/components-composite/setting-form/src/views/index.vue';
    import BizSettingFormGroup from '@/components-composite/setting-form/src/views/group.vue';
    import BizSettingFormItem from '@/components-composite/setting-form/src/views/item.vue';
    import MarketingSelectCardItem from 'views/marketing/components/marketing-select-card-item.vue';
    import BizSettingFormItemTip from '@/components-composite/setting-form/src/views/tip.vue';
    import SelectGoodsTypeList from 'views/marketing/components/select-goods-type-list/select-goods-type-list.vue';
    import SelectReferrerRewardGoodsTable
        from 'views/marketing/components/select-goods-type-list/select-referrer-reward-goods-table.vue';
    import SelectGoodsItemList from 'views/marketing/components/select-goods-type-list/select-goods-item-list.vue';
    import usePaginationSerial from 'views/marketing/hooks/use-pagination-serial';
    import useDataOperationTracker, { OPT_TYPES } from 'views/marketing/hooks/use-data-operation-tracker';


    const ClinicTransfer = () => import('components/clinic-transfer/index.vue');
    import { ModalFunc as AbcModal } from '@abc/ui-pc';

    export default {
        components: {
            SelectGoodsItemList,
            SelectReferrerRewardGoodsTable,
            SelectGoodsTypeList,
            BizSettingFormItemTip,
            MarketingSelectCardItem,
            BizSettingFormItem,
            BizSettingFormGroup,
            BizSettingForm,
            ClinicTransfer,
            SelectedEmployeeDialog,
            SelectedStaffDialog,
            ConflictDetailDialog,
            SelectedCouponDialog,
            SelectDoctorsDialog,
            SelectedPatientDialog,
            ActivityGuideDialog,
            ConfirmActivityDialog,
        },
        props: {
            id: {
                type: String,
                default: '',
            },
            value: {
                type: Boolean,
                default: false,
            },
            isCopy: {
                type: Boolean,
                default: false,
            },
            status: {
                type: [Number, String],
                default: '',
            },
            selectGoodsType: {
                type: Array,
                default: () => {
                    return [ GoodsTypeEnum.MEDICINE,
                             GoodsTypeEnum.MATERIAL,
                             GoodsTypeEnum.EXAMINATION,
                             GoodsTypeEnum.TREATMENT,
                             GoodsTypeEnum.OTHER,
                             GoodsTypeEnum.GOODS,
                             GoodsTypeEnum.EYEGLASSES,
                             GoodsTypeEnum.COMPOSE,
                             GoodsTypeEnum.SURGERY];
                },
            },
        },
        setup() {
            // 初始化数据跟踪器
            const goodsTracker = useDataOperationTracker();
            return {
                goodsTracker,
            };
        },
        data() {
            return {
                showReferrerError: false,
                showReferrerGoodsError: false,
                showReferrerCouponError: false,
                showReferrerCouponRewardError: false,
                showClinicError: false,
                showDateError: false,
                previewUrl: '',
                accessKey: AbcAccess.accessMap.MARKET_REFERRER,
                dialogVisible: false,
                showSelectClinicsTransfer: false,
                patientDialogVisible: false,
                selectedEmployeeDialogVisible: false,
                selectedStaffDialogVisible: false,
                visibleCouponDialog: false,
                visibleReferrerCouponDialog: false,
                showExceptGoodsListDialog: false,
                showExceptDoctorsListDialog: false,
                conflictDialogVisible: false,
                exceptIndex: 0,
                exceptGoodsType: [],
                exceptGoodsSubType: '',
                exceptGoodsCMSpec: '',
                exceptGoodsCustomTypeIdList: [],
                selectedExceptGoodsList: [],
                selectedPatientIds: [],
                referrerCouponList: [],
                // 规则设置
                ruleFormData: {
                    name: '',
                    description: '',
                    isForever: 1,
                    beginDate: '',
                    endDate: '',
                    isAllClinic: 1,
                    isAllReferrer: 1,
                    referrerType: 0,
                    isRefundReferrerRewardAuto: 1,
                    isRewardNewPatient: 1,
                    isRewardReferrer: 1,
                    isRewardReferrerCoupon: 1,
                    rewardReferrerCouponPeriod: 1,
                    rewardReferrerCouponPeriodUnit: 2,
                    rewardReferrerPeriodUnit: 2,
                    rewardReferrerPeriod: 1,
                    awardGoodsList: [],
                    clinicIdList: [],
                    referrerIdList: [], // 推荐人列表
                    referralPatientRewards: [],
                    referrerPatientRewards: [],
                    referralGoods: [],
                    referralMessaging: '送份健康给你，点击预约可享受专属福利',
                    conflicts: [],
                },
                allRewardValue: '', // 批量设置提成比例
                couponList: [],
                // 分享设置
                activeStep: 0,
                activeReferrerType: 0,
                selectDateRange: [],
                pageSize: 5,
                pageIndex: 0,
                referrerMode: 'add',
                goodsId: '',
                sampleList: [
                    {
                        title: '推荐关系如何绑定',
                        desc: '新客通过推荐人分享链接预约医生或项目，完成建档后，自动建立绑定关系（或线下门店预约手动绑定），新客后续消费自动记录首诊推荐来源',
                        img: CreateDocSampleImg,
                    },
                    {
                        title: '推荐人奖励何时发放',
                        desc: `推荐人每成功推荐一位好友到店消费，奖励自动发放，推荐人可在【微${this.$app.institutionTypeWording}-我的-推荐有礼-我的推荐】查看推荐业绩`,
                        img: ReferrerSampleDialog,
                    },
                    {
                        title: '新客奖励何时发放',
                        desc: `新客通过推荐人分享链接完成建档后，立即发放奖励，一个新客只会获得一次奖励，新客可在【微${this.$app.institutionTypeWording} -我的-优惠券】查看奖励`,
                        img: ObtainCouponSampleImg,
                    },
                ],
                periodOptions: [
                    {
                        label: '年',
                        value: 3,
                    },
                    {
                        label: '月',
                        value: 2,
                    },
                    {
                        label: '天',
                        value: 1,
                    },
                ],
                referrerOptions: [
                    {
                        label: '客户',
                        value: 0,
                    },
                    {
                        label: '员工',
                        value: 1,
                    },
                ],
                selectedPatients: [],
                selectedEmployeeList: [],
                currentGoodsList: [],
                chainSubClinicList: [], // 连锁下子店列表
                pickerOptions: {
                    disabledDate: (date) => {
                        const isEndTime = new Date(2038, 0, 18).getTime();
                        return date < new Date(new Date().getTime() - 60 * 60 * 1000 * 24) || date.getTime() > isEndTime;
                    },
                    yearRange: {
                        end: 2038,
                    },
                },
                activeTab: 0,
                activityClinics: '',
                previewShellImg,
                activityGuideDialogVisible: false,
                activityConfirmDialogVisible: false,
                reviewData: null,

                registrationCategory: 0,
                calcContentHeight: 0,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters([
                'isOpenMicroClinicWechat',
            ]),
            ...mapGetters(['isSingleStore']),
            tabOptions() {
                if (this.id && !this.isCopy) {
                    return [
                        {
                            label: '规则设置',
                            value: 0,
                        },
                        {
                            label: '推荐引导设置',
                            value: 1,
                        },
                    ];
                }
                return [
                    {
                        label: '老带新活动',
                        value: 0,
                    },
                ];
            },

            previewImg() {
                return this.previewShellImg;
            },
            sharePreviewImg() {
                const {
                    h5ShareImg,
                    weappShareImg,
                    h5DefaultShareImg,
                    weappDefaultShareImg,
                } = this.viewDistributeConfig.Marketing.referrer || {};

                if (this.ruleFormData.referralMessaging) {
                    if (this.isOpenMicroClinicWechat) {
                        return weappShareImg;
                    }
                    return h5ShareImg;

                }

                if (this.isOpenMicroClinicWechat) {
                    return weappDefaultShareImg;
                }
                return h5DefaultShareImg;

            },
            isEditMode() {
                return !!this.id;
            },
            isEnd() {
                return this.status === 90;
            },
            isDraft() {
                return this.status === 0;
            },
            isNotStart() {
                return this.status === 11;
            },
            isProcessing() {
                return this.status === 12;
            },
            // 删除
            //     草稿-规则，草稿 -分享，已结束-规则， 已结束-分享
            //     草稿， 结束
            // 保存
            //     草稿-规则，草稿-分享，未开启-规则，未开启-分享，进行中-规则，进行中-分享
            // 开启活动
            //     草稿-规则，草稿-分享
            // 终止
            //     未开始-规则，未开始-分享，进行中-规则，进行中-分享，
            // 存为草稿
            //     新建-分享，复制-分享
            // 上一步
            //     新建-分享，复制-分享
            // 上一步
            //     新建-规则，复制-规则
            showDeleteBtn() {
                return this.isDraft || this.isEnd;
            },
            showSaveBtn() {
                return (this.isDraft || this.isNotStart || this.isProcessing) && !this.isCopy;
            },
            showOpenActivityBth() {
                return this.isDraft || ((this.isCopy || !this.id) && this.activeStep);
            },
            showStopBtn() {
                return (this.isNotStart || this.isProcessing) && !this.isCopy;
            },
            showSaveDraftBtn() {
                return (this.isCopy || !this.id) && this.activeStep ;
            },
            showPreStepBtn() {
                return (!this.id || this.isCopy) && this.activeStep;
            },
            showNextStepBtn() {
                return (!this.id || this.isCopy) && !this.activeStep;
            },
            formDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            showErrorTips() {
                return (this.showClinicError || this.showReferrerError || this.showReferrerGoodsError || this.showReferrerCouponError || this.showDateError || this.showReferrerCouponRewardError);
            },
            // 可选的门店 - 没有到期
            availableSubClinics() {
                return this.chainSubClinicList.filter((item) => AbcAccess.checkAvailableByEdition(this.accessKey, item.edition));
            },
            // 是否禁用选择全部门店
            disabledAllSubClinic() {
                return (
                    this.chainSubClinicList.length === 0 ||
                    this.chainSubClinicList.length !== this.availableSubClinics.length
                );
            },
            maxPage() {
                return Math.ceil(this.ruleFormData.referralGoods.length / this.pageSize);
            },
            currentPageData() {
                let startIndex = this.pageIndex * this.pageSize;
                let endIndex = startIndex + this.pageSize;
                const len =
                    (this.ruleFormData.referralGoods && this.ruleFormData.referralGoods.length) || 0;
                if (endIndex > len) {
                    endIndex = len;
                }
                if (startIndex < 0) {
                    startIndex = 0;
                }
                return this.ruleFormData.referralGoods.slice(startIndex, endIndex);
            },
            dialogTitle() {
                return !!this.id && !this.isCopy ? '编辑老带新活动' : '新建老带新活动';
            },
        },
        async mounted() {
            this.instance = new AbcPostMessage({
                receiveRoot: window,
                sendRoot: this.$refs.previewFrame.contentWindow,
                targetOrigin: '*',
            });

            this.instance.start();
            await this.fetchChainClinics();

            this._refreshPreview = debounce(this.refreshPreview, 500, true);
            this.instance.on(PAGE_LOADED, async () => {
                this._refreshPreview();
            });
            if (this.id) {
                await this.getReferrerActivityDetail();
            }
            this.previewUrl = `${getPreviewBaseUrl()}/referral/activity/detail/${PREVIEW_PROJECT_ID}`;
            this.$nextTick(() => {
                this.handleCalcContentHeight();
            });
        },
        beforeDestroy() {
            this.instance.destroy();
            this.instance = null;
            this.$emit('close');
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            handleDateTypeRange(type) {
                if (type) {
                    this.showDateError = false;
                }
                this.handleChange();
            },
            handleOpenClickDialog() {
                if (this.isEnd && !this.isCopy) return;
                this.showSelectClinicsTransfer = true;
            },
            getActivityClinics(list = []) {
                const clinicCount = list.length;

                if (clinicCount === 0) {
                    this.activityClinics = '';
                }

                const displayedOrgans = list.slice(0, 2).map((item) => item?.shortName?.slice(0,8) || item.name.slice(0,8)).join('、');
                this.activityClinics = clinicCount === 1 ?
                    `已选${displayedOrgans}门店` :
                    `已选${displayedOrgans}${clinicCount > 2 ? '...' : ''}等${clinicCount}家门店`;
            },
            async refreshPreview() {
                const postData = this.getPostData(this.isDraft, true);
                if (!this.ruleFormData?.name) {
                    postData.name = '未填写活动名称';
                }
                const { data } = await MarketingAPI.referrer.transPreview(postData);
                this.reviewData = data;
                this.reviewData.referralGoods = this.ruleFormData.referralGoods;
                this.instance.emit(REFRESH_DATA, this.reviewData);
            },

            handleGoodsListChange() {
                this.showReferrerGoodsError = !this.ruleFormData.referralGoods.length;
                this.handleChange();
            },

            handleChange() {
                this._refreshPreview();
            },
            handleRewardChange(val) {
                if (!val) {
                    this.showReferrerGoodsError = false;
                }
                this.handleChange();
            },
            handleReferrerRewardChange(val) {
                if (!val) {
                    this.showReferrerCouponRewardError = false;
                }
                this.handleChange();
            },
            handleCouponChange(val) {
                if (!val) {
                    this.showReferrerCouponError = false;
                }
                this.handleChange();
            },

            changeTab(tab) {
                this.activeStep = tab;
            },
            validatePeriod(value, callback) {
                if (!value && this.ruleFormData.isRewardReferrer) {
                    return callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
            },
            validateCouponPeriod(value, callback) {
                if (!value && this.ruleFormData.isRewardReferrerCoupon) {
                    return callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
            },
            handleStop() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    confirmText: '确认终止',
                    content: '活动终止后，不可重新开启？',
                    onConfirm: () => {
                        this.stopSubmit();
                    },
                });
            },
            async stopSubmit() {
                try {
                    const res = await MarketingAPI.referrer.stopActivity(this.id);
                    if (res) {
                        this.$Toast({
                            message: '终止成功',
                            type: 'success',
                        });
                        this.formDialogVisible = false;
                        this.$emit('refresh');
                    }
                } catch (err) {
                    console.log(err);
                }
            },
            handleDelete() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除该活动？',
                    onConfirm: () => {
                        this.deleteSubmit();
                    },
                });
            },

            async deleteSubmit() {
                const res = await MarketingAPI.referrer.delete(this.id);
                if (res?.data?.code === 200) {
                    this.$emit('refresh');
                    this.formDialogVisible = false;
                } else {
                    this.$Toast({
                        message: res?.message || '删除失败!',
                        type: 'error',
                    });
                }
            },
            async getReferrerActivityDetail() {
                try {
                    const { data: referrerInitData } = await MarketingAPI.referrer.getReferrerDetail(this.id);
                    this.getActivityClinics(referrerInitData.clinicList);
                    await this.fetchPageReferrerDataDetails(referrerInitData);
                } catch (err) {
                    console.log(err);
                }
            },
            async fetchPageReferrerDataDetails(initData) {
                const handlePageData = (pageData) => {
                    // 这里可以处理每一页的数据
                    this.initReferrerData({
                        ...initData,
                        referralGoods: pageData.rows ?? [],
                    });
                    // 如果需要，可以在这里执行其他操作
                };
                const {
                    run,
                } = usePaginationSerial(
                    async (params) => {
                        const pageData = await MarketingAPI.getReferralGoodsPageListDetail({
                            ...params, referralId: this.id,
                        });
                        // 每次请求完成后立即处理数据
                        handlePageData(pageData);
                        return pageData;
                    },
                    {
                        limit: 1000,
                    },
                );
                try {
                    await run();
                } catch (e) {
                    console.log(e);
                }
            },
            initReferrerData(data) {
                this.activeReferrerType = data.referrerType;
                this.selectedEmployeeList = data.referrerList || [];
                if (data.beginDate && data.endDate) {
                    this.selectDateRange = [data.beginDate, data.endDate];
                }
                // 给每一项 goods 添加 isOriginData 作为源数据标识
                data.referralGoods.forEach((item) => {
                    item.isOriginData = true;
                });

                if (this.isCopy) {
                    this.ruleFormData = {
                        ...data,
                        id: '',
                        name: '',
                        status: '',
                        referralGoods: [...(this.ruleFormData?.referralGoods || []), ...data.referralGoods],
                        clinicIdList: data.clinicList || [],
                        referrerIdList: data.referrerList || [],
                        referralPatientRewards: data.referralPatientRewards || [],
                        referrerPatientRewards: data.referrerPatientRewards || [],
                    };

                    this.couponList = data.referralPatientRewards || [];
                    this.referrerCouponList = data.referrerPatientRewards || [];

                } else {
                    this.ruleFormData = {
                        ...data,
                        referralGoods: [...this.ruleFormData.referralGoods, ...data.referralGoods],
                    };
                    this.ruleFormData.clinicIdList = data.clinicList || [];
                    this.ruleFormData.referrerIdList = data.referrerList || [];
                    this.couponList = data.referralPatientRewards || [];
                    this.referrerCouponList = data.referrerPatientRewards || [];
                }
            },
            handleBatchSettingAward() {
                this.ruleFormData.referralGoods = this.ruleFormData.referralGoods.map((item) => {
                    const obj = {
                        ...item,
                        rewardValue: this.allRewardValue,
                    };
                    this.handleOptTracker(obj, OPT_TYPES.UPDATE);
                    return obj;
                });
                this.handleChange();
            },
            async fetchChainClinics() {
                try {
                    const { data } = await ClinicAPI.chainClinicV3();
                    this.chainSubClinicList = (data.rows || []).filter((item) => isChainSubClinic(item));
                } catch (error) {
                    console.log('fetchChainClinics error', error);
                }
            },
            openExceptGoodsListDialog(item) {
                this.exceptGoodsSubType = item.goodsSubType;
                this.exceptGoodsCMSpec = item.goodsCMSpec;
                this.exceptGoodsType = [ item.goodsType ];
                this.exceptGoodsCustomTypeIdList = item.customTypeId ? [ item.customTypeId ] : [];
                this.exceptIndex = this.ruleFormData.referralGoods.findIndex((it) => it.goodsIdKey === item.goodsIdKey && it.name === item.name);
                if ([GoodsTypeIdEnum.REGISTRATION].includes(item.goodsType)) {
                    this.goodsId = item.goodsId;
                    if (item.exceptInfo?.employeeItems?.length) {
                        this.selectedExceptGoodsList = item.exceptInfo?.employeeItems?.map((item) => {
                            return {
                                ...item,
                            };
                        });
                    } else {
                        this.selectedExceptGoodsList = [];
                    }

                    if (item.goodsSubType === 0) {
                        this.registrationCategory = 0;
                    } else if (item.goodsSubType === 3) {
                        this.registrationCategory = 2;
                    } else if (item.goodsSubType === 2) {
                        this.registrationCategory = 1;
                    } else {
                        this.registrationCategory = item.goodsSubType;
                    }

                    this.showExceptDoctorsListDialog = true;
                    return;
                }
                if (item.exceptInfo?.goodsItems?.length) {
                    this.selectedExceptGoodsList = item.exceptInfo?.goodsItems?.map((item) => {
                        return {
                            ...item,
                        };
                    });
                } else {
                    this.selectedExceptGoodsList = [];
                }
                this.showExceptGoodsListDialog = true;
            },
            getExceptInfoNumber(item) {
                if ([GoodsTypeIdEnum.REGISTRATION].includes(item.goodsType)) {
                    return item.exceptInfo?.employeeItems?.length || 0;
                }
                return item.exceptInfo?.goodsItems?.length || 0;
            },
            changeExceptGoodsList(list) {
                if (!list) {
                    this.ruleFormData.referralGoods[this.exceptIndex].exceptInfo = null;
                    return;
                }
                const {
                    goodsType,
                } = this.ruleFormData.referralGoods[this.exceptIndex];
                // 挂号需要特殊处理为人
                if ([GoodsTypeIdEnum.REGISTRATION].includes(goodsType)) {
                    this.$set(this.ruleFormData.referralGoods[this.exceptIndex], 'exceptInfo', {
                        goodsItems: [],
                        employeeItems: list.map((item) => {
                            return {
                                ...item,
                            };
                        }),
                        type: 1,
                    });
                } else {
                    // 获取当前商品项
                    const item = this.ruleFormData.referralGoods[this.exceptIndex];
                    
                    // 获取当前的商品例外项
                    const currentGoodsItems = item.exceptInfo?.goodsItems ?? [];
                        
                    // 找出新增项，设置 optType 为 ADD
                    const addedItems = list.filter((newItem) =>
                        !currentGoodsItems.some((currentItem) => currentItem.goodsId === newItem.goodsId),
                    ).map((newItem) => ({
                        ...newItem,
                        optType: OPT_TYPES.ADD,
                        id: null,
                    }));
                        
                    // 找出被删除的项，设置 optType 为 DELETE
                    const deletedItems = currentGoodsItems.filter((currentItem) =>
                        !list.some((newItem) => newItem.goodsId === currentItem.goodsId),
                    ).map((_item) => ({
                        ..._item,
                        optType: OPT_TYPES.DELETE,
                    }));
                        
                    // 合并结果，只包含新增项和删除项
                    const finalList = [...addedItems, ...deletedItems];

                    // 如果有变更项，触发处理事件
                    if (finalList.length > 0) {
                        this.handleOptTracker(this.ruleFormData.referralGoods[this.exceptIndex], OPT_TYPES.UPDATE);
                    }
                    
                    // 保留原有设置例外项的逻辑
                    this.$set(this.ruleFormData.referralGoods[this.exceptIndex], 'exceptInfo', {
                        goodsItems: list.map((listItem) => {
                            return {
                                ...listItem,
                            };
                        }),
                        employeeItems: [],
                        type: 0,
                    });
                }
                this.handleChange();
            },
            changePageHandler(pageIndex) {
                this.pageIndex = pageIndex;
            },
            deleteItem(index) {
                this.handleOptTracker(this.ruleFormData.referralGoods[this.pageIndex * this.pageSize + index],OPT_TYPES.DELETE);
                this.ruleFormData.referralGoods.splice(
                    this.pageIndex * this.pageSize + index,
                    1,
                );
                if (this.currentPageData.length === 0 && this.pageIndex > 0) {
                    this.pageIndex--;
                }
                this.handleChange();
            },
            changeGoodsList(list = []) {
                this.showReferrerGoodsError = !list.length;
                const newList = list.reverse();
                this.ruleFormData.referralGoods = newList.map((item) => {
                    return {
                        ...item,
                        // 员工是提成，客户就积分
                        rewardType: this.activeReferrerType ? 1 : 0,
                        rewardValue: item?.rewardValue || '',
                        showExceptItems: item.type === 1 ? true : false,
                    };
                });
                this.$nextTick(() => {
                    this.$refs.rewardInputRef?.[0].focus();
                });
                this.pageIndex = 0;
                this.handleChange();
            },
            async obtainCoupons() {
                const data = await MarketingAPI.referrer.toObtainCoupons({
                    applyClinicIdList: this.ruleFormData.clinicIdList?.map((item) => item.id),
                });
                if (!data?.length) {
                    AbcModal.alert({
                        type: 'warn',
                        title: '提示',
                        content: `暂无可发放的优惠券，请前往${this.isSingleStore ? '「营销」' : '「总部-营销」'}添加`,
                    });
                    return false;
                }
                return true;
            },
            async addCouponBtnClick() {
                this.visibleCouponDialog = await this.obtainCoupons();
            },
            async addReferrerCouponBtnClick() {
                this.visibleReferrerCouponDialog = await this.obtainCoupons();
            },
            handleSelectedCouponChange(list = []) {
                this.showReferrerCouponError = !list.length;
                this.couponList = list;
                this.ruleFormData.referralPatientRewards = list.map((item) => {
                    return {
                        ...item,
                        // 优惠券是1
                        rewardType: 1,
                    };
                });
            },
            handleSelectedReferrerCouponChange(list = []) {
                this.showReferrerCouponRewardError = !list.length;
                this.referrerCouponList = list;
                this.ruleFormData.referrerPatientRewards = list.map((item) => {
                    return {
                        ...item,
                        // 优惠券是1
                        rewardType: 1,
                    };
                });
            },
            handleReferrerTypeChange(type) {
                if (type) {
                    this.showReferrerError = false;
                }
                this.ruleFormData.isAllReferrer = type;
                this.allRewardCostThreshold = '';
                this.allRewardValue = '';
                this.handleChange();
            },
            handleClinicChange(type) {
                if (type) {
                    this.ruleFormData.clinicIdList = [];
                    this.showClinicError = false;
                }
                this.handleChange();
            },
            handleChangeEmployee(list = []) {
                this.ruleFormData.referrerIdList = list;
                if (!this.ruleFormData.isAllReferrer && list.length) {
                    this.showReferrerError = false;
                }
                this.selectedEmployeeList = list;
            },
            onConfirmPatients(selectedPatients = []) {
                this.patientDialogVisible = false;
                if (!this.ruleFormData.isAllReferrer && selectedPatients.length) {
                    this.showReferrerError = false;
                }
                this.updateSelectPatients(selectedPatients);
            },
            updateSelectPatients(selectedPatients = []) {
                this.selectedPatients = selectedPatients;
                this.selectedEmployeeList = selectedPatients;
                this.ruleFormData.referrerIdList = selectedPatients;
                this.handleChange();
            },

            handleOpenReferrerDialog(mode) {
                if (this.isEnd && !this.isCopy) return;
                this.referrerMode = mode;
                if (+this.ruleFormData.referrerType) {
                    // 员工
                    this.selectedStaffDialogVisible = true;
                } else {
                    // 客户
                    this.patientDialogVisible = true;
                    this.updateSelectPatients(this.selectedEmployeeList);
                }
            },
            handleChangeReferrerTab(item) {
                if (item.value) {
                    this.referrerCouponList = [];
                    this.ruleFormData.referrerPatientRewards = [];
                    this.ruleFormData.isRewardReferrerCoupon = 0;
                    this.ruleFormData.rewardReferrerCouponPeriod = '';
                    this.ruleFormData.rewardReferrerCouponPeriodUnit = '';
                }
                if (!this.isDraft && !this.isCopy && this.id) return;
                this.ruleFormData.referrerType = item.value;
                if (item.value === this.activeReferrerType) return;
                this.activeReferrerType = item.value;
                this.selectedEmployeeList = [];
                this.ruleFormData.referrerIdList = [];
                this.ruleFormData.referralGoods = this.ruleFormData?.referralGoods?.map((it) => {
                    return {
                        ...it,
                    };
                });
                this.handleChange();
                this.$router.push({
                    name: 'referrer',
                    query: {
                        tab: item.value,
                    },
                });

            },
            changeClinics(list = []) {
                this.ruleFormData.clinicIdList = list;
                this.getActivityClinics(list);
                if (!this.ruleFormData.isAllClinic && list.length) {
                    this.showClinicError = false;
                }
                this.couponList = [];
                this.referrerCouponList = [];
                this.ruleFormData.referralPatientRewards = [];
                this.ruleFormData.referrerPatientRewards = [];
                this.handleChange();
            },
            findErrorItemIndex() {
                const list = this.ruleFormData.referralGoods || [];
                return list?.findIndex((item) => {
                    return !item.rewardValue;
                });
            },
            handleValidateGoodsTable() {
                const errorIndex = this.findErrorItemIndex();
                if (errorIndex > -1 && this.$refs.referrerRewardGoodsTableRef) {
                    const pageIndex = Math.ceil((errorIndex + 1) / this.$refs?.referrerRewardGoodsTableRef?.pageParams?.pageSize);
                    if (pageIndex) {
                        this.$refs.referrerRewardGoodsTableRef?.changePageHandler(pageIndex);
                    }
                    this.$nextTick(() => {
                        this.$refs.ruleFormRef.validate();
                    });
                    return false;
                }
                return true;
            },
            preSaveHandler() {
                let flag = false;
                if (!this.ruleFormData.isAllClinic && !this.ruleFormData?.clinicIdList?.length) {
                    this.showClinicError = true;
                    flag = false;
                }
                if (!this.ruleFormData.isForever && !this.selectDateRange?.length) {
                    this.showDateError = true;
                    flag = false;
                }
                if (!this.ruleFormData.isAllReferrer && !this.ruleFormData?.referrerIdList?.length) {
                    this.showReferrerError = true;
                    flag = false;
                }
                if (this.ruleFormData.isRewardReferrer && !this.ruleFormData.referralGoods?.length) {
                    this.showReferrerGoodsError = true;
                    flag = false;
                }
                if (this.ruleFormData.isRewardNewPatient && !this.couponList?.length) {
                    this.showReferrerCouponError = true;
                    flag = false;
                }
                if (this.ruleFormData.isRewardReferrerCoupon && !this.referrerCouponList?.length && !this.activeReferrerType) {
                    this.showReferrerCouponRewardError = true;

                    flag = false;
                }

                return flag;
            },
            handleSaveDraft() {
                this.preSaveHandler();
                this.$refs.ruleFormRef.validate(async (valid) => {
                    if (valid && !this.showErrorTips) {
                        this.submit(true);
                    }
                });
            },
            handleStart() {
                this.preSaveHandler();
                const validGoodsTableRes = this.handleValidateGoodsTable();
                if (!validGoodsTableRes) return;
                this.$refs.ruleFormRef.validate(async (valid) => {
                    if (valid && !this.showErrorTips) {
                        this.activityConfirmDialogVisible = true;
                    }
                });
            },
            handleStepClick() {
                this.activeStep = this.activeStep === 0 ? 1 : 0;
            },
            handleNextStepClick() {
                this.preSaveHandler();
                const validGoodsTableRes = this.handleValidateGoodsTable();
                if (!validGoodsTableRes) return;
                this.$refs.ruleFormRef.validate((valid) => {
                    if (valid && !this.showErrorTips) {
                        this.activeStep = this.activeStep === 0 ? 1 : 0;
                    }
                });
            },

            getPostData(isDraft, preview = false) {
                const params = {
                    ...this.ruleFormData,
                };
                if (isDraft) {
                    params.status = 0;
                } else {
                    params.status = 10;
                }

                if (this.ruleFormData.isAllReferrer && !preview) {
                    this.ruleFormData.referrerIdList = [];
                    this.selectedEmployeeList = [];
                }
                params.clinicIdList = params.clinicIdList?.map((item) => item.id);
                params.referrerIdList = params.referrerIdList?.map((item) => item.id).filter((it) => it);
                if (!this.isCopy) {
                    params.referralGoods = (this.goodsTracker.operationData.value || [])?.map((item) => {
                        // 从 ruleFormData 中获取对应的 exceptInfo
                        const matchedItem = this.ruleFormData.referralGoods.find((goods) => goods.id === item.id || goods.goodsId === item.goodsId);
                        const exceptInfo = matchedItem ? matchedItem.exceptInfo : item.exceptInfo;
                        return {
                            ...item,
                            exceptInfo,
                            rewardType: this.activeReferrerType ? 1 : 0,
                        };
                    });
                } else {
                    // 如果是复制操作，直接丢全量数据
                    params.referralGoods = params.referralGoods?.map((item) => {
                        return {
                            ...item,
                            optType: OPT_TYPES.ADD,
                            rewardType: this.activeReferrerType ? 1 : 0,
                        };
                    });
                }

                if (this.id) {
                    delete params.clinicList;
                    delete params.referrerList;
                }
                return params;
            },

            async submit(isDraft, isStart = false) {
                try {
                    const postData = this.getPostData(isDraft);
                    const res = !this.id || this.isCopy ?
                        await MarketingAPI.referrer.createReferrerActivity(postData) :
                        await MarketingAPI.referrer.updateReferrer(this.id, postData);

                    if (res) {
                        this.$emit('refresh');
                        if (isStart) {
                            this.$emit('open-activity-spread', {
                                id: res.data?.id,
                                name: this.ruleFormData.name,
                            });
                        }
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                        this.formDialogVisible = false;
                    }

                } catch (err) {
                    console.log(err);
                }
            },
            handleSave() {
                this.preSaveHandler();
                const validGoodsTableRes = this.handleValidateGoodsTable();
                if (!validGoodsTableRes) return;
                this.$refs.ruleFormRef.validate(async (valid) => {
                    if (valid && !this.showErrorTips) {
                        this.submit(this.isDraft);
                    }
                });
            },
            handleDateChange(list = []) {
                if (list.length) {
                    this.showDateError = false;
                }
                if (list) {
                    this.ruleFormData.beginDate = list[0];
                    this.ruleFormData.endDate = list[1];
                } else {
                    this.ruleFormData.beginDate = '';
                    this.ruleFormData.endDate = '';
                }
            },
            handleCalcContentHeight() {
                this.calcContentHeight = this.$refs.settingLeft.clientHeight;
            },
            handleOptTracker(goodsItem,optType) {
                if (!goodsItem) return;
                if (optType === OPT_TYPES.DELETE) {
                    this.goodsTracker.saveDeleteItem(goodsItem);
                }
                if (optType === OPT_TYPES.ADD) {
                    this.goodsTracker.saveAddItem(goodsItem);
                }
                if (optType === OPT_TYPES.UPDATE) {
                    this.goodsTracker.saveUpdateItem(goodsItem);
                }
            },
        },
    };
</script>
<style lang="scss">
@import 'src/styles/theme.scss';
@import 'src/styles/mixin.scss';

.marketing-referrer-activity-dialog {
    .abc-dialog-body {
        overflow-y: hidden !important;
    }

    .abc-scrollbar-container {
        position: relative;
        height: calc(100% - 109px);
        padding: 0;
        overflow-y: auto;
    }

    .setting-left {
        width: 741px;
        padding: 24px;
        overflow-y: auto;

        @include scrollBar;
    }

    .setting-content__rule-preview {
        position: fixed;
        top: 109px;
        right: 0;
        width: 460px;
        height: 100%;

        .setting-content__preview-frame {
            position: relative;
            width: 460px;
            overflow-y: auto;
            background-color: #f5f7fb;

            @include scrollBar;

            .preview-img {
                position: absolute;
                top: 0;
                left: 0;
                width: 48px;
                height: 48px;
            }

            .preview-title {
                margin: 20px 0 28px 0;
            }

            .phone-view {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .preview-shell,
                .rule-preview-shell {
                    position: relative;
                    top: -30px;
                    z-index: 0;
                    width: 300px;
                    height: 617px;
                    overflow: hidden;
                    transform: scale(0.9);
                }

                .referrer-preview {
                    position: absolute;
                    top: 79px;
                    left: 50%;
                    z-index: 0;
                    width: 246px;
                    height: 535px;
                    overflow: hidden;
                    border-radius: 40px;
                    transform: translateX(-50%);
                }

                .referrer-text {
                    position: absolute;
                    top: 424px;
                    left: 206px;
                    width: 160px;
                    overflow: hidden;
                    font-weight: bold;
                    color: #ffffff;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                    &.is-h5 {
                        top: 220px;
                        left: 159px;
                        width: 144px;
                        font-weight: normal;
                        color: black;
                        text-overflow: initial;
                        white-space: inherit;
                    }
                }

                .decorate-preview__iframe {
                    position: absolute;
                    top: -58px;
                    left: 50%;
                    z-index: 0;
                    height: 809px;
                    border-radius: 50px;
                    transform: translateX(-50%) scale(0.66);
                }
            }
        }
    }
}
</style>
