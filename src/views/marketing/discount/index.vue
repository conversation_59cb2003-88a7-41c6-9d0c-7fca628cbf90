<template>
    <abc-layout style="height: 100%;">
        <abc-layout-header style="padding: 0;">
            <abc-tabs-v2
                :option="tabsOption"
                size="huge"
                style="padding: 0 24px 0 8px;"
                @change="handleTabsChange"
            ></abc-tabs-v2>
            <abc-button
                v-if="showDiscountSettingBtn"
                icon="s-b-settings-line"
                variant="text"
                size="small"
                style="position: absolute; top: 16px; right: 16px;"
                @click="showNoDiscountView"
            >
                不参与折扣项目({{ noDiscountGoodsListLengthCount }})
            </abc-button>
        </abc-layout-header>
        <router-view></router-view>
    </abc-layout>
</template>

<script type="text/ecmascript-6">
    import useMarketingDiscountSetting from 'views/marketing/hooks/discountHook';
    const tabsOption = [
        {
            label: '折扣活动', value: 'discount-home',
        },
        {
            label: '服务介绍', value: 'discount-introduce',
        },
    ];
    export default {
        setup() {
            const {
                setMarketingDiscountSetting,
                noDiscountGoodsListLengthCount,
            } = useMarketingDiscountSetting();
            return {
                setMarketingDiscountSetting,
                noDiscountGoodsListLengthCount,
            };
        },
        data() {
            return {
                tabsOption,
                showDiscountSettingBtn: true,
            };
        },
        methods: {
            handleTabsChange(index, item) {
                this.showDiscountSettingBtn = item?.value === 'discount-home';
                this.$router.push({
                    name: item.value,
                });
            },
            showNoDiscountView() {
                this.setMarketingDiscountSetting(true);
            },
        },
    };
</script>
