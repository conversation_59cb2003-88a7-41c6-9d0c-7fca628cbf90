<template>
    <biz-fill-remain-height class="main-content" style="height: 100%; padding: 0;">
        <template #header>
            <abc-tabs-v2
                :option="tabsOption"
                size="huge"
                style="padding: 0 24px 0 8px;"
                :disable-indicator="tabsOption.length <= 1"
                @change="handleTabsChange"
            ></abc-tabs-v2>
        </template>

        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script type="text/ecmascript-6">
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    const tabsOption = [
        {
            label: '积分', value: 'integral-basic',
        },
    ];
    export default {
        components: { BizFillRemainHeight },
        data() {
            return {
                tabsOption,
            };
        },
        methods: {
            handleTabsChange(index, item) {
                this.$router.push({
                    name: item.value,
                });
            },
        },
    };
</script>
