<template>
    <biz-setting-layout class="field-layout-patient-card-wrapper">
        <biz-setting-content>
            <abc-layout preset="setting-table" style="padding: 0;">
                <abc-layout-header>
                    <setting-second-tab
                        v-model="currentType"
                        :options="OPTIONS"
                        :style="{
                            width: '248px',
                        }"
                        @change="onChangeType"
                    ></setting-second-tab>
                </abc-layout-header>

                <abc-layout-content>
                    <abc-table
                        v-if="currentType === OPTION_ENUM.CREATE_PATIENT_CARD"
                        :render-config="renderConfig"
                        :data-list.sync="patientDataList"
                        :loading="loading"
                        style="max-width: 768px;"
                    ></abc-table>

                    <abc-table
                        v-else
                        :render-config="renderConfig"
                        :data-list.sync="familyPatientDataList"
                        :loading="loading"
                        style="max-width: 768px;"
                    ></abc-table>
                </abc-layout-content>
            </abc-layout>

            <template #footer>
                <biz-setting-footer>
                    <abc-flex>
                        <abc-button
                            :disabled="!isUpdate"
                            :loading="confirmLoading"
                            @click="onSave"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            :loading="resetLoading"
                            @click="onReset"
                        >
                            恢复默认
                        </abc-button>
                    </abc-flex>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar style="position: relative;">
            <preview-layout>
                <template #previewTab>
                    <div class="preview-tab-item preview-tab-item__active">
                        {{ previewTitle }}
                    </div>
                </template>

                <template #previewHtml>
                    <iframe
                        ref="previewFrame"
                        frameborder="none"
                        :style="{
                            transformOrigin: 'top',
                            height: '100%',
                            width: '375px'
                        }"
                        wmode="transparent"
                        :src="previewUrl"
                        class="decorate-preview__iframe"
                    ></iframe>
                </template>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import { mapGetters } from 'vuex';
    import PropertyAPI from 'api/property';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import WeappPatientCardTable from './table';
    import PreviewLayout from 'views/settings/print-config/components/preview-layout.vue';
    import { getPreviewBaseUrl } from 'views/settings/micro-clinic/decoration/config';
    import {
        OPTIONS, OPTION_ENUM, PATIENT_CARD_FILED, FAMILY_PATIENT_CARD_FILED,
    } from 'views/settings/field-layout/weapp-patient-card/constant';
    import { AbcPostMessage } from 'views/we-clinic/frames/decoration/components/decoration/post-message';
    import {
        REFRESH_DATA, PAGE_LOADED,
    } from 'views/settings/micro-clinic/decoration/constant';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingSidebar,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index';
    import SettingSecondTab from 'views/settings/components/setting-second-tab/index.vue';

    export default {
        components: {
            PreviewLayout,
            BizSettingLayout,
            BizSettingContent,
            BizSettingSidebar,
            BizSettingFooter,
            SettingSecondTab,
        },
        beforeRouteLeave(to, from, next) {
            if (this.isUpdate) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                loading: false,

                confirmLoading: false,
                resetLoading: false,

                previewUrl: '',
                OPTIONS,
                currentType: OPTION_ENUM.CREATE_PATIENT_CARD,
                OPTION_ENUM,

                patientDataList: [],
                familyPatientDataList: [],

                cachePatientDataList: [],
                cacheFamilyPatientDataList: [],
            };
        },

        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            renderConfig() {
                const that = this;

                return {
                    ...WeappPatientCardTable.staticFieldConfig,
                    list: WeappPatientCardTable.staticFieldConfig.list.map((config) => {
                        if (config.key === 'key') {
                            config.dataFormatter = (val) => {
                                return that.currentType === OPTION_ENUM.CREATE_PATIENT_CARD ?
                                    PATIENT_CARD_FILED[val] :
                                    FAMILY_PATIENT_CARD_FILED[val];
                            };
                        }

                        if (config.key === 'required') {
                            config.customRender = (h, item) => {
                                return (
                                    <abc-flex
                                        class="table-cell"
                                        justify="flex-start"
                                    >
                                        <abc-switch
                                            type={'number'}
                                            value={item.required}
                                            disabled={!!item.unmodifiable}
                                            onInput={(val) => {item.required = val;}}
                                        ></abc-switch>
                                    </abc-flex>
                                );
                            };
                        }
                        return config;
                    }),
                };
            },

            previewTitle() {
                if (this.currentType === OPTION_ENUM.CREATE_PATIENT_CARD) {
                    return '创建就诊卡';
                }

                return '创建家庭成员';
            },

            patientIsUpdate() {
                return !isEqual(this.cachePatientDataList, this.patientDataList);
            },

            familyPatientIsUpdate() {
                return !isEqual(this.cacheFamilyPatientDataList, this.familyPatientDataList);
            },

            isUpdate() {
                return this.patientIsUpdate || this.familyPatientIsUpdate;
            },
        },

        watch: {
            patientDataList: {
                handler() {
                    this._refreshPreview();
                },
                deep: true,
            },

            familyPatientDataList: {
                handler() {
                    this._refreshPreview();
                },
                deep: true,
            },
        },

        async mounted() {
            await this.loadData();
            this.instance = new AbcPostMessage({
                receiveRoot: window,
                sendRoot: this.$refs.previewFrame.contentWindow,
                targetOrigin: '*',
            });

            this.instance.start();

            this.instance.on(PAGE_LOADED, async () => {
                console.log('page loaded');
                this._refreshPreview();
            });


            this.previewUrl = `${getPreviewBaseUrl()}/create-patient`;
        },

        beforeDestroy() {
            this.instance.destroy();
            this.instance = null;
        },

        methods: {
            _refreshPreview() {
                const payload = {
                    createPatientCardRequiredKey: this.patientDataList.filter((o) => o.required).map((o) => o.key),
                    createFamilyPatientCardRequiredKey: this.familyPatientDataList.filter((o) => o.required).map((o) => o.key),
                };


                this.instance && this.instance.emit(REFRESH_DATA, payload);
            },

            onChangeType(val) {
                if (val === OPTION_ENUM.CREATE_PATIENT_CARD) {
                    this.previewUrl = `${getPreviewBaseUrl()}/create-patient`;
                } else {
                    this.previewUrl = `${getPreviewBaseUrl()}/add-family-member`;
                }
            },

            async loadData() {
                try {
                    this.loading = true;
                    const { data } = await PropertyAPI.getV3('field', 'chain');
                    const {
                        weappPatientCard, weappFamilyPatientCard,
                    } = data || {};

                    this.patientDataList = this.handleData(weappPatientCard?.create);
                    this.familyPatientDataList = this.handleData(weappFamilyPatientCard?.create);

                    this.cachePatientDataList = Clone(this.patientDataList);
                    this.cacheFamilyPatientDataList = Clone(this.familyPatientDataList);
                } catch (e) {
                    console.error(e.message);
                } finally {
                    this.loading = false;
                }
            },

            handleData(data) {
                if (!data) return [];

                return Object.keys(data).map((key) => {
                    return {
                        key,
                        ...data[key],
                        unmodifiable: data[key].unmodifiable || 0,
                    };
                }).sort((a, b) => a.sort - b.sort);
            },

            handlePostData(data) {
                return data.reduce((acc, cur) => {
                    acc[cur.key] = {
                        unmodifiable: cur.unmodifiable,
                        sort: cur.sort,
                        required: cur.required,
                    };

                    return acc;
                }, {});
            },

            async onSave() {
                try {
                    this.confirmLoading = true;

                    const promises = [];
                    if (this.patientIsUpdate) {
                        promises.push(PropertyAPI.updateV3('field.weappPatientCard.create', 'chain', this.handlePostData(this.patientDataList)));
                    }
                    if (this.familyPatientIsUpdate) {
                        promises.push(PropertyAPI.updateV3('field.weappFamilyPatientCard.create', 'chain', this.handlePostData(this.familyPatientDataList)));
                    }
                    await Promise.all(promises),

                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });

                    await this.loadData();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.confirmLoading = false;
                }
            },

            async onReset() {
                try {
                    this.resetLoading = true;

                    const { data } = await PropertyAPI.getV3('field', 'chain/default');
                    const {
                        weappPatientCard, weappFamilyPatientCard,
                    } = data || {};

                    this.patientDataList = this.handleData(weappPatientCard?.create);
                    this.familyPatientDataList = this.handleData(weappFamilyPatientCard?.create);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.resetLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/abc-common.scss';

.field-layout-patient-card-wrapper {
    position: relative;

    .abc-layout-wrapper {
        padding: 24px;
        transform: translate(0, 0);
    }

    .print-preview-layout {
        display: flex;
        flex-direction: column;
        overflow-y: hidden;
    }

    .preview-html-wrapper {
        width: 375px;
        height: 640px;
        padding: 0;
        margin: 0 auto;
        background: var(--abc-color-P5);
        border: transparent;
        border-radius: var(--abc-border-radius-small);

        .preview-html {
            display: flex;
            justify-content: center;
            width: 100%;
            height: 100%;
            transform: unset;
        }
    }
}
</style>
