<template>
    <biz-setting-layout
        class="field-layout-patient-filing-wrapper"
        :class=" {
            'field-layout-patient-filing-wrapper-pharmacy': isPharmacy,
        }"
    >
        <biz-setting-content>
            <abc-layout class="abc-layout-wrapper-model" preset="setting-table" style="padding: 0;">
                <abc-layout-content>
                    <abc-table
                        :render-config="renderConfig"
                        :data-list.sync="dataList"
                        :loading="loading"
                        style="max-width: 768px;"
                    >
                        <template #key="{ trData: item }">
                            <abc-table-cell v-if="item.key === 'certificates'">
                                <abc-flex :gap="8">
                                    <abc-text theme="black">
                                        证件
                                    </abc-text>
                                    <abc-text theme="gray">
                                        身份证/外籍护照/台胞证等
                                    </abc-text>
                                </abc-flex>
                            </abc-table-cell>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>

            <template #footer>
                <biz-setting-footer>
                    <abc-flex>
                        <abc-button
                            :disabled="!isUpdate"
                            :loading="confirmLoading"
                            @click="onSave"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            :loading="resetLoading"
                            @click="onReset"
                        >
                            恢复默认
                        </abc-button>
                    </abc-flex>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar style="position: relative;">
            <preview-layout>
                <template #previewTab>
                    <div class="preview-tab-item preview-tab-item__active">
                        {{ isPharmacy ? '新建会员' : '新建患者' }}
                    </div>
                </template>
                <template #previewHtml>
                    <div class="field-layout-dialog-info-patient-wrapper">
                        <dialog-info-patient
                            v-if="showDialogInfoPatient "
                            v-model="showDialogInfoPatient"
                            :class="[
                                'field-layout-dialog-info-patient',
                                {
                                    'field-layout-dialog-info-patient-hospital': isHospital,
                                    'field-layout-dialog-info-patient-distribute': isDentistry,
                                    'field-layout-dialog-info-patient-pharmacy': isPharmacy,
                                }
                            ]"
                            :show-close="false"
                            :show-dialog-cover="false"
                            :auto-focus="false"
                            :config-list="dataList"
                            readonly
                            :append-to-body="false"
                            :need-fetch-member-types="false"
                            :is-field-layout-patient-create="true"
                        ></dialog-info-patient>
                    </div>
                </template>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import PropertyAPI from 'api/property';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import Logger from 'utils/logger';
    import patientKeyTable from './table';
    import PreviewLayout from 'views/settings/print-config/components/preview-layout.vue';
    import DialogInfoPatient from 'views/crm/common/package-info/dialog-info-patient.vue';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingSidebar,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index';


    export default {
        components: {
            PreviewLayout,
            DialogInfoPatient,
            BizSettingLayout,
            BizSettingContent,
            BizSettingSidebar,
            BizSettingFooter,
        },
        beforeRouteLeave(to, from, next) {
            if (this.isUpdate) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        props: {},
        data() {
            return {
                configList: [],
                loading: false,
                isUpdate: false,
                confirmLoading: false,
                resetLoading: false,
                showDialogInfoPatient: true,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            ...mapGetters('crm', [
                'crmConfigList',
            ]),
            ...mapGetters(['isHospital', 'isDentistry', 'isPharmacy']),
            renderConfig() {
                return patientKeyTable.getRenderConfig();
            },
            dataList() {
                return this.configList || [];
            },
            switchSetting() {
                const keys = this.dataList.map((item) => item.key);
                const setting = {};
                keys.forEach((key) => {
                    setting[key] = 1;
                });
                return setting;
            },
        },
        watch: {
            configList: {
                handler() {
                    this.isUpdate = !isEqual(this._configList, this.configList);
                },
                deep: true,
            },
        },
        created() {
            this.init();
        },
        methods: {
            ...mapActions('crm', ['fetchCrmConfigList']),
            async init(showLoading = true) {
                try {
                    if (showLoading) {
                        this.loading = true;
                    }
                    await this.fetchCrmConfigList(true);
                    this.configList = Object.entries(this.crmConfigList || {}).map(([key, value]) => {
                        return {
                            key,
                            required: value.required,
                            disabled: !!value.unmodifiable,
                            sort: value.sort,
                        };
                    }).sort((a, b) => a.sort - b.sort);
                    this._configList = Clone(this.configList);
                } catch (e) {
                    Logger.error({
                        scene: 'fetchPatientKey',
                        msg: 'fetchPatientKey error',
                        err: e,
                    });
                } finally {
                    if (showLoading) {
                        this.loading = false;
                    }
                }
            },

            async onSave() {
                try {
                    this.confirmLoading = true;
                    const obj = {};
                    this.configList.forEach((item) => {
                        obj[item.key] = {
                            required: item.required, sort: item.sort,
                        };
                    });
                    await PropertyAPI.updateV3('field.patient.create', 'clinic', obj);
                    this.init(false);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (e) {
                    Logger.error({
                        scene: 'update patientKeyTable',
                        msg: 'setting error',
                        err: e,
                    });
                } finally {
                    this.confirmLoading = false;
                }
            },

            async onReset() {
                try {
                    this.resetLoading = true;
                    // 重置
                    const { data } = await PropertyAPI.getV3('field.patient.create', 'clinic/default');
                    const list = Clone(this.configList);
                    list.forEach((item) => {
                        for (const key in data) {
                            if (Object.prototype.hasOwnProperty.call(this.crmConfigList, key)) {
                                if (item.key === key) {
                                    item.required = data[key].required;
                                    item.sort = data[key].sort;
                                }
                            }
                        }
                    });
                    this.configList = list;
                } catch (e) {
                    Logger.error({
                        scene: 'update patientKeyTable',
                        msg: 'setting error',
                        err: e,
                    });
                } finally {
                    this.resetLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .field-layout-patient-filing-wrapper {
        position: relative;

        &-pharmacy {
            .print-preview-layout {
                padding: 0 58px;
            }
        }

        .abc-layout-wrapper-model {
            padding: 24px;
            transform: translate(0, 0);
        }

        .preview-html-wrapper {
            width: auto;
            padding: 0;
            overflow: unset;
            background: transparent !important;
            border: transparent;
            border-radius: var(--abc-border-radius-small);
            box-shadow: unset;

            .preview-html {
                width: auto;
                height: auto;
                transform: unset;
            }

            .outpatient-form-item .title {
                height: 40px;
                padding: 0 12px;
                border-bottom-color: var(--abc-color-card-divider-color);
            }
        }

        .abc-table-tr.is-drag-disabled {
            .abc-table-td.drag-handle {
                cursor: unset;

                .abc-table-cell {
                    display: none;
                }
            }
        }
    }

    .field-layout-dialog-info-patient-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .field-layout-dialog-info-patient {
        &.abc-dialog-wrapper {
            position: absolute !important;
            top: 0 !important;
            right: unset !important;
            bottom: unset !important;
            left: 0 !important;
            width: 510px;
            height: 724px;
            box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.1) !important;
            transform: scale(0.8) !important;
            transform-origin: 0 0 !important;

            .abc-dialog {
                top: 0 !important;
                left: 0 !important;
                transform: unset !important;

                .abc-dialog-body {
                    height: 668px !important;
                }
            }
        }
    }

    .field-layout-dialog-info-patient-distribute {
        &.abc-dialog-wrapper {
            height: 813px !important;

            .abc-dialog {
                .abc-dialog-body {
                    height: 756px !important;
                }
            }
        }
    }

    .field-layout-dialog-info-patient-hospital {
        &.abc-dialog-wrapper {
            height: 710px !important;
        }
    }

    .field-layout-dialog-info-patient-pharmacy {
        &.abc-dialog-wrapper {
            width: 460px !important;
            height: 712px !important;
            transform: scale(0.74) !important;
            transform-origin: 0 0 !important;

            .abc-dialog {
                .abc-dialog-body {
                    height: 656px !important;
                }
            }
        }
    }
</style>
