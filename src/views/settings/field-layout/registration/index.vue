<template>
    <biz-setting-layout class="field-layout-registration-wrapper">
        <biz-setting-content>
            <abc-layout preset="setting-table" style="padding: 0;">
                <abc-layout-content>
                    <abc-table
                        :render-config="renderConfig"
                        :data-list.sync="dataList"
                        :loading="loading"
                        style="max-width: 768px;"
                    ></abc-table>
                </abc-layout-content>
            </abc-layout>

            <template #footer>
                <biz-setting-footer>
                    <abc-flex>
                        <abc-button
                            :disabled="!isUpdate"
                            :loading="confirmLoading"
                            @click="onSave"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            :loading="resetLoading"
                            @click="onReset"
                        >
                            恢复默认
                        </abc-button>
                    </abc-flex>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar style="position: relative;">
            <preview-layout>
                <template #previewTab>
                    <div class="preview-tab-item preview-tab-item__active">
                        预约挂号
                    </div>
                </template>
                <template #previewHtml>
                    <appointment-base-card
                        :value="true"
                        form-source="field-layout"
                        :is-reserved="0"
                        :require-config-list="dataList"
                    ></appointment-base-card>
                </template>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import PropertyAPI from 'api/property';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import Logger from 'utils/logger';
    import RegistrationKeyTable from './table';
    import PreviewLayout from 'views/settings/print-config/components/preview-layout.vue';
    import AppointmentBaseCard from '@/views-dentistry/registration/appointment-base-card.vue';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingSidebar,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index';

    export default {
        components: {
            PreviewLayout,
            AppointmentBaseCard,
            BizSettingLayout,
            BizSettingContent,
            BizSettingSidebar,
            BizSettingFooter,
        },
        beforeRouteLeave(to, from, next) {
            if (this.isUpdate) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        props: {},
        data() {
            return {
                dataMap: [],
                loading: false,
                isUpdate: false,
                confirmLoading: false,
                resetLoading: false,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            renderConfig() {
                return RegistrationKeyTable.getRenderConfig({
                    handleInputRequired: this.handleInputRequired,
                });
            },
            dataList() {
                return this.dataMap || [];
            },
            switchSetting() {
                const keys = this.dataList.map((item) => item.key);
                const setting = {};
                keys.forEach((key) => {
                    setting[key] = 1;
                });
                return setting;
            },
        },
        watch: {
            dataMap: {
                handler() {
                    this.isUpdate = !isEqual(this._dataMap, this.dataMap);
                },
                deep: true,
            },
        },
        created() {
            this.init();
        },
        methods: {
            ...mapActions(['fetchClinicRegistrationFieldConfig']),

            async init() {
                try {
                    this.loading = true;
                    const data = await this.fetchClinicRegistrationFieldConfig();
                    this.dataMap = Object.entries(data || {}).map(([key, value]) => {
                        return {
                            key,
                            required: value.required,
                            disabled: !!value.unmodifiable,
                            sort: value.sort,
                        };
                    }).sort((a, b) => a.sort - b.sort);
                    this._dataMap = Clone(this.dataMap);
                } catch (e) {
                    Logger.error({
                        scene: 'fetchPatientKey',
                        msg: 'fetchPatientKey error',
                        err: e,
                    });
                } finally {
                    this.loading = false;
                }
            },


            handleInputRequired(val) {
                this.dataList[val.index].required = val.value;
                this.dataList = Clone(this.dataList);
            },

            async onSave() {
                try {
                    this.confirmLoading = true;
                    // 接口调用
                    const obj = {};
                    this.dataMap.forEach((item) => {
                        obj[item.key] = {
                            required: item.required, sort: item.sort,
                        };
                    });
                    await PropertyAPI.updateV3('field.registration.create', 'clinic', obj);
                    await this.init();
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (e) {
                    Logger.error({
                        scene: 'update registrationKeyTable',
                        msg: 'setting error',
                        err: e,
                    });
                } finally {
                    this.confirmLoading = false;
                }
            },

            async onReset() {
                try {
                    this.resetLoading = true;
                    // 重置
                    const { data } = await PropertyAPI.getV3('field.registration.create', 'clinic/default');
                    const list = Clone(this.dataMap);
                    list.forEach((item) => {
                        for (const key in data) {
                            if (Object.prototype.hasOwnProperty.call(data, key)) {
                                if (item.key === key) {
                                    item.required = data[key].required;
                                    item.sort = data[key].sort;
                                }
                            }
                        }
                    });
                    this.dataMap = list;
                } catch (e) {
                    Logger.error({
                        scene: 'update registrationKeyTable',
                        msg: 'setting error',
                        err: e,
                    });
                } finally {
                    this.resetLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .field-layout-registration-wrapper {
        position: relative;

        .abc-layout-wrapper {
            padding: 24px;
            transform: translate(0, 0);
        }

        .abc-layout-header + .abc-layout-content {
            padding-bottom: 24px;
            margin-top: 24px !important;
            margin-bottom: 40px;
        }

        .preview-html-wrapper {
            width: auto;
            height: auto;
            padding: 0;
            background: var(--abc-color-P5);
            border: transparent;
            border-radius: var(--abc-border-radius-small);
            box-shadow: unset;

            .preview-html {
                width: auto;
                height: auto;
                transform: unset;

                .appointment-base-card-wrapper {
                    .edit-status-wrapper {
                        box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1) !important;
                        transform: scale(0.8) !important;
                        transform-origin: 0 0 !important;
                    }
                }
            }

            .outpatient-form-item .title {
                height: 40px;
                padding: 0 12px;
                border-bottom-color: var(--abc-color-card-divider-color);
            }
        }

        .abc-table-tr.is-drag-disabled {
            .abc-table-td.drag-handle {
                cursor: unset;

                .abc-table-cell {
                    display: none;
                }
            }
        }
    }
</style>
