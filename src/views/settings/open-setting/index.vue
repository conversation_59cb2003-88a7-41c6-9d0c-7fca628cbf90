<template>
    <biz-fill-remain-height class="open-setting-wrapper">
        <template #header>
            <abc-manage-tabs
                v-if="showTabs"
                :option="tabOptions"
                @change="handleTabsChange"
            ></abc-manage-tabs>
        </template>

        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    export default {
        name: 'OpenSettingIndex', // 开出设置
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        computed: {
            ...mapGetters(['isChainAdmin','multiPharmacyCanUse', 'multiPharmacyCanUseWithDefault']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            gradeLevel() {
                return this.viewDistributeConfig.Settings.antimicrobial.gradeLevel;
            },
            showWareHouseSetBasic() {
                return this.multiPharmacyCanUse || this.multiPharmacyCanUseWithDefault;
            },
            showTabs() {
                const noTabsNames = [ 'releaseRulesSettingAdd', 'releaseRulesSettingForm'];
                return noTabsNames.indexOf(this.$route.name) === -1;
            },
            tabOptions() {
                return [
                    {
                        label: '开出设置', value: 'openSetting', visible: true,
                    },
                    {
                        label: '下达设置', value: 'releaseRulesSetting', visible: this.showWareHouseSetBasic,
                    },
                    {
                        label: '抗菌用药管理', value: 'antimicrobialManagement', visible: !this.gradeLevel,
                    },
                ].filter((item) => item.visible);
            },
        },

        methods: {
            handleTabsChange(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.open-setting-wrapper {
    background-color: #ffffff;
}
</style>
