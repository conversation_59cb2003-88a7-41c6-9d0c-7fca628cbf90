<template>
    <biz-setting-layout>
        <biz-setting-content>
            <abc-form ref="openSetForm" v-abc-loading="loading">
                <biz-setting-form :label-width="84">
                    <biz-setting-form-group>
                        <biz-setting-form-item label="出库顺序" has-divider>
                            <abc-text>
                                默认按效期顺序出库，效期近的先出（未录效期的按入库时间先进先出）
                            </abc-text>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="库存不足" has-divider>
                            <biz-setting-form-item-tip v-if="isLockBatchNotModify" tip="医保要求门诊、收费开单后必须锁定批次，因此库存不足时不允许继续开出">
                                不允许开出
                            </biz-setting-form-item-tip>
                            <abc-radio-group v-else :value="postData.stockGoodsConfig.disableNoStockGoods">
                                <!--
                                    为了对功能影响最小，
                                    把之前的按钮修改文案上下调换了位置，增加了选项三，状态定为2
                                    这样多处判断条件只要改成即可
                                    这样
                                    -->
                                <abc-tooltip placement="top-start" :arrow-offset="20" :disabled="!disabledCheckbox">
                                    <div>
                                        <abc-radio
                                            :label="0"
                                            data-type="label-align"
                                            :disabled="disabledCheckbox"
                                            @click="handleNoStockGoodsRadioClick(0)"
                                        >
                                            允许开出，同时系统会提醒库存不足
                                        </abc-radio>
                                    </div>
                                    <template #content>
                                        <span v-if="isWholeBillCharge">收费设置开启整单收退费，请先关闭后选择；<br /></span>
                                        <span v-if="isWholeBillDispensing">发药设置开启整单收退药，请先关闭后选择；</span>
                                    </template>
                                </abc-tooltip>

                                <abc-tooltip placement="top-start" :arrow-offset="20" :disabled="!disabledCheckbox">
                                    <div>
                                        <abc-radio
                                            :label="2"
                                            :disabled="disabledCheckbox"
                                            @click="handleNoStockGoodsRadioClick(2)"
                                        >
                                            允许开出，系统不做任何警示
                                        </abc-radio>
                                    </div>

                                    <template #content>
                                        <span v-if="isWholeBillCharge">收费设置开启整单收退费，请先关闭后选择；<br /></span>
                                        <span v-if="isWholeBillDispensing">发药设置开启整单收退药，请先关闭后选择；</span>
                                    </template>
                                </abc-tooltip>

                                <biz-setting-form-item-tip>
                                    <abc-radio :label="1" @click="handleNoStockGoodsRadioClick(1)">
                                        不允许开出
                                    </abc-radio>
                                </biz-setting-form-item-tip>
                                <abc-text v-if="isSupportCostPriceMakeUp" size="normal">
                                    例外：进价加成或医保按批次限价的商品，库存不足时始终【不允许开出】
                                </abc-text>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="库存过期" :has-divider="!!postData.lockInventoryConfigs?.length">
                            <biz-setting-form-item-tip tip="修改过期库存开出规则后，次日生效">
                                <abc-checkbox
                                    v-model="postData.stockGoodsConfig.disableExpiredGoods"
                                    type="number"
                                >
                                    开出时，系统自动排除过期库存
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <template v-for="item in postData.lockInventoryConfigs">
                            <biz-setting-form-item
                                :key="item.sceneType"
                                :label="getLabel(item)"
                            >
                                <biz-setting-form-item-tip v-if="isLockBatchNotModify" tip="医保要求在医保结算上传费用明细时上传药品追溯码、批次号、生产批号，因此必须在开单后锁定库存批次">
                                    开单后锁定{{ lockText }}
                                </biz-setting-form-item-tip>
                                <abc-radio-group v-else :value="item.lockFlag">
                                    <biz-setting-form-item-tip tip="不锁定库存，发药时扣除库存，从任意锁定库存切换为不锁定时，次日解锁全部已锁定库存">
                                        <abc-radio
                                            :label="0"
                                            data-type="label-align"
                                            @click="handleLockingRadioClick(item, 0)"
                                        >
                                            不锁定
                                        </abc-radio>
                                    </biz-setting-form-item-tip>

                                    <template v-if="item.sceneType === 0">
                                        <biz-setting-form-item-tip :tip="`收费后锁定相应${lockTextTip}以备发药，关闭发药单或退费后解除锁定`">
                                            <abc-radio
                                                :label="20"
                                                @click="handleLockingRadioClick(item, 20)"
                                            >
                                                收费后锁定{{ lockText }}
                                            </abc-radio>
                                        </biz-setting-form-item-tip>

                                        <biz-setting-form-item-indent>
                                            <biz-setting-form-item-tip>
                                                <abc-radio
                                                    :label="10"
                                                    @click="handleLockingRadioClick(item, 10)"
                                                >
                                                    开单后锁定{{ lockText }}
                                                </abc-radio>

                                                <template #tip>
                                                    <abc-space>
                                                        <template v-if="postData.chargeSheetAutoClose.autoClosedSwitch">
                                                            <abc-text size="mini" theme="gray">
                                                                开单后锁定相应{{ lockTextTip }}以备发药，关闭收费单、退费后、或超过{{
                                                                    postData.chargeSheetAutoClose.autoClosedTime
                                                                }}小时未付费时解除锁定
                                                            </abc-text>
                                                        </template>

                                                        <template v-else>
                                                            <abc-text size="mini" theme="gray">
                                                                开单后锁定相应{{ lockTextTip }}以备发药，关闭收费单、退费后解除锁定
                                                            </abc-text>
                                                        </template>

                                                        <abc-link
                                                            v-if="item.lockFlag === 10"
                                                            theme="primary"
                                                            size="small"
                                                            @click.stop="onLockInventoryClick"
                                                        >
                                                            <abc-text size="small">
                                                                设置自动解锁
                                                            </abc-text>
                                                        </abc-link>
                                                    </abc-space>
                                                </template>
                                            </biz-setting-form-item-tip>
                                        </biz-setting-form-item-indent>
                                        <abc-text
                                            v-if="isSupportCostPriceMakeUp"
                                            tag="div"
                                            size="normal"
                                        >
                                            例外：进价加成或医保按批次限价的商品，始终【开单后锁定库存】
                                        </abc-text>
                                    </template>

                                    <template v-if="item.sceneType === 10">
                                        <biz-setting-form-item-tip tip="核对医嘱生成执行任务后锁定库存，撤销医嘱或药房拒发后解除锁定">
                                            <abc-radio
                                                :label="110"
                                                @click="handleLockingRadioClick(item, 110)"
                                            >
                                                核对医嘱后锁定库存
                                            </abc-radio>
                                        </biz-setting-form-item-tip>
                                    </template>
                                </abc-radio-group>
                            </biz-setting-form-item>
                        </template>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button
                        :loading="btnLoading"
                        :disabled="btnDisabled"
                        @click="submit"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            title="快捷设置"
            append-to-body
            size="medium"
        >
            <abc-form ref="chargeForm">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span style="font-size: 14px; color: #7a8794;">收费单自动关闭</span>

                    <abc-checkbox v-model="dialogData.autoClosedSwitch" type="number" style="margin-left: auto;">
                        开启
                    </abc-checkbox>

                    <span
                        style="margin-left: 4px; font-size: 12px; color: #aab4bf;"
                    >收费单关闭后，系统将解除锁定库存</span>
                </div>

                <abc-form-item
                    v-if="dialogData.autoClosedSwitch"
                    required
                    style="display: flex; align-items: center; justify-content: flex-end; margin: 8px 0 0;"
                >
                    <abc-select
                        v-model="dialogData.autoClosedTime"
                        placeholder="请选择"
                        :disabled="!dialogData.autoClosedSwitch"
                        width="80px"
                    >
                        <abc-option :value="12" label="12小时"></abc-option>
                        <abc-option :value="24" label="24小时"></abc-option>
                        <abc-option :value="48" label="48小时"></abc-option>
                        <abc-option :value="72" label="72小时"></abc-option>
                    </abc-select>
                    <span style="margin-left: 8px;">内未收费，自动关闭收费单</span>
                </abc-form-item>
            </abc-form>

            <div slot="footer" class="dialog-footer">
                <abc-button @click="onConfirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="onCancel">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </biz-setting-layout>
</template>

<script>
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone';
    import PropertyAPI from 'api/property';
    import SettingApi from 'api/settings';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
        BizSettingFormItemIndent,
    } from '@/components-composite/setting-form/index.js';

    export default {
        name: 'OpenSetting', // 开出设置
        components: {
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingFormItemIndent,
        },
        beforeRouteLeave(to, from, next) {
            if (!this.btnDisabled) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                showDialog: false,
                dialogData: null,
                postData: {
                    stockGoodsConfig: {
                        disableNoStockGoods: 0, // 0:允许，会警示 1：不允许，会警示 2 允许，不警示
                        disableExpiredGoods: 0, // 过期药品能否被开出 0:允许， 1：不允许
                    },
                    lockInventoryConfigs: [],// 锁库配置
                    chargeSheetAutoClose: {
                        autoClosedTime: 0,// 收费配置（快捷配置）-关闭收费单时间
                        autoClosedSwitch: 0,// 收费配置（快捷配置）-自动关闭收费单
                    },
                },
                isWholeBillCharge: false,
                cachePostData: null,
                loading: false,
                btnLoading: false,
            };
        },
        computed: {
            ...mapGetters(['goodsConfig', 'chargeConfig', 'dispensingConfig', 'lockInventoryConfigs','isLockBatchNotModify']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isSupportCostPriceMakeUp() {
                return this.viewDistributeConfig.Inventory.isSupportCostPriceMakeUp;
            },
            outpatientLockGoodsName() {
                return this.viewDistributeConfig.Settings.openSetting.outpatientLockGoodsName;
            },
            btnDisabled() {
                return isEqual(this.postData, this.cachePostData);
            },
            isChangeLockConfig() {
                return !isEqual(this.postData.lockInventoryConfigs, this.cachePostData.lockInventoryConfigs);
            },
            isChangeChargeConfig() {
                return !isEqual(this.postData.chargeSheetAutoClose, this.cachePostData.chargeSheetAutoClose);
            },
            isChangeStockConfig() {
                return !isEqual(this.postData.stockGoodsConfig, this.cachePostData.stockGoodsConfig);
            },
            isShandongRizhao() {
                return this.$abcSocialSecurity.config.isShandongRizhao;
            },
            lockText() {
                return this.isShandongRizhao ? '批次' : '库存';
            },
            lockTextTip() {
                return this.isShandongRizhao ? '批次' : '库存量';
            },
            // 是否整单发退药
            isWholeBillDispensing() {
                return !!this.dispensingConfig.wholeSheetOperateEnable;
            },
            // 库存不足允许开出禁用判断
            disabledCheckbox() {
                return this.isWholeBillCharge || this.isWholeBillDispensing;
            },
        },
        created() {
            this.$store.dispatch('fetchPharmacyConfig');
            this.fetchData();
        },
        methods: {
            ...mapActions(['setGoodsConfig', 'updateStockLockConfig']),

            getLabel(item) {
                return item.sceneType === 0 ? this.isLockGoodsName ? '门诊/咨询锁定库存' : '门诊锁定库存' : '住院锁定库存';
            },

            async fetchData() {
                this.loading = true;
                try {
                    const {
                        data: {
                            autoClosedTime, autoClosedSwitch, wholeSheetOperateEnable,
                        },
                    } = await SettingApi.chargeSet.selectChargesConfig();
                    this.postData = {
                        stockGoodsConfig: { ...this.goodsConfig.stockGoodsConfig },
                        lockInventoryConfigs: clone(this.lockInventoryConfigs),
                        chargeSheetAutoClose: {
                            autoClosedTime,// 收费配置（快捷配置）-关闭收费单时间
                            autoClosedSwitch,// 收费配置（快捷配置）-自动关闭收费单
                        },
                    };

                    this.isWholeBillCharge = !!wholeSheetOperateEnable;

                    this.cachePostData = clone(this.postData);
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },
            submit() {
                this.$refs.openSetForm.validate(async (val) => {
                    if (val) {
                        try {
                            this.btnLoading = true;
                            const submitData = { ...this.postData };

                            if (this.isChangeLockConfig) {
                                await this.updateStockLockConfig(submitData.lockInventoryConfigs);
                            }

                            if (this.isChangeChargeConfig) {
                                await PropertyAPI.updateV3(
                                    'chargeSheetAutoClose',
                                    'clinic',
                                    submitData.chargeSheetAutoClose,
                                );
                            }

                            delete submitData.lockInventoryConfigs;
                            delete submitData.chargeSheetAutoClose;

                            if (this.isChangeStockConfig) {
                                await this.setGoodsConfig(submitData);
                            }

                            this.cachePostData = clone(this.postData);// 缓存灰度的值，需要用于校验是否修改

                            this.$Toast({
                                message: '保存成功',
                                type: 'success',
                            });
                        } catch (err) {
                            if (!err.alerted) {
                                this.$Toast({
                                    message: err.message || '保存失败',
                                    type: 'error',
                                });
                            }
                        } finally {
                            this.btnLoading = false;
                        }
                    }
                });
            },
            onLockInventoryClick() {
                this.showDialog = true;
                this.dialogData = {
                    ...this.postData.chargeSheetAutoClose,
                };
            },

            handleNoStockGoodsRadioClick(value) {
                if (this.postData.lockInventoryConfigs.some((item) => item.lockFlag !== 0) && [0, 2].includes(value)) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '开启锁库时，库存不足的药品不允许开出',
                    });
                } else {
                    this.postData.stockGoodsConfig.disableNoStockGoods = value;
                }
            },

            handleLockingRadioClick(item, value) {
                if (this.postData.stockGoodsConfig.disableNoStockGoods !== 1 && value !== 0) {
                    return this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '库存不足的药品允许开出时，不支持开启锁库',
                    });
                }

                if (this.isShandongRizhao) {
                    if (item.sceneType === 0 && (value === 10 || value === 20)) {
                        item.lockBatch = 10;
                    }
                    item.lockFlag = value;
                } else {
                    if (item.lockBatch && item.lockFlag !== value) {
                        this.message = this.$alert({
                            type: 'warn',
                            title: '无法修改锁库设置',
                            content: '已开启基于批次进价上浮的医保限价功能，依赖开单后锁定库存功能，无法修改。',
                        });
                    } else {
                        item.lockFlag = value;
                    }
                }

            },
            onConfirm() {
                this.$refs.chargeForm.validate((val) => {
                    if (val) {
                        this.postData.chargeSheetAutoClose = { ...this.dialogData };
                        this.onCancel();
                    }
                });
            },
            onCancel() {
                this.showDialog = false;
                this.dialogData = null;
            },
        },
    };
</script>


