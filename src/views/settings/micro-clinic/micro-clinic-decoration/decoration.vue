<template>
    <manage-page
        class="micro-clinic-decoration-wrapper micro-clinic-decoration-manager"
        :content-styles="contentStyles"
        :footer-styles="ManagePageFooterStyle"
    >
        <div class="notice-manager-title">
            <div
                v-for="(item,index) in titleTypeList"
                :key="index"
                class="title-name"
                :class="{ 'title_name': choiceType === item.flag }"
                @click="checkTab(item)"
            >
                {{ item.name }}
            </div>
        </div>
        <div class="micro-clinic-decoration-manager-info">
            <micro-clinic-homepage v-if="choiceType === 0" @updateHomepageConfig="updateHomepageConfig"></micro-clinic-homepage>
            <store-homepage v-else-if="choiceType === 1" @updateHomepageConfig="updateHomepageConfig"></store-homepage>
            <service-project v-else-if="choiceType === 2" @updateHomepageConfig="updateHomepageConfig"></service-project>
            <find-doctor-list v-else-if="choiceType === 3" @updateHomepageConfig="updateHomepageConfig"></find-doctor-list>
            <online-doctor-list v-else-if="choiceType === 4" @updateHomepageConfig="updateHomepageConfig"></online-doctor-list>
            <outpatient-appointment v-else-if="choiceType === 5" @updateHomepageConfig="updateHomepageConfig"></outpatient-appointment>
            <therapy-appointment v-else-if="choiceType === 6" @updateHomepageConfig="updateHomepageConfig"></therapy-appointment>
            <doctor-appointment
                v-else-if="choiceType === 7"
                @changeTab="changeTab"
                @updateHomepageConfig="updateHomepageConfig"
            ></doctor-appointment>
            <public-account-menu
                v-else-if="choiceType === 8"
            ></public-account-menu>
            <style-setting v-else-if="choiceType === 9"></style-setting>
        </div>
    </manage-page>
</template>

<script>
    import ManagePage from '@/views/settings/components/manage-page.vue';
    import MicroClinicHomepage from './micro-clinic-homepage/micro-clinic-homepage.vue';
    import StoreHomepage from './store-homepage/store-homepage.vue';
    import leaveHint from './mixins/leave-hint';
    import FindDoctorList from './doctor/find-doctor-list';
    import OnlineDoctorList from './doctor/online-doctor-list';
    import OutpatientAppointment from './doctor/outpatient-appointment';
    import TherapyAppointment from './doctor/therapy-appointment';
    import DoctorAppointment from './doctor/doctor-appointment';
    import PublicAccountMenu from './public-account-menu/index.vue';
    import StyleSetting from './style-setting/style-setting';
    import ServiceProject from './service/service-project';
    import { mapGetters } from 'vuex';

    export default {
        name: 'MicroClinicDecoration',
        components: {
            ManagePage,
            StoreHomepage,
            MicroClinicHomepage,
            FindDoctorList,
            OnlineDoctorList,
            OutpatientAppointment,
            DoctorAppointment,
            PublicAccountMenu,
            TherapyAppointment,
            StyleSetting,
            ServiceProject,
        },
        mixins: [leaveHint],
        data() {
            return {
                choiceType: 0,
                isChange: false,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['featureTherapy']),
            ...mapGetters('viewDistribute',[
                'featureTreatOnline',
            ]),
            titleTypeList() {
                const tabList = [
                    {
                        name: `微${this.$app.institutionTypeWording}主页`,
                        flag: 0,
                        common: 'microClinicHomepage',
                        visible: true,
                    },
                    {
                        name: '门店主页',
                        flag: 1,
                        common: 'StoreHomepage',
                        visible: true,
                    },
                    {
                        name: '服务项目列表',
                        flag: 2,
                        common: 'Service',
                        visible: true,
                    },
                    {
                        name: '查找医生列表',
                        flag: 3,
                        common: 'findDoctorList',
                        visible: true,
                    },
                    {
                        name: '在线咨询列表',
                        flag: 4,
                        common: 'onlineConsultationList',
                        visible: this.featureTreatOnline,
                    },
                    {
                        name: '门诊预约列表',
                        flag: 5,
                        common: 'outpatientAppointmentList',
                        visible: true,
                    },
                    {
                        name: '理疗预约列表',
                        flag: 6,
                        common: 'therapyAppointment',
                        visible: this.featureTherapy,
                    },
                    {
                        name: '坐诊表',
                        flag: 7,
                        common: 'diagnosisList',
                        visible: true,
                    },
                    {
                        name: '公众号菜单',
                        flag: 8,
                        common: 'publicAccountMenu',
                        visible: true,
                    },
                    {
                        name: '风格设置',
                        flag: 9,
                        common: 'styleSetting',
                        visible: true,
                    },
                ];
                return tabList.filter((item) => {
                    return item.visible;
                });
            },
            ManagePageFooterStyle() {
                return 'display: none;';
            },
            contentStyles() {
                return 'height:calc(100vh - 170px);';
            },
        },
        created() {
            const { tab = 0 } = this.$route.query;
            this.choiceType = tab;
        },
        methods: {
            setTab(item) {
                this.choiceType = item.flag;
            },
            changeTab() {
                this.choiceType = 3;
            },
            checkTab(item) {
                if (this.isChange) {
                    this.leaveHintSave(item.flag);
                } else {
                    this.choiceType = item.flag;
                }
            },
            updateHomepageConfig(isChange) {
                this.isChange = isChange;
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.micro-clinic-decoration-wrapper {
    &.micro-clinic-decoration-manager {
        padding-right: 24px;
    }

    .micro-clinic-decoration-manager-info {
        width: 100%;
        // height: auto;
        height: calc(100vh - 224px);
        margin-top: 24px;
    }

    .notice-manager-title {
        display: flex;
        width: 100%;
        height: 36px;

        .title-name {
            min-width: 77px;
            height: 36px;
            padding: 10px 10px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1;
            color: $T2;
            text-align: center;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);

            &:not(:first-child) {
                margin-left: 6px;
                cursor: pointer;
            }
        }

        .title_name {
            color: #005ed9;
            background: #f0f7ff;
            border: 1px solid #3495ff;
        }
    }
}
</style>
