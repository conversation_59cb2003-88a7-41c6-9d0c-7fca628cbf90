<template>
    <div class="micro-clinic-visit-table-wrapper">
        <div class="micro-clinic-decorate-preview-layout">
            <decoration-preview
                ref="decorationPreview"
                :preview-url="previewUrl"
                :arrow-default-top="500"
                @page-loaded="handlePageLoad"
            >
                <div class="decorate-preview-slot-wrapper">
                    <div class="decorate-preview-slot-header">
                        服务项目列表中的项目排序
                    </div>
                    <div class="decorate-preview-slot-content">
                        <sort-form
                            v-model="projectList"
                            v-abc-loading="sortLoading"
                            :sort-type.sync="sortType"
                            :sort-options="sortOptions"
                            :draggable="isCustomSort"
                            sort-title="排序方式"
                            drag-style="max-width: 959px;"
                            sort-label="项目排序"
                            style="margin-top: -3px;"
                            @changeSortType="handleSortType"
                        >
                            <abc-radio
                                slot="sortTypeItem"
                                slot-scope="{ sortOption }"
                                :label="sortOption.value"
                            >
                                {{ sortOption.label }}
                                <span v-if="sortOption.tips" class="tips-info">{{ sortOption.tips }}</span>
                            </abc-radio>

                            <sort-item
                                :key="sortItem.id"
                                slot-scope="{ sortItem }"
                                class="drag-item"
                                :icon-list="[
                                    {
                                        name: 'move_item', color: '#C3CBD2', style: 'margin-right: 4px;'
                                    },
                                    {
                                        name: 'module', color: '#91C1FF'
                                    },
                                ]"
                                :content-style="'width: 230px;'"
                                :class="{ draggable: isCustomSort }"
                                :is-hidden-move="!isCustomSort"
                                :label="sortItem.name"
                            ></sort-item>
                        </sort-form>
                    </div>
                    <div class="decorate-preview-slot-footer">
                        <abc-button
                            :loading="loading"
                            :disabled="!isModify"
                            style="width: 80px;"
                            @click="submit"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            type="blank"
                            style="width: 80px;"
                            :loading="loading"
                            :disabled="!isModify"
                            @click="cancel"
                        >
                            取消
                        </abc-button>
                    </div>
                </div>
            </decoration-preview>
        </div>
    </div>
</template>

<script>
    import DecorationPreview from 'views/settings/micro-clinic/decoration/index';
    import SortItem from 'views/settings/micro-clinic/layout/sort-item';
    import SortForm from 'views/settings/micro-clinic/layout/sort-form';
    import { getPreviewBaseUrl } from 'views/settings/micro-clinic/decoration/config';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import WeClinicAPI from 'api/we-clinic';
    import {
        ACTIVATE_EDITOR, REFRESH_DATA,
    } from 'views/settings/micro-clinic/decoration/constant';
    const SortType = {
        PIN_YIN: 1,
        DOWN: 7,
        UP: 6,
        CUSTOM: 3,
    };
    export default {
        name: 'ServiceProject',
        components: {
            SortItem,
            SortForm,
            DecorationPreview,
        },
        data() {
            return {
                sortOptions: [
                    {
                        label: '按首字母A-Z排序',
                        value: SortType.PIN_YIN,
                    },
                    {
                        label: '按价格由高到低排序',
                        value: SortType.DOWN,
                    },
                    {
                        label: '按价格由低到高排序',
                        value: SortType.UP,
                    },
                    {
                        label: '自定义',
                        value: SortType.CUSTOM,
                        tips: '拖动名称排序',
                    },
                ],
                sortType: SortType.PIN_YIN,
                sortTypeCache: SortType.PIN_YIN,
                projectList: [],
                projectListCache: [],
                loading: false,
                sortLoading: false,
            };
        },
        computed: {
            previewUrl() {
                return `${getPreviewBaseUrl()}/service`;
            },
            isModify() {
                return !isEqual(this.projectListCache, this.projectList) || this.sortType !== this.sortTypeCache;
            },
            isCustomSort() {
                return this.sortType === SortType.CUSTOM;
            },
            isPinYinSort() {
                return this.sortType === SortType.PIN_YIN;
            },
        },
        async created() {
            await this.fetchData();
        },
        methods: {
            async fetchData() {
                this.sortLoading = true;
                try {
                    const { data } = await WeClinicAPI.fetchRegistrationProducts({ registrationType: 1 });
                    console.log(data);
                    this.projectList = data?.registrationProducts?.map((item) => {
                        return {
                            ...item,
                            name: item.displayName,
                            namePyFirst: item?.namePyFirst || '',
                            namePy: item?.namePy || '',
                        };
                    }) || [];
                    this.sortType = data?.sortType || SortType.PIN_YIN;
                    this.sortTypeCache = data?.sortType || SortType.PIN_YIN;
                    this.projectListCache = Clone(this.projectList);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.sortLoading = false;
                }
            },
            sortByPinYin() {
                this.projectList.sort((itemA, itemB) => {
                    return itemA.namePyFirst >= itemB.namePyFirst ? 1 : -1;
                });
            },
            sortByPrice(type = 'down') {
                this.projectList.sort((itemA, itemB) => {
                    if (type === 'up') {
                        return Number(itemA.price) >= Number(itemB.price) ? 1 : -1;
                    }
                    return Number(itemA.price) <= Number(itemB.price) ? 1 : -1;
                });
            },
            async submit() {
                try {
                    const { data } = await WeClinicAPI.updateRegistrationProducts({
                        registrationType: 1,
                        sortType: this.sortType || SortType.CUSTOM,
                        registrationProductIds: this.projectList?.map((item) => {
                            return item?.id || '';
                        }),
                    });
                    if (data) {
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                        this.sortTypeCache = this.sortType;
                        this.projectListCache = Clone(this.projectList);
                        this.$refs.decorationPreview.getInstance()
                            .emit(REFRESH_DATA, {
                                command: 'serviceList',
                            });
                    }
                } catch (e) {
                    console.log(e);
                    this.$Toast({
                        type: 'error',
                        message: '保存失败',
                    });
                }

            },
            handleSortType() {
                switch (this.sortType) {
                    case SortType.PIN_YIN:
                        this.sortByPinYin();
                        break;
                    case SortType.CUSTOM:
                        break;
                    case SortType.UP:
                        this.sortByPrice('up');
                        break;
                    case SortType.DOWN:
                        this.sortByPrice();
                        break;
                    default:
                        break;
                }
            },
            cancel() {
                this.sortType = this.sortTypeCache;
                this.projectList = Clone(this.projectListCache);
            },
            handlePageLoad() {
                const instance = this.$refs.decorationPreview.getInstance();
                instance.emit(ACTIVATE_EDITOR, { command: 'serviceList' });
            },
        },
    };
</script>

<style lang="scss" scoped>
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.micro-clinic-visit-table-wrapper {
    overflow: hidden;

    .label-box {
        margin-bottom: 24px;

        .label-box-title {
            display: flex;
            align-items: center;
            height: 20px;
            font-size: 14px;
            line-height: 20px;
            color: #7a8794;
        }

        .label-box-info {
            height: auto;
            margin-top: 8px;

            .time-set-wrapper {
                .time-set-wrapper-line {
                    height: 48px;
                }
            }
        }
    }
}
</style>
