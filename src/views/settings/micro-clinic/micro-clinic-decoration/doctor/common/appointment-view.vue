<template>
    <div class="appointment-view">
        <abc-icon
            v-if="sortType"
            icon="move_item"
            size="14"
            color="#7a8794"
            style="margin-right: 8px;"
        ></abc-icon>
        <div class="appointment-view_name" :title="appointmentInfo.name">
            {{ appointmentInfo.name }}
        </div>
        <div class="appointment-view_time">
            {{ appointmentInfo.time }}
        </div>
        <div class="appointment-view_number">
            {{ appointmentInfo.number }}
        </div>
    </div>
</template>

<script>
    export default {
        name: 'AppointmentView',
        props: {
            appointmentInfo: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            index: {
                type: Number,
                default: 0,
            },
            sortType: Boolean,
        },
    };
</script>

<style scoped lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.appointment-view {
    display: flex;
    align-items: center;
    width: 100%;
    height: 32px;
    padding: 8px 8px;
    cursor: pointer;
    border-bottom: 1px solid $P6;

    &:hover {
        background-color: #eff3f6;
    }

    div {
        height: 20px;
        font-size: 14px;
        line-height: 20px;
        color: $T1;
    }

    &_name {
        max-width: calc(100% - 148px);
        letter-spacing: 0;

        @include ellipsis;
    }

    &_time {
        width: 76px;
        margin-left: auto;
    }

    &_number {
        width: 42px;
        margin-left: 8px;
    }
}
</style>
