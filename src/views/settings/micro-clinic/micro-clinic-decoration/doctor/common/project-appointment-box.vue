<template>
    <div class="project-appointment_box">
        <div class="project-appointment_box--line">
            <div class="project-appointment_box--line-title">
                项目排序方式
            </div>
            <div class="project-appointment_box--line-radio">
                <abc-radio-group v-model="sortType" item-block>
                    <abc-radio :label="5">
                        按预约数量排序
                    </abc-radio>
                    <abc-radio :label="3">
                        自定义 <span style="color: #7a8794;">拖动名称排序</span>
                    </abc-radio>
                </abc-radio-group>
            </div>
        </div>
        <div class="project-appointment_box--line">
            <div class="project-appointment_box--line-title">
                项目顺序
            </div>
            <div class="project-appointment_box--line-List">
                <draggable
                    v-model="appointmentProjectList"
                    :handle="'.appointment-view'"
                    :animation="60"
                    ghost-class="abc-sortable-ghost"
                    :move="handleDragMove"
                >
                    <transition-group
                        name="list-complete"
                        :css="false"
                        tag="div"
                        mode="out-in"
                        class="sort-list-wrapper"
                    >
                        <template v-for="(item, index) in appointmentProjectList">
                            <slot :sort-item="item" :index="index" :draggable="draggable">
                                <appointment-view
                                    :key="index"
                                    :sort-type="sortType === 3"
                                    :index="index"
                                    :appointment-info="item"
                                ></appointment-view>
                            </slot>
                        </template>
                    </transition-group>
                </draggable>
            </div>
        </div>
    </div>
</template>

<script>
    import AppointmentView from './appointment-view';
    import Draggable from 'vuedraggable';
    export default {
        name: 'ProjectAppointmentBox',
        components: {
            AppointmentView,
            Draggable,
        },
        props: {
            appointmentList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            choiceSortType: {
                type: Number,
                default: 5,
            },
        },
        data() {
            return {
            };
        },
        computed: {
            sortType: {
                get() {
                    return this.choiceSortType;
                },
                set(v) {
                    this.$emit('update:choiceSortType', v);
                },
            },
            appointmentProjectList: {
                get() {
                    return this.appointmentList;
                },
                set(v) {
                    this.$emit('update:appointmentList', v);
                },
            },
            draggable() {
                return this.sortType === 3;
            },
        },
        methods: {
            handleDragMove() {
                return this.draggable;
            },
        },
    };
</script>

<style scoped lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.project-appointment_box {
    width: 100%;
    height: auto;

    &--line {
        width: 100%;
        margin-top: 24px;

        &:first-child {
            margin-top: 0;
        }

        &-title {
            height: 20px;
            font-size: 14px;
            line-height: 20px;
            color: $T2;
        }

        &-radio {
            width: 100%;
            margin-top: 8px;
        }

        &-List {
            width: 100%;
            margin-top: 8px;
            border-top: 1px solid $P6;
        }
    }
}
</style>
