<template>
    <div class="department-navigation-wrapper">
        <div class="wc-bg-header">
            <div
                v-if="!isFocused"
                class="wc-bg-header-detail"
                @click="isFocused = true"
            >
                <div
                    class="wc-bg-header-title"
                >
                    {{ currentHomepageConfig.departmentNavigation.title }}
                </div>
                <abc-icon size="14" icon="Edit_Profile"></abc-icon>
            </div>
            <abc-input
                v-else
                v-model="currentHomepageConfig.departmentNavigation.title"
                type="text"
                :width="165"
                max-length="6"
                trim
                :input-custom-style="{
                    padding: '8px 12px', textAlign: 'left', fontWeight: 'bold'
                }"
                placeholder="科室导航"
                @blur="blurHandler"
            ></abc-input>
        </div>

        <div class="wc-bg-middle">
            <sort-list
                v-model="currentHomepageConfig.departmentNavigation.departments"
            >
                <sort-item
                    :key="sortItem.id || sortItem.createId"
                    slot-scope="{
                        sortItem, index
                    }"
                    :label="sortItem.name"
                    deletable
                    class="draggable drag-item"
                    :icon-list="[
                        {
                            name: 'move_item', color: '#C3CBD2', style: 'margin-right: 4px;'
                        },
                        {
                            name: 'department', color: '#85BAFF'
                        },
                    ]"
                    content-style="min-width: 138px;"
                    line-right=""
                    @click.native="handleEditDepartmentClick(sortItem)"
                    @delete="handleDeleteDepartmentClick(index)"
                ></sort-item>

                <div
                    slot="append"
                    key="add-department-btn"
                    class="wc-bg-doctor-item wc-bg-department-add"
                    @click="handleCreateDepartmentClick()"
                >
                    添加
                </div>
            </sort-list>
        </div>

        <department-editor
            v-if="isShowDepartmentEditor"
            v-model="currentEditDepartment"
            :visible.sync="isShowDepartmentEditor"
            :department-type="currentEditDepartment.type"
            :all-departments="currentHomepageConfig.departmentNavigation.departments"
            @change="handleUpdateDepartment"
            @create="handleCreateDepartment"
        ></department-editor>

        <div class="wc-bg-footer">
            <abc-button :loading="isLoading" :disabled="!isChange || isLoading" @click="handleSave">
                保存
            </abc-button>
            <abc-button :disabled="!isChange || isLoading" type="blank" @click="handleCancel">
                取消
            </abc-button>
        </div>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';

    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import leaveHint from '../../mixins/leave-hint';

    import SortList from 'views/settings/micro-clinic/layout/sort-list/index.vue';
    import SortItem from 'views/settings/micro-clinic/layout/sort-item.vue';
    import DepartmentEditor from 'views/settings/micro-clinic/layout/department-editor.vue';

    export default {
        name: 'DepartmentNavigation',
        components: {
            SortItem,
            SortList,
            DepartmentEditor,
        },
        mixins: [leaveHint],
        props: {
            homepageConfig: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                isLoading: false,

                originHomepageConfig: null,
                currentHomepageConfig: null,

                // 科室导航
                currentEditDepartment: {
                    doctors: [],
                    name: '',
                },
                isShowDepartmentEditor: false,
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'isOpenMp', 'clinicConfig', 'isAdmin', 'userInfo']),

            isChange() {
                return !isEqual(this.originHomepageConfig, this.currentHomepageConfig);
            },
        },
        watch: {
            homepageConfig: {
                handler(config) {
                    this.originHomepageConfig = Clone(config);
                    this.currentHomepageConfig = Clone(config);

                    this.currentBanners = config.headBanners.map((item) => {
                        return {
                            ...item,
                            keyId: item.id,
                        };
                    });

                    this.originBanners = Clone(this.currentBanners);
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            handleCreateDepartmentClick() {
                this.currentEditDepartment = {
                    doctors: [],
                    name: '',
                };
                this.isShowDepartmentEditor = true;
            },

            handleEditDepartmentClick(tagInfo) {
                this.currentEditDepartment = tagInfo;
                this.isShowDepartmentEditor = true;
            },

            async handleDeleteDepartmentClick(index) {
                this.currentHomepageConfig.departmentNavigation.departments.splice(index, 1);
            },

            handleUpdateDepartment(department) {
                const origin = this.currentHomepageConfig.departmentNavigation.departments.find(
                    (item) => item.id === department.id,
                );
                if (origin) {
                    Object.assign(origin, department);
                }
            },

            handleCreateDepartment(department) {
                this.currentHomepageConfig.departmentNavigation.departments.push(department);
            },

            async handleSave() {
                try {
                    this.isLoading = true;
                    const postData = Clone(this.currentHomepageConfig);

                    const data = await this.$store.dispatch('weClinic/updateHomepageConfig', postData);
                    this.$emit('save-success', 'department');

                    if (data) {
                        this.$Toast({
                            message: '保存成功!',
                            type: 'success',
                        });
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.isLoading = false;
                }
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/theme.scss';

.department-navigation-wrapper {
    .wc-bg-department-add {
        display: inline-block;
        height: 28px;
        margin: 0 8px 0 4px;
        font-size: 14px;
        line-height: 28px;
        color: $B1;
        cursor: pointer;
        border: none;
    }

    .wc-bg-content-item {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: flex-start;

        > span {
            margin-right: 40px;
            font-size: 14px;
            color: #626d77;
        }

        label.title {
            font-size: 14px;
            color: #626d77;
        }

        span.tips {
            margin-left: 8px;
            font-size: 12px;
            color: #8d9aa8;
        }

        em {
            font-size: 12px;
            color: #8d9aa8;
        }

        .wc-decorate-homepage-banner-wrapper {
            display: flex;
            flex-direction: column;

            .banner-upload-list-wrapper {
                display: flex;

                .banner-upload-item-wrapper {
                    display: flex;
                    flex-direction: column;
                    margin-top: 8px;
                    margin-right: 8px;

                    .banner-img-wrapper {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        width: 210px;
                        height: 110px;
                        padding: 4px;
                        margin-bottom: 8px;
                        cursor: pointer;
                        border: 1px solid $P1;
                        border-radius: var(--abc-border-radius-small);

                        i {
                            color: #8d9aa8;
                        }

                        img {
                            position: absolute;
                            width: 100%;
                            height: 100%;
                            padding: 4px;
                            border: 0;
                            object-fit: cover;
                        }

                        .abc-progress-wrapper {
                            position: absolute;
                            bottom: 4px;
                            left: 4px;

                            span {
                                color: white;
                            }
                        }

                        i.cis-icon-delete_file {
                            position: absolute;
                            top: -8px;
                            right: -8px;
                            font-size: 16px;
                            color: $P1;
                        }
                    }
                }
            }
        }
    }

    .wc-bg-content-item + .wc-bg-content-item {
        margin-top: 24px;
    }
}
</style>
