<template>
    <div class="introduction-home-nav" style="height: 100%;">
        <div class="wc-bg-header">
            <div class="wc-bg-header-title nav-header-title">
                导航设置
                <abc-tooltip-info>
                    <div>
                        导航可方便用户快速找到对应业务，提升业务曝光度
                    </div>
                </abc-tooltip-info>
            </div>
        </div>
        <div class="wc-bg-middle nav-bg-middle">
            <div class="nav-bg-middle_box">
                <div class="nav-bg-middle_box--title">
                    <span>导航名称</span>
                    <span>是否展示</span>
                </div>
                <draggable
                    v-model="navList"
                    :handle="'.nav-bg-middle_box--line'"
                    :animation="60"
                    ghost-class="abc-sortable-ghost"
                    :move="handleDragMove"
                >
                    <transition-group
                        :css="false"
                        mode="out-in"
                        name="list-complete"
                        tag="div"
                    >
                        <template v-for="(item, index) in navList">
                            <slot :sort-item="item" :index="index" :draggable="draggable">
                                <div :key="index" class="nav-bg-middle_box--line">
                                    <div class="nav-bg-middle_box--line-label">
                                        <abc-icon
                                            icon="move_item"
                                            size="14"
                                            color="#7a8794"
                                            style="margin-right: 8px;"
                                        ></abc-icon>{{ item.name }}
                                        <abc-space
                                            v-if="!item.innerFlag"
                                            class="nav-bg-middle_box--line-label-modify"
                                            :size="4"
                                            style="margin-left: 4px;"
                                        >
                                            <abc-button type="text" @click="addOwnNav(item)">
                                                修改
                                            </abc-button>
                                            <abc-button type="text" style="color: #ff3166;" @click="deleteOwnNav(item)">
                                                删除
                                            </abc-button>
                                        </abc-space>
                                    </div>
                                    <div class="nav-bg-middle_box--line-switch">
                                        <abc-popover
                                            :disabled="!item.disabled"
                                            width="266px"
                                            placement="top-start"
                                            trigger="hover"
                                            theme="yellow"
                                        >
                                            <div style="display: inline-block;">
                                                {{ item.disableTips }}
                                            </div>
                                            <abc-switch
                                                slot="reference"
                                                v-model="item.isHidden"
                                                @change="handleSwitchChange(item)"
                                            ></abc-switch>
                                        </abc-popover>
                                    </div>
                                </div>
                            </slot>
                        </template>
                    </transition-group>
                </draggable>
                <div v-if="isAllowAdd" class="nav-bg-middle_box--line-add" @click="addOwnNav(null)">
                    <abc-button icon="plus_thin" type="text">
                        新增
                    </abc-button>
                </div>
            </div>
        </div>
        <div class="wc-bg-footer">
            <abc-button :loading="isLoading" :disabled="!isModify" @click="postBatchMicroNavList">
                保存
            </abc-button>
            <abc-button :disabled="!isModify" type="blank" @click="handleCancelSave">
                取消
            </abc-button>
        </div>
        <micro-setting-header-img-url-dialog
            v-if="microSettingHeaderImgUrlDialogVisible"
            v-model="microSettingHeaderImgUrlDialogVisible"
            :header-img-url="headerUrl"
            :type="1"
            :own-name="ownName"
            :nav-id="navId"
            :sort="sort"
            :own-icon-url="OwnIconUrl"
            :header-page-to-home="headerPageToHome"
            :header-jump-url="headerJumpUrl"
            :jump-app-id="jumpAppId"
            @addOwnNavItem="addOwnNavItem"
            @modifyOwnNav="modifyOwnNav"
        ></micro-setting-header-img-url-dialog>
    </div>
</template>

<script>
    import Draggable from 'vuedraggable';
    import {
        mapGetters,
    } from 'vuex';
    import MicroSettingHeaderImgUrlDialog from '../common/micro-setting-header-img-url-dialog.vue';
    import { McConfigAPI } from 'views/settings/micro-clinic/core/mc-config-api.js';
    import Clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';
    const OwnIconUrl = '//static-common-cdn.abcyun.cn/img/weapp/icon-home-custom-nav.png';
    export default {
        name: 'HomeNavSetting',
        components: {
            Draggable,
            MicroSettingHeaderImgUrlDialog,
        },
        data() {
            return {
                OwnIconUrl,
                isLoading: false,
                headerUrl: '',
                headerJumpUrl: '',
                microSettingHeaderImgUrlDialogVisible: false,
                draggable: true,
                navList: [
                ],
                navListCache: [],
                ownName: '',
                navId: '',
                headerPageToHome: 0,
                needDisable: false,
                sort: 0,
                jumpAppId: '',
            };
        },
        computed: {
            ...mapGetters(['chainBasic']),
            ...mapGetters('weClinic', ['config']),
            ...mapGetters(['isSingleStore']),
            isEnableCustomNavigationMenu() {
                return this.config?.isEnableCustomNavigationMenu;
            },
            isAllowAdd() {
                return !this.navList.find((item) => !item.innerFlag) && this.isEnableCustomNavigationMenu;
            },
            isModify() {
                return !isEqual(this.navList, this.navListCache);
            },
        },
        async created() {
            await this.fetchMicroNavList();
        },
        methods: {
            handleSwitchChange(item) {
                if (!this.navList.find((item) => {
                    return item.isHidden;
                })) {
                    this.$Toast({
                        message: '导航栏需要至少开启一个',
                        type: 'warn',
                    });
                    const navItem = this.navList.find((it) => {
                        return it.id === item.id;
                    });
                    navItem.isHidden = true;
                }
            },
            modifyOwnNav(params, id) {
                const navItem = this.navList.find((it) => {
                    return it.id === id;
                });
                navItem.jumpConfig = params.jumpConfig;
                navItem.name = params.name;
                const navCacheItem = this.navListCache.find((it) => {
                    return it.id === id;
                });
                navCacheItem.jumpConfig = params;
                this.$emit('save-success', 'navBar');
            },
            addOwnNavItem(params) {
                params.isHidden = params.isHidden === 1 ? false : true,
                this.navList.push(params);
                this.navListCache.push(params);
                this.$emit('save-success', 'navBar');
            },
            async postBatchMicroNavList() {
                this.loading = true;
                try {
                    const params = {
                        mcNavigationMenuReqs: this.navList.map((item, index) => {
                            return {
                                ...item,
                                sort: index,
                                isHidden: item.isHidden ? 0 : 1,
                            };
                        }),
                    };
                    const data = await McConfigAPI.postBatchMicroNavList(params);
                    if (data) {
                        this.$Toast({
                            message: '保存成功',
                            type: 'success',
                        });
                        this.navListCache = Clone(this.navList);
                        this.$emit('save-success', 'navBar');
                    }
                } catch (e) {
                    this.$Toast({
                        message: '保存失败',
                        type: 'error',
                    });
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            async deleteMicroNavList(item) {
                try {

                    const data = await McConfigAPI.deleteMicroNavList(item.id);
                    console.log('删除', data);
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    this.$emit('save-success', 'navBar');
                } catch (e) {
                    console.log(e);
                } finally {
                    this.navList = this.navList.filter((it) => it.id !== item.id);
                    this.navListCache = this.navListCache.filter((it) => it.id !== item.id);
                }
            },
            async fetchMicroNavList() {
                try {
                    const { rows = [] } = await McConfigAPI.fetchMicroNavList();
                    let adminChainStr = '总部';
                    // 如果是单店
                    if (this.isSingleStore) {
                        adminChainStr = '门店';
                    }
                    this.navList = rows.map((item) => {
                        let disableTips = '';
                        let disabled = false;
                        if (item.name === '就诊报告' && !item.isSupport) {
                            disabled = true;
                            disableTips = `请先至${adminChainStr}「微${this.$app.institutionTypeWording}」中开启就诊报告`;
                        }
                        if (item.name === '坐诊表' && !item.isSupport) {
                            disabled = true;
                            disableTips = `请先至${adminChainStr}「微${this.$app.institutionTypeWording}」中开启坐诊表医生坐诊表`;
                        }
                        if (item.name === '自助续方' && !item.isSupport) {
                            disabled = true;
                            disableTips = `请先至${adminChainStr}「自助续方」中开启在线自助续方`;
                        }
                        if (item.name === '在线咨询' && !item.isSupport) {
                            disabled = true;
                            disableTips = `请先至${adminChainStr}「网络问诊」中开启在线问诊`;
                        }
                        if (item.name === '理疗预约' && !item.isSupport) {
                            disabled = true;
                            disableTips = '请先至门店「预约设置」中开启治疗理疗预约';
                        }
                        if (item.name === '客服咨询' && !item.isSupport) {
                            disabled = true;
                            disableTips = '请先至门店「预约设置」中开启微信沟通';
                        }
                        if (item.name === '医保' && !item.isSupport) {
                            disabled = true;
                            disableTips = '请先至门店「医保 - 医保移动支付」中开启医保移动支付';
                        }
                        if (item.name === '服务项目' && !item.isSupport) {
                            disabled = true;
                            disableTips = '请先至门店「预约设置」中开启项目预约';
                        }
                        return {
                            ...item,
                            disabled,
                            disableTips,
                            isHidden: item.isHidden === 1 ? false : true,
                        };
                    }).sort((a, b) => {
                        return a.sort - b.sort;
                    });
                    this.navListCache = Clone(this.navList);
                } catch (e) {
                    console.log(e);
                }
            },
            handleCancelSave() {
                this.navList = Clone(this.navListCache);
            },
            handleDragMove() {
                return this.draggable;
            },
            addOwnNav(item = null) {
                console.log(item);
                this.ownName = '';
                this.navId = '';
                this.headerPageToHome = 0;
                this.sort = this.navList.length + 1;
                this.headerJumpUrl = '';
                this.jumpAppId = '';
                if (item) {
                    this.ownName = item.name;
                    this.navId = item.id;
                    this.headerPageToHome = item.jumpConfig?.headerPageToHome || 0;
                    this.sort = item.sort;
                    this.headerJumpUrl = item.jumpConfig?.path;
                    this.jumpAppId = item.jumpConfig.appId;
                }
                this.microSettingHeaderImgUrlDialogVisible = true;
            },
            deleteOwnNav(item) {
                this.$confirm({
                    type: 'warn',
                    title: '删除确认',
                    content: `是否确认删除导航“${item.name}”？删除后微诊所首页不再展示此入口`,
                    onConfirm: async () => {
                        await this.deleteMicroNavList(item);
                    },
                });

            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.introduction-home-nav {
    .nav-header-title {
        div {
            display: inline-block;
        }
    }

    .nav-bg-middle {
        padding: 24px 24px 0 24px;

        &_box {
            width: 100%;
            height: auto;

            &--title {
                display: flex;
                justify-content: space-between;
                height: 32px;
                padding: 0 12px;
                font-size: 14px;
                line-height: 32px;
                color: #8d9aa8;
                background: #f5f7fb;
                box-shadow: inset 0 1px 0 0 $P6, inset 0 -1px 0 0 $P6;
            }

            &--line {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 32px;
                padding: 0 12px;
                line-height: 32px;
                color: $T2;
                border-bottom: 1px solid $P6;

                &-label {
                    &-modify {
                        display: none;
                    }
                }

                &:hover {
                    cursor: pointer;
                    background-color: $P4;

                    .nav-bg-middle_box--line-label-modify {
                        display: inline-flex;
                    }
                }

                &-add {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 32px;
                    padding: 0 12px;
                    line-height: 32px;
                    color: $T2;
                    border-bottom: 1px solid $P6;
                }

                &-switch {
                    display: flex;
                    align-items: center;
                    height: 32px;
                }
            }
        }
    }
}
</style>
