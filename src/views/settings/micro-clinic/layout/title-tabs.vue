<template>
    <div class="title-tabs">
        <div
            v-for="(item,index) in titleTabs"
            :key="index"
            class="title-name"
            :class="{ 'title_name': currentId === index }"
            @click="checktabs(item,index)"
        >
            {{ item.name }}
        </div>
    </div>
</template>

<script>
    export default {
        name: 'TitleTabs',
        props: {
            titleTabs: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            currentId: Number,
        },
        methods: {
            checktabs(item,index) {
                this.$emit('checktabs',item,index);
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.title-tabs {
    display: flex;
    width: 100%;
    height: 36px;

    .title-name {
        height: 36px;
        padding: 10px 10px;
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        color: $T2;
        text-align: center;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);

        &:not(:first-child) {
            margin-left: 6px;
            cursor: pointer;
        }
    }

    .title_name {
        color: #005ed9;
        background: #f0f7ff;
        border: 1px solid #3495ff;
    }
}
</style>
