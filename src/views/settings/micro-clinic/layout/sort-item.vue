<template>
    <div :class="[`sort-item-wrapper sort_item_wrapper_${lineRight}`,{ 'sort_item_wrapper_move': !isHiddenMove }]" :style="contentStyle">
        <abc-icon
            v-for="(icon, index) in iconsList"
            :key="icon.name + index"
            :icon="icon.name"
            :color="icon.color"
            :style="icon.style"
            :size="12"
        ></abc-icon>
        <span :title="label">
            <span class="label-text" :class="{ 'label-text-overflow': label.length > 4 }">{{ label }}</span>
        </span>
        <div v-if="deletable" class="wc-bg-doctor-op" @click.stop="handleDeleteClick">
            <i></i>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'SortItem',
        props: {
            iconList: {
                type: Array,
                default: () => [{
                    name: 'people', color: '#7a8794', size: '12',
                }],
            },
            contentStyle: {
                type: String,
                default: '',
            },
            lineRight: {
                type: [ String, Number ],
                default: '6',
            },
            label: {
                type: String,
                default: '没有数据',
            },
            isShowTip: {
                type: Boolean,
                default: true,
            },
            isHiddenMove: { // 是否隐藏移动图标
                type: Boolean,
                default: false,
            },

            // @desc 是否可删除
            deletable: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            iconsList() {
                if (this.isHiddenMove) {
                    return this.iconList.filter((item) => {return item.name !== 'move_item';});
                }
                return this.iconList;
            },
        },

        methods: {
            handleDeleteClick() {
                this.$emit('delete');
            },
        },
    };
</script>

<style lang="scss" scoped>
    @import 'src/styles/theme.scss';
    @import '~styles/abc-common.scss';

    .sort-item-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        width: 88px;
        height: 28px;
        padding: 6px 6px 6px 12px;
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;
        background-color: #ffffff;
        border: 1px solid $P1;

        &:hover {
            .wc-bg-doctor-op {
                display: inline-block;
            }
        }

        &:last-child {
            margin-right: 0;
        }

        &.draggable {
            cursor: move;
        }

        .iconfont {
            margin-right: 6px;
            font-size: 12px;
            color: #73a8e2;
        }

        .label-text {
            font-size: 12px;
            line-height: 16px;
            color: $T1;
            cursor: pointer;
        }

        .label-text-overflow {
            @include ellipsis;
        }

        .wc-bg-doctor-op {
            position: absolute;
            top: -8px;
            right: -8px;
            display: none;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            cursor: pointer;
            background: url('~assets/images/<EMAIL>') no-repeat center;
            background-size: contain;
        }
    }

    .sort_item_wrapper_move {
        width: 100px;
        padding: 6px 4px;
    }

    .sort_item_wrapper_6 {
        &:nth-child(6n) {
            margin-right: 0 !important;
        }
    }

    .sort_item_wrapper_4 {
        &:nth-child(4n) {
            margin-right: 0 !important;
        }
    }
</style>
