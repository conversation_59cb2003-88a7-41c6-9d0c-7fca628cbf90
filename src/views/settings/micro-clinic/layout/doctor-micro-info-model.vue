<template>
    <div class="doctor-micro-info-model" :class="{ 'doctor_micro_info_model': isShowModelDownLine }">
        <div class="doctor-micro-info-model-left">
            <img v-if="doctorInfo.practiceImgUrl" :src="doctorInfo.practiceImgUrl" />
            <img v-else src="~assets/images/announcement/doctor_default.png" />
            <div v-if="isConsultation" class="consultation-sign">
                最近咨询
            </div>
        </div>
        <div class="doctor-micro-info-model-right">
            <div class="model-right-title">
                <div class="right-title-text">
                    <div class="right-title-text-name">
                        {{ doctorInfo.name }}
                    </div>
                    <div v-if="doctorInfo.practiceInfo" class="right-title-text-position">
                        {{ (doctorInfo.practiceInfo[0] && doctorInfo.practiceInfo[0].title.split('|')[1]) || '' }}
                    </div>
                </div>
                <div v-if="isAppointment" class="right-title-sign" :class="{ 'title-sign-green': doctorInfo.status === 1 }">
                    {{ doctorInfo.status | getStatus }}
                </div>
                <div v-else class="right-title-sign" :class="{ 'title-sign-green': doctorInfo.status }">
                    {{ doctorInfo.status ? '在线' : '离线' }}
                </div>
            </div>
            <div v-if="doctorInfo.introduction" class="model-right-box">
                {{ doctorInfo.introduction }}
            </div>
            <div v-else class="model-right-box">
                暂无介绍
            </div>
            <div v-if="doctorInfo.doctorTags && doctorInfo.doctorTags.length" class="model-label">
                <div v-for="(item,index) in doctorInfo.doctorTags" :key="index" class="model-label-tags">
                    {{ item }}
                </div>
            </div>
            <div class="consultation-price">
                <div v-if="doctorInfo.fee" class="price-cost">
                    <abc-money :value="doctorInfo.fee" :is-show-space="true" :is-format-money="false"></abc-money>
                </div>

                <div v-if="doctorInfo.fee" class="consultation-price-cost">
                    图文咨询
                </div>

                <div v-if="!doctorInfo.fee" class="consultation-price-free">
                    免费咨询
                </div>
                <div v-if="doctorInfo.onlineReplyCount || doctorInfo.avgReplyTime" class="consultation-price-other">
                    <span v-if="doctorInfo.onlineReplyCount">{{ doctorInfo.onlineReplyCount }}个回答 </span>
                    <span
                        v-if="doctorInfo.avgReplyTime"
                        :style="{
                            marginLeft: '16px',
                        }"
                    >{{ doctorInfo.avgReplyTime }}分钟响应</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'DoctorMicroInfoModel',
        filters: {
            getStatus(status) { //status 状态；0：有号；1：约满；2无号；3：未放号；4：完诊；5：无排班
                switch (status) {
                    case 0:
                        return '有号';
                    case 1:
                        return '约满';
                    case 2:
                        return '无号';
                    case 3:
                        return '未放号';
                    case 4:
                        return '完诊';
                    case 5:
                        return '未排班';
                    default:
                        return '-';
                }

            },
        }, // 微诊所医生模版
        props: {
            doctorInfo: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            isShowTitleSign: Boolean,
            isShowModelDownLine: Boolean,
            isConsultation: Boolean,
            isAppointment: Boolean,
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.doctor-micro-info-model {
    display: flex;
    width: 288px;
    min-height: 118px;
    max-height: auto;
    padding: 16px 0 16px 0;
    margin-right: 16px;
    margin-left: 16px;

    .doctor-micro-info-model-left {
        position: relative;
        width: 56px;
        height: auto;

        img {
            z-index: 100;
            width: 56px;
            height: 56px;
            border-radius: var(--abc-border-radius-small);
        }

        .consultation-sign {
            position: absolute;
            top: 46px;
            z-index: 999;
            width: 56px;
            height: 18px;
            font-size: 12px;
            line-height: 18px;
            color: #ffffff;
            text-align: center;
            background: $G2;
            border-radius: 2px;
        }
    }

    .doctor-micro-info-model-right {
        width: calc(100% - 68px);
        height: auto;
        margin-left: 12px;

        .model-right-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 24px;

            .right-title-text {
                display: flex;
                align-items: center;
                width: auto;
                height: 24px;

                .right-title-text-name {
                    max-width: 60px;
                    height: 24px;
                    overflow: hidden;
                    font-size: 15px;
                    font-weight: bold;
                    line-height: 24px;
                    color: #333333;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .right-title-text-position {
                    height: 20px;
                    margin-left: 8px;
                    font-size: 13px;
                    line-height: 20px;
                    color: #777777;
                }
            }

            .right-title-sign {
                width: auto;
                max-width: 62px;
                height: 22px;
                padding: 0 12px;
                font-size: 12px;
                line-height: 20px;
                color: #7a8794;
                border: 1px solid $P4;
                border-radius: 11px;
            }

            .title-sign-green {
                color: $G2;
                border: 1px solid $G2;
            }
        }

        .model-right-box {
            display: -webkit-box;
            width: 100%;
            max-height: 32px;
            margin-top: 4px;
            overflow: hidden;
            font-size: 12px;
            line-height: 16px;
            color: #aaabb3;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }

        .model-label {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            width: 100%;
            height: auto;
            min-height: 18px;
            margin-top: 8px;

            .model-label-tags {
                width: auto;
                height: 18px;
                padding-right: 3px;
                padding-left: 3px;
                margin-top: 4px;
                margin-right: 4px;
                font-size: 12px;
                font-weight: 400;
                color: #c66e4e;
                background: #fff5f1;
                border-radius: 2px;

                &:first-child {
                    margin-left: 0;
                }
            }
        }

        .consultation-price {
            display: flex;
            align-items: center;
            height: 16px;
            margin-top: 16px;

            .price-cost {
                height: 22px;
                font-size: 16px;
                font-weight: bold;
                line-height: 22px;
                color: #c66e4e;
            }

            .consultation-price-cost {
                height: 16px;
                margin-left: 4px;
                font-size: 12px;
                line-height: 16px;
                color: #777777;
            }

            .consultation-price-free {
                font-size: 14px;
                font-weight: bold;
                line-height: 16px;
                color: $G2;
            }

            .consultation-price-other {
                height: 14px;
                padding-left: 8px;
                margin-left: 8px;
                font-size: 12px;
                line-height: 14px;
                color: #333333;
                border-left: 1px solid #f0f0f0;
            }
        }
    }
}

.doctor_micro_info_model {
    border-bottom: 1px solid #f0f0f0;
}
</style>
