<template>
    <div v-abc-loading="loading" :class="['abc-file-uploader-wrapper', editable ? '' : 'disabled']" :style="customStyle">
        <input
            v-if="editable"
            type="file"
            :accept="accept"
            title=""
            @change="handleFileChange"
        />
        <template v-if="fileUrl">
            <img
                class="preview-img"
                :src="fileUrl"
                alt=""
                @click="handlePreview"
            />
            <i v-if="editable" class="iconfont cis-icon-delete_file" @click="handleFileDelete"></i>
        </template>

        <slot v-if="!fileUrl && !loading">
            <div class="upload-icon-wrapper">
                <i class="iconfont cis-icon-plus"></i>
                <em>上传文件</em>
            </div>
        </slot>
    </div>
</template>

<script>
    export default {
        name: 'FileUploader',
        model: {
            prop: 'fileUrl',
        },
        props: {
            fileUrl: {
                type: String,
                default: '',
            },
            /**
             * @desc 是否可编辑，关闭后，不能删除，不能上传
             * <AUTHOR>
             * @date 2020/12/04 11:03:19
             * @params
             * @return
             */
            editable: {
                type: Boolean,
                default: true,
            },

            accept: {
                type: String,
                default: 'image/*',
            },

            customStyle: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                loading: false,
            };
        },
        methods: {
            closeLoading(bool) {
                this.loading = bool;
            },

            async handleFileChange(e) {
                if (!this.editable) {
                    return;
                }
                if (!e.currentTarget.files.length) return;
                const fileName = e.currentTarget.files[0].name;
                const index = fileName.lastIndexOf('.');
                const ext = fileName.substr(index + 1).toLowerCase();
                if (this.fileType && this.fileType.length > 0 && this.fileType.indexOf(ext) === -1) {
                    this.$emit('error', this.id, '不支持该文件类型');
                    return;
                }

                this.file = e.target.files[0];
                this.$emit('after-read', this.file, this.closeLoading.bind(this));
            },

            handlePreview() {
                this.$emit('preview', this.fileUrl);
            },

            handleFileDelete() {
                this.$el.querySelector('input').value = null;
                this.$emit('delete', this.fileUrl);
            },
        },
    };
</script>
<style lang="scss">
    @import '~styles/theme';

    .abc-file-uploader-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        cursor: pointer;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);

        &.disabled {
            .upload-icon-wrapper {
                cursor: not-allowed;
            }
        }

        input {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
        }

        .upload-icon-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #96a4b3;
            user-select: none;

            em {
                margin-top: 6px;
                font-size: 12px;

                & + em {
                    margin-top: 0;
                }
            }
        }

        img.preview-img {
            position: absolute;
            width: calc(100% - 4px);
            height: calc(100% - 4px);
            cursor: pointer;
            object-fit: cover;
        }

        i.cis-icon-delete_file {
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 16px;
            color: $P1;
            cursor: pointer;
        }
    }
</style>
