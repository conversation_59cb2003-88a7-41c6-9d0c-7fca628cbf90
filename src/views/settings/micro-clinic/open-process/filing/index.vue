<template>
    <div class="filing-step_box">
        <div v-if="showWaitFilingNotice" class="filing-step_box--text">
            因腾讯及通管局要求，所有小程序必须备案，请前往微信公众平台完成备案
        </div>
        <div v-else class="filing-step_box--text">
            <div class="filing-step_box--text-title">
                <abc-icon
                    v-if="filingsConfig.warnIcon"
                    :icon="filingsConfig.warnIcon"
                    size="16"
                    :color="filingsConfig.warnIconColor"
                    style="margin-right: 4px;"
                >
                </abc-icon>
                {{ filingsConfig.title }}
            </div>
            <div :style="filingsConfig.style">
                <span v-html="filingsConfig.desc"></span>
                <abc-button
                    v-if="[3, 5].includes(filingsStatus)"
                    style="margin-left: 4px;"
                    type="text"
                    @click="lookReason"
                >
                    常见驳回原因处理
                </abc-button>
                <abc-button
                    v-if="filingsStatus === 4"
                    style="margin-left: 4px;"
                    type="text"
                    @click="handleMethod"
                >
                    短信核验常见问题
                </abc-button>
            </div>
            <div v-if="[OpenProcessStatusCode.FILING_REFUSE, OpenProcessStatusCode.FILING_REFUSE_BY_ADMIN].includes(filingsStatus) && filingTable.length" class="filing-step_box--text-table">
                <div class="filing-step_box--text-table-th">
                    <div class="filing-th">
                        修改字段
                    </div>
                    <div class="filing-th">
                        待完善原因
                    </div>
                    <div class="filing-th-flex">
                        修改建议
                    </div>
                </div>
                <div class="filing-step_box--text-table-tr-body">
                    <div v-for="(item, index) in filingTable" :key="index" class="filing-step_box--text-table-tr">
                        <div class="filing-tr">
                            {{ item.text }}
                        </div>
                        <div class="filing-tr">
                            {{ item.reason }}
                        </div>
                        <div class="filing-tr-flex" v-html="item.notice">
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="filingsStatus === OpenProcessStatusCode.FILING_WAIT_CHECK_BY_ADMIN" class="filing-step_box--text-desc">
                <div class="filing-step_box--text-desc-title">
                    短信核验方法
                </div>
                <div class="filing-step_box--text-desc-info">
                    <div class="filing-step_box--text-desc-info-tab" style="width: 372px; border-right: 1px solid #e6eaee;">
                        <div class="filing-step_box--text-desc-info-tab-title">
                            1.收到短信，<abc-button type="text" @click="openCheckMsg">
                                打开核验链接
                            </abc-button>
                        </div>
                        <img style="width: 324px; height: 124px; margin: 21px auto;" src="//static-common-cdn.abcyun.cn/img/weapp/open/bg-check_1.png" />
                    </div>
                    <div class="filing-step_box--text-desc-info-tab" style="width: 390px;">
                        <div class="filing-step_box--text-desc-info-tab-title">
                            2.填写信息并提交
                        </div>
                        <img style="width: 300px; height: 149px; margin: 8px auto;" src="//static-common-cdn.abcyun.cn/img/weapp/open/bg-check_2.png" />
                    </div>
                </div>
            </div>
            <div v-if="filingsConfig.tips" class="filing-step_box--text-tips">
                {{ filingsConfig.tips }}
            </div>
        </div>
    </div>
</template>

<script>
    import { OpenProcessStatusCode } from 'src/views/settings/micro-clinic/constant';
    import {
        fetchFilingRefuseTable, lookReason,
    } from 'utils/handle-we-clinic-open-process';
    import { windowOpen } from '@/core/navigate-helper';
    export default {
        name: 'index.vue',
        props: {
            filingsStatus: Number,
        },
        data() {
            return {
                filingTable: [],
                OpenProcessStatusCode,
            };
        },
        computed: {
            showWaitFilingNotice() {
                return ![OpenProcessStatusCode.FILING_REFUSE, OpenProcessStatusCode.FILING_REFUSE_BY_ADMIN, OpenProcessStatusCode.FILING_WAIT_CHECK, OpenProcessStatusCode.FILING_WAIT_CHECK_BY_ADMIN].includes(this.filingsStatus);
            },
            filingsConfig() {
                const config = {
                    title: '',
                    warnIcon: false,
                    desc: '',
                    style: {},
                    tips: '',
                };
                switch (this.filingsStatus) {
                    case 2:
                        config.title = '微信平台初审中';
                        config.warnIcon = 'time2';
                        config.warnIconColor = '#FF9933';
                        config.desc = '微信平台将会在1-2个工作日内完成初审（具体请以实际审核时间为准），审核结果将以站内信、模版消息等形式通知管理员。在平台初审的过程，请备案相关人员保持电话畅通，以便平台对你提交的备案信息进行核验。';
                        break;
                    case 3:
                        config.title = '微信平台初审驳回';
                        config.warnIcon = 'delete_file';
                        config.warnIconColor = '#FF3333';
                        config.desc = '请前往微信小程序后台【设置-基本设置-小程序备案】，按修改建议完善备案资料后，重新提交备案<br/>若遇到困难请查看';
                        config.style = {
                            textAlign: 'center',
                        };
                        break;
                    case 4:
                        config.title = '通管局审核中';
                        config.warnIcon = 'time2';
                        config.warnIconColor = '#FF9933';
                        config.desc = '请注意查收工信部发送的核验短信（发送号码：12381），请在收到短信的24小时内完成短信核验，否则将被<br/>自动驳回<br/>若遇到困难请查看';
                        config.tips = 'Tips:短信核验提交成功后，通管局将在1～20工作日完成审核，审核通过后，将短信下发的小程序备案号，代表你的小程序已完成备案';
                        break;
                    case 5:
                        config.title = '通管局审核驳回';
                        config.warnIcon = 'delete_file';
                        config.warnIconColor = '#FF3333';
                        config.desc = '请登录小程序后台【首页-设置-小程序备案】，按修改建议完善备案资料后，重新提交备案<br/>若遇到困难请查看';
                        config.style = {
                            textAlign: 'center',
                        };
                        break;
                    default:
                        break;
                }
                return config;
            },
        },
        async created() {
            if ([OpenProcessStatusCode.FILING_REFUSE, OpenProcessStatusCode.FILING_REFUSE_BY_ADMIN].includes(this.filingsStatus)) {
                await this.fetchFilingRefuseTable();
            }
        },
        methods: {
            lookReason,
            async fetchFilingRefuseTable() {
                this.filingTable = await fetchFilingRefuseTable();
            },
            handleMethod() {
                windowOpen('https://developers.weixin.qq.com/miniprogram/product/record/sms_verification.html');
            },
            openCheckMsg() {
                windowOpen('https://beian.miit.gov.cn');
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.filing-step_box {
    display: flex;
    justify-content: space-around;
    width: 100%;
    height: auto;
    padding: 53px 76px 0 73px;
    font-size: 14px;
    line-height: 20px;
    color: $T1;
    text-align: center;

    &--text {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;

        &-title {
            height: 22px;
            margin-bottom: 17px;
            font-size: 16px;
            font-weight: bold;
            line-height: 22px;
        }

        &-table {
            width: 100%;
            max-height: 360px;

            &-tr {
                display: flex;
                width: 100%;
                padding: 9px 0;
                border-bottom: 1px solid $P6;

                &-body {
                    width: calc(100% + 10px);
                    max-height: 320px;
                    overflow-y: scroll;
                }

                .filing-tr {
                    width: 200px;
                    padding: 0 12px;
                    font-size: 14px;
                    line-height: 21px;
                    color: $T1;

                    &-flex {
                        flex: 1;
                        width: auto;
                        padding: 0 12px;
                        font-size: 14px;
                        line-height: 21px;
                        color: $T1;
                    }
                }
            }

            &-th {
                display: flex;
                width: 100%;
                min-height: 40px;
                background: #f5f7fb;
                border: 1px solid $P6;
                border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;

                .filing-th {
                    width: 200px;
                    padding: 0 12px;
                    font-size: 14px;
                    line-height: 40px;
                    color: $T2;

                    &-flex {
                        flex: 1;
                        width: auto;
                        padding: 0 12px;
                        font-size: 14px;
                        line-height: 40px;
                        color: $T2;
                    }
                }
            }
        }

        &-img {
            width: 762px;
            height: 273px;
            margin-top: 24px;
            border: 1px solid $P6;
        }

        &-tips {
            margin-top: 16px;
            font-size: 14px;
            line-height: 20px;
            color: $T2;
            text-align: left;
        }

        &-desc {
            width: 762px;
            height: 273px;
            padding: 24px 0;
            margin-top: 24px;
            background: #f5f7fb;
            border: 1px solid $P6;

            &-title {
                font-size: 14px;
                line-height: 20px;
                color: $T1;
                text-align: center;
            }

            &-info {
                display: flex;
                width: 100%;
                margin-top: 24px;

                &-tab {
                    &-title {
                        font-size: 14px;
                        line-height: 20px;
                        color: $T1;
                        text-align: center;
                    }
                }
            }
        }
    }
}
</style>
