<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="创建小程序"
        content-styles="padding:24px 24px;width: 900px;max-width: 900px;"
        custom-class="create-we-clinic_dialog"
    >
        <div v-abc-loading="dialogLoading">
            <step-title :step="step"></step-title>
            <div v-if="step === 0" class="wechat-authorization-services-box-default"></div>
            <div v-else-if="step === 1" class="wechat-authorization-services-box">
                <div class="wechat-auth-dialog-box">
                    <div v-if="authReject" class="wechat-auth-dialog-box-tips">
                        <div class="wechat-auth-dialog-box-tips--text">
                            <abc-icon
                                icon="Attention"
                                color="#ff9933"
                                size="12"
                                style="margin-top: 4px; margin-right: 6px;"
                            ></abc-icon>授权审核失败，请按照要求修改材料后重新提交<abc-button type="text" style="margin-left: 6px;" @click="$emit('viewAuditFailReason')">
                                查看原因
                            </abc-button>
                        </div>
                    </div>
                    <abc-form ref="saveQualifyForm" item-block>
                        <div class="wechat-auth-wrapper-input-name">
                            小程序名称
                        </div>
                        <div class="wechat-auth-wrapper-input">
                            <abc-form-item
                                ref="wechatAuth"
                                style="width: 240px; margin-right: 0; margin-bottom: 0;"
                                :validate-event="validateWechatAuthName"
                            >
                                <abc-input
                                    v-model.trim="weappQualify.nickName"
                                    :max-length="15"
                                    trim
                                    :width="240"
                                    :disabled="!(!!weappQualify.enableUpdateNickName)"
                                    placeholder="最多可填写15字，不可填写符号"
                                >
                                </abc-input>
                            </abc-form-item>
                            <abc-button type="text" style="margin-left: 8px;" @click="readAuthNameRules">
                                查看命名规范
                            </abc-button>
                        </div>
                        <div class="wechat-auth-wrapper-input-name" style="margin-top: 24px;">
                            上传资质
                        </div>
                        <div class="wechat-auth-wrapper-info">
                            <div class="wechat-auth-wrapper" style="margin-left: 0;">
                                <file-uploader
                                    v-model="weappQualify.medicalLicenseUrl"
                                    :editable="!!weappQualify.enableUploadMedicalLicense"
                                    accept="image/jpeg,image/jpg,image/png"
                                    @after-read="(file, fn) => handleFileRead(file, 'medicalLicenseUrl', fn)"
                                    @preview="handlePreview"
                                    @delete="handleFileDelete('medicalLicenseUrl')"
                                >
                                    <div class="wechat-auth-wrapper-upload">
                                        <abc-icon
                                            color="#d9dbe3"
                                            :size="14"
                                            :icon="'plus'"
                                            class="wechat-auth-wrapper-upload-icon"
                                        ></abc-icon>
                                        <div class="wechat-auth-wrapper-upload-tips">
                                            需带鲜章，主体和公众号需一致
                                        </div>
                                    </div>
                                </file-uploader>
                                <div class="wechat-auth-wrapper-text">
                                    《医疗资质许可证》
                                </div>
                            </div>
                            <div class="wechat-auth-wrapper">
                                <file-uploader
                                    v-model="weappQualify.businessLicenseUrl"
                                    :editable="!!weappQualify.enableUploadBusinessLicense"
                                    accept="image/jpeg,image/jpg,image/png"
                                    @after-read="(file, fn) => handleFileRead(file, 'businessLicenseUrl', fn)"
                                    @preview="handlePreview"
                                    @delete="handleFileDelete('businessLicenseUrl')"
                                >
                                    <div class="wechat-auth-wrapper-upload">
                                        <abc-icon
                                            color="#d9dbe3"
                                            :size="14"
                                            :icon="'plus'"
                                            class="wechat-auth-wrapper-upload-icon"
                                        ></abc-icon>
                                        <div class="wechat-auth-wrapper-upload-tips">
                                            需带鲜章，主体和公众号需一致
                                        </div>
                                    </div>
                                </file-uploader>
                                <div class="wechat-auth-wrapper-text">
                                    《营业执照》
                                </div>
                            </div>
                        </div>
                        <div class="wechat-authorization-tips">
                            <abc-button type="text" @click="setUpLoadSureFlag">
                                上传补充材料
                            </abc-button><span style="margin-left: 8px; color: #7a8794;">若上述证件主体不同，需提供材料证明其属于同一责任人</span>
                        </div>
                        <div v-if="upLoadSureFlag || weappQualify.additionalQualifies" class="file-item">
                            <abc-button v-if="weappQualify.additionalQualifies" class="file-btn has-file">
                                <abc-image
                                    :src="weappQualify.additionalQualifies"
                                    oss-style-name="licenseCompress"
                                    :width="38"
                                    :height="32"
                                    class="file-img"
                                    @click="showImg(weappQualify.additionalQualifies)"
                                ></abc-image>
                                <span @click="showImg(weappQualify.additionalQualifies)">证明材料</span>
                                <abc-button type="text" class="re-upload">
                                    重传
                                    <input
                                        type="file"
                                        :class="key"
                                        accept="image/*"
                                        title=""
                                        @change="reUpload"
                                    />
                                </abc-button>
                                <abc-button
                                    type="text"
                                    class="delete-file"
                                    @click.stop="handleFileDelete('additionalQualifies')"
                                >
                                    删除
                                </abc-button>
                            </abc-button>
                            <!--未上传图片-->
                            <abc-button v-else type="blank" class="file-btn no-file">
                                <abc-progress :percentage="0"></abc-progress>
                                <span class="plus-wrapper">
                                    <img src="~/assets/images/<EMAIL>" class="plus" />
                                </span>
                                <span>请上传证明材料</span>
                                <input
                                    type="file"
                                    :class="key"
                                    accept="image/*"
                                    title=""
                                    @change="fileChange"
                                />
                            </abc-button>
                        </div>
                    </abc-form>
                </div>
            </div>
            <div v-else-if="step === 2" class="wechat-authorization-services-box-step2">
                {{ auditText }}
            </div>
            <div v-else-if="step === 3" class="wechat-authorization-services-box-step3">
                <authentication-box :current-step="authStep"></authentication-box>
            </div>
            <div v-else-if="step === 4" class="wechat-authorization-services-box-step4">
                <filing-box :filings-status="filingsStatus"></filing-box>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <div v-if="step === 1" class="footer-box">
                <div class="wechat-auth-sure">
                    <abc-checkbox
                        v-model="readingAgreement"
                        type="number"
                    >
                        <span style="color: #7a8794;">已阅读</span>
                    </abc-checkbox>
                    <div class="wechat-auth-sure-agreement" @click="isShowProtocolFlag = true">
                        《微{{ $app.institutionTypeWording }}服务开通协议》
                    </div>
                </div>
                <div class="footer-button">
                    <abc-button
                        :disabled="createWeappDisabled"
                        :loading="loading"
                        @click="saveQualify"
                    >
                        下一步
                    </abc-button>
                </div>
            </div>
            <template v-else-if="step === 2">
                <abc-button @click="step = 1">
                    上一步
                </abc-button>
                <abc-button :disabled="auditing" @click="handleWeClinic">
                    去授权
                </abc-button>
            </template>
            <template v-else-if="step === 3">
                <abc-button v-if="authStep === 1" @click="handleAuthentication">
                    前往认证
                </abc-button>
                <abc-button v-else-if="authStep === 2" @click="goToWechat">
                    前往查看进度
                </abc-button>
                <abc-button v-else-if="authStep === 3" @click="goToWechat">
                    前往重填资料
                </abc-button>
                <abc-button v-else @click="goToWechat">
                    前往重新认证
                </abc-button>
                <abc-button type="blank" @click="openAuthLead">
                    查看认证教程
                </abc-button>
            </template>
            <template v-else-if="step === 4">
                <abc-button v-if="![2, 3, 4, 5].includes(filingsStatus)" @click="handleFiling">
                    前往备案
                </abc-button>
                <abc-button v-else-if="[3, 5].includes(filingsStatus)" @click="goToWechat">
                    前往微信公众平台
                </abc-button>
                <abc-button type="blank" @click="openFilingLead">
                    查看备案教程
                </abc-button>
            </template>
        </div>
        <wc-protocol
            v-if="isShowProtocolFlag"
            :visible="isShowProtocolFlag"
            @visible="(val) => (isShowProtocolFlag = val)"
            @cancel="isShowProtocolFlag = false"
        ></wc-protocol>
    </abc-dialog>
</template>

<script>
    import WcProtocol from 'views/settings/micro-clinic/layout/wc-protocol.vue';
    import FileUploader from 'views/settings/micro-clinic/layout/file-uploader.vue';
    import { McComponentAPI } from 'views/settings/micro-clinic/core/mc-component-api.js';
    import clone from 'utils/clone';
    import AuthenticationBox from './authentication/index';
    import FilingBox from './filing/index';
    import StepTitle from './step-title';
    import {
        mapGetters,
    } from 'vuex';
    import {
        WeappStatus,
    } from 'views/we-clinic/data';
    import {
        geyMcWeappIcpEnTranceInfo, goToWechat, transWeappAuthStatus, openAuthLead, openFilingLead,
    } from 'utils/handle-we-clinic-open-process';
    import { windowOpen } from '@/core/navigate-helper';
    import { McConfigAPI } from 'views/settings/micro-clinic/core/mc-config-api.js';
    export default {
        name: 'OpenProcessDialog',
        components: {
            WcProtocol,
            FileUploader,
            AuthenticationBox,
            FilingBox,
            StepTitle,
        },
        props: {
            value: Boolean,
            isAuthed: Boolean,
            currentStep: {
                type: Number,
                default: 1,
            },
            isH5: Boolean,
        },
        data() {
            return {
                step: 0,
                weappQualify: {
                    businessLicenseUrl: '',
                    medicalLicenseUrl: '',
                    weappInfoAuditFailReason: '',
                    nickName: '',
                    enableUpdateNickName: 1,
                    enableUploadBusinessLicense: 1,
                    enableUploadMedicalLicense: 1,
                    additionalQualifies: '',
                },
                appInfoList: [],
                upLoadSureFlag: false,
                loading: false,
                readingAgreement: false,
                isShowProtocolFlag: false,
                WeappStatus,
                dialogLoading: false,
                filingsStatus: 1024,
            };
        },
        computed: {
            ...mapGetters('weClinic', ['weClinicType', 'mpStatus', 'weappStatus']),
            ...mapGetters(['currentClinic']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            createWeappDisabled() {
                return !this.readingAgreement || !this.weappQualify.nickName || !this.weappQualify.medicalLicenseUrl || !this.weappQualify.businessLicenseUrl;
            },
            auditing() {
                return this.weappStatus === WeappStatus.AUDITING;
            },
            // 小程序代码审核通过 40
            waitAuditPass() {
                return this.weappStatus < WeappStatus.AUDIT_PASS;
            },
            // 小程序认证 认证未成功 80
            waitAuthWeappPass() {
                return this.weappStatus < WeappStatus.AUTH_SUCCESS;
            },
            // 小程序备案 备案未成功 130
            waitFilingsPass() {
                return this.weappStatus < WeappStatus.FILING_ADMIN_SUCCESS;
            },
            // 成功上传了医疗资质
            haveWeappQualify() {
                // 小程序名称 资质 医疗许可证 缺一不可
                return this.weappQualify.nickName && this.weappQualify.businessLicenseUrl && this.weappQualify.medicalLicenseUrl;
            },
            // 小程序授权失败
            authReject() {
                return this.weappStatus === WeappStatus.AUDIT_REJECT;
            },
            authStep() {
                return transWeappAuthStatus(this.weappStatus);
            },
            auditText() {
                if (this.auditing) {
                    return '快速注册小程序成功，微信平台审核中，预计1-3个工作日';
                }
                return '请公众号管理员，点击「去授权」跳转微信公众平台，扫码后，完成快速注册小程序';
            },
        },
        async created() {
            try {
                this.dialogLoading = true;
                // 拉取授权信息
                await this.initWeappQualify();
                await this.fetchQualificationInfo();
                // 小程序必须授权过了
                if (!this.waitAuditPass) {
                    // 拉取备案信息
                    await this.geyMcWeappIcpEnTranceInfo(this.isH5);
                }
                this.step = this.currentStep;
                // 没有资质或授权被拒绝
                if (!this.haveWeappQualify || this.authReject) {
                    this.step = 1;
                    //资质已经上传 小程序代码未认证 即可进入认证流程
                } else if (this.haveWeappQualify && this.waitAuditPass) {
                    this.step = 2;
                    //  小程序授权通过了 认证未通过
                } else if (!this.waitAuditPass && this.waitAuthWeappPass) {
                    this.step = 3;
                    // 小程序认证通过了 备案未通过
                } else if (!this.waitAuthWeappPass && this.waitFilingsPass) {
                    this.step = 4;
                } else {
                    this.step = 5;
                }
            } catch (e) {
                console.log(e);
            } finally {
                this.dialogLoading = false;
            }

        },
        methods: {
            goToWechat,
            openAuthLead,
            openFilingLead,
            async fetchQualificationInfo() {
                try {
                    const data = await McConfigAPI.fetchQualificationInfo();
                    const certs = data?.certs || {};
                    const {
                        businessLicense = '', medicalOrigin = '',
                    } = certs;
                    this.weappQualify.medicalLicenseUrl = this.weappQualify.medicalLicenseUrl || medicalOrigin;
                    this.weappQualify.businessLicenseUrl = this.weappQualify.businessLicenseUrl || businessLicense;
                    // 反填入信息
                } catch (e) {
                    console.log(e);
                }
            },
            //  step5 start
            goToRecordGuidelines() {
                windowOpen(`//${window.location.host}/record-guidelines`);
            },
            async geyMcWeappIcpEnTranceInfo(isH5) {
                const res = await geyMcWeappIcpEnTranceInfo(isH5);
                this.filingsStatus = res?.filingsStatus || 0;
            },
            //  step5 end
            // step4 start
            async handleFiling() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '为了避免审核失败，备案前请务必认真阅读小程序备案教程',
                    confirmText: '查看备案教程',
                    cancelText: '我已知晓，直接前往',
                    onConfirm: async () => {
                        this.openFilingLead();
                    },
                    onCancel: async () => {
                        this.goToWechat();
                    },
                });
            },
            // step4 end
            // step3 start
            async handleAuthentication() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '认证前，请务必查看认证教程获取登陆账号，否则将无法完成认证',
                    confirmText: '查看认证教程',
                    cancelText: '已有账号，去登陆',
                    onConfirm: async () => {
                        this.openAuthLead();
                    },
                    onCancel: async () => {
                        this.goToWechat();
                    },
                });
            },
            // step3 end
            // step2 start
            async handleWeClinic() {
                await this.handleCreateWeappClick();
            },
            openCreateWeappAuthPage(url) {
                this.weappAuthWindow = windowOpen(url, '_blank', 'width=1360,height=920');
            },
            async openCreateWeappPage(data, needAuth = true) {
                try {
                    // 上传
                    await McComponentAPI.modifyWeappQualifyUsingPOST(data);
                    if (!needAuth) return;
                    // 获取微信授权页面 URL
                    const { authUrl } = await McComponentAPI.getFastRegisterAuthUrlUsingGET();
                    this.openCreateWeappAuthPage(authUrl);
                } catch (e) {
                    console.error('openCreateWeappPage.error', e);
                }
            },
            async handleCreateWeappClick() {
                const data = clone(this.weappQualify);
                data.additionalQualifies = data.additionalQualifies.split(' ');
                const needAuth = this.weappStatus === this.WeappStatus.NO_AUTH;
                await this.openCreateWeappPage(data, needAuth);
            },
            // step2 end
            // step1 start
            async fetchMpWeappInfo() {
                let res = [];
                try {
                    const { mpWeappViewList = [] } = await McComponentAPI.getAuthedMpWeappUsingGET();
                    res = mpWeappViewList;
                } catch (e) {
                    console.error('fetchMpWeappInfo.error', e);
                }
                return res;
            },
            async fetchWeappQualify() {
                try {
                    const data = await McComponentAPI.getWeappQualifyUsingGET();

                    if (!data.additionalQualifies) {
                        data.additionalQualifies = '';
                    } else {
                        data.additionalQualifies = data.additionalQualifies.join('');
                    }
                    return data;
                } catch (e) {
                    console.error('fetchWeappQualify.error', e);
                }
            },
            async initWeappQualify() {
                const [appInfoList, weappQualify] = await Promise.all([this.fetchMpWeappInfo(), this.fetchWeappQualify()]);
                this.appInfoList = appInfoList;
                this.weappQualify = weappQualify;
                // 如果小程序没有命名，默认给一个名称
                if (!this.weappQualify.nickName) {
                    this.weappQualify.nickName = this.currentClinic?.chainName || this.currentClinic?.clinicName || this.weappQualify.nickName;
                }
            },
            async uploadWeappQualify(data) {
                // 上传
                await McComponentAPI.modifyWeappQualifyUsingPOST(data);
            },
            async saveQualify() {
                this.$refs.saveQualifyForm.validate(async (val) => {
                    if (val) {
                        this.loading = true;
                        const data = clone(this.weappQualify);
                        data.additionalQualifies = data.additionalQualifies.split(' ');
                        const needAuth = this.weappStatus === this.WeappStatus.NO_AUTH;
                        try {
                            await this.uploadWeappQualify(data, needAuth);
                            // 上传资质成功开始扫码授权
                            this.$Toast({
                                type: 'success',
                                message: this.authReject ? '重新上传资质成功' : '上传资质成功',
                            });
                            this.step = 2;
                        } catch (e) {
                            this.$Toast({
                                type: 'error',
                                message: this.authReject ? '重新上传资质失败' : '上传资质失败',
                            });
                            console.error(e);
                        } finally {
                            this.loading = false;
                        }
                    }
                });
            },
            readAuthNameRules() {
                this.$emit('readAuthNameRules');
            },
            handlePreview(imageUrl) {
                this.$emit('handlePreview', imageUrl);
            },
            handleFileDelete(type) {
                this.weappQualify[type] = '';
            },
            setUpLoadSureFlag() {
                this.upLoadSureFlag = true;
            },
            showImg(url) {
                this.$emit('showImg', url);
            },
            reUpload(e) {
                this.fileChange(e);
            },
            async fileChange(e) {
                const file = e.target.files[0];
                try {
                    const res = await this.$abcPlatform.service.oss.clinicUsageUpload(
                        { filePath: 'weapp/certification' },
                        file,
                    );
                    this.weappQualify.additionalQualifies = res.url;
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: '上传失败，请重试',
                    });
                }
            },
            async handleFileRead(file, type, fn) {
                const MAX_SIZE = 1024 * 1024 * 20; // 最大限制20M
                if (file.size > MAX_SIZE) {
                    return this.$Toast({
                        type: 'error',
                        message: `上传${type === 'medicalLicenseUrl' ? '医疗资质许可证文件' : '营业执照文件'}不能超过20M`,
                    });
                }
                fn(true);
                try {
                    const res = await this.$abcPlatform.service.oss.clinicUsageUpload(
                        { filePath: 'weapp/certification' },
                        file,
                    );
                    this.weappQualify[type] = res.url;
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: '上传失败，请重试',
                    });
                } finally {
                    fn(false);
                }
            },
            // 校验小程序授权名称是否合法
            validateWechatAuthName(value) {
                if (/[^\u4e00-\u9fa5a-zA-Z0-9]/gi.test(value)) { //判断是不是有符号
                    this.$refs.wechatAuth.validateMessage = '小程序名称不可以包含字符';
                    this.$refs.wechatAuth.validateState = 'error';
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '小程序名称不可以包含字符',
                        });
                    };
                }
                return (_, callback) => {
                    callback({
                        validate: true,
                    });
                };
            },
            // step1 end
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.create-we-clinic_dialog {
    max-height: 700px;

    .wechat-authorization-services-box {
        height: 412px;

        &-default {
            height: 412px;
        }

        &-step2 {
            display: flex;
            justify-content: space-around;
            height: 412px;
            padding-top: 56px;
            font-size: 14px;
            line-height: 20px;
            color: $T1;
            text-align: center;
        }

        &-step3 {
            height: 412px;
            font-weight: bold;
            text-align: center;
        }

        &-step4 {
            min-height: 412px;
            font-weight: bold;
            text-align: center;
        }

        .wechat-auth-dialog-box {
            width: 100%;

            &-tips {
                display: flex;
                justify-content: space-around;
                width: 100%;
                height: 32px;
                margin-bottom: 16px;
                background-color: $Y4;
                border: 1px solid $Y5;
                border-radius: var(--abc-border-radius-small);

                &--text {
                    display: flex;
                    align-items: center;
                    height: 32px;
                    margin-bottom: 4px;
                    line-height: 32px;
                    color: $Y2;
                }
            }

            .wechat-auth-wrapper-info {
                display: flex;
                width: auto;
                height: 180px;
                margin-top: 8px;
                text-align: left;

                .wechat-auth-wrapper {
                    position: relative;
                    width: 240px;
                    height: 180px;
                    margin-left: 16px;

                    .wechat-auth-wrapper-text {
                        position: absolute;
                        bottom: -28px;
                        left: 57px;
                        width: 126px;
                        height: 20px;
                        font-size: 14px;
                        line-height: 20px;
                        color: $T2;
                        text-align: center;
                    }

                    :first-child {
                        margin-top: 0;
                        margin-left: 0;
                    }

                    &.custom {
                        width: 80px;
                        height: 30px;
                    }

                    & + .weapp-upload-wrapper {
                        margin-top: 24px;
                    }

                    .wechat-auth-wrapper-upload {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        width: 240px;
                        height: 180px;

                        .wechat-auth-wrapper-upload-icon {
                            margin-top: 64px;
                        }

                        .wechat-auth-wrapper-upload-text {
                            height: 20px;
                            margin-top: 12px;
                            font-size: 14px;
                            line-height: 20px;
                            color: $T2$T2;
                        }

                        .wechat-auth-wrapper-upload-tips {
                            height: 16px;
                            margin-top: 6px;
                            font-size: 12px;
                            line-height: 16px;
                            color: $T2;
                        }
                    }
                }
            }

            .wechat-auth-wrapper-input-name {
                height: 20px;
                font-size: 14px;
                font-weight: bold;
                line-height: 20px;
                color: #333333;
            }

            .wechat-authorization-tips {
                height: 20px;
                margin-top: 52px;
                font-size: 14px;
                line-height: 20px;
                color: $B1;
                cursor: pointer;
            }

            .wechat-auth-wrapper-input {
                display: flex;
                align-items: center;
                height: 40px;
                margin-top: 8px;
            }

            .file-item {
                position: relative;
                width: 100%;
                margin-top: 10px;

                &:hover {
                    .re-upload,
                    .delete-file {
                        display: inline-block;
                        vertical-align: middle;
                    }
                }

                .re-upload,
                .delete-file {
                    position: absolute;
                    top: 13px;
                    display: none;
                    min-width: 28px;
                    margin-left: 0;
                }

                .re-upload {
                    left: 412px;
                    cursor: pointer;

                    input {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        font-size: 0;
                        opacity: 0;

                        &::-webkit-file-upload-button {
                            cursor: pointer;
                        }
                    }
                }

                .delete-file {
                    left: 456px;
                    color: $R2;
                }

                .file-btn {
                    position: relative;
                    display: block;
                    width: 496px;
                    height: 40px;
                    padding: 0;
                    margin-top: 8px;
                    margin-left: 0;
                    font-size: 0;
                    text-align: left;
                    background: #ffffff;
                    border: 1px solid $P1;
                    border-radius: var(--abc-border-radius-small);

                    input {
                        cursor: pointer;
                    }

                    &:hover {
                        background-color: #fafafa;
                        border: 1px solid $T2;
                    }

                    img {
                        position: relative;
                        z-index: 1;
                        display: inline-block;
                        vertical-align: middle;
                        cursor: pointer;
                        animation: none;
                    }

                    .plus {
                        position: relative;
                        z-index: 1;
                        width: 12px;
                        height: 12px;
                    }

                    .file-img {
                        position: relative;
                        z-index: 1;
                        width: 38px;
                        height: 32px;

                        /* border: 1px solid #000000; */
                        margin: 2px 8px 2px 4px;
                        vertical-align: middle;
                    }

                    span {
                        position: relative;
                        z-index: 1;
                        display: inline-block;
                        height: 38px;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 38px;
                        color: $T3;
                        vertical-align: middle;
                    }

                    .abc-progress-wrapper {
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 38px;

                        .progress {
                            height: 38px;
                            background-color: #ccf0f0;
                            opacity: 1 !important;
                        }

                        .percentage {
                            height: 38px;

                            span {
                                display: none;
                            }
                        }
                    }
                }

                &:first-child {
                    .file-btn {
                        margin-top: 0;
                    }
                }

                .has-file {
                    span {
                        width: 266px;
                        color: $T1;
                    }
                }

                .no-file {
                    input {
                        position: absolute;
                        top: 0;
                        left: 0;
                        z-index: 2;
                        width: 100%;
                        height: 40px;
                        line-height: 40px;
                        opacity: 0;
                    }

                    .errorMsg {
                        position: absolute;
                        top: 0;
                        right: 12px;
                        color: #ff3366;
                    }

                    .plus-wrapper {
                        position: relative;
                        z-index: 1;
                        width: 50px;
                        height: 32px;
                        line-height: 32px;
                        text-align: center;
                    }
                }
            }
        }
    }

    .footer-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .wechat-auth-sure {
            display: flex;
            align-items: center;
            width: 240px;
            height: 16px;
            text-align: left;

            .wechat-auth-sure-agreement {
                height: 17px;
                margin-left: -8px;
                font-size: 14px;
                line-height: 17px;
                color: $B1;
                cursor: pointer;
            }
        }

        .footer-button {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: flex-end;
            width: auto;
            height: 100%;
        }
    }
}
</style>
