<template>
    <div class="open-process_step--title">
        <template
            v-for="(item, index) in stepList"
        >
            <div
                :key="index"
                class="open-process_step--title-name"
                :class="{ 'open-process_step--title-name-finish': item.step <= step }"
            >
                <abc-icon
                    size="14"
                    icon="chosen"
                    style="margin-right: 4px;"
                    :color="item.step <= step ? '#1EC761' : '#7A8794'"
                ></abc-icon>
                <span>{{ item.name }}</span>
            </div>
            <abc-icon
                v-if="item.step !== stepList.length"
                :key="`icon_${index}`"
                size="14"
                icon="Arrow_Rgiht_"
                color="#7A8794"
            ></abc-icon>
        </template>
    </div>
</template>

<script>
    export default {
        name: 'StepTitle',
        props: {
            step: Number,
            ownStepList: {
                type: Array,
                default: () => {
                    return [{
                                name: '上传资质',
                                step: 1,
                            },
                            {
                                name: '快速注册小程序',
                                step: 2,
                            },
                            {
                                name: '小程序认证',
                                step: 3,
                            },
                            {
                                name: '小程序备案',
                                step: 4,
                            },
                            {
                                name: '创建完成',
                                step: 5,
                            }];
                },
            },
        },
        data() {
            return {
                stepList: [

                ],
            };
        },
        created() {
            this.stepList = this.ownStepList;
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.open-process_step--title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 24px;
    padding: 0 20px;
    margin-bottom: 24px;

    &-name {
        color: #7a8794;

        &-finish {
            color: #08a446;
        }
    }
}
</style>
