<template>
    <div class="appointmentView" :class="{ 'appointmentView_line': isShowInputID === index }">
        <div class="appointmentView-img">
            <abc-icon icon="move_item" size="14" color="#7a8794"></abc-icon>
        </div>
        <abc-input
            v-model.trim="content.nel"
            class="appointmentView-text-input"
            max-length="1000"
            placeholder="输入自定义内容"
        ></abc-input>
        <div class="appointmentView-text">
            {{ contentCleanBlank === '' ? '输入自定义内容' : contentCleanBlank }}
        </div>
        <div class="appointmentView-modify">
            <abc-icon
                class="appointmentView-modify-icon"
                :icon="'trash'"
                :size="15"
                color="#B0B9C4"
                @click="deleteView"
            >
            </abc-icon>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'AppointmentView',
        props: {
            content: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            index: {
                type: Number,
                default: 0,
            },
            isShowInputID: { //通过当前Id是否匹配判断是否一直展示输入框
                type: Number,
                default: -1,
            },
        },
        computed: {
            contentCleanBlank() { //去掉空格的内容
                return this.content?.nel.replace(/\s*/g,'') || '';
            },
        },
        watch: {
            content: {
                handler(newValue) {
                    this.content.nel = newValue.nel.replace(/\s*/g,'');
                },
                immediate: true,
            },
        },
        methods: {
            deleteView() {
                this.$emit('deleteView',this.index);
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/theme.scss';
@import "src/styles/abc-common.scss";

.appointmentView {
    display: flex;
    align-items: center;
    width: 100%;
    height: 34px;
    padding: 0 8px;
    background: $S2;
    border-bottom: 1px solid #e6eaee;

    .appointmentView-img {
        width: 14px;
        height: 14px;

        img {
            width: 14px;
            height: 14px;
        }
    }

    .appointmentView-text {
        display: inline-block;
        width: calc(100% - 44px);
        height: 32px;
        margin-left: 8px;
        overflow: hidden;
        line-height: 32px;
        color: $T2;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .appointmentView-text-input {
        display: none;
    }

    .appointmentView-modify {
        display: none !important;
        width: 34px;
        height: 20px;

        .appointmentView-modify-icon {
            float: right;
            margin-top: 2px;
        }
    }

    &:hover {
        cursor: pointer;
        background-color: #eff3f6 !important;

        .abc-input-wrapper {
            width: calc(100% - 44px);
            margin-left: 8px;
        }

        .appointmentView-text-input {
            display: inline-block;
        }

        .appointmentView-modify {
            display: block !important;
        }

        ::v-deep .abc-input__inner {
            width: 100% !important;
            padding: 0 0;
            font-weight: 400;
            line-height: 20px;
            color: #333333;
            background-color: #eff3f6;
            border: none;

            &:hover {
                border: none;
            }

            &:focus {
                border: none;
                outline: none;
                box-shadow: none !important;
            }
        }

        .appointmentView-text {
            display: none;
            width: calc(100% - 44px);
            color: #333333;
        }
    }
}

.appointmentView_line {
    cursor: pointer !important;
    border: 1px solid #0370c9;
    box-shadow: 0 0 6px 0 #c4e0fe !important;

    &:hover {
        background-color: $S2 !important;

        .appointmentView-modify {
            display: none !important;
        }
    }

    .abc-input-wrapper {
        width: calc(100% - 44px) !important;
        margin-left: 8px !important;
    }

    .appointmentView-text-input {
        display: inline-block !important;
    }

    .appointmentView-modify {
        display: none !important;
    }

    ::v-deep .abc-input__inner {
        width: 100% !important;
        padding: 0 0;
        font-weight: 400;
        line-height: 20px;
        color: #333333;
        background-color: $S2 !important;
        border: none;

        &:hover {
            border: none;
        }

        &:focus {
            border: none;
            outline: none;
            box-shadow: none !important;
        }
    }

    .appointmentView-text {
        display: none;
        width: calc(100% - 44px);
        color: #333333;
    }
}
</style>
