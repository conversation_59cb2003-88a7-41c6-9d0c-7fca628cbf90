<template>
    <div class="payment-mobile-view">
        <div class="payment-mobile-view-nel">
            <img class="right-info-img" :src="themeList[miniClinicConfig.style]" />
            <div class="right-text-line">
                <div class="right-text-line-box-left">
                    <div>ABC诊所-高新医疗中心</div>
                    <div>四川省成都市高新区富华南路1666号保利<br />大厦1层</div>
                </div>
                <div class="right-text-line-box-right">
                    <abc-icon icon="Arrow_Rgiht" size="13" color="#D5DCE4"></abc-icon>
                </div>
            </div>
            <div class="right-text-line-info">
                <div>
                    药品加工
                </div>
                <div>
                    处方1人工煎药，处方2机器煎药
                    <abc-icon icon="Arrow_Rgiht" size="13" color="#D5DCE4"></abc-icon>
                </div>
            </div>
            <div class="right-text-line-info" :class="{ 'right_text_line_info': !isShowMerge }">
                <div>
                    配送费用
                </div>
                <div>
                    到付
                    <abc-icon icon="Arrow_Rgiht" size="13" color="#D5DCE4"></abc-icon>
                </div>
            </div>
            <div v-if="isShowMerge" class="right-text-line-merge">
                <div v-if="config.showRegistrationFee" class="merge-line">
                    <div>{{ $t('registrationFeeName') }}</div>
                    <div>1项</div>
                </div>

                <div v-if="config.showExaminationItem === 2" class="merge-line">
                    <div>检查检验</div>
                    <div>2项</div>
                </div>
                <div v-if="config.showTreatItem === 2" class="merge-line">
                    <div>{{ treatmentText }}</div>
                    <div>3项</div>
                </div>
                <div v-if="config.showNursingItem === 2" class="merge-line">
                    <div>护理费</div>
                    <div>1项</div>
                </div>
                <div v-if="config.showComposeItem === 2" class="merge-line">
                    <div>套餐</div>
                    <div>1项</div>
                </div>
                <div v-if="config.showDrugMaterialItem === 2" class="merge-line">
                    <div>药品材料</div>
                    <div>西药2种，中药2种，材料商品1种</div>
                </div>
                <div v-if="config.showOtherItem === 2" class="merge-line">
                    <div>其他</div>
                    <div>1项</div>
                </div>
            </div>
            <div v-if="config.showExaminationItem === 1" class="merge-line-info">
                <div class="merge-line-info-text">
                    <div>血脂四项</div>
                    <div>1项</div>
                </div>
            </div>
            <div v-if="config.showTreatItem === 1" class="merge-line-info">
                <div class="merge-line-info-text">
                    <div>推拿治疗</div>
                    <div>1次</div>
                </div>
            </div>
            <div v-if="config.showNursingItem === 1" class="merge-line-info">
                <div class="merge-line-info-text">
                    <div>脊椎穿刺</div>
                    <div>1项</div>
                </div>
            </div>
            <div v-if="config.showComposeItem === 1" class="merge-line-info">
                <div class="merge-line-info-text">
                    <div>综合调养套餐</div>
                    <div>1次</div>
                </div>
            </div>
            <div v-if="config.showDrugMaterialItem === 1" class="merge-line-info">
                <div class="merge-line-info-text">
                    <div>复方氨基酸注射液 500ml/瓶</div>
                    <div>1个</div>
                </div>
            </div>
            <div v-if="config.showDrugMaterialItem === 1" class="merge-line-info">
                <div class="merge-line-info-text">
                    <div>阿莫西林 1g*10片/盒</div>
                    <div>3片</div>
                </div>
                <div class="merge-line-info-text">
                    <div>医用棉签</div>
                    <div>1盒</div>
                </div>
            </div>
            <div v-if="config.showDrugMaterialItem === 1" class="merge-line-info">
                <div class="merge-line-info-nel">
                    <div>黄芪 2g</div><div>黄芪 2g</div><div>黄芪 2g</div>
                </div>
                <div class="merge-line-info-black">
                    共3剂，每剂2g，每剂1味
                </div>
            </div>
            <div v-if="config.showOtherItem === 1" class="merge-line-info">
                <div class="merge-line-info-text">
                    <div>输液费</div>
                    <div>1项</div>
                </div>
            </div>
            <div class="right-text-line-payment">
                <div class="line-payment-title">
                    支付信息
                </div>
                <div class="line-payment-info">
                    <div>总金额</div>
                    <div>50</div>
                </div>
                <div class="line-payment-info">
                    <div>营销卡项</div>
                    <div style="color: #ff3333;">
                        已使用两张
                        <img src="~assets/images/announcement/grayRightIcon.png" style="width: 10px; height: 10px;" />
                    </div>
                </div>
                <div class="line-payment-info">
                    <div>服务抵扣</div>
                    <div>-2.00</div>
                </div>
                <div class="line-payment-info">
                    <div>折扣优惠</div>
                    <div>-10.00</div>
                </div>
                <div class="line-payment-info-bottom">
                    <div>应付</div>
                    <div><abc-money :value="38" :symbol-icon-size="12"></abc-money></div>
                </div>
            </div>
            <div class="right-text-line-call">
                <div class="line-call">
                    <div>咨询时间</div><div>2018-12-09</div>
                </div>
                <div class="line-call">
                    <div>咨询医生</div><div>王清</div>
                </div>
                <div class="line-call">
                    <div>病情诊断</div>
                    <div>
                        急性肠胃炎
                    </div>
                </div>
                <div class="call">
                    <span>联系客服</span><img src="~assets/images/announcement/call.png" />
                </div>
            </div>
            <div class="right-text-button">
                <div :style="{ backgroundColor: themeColorList[miniClinicConfig.style] }">
                    支付 <abc-money :value="38"></abc-money>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import rechargeTitleImg from 'src/assets/images/announcement/rechargeTitle.png';
    import rechargeTitleBlueImg from 'src/assets/images/announcement/rechargeTitle-blue.png';
    import rechargeTitleYellowImg from 'src/assets/images/announcement/rechargeTitle-yellow.png';
    import rechargeTitleCyanImg from 'src/assets/images/announcement/rechargeTitle-cyan.png';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    export default {
        name: 'PaymentMobileView',
        props: {
            config: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                themeList: {
                    theme1: rechargeTitleImg,
                    theme2: rechargeTitleBlueImg,
                    theme3: rechargeTitleYellowImg,
                    theme4: rechargeTitleCyanImg,
                },
                themeColorList: {
                    theme1: '#D66A44',
                    theme2: '#377BD8',
                    theme3: '#EB8F3B',
                    theme4: '#1D8CA2',
                },
            };
        },
        computed: {
            ...mapState('weClinic',{
                miniClinicConfig: (state) => state.config,
            }),
            treatmentText() {
                const { microClinic } = getViewDistributeConfig().Settings;
                return microClinic.treatmentText;
            },
            isShowMerge() {
                return this.config && (this.config.showRegistrationFee ||
                    this.config.showExaminationItem === 2 ||
                    this.config.showTreatItem === 2 ||
                    this.config.showComposeItem === 2 ||
                    this.config.showDrugMaterialItem === 2 ||
                    this.config.showOtherItem === 2 ||
                    this.config.showNursingItem === 2
                );
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/theme.scss';
@import '~styles/mixin.scss';

.payment-mobile-view {
    display: flex;
    flex-direction: column;
    width: 332px !important;
    height: calc(100vh - 258px);
    overflow-y: scroll;

    @include scrollBar;

    .payment-mobile-view-nel {
        width: 320px;
        height: auto;

        .right-info-img {
            width: 100%;
        }

        .right-text-line {
            display: flex;
            width: 100%;
            height: 69px;
            margin-top: 0 !important;
            background-color: $S2;

            .right-text-line-box-left {
                width: 248px;

                div {
                    &:nth-child(1) {
                        width: 248px;
                        padding: 7px 13px;
                        font-size: 11px;
                        font-weight: 500;
                        line-height: 12px;
                        color: $S1;
                        letter-spacing: 0;
                    }

                    &:nth-child(2) {
                        width: 248px;
                        height: 38px;
                        margin-left: 13px;
                        font-size: 13px;
                        font-weight: 500;
                        line-height: 16px;
                    }
                }
            }

            .right-text-line-box-right {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                width: calc(100% - 248px);
                padding-right: 12px;
            }
        }

        .merge-line-info {
            width: 100%;
            height: auto;
            padding: 8px 12px;
            background-color: $S2;
            border-bottom: 1px dashed #e6eaee;

            .merge-line-info-nel {
                display: flex;
                height: 19px;
                margin-bottom: 5px;
                font-size: 11px;
                font-weight: 400;
                line-height: 19px;
                color: #aaabb3;
                letter-spacing: 0;

                div {
                    width: 33.33%;
                    text-align: left;
                }
            }

            .merge-line-info-black {
                height: 20px;
                font-size: 11px;
                font-weight: 400;
                line-height: 19px;
                color: $S1;
                text-align: right;
                letter-spacing: 0;
            }

            .merge-line-info-text {
                display: flex;
                justify-content: space-between;
                width: 100%;
                height: 19px;
                margin-bottom: 3px;
                font-size: 11px;
                font-weight: 400;
                line-height: 19px;
                color: $S1;
                text-align: right;
                letter-spacing: 0;

                div {
                    &:nth-child(1) {
                        color: #aaabb3;
                    }
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .right-text-line-merge {
            width: 100%;
            height: auto;
            padding: 8px 12px;
            background-color: $S2;
            border-top: 1px dashed #e6eaee;
            border-bottom: 1px dashed #e6eaee;

            .merge-line {
                display: flex;
                justify-content: space-between;
                width: 100%;
                height: 19px;
                margin-bottom: 3px;
                font-size: 11px;
                font-weight: 400;
                line-height: 19px;
                color: $S1;
                text-align: right;
                letter-spacing: 0;

                div {
                    &:nth-child(1) {
                        color: #aaabb3;
                    }
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        .right_text_line_info {
            border-bottom: 1px dashed #e6eaee;
        }

        .right-text-line-info {
            display: flex;
            justify-content: space-between;
            width: 100%;
            height: 36px;
            padding: 8px 12px 9px 12px;
            background-color: $S2;
            border-top: 1px dashed #e6eaee;

            img {
                position: absolute;
                top: 13px;
                right: 10px;
                width: 10px;
                height: 10px;
            }

            div {
                &:nth-child(1) {
                    height: 19px;
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 19px;
                    color: #aaabb3;
                    letter-spacing: 0;
                }

                &:nth-child(2) {
                    height: 19px;
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 19px;
                    color: $S1;
                    text-align: right;
                    letter-spacing: 0;
                }
            }
        }

        .right-text-line-payment {
            width: 100%;
            height: auto;
            padding: 12px 12px 0 12px;
            margin-top: 6.2px;
            background-color: $S2;

            .line-payment-title {
                height: 19px;
                margin-bottom: 10px;
                font-size: 13px;
                font-weight: 500;
                line-height: 19px;
                color: #333333;
                letter-spacing: 0;
            }

            .line-payment-info {
                display: flex;
                justify-content: space-between;
                width: 100%;
                height: 16px;
                margin-bottom: 8px;
                font-size: 11px;
                font-weight: 400;
                letter-spacing: 0;

                &:last-child {
                    margin-bottom: 12px;
                }

                div {
                    &:nth-child(1) {
                        color: #aaabb3;
                    }

                    &:nth-child(2) {
                        color: #333333;
                    }
                }
            }

            .line-payment-info-bottom {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 45px;
                border-top: 1px solid #f0f0f0;

                div {
                    &:nth-child(1) {
                        height: 18px;
                        font-size: 13px;
                        font-weight: 500;
                        color: #333333;
                        letter-spacing: 0;
                    }

                    &:nth-child(2) {
                        height: 22px;
                        font-size: 16px;
                        font-weight: 500;
                        color: #ff3333;
                        text-align: right;
                        letter-spacing: 0;
                    }
                }
            }
        }

        .right-text-line-call {
            width: 100%;
            height: auto;
            padding: 12px 9px 8px 9px;
            margin-top: 6.2px;
            background-color: $S2;

            .line-call {
                display: flex;
                justify-content: space-between;
                height: 19px;
                margin-bottom: 3px;
                font-size: 11px;
                font-weight: 400;
                line-height: 19px;
                color: #aaabb3;
                letter-spacing: 0;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .call {
                height: 16px;
                margin-top: 8px;
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                color: #c66e4e;
                text-align: right;
                letter-spacing: 0;

                img {
                    width: 12px;
                    height: 12px;
                    margin-left: 6.2px;
                }
            }
        }

        .right-text-button {
            display: flex;
            align-items: center;
            justify-content: space-around;
            width: 100%;
            height: 53px;
            margin-top: 6.2px;
            background-color: $S2;

            div {
                width: 280.8px;
                height: 35.2px;
                line-height: 35.2px;
                color: $S2;
                text-align: center;
                background-color: #d66a44;
            }
        }
    }
}
</style>
