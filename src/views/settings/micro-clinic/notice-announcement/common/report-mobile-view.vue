<template>
    <div class="report-mobile-view">
        <div class="report-mobile-view_box">
            <img src="~assets/images/announcement/reportTitle.png" />
            <img src="~assets/images/announcement/reportInfo.png" />
            <div v-if="config.showMedicalRecord.includes(0) || config.showMedicalRecord.includes(1) || config.showMedicalRecord.includes(2)" id="patientInfo" class="announcement-report-img-model">
                <div class="model-title">
                    病历
                </div>
                <div class="model-body">
                    <template v-if="config.showMedicalRecord.includes(0)">
                        <div
                            v-for="(item,index) in medicalRecordOptions"
                            :key="index"
                            class="model-body-line"
                        >
                            <div>{{ item.label }}:</div>
                            <div>{{ item.value }}</div>
                        </div>
                    </template>
                    <div
                        v-if="config.showMedicalRecord.includes(1)"
                        class="model-body-line"
                    >
                        <div>医嘱事项:</div>
                        <div>忌辛辣烟酒，多喝水，少熬夜、保持充足睡眠。</div>
                    </div>
                    <div v-if="config.showMedicalRecord.includes(2)" class="model-body-line">
                        <div>附件:</div>
                        <div>
                            <img src="~assets/images/announcement/report1.png" />
                            <img src="~assets/images/announcement/report2.png" />
                        </div>
                    </div>
                    <div v-if="config.showMedicalRecord.includes(3)" class="model-body-line">
                        <div>检查报告:</div>
                        <div>
                            <div v-for="(o, key) in examination" :key="key" class="examination-item-wrapper">
                                <img src="~assets/images/announcement/icon-examination.png" class="examination-item-avatar" />
                                <span class="examination-item-text">{{ o }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="showMedicalRecordInfo" class="announcement-report-img-box">
                通用问诊单
            </div>
            <div v-if="config.showExaminationItem === 1" id="showExaminationItem" class="announcement-report-img-model">
                <div class="model-title">
                    辅助检查
                </div>
                <div class="model-body">
                    <div class="model-body-line">
                        <div class="body-line-black">
                            心电图
                        </div>
                        <div style="color: #2576ff !important;" class="body-line-gray">
                            查看报告
                        </div>
                    </div>
                    <div class="model-body-line">
                        <div class="body-line-black">
                            血常规
                        </div>
                        <div style="color: #2576ff !important;" class="body-line-gray">
                            查看报告
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="config.showTreatItem === 1" id="showTreatItem" class="announcement-report-img-model">
                <div class="model-title">
                    治疗
                </div>
                <div class="model-body">
                    <div class="model-body-line">
                        <div class="body-line-black">
                            小针刀
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="config.showNursingItem === 1" id="showNursingItem" class="announcement-report-img-model">
                <div class="model-title">
                    护理
                </div>
                <div class="model-body">
                    <div class="model-body-line">
                        <div class="body-line-black">
                            脊椎穿刺
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="config.showComposeItem === 1 || config.showComposeItem === 2" id="showComposeItem" class="announcement-report-img-model">
                <div class="model-title">
                    治疗优惠套餐
                </div>
                <div class="model-body">
                    <div class="model-body-line">
                        <div class="body-line-black" style="width: 110px;">
                            治疗加理疗套餐
                        </div>
                        <div class="body-line-gray">
                            x1
                        </div>
                    </div>
                    <div v-if="config.showComposeItem === 2" class="model-body-line">
                        <div class="body-line-black" style="width: 110px; color: #aaabb3 !important;">
                            血液检查
                        </div>
                        <div class="body-line-gray" style="color: rgb(37, 118, 255) !important;">
                            查看报告
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="config.showDrugMaterialItem === 1" id="showDrugMaterialItem" class="announcement-report-img-model">
                <div class="model-title">
                    材料商品
                </div>
                <div class="model-body">
                    <div class="model-body-line">
                        <div class="body-line-black" style="width: 110px;">
                            医用半月板
                        </div>
                        <div class="body-line-gray">
                            1个
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="config.showOtherItem === 1" id="showOtherItem" class="announcement-report-img-model">
                <div class="model-title">
                    其他
                </div>
                <div class="model-body">
                    <div class="model-body-line">
                        <div class="body-line-black">
                            其他
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="config.showWesternMedicine === 1" id="showWesternMedicine" class="announcement-report-img-model">
                <div class="model-title">
                    西药处方
                </div>
                <div class="model-body">
                    <div class="model-body-line">
                        <div class="body-line-black" style="width: 110px;">
                            肠炎宁片
                        </div>
                        <div class="body-line-gray">
                            12片*4板/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1盒
                        </div>
                    </div>
                    <div class="model-body-line-western">
                        用法：口服，1天3次，每次2片
                    </div>
                </div>
            </div>
            <div v-if="config.showChinese === 1" id="showChinese" class="announcement-report-img-model">
                <div class="model-title">
                    中药处方<span class="unit">3剂</span>
                </div>
                <div class="model-chinese">
                    <div>玉米须、木棉花等2味中药</div>
                    <div>用法：每1日半1剂，水煎400ml，早晚空腹服用</div>
                </div>
            </div>

            <div v-if="config.showInfusion === 1" id="showInfusion" class="announcement-report-img-model">
                <div class="model-title">
                    输液处方
                </div>

                <div class="model-body">
                    <div class="model-showInfusion">
                        组1
                    </div>
                    <div class="model-body-line">
                        <div class="body-line-black" style="width: 100px;">
                            清开灵注射液
                        </div>
                        <div class="body-line-gray">
                            1瓶
                        </div>
                    </div>
                    <div class="model-body-line">
                        <div class="body-line-black" style="width: 100px;">
                            柴胡注射射液
                        </div>
                        <div class="body-line-gray">
                            1瓶
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="config.showExternal === 1" id="showExternal" class="announcement-report-img-model">
                <div class="model-title">
                    外治处方
                </div>
                <div class="model-body">
                    <div class="model-body-line">
                        <div class="body-line-black">
                            推拿A
                        </div>
                        <div class="body-line-gray">
                            一次
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="isShowAllProject" id="showALL" class="announcement-report-img-model">
                <div class="model-title">
                    治疗方案
                </div>
                <div class="model-body">
                    <div v-if="config.showExaminationItem === 2" class="model-body-line">
                        <div class="body-line-black">
                            检查检验
                        </div>
                        <div class="body-line-gray">
                            2项
                        </div>
                    </div>
                    <div v-if="config.showTreatItem === 2" class="model-body-line">
                        <div class="body-line-black">
                            {{ treatmentText }}
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                    <div v-if="config.showNursingItem === 2" class="model-body-line">
                        <div class="body-line-black">
                            护理
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                    <div v-if="config.showComposeItem === 3" class="model-body-line">
                        <div class="body-line-black">
                            套餐
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                    <div v-if="config.showDrugMaterialItem === 2" class="model-body-line">
                        <div class="body-line-black">
                            材料商品
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                    <div v-if="config.showOtherItem === 2" class="model-body-line">
                        <div class="body-line-black">
                            其他
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                    <div v-if="config.showWesternMedicine === 2" class="model-body-line">
                        <div class="body-line-black" style="width: 90px;">
                            中西成药处方
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                    <div v-if="config.showChinese === 2" class="model-body-line">
                        <div class="body-line-black">
                            中药处方
                        </div>
                        <div class="body-line-gray">
                            2项
                        </div>
                    </div>
                    <div v-if="config.showInfusion === 2" class="model-body-line">
                        <div class="body-line-black">
                            输液处方
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                    <div v-if="config.showExternal === 2" class="model-body-line">
                        <div class="body-line-black">
                            外治处方
                        </div>
                        <div class="body-line-gray">
                            1项
                        </div>
                    </div>
                </div>
            </div>
            <div id="payment" class="announcement-report-img-model">
                <div class="model-title">
                    收费
                </div>
                <div class="model-body">
                    <div v-if="config.showFeeInfo.includes(0)" class="model-body-line">
                        <div>费用合计</div>
                        <div class="body-line-gray" style="color: #ff9933 !important;">
                            <abc-money :value="88"></abc-money>
                        </div>
                    </div>
                    <div v-if="config.showFeeInfo.includes(1)" class="model-body-line">
                        <div>折扣</div>
                        <div class="body-line-gray">
                            -<abc-money :value="20"></abc-money>
                        </div>
                    </div>
                    <div class="model-body-line">
                        <div>已付</div>
                        <div class="body-line-gray">
                            <abc-money :value="68"></abc-money>
                        </div>
                    </div>
                    <div class="model-body-line">
                        <div>收费时间</div>
                        <div class="body-line-gray">
                            2019-12-12  15:35
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    export default {
        name: 'ReportMobileView',
        props: {
            config: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            medicalRecordOptions: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                examination: ['视光初查', '角膜塑形镜配前检查'],
            };
        },
        computed: {
            treatmentText() {
                const { microClinic } = getViewDistributeConfig().Settings;
                return microClinic.treatmentText;
            },
            // 是否展示带所有治疗方案的模块
            isShowAllProject() {
                return this.config.showChinese === 2 ||
                    this.config.showDrugMaterialItem === 2 ||
                    this.config.showExaminationItem === 2 ||
                    this.config.showExternal === 2 ||
                    this.config.showInfusion === 2 ||
                    this.config.showOtherItem === 2 ||
                    this.config.showTreatItem === 2 ||
                    this.config.showWesternMedicine === 2 ||
                    this.config.showNursingItem === 2 ||
                    this.config.showComposeItem === 3;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.report-mobile-view {
    display: flex;
    flex-direction: column;
    width: 332px !important;
    height: calc(100vh - 258px);
    overflow-y: scroll;

    @include scrollBar;

    &_box {
        width: 320px;
        height: auto;
        background-color: #f4f7fa;

        img {
            width: 100%;
        }

        .announcement-report-img-box {
            width: 100%;
            height: auto;
            padding: 8px 12px;
            font-size: 12px;
            color: $S1;
            background-color: $S2;
            border-top: 6px solid #f4f7fa;
        }

        .announcement-report-img-model {
            width: 100%;
            height: auto;
            background-color: $S2;
            border-top: 6px solid #f4f7fa;

            .model-title {
                height: 32px;
                padding: 10px 12px;
                font-size: 13px;
                font-weight: 500;
                line-height: 13px;
                color: $S1;
                border-bottom: 1px solid #f9fafb;

                .unit {
                    float: right;
                    font-size: 11px;
                    color: #aaabb3;
                }
            }

            .model-chinese {
                padding: 8px 12px 10px 12px;

                div {
                    &:nth-child(1) {
                        height: 19px;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 19px;
                        color: $S1;
                        letter-spacing: 0;
                    }

                    &:nth-child(2) {
                        margin-top: 4px;
                        font-size: 10px;
                        font-weight: 400;
                        line-height: 10px;
                        color: #aaabb3;
                        letter-spacing: 0;
                    }
                }
            }

            .model-body-info {
                height: 36px;
                padding: 8px 12px;
                font-size: 12px;
                color: $S1;
                background: $S2;
            }

            .model-body {
                padding: 10px 12px 6px 12px;

                .model-showInfusion {
                    min-height: 19px;
                    margin-bottom: 4px;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 19px;
                    color: #aaabb3;
                }

                .model-body-line-western {
                    height: 10px;
                    font-size: 10px;
                    font-weight: 400;
                    line-height: 10px;
                    color: #aaabb3;
                    letter-spacing: 0;
                }

                .model-body-line {
                    display: flex;
                    min-height: 19px;
                    margin-bottom: 4px;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 19px;
                    color: #aaabb3;

                    > div {
                        &:nth-child(1) {
                            width: 60px;
                        }

                        &:nth-child(2) {
                            width: calc(100% - 60px);
                            margin-left: 4px;
                            color: $S1;

                            img {
                                width: 40px;
                                height: 40px;
                                margin-right: 8px;
                                border-radius: 8px;
                            }
                        }
                    }

                    .examination-item-wrapper {
                        display: flex;
                        align-items: center;
                        width: 100%;
                        height: 44px;
                        padding: 0 12px;
                        border: 1px solid #f0f0f0;
                        border-radius: var(--abc-border-radius-small);

                        &:first-child {
                            margin-bottom: 12px;
                        }

                        .examination-item-avatar {
                            width: 22px;
                            height: 22px;
                            margin-right: 8px;
                            border-radius: 0;
                        }
                    }

                    .body-line-black {
                        color: $S1 !important;
                    }

                    .body-line-gray {
                        color: #aaabb3 !important;
                        text-align: right !important;
                    }
                }
            }
        }
    }
}
</style>
