<template>
    <biz-setting-layout
        ref="container"
        v-abc-loading="loading"
        class="pharmacy-screen-setting"
    >
        <biz-setting-content>
            <pharmacy-call-tab style="margin-bottom: 24px;"></pharmacy-call-tab>

            <div v-if="!devices.length && !loading" class="no-data">
                <call-number-empty scene="pharmacy"></call-number-empty>
            </div>

            <template v-if="devices.length">
                <abc-space :size="10" wrap class="list-box">
                    <div
                        v-for="item in devices"
                        :key="item.id"
                        :class="{
                            item: true,
                            active: item.id === curEditorId,
                        }"
                        @click="initPostData(item)"
                    >
                        <img src="~assets/images/<EMAIL>" alt="" />
                        <span>{{ item.name || '叫号屏' }}</span>
                    </div>

                    <abc-button variant="text" theme="primary" @click="handleOpenTeachDialog">
                        绑定教程
                    </abc-button>
                </abc-space>

                <abc-divider margin="large"></abc-divider>

                <template v-if="devices.length">
                    <div class="label">
                        屏幕显示设置
                    </div>
                    <div class="screen-box">
                        <header>
                            <div class="logo-box">
                                <span v-if="!postData.logoUrl" class="placeholder">设置品牌logo</span>
                                <div
                                    v-else
                                    class="logo"
                                    :style="{
                                        'background-image': `url(${postData.logoUrl})`,
                                    }"
                                ></div>
                                <div v-if="postData.logoUrl" class="delete-icon" @click="postData.logoUrl = ''"></div>
                                <input
                                    v-if="!checkedBlob"
                                    type="file"
                                    class="inpt-node"
                                    :disabled="updateLoading"
                                    :title="''"
                                    accept="image/*"
                                    @change="onChangeLogoFile($event)"
                                />
                            </div>
                            <div class="title-box">
                                <abc-input v-model="postData.name" :max-length="10" placeholder="设置叫号屏名称"></abc-input>
                            </div>
                            <div class="date-box">
                                <div class="date">
                                    <span>{{ curWeek }}</span>
                                    <span>{{ curDateStr }}</span>
                                </div>
                                <div class="time">
                                    {{ curTime }}
                                </div>
                            </div>
                        </header>
                        <section>
                            <span class="placeholder">已关联药房</span>
                            <abc-flex :gap="8" style="margin-top: 24px;">
                                <div class="pharmacy-wrapper">
                                    <abc-icon icon="clinic" color="#85BAFF" size="12"></abc-icon>
                                    <span class="name">{{ postData.pharmacyName }}</span>
                                </div>

                                <abc-button
                                    theme="primary"
                                    variant="outline"
                                    icon="n-add-line-medium"
                                    shape="round"
                                    style="height: 28px;"
                                    @click="onClickSelectPharmacy"
                                >
                                    药房
                                </abc-button>
                            </abc-flex>
                        </section>
                        <footer>
                            <abc-input v-model="postData.remindTips" :max-length="200" placeholder="设置滚动提醒内容"></abc-input>
                        </footer>
                    </div>
                </template>
            </template>

            <template v-if="devices.length && !!curEditorId" #footer>
                <biz-setting-footer>
                    <abc-flex justify="space-between" align="center" style="width: 790px;">
                        <abc-button
                            style="min-width: 80px;"
                            :loading="saveLoading"
                            :disabled="!canClickSave"
                            @click="onClickSave"
                        >
                            保存
                        </abc-button>

                        <abc-button
                            type="danger"
                            style="min-width: 80px;"
                            :loading="unbindLoading"
                            @click="onClickUnBind"
                        >
                            解绑
                        </abc-button>
                    </abc-flex>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <abc-editor-img
            v-if="!!checkedBlob"
            :blob="checkedBlob"
            @confirm="onEditorConfirm"
            @cancel="
                checkedBlob = null;
                checkedFile = null;
            "
        ></abc-editor-img>

        <abc-dialog
            v-if="showSelectPharmacy"
            v-model="showSelectPharmacy"
            title="设置关联药房"
            content-styles="width: 370px; height: 100px; padding: 24px 24px 0;"
        >
            <abc-form
                ref="pharmacyForm"
                :label-width="84"
                label-position="left"
                item-block
            >
                <abc-form-item label="药房" required hidden-red-dot>
                    <abc-select
                        v-model="selectedPharmacy.pharmacyNo"
                        :width="160"
                        :max-height="120"
                        placeholder="请选择药房"
                        custom-class="select-pharmacy-wrapper"
                        empty-text="暂无药房"
                    >
                        <abc-option
                            v-for="item in pharmacyList"
                            :key="item.pharmacyNo"
                            :value="item.pharmacyNo"
                            :label="item.pharmacyName"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
            </abc-form>
            <div slot="footer" class="dialog-footer">
                <abc-button @click="onClickSelectConfirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="showSelectPharmacy = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </biz-setting-layout>
</template>

<script>
    import Api from 'api/settings';
    import PharmacyCallAPI from 'api/pharmacy-call';
    import clone from 'utils/clone';
    import { formatDate } from '@tool/date';
    import {
        isEqual,
    } from 'utils/lodash';
    import AbcEditorImg from '@/views/layout/editor-img/index.vue';
    import Clone from 'utils/clone';
    import handleLogoMixin from 'views/settings/call-number/mixins/handle-logo.js';
    import { PharmacyTypeEnum } from 'views/common/enum';
    import CallNumberEmpty from 'views/settings/call-number/components/empty.vue';
    import TeachDialog from 'views/settings/call-number/inspect-call/teach-dialog';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import PharmacyCallTab from 'views/settings/call-number/components/pharmacy-call-tab/index.vue';

    const TIME_FORMAT = 'HH:mm';
    const DATE_FORMATE = 'YYYY年MM月DD日';
    const weekList = ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    export default {
        components: {
            PharmacyCallTab,
            CallNumberEmpty,
            AbcEditorImg,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
        },
        mixins: [
            handleLogoMixin,
        ],
        async beforeRouteLeave(to, from, next) {
            if (this.canClickSave) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                date: new Date(),
                loading: false,
                devices: [],
                curEditorId: 1,
                defaultLogo: null,
                postData: {
                    name: '',
                    logoUrl: '',
                    pharmacyName: '',
                    pharmacyNo: '',
                    pharmacyType: '',
                    remindTips: '',
                },

                updateLoading: false,
                unbindLoading: false,
                saveLoading: false,

                pharmacyList: [], // 药房列表
                showSelectPharmacy: false,
                selectedPharmacy: {
                    pharmacyName: '',
                    pharmacyNo: '',
                    pharmacyType: '',
                },
            };
        },
        computed: {
            // 时间展示：12:24
            curTime() {
                return formatDate(this.date, TIME_FORMAT);
            },
            // 星期展示：星期日 ~ 星期六
            curWeek() {
                const week = this.date.getDay();
                return weekList[week];
            },
            // 日期展示：2020年01月01日
            curDateStr() {
                return formatDate(this.date, DATE_FORMATE);
            },
            // 是否可以保存
            canClickSave() {
                let target = {};
                const postData = Clone(this.postData);
                target = this.devices.find((item) => item.id === this.curEditorId);
                if (target) {
                    target.remindTips = target.remindTips === '' ? null : target.remindTips;
                    postData.remindTips = postData.remindTips === '' ? null : postData.remindTips;
                    return !isEqual(target, postData);
                }
                return false;
            },
        },
        created() {
            this.getScreenList();
        },
        methods: {
            /**
             * desc [拉取药房列表]
             */
            async fetchPharmacyList() {
                try {
                    const { data } = await PharmacyCallAPI.fetchDispensingPharmaciesList();
                    const localPharmacyList = data?.dispensablePharmacies?.filter((item) => item.type === PharmacyTypeEnum.LOCAL_PHARMACY) || [];
                    this.pharmacyList = localPharmacyList.map((item) => {
                        return {
                            pharmacyNo: item.no,
                            pharmacyName: item.name,
                            pharmacyType: item.type,
                        };
                    });
                } catch (error) {
                    this.pharmacyList = [];
                    console.log('fetchPharmacyList error', error);
                }
            },

            /**
             * desc [拉取诊所Logo信息]
             */
            async getClinicInfo() {
                try {
                    const { data } = await Api.clinic.fetchClinicInfoById();
                    if (data && data.data) {
                        this.defaultLogo = data.data.logo || '';
                    }
                } catch (error) {
                    console.log('getClinicInfo error', error);
                }
            },
            /**
             * desc [拉取叫号屏绑定列表]
             */
            async getScreenList() {
                this.loading = true;
                if (!this.pharmacyList.length) {
                    await this.fetchPharmacyList();
                }
                if (!this.defaultLogo) {
                    await this.getClinicInfo();
                }
                try {
                    const { data } = await PharmacyCallAPI.getDispensingDevices();
                    if (data?.rows?.length) {
                        this.devices = data.rows.map((item) => {
                            // 当没有设置logo时，同时有诊室默认logo  此时显示默认logo
                            if (item.status === 0 && !item.logoUrl && this.defaultLogo) {
                                item.logoUrl = this.defaultLogo;
                            }
                            return item;
                        });
                        this.initPostData(this.devices[0]);
                    } else {
                        this.devices = [];
                        this.curEditorId = '';
                    }
                } catch (error) {
                    console.log('getScreenList error', error);
                }
                this.loading = false;
            },
            /**
             * desc [当点击切换屏设置时]
             */
            onClickCheckout(item) {
                if (this.canClickSave) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '当前屏设置有修改，是否确认保存？',
                        onConfirm: async () => {
                            await this.onClickSave();
                            this.initPostData(item);
                        },
                    });
                } else {
                    this.initPostData(item);
                }
            },
            /**
             * desc [初始postData]
             */
            initPostData(device) {
                this.curEditorId = device.id;
                this.postData = clone(device);
            },
            /**
             * desc [点击选中药房]
             */
            onClickSelectPharmacy() {
                const {
                    pharmacyNo, pharmacyName, pharmacyType,
                } = this.postData;
                this.selectedPharmacy = {
                    pharmacyNo,
                    pharmacyName,
                    pharmacyType,
                };
                this.showSelectPharmacy = true;
            },
            /**
             * desc [确认]
             */
            onClickSelectConfirm() {
                this.$refs.pharmacyForm.validate((valid) => {
                    if (valid) {
                        const item = this.pharmacyList.find((pharmacyItem) => pharmacyItem.pharmacyNo === this.selectedPharmacy.pharmacyNo);
                        if (item) {
                            const {
                                pharmacyNo, pharmacyName, pharmacyType,
                            } = item;
                            this.postData.pharmacyNo = pharmacyNo;
                            this.postData.pharmacyName = pharmacyName;
                            this.postData.pharmacyType = pharmacyType;
                        }
                        this.showSelectPharmacy = false;
                    }
                });
            },
            /**
             * desc [点击保存设置]
             */
            async onClickSave() {
                if (this.saveLoading) return;
                this.saveLoading = true;
                try {
                    await PharmacyCallAPI.updateDispensingDevice(this.curEditorId, this.postData);
                    const item = this.devices.find((it) => it.id === this.curEditorId);
                    if (item) {
                        Object.assign(item, this.postData);
                    }
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                } catch (error) {
                    console.log('updateDevice error', error);
                    this.$Toast({
                        type: 'error',
                        message: error.message,
                    });
                }
                this.saveLoading = false;
            },
            /**
             * desc [点击解绑触发]
             */
            onClickUnBind() {
                if (this.unbindLoading) return;
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: `解绑后，${this.postData.name}将失效，是否确认？`,
                    onConfirm: async () => {
                        this.unbindLoading = true;
                        try {
                            await PharmacyCallAPI.unbindDispensingDevice(this.curEditorId);
                            this.getScreenList();
                            this.$Toast({
                                type: 'success',
                                message: '解绑成功',
                            });
                        } catch (error) {
                            console.log('onClickUnBind error', error);
                        }
                        this.unbindLoading = false;
                    },
                });
            },

            handleOpenTeachDialog() {
                new TeachDialog({
                    scene: 'pharmacy',
                }).generateDialogAsync();
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/abc-common.scss';

.pharmacy-screen-setting {
    position: relative;
    box-sizing: border-box;
    width: 100%;

    .list-box {
        .item {
            box-sizing: border-box;
            width: 216px;
            height: 64px;
            padding-left: 16px;
            cursor: pointer;
            border: 1px solid $P10;
            border-radius: var(--abc-border-radius-small);

            @include flex(row, flex-start, center);

            &.active {
                padding-left: 15px;
                border: 2px solid $theme2;
            }

            img {
                width: 36px;
                height: 36px;
                margin-right: 12px;
            }

            span {
                display: inline-block;
                max-width: 140px;
                font-size: 14px;
                font-weight: 500;
                color: $T1;

                @include ellipsis(1);
            }
        }
    }

    .no-data {
        height: 240px;

        span.tit {
            margin-bottom: 8px;
            color: $T2;
        }
    }

    .label {
        width: 816px;
        margin-bottom: 15px;
        font-size: 14px;
        color: $T2;
        text-align: center;
    }

    .screen-box {
        width: 816px;
        height: 514px;
        padding: 17px 27px 47px 17px;
        background-image: url('~assets/images/<EMAIL>');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        transform: translateX(-8px);

        > header {
            height: 56px;
            border-bottom: 1px solid $P1;

            @include flex(row, center, stretch);

            .logo-box {
                position: relative;
                flex-shrink: 0;
                width: 215px;
                border-right: 1px solid $P1;

                @include flex(row, center, center);

                .logo {
                    width: 200px;
                    height: 40px;
                    background-repeat: no-repeat;
                    background-position: center center;
                    background-size: auto 100%;
                }

                .inpt-node {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 1;
                    cursor: pointer;
                    opacity: 0;
                }

                .delete-icon {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    z-index: 2;
                    display: none;
                    width: 16px;
                    height: 16px;
                    background: url('~assets/images/<EMAIL>') no-repeat center;
                    background-size: contain;
                    transition: opacity 0.2s;

                    &:hover {
                        background: url('~assets/images/<EMAIL>') no-repeat center;
                        background-size: contain;
                    }
                }
            }

            .logo-box:hover {
                .delete-icon {
                    display: inline-block;
                }
            }

            .title-box {
                flex: 1;

                .abc-input-wrapper {
                    width: 100%;
                    height: 100%;

                    > input {
                        width: 100%;
                        height: 100%;
                        font-size: 18px;
                        font-weight: 500;
                        color: $T1;
                        text-align: center;
                        border-color: rgba(0, 0, 0, 0);

                        &:focus,
                        &:hover,
                        &:active {
                            border-radius: 0 !important;
                        }

                        &::placeholder {
                            font-size: 14px;
                            font-weight: normal;
                        }
                    }
                }
            }

            .date-box {
                flex-shrink: 0;
                width: 215px;
                padding: 0 15px;
                border-left: 1px solid $P1;

                @include flex(row, flex-end, center);

                .date {
                    height: 100%;

                    @include flex(column, center, flex-end);

                    span {
                        font-size: 12px;
                        line-height: 12px;
                        color: $T3;

                        &:last-child {
                            margin-top: 4px;
                        }
                    }
                }

                .time {
                    margin: 0 8px;
                    font-size: 30px;
                    color: $T3;
                }
            }
        }

        > section {
            height: 354px;
            cursor: pointer;

            @include flex(column, center, center);

            .pharmacy-wrapper {
                @include flex(row, center, center);

                height: 28px;
                padding: 0 10px;
                background: $P4;
                border-radius: 14px;

                .name {
                    margin-left: 6px;
                    font-size: 12px;
                    color: #000000;
                }

                &:hover {
                    background: #ffffff;
                    border: 1px solid var(--abc-color-P7);
                    transition: none;
                }
            }
        }

        > footer {
            height: 42px;
            border-top: 1px solid $P1;

            .abc-input-wrapper {
                width: 100%;
                height: 100%;

                > input {
                    width: 100%;
                    height: 100%;
                    font-size: 14px;
                    font-weight: 500;
                    color: $T1;
                    text-align: center;
                    border-color: rgba(0, 0, 0, 0);

                    &:focus,
                    &:hover,
                    &:active {
                        border-radius: 0 !important;
                    }

                    &::placeholder {
                        font-size: 14px;
                        font-weight: normal;
                    }
                }
            }
        }

        .placeholder {
            font-size: 14px;
            color: $T3;
        }
    }

    .handle-box-wrapper {
        background-color: $S2;

        .handle-box {
            width: 790px;
            padding: 16px 0;

            @include flex(row, space-between, center);
        }
    }
}

.select-pharmacy-wrapper {
    .not-data {
        height: 80px;
        color: $T3;

        @include flex(row, center, center);
    }
}
</style>
