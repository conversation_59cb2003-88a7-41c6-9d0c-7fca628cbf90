<template>
    <biz-setting-layout
        v-abc-loading="loading"
        class="inspect-call-rule-setting"
    >
        <biz-setting-content>
            <abc-form ref="form" item-no-margin>
                <biz-setting-form :label-width="112">
                    <biz-setting-form-header>
                        <inspect-call-tab></inspect-call-tab>
                    </biz-setting-form-header>

                    <biz-setting-form-group
                        class="item-set"
                        :style="postData.enableCalling ? 'padding-bottom: 24px;' : 'padding-bottom: 16px;'"
                    >
                        <biz-setting-form-item label="排队叫号">
                            <biz-setting-form-item-tip tip="开启后，可通过叫号屏显示叫号排队信息，语音呼叫患者就诊">
                                <abc-checkbox
                                    v-model="postData.enableCalling"
                                    type="number"
                                    @input="onChangeInput"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="支持叫号类型" :vertical="false">
                            <abc-checkbox-group v-model="postData.deviceTypes">
                                <abc-checkbox :label="INSPECT_TYPE.CDU">
                                    彩超
                                </abc-checkbox>
                                <abc-checkbox :label="INSPECT_TYPE.CT">
                                    CT
                                </abc-checkbox>
                                <abc-checkbox :label="INSPECT_TYPE.DR">
                                    DR
                                </abc-checkbox>
                                <abc-checkbox :label="INSPECT_TYPE.MR">
                                    MR
                                </abc-checkbox>
                            </abc-checkbox-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="叫号流程" :has-divider="postData.enableCalling === 1">
                            <biz-setting-form-item-tip tip="开启后，医生可在叫号功能中操作【完检】并呼叫下一位，但检查报告仍可继续填写">
                                <abc-checkbox
                                    v-model="postData.enableNext"
                                    type="number"
                                    @input="onChangeInput"
                                >
                                    检查报告未填写完成时可呼叫下一位
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <template v-if="postData.enableCalling === 1">
                            <biz-setting-form-item label="患者姓名隐私保护">
                                <biz-setting-form-item-tip tip="开启后，患者姓名不会完全显示。但语音播报仍然播报全名">
                                    <abc-checkbox
                                        v-model="postData.hidePatientName"
                                        type="number"
                                        @input="onChangeInput"
                                    >
                                        开启
                                    </abc-checkbox>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="上屏人数" label-line-height-size="medium">
                                <biz-setting-form-item-tip :tip="`当候诊人数超过${ postData.screenCount }人，最多上屏${ postData.screenCount }人`">
                                    <abc-flex align="center" gap="8">
                                        <span>指定数量</span>
                                        <abc-form-item :required="!!postData.enableLockScreen" :validate-event="validateScreenCount">
                                            <abc-input
                                                v-model.number="postData.screenCount"
                                                size="small"
                                                type="number"
                                                :width="56"
                                                :config="{
                                                    supportZero: false
                                                }"
                                            >
                                                <span slot="append">人</span>
                                            </abc-input>
                                        </abc-form-item>
                                    </abc-flex>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="已上屏顺序锁定">
                                <biz-setting-form-item-tip tip="开启后，已在叫号屏上显示的患者顺序不可更改">
                                    <abc-checkbox
                                        v-model="postData.enableLockScreen"
                                        type="number"
                                        @input="onChangeInput"
                                    >
                                        开启
                                    </abc-checkbox>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item
                                label="语音播报内容"
                                class="item-set-custom"
                            >
                                <abc-radio-group v-model="postData.speechTemplateId">
                                    <abc-radio :label="0">
                                        播报“请xx号xx检查”
                                    </abc-radio>
                                    <abc-radio :label="1">
                                        播报“请xx号xx检查，请xx号xx等待”
                                    </abc-radio>
                                </abc-radio-group>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="号数展示">
                                <biz-setting-form-item-tip tip="设置后，叫号屏及语音播报，会显示患者检查号数">
                                    <abc-checkbox
                                        v-model="postData.showOrderNo"
                                        type="number"
                                    >
                                        展示患者检查号数
                                    </abc-checkbox>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="显示风格">
                                <abc-radio-group v-model="postData.style">
                                    <abc-radio :label="0">
                                        浅色淡雅
                                    </abc-radio>
                                    <abc-radio :label="1">
                                        深色通透
                                    </abc-radio>
                                </abc-radio-group>
                            </biz-setting-form-item>
                        </template>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button :loading="btnLoading" :disabled="!isUpdate" @click="onClickSave">
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <abc-dialog
            v-if="showSignInAlert"
            v-model="showSignInAlert"
            class="dialog-signin-alert"
            content-styles="width: 400px; padding: 24px"
        >
            <div class="tips" style="font-size: 16px;">
                为保证叫号功能正常使用，需要开启检查预约设置中的【预约签到】，预约患者签到确认到店后，才会进入叫号队列。是否开启【预约签到】？
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button @click="onOpenSignInNow">
                    现在开启
                </abc-button>
                <abc-button type="blank" @click="onTemporarilyOpen">
                    暂不开启
                </abc-button>
            </div>
        </abc-dialog>
    </biz-setting-layout>
</template>

<script>
    import clone from 'utils/clone';
    import {
        isEqual, pick1,
    } from 'utils/lodash';
    import ReservationAPI from 'api/registrations/reservation';
    import SettingAPI from 'api/settings';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
        BizSettingFormHeader,
    } from '@/components-composite/setting-form/index.js';

    import {
        mapGetters,
    } from 'vuex';
    import { INSPECT_TYPE } from '@/views-hospital/inspect-setting/utils/constant';
    import InspectCallTab from 'views/settings/call-number/components/inspect-call-tab/index.vue';

    const unitOptions = [
        {
            label: '分钟', value: 0,
        },
        {
            label: '小时', value: 1,
        },
    ];
    export default {
        components: {
            InspectCallTab,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingFormHeader,
        },
        async beforeRouteLeave(to, from, next) {
            if (this.isUpdate) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                unitOptions,
                loading: false,
                btnLoading: false,
                config: null,
                // 检查预约配置
                inspectReservationConfig: null,
                postData: {
                    speechTemplateId: undefined, // 语音播报内容
                    enableCalling: 0, // 开启叫号
                    enableNext: 0, // 病历未填完时可呼叫下一位
                    enableLockScreen: 0, // 开启上屏锁定
                    hidePatientName: 0, // 患者姓名隐私保护
                    showOrderNo: 0, // 号数展示
                    screenCount: 2, // 上屏人数
                    deviceTypes: [], // 支持的检查类型
                },
                showSignInAlert: false, // 开启预约签到提醒
            };
        },
        computed: {
            INSPECT_TYPE() {
                return INSPECT_TYPE;
            },
            ...mapGetters(['registrationsConfig','isEnableRegUpgrade', 'appointmentConfig']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            /**
             * desc [是否有配置调整更新]
             */
            isUpdate() {
                if (!this.config) return false;
                return !isEqual(this.config, this.postData);
            },
            // 是否开启预约签到
            isOpenSignIn() {
                return this.inspectReservationConfig?.needSignIn;
            },
        },
        async created() {
            await this.getConfig();
            await this.fetchInspectReservationConfig();
        },
        methods: {
            // 拉取配置
            async getConfig() {
                this.loading = true;
                try {
                    const config = await SettingAPI.callNumber.fetchInspectConfig();
                    if (config.style === undefined) {
                        // 当没有默认值时，初始化默认值：0 浅色淡雅  1  深色通透
                        config.style = 0;
                    }
                    config.deviceTypes = config.deviceTypes || [];
                    this.config = config;
                    this.postData = clone(this.config);
                    this.$store.commit('inspect/updateCallNumberRule', clone(this.config));
                } catch (error) {
                    console.log('getScreenList error', error);
                    this.$Toast({
                        type: 'error',
                        message: error.message,
                    });
                }
                this.loading = false;
            },

            //点击保存
            async onClickSave() {
                if (
                    (this.postData.enableCalling === 1 && this.isOpenSignIn) ||
                    this.postData.enableCalling === 0
                ) {
                    if (this.btnLoading) return;
                    this.$refs.form.validate(async (valid) => {
                        if (valid) {
                            this.btnLoading = true;
                            try {
                                const params = clone(this.postData);
                                const { data } = await SettingAPI.callNumber.updateInspectConfig(params);
                                this.config = data;
                                this.postData = clone(this.config);
                                this.$store.commit('inspect/updateCallNumberRule', clone(this.config));
                                this.$Toast({
                                    type: 'success',
                                    message: '保存成功',
                                });
                            } catch (error) {
                                this.$Toast({
                                    type: 'error',
                                    message: error.message,
                                });
                            }
                        }
                    });
                    this.btnLoading = false;
                } else {
                    this.$Toast({
                        type: 'error',
                        message: '请到【预约设置】-【检查预约】开启预约签到',
                    });
                }
            },

            //开启排队叫号
            async onChangeInput(value) {
                if (value === 1) {
                    if (!this.isOpenSignIn) {
                        this.showSignInAlert = true;
                    }
                }
            },

            async fetchInspectReservationConfig() {
                const { data } = await ReservationAPI.fetchReservationConfig({
                    registrationType: 10,
                });

                if (!data.reservationTimeRange) {
                    data.reservationTimeRange = {};
                }
                data.reservationTimeRange.registerStartTime = data.reservationTimeRange?.registerStartTime || '08:00';
                data.reservationTimeRange.startTime = data.reservationTimeRange?.startTime || {
                    hour: 0,
                    min: 60,
                };
                data.reservationTimeRange.endTime = data.reservationTimeRange?.endTime || {
                    month: 0,
                    week: 0,
                    day: 0,
                };

                data.aheadCloseReserveTime = data.aheadCloseReserveTime || {
                    min: 0,
                    hour: 1,
                };

                this.inspectReservationConfig = pick1(data, [
                    'serviceType',
                    'needScanSignIn',
                    'needSignIn',
                    'enableAheadCloseReserve',
                    'reservationTimeRange',
                    'aheadCloseReserveTime',
                ]);
            },

            // 开启预约叫号
            async onOpenSignInNow() {
                try {
                    await ReservationAPI.updateReservationConfig({
                        ...this.inspectReservationConfig,
                        needSignIn: 1,
                    });
                    this.inspectReservationConfig.needSignIn = 1;
                } catch (err) {
                    console.log(err);
                }
                this.showSignInAlert = false;
            },

            //暂不开启
            onTemporarilyOpen() {
                this.postData.enableCalling = 0;
                this.showSignInAlert = false;
            },

            validateScreenCount(val, callback) {
                if (val > 20) {
                    callback({
                        validate: false,
                        message: '最多上屏20人',
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/theme.scss';

.inspect-call-rule-setting {
    position: relative;

    .line {
        margin-bottom: 24px;
        border-bottom: 1px solid $P6;
    }

    .item-set {
        .is-empty {
            display: none;
        }

        .other {
            padding-left: 130px;
            margin-top: 10px;
        }
    }

    .item-set-custom {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        > label {
            display: inline-block;
            width: 130px;
            color: $T2;
        }

        .abc-radio {
            height: 16px;

            &:not(:nth-of-type(1)) {
                margin-left: 24px;
            }
        }
    }

    .example-tips {
        display: block;
        margin-top: 6px;
        font-size: 12px;
        color: var(--abc-color-T2);
    }

    .delay-number {
        display: block;
        margin-top: 8px;
    }

    .warning .tips {
        color: #ff9933;
    }
}
</style>
