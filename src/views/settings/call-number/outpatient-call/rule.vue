<template>
    <biz-setting-layout
        v-abc-loading="loading"
        class="rule-setting"
    >
        <biz-setting-content>
            <abc-form ref="form" item-no-margin>
                <biz-setting-form :label-width="112">
                    <biz-setting-form-header>
                        <outpatient-call-tab></outpatient-call-tab>
                    </biz-setting-form-header>

                    <biz-setting-form-group>
                        <biz-setting-form-item label="排队叫号">
                            <biz-setting-form-item-tip tip="开启后，可通过叫号屏显示叫号排队信息，语音呼叫患者就诊">
                                <abc-checkbox
                                    v-model="postData.enableCalling"
                                    type="number"
                                    @input="onChangeInput"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="叫号流程" :has-divider="postData.enableCalling === 1">
                            <biz-setting-form-item-tip tip="开启后，医生可在叫号功能中操作【完诊】并呼叫下一位，但门诊单仍可继续填写">
                                <abc-checkbox
                                    v-model="postData.enableNext"
                                    type="number"
                                    @input="onChangeInput"
                                >
                                    病历未填写完成时可呼叫下一位
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <template v-if="postData.enableCalling === 1">
                            <biz-setting-form-item label="患者姓名隐私保护">
                                <biz-setting-form-item-tip tip="开启后，患者姓名不会完全显示。但语音播报仍然播报全名">
                                    <abc-checkbox
                                        v-model="postData.hidePatientName"
                                        type="number"
                                    >
                                        开启
                                    </abc-checkbox>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="上屏人数" label-line-height-size="medium">
                                <biz-setting-form-item-tip :tip="`当候诊人数超过${ postData.screenCount }人，最多上屏${ postData.screenCount }人`">
                                    <abc-flex align="center" gap="8">
                                        <span>指定数量</span>
                                        <abc-form-item :required="!!postData.enableLockScreen" :validate-event="validateScreenCount">
                                            <abc-input
                                                v-model.number="postData.screenCount"
                                                size="small"
                                                type="number"
                                                :width="56"
                                                :config="{
                                                    supportZero: false
                                                }"
                                            >
                                                <span slot="append">人</span>
                                            </abc-input>
                                        </abc-form-item>
                                    </abc-flex>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="上屏顺序锁定">
                                <biz-setting-form-item-tip tip="开启后，已在叫号屏上显示的患者顺序不可更改">
                                    <abc-checkbox
                                        v-model="postData.enableLockScreen"
                                        type="number"
                                    >
                                        开启
                                    </abc-checkbox>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item
                                label="语音播报内容"
                                class="item-set-custom"
                            >
                                <abc-radio-group v-model="postData.speechTemplateId">
                                    <abc-radio :label="0">
                                        播报“请xx号xx就诊”
                                    </abc-radio>
                                    <abc-radio :label="1">
                                        播报“请xx号xx就诊，请xx号xx候诊”
                                    </abc-radio>
                                </abc-radio-group>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="医生展示">
                                <abc-radio-group v-model="postData.showAllDoctors">
                                    <abc-radio :label="0">
                                        仅显示有待叫号患者的医生
                                    </abc-radio>
                                    <abc-radio :label="1">
                                        显示所有排班的医生
                                    </abc-radio>
                                </abc-radio-group>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="医生姓名隐私保护">
                                <biz-setting-form-item-tip tip="开启后，医生姓名不会完全显示。">
                                    <abc-checkbox
                                        v-model="postData.hideDoctorName"
                                        type="number"
                                    >
                                        开启
                                    </abc-checkbox>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="号数展示">
                                <biz-setting-form-item-tip tip="设置后，叫号屏及语音播报，会显示患者就诊号数">
                                    <abc-checkbox
                                        v-model="postData.showOrderNo"
                                        type="number"
                                    >
                                        展示患者就诊号数
                                    </abc-checkbox>
                                </biz-setting-form-item-tip>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="预约迟到惩罚">
                                <biz-setting-form-item-indent>
                                    <biz-setting-form-item-tip :tip="latePunishTip" :is-warn="!isEnableLatePunish">
                                        <abc-checkbox
                                            v-model="postData.enableLatePunish"
                                            type="number"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>

                                    <template #content>
                                        <div v-if="!!postData.enableLatePunish" class="other">
                                            <abc-space>
                                                超过
                                                <abc-select
                                                    v-model="postData.latePunishInfo.delayMode"
                                                    :width="145"
                                                >
                                                    <abc-option :value="1" label="预计就诊开始时间"></abc-option>
                                                    <abc-option :value="0" label="预计就诊结束时间"></abc-option>
                                                </abc-select>
                                                <abc-input
                                                    v-model.number="postData.latePunishInfo.lateMinutes"
                                                    v-abc-focus-selected
                                                    type="number"
                                                    :width="56"
                                                    :config="{
                                                        max: postData.latePunishInfo.lateHours === 0 ? 300 : 24,
                                                        supportZero: false,
                                                    }"
                                                    @blur="onBlurInput"
                                                >
                                                </abc-input>
                                                <abc-select v-model="postData.latePunishInfo.lateHours" :width="72">
                                                    <abc-option
                                                        v-for="item in unitOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    ></abc-option>
                                                </abc-select>
                                                <span>仍未签到，则判定为迟到</span>
                                            </abc-space>
                                            <template v-if="isFlexibleTimeMode">
                                                <span class="delay-number">
                                                    <abc-space>
                                                        <span style="margin-top: 8px;">迟到患者，预计就诊时间从当前时间延后</span>
                                                        <abc-input
                                                            v-model.number="postData.latePunishInfo.delayMinutes"
                                                            v-abc-focus-selected
                                                            type="number"
                                                            :width="56"
                                                            :config="{
                                                                max: 300, supportZero: false
                                                            }"
                                                            @blur="onBlurInput"
                                                        >
                                                        </abc-input>
                                                        <abc-select v-model="postData.latePunishInfo.delayHours" :width="72">
                                                            <abc-option
                                                                v-for="item in unitOptions"
                                                                :key="item.value"
                                                                :value="item.value"
                                                                :label="item.label"
                                                            ></abc-option>
                                                        </abc-select>
                                                    </abc-space>
                                                    <span class="example-tips">
                                                        如：患者原预计就诊时间10:00~10:30，
                                                        {{ baseTimeAddMinutes(
                                                            postData.latePunishInfo.delayMode === 1 ? '10:00' : '10:30',
                                                            postData.latePunishInfo.lateMinutes
                                                        ) }}签到，判定为迟到，
                                                        预计就诊时间延至{{ baseTimeAddMinutes(baseTimeAddMinutes('10:30',postData.latePunishInfo.lateMinutes),postData.latePunishInfo.delayMinutes) }}开始
                                                    </span>
                                                </span>
                                            </template>
                                            <template v-else>
                                                <span v-if="serviceType === 1" class="delay-number">
                                                    <abc-space>
                                                        <span style="margin-top: 8px;">迟到患者，叫号顺序延后至正在就诊患者后</span>
                                                        <abc-input
                                                            v-model.number="postData.latePunishInfo.delayNo"
                                                            type="number"
                                                            :width="56"
                                                            :config="{
                                                                max: 300, supportZero: false
                                                            }"
                                                            @blur="onBlurInput"
                                                        >
                                                        </abc-input>
                                                        <span>位</span>
                                                    </abc-space>
                                                </span>
                                                <span v-if="isSegmentTime" class="delay-number">
                                                    迟到患者，叫号顺序延后至下一时段队尾
                                                </span>
                                                <span v-if="serviceType === 1" class="example-tips">
                                                    如：患者为上午05号，就诊时间10:00~10:05，超过{{
                                                        baseTimeAddMinutes(
                                                            postData.latePunishInfo.delayMode === 1 ? '10:00' : '10:05',
                                                            addMinitues
                                                        )
                                                    }}未签到，判定为迟到，需等待{{ postData.latePunishInfo.delayNo }}人完成诊后才可就诊
                                                </span>
                                                <span v-if="isSegmentTime" class="example-tips">
                                                    <template v-if="serviceType === 2">
                                                        如：患者为上午05号，就诊时间10:00~10:30，超过{{
                                                            baseTimeAddMinutes(
                                                                postData.latePunishInfo.delayMode === 1 ? '10:00' : '10:30',
                                                                addMinitues
                                                            )
                                                        }}未签到，判定为迟到，将延至下一时段10:30~11:00最后一位叫号
                                                    </template>
                                                    <template v-else>
                                                        如：患者为上午05号，就诊时间8:00~12:00，超过{{
                                                            baseTimeAddMinutes(
                                                                postData.latePunishInfo.delayMode === 1 ? '8:00' : '12:00',
                                                                addMinitues
                                                            )
                                                        }}未签到，判定为迟到，将延至下午最后一位叫号
                                                    </template>
                                                </span>
                                            </template>
                                        </div>
                                    </template>
                                </biz-setting-form-item-indent>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="显示风格">
                                <abc-radio-group v-model="postData.style">
                                    <abc-radio :label="0">
                                        浅色淡雅
                                    </abc-radio>
                                    <abc-radio :label="1">
                                        深色通透
                                    </abc-radio>
                                </abc-radio-group>
                            </biz-setting-form-item>
                        </template>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template slot="footer">
                <biz-setting-footer>
                    <abc-button :loading="btnLoading" :disabled="!isUpdate" @click="onClickSave">
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <abc-dialog
            v-if="showSignInAlert"
            v-model="showSignInAlert"
            class="dialog-signin-alert"
            content-styles="width: 400px; padding: 24px"
        >
            <div class="tips" style="font-size: 16px;">
                为保证叫号功能正常使用，需要开启预约设置中的【预约签到】，预约患者签到确认到店后，才会进入叫号队列。是否开启【预约签到】？
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button @click="onOpenSignInNow">
                    现在开启
                </abc-button>
                <abc-button type="blank" @click="onTemporarilyOpen">
                    暂不开启
                </abc-button>
            </div>
        </abc-dialog>
    </biz-setting-layout>
</template>

<script>
    import clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';
    import CallingAPI from 'api/call';
    import ReservationAPI from 'api/registrations/reservation';
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { parseTime } from '@/filters';
    import { RESERVATION_MODE_TYPE } from 'views/settings/registered-reservation/constant';
    import Clone from 'utils/clone';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
        BizSettingFormItemIndent,
        BizSettingFormHeader,
    } from '@/components-composite/setting-form/index.js';
    import OutpatientCallTab from 'views/settings/call-number/components/outpatient-call-tab/index.vue';


    const unitOptions = [
        {
            label: '分钟', value: 0,
        },
        {
            label: '小时', value: 1,
        },
    ];
    export default {
        components: {
            OutpatientCallTab,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingFormItemIndent,
            BizSettingFormHeader,
        },
        async beforeRouteLeave(to, from, next) {
            if (this.isUpdate) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                unitOptions,
                loading: false,
                btnLoading: false,
                config: null,
                SignInData: null,
                postData: {
                    speechTemplateId: undefined, // 语音播报内容
                    showAllDoctors: undefined, // 医生展示
                    latePunishInfo: {
                        lateMinutes: 30, // 迟到多久
                        lateHours: 0, // 是否以小时算
                        delayNo: 10, // 延后数
                        delayMinutes: 30, // 延后时间
                        delayHours: 0, // 是否以小时算（延后时间单位）
                        delayMode: undefined, // 0: 预计就诊开始时间 1: 预计就诊结束时间
                    },
                    enableCalling: 0, // 开启叫号
                    enableNext: 0, // 病历未填完时可呼叫下一位
                    enableLockScreen: 0, // 开启上屏锁定
                    enableLatePunish: 0, // 迟到惩罚
                    hidePatientName: 0, // 患者姓名隐私保护
                    hideDoctorName: 0, // 医生姓名隐私保护
                    showOrderNo: 0, // 号数展示
                    screenCount: 2, // 上屏人数
                },
                showSignInAlert: false, // 开启预约签到提醒
                serviceType: 0, // 0：按上午/下午/晚上预约；1：按精确时间段预约；2：按自定义时间段预约
            };
        },
        computed: {
            ...mapGetters(['registrationsConfig','isEnableRegUpgrade', 'appointmentConfig']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            /**
             * desc [是否有配置调整更新]
             */
            isUpdate() {
                if (!this.config) return false;
                return !isEqual(this.config, this.postData);
            },
            isFlexibleTimeMode() {
                // modeType 0: 固定号源模式 1: 灵活时间预约模式
                return this.registrationsConfig?.modeType === RESERVATION_MODE_TYPE.FLEXIBLE_TIME;
            },
            addMinitues() {
                if (this.postData.latePunishInfo) {
                    const { latePunishInfo } = this.postData;
                    return latePunishInfo.lateMinutes * (+latePunishInfo.lateHours === 1 ? 60 : 1);
                }
                return 0;
            },
            addNumbers() {
                if (this.postData.latePunishInfo) {
                    return this.postData.latePunishInfo.delayNo;
                }
                return 0;
            },
            isEnableLatePunish() {
                return this.serviceType !== 0;
            },
            isSegmentTime() {
                return [0,2].includes(this.serviceType);
            },
            latePunishTip() {
                return '开启后，可延后迟到患者的叫号顺位，鼓励患者按时到店就诊';
            },
        },
        async created() {
            await this.getConfig();
            await this.fetchReservation();
        },
        methods: {
            ...mapActions(['updateCallConfig']),
            baseTimeAddMinutes(baseTime = '10:00', plusMinutes = 100) {
                const baseDate = new Date();
                const [hours, minutes] = baseTime.split(':');
                baseDate.setHours(+hours);
                baseDate.setMinutes(+minutes);
                const newDate = new Date(baseDate.getTime() + plusMinutes * 60 * 1000);
                return parseTime(newDate, 'h:i');
            },
            baseNumberAddNumbers(baseNumber = '05', plusNumbers = 3) {
                const newNumber = +baseNumber + +plusNumbers;
                if (newNumber < 10) {
                    return `0${newNumber}`;
                }
                return newNumber;
            },
            async fetchReservation() {
                // 新的门诊预约
                if (this.viewDistributeConfig.Settings.reservation.fetchReservationNotFromAPI || this.isEnableRegUpgrade) {
                    await this.$store.dispatch('initOutpatientRegistrationsConfig'); // 避免手动刷新还未拉取到配置信息
                    this.serviceType = this.registrationsConfig.serviceType;
                } else {
                    try {
                        await this.$store.dispatch('initAppointmentConfig');
                        this.serviceType = this.appointmentConfig.serviceType;
                    } catch (e) {
                        console.error(e);
                    }
                }
            },
            /**
             * desc [拉取配置]
             */
            async getConfig() {
                this.loading = true;
                try {
                    const { data } = await CallingAPI.fetchCallingSettings();
                    if (data.config.style === undefined) {
                        // 当没有默认值时，初始化默认值：0 浅色淡雅  1  深色通透
                        data.config.style = 0;
                    }

                    this.config = data.config;
                    this.postData = clone(this.config);
                    this.updateCallConfig(clone(this.config));
                } catch (error) {
                    console.log('getScreenList error', error);
                    this.$Toast({
                        type: 'error',
                        message: error.message,
                    });
                }
                this.loading = false;
            },
            /**
             * descc [点击保存]
             */
            async onClickSave() {
                if (!this.SignInData) {
                    await this.fetchSignIn();
                }
                if (
                    (this.postData.enableCalling === 1 && this.SignInData && this.SignInData.needSignIn === 1) ||
                    this.postData.enableCalling === 0
                ) {
                    if (this.btnLoading) return;
                    this.btnLoading = true;
                    try {
                        this.$refs.form.validate(async (valid) => {
                            if (valid) {
                                const params = clone(this.postData);
                                const { data } = await CallingAPI.updateCallConfig(params);
                                this.config = data;
                                this.postData = clone(this.config);
                                this.updateCallConfig(clone(this.config));
                                this.$Toast({
                                    type: 'success',
                                    message: '保存成功',
                                });
                            }
                        });
                    } catch (error) {
                        this.$Toast({
                            type: 'error',
                            message: error.message,
                        });
                    }
                    this.btnLoading = false;
                } else {
                    this.$Toast({
                        type: 'error',
                        message: '请开启预约签到',
                    });
                }
            },
            /**
             * desc [开启排队叫号]
             */
            async onChangeInput(value) {
                if (value === 1) {
                    if (!this.SignInData) {
                        await this.fetchSignIn();
                    }
                    if (this.SignInData) {
                        if (this.SignInData.needSignIn === 0) {
                            this.showSignInAlert = true;
                        }
                    } else {
                        this.$nextTick(() => {
                            this.postData.enableCalling = 0;
                        });
                    }
                }
            },
            /**
             * desc [查询是否开启预约签到]
             */
            async fetchSignIn() {
                if (this.viewDistributeConfig.Settings.reservation.fetchReservationNotFromAPI || this.isEnableRegUpgrade) {
                    this.SignInData = this.registrationsConfig;
                } else {
                    try {
                        await this.$store.dispatch('initAppointmentConfig');
                        const data = Clone(this.appointmentConfig);
                        this.SignInData = data;
                    } catch (error) {
                        console.log('fetchSignIn error', error);
                    }
                }
            },
            /**
             * desc [现在开启]
             */
            async onOpenSignInNow() {
                try {
                    const postData = clone(this.SignInData);
                    postData.needSignIn = 1;
                    const { newOpenSwitchMethods } = this.viewDistributeConfig.Settings.callNumber;
                    let result = null;
                    if (this.isEnableRegUpgrade || newOpenSwitchMethods) {
                        result = await ReservationAPI.updateReservationConfig(postData);
                    } else {
                        result = await ReservationAPI.updateReservation(postData);
                    }
                    this.SignInData = result?.data || {};
                } catch (error) {
                    console.log('fetchSignIn error', error);
                    this.postData.enableCalling = 0;
                }
                this.showSignInAlert = false;
            },
            /**
             * desc [失焦时]
             */
            onBlurInput() {
                if (!this.postData.latePunishInfo.lateMinutes && this.config) {
                    this.postData.latePunishInfo.lateMinutes = this.config.latePunishInfo.lateMinutes;
                }
                if (this.postData.latePunishInfo.delayNo === '' && this.config) {
                    this.postData.latePunishInfo.delayNo = this.config.latePunishInfo.delayNo;
                }
            },
            /**
             * desc [暂不开启]
             */
            onTemporarilyOpen() {
                this.postData.enableCalling = 0;
                this.showSignInAlert = false;
            },
            validateScreenCount(val, callback) {
                if (val > 20) {
                    callback({
                        validate: false,
                        message: '最多上屏20人',
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/theme.scss';

.rule-setting {
    position: relative;

    .line {
        margin-bottom: 24px;
        border-bottom: 1px solid $P6;
    }

    .item-set {
        .is-empty {
            display: none;
        }

        .other {
            padding-left: 130px;
            margin-top: 10px;
        }
    }

    .item-set-custom {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        > label {
            display: inline-block;
            width: 130px;
            color: $T2;
        }

        .abc-radio {
            height: 16px;

            &:not(:nth-of-type(1)) {
                margin-left: 24px;
            }
        }
    }

    .example-tips {
        display: block;
        margin-top: 6px;
        font-size: 12px;
        color: var(--abc-color-T2);
    }

    .delay-number {
        display: block;
        margin-top: 8px;
    }

    .warning .tips {
        color: #ff9933;
    }
}
</style>
