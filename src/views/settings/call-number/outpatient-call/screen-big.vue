<template>
    <biz-setting-layout
        ref="container"
        class="outpatient-big-screen-setting"
    >
        <biz-setting-content>
            <outpatient-call-tab style="margin-bottom: 24px;"></outpatient-call-tab>

            <template v-if="!devices.length">
                <div class="no-data">
                    <template v-if="!isLoading">
                        <call-number-empty scene="outpatient"></call-number-empty>
                    </template>
                </div>
            </template>
            <template v-else>
                <div v-if="devices.length" class="list-box clearfix border-bottom-dotted-line">
                    <div
                        v-for="item in devices"
                        :key="item.id"
                        :class="{
                            item: true,
                            'big-item': true,
                            active: item.id === curEditorId,
                        }"
                        @click="initPostdata(item)"
                    >
                        <img src="~assets/images/<EMAIL>" alt="" />
                        <span>{{ item.name || '叫号屏' }}</span>
                    </div>

                    <abc-button variant="text" theme="primary" @click="handleOpenTeachDialog">
                        绑定教程
                    </abc-button>
                </div>

                <div class="split-line"></div>

                <template v-if="devices.length">
                    <div :class="['label']">
                        屏幕显示设置
                    </div>
                    <div class="screen-box">
                        <header>
                            <div class="logo-box">
                                <span v-if="!postData.logoUrl" class="placeholder">设置品牌logo</span>
                                <div
                                    v-else
                                    class="logo"
                                    :style="{
                                        'background-image': `url(${postData.logoUrl})`,
                                    }"
                                ></div>
                                <div v-if="postData.logoUrl" class="delete-icon" @click="postData.logoUrl = ''"></div>
                                <input
                                    v-if="!checkedBlob"
                                    type="file"
                                    class="inpt-node"
                                    :disabled="updateLoading"
                                    :title="''"
                                    accept="image/*"
                                    @change="onChangeLogoFile($event)"
                                />
                            </div>
                            <div class="title-box">
                                <abc-input v-model="postData.name" :max-length="10" placeholder="设置候诊区名称"></abc-input>
                            </div>
                            <div class="date-box">
                                <div class="date">
                                    <span>{{ curWeek }}</span>
                                    <span>{{ curDateStr }}</span>
                                </div>
                                <div class="time">
                                    {{ curTime }}
                                </div>
                            </div>
                        </header>
                        <section>
                            <span class="placeholder">关联诊室</span>
                            <vuedraggable
                                v-model="postData.consultingRooms"
                                :animation="300"
                                chosen-class="chosen"
                                ghost-class="ghost"
                                handle=".item"
                                mode="out-in"
                                :move="onMove"
                            >
                                <transition-group tag="div" class="rooms">
                                    <div
                                        v-for="room in [...postData.consultingRooms, {
                                            id: 999
                                        }]"
                                        :key="room.id"
                                    >
                                        <abc-tooltip
                                            content="可拖拽排序"
                                            placement="top-start"
                                            :disabled="room.id === 999"
                                        >
                                            <abc-button
                                                v-if="room.id === 999"
                                                theme="primary"
                                                variant="outline"
                                                icon="n-add-line-medium"
                                                shape="round"
                                                class="add-item"
                                                @click="onClickSelectRooms"
                                            >
                                                诊室
                                            </abc-button>

                                            <div v-else class="item">
                                                <span class="name">{{ room.name }}</span>
                                            </div>
                                        </abc-tooltip>
                                    </div>
                                </transition-group>
                            </vuedraggable>
                        </section>
                        <footer>
                            <abc-input v-model="postData.remindTips" :max-length="200" placeholder="设置滚动提醒内容"></abc-input>
                        </footer>
                    </div>
                </template>
            </template>

            <template v-if="devices.length && !!curEditorId" #footer>
                <biz-setting-footer>
                    <abc-flex justify="space-between" align="center" style="width: 790px;">
                        <abc-button
                            style="min-width: 80px;"
                            :loading="saveLoading"
                            :disabled="!canClickSave"
                            @click="onClickSave"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            type="danger"
                            style="min-width: 80px;"
                            :loading="unbindLoading"
                            @click="onClickUnBind"
                        >
                            解绑
                        </abc-button>
                    </abc-flex>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <abc-editor-img
            v-if="!!checkedBlob"
            :blob="checkedBlob"
            @confirm="onEditorConfirm"
            @cancel="
                checkedBlob = null;
                checkedFile = null;
            "
        ></abc-editor-img>

        <abc-dialog
            v-if="showSelectRooms"
            v-model="showSelectRooms"
            title="设置关联诊室"
            class="dialog-select-rooms"
        >
            <div class="tips" style="font-size: 16px;">
                <!--                    abc-option没有；show-empty: false-->
                <abc-select
                    ref="select"
                    :width="322"
                    :max-width="322"
                    :show-value="showValue"
                    custom-class="select-screen-bind-room"
                    placeholder="请选择"
                    :show-empty="false"
                >
                    <div v-if="consultingRooms.length !== 0" class="room-list-box">
                        <div
                            v-for="item in consultingRooms"
                            :key="item.id"
                            class="abc-option-item"
                            @click="toggleRoom(item)"
                        >
                            <abc-checkbox :value="cacheConsultingRoomIds.includes(item.id)" @click="toggleRoom(item)">
                                {{ item.name }}
                            </abc-checkbox>
                        </div>
                    </div>
                    <div v-else class="no-types">
                        <span>暂无诊室</span>
                    </div>
                </abc-select>
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button @click="onClickSelectConfirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="showSelectRooms = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </biz-setting-layout>
</template>

<script>
    import Api from 'api/settings';
    import CallingAPI from 'api/call';
    import clone from 'utils/clone';
    import {
        isEqual,
    } from 'utils/lodash';
    import ConsultingRoomAPI from 'api/registrations/consulting-room';
    import AbcEditorImg from '@/views/layout/editor-img/index.vue';
    import Vuedraggable from 'vuedraggable';
    import Clone from 'utils/clone';
    import AbcAccess from '@/access/utils.js';
    import { formatDate } from '@tool/date';
    import CallNumberEmpty from 'views/settings/call-number/components/empty.vue';
    import TeachDialog from 'views/settings/call-number/inspect-call/teach-dialog';
    import OutpatientCallTab from 'views/settings/call-number/components/outpatient-call-tab/index.vue';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';

    const TIME_FORMAT = 'HH:mm';
    const DATE_FORMAT = 'YYYY年MM月DD日';
    const weekList = ['星期天', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    export default {
        components: {
            OutpatientCallTab,
            CallNumberEmpty,
            AbcEditorImg,
            Vuedraggable,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
        },
        async beforeRouteLeave(to, from, next) {
            if (this.canClickSave) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                date: new Date(),
                isLoading: false,
                devices: [],
                deviceType: 0, // 0: 大屏  2: 小屏
                curEditorId: '',
                defaultLogo: null,
                postData: {
                    name: '',
                    logoUrl: '',
                    remindTips: '',
                    consultingRooms: [],
                },

                checkedFile: null,
                checkedBlob: null,
                updateLoading: false,
                unbindLoading: false,
                saveLoading: false,

                showSelectRooms: false,
                consultingRooms: null, // 诊室列表

                cacheConsultingRooms: [],
            };
        },
        computed: {
            isPurchased() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.CALLING_NUM_MINI_SCREEN);
            },

            showValue() {
                return this.cacheConsultingRooms.map((item) => item.name).toString();
            },

            cacheConsultingRoomIds() {
                return this.cacheConsultingRooms.map((item) => item.id);
            },

            // 时间展示：12:24
            curTime() {
                return formatDate(this.date, TIME_FORMAT);
            },

            // 星期展示：星期日 ~ 星期六
            curWeek() {
                const week = this.date.getDay();
                return weekList[week];
            },

            // 日期展示：2020年01月01日
            curDateStr() {
                return formatDate(this.date, DATE_FORMAT);
            },

            // 是否可以保存
            canClickSave() {
                let target = {};
                const postData = Clone(this.postData);
                target = this.devices.find((item) => item.id === this.curEditorId);
                if (target) {
                    target.remindTips = target.remindTips === '' ? null : target.remindTips;
                    postData.remindTips = postData.remindTips === '' ? null : postData.remindTips;
                    return !isEqual(target, postData);
                }
                return false;
            },
        },
        created() {
            this.getScreenList();
        },
        methods: {
            /**
             * desc [拉取诊室列表]
             */
            async fetchConsultingRooms() {
                try {
                    const { data } = await ConsultingRoomAPI.getConsultingRooms();
                    this.consultingRooms = data;
                } catch (error) {
                    this.consultingRooms = [];
                    console.log('fetchConsultingRooms error', error);
                }
            },

            /**
             * desc [拉取叫号屏绑定列表]
             */
            async getScreenList() {
                this.isLoading = true;
                const screenLoading = this.$Loading({
                    customClass: 'screen-loading-wrapper',
                });
                if (this.consultingRooms === null) {
                    await this.fetchConsultingRooms();
                }
                if (this.defaultLogo === null) {
                    await this.getClinicInfo();
                }
                try {
                    const { data } = await CallingAPI.fetchCallingSettings();
                    if (data?.devices?.length) {
                        // 大屏列表
                        this.devices = data.devices?.filter((item) => item?.deviceType === 0)?.map((item) => {
                            if (item.status === 0 && !item.logoUrl && this.defaultLogo) {
                                // 当没有设置logo时，同时有诊室默认logo  此时显示默认logo
                                item.logoUrl = this.defaultLogo;
                            }
                            // 1、对已经删除的诊室，进行过滤
                            // 2、对关联诊室，进行排序
                            if (Array.isArray(item.consultingRooms)) {
                                item.consultingRooms = item.consultingRooms
                                    .filter((item) => !!this.consultingRooms.find((one) => one.id === item.id))
                                    .sort((a, b) => {
                                        if (typeof a.sort === 'number' && typeof b.sort === 'number') {
                                            return a.sort - b.sort;
                                        }
                                        return 1;

                                    });
                            }
                            return item;
                        });
                        if (this.devices.length) {
                            this.initPostdata(this.devices[0]);
                        }
                    } else {
                        this.devices = [];
                        this.curEditorId = '';
                    }
                } catch (error) {
                    console.log('getScreenList error', error);
                }
                screenLoading.close();
                this.isLoading = false;
            },

            /**
             * desc [拉取诊所信息]
             */
            async getClinicInfo() {
                try {
                    const { data } = await Api.clinic.fetchClinicInfoById();
                    if (data && data.data) {
                        this.defaultLogo = data.data.logo || '';
                    }
                } catch (error) {
                    console.log('getClinicInfo error', error);
                }
            },

            /**
             * desc [初始postData]
             */
            initPostdata(device) {
                this.curEditorId = device.id;
                this.deviceType = device.deviceType;
                this.postData = clone(device);
            },

            /**
             * desc [点击选中诊室]
             */
            onClickSelectRooms() {
                this.cacheConsultingRooms = this.postData.consultingRooms.slice();
                this.showSelectRooms = true;
            },

            /**
             * desc [切换诊室选中状态]
             */
            toggleRoom(item) {
                const index = this.cacheConsultingRooms.findIndex((one) => one.id === item.id);
                if (index === -1) {
                    this.cacheConsultingRooms.push(item);
                } else {
                    this.cacheConsultingRooms.splice(index, 1);
                }
            },

            /**
             * desc [图片有选中]
             */
            onChangeLogoFile(event) {
                const imageType = ['jpg', 'png', 'gif', 'bmp', 'jpeg'];
                const file = event.target.files[0];
                if (!file) return;
                const fileName = file.name;
                const index = fileName.lastIndexOf('.');
                const ext = fileName.substr(index + 1).toLowerCase();
                if (!imageType.includes(ext)) {
                    return this.$Toast({
                        type: 'error',
                        message: '不支持该文件类型',
                    });
                }
                const reader = new FileReader();
                reader.onload = (e) => {
                    let blob;
                    if (typeof e.target.result === 'object') {
                        // 把Array Buffer转化为blob 如果是base64不需要
                        blob = window.URL.createObjectURL(new Blob([e.target.result]));
                    } else {
                        blob = e.target.result;
                    }
                    this.checkedFile = file;
                    this.checkedBlob = blob;
                };
                // 转化为base64
                // reader.readAsDataURL(file)
                // 转化为blob
                reader.readAsArrayBuffer(file);
            },

            /**
             * desc [编辑图片完成]
             */
            async onEditorConfirm(blob) {
                const file = new File([blob], this.checkedFile.name, {
                    type: this.checkedFile.type,
                });
                // 图片规格超出限制
                const MAX_SIZE = 1024 * 1024 * 20; // 最大限制20M
                if (file.size > MAX_SIZE) {
                    return this.$Toast({
                        type: 'error',
                        message: '上传附件不能超过20M',
                    });
                }
                try {
                    const imgRes = await this.$abcPlatform.service.oss.clinicUsageUpload(
                        { filePath: 'call-number' },
                        file,
                    );
                    this.postData.logoUrl = imgRes.url;
                } catch (e) {
                    return this.$Toast({
                        type: 'error',
                        message: '上传失败，请重新上传',
                    });
                }
                this.checkedFile = null;
                this.checkedBlob = null;
            },

            /**
             * desc [设置诊所确认]
             */
            onClickSelectConfirm() {
                this.postData.consultingRooms = this.cacheConsultingRooms;
                this.showSelectRooms = false;
            },

            /**
             * desc [点击保存设置]
             */
            async onClickSave() {
                if (this.saveLoading) return;
                this.saveLoading = true;
                try {
                    this.postData.consultingRooms.forEach((item, index) => {
                        item.sort = index;
                    });
                    await CallingAPI.updateDeviceConfig(this.curEditorId, this.postData);
                    let item = null;

                    item = this.devices.find((item) => item.id === this.curEditorId);
                    item && Object.assign(item, this.postData);
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                } catch (error) {
                    console.log('updateDevice error', error);
                }
                this.saveLoading = false;
            },

            /**
             * desc [点击解绑触发]
             */
            onClickUnBind() {
                if (this.unbindLoading) return;
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: `解绑后，${this.postData.name}将失效，是否确认？`,
                    onConfirm: async () => {
                        this.unbindLoading = true;
                        try {
                            await CallingAPI.deleteCallConfig(this.curEditorId);
                            this.getScreenList();
                            this.$Toast({
                                type: 'success',
                                message: '解绑成功',
                            });
                        } catch (error) {
                            console.log('onClickUnBind error', error);
                        }
                        this.unbindLoading = false;
                    },
                });
            },

            handleOpenTeachDialog() {
                new TeachDialog({
                    scene: 'outpatient',
                }).generateDialogAsync();
            },

            onMove(e) {
                if (!e.relatedContext?.element) {
                    return false;
                }
                //不允许停靠
                return e.relatedContext.element.id !== 999;
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/abc-common.scss';

.outpatient-big-screen-setting {
    position: relative;
    box-sizing: border-box;
    width: 100%;

    .list-wrapper {
        display: flex;
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 400;
        color: $T2;

        .not-bind {
            .not-bind-devices {
                margin: 0 16px;
                color: $Y3;
            }
        }
    }

    .list-box {
        flex-wrap: wrap;
        gap: 10px;
        padding-bottom: 8px;
        // border-bottom: 1px solid $P6;

        @include flex(row, flex-start, center);

        .item {
            box-sizing: border-box;
            float: left;
            width: 216px;
            height: 64px;
            cursor: pointer;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);

            @include flex(row, center, center);

            &.big-item {
                @include flex(row, flex-start, center);

                padding-left: 16px;
            }

            img {
                width: 36px;
                height: 36px;
                margin-right: 12px;
            }

            span {
                display: inline-block;
                max-width: 140px;
                font-size: 14px;
                font-weight: 500;
                color: $T1;

                @include ellipsis(1);
            }
        }

        .active {
            border: 2px solid $theme2;

            &.big-item {
                padding-left: 15px;
            }
        }

        //&.small-list-box {
        //    .item {
        //        width: 103px;
        //        height: 56px;
        //    }
        //}
    }

    .split-line {
        margin-top: 16px;
        border-bottom: 1px solid $P6;
    }

    .no-data {
        height: 240px;

        span.tit {
            margin-bottom: 8px;
            color: $T2;
        }
    }

    .label {
        width: 816px;
        margin: 24px 0 15px;
        font-size: 14px;
        color: $T2;
        text-align: center;

        &.small-screen-label {
            width: 408px;
        }
    }

    .screen-box {
        width: 816px;
        height: 514px;
        padding: 17px 27px 47px 17px;
        background-image: url('~assets/images/<EMAIL>');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        transform: translateX(-8px);

        > header {
            height: 56px;
            border-bottom: 1px solid $P1;

            @include flex(row, center, stretch);

            .logo-box {
                position: relative;
                flex-shrink: 0;
                width: 215px;
                border-right: 1px solid $P1;

                @include flex(row, center, center);

                .logo {
                    width: 200px;
                    height: 40px;
                    background-repeat: no-repeat;
                    background-position: center center;
                    background-size: auto 100%;
                }

                .inpt-node {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 1;
                    cursor: pointer;
                    opacity: 0;
                }

                .delete-icon {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    z-index: 2;
                    display: none;
                    width: 16px;
                    height: 16px;
                    background: url('~assets/images/<EMAIL>') no-repeat center;
                    background-size: contain;
                    transition: opacity 0.2s;

                    &:hover {
                        background: url('~assets/images/<EMAIL>') no-repeat center;
                        background-size: contain;
                    }
                }
            }

            .logo-box:hover {
                .delete-icon {
                    display: inline-block;
                }
            }

            .title-box {
                flex: 1;

                .abc-input-wrapper {
                    width: 100%;
                    height: 100%;

                    > input {
                        width: 100%;
                        height: 100%;
                        font-size: 18px;
                        font-weight: 500;
                        color: $T1;
                        text-align: center;
                        border-color: rgba(0, 0, 0, 0);

                        &:focus,
                        &:hover,
                        &:active {
                            border-radius: 0 !important;
                        }

                        &::placeholder {
                            font-size: 14px;
                            font-weight: normal;
                        }
                    }
                }
            }

            .date-box {
                flex-shrink: 0;
                width: 215px;
                padding: 0 15px;
                border-left: 1px solid $P1;

                @include flex(row, flex-end, center);

                .date {
                    height: 100%;

                    @include flex(column, center, flex-end);

                    span {
                        font-size: 12px;
                        line-height: 12px;
                        color: $T3;

                        &:last-child {
                            margin-top: 4px;
                        }
                    }
                }

                .time {
                    margin: 0 8px;
                    font-size: 30px;
                    color: $T3;
                }
            }
        }

        > section {
            height: 354px;
            cursor: pointer;

            @include flex(column, center, center);

            .rooms {
                flex-wrap: wrap;
                padding: 0 120px;
                margin-top: 12px;

                @include flex(row, center, flex-start);

                .ghost {
                    .item {
                        background-color: transparent;
                        border: 1px dashed var(--abc-color-P10);

                        .name {
                            color: var(--abc-color-T3);
                        }
                    }
                }

                .chosen {
                    .item {
                        background: #ffffff;
                        border: 1px solid var(--abc-color-P7);
                    }
                }

                .item {
                    box-sizing: border-box;
                    min-width: 82px;
                    height: 28px;
                    padding: 0 10px;
                    margin: 8px 4px 0;
                    user-select: none;
                    background-color: var(--abc-color-P4);
                    border: 1px solid var(--abc-color-P4);
                    border-radius: 28px;
                    outline-style: none;
                    transition: all 0.2s linear;

                    @include flex(row, center, center);

                    .name {
                        margin-left: 6px;
                        font-size: 12px;
                        font-weight: 500;
                        line-height: 12px;
                        color: $T1;
                    }

                    &:hover {
                        background: #ffffff;
                        border: 1px solid var(--abc-color-P7);
                        transition: none;
                    }
                }

                .add-item {
                    height: 28px;
                    margin: 8px 4px 0;
                }
            }
        }

        > footer {
            height: 42px;
            border-top: 1px solid $P1;

            .abc-input-wrapper {
                width: 100%;
                height: 100%;

                > input {
                    width: 100%;
                    height: 100%;
                    font-size: 14px;
                    font-weight: 500;
                    color: $T1;
                    text-align: center;
                    border-color: rgba(0, 0, 0, 0);

                    &:focus,
                    &:hover,
                    &:active {
                        border-radius: 0 !important;
                    }

                    &::placeholder {
                        font-size: 14px;
                        font-weight: normal;
                    }
                }
            }
        }

        .placeholder {
            font-size: 14px;
            color: $T3;
        }

        &.small-screen-box {
            width: 438px;
            height: 495px;
            background-image: url('~assets/images/<EMAIL>');
            background-repeat: no-repeat;
            background-size: 100% 100%;

            > header {
                height: 56px;
                border-bottom: 1px solid $P1;

                @include flex(row, center, stretch);

                .title-box {
                    width: 177px;

                    .select-room {
                        &.abc-select-wrapper .abc-input__inner {
                            height: 56px;
                            font-size: 18px;
                            font-weight: 500;
                            text-align: center;
                            border: 1px solid transparent;
                            border-radius: 0;
                        }
                    }
                }
            }

            > section {
                height: 354px;
                cursor: pointer;

                @include flex(column, center, center);
            }

            > footer {
                height: 42px;
                border-top: 1px solid $P1;

                .abc-input-wrapper {
                    width: 100%;
                    height: 100%;

                    > input {
                        width: 100%;
                        height: 100%;
                        font-size: 14px;
                        font-weight: 500;
                        color: $T1;
                        text-align: center;
                        border-color: rgba(0, 0, 0, 0);

                        &:focus,
                        &:hover,
                        &:active {
                            border-radius: 0 !important;
                        }

                        &::placeholder {
                            font-size: 14px;
                            font-weight: normal;
                        }
                    }
                }
            }
        }
    }

    .handle-box-wrapper {
        background-color: #ffffff;

        .handle-box {
            width: 790px;
            padding: 16px 0;

            @include flex(row, space-between, center);

            &.small-screen {
                width: 410px;
            }
        }
    }
}

.select-screen-bind-room {
    .room-list-box {
        padding: 4px 0;

        .abc-option-item > label {
            color: $T1;
        }
    }

    .no-types {
        height: 120px;
        font-size: 12px;
        color: $T3;

        @include flex(row, center, center);
    }
}

.screen-loading-wrapper {
    left: 17%;
}
</style>
