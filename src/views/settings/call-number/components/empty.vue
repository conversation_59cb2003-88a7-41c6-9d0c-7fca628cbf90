<template>
    <abc-content-empty value="<span class='text tit'>未绑定叫号屏</span>">
        <!--诊所，需要版本提示信息-->
        <template v-if="isCheckByEdition">
            <abc-text theme="gray-light">
                <template v-if="isSmall">
                    {{ isPurchased ? '如需使用，请联系你的一对一专属客服' : '升级「大客户版」可用，' }}
                    <span v-if="!isPurchased" style="color: dodgerblue; cursor: pointer;" @click="updateClinicVersion()">去升级</span>
                </template>

                <template v-else>
                    {{ isPurchased ? '如需使用，请联系你的一对一专属客服' : '如需使用，请联系你的客户成功顾问：4001751775转1' }}
                </template>
            </abc-text>
        </template>

        <template v-else>
            <abc-text theme="gray-light">
                {{ isPurchased ? '如需使用，请联系你的一对一专属客服' : '如需使用，请联系你的客户成功顾问：4001751775转1' }}
            </abc-text>
        </template>

        <div v-if="isPurchased" style="margin-top: 12px;">
            <abc-button theme="primary" variant="ghost" @click="handleOpenTeachDialog">
                绑定教程
            </abc-button>
        </div>
    </abc-content-empty>
</template>

<script>
    import TeachDialog from 'views/settings/call-number/inspect-call/teach-dialog';
    import AbcAccess from '@/access/utils';
    import { mapGetters } from 'vuex';

    export default {
        name: 'CallNumberEmpty',

        props: {
            isSmall: Boolean,

            scene: {
                type: String,
                default: 'outpatient',
            },
        },

        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),

            isCheckByEdition() {
                return this.viewDistributeConfig.Settings.callNumber.isCheckByEdition;
            },
            isPurchased() {
                const map = {
                    outpatient: {
                        big: AbcAccess.accessMap.CALLING_NUM,
                        small: AbcAccess.accessMap.CALLING_NUM_MINI_SCREEN,
                    },
                    pharmacy: {
                        big: AbcAccess.accessMap.DISPENSE_CALL_NUMBER,
                    },
                    inspect: {
                        big: AbcAccess.accessMap.INSPECT_CALLING_NUM,
                        small: AbcAccess.accessMap.INSPECT_CALLING_NUM_MINI_SCREEN,
                    },
                };
                const key = map[this.scene][this.isSmall ? 'small' : 'big'];
                return AbcAccess.getPurchasedByKey(key);
            },
        },

        methods: {
            handleOpenTeachDialog() {
                new TeachDialog({
                    scene: this.scene,
                }).generateDialogAsync();
            },

            updateClinicVersion() {
                this.$router.push({
                    name: 'product-center-upgrade',
                    query: {
                        accessKey: AbcAccess.accessMap.CALLING_NUM_MINI_SCREEN,
                    },
                });
            },
        },
    };
</script>
