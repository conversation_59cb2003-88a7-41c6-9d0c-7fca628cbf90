<template>
    <biz-setting-layout v-abc-loading="contentLoading" class="settings-item-wrapper">
        <biz-setting-content>
            <biz-setting-form :label-width="70">
                <biz-setting-form-header>
                    <abc-tips-card-v2
                        theme="primary"
                        title="费用说明"
                        width="auto"
                    >
                        <div>例：当毛利率为50%时，如处方中药品总销售额为100元，则该处方药品利润为100*50%=50元</div>
                    </abc-tips-card-v2>
                </biz-setting-form-header>

                <biz-setting-form-group
                    class="air-pharmacy-item setting-item"
                    content-full-screen
                >
                    <biz-setting-form-item
                        v-if="isChainAdmin || postData.allowClinicSetting"
                        label="毛利率设置"
                        class="manage-section-item-label"
                        label-line-height-size="medium"
                    >
                        <div class="goods-type-percent-wrapper">
                            <abc-space v-for="item in postData.goodsTypes" :key="item.typeId" class="goods-type-percent">
                                <abc-text theme="gray" size="normal">
                                    {{ percentNameStr(item) }}
                                </abc-text>
                                <abc-input
                                    v-model.number="item.profitsPercent"
                                    v-abc-focus-selected
                                    v-abc-auto-focus
                                    :width="49"
                                    type="number"
                                    max-length="4"
                                    :config="{
                                        supportZero: true, max: 99
                                    }"
                                    :input-custom-style="{ textAlign: 'center' }"
                                    @blur="handleBlur"
                                >
                                    <template #append>
                                        %
                                    </template>
                                </abc-input>
                            </abc-space>
                        </div>
                    </biz-setting-form-item>

                    <biz-setting-form-item v-if="isChainAdmin" label="自主定价">
                        <biz-setting-form-item-tip tip="开启后，各门店可自行设置价格">
                            <abc-checkbox v-model="postData.supportClinicSetting" type="number" @change="handleChange">
                                开启
                            </abc-checkbox>
                        </biz-setting-form-item-tip>

                        <section v-if="postData.supportClinicSetting" class="air-pharmacy-item">
                            <div style="margin-bottom: 8px;">
                                允许自主定价门店
                            </div>
                            <div class="operations">
                                <abc-space wrap>
                                    <template v-for="(item, index) in postData.supportClinics">
                                        <abc-tag-v2
                                            :key="item.id"
                                            variant="outline"
                                            closable
                                            size="huge"
                                            icon="s-organization-color"
                                            @close="deleteClinic(index)"
                                        >
                                            {{ item.shortNameFirst }}
                                        </abc-tag-v2>
                                    </template>
                                    <abc-button
                                        variant="text"
                                        size="small"
                                        @click="showClinicsDialog = true"
                                    >
                                        设置
                                    </abc-button>
                                </abc-space>
                            </div>
                        </section>
                    </biz-setting-form-item>

                    <biz-setting-form-item label="服务药房" label-line-height-size="large">
                        <div>
                            <abc-table
                                style="position: relative; width: 100%;"
                                body-styles="height: auto;min-height: 260px"
                                :data-list="supportVendors"
                                :render-config="vendorConfig"
                                :empty-opt="{ label: '当前地区暂不支持空中药房业务' }"
                            >
                                <template #vendorName="{ trData: item }">
                                    <abc-table-cell class="ellipsis">
                                        <span>
                                            {{ item.vendorName || '' }}
                                        </span>
                                    </abc-table-cell>
                                </template>

                                <template #viewPrice="{ trData: item }">
                                    <abc-table-cell class="ellipsis">
                                        <abc-button variant="text" @click="viewPrice(item)">
                                            零售价明细
                                        </abc-button>
                                    </abc-table-cell>
                                </template>

                                <template #businessScopeName="{ trData: item }">
                                    <abc-table-cell class="ellipsis">
                                        <span>
                                            {{ item.businessScopeName.replace('中药', '') }}
                                        </span>
                                    </abc-table-cell>
                                </template>

                                <template #ruleInfo="{ trData: item }">
                                    <abc-table-cell>
                                        <div class="ellipsis" style="cursor: pointer;" :title="item.ruleInfo">
                                            {{ item.ruleInfo || '' }}
                                        </div>
                                    </abc-table-cell>
                                </template>
                            </abc-table>

                            <abc-pagination
                                :pagination-params="paginationParams"
                                :count="totalCount"
                                @current-change="changePageIndex"
                            >
                            </abc-pagination>
                        </div>
                    </biz-setting-form-item>
                </biz-setting-form-group>
            </biz-setting-form>
        </biz-setting-content>

        <clinic-transfer
            v-if="showClinicsDialog"
            v-model="showClinicsDialog"
            :clinics="postData.supportClinics"
            title="开启门店"
            @confirm="changeClinics"
        ></clinic-transfer>
    </biz-setting-layout>
</template>

<script>
    import AirPharmacyAPI from 'src/api/air-pharmacy';
    const ClinicTransfer = () => import('components/clinic-transfer/index.vue');
    import viewVendorPrice from './view-price-dialog';
    import {
        BizSettingLayout,
        BizSettingContent,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormItem,
        BizSettingFormItemTip,
        BizSettingFormGroup,
    } from '@/components-composite/setting-form/index.js';

    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import BizSettingFormHeader from '@/components-composite/setting-form/src/views/header.vue';

    export default {
        name: 'ServicePharmacy',
        components: {
            BizSettingFormHeader,
            ClinicTransfer,
            BizSettingLayout,
            BizSettingContent,
            BizSettingForm,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingFormGroup,
        },
        data() {
            return {
                contentLoading: false,
                isUpdate: false,
                showClinicsDialog: false,

                postData: {
                    supportClinicSetting: 0,
                    supportClinics: [],
                    goodsTypes: [],
                    allowClinicSetting: 0,
                },
                vendorConfig: {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'vendorName',
                            label: '药房',
                            style: {
                                width: `${200}px`,
                                flex: 0,
                            },
                        },
                        {
                            key: 'viewPrice',
                            label: '',
                            style: {
                                width: `${160}px`,
                                flex: 0,
                            },
                        },
                        {
                            key: 'businessScopeName',
                            label: '经营内容',
                            style: {
                                width: `${180}px`,
                                flex: 0,
                            },
                        },
                        {
                            key: 'ruleInfo',
                            label: '药房简介',
                            style: {
                                width: `${160}px`,
                            },
                        },
                    ],
                },

                fetchParams: {
                    offset: 0,
                    limit: 5,
                },
                totalCount: 0,
                supportVendors: [],
                currentTypeId: null,
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin', 'currentClinic']),
            ...mapGetters('airPharmacy', ['systemConfig']),
            paginationParams() {
                const {
                    limit: pageSize, offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
        },
        watch: {
            postData: {
                handler (val) {
                    this.isUpdate = !isEqual(this._postDataCache, val);
                },
                deep: true,
            },
        },
        created() {
            if (this.isChainAdmin) {
                this.fetchBisChainSetting();
            } else {
                this.fetchBisClinicSetting();
            }
            this.fetchSupportVendors();
            this.initAirPharmacySystemConfig();
        },

        beforeRouteLeave(to, from, next) {
            if (this.isUpdate) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你修改的内容尚未保存，确定要离开吗 ？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },

        methods: {
            ...mapActions('airPharmacy', ['initAirPharmacySystemConfig']),

            percentNameStr(item) {
                const str = item.typeName.replace('中药', '');
                return `${str}毛利率 = `;
            },
            changeClinics(data) {
                this.postData.supportClinics = data.map((item) => {
                    return {
                        id: item.id,
                        shortNameFirst: item.name,
                        clinicId: item.id,
                        clinicName: item.name,
                    };
                });
                this.submit();
            },

            deleteClinic(index) {
                this.postData.supportClinics.splice(index, 1);
            },

            async fetchBisChainSetting() {
                this.contentLoading = true;
                const { data } = await AirPharmacyAPI.fetchBisChainSetting();
                data.supportClinics = data.supportClinics.map((item) => {
                    item.id = item.clinicId;
                    item.name = item.clinicName;
                    return item;
                });
                this._postDataCache = data;
                this.postData = Clone(this._postDataCache);
                this.contentLoading = false;
            },
            async updateBisChainSetting() {
                try {
                    this.contentLoading = true;
                    await AirPharmacyAPI.updateBisChainSetting({
                        supportClinicSetting: this.postData.supportClinicSetting,
                        supportClinics: this.postData.supportClinics,
                        goodsTypes: this.postData.goodsTypes,
                    });
                    this._postDataCache = Clone(this.postData);
                    this.isUpdate = false;
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.contentLoading = false;
                } catch (e) {
                    console.error(e);
                    this.contentLoading = false;
                }
            },

            async fetchBisClinicSetting() {
                this.contentLoading = true;
                const { data } = await AirPharmacyAPI.fetchBisClinicSetting();
                this.postData = data;
                this._postDataCache = Clone(data);
                this.contentLoading = false;
            },
            async updateBisClinicSetting() {
                try {
                    this.contentLoading = true;
                    await AirPharmacyAPI.updateBisClinicSetting({
                        goodsTypes: this.postData.goodsTypes,
                    });
                    this._postDataCache = Clone(this.postData);
                    this.isUpdate = false;
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.contentLoading = false;
                } catch (e) {
                    console.error(e);
                    this.contentLoading = false;
                }
            },

            changePageIndex(index) {
                this.fetchParams.offset = (index - 1) * this.fetchParams.limit;
                this.fetchSupportVendors();
            },

            async fetchSupportVendors() {
                const { data } = await AirPharmacyAPI.fetchSupportVendors(this.fetchParams);
                this.supportVendors = data.rows;
                this.totalCount = data.total;
            },

            viewPrice(item) {
                viewVendorPrice({
                    vendorId: item.vendorId,
                    vendorName: item.vendorName,
                    clinicId: this.currentClinic?.clinicId,
                    goodsTypeId: item.goodsTypeId,
                });
            },

            submit() {
                if (this.isChainAdmin) {
                    this.updateBisChainSetting();
                } else {
                    this.updateBisClinicSetting();
                }
            },

            handleBlur() {
                if (this.isUpdate) {
                    this.postData.goodsTypes.forEach((x) => {
                        if (x.profitsPercent === '') {
                            x.profitsPercent = 0;
                        }
                    });
                    this.submit();
                }
                this.currentTypeId = null;
            },

            handleChange() {
                this.submit();
            },
        },
    };
</script>
