<template>
    <abc-flex class="abc-manage-tabs" align="center" justify="space-between">
        <abc-tabs-v2
            v-abc-sticky-top
            :disable-indicator="option.length === 1"
            :option="option"
            v-bind="$attrs"
            data-cy="abc-manage-tabs"
            size="huge"
            v-on="$listeners"
        ></abc-tabs-v2>

        <slot name="append"></slot>
    </abc-flex>
</template>

<script>
    export default {
        name: 'AbcManageTabs',

        props: {
            option: {
                type: Array,
                required: true,
            },
        },
    };
</script>

<style lang='scss'>
.abc-manage-tabs {
    padding: 0 24px 0 12px;
    border-bottom: 1px solid var(--abc-color-P6);

    >.abc-tabs-v2 {
        flex-shrink: 0;
        border-bottom: none;
    }

    >.abc-tabs-v2.abc-tabs-v2-huge {
        min-height: 56px;
    }
}
</style>
