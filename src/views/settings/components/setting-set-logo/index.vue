<template>
    <div
        :style="{
            width: width, height: height
        }"
        class="setting-set-logo-wrapper"
        :class="{ 'is-uploaded': !!value }"
    >
        <div class="base" @click="handleUpload">
            <abc-text tag="div" theme="gray" size="large">
                <abc-icon icon="s-b-add-line-medium"></abc-icon>
            </abc-text>
            <abc-text theme="gray">
                上传图片
            </abc-text>
        </div>

        <img
            v-if="!!value"
            :src="value"
            class="img-box"
        />

        <div v-show="!!value" class="img-shadow" @click="handleUpload">
            <div class="icon-box">
                <span class="iconfont cis-icon-camera"></span>
            </div>
        </div>

        <abc-editor-img
            v-if="!!checkedBlob"
            :blob="checkedBlob"
            :option="cutPictureOption"
            @confirm="onEditorConfirm"
            @cancel="checkedBlob = null;checkedFile = null"
        ></abc-editor-img>
    </div>
</template>

<script>
    import AbcEditorImg from '@/views/layout/editor-img/index.vue';
    import { createGUID } from '@/utils';
    export default {
        name: 'SettingSetLogo',

        components: {
            AbcEditorImg,
        },

        props: {
            value: {
                type: String,
                default: '',
            },

            filePath: {
                type: String,
                default: 'basic',
            },

            width: {
                type: String,
                default: '300px',
            },

            height: {
                type: String,
                default: '75px',
            },

            cutPictureOption: {
                type: Object,
                default: () => ({
                    fixed: false,
                }),
            },
        },

        data() {
            return {
                checkedFile: null,
                checkedBlob: null,
            };
        },

        methods: {
            onChangeLogoFile(event) {
                const imageType = ['jpg' , 'png' , 'gif' , 'bmp' , 'jpeg'];
                const file = event.target.files[0];
                if (!file) return;
                const fileName = file.name;
                const index = fileName.lastIndexOf('.');
                const ext = fileName.substr(index + 1).toLowerCase();
                if (!imageType.includes(ext)) {
                    return this.$Toast({
                        type: 'error',
                        message: '不支持该文件类型',
                    });
                }
                const reader = new FileReader();
                reader.onload = (e) => {
                    let blob;
                    if (typeof e.target.result === 'object') {
                        // 把Array Buffer转化为blob 如果是base64不需要
                        blob = window.URL.createObjectURL(new Blob([e.target.result]));
                    } else {
                        blob = e.target.result;
                    }
                    this.checkedFile = file;
                    this.checkedBlob = blob;
                };
                // 转化为base64
                // reader.readAsDataURL(file)
                // 转化为blob
                reader.readAsArrayBuffer(file);
            },

            async onEditorConfirm(blob) {
                const file = new File([blob], this.checkedFile.name, {
                    type: this.checkedFile.type,
                });
                //图片规格超出限制
                const MAX_SIZE = 1024 * 1024 * 20; //最大限制20M
                if (file.size > MAX_SIZE) {
                    return this.$Toast({
                        type: 'error',
                        message: '上传附件不能超过20M',
                    });
                }
                try {
                    const imgRes = await this.$abcPlatform.service.oss.clinicUsageUpload(
                        {
                            filePath: this.filePath,
                            fileName: `${createGUID()}_${new Date().getTime()}.png`,
                        },
                        file,
                    );
                    this.$emit('input', imgRes.url);
                } catch (e) {
                    return this.$Toast({
                        type: 'error',
                        message: '上传失败，请重新上传',
                    });
                }
                this.checkedFile = null;
                this.checkedBlob = null;
            },

            handleUpload() {
                // 创建一个文件输入框（input[type="file"]）
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*';
                fileInput.style.display = 'none'; // 不显示 input 元素

                // 监听文件选择事件
                fileInput.addEventListener('change', (...props) => {
                    this.onChangeLogoFile(...props);
                });

                // 将 input 添加到页面上（需要放到一个父元素中才能触发文件选择）
                document.body.appendChild(fileInput);

                // 手动触发点击事件，让用户选择文件
                fileInput.click();

                // 移除 input 元素
                document.body.removeChild(fileInput);
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";

.setting-set-logo-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: var(--abc-border-radius-small);

    &.is-uploaded {
        border: 1px solid var(--abc-color-P1);
    }

    &:hover .img-shadow {
        opacity: 1;
    }

    .base {
        width: 100%;
        height: 100%;
        cursor: pointer;
        border: 1px dashed var(--abc-color-P1);

        &:hover {
            border: 1px solid var(--abc-color-P1);
        }

        @include flex(column, center, center);
    }

    .img-box {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 98;
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        object-fit: cover;
    }

    .img-shadow {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 99;
        background: rgba(0, 0, 0, 0.3);
        opacity: 0;
        transition: all 0.15s ease-out;

        @include flex(row, center, center);

        .icon-box {
            width: 32px;
            height: 32px;
            background-color: #ffffff;
            border-radius: 32px;

            @include flex(row, center, center);

            >span.cis-icon-camera {
                font-size: 14px;
                color: $T1;
            }
        }
    }
}
</style>
