<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :auto-focus="false"
        title="价签纸尺寸设置"
        append-to-body
        class="price-tag-size-dialog"
        content-styles="padding:0;max-height: 276px;min-height:180px"
        size="small"
    >
        <abc-form ref="postForm" item-no-margin>
            <draggable
                v-model="currentOptions"
                :draggable="false"
                @update="handleSortChange"
            >
                <transition-group>
                    <div
                        v-for="(item, index) in currentOptions"
                        :key="`${item.id }${ index }`"
                        :class="{
                            'editing-list-item': index === currentEditIndex,
                            'hover-list-item': hoverIndex === index && currentEditIndex === -1,
                            'not-editing-options': index !== currentEditIndex && currentEditIndex !== -1,
                        }"
                        class="list-item"
                        @mouseenter="changeHoverIndex(index)"
                    >
                        <abc-icon
                            icon="s-paper-color"
                            :size="14"
                            style="margin-right: 8px;"
                        ></abc-icon>
                        <abc-flex style="flex: 1;" align="center" :gap="4">
                            <template v-if="currentEditIndex === index">
                                <abc-form-item
                                    laebl="''"
                                    required
                                >
                                    <abc-input
                                        v-model.trim="item.width"
                                        v-abc-focus-selected
                                        placeholder="长"
                                        :width="56"
                                        :class="{ 'is-editing': index === currentEditIndex }"
                                        :input-custom-style="{
                                            background: 'transparent', textAlign: 'center'
                                        }"
                                        :max-length="10"
                                        :readonly="index !== currentEditIndex"
                                        type="number"
                                        :config="{
                                            max: 99,
                                            supportZero: false,
                                        }"
                                    >
                                        <span slot="append">mm</span>
                                    </abc-input>
                                </abc-form-item>

                                <abc-text>*</abc-text>

                                <abc-form-item
                                    laebl="''"
                                    required
                                >
                                    <abc-input
                                        v-model.trim="item.height"
                                        v-abc-focus-selected
                                        placeholder="宽"
                                        :width="56"
                                        :class="{ 'is-editing': index === currentEditIndex }"
                                        :input-custom-style="{
                                            background: 'transparent', textAlign: 'center'
                                        }"
                                        :max-length="10"
                                        :readonly="index !== currentEditIndex"
                                        type="number"
                                        :config="{
                                            max: 99,
                                            supportZero: false,
                                        }"
                                    >
                                        <span slot="append">mm</span>
                                    </abc-input>
                                </abc-form-item>
                            </template>

                            <template v-else>
                                <abc-text>
                                    {{ item.width }}mm x {{ item.height }}mm
                                </abc-text>
                            </template>
                        </abc-flex>
                        <abc-tooltip
                            :disabled="!item.disabled"
                            :open-delay="500"
                            content="系统默认价签不可删除"
                            placement="right"
                        >
                            <abc-space>
                                <template v-if="currentEditIndex === -1 && index === hoverIndex">
                                    <abc-button
                                        :disabled="item.disabled"
                                        size="small"
                                        variant="text"
                                        @click.stop="handleEditClick(index, item)"
                                    >
                                        修改
                                    </abc-button>
                                    <abc-button
                                        :disabled="item.disabled"
                                        size="small"
                                        theme="danger"
                                        variant="text"
                                        @click="handleDeleteConfirmClick(index, item)"
                                    >
                                        删除
                                    </abc-button>
                                </template>
                                <template v-else-if="index === currentEditIndex">
                                    <abc-button
                                        :disabled="item.disabled"
                                        size="small"
                                        variant="text"
                                        @click.stop="handleConfirmClick(item, index)"
                                    >
                                        确定
                                    </abc-button>
                                    <abc-button
                                        :disabled="item.disabled"
                                        size="small"
                                        variant="text"
                                        @click="handleCancelClick(item, index)"
                                    >
                                        取消
                                    </abc-button>
                                </template>
                            </abc-space>
                        </abc-tooltip>
                    </div>
                </transition-group>
            </draggable>
        </abc-form>

        <div slot="footer">
            <abc-button
                :disabled="currentEditIndex !== -1"
                icon="s-b-add-line-medium"
                variant="text"
                size="small"
                @click="handleAddClick"
            >
                新增
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import Draggable from 'vuedraggable';
    import clone from 'utils/clone';
    import PriceTagAPI from 'api/price-tag';

    export default {
        name: 'RoleManagementDialog',
        components: {
            Draggable,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                currentEditIndex: -1,
                currentEditItem: null,
                hoverIndex: -1,
                currentOptions: [],
            };
        },
        computed: {
            addIcon() {
                return 's-module-color';
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        created() {
            this.initList();
        },
        methods: {
            async initList() {
                try {
                    this.currentOptions = (await PriceTagAPI.getPriceTagSizeTemplates()).map((it) => ({
                        ...it,
                        disabled: it.clinicId === '00000000000000000000000000000000',
                    }));

                    this.$emit('change', this.currentOptions.concat([]));
                } catch (e) {
                    console.warn('initList error', e);
                }
            },
            changeHoverIndex(index) {
                this.hoverIndex = index;
            },
            handleEditClick(index, item) {
                this.currentEditIndex = index;
                this.currentEditItem = clone(item);
                this.focusInput();
            },
            handleCancelClick(item, index) {
                this.currentEditIndex = -1;
                if (this.currentEditItem && this.currentEditItem.id) {
                    this.currentOptions[index].name = this.currentEditItem.name;
                } else {
                    // 新增之后点击取消直接删除该项
                    if (!item.id) {
                        this.currentOptions.splice(index, 1);
                    }
                }
                this.currentEditItem = null;
            },
            async handleConfirmClick(item, index) {
                this.$refs.postForm.validate(async (val) => {
                    if (val) {
                        if (!item.id) {
                            // 新增
                            await this.createHandler(index, item);
                        } else {
                            await this.updateHandler(index, item);
                        }
                    }
                });
            },
            async handleSortChange(data) {
                const {
                    newIndex, oldIndex,
                } = data;
                try {
                    await this.onSortChange(newIndex, oldIndex);
                    this.currentEditItem = null;
                    this.currentEditIndex = -1;
                    this.hoverIndex = newIndex;
                } catch (e) {
                    console.warn('handleSortChange error', e);
                }
            },
            async createHandler(index, item) {
                try {
                    await this.onCreate(index, item);
                    this.currentEditIndex = -1;
                    this.currentEditItem = null;
                    await this.initList();
                } catch (e) {
                    this.$Toast.error(e.message);
                }
            },
            async updateHandler(index, item) {
                try {
                    await this.onUpdate(index, item);
                    this.currentEditIndex = -1;
                    this.currentEditItem = null;
                    await this.initList();
                } catch (e) {
                    this.$Toast.error(e.message);
                }
            },
            async handleDeleteConfirmClick(index, item) {
                try {
                    await this.onDelete(index, item);
                    this.currentOptions.splice(index, 1);
                    this.currentEditIndex = -1;
                    this.currentEditItem = null;
                    await this.initList();
                } catch (e) {
                    this.$Toast.error(e.message);
                }
            },
            handleAddClick() {
                this.currentOptions.push({
                    id: '',
                    width: '',
                    height: '',
                });
                this.currentEditItem = {
                    id: '',
                    name: '',
                    width: '',
                    height: '',
                };
                this.currentEditIndex = this.currentOptions.length - 1;
                this.hoverIndex = this.currentOptions.length - 1;
                this.focusInput();
            },
            focusInput() {
                this.$nextTick(() => {
                    const editInput = document.querySelector('.list-item .is-editing .abc-input__inner');
                    editInput && editInput.focus();
                });
            },
            async onCreate(index, item) {
                await PriceTagAPI.createPriceTagSizeTemplate(item);
            },
            async onUpdate(index, item) {
                await PriceTagAPI.updatePriceTagSizeTemplate(item.id, item);
            },
            async onDelete(index, item) {
                await PriceTagAPI.deletePriceTagSizeTemplate(item.id);
            },
            async onSortChange(newIndex, oldIndex) {
                console.log('onSortChange index', newIndex, oldIndex);
            },
        },
    };
</script>
<style lang="scss">
.price-tag-size-dialog {
    padding: 0;

    .list-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 40px;
        padding: 0 24px;
        cursor: pointer;

        &:not(:last-child) {
            border-bottom: 1px solid var(--abc-color-P4);
        }

        &.hover-list-item:hover {
            background-color: var(--abc-color-P4);
        }

        &.editing-list-item {
            box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
        }

        &.not-editing-options {
            .abc-input__inner {
                color: var(--abc-color-T2);
            }
        }

        &.disabled-list-item {
            cursor: not-allowed;

            .abc-input__inner {
                color: var(--abc-color-T3);
                cursor: not-allowed;
            }
        }
    }
}
</style>
