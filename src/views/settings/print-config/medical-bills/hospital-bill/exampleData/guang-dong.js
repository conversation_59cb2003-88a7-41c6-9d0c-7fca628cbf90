import i18n from '@/i18n';

export const GUAND_DONG_EXAMPLE_DATA = {
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    prepaidSettleSummary: {
        hospitalAdvancePayment: 1000,
        refundedFee: 1200,
        repaidFee: 1300,
        prepaidFee: 6000,
    },
    buyerTaxNum: '10000000000000000000',
    buyerSex: '女',
    buyerName: '任盈盈',
    inpatientDays: 10,
    hospitalNo: '0000001',
    caseNumber: '**********',
    inHospitalDate: '2025-04-01',
    outHospitalDate: '2025-04-10',
    payee: '令狐冲',
    invoiceDate: '2025-04-10 10:00:00',
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                    discountedPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                    discountedPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                    discountedPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305903',
            chargeFormItems: [
                {
                    id: 'ffebc4a7da95425489aeeb456b0c43ec',
                    name: '推拿',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 50.0,
                    discountedPrice: 50.0,
                },
                {
                    id: 'eb2a534087c34b18934c84f5af292fd6',
                    name: '肩周炎针灸治疗',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 90.0,
                    discountedPrice: 90.0,
                },
            ],
            sourceFormType: 3,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedlPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedlPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedlPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
            'discountedlPrice': 2289,
        },
        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    discountedPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                    discountedPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                    discountedPrice: 20.0,
                    goodsStockInfos: [
                        {
                            'stockId': '100013473',
                            'batchNo': '',
                            'expiryDate': '',
                            'manufacturer': '贵州肾元',
                            'manufacturerFull': '贵州肾元制药有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },

            ],
            sourceFormType: 4,
        },
        {
            id: '9410ffd3ece8439e9e12c8f3df396bc8',
            chargeFormItems: [
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    discountedPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                    name: '盐知母',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    discountedPrice: 36.0,
                    specialRequirement: '包煎',
                },
                {
                    id: '7ef3ac794a034b4e952031d4b14b18c1',
                    name: '盐黄柏',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 0.03,
                    discountedPrice: 0.03,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9072',
                    name: '山药YG',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    discountedPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9073',
                    name: '牡丹皮YG',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    discountedPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9076',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    discountedPrice: 16.2,
                    specialRequirement: '先煎',
                },
            ],
            sourceFormType: 6,
            specification: '中药饮片',
            doseCount: 1,
            dailyDosage: '1日1剂',
            usage: '煎服',
            freq: '1日3次',
            usageLevel: '每次150ml',
        },
        {
            id: 'a96615e57a564538a4df211eee45278c',
            chargeFormItems: [
                {
                    id: '9fb64806a842400b8793dd02f699f2e6',
                    name: '热敏灸盒',
                    unit: '盒',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                    discountedPrice: 20.0,
                },
            ],
            sourceFormType: 9,
        },
        {
            id: 'a96615e57a564538a4df211eee45278c01',
            chargeFormItems: [
                {
                    id: '9fb64806a842400b8793dd02f699f2e7',
                    name: '一次性针灸针',
                    unit: '支',
                    count: 10.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 8.0,
                    discountedPrice: 8.0,
                },
            ],
            sourceFormType: 9,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号
    invoiceFee: 666.11,

    patientOrderNo: '**********',
    memberCardBalance: null,
    memberCardMobile: '',
    memberCardBeginningBalance: '', // 会员卡原有余额
    healthCardBeginningBalance: '567.68', // 社保卡原有余额
    healthCardOwnerRelationToPatient: '父女', // 持卡人关系
    healthCardBalance: '0.00', // 社保卡余额
    healthCardNo: '********', // 社保卡卡号
    healthCardOwner: '任我行', // 持卡人姓名"
    serialNo: '********', // 门诊流水号"

    healthCardId: '********', // 医保编号
    healthCardAccountPaymentFee: '19.99', // 帐户支付金额
    healthCardFundPaymentFee: 20, // 统筹支付金额
    healthCardOtherPaymentFee: '10', // 其它支付金额
    healthCardCardOwnerType: '职工', // 医保类型 职工 居民 离休干部
    healthCardSelfConceitFee: '11', // 自负金额
    healthCardSelfPayFee: '22', // 自费金额
    personalPaymentFee: '33', // 个人现金支付

    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: i18n.t('registrationFeeName'),
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '********9999', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        medType: '普通门诊',
        extraInfo: {
            // 重庆社保数据
            civilServiceSubsidy: 0, // 公务员补助
            largeClaimAmount: 0, // 大额理赔金额
            civilServiceBack: 0, // 历史起付线公务员返还
            singleDiseaseSupport: 0, // 单病种定点医疗机构垫支
            civilAidAmount: 0, // 民政救助金额
            civilAidOutpatientBalance: 0, // 民政救助门诊余额
            totalSubstituteFee: 0, // 总共抵用费用
            healthHelpFundFee: 0, // 健康扶贫医疗基金
            precisionPovertyFee: 0, // 精准脱贫保险金额
            otherPovertyReliefFee: 0, // 其他扶贫报销金额

            // 杭州社保数据
            curYearBalance: 0, // 当年账户余额
            allYearBalance: 0, // 历年账户余额
            curYearAccountPaymentFee: 0, // 本年账户支付
            allYearAccountPaymentFee: 0, // 历年账户支付
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 0, // 自负金额
            allYearAccountPaymentSelfConceitFee: 0, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 0, // 自理金额
            allYearAccountPaymentPersonalHandled: 0, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 0, // 自费金额
            allYearAccountPaymentPersonalPayment: 0, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 0, // 本年门诊起付标准支付累计 （省医保为空）

            // 武汉数据
            communityThreeFree: 0, // 社区三免
            lowPaidLine: 0, // 起付线
            superCappingLine: 0, // 超顶封线
            medicalInsuranceFee: 0, // 列入医保费用
            selfPaymentFee: 0, // 纯自费项目金额
            settleOrderNo: 'MZJS000000000', // 结算序列号
            medicalRecordNumber: '000000', // 就诊记录号

            // 青岛数据
            individualAffordabilityLine: '9.9', //个人负担起付线

            optFundPayCum: 20,
            ownPay: 22,

        },
    },
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    normalInvoice: {
        invoiceCode: '*********',
        invoiceNumber: '**********',
    },
    invoiceCode: '20250401',
    invoiceNumber: '**********',
};
export const QING_DAO_EXAMPLE_DATA = {
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '9410ffd3ece8439e9e12c8f3df396bc8',
            chargeFormItems: [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedlPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedlPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedlPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    discountedPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                    name: '盐知母',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    discountedPrice: 36.0,
                    specialRequirement: '包煎',
                    ownExpenseFee: 36,
                },
                {
                    id: '7ef3ac794a034b4e952031d4b14b18c1',
                    name: '盐黄柏',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 0.03,
                    discountedPrice: 0.03,
                    specialRequirement: '先煎',
                    ownExpenseFee: 0.63,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    discountedPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16.2,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9072',
                    name: '山药YG',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    discountedPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9073',
                    name: '牡丹皮YG',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    discountedPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     discountedPrice: 16.2,
                //     specialRequirement: '先煎',
                //     ownExpenseFee: 16,
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                //     name: '盐知母',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 36.0,
                //     specialRequirement: '包煎',
                // },
                // {
                //     id: '7ef3ac794a034b4e952031d4b14b18c1',
                //     name: '盐黄柏',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 1.0,
                //     doseCount: 1.0,
                //     totalPrice: 0.03,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9072',
                //     name: '山药YG',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9073',
                //     name: '牡丹皮YG',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },{
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                //     name: '盐知母',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 36.0,
                //     specialRequirement: '包煎',
                // },
                // {
                //     id: '7ef3ac794a034b4e952031d4b14b18c1',
                //     name: '盐黄柏',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 1.0,
                //     doseCount: 1.0,
                //     totalPrice: 0.03,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9072',
                //     name: '山药YG',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9073',
                //     name: '牡丹皮YG',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
            ],
            sourceFormType: 6,
            specification: '中药饮片',
            doseCount: 1,
            dailyDosage: '1日1剂',
            usage: '煎服',
            freq: '1日3次',
            usageLevel: '每次150ml',
        },

        // {
        //     id: '338adf3126c141e0ab38d5de35e9305902',
        //     chargeFormItems: [
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //     ],
        //     sourceFormType: 2,
        // },

    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            payModeDisplayName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '银行卡',
            payModeDisplayName: '银行卡',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号
    diagnosisInfos: [
        {
            code: 'L55.900',
            diseaseType: null,
            name: '晒斑[晒伤]',
        },
    ],
    inHospitalDate: '2025-04-01', // 入院日期
    outHospitalDate: '2025-05-12', // 出院日期
    inpatientDays: 42, // 住院天数
    caseNumber: '**********', // 住院号
    invoiceFee: 64, // 总费用
    payee: '令狐冲', // 收款人
    prepaidSettleSummary: {
        hospitalAdvancePayment: 1000,
        refundedFee: 1200,
        repaidFee: 1300,
        prepaidFee: 6000,
    },

    patientOrderNo: '**********',
    memberCardBalance: null,
    memberCardMobile: '',
    memberCardBeginningBalance: '', // 会员卡原有余额
    healthCardBeginningBalance: '567.68', // 社保卡原有余额
    healthCardOwnerRelationToPatient: '父女', // 持卡人关系
    healthCardBalance: '0.00', // 社保卡余额
    healthCardNo: '********', // 社保卡卡号
    healthCardOwner: '任我行', // 持卡人姓名"
    serialNo: '********', // 门诊流水号"

    healthCardId: '********', // 医保编号
    healthCardAccountPaymentFee: '19.99', // 帐户支付金额
    healthCardFundPaymentFee: 20, // 统筹支付金额
    healthCardOtherPaymentFee: '10', // 其它支付金额
    healthCardCardOwnerType: '职工', // 医保类型 职工 居民 离休干部
    healthCardSelfConceitFee: '11', // 自负金额
    healthCardSelfPayFee: '22', // 自费金额
    personalPaymentFee: '33', // 个人现金支付

    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },

        {
            name: i18n.t('registrationFeeName'),
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
    ],

    shebaoPayment: {
        cardId: '********', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        selfHandledPaymentFee: 0.00, //自理金额
        medType: '普通门诊',
        extraInfo: {
            cvlservPay: 0.00, // 公务员补助
            hifobPay: 0.00, // 职工大额医疗费用补助基金支出
            hifesPay: 0.00, // 企业补充医疗保险基金支出
            hifmiPay: 0.00, // 居民大病保险资金支出
            mafPay: 0.00, // 医疗救助基金支出
            psnCashPay: 0.00, // 个人现金支出
            hifpPay: 0.00, // 基本医疗保险统筹基金支出
            actPayDedc: 0.00, // 实际支付起付线
            othPay: 0.00, // 其他支出
            fulamtOwnpayAmt: 0.00, //  全自费金额
            overlmtSelfpay: 0.00, // 超限价自费费用
            preselfpayAmt: 0.00, // 先行自付金额
            inscpScpAmt: 0.00, // 符合政策范围金额
            acctMulaidPay: 0.00, // 个人账户共济支付金额
            hospPartAmt: 0.00, // 医院负担金额
            psnNo: 0.00, //   人员编号
            iptOtpNo: 0.00, //   住院/门诊号
            insutype: 0.00, // 险种类型
            // 青岛数据
            individualAffordabilityLine: '9.9', //个人负担起付线
        },
    },
};

