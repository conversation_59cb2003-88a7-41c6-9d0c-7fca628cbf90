<template>
    <div class="print-tab-wrapper">
        <div
            v-for="(item, index) in options"
            :key="item.value"
            class="print-tab-item"
            :class="{ 'is-selected': item.value === value }"
            @click="handleTabChange(item, index)"
        >
            {{ item.label }}
        </div>
    </div>
</template>

<script>
    export default {
        name: 'PrintTab',
        props: {
            options: {
                type: Array,
                required: true,
            },
            value: {
                type: String,
                required: true,
            },
        },
        methods: {
            handleTabChange(item, index) {
                this.$emit('change', item, index);
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme";

.print-tab-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 10px;
    align-items: center;
    width: 604px;
    margin-bottom: 24px;

    .print-tab-item {
        min-width: 84px;
        height: 36px;
        padding: 8px 5px;
        color: $T2;
        text-align: center;
        cursor: pointer;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);

        &.is-selected {
            color: #005ed9;
            background-color: #f0f7ff;
            border-color: #3495ff;
        }
    }
}
</style>
