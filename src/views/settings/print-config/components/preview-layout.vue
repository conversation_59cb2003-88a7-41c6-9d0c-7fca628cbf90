<template>
    <div
        class="print-preview-layout"
        :class="{
            'is-ticket-preview-layout': isTicket,'is-tag-preview-layout': isTag, 'is-esc-pos-ticket-preview-layout': isEscPosTicket
        }"
    >
        <img src="~assets/images/<EMAIL>" alt="" class="preview-img" />
        <div v-if="$slots.previewTab" class="preview-tab">
            <slot name="previewTab"></slot>
        </div>
        <div
            v-if="$slots.previewHtml"
            class="preview-html-wrapper abc-page abc-page_preview"
            :class="{ 'has-tab': $slots.previewTab }"
            :style="pageStyle"
        >
            <div class="preview-html" :class="[customClass]" :style="customStyle">
                <slot name="previewHtml"></slot>
            </div>
        </div>
        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: 'PreviewLayout',
        props: {
            isTicket: {
                type: Boolean,
                default: false,
            },
            isTag: {
                type: Boolean,
                default: false,
            },
            isEscPosTicket: {
                type: Boolean,
                default: false,
            },
            isEscPos80Ticket: {
                type: Boolean,
                default: false,
            },
            customClass: {
                type: String,
                default: '',
            },
            customStyle: {
                type: Object,
                default: () => ({}),
            },
            pageStyle: {
                type: Object,
                default: () => ({}),
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.print-preview-layout {
    position: absolute;
    top: 0;
    right: 1px;
    z-index: 1701;
    width: 100%;
    min-width: 100%;
    height: 100%;
    padding: 0 24px;
    overflow: hidden;
    overflow-y: scroll;
    background-color: $P5;
    border-left: 1px solid $P6;
    box-shadow: 1px 0 0 0 $P6;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &.is-ticket-preview-layout {
        width: 360px;
        padding: 0 80px 0;

        .preview-html-wrapper {
            height: auto;
            padding: 4mm 4mm 0 4mm;
        }

        .preview-html {
            width: 78mm;
            height: auto;
            transform: scale(0.9);
        }
    }

    &.is-esc-pos-ticket-preview-layout {
        width: 368px;
        padding: 0 76px 0;

        .preview-html-wrapper {
            height: auto;
            padding: 4mm 4mm 0 4mm;
        }

        .preview-html {
            width: 384px;
            height: auto;
        }

        &.is-esc-pos-80-ticket-preview-layout {
            width: 440px;
            padding: 0 40px 0;

            .preview-html {
                transform: scale(0.6);
            }
        }
    }

    &.is-cashier-a5 {
        .preview-html-wrapper {
            height: 730px;
            padding: 3mm;
            margin: 78px auto 0;
            overflow: hidden;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);

            &.has-tab {
                margin-top: 24px;
            }
        }

        .preview-html {
            position: relative;
            width: 142mm;
            height: 1010px;
            transform: scale(0.7);
            transform-origin: left top;
        }
    }

    &.is-a4-preview-layout {
        &.is-inspect-report-preview-layout {
            .preview-html-wrapper {
                padding: 8mm;
            }
        }

        .preview-html-wrapper {
            width: 210mm;
            height: 297mm;
            padding: 4mm;
            margin: 78px auto 0;
            overflow: hidden;
            //transform: scale(0.5);
            //transform-origin: left top;
            zoom: 0.5;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
        }

        .preview-html {
            position: relative;
            width: 100%;
            height: 100%;
            transform: scale(1);
        }
    }

    &.is-a4-preview-layout-more-padding-15 {
        .preview-html-wrapper {
            padding: 15mm;
        }
    }

    &.is-a5-preview-layout {
        .preview-html-wrapper {
            width: 148mm;
            height: 210mm;
            padding: 4mm;
            margin: 78px auto 0;
            overflow: hidden;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
            transform: scale(0.71);
            transform-origin: left top;
        }

        .preview-html {
            position: relative;
            width: 100%;
            height: 100%;
            transform: scale(1);
        }
    }

    &.is-a5-prescription-preview-layout {
        .preview-html-wrapper {
            width: 148mm;
            height: 210mm;
            padding: 26px 40px;
            margin: 78px auto 0;
            overflow: hidden;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
            transform: scale(0.71);
            transform-origin: left top;
        }

        .preview-html {
            position: relative;
            width: 100%;
            height: 100%;
            transform: scale(1);
        }
    }

    &.is-a5-prescription-landscape-preview-layout {
        .preview-html-wrapper {
            width: 210mm;
            height: 148mm;
            padding: 26px 40px;
            margin: 78px auto 0;
            overflow: hidden;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
            transform: scale(0.5);
            transform-origin: left top;
        }

        .preview-html {
            position: relative;
            width: 100%;
            height: 100%;
            transform: scale(1);
        }
    }

    &.is-a4-preview-layout-more-padding {
        .preview-html-wrapper {
            padding: 11mm !important;
        }
    }

    &.is-height-level3-preview-layout {
        .preview-html-wrapper {
            width: 210mm;
            height: 93mm;
            padding: 4mm;
            margin: 78px auto 0;
            overflow: hidden;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
            transform: scale(0.5);
            transform-origin: left top;
        }

        .preview-html {
            position: relative;
            width: 100%;
            height: 100%;
            transform: scale(1);
        }
    }

    &.is-tag-preview-layout {
        width: 360px;
        padding: 0 80px 0;

        .preview-html-wrapper {
            height: auto;
            padding: 0;
        }

        .preview-html {
            width: auto;
            height: 40mm;
            transform: initial;
        }
    }

    &.is-tianjin-chinese {
        .preview-html-wrapper {
            width: 200mm;
            height: 127mm;
            padding: 0;
            transform: scale(0.5);
            transform-origin: left top;

            .preview-html {
                width: 200mm;
                height: 127mm;
                transform: scale(1);
            }
        }
    }

    &.is-tianjin-western {
        .preview-html-wrapper {
            width: 150mm;
            height: 203mm;
            padding: 0;
            transform: scale(0.7);
            transform-origin: left top;

            .preview-html {
                width: 150mm;
                height: 203mm;
                transform: scale(1);
            }
        }
    }

    .preview-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 48px;
        height: 48px;
    }

    .preview-html-wrapper {
        height: 568px;
        padding: 3mm;
        margin: 78px auto 0;
        overflow: hidden;
        background: $S2;
        border: 1px solid $P1;
        box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);

        &.has-tab {
            margin-top: 24px;
        }
    }

    .preview-html {
        position: relative;
        width: 142mm;
        height: 204mm;
        transform: scale(0.7);
        transform-origin: left top;
    }

    .preview-tab {
        display: flex;
        justify-content: center;
        width: 100%;
        padding: 34px 78px 0;

        .preview-tab-item {
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            color: $T2;
            cursor: pointer;

            &:not(:last-child) {
                margin-right: 24px;
            }

            &.preview-tab-item__active {
                font-weight: 700;
                color: $T1;
            }
        }
    }

    [data-type~=footer] {
        position: absolute;
        bottom: 0;
        width: 100%;

        .print-row {
            overflow: initial;
        }
    }

    .next-page {
        display: none;
    }

    .prev-page {
        display: none;
    }
}

@media screen and (max-width: 1366px) {
    .print-preview-layout {
        zoom: 0.9;
    }
}

@media screen and (max-width: 1280px) {
    .print-preview-layout {
        zoom: 0.8;
    }
}
</style>
