<template>
    <div class="relate-project-table-wrapper">
        <abc-form>
            <abc-table type="excel" custom :support-delete-tr="!disabled">
                <abc-table-body>
                    <abc-table-tr
                        v-for="(item,i) in relateGoodsList"
                        :key="i"
                        @delete-tr="handleDeleteGoodsItem(i)"
                    >
                        <!-- 项目名 -->
                        <abc-table-td style="flex: 1;">
                            <div class="text-ellipsis" :title="item.name">
                                {{ item.name }}
                            </div>
                        </abc-table-td>

                        <!-- 数量 -->
                        <abc-table-td :width="columnWidth[1]" custom-td>
                            <abc-form-item required hidden-red-dot>
                                <abc-input
                                    v-model="item[getCountPropName(item)]"
                                    v-abc-focus-selected
                                    type="number"
                                    :disabled="disabled"
                                >
                                </abc-input>
                            </abc-form-item>
                        </abc-table-td>

                        <!-- 单位 -->
                        <abc-table-td :width="columnWidth[2]" custom-td>
                            <abc-select
                                v-model="item.composeUseDismounting"
                                size="small"
                                adaptive-width
                                no-icon
                                :show-value="getItemUnitValue(item)"
                                :title="getItemUnitValue(item)"
                                :disabled="disabled"
                                @change="v => handleUnitChange(v,i)"
                            >
                                <abc-option
                                    v-for="(it,k) in getGoodsUnitList(item)"
                                    :key="k"
                                    v-bind="it"
                                >
                                </abc-option>
                            </abc-select>
                        </abc-table-td>
                    </abc-table-tr>
                </abc-table-body>

                <abc-table-footer>
                    <abc-autocomplete
                        v-model="keyword"
                        v-abc-focus-selected
                        :fetch-suggestions="fetchSuggestion"
                        :max-length="20"
                        async-fetch
                        focus-show
                        style="width: 100%;"
                        placeholder="输入项目名称或简拼"
                        :disabled="disabled"
                        @enterEvent="selectGoodsItem"
                        @blur="blurHandler"
                    >
                        <abc-icon
                            slot="prepend"
                            icon="plus"
                            size="12"
                            color="#D9DBE3"
                        ></abc-icon>

                        <template slot="suggestions" slot-scope="{ suggestion }">
                            <div
                                class="relate-project-table__suggestions-item"
                                @mousedown="selectGoodsItem(suggestion)"
                            >
                                <span class="goods-name">
                                    {{ suggestion.displayName }}
                                </span>

                                <span class="goods-price">
                                    <abc-currency-symbol-icon color="#7a8794"></abc-currency-symbol-icon>
                                    {{ formatMoney(suggestion.packagePrice) }}
                                </span>
                            </div>
                        </template>
                    </abc-autocomplete>
                </abc-table-footer>
            </abc-table>
        </abc-form>
    </div>
</template>

<script>
    import GoodsAPI from 'api/goods/index-v3';
    import {
        GoodsTypeEnum,
    } from '@abc/constants';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import { formatMoney } from '@/filters';
    import { UNIT_TYPE } from './constant';

    export default {
        name: 'RelateProjectTable',
        components: {
            AbcCurrencySymbolIcon,
        },
        props: {
            goodsList: {
                type: Array,
                default: () => [],
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                suggestions: [],
                keyword: '',
            };
        },
        computed: {
            relateGoodsList: {
                get() {
                    return this.goodsList;
                },
                set(v) {
                    this.$emit('update:goodsList', v);
                },
            },
            columnWidth() {
                return [174, 64, 64];
            },
        },
        methods: {
            // 获取goods的单位
            getGoodsUnitList(goods) {
                const res = [];
                const {
                    dismounting,
                    pieceUnit,
                    packageUnit,
                } = goods;

                if (dismounting) {
                    if (pieceUnit) {
                        res.push({
                            label: pieceUnit,
                            value: UNIT_TYPE.small,
                        });
                    }
                    if (packageUnit && packageUnit !== pieceUnit) {
                        res.push({
                            label: packageUnit,
                            value: UNIT_TYPE.big,
                        });
                    }
                } else {
                    if (packageUnit) {
                        res.push({
                            label: packageUnit,
                            value: UNIT_TYPE.big,
                        });
                    }
                }
                return res;
            },

            async fetchSuggestion(key, callback) {
                try {
                    const { data } = await GoodsAPI.searchGoods({
                        keyword: key,
                        jsonType: [ { type: GoodsTypeEnum.MATERIAL } ],
                    });

                    this.suggestions = data.list || [];
                    callback(this.suggestions);
                } catch (e) {
                    console.error('获取耗材goods失败', e);
                }
            },

            selectGoodsItem(item) {
                const goodsList = [
                    ...this.relateGoodsList,
                    {
                        ...item,
                        composePackageCount: 1,
                        composePieceCount: 1,
                        composeUseDismounting: item.dismounting, //如果可以拆零，默认小单位
                    },
                ];

                this.relateGoodsList = goodsList;
            },

            blurHandler() {
                this.keyword = '';
                this.suggestions = [];
            },

            handleDeleteGoodsItem(i) {
                const goodsList = this.relateGoodsList.filter((_,idx) => idx !== i);

                this.relateGoodsList = goodsList;
            },

            getCountPropName(item) {
                const {
                    composeUseDismounting,
                } = item;

                const map = {
                    [UNIT_TYPE.big]: 'composePackageCount',
                    [UNIT_TYPE.small]: 'composePieceCount',
                };

                return map[composeUseDismounting];
            },

            getItemUnitValue(goods) {
                const {
                    composeUseDismounting,
                    packageUnit,
                    pieceUnit,
                } = goods;

                const map = {
                    [UNIT_TYPE.big]: packageUnit,
                    [UNIT_TYPE.small]: pieceUnit,
                };

                return map[composeUseDismounting] || '';
            },

            handleUnitChange(v, idx) {
                const map = {
                    [UNIT_TYPE.big]: 'composePackageCount',
                    [UNIT_TYPE.small]: 'composePieceCount',
                };

                const setPropName = map[v];
                const getPropName = v === UNIT_TYPE.big ? 'composePieceCount' : 'composePackageCount';

                this.$set(
                    this.goodsList[idx],
                    setPropName,
                    this.goodsList[idx][getPropName],
                );
            },

            formatMoney,
        },
    };
</script>

<style lang="scss">
.relate-project-table__suggestions-item {
    display: flex;
    align-items: center;
    min-height: 36px;
    padding: 0 12px;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    user-select: none;
    border-bottom: 1px solid #e6eaee;

    &:hover,
    &.selected {
        background-color: #eff3f6;
    }

    .goods-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .goods-price {
        width: 80px;
    }
}

.relate-project-table-wrapper {
    .abc-autocomplete-wrapper {
        .abc-input__inner {
            padding: 0 24px;
        }
    }

    .text-ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
