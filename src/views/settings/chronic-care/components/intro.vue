<template>
    <biz-setting-layout>
        <biz-setting-content>
            <div class="intro-wrapper">
                <img src="~assets/images/intro.png" alt="" class="icon-bg" />

                <div class="intro-title">
                    慢病管理系统
                </div>

                <div class="intro-tip">
                    结合权威指南打造的慢病管理全套解决方案，赋能{{ $app.institutionTypeWording }}零门槛开展新业务，增加收入
                </div>

                <div v-if="!isPurchased" class="operate-wrapper">
                    <div @click="update">
                        立即升级
                    </div>

                    <span>升级「专业版」使用</span>
                </div>
            </div>

            <div class="card-wrapper">
                <div v-for="(item, index) in cardList" :key="index">
                    <img :src="item.url" alt="" />
                    <div class="card-title">
                        {{ item.title }}
                    </div>
                    <div class="card-tip">
                        {{ item.label }}
                    </div>
                </div>
            </div>

            <div class="feature-wrapper" style="padding-left: 24px;">
                <div class="feature-title">
                    功能特效一览
                </div>

                <div
                    v-for="(item, index) in features"
                    :key="index"
                    class="feature-item"
                >
                    <i class="iconfont cis-icon-chosen"></i>
                    {{ item }}
                </div>
            </div>
        </biz-setting-content>
    </biz-setting-layout>
</template>

<script>
    import IENTIVIRUS_1 from '@/assets/images/intro-1.png';
    import IENTIVIRUS_2 from '@/assets/images/intro-2.png';
    import IENTIVIRUS_3 from '@/assets/images/intro-3.png';
    import IENTIVIRUS_4 from '@/assets/images/intro-4.png';
    import IENTIVIRUS_5 from '@/assets/images/intro-5.png';
    import AbcAccess from '@/access/utils.js';
    import {
        BizSettingLayout,
        BizSettingContent,
    } from '@/components-composite/setting-form-layout/index.js';

    export default {
        components: {
            BizSettingLayout,
            BizSettingContent,
        },

        data() {
            const { institutionTypeWording } = this.$app;
            return {
                cardList: [
                    {
                        title: `提高${institutionTypeWording}经营收入`, label: `扩大${institutionTypeWording}经营范围及用户规模，提高收入`, url: IENTIVIRUS_1,
                    },
                    {
                        title: '零门槛开展慢病业务', label: '提供慢病管理全套解决方案，无需额外购买设备仪器', url: IENTIVIRUS_2,
                    },
                    {
                        title: '提升品牌专业形象', label: '参考国家权威医学指南建立慢病管理流程', url: IENTIVIRUS_3,
                    },
                    {
                        title: '全面掌握患者病情', label: '评估、治疗情况全程跟踪，及时掌握关键指标变化趋势', url: IENTIVIRUS_4,
                    },
                    {
                        title: '提升患者就诊体验', label: '智能匹配随访内容及时间，科学管理患者随访计划', url: IENTIVIRUS_5,
                    },
                ],

                features: [
                    `覆盖当前两大主流慢病 - 高血压和糖尿病，为${institutionTypeWording}提供慢病管理工具和平台`,
                    '患者健康档案数字化管理 - 个人病史、生活方式、用药记录、伴发疾病等信息全方位记录，及时掌握患者病情变化',
                    '高血压风险水平权威评估模型 - 根据检测结果，智能评估患者当前风险水平，并生成评估报告，辅助医生诊断',
                    '智能随访 - 根据患者病情匹配随访内容、随访时间，合理规划随访计划，提高慢病随访效率及患者满意度',
                ],
            };
        },

        computed: {
            isPurchased() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.CHRONIC_CARE);
            },
        },

        methods: {
            update() {
                this.$router.push({
                    name: 'product-center-upgrade',
                    query: {
                        accessKey: AbcAccess.accessMap.CHRONIC_CARE,
                    },
                });
            },
        },
    };
</script>

<style lang="scss" scoped>
.intro-wrapper {
    position: relative;
    height: 224px;
    padding: 40px 0 0 60px;
    background: linear-gradient(246deg, #0099ef 0%, #3f5deb 100%);
    border-radius: var(--abc-border-radius-small);

    .icon-bg {
        position: absolute;
        top: 0;
        right: 0;
        width: 400px;
        height: 224px;
    }

    .intro-title {
        margin-bottom: 12px;
        font-size: 24px;
        font-weight: 500;
        line-height: 28px;
        color: #ffffff;
    }

    .intro-tip {
        margin-bottom: 28px;
        font-size: 18px;
        line-height: 24px;
        color: rgba(225, 225, 225, 0.6);
    }

    .operate-wrapper {
        display: flex;
        flex-direction: column;

        > div {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 120px;
            height: 32px;
            margin-bottom: 12px;
            color: #3e5deb;
            cursor: pointer;
            background: #ffffff;
            border-radius: var(--abc-border-radius-small);
        }

        > span {
            font-size: 12px;
            line-height: 16px;
            color: rgba(225, 225, 225, 0.6);
        }
    }
}

.card-wrapper {
    display: flex;
    padding: 12px 0 24px;
    border-bottom: 1px dashed #e6eaee;

    > div {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;

        img {
            width: 56px;
            height: 56px;
            margin: 20px 0 14px;
        }

        .card-title {
            margin-bottom: 8px;
            font-weight: 500;
            line-height: 20px;
            color: #000000;
        }

        .card-tip {
            max-width: 192px;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            color: #7a8794;
            text-align: center;
        }
    }
}

.feature-title {
    padding: 16px 0 12px;
    font-weight: 500;
    line-height: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    color: #7a8794;

    > i {
        margin-right: 8px;
        font-size: 14px;
        color: #1ec761;
    }
}
</style>
