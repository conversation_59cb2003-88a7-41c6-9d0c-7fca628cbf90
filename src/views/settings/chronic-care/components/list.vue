<template>
    <abc-layout preset="setting-table">
        <abc-layout-content>
            <abc-table
                :data-list="goodsList"
                :render-config="tableConfig"
                :loading="loading"
                empty-content="暂无慢病管理项目"
                :show-hover-tr-bg="false"
                @handleClickTr="clickEvent"
            >
                <template #name="{ trData: item }">
                    <abc-table-cell>
                        <abc-text theme="primary">
                            {{ item.name }}
                        </abc-text>
                    </abc-table-cell>
                </template>

                <template #buildPatientCount="{ trData: item }">
                    <abc-table-cell>
                        {{ item.buildPatientCount }}
                    </abc-table-cell>
                </template>

                <template #status="{ trData: item }">
                    <abc-table-cell>
                        <span v-if="item.status === 0">已启用</span>
                        <span v-else style="color: #7a8794;">已停用</span>
                    </abc-table-cell>
                </template>

                <template #handleBar="{ trData: item }">
                    <abc-table-cell>
                        <abc-button variant="text" @click.stop="disableSwitch(item)">
                            <template v-if="item.status === 1">
                                启用
                            </template>
                            <template v-else>
                                停用
                            </template>
                        </abc-button>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="paginationParams"
                :count="totalCount"
                @current-change="changePageIndex"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import ChronicCareAPI from '@/api/chronic-care/settings';
    import {
        mapGetters, mapState,
    } from 'vuex';

    export default {
        data() {
            return {
                loading: false,
                totalCount: 0,
                fetchParams: {
                    offset: 0,
                    limit: 10,
                },
                goodsList: [],

                currentId: null,
                showDialog: false,

                switchLoading: false,
            };
        },

        computed: {
            ...mapGetters(['isAdmin']),
            ...mapState('socialPc', ['warningDictExpired', 'warningPriceExceed']),
            paginationParams() {
                const {
                    limit: pageSize, offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
            tableConfig() {
                const config = [
                    {
                        label: '项目名',
                        key: 'name',
                        style: {
                            flex: 1,
                        },
                    },
                    {
                        label: '建档患者',
                        key: 'buildPatientCount',
                        style: {
                            width: '200px',
                            textAlign: 'right',
                            flex: 0,
                        },
                    },
                    {
                        label: '状态',
                        key: 'status',
                        style: {
                            width: '200px',
                            flex: 0,
                            textAlign: 'center',
                        },
                    },
                ];
                if (this.isAdmin) {
                    config.push({
                        label: '操作',
                        key: 'handleBar',
                        style: {
                            width: '200px',
                            textAlign: 'center',
                            flex: 0,
                        },
                    });
                }
                return {
                    hasInnerBorder: false,
                    list: config,
                };
            },
        },

        created() {
            this.fetchTableData();
        },

        methods: {
            clickEvent(item) {
                this.currentId = item.id;
                this.$router.push({
                    name: '@settings/chronic-care-detail',
                    params: {
                        id: item.id,
                    },
                });
            },

            async disableSwitch(item) {
                if (item.status) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '启用后，将可为患者新建此项目档案，是否启用 ？',
                        onConfirm: () => {
                            this.switchSubmit(item);
                        },
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '停用后，将不可新建此项目档案，已有档案将仍旧保留，是否停用 ？',
                        onConfirm: () => {
                            this.switchSubmit(item);
                        },
                    });
                }
            },

            /**
             * @desc 启用停用
             * <AUTHOR>
             * @date 2021-01-26 16:52:36
             */
            async switchSubmit(item) {
                try {
                    if (this.switchLoading) return false;
                    this.switchLoading = true;
                    await ChronicCareAPI.switchChronicCareItemStatus(item.id, +!item.status);
                    this.$Toast({
                        message: `${item.status ? '启用' : '停用'}成功`,
                        type: 'success',
                    });
                    item.status = +!item.status;
                    this.switchLoading = false;
                } catch (e) {
                    this.switchLoading = false;
                }
            },

            async fetchTableData(initOffset = false) {
                if (initOffset) {
                    this.fetchParams.offset = 0;
                }
                const { offset } = this.fetchParams;
                try {
                    this.loading = true;
                    const { data } = await ChronicCareAPI.fetchChronicCareItems(this.fetchParams);
                    if (offset === this.fetchParams.offset) {
                        this.goodsList = data.rows;
                        this.totalCount = data.total;
                    }
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },

            changePageIndex(index) {
                this.fetchParams.offset = (index - 1) * this.fetchParams.limit;
                this.fetchTableData();
            },
        },
    };
</script>
