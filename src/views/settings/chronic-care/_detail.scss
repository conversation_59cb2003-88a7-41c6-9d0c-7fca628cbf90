@import '~styles/abc-common.scss';

.chronic-setting-detail-wrapper {
    .chronic-setting-detail-content-wrapper {
        padding: 24px;

        section {
            & + section {
                margin-top: 24px;
            }

            label {
                span.label {
                    font-weight: 400;
                    line-height: 20px;
                    color: $T2;
                }

                span.description {
                    margin-left: 8px;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 16px;
                    color: #96a4b3;
                }
            }

            .content {
                margin-top: 10px;

                .add-box {
                    @include flex(row, center, center);

                    box-sizing: border-box;
                    float: left;
                    width: 40px;
                    height: 22px;
                    margin-bottom: 8px;
                    color: $T1;
                    cursor: pointer;
                    border: 1px solid $T3;
                    border-radius: 12px;
                    outline-style: none;
                    transition: all 0.1s ease-out;

                    .iconfont {
                        font-size: 10px;
                    }

                    &:hover {
                        color: $theme1;
                        border: 1px solid $theme1;
                    }
                }
            }

            .form-card-container {
                display: flex;
                flex-wrap: wrap;

                .chronic-form-card {
                    margin-right: 8px;
                    margin-bottom: 8px;
                }
            }
        }
    }
}
