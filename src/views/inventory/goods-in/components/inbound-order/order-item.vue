<template>
    <div class="inbound-order-item">
        <div class="order-info">
            <span>订单号：{{ order.mallOrderId }}</span>
            <span>创建时间：{{ order.created | parseTime('y-m-d h:i:s') }}</span>
            <span>商家：{{ order.vendorName }}</span>
            <span>关联采购单：{{ order.relatedHisOrderNo }}</span>
        </div>
        <div class="order-detail">
            <div class="goods">
                <div class="total">
                    {{ order.kindCount }}种商品
                </div>
                <div class="desc">
                    {{ order.abstractInfo }}
                </div>
            </div>
            <div class="amount">
                <div class="total">
                    <abc-money :value="order.totalPrice"></abc-money>
                </div>
                <div class="desc">
                    (含配送费<abc-money :value="order.deliveryPrice" :show-symbol="false"></abc-money>
                </div>
            </div>
            <div class="trans-amount">
                <div class="total">
                    <abc-money :value="order.dealTotalPrice"></abc-money>
                </div>
                <div class="desc">
                    (含配送费<abc-money :show-symbol="false" :value="order.dealDeliveryPrice"></abc-money>)
                </div>
            </div>
            <div class="receiving-comment">
                <div class="total">
                    {{ logistics.logisticsName }} {{ logistics.logisticsMobile }}
                </div>
                <div class="small-total ellipsis" :title="deliveryInfoStr(logistics)">
                    {{ deliveryInfoStr(logistics) }}
                </div>
                <div class="desc ellipsis">
                    {{ logistics.logisticsCompanyName }} {{ logistics.logisticsNo }}
                </div>
            </div>
            <div class="status">
                <span class="total" :style="getColor(order)">{{ order.statusName }}</span>
            </div>
            <div class="operate">
                <abc-button
                    :disabled="
                        order.inOrderStatus !== StockInStatus.INBOUND &&
                            order.inOrderStatus !== StockInStatus.PART_INBOUND
                    "
                    :class="{
                        'abc-tipsy abc-tipsy--nw':
                            order.inOrderStatus !== StockInStatus.INBOUND &&
                            order.inOrderStatus !== StockInStatus.PART_INBOUND,
                    }"
                    data-tipsy="所有商品都已入库或在入库审核中"
                    @click="selectHandler"
                >
                    去入库
                </abc-button>
            </div>
        </div>
    </div>
</template>

<script>
    import { deliveryInfoStr } from 'views/purchase/utils';
    import { purchaseOrderStatus } from 'views/purchase/constant';
    const StockInStatus = {
        DISABLED_INBOUND: 0, // 不可入库
        INBOUND: 1, // 可入库
        PART_INBOUND: 2, // 部分入库
        INBOUNDED: 3, // 已入库
    };
    export default {
        name: 'OrderItem',
        props: {
            order: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                StockInStatus,
            };
        },
        computed: {
            /**
             * @desc 收货信息
             * <AUTHOR>
             * @date 2020-12-21 10:15:21
             */
            logistics() {
                return this.order.logistics || {};
            },
        },
        methods: {
            deliveryInfoStr,
            selectHandler() {
                this.$emit('select', this.order);
            },
            getColor(order) {
                if (order.status === purchaseOrderStatus.MALL_WAITING_RECV_GOODS) {
                    return {
                        color: '#FF9933',
                    };
                }
                return {
                    color: '#7A8794',
                };
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';
    @import 'src/styles/mixin.scss';

    .inbound-order-item {
        border: 1px solid $P6;
        border-radius: var(--abc-border-radius-small);

        .order-info {
            display: flex;
            align-items: center;
            height: 36px;
            padding-left: 12px;
            color: $T2;
            background-color: #f5f7fb;
            border-bottom: 1px solid $P6;

            > span {
                &:not(:last-child) {
                    margin-right: 24px;
                }
            }
        }

        .order-detail {
            display: flex;
            height: 72px;

            .total {
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
            }

            .small-total {
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
            }

            .desc {
                max-height: 32px;
                margin-top: 2px;
                font-size: 12px;
                line-height: 16px;
                color: $T2;

                @include maxline(2);
            }

            .goods,
            .amount,
            .trans-amount,
            .receiving-comment,
            .status {
                border-right: 1px solid $P6;
            }

            .goods,
            .amount,
            .trans-amount,
            .receiving-comment {
                padding-top: 8px;
            }

            .receiving-comment {
                padding-left: 12px;
            }

            .status,
            .operate {
                display: flex;
                align-items: center;
                justify-content: center;

                .abc-button {
                    min-width: 68px;
                }
            }
        }
    }
</style>
