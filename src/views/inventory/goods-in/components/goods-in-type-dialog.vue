<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        custom-class="custom-goods-action-dialog"
        data-cy="inventory-goods-in-type-select-dialog"
        title="选择入库方式"
        :disabled-keyboard="disabledKeyboard"
        size="medium"
        @open="pushDialogName"
        @close="popDialogName"
    >
        <abc-form ref="pharmacyForm">
            <abc-form-item-group grid>
                <abc-form-item
                    v-if="multiPharmacyCanUse && !isVirtualPharmacy"
                    label="入库库房"
                    :validate-event="handleEvent"
                >
                    <abc-select
                        v-model="selectedPharmacyNo"
                        placeholder="选择入库库房"
                        :disabled="disabledSelect"
                    >
                        <abc-option
                            v-for="item in pharmacyTypeList"
                            :key="item.no"
                            :label="item.name"
                            :value="item.no"
                        >
                        </abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item label="入库方式">
                    <abc-flex vertical :gap="12">
                        <abc-option-card
                            :value="goodsInType === goodsInTypeEnum.purchase"
                            width="100%"
                            icon="s-document-fill"
                            title="手工录入"
                            data-cy="inventory-goods-in-type-manual-card"
                            @change="handleChangeType(goodsInTypeEnum.purchase)"
                        >
                        </abc-option-card>
                        <abc-option-card
                            v-if="!isChainAdmin"
                            :value="goodsInType === goodsInTypeEnum.traceableCode"
                            width="100%"
                            icon="n-barcode-fill"
                            data-cy="inventory-goods-in-type-trace-code-card"
                            @change="handleChangeType(goodsInTypeEnum.traceableCode)"
                        >
                            <template #title>
                                <abc-flex justify="space-between">
                                    <abc-text size="large" style="font-size: 16px;">
                                        一键导入 - 码上放心
                                    </abc-text>
                                    <img
                                        src="~assets/images/trace-code/mashangfangxin-icon.png"
                                        alt="mashangfangxin"
                                        :height="24"
                                    />
                                </abc-flex>
                            </template>
                        </abc-option-card>
                        <abc-option-card
                            :value="goodsInType === goodsInTypeEnum.excel"
                            width="100%"
                            icon="n-xls-fill"
                            data-cy="inventory-goods-in-type-excel-card"
                            @change="handleChangeType(goodsInTypeEnum.excel)"
                        >
                            <template #title>
                                <abc-space>
                                    <abc-text size="large" style="font-size: 16px;">
                                        一键导入 - Excel
                                    </abc-text>
                                    <abc-text theme="warning" style="font-size: 16px;">
                                        公测
                                    </abc-text>
                                </abc-space>
                            </template>
                        </abc-option-card>
                        <template v-if="!isChainAdmin && isOpenB2BMall && !isVirtualPharmacy">
                            <abc-option-card
                                :value="goodsInType === goodsInTypeEnum.mall"
                                width="100%"
                                icon="s-order-1-fill"
                                title="一键导入 - 商城订单"
                                data-cy="inventory-goods-in-type-mall-card"
                                @change="handleChangeType(goodsInTypeEnum.mall)"
                            >
                            </abc-option-card>
                        </template>
                        <template v-if="isVirtualPharmacy">
                            <abc-option-card
                                :value="goodsInType === goodsInTypeEnum.supplier"
                                width="100%"
                                icon="s-supplier-fill"
                                title="一键入库 - 按供应商销量"
                                data-cy="inventory-goods-in-type-supplier-card"
                                @change="handleChangeType(goodsInTypeEnum.supplier)"
                            >
                            </abc-option-card>
                        </template>
                    </abc-flex>
                </abc-form-item>
            </abc-form-item-group>
        </abc-form>

        <goods-in-form
            v-if="showForm"
            v-model="showForm"
            :is-summary="isNull(pharmacyNo) || isChainAdmin"
            :pharmacy-type="pharmacyType"
            :pharmacy-no="selectedPharmacyNo"
            :pharmacy-name="pharmacyName"
            :virtual-supplier-info="virtualSupplierInfo"
            :is-import="isImport"
            :import-order-data="parseData"
            :mall-order-id="mallOrderId"
            :stock-in-type="stockInType"
            support-replace-goods
            @refresh="successHandler"
            @close="closeHandler"
        ></goods-in-form>

        <div>
            <inbound-order-dialog
                v-if="showInboundOrder"
                v-model="showInboundOrder"
                :pharmacy-no="selectedPharmacyNo"
                :pharmacy-name="pharmacyName"
                @createInOrderByMall="handleCreateInOrderByMall"
            ></inbound-order-dialog>
        </div>


        <div>
            <goods-import-parse-dialog
                v-if="showTemplate"
                v-model="showTemplate"
                :import-draft-id="importDraftId"
                :mall-goods-list="mallGoodsList"
                :mall-order-id="mallOrderId"
                :pharmacy-type="pharmacyType"
                @open-form="importProjectAfter"
            ></goods-import-parse-dialog>
        </div>

        <div>
            <goods-import-trace-code-dialog
                v-if="showTraceCodeTemplate"
                v-model="showTraceCodeTemplate"
                @open-form="importProjectAfter"
            ></goods-import-trace-code-dialog>
        </div>


        <order-return
            v-if="showReturnForm"
            :visible.sync="showReturnForm"
            :pharmacy-no="selectedPharmacyNo"
            :pharmacy-name="pharmacyName"
            @refresh="successHandler"
            @close="closeHandler"
        ></order-return>
        <div v-if="multiPharmacyCanUse && !isVirtualPharmacy" slot="footer" class="dialog-footer">
            <abc-button
                :disabled="!goodsInType"
                @click="handleConfirm"
            >
                确定
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import OrderReturn from 'views/inventory/goods-out/return.vue';
    import InboundOrderDialog from './inbound-order/inbound-order-dialog';
    import GoodsImportParseDialog from 'views/inventory/goods-in/components/goods-import-parse-dialog/goods-import-parse-dialog.vue';

    const GoodsInForm = () => import('../form');

    import GoodsInImg from 'src/assets/images/inventory/goods-in-artificial.png';
    import GoodsInMallImg from 'src/assets/images/inventory/goods-in-mall.png';
    import GoodsInExcelImg from 'src/assets/images/inventory/goods-in-excel.png';
    import GoodsInSupplier from 'src/assets/images/inventory/goods-in-supplier.png';

    import { mapGetters } from 'vuex';
    import { PharmacyTypeEnum } from 'views/common/enum.js';
    import { VirtualSuppliersDialog } from 'views/inventory/goods-in/components/virtual-suppliers-dialog/index.js';
    import AbcAccess from '@/access/utils';
    import { isNull } from 'utils/lodash';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import { GoodsInFromTypeEnum } from 'views/inventory/constant.js';
    import useParseNamesByRegion from 'views/inventory/hooks/useParseNameByRegion';
    import GoodsImportTraceCodeDialog
        from 'views/inventory/goods-in/components/goods-import-trace-code-dialog/goods-import-trace-code-dialog.vue';

    export default {
        name: 'GoodsInTypeDialog',
        components: {
            GoodsImportTraceCodeDialog,
            GoodsInForm,
            OrderReturn,
            InboundOrderDialog,
            GoodsImportParseDialog,
        },

        props: {
            value: {
                type: Boolean,
                required: true,
            },
            pharmacyType: {
                type: Number,
                required: true,
            },
            pharmacyNo: {
                // eslint-disable-next-line vue/require-prop-type-constructor
                type: Number | null,
                required: true,
            },
            pharmacyList: {
                type: Array,
                required: true,
            },
        },

        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('选择入库方式');

            const {
                isJiangSuNanJing, initRegion,
            } = useParseNamesByRegion();

            return {
                disabledKeyboard,
                isJiangSuNanJing,
                initRegion,
                pushDialogName,
                popDialogName,
            };
        },
        data() {
            return {
                goodsInType: null,// 1手动添加入库，2 商城一键入库，3 导入随货单Excel模板入库，4 供应商一键入库 5 退货出库
                showForm: false,
                isImport: false,
                showReturnForm: false,
                showInboundOrder: false,
                showTemplate: false,
                showTraceCodeTemplate: false,
                virtualSupplierInfo: null,
                virtualDialog: null,
                selectedPharmacyNo: this.pharmacyNo,
                parseData: null,
                mallGoodsList: [],
                mallOrderId: undefined,
                importDraftId: '',
                stockInType: undefined,
                GoodsInImg,
                GoodsInMallImg,
                GoodsInExcelImg,
                GoodsInSupplier,
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin', 'clinicConfig', 'multiPharmacyCanUse', 'isIntranetUser']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            pharmacyName() {
                if (isNull(this.selectedPharmacyNo)) {
                    return '';
                }
                return this.pharmacyTypeList.find((e) => e.no === this.selectedPharmacyNo)?.name ?? '';
            },
            disabledSelect() {
                return !isNull(this.pharmacyNo);
            },
            pharmacyTypeList() {
                return this.pharmacyList.filter((item) => item.status === 1 && item.enablePurchase);
            },
            isOpenB2BMall() {
                if (this.isIntranetUser) return false;
                if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.B2B_MALL)) return false;
                return (
                    this.clinicConfig &&
                    this.clinicConfig.b2bMallConfig &&
                    this.clinicConfig.b2bMallConfig.isEnableB2BMall
                );
            },
            isVirtualPharmacy() {
                return this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            goodsInTypeEnum() {
                return {
                    purchase: 1, // 手动添加入库
                    mall: 2, // 商城一键入库
                    excel: 3, // 导入随货单Excel模板入库
                    supplier: 4, // 供应商一键入库
                    returnForm: 5,
                    traceableCode: 6, //追溯码码上放心一键入库
                };
            },
        },
        created() {
            this.initRegion(this.$abcSocialSecurity.region);
        },
        methods: {
            isNull,
            handleChangeType(type) {
                const isChange = this.goodsInType !== type;
                if (this.multiPharmacyCanUse && !this.isVirtualPharmacy) {
                    this.goodsInType = type;
                } else {
                    this.changeGoodsInType(type);
                }

                if (!isChange) {
                    return;
                }
                try {
                    if (type === this.goodsInTypeEnum.excel) {
                        // 一键导入 - Excel 上报
                        this.$abcPlatform.service.report.reportEventSLS('inventory_import_select_way_excel', '【一键导入-Excel】 按钮点击次数');
                    } else if (type === this.goodsInTypeEnum.purchase) {
                        this.$abcPlatform.service.report.reportEventSLS('inventory_import_select_way_manual', '【手工录入】按钮点击次数');
                    }
                } catch (e) {
                    console.error(e);
                }
            },
            changeGoodsInType(type) {
                this.parseData = null;
                this.mallOrderId = undefined;
                if (type === this.goodsInTypeEnum.mall) {
                    this.showInboundOrder = true;
                    this.stockInType = GoodsInFromTypeEnum.MALL;
                } else if (type === this.goodsInTypeEnum.traceableCode) {
                    this.showTraceCodeTemplate = true;
                    this.stockInType = GoodsInFromTypeEnum.TRACE_CODE;
                } else if (type === this.goodsInTypeEnum.excel) {
                    this.showTemplate = true;
                    this.stockInType = GoodsInFromTypeEnum.EXCEL;
                } else if (type === this.goodsInTypeEnum.returnForm) {
                    this.showReturnForm = true;
                } else if (type === this.goodsInTypeEnum.supplier) {
                    this.virtualDialog = new VirtualSuppliersDialog({
                        pharmacyType: this.pharmacyType,
                        pharmacyNo: this.selectedPharmacyNo,
                        createOrderFunc: (postData) => {
                            if (postData) {
                                this.virtualSupplierInfo = postData;
                                this.showForm = true;
                            }
                        },
                    });
                    this.virtualDialog.generateDialog({ parent: this });
                } else {
                    this.virtualSupplierInfo = null;
                    this.showForm = true;
                    this.stockInType = GoodsInFromTypeEnum.PURCHASE;
                }
            },
            /**
             * @desc 商城入库，解析商城商品
             * <AUTHOR>
             * @date 2023-09-18 17:35:44
             */
            async handleCreateInOrderByMall(data) {
                const {
                    draftId,
                    goodsList,
                    mallOrderId,
                } = data;

                this.importDraftId = draftId;
                this.mallGoodsList = goodsList;
                this.mallOrderId = mallOrderId;

                this.isImport = true;
                this.showTemplate = true;
                this.showInboundOrder = false;
            },
            handleConfirm() {
                if (!this.goodsInType) return;
                this.$refs.pharmacyForm.validate((valid) => {
                    if (valid) {
                        this.changeGoodsInType(this.goodsInType);
                    }
                });
            },
            successHandler() {
                this.$emit('refresh', true);
                this.showDialog = false;
                this.virtualDialog?.destroyDialog();
            },
            closeHandler() {
                this.$emit('close');
                this.showDialog = false;
                this.virtualDialog?.destroyDialog();
            },
            handleEvent(value, callback) {
                if (isNull(value)) {
                    callback({
                        validate: false,
                        message: '入库库房不能为空',
                    });
                    return;
                }
                callback({
                    validate: true,
                });
            },
            importProjectAfter(order) {
                this.showTemplate = false;
                this.showTraceCodeTemplate = false;
                // 打开入库弹窗
                this.showForm = true;
                this.parseData = {
                    ...order,
                };
                if (this.stockInType === GoodsInFromTypeEnum.TRACE_CODE) {
                    this.$Toast({
                        type: 'success',
                        message: '一键导入成功',
                    });
                }
            },
        },
    };
</script>
