<template>
    <abc-form ref="importTableForm">
        <abc-table type="excel" custom empty-size="small">
            <abc-table-header>
                <abc-table-td :width="168">
                    ABC 系统字段
                </abc-table-td>
                <abc-table-td :width="168">
                    Excel 列标题
                </abc-table-td>
                <abc-table-td :width="190">
                    Excel 列示例（第一行）
                </abc-table-td>
            </abc-table-header>

            <abc-table-body>
                <abc-table-tr v-for="(item, index) in dataList" :key="item.keyId" :data-id="item.keyId">
                    <abc-table-td :width="168">
                        {{ item.standardName }}
                    </abc-table-td>

                    <abc-table-td custom-td :width="168">
                        <abc-form-item :required="requireHeaderName(item.standardName)">
                            <abc-select
                                v-model="item.fileHeaderName"
                                :width="168"
                                placeholder="无"
                                clearable
                                @change="handleClearRepeatName(item, index)"
                            >
                                <abc-option
                                    v-for="it in renderExcelOptions"
                                    :key="it.label"
                                    :value="it.value"
                                    :label="it.label"
                                >
                                </abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-table-td>

                    <abc-table-td :width="190" class="ellipsis" style="color: #7a8794;">
                        {{ findExample(item) }}
                    </abc-table-td>
                </abc-table-tr>
            </abc-table-body>
        </abc-table>
    </abc-form>
</template>

<script>
    export default {
        name: 'ImportExcelTable',
        props: {
            dataList: Array,
            originExcelOptions: Array,
            validateFn: Function,
        },
        computed: {
            renderExcelOptions() {
                return this.originExcelOptions.map((item) => {
                    return {
                        label: item.fileHeaderName,
                        value: item.fileHeaderName,
                        fileHeaderName: item.fileHeaderName,
                    };
                });
            },
            requiredHeaderNames() {
                return ['商品名称', '数量', '药品名称'];
            },
        },
        methods: {
            findExample(item) {
                return this.originExcelOptions.find((it) => {
                    return it.fileHeaderName === item.fileHeaderName;
                })?.example || '';
            },

            validateForm() {
                this.$refs.importTableForm.validate((val) => {
                    if (this.validateFn && typeof this.validateFn === 'function') {
                        this.validateFn(val);
                    }
                });
            },
            /**
             * @desc 忽略 生产日期，批号，有效日期的必填校验
             * <AUTHOR>
             * @date 2023-09-15 16:12:03
             */
            requireHeaderName(headerName) {
                return this.requiredHeaderNames.includes(headerName);
            },

            handleClearRepeatName(item, index) {
                this.dataList.forEach((it, pIndex) => {
                    if (it.fileHeaderName === item.fileHeaderName && index !== pIndex) {
                        it.fileHeaderName = '';
                    }
                });
            },
        },
    };
</script>
