<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle.title"
        responsive
        size="hugely"
        append-to-body
        class="goods-in-wrapper"
        :disabled-keyboard="disabledKeyboard"
        :before-close="beforeClose"
        @open="pushDialogName"
        @close="popDialogName"
    >
        <div slot="title-append" style="margin-left: 8px;">
            <abc-space>
                <abc-text theme="gray">
                    {{ dialogTitle.orderNo }}
                </abc-text>

                <abc-tag-v2
                    v-if="dialogTitle.hasTag"
                    :variant="dialogTitle.variant"
                    :theme="dialogTitle.tagTheme"
                    size="small"
                >
                    {{ dialogTitle.tagName }}
                </abc-tag-v2>
            </abc-space>
        </div>

        <!--修改入库单  只修改进价和数量-->
        <abc-form
            ref="updateForm"
            v-abc-loading.coverOpaque="!order"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout v-if="order" preset="dialog-table">
                <abc-layout-header>
                    <abc-form-item-group is-excel>
                        <abc-popover
                            ref="orderInfoPopoverRef"
                            placement="right"
                            trigger="hover"
                            theme="white"
                            :close-delay="0"
                            :disabled="!canEditOrderInfo"
                            :resident-popover="edit"
                            :popper-style="{
                                display: 'flex', padding: '6px 8px','margin-left': '-2px'
                            }"
                        >
                            <abc-descriptions
                                slot="reference"
                                :column="4"
                                :label-width="88"
                                :disabled="!edit"
                                grid
                                size="large"
                                stretch-last-item
                            >
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="入库人"
                                >
                                    <span>{{ order.createdUser?.name ?? '' }}</span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="入库门店"
                                >
                                    <span v-abc-title="clinicName(order.toOrgan) || '-'"></span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    v-if="isChainAdmin || !!multiPharmacyCanUse"
                                    :span="1"
                                    content-class-name="ellipsis"
                                    :content-padding="order.status === 1 && clinicId === order.toOrganId ? '0px' : ''"
                                    label="入库库房"
                                >
                                    <abc-form-item v-if="order.status === 1 && clinicId === order.toOrganId" required>
                                        <abc-select
                                            ref="supplierSelect"
                                            v-model="pharmacyNo"
                                            :custom-class="['supplier-select']"
                                            :input-style="{
                                                'border': 'none'
                                            }"
                                            @change="onPharmacyNoChange"
                                        >
                                            <abc-option
                                                v-for="it in goodsInPharmacyList"
                                                :key="`${it.no }`"
                                                :label="it.name"
                                                :value="it.no"
                                            >
                                            </abc-option>
                                        </abc-select>
                                    </abc-form-item>
                                    <span v-else v-abc-title.ellipsis="order.pharmacy?.name || '-'"></span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    :content-padding="edit ? '0px' : ''"
                                    content-class-name="ellipsis"
                                    label="供应商"
                                >
                                    <abc-form-item v-if="edit" required>
                                        <abc-select
                                            id="supplierSelect"
                                            ref="supplierSelect"
                                            v-model="supplierId"
                                            :custom-class="['supplier-select']"
                                            with-search
                                            :fetch-suggestions="fetchSuggestions"
                                            adaptive-width
                                            only-bottom-border
                                            inner-width="280px"
                                            :input-style="{
                                                'border': 'none'
                                            }"
                                            setting
                                            setting-text="新增供应商"
                                            setting-icon="n-add-line-medium"
                                            @set="openSupplierDialog"
                                        >
                                            <abc-option
                                                v-for="it in currentSupplierList"
                                                :key="`${it.id }`"
                                                :label="it.name"
                                                :value="it.id"
                                            ></abc-option>
                                        </abc-select>
                                    </abc-form-item>
                                    <span v-else v-abc-title.ellipsis="supplierName"></span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    :content-padding="(edit) ? '0px' : ''"
                                    content-class-name="ellipsis"
                                    label="随货单号"
                                >
                                    <abc-form-item v-if="edit">
                                        <abc-input
                                            v-model="order.outOrderNo"
                                            :max-length="50"
                                            :input-custom-style="{
                                                border: 'none'
                                            }"
                                            clearable
                                        ></abc-input>
                                    </abc-form-item>
                                    <span v-else v-abc-title.ellipsis="order.outOrderNo || '-'"></span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    v-if="isShowPurchaseType"
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="采购方式"
                                >
                                    <span v-abc-title.ellipsis="GoodsInPurchaseTypeEnumString[order.purchaseType] || '-'"></span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    v-if="isShebaoCollect"
                                    :span="1"
                                    content-class-name="ellipsis"
                                    label="采购平台"
                                >
                                    <span v-abc-title.ellipsis="GoodsInPurchasePlatformEnumString[order.platformType] || '-'"></span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :span="1"
                                    :content-padding="(edit) ? '0px' : ''"
                                    content-class-name="ellipsis"
                                    label="验收人"
                                >
                                    <abc-form-item v-if="edit">
                                        <employee-select
                                            v-model="order.inspectBy"
                                            with-search
                                            :employee-list="stockEmployeeList"
                                            only-bottom-border
                                        >
                                        </employee-select>
                                    </abc-form-item>
                                    <span v-else v-abc-title.ellipsis="order.inspectUser?.name ?? '-'"></span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    :content-padding="(edit) ? '0px' : ''"
                                    content-class-name="ellipsis"
                                    label="备注"
                                >
                                    <abc-form-item v-if="edit">
                                        <abc-input
                                            v-model="comment"
                                            :max-length="200"
                                            :input-custom-style="{
                                                border: 'none'
                                            }"
                                        ></abc-input>
                                    </abc-form-item>
                                    <span v-else v-abc-title.ellipsis="comment || '-'"></span>
                                </abc-descriptions-item>
                            </abc-descriptions>
                            <abc-space split>
                                <abc-button v-if="!edit" type="text" @click="handleUpdate">
                                    修改
                                </abc-button>
                                <template v-else>
                                    <abc-button type="text" :loading="saveBtnLoading" @click="updateOrderInfo">
                                        保存
                                    </abc-button>

                                    <abc-button type="text" :disabled="saveBtnLoading" @click="cancelSupplier">
                                        取消
                                    </abc-button>
                                </template>
                            </abc-space>
                        </abc-popover>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        type="excel"
                        :render-config="tableConfigRender"
                        :data-list="order.list"
                        empty-size="small"
                        :show-checked="false"
                        cell-size="large"
                        :scroll-load-config="{
                            fetchData: fetchOrder,
                            total: order.totalCount
                        }"
                    >
                        <template #footer>
                            <abc-flex
                                flex="1"
                                align="center"
                                justify="flex-end"
                                style="padding-right: 12px;"
                            >
                                <abc-space :size="4">
                                    <abc-text theme="gray">
                                        品种
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.kindCount }}
                                    </abc-text>

                                    <abc-text theme="gray">
                                        ，数量
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ moneyDigit(totalCount, 4, false) }}
                                    </abc-text>

                                    <abc-text theme="gray">
                                        ，售价合计（含税）
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.totalPrice | formatMoney }}
                                    </abc-text>

                                    <abc-text theme="gray">
                                        ，含税金额
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ amount | formatMoney }}
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </template>
                        <!--药品编码-->
                        <template #shortId="{ trData: { goods } }">
                            <abc-table-cell class="ellipsis">
                                <overflow-tooltip :content="goods?.shortId || '-'"></overflow-tooltip>
                            </abc-table-cell>
                        </template>
                        <!--药品名称-->
                        <template #cadn="{ trData: item }">
                            <display-name-cell
                                :goods="item.goods"
                                :hover-config="{
                                    openDelay: 500,
                                    showPrice: true,
                                    showShebaoCode: true,
                                    showF2,
                                    pharmacyNo: order.pharmacy ? order.pharmacy.no : pharmacyNo
                                }"
                            >
                            </display-name-cell>
                        </template>
                        <!--售价-->
                        <template
                            #sellPrice="{
                                trData: {
                                    goods, sellingPriceUnitStr
                                }
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <div
                                    v-if="isChineseMedicine(goods)"
                                    v-abc-title.ellipsis="`${moneyDigit(goods.piecePrice, 5)}/${goods.pieceUnit}`"
                                >
                                </div>
                                <div v-else v-abc-title.ellipsis="sellingPriceUnitStr">
                                </div>
                            </abc-table-cell>
                        </template>
                        <!--单位-->
                        <template
                            #unit="{
                                trData: item
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <span :title="item.useUnit">{{ item.useUnit }}</span>
                            </abc-table-cell>
                        </template>
                        <!--数量-->
                        <template
                            #count="{
                                trData: item
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <inventory-order-fixed-hover-popover
                                    :order-item="item"
                                    :order-type="CorrectOrderTypeEnum.GoodsIn"
                                    is-revise-order-hover
                                    only-show-count-change
                                >
                                    <abc-text :theme="getCountTheme(item)" :title="item.useCount">
                                        {{ item.useCount }}
                                    </abc-text>
                                </inventory-order-fixed-hover-popover>
                            </abc-table-cell>
                        </template>

                        <!--进价-->
                        <template
                            #packageCostPrice="{
                                trData: item,
                            }"
                        >
                            <!--中西药 进价均支持 5 位小数输入-->
                            <abc-table-cell class="ellipsis">
                                <abc-text v-abc-title="getGoodsPriceStr(item.goods, item.useUnitCostPrice, item.useUnit)">
                                </abc-text>
                            </abc-table-cell>
                        </template>
                        <!--金额-->
                        <template
                            #totalPrice="{
                                trData: item,
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <inventory-order-fixed-hover-popover :order-item="item" :order-type="CorrectOrderTypeEnum.GoodsIn" is-revise-order-hover>
                                    <abc-text
                                        :theme="getAmountTheme(item)"
                                        :title="item.useTotalCostPrice | formatMoney(false)"
                                    >
                                        {{ item.useTotalCostPrice | formatMoney(false) }}
                                    </abc-text>
                                </inventory-order-fixed-hover-popover>
                            </abc-table-cell>
                        </template>
                        <!--批号-->
                        <template
                            #batchNo="{
                                trData: item,
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <span
                                    :title="item.batchNo"
                                >
                                    {{ item.batchNo }}
                                </span>
                            </abc-table-cell>
                        </template>


                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item,
                            }"
                        >
                            <traceable-code-cell
                                v-model="item.traceableCodeList"
                                :goods="item.goods"
                                :goods-count="{
                                    label: '入库数量',
                                    unit: item.useUnit,
                                    unitCount: item.useCount || 0
                                }"
                                readonly
                            ></traceable-code-cell>
                        </template>
                        <!--  医保上报相关  -->
                        <template #shebaoCode="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <abc-tooltip theme="black" size="small" :content="row.shebaoCode || ''">
                                    <span class="ellipsis">{{ row.shebaoCode || '' }}</span>
                                </abc-tooltip>
                            </abc-table-cell>
                        </template>

                        <template #erpGoodsId="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <abc-tooltip theme="black" size="small" :content="row.erpGoodsId || ''">
                                    <span class="ellipsis">{{ row.erpGoodsId || '' }}</span>
                                </abc-tooltip>
                            </abc-table-cell>
                        </template>

                        <template #emergencyFlag="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis">{{ row.emergencyFlag === 1 ? '是' : '否' || '' }}</span>
                            </abc-table-cell>
                        </template>

                        <template #erpOrderItemId="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <abc-tooltip theme="black" size="small" :content="row.erpOrderItemId || ''">
                                    <span class="ellipsis">{{ row.erpOrderItemId || '' }}</span>
                                </abc-tooltip>
                            </abc-table-cell>
                        </template>

                        <template #operate="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <abc-button
                                    variant="text"
                                    size="small"
                                    theme="primary"
                                    @click="handleIsEditCurrentItem(row)"
                                >
                                    修改
                                </abc-button>
                                <!--                                <abc-tooltip-->
                                <!--                                    placement="bottom"-->
                                <!--                                    :content="row.editable?.reason ?? ''"-->
                                <!--                                    :disabled="!row.editable?.reason"-->
                                <!--                                >-->
                                <!--                                    <div>-->
                                <!--                                        <abc-button-->
                                <!--                                            variant="text"-->
                                <!--                                            size="small"-->
                                <!--                                            theme="primary"-->
                                <!--                                            :disabled="!!(row.editable?.readOnly)"-->
                                <!--                                            @click="handleIsEditCurrentItem(row)"-->
                                <!--                                        >-->
                                <!--                                            修改-->
                                <!--                                        </abc-button>-->
                                <!--                                    </div>-->
                                <!--                                </abc-tooltip>-->
                            </abc-table-cell>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>
        <div slot="footer" class="dialog-footer" style="min-height: 32px;">
            <abc-space style="margin-right: auto;">
                <abc-button
                    v-if="order?.mallOrderId"
                    variant="ghost"
                    @click="handleOpenMallOrderDetail"
                >
                    查看商城订单
                </abc-button>
                <logs-v3-popover v-if="order?.logs?.length" :show-icon="!hasRevise(order?.importFlag)" :logs="order.logs"></logs-v3-popover>
                <abc-button
                    v-if="hasRevise(order?.importFlag)"
                    variant="text"
                    @click="handleOpenReviseLog"
                >
                    修正记录
                </abc-button>
            </abc-space>


            <template v-if="order">
                <!--总部待审核-->
                <template v-if="canReview">
                    <abc-button type="primary" :loading="btnLoading" @click="reviewOrderPrev('pass')">
                        通过
                    </abc-button>
                    <abc-button
                        variant="ghost"
                        theme="danger"
                        @click="reviewOrderPrev('fail')"
                    >
                        驳回
                    </abc-button>
                </template>
                <!--门店待确认-->
                <template v-else-if="canConfirm">
                    <abc-button type="primary" @click="confirmOrderPrev">
                        确认入库
                    </abc-button>
                    <abc-button
                        variant="ghost"
                        theme="danger"
                        @click="cancelOrderPrev"
                    >
                        驳回入库
                    </abc-button>
                </template>
                <!--门店待审核-总店待确认-->
                <abc-button
                    v-else-if="canRevoke"
                    variant="ghost"
                    theme="danger"
                    :loading="revokeLoading"
                    @click="revokeHandler"
                >
                    撤回
                </abc-button>

                <template v-else>
                    <abc-space>
                        <!--已撤回-已驳回-->
                        <abc-button
                            v-if="canReOrderPrev"
                            @click="reOrderHandler"
                        >
                            修改并重新发起
                        </abc-button>

                        <!--已完成-支持退回-->
                        <abc-button
                            v-else-if="canReturn"
                            variant="ghost"
                            @click="returnHandler"
                        >
                            退货
                        </abc-button>

                        <abc-check-access>
                            <abc-button
                                variant="ghost"
                                @click="exportRK"
                            >
                                导出
                            </abc-button>
                        </abc-check-access>

                        <abc-check-access>
                            <print-dropdown
                                :loading="printBtnLoading"
                                @print="print"
                                @select-print-setting="openPrintConfigSettingDialog"
                            ></print-dropdown>
                        </abc-check-access>

                        <abc-dropdown v-if="isEnableTransInFormFullTransOut" @change="handleMoreDropdownChange">
                            <div slot="reference">
                                <abc-button variant="ghost">
                                    更多
                                </abc-button>
                            </div>
                            <abc-dropdown-item label="整单调出" value="TransFormGoods"></abc-dropdown-item>
                        </abc-dropdown>
                    </abc-space>
                </template>
            </template>
            <abc-button variant="ghost" style="margin-left: 8px;" @click="showDialog = false">
                关闭
            </abc-button>
        </div>

        <abc-dialog
            v-if="showReviewDialog"
            v-model="showReviewDialog"
            title="审核"
            append-to-body
            custom-class="goods-confirm"
            content-styles="width: 360px;"
        >
            <div class="dialog-content clearfix">
                <p v-if="confirmStatus === 0" class="confirm-result">
                    审核结果：<span class="confirm-refuse">驳回</span>
                </p>
                <p v-if="confirmStatus === 1" class="confirm-result">
                    审核结果：<span class="confirm-success">通过</span>
                </p>

                <p v-if="confirmStatus === 1" class="confirm-result">
                    审核通过后将会实时更新库存信息
                </p>
                <template v-if="confirmStatus === 0">
                    <abc-form
                        ref="checkForm"
                        label-position="left"
                        :label-width="100"
                    >
                        <abc-form-item required style="margin-bottom: 0;" :validate-event="validateComment">
                            <abc-edit-div
                                v-model="reviewData.comment"
                                :maxlength="100"
                                placeholder="请输入驳回原因"
                                responsive
                                style=" width: 312px; height: 50px;"
                            ></abc-edit-div>
                        </abc-form-item>
                    </abc-form>
                    <p class="confirm-result" style="margin-top: 16px;">
                        门店可在驳回单据内修改并重新发起
                    </p>
                </template>
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button style="margin-left: auto;" :loading="btnLoading" @click="reviewHandle">
                    确定
                </abc-button>
                <abc-button type="blank" @click="showReviewDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <abc-dialog
            v-if="showConfirmDialog"
            v-model="showConfirmDialog"
            title="确认"
            custom-class="goods-confirm"
            content-styles="width: 360px;"
        >
            <div class="dialog-content clearfix">
                <p v-if="confirmStatus === 0" class="confirm-result">
                    确认结果：<span class="confirm-refuse">驳回入库</span>
                </p>
                <p v-if="confirmStatus === 1" class="confirm-result">
                    确认结果：<span class="confirm-success">确认入库</span>
                </p>

                <p v-if="confirmStatus === 1" class="confirm-result">
                    确认入库将会实时更新库存信息
                </p>
                <template v-if="confirmStatus === 0">
                    <abc-form
                        ref="checkForm"
                        label-position="left"
                        :label-width="100"
                    >
                        <abc-form-item required style="margin-bottom: 0;" :validate-event="validateComment">
                            <abc-textarea
                                v-model="rejectComment"
                                :width="312"
                                :height="50"
                                :maxlength="100"
                                placeholder="请输入驳回原因"
                            >
                            </abc-textarea>
                        </abc-form-item>
                    </abc-form>
                    <p class="confirm-result" style="margin-top: 16px;">
                        总店可在驳回单据内修改并重新发起
                    </p>
                </template>
            </div>
            <div slot="footer" class="dialog-footer">
                <abc-button style="margin-left: auto;" :loading="btnLoading" @click="confirmHandle">
                    确定
                </abc-button>
                <abc-button type="blank" @click="showConfirmDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <supplier-dialog
            v-if="showSupplierDialog"
            v-model="showSupplierDialog"
            type="add"
            @add="addSupplier"
        ></supplier-dialog>

        <goods-trans-action-dialog
            v-if="dialogVisible"
            v-model="dialogVisible"
            :pharmacy-no="pharmacyNo"
            :default-trans-type="multiPharmacyCanUse ? transTypeOptions[0].value : null"
            :trans-type-options="transTypeOptions"
            :multi-pharmacy-can-use="multiPharmacyCanUse"
            :local-pharmacy-list="localPharmacyList"
            :enable-trans-in="false"
            :enable-change-trans-out-pharmacy="false"
            :default-trans-out-pharmacy-no="pharmacyNo"
            @confirm="handleConfirmAction"
        ></goods-trans-action-dialog>

        <out-form
            v-if="showOutForm"
            :visible.sync="showOutForm"
            :out-pharmacy-no="transType === 1 ? transOutPharmacyNo : selectedPharmacyNo"
            :in-pharmacy-no="transInPharmacyNo"
            :trans-type="transType"
            :default-order-list="orderList"
            @close="closeDialogHandler"
        ></out-form>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import StockInAPI from 'api/goods/stock-in';
    import dialogAutoWidth from 'views/inventory/mixins/dialog-auto-width.js';

    import EnterEvent from 'views/common/enter-event';

    import {
        clinicName,
        getGoodsPriceStr,
        goodsTotalCostPrice,
        isChineseMedicine,
        isChinesePatentMedicine,
        isWesternMedicine,
    } from 'src/filters/goods';
    // import Big from 'big.js';
    import {
        isNotNull,
        // getSafeNumber,
        moneyDigit, paddingMoney,
    } from '@/utils';
    import {
        CHECK_IN_SUPPLIER_ID,
        CorrectOrderTypeEnum,
        GOODS_IN_PRICE_TYPE,
        GOODS_IN_STATUS, GoodsInInitTypeEnum,
        GoodsInPurchasePlatformEnumString,
        GoodsInPurchaseTypeEnum,
        GoodsInPurchaseTypeEnumString, GoodsReturnInitTypeEnum,
        LOCAL_DEFAULT_PHARMACY,
    } from 'views/inventory/constant.js';

    import {
        calCostPriceSingle,
        calCostPriceTotal,
        checkExpiryDate,
        formatGoodsNameSpec,
        showExpiryDateTip,
        showProductionDateTip,
        validateExpirationTime,
        validatePrice,
    } from './common';
    import { isNull } from 'utils/lodash';
    import Clone from 'utils/clone';
    import GoodsTableV3Mixins from 'views/inventory/mixins/goods-table-v3.js';
    import AbcPrinter from '@/printer';
    import { ABCPrintConfigKeyMap } from '@/printer/constants.js';
    import PrintDropdown from 'views/print/print-dropdown';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import {
        PharmacyTypeEnum,
    } from '@abc/constants';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import OutForm from 'views/inventory/goods-trans/out-form.vue';
    import GoodsAPI from 'api/goods';
    import Trans from 'views/inventory/goods-trans/trans';
    import EmployeeSelect from '@/views-pharmacy/inventory/frames/components/employee-select.vue';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';
    import { TagHelper } from 'utils/tag-helper';
    import TraceCode, {
        TraceableCodeTypeEnum, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import { isEqual } from '@abc/utils';
    import InventoryGoodsInTable from 'views/inventory/goods-in/table-config';
    import OverflowTooltip from 'components/overflow-tooltip.vue';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import AbcCorrectionOrderDialog from 'views/inventory/composite-components/correction-order-dialog';
    import useReviseOrder from 'views/inventory/hooks/useReviseOrder';

    import AbcCorrectionOrderLogDialog from 'views/inventory/components/correction-order-log-dialog';
    import useDeleteGoodsHandler from 'views/inventory/hooks/useDeleteGoodsHandler';
    const InventoryOrderFixedHoverPopover = () => import('views/inventory/components/inventory-order-fixed-hover-popover.vue');

    const SupplierDialog = () => import('../goods-supplier/supplier');

    const compareKey = ['useTotalCostPrice', 'useUnitCostPrice', 'useCount', 'useUnit', 'expiryDate', 'productionDate', 'batchNo', 'traceableCodeList'];

    export default {
        name: 'OrderDetail',

        components: {
            DisplayNameCell,
            OverflowTooltip,
            EmployeeSelect,
            OutForm,
            GoodsTransActionDialog: () => import('views/inventory/goods-in/components/goods-trans-action-dialog/goods-trans-action-dialog.vue'),
            SupplierDialog,
            PrintDropdown,
            LogsV3Popover: () => import('@/views-pharmacy/components/logs-v3-popover.vue'),
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
            InventoryOrderFixedHoverPopover,
        },

        mixins: [EnterEvent, dialogAutoWidth, GoodsTableV3Mixins, Trans],

        props: {
            orderId: {
                type: [String, Number],
                default: '',
            },
            value: Boolean,
            goodsId: {
                type: String,
                default: '',
            },
            pharmacyType: {
                type: Number,
                default: 0,
            },
        },
        setup(props) {
            const {
                disabledKeyboard, pushDialogName,popDialogName,
            } = useDialogStackManager(`入库单详情-${Math.random().toString(36).slice(2)}`);

            const {
                currentSupplierList,
                initSupplierList,
                fetchSuggestions,
            } = useSearchSupplier({
                status: 1,
                excludeInitSupplier: true,
                pharmacyType: props.pharmacyType,
            });


            const {
                handleGoodsDelete,
                handleGoodsDisable,
            } = useDeleteGoodsHandler();


            const {
                isReviseCount,
                isReviseCostPrice,
                isReviseTotalPrice,
                hasRevise,
                getReviseOrderList,
                createReviseCoreData,
            } = useReviseOrder();

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                currentSupplierList,
                initSupplierList,
                fetchSuggestions,

                isReviseCount,
                isReviseCostPrice,
                isReviseTotalPrice,
                hasRevise,
                getReviseOrderList,
                createReviseCoreData,

                handleGoodsDelete,
                handleGoodsDisable,
            };
        },
        data() {
            return {
                CorrectOrderTypeEnum,
                CHECK_IN_SUPPLIER_ID,
                showDialog: this.value,
                offset: 0,
                limit: 100,
                tableLoading: false,
                searchKey: '',
                order: null,
                pharmacyNo: '',
                comment: '',
                currentChangeIndex: -1,
                isFirstPrint: true,
                cloneOrder: {},
                buttonLoading: false,
                kindsNum: 0,
                isDiff: [],
                tempItem: {}, // 目前修改的药品
                showReviewDialog: false,
                reviewData: {
                    pass: 1,
                    comment: '',
                },
                edit: false,
                saveBtnLoading: false,
                suppliers: [],
                supplierId: '',
                showSupplierDialog: false,
                showConfirmDialog: false, // 门店确认入库
                confirmStatus: 1, // 1 确认入库, 审核通过 0 取消入库 审核不通过
                rejectComment: '',
                confirmLoading: false,
                btnLoading: false,
                revokeLoading: false,
                printBtnLoading: false,

                expiryDateError: {
                    error: false,
                    message: '',
                },
                productionDateError: {
                    error: false,
                    message: '',
                },
                useUnitCostPriceError: {
                    error: false,
                    message: '',
                },
                isShowTraceableDialog: false,//追溯码弹窗flag
                goodsName: '',
                goodsCount: 0,
                goodsIndex: '',
                traceableList: [],
                // 进销存记录修正条数与预估操作时间
                reachData: {
                    tips: '',
                    totalCount: 0,
                    totalSeconds: 0,
                },

                dialogVisible: false,

                showOutForm: false,
                transType: 0,
                transOutPharmacyNo: null,// 店内调拨调出库房
                transInPharmacyNo: null,// 店内调拨调入库房
                selectedPharmacyNo: null,// 调拨库房
                orderList: [],
                validateCell: false,
            };
        },
        computed: {
            GoodsInPurchasePlatformEnumString() {
                return GoodsInPurchasePlatformEnumString;
            },
            GoodsInPurchaseTypeEnumString() {
                return GoodsInPurchaseTypeEnumString;
            },
            ...mapGetters([
                'subClinics',
                'userInfo',
                'currentClinic',
                'isAdmin',
                'clinicConfig',
                'isChainSubStore',
                'isChainAdmin',
                'isChain',
                'goodsConfig',
                'multiPharmacyCanUse',
                'localPharmacyUserList',
                'localPharmacyList',
                'isSingleStore',
                'stockEmployeeList',
                'traceCodeConfig',
                'isStrictCountWithTraceCodeCollect',
                'isDisableSubClinicStockInReturnOut',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            needTransGoodsClassificationName() {
                return this.viewDistributeConfig.needTransGoodsClassificationName;
            },
            orderMainNameText() {
                return this.viewDistributeConfig.Inventory.orderMainNameText;
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            isSupportUpdateGoodsInOrder() {
                return this.viewDistributeConfig.Inventory.isSupportUpdateGoodsInOrder;
            },
            isShowPurchaseType() {
                return this.$abcSocialSecurity.region === 'jiangsu_nanjing';
            },
            isShebaoCollect() {
                return this.order?.purchaseType === GoodsInPurchaseTypeEnum.COLLECT;
            },
            isEnableTransInFormFullTransOut() {
                return this.viewDistributeConfig.Inventory.isEnableTransInFormFullTransOut;
            },

            currentSubClinicsArray() {
                return this.subClinics || [];
            },
            goodsInPharmacyList() {
                return this.localPharmacyUserList.filter((item) => item.status === 1 && item.enablePurchase);
            },
            canEditOrderInfo() {
                // 有结算单settlementOrderId，标识已经结算了，禁用不能修改
                return this.canUpdate && !this.order?.settlementOrderId && String(this.order?.supplierId) !== CHECK_IN_SUPPLIER_ID;
            },
            supplierName() {
                return this.order?.supplierName || this.order?.supplier || '-';
            },
            // 是否初始化入库单
            isInitStockInOrder() {
                return GoodsInInitTypeEnum.includes(this.order?.type);
            },
            // 是否初始化退货单
            isInitStockOutOrder() {
                return GoodsReturnInitTypeEnum.includes(this.order?.type);
            },
            dialogTitle() {
                const obj = {
                    title: '采购入库单',
                    orderNo: this.order?.orderNo || '',
                    hasTag: false,
                    variant: '',
                    tagName: '',
                    tagTheme: '',
                };

                if (this.isInitStockInOrder) {
                    obj.title = '初始化入库单';
                }

                if (this.isInitStockOutOrder) {
                    obj.title = '初始化退货单';
                }

                if (this.orderId && this.order) {
                    if (this.order.status === GOODS_IN_STATUS.REVIEW) {
                        obj.hasTag = true;
                        obj.tagName = this.canReview ? '待审核' : '待总部审核';
                        obj.tagTheme = this.canReview ? TagHelper.TODO_TAG.theme : TagHelper.ING_TAG.theme;
                        obj.variant = this.canReview ? TagHelper.TODO_TAG.variant : TagHelper.ING_TAG.variant;
                    }
                    if (this.order.status === GOODS_IN_STATUS.REFUSE) {
                        obj.hasTag = true;
                        obj.tagName = '已驳回';
                        obj.tagTheme = TagHelper.REFUSE_TAG.theme;
                        obj.variant = TagHelper.REFUSE_TAG.variant;
                    }
                    if (this.order.status === GOODS_IN_STATUS.WITH_DRAW) {
                        obj.hasTag = true;
                        obj.tagName = '已撤回';
                        obj.tagTheme = TagHelper.CANCEL_TAG.theme;
                        obj.variant = TagHelper.CANCEL_TAG.variant;
                    }
                    if (
                        this.order.status === GOODS_IN_STATUS.CONFIRM &&
                        this.order.toOrgan &&
                        (this.order.toOrgan.shortName || this.order.toOrgan.name)
                    ) {
                        obj.hasTag = true;
                        obj.tagName = `待${this.order.toOrgan.shortName || this.order.toOrgan.name}确认`;
                        obj.tagTheme = this.canConfirm ? TagHelper.TODO_TAG.theme : TagHelper.ING_TAG.theme;
                        obj.variant = this.canConfirm ? TagHelper.TODO_TAG.variant : TagHelper.ING_TAG.variant;
                    }
                }
                return obj;
            },
            tableConfigRender() {
                const TableConfig = new InventoryGoodsInTable();

                return TableConfig.createRenderConfig({
                    // hasInnerBorder: false,
                    orderMainNameText: this.orderMainNameText,
                    isEnableTraceableCode: this.isEnableTraceableCode,
                    isShebaoCollect: this.isShebaoCollect,
                    canUpdate: this.canUpdate,
                });
            },
            userId() {
                return this.userInfo && this.userInfo.id;
            },

            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },
            // 总部给子店入库不显示调价
            showF2() {
                if (this.isChainAdmin && (this.order.toOrganId !== this.clinicId)) {
                    return false;
                }
                return true;
            },
            stockInChainReview() {
                return this.goodsConfig?.chainReview?.stockInChainReview;
            },
            canReview() {
                return this.order.status === GOODS_IN_STATUS.REVIEW && this.isChainAdmin;
            },
            canConfirm() {
                return this.order.status === GOODS_IN_STATUS.CONFIRM && !this.isChainAdmin;
            },
            // 判断是否能修改，能够修改的条件 (入库修改权限修改，之前的修改权限是只能创建者，现在为 有库存权限的人 )
            // 1. 库存权限 ，2.未对账 3.是否在总部  其余用户打开入库单均不能编辑，只能查看修改详细信息
            // 修改 1.门店确认了入库单之后修改，给出修改者提示会刷新库存，门店无须再次确认入库单 2.门店未确认入库单，给门店提示该入库单已被修改
            // 创建者通过点击修改才能进行修改入库单
            // 条件 1.创建者 2.为对账 3.确认后 4.在总部 5.设置了review并且在连锁子店不能编辑 6.审核未通过也不能编辑
            canUpdate() {
                return (
                    // 医保集采不允许修改
                    !this.isShebaoCollect &&
                    this.order?.status === GOODS_IN_STATUS.GOODS_IN &&
                    this.isSupportUpdateGoodsInOrder &&
                    !(this.stockInChainReview && this.isChainSubStore)
                );
            },
            // 待审核和待确定可以撤回
            canRevoke() {
                return this.order.status === GOODS_IN_STATUS.REVIEW || this.order.status === GOODS_IN_STATUS.CONFIRM;
            },
            // 已完成的入库单可以退货
            canReturn() {
                // 子店读取，是否禁用退货出库功能开关，后端针对张仲景门店的子店会返回1
                if (this.isChainSubStore && this.isDisableSubClinicStockInReturnOut) {
                    return false;
                }
                if (this.order.status === GOODS_IN_STATUS.GOODS_IN && !this.isVirtualPharmacy) {
                    // 限制总部不能退回子店的领用单
                    if (this.isChainAdmin) {
                        return this.order.toOrgan?.clinicId === this.currentClinic?.clinicId;
                    }
                    return true;
                }
                return false;
            },
            // 修改并重新发起
            canReOrderPrev() {
                const {
                    applyClinicId, status,
                } = this.order;

                // 之前的入库单没有记录发起方是谁，默认不支持重新入库
                if (!applyClinicId) return false;
                // （已驳回/已撤回）&& 发起入库单的是当前门店的入库单可以修改并重新发起
                return (status === GOODS_IN_STATUS.REFUSE || status === GOODS_IN_STATUS.WITH_DRAW) && applyClinicId === this.currentClinic?.id;
            },
            isVirtualPharmacy() {
                return this.order?.pharmacy?.type === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            totalCount() {
                let count = 0;
                if (!this.tempItem?.id) {
                    return this.order.sum;
                }
                this.order?.list?.forEach((item, index) => {
                    if (index === this.currentChangeIndex) {
                        count += (+this.tempItem?.useCount || 0) * 10000;
                    } else {
                        count += (+item.useCount || 0) * 10000;
                    }

                });
                return count / 10000;
            },

            amount() {
                let amount = 0;
                if (!this.tempItem?.id) {
                    return this.order.amount;
                }
                this.order?.list?.forEach((item, index) => {
                    let useTotalCostPrice = 0;
                    if (index === this.currentChangeIndex) {
                        const {
                            useCount, useUnitCostPrice,
                        } = this.tempItem;
                        useTotalCostPrice = (+useUnitCostPrice * 100000) * (+useCount * 100000);
                    } else {
                        const {
                            useCount, useUnitCostPrice,
                        } = item;
                        useTotalCostPrice = (+useUnitCostPrice * 100000) * (+useCount * 100000);
                    }
                    amount += useTotalCostPrice;
                });
                return (amount / Math.pow(10, 10)).toFixed(2);
            },
            transTypeOptions() {
                if (this.isSingleStore) {
                    return [{
                        label: '店内调拨',value: 1,
                    }];
                }
                return [
                    {
                        label: '店间调拨',value: 0,
                    },
                    {
                        label: '店内调拨',value: 1,
                    },
                ];
            },
            needReturnConfirm() {
                return this.isReviseCount(this.order?.importFlag);
            },
        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
                if (!val) {
                    this.$emit('close');
                }
            },
        },
        async created() {
            if (this.isStrictCountWithTraceCodeCollect) {
                this.validateCell = true;
            }
            this.fetchOrder();
            await this.$store.dispatch('fetchPrintInventoryConfig');
            AbcPrinter.setGlobalConfig();
        },
        beforeDestroy() {
            if (this._AbcScheduleDialogInstance) {
                this._AbcScheduleDialogInstance?.destroyDialog();
                this._AbcScheduleDialogInstance = null;
            }
        },
        methods: {
            clinicName,
            isWesternMedicine,
            isChinesePatentMedicine,
            isChineseMedicine,
            goodsTotalCostPrice,
            paddingMoney,
            moneyDigit,
            calCostPriceTotal,
            calCostPriceSingle,
            validatePrice,
            formatGoodsNameSpec,
            getGoodsPriceStr,
            showProductionDateTip,
            checkExpiryDate,
            showExpiryDateTip,
            validateExpirationTime,
            createRowKey(row, index) {
                return `${row.goodsId || row.goods?.id}-${index}`;
            },
            createTrClassName() {
                return this.isShebaoCollect ? 'shebao-detail-tr' : '';
            },
            getAmountTheme(item) {
                return item.fixedStockOrderList?.length ? 'warning-light' : 'black';
            },
            getCountTheme(item) {
                return item.fixedStockOrderList?.[0]?.list?.[0]?.diffView?.displayGoodsCount ? 'warning-light' : 'black';
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'goods-in' }).generateDialogAsync({ parent: this });
            },
            initNoTraceCodeList(item) {
                if (this.isEnableTraceableCode) {
                    // 对无码商品初始化追溯码
                    if (TraceCode.isSupplementNoCodeGoods(item.goods)) {
                        item.traceableCodeList = [];
                        item.traceableCodeList = TraceCode.mergeNoTraceCodeList({
                            ...item,
                            isTrans: item._isTransformable,
                            maxCount: item._maxTraceCodeCount,
                            unitCount: item.useCount,
                            unit: item.useUnit,
                        });
                    } else if (TraceCode.isNullCodeGoods(item.goods)) {
                        item.traceableCodeList = [];
                    } else if (!TraceCode.isNoTraceCodeGoods(item.goods)) {
                        // 如果不是无码，需要清除已采集的无码追溯码
                        item.traceableCodeList = (item.traceableCodeList || []).filter((it) => {
                            if (isNotNull(it.traceableCodeNoInfo?.type)) {
                                return it.traceableCodeNoInfo.type !== TraceableCodeTypeEnum.NO_CODE;
                            }
                            if (isNotNull(it.type)) {
                                return it.type !== TraceableCodeTypeEnum.NO_CODE;
                            }
                            return true;
                        });
                    }
                }
            },
            /**
             * @desc 初始化入库单详情信息  每个药品的金额增加 costPriceTotal 属性，计算金额
             * <AUTHOR>
             * @date 2018/11/12 09:21:33
             * @params
             * @return
             */
            async fetchOrder() {
                try {
                    this.tableLoading = true;
                    const order = await StockInAPI.getOrderById(this.orderId,{
                        limit: this.limit,
                        offset: this.offset,
                        withGoodsId: this.goodsId,
                        withGoodsIdOrderFirst: this.goodsId ? 1 : 0,
                    });
                    // offset 为 0 时，表示初始化或者是刷新
                    const list = this.offset === 0 ? order.list : [...this.order?.list ?? [], ...order.list ?? []];

                    // 已完成的入库单不需要补充特殊无码标识
                    // list.forEach((item) => {
                    //     // 补充无码标识
                    //     this.initNoTraceCodeList(item);
                    // });

                    this.order = {
                        ...order,
                        ...order.extendData,
                        list,
                        totalCount: order.totalCount || list.length,
                    };
                    this.offset += this.limit;
                    if (this.order.comment?.length) {
                        this.comment = this.order.comment[this.order.comment.length - 1].content;
                    }
                    if (!this.order.inspectBy) {
                        this.order.inspectBy = this.order.inspectUser?.id ?? '';
                    }

                    this.isFirstPrint = true;
                    this.formatOrderList(this.order.list);
                    this.supplierId = this.order.supplierId;

                    // TODO 需要和后端确认总部给门店入库没有默认库房了？
                    if (!this.order.pharmacy) {
                        this.order.pharmacy = LOCAL_DEFAULT_PHARMACY;
                    }
                    this.pharmacyNo = this.order.pharmacy?.no ?? '';
                    this.cloneOrder = Clone(this.order);
                } catch (e) {
                    console.log(e);
                } finally {
                    this.tableLoading = false;
                }
            },
            formatOrderList(orderList = []) {
                orderList.forEach((item) => {
                    if (item.goods) {
                        item.sellingPriceUnitStr = getGoodsPriceStr(item.goods, item.goods.packagePrice, item.goods.packageUnit);
                        item.purchasePriceUnitStr = getGoodsPriceStr(item.goods, item.useUnitCostPrice, item.useUnit);
                        item.goods.disable = item.disable;
                    }
                    if (this.isShebaoCollect) {
                        const {
                            shebaoCode,erpGoodsId,erpOrderItemId,emergencyFlag,
                        } = item.extendData || {};
                        item.shebaoCode = shebaoCode || '';
                        item.erpGoodsId = erpGoodsId || '';
                        item.erpOrderItemId = erpOrderItemId || '';
                        item.emergencyFlag = emergencyFlag || '';
                    }
                    // item.goods 所存的商品信息均为快照，修改药品资料不会及时刷新，item.disable为当前药品的停用状态
                    this.$set(item, 'costPriceTotal', this.calCostPriceTotal(item));
                });
            },
            ValidateCostPrice(value, callback) {
                if (Number(this.tempItem.useUnitCostPrice) === 0 && Number(value) === 0) {
                    callback({
                        validate: true,
                    });
                    return;
                }

                const isError = (Number(this.tempItem.useCount) === 0 && Number(value) > 0) ||
                    (Number(value) === 0 && Number(this.tempItem.useCount) > 0);
                if (isError) {
                    callback({
                        validate: false,
                        message: `入库数量为${Number(this.tempItem.useCount)}，请重新检查入库总金额`,
                    });
                    return;
                }

                callback({
                    validate: true,
                });
            },

            /**
             * @desc  选择当前唯一编写的一列
             * <AUTHOR>
             * @date 2018/11/09 18:28:44
             * @params
             * @return
             */
            handleIsEditCurrentItem(item) {
                if (!this.canUpdate) return;
                new AbcCorrectionOrderDialog({
                    visible: true,
                    cancelText: '取消',
                    supplierName: this.supplierName,
                    orderItem: item,
                    inOrderId: this.orderId,
                    successCallback: (res) => {
                        console.log('successCallback', res);
                        this.offset = 0;
                        this.fetchOrder();
                        if (res.fixedOrderId) {
                            this.handleOpenCorrectionOrder(res.fixedOrderId);
                        }
                    },
                }).generateDialogAsync({
                    parent: this,
                });
            },
            handleOpenCorrectionOrder(orderId) {
                new AbcCorrectionOrderDialog({
                    visible: true,
                    orderType: CorrectOrderTypeEnum.GoodsIn,
                    orderId,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            /**
             * @desc  比较入库单 修改的部分 给出不同的提示文案
             * <AUTHOR>
             * @date 2018/12/05 17:10:59
             */
            compareUpdate(item, index) {
                this.isDiff = [];
                const titleList = [];
                if (this.currentChangeIndex !== -1) {
                    this.isDiff = compareKey.filter((it) => {
                        const newItem = this.order.list[index][it];
                        const oldItem = this.cloneOrder.list[index][it];
                        if (!isNaN(Number(newItem)) && !isNaN(Number(oldItem))) {
                            return Number(newItem) !== Number(oldItem);
                        }
                        if (isNull(newItem) && isNull(oldItem)) {
                            return false;
                        }
                        return !isEqual(newItem, oldItem);
                    });

                    this.isDiff.forEach((it) => {
                        if (['useCount'].indexOf(it) > -1 || ['useUnit'].indexOf(it) > -1) {
                            titleList.push('数量');
                        }
                        if (['useUnitCostPrice'].indexOf(it) > -1) {
                            titleList.push('进价');
                        }
                        if (['useTotalCostPrice'].indexOf(it) > -1) {
                            titleList.push('金额');
                        }
                        if (['batchNo'].indexOf(it) > -1) {
                            titleList.push('生产批号');
                        }
                        if (['productionDate'].indexOf(it) > -1) {
                            titleList.push('生产日期');
                        }
                        if (['expiryDate'].indexOf(it) > -1) {
                            titleList.push('有效日期');
                        }
                        if (['traceableCodeList'].indexOf(it) > -1) {
                            titleList.push('追溯码');
                        }

                    });
                    const titleSet = new Set(titleList);

                    return [...titleSet].join('、');
                }
                return '';
            },

            /**
             * @desc  总部给门店入库需要门店确认入库
             * <AUTHOR>
             * @date 2018/11/09 14:54:16
             * @params
             * @return
             */
            async confirmOrderPrev() {
                this.$refs.updateForm.validate((val) => {
                    if (val) {
                        this.showConfirmDialog = true;
                        this.confirmStatus = 1; // 确认入库
                    }
                });
            },
            cancelOrderPrev() {
                this.confirmStatus = 0;
                this.showConfirmDialog = true;
            },
            confirmHandle() {
                if (this.confirmStatus === 0) {
                    // 取消入库
                    this.$refs.checkForm.validate((val) => {
                        if (val) {
                            this.cancelOrder();
                        }
                    });
                }
                if (this.confirmStatus === 1) {
                    // 确认入库
                    this.confirmOrder();
                }
            },
            async confirmOrder() {
                this.btnLoading = true;
                let { list } = this.order;
                const {
                    lastModifiedDate, supplier, inspectBy,
                } = this.order;
                list = list.map((item) => {
                    return {
                        id: item.id,
                        traceableCodeList: item.traceableCodeList,
                    };
                });
                try {
                    await StockInAPI.confirmOrder(this.order.id, {
                        lastModifiedDate,
                        list,
                        supplier,
                        inspectBy,
                        comment: '',
                        pharmacyNo: this.order?.pharmacy?.no,
                    });
                    this.btnLoading = false;
                    this.showDialog = false;
                    this.$emit('refresh', true);
                } catch (e) {
                    if (e.code === '492' || e.code === '962') {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前入库单已被修改，请再次确认',
                            onClose: () => {
                                this.$emit('refresh');
                            },
                        });
                    } else if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail);
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail);
                    }
                    this.btnLoading = false;
                }
            },
            /**
             * @desc 取消入库
             * <AUTHOR>
             * @date 2019/09/16 19:41:05
             * @params
             * @return
             */
            async cancelOrder() {
                try {
                    this.btnLoading = true;
                    await StockInAPI.rejectOrder(this.order.id, {
                        comment: this.rejectComment,
                        lastModifiedDate: this.order.lastModifiedDate,
                    });
                    this.showConfirmDialog = false;
                    this.showDialog = false;
                    this.btnLoading = false;
                    this.$emit('refresh');
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            reviewOrderPrev(type) {
                if (type === 'pass') {
                    this.$refs.updateForm.validate((val) => {
                        if (val) {
                            this.confirmStatus = 1;
                        }
                    });
                } else {
                    this.confirmStatus = 0;
                }
                this.showReviewDialog = true;
            },
            reviewHandle() {
                if (this.confirmStatus === 0) {
                    this.$refs.checkForm.validate((val) => {
                        if (val) {
                            this.reviewData.pass = 0;
                            this.reviewOrder();
                        }
                    });
                }
                if (this.confirmStatus === 1) {
                    this.reviewOrder();
                }
            },
            reOrderHandler() {
                this.showDialog = false;
                this.$emit('reInStock', this._props);
            },
            returnHandler() {
                if (this.needReturnConfirm) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '该入库单存在入库数量/进价修改过的药品，将基于正确入库信息退货',
                        onConfirm: () => {
                            this.showDialog = false;
                            this.$emit('return', this._props);
                        },
                    });
                } else {
                    this.showDialog = false;
                    this.$emit('return', this._props);
                }
            },
            revokeHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确认撤回后单据将失效，可在此单据内修改并重新发起',
                    onConfirm: async () => {
                        await this.revoke();
                    },
                });
            },
            async revoke() {
                try {
                    this.revokeLoading = true;
                    await StockInAPI.revokeOrder(this.orderId);
                    this.revokeLoading = false;
                    this.offset = 0;
                    this.fetchOrder();
                    this.$emit('refresh', false, '', false);
                } catch (e) {
                    this.revokeLoading = false;
                }
            },
            /**
             * @desc  如果打开了入库审核确认开关，门店入库需要总部审核
             * <AUTHOR>
             * @date 2018/11/09 14:54:16
             * @params
             * @return
             */
            async reviewOrder() {
                try {
                    this.btnLoading = true;
                    await StockInAPI.review(this.order.id, {
                        ...this.reviewData,
                        lastModifiedDate: this.order.lastModifiedDate,
                        pharmacyNo: this.order?.pharmacy?.no,
                    });
                    this.$emit('refresh');
                    this.showReviewDialog = false;
                    this.showDialog = false;
                } catch (e) {
                    console.log(e);
                    if (e.code === 962) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前入库单已被修改，请再次确认',
                            onClose: () => {
                                this.$emit('refresh');
                            },
                        });
                    } else if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail);
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail);
                    }
                } finally {
                    this.btnLoading = false;
                }
            },

            /**
             * @desc 打印入库单
             * <AUTHOR>
             * @date 2018/11/24 14:03:17
             * @params
             * @return
             */
            async print() {
                const printAction = async () => {
                    try {
                        let printData = Clone(this.order);
                        printData.comment = this.comment || '';
                        printData.multiPharmacyCanUse = !!this.multiPharmacyCanUse;
                        printData.needTransGoodsClassificationName = this.needTransGoodsClassificationName;
                        // 是否产生修正
                        const hasRevise = this.hasRevise(printData?.importFlag);

                        // 当前数据未加载完，或者有修正，都重新拉接口带上合并标记
                        if (this.order?.list?.length !== this.order?.totalCount || hasRevise) {
                            this.printBtnLoading = true;
                            // 获取完整数据
                            const fullOrder = await StockInAPI.getOrderById(this.orderId, {
                                limit: 9999,
                                offset: 0,
                                needMergedOrder: 1,
                            });

                            printData = {
                                ...fullOrder,
                                ...fullOrder.extendData,
                                comment: this.comment || '',
                                multiPharmacyCanUse: !!this.multiPharmacyCanUse,
                            };

                            this.formatOrderList(printData.list);
                        }

                        // 处理打印数据为，修正后正确的入库数据
                        // if (hasRevise) {
                        //     const orderList = printData.list;
                        //     const originalListItemMap = new Map();
                        //
                        //     orderList.forEach((item) => {
                        //         originalListItemMap.set(item.id, item);
                        //     });
                        //
                        //     // 1、调fetchOrderList拿到所有有修正的数据。
                        //     const reviseOrderList = this.getReviseOrderList({ list: orderList });
                        //     console.log('所有处理过的修正单数据', reviseOrderList);
                        //
                        //     // 2、遍历所有有修正的数据，找到当前药品的修正数据，将修正数据赋值给当前药品
                        //     reviseOrderList.forEach((item) => {
                        //         const {
                        //             originalItemId, inId,
                        //         } = item;
                        //         const originalListItem = originalListItemMap.get(originalItemId || inId);
                        //         if (originalListItem) {
                        //             const {
                        //                 before, after,
                        //             } = this.createReviseCoreData({
                        //                 fixedItem: item,
                        //                 originalItem: item.originalItem,
                        //                 orderType: CorrectOrderTypeEnum.GoodsIn,
                        //             });
                        //
                        //             originalListItem.useUnit = after.unit;
                        //             originalListItem.useCount = after.count;
                        //             originalListItem.packageCount = Big(before.packageCount).plus(after.packageCount).toNumber();
                        //             originalListItem.pieceCount = Big(before.pieceCount).plus(after.pieceCount).toNumber();
                        //             originalListItem.useUnitCostPrice = after.packageCostPrice;
                        //             originalListItem.useTotalCostPrice = after.totalCost;
                        //             originalListItem.useTotalCostPriceE = after.totalCostE;
                        //         }
                        //     });
                        //
                        //     // 3、汇总总金额
                        //     let amount = Big(0);
                        //     let amountExcludingTax = Big(0);
                        //
                        //     printData.list.forEach((item) => {
                        //         amount = Big(getSafeNumber(item.amount)).plus(item.useTotalCostPrice);
                        //         amountExcludingTax = Big(getSafeNumber(item.amountExcludingTax)).plus(item.useTotalCostPriceE);
                        //     });
                        //
                        //     printData.amount = Big(amount).toNumber();
                        //     printData.amountExcludingTax = Big(amountExcludingTax).toNumber();
                        // }

                        AbcPrinter.abcPrint({
                            templateKey: window.AbcPackages.AbcTemplates.goodsIn,
                            printConfigKey: ABCPrintConfigKeyMap.RK,
                            data: printData,
                        });
                    } catch (e) {
                        console.error('打印失败:', e);
                        this.$Toast({
                            type: 'error',
                            message: '打印失败，请重试',
                        });
                    } finally {
                        this.printBtnLoading = false;
                    }
                };

                // 保持原有的确认逻辑
                if (this.needReturnConfirm) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '该入库单存在入库数量/进价修改过的药品，将基于正确入库信息打印',
                        onConfirm: () => {
                            printAction();
                        },
                    });
                } else {
                    printAction();
                }
            },
            /**
             * @desc  导出入库单
             * <AUTHOR>
             * @date 2018/11/28 10:44:05
             */
            async exportRK() {
                await StockInAPI.exportById(this.orderId);
            },
            beforeClose() {
                this.showDialog = false;
                this.$emit('refresh');
            },
            getCostPriceSingle(item) {
                if (item.useCount !== '' && item.useTotalCostPrice !== '') {
                    item.useUnitCostPrice = this.calCostPriceSingle(item);
                }
            },
            getCostPriceTotal(item) {
                if (item.useCount !== '' && item.useUnitCostPrice !== '') {
                    item.useTotalCostPrice = this.calCostPriceTotal(item);
                }
            },
            packageCostPriceChange(item) {
                item.opType = GOODS_IN_PRICE_TYPE.UNIT_PRICE;
            },
            totalCostPriceChange(item) {
                item.opType = GOODS_IN_PRICE_TYPE.TOTAL_PRICE;
            },
            async handleChangeUnitCount(item) {
                await this.initCollectCodeCountList([item]);

                this.initNoTraceCodeList(item);

                this.getCostPriceTotal(item);
            },
            async initCollectCodeCountList(list = []) {
                if (this.isEnableTraceableCode) {
                    const resList = await TraceCode.getMaxTraceCountList({
                        scene: TraceCodeScenesEnum.INVENTORY,
                        dataList: list,
                        getGoodsInfo: (item) => item.goods,
                        getUnitInfo: (item) => ({
                            unitCount: item.useCount,
                            unit: item.useUnit,
                            traceableCodeList: (item.traceableCodeList || []).map((traceableCode) => {
                                const {
                                    count, hisPackageCount, hisPieceCount, ...rest
                                } = traceableCode;
                                return rest;
                            }),
                        }),
                        createKeyId: (item) => item.id,
                    });

                    resList.forEach((e) => {
                        const item = list.find((i) => i.id === e.keyId);
                        if (item) {
                            if (this.isStrictCountWithTraceCodeCollect && TraceCode.isSupportTraceCodeForceCheckStock()) {
                                this.$set(item, '_maxTraceCodeCount', e.traceableCodeNum);
                                this.$set(item, '_isTransformable', e.isTransformable);
                            }
                            this.$set(item, '_traceableCodeMaxNum', e.traceableCodeMaxNum);

                            if (e.list) {
                                const traceableCodeList = item.traceableCodeList ?? [];
                                e.list.forEach((codeInfo) => {
                                    const findTraceableCode = traceableCodeList.find((traceableCodeItem) => traceableCodeItem.no === codeInfo.no);
                                    if (findTraceableCode) {
                                        this.$set(findTraceableCode, 'hisMaxPackageCount', codeInfo.hisMaxPackageCount ?? Number.MAX_SAFE_INTEGER);
                                    }
                                });
                            }
                        }
                    });
                }
            },
            isFloat(n) {
                return Number(n) !== parseInt(n);
            },
            sortOrder() {
                const { goodsId } = this;
                const newOrderList = Clone(this.order.list);
                newOrderList.sort((a, b) => {
                    return (b.goodsId === goodsId) - (a.goodsId === goodsId);
                });
                this.order.list = Clone(newOrderList);
            },
            validateComment(value, callback) {
                value = value.trim();
                if (value) {
                    callback({
                        validate: true,
                    });
                } else {
                    callback({
                        message: '不能为空',
                        validate: false,
                    });
                }
            },
            getCostConfig(item) {
                if (isChineseMedicine(item.goods) && item.useUnit === 'Kg') {
                    return {
                        formatLength: 2, max: 10000000, supportZero: true,
                    };
                }
                return {
                    formatLength: 5, max: 10000000, supportZero: true,
                };
            },
            async updateOrderInfo() {
                const postData = {
                    lastModifiedDate: this.order.lastModifiedDate,
                    supplierId: this.supplierId,
                    outOrderNo: this.order.outOrderNo,
                    inspectBy: this.order.inspectBy,
                    comment: this.comment,
                };
                try {
                    this.saveBtnLoading = true;
                    await StockInAPI.updateOrderInfo(this.orderId, postData);
                    this.offset = 0;
                    await this.fetchOrder();
                    this.$Toast({
                        type: 'success',
                        message: '入库单修改成功',
                    });
                    this.edit = false;
                    this.$nextTick(() => {
                        this.$refs.orderInfoPopoverRef?.doClose();
                    });
                } catch (e) {
                    console.log(e);
                } finally {
                    this.saveBtnLoading = false;
                }
            },
            openSupplierDialog() {
                this.$refs.supplierSelect.showPopper = false;
                this.showSupplierDialog = true;
            },
            onPharmacyNoChange(no) {
                const pharmacy = this.goodsInPharmacyList.find((item) => item.no === no);
                this.order.pharmacy = pharmacy || {};
            },
            /**
             * @desc 新增供应商
             * <AUTHOR>
             * @date 2019/07/10 23:07:07
             * @params
             * @return
             */
            addSupplier(supplier) {
                this.suppliers.push(supplier);
                this.supplierId = supplier.id;
            },

            cancelSupplier() {
                this.edit = false;
                this.order = Clone(this.cloneOrder);
                if (this.order.comment?.length) {
                    this.comment = this.order.comment[this.order.comment.length - 1].content;
                }
            },
            handleUpdate() {
                this.edit = true;
                this.supplierId = this.order.supplierId;
            },
            //是否能填写追溯码
            isEnterTraceableCode(item) {
                const canEnterTraceableCode =
                    item.useCount && this.isPackageUnitMedicine(item) && (isChinesePatentMedicine(item.goods) || isWesternMedicine(item.goods));
                if (!canEnterTraceableCode) {
                    item.traceableCodeList = null;
                }
                return canEnterTraceableCode;
            },
            //是否是大单位药
            isPackageUnitMedicine(item) {
                return item.useUnit === (item.goods?.packageUnit || '') ;
            },

            async handleOpenMallOrderDetail() {
                if (this.order.mallOrderId) {
                    const mall = await this.$abcPlatform.module.mall;
                    mall.service.OrderDetailDialog({
                        orderId: this.order.mallOrderId,
                    });
                }
            },
            handleOpenReviseLog() {
                new AbcCorrectionOrderLogDialog({
                    visible: true,
                    title: '修改记录',
                    orderId: this.orderId,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            handleMoreDropdownChange(type) {
                if (type === 'TransFormGoods') {
                    this.dialogVisible = true;
                }
            },
            async handleConfirmAction({
                transType,
                transferType,
                transOutPharmacyNo,
                transInPharmacyNo,
                selectedPharmacyNo,
            }) {
                this.transType = transType;
                this.transOutPharmacyNo = transOutPharmacyNo;
                this.transInPharmacyNo = transInPharmacyNo;
                this.selectedPharmacyNo = selectedPharmacyNo;
                this.orderList = await this.getFullOrderList();
                // 店内调拨
                if (transType === 1) {
                    this.showOutForm = true;
                } else {
                    // 店间调拨调出
                    if (transferType !== 1) {
                        this.showOutForm = true;
                    }
                }
                this.dialogVisible = false;
            },
            async getFullOrderList() {
                const { list: orderList } = this.order;
                // 按照goodsId分组
                const groupListByGoodsId = orderList.reduce((acc, item) => {
                    const { goodsId } = item;
                    const finedItem = acc.find((it) => it.goodsId === goodsId);
                    if (!finedItem) {
                        acc.push({
                            goodsId,
                            list: [item],
                        });
                    } else {
                        finedItem.list.push(item);
                    }
                    return acc;
                }, []);

                // 根据goodsId查询批次信息
                const goodsIds = groupListByGoodsId.map((item) => item.goodsId);
                const { data: { list = [] } } = await GoodsAPI.fetchGoodIdsBatchs(
                    goodsIds,
                    this.clinicId,
                    this.order?.pharmacy?.no,
                    this.order?.pharmacy?.type,
                );

                return list.map((it) => {
                    // 当前批次信息
                    const { batchs } = it;
                    // 入库批次信息
                    const inBatchs = groupListByGoodsId.find((item) => item.goodsId === it.goodsId).list;
                    // 根据入库单信息添加对应批次
                    const newBatchs = inBatchs.map((item) => {
                        const { batchId } = item;
                        const findBatch = batchs.find((batch) => batch.batchId === batchId);
                        return {
                            ...findBatch,
                            ...item,
                        };
                    });
                    return {
                        // 库存信息
                        stocks: batchs,
                        goods: {
                            ...it,
                            ...it.goods,
                        },
                        packageCostPrice: this.stocksCostPrice(batchs),
                        outPackageCostPrice: this.stocksCostPrice(batchs),
                        batchs: newBatchs,
                    };
                });
            },
            closeDialogHandler() {
                this.showDialog = false;
            },
        },
    };
</script>
<style lang="scss" scoped>
@import 'src/styles/theme.scss';
@import 'src/styles/mixin.scss';

.handle-traceable-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: #005ed9;
    cursor: pointer;
}
</style>

