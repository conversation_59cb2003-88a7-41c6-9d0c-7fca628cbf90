<!--prop type 的意思： -->
<!--1：只有销售单位： 采购退货出库，调拨（中药只有小单位）-->
<!--2：大 + 小 ： 盘点（单独写了）-->
<!--3：单位可选： 入库，可是出库，报损出库-->

<template>
    <abc-form-item class="goods-unit-input-wrapper" required :validate-event="validateFn(currentValue)">
        <!--中药单独处理-->
        <abc-input
            v-if="isChineseMedicine(currentValue.goods)"
            v-model.number="count"
            v-abc-focus-selected
            type="number"
            :input-custom-style="{ textAlign: 'center' }"
            :width="width"
            :max-length="maxLength"
            :config="countInputConfig"
            @enter="enterEvent"
            @input="inputChange"
            @change="changeCount"
        >
            <div v-if="$slots.tips" slot="prepend" class="count-tips">
                <slot name="tips"></slot>
            </div>
            <!--中药显示小单位-->
            <span slot="append">{{ currentValue.goods.pieceUnit }}</span>
        </abc-input>

        <template v-else>
            <abc-input
                v-if="type === 1"
                v-model.number="count"
                v-abc-focus-selected
                type="number"
                :input-custom-style="{ textAlign: 'center' }"
                :width="width"
                :max-length="maxLength"
                :config="countInputConfig"
                @enter="enterEvent"
                @change="changeCount"
                @input="inputChange"
            >
                <div v-if="$slots.tips" slot="prepend" class="count-tips">
                    <slot name="tips"></slot>
                </div>
                <span slot="append">{{ unit }}</span>
            </abc-input>

            <template v-if="type === 3">
                <abc-input
                    v-model.number="count"
                    v-abc-focus-selected
                    type="number"
                    :width="width"
                    :max-length="maxLength"
                    :input-custom-style="{ textAlign: 'center' }"
                    :config="countInputConfig"
                    @enter="enterEvent"
                    @change="changeCount"
                    @input="inputChange"
                >
                    <div v-if="$slots.tips" slot="prepend" class="count-tips">
                        <slot name="tips"></slot>
                    </div>
                    <span v-if="!currentValue.goods.dismounting" slot="append">{{ unit }}</span>
                </abc-input>

                <abc-select
                    v-if="currentValue.goods.dismounting"
                    v-model="unit"
                    :width="36"
                    :tabindex="-1"
                    @change="changeUnit"
                    @enter="enterEvent"
                >
                    <abc-option
                        :value="currentValue.goods.packageUnit"
                        :label="currentValue.goods.packageUnit"
                    ></abc-option>
                    <abc-option
                        :value="currentValue.goods.pieceUnit"
                        :label="currentValue.goods.pieceUnit"
                    ></abc-option>
                </abc-select>
            </template>
            <template v-if="type === 4">
                <abc-input
                    v-model.number="count"
                    v-abc-focus-selected
                    type="number"
                    :width="width"
                    :max-length="maxLength"
                    :input-custom-style="{ textAlign: 'center' }"
                    :config="countInputConfig"
                    @enter="enterEvent"
                    @change="changeCount"
                    @input="inputChange"
                >
                    <div v-if="$slots.tips" slot="prepend" class="count-tips">
                        <slot name="tips"></slot>
                    </div>
                    <span v-if="unitEqual(currentValue.goods)" slot="append">{{ unit }}</span>
                </abc-input>

                <abc-select
                    v-if="!unitEqual(currentValue.goods)"
                    v-model="unit"
                    :width="36"
                    :tabindex="-1"
                    @change="changeUnit"
                    @enter="enterEvent"
                >
                    <abc-option
                        :key="currentValue.goods.packageUnit"
                        :value="currentValue.goods.packageUnit"
                        :label="currentValue.goods.packageUnit"
                    ></abc-option>
                    <abc-option
                        :key="currentValue.goods.pieceUnit"
                        :value="currentValue.goods.pieceUnit"
                        :label="currentValue.goods.pieceUnit"
                    ></abc-option>
                </abc-select>
            </template>
        </template>
    </abc-form-item>
</template>

<script>
    import EnterEvent from 'views/common/enter-event';
    import { isChineseMedicine } from 'src/filters/goods.js';
    import { unitEqual } from 'views/inventory/goods-utils';
    import common from 'components/common/form';
    import { GoodsTypeEnum } from '@abc/constants';

    function validateEvent() {
        return (_, callback) => {
            callback({ validate: true });
        };
    }

    export default {
        name: 'GoodsUnitInput',
        mixins: [EnterEvent, common],
        props: {
            type: {
                type: Number,
                required: true,
            },
            value: Object,
            supportZero: {
                type: Boolean,
                default: false,
            },
            formatLength: {
                type: Number,
                default: 0,
            },
            validateFn: {
                type: Function,
                default: validateEvent,
            },
            width: {
                type: Number,
                default: 60,
            },
            maxLength: {
                type: Number,
                default: 9,
            },
        },

        data() {
            return {
                count: '',
                unit: '',
            };
        },

        computed: {
            /*eslint-disable*/
            currentValue: {
                get() {
                    if (this.value.packageCount) {
                        this.unit = this.value.goods.packageUnit;
                    } else if (this.value.pieceCount) {
                        this.unit = this.value.goods.pieceUnit;
                    } else {
                        this.unit = isChineseMedicine(this.value.goods)
                            ? this.value.goods.pieceUnit
                            : this.unit && this.hasUnit(this.unit, this.value.goods)
                            ? this.unit
                            : this.value.goods.packageUnit;
                    }
                    if (this.supportZero) {
                        this.count = this.value.packageCount || this.value.pieceCount || 0;
                    } else {
                        this.count = this.value.packageCount || this.value.pieceCount;
                    }
                    this.$emit('blur', this.value);
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                    this.formItem && this.formItem.$emit('formFieldInput', val);
                },
            },
            /*eslint -enable*/
            countInputConfig(){
                if(this.value.goods.type === GoodsTypeEnum.GOODS) {
                    return { max: 100000000, supportZero: this.supportZero, formatLength: 4 };
                }
                return { max: 100000000, supportZero: this.supportZero, formatLength: this.formatLength };
            }
        },
        created() {
            if (this.value.packageCount) {
                this.unit = this.value.goods.packageUnit;
            } else if (this.value.pieceCount) {
                this.unit = this.value.goods.pieceUnit;
            } else {
                this.unit = isChineseMedicine(this.value.goods)
                    ? this.value.goods.pieceUnit
                    : this.unit
                    ? this.unit
                    : this.value.goods.packageUnit;
            }
            this.$emit('update:currentUnit', this.unit);
            this.$emit('change', this.currentValue);
        },

        methods: {
            isChineseMedicine,
            unitEqual,
            // 判断药品是否有这个单位
            hasUnit(unit, goods) {
                return goods.packageUnit === unit || goods.pieceUnit === unit;
            },

            /**
             * @desc count change
             * <AUTHOR>
             * @date 2018/11/27 14:21:22
             */
            changeCount(count) {
                if (this.isChineseMedicine(this.currentValue.goods)) {
                    this.currentValue.packageCount = '';
                    this.currentValue.pieceCount = count;
                } else {
                    if (this.unit === this.currentValue.goods.packageUnit) {
                        this.currentValue.packageCount = count;
                        this.currentValue.pieceCount = '';
                    } else if (this.unit === this.currentValue.goods.pieceUnit) {
                        this.currentValue.packageCount = '';
                        this.currentValue.pieceCount = count;
                    }
                }

                this.$emit('update:currentUnit', this.unit);
                this.$emit('change', this.currentValue);
            },

            inputChange(count) {
                // 中药的packageUnit 和 pieceUnit 可能会一样
                if (this.isChineseMedicine(this.currentValue.goods)) {
                    this.currentValue.packageCount = '';
                    this.currentValue.pieceCount = count;
                } else {
                    if (this.unit === this.currentValue.goods.packageUnit) {
                        this.currentValue.packageCount = count;
                        this.currentValue.pieceCount = '';
                    } else if (this.unit === this.currentValue.goods.pieceUnit) {
                        this.currentValue.packageCount = '';
                        this.currentValue.pieceCount = count;
                    }
                }
                this.$emit('input', this.currentValue);
            },

            /**
             * @desc unit change
             * <AUTHOR>
             * @date 2018/11/27 14:22:47
             */
            changeUnit(unit) {
                if (unit === this.currentValue.goods.packageUnit) {
                    this.currentValue.packageCount = this.count;
                    this.currentValue.pieceCount = '';
                } else if (unit === this.currentValue.goods.pieceUnit) {
                    this.currentValue.pieceCount = this.count;
                    this.currentValue.packageCount = '';
                }
                this.$emit('update:currentUnit', unit);
                this.$emit('change', this.currentValue);
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    .goods-unit-input-wrapper {
        display: flex;
        align-items: center;
        margin-right: 0;
        margin-bottom: 0;

        .abc-input__inner {
            height: 36px;
            line-height: 36px;
        }

        .append-input {
            width: 36px;
            min-width: 36px;
        }

        .prepend-input {
            right: 36px;
            left: auto;
            z-index: 4;
            width: 16px;

            .cis-icon-Attention {
                color: #ff9933;

                &:focus {
                    outline: none;
                }
            }
        }

        .abc-input-wrapper {
            .abc-input__inner {
                padding: 3px 8px;
                text-align: center;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;

                &:focus {
                    position: relative;
                    z-index: 1;
                }
            }
        }

        .abc-select-wrapper {
            .abc-input__inner {
                padding: 0 8px;
                border-left-width: 0;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;

                &:active {
                    border-left-width: 1px;
                }

                &:focus {
                    border-left-width: 1px;
                }
            }

            .cis-icon-dropdown_triangle {
                right: 0;
            }
        }
    }
</style>
