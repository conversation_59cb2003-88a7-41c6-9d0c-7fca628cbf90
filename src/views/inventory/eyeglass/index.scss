@import 'src/styles/mixin';

.eyeglass-core {
    position: relative;

    .title {
        display: flex;
        gap: 8px;
        align-items: center;
        margin-bottom: 12px;

        .text span {
            font-size: 14px;
            font-weight: bold;
            line-height: 20px;
        }
    }

    .divider {
        height: 0;
        margin-top: 24px;
        margin-bottom: 12px;
        border-top: 1px dashed $P6;
    }

    .sku-table-wrapper {
        position: relative;
        display: flex;
        margin-top: 12px;
        overflow: hidden;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);
        //transform: translateZ(0);

        .fixed-table {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;

            &.table-shadow {
                .table {
                    box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
                }
            }

            .table {
                .th.fixed:last-child,
                .td.fixed:last-child {
                    border-right: 1px solid $P1 !important;
                }

                .table-body {
                    overflow: hidden;

                    .scrolling-container {
                        overflow: hidden;

                        .phantom-content.phantom-stripe {
                            border-right: 1px solid $P1 !important;
                        }
                    }
                }
            }
        }

        .sku-table {
            display: flex;
            flex: 1;
            flex-direction: column;
            height: 302px;
            //padding-bottom: 10px;
            //overflow-x: overlay;
            //overflow-y: hidden;
            overflow: auto;
            scroll-behavior: smooth;

            .table--li-border-right {
                .table-header {
                    //overflow-y: scroll;
                    //border-right: 1px solid transparent;
                    padding-right: 11px !important;
                }

                .table-body > .li,
                .table-body .actual-content > .li {
                    border-right: 1px solid $P1 !important;
                }
            }

            .table-body {
                background-color: #ffffff !important;
            }

            .always-center {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                margin: auto;
                pointer-events: none;
                transform: none;
            }
        }

        .luminosity-table {
            width: 114px;
            height: 100%;

            .th {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 32px;
                color: #7a8794;
                background-color: #f9fafc;
            }

            .td {
                width: 100%;
                height: 270px;
                background-color: #ffffff;
                border-top: 1px solid $P1;
                border-left: 1px solid $P1;

                .photometry {
                    width: 107px !important;
                    height: 242px !important;
                    margin-top: 16px;
                    margin-left: 3px;
                }
            }
        }

        // 复写v3table样式
        .table {
            .table-header {
                height: 32px !important;
            }

            .table-body {
                height: calc(100% - 32px) !important;
                min-height: 37px !important;

                .li {
                    .tr {
                        height: 37px !important;

                        .td:not(.td-custom-input) {
                            .abc-form-item input,
                            .abc-input__inner {
                                height: 36px !important;
                            }
                        }
                    }
                }

                // 去掉第一行li的tr的border-top
                & > .li:first-of-type > .row-popper > .tr {
                    height: 36px !important;
                }

                .scrolling-container .actual-content > .li:first-of-type > .row-popper > .tr {
                    height: 36px !important;
                }

                .scrolling-container .phantom-content.phantom-stripe {
                    background-size: 100% 74px !important;
                }
            }
        }
    }

    .table {
        margin-bottom: 0 !important;
        border: none !important;
        border-bottom: 1px solid $P1 !important;
        border-radius: 0 !important;

        &.table--no-border-bottom {
            border-bottom: none !important;
        }

        .abc-form-item {
            width: 100%;
        }

        .abc-form-item > .abc-form-item-content {
            width: 100%;
        }

        .abc-form-item > .abc-form-item-content > .abc-input-wrapper {
            width: 100%;
        }

        .table-body {
            &.no-data {
                min-height: 0 !important;
                border-top: none !important;
            }
        }
    }

    .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 85px;
        height: 51px;
        margin: 56px auto 194px;

        i {
            color: $P1;
            transition: color 0.3s ease-in-out;
        }

        .text {
            font-size: 14px;
            color: $T3;
            transition: color 0.3s ease-in-out;
        }

        &:hover {
            cursor: pointer;

            .text,
            i {
                color: $T2;
            }
        }
    }
}

// sku规格设置弹窗
.eyeglass-edit-sku-spec-setting-dialog {
    .dialog-content {
        display: flex;
        padding: 0 0 0 24px;

        .left {
            width: 424px;

            .scroll-content {
                max-height: 420px;
                padding-top: 12px;
                padding-right: 12px;
                padding-left: 2px;
                margin-right: 0;
                margin-bottom: 23px;
                margin-left: -2px;
                overflow: auto;
                overflow: overlay;
                scroll-behavior: smooth;

                @include scrollBar();

                .color-wrapper {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 12px;

                    .color-tag {
                        position: relative;
                        width: 92px;
                        height: 32px;
                        font-size: 14px;
                        line-height: 32px;
                        color: #000000;
                        text-align: center;
                        border: 1px solid $P1;
                        border-radius: 0;
                        outline: none;

                        .delete-icon {
                            position: absolute;
                            top: 0;
                            right: 0;
                            background: #ffffff;
                            transform: translate(50%, -50%);
                        }

                        &:hover {
                            border-color: #0270c9;
                        }

                        &:focus {
                            z-index: 2 !important;
                            border-color: #0270c9;
                            box-shadow: 0 0 0 2px #c3e0fe !important;
                            transition: border 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                        }

                        &.add {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 20px;
                            color: #aab4bf;

                            &:hover {
                                color: #7a8794;
                                border-color: #aab4bf;
                            }
                        }
                    }
                }
            }
        }

        .right {
            box-sizing: content-box;
            width: 140px;
            height: 300px;
            padding: 63px 44px 120px 38px;
            margin-left: 2px;
            background-color: #f5f7fb;
            border-left: 1px solid #e6eaee;
        }
    }
}
