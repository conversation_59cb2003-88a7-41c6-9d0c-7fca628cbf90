<template>
    <div
        v-abc-loading="loading"
        class="eyeglass-core-wrapper"
    >
        <abc-form
            ref="eyeglassForm"
            class="eyeglass-core"
        >
            <div class="title">
                <span class="text">
                    <span>SPU信息</span>
                    <abc-popover
                        placement="top-start"
                        theme="yellow"
                        trigger="hover"
                        width="300px"
                        style="display: inline-block;"
                    >
                        <abc-icon
                            slot="reference"
                            icon="info_bold"
                            size="14"
                            :color="$store.state.theme.style.P3"
                        >
                        </abc-icon>
                        <div>
                            SPU：标准化产品单元，属性、特征相同的商品，例：暴龙BJ6105镜架为一个SPU，其中灰色、黑色各为一个SKU
                        </div>
                    </abc-popover>
                </span>
            </div>

            <spu-info
                :keys="spuKeys"
                :type="eyeglassInfo.type"
                :sub-type="eyeglassInfo.subType"
                :form-data="eyeglassInfo.spu"
                :spu-tag-list="eyeglassInfo.spuTagList"
                :disabled="!isAdmin || disabledBaseInfo"
                :fee-types-list="feeTypesList"
                style="min-height: 132px;"
                @update="updateSpuTagList"
            ></spu-info>

            <div class="divider"></div>

            <div class="title">
                <span class="text">
                    <span>SKU列表</span>
                    <abc-popover
                        placement="top-start"
                        theme="yellow"
                        trigger="hover"
                        width="300px"
                        style="display: inline-block;"
                    >
                        <abc-icon
                            slot="reference"
                            icon="info_bold"
                            size="14"
                            :color="$store.state.theme.style.P3"
                        >
                        </abc-icon>
                        <div>
                            SKU：最小库存单元，商品下规格不同的子商品，例：暴龙BJ6105镜架为一个SPU，其中灰色、黑色各为一个SKU
                        </div>
                    </abc-popover>
                </span>
                <!--门店筛选-->
                <clinic-select
                    v-if="isChainAdmin && spuGoodsId"
                    v-model="selectedClinicId"
                    @change="fetchSpuGoods(true, true)"
                ></clinic-select>
                <!--sku类型-->
                <abc-tabs
                    v-if="eyeglassInfo.subType === 1"
                    v-model="currentTab"
                    :option="options.tab"
                    size="middle"
                    type="outline"
                    @change="onTabChange"
                ></abc-tabs>
                <!--筛选-->
                <eyeglass-select-popover
                    v-if="spuGoodsId"
                    :params="selectParams"
                    :sub-type="eyeglassInfo.subType"
                    :include-zero-stock="includeZeroStock"
                    :include-disable-stock="includeDisableStock"
                    @change="onSelectParamsChange"
                >
                </eyeglass-select-popover>

                <abc-button
                    v-if="isAdmin && (eyeglassInfo.sku.goodsList.length || spuGoodsId)"
                    type="text"
                    style="margin-left: auto;"
                    @click="onEditSkuSpec"
                >
                    编辑规格
                </abc-button>
            </div>

            <!--skuGoodsList-->
            <div
                v-abc-loading="tableLoading"
                class="sku-table-wrapper"
                :style="{
                    border: eyeglassInfo.sku.goodsList.length || spuGoodsId ? '1px solid #d9dbe3' : 'none'
                }"
            >
                <div
                    v-if="eyeglassInfo.skuList.length && hasScrollBar"
                    class="fixed-table"
                    :class="scrollLeft ? 'table-shadow' : ''"
                >
                    <goods-table-v3
                        ref="fixedTable"
                        :key="tableKey"
                        readonly
                        :header-config="currentSkuConfig.filter(e=>e.fixed)"
                        :data-list="eyeglassInfo.skuList"
                        row-key="id"
                        class="table"
                        :class="{ 'table--no-border-bottom': !hasScrollBar && eyeglassInfo.skuList.length > 7 }"
                        :style="{
                            width: `${skuFixedTableWidth}px`,
                            height: eyeglassInfo.skuList.length > 7 ? (hasScrollBar ? `292px` : '302px') : 'auto',
                        }"
                        :enable-virtual-list="enableVirtualList"
                        :virtual-list-config="{
                            ...virtualListConfig,
                            total: 0
                        }"
                        :scroll-bar-watch="false"
                        :create-tr-class-name="createTrClassName"
                        @sort="onSort"
                    >
                        <template #index="{ rowIndex }">
                            <span>{{ rowIndex + 1 }}</span>
                        </template>

                        <template #range="{ row }">
                            <span v-abc-title.ellipsis="row.name || ''"></span>
                        </template>

                        <template #spherical="{ row }">
                            <span v-abc-title.ellipsis="formatDegree(row.spherical?.start)"></span>
                        </template>

                        <template #lenticular="{ row }">
                            <span v-abc-title.ellipsis="formatDegree(row.lenticular?.start)"></span>
                        </template>

                        <template #focalLength="{ row }">
                            <span v-abc-title.ellipsis="formatDegree(row.focalLength?.start)"></span>
                        </template>

                        <template #color="{ row }">
                            <span v-abc-title.ellipsis="row.color || ''"></span>
                        </template>

                        <template #spec="{ row }">
                            <span v-abc-title.ellipsis="row.spec || ''"></span>
                        </template>
                    </goods-table-v3>
                </div>

                <div ref="skuTableRef" class="sku-table" @scroll="onScroll">
                    <template v-if="eyeglassInfo.sku.goodsList.length || spuGoodsId">
                        <goods-table-v3
                            ref="goodsTable"
                            :key="tableKey"
                            readonly
                            :header-config="currentSkuConfig"
                            :data-list="eyeglassInfo.skuList"
                            row-key="id"
                            class="table"
                            :class="{
                                'table--no-border-bottom': !hasScrollBar && eyeglassInfo.skuList.length > 7,
                                'table--li-border-right': eyeglassInfo.skuList.length > 7
                            }"
                            :style="{
                                width: '100%',
                                minWidth: `${skuTableWidth}px`,
                            }"
                            :scroll-bar-watch="false"
                            :enable-virtual-list="enableVirtualList"
                            :virtual-list-config="virtualListConfig"
                            :create-tr-class-name="createTrClassName"
                            @sort="onSort"
                            @scroll="onTableV3Scroll"
                        >
                            <template #index="{ rowIndex }">
                                <span>{{ rowIndex + 1 }}</span>
                            </template>

                            <template #range="{ row }">
                                <span v-abc-title.ellipsis="row.name || ''"></span>
                            </template>

                            <template #spherical="{ row }">
                                <span v-abc-title.ellipsis="formatDegree(row.spherical?.start)"></span>
                            </template>

                            <template #lenticular="{ row }">
                                <span v-abc-title.ellipsis="formatDegree(row.lenticular?.start)"></span>
                            </template>

                            <template #focalLength="{ row }">
                                <span v-abc-title.ellipsis="formatDegree(row.focalLength?.start)"></span>
                            </template>

                            <template #color="{ row }">
                                <span v-abc-title.ellipsis="row.color || ''"></span>
                            </template>

                            <template #spec="{ row }">
                                <span v-abc-title.ellipsis="row.spec || ''"></span>
                            </template>

                            <template #dispGoodsCount="{ row }">
                                <span
                                    v-abc-title.ellipsis="row.goods?.dispGoodsCount || ''"
                                    style="padding: 0 10px;"
                                ></span>
                            </template>
                            <template #dispStockGoodsCount="{ row }">
                                <span
                                    v-abc-title.ellipsis="row.goods?.dispStockGoodsCount || ''"
                                    style="padding: 0 10px;"
                                ></span>
                            </template>
                            <template #minExpiryDate="{ row }">
                                <span
                                    v-abc-title.ellipsis="row.goods?.minExpiryDate || ''"
                                    style="padding: 0 10px;"
                                ></span>
                            </template>
                            <template #packageCostPrice="{ row }">
                                <span
                                    v-abc-title.ellipsis="moneyDigit(row?.goods?.packageCostPrice, 2)"
                                    style="padding: 0 10px;"
                                ></span>
                            </template>
                            <template #lastPackageCostPrice="{ row }">
                                <span
                                    v-abc-title.ellipsis="formatPrice(row?.goods?.lastPackageCostPrice)"
                                    style="padding: 0 10px;"
                                ></span>
                            </template>
                            <template #recentAvgSell="{ row }">
                                <span
                                    v-abc-title.ellipsis="formatRecentAvgSell(row?.goods?.recentAvgSell ?? '', row.goods)"
                                    style="padding: 0 10px;"
                                ></span>
                            </template>
                            <template #profitRat="{ row }">
                                <span
                                    v-abc-title.ellipsis="formatProfitRat(row?.goods?.profitRat)"
                                    style="padding: 0 10px;"
                                ></span>
                            </template>
                            <template #turnoverDays="{ row }">
                                <span
                                    v-abc-title.ellipsis="formatDay(row?.goods?.turnoverDays)"
                                    style="padding: 0 10px;"
                                ></span>
                            </template>


                            <template #packagePrice="{ row }">
                                <abc-form-item v-if="isAdmin && isEditing" required>
                                    <abc-input
                                        v-model="row.packagePrice"
                                        v-abc-focus-selected
                                        type="money"
                                        :config="{ formatLength: 2 }"
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        @change="onChangeSkuListItem(row)"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <span v-else style="padding: 0 10px; text-align: right;">{{
                                    formatMoney(row.packagePrice)
                                }}</span>
                            </template>
                            <template #packagePriceAppend>
                                <abc-popover
                                    key="packagePriceAppend"
                                    v-model="batchDialogVisible"
                                    placement="bottom-end"
                                    trigger="manual"
                                    theme="white"
                                    :disabled="batchDialogData.key !== 'packagePrice'"
                                    style="margin-left: auto;"
                                >
                                    <abc-button
                                        v-if="isAdmin && isEditing && eyeglassInfo.skuList.length > 0"
                                        slot="reference"
                                        type="text"
                                        @click.stop="onBatchAction('packagePrice')"
                                    >
                                        批量
                                    </abc-button>
                                    <abc-form
                                        ref="batchForm"
                                        label-position="left"
                                        :label-width="80"
                                        item-block
                                    >
                                        <abc-form-item label="SKU范围" style="margin: 0 0 8px 0;">
                                            <abc-select
                                                v-model="batchDialogData.groupId"
                                                :disabled="!showPhotometry"
                                                :width="200"
                                            >
                                                <abc-option
                                                    v-for="s in groups"
                                                    :key="s.value"
                                                    :value="s.value"
                                                    :label="s.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-form-item
                                            :label="isSingleStore ? '零售定价' : '总部定价'"
                                            style="margin: 0 0 8px 0;"
                                        >
                                            <abc-input v-model="batchDialogData.value" :width="200">
                                                <abc-currency-symbol-icon slot="prepend"></abc-currency-symbol-icon>
                                            </abc-input>
                                        </abc-form-item>
                                    </abc-form>
                                    <div style="display: flex; justify-content: flex-end; margin-top: 12px;">
                                        <abc-button type="primary" @click="onBatchConfirm">
                                            批量填入
                                        </abc-button>
                                        <abc-button type="blank" @click="batchDialogVisible = false">
                                            取消
                                        </abc-button>
                                    </div>
                                </abc-popover>
                            </template>

                            <template #piecePrice="{ row }">
                                <abc-form-item required>
                                    <abc-input
                                        v-model="row.piecePrice"
                                        v-abc-focus-selected
                                        type="money"
                                        :config="{ formatLength: 2 }"
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        :disabled="!showSubSetPrice"
                                        @change="onChangeSkuListItem(row)"
                                    >
                                    </abc-input>
                                </abc-form-item>
                            </template>
                            <template #piecePriceAppend>
                                <abc-popover
                                    key="piecePriceAppend"
                                    v-model="batchDialogVisible"
                                    placement="bottom-end"
                                    trigger="manual"
                                    theme="white"
                                    :disabled="batchDialogData.key !== 'piecePrice'"
                                    style="margin-left: auto;"
                                >
                                    <abc-button
                                        v-if="eyeglassInfo.skuList.length > 0"
                                        slot="reference"
                                        type="text"
                                        @click.stop="onBatchAction('piecePrice')"
                                    >
                                        批量
                                    </abc-button>
                                    <abc-form
                                        ref="batchForm"
                                        label-position="left"
                                        :label-width="80"
                                        item-block
                                    >
                                        <abc-form-item label="SKU范围" style="margin: 0 0 8px 0;">
                                            <abc-select
                                                v-model="batchDialogData.groupId"
                                                :disabled="!showPhotometry"
                                                :width="200"
                                            >
                                                <abc-option
                                                    v-for="s in groups"
                                                    :key="s.value"
                                                    :value="s.value"
                                                    :label="s.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-form-item
                                            v-if="batchDialogData.key === 'piecePrice'"
                                            label="零售定价"
                                            style="margin: 0 0 8px 0;"
                                        >
                                            <abc-input v-model="batchDialogData.value" :width="200">
                                                <abc-currency-symbol-icon slot="prepend"></abc-currency-symbol-icon>
                                            </abc-input>
                                        </abc-form-item>
                                    </abc-form>
                                    <div style="display: flex; justify-content: flex-end; margin-top: 12px;">
                                        <abc-button type="primary" @click="onBatchConfirm">
                                            批量填入
                                        </abc-button>
                                        <abc-button type="blank" @click="batchDialogVisible = false">
                                            取消
                                        </abc-button>
                                    </div>
                                </abc-popover>
                            </template>

                            <template #processPrice="{ row }">
                                <abc-form-item>
                                    <abc-input
                                        v-model="row.processPrice"
                                        v-abc-focus-selected
                                        type="money"
                                        :config="{ formatLength: 2 }"
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        @change="onChangeSkuListItem(row)"
                                    >
                                    </abc-input>
                                </abc-form-item>
                            </template>
                            <template #processPriceAppend>
                                <abc-popover
                                    key="processPriceAppend"
                                    v-model="batchDialogVisible"
                                    placement="bottom-end"
                                    trigger="manual"
                                    theme="white"
                                    :disabled="batchDialogData.key !== 'processPrice'"
                                    style="margin-left: auto;"
                                >
                                    <abc-button
                                        v-if="eyeglassInfo.skuList.length > 0"
                                        slot="reference"
                                        type="text"
                                        @click.stop="onBatchAction('processPrice')"
                                    >
                                        批量
                                    </abc-button>
                                    <abc-form
                                        ref="batchForm"
                                        label-position="left"
                                        :label-width="80"
                                        item-block
                                    >
                                        <abc-form-item label="SKU范围" style="margin: 0 0 8px 0;">
                                            <abc-select
                                                v-model="batchDialogData.groupId"
                                                :disabled="!showPhotometry"
                                                :width="200"
                                            >
                                                <abc-option
                                                    v-for="s in groups"
                                                    :key="s.value"
                                                    :value="s.value"
                                                    :label="s.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-form-item
                                            v-if="batchDialogData.key === 'processPrice'"
                                            label="加工价"
                                            style="margin: 0 0 8px 0;"
                                        >
                                            <abc-input v-model="batchDialogData.value" :width="200">
                                                <abc-currency-symbol-icon slot="prepend"></abc-currency-symbol-icon>
                                            </abc-input>
                                        </abc-form-item>
                                    </abc-form>
                                    <div style="display: flex; justify-content: flex-end; margin-top: 12px;">
                                        <abc-button type="primary" @click="onBatchConfirm">
                                            批量填入
                                        </abc-button>
                                        <abc-button type="blank" @click="batchDialogVisible = false">
                                            取消
                                        </abc-button>
                                    </div>
                                </abc-popover>
                            </template>

                            <template #pieceNum="{ row }">
                                <abc-form-item v-if="isAdmin" required>
                                    <abc-input
                                        v-model="row.pieceNum"
                                        type="money"
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        @change="onChangeSkuListItem(row)"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <span v-else style="padding: 0 10px;">{{ row.pieceNum }}</span>
                            </template>
                            <template #pieceNumAppend>
                                <abc-popover
                                    key="pieceNumAppend"
                                    v-model="batchDialogVisible"
                                    placement="bottom-end"
                                    trigger="manual"
                                    theme="white"
                                    :disabled="batchDialogData.key !== 'pieceNum'"
                                    style="margin-left: auto;"
                                >
                                    <abc-button
                                        v-if="eyeglassInfo.skuList.length > 0"
                                        slot="reference"
                                        type="text"
                                        @click.stop="onBatchAction('pieceNum')"
                                    >
                                        批量
                                    </abc-button>
                                    <abc-form
                                        ref="batchForm"
                                        label-position="left"
                                        :label-width="80"
                                        item-block
                                    >
                                        <abc-form-item label="SKU范围" style="margin: 0 0 8px 0;">
                                            <abc-select
                                                v-model="batchDialogData.groupId"
                                                :disabled="!showPhotometry"
                                                :width="200"
                                            >
                                                <abc-option
                                                    v-for="s in groups"
                                                    :key="s.value"
                                                    :value="s.value"
                                                    :label="s.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-form-item
                                            v-if="batchDialogData.key === 'pieceNum'"
                                            label="包装数量"
                                            style="margin: 0 0 8px 0;"
                                        >
                                            <abc-input v-model="batchDialogData.value" :width="200">
                                            </abc-input>
                                        </abc-form-item>
                                    </abc-form>
                                    <div style="display: flex; justify-content: flex-end; margin-top: 12px;">
                                        <abc-button type="primary" @click="onBatchConfirm">
                                            批量填入
                                        </abc-button>
                                        <abc-button type="blank" @click="batchDialogVisible = false">
                                            取消
                                        </abc-button>
                                    </div>
                                </abc-popover>
                            </template>

                            <template #shortId="{ row }">
                                <abc-form-item
                                    v-if="isAdmin"
                                    :validate-event="validateWrapper"
                                    :validate-params="{
                                        showError: shortIdErrorInfo[row.shortId] > 1,
                                        message: 'SKU编码重复'
                                    }"
                                >
                                    <abc-input
                                        v-model.trim="row.shortId"
                                        :disabled="!isEditing"
                                        placeholder="系统生成或自定义"
                                        @change="batchCheckShortId(row)"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="row.shortId || ''" style="padding: 0 10px;"></span>
                            </template>
                            <template #shortIdAppend>
                                <abc-popover
                                    placement="top"
                                    theme="yellow"
                                    trigger="hover"
                                    style="margin-left: 4px;"
                                >
                                    <abc-icon
                                        slot="reference"
                                        icon="info_bold"
                                        size="14"
                                        :color="$store.state.theme.style.P3"
                                    >
                                    </abc-icon>
                                    <div>
                                        系统生成或自定义
                                    </div>
                                </abc-popover>
                            </template>

                            <template #barCode="{ row }">
                                <abc-form-item
                                    v-if="isAdmin"
                                    :validate-event="validateWrapper"
                                    :validate-params="{
                                        showError: barCodeErrorInfo[row.barCode] > 1,
                                        message: '条码重复'
                                    }"
                                >
                                    <abc-input
                                        v-model.trim="row.barCode"
                                        :disabled="!isEditing"
                                        @change="batchCheckBarCode(row)"
                                    >
                                    </abc-input>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="row.barCode || ''" style="padding: 0 10px;"></span>
                            </template>

                            <template #v2DisableStatus="{ row }">
                                <abc-form-item required>
                                    <abc-select
                                        :value="row.v2DisableStatus"
                                        adaptive-width
                                        :disabled="isAdmin ? false : !!row.chainDisable"
                                        @change="onCheckDisable(row, $event)"
                                    >
                                        <abc-option
                                            v-for="s in options.status"
                                            :key="s.value"
                                            :value="s.value"
                                            :label="s.label"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </template>
                            <template #v2DisableStatusAppend>
                                <abc-popover
                                    key="v2DisableStatusAppend"
                                    v-model="batchDialogVisible"
                                    placement="bottom-end"
                                    trigger="manual"
                                    theme="white"
                                    :disabled="batchDialogData.key !== 'v2DisableStatus'"
                                    style="margin-left: auto;"
                                >
                                    <abc-button
                                        v-if="eyeglassInfo.skuList.length > 0"
                                        slot="reference"
                                        type="text"
                                        @click.stop="onBatchAction('v2DisableStatus')"
                                    >
                                        批量
                                    </abc-button>
                                    <abc-form
                                        ref="batchForm"
                                        label-position="left"
                                        :label-width="80"
                                        item-block
                                    >
                                        <abc-form-item label="SKU范围" style="margin: 0 0 8px 0;">
                                            <abc-select
                                                v-model="batchDialogData.groupId"
                                                :disabled="!showPhotometry"
                                                :width="200"
                                            >
                                                <abc-option
                                                    v-for="s in groups"
                                                    :key="s.value"
                                                    :value="s.value"
                                                    :label="s.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-form-item
                                            v-if="batchDialogData.key === 'v2DisableStatus'"
                                            label="状态"
                                            style="margin: 0 0 8px 0;"
                                        >
                                            <abc-select v-model="batchDialogData.value" :width="200">
                                                <abc-option
                                                    v-for="s in options.status"
                                                    :key="s.value"
                                                    :value="s.value"
                                                    :label="s.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                    </abc-form>
                                    <div style="display: flex; justify-content: flex-end; margin-top: 12px;">
                                        <abc-button type="primary" @click="onBatchConfirm">
                                            批量填入
                                        </abc-button>
                                        <abc-button type="blank" @click="batchDialogVisible = false">
                                            取消
                                        </abc-button>
                                    </div>
                                </abc-popover>
                            </template>
                        </goods-table-v3>
                        <abc-content-empty v-if="!eyeglassInfo.skuList.length" class="always-center" value="暂无数据"></abc-content-empty>
                    </template>
                    <template v-else>
                        <div v-if="isAdmin" class="empty" @click="onAddSkuSpec">
                            <abc-icon icon="plus"></abc-icon>
                            <span class="text">添加SKU</span>
                        </div>
                    </template>
                </div>

                <div v-if="eyeglassInfo.skuList.length && showPhotometry" class="luminosity-table">
                    <div class="th">
                        光度范围
                    </div>
                    <div class="td">
                        <photometry
                            class="photometry"
                            :is-pwr="isPwr"
                            :photometry-data="currentPhotometryData"
                        ></photometry>
                    </div>
                </div>
            </div>
            <!--保证弹窗大小不变化-->
            <!--<div v-if="!isLoaded" class="block" style="height: 56px;"></div>-->
        </abc-form>
        <!--SKU规格设置-->
        <abc-dialog
            v-if="skuSpecDialogVisible"
            v-model="skuSpecDialogVisible"
            title="SKU规格设置"
            content-styles="padding:0;"
            class="eyeglass-edit-sku-spec-setting-dialog"
        >
            <div class="dialog-content">
                <div class="left">
                    <abc-tabs
                        v-model="currentSpecTab"
                        size="middle"
                        :option="options.spec"
                        :style="{
                            width: 'calc(100% - 12px)'
                        }"
                    ></abc-tabs>
                    <div ref="scrollRef" class="scroll-content">
                        <template v-if="currentSpecTab === SpecType.Photometric">
                            <luminosity-group
                                ref="luminosityGroup"
                                :spu-name="eyeglassInfo.spu.name"
                                :current-item="currentSelectedGroup"
                                :is-pwr="isPwr"
                                :loading="luminosityGroupsLoading"
                                :luminosity-groups="eyeglassInfo.specGroup.photometricRange"
                                @delete="onDeleteGroup"
                                @select="onSelectGroup"
                            ></luminosity-group>
                            <abc-button
                                size="small"
                                type="success"
                                @click="onAddGroup"
                            >
                                加一组
                            </abc-button>
                        </template>
                        <sku-tag-group
                            v-else-if="currentSpecTab === SpecType.Color"
                            :items="eyeglassInfo.specGroup.colors"
                            :loading="tagGroupsLoading"
                            @delete="onDeleteTag"
                            @change="onTagGroupChange"
                        ></sku-tag-group>
                        <sku-tag-group
                            v-else-if="currentSpecTab === SpecType.Spec"
                            :items="eyeglassInfo.specGroup.specs"
                            item-key="spec"
                            :loading="tagGroupsLoading"
                            @delete="onDeleteTag"
                            @change="onTagGroupChange"
                        ></sku-tag-group>
                    </div>
                </div>
                <div v-if="showPhotometry" class="right">
                    <photometry
                        selectable
                        :is-pwr="isPwr"
                        :photometry-data="photometryData"
                        :selected-id="currentSelectedGroup?.id"
                        @select="onSelectPhotometry"
                    ></photometry>
                </div>
            </div>

            <div slot="footer" class="dialog-footer">
                <span style="margin-right: auto; font-size: 14px; color: #7a8794;">{{ footerLeftText }}</span>
                <abc-button type="primary" :loading="luminosityGroupsLoading" @click="onConfirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="onCancel">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </div>
</template>

<script>
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import { EyeglassAPI } from 'api/goods/eyeglass';
    import {
        isNull, formatMoney, paddingMoney, moneyDigit,
    } from '@/utils';
    import { percent } from '@/filters';
    import clone from 'utils/clone';
    import SpuInfo from 'views/inventory/eyeglass/components/spu-info.vue';
    import Photometry from 'views/inventory/eyeglass/components/photometry.vue';
    import LuminosityGroup from 'views/inventory/eyeglass/components/luminosityGroup.vue';
    import SkuTagGroup from 'views/inventory/eyeglass/components/sku-tag.vue';
    import eyeglassSelectPopover from 'views/inventory/eyeglass/components/eyeglass-select-popover.vue';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';
    const GoodsTableV3 = () => import('views/inventory/components/v3goods/goods-table.vue');
    import {
        calcStepData,
        validateSpherical,
        validateLenticular,
        merge,
        transformGroup,
        filterChangeData,
        classifyGoodsByType, validateFocalLength, formatSkuList, filterSkuGoodsForKeys, filterSpecGroupForKey,
    } from 'views/inventory/eyeglass/utils';
    import {
        OpFlag, SkuType, SpecType, DisableStatus, SpuInfoFormData, SkuFilterFormData,
    } from 'views/inventory/eyeglass/constant';
    import { formatRecentAvgSell } from 'views/purchase/utils';
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import GoodsAPI from 'api/goods';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';


    export default {
        name: 'EyeglassCore',
        components: {
            SpuInfo,
            GoodsTableV3,
            Photometry,
            LuminosityGroup,
            SkuTagGroup,
            ClinicSelect,
            eyeglassSelectPopover,
            AbcCurrencySymbolIcon,
        },
        props: {
            typeId: Number,
            subType: [Number, String],
            spuGoodsId: String,
            skuGoodsId: String,
            spuSpecItem: Object,
            disabledBaseInfo: Boolean,
            visible: Boolean,
            // eslint-disable-next-line vue/require-default-prop
            refresh: Function,
            // eslint-disable-next-line vue/require-default-prop
            close: Function,
            // eslint-disable-next-line vue/require-default-prop
            updateList: Function,
        },
        data() {

            return {
                SpecType,
                hasScrollBar: false,
                isLoaded: false,// 详情数据是否加载完成
                selectedClinicId: '', // 被选中的门店ID
                showDialog: this.visible,
                isPullAll: false,// 是否拉取全量数据
                isEditing: true,// 新建默认为编辑状态
                loading: false,// 获取详情
                tableLoading: false,// 表格加载
                btnLoading: false,
                btnDisabled: false,
                continueBtnLoading: false,
                continueBtnDisabled: false,
                luminosityGroupsLoading: false,// 光度组加载(查询删除显示)
                tagGroupsLoading: false,// 规格颜色组加载(查询删除显示)
                currentTab: SkuType.Finished,
                currentSpecTab: SpecType.Photometric,
                orderBy: '',
                orderType: '',
                tableKey: +new Date,
                options: {
                    tab: [
                        {
                            label: '成品', value: SkuType.Finished,
                        },
                        {
                            label: '定制', value: SkuType.CustomTailor,
                        },
                    ],
                    spec: [
                        {
                            label: '光度', value: SpecType.Photometric,
                        },
                        {
                            label: '颜色', value: SpecType.Color,
                        },
                        {
                            label: '规格', value: SpecType.Spec,
                        },
                    ],
                    steps: [
                        {
                            label: '0.25', value: 0.25,
                        },
                        {
                            label: '0.50', value: 0.5,
                        },
                        {
                            label: '1.00', value: 1,
                        },
                    ],
                    status: [
                        {
                            label: '启用', value: DisableStatus.Usable,
                        },
                        {
                            label: '停用：余量可售', value: DisableStatus.Disable10,
                        },
                        {
                            label: '停用：余量禁售', value: DisableStatus.Disable20,
                        },
                    ],
                },
                batchDialogVisible: false,
                batchDialogData: {
                    key: '',// packagePrice总部价格 piecePrice门店售价 processPrice加工价格 disable状态
                    groupId: '',
                    value: '',
                },
                skuSpecDialogVisible: false,
                currentSelectedGroup: null,// 当前光度范围组
                selectParams: {
                    ...SkuFilterFormData,
                },
                // 机制概述：先加载50条，无尽列表滚动加载，不滚动不加载，批量操作重新发送全量请求，点开功能时先loading加载全部数据，加载完成后操作
                limit: 50,
                offset: 0,
                total: 0,
                includeZeroStock: 1,
                includeDisableStock: 1,
                isChangeSpuInfo: false,
                isChangeSpuTagList: false,
                skuItemChangeKey: +new Date,
                eyeglassInfo: {
                    type: GoodsTypeEnum.EYEGLASSES,
                    subType: this.subType,
                    spu: {
                        typeId: GoodsTypeIdEnum.LENS,
                        customTypeId: '',
                    },
                    sku: {
                        goodsList: [],// 源数据
                        specGroup: {
                            colors: [],
                            specs: [],
                            photometricRange: [],
                        },
                    },
                    skuList: [],// 用于渲染的数据，会过滤与排序
                    // 规格弹窗操作的数据
                    specGroup: {
                        colors: [],
                        specs: [],
                        photometricRange: [],
                    },
                    spuTagList: [],
                },
                scrollLeft: 0,
                shortIdErrorInfo: {},// 编码错误信息
                barCodeErrorInfo: {},// 条码错误信息
                feeTypesList: [], // 费用类型列表
                defaultFeeType: '', // 眼镜的默认费用类型
            };
        },
        computed: {
            ...mapGetters(['isAdmin','isSingleStore', 'isChainAdmin', 'goodsConfig', 'showSubSetPrice', 'currentPharmacy']),
            isPwr() {
                return this.eyeglassInfo.subType === 4 || this.eyeglassInfo.subType === 5;
            },
            enableVirtualList() {
                if (this.spuGoodsId) {
                    return this.total > 50;//规则是一次加载50条，如果总数大于50条，就开启虚拟列表
                }
                return this.eyeglassInfo.skuList.length > 100;
            },
            virtualListConfig() {
                const config = {
                    bufferLoad: true,
                    bufferSize: 10,
                    visibleCount: 10,
                    scrollendTime: 300,
                    rowHeight: 37,// 36+1边框
                    phantomStripe: true,
                    phantomHeightOffset: 1,
                };
                // 加入滚动加载配置
                if (this.spuGoodsId) {
                    // 拉过全量数据后就不用滚动加载了
                    config.total = this.isPullAll ? 0 : this.total;
                    config.bufferLoad = false;
                    config.fetchData = this.fetchSpuGoods;
                }
                return config;
            },
            // totalCount() {
            //     const [goodsList0, goodsList10] = classifyGoodsByType(this.eyeglassInfo.sku.goodsList, SkuType.CustomTailor);
            //
            //     return {
            //         [SkuType.Finished]: goodsList10.length,
            //         [SkuType.CustomTailor]: goodsList0.length,
            //     };
            // },
            showPhotometry() {
                return this.eyeglassInfo.subType === 1 || this.eyeglassInfo.subType === 4 || this.eyeglassInfo.subType === 5;
            },
            groups() {
                return this.eyeglassInfo.sku.specGroup.photometricRange.reduce((groups, group) => {
                    if (this.currentTab === group.type) {
                        groups.push({
                            label: `组${groups.length}：${group.name}`, value: group.id,
                        });
                    }
                    return groups;
                }, [{
                    label: '全部', value: '',
                }]);
            },
            footerLeftText() {
                // 生成最新规格数据创建的列表
                const {
                    goodsList, photometricCount, colors, specs,
                } = this.createGoodsList({
                    ...this.eyeglassInfo.specGroup,
                    photometricRange: this.photometryData,
                });
                // console.log('footerLeftText', goodsList, photometricRange, photometricCount, colors, specs);

                if (this.eyeglassInfo.subType === 1) {
                    if (!photometricCount) return '';
                    if (this.currentTab === SkuType.CustomTailor) {
                        return `光度定制范围${photometricCount}种`;
                    }
                    return `光度${photometricCount}种，共${photometricCount}个成品规格`;
                }
                if (this.eyeglassInfo.subType === 4) {
                    if (!photometricCount) return '';
                    if (!colors.length) {
                        return `光度${photometricCount}种，共${photometricCount}个成品规格`;
                    }
                    return `光度${photometricCount}种，颜色${colors.length}种，共${goodsList.length}种规格`;
                }
                if (this.eyeglassInfo.subType === 5) {
                    if (!photometricCount) return '';
                    return `光度定制范围${photometricCount}种`;
                }
                if (this.eyeglassInfo.subType === 3 || this.eyeglassInfo.subType === 6) {
                    if (!specs?.length) return '';
                    return `规格${specs.length}种，共${goodsList.length}项`;
                }
                if (this.eyeglassInfo.subType === 2) {
                    if (!colors?.length) return '';
                    return `颜色${colors.length}种，共${goodsList.length}项`;
                }

                if (!goodsList?.length) return '';
                return `${goodsList.length}种`;


            },
            // 分类型处理-生成光度图数据
            photometryData() {
                return this.eyeglassInfo.specGroup.photometricRange?.filter((item) => {

                    if (this.eyeglassInfo.subType === 4 || this.eyeglassInfo.subType === 5) {
                        let passCount = 0;
                        validateFocalLength((val) => {
                            if (val.validate) passCount++;
                        }, item, 'start');
                        validateFocalLength((val) => {
                            if (val.validate) passCount++;
                        }, item, 'end');

                        return passCount === 2;
                    }
                    if (this.eyeglassInfo.subType === 1) {
                        let passCount = 0;
                        validateSpherical((val) => {
                            if (val.validate) passCount++;
                        }, item, 'start');
                        validateSpherical((val) => {
                            if (val.validate) passCount++;
                        }, item, 'end');
                        validateLenticular((val) => {
                            if (val.validate) passCount++;
                        }, item, 'start');
                        validateLenticular((val) => {
                            if (val.validate) passCount++;
                        }, item, 'end');

                        return passCount === 4;
                    }
                    return false;

                }).map((group) => transformGroup(group, this.showPhotometry, this.isPwr));
            },
            // 表格右侧光度图数据
            currentPhotometryData() {
                return this.eyeglassInfo.sku.specGroup.photometricRange?.filter((item) => {
                    return item.type === this.currentTab && item.opFlag !== OpFlag.Deletion;
                });
            },
            currentSkuConfig() {
                const fixedStyle = {
                // thStyle: {
                //     position: 'relative',
                //     left: `${this.scrollLeft}px`,
                //     willChange: 'left',
                //     zIndex: 10,
                // },
                // tdStyle: {
                //     position: 'relative',
                //     left: `${this.scrollLeft}px`,
                //     willChange: 'left',
                //     zIndex: 10,
                // },
                };

                return [
                    {
                        label: '序号',
                        prop: 'index',
                        width: 50,
                        justifyContent: 'center',
                        tdClass: 'is-disabled',
                        fixed: 1,
                        ...fixedStyle,
                    },
                    {
                        label: '光度范围',
                        prop: 'range',
                        width: 240,
                        hidden: (this.eyeglassInfo.subType === 1 ? this.currentTab === SkuType.Finished : this.eyeglassInfo.subType !== 5),
                        tdClass: 'is-disabled',
                        fixed: 1,
                        ...fixedStyle,
                    },
                    {
                        label: '球镜',
                        prop: 'spherical',
                        width: 70,
                        sortable: 1,
                        fixed: 1,
                        tdClass: 'is-disabled',
                        thStyle: {
                            justifyContent: 'space-between',
                            ...fixedStyle.thStyle,
                        },
                        tdStyle: {
                            justifyContent: 'flex-start',
                            ...fixedStyle.tdStyle,
                        },
                        hidden: this.eyeglassInfo.subType === 1 ? this.currentTab === SkuType.CustomTailor : true,
                    },
                    {
                        label: '柱镜',
                        prop: 'lenticular',
                        width: 70,
                        sortable: 1,
                        fixed: 1,
                        tdClass: 'is-disabled',
                        thStyle: {
                            justifyContent: 'space-between',
                            ...fixedStyle.thStyle,
                        },
                        tdStyle: {
                            justifyContent: 'flex-start',
                            ...fixedStyle.tdStyle,
                        },
                        hidden: this.eyeglassInfo.subType === 1 ? this.currentTab === SkuType.CustomTailor : true,
                    },
                    {
                        label: '后顶焦度',
                        prop: 'focalLength',
                        width: 100,
                        sortable: 1,
                        fixed: 1,
                        tdClass: 'is-disabled',
                        thStyle: {
                            justifyContent: 'space-between',
                            ...fixedStyle.thStyle,
                        },
                        tdStyle: {
                            justifyContent: 'flex-start',
                            ...fixedStyle.tdStyle,
                        },
                        hidden: this.eyeglassInfo.subType !== 4,
                    },
                    {
                        label: '颜色',
                        prop: 'color',
                        width: 170,
                        tdClass: 'is-disabled',
                        hidden: !(this.eyeglassInfo.subType === 2 || this.eyeglassInfo.subType === 4),
                        fixed: 1,
                        ...fixedStyle,
                    },
                    {
                        label: '规格',
                        prop: 'spec',
                        width: 170,
                        tdClass: 'is-disabled',
                        hidden: !(this.eyeglassInfo.subType === 3 || this.eyeglassInfo.subType === 6),
                        fixed: 1,
                        ...fixedStyle,
                    },
                    {
                        label: '包装数量',
                        prop: 'pieceNum',
                        width: 120,
                        append: this.isAdmin,
                        tdClass: 'is-disabled',
                        tdStyle: {
                            padding: '0px',
                        },
                        justifyContent: 'flex-end',
                        hidden: !(this.eyeglassInfo.subType === 4 || this.eyeglassInfo.subType === 5),
                    },
                    {
                        label: '最小单位',
                        prop: 'pieceUnit',
                        width: 80,
                        tdClass: 'is-disabled',
                        hidden: this.spuGoodsId || !(this.eyeglassInfo.subType === 4 || this.eyeglassInfo.subType === 5),
                    },
                    {
                        label: '包装单位',
                        prop: 'packageUnit',
                        width: 80,
                        tdClass: 'is-disabled',
                        hidden: this.spuGoodsId,
                    },
                    {
                        label: this.isSingleStore ? '零售价格' : '总部定价',
                        prop: 'packagePrice',
                        thStyle: {
                            justifyContent: 'flex-start',
                        },
                        flex: '1 140px',
                        append: 1,
                        sortable: 1,
                        // required: this.isAdmin,
                        minWidth: 140,
                        tdClass: 'is-disabled',
                        tdStyle: {
                            justifyContent: 'flex-start',
                            padding: '0px',
                        },
                    },
                    {
                        label: '零售定价',
                        prop: 'piecePrice',
                        flex: '1 140px',
                        append: this.showSubSetPrice,
                        // required: this.showSubSetPrice,
                        sortable: 1,
                        minWidth: 140,
                        tdClass: 'is-disabled',
                        thStyle: {
                            justifyContent: 'flex-start',
                        },
                        tdStyle: {
                            padding: '0px',
                        },
                        hidden: this.isAdmin,
                    },
                    {
                        label: '加工价',
                        prop: 'processPrice',
                        thStyle: {
                            justifyContent: 'flex-start',
                        },
                        flex: '1 120px',
                        append: 1,
                        sortable: 1,
                        minWidth: 120,
                        tdClass: 'is-disabled',
                        tdStyle: {
                            padding: '0px',
                        },
                        // 暂时隐藏
                        hidden: true,
                    // hidden: !((this.eyeglassInfo.subType === 1 && this.currentTab === SkuType.CustomTailor)) || this.eyeglassInfo.subType === 3,
                    },
                    {
                        label: '状态',
                        prop: 'v2DisableStatus',
                        width: 140,
                        append: 1,
                        tdClass: 'is-disabled',
                        thStyle: {
                            justifyContent: 'flex-start',
                        },
                        tdStyle: {
                            padding: '0px',
                        },
                    },
                    {
                        label: 'SKU编码',
                        prop: 'shortId',
                        flex: '1 140px',
                        minWidth: 140,
                        // append: 1,
                        tdClass: 'is-disabled',
                        tdStyle: {
                            padding: '0px',
                        },
                    },
                    {
                        label: '条码',
                        prop: 'barCode',
                        flex: '1 140px',
                        minWidth: 140,
                        tdClass: 'is-disabled',
                        tdStyle: {
                            padding: '0px',
                        },
                        hidden: ((this.eyeglassInfo.subType === 1 && this.currentTab === SkuType.CustomTailor)) || this.eyeglassInfo.subType === 3 || this.eyeglassInfo.subType === 5,
                    },
                    {
                        label: '当前库存',
                        prop: 'dispGoodsCount',
                        justifyContent: 'flex-end',
                        sortable: 1,
                        flex: '1 100px',
                        minWidth: 100,
                        tdStyle: {
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                        hidden: !this.spuGoodsId,
                    },
                    {
                        label: '可售库存',
                        prop: 'dispStockGoodsCount',
                        justifyContent: 'flex-end',
                        flex: '1 100px',
                        sortable: 1,
                        minWidth: 100,
                        tdStyle: {
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                        hidden: !this.spuGoodsId,
                    },
                    {
                        label: '最近效期',
                        prop: 'minExpiryDate',
                        flex: '1 100px',
                        sortable: 1,
                        minWidth: 100,
                        tdStyle: {
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                        hidden: !this.spuGoodsId,
                    },
                    {
                        label: '成本金额',
                        prop: 'packageCostPrice',
                        justifyContent: 'flex-end',
                        flex: '1 100px',
                        sortable: 1,
                        minWidth: 100,
                        tdStyle: {
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                        hidden: !this.spuGoodsId,
                    },
                    {
                        label: '最近进价',
                        prop: 'lastPackageCostPrice',
                        justifyContent: 'flex-end',
                        flex: '1 100px',
                        sortable: 1,
                        minWidth: 100,
                        tdStyle: {
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                        hidden: !this.spuGoodsId,
                    },
                    {
                        label: '日均销量',
                        prop: 'recentAvgSell',
                        justifyContent: 'flex-end',
                        flex: '1 100px',
                        sortable: 1,
                        minWidth: 100,
                        tdStyle: {
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                        hidden: !this.spuGoodsId,
                    },
                    {
                        label: '毛利率',
                        prop: 'profitRat',
                        justifyContent: 'flex-end',
                        flex: '1 100px',
                        sortable: 1,
                        minWidth: 100,
                        tdStyle: {
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                        hidden: !this.spuGoodsId,
                    },
                    {
                        label: '周转天数',
                        prop: 'turnoverDays',
                        justifyContent: 'flex-end',
                        flex: '1 100px',
                        sortable: 1,
                        minWidth: 100,
                        tdStyle: {
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                        hidden: !this.spuGoodsId,
                    },
                ].filter((item) => !item.hidden);
            },

            spuKeys() {
                let keys = [];

                if (this.eyeglassInfo.subType === 1) {
                    keys = [
                        {
                            prop: 'name', label: '商品名',
                        },
                        {
                            prop: 'typeId', label: '商品类型',
                        },
                        {
                            prop: 'brandName', label: '品牌',
                        },
                        {
                            prop: 'manufacturerFull', label: '生产厂家',
                        },
                        {
                            prop: 'refractiveIndex', label: '折射率',
                        },
                        {
                            prop: 'addLightLeft', label: '下加光', hidden: this.currentTab === SkuType.Finished,
                        },
                        {
                            prop: 'packageUnit', label: '单位',
                        },
                        {
                            prop: 'inTaxRat', label: '税率',
                        },
                    // { prop: 'processManufacturer', label: '加工厂家', hidden: this.currentTab === SkuType.Finished },
                    ];
                }
                if (this.eyeglassInfo.subType === 2) {
                    keys = [
                        {
                            prop: 'name', label: '商品名',
                        },
                        {
                            prop: 'typeId', label: '商品类型',
                        },
                        {
                            prop: 'brandName', label: '品牌',
                        },
                        {
                            prop: 'manufacturerFull', label: '生产厂家',
                        },
                        {
                            prop: 'material', label: '材质',
                        },
                        {
                            prop: 'packageUnit', label: '单位',
                        },
                        {
                            prop: 'inTaxRat', label: '税率',
                        },
                    ];
                }
                if (this.eyeglassInfo.subType === 3) {
                    keys = [
                        {
                            prop: 'name', label: '商品名',
                        },
                        {
                            prop: 'typeId', label: '商品类型',
                        },
                        {
                            prop: 'brandName', label: '品牌',
                        },
                        {
                            prop: 'manufacturerFull', label: '生产厂家',
                        },
                        {
                            prop: 'registrationNumber', label: '批准文号',// 实际会将注册编号与批准文号同步修改
                        },
                        {
                            prop: 'packageUnit', label: '单位',
                        },
                        {
                            prop: 'inTaxRat', label: '税率',
                        },
                    // { prop: 'processManufacturer', label: '加工厂家' },
                    ];
                }
                if (this.eyeglassInfo.subType === 4 || this.eyeglassInfo.subType === 5) {
                    keys = [
                        {
                            prop: 'name', label: '商品名',
                        },
                        {
                            prop: 'typeId', label: '商品类型',
                        },
                        {
                            prop: 'brandName', label: '品牌',
                        },
                        {
                            prop: 'manufacturerFull', label: '生产厂家',
                        },
                        {
                            prop: 'wearCycle', label: '佩戴周期',
                        },
                        {
                            prop: 'registrationNumber', label: '批准文号',
                        },
                        {
                            prop: 'pieceUnit', label: '最小单位',
                        },
                        {
                            prop: 'packageUnit', label: '包装单位',
                        },
                        {
                            prop: 'inTaxRat', label: '税率',
                        },
                    ];
                }

                if (this.eyeglassInfo.subType === 6) {
                    keys = [
                        {
                            prop: 'name', label: '商品名',
                        },
                        {
                            prop: 'typeId', label: '商品类型',
                        },
                        {
                            prop: 'brandName', label: '品牌',
                        },
                        {
                            prop: 'manufacturerFull', label: '生产厂家',
                        },
                        {
                            prop: 'inTaxRat', label: '税率',
                        },
                        {
                            prop: 'packageUnit', label: '单位',
                        },
                    ];
                }

                keys.push(
                    {
                        prop: 'feeType', label: '费用类型',
                    },
                    {
                        prop: 'spuTagList', label: '标签',
                    },
                );
                return keys.filter((item) => !item.hidden);
            },

            skuTableWidth() {
                return this.currentSkuConfig.reduce((res, item) => {
                    res += item.width || item.minWidth;
                    return res;
                }, this.eyeglassInfo.skuList.length > 7 ? 11 : 0);// 右侧滚动条宽度10+1px边框
            },
            skuFixedTableWidth() {
                return this.currentSkuConfig.filter((e) => e.fixed).reduce((res, item) => {
                    res += item.width || item.minWidth;
                    return res;
                }, 0);
            },
            // 只有规格和颜色的商品类型需要有一个默认的光度范围组，用 id做关联
            groupSpecId() {
                return this.eyeglassInfo?.sku?.specGroup?.photometricRange?.[0]?.id ?? '';
            },
            // 数据是否发生修改
            isChangeEyeglassInfo() {
                // 收集this.tableKey和this.skuItemChangeKey，变化时触发计算属性重新执行，避免直接监听this.eyeglassInfo.sku.goodsList
                console.log('isChangeEyeglassInfo', this.tableKey, this.skuItemChangeKey);
                // spu是否修改
                if (this.isChangeSpuInfo) return true;
                // spuTagList是否修改
                if (this.isChangeSpuTagList) return true;
                // sku列表数据是否修改
                return !!filterChangeData(this.eyeglassInfo.sku.goodsList)?.length;
            },
        },
        watch: {
            'eyeglassInfo.subType': {
                handler(subType) {
                    // 如果是角膜塑形镜或者硬性透氧镜默认选中定制
                    if (subType === 3 || subType === 5) {
                        this.currentTab = SkuType.CustomTailor;
                    } else {
                        this.currentTab = SkuType.Finished;
                    }

                    // 处理规格弹窗 tab
                    if (subType === 1 || subType === 5) {
                        this.currentSpecTab = SpecType.Photometric;
                        this.options.spec = [
                            {
                                label: '光度', value: SpecType.Photometric,
                            },
                        ];
                    }

                    if (subType === 3 || subType === 6) {
                        this.currentSpecTab = SpecType.Spec;
                        this.options.spec = [
                            {
                                label: '规格', value: SpecType.Spec,
                            },
                        ];
                        if (!this.spuGoodsId) {
                            this.eyeglassInfo.sku.specGroup.photometricRange = [
                                // 需要有个默认的组,做关联使用
                                {
                                    id: `${+new Date}`,
                                    opFlag: OpFlag.Addition,
                                    type: this.currentTab,
                                },
                            ];
                        }
                    }

                    if (subType === 4) {
                        this.currentSpecTab = SpecType.Photometric;
                        this.options.spec = [
                            {
                                label: '光度', value: SpecType.Photometric,
                            },
                            {
                                label: '颜色', value: SpecType.Color,
                            },
                        ];
                    }

                    if (subType === 2) {
                        this.currentSpecTab = SpecType.Color;
                        this.options.spec = [
                            {
                                label: '颜色', value: SpecType.Color,
                            },
                        ];
                        if (!this.spuGoodsId) {
                            this.eyeglassInfo.sku.specGroup.photometricRange = [
                                // 需要有个默认的组,做关联使用
                                {
                                    id: `${+new Date}`,
                                    opFlag: OpFlag.Addition,
                                    type: this.currentTab,
                                },
                            ];
                        }
                    }

                },
                immediate: true,
            },
            'eyeglassInfo.skuList': {
                handler(val) {
                    if (val) {
                        this.$nextTick(() => {
                            const { skuTableRef } = this.$refs;
                            this.hasScrollBar = skuTableRef.clientWidth < skuTableRef.scrollWidth;
                        });
                    }
                },
                immediate: true,
            },
            'eyeglassInfo.spu.defaultInOutTax': {
                handler(val) {
                    if (val) {
                        this.inOutTaxChangeHandle();
                    }
                },
                immediate: true,
            },
            'eyeglassInfo.spu.packageUnit': {
                handler(val) {
                    if (val) {
                        this.eyeglassInfo.sku.goodsList.forEach((item) => {
                            item.packageUnit = val || '';
                        });
                    }
                },
                immediate: true,
            },
            'eyeglassInfo.spu.pieceUnit': {
                handler(val) {
                    if (val) {
                        this.eyeglassInfo.sku.goodsList.forEach((item) => {
                            item.pieceUnit = val || '';
                        });
                    }
                },
                immediate: true,
            },
            'eyeglassInfo.spu': {
                handler(val) {
                    if (val) {
                        this.isChangeSpuInfo = !isEqual(val, this._cacheSpuInfo);
                    }
                },
                deep: true,
            },
            'eyeglassInfo.spuTagList': {
                handler(val) {
                    if (val) {
                        this.isChangeSpuTagList = !isEqual(val, this._cacheSpuTagList);
                    }
                },
                deep: true,
            },
            isChangeEyeglassInfo() {
                this.$emit('change', this.isChangeEyeglassInfo);
            },
        },
        async created() {
            await this.fetchFeeTypes();
            this.initData();
            this.updateTagsByFetch(true);
        },
        methods: {
            ...mapActions('goods', ['updateTagsByFetch']),
            formatRecentAvgSell,
            formatMoney,
            paddingMoney,
            moneyDigit,
            calcStepData,
            formatDegree(val) {
                return val > 0 ? `+${formatMoney(val)}` : formatMoney(val);
            },
            formatPrice(price) {
                return isNull(price) ? '' : this.paddingMoney(price);
            },
            formatProfitRat(profitRat) {
                return isNull(profitRat) ? '' : percent(profitRat);
            },
            formatDay(day) {
                return isNull(day) ? '' : `${day}天`;
            },
            validateWrapper(_, callback, {
                showError, message = 'SKU编码重复',
            }) {
                if (showError) {
                    return callback({
                        validate: false,
                        message,
                    });
                }
                return callback({
                    validate: true,
                });
            },
            inOutTaxChangeHandle() {
                const { typeId } = this.eyeglassInfo.spu;
                this.goodsConfig?.inOutTaxList?.forEach((item) => {
                    if (typeId.toString() === item.typeId.toString()) {
                        this.eyeglassInfo.spu.outTaxRat = Number(item.outTaxRat);
                        this.eyeglassInfo.spu.inTaxRat = Number(item.inTaxRat);
                    }
                });
            },
            onScroll(e) {
                this.scrollLeft = e.target.scrollLeft;
            },
            onTableV3Scroll(e) {
                this.$refs.fixedTable?.scrollTo?.(e.target.scrollTop);
            },
            // 过滤筛选组件的数据，针对每一条数据，如果有一个不符合条件，就返回false
            filterSelectParams(item = {}, params = {}) {
                let flag = true;

                Object.keys(params).forEach((key) => {
                    const value = params[key];
                    const itemValue = item[key];
                    if (isNull(value)) return;
                    // 如果已经找到不符合条件就不再继续
                    if (!flag) return flag;
                    if (key === 'spherical' || key === 'lenticular' || key === 'focalLength') {
                        const {
                            start, end,
                        } = itemValue || {};
                        // value在范围内
                        flag = (+value >= +start && +value <= +end) || (+value <= +start && +value >= +end);
                    } else {
                        flag = itemValue.includes(value);
                    }
                });

                return flag;
            },
            // 根据初始化data中的部分数据
            initData() {
                // 详情与编辑
                if (this.spuGoodsId) {
                    this.fetchSpuGoods(true);
                } else {
                    // 新增
                    // 直接展示数据
                    this.isLoaded = true;

                    let packageUnit = '片';// 类型1、3
                    let pieceUnit = '';
                    if (this.subType === 4 || this.subType === 5) {
                        packageUnit = '盒';
                        pieceUnit = '片';
                    }
                    if (this.subType === 6 || this.subType === 2) {
                        packageUnit = '副';
                        pieceUnit = '';
                    }

                    this.eyeglassInfo.subType = this.subType;
                    this.eyeglassInfo.spu = {
                        ...SpuInfoFormData,
                        typeId: this.typeId,
                        packageUnit,
                        pieceUnit,
                        feeTypeId: this.defaultFeeType,
                    };
                    this._cacheSpuInfo = clone(this.eyeglassInfo.spu);
                    this.eyeglassInfo.sku = {
                        goodsList: [],
                        specGroup: {
                            colors: [],
                            specs: [],
                            photometricRange: ([2, 3, 6].includes(this.eyeglassInfo.subType)) ? [
                                {
                                    id: `${+new Date}`,
                                    opFlag: OpFlag.Addition,
                                    type: this.currentTab,
                                },
                            ] : [],
                        },
                    };
                    this.eyeglassInfo.skuList = [];
                    this.options.status = [
                        {
                            label: '启用', value: DisableStatus.Usable,
                        },
                        {
                            label: '停用', value: DisableStatus.Disable20,
                        },
                    ];
                }
            },
            async fetchSpuGoods(isNeedLoading = false, isPullAll = false) {
                let modifyDataMap = null;
                if (this.spuGoodsId) {
                    if (isNeedLoading) {
                        this.loading = true;
                    } else {
                        this.tableLoading = true;
                    }
                    try {
                        const params = {
                            clinicId: this.selectedClinicId,
                            pharmacyNo: this.isChainAdmin ? '' : (this.currentPharmacy?.no ?? ''),
                        };
                        if (!isPullAll) {
                            params.limit = this.limit;
                            params.offset = this.offset;
                            params.customType = this.currentTab;
                            params.goodsId = this.skuGoodsId;
                        }

                        // 修改过的数据
                        modifyDataMap = filterChangeData(this.eyeglassInfo.sku.goodsList).reduce((res, item) => {
                            res[item.id] = item;
                            return res;
                        }, {});

                        const { data } = await EyeglassAPI.getSpuGoodsInfo(this.spuGoodsId, params);

                        if (data) {
                            this.eyeglassInfo.spu = Object.assign({}, SpuInfoFormData, data.spu);
                            this.eyeglassInfo.type = +data.type;
                            this.eyeglassInfo.subType = +data.subType;
                            this.eyeglassInfo.spuTagList = (data.spuTagList || []).map((item) => ({
                                ...item,
                                disabled: this.isChainAdmin || this.isSingleStore ? false : !item.clinicId,
                            }));
                            this._cacheSpuInfo = clone(this.eyeglassInfo.spu);
                            this._cacheSpuTagList = clone(this.eyeglassInfo.spuTagList);

                            if (!isPullAll) {
                                data.skuList = [...(this._cachedSkuList || []), ...data.skuList];
                                // 保存skuList
                                this._cachedSkuList = data.skuList;
                            } else {
                                // 保存全部skuList
                                this._cachedSkuList = data.skuList || [];
                                this.isPullAll = true;
                            }

                            // 这个total没区分customType
                            this.total = data.total || this._cachedSkuList.length;
                            if (this.total > this._cachedSkuList.length) {
                                this.offset += this.limit;
                            } else {
                                // 不是镜片的话表示滚动加载完所有数据了
                                if (this.eyeglassInfo.subType !== 1 && !this.skuGoodsId) {
                                    this.isPullAll = true;
                                }
                            }
                            // 为规格组中的颜色和规格生成id
                            const colorIdMap = {};
                            const specIdMap = {};
                            data.specGroup?.colors?.forEach((item) => {
                                const colorId = `${+new Date}-${item.color}`;
                                colorIdMap[item.color] = colorId;
                                item.id = colorId;
                            });
                            data.specGroup?.specs?.forEach((item) => {
                                const specId = `${+new Date}-${item.spec}`;
                                specIdMap[item.spec] = specId;
                                item.id = specId;
                            });
                            this.eyeglassInfo.sku = {
                                goodsList: formatSkuList(data.skuList || [], +data.subType, this.isAdmin, colorIdMap, specIdMap),
                                specGroup: data.specGroup,
                            };
                            // 保存skuList
                            this._cachedSkuList = data.skuList || [];

                            // 合并数据
                            if (modifyDataMap) {
                                this.eyeglassInfo.sku.goodsList.forEach((item) => {
                                    const { id } = item;
                                    if (modifyDataMap[id]) {
                                        Object.assign(item, modifyDataMap[id]);
                                    }
                                });
                            }
                            this.$nextTick(() => {
                                // 如果是sku查看详情，需要筛选数据展示当前sku
                                if (this.spuSpecItem && !isPullAll) {
                                    const {
                                        type, color, spec, focalLength, spherical, lenticular,
                                    } = this.spuSpecItem;
                                    this.currentTab = type;
                                    Object.assign(this.selectParams, {
                                        color,
                                        spec,
                                        focalLength: focalLength?.start,
                                        spherical: spherical?.start,
                                        lenticular: lenticular?.start,
                                    });
                                }
                                if (isPullAll) {
                                    this.tableKey = +new Date();
                                }
                                this.filterGoodsList();

                                this.$nextTick(() => {
                                    this.loading = false;
                                    this.tableLoading = false;
                                    this.isLoaded = true;
                                    this.$emit('loaded');
                                });
                            });

                        }
                    } catch (e) {
                        console.error(e);
                        this.$Toast.error(e.message || 'SPU详情加载失败');
                        this.loading = false;
                        this.tableLoading = false;
                        this.isLoaded = true;
                    }
                }
            },
            updateSpuTagList(tagList) {
                this.eyeglassInfo.spuTagList = tagList;
            },
            async onSort({
                orderBy, orderType,
            }) {
                this.orderBy = orderBy;
                this.orderType = orderType;
                if (this.orderBy && this.orderType) {
                    if (this.spuGoodsId && !this.isPullAll) {
                        await this.fetchSpuGoods(true, true);
                    }
                    // 排序数据
                    this.eyeglassInfo.skuList.sort((a, b) => {
                        let v1 = a[this.orderBy];
                        let v2 = b[this.orderBy];
                        // 按光度排序
                        if (['spherical', 'lenticular', 'focalLength'].includes(this.orderBy)) {
                            v1 = a[this.orderBy]?.start;
                            v2 = b[this.orderBy]?.start;
                        }
                        if (this.orderBy === 'dispGoodsCount') {
                            {
                                const packageCount = a?.goods?.packageCount ?? 0;
                                const pieceCount = a?.goods?.pieceCount ?? 0;
                                const pieceNum = a?.goods?.pieceNum ?? 1;
                                v1 = packageCount * pieceNum + pieceCount;
                            }
                            {
                                const packageCount = b?.goods?.packageCount ?? 0;
                                const pieceCount = b?.goods?.pieceCount ?? 0;
                                const pieceNum = b?.goods?.pieceNum ?? 1;
                                v2 = packageCount * pieceNum + pieceCount;
                            }
                        }
                        if (this.orderBy === 'dispStockGoodsCount') {
                            {
                                const packageCount = a?.goods?.stockPackageCount ?? 0;
                                const pieceCount = a?.goods?.stockPieceCount ?? 0;
                                const pieceNum = a?.goods?.pieceNum ?? 1;
                                v1 = packageCount * pieceNum + pieceCount;
                            }
                            {
                                const packageCount = b?.goods?.stockPackageCount ?? 0;
                                const pieceCount = b?.goods?.stockPieceCount ?? 0;
                                const pieceNum = b?.goods?.pieceNum ?? 1;
                                v2 = packageCount * pieceNum + pieceCount;
                            }
                        }
                        if (this.orderBy === 'minExpiryDate') {
                            v1 = a?.goods?.minExpiryDate ? +new Date(a?.goods?.minExpiryDate) : 0;
                            v2 = b?.goods?.minExpiryDate ? +new Date(b?.goods?.minExpiryDate) : 0;
                        }

                        const keys = ['packageCostPrice', 'lastPackageCostPrice', 'recentAvgSell', 'profitRat', 'turnoverDays'];
                        if (keys.includes(this.orderBy)) {
                            v1 = a?.goods?.[this.orderBy];
                            v2 = b?.goods?.[this.orderBy];
                        }
                        return this.orderType === 'asc' ? v2 - v1 : v1 - v2;
                    });
                }
            },
            // 动态创建类名
            createTrClassName(row) {
                return `id${row.groupSpecId}`;
            },
            beforeCloseSpecDialog() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '你的编辑内容尚未保存，确定离开？',
                    onConfirm: () => {
                        this.skuSpecDialogVisible = false;
                    },
                });
            },
            onCheckDisable(skuItem, value) {
                console.log('onCheckDisable', skuItem, value);
                // 修改状态为停用时,需要校验是否有关联的商品
                if (skuItem.goodsId && value !== DisableStatus.Usable) {
                    this.batchCheckDisable([{
                        disable: skuItem.disable,
                        goodsId: skuItem.goodsId,
                        v2DisableStatus: value,
                    }]).then(() => {
                        skuItem.v2DisableStatus = value;
                        this.onChangeSkuListItem(skuItem);
                    });
                } else {
                    skuItem.v2DisableStatus = value;
                    this.onChangeSkuListItem(skuItem);
                }
            },
            onChangeSkuListItem(skuItem) {
                skuItem.opFlag = isNull(skuItem.opFlag) ? OpFlag.Modification : skuItem.opFlag;
                this.skuItemChangeKey = +new Date();
            },
            async batchCheckBarCode(skuItem) {
                this.onChangeSkuListItem(skuItem);
                const { goodsList } = this.eyeglassInfo.sku;
                const list = goodsList.reduce((res, item) => {
                    if (!(item.opFlag === OpFlag.Addition && !item.barCode)) {
                        res.push({
                            barCode: item.barCode,
                            goodsId: item.goodsId,
                            opFlag: item.opFlag,
                        });
                    }
                    return res;
                }, []);

                this.barCodeErrorInfo = goodsList.filter((e) => e.opFlag !== OpFlag.Deletion).reduce((res, item) => {
                    if (!item.barCode) return res;

                    // 本地数据检查重复
                    if (res[item.barCode]) {
                        res[item.barCode] += 1;
                    } else {
                        res[item.barCode] = 1;// 出现次数
                    }

                    return res;
                }, {});

                if (list.length) {
                    // 远程检查重复
                    await EyeglassAPI.checkBarCode({
                        goodsType: this.eyeglassInfo.type,
                        list,
                    });
                }
            },
            async batchCheckShortId(skuItem) {
                this.onChangeSkuListItem(skuItem);
                const { goodsList } = this.eyeglassInfo.sku;
                const list = goodsList.reduce((res, item) => {
                    if (!(item.opFlag === OpFlag.Addition && !item.shortId)) {
                        res.push({
                            shortId: item.shortId,
                            goodsId: item.goodsId,
                            opFlag: item.opFlag,
                        });
                    }
                    return res;
                }, []);

                this.shortIdErrorInfo = goodsList.filter((e) => e.opFlag !== OpFlag.Deletion).reduce((res, item) => {
                    if (!item.shortId) return res;

                    // 本地数据检查重复
                    if (res[item.shortId]) {
                        res[item.shortId] += 1;
                    } else {
                        res[item.shortId] = 1;// 出现次数
                    }

                    return res;
                }, {});

                if (list.length) {
                    // 服务端检查重复
                    await EyeglassAPI.checkShortId({
                        goodsType: this.eyeglassInfo.type,
                        list,
                    });
                }

            },
            // 检查能否禁用
            async batchCheckDisable(list = []) {
                if (list.length) {
                    await EyeglassAPI.checkDisable({
                        goodsType: this.eyeglassInfo.type,
                        list,
                    });
                }
            },
            // 根据条件过滤数据
            filterGoodsList(needSort = true) {

                this.eyeglassInfo.skuList = this.eyeglassInfo.sku.goodsList.reduce((res, item) => {
                    // 过滤类型,过滤删除
                    if (item.type === this.currentTab && item.opFlag !== OpFlag.Deletion) {
                        // 过滤参数
                        const flag = this.filterSelectParams(item, this.selectParams);
                        if (flag) {
                            // 过滤0库存
                            if (!this.includeZeroStock) {
                                if (item.goods?.packageCount === 0 && item.goods?.pieceCount === 0) {
                                    return res;
                                }
                            }
                            // 过滤停用
                            if (!this.includeDisableStock) {
                                // eslint-disable-next-line vue/max-len
                                if (item.disable === DisableStatus.Disabled || [DisableStatus.Disable10, DisableStatus.Disable20].includes(item.v2DisableStatus)) {
                                    return res;
                                }
                            }
                            res.push(item);
                        }
                    }
                    return res;
                }, []);
                if (needSort) {
                    this.onSort({
                        orderBy: this.orderBy, orderType: this.orderType,
                    });
                }
            },
            async onTabChange() {
                if (this.spuGoodsId && !this.isPullAll) {
                    await this.fetchSpuGoods(true, true);
                }
                this.orderType = '';
                this.orderBy = '';
                this.tableKey = +new Date();
                this.filterGoodsList();
            },
            async onSelectParamsChange({
                params, includeZeroStock, includeDisableStock,
            }, filter = true) {
                if (this.spuGoodsId && !this.isPullAll) {
                    await this.fetchSpuGoods(true, true);
                }

                this.selectParams = params;
                this.includeZeroStock = includeZeroStock;
                this.includeDisableStock = includeDisableStock;

                if (filter) {
                    this.tableKey = +new Date();
                    this.filterGoodsList();
                }
            },
            onSpuInfoChange(spu) {
                const {
                    packageUnit, pieceUnit,
                } = Object.assign(this.eyeglassInfo.spu, spu);

                if (packageUnit || pieceUnit) {
                    this.eyeglassInfo.sku.goodsList.forEach((item) => {
                        item.packageUnit = packageUnit || '';
                        item.pieceUnit = pieceUnit || '';
                    });
                }
                this.isChangeSpuInfo = true;
            },
            // 添加光度组
            onAddGroup() {
                this.eyeglassInfo.specGroup.photometricRange.push(
                    {
                        // 规则组ID 新增为null，删除或修改为后台数据存储的ID（新增前端随机指定一个id，用于goodsItem的映射）
                        id: `${+new Date}`,
                        type: this.currentTab,
                        name: '',
                        opFlag: OpFlag.Addition,
                        myopiaCombinedLuminosity: undefined,//近视联合光度
                        hyperopiaCombinedLuminosity: undefined,// 远视联合光度
                        spherical: this.isPwr ? null : {
                            start: '',
                            end: '',
                            step: 0.25,
                        },
                        lenticular: this.isPwr ? null : {
                            start: '',
                            end: '',
                            step: 0.25,
                        },
                        focalLength: this.isPwr ? {
                            start: '',
                            end: '',
                            step: 0.25,
                        } : null,
                    });

                this.$nextTick(() => {
                    if (this.$refs.scrollRef) {
                        this.$refs.scrollRef.scrollTop = 9999;
                    }
                });
            },
            onSelectGroup(item) {
                this.currentSelectedGroup = item;
            },
            onSelectPhotometry(item) {
                this.currentSelectedGroup = item;
            },
            async onDeleteGroup(item, index) {
                console.log('查询 item是否还有库存', item);
                if (!this.spuGoodsId) {
                    this.eyeglassInfo.specGroup.photometricRange.splice(index, 1);
                    return;
                }

                try {
                    const {
                        goodsList,
                    } = this.createGoodsList({
                        ...this.eyeglassInfo.specGroup,
                        photometricRange: [item],
                    });

                    if (goodsList.length) {
                        const goodsListMap = goodsList.reduce((res, e) => {
                            res[e.id] = e;
                            return res;
                        }, {});

                        const goodsIds = this.eyeglassInfo.sku.goodsList
                            .filter((e) => goodsListMap[e.id] && e.goodsId)
                            .map((e) => e.goodsId);

                        if (goodsIds.length) {
                            this.luminosityGroupsLoading = true;
                            await EyeglassAPI.checkDelete({
                                goodsIds,
                            });
                        }
                    }
                    this.eyeglassInfo.specGroup.photometricRange.splice(index, 1);
                } catch (e) {
                    console.error(e);
                    if (e.code === 12004) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else {
                        this.$Toast({
                            type: 'error',
                            message: e.message || '删除失败',
                        });
                    }
                } finally {
                    this.luminosityGroupsLoading = false;
                }

            },
            async onDeleteTag(type, item, index) {
                console.log('查询 item是否还有库存', item);
                const tags = type === 'color' ? this.eyeglassInfo.specGroup.colors : this.eyeglassInfo.specGroup.specs;
                const value = type === 'color' ? item.color : item.spec;
                if (!this.spuGoodsId) {
                    tags.splice(index, 1);
                    return;
                }

                try {

                    const goodsIds = this.eyeglassInfo.sku.goodsList
                        .filter((e) => e[type] === value && e.goodsId)
                        .map((e) => e.goodsId);

                    if (goodsIds.length) {
                        this.tagGroupsLoading = true;
                        await EyeglassAPI.checkDelete({
                            goodsIds,
                        });
                    }

                    tags.splice(index, 1);
                } catch (e) {
                    console.error(e);
                    if (e.code === 12004) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else {
                        this.$Toast({
                            type: 'error',
                            message: e.message || '删除失败',
                        });
                    }
                } finally {
                    this.tagGroupsLoading = false;
                }

            },
            onTagGroupChange(tags) {
                const key = this.currentSpecTab === SpecType.Color ? 'colors' : 'specs';
                // 只是更新到当前弹窗数据中
                this.eyeglassInfo.specGroup[key] = tags.map((c) => ({
                    ...c,
                    type: c.type || this.currentTab,
                }));
            },
            // 添加规格
            onAddSkuSpec() {
                this.onEditSkuSpec();
            },
            // 编辑规格
            async onEditSkuSpec() {
                if (this.spuGoodsId && !this.isPullAll) {
                    await this.fetchSpuGoods(true, true);
                }
                const specGroup = clone(this.eyeglassInfo.sku.specGroup);
                // 更新到规格弹窗数据源中
                this.eyeglassInfo.specGroup = {
                    colors: specGroup?.colors?.filter((item) => item.type === this.currentTab && item.opFlag !== OpFlag.Deletion) || [],
                    specs: specGroup?.specs?.filter((item) => item.type === this.currentTab && item.opFlag !== OpFlag.Deletion) || [],
                    photometricRange: specGroup?.photometricRange?.filter((item) => item.type === this.currentTab && item.opFlag !== OpFlag.Deletion) || [],
                };

                if (this.showPhotometry && this.eyeglassInfo.specGroup.photometricRange.length === 0) {
                    this.onAddGroup();
                }

                this.skuSpecDialogVisible = true;
            },
            onBatchAction(key) {
                if (this.batchDialogVisible) {
                    this.batchDialogVisible = false;
                }

                this.$nextTick(() => {
                    this.batchDialogData.key = key;
                    this.batchDialogData.value = '';
                    this.batchDialogData.groupId = '';
                    this.batchDialogVisible = true;
                });
            },
            async onBatchConfirm() {
                const {
                    key, value, groupId,
                } = this.batchDialogData;
                if (isNull(value)) return;

                // 批量操作、拉全量数据后再更新
                if (this.spuGoodsId && !this.isPullAll) {
                    await this.fetchSpuGoods(true, true);
                }

                const list = this.eyeglassInfo.skuList.filter((item) => {
                    // 子店下如果总部状态禁用的就不管了
                    if (key === 'v2DisableStatus' && !this.isAdmin && item.chainDisable) {
                        return;
                    }
                    return (groupId ? item.groupSpecId === groupId : true);
                });
                if (key === 'v2DisableStatus' && value !== DisableStatus.Usable) {
                    await this.batchCheckDisable(list.reduce((res, item) => {
                        if (item.goodsId) {
                            res.push({
                                disable: item.disable,
                                goodsId: item.goodsId,
                                v2DisableStatus: value,
                            });
                        } else {
                            // 新增的商品直接修改状态
                            item[key] = value;
                            this.onChangeSkuListItem(item);
                        }
                        return res;
                    }, []));
                }

                list.forEach((item) => {
                    item[key] = ['packagePrice', 'piecePrice', 'processPrice'].includes(key) ? Number(value).toFixed(2) : value;
                    this.onChangeSkuListItem(item);
                });

                this.batchDialogVisible = false;
            },
            // 规格设置弹窗关闭
            onCancel() {
                this.eyeglassInfo.specGroup = {};
                this.skuSpecDialogVisible = false;
                this.currentSelectedGroup = null;
            },
            // 规格设置弹窗确认
            async onConfirm() {
                // 有光度范围的时候，需要校验光度范围
                if (this.showPhotometry) {
                    if (this.eyeglassInfo.subType === 4) {
                        if (!this.eyeglassInfo.specGroup.colors.length) {
                            // 隐形眼镜提交时没有写颜色，自动添加无色
                            this.eyeglassInfo.specGroup.colors.push({
                                id: `${+new Date}-无色`,
                                color: '无色',
                                type: this.currentTab,
                            });
                        } else {
                            //检查重复颜色
                            const map = {};
                            let isRepeat = false;
                            this.eyeglassInfo.specGroup.colors.forEach((item) => {
                                if (isRepeat) return;
                                if (map[item.color]) {
                                    this.$Toast.error(`“${item.color}”颜色重复`);
                                    isRepeat = true;
                                    return;
                                }
                                map[item.color] = true;
                            });

                            if (isRepeat) {
                                this.currentSpecTab = SpecType.Color;
                                return;
                            }

                        }
                        this.currentSpecTab = SpecType.Photometric;
                        await this.$nextTick();

                    }
                    this.$refs.luminosityGroup.validate((val) => {
                        if (val) {
                            this.updateSkuData();
                        }
                    });
                } else {
                    if (this.eyeglassInfo.subType === 2) {
                        if (!this.eyeglassInfo.specGroup?.colors?.length) {
                            this.$Toast.error('请添加颜色');
                            return;
                        }
                        //检查重复颜色
                        const map = {};
                        let isRepeat = false;
                        this.eyeglassInfo.specGroup.colors.forEach((item) => {
                            if (isRepeat) return;

                            if (map[item.color]) {
                                this.$Toast.error(`“${item.color}”颜色重复`);
                                isRepeat = true;
                                return;
                            }
                            map[item.color] = true;
                        });

                        if (isRepeat) return;
                    } else {
                        if (!this.eyeglassInfo.specGroup?.specs?.length) {
                            this.$Toast.error('请添加规格');
                            return;
                        }
                        //检查重复规格
                        const map = {};
                        let isRepeat = false;
                        this.eyeglassInfo.specGroup.specs.forEach((item) => {
                            if (isRepeat) return;

                            if (map[item.spec]) {
                                this.$Toast.error(`“${item.spec}”规格重复`);
                                isRepeat = true;
                                return;
                            }
                            map[item.spec] = true;
                        });

                        if (isRepeat) return;
                    }
                    // 数据合法，更新到规格数据源中
                    this.updateSkuData();
                }
            },
            // 更新规格数据
            async updateSkuData() {
                try {

                    // 生成最新规格数据创建的列表
                    const {
                        goodsList, photometricRange, colors, specs,
                    } = this.createGoodsList(this.eyeglassInfo.specGroup);

                    // 将源数据分类
                    const [goodsList0, goodsList10] = classifyGoodsByType(this.eyeglassInfo.sku.goodsList, SkuType.CustomTailor);
                    const [photometricRange0, photometricRange10] = classifyGoodsByType(this.eyeglassInfo.sku.specGroup.photometricRange, SkuType.CustomTailor);
                    const [colors0, colors10] = classifyGoodsByType(this.eyeglassInfo.sku.specGroup.colors, SkuType.CustomTailor);
                    const [specs0, specs10] = classifyGoodsByType(this.eyeglassInfo.sku.specGroup.specs, SkuType.CustomTailor);
                    let goodsListRes = [];
                    let photometricRangeRes = [];
                    let colorsRes = [];
                    let specsRes = [];

                    // 里面包括新增修改删除的数据
                    if (this.currentTab === SkuType.CustomTailor) {
                        goodsListRes = goodsList10.concat(merge(goodsList, goodsList0, this.compareItem, this.assignItem));
                        photometricRangeRes = photometricRange10.concat(merge(photometricRange, photometricRange0));
                        colorsRes = colors10.concat(merge(colors, colors0, undefined, undefined));
                        specsRes = specs10.concat(merge(specs, specs0, undefined, undefined));
                    } else {
                        goodsListRes = goodsList0.concat(merge(goodsList, goodsList10, this.compareItem, this.assignItem));
                        photometricRangeRes = photometricRange0.concat(merge(photometricRange, photometricRange10));
                        colorsRes = colors0.concat(merge(colors, colors10, undefined, undefined));
                        specsRes = specs0.concat(merge(specs, specs10, undefined, undefined));
                    }

                    const goodsIds = goodsListRes.filter((item) => item.opFlag === OpFlag.Deletion && item.goodsId).map((item) => item.goodsId);
                    // 检查库存删除的数据
                    if (this.spuGoodsId && goodsIds.length) {
                        this.luminosityGroupsLoading = true;
                        await EyeglassAPI.checkDelete({
                            goodsIds,
                        });
                    }
                    this.eyeglassInfo.sku.goodsList = goodsListRes;
                    this.eyeglassInfo.sku.specGroup.photometricRange = photometricRangeRes;
                    this.eyeglassInfo.sku.specGroup.colors = colorsRes;
                    this.eyeglassInfo.sku.specGroup.specs = specsRes;

                    // 清除筛选条件
                    this.selectParams = {
                        ...SkuFilterFormData,
                    };
                    this.includeZeroStock = 1;
                    this.includeDisableStock = 1;
                    this.tableKey = +new Date();
                    this.filterGoodsList();// 生成skuList
                    this.onCancel();
                } catch (e) {
                    console.error(e);
                    if (e.code === 12004) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else {
                        this.$Toast({
                            type: 'error',
                            message: e.message || '删除失败',
                        });
                    }
                } finally {
                    this.luminosityGroupsLoading = false;
                }


            },
            // 创建 skuGoodsList
            createGoodsList(specGroup) {
                const goodsList = [];
                const photometricRange = specGroup.photometricRange.map((item) => transformGroup(item, this.showPhotometry, this.isPwr));
                const colors = [...specGroup.colors];
                const specs = [...specGroup.specs];
                let photometricCount = 0;

                if (this.eyeglassInfo.subType === 1) {
                    const uidMap = {};
                    photometricRange.forEach((item) => {
                        const {
                            id, spherical, lenticular, myopiaCombinedLuminosity, hyperopiaCombinedLuminosity,
                        } = item;

                        // 生成成品数据
                        if (this.currentTab === SkuType.Finished) {
                            if (spherical.step && lenticular.step) {
                                const arr1 = this.calcStepData(spherical.start, spherical.end, spherical.step);
                                const arr2 = this.calcStepData(lenticular.start, lenticular.end, lenticular.step);

                                arr1.forEach((s) => {
                                    arr2.forEach((c) => {
                                        const uid = `${s}~${c}`;
                                        // 过滤重复数据-和超过联合光度的数据
                                        let flag = true;
                                        if (s >= 0 && hyperopiaCombinedLuminosity) {
                                            flag = Math.abs(s) + Math.abs(c) <= hyperopiaCombinedLuminosity;
                                        }
                                        if (s <= 0 && myopiaCombinedLuminosity) {
                                            flag = Math.abs(s) + Math.abs(c) <= myopiaCombinedLuminosity;
                                        }
                                        if (!uidMap[uid] && flag) {
                                            goodsList.push(this.createGoodsListItem(uid, item, {
                                                spherical: {
                                                    start: s,
                                                    end: s,
                                                    step: 0,
                                                },
                                                lenticular: {
                                                    start: c,
                                                    end: c,
                                                    step: 0,
                                                },
                                            }));
                                            uidMap[uid] = true;
                                        }
                                    });
                                });
                            }
                        } else {
                            // 生成定制数据
                            const uid = `${id}`;
                            if (!uidMap[uid]) {
                                goodsList.push(this.createGoodsListItem(uid, item, {
                                    spherical, lenticular,
                                }));
                                uidMap[uid] = true;
                            }
                        }
                    });
                    photometricCount = Object.keys(uidMap).length;
                }

                if (this.eyeglassInfo.subType === 2) {
                    colors.forEach((item) => {
                        const uid = `${this.groupSpecId}~${item.id}`;
                        goodsList.push(this.createGoodsListItem(uid, item, {
                            groupSpecId: this.groupSpecId,
                            colorId: item.id,// 颜色id
                            color: item.color,// 颜色
                        }));
                    });
                }
                if (this.eyeglassInfo.subType === 3 || this.eyeglassInfo.subType === 6) {
                    specs.forEach((item) => {
                        const uid = `${this.groupSpecId}~${item.id}`;
                        goodsList.push(this.createGoodsListItem(uid, item, {
                            groupSpecId: this.groupSpecId,
                            specId: item.id,// 规格id
                            spec: item.spec,// 规格
                        }));
                    });
                }

                if (this.eyeglassInfo.subType === 4) {
                    const uidMap = {};
                    photometricRange.forEach((item) => {
                        const { focalLength } = item;

                        // 生成成品数据
                        if (this.currentTab === SkuType.Finished) {
                            if (focalLength.step) {
                                const arr = this.calcStepData(focalLength.start, focalLength.end, focalLength.step);
                                photometricCount += arr.length ?? 0;
                                arr.forEach((f) => {
                                    colors.forEach((c) => {
                                        const uid = `${f}~${c.id}`;
                                        // 过滤重复数据
                                        if (!uidMap[uid]) {
                                            goodsList.push(this.createGoodsListItem(uid, item, {
                                                focalLength: {
                                                    start: f,
                                                    end: f,
                                                    step: 0,
                                                },
                                                colorId: c.id,// 颜色id
                                                color: c.color,
                                            }));
                                            uidMap[uid] = true;
                                        }
                                    });
                                });
                            }
                        }
                    });
                }

                if (this.eyeglassInfo.subType === 5) {
                    photometricRange.forEach((item) => {
                        const {
                            id, focalLength,
                        } = item;

                        // 生成定制数据
                        if (this.currentTab === SkuType.CustomTailor) {
                            // 生成定制数据
                            const uid = `${id}~${focalLength?.start}`;// 使用组 id 作为唯一 id
                            goodsList.push(this.createGoodsListItem(uid, item, {
                                focalLength,
                            }));
                        }
                    });
                    photometricCount = photometricRange.length;

                }

                return {
                    goodsList,
                    photometricRange,
                    photometricCount,
                    colors,
                    specs,
                };
            },
            // 创建 skuGoodsListItem
            createGoodsListItem(id, item, Obj = {}) {
                const tempObj = {
                    id,// 需要唯一
                    groupSpecId: item.id,
                    type: item.type,
                    name: item.name,
                    shortId: '',
                    barCode: '',
                    v2DisableStatus: 0,
                    color: '',// 颜色
                    spec: '',// 规格
                    processPrice: '',
                    packagePrice: '',
                    piecePrice: '',
                    pieceNum: '',
                    packageUnit: this.eyeglassInfo.spu.packageUnit,
                    pieceUnit: this.eyeglassInfo.spu.pieceUnit,
                    spherical: null,
                    lenticular: null,
                    focalLength: null,
                    myopiaCombinedLuminosity: item.myopiaCombinedLuminosity,
                    hyperopiaCombinedLuminosity: item.hyperopiaCombinedLuminosity,
                };

                return Object.assign(tempObj, Obj);
            },
            // skuList使用
            compareItem(item, newItem) {
                const compareKey = [
                    'groupSpecId',
                    'color',
                    'spec',
                    'spherical',
                    'lenticular',
                    'focalLength',
                    'myopiaCombinedLuminosity',
                    'hyperopiaCombinedLuminosity',
                ];
                let isChange = false;
                compareKey.forEach((key) => {
                    // 特殊情况，如果两个值都是 “null”，也认为是相同的
                    if ((isNull(item[key]) && isNull(newItem[key])) || isChange) {
                        return;
                    }
                    if (key === 'spherical' || key === 'lenticular' || key === 'focalLength') {
                        isChange = !isEqual(item[key], newItem[key]);
                    } else {
                        isChange = item[key] !== newItem[key];
                    }
                });
                return isChange;
            },
            // skuList使用-原则是从规格弹窗的数据需要更新最新，列表填写的保持不动
            assignItem(item, newItem) {
                return Object.assign(item, {
                    groupSpecId: newItem.groupSpecId,
                    colorId: newItem.colorId,
                    color: newItem.color,
                    specId: newItem.specId,
                    spec: newItem.spec,
                    type: newItem.type,
                    name: newItem.name,
                    spherical: newItem.spherical,
                    lenticular: newItem.lenticular,
                    focalLength: newItem.focalLength,
                    myopiaCombinedLuminosity: newItem.myopiaCombinedLuminosity,
                    hyperopiaCombinedLuminosity: newItem.hyperopiaCombinedLuminosity,
                });
            },
            // 镜片弹窗完成-会在外部被调用
            async submit(callback, isContinue) {
                if (await this.validate()) {
                    try {
                        const spu = {
                            ...this.eyeglassInfo.spu,
                            defaultInOutTax: +this.eyeglassInfo.spu.defaultInOutTax,
                        };

                        const skuGoodsList = filterSkuGoodsForKeys(this.eyeglassInfo.sku.goodsList);
                        const specGroup = clone(this.eyeglassInfo.sku.specGroup);

                        specGroup.colors = filterSpecGroupForKey(specGroup.colors || [], 'color');
                        specGroup.specs = filterSpecGroupForKey(specGroup.specs || [], 'spec');

                        const params = {
                            spu,
                            specGroup,
                            skuGoodsList,
                            type: this.eyeglassInfo.type,
                            subType: this.eyeglassInfo.subType,
                            orderClientUniqKey: `${Math.random().toString(36).slice(2)}`,
                            spuTagIdList: this.eyeglassInfo.spuTagList.map((item) => item.tagId),
                        };

                        // 修改-只传修改过的数据到后端
                        if (this.spuGoodsId) {
                            if (!this.isAdmin) {
                                delete params.spu;
                            }
                            delete params.orderClientUniqKey;
                            params.skuGoodsList = filterChangeData(params.skuGoodsList);
                            params.specGroup.photometricRange = filterChangeData(params.specGroup.photometricRange);
                            params.specGroup.colors = filterChangeData(params.specGroup.colors);
                            params.specGroup.specs = filterChangeData(params.specGroup.specs);

                            // TODO: 临时处理，后端需要修改只改颜色规格时，把默认的光度组也更新传给后端
                            if ([2, 3, 6].includes(params.subType)) {
                                params.specGroup.photometricRange = [{
                                    id: this.groupSpecId,
                                    opFlag: OpFlag.Modification,
                                    type: this.currentTab,
                                }];
                            }
                        }
                        this.loading = true;
                        // 参数交到外部执行创建或修改
                        await callback(params);
                        // 是否继续添加，清除当前数据
                        if (isContinue) {
                            this.initData();
                        }
                    } catch (e) {
                        console.error(e);
                    } finally {
                        this.loading = false;
                    }
                } else {
                    console.log('二次校验出提示');
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this.$refs.eyeglassForm.validate();
                    }, 500);// 需要延迟一下，否则会出现校验不出来的情况
                }
            },
            // 镜片弹窗完成并继续-会在外部被调用
            saveContinueSubmit(callback) {
                this.submit(callback, true);
            },
            // 校验未渲染的数据并滚动到对应的位置
            async validate() {
                // 决定提交数据是否有效
                let validate = true;
                let index = -1;
                let [goodsList0, goodsList10] = classifyGoodsByType(this.eyeglassInfo.sku.goodsList, SkuType.CustomTailor);
                goodsList0 = goodsList0.filter((item) => item.opFlag !== OpFlag.Deletion);
                goodsList10 = goodsList10.filter((item) => item.opFlag !== OpFlag.Deletion);

                if (!goodsList0.length && !goodsList10.length) {
                    // TODO:处理眼镜类型为成品的情况，但是定制有数据，只是没有拉全量数据所以列表为空，但是是合法的
                    if (!this.isPullAll && this.eyeglassInfo.subType === 1 && this.currentTab === SkuType.Finished) {
                        return true;
                    }
                    this.$Toast.error('请添加SKU');
                    return false;
                }
                const isCustomType = this.currentTab === SkuType.CustomTailor;
                const currentList = isCustomType ? goodsList0 : goodsList10;
                // 先校验当前tab的数据
                for (let j = 0; j < currentList.length; j++) {
                    const item = currentList[j];
                    if (this.isAdmin) {
                        if (!item.packagePrice || this.shortIdErrorInfo[item.shortId] > 1 || this.barCodeErrorInfo[item.barCode] > 1) {
                            validate = false;
                            index = j;
                            break;
                        }
                    } else {
                        if (!item.piecePrice) {
                            validate = false;
                            index = j;
                            break;
                        }
                    }
                }
                if (!validate) {
                    // 清除过滤条件
                    await this.onSelectParamsChange({
                        params: {
                            ...SkuFilterFormData,
                        },
                        includeZeroStock: 1,
                        includeDisableStock: 1,
                    });
                    const item = currentList[index];
                    const idx = this.eyeglassInfo.skuList.findIndex((e) => e.id === item.id);
                    // 等待dom渲染
                    await this.$nextTick();
                    this.$refs.goodsTable.scrollTo(idx * 37);
                }
                // 再校验另一个tab的数据
                if (validate) {
                    const otherList = isCustomType ? goodsList10 : goodsList0;

                    for (let j = 0; j < otherList.length; j++) {
                        const item = otherList[j];
                        if (this.isAdmin) {
                            if (!item.packagePrice || this.shortIdErrorInfo[item.shortId] > 1 || this.barCodeErrorInfo[item.barCode] > 1) {
                                validate = false;
                                index = j;
                                break;
                            }
                        } else {
                            if (!item.piecePrice) {
                                validate = false;
                                index = j;
                                break;
                            }
                        }
                    }
                    if (!validate) {
                        // 切换tab
                        this.currentTab = isCustomType ? SkuType.Finished : SkuType.CustomTailor;
                        // 清除过滤条件
                        await this.onSelectParamsChange({
                            params: {
                                ...SkuFilterFormData,
                            },
                            includeZeroStock: 1,
                            includeDisableStock: 1,
                        }, false);
                        await this.onTabChange();

                        // 找到对应的数据并滚动到对应的位置
                        const item = otherList[index];
                        const idx = this.eyeglassInfo.skuList.findIndex((e) => e.id === item.id);
                        // 等待dom渲染
                        await this.$nextTick();
                        this.$refs.goodsTable.scrollTo(idx * 37);
                    }
                }

                return validate;
            },
            setEditFlag(flag) {
                this.isEditing = flag;
            },
            /**
             * 请求费用类型
             * @returns {Promise<void>}
             */
            async fetchFeeTypes() {
                try {
                    const res = await GoodsAPI.fetchFeeTypes({ scopeId: 1 });
                    const feeTypesList = res?.data?.rows || [];
                    // 将禁用的费用项过滤
                    this.feeTypesList = feeTypesList.filter((item) => !item.disabled);

                    // 获取默认费用类型
                    const glassesFeeType = this.feeTypesList.find((item) => item.name === '卫生材料费');
                    if (glassesFeeType) {
                        this.defaultFeeType = glassesFeeType.feeTypeId;
                    }
                } catch (error) {
                    console.log(error);
                }
            },
        },
    };
</script>
