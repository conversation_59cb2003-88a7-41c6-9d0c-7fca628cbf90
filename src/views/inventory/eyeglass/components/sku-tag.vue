<template>
    <div v-abc-loading="loading" class="wrapper">
        <template
            v-for="(item, index) in items"
        >
            <div
                :key="item.id"
                style="margin: 0;"
            >
                <abc-input
                    v-if="disabled || item.id === currentId"
                    v-model="item[itemKey]"
                    v-abc-auto-focus
                    class="input-new-tag"
                    :width="92"
                    :max-length="10"
                    :disabled="disabled"
                    @keyup.enter.native="handleInputConfirm(item[itemKey])"
                    @blur="handleInputConfirm(item[itemKey])"
                >
                </abc-input>
                <div
                    v-else
                    class="tag"
                    @click.stop="currentId = item.id"
                >
                    <div v-abc-title.ellipsis="item[itemKey]"></div>
                    <abc-icon
                        icon="delete_file"
                        class="icon"
                        color="#D9DBE3"
                        @click.stop="handleDelete(item, index)"
                    ></abc-icon>
                </div>
            </div>
        </template>


        <template v-if="!disabled">
            <div class="tag add" @click.stop="handleAdd">
                <abc-icon
                    icon="plus_thin"
                    color="#AAB4BF"
                ></abc-icon>
            </div>
        </template>
    </div>
</template>

<script>
    export default {
        name: 'SkuTag',
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            loading: Boolean,
            items: {
                type: Array,
                default: () => ([]),
            },
            itemKey: {
                type: String,
                default: 'color',
            },
        },
        data() {
            return {
                currentId: '',
                inputVisible: false,
                inputValue: '',
            };
        },
        watch: {
            items: {
                handler(val) {
                    //检查重复
                    const map = {};
                    let isRepeat = false;
                    val.forEach((item) => {
                        if (isRepeat) return;

                        if (map[item[this.itemKey]]) {
                            this.$Toast.error(`“${item[this.itemKey]}”${this.itemKey === 'color' ? '颜色' : '规格'}重复`);
                            isRepeat = true;
                            return;
                        }
                        map[item[this.itemKey]] = true;
                    });
                },
                deep: true,
            },
        },
        methods: {
            handleChange() {
                this.$emit('change', this.items.filter((item) => item[this.itemKey]?.trim()));
            },
            handleDelete(item, index) {
                this.$emit('delete', this.itemKey, item, index);
            },
            handleAdd() {
                const id = `${+new Date}`;
                this.items.push({
                    [this.itemKey]: '',
                    id,
                });
                this.currentId = id;
            },
            handleInputConfirm(val) {
                if (!val.trim()) {
                    this.$Toast.error('不能为空');
                    return;
                }
                this.currentId = '';
                this.handleChange();
            },
        },
    };
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding-bottom: 2px;

    .tag {
        position: relative;
        box-sizing: border-box;
        width: 92px;
        height: 32px;
        padding: 6px 10px;
        font-size: 14px;
        line-height: 20px;
        color: #000000;
        text-align: center;
        border: 1px solid $P1;

        .icon {
            position: absolute;
            top: 0;
            right: 0;
            display: none;
            background: #ffffff;
            transform: translate(50%, -50%);
        }

        &:hover {
            border-color: #459eff;

            &.add {
                border-color: #7a8794;

                .abc-icon {
                    color: #7a8794 !important;
                }
            }

            & > .icon {
                display: block;
            }
        }
    }

    .input-new-tag {
        ::v-deep .abc-input__inner {
            text-align: center;
            border-radius: 0;
        }
    }
}
</style>
