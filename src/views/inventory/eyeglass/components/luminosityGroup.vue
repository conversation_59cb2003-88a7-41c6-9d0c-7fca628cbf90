<template>
    <abc-form
        ref="luminosityForm"
        v-abc-loading="loading"
        label-position="left"
    >
        <div
            v-for="(item, index) in luminosityGroups"
            :key="item.id"
            class="card"
            :class="currentItem?.id === item.id ? 'active' : ''"
        >
            <div class="header" @click="handleSelect(item, index)">
                <span class="title">
                    {{ formatGroupTitle(item, index) }}
                </span>
                <abc-icon
                    v-if="!disabled"
                    icon="trash"
                    color="#AAB4BF"
                    :size="14"
                    @click.stop="onDelete(item, index)"
                ></abc-icon>
                <!--<trash-button v-if="!disabled" @delete="onDelete(item, index)"></trash-button>-->
            </div>
            <div v-if="isPwr" class="card-item">
                <div class="left">
                    <abc-form-item
                        label="后顶焦度"
                        :validate-event="validateWrapper"
                        :validate-params="{
                            showError: index === errorIndex,
                            fn: (callback) => validateFocalLength(callback, item, 'start')
                        }"
                    >
                        <abc-input
                            v-model="item.focalLength.start"
                            v-abc-focus-selected
                            :width="80"
                            type="money"
                            :config="inputConfig"
                            :disabled="disabled"
                            @enter="enterEvent"
                        >
                        </abc-input>
                    </abc-form-item>

                    <div
                        style="display: inline-block; width: 15px; font-size: 14px; color: #7a8794; text-align: center;"
                    >
                        ~
                    </div>

                    <abc-form-item
                        label=""
                        :validate-event="validateWrapper"
                        :validate-params="{
                            showError: index === errorIndex,
                            fn: (callback) => validateFocalLength(callback, item, 'end')
                        }"
                    >
                        <abc-input
                            v-model="item.focalLength.end"
                            v-abc-focus-selected
                            :width="80"
                            type="money"
                            :config="inputConfig"
                            :disabled="disabled"
                            @enter="enterEvent"
                        >
                        </abc-input>
                    </abc-form-item>
                </div>
                <abc-form-item label="光度台阶">
                    <abc-select
                        v-model="item.focalLength.step"
                        :width="80"
                        :show-value="showValueStep(item.focalLength)"
                        :disabled="disabledStep(item.focalLength)"
                    >
                        <abc-option
                            v-for="it in steps"
                            :key="it.label"
                            :label="it.label"
                            :value="it.value"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
            </div>
            <template v-else>
                <div class="card-item">
                    <div class="left">
                        <abc-form-item
                            label="球镜"
                            :validate-event="validateWrapper"
                            :validate-params="{
                                showError: index === errorIndex,
                                fn: (callback) => validateSpherical(callback, item, 'start')
                            }"
                        >
                            <abc-input
                                v-model="item.spherical.start"
                                v-abc-focus-selected
                                :width="80"
                                type="money"
                                :config="inputConfig"
                                :disabled="disabled"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>

                        <div
                            style="display: inline-block; width: 15px; font-size: 14px; color: #7a8794; text-align: center;"
                        >
                            ~
                        </div>

                        <abc-form-item
                            label=""
                            :validate-event="validateWrapper"
                            :validate-params="{
                                showError: index === errorIndex,
                                fn: (callback) => validateSpherical(callback, item, 'end')
                            }"
                        >
                            <abc-input
                                v-model="item.spherical.end"
                                v-abc-focus-selected
                                :width="80"
                                type="money"
                                :config="inputConfig"
                                :disabled="disabled"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                    </div>
                    <abc-form-item label="光度台阶">
                        <abc-select
                            v-model="item.spherical.step"
                            :width="80"
                            :show-value="showValueStep(item.spherical)"
                            :disabled="disabledStep(item.spherical)"
                        >
                            <abc-option
                                v-for="it in steps"
                                :key="it.label"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </div>

                <div class="card-item">
                    <div class="left">
                        <abc-form-item
                            label="柱镜"
                            :validate-event="validateWrapper"
                            :validate-params="{
                                showError: index === errorIndex,
                                fn: (callback) => validateLenticular(callback, item, 'start')
                            }"
                        >
                            <abc-input
                                v-model="item.lenticular.start"
                                v-abc-focus-selected
                                :width="80"
                                type="money"
                                :config="inputConfig"
                                :disabled="disabled"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>

                        <div
                            style="display: inline-block; width: 15px; font-size: 14px; color: #7a8794; text-align: center;"
                        >
                            ~
                        </div>

                        <abc-form-item
                            label=""
                            :validate-event="validateWrapper"
                            :validate-params="{
                                showError: index === errorIndex,
                                fn: (callback) => validateLenticular(callback, item, 'end')
                            }"
                        >
                            <abc-input
                                v-model="item.lenticular.end"
                                v-abc-focus-selected
                                :width="80"
                                type="money"
                                :config="inputConfig"
                                :disabled="disabled"
                                @enter="enterEvent"
                            >
                            </abc-input>
                        </abc-form-item>
                    </div>
                    <abc-form-item label="光度台阶">
                        <abc-select
                            v-model="item.lenticular.step"
                            :width="80"
                            :show-value="showValueStep(item.lenticular)"
                            :disabled="disabledStep(item.lenticular)"
                        >
                            <abc-option
                                v-for="it in steps"
                                :key="it.label"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </div>

                <div class="limit">
                    <abc-form-item
                        label="近视联合 ≤"
                        :validate-event="(value, callback) => validateCombinedLuminosity(item, value, 'myopiaCombinedLuminosity', callback)"
                    >
                        <abc-input
                            v-model="item.myopiaCombinedLuminosity"
                            type="money"
                            :config="{
                                max: 100, formatLength: 2
                            }"
                            :width="80"
                        >
                        </abc-input>
                    </abc-form-item>
                    <abc-form-item
                        label="远视联合 ≤"
                        :validate-event="(value, callback) => validateCombinedLuminosity(item, value, 'hyperopiaCombinedLuminosity', callback)"
                    >
                        <abc-input
                            v-model="item.hyperopiaCombinedLuminosity"
                            type="money"
                            :config="{
                                max: 100, formatLength: 2
                            }"
                            :width="80"
                        >
                        </abc-input>
                    </abc-form-item>
                </div>
            </template>
        </div>
    </abc-form>
</template>

<script>
    import SAT from 'sat';
    import {
        validateSpherical,
        validateLenticular,
        validateFocalLength,
        validateCombinedLuminosity,
        createGroupName,
        findOverlayIndex,
    } from '../utils';
    import EnterEvent from 'views/common/enter-event';
    // import TrashButton from 'views/layout/prescription/common/trash-button.vue';
    import { isNull } from 'utils/lodash';

    export default {
        name: 'LuminosityGroup',
        // components: {
        //     TrashButton,
        // },
        mixins: [EnterEvent],
        props: {
            disabled: {
                type: Boolean,
                default: false,
            },
            // 当前光度范围组
            currentItem: {
                type: Object,
                default: null,
            },
            luminosityGroups: {
                type: Array,
                default: () => [],
            },
            spuName: String,
            loading: Boolean,
            isPwr: Boolean,//是否是后顶焦度
        },
        data() {
            return {
                popoverVisible: false,
                popoverItem: null,// 当前弹出的联合光度限制
                steps: [
                    {
                        label: '0.25', value: 0.25,
                    },
                    {
                        label: '0.50', value: 0.5,
                    },
                    {
                        label: '1.00', value: 1,
                    },
                ],
                overlayIndex: -1,// 被重叠的索引
                errorIndex: -1,// 要错误提示的索引
            };
        },
        computed: {
            inputConfig() {
                return {
                    max: 100, formatLength: 2, supportNegative: true, supportZero: true,
                };
            },
        },
        watch: {
            luminosityGroups: {
                handler(groups) {
                    // 合法数据才进行比较
                    const arr = groups.filter((item) => {
                        let passCount = 0;
                        if (this.isPwr) {
                            validateFocalLength((val) => {
                                if (val.validate) passCount++;
                            }, item, 'start');
                            validateFocalLength((val) => {
                                if (val.validate) passCount++;
                            }, item, 'end');

                            return passCount === 2;
                        }
                        validateSpherical((val) => {
                            if (val.validate) passCount++;
                        }, item, 'start');
                        validateSpherical((val) => {
                            if (val.validate) passCount++;
                        }, item, 'end');
                        validateLenticular((val) => {
                            // if (val.validate) passCount++;
                            if (val.validate && !isNull(item?.lenticular?.start)) passCount++;
                        }, item, 'start');
                        validateLenticular((val) => {
                            // if (val.validate) passCount++;
                            if (val.validate && !isNull(item?.lenticular?.end)) passCount++;
                        }, item, 'end');

                        return passCount === 4;
                    });

                    // 所有数据都填了才进行一次全部校验
                    if (arr.length === groups.length) {
                        console.log('all validate');
                        this.validate();
                    }
                },
                deep: true,
            },
        },
        created() {
            const V = SAT.Vector;
            const P = SAT.Polygon;
            const R = SAT.Response;

            // A square
            const polygon1 = new P(new V(0,0), [
                new V(0,0), new V(40,0), new V(40,40), new V(0,40),
            ]);
            // A triangle
            const polygon2 = new P(new V(30,-10), [
                new V(0,0), new V(30, 0), new V(0, -30),
            ]);
            const response = new R();
            const collided = SAT.testPolygonPolygon(polygon1, polygon2, response);

            console.log(collided);
            console.log(response);
        },
        methods: {
            validateSpherical,
            validateLenticular,
            validateFocalLength,
            validateCombinedLuminosity,
            validateWrapper(_, callback, {
                showError, fn,
            }) {
                if (showError) {
                    return callback({
                        validate: false,
                        message: `光度不能与组${this.overlayIndex + 1}重叠`,
                    });
                }
                return fn(callback);
            },
            formatGroupTitle(item, index) {
                return createGroupName(item, index, this.isPwr);
            },
            handleSelect(item, index) {
                this.$emit('select', item, index);
            },
            clearSelect() {
                this.$emit('select', null, -1);
            },
            showValueStep({
                start, end, step,
            }) {
                if (isNull(start) && isNull(end)) {
                    return step;
                }
                return start === end ? ' ' : step.toFixed(2);
            },
            requiredStep({
                start, end,
            }) {
                return start !== end;
            },
            disabledStep({
                start, end,
            }) {
                if (this.disabled) return true;
                if (!isNull(start) && !isNull(end)) {
                    return start === end;
                }
                return false;
            },
            errorInfo(index) {
                if (index === this.errorIndex) {
                    return {
                        error: true,
                        message: `光度不能与组${index + 1}重叠`,
                    };
                }
                return {
                    error: false,
                    message: '',
                };
            },
            onDelete(item, index) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: `${this.spuName || ''}规格将全部删除，不可恢复，请谨慎操作`,
                    confirmText: '确认删除',
                    onConfirm: async () => {
                        this.$emit('delete', item, index);
                    },
                });
            },
            validate(callback) {
                const {
                    overlayIndex,
                    errorIndex,
                } = findOverlayIndex(this.luminosityGroups, this.isPwr);
                this.overlayIndex = overlayIndex;
                this.errorIndex = errorIndex;

                this.$nextTick(() => {
                    this.$refs.luminosityForm.validate(callback);
                });

            },

        },
    };
</script>

<style lang="scss" scoped>
.card {
    padding: 10px 12px;
    margin-bottom: 12px;
    background: #ffffff;
    border: 1px solid #e6eaee;
    border-radius: var(--abc-border-radius-small);

    &.active {
        border-color: #005ed9;
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .title {
            margin-right: 16px;
            font-size: 14px;
            font-weight: bold;
            line-height: 20px;
            color: #000000;
        }
    }

    .limit {
        display: flex;
        align-items: center;
        padding-top: 8px;
        margin-top: 12px;
        border-top: 1px dashed #e6eaee;

        ::v-deep .abc-form-item {
            margin: 0;

            .abc-form-item-content {
                margin: 0 12px;
            }
        }
    }

    .card-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .left {
            display: flex;
            align-items: center;
        }

        ::v-deep .abc-form-item {
            margin: 0;

            .abc-form-item-label {
                margin-right: 8px;
            }
        }
    }
}

.limit-form {
    .limit-form-item {
        display: flex;
        align-items: center;
        height: 32px;
        margin-bottom: 8px;

        ::v-deep .abc-form-item {
            margin: 0;
        }
    }
}
</style>
