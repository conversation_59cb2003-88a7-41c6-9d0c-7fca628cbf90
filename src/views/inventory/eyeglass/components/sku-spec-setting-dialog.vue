<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="title"
        append-to-body
        content-styles="padding:0;"
    >
        <div class="wrapper">
            <div class="left">
                <abc-tabs
                    v-model="currentTab"
                    size="middle"
                    :option="options"
                ></abc-tabs>
                <div class="scroll-content">
                    <abc-form v-if="tabValue === 'luminosity'" ref="luminosityForm" label-position="left">
                        <div v-for="(item, index) in currentSpecData[currentTab]" :key="item.id" class="card">
                            <div class="header">
                                <span class="title">
                                    {{ formatGroupTitle(item, index) }}
                                </span>
                                <abc-icon
                                    slot="prepend"
                                    icon="trash"
                                    color="#AAB4BF"
                                    @click="onDelete(item, index)"
                                ></abc-icon>
                            </div>
                            <div class="card-item">
                                <div class="left">
                                    <abc-form-item
                                        label="球镜"
                                        :validate-event="handleEvent"
                                        :validate-params="{
                                            item,
                                            type: 's',
                                            isPositiveNumber: true
                                        }"
                                    >
                                        <abc-input
                                            v-model="item.sphericMirror.positiveNumber"
                                            :width="80"
                                            type="money"
                                            :config="{
                                                formatLength: 2,max: 100, supportZero: true
                                            }"
                                            :input-custom-style="{ 'padding': '0 16px 0 24px' }"
                                        >
                                            <span slot="prepend">+</span>
                                        </abc-input>
                                    </abc-form-item>

                                    <div
                                        style="display: inline-block; width: 15px; font-size: 14px; color: #7a8794; text-align: center;"
                                    >
                                        ~
                                    </div>

                                    <abc-form-item
                                        label=""
                                        :validate-event="handleEvent"
                                        :validate-params="{
                                            item,
                                            type: 's',
                                            isPositiveNumber: false
                                        }"
                                    >
                                        <abc-input
                                            v-model="item.sphericMirror.negativeNumber"
                                            :width="80"
                                            type="money"
                                            :config="{
                                                formatLength: 2, max: 100, supportZero: true
                                            }"
                                            :input-custom-style="{ 'padding': '0 16px 0 24px' }"
                                        >
                                            <span slot="prepend">-</span>
                                        </abc-input>
                                    </abc-form-item>
                                </div>
                                <abc-form-item label="光度台阶" required>
                                    <abc-select
                                        v-model="item.sphericMirror.stepNumber"
                                        :width="80"
                                    >
                                        <abc-option
                                            v-for="it in steps"
                                            :key="it.label"
                                            :label="it.label"
                                            :value="it.value"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </div>
                            <div class="card-item">
                                <div class="left">
                                    <abc-form-item
                                        label="柱镜"
                                        :validate-event="handleEvent"
                                        :validate-params="{
                                            item,
                                            type: 'c',
                                            isPositiveNumber: true
                                        }"
                                    >
                                        <abc-input
                                            v-model="item.cylindricalMirror.positiveNumber"
                                            :width="80"
                                            type="money"
                                            :config="{
                                                formatLength: 2, max: 100, supportZero: true
                                            }"
                                            :input-custom-style="{ 'padding': '0 16px 0 24px' }"
                                        >
                                            <span slot="prepend">+</span>
                                        </abc-input>
                                    </abc-form-item>
                                    <div
                                        style="display: inline-block; width: 15px; font-size: 14px; color: #7a8794; text-align: center;"
                                    >
                                        ~
                                    </div>
                                    <abc-form-item
                                        label=""
                                        :validate-event="handleEvent"
                                        :validate-params="{
                                            item,
                                            type: 'c',
                                            isPositiveNumber: false
                                        }"
                                    >
                                        <abc-input
                                            v-model="item.cylindricalMirror.negativeNumber"
                                            :width="80"
                                            type="money"
                                            :config="{
                                                formatLength: 2, max: 100, supportZero: true
                                            }"
                                            :input-custom-style="{ 'padding': '0 16px 0 24px' }"
                                        >
                                            <span slot="prepend">-</span>
                                        </abc-input>
                                    </abc-form-item>
                                </div>
                                <abc-form-item label="光度台阶" required>
                                    <abc-select
                                        v-model="item.cylindricalMirror.stepNumber"
                                        :width="80"
                                    >
                                        <abc-option
                                            v-for="it in steps"
                                            :key="it.label"
                                            :label="it.label"
                                            :value="it.value"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </div>

                            <div
                                v-if="item.uniteLuminosity && (item.uniteLuminosity.nearChecked || item.uniteLuminosity.farChecked)"
                                class="limit"
                            >
                                <div class="left">
                                    <p v-if="item.uniteLuminosity.nearChecked">
                                        <span>近视联合</span>
                                        <span>≤{{ item.uniteLuminosity.near }}</span>
                                    </p>
                                    <p v-if="item.uniteLuminosity.farChecked">
                                        <span>远视联合</span>
                                        <span>≤{{ item.uniteLuminosity.far }}</span>
                                    </p>
                                </div>

                                <abc-icon
                                    slot="prepend"
                                    icon="Edit_Profile"
                                    color="#AAB4BF"
                                    @click="onEditUniteLuminosityLimit(item)"
                                ></abc-icon>
                            </div>
                            <div v-else class="text-btn" @click="onAddUniteLuminosityLimit(item)">
                                添加联合光度限制
                            </div>
                        </div>
                    </abc-form>
                    <template v-if="tabValue === 'color'">
                        <div class="color-wrapper">
                            <div
                                v-for="(color, index) in currentSpecData[currentTab]"
                                :key="color + index"
                                contenteditable="true"
                                class="color-tag"
                            >
                                <span v-abc-title.ellipsis="color"></span>
                                <abc-icon
                                    class="delete-icon"
                                    icon="delete_file"
                                    color="#D9DBE3"
                                    @click="handleColorDelete(index)"
                                ></abc-icon>
                            </div>

                            <div class="color-tag add">
                                +
                            </div>
                        </div>
                    </template>
                </div>

                <abc-button v-if="tabValue === 'luminosity'" type="success" @click="onAdd">
                    加一组
                </abc-button>
            </div>
            <div class="right">
                <!--<photometry-vue :photometry-data="currentSpecData['luminosity']"></photometry-vue>-->
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <span style="margin-right: auto; font-size: 14px; color: #7a8794;">{{ footerLeftText }}</span>
            <abc-button type="primary" @click="onConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>

        <!--联合光度限制弹窗-->
        <abc-dialog
            v-if="uniteLuminosityDialogShow"
            v-model="uniteLuminosityDialogShow"
            :show-close="false"
            content-styles="padding:12px;"
        >
            <abc-form
                ref="limitForm"
                class="limit-form"
            >
                <div class="limit-form-item">
                    <abc-checkbox v-model="dialogData.nearChecked">
                        近视联合
                    </abc-checkbox>
                    <abc-form-item>
                        <abc-input
                            v-model="dialogData.near"
                            type="number"
                            :config="{ max: 100 }"
                            :width="100"
                        >
                            <span slot="prepend">≤</span>
                        </abc-input>
                    </abc-form-item>
                </div>

                <div class="limit-form-item">
                    <abc-checkbox v-model="dialogData.farChecked">
                        远视联合
                    </abc-checkbox>
                    <abc-form-item>
                        <abc-input
                            v-model="dialogData.far"
                            type="number"
                            :config="{ max: 100 }"
                            :width="100"
                        >
                            <span slot="prepend">≤</span>
                        </abc-input>
                    </abc-form-item>
                </div>
            </abc-form>

            <div style="display: flex; justify-content: flex-end; margin-top: 12px;">
                <abc-button type="primary" @click="onUniteLuminosityLimitConfirm">
                    确定
                </abc-button>
                <abc-button type="blank" @click="uniteLuminosityDialogShow = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </abc-dialog>
</template>

<script>
    import clone from 'utils/clone';
    import { formatMoney } from '@/utils';

    export default {
        name: 'SkuSpecSettingDialog',
        components: {
        },
        props: {
            value: Boolean,
            title: {
                type: String,
                default: 'SKU规格设置',
            },
            tabValue: {
                type: String,
                default: 'luminosity',
            },
            options: {
                type: Array,
                default: () => [{
                    label: '光度',
                    value: 'luminosity',
                }],
            },
            specData: {
                type: Object,
                required: true,
            },
            isUpdate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                showDialog: this.value,
                currentTab: this.tabValue,
                uniteLuminosityDialogShow: false,
                dialogData: {
                    far: '',
                    near: '',
                    farChecked: false,
                    nearChecked: false,
                    currentItem: null,
                },
                currentSpecData: {},
                steps: [
                    {
                        label: '0.25', value: 0.25,
                    },
                    {
                        label: '0.50', value: 0.5,
                    },
                ],
            };
        },
        computed: {
            footerLeftText() {
                let luminosityCount = 0;
                const colorCount = this.currentSpecData.color?.length ?? 0;
                this.currentSpecData.luminosity?.forEach((item) => {
                    const {
                        sphericMirror, cylindricalMirror, invalidated,
                    } = item;
                    if (!invalidated) {
                        const sCount = (sphericMirror.positiveNumber + sphericMirror.negativeNumber) / sphericMirror.stepNumber;
                        const cCount = (cylindricalMirror.positiveNumber + cylindricalMirror.negativeNumber) / cylindricalMirror.stepNumber;
                        console.log(sCount, cCount);
                        luminosityCount += sCount * cCount;
                    }
                });

                return `光度${luminosityCount}种，颜色${colorCount}种，共${luminosityCount + colorCount}种规格`;
            },
        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        created() {
            this.currentSpecData = clone(this.specData);
        },
        methods: {
            formatGroupTitle(item, index) {
                const {
                    sphericMirror, cylindricalMirror,
                } = item;
                return `组${index + 1}
                S:+${formatMoney(sphericMirror.positiveNumber)}~-${formatMoney(sphericMirror.negativeNumber)}/
                C:+${formatMoney(cylindricalMirror.positiveNumber)}~-${formatMoney(cylindricalMirror.negativeNumber)}`;
            },
            handleEvent(_, callback, {
                item, type, isPositiveNumber,
            }) {
                const {
                    sphericMirror, cylindricalMirror, uniteLuminosity,
                } = item;

                if (type === 's') {
                    if (isPositiveNumber && sphericMirror.positiveNumber > 20) {
                        item.invalidated = true;
                        return callback({
                            validate: false, message: '球镜有效范围+20.00 ~ -20.00',
                        });
                    }

                    if (!isPositiveNumber && sphericMirror.negativeNumber > 20) {
                        item.invalidated = true;
                        return callback({
                            validate: false, message: '球镜有效范围+20.00 ~ -20.00',
                        });
                    }

                    if (!isPositiveNumber && uniteLuminosity?.nearChecked && sphericMirror.negativeNumber > uniteLuminosity.near) {
                        item.invalidated = true;
                        return callback({
                            validate: false, message: '球镜范围不能超出联合光度',
                        });
                    }

                    if (isPositiveNumber && uniteLuminosity?.farChecked && sphericMirror.positiveNumber > uniteLuminosity.far) {
                        item.invalidated = true;
                        return callback({
                            validate: false, message: '球镜范围不能超出联合光度',
                        });
                    }
                }

                if (type === 'c') {
                    if (isPositiveNumber && cylindricalMirror.positiveNumber > 0) {
                        item.invalidated = true;
                        return callback({
                            validate: false, message: '柱镜有效范围0 ~ -6.00',
                        });
                    }

                    if (!isPositiveNumber && cylindricalMirror.negativeNumber > 6) {
                        item.invalidated = true;
                        return callback({
                            validate: false, message: '柱镜有效范围0 ~ -6.00',
                        });
                    }

                    if (!isPositiveNumber && uniteLuminosity?.nearChecked && cylindricalMirror.negativeNumber > uniteLuminosity.near) {
                        item.invalidated = true;
                        return callback({
                            validate: false, message: '柱镜范围不能超出联合光度',
                        });
                    }

                    if (isPositiveNumber && uniteLuminosity?.farChecked && cylindricalMirror.positiveNumber > uniteLuminosity.far) {
                        item.invalidated = true;
                        return callback({
                            validate: false, message: '柱镜范围不能超出联合光度',
                        });
                    }

                }


                item.invalidated = false;
                callback({ validate: true });

            },
            onAdd() {
                this.currentSpecData[this.currentTab].push({
                    id: Math.random().toString(32),
                    sphericMirror: { // 球镜
                        positiveNumber: '',// 正数
                        negativeNumber: '',// 负数
                        stepNumber: '',// 光度台阶
                    },
                    cylindricalMirror: { // 柱镜
                        positiveNumber: '',// 正数
                        negativeNumber: '',// 负数
                        stepNumber: '',// 光度台阶
                    },
                    invalidated: true,// 默认数据是不合法的
                });
            },
            onDelete(item, index) {
                // 编辑规格会影响库存，会去查询
                if (this.isUpdate) {
                    console.log(item);
                } else {
                    this.currentSpecData[this.currentTab].splice(index, 1);
                }
            },
            onAddUniteLuminosityLimit(item) {
                this.uniteLuminosityDialogShow = true;

                this.dialogData = {
                    far: '',// 远视联合
                    near: '',// 近视联合
                    farChecked: false,
                    nearChecked: false,
                    currentItem: item,// 当前操作的 item
                };
            },
            onEditUniteLuminosityLimit(item) {
                this.uniteLuminosityDialogShow = true;

                this.dialogData = {
                    ...item.uniteLuminosity,
                    currentItem: item,
                };
            },
            onUniteLuminosityLimitConfirm() {
                const {
                    far, near, farChecked, nearChecked, currentItem,
                } = this.dialogData;

                if (currentItem) {
                    this.$set(currentItem, 'uniteLuminosity', {
                        far,// 远视联合
                        near,// 近视联合
                        farChecked,
                        nearChecked,
                    });
                }

                this.uniteLuminosityDialogShow = false;
            },
            onConfirm() {
                this.$emit('changeSpecData', this.currentSpecData);
                this.showDialog = false;
            },

            handleColorDelete(index) {
                this.currentSpecData[this.currentTab].splice(index, 1);
            },
        },
    };
</script>

<style lang="scss" scoped>
.wrapper {
    display: flex;
    padding: 0 24px 24px;

    .left {
        width: 404px;

        .scroll-content {
            height: 348px;
            padding-top: 12px;
            padding-right: 2px;
            padding-left: 2px;
            margin-right: -2px;
            margin-bottom: 12px;
            margin-left: -2px;
            overflow: auto;
            overflow: overlay;

            .card {
                padding: 10px 12px;
                margin-bottom: 12px;
                background: #ffffff;
                border: 1px solid #e6eaee;
                border-radius: var(--abc-border-radius-small);

                .header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 12px;

                    .title {
                        font-size: 14px;
                        font-weight: bold;
                        line-height: 20px;
                        color: #000000;
                    }
                }

                .text-btn {
                    display: inline-block;
                    margin-top: 12px;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    color: #005ed9;
                    cursor: pointer;
                }

                .limit {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding-top: 8px;
                    margin-top: 12px;
                    border-top: 1px dashed #e6eaee;

                    .left {
                        p {
                            font-size: 14px;
                            font-weight: 400;
                            color: #000000;

                            span:first-child {
                                color: #7a8794;
                            }
                        }
                    }
                }

                .card-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 8px;

                    .left {
                        display: flex;
                        align-items: center;
                    }

                    ::v-deep .abc-form-item {
                        margin: 0;

                        .abc-form-item-label {
                            margin-right: 8px;
                        }
                    }
                }
            }

            .color-wrapper {
                display: flex;
                flex-wrap: wrap;
                gap: 12px;

                .color-tag {
                    position: relative;
                    width: 92px;
                    height: 32px;
                    font-size: 14px;
                    line-height: 32px;
                    color: #000000;
                    text-align: center;
                    border: 1px solid $P1;
                    border-radius: 0;
                    outline: none;

                    .delete-icon {
                        position: absolute;
                        top: 0;
                        right: 0;
                        background: #ffffff;
                        transform: translate(50%, -50%);
                    }

                    &:hover {
                        border-color: #0270c9;
                    }

                    &:focus {
                        z-index: 2 !important;
                        border-color: #0270c9;
                        box-shadow: 0 0 0 2px #c3e0fe !important;
                        transition: border 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
                    }

                    &.add {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 20px;
                        color: #aab4bf;

                        &:hover {
                            color: #7a8794;
                            border-color: #aab4bf;
                        }
                    }
                }
            }
        }
    }

    .right {
        width: 124px;
        height: 320px;
        margin: 63px 27px 0 46px;
    }
}

.limit-form {
    .limit-form-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        ::v-deep .abc-form-item {
            margin: 0;
        }
    }
}
</style>
