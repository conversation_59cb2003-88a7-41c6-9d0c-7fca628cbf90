<template>
    <div v-if="keys" class="spu-info-wrapper">
        <template v-for="key in keys">
            <abc-form-item
                v-if="key.prop === 'name'"
                :key="key.prop"
                :label="key.label"
                :required="!disabled"
            >
                <abc-input
                    v-model="formData.name"
                    :width="210"
                    :disabled="disabled"
                    @enter="enterEvent"
                >
                </abc-input>
            </abc-form-item>

            <abc-form-item
                v-if="key.prop === 'typeId'"
                :key="key.prop"
                :label="key.label"
                class="goods-type-wrapper"
            >
                <abc-select
                    v-model="formData.typeId"
                    class="goods-subtype"
                    :width="104"
                    disabled
                    @change="formData.customTypeId = ''"
                    @enter="enterEvent"
                >
                    <abc-option
                        v-for="it in primaryClassification"
                        :key="it.id"
                        :label="it.name"
                        :value="it.id"
                    ></abc-option>
                </abc-select>
                <secondary-classification-select
                    v-model="formData.customTypeId"
                    :type-id="formData.typeId"
                    :disabled="disabled"
                    clearable
                ></secondary-classification-select>
            </abc-form-item>

            <abc-form-item v-if="key.prop === 'brandName'" :key="key.prop" :label="key.label">
                <abc-select
                    ref="customType10"
                    v-model="formData.brandName"
                    :show-value="formData.brandName"
                    :width="210"
                    :disabled="disabled"
                    class="goods-dosageform"
                    custom-class="goods-classification-select"
                    :show-empty="false"
                    @enter="enterEvent"
                    @change="changeBrandNameValue"
                >
                    <abc-option
                        v-for="it in brandNameCustomTypes"
                        :key="`${it.id}${it.name}`"
                        :label="it.name"
                        :value="it.name"
                    ></abc-option>
                    <div
                        class="setting"
                        :class="{
                            'abc-tipsy abc-tipsy--n': disabled, 'no-settings': !brandNameCustomTypes || !brandNameCustomTypes.length
                        }"
                        data-tipsy="仅连锁总部可以进行设置"
                    >
                        <div v-if="!brandNameCustomTypes || !brandNameCustomTypes.length" class="no-settings-tips">
                            暂无数据
                        </div>
                        <span class="icon" @click.stop="openCustomTypeDialog(10)">
                            <i class="iconfont cis-icon-set"></i>设置
                        </span>
                    </div>
                </abc-select>
            </abc-form-item>

            <abc-form-item v-if="key.prop === 'manufacturerFull'" :key="key.prop" :label="key.label">
                <abc-input
                    v-model="formData.manufacturerFull"
                    :width="210"
                    :disabled="disabled"
                    @enter="enterEvent"
                >
                </abc-input>
            </abc-form-item>

            <abc-form-item v-if="key.prop === 'material'" :key="key.prop" :label="key.label">
                <abc-select
                    ref="customType20"
                    v-model="formData.material"
                    :show-value="formData.material"
                    :width="210"
                    :disabled="disabled"
                    class="goods-dosageform"
                    custom-class="goods-classification-select"
                    :show-empty="false"
                    @enter="enterEvent"
                    @change="changeMaterialValue"
                >
                    <abc-option
                        v-for="it in materialCustomTypes"
                        :key="`${it.id}${it.name}`"
                        :label="it.name"
                        :value="it.name"
                    ></abc-option>
                    <div
                        class="setting"
                        :class="{
                            'abc-tipsy abc-tipsy--n': disabled, 'no-settings': !materialCustomTypes || !materialCustomTypes.length
                        }"
                        data-tipsy="仅连锁总部可以进行设置"
                    >
                        <div v-if="!materialCustomTypes || !materialCustomTypes.length" class="no-settings-tips">
                            暂无数据
                        </div>
                        <span class="icon" @click.stop="openCustomTypeDialog(20)">
                            <i class="iconfont cis-icon-set"></i>设置
                        </span>
                    </div>
                </abc-select>
            </abc-form-item>

            <abc-form-item
                v-if="key.prop === 'refractiveIndex'"
                :key="key.prop"
                :label="key.label"
                :validate-event="validateRefractiveIndex"
            >
                <abc-autocomplete
                    v-model="formData.refractiveIndex"
                    inner-width="210px"
                    :width="210"
                    :delay-time="0"
                    :async-fetch="true"
                    :fetch-suggestions="fetchOptions"
                    :max-length="5"
                    focus-show
                    type="money"
                    :auto-focus-first="false"
                    @enterEvent="handleSelect"
                    @change="handleRefractiveIndexChange"
                >
                    <template #suggestions="props">
                        <dt
                            class="suggestions-item"
                            :class="{ selected: props.index == props.currentIndex }"
                            @click="handleSelect(props.suggestion)"
                        >
                            <div>{{ props.suggestion.label }}</div>
                        </dt>
                    </template>
                </abc-autocomplete>
            </abc-form-item>

            <abc-form-item v-if="key.prop === 'wearCycle'" :key="key.prop" :label="key.label">
                <abc-select
                    v-model="formData.wearCycle"
                    :width="210"
                    :disabled="disabled"
                    @enter="enterEvent"
                >
                    <abc-option
                        v-for="it in options.wearCycle"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                    ></abc-option>
                </abc-select>
            </abc-form-item>

            <abc-form-item
                v-if="key.prop === 'addLightLeft'"
                :key="key.prop"
                :label="key.label"
                :validate-event="(_, callback) => validateAddLight(_, callback)"
            >
                <abc-input
                    v-model="formData.addLightLeft"
                    :width="72"
                    :disabled="disabled"
                    type="money"
                    :config="{
                        max: 100, formatLength: 2, supportZero: true
                    }"
                    :input-custom-style="{
                        textAlign: 'left',
                        paddingLeft: '18px'
                    }"
                    @enter="enterEvent"
                >
                    <span v-if="formData.addLightLeft" slot="prepend" style="margin-bottom: 2px;">+</span>
                    <label slot="append" class="append">D</label>
                </abc-input>
                <div style="display: inline-block; width: 16px; font-size: 14px; color: #7a8794; text-align: center;">
                    -
                </div>
                <abc-input
                    v-model="formData.addLightRight"
                    :width="72"
                    :disabled="disabled"
                    type="money"
                    :config="{
                        max: 100, formatLength: 2, supportZero: true
                    }"
                    :input-custom-style="{
                        textAlign: 'left',
                        paddingLeft: '18px'
                    }"
                    @enter="enterEvent"
                >
                    <span v-if="formData.addLightRight" slot="prepend" style="margin-bottom: 2px;">+</span>
                    <label slot="append" class="append">D</label>
                </abc-input>
            </abc-form-item>

            <abc-form-item v-if="key.prop === 'registrationNumber'" :key="key.prop" :label="key.label">
                <abc-input
                    v-model="formData.registrationNumber"
                    :width="210"
                    :disabled="disabled"
                    @enter="enterEvent"
                >
                </abc-input>
            </abc-form-item>

            <abc-form-item
                v-if="key.prop === 'packageUnit'"
                :key="key.prop"
                :label="key.label"
                :required="!disabled"
            >
                <select-usage
                    v-model="formData.packageUnit"
                    type="materialUnit"
                    class="goods-unit"
                    :disabled="disabled"
                    placement="bottom-start"
                    @enter="enterEvent"
                >
                </select-usage>
            </abc-form-item>

            <abc-form-item
                v-if="key.prop === 'pieceUnit'"
                :key="key.prop"
                :label="key.label"
                :required="!disabled"
            >
                <select-usage
                    v-model="formData.pieceUnit"
                    type="materialUnit"
                    class="goods-unit"
                    :disabled="disabled"
                    placement="bottom-start"
                    @enter="enterEvent"
                >
                </select-usage>
            </abc-form-item>

            <div v-if="key.prop === 'inTaxRat'" :key="key.prop" class="goods-taxrat">
                <div class="top">
                    <abc-checkbox
                        v-model="formData.defaultInOutTax"
                        :disabled="disabled"
                        type="number"
                        class="goods-taxrat-chechbox"
                    >
                        默认进/销项税率
                    </abc-checkbox>
                    <abc-tooltip-info placement="top">
                        <div v-if="isAdmin">
                            请在「管理-定价和税率」设置
                        </div>
                        <div v-else>
                            请在「总部-管理-定价和税率」设置
                        </div>
                    </abc-tooltip-info>
                    <span v-if="goodsId" class="view-history-taxrat" @click="showHistoryDialog = true">
                        历史税率
                    </span>
                </div>
                <abc-form-item>
                    <abc-input
                        v-model="formData.inTaxRat"
                        :width="72"
                        :config="{ max: 100 }"
                        :disabled="disabledTax"
                        @enter="enterEvent"
                    >
                        <label slot="append" class="append">%</label>
                    </abc-input>
                    <div
                        style="display: inline-block; width: 16px; font-size: 14px; color: #7a8794; text-align: center;"
                    >
                        -
                    </div>
                    <abc-input
                        v-model="formData.outTaxRat"
                        :width="72"
                        :config="{ max: 100 }"
                        :disabled="disabledTax"
                        @enter="enterEvent"
                    >
                        <label slot="append" class="append">%</label>
                    </abc-input>
                </abc-form-item>
            </div>

            <abc-form-item
                v-if="key.prop === 'spuTagList'"
                :key="key.prop"
                :label="key.label"
            >
                <goods-tag-select
                    v-model="goodsTagList"
                    :clearable="false"
                    @enter="enterEvent"
                ></goods-tag-select>
            </abc-form-item>

            <abc-form-item v-if="key.prop === 'processManufacturer'" :key="key.prop" :label="key.label">
                <abc-input
                    v-model="formData.processManufacturer"
                    :width="210"
                    :disabled="disabled"
                    @enter="enterEvent"
                >
                </abc-input>
            </abc-form-item>

            <div v-if="featureFeeCompose && key.prop === 'feeType'" :key="key.prop">
                <template v-if="disabled">
                    <abc-form-item :label="key.label">
                        <abc-select
                            v-model="formData.feeTypeId"
                            :width="210"
                            :disabled="disabled"
                        >
                            <abc-option
                                v-for="feeType in feeTypesList"
                                :key="feeType.feeTypeId"
                                :value="feeType.feeTypeId"
                                :label="feeType.name"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </template>
                <template v-else>
                    <abc-form-item required :label="key.label">
                        <abc-select
                            v-model="formData.feeTypeId"
                            :width="210"
                            :disabled="disabled"
                        >
                            <abc-option
                                v-for="feeType in feeTypesList"
                                :key="feeType.feeTypeId"
                                :value="feeType.feeTypeId"
                                :label="feeType.name"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </template>
            </div>
        </template>
        <history-taxrate-dialog
            v-if="showHistoryDialog"
            :dialog-visible.sync="showHistoryDialog"
            :type-id="formData.typeId"
            :goods-id="goodsId"
        ></history-taxrate-dialog>

        <custom-type-dialog
            v-if="showCustomTypeDialog"
            v-model="showCustomTypeDialog"
            :title="customTypeTitle"
            :type="type"
            :sub-type="subType"
            :unit-type="customType"
            :options="customTypeOptions"
            @change="changeCustomTypes"
        ></custom-type-dialog>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    // import { GoodsTypeEnum } from '@abc/constants';
    import GoodsAPI from 'api/goods';
    import TreatmentAPI from 'api/treatment';
    import EnterEvent from 'views/common/enter-event';
    import CustomTypeDialog from 'views/inventory/eyeglass/components/customTypeDialog.vue';
    import SelectUsage from 'views/layout/select-group/index.vue';
    import HistoryTaxrateDialog from 'views/settings/price-taxrat/components/history-taxrate-dialog.vue';

    const SecondaryClassificationSelect = () => import('views/inventory/goods/components/secondary-classification/secondary-select.vue');

    import {
        validateAddLight, validateRefractiveIndex,
    } from 'views/inventory/eyeglass/utils';
    import { GoodsTypeEnum } from '@abc/constants';
    import { moneyDigit } from '@/utils';
    import GoodsTagSelect from 'views/inventory/goods/archives/components/goods-tag-select.vue';

    export default {
        name: 'SpuInfo',
        components: {
            GoodsTagSelect,
            CustomTypeDialog,
            SelectUsage,
            HistoryTaxrateDialog,
            SecondaryClassificationSelect,
        },
        mixins: [EnterEvent],
        props: {
            formData: {
                type: Object,
                required: true,
            },
            spuTagList: {
                type: Array,
                default: () => [],
            },
            // 控制字段显示
            keys: {
                type: Array,
                require: true,
            },
            goodsId: {
                type: String,
                default: '',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            type: {
                type: [Number, String],
                default: '',
            },
            subType: {
                type: [Number, String],
                default: '',
            },
            feeTypesList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                showHistoryDialog: false,
                showCustomTypeDialog: false,
                customType: '',// 10 品牌 20 材质
                brandNameCustomTypes: [], // 品牌的数据
                materialCustomTypes: [], // 材质的数据
                brandNameValue: '',// 品牌的值
                materialValue: '',// 材质的值
                options: {
                    refractive: [
                        {
                            label: '1.49', value: 1.49,
                        },
                        {
                            label: '1.50', value: 1.50,
                        },
                        {
                            label: '1.53', value: 1.53,
                        },
                        {
                            label: '1.54', value: 1.54,
                        },
                        {
                            label: '1.55', value: 1.55,
                        },
                        {
                            label: '1.56', value: 1.56,
                        },
                        {
                            label: '1.59', value: 1.59,
                        },
                        {
                            label: '1.60', value: 1.60,
                        },
                        {
                            label: '1.61', value: 1.61,
                        },
                        {
                            label: '1.67', value: 1.67,
                        },
                        {
                            label: '1.70', value: 1.70,
                        },
                        {
                            label: '1.71', value: 1.71,
                        },
                        {
                            label: '1.73', value: 1.73,
                        },
                        {
                            label: '1.74', value: 1.74,
                        },
                        {
                            label: '1.76', value: 1.76,
                        },
                        {
                            label: '1.80', value: 1.80,
                        },
                        {
                            label: '1.90', value: 1.90,
                        },
                    ],
                    wearCycle: [
                        {
                            label: '日抛', value: '日抛',
                        },
                        {
                            label: '双周抛', value: '双周抛',
                        },
                        {
                            label: '月抛', value: '月抛',
                        },
                        {
                            label: '季抛', value: '季抛',
                        },
                        {
                            label: '半年抛', value: '半年抛',
                        },
                        {
                            label: '年抛', value: '年抛',
                        },
                    ],
                },
            };
        },
        computed: {
            ...mapGetters(['goodsPrimaryClassification', 'isAdmin', 'isChainAdmin', 'showSubSetPrice']),
            ...mapGetters('viewDistribute',[
                'featureFeeCompose',
            ]),
            primaryClassification() {
                return this.goodsPrimaryClassification.filter((item) => {
                    return item.goodsType === GoodsTypeEnum.EYEGLASSES;
                }) || [];
            },
            disabledTax() {
                return this.disabled || Boolean(this.formData.defaultInOutTax);
            },
            customTypeTitle() {
                return this.customType === 10 ? '常用品牌' : '常用材质';
            },
            customTypeOptions() {
                return this.customType === 10 ? this.brandNameCustomTypes : this.materialCustomTypes;
            },
            needFetch() {
                return `${this.type}${this.subType}`;
            },
            goodsTagList: {
                get() {
                    return this.spuTagList;
                },
                set(v) {
                    this.$emit('update', v);
                },
            },
        },
        watch: {
            needFetch: {
                async handler() {
                    if (this.type && this.subType) {
                        const {
                            brandName, material,
                        } = this.formData;
                        this.brandNameCustomTypes = await this.getCustomType(10);
                        this.materialCustomTypes = await this.getCustomType(20);
                        this.changeBrandNameValue(brandName);
                        this.changeMaterialValue(material);
                    }
                },
                immediate: true,
            },
        },
        methods: {
            validateRefractiveIndex,
            validateAddLight,
            fetchOptions(key, callback) {
                return callback(this.options.refractive);
            },
            handleSelect(data) {
                this.formData.refractiveIndex = moneyDigit(data.value, 3);
            },
            handleRefractiveIndexChange(value) {
                this.formData.refractiveIndex = moneyDigit(value, 3);
            },
            /**
             * @desc  查询商品名
             * <AUTHOR>
             * @date 2018/11/01 08:53:22
             */
            async searchByTreadName(keyword, next) {
                try {
                    const fetchParams = {
                        client: 'medicine-tradename',
                        key_word: keyword,
                    };
                    const { data } = await GoodsAPI.searchProducts(fetchParams);
                    const hits = (data && data.hits) || [];
                    const treadNameList = hits.filter((item) => {
                        return item.tradeName;
                    });
                    next(treadNameList);
                } catch (err) {
                    throw new Error(err);
                }
            },
            /**
             * @desc  查询厂家信息
             * <AUTHOR>
             * @date 2018/11/01 09:56:34
             */
            async searchByManufacturer(keyword, next) {
                try {
                    const fetchParams = {
                        client: 'medicine-manufacturer',
                        key_word: keyword,
                        tradeName: this.formData.name,
                    /* eslint-enable */
                    };
                    const { data } = await GoodsAPI.searchProducts(fetchParams);
                    let result = [];
                    result = (data && data.hits) || [];
                    next(result);
                } catch (e) {
                    next([]);
                }
            },
            handleSelectTreadName(suggestion) {
                this.formData.name = suggestion.tradeName;
            },
            handleSelectManufacturer(suggestion) {
                this.formData.manufacturer = suggestion.medicine_manufacturer;
            },

            // 获取自定义类型
            async getCustomType(unitType) {
                const {
                    data: {
                        sysUnitList = [], customUnitList = [],
                    },
                } = await TreatmentAPI.fetchCustomUnit(this.type, {
                    subType: this.subType,
                    unitType,// 10 品牌 20 材质
                });

                return [
                    ...(customUnitList || []),
                    ...(sysUnitList?.map((e) => ({
                        ...e, disabled: true,
                    })) || []),
                ];
            },
            // 打开自定义弹窗
            openCustomTypeDialog(type) {
                this.customType = type;
                if (this.$refs[`customType${type}`][0]) {
                    this.$refs[`customType${type}`][0].showPopper = false;
                }
                this.showCustomTypeDialog = true;
            },
            // item是新增、修改、删除的数据
            changeCustomTypes(options) {
                if (this.customType === 10) {
                    this.brandNameCustomTypes = options;
                    const index = options.findIndex((item) => {
                        return item.id === this.brandNameValue;
                    });
                    if (index === -1) {
                        this.formData.brandName = '';
                    } else {
                        this.formData.brandName = options[index].name;
                    }
                } else {
                    this.materialCustomTypes = options;
                    const index = options.findIndex((item) => {
                        return item.id === this.materialValue;
                    });
                    if (index === -1) {
                        this.formData.material = '';
                    } else {
                        this.formData.material = options[index].name;
                    }
                }
            },
            changeBrandNameValue(name) {
                this.brandNameValue = this.brandNameCustomTypes.find((b) => b.name === name)?.id;
            },
            changeMaterialValue(name) {
                this.materialValue = this.materialCustomTypes.find((b) => b.name === name)?.id;
            },
        },
    };
</script>

<style lang="scss" scoped>
.spu-info-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;

    ::v-deep .abc-form-item {
        margin: 0;
    }

    .goods-type-wrapper {
        ::v-deep .abc-form-item-content {
            display: flex;
            align-items: center;
        }

        .goods-subtype {
            ::v-deep .abc-input__inner {
                border-right: none;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
        }

        ::v-deep .goods-dosageform .abc-input__inner {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    .goods-unit {
        ::v-deep .abc-input__inner {
            width: 210px;
            height: 32px;
            padding: 3px 8px;
        }
    }

    .goods-taxrat {
        display: flex;
        flex-direction: column;

        .top {
            display: flex;
            align-items: center;
            height: 16px;
            margin-bottom: 6px;
            font-size: 14px;
            line-height: 16px;

            .view-history-taxrat {
                margin-left: auto;
                font-size: 14px;
                color: #005ed9;
                cursor: pointer;
            }
        }

        ::v-deep .goods-taxrat-chechbox {
            margin-right: 2px;
            color: #7a8794;
        }
    }
}
</style>
