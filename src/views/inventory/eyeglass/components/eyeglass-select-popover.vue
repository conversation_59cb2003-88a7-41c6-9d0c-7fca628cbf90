<template>
    <abc-popover
        ref="popover"
        trigger="click"
        placement="bottom-start"
        theme="white"
        :popper-style="{
            padding: 0
        }"
    >
        <abc-button slot="reference" :type="filterParamsText ? 'blank' : 'ghost'">
            <abc-icon
                icon="filtrate"
                :size="14"
            ></abc-icon>
            <span
                v-abc-title.ellipsis="filterParamsText || '筛选'"
                :style="{
                    display: 'inline-block',
                    color: filterParamsText ? '#000' : '#7a8794',
                    maxWidth: '200px',
                }"
            >
            </span>
        </abc-button>

        <div>
            <abc-form ref="formRef" label-position="left" style="display: flex; flex-wrap: wrap; gap: 8px 24px; max-width: 564px; padding: 12px;">
                <template v-for="key in filterKeys">
                    <abc-form-item
                        v-if="key === 'brandName'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <slot name="brandName" :form-data="curParams">
                            <abc-input v-model="curParams.brandName" clearable :width="100">
                            </abc-input>
                        </slot>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'refractiveIndex'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <abc-select
                            v-model="curParams.refractiveIndex"
                            :width="100"
                            clearable
                        >
                            <abc-option
                                v-for="it in options.refractive"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'customType'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <abc-select
                            v-model="curParams.customType"
                            :width="100"
                            clearable
                        >
                            <abc-option
                                v-for="it in options.tab"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'addLight'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <abc-input
                            v-model="curParams.addLight"
                            v-abc-focus-selected
                            type="money"
                            :width="100"
                            :config="inputConfig"
                            :input-custom-style="{ textAlign: 'left' }"
                        >
                        </abc-input>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'wearCycle'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <abc-select
                            v-model="curParams.wearCycle"
                            :width="100"
                            clearable
                        >
                            <abc-option
                                v-for="it in options.wearCycle"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'material'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <slot name="material" :form-data="curParams">
                            <abc-input v-model="curParams.material" clearable :width="100">
                            </abc-input>
                        </slot>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'spherical'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <abc-input
                            v-model="curParams.spherical"
                            v-abc-focus-selected
                            :width="100"
                            type="money"
                            :config="inputConfig"
                            :input-custom-style="{ textAlign: 'left' }"
                        >
                        </abc-input>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'focalLength'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <abc-input
                            v-model="curParams.focalLength"
                            v-abc-focus-selected
                            :width="100"
                            type="money"
                            :config="inputConfig"
                            :input-custom-style="{ textAlign: 'left' }"
                        >
                        </abc-input>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'lenticular'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <abc-input
                            v-model="curParams.lenticular"
                            v-abc-focus-selected
                            :width="100"
                            type="money"
                            :config="inputConfig"
                            :input-custom-style="{ textAlign: 'left' }"
                        >
                        </abc-input>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'spec'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <slot name="spec" :form-data="curParams">
                            <abc-input v-model="curParams.spec" clearable :width="100">
                            </abc-input>
                        </slot>
                    </abc-form-item>
                    <abc-form-item
                        v-if="key === 'color'"
                        :key="key"
                        style="margin: 0;"
                        :label="SkuFilterKeyNameMap[key]"
                        :label-width="64"
                    >
                        <slot name="color" :form-data="curParams">
                            <abc-input v-model="curParams.color" clearable :width="100">
                            </abc-input>
                        </slot>
                    </abc-form-item>
                </template>
            </abc-form>

            <div style="display: flex; padding: 16px 12px; border-top: 1px solid #e6eaee;">
                <abc-checkbox
                    v-if="showZeroStock"
                    v-model="curIncludeZeroStock"
                    type="number"
                    style="margin-right: 24px;"
                >
                    含0库存
                </abc-checkbox>

                <abc-checkbox
                    v-if="showDisableStock"
                    v-model="curIncludeDisableStock"
                    type="number"
                >
                    含停用
                </abc-checkbox>
                <abc-button style="margin-left: auto;" @click="handleChange">
                    筛选
                </abc-button>
                <abc-button type="blank" @click="handleReset">
                    清空
                </abc-button>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import { isNull } from '@/utils';
    import { SkuFilterFormData, SkuFilterKeyNameMap, SkuType } from 'views/inventory/eyeglass/constant';

    export default {
        name: 'EyeglassSelectPopover',
        props: {
            params: {
                type: Object,
                required: true,
            },
            isStockEntryStat: {
                type: Boolean,
                default: false,
            },
            subType: {
                type: [Number, String],
            },
            includeZeroStock: {
                type: Number,
            },
            includeDisableStock: {
                type: Number,
            },
            showZeroStock: {
                type: Boolean,
                default: true,
            },
            showDisableStock: {
                type: Boolean,
                default: true,
            },
            itemKeys: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                SkuFilterKeyNameMap,
                curParams: {
                    ...SkuFilterFormData,
                    ...this.params,
                },
                curIncludeZeroStock: this.includeZeroStock,
                curIncludeDisableStock: this.includeDisableStock,
                options: {
                    refractive: [
                        { label: '1.49', value: 1.49 },
                        { label: '1.50', value: 1.50 },
                        { label: '1.53', value: 1.53 },
                        { label: '1.54', value: 1.54 },
                        { label: '1.55', value: 1.55 },
                        { label: '1.56', value: 1.56 },
                        { label: '1.59', value: 1.59 },
                        { label: '1.60', value: 1.60 },
                        { label: '1.61', value: 1.61 },
                        { label: '1.67', value: 1.67 },
                        { label: '1.70', value: 1.70 },
                        { label: '1.71', value: 1.71 },
                        { label: '1.73', value: 1.73 },
                        { label: '1.74', value: 1.74 },
                        { label: '1.76', value: 1.76 },
                        { label: '1.80', value: 1.80 },
                        { label: '1.90', value: 1.90 },
                    ],
                    wearCycle: [
                        { label: '日抛', value: '日抛' },
                        { label: '双周抛', value: '双周抛' },
                        { label: '月抛', value: '月抛' },
                        { label: '季抛', value: '季抛' },
                        { label: '半年抛', value: '半年抛' },
                        { label: '年抛', value: '年抛' },
                    ],
                    tab: [
                        { label: '成品', value: SkuType.Finished },
                        { label: '定制', value: SkuType.CustomTailor },
                    ],
                },
            };
        },
        computed: {
            filterKeys() {
                if (this.isStockEntryStat) return this.itemKeys;
                if (!this.subType) return ['brandName', 'refractiveIndex', 'customType', 'addLight', 'wearCycle', 'material', 'spherical', 'focalLength', 'lenticular', 'spec', 'color'];

                let keys = [];

                if (this.subType === 1) {
                    keys = ['spherical', 'lenticular'];
                }
                if (this.subType === 2) {
                    keys = ['color'];
                }
                if (this.subType === 4) {
                    keys = ['focalLength', 'color'];
                }
                if (this.subType === 5) {
                    keys = ['focalLength'];
                }
                if (this.subType === 3 || this.subType === 6) {
                    keys = ['spec'];
                }

                return [...new Set(this.itemKeys.concat(keys))];
            },
            filterParamsText() {
                return Object.entries(this.params).reduce((res, [k, v]) => {
                    if (!isNull(v)) {
                        if (k === 'customType') {
                            v = v === SkuType.Finished ? '成品' : '定制';
                        }
                        res += `${SkuFilterKeyNameMap[k]}:${v} / `;
                    }
                    return res;
                }, '').slice(0, -2);// 去掉最后一个斜杠和空格
            },
            inputConfig() {
                return {
                    max: 100, formatLength: 2, supportNegative: true, supportZero: true,
                };
            },
        },
        watch: {
            params: {
                handler() {
                    this.curParams = {
                        ...SkuFilterFormData,
                        ...this.params,
                    };
                },
                deep: true,
            },
            includeZeroStock() {
                this.curIncludeZeroStock = this.includeZeroStock;
            },
            includeDisableStock() {
                this.curIncludeDisableStock = this.includeDisableStock;
            },
        },
        methods: {
            handleChange() {
                this.$emit('change', {
                    params: {
                        ...this.curParams,
                        addLight: isNull(this.curParams.addLight) ? '' : +this.curParams.addLight,
                        spherical: isNull(this.curParams.spherical) ? '' : +this.curParams.spherical,
                        lenticular: isNull(this.curParams.lenticular) ? '' : +this.curParams.lenticular,
                        focalLength: isNull(this.curParams.focalLength) ? '' : +this.curParams.focalLength,
                    },
                    includeZeroStock: this.curIncludeZeroStock,
                    includeDisableStock: this.curIncludeDisableStock,// 含停用药品
                });
                this.close();
            },
            handleReset() {
                this.curParams = {
                    ...SkuFilterFormData,
                };
                this.curIncludeZeroStock = 1;
                this.curIncludeDisableStock = 1;
                this.handleChange();
            },
            close() {
                this.$refs.popover?.doClose();
            },
        },
    };
</script>
