import {
    mapGetters, mapActions,
} from 'vuex';
import { formatCacheTime } from '@/utils';
import Clone from 'utils/clone';

export default {
    data() {
        return {
            deleteDraftButtonLoading: false,
            saveDraftButtonLoading: false,
        };
    },
    computed: {
        ...mapGetters([
            'draftGoodsPurchase',
            'draftGoodsIn',
            'draftGoodsOut',
            'draftGoodsTrans',
            'draftGoodsCheck',
            'draftGoodsApply',
        ]),

    },
    methods: {
        ...mapActions([
            'saveGoodsCloudDraft',
            'deleteGoodsCloudDraft',
            'getGoodsCloudDraftDetail',
        ]),
        formatCacheTime,
        // 弹窗手动关闭-处理草稿数据
        async closeDraftHandler(key = 'goods-check') {
            // 删除自动保存的草稿
            if (this._draftId) {
                this.clearDraft(
                    key,
                    this._draftId,
                );
            }

            // 恢复打开时的草稿
            if (!this._isCloudDraft && this.draftId) {
                // 恢复打开时的草稿
                this.setDraft(
                    key,
                    this._beforeDraft,
                );
            }
        },
        // 加载草稿数据
        async fetchDraft(key, draftId, formatDraft = (d) => d) {
            const draftMap = {
                'goods-in': this.draftGoodsIn,
                'goods-out': this.draftGoodsOut,
                'goods-check': this.draftGoodsCheck,
                'goods-trans': this.draftGoodsTrans,
                'goods-apply': this.draftGoodsApply,
            };

            // 获取本地草稿数据
            const draft = draftMap[key]?.find((item) => item.draftId === draftId);
            if (draft) {
                // 使用本地草稿数据
                if (draft) {
                    const cacheOrder = Clone({
                        ...this.order,
                        ...(draft.order ?? {}),
                        stockCheckScope: draft.stockCheckScope,
                    });

                    if (['goods-in', 'goods-check'].includes(key)) {
                        await this.initSummaryTraceCode(cacheOrder.list);
                    }

                    this.order = cacheOrder;
                    this._beforeDraft = Clone(draft);
                }
            } else {
                try {
                    this.loading = true;
                    const cloudDraft = await this.getGoodsCloudDraftDetail({
                        key,
                        draftId,
                    });

                    // 使用云草稿数据
                    if (cloudDraft) {
                        const cacheOrder = Clone({
                            ...this.order,
                            ...formatDraft(cloudDraft),
                        });

                        if (['goods-in', 'goods-check'].includes(key)) {
                            await this.initSummaryTraceCode(cacheOrder.list);
                        }

                        this.order = cacheOrder;
                        this._beforeDraft = Clone(cloudDraft);
                    }
                } catch (e) {
                    console.error(e);
                    this.$Toast({
                        type: 'error',
                        message: e.message,
                    });
                } finally {
                    this.loading = false;
                }
            }

            if (this._beforeDraft) {
                console.log('缓存的草稿数据', this._beforeDraft);
                // 标记为云草稿
                this._isCloudDraft = !this._beforeDraft.draftId && this._beforeDraft.id;
            }
        },
        /**
         * @desc set 相关随写随存 相关数据
         * <AUTHOR>
         * @date 2018/06/19 10:19:41
         */
        setDraft(key, record) {
            this.$store.dispatch('SetDraft', {
                key, record,
            });
        },

        /**
         * @desc 清除 相关随写随存 相关数据
         * <AUTHOR>
         * @date 2018/06/19 10:23:46
         */
        clearDraft(key, draftId) {
            this.$store.dispatch('ClearDraft', {
                key, draftId,
            });
        },

        // ================================== 封装了本地和云端的草稿保存功能 ==================================
        async saveDraftMixin(key, draft, isSaveCloudDraft = true) {
            //本地草稿需要draftId云草稿不需要
            // this._beforeDraft?.draftId是打开草稿编辑，this._draftId这个是自动保存产生的
            if (!this._draftId) {
                this._draftId = this._beforeDraft?.draftId || `${Date.now()}`;
            }
            const record = {
                id: this._beforeDraft?.id,
                draftId: this._draftId,
                ...draft,
            };
            if (!isSaveCloudDraft) {
                // 排除了外层list，避免本地草稿数据量过大
                delete record.list;
                return this.setDraft(key, record);
            }
            return this.saveGoodsCloudDraft({
                key,
                record,
            });
        },
        async deleteDraftMixin(key, draftId) {
            // 是否是本地草稿
            if (this._beforeDraft?.draftId || this._draftId) {
                return this.clearDraft(key, this._beforeDraft?.draftId || this._draftId);
            }
            return this.deleteGoodsCloudDraft({
                key, draftId,
            });

        },
    },
};
