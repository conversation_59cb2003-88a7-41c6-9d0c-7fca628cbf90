<template>
    <abc-row :gutter="[24, 16]" :wrap="'wrap'" class="goods-archives-info base-info-wrapper">
        <template v-for="item in renderData">
            <abc-col v-bind="item.colProps" :key="item.prop">
                <abc-form-item
                    v-if="item.prop === 'currentMedicineCadn'"
                    :required="item.required"
                    :label="item.label"
                    :validate-event="item.validateEvent"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-autocomplete
                        v-model.trim="currentMedicineCadn"
                        :width="206"
                        :inner-width="206"
                        :fetch-suggestions="searchByCadn"
                        async-fetch
                        :disabled="disabled"
                        :max-length="80"
                        :class="{ 'is-disabled-goods': disableStatus && isShowDisableStatusText }"
                        data-cy="inventory-archives-base-info-form-medicine-cadn-input"
                        @focus="handleFocus"
                        @enterEvent="handleSelectCadn"
                        @enter="enterEvent"
                        @blur="$emit('blurInput', 'medicineCadn');isShowDisableStatusText = true"
                    >
                        <template slot="suggestions" slot-scope="props">
                            <dt
                                class="suggestions-item"
                                :class="{ selected: props.index == props.currentIndex }"
                                @click="handleSelectCadn(props.suggestion)"
                            >
                                <div class="ellipsis" :title="props.suggestion.medicineCadn">
                                    {{ props.suggestion.medicineCadn }}
                                </div>
                            </dt>
                        </template>
                        <span v-if="disableStatus && isShowDisableStatusText" slot="append" class="disable-status">已停用</span>
                    </abc-autocomplete>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentTypeId'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-space is-compact>
                        <abc-input
                            v-if="disableTypeSelect"
                            :width="103"
                            disabled
                            :value="currentTypeName"
                        >
                            <template v-if="isSupportReCreateArchive && goodsInfo.id" #appendInner>
                                <abc-tooltip
                                    placement="top"
                                    content="无修改档案权限，请联系总部开启权限"
                                    :disabled="!disabledEditButton"
                                >
                                    <div>
                                        <abc-button
                                            icon="s-b-edited-line"
                                            size="small"
                                            variant="text"
                                            :disabled="disabledEditButton"
                                            @click="handleEditGoodsType"
                                        >
                                        </abc-button>
                                    </div>
                                </abc-tooltip>
                            </template>
                        </abc-input>
                        <abc-select
                            v-else
                            v-model="currentTypeId"
                            :width="103"
                            :inner-width="renderTypeList.length > 9 ? 106 : 103"
                            :disabled="disableTypeSelect"
                            data-cy="inventory-archives-base-info-form-type-id-select"
                            @enter="enterEvent"
                            @change="handleTypeChange"
                        >
                            <abc-option
                                v-for="it in renderTypeList"
                                :key="it.id"
                                :label="it.name"
                                :value="it.id"
                            ></abc-option>
                            <template v-if="showEditOtherType && !disabledEditButton" #bottom-fixed>
                                <abc-flex class="abc-select-panel-bottom-wrapper" style="justify-content: center;">
                                    <abc-button
                                        variant="text"
                                        size="small"
                                        style="padding: 2px;"
                                        @click="handleEditGoodsType"
                                    >
                                        改为其他类型
                                    </abc-button>
                                </abc-flex>
                            </template>
                        </abc-select>
                        <secondary-classification-select
                            v-model="currentCustomTypeId"
                            clearable
                            :width="103"
                            :disabled="disabled"
                            :type-id="currentTypeId"
                        ></secondary-classification-select>
                    </abc-space>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentPharmacologyType'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-cascader
                        v-model="currentPharmacologyType"
                        class="pharmacology-type-cascader"
                        :props="{
                            children: 'children',
                            label: 'category',
                            value: 'id'
                        }"
                        placeholder=""
                        :disabled="disabled"
                        :width="206"
                        :panel-max-height="210"
                        separation="/"
                        :options="instructionsCategory.items"
                        data-cy="inventory-archives-base-info-form-pharmacology-type-cascader"
                    >
                    </abc-cascader>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentName'"
                    :label="item.label"
                    :required="item.required"
                    :validate-event="item.validateEvent"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-autocomplete
                        v-model.trim="currentName"
                        :width="206"
                        :inner-width="206"
                        :fetch-suggestions="item.fetchSuggestions"
                        async-fetch
                        :disabled="disabled"
                        :max-length="50"
                        focus-show
                        :class="{ 'is-disabled-goods': disableStatus && isShowDisableStatusText }"
                        data-cy="inventory-archives-base-info-form-name-input"
                        @enterEvent="handleSelectTreadName"
                        @enter="enterEvent"
                        @blur="$emit('blurInput', 'name')"
                    >
                        <template slot="suggestions" slot-scope="props">
                            <dt
                                class="suggestions-item"
                                :class="{ selected: props.index == props.currentIndex }"
                                @click="handleSelectTreadName(props.suggestion)"
                            >
                                <div>
                                    {{ props.suggestion.tradeName || props.suggestion.name || props.suggestion.medicineCadn }}
                                </div>
                            </dt>
                        </template>
                        <span v-if="disableStatus && !isMedicineType" slot="append" class="disable-status">已停用</span>
                    </abc-autocomplete>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentMedicineDosageForm'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-space is-compact>
                        <abc-select
                            v-model="currentMedicineDosageForm"
                            :width="206"
                            :disabled="disabled"
                            clearable
                            :max-height="210"
                            data-cy="inventory-archives-base-info-form-medicine-dosage-select"
                            with-search
                            :fetch-suggestions="fetchDosageFormTypeSuggestions"
                            @enter="enterEvent"
                        >
                            <abc-option
                                v-for="it in dosageFormTypeRenderList"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-space>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentOrigin'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-select
                        v-model="currentOrigin"
                        :disabled="disabled"
                        :width="206"
                        data-cy="inventory-archives-base-info-form-origin-select"
                        @enter="enterEvent"
                    >
                        <abc-option value="国产" label="国产">
                            国产
                        </abc-option>
                        <abc-option value="进口" label="进口">
                            进口
                        </abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentManufacturer'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-autocomplete
                        v-model.trim="currentManufacturer"
                        :width="206"
                        :inner-width="206"
                        :fetch-suggestions="item.fetchSuggestions"
                        async-fetch
                        focus-show
                        :disabled="disabled"
                        :max-length="50"
                        data-cy="inventory-archives-base-info-form-manufacturer-input"
                        @enterEvent="handleSelectManufacturer"
                        @change="handleChangeManufacturer"
                        @enter="enterEvent"
                        @blur="$emit('blurInput', 'manufacturerFull')"
                    >
                        <template slot="suggestions" slot-scope="props">
                            <dt
                                class="suggestions-item"
                                :class="{ selected: props.index == props.currentIndex }"
                                @click="handleSelectManufacturer(props.suggestion)"
                            >
                                <div class="ellipsis" :title="props.suggestion.manufacturerFull">
                                    {{ props.suggestion.manufacturerFull }}
                                </div>
                            </dt>
                        </template>
                    </abc-autocomplete>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentProducer'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-autocomplete
                        v-model.trim="currentProducer"
                        :width="206"
                        :inner-width="206"
                        :fetch-suggestions="item.fetchSuggestions"
                        async-fetch
                        focus-show
                        :disabled="disabled"
                        :max-length="50"
                        data-cy="inventory-archives-base-info-form-producer-input"
                        @enterEvent="handleSelectProducer"
                        @enter="enterEvent"
                        @blur="$emit('blurInput', 'origin')"
                    >
                        <template slot="suggestions" slot-scope="props">
                            <dt
                                class="suggestions-item"
                                :class="{ selected: props.index == props.currentIndex }"
                                @click="handleSelectProducer(props.suggestion)"
                            >
                                <div class="ellipsis" :title="props.suggestion.manufacturerFull">
                                    {{ props.suggestion.manufacturerFull }}
                                </div>
                            </dt>
                        </template>
                    </abc-autocomplete>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentLastStockInOrderSupplier'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model.trim="currentLastStockInOrderSupplier"
                        :width="206"
                        disabled
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentCertificateName'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model.trim="currentCertificateName"
                        :width="206"
                        :disabled="disabled"
                        data-cy="inventory-archives-base-info-form-certificate-name-input"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentApproveNumber'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-autocomplete
                        v-if="isMaterialMedicineType"
                        v-model.trim="currentMedicineNmpn"
                        :width="206"
                        :inner-width="206"
                        :fetch-suggestions="item.fetchSuggestions"
                        async-fetch
                        focus-show
                        :disabled="disabled"
                        :max-length="50"
                        data-cy="inventory-archives-base-info-form-medicine-nmpn-input"
                        @enterEvent="handleSelectMedicineNmpn"
                        @enter="enterEvent"
                        @blur="$emit('blurInput', 'medicineNmpn')"
                    >
                        <template slot="suggestions" slot-scope="props">
                            <dt
                                class="suggestions-item"
                                :class="{ selected: props.index == props.currentIndex }"
                                @click="handleSelectMedicineNmpn(props.suggestion)"
                            >
                                <div class="ellipsis" :title="props.suggestion.medicineNmpn">
                                    {{ props.suggestion.medicineNmpn }}
                                </div>
                            </dt>
                        </template>
                    </abc-autocomplete>

                    <abc-input
                        v-else
                        v-model="currentMedicineNmpn"
                        :width="206"
                        :disabled="disabled"
                        max-length="50"
                        data-cy="inventory-archives-base-info-form-medicine-nmpn-input"
                        @enter="enterEvent"
                        @blur="$emit('blurInput', 'medicineNmpn')"
                    >
                    </abc-input>
                </abc-form-item>


                <abc-form-item
                    v-else-if="item.prop === 'currentSmartDispenseMachineNo'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <div v-if="!isOpenSmartDispensing" class="smart-dispense">
                        <abc-popover
                            :open-delay="300"
                            theme="yellow"
                            width="420px"
                            trigger="hover"
                            class="smart-dispense-popover"
                        >
                            <abc-input
                                slot="reference"
                                :width="206"
                                placeholder="未开通"
                                disabled
                            >
                                <abc-icon
                                    slot="appendInner"
                                    icon="info_bold"
                                    size="14"
                                    :color="$store.state.theme.style.P3"
                                ></abc-icon>
                            </abc-input>

                            <div class="smart-dispense-content">
                                <p style="margin-bottom: 10px; font-weight: bolder;">
                                    开通服务请联系您的专属销售顾问
                                </p>
                                <p style="margin-bottom: 10px; font-weight: bolder;">
                                    支持服务
                                </p>
                                <div style="color: #5c5955;">
                                    单台、多台颗粒机对接，收费后自动传单给颗粒机，完成智能发药
                                </div>
                                <p style="margin: 10px 0; font-weight: bolder;">
                                    支持颗粒机
                                </p>
                                <div style="color: #5c5955;">
                                    天江，康仁堂，一方，新绿色，三九，冠道，农本方
                                </div>
                            </div>
                        </abc-popover>
                    </div>
                    <abc-select
                        v-else
                        v-model="currentSmartDispenseMachineNo"
                        :width="206"
                        data-cy="inventory-archives-base-info-form-smart-dispense-select"
                        @enter="enterEvent"
                    >
                        <abc-option
                            v-for="it in dispenseMachineList"
                            :key="it.no"
                            :value="it.no"
                            :label="it.name"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentBarCode'"
                    :label="item.label"
                    :validate-event="validateBarCode"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model="currentBarCode"
                        :width="206"
                        :disabled="disabled"
                        type="number-en-char"
                        max-length="50"
                        data-cy="inventory-archives-base-info-form-bar-code-input"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentShortId'"
                    :label="item.label"
                    :required="item.required"
                    :validate-event="validateShortId"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model="currentShortId"
                        :width="206"
                        :disabled="disabled"
                        placeholder="系统生成或自定义"
                        type="text"
                        max-length="20"
                        data-cy="inventory-archives-base-info-form-short-id-input"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'isPreciousDevice'"
                    :label="item.label"
                >
                    <abc-select
                        v-model="isPreciousDevice"
                        :disabled="disabled"
                        :width="206"
                        data-cy="inventory-archives-base-info-form-is-precious-device-select"
                        @enter="enterEvent"
                    >
                        <abc-option
                            v-for="it in options.isPreciousDeviceType"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentExtendSpec'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model="currentExtendSpec"
                        :width="206"
                        :disabled="disabled"
                        :max-length="20"
                        type="text"
                        data-cy="inventory-archives-base-info-form-extend-spec-input"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentPieceUnit'"
                    required
                    :label="'34499'"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <select-usage
                        v-model="currentPieceUnit"
                        type="chpUnit"
                        class="select-usage-unit huge"
                        :disabled="disabled"
                        placement="bottom-start"
                        data-cy="inventory-archives-base-info-form-piece-unit-select"
                        @enter="enterEvent"
                    >
                    </select-usage>
                </abc-form-item>

                <abc-form-item
                    v-else-if="enableEqCoefficient && item.prop === 'eqCoefficient'"
                    required
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                    :label-tips-props="{
                        maxWidth: '350px',
                    }"
                >
                    <template slot="labelTips">
                        <abc-text>
                            设置换算当量后，门诊开单可查看等效饮片用量并用饮片量开方；入库、实际收费和发药时会自动转化为按实际颗粒量
                        </abc-text>
                    </template>
                    <abc-space is-compact compact-block>
                        <abc-input
                            value="1 g 颗粒 ="
                            :width="80"
                            tabindex="-1"
                            readonly
                            :disabled="disabled"
                            :input-custom-style="{
                                color: 'var(--abc-color-T3)',
                            }"
                        ></abc-input>
                        <abc-input
                            v-model.number="currentEqCoefficient"
                            v-abc-focus-selected
                            :width="68"
                            :disabled="disabled"
                            type="number"
                            :config="{
                                min: 0,
                                max: 999.99,
                                formatLength: 2,
                            }"
                            :input-custom-style="{
                                textAlign: 'center',
                            }"
                            data-cy="inventory-archives-base-info-form-extend-eq-coefficient-input"
                            @enter="enterEvent"
                        >
                        </abc-input>
                        <abc-input
                            value="g 饮片"
                            :width="60"
                            tabindex="-1"
                            readonly
                            :disabled="disabled || !(currentPieceUnit === 'g' || currentPieceUnit === '克')"
                            :input-custom-style="{
                                color: 'var(--abc-color-T3)',
                            }"
                        ></abc-input>
                    </abc-space>
                </abc-form-item>

                <spec-info
                    v-else-if="item.prop === 'currentSpec'"
                    :type="currentType"
                    :sub-type="currentSubType"
                    :disabled="disabled"
                    :disabled-edit-button="disabledEditButton"
                    :dismounting="currentDismounting"
                    :is-chinese-western-patent="isChineseWesternPatentType"
                    :spec-type.sync="currentSpecType"
                    :medicine-dosage-num.sync="currentMedicineDosageNum"
                    :medicine-dosage-unit.sync="currentMedicineDosageUnit"
                    :component-content-num.sync="currentComponentContentNum"
                    :component-content-unit.sync="currentComponentContentUnit"
                    :piece-num.sync="currentPieceNum"
                    :piece-unit.sync="currentPieceUnit"
                    :package-unit.sync="currentPackageUnit"
                    :material-spec.sync="currentMaterialSpec"
                    :goods-info="goodsInfo"
                    @blurInput="(key)=>{
                        $emit('blurInput', key);
                    }"
                    @iconClick="handleEditPieceNum"
                    @copyArchive="handleCopyArchive"
                >
                </spec-info>

                <abc-form-item
                    v-else-if="item.prop === 'currentShebaoSpec'"
                    :label="item.label"
                >
                    <abc-input
                        v-model="currentShebaoSpec"
                        :width="206"
                        :title="currentShebaoSpec"
                        :disabled="true"
                        :max-length="100"
                        type="text"
                    >
                    </abc-input>
                </abc-form-item>

                <identification-code-select
                    v-else-if="item.prop === 'currentTraceableCodeNoInfoList'"
                    :traceable-code-no-info-list.sync="currentTraceableCodeNoInfoList"
                    :item="item"
                    :allow-report-no-code="allowReportNoCode"
                    :goods-info="goodsInfo"
                    :is-medicine-type="isMedicineType"
                    :disabled="disabledBaseInfo"
                ></identification-code-select>

                <abc-form-item
                    v-else-if="item.prop === 'currentMark'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model="currentMark"
                        :width="206"
                        :title="currentMark"
                        :disabled="disabled"
                        :max-length="100"
                        type="text"
                        data-cy="inventory-archives-extend-info-form-mark-input"
                        :placeholder="item.placeholder"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentGoodsTagIds'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <goods-tag-select
                        v-model="currentGoodsTagIds"
                        :disabled="disabledBaseInfo"
                        :clearable="false"
                        @enter="enterEvent"
                    ></goods-tag-select>
                </abc-form-item>
            </abc-col>
        </template>
    </abc-row>
</template>

<script>
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import { isEqual } from 'utils/lodash';
    import GoodsAPI from 'api/goods';
    import CdssAPI from 'api/cdss';

    import EnterEvent from 'views/common/enter-event';
    const SecondaryClassificationSelect = () => import('views/inventory/goods/components/secondary-classification/secondary-select.vue');

    import SpecInfo from 'views/inventory/goods/archives/components/spec-info.vue';
    import SelectUsage from 'views/layout/select-group/index.vue';
    import {
        isPreciousDeviceType, OtcType,
        typeIdToInstructionsCategory,
    } from 'views/common/inventory/constants';
    import { trimTree } from 'views/inventory/goods/archives/utils';
    import { mapGetters } from 'vuex';
    import {
        GoodsArchivesControllerKey,
        disabledBaseInfoKey,
        usePieceUnitFlagKey, modifyGoodsItemKey, canUpdateGoodsInfoKey,
    } from 'views/inventory/goods/archives/provideKeys';
    import {
        isNotNull, isNull,
    } from '@/utils';
    import TraceCode, { TraceableCodeTypeEnum } from '@/service/trace-code/service';
    import identificationCodeSelect
        from 'views/inventory/goods/archives/components/identification-code-select/index.vue';
    import GoodsTagSelect from 'views/inventory/goods/archives/components/goods-tag-select.vue';
    import useBusinessScope from 'views/inventory/goods/archives/hook/useBusinessScope';
    import useDictionary, {
        CatalogueEnum,
    } from '@/hooks/business/use-dictionary';
    import { ArchiveLabelManager } from 'views/common/inventory/ArchiveLabelManager';
    import GoodsAPIV3 from 'api/goods/index-v3';

    export default {
        name: 'BaseInfo',
        components: {
            GoodsTagSelect,
            identificationCodeSelect,
            SelectUsage,
            SecondaryClassificationSelect,
            SpecInfo,
        },
        mixins: [EnterEvent],
        inject: {
            // 药品档案控制器
            GoodsArchivesController: { from: GoodsArchivesControllerKey },
            // 手动控制部分字段的禁用
            disabledBaseInfo: {
                from: disabledBaseInfoKey,
                default: false,
            },
            // 控制是否能够快速建档
            usePieceUnitFlag: {
                from: usePieceUnitFlagKey,
                default: false,
            },
            modifyGoodsItem: {
                from: modifyGoodsItemKey,
                default: null,
            },
            canUpdateGoodsInfo: {
                from: canUpdateGoodsInfoKey,
                default: true,
            },
        },
        props: {
            goodsInfo: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            // 是否开启智能配药
            isOpenSmartDispensing: {
                type: Boolean,
                default: false,
            },
            // 颗粒机列表
            dispenseMachineList: {
                type: Array,
                default: () => [],
            },
            // 药品的一级分类
            primaryClassification: {
                type: Array,
                default: () => ([]),
            },
            // 药理作用分类-药品说明书数据
            instructionsCategoryMap: {
                type: Object,
                default: () => ({}),
            },
            dosageFormTypeList: {
                type: Array,
                default: () => ([]),
            },
            isMedicineDisinfectant: Boolean,
            isMedicineMaterial: Boolean,
            isGoodsMaterial: Boolean,
            isChineseMedicineType: Boolean,
            isChinesePatentMedicineType: Boolean,
            isChineseWesternPatentType: Boolean,
            isFixedOrLogisticsMaterial: Boolean,
            // 错误提示
            errorNameTip: String,
            errorBarCodeTip: String,
            errorShortIdTip: String,
            // 是否禁用typeId select
            disabledTypeIdSelector: Boolean,
        },
        setup() {
            const {
                getDefaultBusinessScopeObj,
            } = useBusinessScope(CatalogueEnum.BUSINESS_SCOPE_GOODS);

            const { getDictionaryByCatalogueName } = useDictionary({ GoodsAPIV3 });


            return {
                getDefaultBusinessScopeObj,
                getDictionaryByCatalogueName,
            };
        },
        data() {
            return {
                options: {
                    isPreciousDeviceType,
                },
                isShowDisableStatusText: true,
                traceCodeType: TraceableCodeTypeEnum.HAS_CODE,// 有码
                identificationCode: '',
                cacheGoodsTagIdList: [],
                dosageFormTypeKey: '',
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'isSingleStore',
                'traceCodeConfig',
                'isCanCreateGoodsArchivesInInventory',
                'isCanModifyGoodsArchivesInInventory',
                'isCanDeleteGoodsArchivesInInventory',
                'chainBasic',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            dosageFormTypeRenderList() {
                return this.dosageFormTypeList.filter((item) => {
                    return item.label.includes(this.dosageFormTypeKey);
                });
            },
            enableEqCoefficient() {
                return this.chainBasic.enableEqCoefficient;
            },
            isCanOperateGoodsArchivesInInventory() {
                // 有新建档案和删除档案权限才可以重新建档
                return this.isCanDeleteGoodsArchivesInInventory && this.isCanCreateGoodsArchivesInInventory;
            },
            isChineseMedicineAutoLinkPrescription() {
                return this.viewDistributeConfig.Inventory.isChineseMedicineAutoLinkPrescription;
            },
            chineseHerbSliceModifiable() {
                return this.viewDistributeConfig.Inventory.chineseHerbSliceModifiable;
            },
            isSupportReCreateArchive() {
                return this.viewDistributeConfig.Inventory.isSupportReCreateArchive;
            },
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            allowReportNoCode() {
                return !!this.traceCodeConfig.inventory?.isEnableNoCodeReport;
            },
            currentType() {
                return +this.goodsInfo.type;
            },
            currentSubType() {
                return +this.goodsInfo.subType;
            },
            // 药品
            isMedicineType() {
                return this.currentType === GoodsTypeEnum.MEDICINE;
            },
            // 商品
            isGoodsType() {
                return this.currentType === GoodsTypeEnum.GOODS;
            },
            // 中药颗粒
            isChineseMedicineGranuleType() {
                return this.goodsInfo.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE;
            },
            // 医疗器械
            isMaterialMedicineType() {
                return this.goodsInfo.typeId === GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL;
            },
            // 是否支持录入标识码
            isSupportTraceCode() {
                return TraceCode.isSupportTraceCode(this.currentTypeId);
            },
            // 控制渲染
            renderData() {
                const { region } = this.$abcSocialSecurity;
                const archiveLabelManager = new ArchiveLabelManager(this.goodsInfo);

                let pharmacologyName = '药理作用分类';
                if (this.isChinesePatentMedicineType) {
                    pharmacologyName = '科室分类';
                }
                if (this.isChineseMedicineType) {
                    pharmacologyName = '功效分类';
                }


                // '通用名',
                const currentMedicineCadnName = archiveLabelManager.getLabel('currentMedicineCadn', { region });
                // '商品名',
                const currentGoodsName = archiveLabelManager.getLabel('currentName', { region });
                // '生产厂家',
                const currentManufacturerName = archiveLabelManager.getLabel('currentManufacturer', { region });
                // 产地
                const currentProducerName = archiveLabelManager.getLabel('currentProducer', { region });
                // '供应商名称',
                const currentLastStockInOrderSupplierName = archiveLabelManager.getLabel('currentLastStockInOrderSupplier', { region });
                // '国药准字（批准文号）',
                const currentApproveNumberName = archiveLabelManager.getLabel('currentApproveNumber', { region });
                // '注册证号',
                const currentCertificateName = archiveLabelManager.getLabel('currentCertificateName', { region });
                // '包装',
                const currentExtendSpecName = archiveLabelManager.getLabel('currentExtendSpec', { region });
                // '包装单位',
                const currentPieceUnitName = archiveLabelManager.getLabel('currentPieceUnit', { region });
                return [
                    {
                        prop: 'currentMedicineCadn',
                        label: currentMedicineCadnName,
                        order: 10,
                        required: true,
                        validateEvent: this.validateName,
                        hidden: !this.isMedicineType,
                        help: this.getHelpText('medicineCadn',null, currentMedicineCadnName),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentTypeId',
                        label: '类型丨自定义二级类型',
                        order: 10,
                        hidden: false,
                        help: (() => {
                            // 有值代表有修改
                            const typeName = this.getHelpText('typeId', (e) => e);
                            // 有值代表有修改，为null表示删除
                            const customTypeName = this.getHelpText('customTypeId',(e) => e);

                            // 无需提示
                            if (!typeName && (!customTypeName) && customTypeName !== null) {
                                return '';
                            }

                            // 同时修改一级、二级类型
                            if (typeName && customTypeName) {
                                return `审批中：${this.modifyGoodsItem?.typeName} ${this.modifyGoodsItem?.customTypeName}`;
                            }

                            // 只修改了一级类型
                            if (typeName) {
                                return `审批中：${this.modifyGoodsItem?.typeName}`;
                            }

                            return customTypeName ? `审批中：${this.modifyGoodsItem?.customTypeName ?? ''}` : '审批中：删除“自定义二级类型”';
                        })(),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentPharmacologyType',
                        label: pharmacologyName,
                        order: 10,
                        hidden: !this.isMedicineType,
                        help: this.getHelpText('pharmacologicId', () => {
                            // 后端返回的pharmacologicName
                            const pharmacologicName = this.modifyGoodsItem?.pharmacologicName;
                            return pharmacologicName ? `审批中：${pharmacologicName}` : `审批中：删除“${pharmacologyName}”`;
                        }),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentName',
                        label: currentGoodsName,
                        order: this.isMedicineType ? 10 : 1,
                        required: !this.isMedicineType,
                        fetchSuggestions: this.isMedicineType ? this.searchByTreadName : this.searchByCadn,
                        hidden: this.isChineseMedicineType,
                        validateEvent: this.isMedicineType ? null : this.validateName,
                        help: this.getHelpText('name', (v) => {
                            return isNull(v) ? `审批中：删除“${currentGoodsName}”` : `审批中：${v}`;
                        }),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentMedicineDosageForm',
                        label: '剂型',
                        order: 10,
                        hidden: !this.isChineseWesternPatentType,
                        help: this.getHelpText('dosageFormType', (v) => {
                            const label = this.dosageFormTypeList.find((item) => item.value === v)?.label;
                            return isNull(label) ? '审批中：删除“剂型”' : `审批中：${label}`;
                        }),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentOrigin',
                        label: '进口丨国产',
                        order: 10,
                        hidden: !this.isMedicineMaterial,
                        help: this.getHelpText('origin',null,'进口丨国产'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentManufacturer',
                        label: currentManufacturerName,
                        help: this.getHelpText('manufacturerFull', null, currentManufacturerName),
                        helpTheme: 'warning',
                        order: 10,
                        fetchSuggestions: this.isChineseWesternPatentType ? this.searchByManufacturer : this.searchByManufacturerFull,
                        hidden: false,
                    },
                    {
                        prop: 'currentProducer',
                        label: currentProducerName,
                        help: this.getHelpText('origin', null, currentProducerName),
                        helpTheme: 'warning',
                        order: 10,
                        fetchSuggestions: this.isChineseWesternPatentType ? this.searchByManufacturer : this.searchByManufacturerFull,
                        hidden: !(region === 'jiangsu_nanjing' && this.isChineseMedicineType),
                    },
                    {
                        prop: 'currentLastStockInOrderSupplier',
                        label: currentLastStockInOrderSupplierName,
                        order: 10,
                        hidden: !(this.goodsInfo.id && this.isMedicineMaterial),
                    },
                    {
                        prop: 'currentCertificateName',
                        label: currentCertificateName,
                        order: 10,
                        hidden: !(this.isMedicineMaterial),
                        help: this.getHelpText('certificateName', null, currentCertificateName),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentApproveNumber',
                        label: currentApproveNumberName,
                        order: 10,
                        fetchSuggestions: this.searchByCertificateNo,
                        hidden: this.isChineseMedicineType || (this.currentTypeId === GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS || this.currentTypeId === GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL),
                        help: this.getHelpText('medicineNmpn', null, currentApproveNumberName),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentSmartDispenseMachineNo',
                        label: '智能发药',
                        order: 10,
                        hidden: !(this.isChineseMedicineGranuleType && !this.isChainAdmin),
                    },
                    {
                        prop: 'currentBarCode',
                        label: '条码',
                        order: 10,
                        hidden: false,
                        help: this.getHelpText('barCode', null, '条码'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentShortId',
                        label: '商品编码',
                        order: 10,
                        hidden: false,
                        help: this.getHelpText('shortId', null, '商品编码'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'isPreciousDevice',
                        label: '贵重器械',
                        order: 10,
                        hidden: !this.isMaterialMedicineType,
                    },
                    {
                        prop: 'currentExtendSpec',
                        label: currentExtendSpecName,
                        order: 10,
                        hidden: !this.isChineseMedicineType,
                        help: this.getHelpText('extendSpec', null, currentExtendSpecName),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentPieceUnit',
                        label: currentPieceUnitName,
                        order: 10,
                        hidden: !this.isChineseMedicineType,
                        help: this.getHelpText('pieceUnit', null, currentPieceUnitName),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'eqCoefficient',
                        label: '换算当量',
                        order: 10,
                        hidden: !(this.enableEqCoefficient && this.isChineseGranule && (this.currentPieceUnit === 'g' || this.currentPieceUnit === '克')),
                        help: this.getHelpText('eqCoefficient', null, '换算当量'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentSpec',
                        colProps: {
                            span: 24,
                        },
                        order: 10,
                        hidden: this.isChineseMedicineType,
                    },
                    {
                        prop: 'currentShebaoSpec',
                        label: '医保规格',
                        order: 10,
                        hidden: !(region === 'zhejiang_hangzhou' && this.isChineseWesternPatentType),
                    },
                    {
                        prop: 'currentTraceableCodeNoInfoList',
                        label: this.isMedicineMaterial ? '产品标识码 (追溯码01号段)' : '药品标识码 (追溯码前7位)',
                        config: {
                            focusPlaceholder: this.isMedicineMaterial ? '扫描或输入产品标识码' : '扫描或输入前7位',
                            // maxLength: this.isMedicineMaterial ? 20 : 7,
                        },
                        order: 10,
                        hidden: !(this.isSupportTraceCode),
                        help: this.getHelpText('traceableCodeNoInfoList', (v) => {
                            const label = v?.[0]?.drugIdentificationCode ?? '';
                            if (this.isMedicineMaterial) {
                                return isNull(label) ? '审批中：删除“产品标识码”' : `审批中：${label}`;
                            }
                            return isNull(label) ? '审批中：删除“药品标识码”' : `审批中：${label}`;
                        }, this.isMedicineMaterial ? '产品标识码' : '药品标识码'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentGoodsTagIds',
                        label: '标签',
                        order: 10,
                        help: this.getHelpText('goodsTagIdList', null, '标签'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentMark',
                        label: '备注',
                        order: 10,
                        help: this.getHelpText('remark', null, '备注'),
                        helpTheme: 'warning',
                        placeholder: this.isFixedOrLogisticsMaterial ? '' : '开单搜索时可展示',
                    },
                ].filter((item) => {
                    if (typeof item.hidden === 'function') {
                        return !item.hidden();
                    }
                    return !item.hidden;
                }).sort((a, b) => a.order - b.order);
            },
            disableStatus() {
                return !!this.goodsInfo.v2DisableStatus;
            },
            disabledEditButton() {
                if (this.operateGoodsArchives) {
                    return !this.isCanModifyGoodsArchivesInInventory;
                }
                return this.disabled;
            },
            showEditOtherType() {
                if (!this.isSupportReCreateArchive) return false;

                return this.goodsInfo?.id && this.isChineseHerbSliceModifiable;
            },
            isChineseHerbSliceModifiable() {
                return this.chineseHerbSliceModifiable && (this.currentTypeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES || this.currentTypeId === GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES);
            },
            disableTypeSelect() {
                if (this.isChineseHerbSliceModifiable) {
                    return this.disabled || this.disabledTypeIdSelector;
                }
                return this.disabled || !!(this.goodsInfo.id) || this.disabledTypeIdSelector;
            },
            // !根据typeId获取药品说明书数据
            instructionsCategory() {
                const type = typeIdToInstructionsCategory[this.currentTypeId];
                if (!type) {
                    return {
                        items: [],
                    };
                }

                const _instructionsCategory = this.instructionsCategoryMap[type];
                if (!_instructionsCategory || !_instructionsCategory.items) {
                    return {
                        items: [],
                    };
                }
                return {
                    ..._instructionsCategory,
                    // 中西成药不要最后一级数据
                    items: type === 3 ? _instructionsCategory.items : trimTree(_instructionsCategory.items),
                };
            },
            // 中西成药-通用名
            currentMedicineCadn: {
                get() {
                    return this.goodsInfo.medicineCadn;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'medicineCadn', v);
                },
            },
            // 一级分类
            currentTypeId: {
                get() {
                    return this.goodsInfo.typeId;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'typeId', v);
                    // this.$emit('updateGoodsInfo', 'customTypeId', '');
                },
            },
            // 二级分类
            currentCustomTypeId: {
                get() {
                    return this.goodsInfo.customTypeId || '';
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'customTypeId', v);
                },
            },
            // 药理作用分类
            currentPharmacologyType: {
                get() {
                    const { pharmacologicId } = this.goodsInfo;
                    if (!pharmacologicId) {
                        return [];
                    }
                    // 数据在请求回来做过处理
                    const { helper } = this.instructionsCategory;
                    const node = helper?.nodeMap[pharmacologicId];
                    if (!node) {
                        return [];
                    }
                    // 多级选择
                    if (node.parentIds) {
                        const result = node.parentIds.map((id) => {
                            const _node = helper.nodeMap[id];
                            return {
                                label: _node.category,
                                value: _node.id,
                            };
                        }).concat({
                            label: node.category,
                            value: node.id,
                        });
                        return result;
                    }

                    // 一级选择
                    return [
                        {
                            label: node.category,
                            value: node.id,
                        },
                    ];

                },
                set(v) {
                    const pharmacologicId = v.slice(-1)?.[0]?.value ?? '';
                    this.$emit('updateGoodsInfo', 'pharmacologicId', pharmacologicId);
                },
            },
            // 商品名
            currentName: {
                get() {
                    return this.goodsInfo.name;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'name', v);
                },
            },
            // 剂型
            currentMedicineDosageForm: {
                get() {
                    return this.goodsInfo.dosageFormType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'dosageFormType', v);
                },
            },
            // 进口/国产
            currentOrigin: {
                get() {
                    return this.goodsInfo.origin;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'origin', v);
                },
            },
            // 厂家
            currentManufacturer: {
                get() {
                    return this.goodsInfo.manufacturerFull;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'manufacturerFull', v);
                },
            },
            // 产地-南京中药才有，所以用了个已有的origin字段写入数据
            currentProducer: {
                get() {
                    return this.goodsInfo.origin;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'origin', v);
                },
            },
            // 供应商
            currentLastStockInOrderSupplier: {
                get() {
                    return this.goodsInfo.lastStockInOrderSupplier;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'lastStockInOrderSupplier', v);
                },
            },
            // 注册证名称
            currentCertificateName: {
                get() {
                    return this.goodsInfo.certificateName;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'certificateName', v);
                },
            },
            // 国字准号
            currentMedicineNmpn: {
                get() {
                    return this.goodsInfo.medicineNmpn;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'medicineNmpn', v);
                },
            },
            // 颗粒机
            currentSmartDispenseMachineNo: {
                get() {
                    return this.goodsInfo.smartDispenseMachineNo || this.goodsInfo.smartDispense;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'smartDispenseMachineNo', v);
                },
            },
            // 条码
            currentBarCode: {
                get() {
                    return this.goodsInfo.barCode;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'barCode', v);
                },
            },
            // 商品编码
            currentShortId: {
                get() {
                    return this.goodsInfo.shortId;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'shortId', v);
                },
            },
            isPreciousDevice: {
                get() {
                    return this.goodsInfo.isPreciousDevice ?? 0;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'isPreciousDevice', v);
                },
            },
            // 剂量模式
            currentSpecType: {
                get() {
                    return this.goodsInfo.specType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'specType', v);
                },
            },
            // 医保规格-浙江杭州定制展示
            currentShebaoSpec() {
                return this.goodsInfo?.shebao?.shebaoMedicineSpec ?? '';
            },
            // 容量
            currentMedicineDosageNum: {
                get() {
                    return this.goodsInfo.medicineDosageNum;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'medicineDosageNum', v);
                },
            },
            // 容量单位
            currentMedicineDosageUnit: {
                get() {
                    return this.goodsInfo.medicineDosageUnit;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'medicineDosageUnit', v);
                },
            },
            // 成分
            currentComponentContentNum: {
                get() {
                    return this.goodsInfo.componentContentNum;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'componentContentNum', v);
                },
            },
            // 成分单位
            currentComponentContentUnit: {
                get() {
                    return this.goodsInfo.componentContentUnit;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'componentContentUnit', v);
                },
            },
            // 最小包装数量
            currentPieceNum: {
                get() {
                    return this.goodsInfo.pieceNum;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'pieceNum', v);
                },
            },
            // 小单位
            currentPieceUnit: {
                get() {
                    return this.goodsInfo.pieceUnit;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'pieceUnit', v);
                },
            },
            // 大单位
            currentPackageUnit: {
                get() {
                    return this.goodsInfo.packageUnit;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'packageUnit', v);
                },
            },
            currentEqCoefficient: {
                get() {
                    return this.goodsInfo.eqCoefficient;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'eqCoefficient', v);
                },
            },
            // 是否拆零
            currentDismounting: {
                get() {
                    return !!this.goodsInfo.dismounting;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'dismounting', Number(v));
                },
            },
            currentExtendSpec: {
                get() {
                    return this.goodsInfo.extendSpec;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'extendSpec', v);
                },
            },
            currentMaterialSpec: {
                get() {
                    return this.goodsInfo.materialSpec;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'materialSpec', v);
                },
            },
            // 商品标识码
            currentTraceableCodeNoInfoList: {
                get() {
                    return this.goodsInfo.traceableCodeNoInfoList ?? [];
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'traceableCodeNoInfoList', v?.length ? v : null);
                },
            },
            // 商品标签
            currentGoodsTagIds: {
                get() {
                    return this.cacheGoodsTagIdList;
                },
                set(v) {
                    // 分为存储多选值
                    const tagIdList = v.map((item) => item.tagId);
                    this.cacheGoodsTagIdList = v;
                    this.$emit('updateGoodsInfo', 'goodsTagIdList', tagIdList);
                },
            },
            // 备注
            currentMark: {
                get() {
                    return this.goodsInfo.remark;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'remark', v);
                },
            },
            renderTypeList() {
                // 是编辑配方饮片、非配方饮片档案时
                if (this.showEditOtherType) {
                    // 只允许配方饮片、非配方饮片互相切换
                    return this.primaryClassification.filter((item) => {
                        return (String(item.id) === String(GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES) || String(item.id) === String(GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES));
                    });
                }
                return this.primaryClassification;
            },
            currentTypeName() {
                return this.renderTypeList.find((item) => {
                    return String(item.id) === String(this.currentTypeId);
                })?.name;
            },
            isChineseGranule() {
                return this.currentTypeId === GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE;
            },
        },
        watch: {
            identificationCode(v) {
                if (v === null || v === undefined) {
                    this.currentTraceableCodeNoInfoList = null;
                    this.traceCodeType = TraceableCodeTypeEnum.HAS_CODE;
                }
            },
            currentTraceableCodeNoInfoList: {
                handler(v) {
                    const traceableCodeNoInfo = v?.[0];
                    console.log('currentTraceableCodeNoInfoList', traceableCodeNoInfo);
                    if (traceableCodeNoInfo && !this.isInit) {
                        this.traceCodeType = traceableCodeNoInfo.type;
                        this.identificationCode = traceableCodeNoInfo.drugIdentificationCode || '';
                        this.isInit = true;
                    }
                },
                immediate: true,
            },
            'goodsInfo.goodsTagList': {
                handler (val) {
                    this.cacheGoodsTagIdList = (val || []).map((item) => ({
                        ...item,
                        disabled: this.isChainAdmin || this.isSingleStore ? false : !item.clinicId,
                    }));
                },
                immediate: true,
            },
        },
        created() {
            // 新建档案触发一次typeChange，联动修改经营范围
            if (!this.GoodsArchivesController.isDetail) {
                this.handleTypeChange(this.currentTypeId);
            }
        },
        methods: {
            fetchDosageFormTypeSuggestions(key) {
                this.dosageFormTypeKey = key;
            },
            getHelpText(key, formatFn, label = '') {
                if (this.disabled && this.modifyGoodsItem && key && this.modifyGoodsItem?.hasOwnProperty(key)) {
                    // 修改
                    const val = this.modifyGoodsItem[key];
                    const originVal = this.goodsInfo[key];
                    // 没变化不提示
                    if (val === originVal) return '';
                    // 变化了自定义提示
                    if (formatFn) return formatFn(val);

                    return isNotNull(val) ? `审批中：${val}` : `审批中：删除“${label}”`;
                }
                return '';
            },
            validateName(_, callback) {
                if (this.errorNameTip) {
                    callback({
                        message: this.errorNameTip,
                        validate: false,
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
            validateShortId(value, callback) {
                if (this.errorShortIdTip) {
                    return callback({
                        message: this.errorShortIdTip,
                        validate: false,
                    });
                }
                // 新建允许空
                if (!this.GoodsArchivesController.isDetail && isNull(value)) {
                    return callback({ validate: true });
                }
                const reg = /^[0-9a-zA-Z、，·.\-/]{1,32}$/;
                const validate = reg.test(value);
                return callback({
                    validate,
                    message: validate ? '' : '商品编码格式不正确',
                });
            },
            validateBarCode(_, callback) {
                if (this.errorBarCodeTip) {
                    callback({
                        message: this.errorBarCodeTip,
                        validate: false,
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
            createSearchNameParams(keyword) {
                return {
                    client: this.currentType === GoodsTypeEnum.MEDICINE ? 'medicine-cadn' : 'material-registration-name',
                    key_word: keyword,
                };
            },
            createNameSuggestions(hits) {
                return hits.reduce((res, item) => {
                    const type = Number(item.type || item.medicine_type);
                    if (type === this.currentType) {

                        if (type === GoodsTypeEnum.MEDICINE) {
                            res.push({
                                medicineId: item.medicineId || '',
                                medicineCadn: item.medicine_cadn || '',
                                name: item.medicine_trade_name || '',
                                type: item.medicine_type,
                                subType: item.medicine_sub_type || '',
                                typeId: item.typeId || '',
                                subTypeId: item.customTypeId || '',
                                manufacturer: item.medicine_manufacturer || '',
                                medicineNmpn: item.medicine_approved_code || '',
                                barCode: item.medicine_bar_code || '',
                                specification: item.medicine_spec || '',
                                packagePrice: item.medicine_unit_price || '',
                                medicineDosageForm: item.medicine_dosage_form || '',
                            });

                        } else {
                            res.push({
                                name: item.registered_name,
                                type: item.type,
                                subType: item.subType || '',
                                typeId: item.typeId || '',
                                manufacturer: item.manufacturer,
                                certificateName: item.registered_name,
                                medicineNmpn: item.registered_no,
                            });
                        }
                    }
                    return res;
                }, []);
            },
            createSearchManufacturerParams(keyword) {
                if (this.currentType === GoodsTypeEnum.MEDICINE) {
                    return {
                        client: 'medicine-manufacturer',
                        key_word: keyword,
                        cadn: this.currentMedicineCadn,
                        tradeName: this.currentName,
                    };
                }
                return {
                    client: 'material-manufacturer',
                    key_word: keyword,
                    registered_name: this.currentName,
                };
            },
            /**
             * @desc  查询药名信息
             * <AUTHOR>
             * @date 2023/11/27
             */
            async searchByCadn(keyword, next) {
                if (!keyword || this.isGoodsType) {
                    next([]);
                    return false;
                }
                try {
                    const fetchParams = this.createSearchNameParams(keyword);
                    const { data } = await GoodsAPI.searchProducts(fetchParams);
                    const afterParams = this.createSearchNameParams(keyword);
                    if (isEqual(fetchParams, afterParams)) {
                        const hits = data?.hits ?? [];

                        return next(this.createNameSuggestions(hits));
                    }
                    next([]);
                } catch (e) {
                    console.error(e);
                    next([]);
                }
            },
            /**
             * @desc  查询品牌名
             * <AUTHOR>
             * @date 2018/11/01 08:53:22
             */
            async searchByTreadName(keyword, next) {
                try {
                    const fetchParams = {
                        client: 'medicine-tradename',
                        key_word: keyword,
                        cadn: this.currentMedicineCadn,
                    };
                    const { data } = await GoodsAPI.searchProducts(fetchParams);
                    const hits = data?.hits ?? [];
                    const treadNameList = hits.filter((item) => {
                        return item.tradeName;
                    });
                    next(treadNameList);
                } catch (e) {
                    console.error(e);
                    next([]);
                }
            },
            /**
             * @desc  查询厂家信息
             * <AUTHOR>
             * @date 2018/11/01 09:56:34
             */
            async searchByManufacturer(keyword, next) {
                if (isNull(keyword)) return next([]);

                try {
                    const fetchParams = this.createSearchManufacturerParams(keyword);
                    const { data } = await GoodsAPI.searchProducts(fetchParams);
                    const afterParams = this.createSearchManufacturerParams(keyword);
                    if (isEqual(fetchParams, afterParams)) {
                        const result = data?.hits ?? [];
                        return next(result.map((item) => {
                            return {
                                ...item,
                                manufacturerFull: item.medicine_manufacturer || item.manufacturerFull,
                            };
                        }));
                    }
                    next([]);
                } catch (e) {
                    console.error(e);
                    next([]);
                }
            },
            /**
             * @desc  物资查询厂家信息
             * <AUTHOR>
             * @date 2018/11/01 09:56:34
             */
            async searchByManufacturerFull(keyword, next) {
                if (isNull(keyword)) return next([]);

                try {
                    const fetchParams = this.createSearchManufacturerParams(keyword);
                    const { data } = await CdssAPI.doSearch(fetchParams);
                    const afterParams = this.createSearchManufacturerParams(keyword);
                    if (isEqual(fetchParams, afterParams)) {
                        const result = data?.hits ?? [];
                        return next(result.map((item) => {
                            return {
                                name: item.registered_name,
                                type: item.type,
                                subType: item.subType || 1,
                                manufacturerFull: item.manufacturer || item.medicine_manufacturer,
                                certificateName: item.registered_name,
                                medicineNmpn: item.registered_no,
                            };
                        }));
                    }
                    next([]);
                } catch (e) {
                    console.error(e);
                    next([]);
                }
            },
            /**
             * @desc 搜索注册证号
             * <AUTHOR>
             * @date 2020-05-09 09:55:31
             */
            async searchByCertificateNo(keyword, next) {
                try {
                    const fetchParams = {
                        client: 'material-registration-no',
                        key_word: keyword || '',
                        registered_name: this.currentName || '',
                        manufacturer: this.currentManufacturer || '',
                        collapse: 1,
                    };
                    const { data } = await CdssAPI.doSearch(fetchParams);
                    const hits = (data && data.hits) || [];
                    const suggestions = hits.map((item) => {
                        return {
                            name: item.registered_name,
                            type: item.type,
                            subType: item.subType || 1,
                            manufacturerFull: item.manufacturer,
                            certificateName: item.registered_name,
                            medicineNmpn: item.registered_no,
                        };
                    });
                    next(suggestions);
                } catch (e) {
                    next([]);
                }
            },
            /**
             * @desc  在通用名下拉选择
             * <AUTHOR>
             * @date 2023/11/27
             */
            handleSelectCadn(suggestion) {
                this.currentMedicineCadn = suggestion.medicineCadn;
                const goods = { ...suggestion };
                // // 避免切到中药后名称丢失
                // if (isChineseMedicine(goods)) {
                //     goods.name = this.currentMedicineCadn;
                // }
                this.GoodsArchivesController.handleSelectGoods(goods);
            },
            handleSelectTreadName(suggestion) {
                console.log('handleSelectTreadName', suggestion);
                this.currentName = suggestion.tradeName || suggestion.name || suggestion.medicineCadn;
                this.currentCertificateName = suggestion.certificateName || '';
            },
            handleSelectManufacturer(value) {
                this.currentManufacturer = value.manufacturerFull;
                this.handleChangeManufacturer(this.currentManufacturer);
            },
            handleSelectProducer(value) {
                this.currentProducer = value.manufacturerFull;
            },
            handleChangeManufacturer(value) {
                // 默认同步到上市许可证持有人
                this.$emit('updateGoodsInfo', 'mha', value);
            },
            handleSelectMedicineNmpn(value) {
                this.currentMedicineNmpn = value.medicineNmpn;
            },
            handleFocus() {
                this.isShowDisableStatusText = false;
            },
            handleTypeChange(typeId) {
                if (
                    typeId === GoodsTypeIdEnum.MATERIAL_DISINFECTANT ||
                    typeId === GoodsTypeIdEnum.ADDITIONAL_COSMETIC ||
                    typeId === GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES ||
                    typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES
                ) {
                    // 经营范围数据初始化好之后，根据当前商品类型设置默认值
                    this.getDefaultBusinessScopeObj((item) => {
                        // 后台目前无关联关系，只能用名字先匹配
                        if (typeId === GoodsTypeIdEnum.MATERIAL_DISINFECTANT) {
                            return item.name === '消毒产品';
                        }
                        if (typeId === GoodsTypeIdEnum.ADDITIONAL_COSMETIC) {
                            return item.name === '化妆品';
                        }
                        // 非配方饮片
                        if (typeId === GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES) {
                            return item.displayName === '中药饮片/不含配方';
                        }
                        // 配方饮片（中药饮片）
                        if (typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES) {
                            return item.displayName === '中药饮片/含配方';
                        }
                        return false;
                    }).then((res) => {
                        console.log('getDefaultBusinessScopeObj', res);
                        this.$emit('updateGoodsInfo', 'businessScopeList', [{
                            id: res.id,
                            name: res.name,
                            parentId: res.parentId,
                            displayName: res?.displayName,
                        }]);
                    });
                } else {
                    this.$emit('updateGoodsInfo', 'businessScopeList', []);
                }

                if (this.isChineseMedicineAutoLinkPrescription) {
                    if (typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES) {
                        this.$emit('updateGoodsInfo', 'otcType', OtcType.NON_OTC);
                    }
                    if (typeId === GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES) {
                        this.$emit('updateGoodsInfo', 'otcType', '');
                    }
                }
            },
            handleEditGoodsType() {
                const vm = this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '变更商品类型存在账目不齐及合规风险，如需更改，请<br>重新创建正确的商品档案',
                    showConfirm: false,
                    showCancel: false,
                    footerPrepend: () => {
                        return (
                            <abc-space>
                                <abc-tooltip
                                    trigger="hover"
                                    content="无新建、删除档案权限，请联系总部开启权限"
                                    placement="top"
                                    z-index={999999}
                                    disabled={this.isCanOperateGoodsArchivesInInventory}
                                >
                                    <div>
                                        <abc-button
                                            disabled={!this.isCanOperateGoodsArchivesInInventory}
                                            onClick={() => {
                                                this.handleReCreateArchive();
                                                vm.close();// 手动关闭
                                            }}
                                        >
                                            重新建档
                                        </abc-button>
                                    </div>
                                </abc-tooltip>
                                <abc-button
                                    type="blank"
                                    onClick={() => {
                                        vm.close();// 手动关闭
                                    }}
                                >
                                    取消
                                </abc-button>
                            </abc-space>
                        );
                    },
                });
            },
            handleEditPieceNum() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '当前商品已发生拆零进销存，修改最小包装数量将导致<br>库存及进销存记录错误，请重新创建正确的商品档案',
                    confirmText: '重新建档',
                    onConfirm: () => {
                        this.handleReCreateArchive();
                    },
                });
            },
            handleReCreateArchive() {
                this.$emit('quickFiling');
            },
            handleCopyArchive() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '当前商品已发生拆零进销存，修改最小包装数量将导致<br>库存及进销存记录错误，请重新创建正确的商品档案',
                    confirmText: '复制原档案建档',
                    onConfirm: () => {
                        this.$emit('quickFiling', 2);
                    },
                });
            },
        },
    };
</script>
