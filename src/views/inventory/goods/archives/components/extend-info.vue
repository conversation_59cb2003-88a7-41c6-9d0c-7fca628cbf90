<template>
    <abc-row :gutter="[24, 16]" :wrap="'wrap'" class="goods-archives-info extend-info-wrapper">
        <template v-for="item in renderData">
            <abc-col v-bind="item.colProps" :key="item.prop">
                <abc-form-item
                    v-if="item.prop === 'currentDeviceType'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-select
                        v-model="currentDeviceType"
                        :width="206"
                        :disabled="disabled"
                        clearable
                        data-cy="inventory-archives-extend-info-form-device-type-select"
                        @enter="enterEvent"
                    >
                        <abc-option
                            v-for="it in options.deviceType"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>
                <abc-form-item
                    v-else-if="item.prop === 'currentOtcType'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-select
                        v-model="currentOtcType"
                        :width="206"
                        :disabled="disabled"
                        clearable
                        data-cy="inventory-archives-extend-info-form-otc-type-select"
                        @enter="enterEvent"
                    >
                        <abc-option
                            v-for="it in options.otcType"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentDangerIngredient'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <multiple-usage
                        v-model="currentDangerIngredient"
                        :options="options.jmd"
                        :disabled="disabled"
                        data-cy="inventory-archives-extend-info-form-danger-ingredient-select"
                        @enter="enterEvent"
                    ></multiple-usage>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentAntibioticLevel'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-space is-compact>
                        <abc-select
                            v-model="currentAntibioticLevel"
                            :width="103"
                            :inner-width="110"
                            :disabled="disabled"
                            clearable
                            data-cy="inventory-archives-extend-info-form-antibiotic-level-select"
                            @enter="enterEvent"
                        >
                            <abc-option
                                v-for="it in options.restrictLevel"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>

                        <abc-input
                            v-model.number="currentAntibioticDDD"
                            :width="53"
                            type="number"
                            :config="{
                                formatLength: 3, max: 9999999,
                            }"
                            data-cy="inventory-archives-extend-info-form-antibiotic-ddd-input"
                            :input-custom-style="{ textAlign: 'center' }"
                            :disabled="disabled"
                            @enter="enterEvent"
                        >
                        </abc-input>

                        <select-usage
                            v-model="currentUnitOfAntibiotic"
                            type="antibioticUnit"
                            class="select-usage-unit"
                            :disabled="disabled"
                            style="width: 52px;"
                            data-cy="inventory-archives-extend-info-form-antibiotic-unit-select"
                            placement="bottom-start"
                            @enter="enterEvent"
                        >
                        </select-usage>
                    </abc-space>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentBaseMedicineType'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-select
                        v-model="currentBaseMedicineType"
                        :width="206"
                        :disabled="disabled"
                        clearable
                        data-cy="inventory-archives-extend-info-form-base-medicine-type-select"
                        @enter="enterEvent"
                    >
                        <abc-option
                            v-for="it in options.baseDrug"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentMha'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-autocomplete
                        v-model.trim="currentMha"
                        :width="206"
                        :inner-width="206"
                        :fetch-suggestions="item.fetchSuggestions"
                        async-fetch
                        focus-show
                        :disabled="disabled"
                        :max-length="50"
                        data-cy="inventory-archives-extend-info-form-mha-input"
                        @enterEvent="handleSelectManufacturer"
                        @enter="enterEvent"
                    >
                        <template slot="suggestions" slot-scope="props">
                            <dt
                                class="suggestions-item"
                                :class="{ selected: props.index == props.currentIndex }"
                                @click="handleSelectManufacturer(props.suggestion)"
                            >
                                <div class="ellipsis" :title="props.suggestion.manufacturerFull">
                                    {{ props.suggestion.manufacturerFull }}
                                </div>
                            </dt>
                        </template>
                    </abc-autocomplete>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentMaintainType'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-select
                        v-model="currentMaintainType"
                        :width="206"
                        :disabled="disabled"
                        clearable
                        data-cy="inventory-archives-extend-info-form-maintain-type-select"
                        @enter="enterEvent"
                    >
                        <abc-option
                            v-for="it in options.conserve"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                        ></abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentStorage'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <storage-select
                        v-model="currentStorage"
                        :popover-config="{
                            placement: 'bottom-start'
                        }"
                        :input-config="{
                            placeholder: '',
                            width: 206,
                            clearable: true,
                            disabled: disabled
                        }"
                        :panel-style="{
                            width: '206px',
                        }"
                        data-cy="inventory-archives-extend-info-form-storage-select"
                        :suggestions="storageTypeLabels"
                        :show-search-icon="false"
                        @enter="enterEvent"
                    >
                    </storage-select>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentShelfLife'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model.number="currentShelfLife"
                        :width="206"
                        :disabled="disabled"
                        :max-length="6"
                        type="number"
                        :input-custom-style="{ textAlign: 'center' }"
                        data-cy="inventory-archives-extend-info-form-shelf-life-input"
                        @enter="enterEvent"
                    >
                        <div slot="appendInner" :style="`color: ${$store.state.theme.style.T3}`">
                            <span>个月</span>
                        </div>
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentMedicineNmpnExpiryDate'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-date-picker
                        v-model="currentMedicineNmpnExpiryDate"
                        type="daterange"
                        :width="206"
                        :disabled="disabled"
                        placeholder=""
                        data-cy="inventory-archives-extend-info-form-medicine-nmpn-expiry-date-picker"
                    >
                    </abc-date-picker>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentMedicineDosageForm'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-space is-compact>
                        <abc-select
                            v-model="currentMedicineDosageForm"
                            :width="206"
                            :disabled="disabled"
                            clearable
                            :max-height="210"
                            data-cy="inventory-archives-base-info-form-medicine-dosage-select"
                            with-search
                            :fetch-suggestions="fetchDosageFormTypeSuggestions"
                            @enter="enterEvent"
                        >
                            <abc-option
                                v-for="it in dosageFormTypeRenderList"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-space>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentDosage'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-space is-compact>
                        <abc-input
                            v-model="currentMedicineDosageNum"
                            :width="143"
                            :max-length="100"
                            type="number"
                            :config="{
                                formatLength: 5,
                                max: 10000000
                            }"
                            :disabled="disabled"
                            @enter="enterEvent"
                        >
                        </abc-input>
                        <abc-input
                            v-model="currentMedicineDosageUnit"
                            :width="62"
                            :max-length="100"
                            type="text"
                            :disabled="disabled"
                            @enter="enterEvent"
                        >
                        </abc-input>
                    </abc-space>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentMedicineNmpn'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model="currentMedicineNmpn"
                        :width="206"
                        :max-length="100"
                        type="text"
                        :disabled="disabled"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentPosition'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <div slot="labelTips" style="width: 260px;">
                        建议统一柜号格式及位数，便于在盘点、清斗等业务中，根据柜号顺序高效工作
                    </div>
                    <abc-input
                        v-model="currentPosition"
                        :width="206"
                        :max-length="10"
                        type="text"
                        data-cy="inventory-archives-extend-info-form-position-input"
                        placeholder="例：01-02-27"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentBusinessScope'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <business-scope-cascader
                        v-model="currentBusinessScope"
                        class="pharmacology-type-cascader"
                        :disabled="disabled"
                        data-cy="inventory-archives-extend-info-form-business-scope-cascader"
                    >
                    </business-scope-cascader>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentPieceWeight'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-space is-compact>
                        <abc-input
                            v-model.number="currentPieceWeight"
                            :width="143"
                            type="number"
                            :config="{
                                formatLength: 2,
                                max: 9999999,
                                min: 0
                            }"
                            :disabled="disabled"
                            data-cy="inventory-archives-extend-info-form-piece-weight-input"
                            @enter="enterEvent"
                        >
                        </abc-input>
                        <abc-input
                            value="克"
                            :width="62"
                            tabindex="-1"
                            readonly
                            :disabled="disabled"
                            :input-custom-style="{
                                color: 'var(--abc-color-T3)',
                            }"
                        >
                        </abc-input>
                    </abc-space>
                </abc-form-item>

                <abc-form-item
                    v-else-if="item.prop === 'currentMark'"
                    :label="item.label"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                >
                    <abc-input
                        v-model="currentMark"
                        :width="206"
                        :disabled="disabled"
                        :max-length="20"
                        type="text"
                        data-cy="inventory-archives-extend-info-form-mark-input"
                        @enter="enterEvent"
                    >
                    </abc-input>
                </abc-form-item>
            </abc-col>
        </template>
    </abc-row>
</template>


<script>
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import {
        GoodsModelOptions,
        IngredientArr,
        IngredientObj,
        StorageTypeLabels,
    } from 'views/common/inventory/constants';
    import EnterEvent from 'views/common/enter-event';
    import SelectUsage from 'views/layout/select-group/index.vue';
    import MultipleUsage from 'views/inventory/goods/archives/components/multipleUsage.vue';
    import StorageSelect from '@/components/input-selectable-panel/index.vue';
    import { mapGetters } from 'vuex';
    import {
        hiddenPositionKey, modifyGoodsItemKey, disabledBaseInfoKey,
    } from 'views/inventory/goods/archives/provideKeys';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import GoodsAPI from 'api/goods';
    import { isEqual } from 'utils/lodash';
    import CdssAPI from 'api/cdss';
    import {
        isNotNull, isNull,
    } from '@/utils';
    import BusinessScopeCascader from 'views/inventory/goods/archives/components/businessScopeCascader.vue';
    import useBusinessScope from 'views/inventory/goods/archives/hook/useBusinessScope';
    import useDictionary, {
        CatalogueEnum,
    } from '@/hooks/business/use-dictionary';
    import TraceCode, { TraceableCodeTypeEnum } from '@/service/trace-code/service';


    export default {
        name: 'ExtendInfo',
        components: {
            BusinessScopeCascader,
            SelectUsage,
            MultipleUsage,
            StorageSelect,
        },
        mixins: [EnterEvent],
        inject: {
            hiddenPosition: {
                from: hiddenPositionKey,
                default: false,
            },
            modifyGoodsItem: {
                from: modifyGoodsItemKey,
                default: null,
            },
            // 手动控制部分字段的禁用
            disabledBaseInfo: {
                from: disabledBaseInfoKey,
                default: false,
            },
        },
        props: {
            goodsInfo: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            dosageFormTypeList: {
                type: Array,
                default: () => ([]),
            },
            errorTraceCodeTip: String,
            selectedClinicId: String,
            isMedicineDisinfectant: Boolean,
            isMedicineMaterial: Boolean,
            isGoodsMaterial: Boolean,
            isChineseMedicineType: Boolean,
            isChineseWesternPatentType: Boolean,
            isFixedOrLogisticsMaterial: Boolean,
        },
        setup() {
            const {
                init,
                getBusinessScopeName,
                getDefaultBusinessScopeObj,
            } = useBusinessScope(CatalogueEnum.BUSINESS_SCOPE_GOODS);

            const { getDictionaryByCatalogueName } = useDictionary({ GoodsAPIV3 });

            return {
                init,
                getBusinessScopeName,
                getDefaultBusinessScopeObj,
                getDictionaryByCatalogueName,
            };
        },
        data() {
            return {
                options: GoodsModelOptions,
                errorIdentificationCodeTip: '',
                lastIdentificationCode: null,
                isShowMask: false,
                traceCodeType: TraceableCodeTypeEnum.HAS_CODE,// 有码
                identificationCode: '',
                cacheGoodsTagIdList: [],
                dosageFormTypeKey: '',
                dosageFormTypes: [],
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin', 'isSingleStore', 'currentPharmacy', 'multiPharmacyCanUse']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            dosageFormTypeRenderList() {
                return this.dosageFormTypeList.filter((item) => {
                    return item.label.includes(this.dosageFormTypeKey);
                });
            },
            showGoodsInfoPositionField() {
                return this.viewDistributeConfig.Inventory.showGoodsInfoPositionField;
            },
            isSupportBusinessScopeInGoodsArchives() {
                return this.viewDistributeConfig.Inventory.isSupportBusinessScopeInGoodsArchives;
            },
            isSupportConvertArchiveFieldName() {
                return this.viewDistributeConfig.Inventory.isSupportConvertArchiveFieldName;
            },
            // 南宁中药会多几个可配置字段
            isShowNanningField() {
                return this.$abcSocialSecurity.region === 'guangxi_nanning' && this.isChineseMedicineType && this.isSupportConvertArchiveFieldName;
            },
            // 南京中药会多几个可配置字段
            isShowNanjingField() {
                return this.$abcSocialSecurity.region === 'jiangsu_nanjing' && this.isChineseMedicineType && this.isSupportConvertArchiveFieldName;
            },
            // 山东济南中药饮片会多几个可配置字段
            isShowJinanField() {
                return this.$abcSocialSecurity.region === 'shandong_jinan' && this.isChineseMedicineType;
            },
            currentType() {
                return +this.goodsInfo.type;
            },
            currentSubType() {
                return +this.goodsInfo.subType;
            },
            // 当前包装单位
            currentPieceUnit() {
                return this.goodsInfo.pieceUnit;
            },
            // 一级分类
            currentTypeId: {
                get() {
                    return this.goodsInfo.typeId;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'typeId', v);
                    // this.$emit('updateGoodsInfo', 'customTypeId', '');
                },
            },
            currentName: {
                get() {
                    return this.goodsInfo.name;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'name', v);
                },
            },
            // 中西成药-通用名
            currentMedicineCadn: {
                get() {
                    return this.goodsInfo.medicineCadn;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'medicineCadn', v);
                },
            },
            // 医疗器械分类
            currentDeviceType: {
                get() {
                    return this.goodsInfo.deviceType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'deviceType', v);
                },
            },
            // 处方/OTC
            currentOtcType: {
                get() {
                    return this.goodsInfo.otcType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'otcType', v);
                },
            },
            // !精麻毒放/麻黄碱
            currentDangerIngredient: {
                get() {
                    const ingredient = this.goodsInfo.dangerIngredient;
                    if (ingredient) {
                        return IngredientArr.filter((item) => ingredient & item);
                    }
                    return [];
                },
                set(v) {
                    // 分为存储多选值
                    const val = v.reduce((res, item) => res | item, 0);
                    this.$emit('updateGoodsInfo', 'dangerIngredient', val);
                },
            },
            // !抗菌药物级别
            currentAntibioticLevel: {
                get() {
                    return this.goodsInfo.antibiotic;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'antibiotic', v);
                },
            },
            // !抗菌药物DDD值
            currentAntibioticDDD: {
                get() {
                    return this.goodsInfo.dddOfAntibiotic;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'dddOfAntibiotic', v);
                },
            },
            // !抗菌药物DDD值单位
            currentUnitOfAntibiotic: {
                get() {
                    return this.goodsInfo.unitOfAntibiotic;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'unitOfAntibiotic', v);
                },
            },
            // !!基药
            currentBaseMedicineType: {
                get() {
                    return this.goodsInfo.baseMedicineType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'baseMedicineType', v);
                },
            },
            // !上市许可持有人
            currentMha: {
                get() {
                    return this.goodsInfo.mha;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'mha', v);
                },
            },
            // !养护分类
            currentMaintainType: {
                get() {
                    return this.goodsInfo.maintainType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'maintainType', v);
                },
            },
            // !存储条件
            currentStorage: {
                get() {
                    return this.goodsInfo.storage;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'storage', v);
                },
            },
            // !保质期（月）
            currentShelfLife: {
                get() {
                    return this.goodsInfo.shelfLife;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'shelfLife', v);
                },
            },
            // !批准文号有效期
            currentMedicineNmpnExpiryDate: {
                get() {
                    const {
                        medicineNmpnEndExpiryDate, medicineNmpnStartExpiryDate,
                    } = this.goodsInfo;
                    if (medicineNmpnEndExpiryDate && medicineNmpnStartExpiryDate) {
                        return [
                            new Date(medicineNmpnStartExpiryDate),
                            new Date(medicineNmpnEndExpiryDate),
                        ];
                    }
                    return [];
                },
                set(selectDate) {
                    const [startDate, endDate] = selectDate || [];

                    this.$emit('updateGoodsInfo', 'medicineNmpnStartExpiryDate', startDate);
                    this.$emit('updateGoodsInfo', 'medicineNmpnEndExpiryDate', endDate);
                },
            },
            // 柜号
            currentPosition: {
                get() {
                    return this.goodsInfo.position;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'position', v);
                },
            },
            // 备注
            currentMark: {
                get() {
                    return this.goodsInfo.remark;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'remark', v);
                },
            },
            // 所属经营范围
            currentBusinessScope: {
                get() {
                    return this.goodsInfo.businessScopeList || [];
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'businessScopeList', v);
                },
            },
            // 剂型
            currentMedicineDosageForm: {
                get() {
                    return this.goodsInfo.dosageFormType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'dosageFormType', v);
                },
            },
            // 剂量
            currentMedicineDosageNum: {
                get() {
                    return this.goodsInfo.medicineDosageNum;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'medicineDosageNum', v);
                },
            },
            // 剂量单位
            currentMedicineDosageUnit: {
                get() {
                    return this.goodsInfo.medicineDosageUnit;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'medicineDosageUnit', v);
                },
            },
            // 国字准号
            currentMedicineNmpn: {
                get() {
                    return this.goodsInfo.medicineNmpn;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'medicineNmpn', v);
                },
            },
            // 单包重量（山东济南特有字段）
            currentPieceWeight: {
                get() {
                    return this.goodsInfo.pieceWeight;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'pieceWeight', v);
                },
            },
            // 是药品类型
            isMedicineType() {
                return this.currentType === GoodsTypeEnum.MEDICINE;
            },
            // 是否支持录入标识码
            isSupportTraceCode() {
                return TraceCode.isSupportTraceCode(this.currentTypeId);
            },
            storageTypeLabels() {
                return StorageTypeLabels;
            },
            renderData() {

                return [
                    {
                        prop: 'currentBusinessScope',
                        label: '所属经营范围',
                        order: 10,
                        help: this.getHelpText('businessScopeList', (v) => {
                            if (v?.length) {
                                return `审批中：${this.getBusinessScopeName(v)}`;
                            }
                            return '审批中：删除“所属经营范围”';
                        }),
                        helpTheme: 'warning',
                        hidden: !this.isSupportBusinessScopeInGoodsArchives,
                    },
                    {
                        prop: 'currentDeviceType',
                        label: '医疗器械分类',
                        order: 10,
                        hidden: !this.isMedicineMaterial,
                        help: this.getHelpText('deviceType', (v) => {
                            const label = this.options.deviceType.find((item) => item.value === v)?.label;
                            return isNull(label) ? '审批中：删除“医疗器械分类”' : `审批中：${label}`;
                        }),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentOtcType',
                        label: '处方药/OTC',
                        order: 10,
                        hidden: !this.isMedicineType,
                        help: this.getHelpText('otcType', (v) => {
                            const label = this.options.otcType.find((item) => item.value === v)?.label;
                            return isNull(label) ? '审批中：删除“处方药/OTC”' : `审批中：${label}`;
                        }),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentDangerIngredient',
                        label: '精麻毒放丨麻黄碱',
                        order: 10,
                        hidden: !this.isChineseWesternPatentType,
                        help: this.getHelpText('dangerIngredient', (v) => {
                            const arr = IngredientArr.filter((item) => v & item);
                            return arr?.length ? `审批中：${arr.map((e) => IngredientObj[e]).join(' ')}` : '审批中：删除“精麻毒放丨麻黄碱”';
                        }),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentAntibioticLevel',
                        label: '抗菌药物丨DDD值',
                        order: 10,
                        hidden: !this.isChineseWesternPatentType,
                        help: (() => {

                            const hasModify = this.modifyGoodsItem?.hasOwnProperty('antibiotic') ||
                                this.modifyGoodsItem?.hasOwnProperty('dddOfAntibiotic') ||
                                this.modifyGoodsItem?.hasOwnProperty('unitOfAntibiotic');

                            const antibiotic = this.modifyGoodsItem?.antibiotic || this.goodsInfo.antibiotic || '';
                            const dddOfAntibiotic = this.modifyGoodsItem?.dddOfAntibiotic || this.goodsInfo.dddOfAntibiotic || '';
                            const unitOfAntibiotic = this.modifyGoodsItem?.unitOfAntibiotic || this.goodsInfo.unitOfAntibiotic || '';

                            if (hasModify && this.disabled) {
                                const label = this.options.restrictLevel.find((item) => item.value === antibiotic)?.label ?? '';
                                return `${isNull(label) ? '审批中：删除“抗菌药物”' : `审批中：${label}`}${dddOfAntibiotic}${unitOfAntibiotic}`;
                            }
                            return '';


                        })(),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentBaseMedicineType',
                        label: '基药',
                        order: 10,
                        hidden: !this.isMedicineType,
                        help: this.getHelpText('baseMedicineType', (v) => {
                            const label = this.options.baseDrug.find((item) => item.value === v)?.label;
                            return isNull(label) ? '审批中：删除“基药”' : `审批中：${label}`;
                        }),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentMha',
                        label: '上市许可持有人',
                        order: 10,
                        fetchSuggestions: this.isChineseWesternPatentType ? this.searchByManufacturer : this.searchByManufacturerFull,
                        hidden: !(this.isMedicineType || this.isMedicineMaterial || this. isMedicineDisinfectant),
                        help: this.getHelpText('mha', null, '上市许可持有人'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentMaintainType',
                        label: '养护分类',
                        order: 10,
                        hidden: !(this.isMedicineType || this.isMedicineMaterial || this. isMedicineDisinfectant),
                        help: (() => {
                            const maintainTypeName = this.getHelpText('maintainType', (v) => {
                                const label = this.options.conserve.find((item) => item.value === v)?.label;
                                return isNull(label) ? '审批中：删除“养护分类”' : `审批中：${label}`;
                            });

                            if (maintainTypeName) {
                                return `${maintainTypeName}`;
                            }

                            return '';
                        })(),
                        helpTheme: 'warning',
                    },

                    {
                        prop: 'currentStorage',
                        label: '存储条件',
                        order: 10,
                        hidden: !(this.isMedicineType || this.isMedicineMaterial || this. isMedicineDisinfectant),
                        help: this.getHelpText('storage', null, '存储条件'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentShelfLife',
                        label: '保质期',
                        order: 10,
                        hidden: !(this.isMedicineType || this.isMedicineMaterial || this. isMedicineDisinfectant),
                        help: this.getHelpText('shelfLife', null, '保质期'),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentMedicineNmpnExpiryDate',
                        label: '批准文号有效期',
                        order: 10,
                        hidden: this.isChineseMedicineType || (this.currentTypeId === GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS || this.currentTypeId === GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL),
                        help: (() => {
                            const {
                                medicineNmpnEndExpiryDate, medicineNmpnStartExpiryDate,
                            } = this.modifyGoodsItem || {};
                            const {
                                medicineNmpnEndExpiryDate: endDate,
                                medicineNmpnStartExpiryDate: startDate,
                            } = this.goodsInfo || {};

                            // 有修改-展示完整时间
                            if (this.disabled && this.modifyGoodsItem?.hasOwnProperty('medicineNmpnEndExpiryDate') || this.modifyGoodsItem?.hasOwnProperty('medicineNmpnStartExpiryDate')) {
                                if (!medicineNmpnEndExpiryDate && !medicineNmpnStartExpiryDate) {
                                    return '审批中：删除“批准文号有效期”';
                                }
                                return `审批中：${medicineNmpnStartExpiryDate || startDate} ~ ${medicineNmpnEndExpiryDate || endDate}`;

                            }

                            return '';
                        })(),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentMedicineDosageForm',
                        label: '剂型',
                        order: 10,
                        hidden: !(this.isShowNanningField || this.isShowNanjingField),
                    },
                    {
                        prop: 'currentDosage',
                        label: '剂量',
                        order: 10,
                        hidden: !this.isShowNanningField,
                    },
                    {
                        prop: 'currentMedicineNmpn',
                        label: '国药准字（批准文号）',
                        order: 10,
                        hidden: !(this.isShowNanningField || this.isShowNanjingField),
                    },
                    {
                        prop: 'currentPieceWeight',
                        label: `单${this.currentPieceUnit}重量`,
                        order: 10,
                        hidden: !(this.isShowJinanField && this.currentPieceUnit && this.currentPieceUnit !== 'g' && this.currentPieceUnit !== '克'),
                        help: this.getHelpText('pieceWeight', null, `单${this.currentPieceUnit}重量`),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentPosition',
                        label: '柜号',
                        order: 10,
                        hidden: this.hiddenPosition || this.isFixedOrLogisticsMaterial,
                    },
                ].filter((item) => {
                    if (typeof item.hidden === 'function') {
                        return !item.hidden();
                    }
                    return !item.hidden;
                }).sort((a, b) => a.order - b.order);
            },
        },
        methods: {
            fetchDosageFormTypeSuggestions(key) {
                this.dosageFormTypeKey = key;
            },
            getHelpText(key, formatFn, label = '') {
                if (this.disabled && this.modifyGoodsItem && key && this.modifyGoodsItem?.hasOwnProperty(key)) {
                    // 修改
                    const val = this.modifyGoodsItem[key];
                    const originVal = this.goodsInfo[key];
                    // 没变化不提示
                    if (val === originVal) return '';
                    // 变化了自定义提示
                    if (formatFn) return formatFn(val);

                    return isNotNull(val) ? `审批中：${val}` : `审批中：删除“${label}”`;
                }
                return '';
            },
            createSearchManufacturerParams(keyword) {
                if (this.currentType === GoodsTypeEnum.MEDICINE) {
                    return {
                        client: 'medicine-manufacturer',
                        key_word: keyword,
                        cadn: this.currentMedicineCadn,
                        tradeName: this.currentName,
                    };
                }
                return {
                    client: 'material-manufacturer',
                    key_word: keyword,
                    registered_name: this.currentName,
                };
            },
            // 这个字段同厂家
            handleSelectManufacturer(value) {
                this.currentMha = value.manufacturerFull;
            },
            /**
             * @desc  查询厂家信息
             * <AUTHOR>
             * @date 2018/11/01 09:56:34
             */
            async searchByManufacturer(keyword, next) {
                try {
                    const fetchParams = this.createSearchManufacturerParams(keyword);
                    const { data } = await GoodsAPI.searchProducts(fetchParams);
                    const afterParams = this.createSearchManufacturerParams(keyword);
                    if (isEqual(fetchParams, afterParams)) {
                        const result = data?.hits ?? [];
                        return next(result.map((item) => {
                            return {
                                ...item,
                                manufacturerFull: item.medicine_manufacturer || item.manufacturerFull,
                            };
                        }));
                    }
                    next([]);
                } catch (e) {
                    console.error(e);
                    next([]);
                }
            },
            /**
             * @desc  物资查询厂家信息
             * <AUTHOR>
             * @date 2018/11/01 09:56:34
             */
            async searchByManufacturerFull(keyword, next) {
                try {
                    const fetchParams = this.createSearchManufacturerParams(keyword);
                    const { data } = await CdssAPI.doSearch(fetchParams);
                    const afterParams = this.createSearchManufacturerParams(keyword);
                    if (isEqual(fetchParams, afterParams)) {
                        const result = data?.hits ?? [];
                        return next(result.map((item) => {
                            return {
                                name: item.registered_name,
                                type: item.type,
                                subType: item.subType || 1,
                                manufacturerFull: item.manufacturer || item.medicine_manufacturer,
                                certificateName: item.registered_name,
                                medicineNmpn: item.registered_no,
                            };
                        }));
                    }
                    next([]);
                } catch (e) {
                    console.error(e);
                    next([]);
                }
            },
        },
    };
</script>
