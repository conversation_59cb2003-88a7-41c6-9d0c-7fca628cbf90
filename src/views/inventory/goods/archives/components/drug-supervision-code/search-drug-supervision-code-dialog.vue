<template>
    <abc-dialog
        v-if="showDialog"
        ref="searchCenterCodeDialog"
        v-model="showDialog"
        title="药监对码 (成药)"
        :show-header-border-bottom="false"
        size="huge"
        append-to-body
        :auto-focus="autoFocus"
        :header-style="{
            background: 'var(--abc-color-B7)',
            minHeight: '40px'
        }"
        content-styles="height: 598px;padding: 0 0;"
        class="search-drug-supervision-code_dialog"
        :close-on-click-modal="true"
    >
        <abc-layout>
            <abc-section>
                <abc-flex
                    style="height: 56px; padding: 0 24px 12px; background-color: var(--abc-color-B7);"
                    align="center"
                >
                    <abc-space size="small" align="center">
                        <abc-input
                            v-model="keyword"
                            size="medium"
                            clearable
                            placeholder="通用名 / 通用名简称"
                            :width="360"
                            @input="_fetchSuggestionHandler"
                        >
                            <abc-icon
                                slot="prepend"
                                icon="n-search-line"
                                color="#B89B7E"
                                size="16"
                            ></abc-icon>
                        </abc-input>
                        <abc-input
                            :value="currentSpec"
                            readonly
                            size="medium"
                            :width="176"
                            placeholder="规格"
                        >
                        </abc-input>
                        <abc-autocomplete
                            v-model="currentManufacture"
                            :width="176"
                            size="medium"
                            :delay-time="0"
                            :async-fetch="true"
                            :fetch-suggestions="fetchManufacturerData"
                            :max-length="40"
                            focus-show
                            :auto-focus-first="false"
                            clearable
                            placeholder="厂商"
                            focus-placeholder="搜索厂商"
                            @clear="handleClearManufacturer"
                            @input="_fetchSuggestionHandler"
                            @enterEvent="handleSelectManufacturer"
                        >
                            <template slot="suggestions" slot-scope="props">
                                <dt
                                    class="suggestions-item"
                                    :class="{ selected: props.index == props.currentIndex }"
                                    @click="handleSelectManufacturer(props.suggestion)"
                                >
                                    <div :title="props.suggestion.label">
                                        {{ props.suggestion.label }}
                                    </div>
                                </dt>
                            </template>
                        </abc-autocomplete>
                        <abc-autocomplete
                            v-model="currentApprovedCode"
                            :width="176"
                            size="medium"
                            :delay-time="0"
                            :async-fetch="true"
                            :fetch-suggestions="fetchApprovedCodeData"
                            :max-length="40"
                            focus-show
                            :auto-focus-first="false"
                            clearable
                            placeholder="国药准字"
                            focus-placeholder="搜索国准"
                            @input="_fetchSuggestionHandler"
                            @clear="handleClearApprovedCode"
                            @enterEvent="handleSelectApprovedCode"
                        >
                            <template slot="suggestions" slot-scope="props">
                                <dt
                                    class="suggestions-item"
                                    :class="{ selected: props.index == props.currentIndex }"
                                    @click="handleSelectApprovedCode(props.suggestion)"
                                >
                                    <div :title="props.suggestion.label">
                                        {{ props.suggestion.label }}
                                    </div>
                                </dt>
                            </template>
                        </abc-autocomplete>
                    </abc-space>
                </abc-flex>
            </abc-section>
            <abc-section v-if="!showResult" style="margin-top: 0; border-top: 1px solid #eaedf1;">
                <abc-flex style="width: 100%; height: 22px; margin-top: 160px;" align="center" justify="space-around">
                    <abc-text theme="gray">
                        搜索通用名、批准文号等信息匹配药监码
                    </abc-text>
                </abc-flex>
                <abc-flex style="width: 100%; height: 22px;" align="center" justify="space-around">
                    <abc-text theme="gray">
                        如无药监码可设置为<abc-link @click="notDrugSupervisionCodeHandle">
                            无码
                        </abc-link>
                    </abc-text>
                </abc-flex>
            </abc-section>
            <abc-section
                v-else
                style="padding: 0 0; margin-top: 0;"
                :style="{
                    borderTop: showTips ? '1px solid #eaedf1' : 'none',
                }"
            >
                <abc-flex
                    v-if="showTips"
                    style="height: 36px;"
                    align="center"
                    justify="space-around"
                >
                    <abc-text theme="warning">
                        输入更多搜索条件可以进一步精准搜索结果
                    </abc-text>
                </abc-flex>
                <abc-table
                    :render-config="renderConfig"
                    :loading="loading"
                    style="border-right: none !important; border-left: none !important; border-radius: 0 !important;"
                    :style="{
                        height: showTips ? '448px' : '486px',
                    }"
                    :data-list="filterViewList"
                    :hidden-table-header="true"
                    show-hover-tr-bg
                    @handleClickTr="handleClickTr"
                >
                    <template #cadn="{ trData: item }">
                        <abc-table-cell
                            style="display: block; width: 313px; min-width: 313px; max-width: 313px; height: 56px; padding: 8px 8px 0 24px;"
                        >
                            <abc-flex align="center" style=" width: 291px; height: 20px; line-height: 20px;">
                                <abc-text
                                    :title="item.cadn"
                                    style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                                    :style="{ 'max-width': item.medicalFeeGrade ? '265px' : '291px' }"
                                >
                                    {{ item.cadn }}
                                </abc-text>
                                <abc-tag-v2
                                    v-if="currentDrugSupervisionInfo && item.centerCode === currentDrugSupervisionInfo.centerCode"
                                    variant="outline"
                                    size="mini"
                                    theme="success"
                                    :min-width="64"
                                    style="margin-left: 4px;"
                                >
                                    当前对码
                                </abc-tag-v2>
                            </abc-flex>
                            <abc-flex style="width: 100%; min-width: 291px; margin-top: 2px; line-height: 16px;" align="center">
                                <abc-text
                                    v-abc-title.ellipsis="item.centerCode || ''"
                                    theme="gray"
                                    style="max-width: 291px;"
                                    size="mini"
                                >
                                </abc-text>
                            </abc-flex>
                        </abc-table-cell>
                    </template>
                    <template #spec="{ trData: item }">
                        <abc-table-cell style="height: 56px; padding: 8px 8px;">
                            <abc-flex align="flex-start" style="width: 100%; height: 40px; line-height: 20px;">
                                <abc-text
                                    :title="item.spec || '暂无规格'"
                                    style="max-width: 124px;"
                                    class="search-drug-supervision-code_dialog--text"
                                >
                                    {{ item.spec || '暂无规格' }}
                                </abc-text>
                            </abc-flex>
                        </abc-table-cell>
                    </template>
                    <template #manufacture="{ trData: item }">
                        <abc-table-cell style="height: 56px; padding: 8px 8px;">
                            <abc-flex align="flex-start" style="width: 100%; height: 40px; line-height: 20px;">
                                <abc-text
                                    :title="item.manufacture || ''"
                                    style="max-width: 164px;"
                                    class="search-drug-supervision-code_dialog--text"
                                >
                                    {{ item.manufacture || '暂无产地/厂家' }}
                                </abc-text>
                            </abc-flex>
                        </abc-table-cell>
                    </template>
                    <template #approvedCode="{ trData: item }">
                        <abc-table-cell style="height: 56px; padding: 8px 8px;">
                            <abc-flex align="flex-start" style="width: 134px; height: 40px; line-height: 20px;">
                                <div
                                    :title="item.approvedCode || '暂无国药准字'"
                                    style="max-width: 134px;"
                                    class="search-drug-supervision-code_dialog--text"
                                >
                                    {{ item.approvedCode || '暂无国药准字' }}
                                </div>
                            </abc-flex>
                        </abc-table-cell>
                    </template>
                    <template #adminStandardCode="{ trData: item }">
                        <abc-table-cell style="height: 56px; padding: 8px 8px;">
                            <abc-flex align="flex-start" style="width: 170px; height: 40px; line-height: 20px;">
                                <div
                                    :title="item.adminStandardCode || ''"
                                    style="max-width: 170px;"
                                    class="search-drug-supervision-code_dialog--text"
                                >
                                    本位码：{{ item.adminStandardCode || '暂无本位码' }}
                                </div>
                            </abc-flex>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-section>
            <abc-flex
                v-if="showResult"
                style="width: 100%; height: 56px; padding: 0 16px;"
                align="center"
                justify="space-between"
            >
                <abc-space :size="4">
                    <abc-button variant="text" @click="notDrugSupervisionCodeHandle">
                        设置为无药监码
                    </abc-button>
                </abc-space>
            </abc-flex>
        </abc-layout>
    </abc-dialog>
</template>

<script>
    import { debounce } from 'utils/lodash';
    import GoodsAPI from 'api/goods';

    export default {
        name: 'SearchDrugSupervisionCodeDialog',
        props: {
            value: Boolean,
            productInfo: {
                type: Object,
                required: true,
            },
            autoFocus: {
                type: Boolean,
                default: true,
            },
            showSearch: {
                type: Boolean,
                default: false,
            },
            currentDrugSupervisionInfo: {
                type: [Object, null],
                default: null,
            },
            selectDrugSupervisionInfo: {
                type: Function,
                default: () => {},
            },
            notDrugSupervisionCode: {
                type: Function,
                default: () => {},
            },
            // 默认的keywords
            defaultKeyword: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                keyword: '',
                currentApprovedCode: null, // 当前国准
                currentApprovedCodeOptions: [],
                currentSpec: null, // 当前规格
                currentManufacture: null, // 当前厂商
                currentManufacturerOptions: [],
                panelPagination: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                list: [],
                loading: false,
                disallowed: new RegExp(/[\u4E00-\u9FA5\uF900-\uFA2D]/),
                init: true,
            };
        },
        computed: {
            showTips() {
                return !(this.keyword && this.currentSpec && this.currentManufacture && this.currentApprovedCode);
            },
            renderConfig() {
                return {
                    list: [
                        {
                            key: 'cadn',
                            label: '通用名',
                            style: {
                                minWidth: '280px',
                                maxWidth: '280px',
                                textAlign: 'left',
                            },
                            pinned: true,
                        },
                        {
                            key: 'spec',
                            label: '规格',
                            style: {
                                minWidth: '140px',
                                maxWidth: '140px',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'manufacture',
                            label: '厂商',
                            style: {
                                minWidth: '160px',
                                maxWidth: '160px',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'approvedCode',
                            label: '国药准字',
                            style: {
                                minWidth: '150px',
                                maxWidth: '150px',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'adminStandardCode',
                            label: '本位码',
                            style: {
                                minWidth: '170px',
                                flex: 1,
                                textAlign: 'left',
                            },
                        },
                    ],
                };
            },
            filterViewList() {
                return this.list.filter((item) => {
                    let flag = true;
                    if (item.centerCode === this.currentDrugSupervisionInfo?.centerCode) {
                        flag = true;
                    }
                    return flag;
                });
            },
            showResult() {
                return this.showSearch || !!this.keyword || !!this.currentApprovedCode || !!this.currentManufacture;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            spec() {
                return this.productInfo?.pieceUnit && this.productInfo?.packageUnit ? `${this.productInfo?.pieceNum || ''} ${this.productInfo?.pieceUnit}/${this.productInfo?.packageUnit}` : '';
            },
            name() {
                return this.productInfo.medicineCadn || this.productInfo.name;
            },
            manufacture() {
                return this.productInfo.manufacturerFull || '';
            },
            approvedCode() {
                return this.productInfo.medicineNmpn || this.productInfo.approvedCode;
            },
            centerCode() {
                return this.productInfo.centerCode || '';
            },
        },
        created() {
            this._fetchSuggestionHandler = debounce(this.fetchSuggestionHandler, 200, true);
        },
        mounted() {
            if (this.currentDrugSupervisionInfo || this.showSearch) {
                this.keyword = this.defaultKeyword || this.name || '';
                this.currentSpec = this.spec || '';
                this.currentManufacture = this.manufacture || '';
                this.currentApprovedCode = this.approvedCode || '';
                this.init = false;
                this._fetchSuggestionHandler();
            }
        },
        methods: {
            fetchManufacturerData(searchKey, callback) {
                if (!searchKey) {
                    return callback(this.currentManufacturerOptions);
                }
                return callback(this.currentManufacturerOptions.filter((option) => option.label.includes(searchKey)));
            },
            handleSelectManufacturer(item) {
                this.currentManufacture = item.value;
                this._fetchSuggestionHandler();
            },
            handleClearManufacturer() {
                this.currentManufacture = '';
                this._fetchSuggestionHandler();
            },
            fetchApprovedCodeData(searchKey, callback) {
                if (!searchKey) {
                    return callback(this.currentApprovedCodeOptions);
                }
                return callback(this.currentApprovedCodeOptions.filter((option) => option.label.includes(searchKey)));
            },
            handleSelectApprovedCode(item) {
                this.currentApprovedCode = item.value;
                this._fetchSuggestionHandler();
            },
            handleClearApprovedCode() {
                this.currentApprovedCode = '';
                this._fetchSuggestionHandler();
            },
            async fetchDrugSupervisionCodeDetail(item) {
                this.selectDrugSupervisionInfo(item);
                this.closeDialog();
            },
            closeDialog() {
                this.showDialog = false;
                this.$refs.searchCenterCodeDialog?.close();
            },
            async handleClickTr(item) {
                await this.fetchDrugSupervisionCodeDetail(item);
            },
            notDrugSupervisionCodeHandle() {
                this.notDrugSupervisionCode();
                this.closeDialog();
            },
            handleOptions(list, key) {
                // 去重
                const handleList = Array.from(new Set(list.map((it) => {
                    return it[key];
                })))?.filter((i) => {
                    return !!i;
                }) || [];
                return handleList?.map((it) => {
                    return {
                        label: it,
                        value: it,
                    };
                }) || [];
            },
            initOptions(list) {
                this.currentApprovedCodeOptions = this.handleOptions(list, 'approvedCode');
                this.currentManufacturerOptions = this.handleOptions(list, 'manufacture');
            },
            async fetchSuggestionHandler() {
                this.loading = true;
                this.panelPagination.pageIndex = 0;
                const params = {
                    keyword: this.keyword,

                    cadn: this.name,
                    centerCode: '', // 中心码
                    adminStandardCode: this.adminStandardCode || '', // 本位码

                    manufacture: this.currentManufacture, // 生产厂家
                    approvedCode: this.currentApprovedCode, // 批准文号
                };
                try {
                    const { data } = await GoodsAPI.fetchDrugSupervisionCodeDetail(params);
                    this.list = data?.rows || [];
                    // 有选中的选项
                    const currentCenterCode = this.currentDrugSupervisionInfo?.centerCode;
                    // 带了当前选项，需要提示出来
                    if (currentCenterCode) {
                        if (this.list.find((item) => {
                            return item.centerCode === currentCenterCode;
                        })) {
                            const firstList = this.list.filter((item) => {
                                return item.centerCode === currentCenterCode;
                            }) || [];
                            this.list = firstList.concat(this.list.filter((item) => {
                                return item.centerCode !== currentCenterCode;
                            }));
                        } else {
                            this.list = [{ ...this.currentDrugSupervisionInfo }].concat(this.list);
                        }
                    }
                    this.init = false;
                    this.initOptions(this.filterViewList);
                } catch (e) {
                    console.error('搜索社保对码失败', e);
                } finally {
                    this.loading = false;
                }
            },
        },
    };
</script>
<style lang="scss" scoped>
@import 'src/styles/abc-common.scss';

.search-drug-supervision-code_dialog {
    ::v-deep {
        .abc-dialog-headerbtn {
            background-color: var(--abc-color-B7);

            &:hover {
                background-color: var(--abc-color-B7);
            }
        }
    }

    &--input {
        height: 40px;
        background-color: transparent !important;

        ::v-deep .abc-input-medium-wrapper {
            background-color: transparent !important;
        }

        ::v-deep .prepend-input {
            width: 32px !important;
        }

        ::v-deep .abc-input__inner {
            height: 40px;
            padding-left: 32px !important;
            line-height: 40px;
            background-color: transparent !important;
            border: none !important;
            box-shadow: none !important;

            &:focus {
                border: none !important;
                box-shadow: none !important;
            }

            &::placeholder {
                color: #b89b7e;
            }

            &:not([disabled]):not(.is-disabled):not([readonly]):not(.is-readonly):focus {
                border: none !important;
                box-shadow: none !important;
            }
        }
    }

    &--autocomplete {
        height: 40px;
        background-color: transparent !important;

        ::v-deep .abc-input-medium-wrapper {
            background-color: transparent !important;
        }

        ::v-deep .abc-input__inner {
            height: 40px;
            line-height: 40px;
            background-color: transparent !important;
            border: none !important;
            box-shadow: none !important;

            &:focus {
                border: none !important;
                box-shadow: none !important;
            }

            &:not([disabled]):not(.is-disabled):not([readonly]):not(.is-readonly):focus {
                border: none !important;
                box-shadow: none !important;
            }

            &::placeholder {
                color: #b89b7e;
            }
        }
    }

    &--text {
        height: 40px;
        line-height: 20px;

        @include ellipsis(2);
    }

    &-popover {
        &-button {
            display: flex;
            align-items: center;
            height: 40px;
            padding: 0 8px;
            margin-top: 2px;
            border-radius: var(--abc-border-radius-small);

            &-manual {
                &:hover {
                    background: transparent !important;
                }
            }

            &:first-child {
                margin-top: 0;
            }

            &:hover {
                background: var(--abc-color-cp-grey4);
            }
        }
    }
}
</style>
