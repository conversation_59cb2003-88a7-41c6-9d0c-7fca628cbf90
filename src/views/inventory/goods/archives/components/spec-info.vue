<template>
    <div class="spec-wrapper">
        <div class="spec-content">
            <!--中西成药剂量模式-->
            <div v-if="isChineseWesternPatent" class="docs-wrap">
                <abc-dropdown
                    custom-class="spec-dropdown-wrapper"
                    placement="bottom-start"
                    :disabled="disabled"
                    data-cy="inventory-archives-spec-info-form-dropdown"
                    @change="onSpecTypeChange"
                >
                    <div slot="reference" class="dropdown-content">
                        剂量{{ subTitle }}
                        <img
                            width="14"
                            height="14"
                            draggable="false"
                            src="~assets/images/<EMAIL>"
                            alt=""
                        />
                    </div>
                    <abc-dropdown-item :value="SpecType.Dose" data-cy="inventory-archives-spec-info-form-item-dose">
                        <div class="dropdown-item">
                            <span>剂量(成分含量)</span><br />
                            <span class="sub-title">如：吗丁啉多潘立酮片 10mg*42粒/盒</span>
                        </div>
                    </abc-dropdown-item>
                    <abc-dropdown-item :value="SpecType.Capacity" data-cy="inventory-archives-spec-info-form-item-capacity">
                        <div class="dropdown-item">
                            <span>剂量(容量：成分含量)</span><br />
                            <span class="sub-title">如：硫酸阿托品注射液 1ml:0.5mg*10支/盒</span>
                        </div>
                    </abc-dropdown-item>
                </abc-dropdown>

                <div class="goods-dosage">
                    <template v-if="specType === SpecType.Capacity">
                        <abc-form-item
                            :help="getHelpMedicineContentText"
                            help-theme="warning"
                        >
                            <abc-space is-compact>
                                <abc-input
                                    ref="dosage"
                                    v-model="currentComponentContentNum"
                                    :width="54"
                                    :disabled="disabled"
                                    type="number"
                                    :config="{
                                        formatLength: 5,
                                        max: 10000000
                                    }"
                                    :input-custom-style="{ textAlign: 'center' }"
                                    data-cy="inventory-archives-spec-info-form-component-dosage-input"
                                    @enter="enterEvent"
                                    @blur="$emit('blurInput','componentContentNum')"
                                >
                                </abc-input>

                                <select-usage
                                    v-model="currentComponentContentUnit"
                                    type="dosageUnit"
                                    class="select-usage-unit small"
                                    :disabled="disabled"
                                    placement="bottom-start"
                                    data-cy="inventory-archives-spec-info-form-component-dosage-select"
                                    @enter="enterEvent"
                                    @change="$emit('blurInput','componentContentUnit')"
                                >
                                </select-usage>
                            </abc-space>
                        </abc-form-item>

                        <div class="spec-symbol" style="width: 12px; margin-top: 0;">
                            :
                        </div>
                    </template>

                    <abc-form-item
                        :help="getHelpMedicineText"
                        help-theme="warning"
                    >
                        <abc-space is-compact>
                            <abc-input
                                ref="dosage"
                                v-model="currentMedicineDosageNum"
                                :width="specType === SpecType.Capacity ? 54 : 143"
                                :disabled="disabled"
                                type="number"
                                :config="{
                                    formatLength: 5,
                                    max: 10000000
                                }"
                                data-cy="inventory-archives-spec-info-form-medicine-dosage-input"
                                :input-custom-style="{ textAlign: 'center' }"
                                @enter="enterEvent"
                                @blur="$emit('blurInput','medicineDosageNum')"
                            >
                            </abc-input>

                            <select-usage
                                v-model="currentMedicineDosageUnit"
                                type="dosageUnit"
                                :disabled="disabled"
                                class="select-usage-unit"
                                :class="specType === SpecType.Capacity ? 'small' : ''"
                                placement="bottom-start"
                                data-cy="inventory-archives-spec-info-form-medicine-dosage-select"
                                @enter="enterEvent"
                                @change="$emit('blurInput','medicineDosageUnit')"
                            >
                            </select-usage>
                        </abc-space>
                    </abc-form-item>
                </div>
            </div>

            <abc-form-item
                v-else
                :label="currentExtendSpecName"
                :help="getHelpText('materialSpec', null, currentExtendSpecName)"
                help-theme="warning"
            >
                <abc-input
                    v-model.trim="currentMaterialSpec"
                    :disabled="disabled"
                    :width="206"
                    data-cy="inventory-archives-spec-info-form-material-spec-input"
                    @enter="enterEvent"
                >
                </abc-input>
            </abc-form-item>

            <div class="spec-symbol">
                *
            </div>

            <abc-space is-compact>
                <abc-form-item
                    :label="currentPieceUnitName"
                    required
                    class="form-item-label--float"
                    :help="getHelpText('pieceNum', null, '最小包装数量')"
                    help-theme="warning"
                >
                    <abc-input
                        v-model.number="currentPieceNum"
                        :width="143"
                        type="number"
                        :input-custom-style="{ textAlign: 'center' }"
                        :disabled="disabled || usePieceUnitFlag"
                        :config="{ max: 100000000 }"
                        data-cy="inventory-archives-spec-info-form-piece-num-input"
                        @enter="enterEvent"
                        @blur="$emit('blurInput','pieceNum')"
                    >
                        <template v-if="usePieceUnitFlag" #appendInner>
                            <abc-tooltip
                                v-if="isSupportReCreateArchive"
                                placement="top"
                                content="无修改档案权限，请联系总部开启权限"
                                :disabled="!disabledEditButton"
                            >
                                <div>
                                    <abc-button
                                        icon="s-b-edited-line"
                                        size="small"
                                        variant="text"
                                        :disabled="disabledEditButton"
                                        @click="$emit('iconClick', 'pieceNum')"
                                    >
                                    </abc-button>
                                </div>
                            </abc-tooltip>

                            <template
                                v-if="!isSupportReCreateArchive && isAdmin"
                            >
                                <abc-button
                                    icon="s-b-edited-line"
                                    size="small"
                                    variant="text"
                                    @click="$emit('copyArchive')"
                                >
                                </abc-button>
                            </template>
                        </template>
                    </abc-input>
                </abc-form-item>

                <abc-form-item
                    label="单位"
                    required
                    :validate-event="unitEqual"
                    class="form-item-label--hidden"
                    :help="getHelpText('pieceUnit', null, '单位')"
                    help-theme="warning"
                    :error="unitErrorInfo"
                >
                    <select-usage
                        ref="pieceUnitInput"
                        v-model="currentPieceUnit"
                        :type="usageUnit"
                        class="select-usage-unit"
                        :disabled="disabled"
                        :extra-piece-unit="shebaoPieceUnit"
                        :extra-package-unit="shebaoPackageUnit"
                        placement="bottom-start"
                        data-cy="inventory-archives-spec-info-form-piece-unit-select"
                        @enter="enterEvent"
                        @change="$emit('blurInput','pieceUnit')"
                    >
                    </select-usage>
                </abc-form-item>
            </abc-space>

            <div class="spec-symbol">
                /
            </div>

            <abc-form-item
                :label="currentPackageUnitName"
                required
                :validate-event="unitEqual"
                :help="getHelpText('packageUnit', null, currentPackageUnitName)"
                help-theme="warning"
                :error="unitErrorInfo"
            >
                <select-usage
                    ref="packageUnitInput"
                    v-model="currentPackageUnit"
                    :type="usageUnit"
                    class="select-usage-unit"
                    style="width: 122px;"
                    :disabled="disabled"
                    :extra-piece-unit="shebaoPieceUnit"
                    :extra-package-unit="shebaoPackageUnit"
                    placement="bottom-start"
                    data-cy="inventory-archives-spec-info-form-package-unit-select"
                    @enter="enterEvent"
                    @change="$emit('blurInput','packageUnit')"
                >
                </select-usage>
            </abc-form-item>
        </div>
        <div class="view-example">
            <abc-button type="text" size="small" @click="isShowMask = true">
                查看示例
            </abc-button>
            <abc-guide-image
                v-model="isShowMask"
                :image="guideImage"
                :image-height="207"
                dom-selector=".spec-wrapper"
                :target-position="{
                    x: 16, y: 131
                }"
            ></abc-guide-image>
        </div>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';

    import { SpecType } from 'views/common/inventory/constants';
    import SelectUsage from 'views/layout/select-group/index.vue';
    import EnterEvent from 'views/common/enter-event';
    import {
        modifyGoodsItemKey, usePieceUnitFlagKey,
    } from 'views/inventory/goods/archives/provideKeys';
    import { GoodsTypeEnum } from '@abc/constants';
    import { isNotNull } from '@/utils';
    import AbcGuideImage from 'components/abc-guide-image/index.vue';
    import WesternMedicineConfig from '@/assets/configure/western-medicine-config.js';

    import GuideImgDose from 'assets/images/img-guide-format-2.png';
    import GuideImgCapacity from 'assets/images/img-guide-format-3.png';
    import GuideImgSpec from 'assets/images/img-guide-format-1.png';
    import { ArchiveLabelManager } from 'views/common/inventory/ArchiveLabelManager';

    export default {
        name: 'SpecInfo',
        components: {
            AbcGuideImage, SelectUsage,
        },
        mixins: [EnterEvent],
        inject: {
            // 控制是否能够快速建档
            usePieceUnitFlag: {
                from: usePieceUnitFlagKey,
                default: false,
            },
            modifyGoodsItem: {
                from: modifyGoodsItemKey,
                default: null,
            },
        },
        props: {
            type: Number,
            subType: Number,
            specType: Number,// 0剂量模式：成分含量，1容量模式：容量:成分含量
            medicineDosageNum: [String, Number],
            medicineDosageUnit: String,
            componentContentNum: [String, Number],
            componentContentUnit: String,
            pieceNum: [String, Number],
            pieceUnit: String,
            packageUnit: String,
            disabled: Boolean,
            dismounting: Boolean,
            materialSpec: String,
            position: String,
            extendSpec: String,
            isChineseWesternPatent: Boolean,
            goodsInfo: Object,
            disabledEditButton: Boolean,
        },
        data() {
            return {
                SpecType,
                isShowMask: false,

                unitErrorInfo: { error: false },
            };
        },
        computed: {
            ...mapGetters(['isAdmin']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isSupportReCreateArchive() {
                return this.viewDistributeConfig.Inventory.isSupportReCreateArchive;
            },
            isSupportConvertArchiveFieldName() {
                return this.viewDistributeConfig.Inventory.isSupportConvertArchiveFieldName;
            },
            guideImage() {
                if (this.isChineseWesternPatent) {
                    return this.specType === SpecType.Dose ? GuideImgDose : GuideImgCapacity;
                }
                return GuideImgSpec;
            },
            subTitle() {
                if (this.$abcSocialSecurity.region === 'guangxi_nanning' && this.isSupportConvertArchiveFieldName) {
                    return '';
                }
                return this.specType === SpecType.Dose ? '(成分含量)' : '(容量:成分含量)';
            },
            usageUnit() {
                if (+this.type === GoodsTypeEnum.MATERIAL || +this.type === GoodsTypeEnum.GOODS) {
                    return 'materialUnit';
                }
                return 'dosageFormUnit';
            },
            currentMedicineDosageNum: {
                get() {
                    return this.medicineDosageNum;
                },
                set(val) {
                    this.$emit('update:medicineDosageNum', val);
                },
            },
            currentMedicineDosageUnit: {
                get() {
                    return this.medicineDosageUnit;
                },
                set(val) {
                    this.$emit('update:medicineDosageUnit', val);
                },
            },
            currentComponentContentNum: {
                get() {
                    return this.componentContentNum;
                },
                set(val) {
                    this.$emit('update:componentContentNum', val);
                },
            },
            currentComponentContentUnit: {
                get() {
                    return this.componentContentUnit;
                },
                set(val) {
                    this.$emit('update:componentContentUnit', val);
                },
            },
            currentPieceNum: {
                get() {
                    return this.pieceNum;
                },
                set(val) {
                    this.$emit('update:pieceNum', val);
                },
            },
            currentPieceUnit: {
                get() {
                    return this.goodsInfo.pieceUnit;
                },
                set(val) {
                    this.$emit('update:pieceUnit', val);
                },
            },
            currentPackageUnit: {
                get() {
                    return this.goodsInfo.packageUnit;
                },
                set(val) {
                    this.$emit('update:packageUnit', val);
                },
            },
            shebaoPieceUnit() {
                return this.goodsInfo.shebao?.shebaoPieceUnit;
            },
            shebaoPackageUnit() {
                return this.goodsInfo.shebao?.shebaoPackageUnit;
            },
            currentMaterialSpec: {
                get() {
                    return this.materialSpec;
                },
                set(val) {
                    this.$emit('update:materialSpec', val);
                },
            },
            getHelpMedicineText() {
                const num = this.getHelpText('medicineDosageNum',null,'成分含量');
                const unit = this.getHelpText('medicineDosageUnit',null,'成分含量单位');
                const {
                    medicineDosageNum, medicineDosageUnit,
                } = this.modifyGoodsItem || {};
                if (medicineDosageNum && medicineDosageUnit) {
                    return `审批中：${medicineDosageNum || ''}${medicineDosageUnit || ''}`;
                }
                return `${num} ${unit}`;
            },
            getHelpMedicineContentText() {
                const num = this.getHelpText('componentContentNum',null,'容量');
                const unit = this.getHelpText('componentContentUnit',null,'容量单位');
                const {
                    componentContentNum, componentContentUnit,
                } = this.modifyGoodsItem || {};
                if (componentContentNum && componentContentUnit) {
                    return `审批中：${componentContentNum || ''}${componentContentUnit || ''}`;
                }
                return `${num} ${unit}`;

            },
            archiveLabelManager() {
                return new ArchiveLabelManager(this.goodsInfo);
            },
            currentExtendSpecName() {
                const { region } = this.$abcSocialSecurity;
                const label = this.archiveLabelManager.getLabel('currentExtendSpec', { region });
                return label;
            },
            currentPieceUnitName() {
                const { region } = this.$abcSocialSecurity;
                const label = this.archiveLabelManager.getLabel('currentPieceUnit', { region });
                return `最小包装数量丨${label}`;
            },
            currentPackageUnitName() {
                const { region } = this.$abcSocialSecurity;
                const label = this.archiveLabelManager.getLabel('currentPackageUnit', { region });
                return label;
            },
        },
        watch: {
            currentPieceNum() {
                this.validateUnit(this.currentPieceUnit, this.currentPackageUnit);
            },
            currentPieceUnit() {
                this.validateUnit(this.currentPieceUnit, this.currentPackageUnit);
            },
            currentPackageUnit() {
                this.validateUnit(this.currentPieceUnit, this.currentPackageUnit);
            },
        },
        methods: {
            // 剂量模式变化
            onSpecTypeChange(val) {
                this.$emit('update:specType', val);
                this.$emit('blurInput', 'specType');
            },
            unitEqual(_, next) {
                return this.dismounting ?
                    next({
                        validate: this.currentPackageUnit !== this.currentPieceUnit,
                        message: '支持拆零时，最小单位和包装单位不能相同',
                    }) :
                    next({ validate: true });
            },
            validateUnit(pieceUnit, packageUnit) {
                let pieceIndex = -1;
                let packageIndex = -1;
                WesternMedicineConfig.unitLevel.forEach((group, index) => {
                    if (group.includes(pieceUnit) && pieceIndex === -1) pieceIndex = index;
                    if (group.includes(packageUnit)) packageIndex = index;
                });
                if (this.currentPieceNum === 1 || pieceIndex === -1 || packageIndex === -1 || pieceIndex < packageIndex) {
                    this.unitErrorInfo = { error: false };
                } else {
                    this.unitErrorInfo = {
                        error: true,
                        message: `包装规格错误：${this.currentPieceNum || ''}${pieceUnit}/${packageUnit}。请确定单位是否设置错误，或者单位是否填反`,
                    };
                }
            },

            getHelpText(key, formatFn, label = '') {
                if (this.modifyGoodsItem && key && this.modifyGoodsItem?.hasOwnProperty(key)) {
                    // 修改
                    const val = this.modifyGoodsItem[key];
                    const originVal = this.goodsInfo?.[key];
                    // 没变化不提示
                    if (val === originVal) return '';
                    // 变化了自定义提示
                    if (formatFn) return formatFn(val);

                    return isNotNull(val) ? `审批中：${val}` : `审批中：删除“${label}”`;
                }
                return '';
            },
        },
    };
</script>
