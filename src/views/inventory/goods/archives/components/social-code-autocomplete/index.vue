<template>
    <div
        class="social-goods-info-wrapper"
        :class="{
            'shebao-goods-info-right': placement === 'right',
            'is-uesed-in-form': isUsedInForm
        }"
    >
        <abc-autocomplete
            id="social-code-autocomplete"
            ref="socialAutocomplete"
            v-model="keyword"
            v-abc-focus-selected
            :width="width"
            :inner-width="innerWidth"
            :max-height="autoCompleteMaxHeight"
            :placeholder="curPlaceholder"
            async-fetch
            focus-show
            :disabled="disabled"
            custom-class="social-code-autocomplete-popover"
            :fetch-suggestions="fetchSuggestionHandler"
            resident-sugguestions
            @enterEvent="selectedSuggestion"
            @input="inputHandler"
            @focus="focusHandler"
            @blur="blurHandler"
        >
            <div
                slot="append"
                style=" position: absolute; right: 0; display: flex; align-items: center; padding: 0 8px;"
            >
                <span
                    v-if="currentSocialInfo?.medicalFeeGrade"
                    style="margin-right: 4px; font-size: 14px; color: #000000;"
                >
                    [{{ currentSocialInfo.medicalFeeGrade | medicalFeeGrade2Str }}]
                </span>
                <span
                    v-if="currentSocialInfo?.shebaoCode"
                    style="font-size: 12px; color: #7a8794;"
                >
                    {{ currentSocialInfo.shebaoCode }}
                </span>
            </div>

            <template
                slot="suggestions"
                slot-scope="{
                    suggestion, index, currentIndex
                }"
            >
                <dt
                    v-if="!isEmptyObject(suggestion)"
                    v-abc-shebao-popver="{
                        socialInfo: suggestion,
                        shebaoCodeType: shebaoCodeType,
                        goodsType: type,
                        openDelay: 500
                    }"
                    class="suggestions-item suggestions-code-item"
                    :class="{ selected: index === currentIndex && curCode !== DISABLED }"
                    @mousedown="selectedSuggestion(suggestion)"
                >
                    <shebao-code-detail
                        :shebao-code-info="suggestion"
                        scene-type="search"
                        :product-info="productInfo"
                        :type="type"
                        :shebao-code-type="shebaoCodeType"
                    ></shebao-code-detail>
                </dt>
            </template>
            <div
                v-if="showDisabledShebao"
                slot="suggestion-fixed-footer"
                style="height: 84px; line-height: 40px; cursor: pointer; border-top: 1px solid #dadbe0;"
                @click.stop
            >
                <div
                    class="suggestions-item"
                    @mousedown="notAllowSocialPay"
                >
                    不刷医保/暂无编码
                </div>
                <div v-if="isManualAdd" class="diagnosis-footer">
                    <abc-input v-model="manualInput" :disallowed="disallowed" placeholder="请输入医保编码"></abc-input>
                    <abc-button
                        type="primary"
                        :disabled="btnDisabled"
                        style="margin-left: 30px;"
                        @click="submit"
                    >
                        保存
                    </abc-button>
                    <abc-button type="blank" @click.stop="cancel">
                        取消
                    </abc-button>
                </div>
                <div v-else class="suggestions-item" @mousedown="isManualAdd = true">
                    手动添加医保码
                </div>
            </div>
        </abc-autocomplete>

        <div v-if="currentSocialInfo" class="shebao-display-detail">
            <shebao-code-detail
                v-abc-shebao-popver="{
                    socialInfo: currentSocialInfo,
                    shebaoCodeType: shebaoCodeType,
                    goodsType: type,
                }"
                :product-info="productInfo"
                :type="type"
                :shebao-code-info="currentSocialInfo"
                scene-type="codeDetail"
                :shebao-code-type="shebaoCodeType"
            ></shebao-code-detail>
            <div v-if="isDictExpired" class="warn-price">
                即将在 {{ shebaoDictInfo.shebaoCodeCurrentEndDate | parseTime('y-m-d') }} 失效
            </div>
        </div>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import {
        isEmptyObject, isNull,
    } from 'utils/lodash';

    const ShebaoCodeDetail = () => import('views/inventory/components/social-code-autocomplete/shebao-code-detail.vue');

    import GoodsAPI from 'api/goods/index';
    import AbcShebaoPopver from './directive/shebao-popper.js';
    import clone from '@/utils/clone.js';

    import {
        SocialCodeTypeEnum, DISABLED,
    } from 'views/inventory/components/social-code-autocomplete/constant.js';

    export default {
        name: 'SocialCodeAutocomplete',
        directives: {
            AbcShebaoPopver,
        },
        components: {
            ShebaoCodeDetail,
        },
        props: {
            value: {
                validator: (prop) => typeof prop === 'string' || prop === null,
            },
            codeId: {
                validator: (prop) => typeof prop === 'string' || prop === null,
            },
            width: {
                type: Number,
                default: 436,
            },
            height: {
                type: Number,
                default: 26,
            },
            // 社保type 1 A类 正常社保， 2 B类 杭州省医保 3 社保国家对码
            shebaoCodeType: {
                type: Number,
                default: SocialCodeTypeEnum.cityCode,
            },
            placeholder: {
                type: String,
                default: '-',
            },
            initialPlaceholder: {
                type: String,
                default: null,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            innerWidth: {
                type: Number,
                default: 436,
            },
            noIcon: {
                type: Boolean,
                default: false,
            },
            type: {
                type: String,
                default: 'goods',
                validator: (prop) => {
                    return ['goods', 'diagnosis-treatment', 'process'].includes(prop);
                },
            },
            productInfo: {
                type: Object,
                required: true,
            },
            defaultKeyword: {
                type: String,
                default: '',
            },
            placement: {
                type: String,
                default: 'bottom',
                validator: (prop) => {
                    return ['right', 'bottom'].includes(prop);
                },
            },
            // 是否是作为输入放在abc-form中
            isUsedInForm: {
                type: Boolean,
                default: false,
            },
            showDisabledShebao: {
                type: Boolean,
                default: true,
            },
            noChangeWidth: {
                type: Number,
                default: 120,
            },
        },
        data() {
            return {
                DISABLED,
                SocialCodeTypeEnum,

                keyword: '',
                cacheKeyword: '',
                manualInput: '',
                currentSocialInfo: null,
                isSelected: true,
                isFocused: false,
                isManualAdd: false,
                isSelectedTop: false,
                curPlaceholder: this.initialPlaceholder || '-',
                isFirstFocus: false, // 第一次聚焦的时候，搜索关键字为 code
                disallowed: new RegExp(/[\u4E00-\u9FA5\uF900-\uFA2D]/),
                autoCompleteMaxHeight: 90,
            };
        },
        computed: {
            codeTypeName() {
                if (this.shebaoCodeType === SocialCodeTypeEnum.cityCode) {
                    return '市编码';
                }
                if (this.shebaoCodeType === SocialCodeTypeEnum.provinceCode) {
                    return '省编码';
                }
                return '国家码';
            },
            showEditIcon() {
                return !this.noIcon && !this.disabled && this.placement !== 'right';
            },
            // 诊疗项目相关
            isTreatment() {
                return this.type === 'diagnosis-treatment';
            },
            isGoods() {
                return this.type === 'goods';
            },
            /**
             * warningDictExpired 医保目录失效提醒
             * warningDaysInAdvance 过期天数提醒
             */
            ...mapState('socialPc', [
                'warningDictExpired',
                'warningDictExpiredProvince',
                'warningDictExpiredNational',
                'warningDaysInAdvance']),
            getWarningDictExpired() {
                if (this.shebaoCodeType === SocialCodeTypeEnum.cityCode) {
                    return this.warningDictExpired;
                }
                if (this.shebaoCodeType === SocialCodeTypeEnum.provinceCode) {
                    return this.warningDictExpiredProvince;
                }
                if (this.shebaoCodeType === SocialCodeTypeEnum.nationalCode) {
                    return this.warningDictExpiredNational;
                }
                return false;
            },
            /**
             * @desc 是否展示医保目录失效提醒
             */
            isDictExpired() {
                if (!this.getWarningDictExpired) return false;
                if (!this.shebaoDictInfo.shebaoCodeCurrentEndDate) return false;
                const warnDays = this.warningDaysInAdvance;
                const endDate = this.shebaoDictInfo.shebaoCodeCurrentEndDate;
                const days = this.calcDays(endDate);
                return days <= warnDays;
            },
            goodsType() {
                return this.productInfo.type;
            },
            goodsSubType() {
                return this.productInfo.subType;
            },
            name() {
                return this.productInfo.medicineCadn || this.productInfo.name;
            },
            manufacturer() {
                return this.productInfo.manufacturerFull || '';
            },
            shebaoDictInfo() {
                return this.currentSocialInfo.shebaoDictInfo || {};
            },
            btnDisabled() {
                return !this.manualInput;
            },
            curCode: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },

        },
        watch: {
            value: {
                handler() {
                    if (!this.value) {
                        this.currentSocialInfo = null;
                    }
                    if (this.isManualAdd || this.value === DISABLED) {
                        this.currentSocialInfo = null;
                        if (this.value === DISABLED) {
                            this.keyword = '不刷医保/暂无编码';
                            this.cacheKeyword = this.keyword;
                        }
                        return;
                    }
                    if (this.value && this.value !== DISABLED) {
                        this.fetchSocialCodeDetail(this.value, this.codeId, this.goodsType, this.goodsSubType, this.shebaoCodeType);
                    }
                },
                immediate: true,
            },
            isFocused(val) {
                if (!val && this.$refs?.socialAutocomplete) {
                    this.$refs?.socialAutocomplete?.handleClose();
                }
            },
        },
        mounted() {
            // eslint-disable-next-line abc/no-timer-id
            setTimeout(() => {
                const {
                    top, bottom,
                } = this.$refs?.socialAutocomplete?.$el?.getBoundingClientRect() ?? {};

                const topHeight = Math.floor(top);
                const bottomHeight = Math.floor(window.innerHeight - bottom);

                const maxHeight = Math.min(topHeight, bottomHeight);

                console.log('剩余空间', maxHeight, topHeight, bottomHeight);
                if (maxHeight) {
                    this.autoCompleteMaxHeight = Math.max((maxHeight - 84 - 32), 90);// 84为固定底部按钮，90是至少保留一条数据展示
                    console.log('滚动最大高度', this.autoCompleteMaxHeight);
                }
            });
        },
        methods: {
            isNull,
            isEmptyObject,
            calcDays(endDate) {
                const start = new Date();
                const end = new Date(endDate);
                return (end - start) / (1000 * 60 * 60 * 24);
            },
            /**
             * @desc 获取社保code的详情
             * <AUTHOR>
             */
            async fetchSocialCodeDetail(code, codeId, goodsType, goodsSubType, shebaoCodeType = SocialCodeTypeEnum.cityCode) {
                try {
                    // 哈尔滨省码不请求对码详情
                    if (this.$abcSocialSecurity.config.isHeilongjiangHarbin && this.shebaoCodeType === SocialCodeTypeEnum.provinceCode) return;
                    const { data } = await GoodsAPI.fetchSocialCodeDetail({
                        code,
                        goodsType,
                        goodsSubType,
                        shebaoCodeType,
                        codeId,
                    });
                    if (data) {
                        this.currentSocialInfo = isEmptyObject(data) ? null : data;
                        this.curCode = data && data.shebaoCode || '';
                        this.keyword = this.currentSocialInfo ? (this.currentSocialInfo.medicineCadn || this.currentSocialInfo.name) : '';
                        this.cacheKeyword = this.keyword;
                    } else {
                        this.currentSocialInfo = null;
                        this.curCode = code;
                        this.keyword = code;
                        this.cacheKeyword = this.keyword;
                    }
                } catch (e) {
                    console.warn('获取社保编码详情失败', e);
                }
            },
            /**
             * @desc 获取社保编码的下拉建议项
             */
            async fetchSuggestionHandler(key, callback) {
                let shebaoCode = '';
                // 没有subType type 不请求对码
                if (!this.goodsType || isNull(this.goodsSubType)) {
                    callback([]);
                    return false;
                }
                /**
                 * @desc 第一次聚焦的时候，如果有社保编码，通过社保编码去搜索，key 为空
                 */
                if (this.isFirstFocus && this.curCode !== DISABLED) {
                    shebaoCode = this.curCode;
                    key = '';

                }
                if (this.curCode === DISABLED && (!key || key === DISABLED)) {
                    key = '';
                }
                try {
                    const { data } = await GoodsAPI.searchSocialCode({
                        keyword: key || this.defaultKeyword,
                        goodsType: this.goodsType,
                        goodsSubType: this.goodsSubType,
                        shebaoCodeType: this.shebaoCodeType,
                        name: this.name,
                        manufacturer: this.manufacturer,
                        cMSpec: this.productInfo?.cMSpec || undefined,
                        shebaoCode,
                    });
                    if (data?.goodsList?.length) {
                        callback(data.goodsList);
                    } else {
                        callback([]);
                        this.$nextTick(() => {
                            if (this.$refs?.socialAutocomplete?.$refs?.suggestions?.updatePopper) {
                                this.$refs.socialAutocomplete.$refs.suggestions.updatePopper();
                            }
                        });
                    }
                } catch (e) {
                    console.warn('获取社保编码建议项', e);
                }
            },
            /**
             * @desc 选择建议项
             */
            selectedSuggestion(suggestion) {
                if (suggestion && !isEmptyObject(suggestion)) {
                    this.currentSocialInfo = suggestion;

                    this.keyword = this.currentSocialInfo.medicineCadn || this.currentSocialInfo.name;
                    this.cacheKeyword = this.keyword;
                    this.curCode = this.currentSocialInfo.shebaoCode;

                    const curProductInfo = clone(this.productInfo);
                    curProductInfo.name = this.currentSocialInfo?.name || '';
                    this.$emit('update:productInfo', curProductInfo);
                    this.$emit('change', this.currentSocialInfo, this.shebaoCodeType);
                    const shebaoCodeId = this.currentSocialInfo ? this.currentSocialInfo.shebaoCodeId : null;
                    this.$emit('update:codeId', shebaoCodeId);

                    this.isSelected = true;
                    this.isFocused = false;

                    // 把选中的社保goods抛出来
                    this.$emit('select', suggestion);
                }
            },
            notAllowSocialPay() {
                this.keyword = '不刷医保/暂无编码';
                this.cacheKeyword = this.keyword;
                this.$emit('input', DISABLED);
                this.$emit('update:codeId', '');
                this.isFocused = false;
            },
            /**
             * @desc 检测是否为用户自己输入，如果是自己输入，则在失焦的时候清空数据
             */
            blurHandler() {
                if (this.isManualAdd || this.isSelectedTop) {
                    return;
                }
                if (!this.isSelected) {
                    if (!this.keyword) {
                        // 如果用户是删除了所有内容，则清空关键字
                        this.keyword = '';
                        this.cacheKeyword = '';
                        this.currentSocialInfo = null;
                        this.curCode = '';
                        this.$emit('change', this.currentSocialInfo, this.shebaoCodeType);
                    } else {
                        this.keyword = this.cacheKeyword;
                        this.$emit('change', this.currentSocialInfo, this.shebaoCodeType);
                    }
                    const shebaoCodeId = this.currentSocialInfo ? this.currentSocialInfo.shebaoCodeId : null;
                    this.$emit('update:codeId', shebaoCodeId);
                }
                this.curPlaceholder = this.initialPlaceholder || '-';
                this.isFocused = false;
                this.$emit('blur');
            },
            focusHandler() {
                this.isFocused = true;
                this.isFirstFocus = true;
                this.isManualAdd = false;
                this.curPlaceholder = this.placeholder || '-';
            },
            submit() {
                this.keyword = this.manualInput;
                this.cacheKeyword = this.keyword;
                this.$emit('input', this.manualInput);
                this.$emit('update:codeId', '');
                this.isFocused = false;
            },
            cancel() {
                this.manualInput = '';
                this.isManualAdd = false;
            },
            /**
             * @desc 点击聚焦后，切换到autocompelete搜索
             * <AUTHOR>
             */
            detailFocusHandler() {
                if (this.disabled) return;

                this.isFocused = true;
                if (this.currentSocialInfo) {
                    this.keyword = this.currentSocialInfo.medicineCadn || this.currentSocialInfo.name;
                    this.cacheKeyword = this.keyword;
                }
                this.$nextTick(() => {
                    if (this.$refs.socialAutocomplete) {
                        $(this.$refs.socialAutocomplete.$el).find('input').focus();
                    }
                });
            },
            inputHandler() {

                this.isFirstFocus = false;
                if (this.cacheKeyword !== this.keyword) {
                    this.isSelected = false;
                }
            },
        },
    };
</script>

<style lang="scss" scoped>
@import 'src/styles/theme.scss';

.social-goods-info-wrapper {
    width: 100%;
    max-width: 100%;

    // 输入框禁用样式修改
    .abc-autocomplete-wrapper.is-disabled {
        ::v-deep .append-input {
            background: none !important;
        }
    }

    &.shebao-goods-info-right {
        display: flex;
        align-items: center;

        .shebao-display-detail {
            margin-left: 16px;
        }

        .shebao-code-detail-wrapper {
            display: flex;
            flex-wrap: wrap;
            max-width: 450px;
            border-color: transparent;
        }
    }

    &.is-uesed-in-form {
        .shebao-display-info .goods-info {
            height: 32px;
            padding: 2px 6px 3px;
            font-size: 14px;
            border-color: #dadbe0;
            border-radius: var(--abc-border-radius-small);
        }

        .abc-input__inner {
            height: 32px;
        }
    }

    .abc-input__inner {
        height: 26px;
    }

    .shebao-display-info {
        display: flex;
        align-items: center;

        &:hover {
            .goods-info {
                border-color: $theme3;
                border-radius: var(--abc-border-radius-small);
            }

            .goods-info.is-disabled {
                border-color: transparent;
            }
        }

        .goods-info {
            display: flex;
            align-items: center;
            max-width: 100%;
            height: 26px;
            padding: 4px 6px;
            font-size: 12px;
            line-height: 16px;
            border: 1px solid transparent;
        }

        .small-font {
            min-width: 16px;
            font-size: 12px;
            color: #96a4b3;
        }
    }
}

.social-item {
    &:hover {
        background-color: $P3;
    }
}

.suggestions-wrapper .suggestions-code-item.suggestions-item {
    padding: 4px 8px;
    line-height: 18px;

    &.selected {
        .shebao-code-detail-wrapper {
            color: #ffffff;

            ::v-deep .goods-name {
                color: whitesmoke !important;
            }
        }
    }
}

.suggestions-item {
    &:last-of-type {
        border-bottom: none;
    }
}

.diagnosis-footer {
    padding: 0 10px;
}

.abc-form-item {
    line-height: 1;

    ::v-deep .abc-form-item-label {
        padding: 0 0;
    }

    ::v-deep .abc-input__inner {
        height: 20px;
        line-height: 20px;
        cursor: pointer;
        background: $S2 !important;
        border: 0 !important;
        border-bottom: 1px solid $P1 !important;
        border-radius: 0;
        outline: none !important;
        box-shadow: none !important;

        &:hover {
            cursor: pointer;
            border-bottom: 1px solid #0270c9 !important;
        }

        &:focus {
            border-bottom: 1px solid #0270c9 !important;
            box-shadow: none !important;
        }
    }

    ::v-deep .iconfont {
        right: 0 !important;
    }
}
</style>
