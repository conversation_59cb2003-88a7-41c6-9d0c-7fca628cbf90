import BaseModel from './baseModel';
import {
    GoodsTypeEnum,
    GoodsTypeIdEnum,
    GoodsSubTypeEnum,
} from '@abc/constants';

export default class ChineseMedicineGranuleModel extends BaseModel {
    constructor(obj) {
        super(obj);
        this.assignGoods({
            typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE,
            type: GoodsTypeEnum.MEDICINE,
            subType: GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine,
            medicineCadn: obj.medicineCadn,
            cMSpec: '中药颗粒',
            pieceUnit: obj.pieceUnit || 'g',
            dismounting: 1,
            isSell: 1, // 是否对外销售
            // 智能发药开关
            smartDispense: obj.smartDispense ?? 0,
            // 智能发药颗粒机
            smartDispenseMachineNo: obj.smartDispenseMachineNo,
            //!功效分类
            pharmacologicId: obj.pharmacologicId,
            extendSpec: obj.extendSpec,// 规格
            // !处方/OTC
            otcType: obj.otcType,
            // !基药
            baseMedicineType: obj.baseMedicineType,
            // !上市许可持有人
            mha: obj.mha,
            // !养护分类
            maintainType: obj.maintainType,
            // !存储条件-Number
            storageType: obj.storageType,
            // !存储条件-String
            storage: obj.storage,
            // !保质期（月）
            shelfLife: obj.shelfLife,
            // 是否有码
            // hasTraceableCode: obj.hasTraceableCode ?? 1,
            // 追溯码信息
            traceableCodeNoInfoList: obj.traceableCodeNoInfoList,
            // 颗粒饮片换算当量，默认为1，1g颗粒 = eqCoefficient g饮片
            eqCoefficient: obj.eqCoefficient || 1,
            // 南宁-剂型
            dosageFormType: obj.dosageFormType,
            // 南宁-剂量
            medicineDosageNum: obj.medicineDosageNum,
            medicineDosageUnit: obj.medicineDosageUnit,
            //南宁-国药准字（批准文号）
            medicineNmpn: obj.medicineNmpn,
        });
    }
}
