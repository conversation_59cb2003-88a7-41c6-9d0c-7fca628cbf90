import BaseModel from './baseModel';
import {
    GoodsTypeEnum,
    GoodsTypeIdEnum,
    GoodsSubTypeEnum,
} from '@abc/constants';

export default class NonPrescriptionChineseMedicinePiecesModel extends BaseModel {
    constructor(obj) {
        super(obj);
        this.assignGoods({
            typeId: GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES,
            type: GoodsTypeEnum.MEDICINE,
            subType: GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine,
            medicineCadn: obj.medicineCadn,
            cMSpec: '非配方饮片',
            pieceUnit: obj.pieceUnit || 'g',
            dismounting: 1,
            isSell: 1, // 是否对外销售
            //!功效分类
            pharmacologicId: obj.pharmacologicId,
            extendSpec: obj.extendSpec,// 规格
            // !处方/OTC
            otcType: obj.otcType,
            // !基药
            baseMedicineType: obj.baseMedicineType,
            // !上市许可持有人
            mha: obj.mha,
            // !养护分类
            maintainType: obj.maintainType,
            // !存储条件
            storageType: obj.storageType,
            // !存储条件-String
            storage: obj.storage,
            // !保质期（月）
            shelfLife: obj.shelfLife,
            // 是否有码
            // hasTraceableCode: obj.hasTraceableCode ?? 1,
            // 追溯码信息
            traceableCodeNoInfoList: obj.traceableCodeNoInfoList,
            // 南宁-剂型
            dosageFormType: obj.dosageFormType,
            // 南宁-剂量
            medicineDosageNum: obj.medicineDosageNum,
            // 容量单位
            medicineDosageUnit: obj.medicineDosageUnit,
            //南宁-国药准字（批准文号）
            medicineNmpn: obj.medicineNmpn,
            // 山东济南-单包重量
            pieceWeight: obj.pieceWeight,
        });
    }
}
