<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="title"
        content-styles="padding: 0;"
        custom-top="60px"
        custom-class="update-spec-dialog"
    >
        <div class="dialog-content clearfix">
            <div class="medicine-name">
                {{ goodsDisplayName }}
                <!--{{ newInfo.medicineCadn || newInfo.name || '' }} {{ newInfo.medicineNmpn || '' }}-->
            </div>

            <div class="action">
                {{ displaySpecBefore }} <span> → </span> {{ displaySpecAfter }}
                <!--{{ oldInfo | goodsSpec }} <span> → </span> {{ newInfo | goodsSpec }}-->
            </div>

            <div class="effect-clinics">
                <p>修改后<span style="padding: 0 4px;">{{ effectClinics.length }}</span>家门店库存将发生变化，系统随后会向受影响门店发送盘点提醒</p>
                <ul>
                    <li v-for="item in effectClinics.slice(0,3)" :key="item.id">
                        <span>{{ item.clinic && item.clinic.name }}&emsp;{{ item.originalGoodsStockCount }} → {{ item.afterGoodsStockCount }}</span>
                    </li>
                    <li v-show="effectClinics.length > 3">
                        <span>...（共{{ effectClinics.length }}家）</span>
                    </li>
                </ul>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <abc-button type="primary" @click="confirm">
                确定修改
            </abc-button>

            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    export default {
        name: 'UpdateSpecDialog',
        props: {
            value: Boolean,
            errorData: Object,
            type: Number,
        },
        data() {
            const {
                clinics, newInfo, oldInfo, effectClinics, goodsDisplayName, displaySpecBefore, displaySpecAfter,
            } = this.errorData;
            return {
                loading: false,
                showDialog: this.value,
                clinics,
                oldInfo,
                newInfo,
                effectClinics,
                goodsDisplayName,
                displaySpecBefore,
                displaySpecAfter,
            };
        },
        computed: {
            title() {
                let str = '';
                if (this.type === 1) {
                    str = '药品';
                }
                if (this.type === 2) {
                    str = '物资';
                }
                if (this.type === 7) {
                    str = '商品';
                }
                return `修改${str}规格`;
            },
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        methods: {
            confirm() {
                this.$emit('confirm');
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import '~styles/theme';
    @import '~styles/mixin';

    .update-spec-dialog {
        width: 580px;

        .medicine-name {
            padding: 0 24px;
            margin-top: 24px;
            font-size: 16px;
            line-height: 20px;
        }

        .action {
            display: flex;
            align-items: center;
            padding: 0 24px;
            margin: 16px 0 24px;
            font-size: 20px;
            line-height: 20px;

            span {
                padding: 0 4px;
                margin-left: 4px;
                font-size: 16px;
                color: #687481;
            }
        }

        .effect-clinics {
            padding: 16px 24px;
            line-height: 1;
            background-color: #f5f7fb;

            span {
                line-height: 20px;
                color: #ff9933;
            }

            li {
                //display: inline-block;
                height: 20px;
                margin: 8px 0 0;
                line-height: 20px;
            }
        }
    }
</style>
