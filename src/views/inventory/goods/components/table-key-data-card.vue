<template>
    <div class="table-key-data-card-wrapper">
        <div class="left">
            <div v-for="(item, index) in leftData" :key="index" class="sign-info-item">
                <span class="sign-info-item_count">
                    {{ item.text }}
                </span>
                <span class="sign-info-item_text">
                    <description-tips
                        v-if="item.tips"
                        color="#b6d7ff"
                        placement="top-start"
                        icon-position="right"
                        :width="item.width"
                    >
                        <span
                            slot="text"
                            class="label"
                            style="font-size: 14px; color: #0090ff;"
                        >{{ item.value }}</span>
                        <span>{{ item.tips }}</span>
                    </description-tips>
                    <span v-else>{{ item.value === '0' ? '0.00' : item.value }}</span>
                </span>
            </div>
        </div>
    </div>
</template>

<script>
    import DescriptionTips from 'views/statistics/common/description-tips/description-tips.vue';
    export default {
        components: {
            DescriptionTips,
        },
        props: {
            list: {
                type: Array,
                default: () => [],
            },
            isReverse: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            leftData() {
                return this.list.filter((item) => item.text !== 'status');
            },
        },

    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.table-key-data-card-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    padding-left: 16px;
    margin-bottom: 8px;
    line-height: 48px;
    background: #e8f3ff;
    border-radius: var(--abc-border-radius-small);

    .left {
        display: flex;
        flex: 1;

        .sign-info-item {
            margin-right: 64px;
            color: $theme2;

            .sign-info-item_count {
                margin-right: 10px;
                font-size: 12px;
            }

            .sign-info-item_text {
                font-size: 22px;
                font-weight: bold;
            }
        }
    }

    .right {
        width: 34px;
        cursor: pointer;
    }
}
</style>
