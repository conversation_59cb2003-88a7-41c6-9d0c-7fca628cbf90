<template>
    <div class="goods-sale-info">
        <div class="sale-content" style="flex-wrap: wrap; gap: 24px;">
            <!-- 门店需要显示总部定价-->
            <template v-if="isChain && !isChainAdmin">
                <abc-form-item label="总部定价" class="goods-chain-price">
                    <abc-input
                        v-if="!isChineseMedicine"
                        v-model="currentChainPackagePrice"
                        :width="130"
                        :config="getPriceConfig"
                        type="money"
                        disabled
                    >
                        <label slot="prepend" class="prepend">
                            <abc-currency-symbol-icon></abc-currency-symbol-icon>
                        </label>
                        <label slot="append">
                            <span v-if="packageUnit">/ {{ packageUnit }}</span>
                        </label>
                    </abc-input>
                    <abc-input
                        v-else
                        v-model="currentChainPiecePrice"
                        :width="130"
                        :config="getPriceConfig"
                        type="money"
                        disabled
                    >
                        <label slot="prepend" class="prepend">
                            <abc-currency-symbol-icon></abc-currency-symbol-icon>
                        </label>

                        <label slot="append">
                            <span v-if="pieceUnit || 'g'">/ {{ pieceUnit || 'g' }}</span>
                        </label>
                    </abc-input>
                </abc-form-item>
            </template>

            <template v-if="+subType === 1 || +subType === 3">
                <div>
                    <abc-form-item
                        ref="packageprice"
                        v-abc-price-reminder="{
                            validate: validatePriceHandle, value: goodsInfo
                        }"
                        :label="isChainAdmin ? '总部定价' : '零售价格'"
                        required
                        class="goods-packageprice"
                        style="margin-bottom: 24px;"
                        :validate-event="validatePackageUnitPrice"
                    >
                        <div v-if="$slots.costprice && isCanSeeGoodsCostInInventory" class="cost-price">
                            <slot name="costprice">
                            </slot>
                        </div>
                        <!--                        <div v-if="isSetPricePower && isDetailMedicine" class="goods-packageprice-set-price">-->
                        <!--                            <template>-->
                        <!--                                <template v-if="showPriceDialog">-->
                        <!--                                    <set-price-dialog-->
                        <!--                                        v-if="showPriceDialog"-->
                        <!--                                        v-model="showPriceDialog"-->
                        <!--                                        :show-sub-set-price="showSubSetPrice"-->
                        <!--                                        :package-unit="packageUnit"-->
                        <!--                                        :piece-unit="pieceUnit"-->
                        <!--                                        :pricetabs="pricetabs"-->
                        <!--                                        :price-label-text="priceLabelText"-->
                        <!--                                        :get-price-config="getPriceConfig"-->
                        <!--                                        :goods-info="goodsCloneInfo"-->
                        <!--                                        @setPriceNumber="setPriceNumber"-->
                        <!--                                    >-->
                        <!--                                    </set-price-dialog>-->
                        <!--                                </template>-->
                        <!--                                <span style="position: relative; font-size: 12px;" @click="openPriceDialog(1)">调价</span>-->
                        <!--                            </template>-->
                        <!--                        </div>-->
                        <div v-show="validatePriceHandle() && currentPackagePrice" class="price-reminder-wrapper">
                            <div class="price-attention-tip">
                                {{ validatePriceHandle() }}
                            </div>
                            <i class="icon iconfont cis-icon-Attention"></i>
                        </div>

                        <abc-popover
                            trigger="hover"
                            style="margin-left: auto;"
                            placement="bottom-end"
                            :open-delay="500"
                            theme="yellow"
                            width="100"
                            :disabled="!tipsText"
                        >
                            <span>{{ tipsText }}</span>
                            <div slot="reference">
                                <abc-input
                                    v-model="currentPackagePrice"
                                    v-abc-focus-selected
                                    :width="130"
                                    type="money"
                                    :disabled="disabledPackagePrice"
                                    :config="getPriceConfig"
                                    @input="calcPackagePrice"
                                    @enter="enterEvent"
                                >
                                    <label slot="prepend" class="prepend">
                                        <abc-currency-symbol-icon></abc-currency-symbol-icon>
                                    </label>
                                    <label slot="append">
                                        <span v-if="packageUnit">/ {{ packageUnit }}</span>
                                    </label>
                                </abc-input>
                            </div>
                        </abc-popover>


                        <div v-if="isChain" class="price-range">
                            <template v-if="showSubSetPrice">
                                <template v-if="isChainAdmin">
                                    <template v-if="currentPackagePrice">
                                        门店自主定价范围：{{ packagePriceLabel }}
                                    </template>
                                </template>
                                <template v-else>
                                    <template>门店自主定价范围：{{ chainPackagePriceRange }}</template>
                                </template>
                            </template>
                            <template v-else>
                                不支持门店自主定价
                            </template>
                        </div>
                    </abc-form-item>
                </div>

                <div class="goods-pieceprice" style="margin-left: 0;">
                    <abc-checkbox
                        v-model="currentDismounting"
                        :disabled="disabled"
                        style="margin-bottom: 4px;"
                        @change="setCurrentDismounting"
                    >
                        允许拆零销售
                    </abc-checkbox>
                    <abc-form-item :required="!!currentDismounting" :validate-event="validatePieceUnitPrice">
                        <!--                        <div-->
                        <!--                            v-if="!(disabledPackagePrice || !currentDismounting) && isDetailMedicine"-->
                        <!--                            class="goods-packageprice-set-price"-->
                        <!--                            style="right: 0;"-->
                        <!--                        >-->
                        <!--                            <template>-->
                        <!--                                <template v-if="showPriceDialog2">-->
                        <!--                                    <set-price-dialog-->
                        <!--                                        v-if="showPriceDialog2"-->
                        <!--                                        v-model="showPriceDialog2"-->
                        <!--                                        :show-sub-set-price="showSubSetPrice"-->
                        <!--                                        :package-unit="packageUnit"-->
                        <!--                                        :piece-unit="pieceUnit"-->
                        <!--                                        :pricetabs="pricetabs"-->
                        <!--                                        :price-label-text="priceLabelText"-->
                        <!--                                        :get-price-config="getPriceConfig"-->
                        <!--                                        :goods-info="goodsCloneInfo"-->
                        <!--                                        @setPriceNumber="setPriceNumber"-->
                        <!--                                    >-->
                        <!--                                    </set-price-dialog>-->
                        <!--                                </template>-->
                        <!--                                <span style="font-size: 12px;" @click="openPriceDialog(2)">调价</span>-->
                        <!--                            </template>-->
                        <!--                        </div>-->

                        <abc-popover
                            trigger="hover"
                            style="margin-left: auto;"
                            placement="bottom-end"
                            theme="yellow"
                            width="100"
                            :open-delay="500"
                            :disabled="!tipsText"
                        >
                            <span v-if="tipsText">{{ tipsText }}</span>
                            <div slot="reference">
                                <abc-input
                                    v-model="currentPiecePrice"
                                    v-abc-focus-selected
                                    :width="130"
                                    type="money"
                                    :config="getPriceConfig"
                                    :disabled="disabledPackagePrice || !currentDismounting "
                                    @enter="enterEvent"
                                >
                                    <label slot="prepend" class="prepend">
                                        <abc-currency-symbol-icon></abc-currency-symbol-icon>
                                    </label>
                                    <label slot="append">
                                        <span v-if="pieceUnit">/ {{ pieceUnit }}</span>
                                    </label>
                                </abc-input>
                            </div>
                        </abc-popover>
                        <div v-if="isChain" class="price-range">
                            <template
                                v-if="showSubSetPrice && currentPiecePrice"
                            >
                                门店自主定价范围：{{ isChainAdmin ? piecePriceLabel : chainPiecePriceRange }}
                            </template>
                        </div>
                    </abc-form-item>
                </div>
            </template>

            <template v-if="+subType === 2">
                <abc-form-item
                    v-abc-price-reminder="{
                        validate: validatePriceHandle, value: goodsInfo
                    }"
                    label="零售价格"
                    required
                    class="goods-packageprice"
                    style="margin-bottom: 24px;"
                    :validate-event="validatePieceUnitPrice"
                >
                    <div v-if="$slots.costprice && isCanSeeGoodsCostInInventory" class="cost-price">
                        <slot name="costprice"></slot>
                    </div>
                    <!--                    <div v-if="isSetPricePower && isDetailMedicine" class="goods-packageprice-set-price">-->
                    <!--                        <template>-->
                    <!--                            <template v-if="showPriceDialog3">-->
                    <!--                                <set-price-dialog-->
                    <!--                                    v-if="showPriceDialog3"-->
                    <!--                                    v-model="showPriceDialog3"-->
                    <!--                                    :show-sub-set-price="showSubSetPrice"-->
                    <!--                                    :package-unit="packageUnit"-->
                    <!--                                    :piece-unit="pieceUnit"-->
                    <!--                                    :pricetabs="pricetabs"-->
                    <!--                                    :price-label-text="priceLabelText"-->
                    <!--                                    :get-price-config="getPriceConfig"-->
                    <!--                                    :goods-info="goodsCloneInfo"-->
                    <!--                                    @setPriceNumber="setPriceNumber"-->
                    <!--                                >-->
                    <!--                                </set-price-dialog>-->
                    <!--                            </template>-->
                    <!--                            <span style="font-size: 12px;" @click="openPriceDialog(3)">调价</span>-->
                    <!--                        </template>-->
                    <!--                    </div>-->
                    <!--<div-->
                    <!--    v-show="validatePriceHandle() && goodsConfig.subClinicPrice.subSetPrice && currentPiecePrice"-->
                    <!--    class="price-reminder-wrapper"-->
                    <!--&gt;-->
                    <!--    <div class="price-attention-tip">-->
                    <!--        {{ validatePriceHandle(goodsInfo) }}-->
                    <!--    </div>-->
                    <!--    <i class="icon iconfont cis-icon-Attention"></i>-->
                    <!--</div>-->

                    <div
                        v-show="validatePriceHandle() && goodsConfig.subClinicPrice.subSetPrice && currentPiecePrice"
                        class="price-reminder-wrapper"
                    >
                        <abc-tooltip placement="top">
                            <abc-icon
                                icon="Attention"
                                size="14"
                                color="#ff9933"
                            ></abc-icon>

                            <div slot="content">
                                {{ validatePriceHandle(goodsInfo) }}
                            </div>
                        </abc-tooltip>
                    </div>

                    <abc-popover
                        trigger="hover"
                        style="margin-left: auto;"
                        placement="bottom-end"
                        theme="yellow"
                        width="100"
                        :open-delay="500"
                        :disabled="!tipsText"
                    >
                        <span>{{ tipsText }}</span>
                        <div slot="reference">
                            <abc-input
                                v-model="currentPiecePrice"
                                v-abc-focus-selected
                                type="money"
                                :width="130"
                                :disabled="disabledPackagePrice"
                                :config="getPriceConfig"
                                @enter="enterEvent"
                            >
                                <label slot="prepend" class="prepend">
                                    <abc-currency-symbol-icon></abc-currency-symbol-icon>
                                </label>

                                <label slot="append">
                                    <span v-if="pieceUnit || 'g'">/ {{ pieceUnit || 'g' }}</span>
                                </label>
                            </abc-input>
                        </div>
                    </abc-popover>


                    <div v-if="isChain" class="price-range">
                        <template v-if="showSubSetPrice">
                            <template
                                v-if="currentPiecePrice"
                            >
                                门店自主定价范围：{{ isChainAdmin ? piecePriceLabel : chainPiecePriceRange }}
                            </template>
                        </template>
                        <template v-else>
                            不支持门店自主定价
                        </template>
                    </div>
                </abc-form-item>
            </template>

            <template v-if="isAdmin">
                <div class="goods-taxrat" style="min-width: auto; max-width: 210px;">
                    <abc-checkbox
                        v-model="currentDefaultInOutTax"
                        :disabled="disabled"
                        class="goods-taxrat-chechbox"
                        style="margin: 0 0 4px 0;"
                    >
                        默认进/销项税率
                    </abc-checkbox>
                    <abc-tooltip-info placement="bottom-start">
                        <div v-if="isAdmin">
                            请在「管理-定价和税率」设置
                        </div>
                        <div v-else>
                            请在「总部-管理-定价和税率」设置
                        </div>
                    </abc-tooltip-info>
                    <span v-if="isDetailMedicine" class="view-history-taxrat" @click="clickHandler">
                        历史税率
                    </span>
                    <abc-form-item style=" display: block;">
                        <abc-input
                            v-model="currentInTaxRat"
                            :width="65"
                            :config="{ max: 100 }"
                            :disabled="disabledPackagePrice || defaultInOutTax"
                            @enter="enterEvent"
                        >
                            <label slot="append" class="append">%</label>
                        </abc-input>
                        <span style="margin: 0 4px; text-align: center;">-</span>
                        <abc-input
                            v-model="currentOutTaxRat"
                            :width="65"
                            :config="{ max: 100 }"
                            :disabled="disabledPackagePrice || defaultInOutTax"
                            @enter="enterEvent"
                        >
                            <label slot="append" class="append">%</label>
                        </abc-input>
                    </abc-form-item>
                </div>
            </template>

            <template v-if="featureFeeCompose">
                <template v-if="disabled">
                    <abc-form-item label="费用类型">
                        <abc-select
                            v-model="currentFeeTypeId"
                            :width="210"
                            :disabled="disabled"
                        >
                            <abc-option
                                v-for="feeType in feeTypesList"
                                :key="feeType.feeTypeId"
                                :value="feeType.feeTypeId"
                                :label="feeType.name"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </template>
                <template v-else>
                    <abc-form-item required label="费用类型">
                        <abc-select
                            v-model="currentFeeTypeId"
                            :width="210"
                            :disabled="disabled"
                            @change="handleChangeFeeType"
                        >
                            <abc-option
                                v-for="feeType in feeTypesList"
                                :key="feeType.feeTypeId"
                                :value="feeType.feeTypeId"
                                :label="feeType.name"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </template>
            </template>

            <template v-if="feeCategoryVisible">
                <abc-form-item label="首页费目">
                    <abc-cascader
                        v-model="currentFeeCategoryId"
                        :options="feeCategoryOptions"
                        :width="210"
                        :disabled="disabled"
                        :props="{
                            children: 'children',
                            label: 'name',
                            value: 'feeCategoryId'
                        }"
                    >
                    </abc-cascader>
                </abc-form-item>
            </template>
        </div>

        <history-taxrate-dialog
            v-if="showHistoryDialog"
            :dialog-visible.sync="showHistoryDialog"
            :type-id="goodsInfo && goodsInfo.typeId"
            :goods-id="goodsInfo && goodsInfo.id"
        ></history-taxrate-dialog>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { paddingMoney } from 'utils/index';
    import EnterEvent from 'views/common/enter-event';
    import { isSupportDecimalsFourMedicine } from 'src/filters/index';
    // import setPriceDialog from '../../components/set-price-dialog';
    import HistoryTaxrateDialog from '@/views/settings/price-taxrat/components/history-taxrate-dialog.vue';
    import { FeeCategoryFormItem } from '../../mixins/fee-category';

    import Clone from 'utils/clone';
    import { moneyDigit } from 'utils';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';

    export default {
        name: 'GoodsSaleInfo',
        components: {
            HistoryTaxrateDialog,
            AbcCurrencySymbolIcon,
        },
        mixins: [EnterEvent, FeeCategoryFormItem],
        props: {
            goodsInfo: Object,
            disabled: Boolean,
            disabledBaseInfo: Boolean,
            supplier: String,
            piecePrice: [String, Number],
            packagePrice: [String, Number],
            defaultInOutTax: [Boolean, Number], //默认进项税
            inTaxRat: [String, Number], //默认进项税
            outTaxRat: [String, Number], //默认销项税
            showAdminPrice: Boolean,
            dismounting: [Boolean, Number],
            chainPackagePrice: [String, Number],
            chainPiecePrice: [String, Number],
            packageUnit: [String],
            pieceUnit: [String],
            pieceNum: [String, Number],
            type: [String, Number],
            subType: [String, Number],
            chainMaxPiecePrice: [String, Number],
            chainMinPiecePrice: [String, Number],
            chainMaxPackagePrice: [String, Number],
            chainMinPackagePrice: [String, Number],
            maxCostInfo: Object,
            disabledStatus: {
                type: Number,
                default: 0,
            },
            isDetailMedicine: Boolean,//是不是调价详情 默认为false
            // 费用类型
            feeTypeId: {
                type: String,
                default: '',
            },
            // 费用类型列表
            feeTypesList: {
                type: Array,
                default () {
                    return [];
                },
            },
        },
        data() {
            return {
                showPriceDialog: false,
                showPriceDialog2: false,
                showPriceDialog3: false,
                isFirstPriceDialog: true,//是否第一次打开
                goodsCloneInfo: {},
                pricetabs: 1,//1 整 2 零售
                modifyPriceOrders: [{}],// 修改价格的数据
                priceLabelText: '',//价格修改范围
                showHistoryDialog: false,//历史税率弹窗开关
            };
        },
        computed: {
            ...mapGetters([
                'showSubSetPrice',
                'clinicConfig',
                'currentClinic',
                'isChain',
                'isChainAdmin',
                'isAdmin',
                'goodsConfig',
                'priceAdjustmentTabType',
                'priceAdjustmentRatio',
                'goodsConfigPriceAdjustmentNoPowerClinicsId',
                'isCanSeeGoodsCostInInventory',
            ]),
            ...mapGetters('viewDistribute', [
                'featureFeeCompose',
            ]),
            tipsText() {
                if (this.goodsInfo.subClinicPriceFlag) {
                    return this.goodsInfo.subClinicPriceFlag > 1 ? '该药品已修改过零售价格,将不随总部定价而改变' : '该药品未修改过零售价格,将随总部定价而改变';
                }
                return '';
            },
            isSetPricePower() { //转换语意，之前定义的禁用编辑的状态视为有权利编辑进价
                return !this.disabledPackagePrice;
            },
            disabledPackagePrice() {
                if (this.disabledBaseInfo) return true;
                if (this.disabledStatus) return true;
                if (!this.disabled) return false; // 总部可编辑
                return !this.showSubSetPrice;
            },
            currentFeeTypeId: {
                get() {
                    return this.feeTypeId;
                },
                set(v) {
                    this.$emit('update:feeTypeId', v);
                },
            },
            currentChainPackagePrice: {
                get() {
                    return this.chainPackagePrice;
                },
                set(v) {
                    this.$emit('update:chainPackagePrice', v);
                },
            },
            currentChainPiecePrice: {
                get() {
                    return this.chainPiecePrice;
                },
                set(v) {
                    this.$emit('update:chainPiecePrice', v);
                },
            },
            currentPiecePrice: {
                get() {
                    return this.piecePrice;
                },
                set(v) {
                    this.$emit('update:piecePrice', v);
                },
            },
            currentPackagePrice: {
                get() {
                    return this.packagePrice;
                },
                set(v) {
                    this.$emit('update:packagePrice', v);
                },
            },
            currentDefaultInOutTax: {
                get() {
                    return this.defaultInOutTax;
                },
                set(v) {
                    this.$emit('update:defaultInOutTax', v);
                },
            },
            currentInTaxRat: {
                get() {
                    return this.inTaxRat;
                },
                set(v) {
                    const inTaxRat = Number(v);
                    this.$emit('update:inTaxRat', inTaxRat);
                },
            },
            currentOutTaxRat: {
                get() {
                    return this.outTaxRat;
                },
                set(v) {
                    const outTaxRat = Number(v);
                    this.$emit('update:outTaxRat', outTaxRat);
                },
            },
            currentDismounting: {
                get() {
                    return this.dismounting;
                },
                set(v) {
                    if (this.isAdmin) {
                        v ? this.calPiecePrice(v, this.currentPackagePrice) : (this.currentPiecePrice = '');
                    }
                    this.$emit('update:dismounting', v);
                },
            },
            maxPackagePrice() {
                if (this.goodsConfig.subClinicPrice.subSetPrice && this.currentPackagePrice) {
                    return (Number(this.currentPackagePrice) * this.goodsConfig.subClinicPrice.maxPricePercent) / 100;
                }
                return '';
            },
            minPackagePrice() {
                if (this.goodsConfig.subClinicPrice.subSetPrice && this.currentPackagePrice) {
                    return (Number(this.currentPackagePrice) * this.goodsConfig.subClinicPrice.minPricePercent) / 100;
                }
                return '';
            },
            maxPiecePrice() {
                if (
                    this.goodsConfig.subClinicPrice.subSetPrice &&
                    (this.currentDismounting || this.subType === 2) &&
                    this.currentPiecePrice
                ) {
                    return (Number(this.currentPiecePrice) * this.goodsConfig.subClinicPrice.maxPricePercent) / 100;
                }
                return '';
            },
            minPiecePrice() {
                if (
                    this.goodsConfig.subClinicPrice.subSetPrice &&
                    (this.currentDismounting || this.subType === 2) &&
                    this.currentPiecePrice
                ) {
                    return (Number(this.currentPiecePrice) * this.goodsConfig.subClinicPrice.minPricePercent) / 100;
                }
                return '';
            },

            packagePriceLabel() {
                let range = '';
                if (this.maxPackagePrice !== '' && this.minPackagePrice !== '') {
                    range += ` ${paddingMoney(this.minPackagePrice)} - ${paddingMoney(this.maxPackagePrice)} `;
                } else if (this.maxPackagePrice !== '' && this.minPackagePrice === '') {
                    range += ` 不高于${paddingMoney(this.maxPackagePrice)}`;
                } else if (this.maxPackagePrice === '' && this.minPackagePrice !== '') {
                    range += ` 不低于${paddingMoney(this.minPackagePrice)}`;
                }
                return `${range}`;
            },
            piecePriceLabel() {
                let range = '';
                if (this.maxPiecePrice !== '' && this.minPiecePrice !== '') {
                    range += ` ${paddingMoney(this.minPiecePrice)} - ${paddingMoney(this.maxPiecePrice)} `;
                } else if (this.maxPiecePrice !== '' && this.minPiecePrice === '') {
                    range += ` 不高于${paddingMoney(this.maxPiecePrice)}`;
                } else if (this.maxPiecePrice === '' && this.minPiecePrice !== '') {
                    range += ` 不低于${paddingMoney(this.minPiecePrice)}`;
                }
                return `${range}`;
            },

            isChineseMedicine() {
                return Number(this.type) === 1 && Number(this.subType) === 2;
            },
            chainPackagePriceRange() {
                let range = '';
                if (this.chainMaxPackagePrice && this.chainMinPackagePrice) {
                    range += ` ${paddingMoney(this.chainMinPackagePrice)} - ${paddingMoney(
                        this.chainMaxPackagePrice,
                    )} `;
                } else if (this.chainMaxPackagePrice && !this.chainMinPackagePrice) {
                    range += ` 不高于${paddingMoney(this.chainMaxPackagePrice)}`;
                } else if (!this.chainMaxPackagePrice && this.chainMinPackagePrice) {
                    range += ` 不低于${paddingMoney(this.chainMinPackagePrice)}`;
                }
                return `${range}`;
            },
            chainPiecePriceRange() {
                let range = '';
                if (this.chainMaxPiecePrice && this.chainMinPiecePrice) {
                    range += ` ${paddingMoney(this.chainMinPiecePrice)} - ${paddingMoney(this.chainMaxPiecePrice)} `;
                } else if (this.chainMaxPiecePrice && !this.chainMinPiecePrice) {
                    range += ` 不高于${paddingMoney(this.chainMaxPiecePrice)}`;
                } else if (!this.chainMaxPiecePrice && this.chainMinPiecePrice) {
                    range += ` 不低于${paddingMoney(this.chainMinPiecePrice)}`;
                }
                return `${range}`;
            },

            getPriceConfig() {
                if (this.isChineseMedicine || isSupportDecimalsFourMedicine(this.goodsInfo)) {
                    return {
                        formatLength: 4, supportZero: true, max: 9999999,
                    };
                }
                return {
                    formatLength: 2, supportZero: true, max: 9999999,
                };
            },
        },
        watch: {
            pieceNum(newVal, oldVal) {
                if (oldVal !== undefined && Number(newVal) !== Number(oldVal)) {
                    if (this.isAdmin) {
                        this.calPiecePrice(this.currentDismounting, this.currentPackagePrice);
                    }
                }
            },
        },
        methods: {
            // 查看历史税率按钮
            clickHandler() {
                this.showHistoryDialog = true;
            },
            setCurrentDismounting() {
                if (this.currentDismounting === true) {
                    if (this.modifyPriceOrders[0] && this.modifyPriceOrders[0].updatePiecePrice) {
                        delete this.modifyPriceOrders[0].updatePiecePrice;
                    }
                }

            },
            async setPriceNumber(num,priceType,percent,price,packageCostPrice) {
                num = moneyDigit(num, 4);
                this.modifyPriceOrders[0].opType = priceType;
                this.modifyPriceOrders[0].lastSupplierName = this.supplier;
                this.modifyPriceOrders[0].upPercent = Number(percent);
                if (this.isChainAdmin) {
                    this.modifyPriceOrders[0].affectedClinicIdList = this.goodsConfigPriceAdjustmentNoPowerClinicsId;
                }
                if (packageCostPrice) {
                    this.modifyPriceOrders[0].packageCostPrice = packageCostPrice;
                }
                if (this.pricetabs === 1) {
                    this.modifyPriceOrders[0].updatePackagePrice = {
                        afterPrice: num,
                        beforePrice: price,
                        opType: priceType,
                        upPercent: Number(percent),
                    };
                    this.currentPackagePrice = num;
                } else {
                    this.modifyPriceOrders[0].updatePiecePrice = {
                        afterPrice: num,
                        beforePrice: price,
                        opType: priceType,
                        upPercent: Number(percent),
                    };
                    this.currentPiecePrice = num;
                }
            },
            openPriceDialog(tabs) {
                let tab = 1;
                tab = tabs;
                if (tabs === 3) {
                    tab = 2;
                }
                this.pricetabs = tab;
                this.priceLabelText = '';
                // eslint-disable-next-line default-case
                switch (tabs) {
                    case 1:
                        if (this.showSubSetPrice) {
                            if (this.isChainAdmin && this.currentPackagePrice) {
                                this.priceLabelText = this.packagePriceLabel;
                            } else {
                                this.priceLabelText = this.chainPackagePriceRange;
                            }
                        } else {
                            this.priceLabelText = '不支持门店自主定价';
                        }
                        this.showPriceDialog = true;
                        break;
                    case 2:
                        if (this.showSubSetPrice && this.currentPiecePrice) {
                            if (this.isChainAdmin) {
                                this.priceLabelText = this.piecePriceLabel;
                            } else {
                                this.priceLabelText = this.chainPiecePriceRange;
                            }
                        }
                        this.showPriceDialog2 = true;
                        break;
                    case 3:
                        if (this.showSubSetPrice) {
                            if (this.isChainAdmin) {
                                this.priceLabelText = this.piecePriceLabel;
                            } else {
                                this.priceLabelText = this.chainPiecePriceRange;
                            }
                        } else {
                            this.priceLabelText = '不支持门店自主定价';
                        }
                        this.showPriceDialog3 = true;

                        break;
                }


                if (this.isFirstPriceDialog) {
                    this.isFirstPriceDialog = false;
                    this.goodsCloneInfo = Clone(this.goodsInfo);
                }
            },
            isSupportDecimalsFourMedicine,
            calcPackagePrice(val) {
                this.calPiecePrice(this.currentDismounting, val);
            },
            validatePrice(item) {
                if (!this.maxCostInfo || !this.maxCostInfo.clinicName) return;
                const {
                    packagePrice, piecePrice, type, subType,
                } = item || {};
                // 中药
                if (type === 1 && subType === 2) {
                    if (+this.maxCostInfo.packageCostPrice && +this.maxCostInfo.packageCostPrice > +piecePrice) {
                        if (this.maxCostInfo.clinicName && this.isChainAdmin) {
                            return `销售价低于进价（${this.$t('currencySymbol')} ${this.maxCostInfo.packageCostPrice}，${this.maxCostInfo.clinicName}）`;
                        }
                        return `销售价低于进价（${this.$t('currencySymbol')} ${this.maxCostInfo.packageCostPrice}）`;
                    }
                } else {
                    if (+this.maxCostInfo.packageCostPrice && +this.maxCostInfo.packageCostPrice > +packagePrice) {
                        if (this.maxCostInfo.clinicName && this.isChainAdmin) {
                            return `销售价低于进价（${this.$t('currencySymbol')} ${this.maxCostInfo.packageCostPrice}，${this.maxCostInfo.clinicName}）`;
                        }
                        return `销售价低于进价（${this.$t('currencySymbol')} ${this.maxCostInfo.packageCostPrice}）`;
                    }
                }
                return '';
            },
            validatePriceHandle() {
                return this.validatePrice({
                    packagePrice: this.currentPackagePrice,
                    piecePrice: this.currentPiecePrice,
                    type: this.type,
                    subType: this.subType,
                });
            },
            validatePieceUnitPrice(value, callback) {
                // 总部或者不是拆零就不校验
                if (this.isAdmin || !this.currentDismounting) {
                    callback({
                        validate: true,
                    });
                    return;
                }
                const maxPrice = +this.chainMaxPiecePrice;
                const minPrice = +this.chainMinPiecePrice;
                const val = +value;
                if (val > 9999999) {
                    callback({
                        validate: false,
                        message: '最多7位数',
                    });
                } else if (maxPrice && val > maxPrice) {
                    callback({
                        validate: false,
                        message: `不能高于最高售价 (${this.$t('currencySymbol')} ${maxPrice})`,
                    });
                } else if (minPrice && val < minPrice) {
                    callback({
                        validate: false,
                        message: `不能低于最低售价 (${this.$t('currencySymbol')} ${minPrice})`,
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
            validatePackageUnitPrice(value, callback) {
                if (this.isAdmin) {
                    callback({
                        validate: true,
                    });
                } else {
                    const maxPrice = Number(this.chainMaxPackagePrice);
                    const minPrice = Number(this.chainMinPackagePrice);
                    const val = Number(value);
                    if (val > 9999999) {
                        callback({
                            validate: false,
                            message: '最多7位数',
                        });
                    } else if (maxPrice && val > maxPrice) {
                        callback({
                            validate: false,
                            message: `不能高于最高售价 (${this.$t('currencySymbol')} ${maxPrice})`,
                        });
                    } else if (minPrice && val < minPrice) {
                        callback({
                            validate: false,
                            message: `不能低于最低售价 (${this.$t('currencySymbol')} ${minPrice})`,
                        });
                    } else {
                        callback({
                            validate: true,
                        });
                    }
                }
            },
            calPiecePrice(dismounting, packagePrice) {
                if (+this.subType !== 2) {
                    if (dismounting && this.pieceNum && packagePrice) {
                        this.currentPiecePrice = ((Number(packagePrice) || 0) / (Number(this.pieceNum) || 1)).toFixed(
                            (this.isChineseMedicine || isSupportDecimalsFourMedicine(this.goodsInfo)) ? 4 : 2,
                        );
                    } else {
                        this.currentPiecePrice = '';
                    }
                }
            },
        },
    };
</script>
