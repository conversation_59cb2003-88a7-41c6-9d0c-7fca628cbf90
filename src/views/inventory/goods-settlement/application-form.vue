<template>
    <div>
        <abc-dialog
            v-if="showDialog"
            ref="settlementDialog"
            v-model="showDialog"
            :title="dialogTitle"
            size="hugely"
            custom-class="settlement-dialog-wrapper"
            content-styles="padding: 24px; height: 624px;"
            :before-close="cancelSubmit"
            append-to-body
        >
            <abc-layout v-abc-loading="pageLoading" class="settlement-detail-content">
                <abc-layout-header style="padding: 0;">
                    <abc-form
                        is-excel
                        item-no-margin
                    >
                        <abc-descriptions
                            :column="3"
                            background
                            grid
                            size="large"
                        >
                            <abc-descriptions-item v-if="id" label="供应商" :span="1">
                                {{ postData.supplierObj && postData.supplierObj.name }}
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                v-else
                                content-padding="0"
                                label="供应商"
                                :span="1"
                            >
                                <abc-form-item>
                                    <abc-select
                                        v-model="supplier.id"
                                        with-search
                                        :fetch-suggestions="_filterSuppliers"
                                        @change="selectSupplier"
                                    >
                                        <abc-option
                                            v-for="(it) in currentSuppliers"
                                            :key="`${it.id }`"
                                            :value="it.id"
                                            :label="it.name"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item :span="2">
                                &nbsp;
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form>
                </abc-layout-header>
                <abc-layout-content style="padding: 0 0 16px;">
                    <abc-text theme="gray" size="normal" style="display: block; margin: 16px 0 4px;">
                        关联单据
                    </abc-text>
                    <abc-table
                        :render-config="tableConfig"
                        :data-list="postData.settlements"
                        empty-size="small"
                        :show-hover-tr-bg="false"
                        :show-checked="false"
                        :support-delete-tr="!id"
                        style="max-height: 458px;"
                        need-delete-confirm
                        :custom-tr-support-delete="customTrSupportDelete"
                        @delete-tr="deleteIt"
                    >
                        <template v-if="!id" #topHeader>
                            <abc-flex flex="1" justify="flex-end">
                                <abc-button
                                    variant="ghost"
                                    theme="default"
                                    :class="{ 'abc-tipsy abc-tipsy--n': !postData.supplierId }"
                                    data-tipsy="请先选择供应商"
                                    :disabled="!postData.supplierId"
                                    @click="openOrdersDialog"
                                >
                                    选择关联单据
                                </abc-button>
                            </abc-flex>
                        </template>
                        <template #orderNo="{ trData }">
                            <abc-table-cell>
                                {{ trData.refOrderNo }}
                            </abc-table-cell>
                        </template>
                        <template #type="{ trData }">
                            <abc-table-cell>
                                {{ formatOrderType(trData) }}
                            </abc-table-cell>
                        </template>
                        <template #date="{ trData }">
                            <abc-table-cell>
                                {{ trData.refOrderDate | parseTime('y-m-d') }}
                            </abc-table-cell>
                        </template>
                        <template #employeeName="{ trData }">
                            <abc-table-cell>
                                {{ trData.refOrderUserName }}
                            </abc-table-cell>
                        </template>
                        <template #kindCount="{ trData }">
                            <abc-table-cell>
                                {{ trData.kindCount }}
                            </abc-table-cell>
                        </template>
                        <template #count="{ trData }">
                            <abc-table-cell>
                                {{ trData.count }}
                            </abc-table-cell>
                        </template>
                        <template #amountExcludingTax="{ trData }">
                            <abc-table-cell>
                                {{ formatAmount(trData.type, trData.amountExcludingTax) | formatMoney }}
                            </abc-table-cell>
                        </template>
                        <template #tax="{ trData }">
                            <abc-table-cell>
                                {{ formatAmount(trData.type, trData.tax) | formatMoney }}
                            </abc-table-cell>
                        </template>
                        <template #clinicName="{ trData }">
                            <abc-table-cell>
                                {{ trData.refOrderClinicName }}
                            </abc-table-cell>
                        </template>
                        <template #footer>
                            <abc-flex
                                align="center"
                                flex="1"
                                justify="flex-end"
                                style="padding: 0 12px;"
                            >
                                <abc-space :size="4">
                                    <abc-text theme="gray">
                                        单据数量
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ postData.settlements && postData.settlements.length }}
                                    </abc-text>
                                    <abc-text theme="gray">
                                        ，价税合计(总计)
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ totalAmount | formatMoney }}
                                    </abc-text>
                                    <abc-text theme="gray">
                                        ，金额(总计)
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ totalAmountExcludingTax | formatMoney }}
                                    </abc-text>
                                    <abc-text theme="gray">
                                        ，税额(总计)
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ totalTax | formatMoney }}
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>

                    <abc-text theme="gray" size="normal" style="display: block; margin: 16px 0 4px;">
                        关联发票
                    </abc-text>
                    <abc-flex flex="1" align="center" wrap="wrap">
                        <template v-if="postData.invoices && postData.invoices.length">
                            <invoice
                                v-for="(invoiceItem,index) in postData.invoices"
                                :key="`${invoiceItem.id }${ index}`"
                                :show-edit="!id"
                                :value="invoiceItem"
                                @delete="deleteSettlement(index)"
                            ></invoice>
                        </template>

                        <abc-option-card
                            v-if="!id"
                            class="invoice-wrapper"
                            style="height: 116px;"
                            :show-icon="false"
                            :selectable="false"
                            @click.native="openInvoiceDialog('add')"
                        >
                            <template #description>
                                <abc-flex
                                    justify="center"
                                    vertical
                                    align="center"
                                    gap="4"
                                    style="height: 100%;"
                                >
                                    <abc-icon icon="n-add-line-medium" color="#7a8794"></abc-icon>
                                    <abc-text theme="gray">
                                        添加关联发票
                                    </abc-text>
                                </abc-flex>
                            </template>
                        </abc-option-card>
                    </abc-flex>
                </abc-layout-content>
            </abc-layout>
            <div slot="footer" class="dialog-footer">
                <abc-flex align="center" flex="1" :justify="postData.logs ? 'space-between' : 'flex-end'">
                    <order-change-log v-if="postData.logs" :logs="postData.logs"></order-change-log>
                    <abc-space>
                        <template v-if="id && postData.status === 0">
                            <abc-button :loading="loading" @click="trashSettlement">
                                撤回
                            </abc-button>
                        </template>
                        <template v-if="!id">
                            <abc-button :disabled="disabled" :loading="loading" @click="submit">
                                提交
                            </abc-button>
                            <abc-button type="blank" @click="cancelSubmit">
                                取消
                            </abc-button>
                        </template>
                        <template v-if="id">
                            <print-popper
                                :options="printOptions"
                                print-text="打印"
                                @print="handlePrint"
                                @select-print-setting="openPrintSettingsDialog"
                            ></print-popper>
                            <abc-button type="blank" @click="showDialog = false">
                                取消
                            </abc-button>
                        </template>
                    </abc-space>
                </abc-flex>
            </div>
        </abc-dialog>
        <associated-order-dialog
            v-if="showAssociatedOrderDialog"
            v-model="showAssociatedOrderDialog"
            :settlements="dialogSettlements"
            :supplier="supplier"
            @confirm="addOrder"
        ></associated-order-dialog>

        <invoice-dialog
            v-if="showInvoiceDialog"
            v-model="showInvoiceDialog"
            :title="invoiceDialogData.title"
            :invoice="invoiceDialogData.invoice"
            @change="confirmInvoice"
        ></invoice-dialog>
    </div>
</template>

<script>
    import SupplierAPI from 'api/goods/supplier.js';
    import SettlementAPI from 'api/goods/settlement.js';
    // import StockInAPI from 'api/goods/stock-in';
    import GoodsCommon from '../common.js';

    import {
        ORDER_TABLE_CONFIG, formatAmount, formatOrderType, formatClinicName,
    } from './common.js';
    import { createGUID } from '../../../utils/index';


    import Invoice from './invoice';
    import AssociatedOrderDialog from './associated-order-dialog';
    import InvoiceDialog from './invoice-dialog.vue';
    import OrderChangeLog from '../common/change-log/change-log';
    import {
        CHECK_IN_SUPPLIER_ID,
        GOODS_IN_ORDER_TYPE,
        // ORDER_EXTERNAL_FLAG,
    } from 'views/inventory/constant.js';
    import { debounce } from 'utils/lodash';
    import PrintPopper from 'views/print/popper.vue';
    import { mapGetters } from 'vuex';
    import Big from 'big.js';
    const InventoryPrintApiModule = () => import('@/printer/print-api/inventory');
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');

    export default {
        name: 'GoodsSettlementForm',
        components: {
            PrintPopper,
            Invoice,
            AssociatedOrderDialog,
            InvoiceDialog,
            OrderChangeLog,
        },
        mixins: [
            GoodsCommon,
        ],
        props: {
            value: [Boolean, String],
            id: [Number, String],
        },
        data() {
            return {
                CHECK_IN_SUPPLIER_ID,
                loading: false,
                pageLoading: false,
                postData: {
                    supplierId: '',
                    settlements: [],
                    invoices: [],
                },
                currentSuppliers: [],
                supplier: {
                    id: '',
                    name: '',
                },
                showAssociatedOrderDialog: false,
                showInvoiceDialog: false,
                invoiceDialogData: {
                    title: '新增发票',
                    invoice: null,
                },
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            dialogContentStyles() {
                let sty = 'padding: 0 24px; ';
                sty += `width: ${window.screen.width <= 1024 ? 1008 : 1200}px;`;
                return sty;
            },
            disabled() {
                return !this.postData.settlements.length;
            },
            computedOrder() {
                const data = this.postData.settlements?.reduce((res, item) => {

                    // 只算出入库原单，不算修正的
                    if (item.type === 0 || item.type === 1) {
                        res.tax = Big(res.tax).plus(formatAmount(item.type, item.diffTax || 0));
                        res.count = Big(res.count).plus(formatAmount(item.type, item.diffCount || 0));
                        res.amount = Big(res.amount).plus(formatAmount(item.type, item.diffAmount || 0));
                        res.amountExcludingTax = Big(res.amountExcludingTax).plus(formatAmount(item.type, item.diffAmountExcludingTax || 0));
                    }

                    return res;
                }, {
                    tax: Big(0),
                    count: Big(0),
                    amount: Big(0),
                    amountExcludingTax: Big(0),
                });

                return {
                    tax: data.tax.toNumber(),
                    count: data.count.toNumber(),
                    amount: data.amount.toNumber(),
                    amountExcludingTax: data.amountExcludingTax.toNumber(),
                };
            },
            count() {
                return this.computedOrder.count || 0;
            },
            totalAmount() {
                if (this.id) return this.postData.amount || 0;

                // 新建时，需要计算 amountExcludingTax
                return this.computedOrder.amount || 0;
            },

            totalTax() {
                if (this.id) return this.postData.tax || 0;

                // 新建时，需要计算 tax
                return this.computedOrder.tax || 0;
            },

            totalAmountExcludingTax() {
                if (this.id) return this.postData.amountExcludingTax || 0;

                return this.computedOrder.amountExcludingTax || 0;
            },

            dialogTitle() {
                if (this.id) {
                    return `结算单${this.postData.orderNo || ''}`;
                }
                return '结算单';
            },
            distributePrintOptions() {
                return this.viewDistributeConfig.Print.printOptions;
            },
            printOptions() {
                const options = this.distributePrintOptions;
                return [
                    {
                        value: options.SETTLEMENT_APPLICATION.value,
                        disabled: false,
                    },
                ];
            },
            dialogSettlements() {
                return this.postData.settlements.filter(this.customTrSupportDelete);
            },
        },
        created() {
            this.tableConfig = ORDER_TABLE_CONFIG;
            this._filterSuppliers = debounce(this.filterSuppliers, 1000, true);

            this._filterSuppliers();
            if (this.id) {
                this.fetchDetail(this.id);
            }
        },
        methods: {
            formatAmount,
            formatOrderType,
            async filterSuppliers(keyword = '') {
                try {
                    keyword = keyword.trim();
                    const params = {
                        keyword,
                        clinicId: this.clinicId,
                        status: '',
                    };
                    const { data } = await SupplierAPI.searchSupplier(params);
                    this.currentSuppliers = data && data.rows && data.rows.filter((item) => {
                        return !!item && item.id !== this.CHECK_IN_SUPPLIER_ID;
                    }) || [];
                } catch (err) {
                    this.currentSuppliers = [];
                }
            },
            /**
             * @desc 选择关联单据，打开关联单据的弹窗
             * <AUTHOR>
             * @date 2019/11/25 17:09:02
             * @params
             * @return
             */
            openOrdersDialog() {
                this.showAssociatedOrderDialog = true;
            },
            /**
             * @desc 选择供应商
             * <AUTHOR>
             * @date 2019/11/26 19:55:31
             */
            selectSupplier() {
                let res = this.currentSuppliers.find((item) => {
                    return item.id === this.supplier.id;
                });
                if (this.postData.supplierId && this.postData.supplierId !== this.supplier.id && this.postData.settlements.length) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '切换供应商后，所有关联的出/入库单将清空',
                        onConfirm: () => {
                            this.postData.settlements = [];
                            this.supplier.name = res.name;
                            this.postData.supplierId = this.supplier.id;
                        },
                        onCancel: () => {
                            this.supplier.id = this.postData.supplierId;
                            res = this.currentSuppliers.find((item) => {
                                return item.id === this.supplier.id;
                            });

                            if (res) {
                                this.supplier.name = res.name;
                            }
                        },
                    });
                } else {
                    this.postData.supplierId = this.supplier.id;
                    this.supplier.name = res.name;
                }
            },

            async addOrder(orderList = []) {
                const settlements = [];

                orderList.forEach((order) => {
                    settlements.push(order, ...order.fixedStockOrderList ?? []);
                });

                this.postData.settlements = settlements;
                this.$nextTick(() => {
                    this.$refs.settlementDialog.updateDialogHeight();
                });
            },
            createSettlementItem(order) {
                return {
                    amount: order.amount,
                    amountExcludingTax: order.amountExcludingTax,
                    count: order.count,
                    id: order.id,
                    kindCount: order.kindCount,
                    refOrderClinicId: order.toOrgan?.clinicId,
                    refOrderClinicName: order.toOrgan?.name,
                    refOrderDate: order.createdDate,
                    refOrderNo: order.orderNo,
                    refOrderUserName: order.createdUser?.name,
                    tax: Big(order.amount || 0).minus(order.amountExcludingTax || 0).toNumber(),
                    refOrderType: order.type,// GOODS_IN_ORDER_TYPE

                    // 结算列表这里的type是固定的1入库2出库，不是order的类型
                    type: (order.type === GOODS_IN_ORDER_TYPE.RETURN_OUT || order.type === GOODS_IN_ORDER_TYPE.CORRECT_RETURN_OUT) ? 2 : 1,
                };
            },
            /**
             * @desc 新增发票
             * <AUTHOR>
             * @date 2019/11/27 10:53:29
             * @params
             * @return
             */
            openInvoiceDialog(type, invoice = '') {
                if (type === 'add') {
                    this.invoiceDialogData.title = '新增发票';
                    this.invoiceDialogData.invoice = null;
                } else {
                    this.invoiceDialogData.title = '编辑发票';
                    this.invoiceDialogData.invoice = invoice ? invoice : {};
                }
                this.showInvoiceDialog = true;
            },
            /**
             * @desc 删除某行
             * <AUTHOR>
             * @date 2019/11/28
             * @params index
             * @return
             */
            deleteIt(index) {
                const order = this.postData.settlements[index];
                if (order?.fixedStockOrderList) {
                    this.postData.settlements = this.postData.settlements.filter((it) => {
                        return ![order, ...order.fixedStockOrderList].some((e) => e.id === it.id);
                    });
                } else {
                    this.postData.settlements.splice(index, 1);
                }
            },
            customTrSupportDelete(trData) {
                return trData.type === 0 || trData.type === 1;
            },
            /**
             * @desc 新增 编辑 发票 判断是否有GUID 没有则是新增发票
             * <AUTHOR>
             * @date 2019/11/28
             * @params
             * @return
             */
            confirmInvoice(val) {
                val.id = createGUID();
                this.postData.invoices.push(val);
            },
            /**
             * @desc 删除发票
             * <AUTHOR>
             * @date 2019/11/28
             * @params
             * @return
             */
            deleteSettlement(index) {
                this.postData.invoices.splice(index, 1);
            },
            async createSettlement() {
                try {
                    this.loading = true;
                    const settlements = this.postData.settlements.map((item) => {
                        return {
                            type: item.type,
                            refOrderId: item.id,
                            lastModifiedDate: item.lastModifiedDate || '',
                        };
                    });
                    const invoices = this.postData.invoices.map((item) => {
                        return {
                            invoiceNo: item.invoiceNo,
                            invoiceDate: item.invoiceDate,
                        };
                    });
                    const data = {
                        supplierId: this.postData.supplierId,
                        settlements,
                        invoices,
                    };
                    await SettlementAPI.createSettlement(data);
                    this.$emit('update', 'add');
                    this.showDialog = false;
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                    if (e.code === 12703) {
                        const details = e.detail.settlements;
                        const orders = [];
                        details.forEach((item) => {
                            const tempOrder = this.postData.settlements.find((it) => {
                                return it.id === item.refOrderId;
                            });
                            orders.push(tempOrder);
                        });
                        const message = [];
                        orders.forEach((item) => {
                            const str = `<p>${item.refOrderNo}</p>`;
                            message.push(str);
                        });
                        this.$alert({
                            type: 'warn',
                            title: '关联的出/入库单被修改，请重新添加',
                            content: message,
                            onClose: () => {
                                details.forEach((item) => {
                                    this.postData.settlements = this.postData.settlements.filter((it) => {
                                        return it.id !== item.refOrderId;
                                    });
                                });
                            },
                        });
                    }
                }
            },
            async submit() {
                this.$confirm({
                    title: '提示',
                    content: '点击确定，该结算单将发起审核',
                    onConfirm: async () => {
                        await this.createSettlement();
                    },
                });
            },
            cancelSubmit() {
                if (this.id) {
                    this.showDialog = false;
                } else {
                    if ((this.postData.settlements && this.postData.settlements.length) || this.postData.supplierId) {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: '点击确定，将删除该结算单',
                            onConfirm: () => {
                                this.showDialog = false;
                            },
                        });
                    } else {
                        this.showDialog = false;
                    }
                }
            },
            /**
             * @desc 获取结算单详情
             * <AUTHOR>
             * @date 2019/12/2
             * @params
             * @return
             */
            async fetchDetail() {
                try {
                    this.pageLoading = true;
                    const { data } = await SettlementAPI.fetchSettlementDetail(this.id);
                    this.postData = data;
                    this.postData.settlements = this.postData.settlements.map((item) => {
                        return {
                            ...item,
                            refOrderUserName: item.orderCreator && item.orderCreator.name || item.refOrderUserName,
                            refOrderClinicName: formatClinicName(item.orderClinic),
                        };
                    });

                    this.$nextTick(() => {
                        this.$refs.settlementDialog.updateDialogHeight();
                    });
                    this.pageLoading = false;
                } catch (e) {
                    console.log('settlement fetchDetail', e);
                    this.pageLoading = false;
                }
            },
            /**
             * @desc 撤回结算单
             * <AUTHOR>
             * @date 2019/11/29
             * @params id：结算单Id
             * @return
             */
            trashSettlement() {
                try {
                    const title = '点击确定，将撤回该结算单申请，状态将更新为已作废';
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: title,
                        onConfirm: async () => {
                            await SettlementAPI.trashSettlement(this.id);
                            this.$emit('update');
                            this.showDialog = false;
                        },
                    });
                } catch (e) {
                    console.error('trashSettlement', e);
                }
            },
            async openPrintSettingsDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'purchase-settlement' }).generateDialogAsync({ parent: this });
            },
            async handlePrint() {
                const { InventoryPrintApi } = await InventoryPrintApiModule();
                InventoryPrintApi.printSettlementApplication(this.postData);
            },
        },
    };
</script>


