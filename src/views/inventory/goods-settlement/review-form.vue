<template>
    <div>
        <abc-dialog
            v-if="showDialog"
            ref="settlementDialog"
            v-model="showDialog"
            :title="dialogTitle"
            size="hugely"
            custom-class="settlement-dialog-wrapper"
            :content-styles="dialogContentStyles"
            append-to-body
        >
            <abc-layout v-abc-loading="loading" class="settlement-detail-content">
                <abc-layout-header style="padding: 0;">
                    <!-- 供应商 -->
                    <abc-descriptions
                        :column="2"
                        background
                        grid
                        size="large"
                    >
                        <abc-descriptions-item label="供应商" :span="1">
                            {{ order.supplierObj && order.supplierObj.name }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="结算金额" :span="1">
                            <abc-text theme="gray">
                                价税合计
                            </abc-text>
                            <abc-text theme="black">
                                {{ order.amount | formatMoney }}
                            </abc-text>
                            <abc-text theme="gray">
                                ，金额
                            </abc-text>
                            <abc-text theme="black">
                                {{ order.amountExcludingTax | formatMoney }}
                            </abc-text>
                            <abc-text theme="gray">
                                ，税额
                            </abc-text>
                            <abc-text theme="black">
                                {{ order.tax | formatMoney }}
                            </abc-text>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-layout-header>
                <abc-layout-content style="padding: 0; padding-bottom: 24px;">
                    <!-- 关联发票 -->
                    <abc-text theme="gray" size="normal" style="display: block; margin: 16px 0 4px;">
                        关联发票
                    </abc-text>
                    <abc-flex flex="1" align="center" wrap="wrap">
                        <template v-if="order.invoices && order.invoices.length">
                            <invoice
                                v-for="(invoiceItem, index) in order.invoices"
                                :key="`${invoiceItem.id }${ index}`"
                                :show-edit="order.status === 0"
                                :value="invoiceItem"
                                @delete="deleteSettlement(index)"
                            ></invoice>
                        </template>

                        <!-- 未结算的结算单才能增加发票 -->
                        <abc-option-card
                            v-if="order.status === 0"
                            class="invoice-wrapper"
                            style="height: 116px;"
                            :show-icon="false"
                            :selectable="false"
                            @click.native="openInvoiceDialog('add')"
                        >
                            <template #description>
                                <abc-flex
                                    justify="center"
                                    vertical
                                    align="center"
                                    gap="4"
                                    style="height: 100%;"
                                >
                                    <abc-icon icon="n-add-line-medium" color="#7a8794"></abc-icon>
                                    <abc-text theme="gray">
                                        添加关联发票
                                    </abc-text>
                                </abc-flex>
                            </template>
                        </abc-option-card>
                    </abc-flex>

                    <!-- 结算明细 -->
                    <abc-text theme="gray" size="normal" style="display: block; margin: 8px 0 4px;">
                        结算明细
                    </abc-text>
                    <abc-form ref="reviewForm" is-excel item-no-margin>
                        <abc-table
                            :render-config="settlementDetailConfig"
                            :data-list="order.items"
                            empty-size="small"
                            :show-hover-tr-bg="false"
                            :show-checked="false"
                            :support-delete-tr="order.status === 0"
                            need-delete-confirm
                            @delete-tr="deleteSettlementDetail"
                        >
                            <template #name="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input v-model="trData.name" @enter="enterEvent"></abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.name }}
                                </abc-table-cell>
                            </template>
                            <template #spec="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input v-model="trData.spec" @enter="enterEvent"></abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.spec }}
                                </abc-table-cell>
                            </template>
                            <template #unit="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input v-model="trData.unit" @enter="enterEvent"></abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.unit }}
                                </abc-table-cell>
                            </template>
                            <template #count="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input
                                        v-model="trData.count"
                                        type="number"
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        :config="{
                                            supportNegative: true,
                                            supportZero: true,
                                            formatLength: 2,
                                        }"
                                        @enter="enterEvent"
                                    ></abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.count }}
                                </abc-table-cell>
                            </template>
                            <template #unitPrice="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input
                                        v-model="trData.unitPrice"
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        :config="{
                                            supportZero: true,
                                            formatLength: 2,
                                        }"
                                        type="money"
                                        @enter="enterEvent"
                                    ></abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.unitPrice }}
                                </abc-table-cell>
                            </template>
                            <template #amount="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input
                                        v-model="trData.amount"
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        :config="{
                                            supportZero: true,
                                            supportNegative: true,
                                            formatLength: 2,
                                        }"
                                        type="money"
                                        @change="handleRowInputChange($event, trData)"
                                        @enter="enterEvent"
                                    ></abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.amount | formatMoney }}
                                </abc-table-cell>
                            </template>
                            <template #amountExcludingTax="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input
                                        v-model="trData.amountExcludingTax"
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        :config="{
                                            supportZero: true,
                                            supportNegative: true,
                                            formatLength: 2,
                                        }"
                                        type="money"
                                        @change="handleRowInputChange($event, trData)"
                                        @enter="enterEvent"
                                    ></abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.amountExcludingTax | formatMoney }}
                                </abc-table-cell>
                            </template>
                            <template #taxRat="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input
                                        v-model="trData.taxRat"
                                        :config="{
                                            supportZero: true,
                                            formatLength: 2,
                                        }"
                                        type="number"
                                        @enter="enterEvent"
                                    >
                                        <span slot="appendInner">%</span>
                                    </abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.taxRat }}%
                                </abc-table-cell>
                            </template>
                            <template #tax="{ trData }">
                                <abc-form-item
                                    v-if="order.status === 0"
                                    required
                                    hidden-red-dot
                                    :validate-event="validateEmpty"
                                >
                                    <abc-input
                                        v-model="trData.tax"
                                        :config="{
                                            supportZero: true,
                                            supportNegative: true,
                                            formatLength: 2,
                                        }"
                                        disabled
                                        :input-custom-style="{ 'text-align': 'right' }"
                                        type="money"
                                    ></abc-input>
                                </abc-form-item>
                                <abc-table-cell v-else>
                                    {{ trData.tax | formatMoney }}
                                </abc-table-cell>
                            </template>
                            <template #footer>
                                <abc-flex
                                    align="center"
                                    flex="1"
                                    :justify="order.status === 0 ? 'space-between' : 'flex-end'"
                                    style="padding: 0 12px;"
                                >
                                    <abc-button
                                        v-if="order.status === 0"
                                        variant="text"
                                        theme="primary"
                                        size="small"
                                        icon="n-add-line-medium"
                                        @click="openSettlementDetailDialog()"
                                    >
                                        添加行
                                    </abc-button>
                                    <abc-space :size="4">
                                        <abc-text theme="gray">
                                            金额合计
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ totalAmountExcludingTax | formatMoney }}
                                        </abc-text>
                                        <abc-text theme="gray">
                                            ，税额合计
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ totalTax | formatMoney }}
                                        </abc-text>
                                        <abc-text theme="gray">
                                            ，价税合计(大写)
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ digitUppercase(totalAmount) }}
                                        </abc-text>
                                        <abc-text theme="gray">
                                            ，价税合计(小写)
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ totalAmount | formatMoney }}
                                        </abc-text>
                                    </abc-space>
                                </abc-flex>
                            </template>
                        </abc-table>
                    </abc-form>

                    <!-- 附件 -->
                    <abc-text theme="gray" size="normal" style="display: block; margin: 16px 0 4px;">
                        附件
                    </abc-text>
                    <abc-table
                        :render-config="settlementEnclosureConfig"
                        :data-list="order.settlements"
                        style="max-height: 458px;"
                        empty-size="small"
                        :show-hover-tr-bg="false"
                        :show-checked="false"
                    >
                        <template #refOrderId="{ trData }">
                            <abc-table-cell>
                                {{ trData.refOrderNo }}
                            </abc-table-cell>
                        </template>
                        <template #type="{ trData }">
                            <abc-table-cell>
                                {{ formatOrderType(trData) }}
                            </abc-table-cell>
                        </template>
                        <template #refOrderDate="{ trData }">
                            <abc-table-cell>
                                {{ trData.refOrderDate | parseTime('y-m-d') }}
                            </abc-table-cell>
                        </template>
                        <template #orderCreator="{ trData }">
                            <abc-table-cell>
                                {{ trData.orderCreator && trData.orderCreator.name || trData.refOrderUserName }}
                            </abc-table-cell>
                        </template>
                        <template #kindCount="{ trData }">
                            <abc-table-cell>
                                {{ trData.kindCount }}
                            </abc-table-cell>
                        </template>
                        <template #count="{ trData }">
                            <abc-table-cell>
                                {{ trData.count }}
                            </abc-table-cell>
                        </template>
                        <template #amountExcludingTax="{ trData }">
                            <abc-table-cell>
                                {{ trData.amountExcludingTax | formatMoney }}
                            </abc-table-cell>
                        </template>
                        <template #amount="{ trData }">
                            <abc-table-cell>
                                {{ trData.amount | formatMoney }}
                            </abc-table-cell>
                        </template>
                        <template #tax="{ trData }">
                            <abc-table-cell>
                                {{ trData.tax | formatMoney }}
                            </abc-table-cell>
                        </template>
                        <template #orderClinic="{ trData }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="formatClinicName(trData.orderClinic)">
                                    {{ formatClinicName(trData.orderClinic) }}
                                </span>
                            </abc-table-cell>
                        </template>
                        <template #footer>
                            <abc-flex
                                align="center"
                                flex="1"
                                justify="flex-end"
                                style="padding: 0 12px;"
                            >
                                <abc-space :size="4">
                                    <abc-text theme="gray">
                                        单据数量
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.settlements.length }}
                                    </abc-text>
                                    <abc-text theme="gray">
                                        ，价税合计(总计)
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.amount | formatMoney }}
                                    </abc-text>
                                    <abc-text theme="gray">
                                        ，金额(总计)
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.amountExcludingTax | formatMoney }}
                                    </abc-text>
                                    <abc-text theme="gray">
                                        ，税额(总计)
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.tax | formatMoney }}
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>


            <div slot="footer" class="dialog-footer">
                <abc-flex align="center" justify="space-between" flex="1">
                    <order-change-log :logs="order.logs"></order-change-log>
                    <abc-space>
                        <template v-if="order.status === 0">
                            <abc-button @click="reviewSettlement('pass')">
                                通过
                            </abc-button>
                            <abc-button type="blank" @click="reviewSettlement('refuse')">
                                不通过
                            </abc-button>
                        </template>
                        <template v-if="order.status === 1">
                            <abc-button type="danger" @click="trashSettlement">
                                撤销结算
                            </abc-button>
                        </template>
                        <print-popper
                            :options="printOptions"
                            print-text="打印"
                            @print="handlePrint"
                            @select-print-setting="openPrintSettingsDialog"
                        ></print-popper>
                        <abc-button type="blank" @click="showDialog = false">
                            关闭
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </div>
        </abc-dialog>
        <invoice-dialog
            v-if="showInvoiceDialog"
            v-model="showInvoiceDialog"
            title="新增发票"
            @change="addInvoice"
        ></invoice-dialog>
        <review-dialog
            v-if="showReviewDialog"
            v-model="showReviewDialog"
            title="审核"
            :pass="reviewData.pass"
            pass-message="点击确定，将更新结算单状态为已结算"
            refuse-message="点击确定，将更新结算单状态为不通过"
            placeholder="请输入不通过原因"
            @confirm="confirmSettlement"
        ></review-dialog>

        <!-- <settlement-detail-dialog
            v-if="showSettlementDetailDialog"
            v-model="showSettlementDetailDialog"
            :title="currentSettlementDetail ? '编辑结算明细' : '新增结算明细'"
            :type="currentSettlementDetail ? 'edit' : 'add'"
            :settlement-detail="currentSettlementDetail"
            @change="updateSettlementDetail"
        ></settlement-detail-dialog> -->
    </div>
</template>

<script>
    // API
    import SettlementAPI from 'api/goods/settlement.js';
    import SupplierAPI from 'api/goods/supplier.js';

    // components
    import Invoice from './invoice';
    import InvoiceDialog from './invoice-dialog';
    import ReviewDialog from './review-dialog';
    // import SettlementDetailDialog from './settlement-detail-dialog';
    import OrderChangeLog from '../common/change-log/change-log';

    // js
    import {
        SETTLEMENT_DETAIL_CONFIG,
        SETTLEMENT_ENCLOSURE_CONFIG,
        formatOrderType,
        formatClinicName,
    } from './common.js';
    // import Clone from 'utils/clone';
    import { digitUppercase } from 'utils/index';
    import { CHECK_IN_SUPPLIER_ID } from 'views/inventory/constant.js';
    import PrintPopper from 'views/print/popper.vue';
    import { mapGetters } from 'vuex';
    import EnterEvent from 'views/common/enter-event';
    const InventoryPrintApiModule = () => import('@/printer/print-api/inventory');
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');

    export default {
        name: 'SettlementDetail',
        components: {
            PrintPopper,
            Invoice,
            InvoiceDialog,
            ReviewDialog,
            // SettlementDetailDialog,
            OrderChangeLog,
        },
        mixins: [EnterEvent],
        props: {
            value: [String, Boolean],
            id: [String, Number],
        },

        data() {
            return {
                CHECK_IN_SUPPLIER_ID,
                order: {
                    items: [],
                    invoices: [],
                    settlements: [],
                    logs: [],
                },
                loading: false,
                currentSuppliers: [],
                showInvoiceDialog: false,
                reviewData: {
                    pass: true,
                    rejectReason: '',
                },
                showReviewDialog: false,
                showSettlementDetailDialog: false,
                currentSettlementDetail: '',
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            dialogContentStyles() {
                let sty = 'padding: 24px; height: 624px;';
                sty += `width: ${window.screen.width <= 1024 ? 1008 : 1200}px;`;
                return sty;
            },
            dialogTitle() {
                if (this.order.orderNo) {
                    // <span class="goods-small-title">待总部审核</span>
                    return `结算单${this.order.orderNo}`;
                }
                return '结算单';
            },
            count() {
                let count = 0;
                this.order.settlements.forEach((item) => {
                    count += Number(item.count);
                });
                return count;
            },
            totalAmountExcludingTax() {
                let amount = 0;
                this.order.items.forEach((item) => {
                    amount += Number(item.amountExcludingTax);
                });
                return amount;
            },
            totalTax() {
                let tax = 0;
                this.order.items.forEach((item) => {
                    tax += Number(item.tax);
                });
                return tax;
            },
            totalAmount() {
                return this.totalAmountExcludingTax + this.totalTax;
            },
            distributePrintOptions() {
                return this.viewDistributeConfig.Print.printOptions;
            },
            printOptions() {
                const options = this.distributePrintOptions;
                return [
                    {
                        value: options.SETTLEMENT_APPLICATION.value,
                        disabled: false,
                    },
                    {
                        value: options.SETTLEMENT_REVIEW.value,
                        disabled: false,
                    },
                ];
            },
        },
        created() {
            this.filterSuppliers();
            this.settlementDetailConfig = SETTLEMENT_DETAIL_CONFIG;
            this.settlementEnclosureConfig = SETTLEMENT_ENCLOSURE_CONFIG;
            this.fetchDetail();
        },
        methods: {
            digitUppercase,
            formatOrderType,
            formatClinicName,
            async fetchDetail() {
                try {
                    this.loading = true;
                    const { data } = await SettlementAPI.fetchSettlementDetail(this.id);
                    this.order = data;

                    this.$nextTick(() => {
                        this.$refs.settlementDialog.updateDialogHeight();
                    });
                    this.loading = false;
                } catch (e) {
                    console.log('settlement fetchDetail', e);
                    this.loading = false;
                }
            },
            async filterSuppliers(keyword = '') {
                try {
                    keyword = keyword.trim();
                    const params = {
                        keyword,
                        clinicId: this.clinicId,
                        status: '',
                    };
                    const { data } = await SupplierAPI.searchSupplier(params);
                    this.currentSuppliers =
                        (data &&
                            data.rows &&
                            data.rows.filter((item) => {
                                return !!item && item.id !== this.CHECK_IN_SUPPLIER_ID;
                            })) ||
                        [];
                } catch (err) {
                    this.currentSuppliers = [];
                }
            },

            deleteSettlement(index) {
                if (this.order.invoices) {
                    this.order.invoices.splice(index, 1);
                }
            },
            /**
             * @desc 在结算单未结算的情况下，可以新增发票
             * <AUTHOR>
             * @date 2019/11/29
             */
            openInvoiceDialog() {
                this.showInvoiceDialog = true;
            },

            addInvoice(val) {
                this.order.invoices.push(val);
            },
            /**
             * @desc 审核结算单
             * <AUTHOR>
             * @date 2019/11/29
             * @params pass  通过  reject 不通过
             * @return
             */
            reviewSettlement(type) {
                // 如果是可编辑状态，校验表格
                this.$refs.reviewForm.validate((val) => {
                    if (val) {
                        this.reviewData.pass = Boolean(type === 'pass');
                        this.showReviewDialog = true;
                    }
                });
            },
            /**
             * @desc  审核结算单
             * <AUTHOR>
             * @date 2019/11/29
             * @params
             * @return
             */
            async confirmSettlement(reason = '') {
                this.reviewData.rejectReason = reason;
                const postData = {
                    invoices: [],
                    items: [],
                    pass: this.reviewData.pass,
                    rejectReason: this.reviewData.rejectReason,
                };
                postData.invoices = this.order.invoices.map((item) => {
                    return {
                        id: item.id || '',
                        invoiceNo: item.invoiceNo,
                        invoiceDate: item.invoiceDate,
                    };
                });
                postData.items = this.order.items.map((item) => {
                    return {
                        id: item.id || '',
                        name: item.name,
                        spec: item.spec,
                        unit: item.unit,
                        count: +item.count,
                        unitPrice: +item.unitPrice,
                        amount: +item.amount,
                        taxRat: +item.taxRat,
                        amountExcludingTax: +item.amountExcludingTax,
                        tax: +item.tax,
                    };
                });
                await this.review(postData);
            },
            async review(postData) {
                try {
                    await SettlementAPI.reviewSettlement(this.id, postData);
                    this.$emit('update');
                    this.showReviewDialog = false;
                    this.showDialog = false;
                } catch (e) {
                    this.showReviewDialog = false;
                }
            },
            /**
             * @desc 撤销结算单
             * <AUTHOR>
             * @date 2019/11/29
             * @params id：结算单Id
             * @return
             */
            trashSettlement() {
                try {
                    const title = '点击确定，将更新结算单状态为已作废，可重新提交新的申请';
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: title,
                        onConfirm: async () => {
                            await SettlementAPI.trashSettlement(this.id);
                            this.$emit('update');
                            this.showDialog = false;
                        },
                    });
                } catch (e) {
                    console.warn(e);
                }
            },
            openSettlementDetailDialog() {
                this.order.items.push({
                    name: '药品',
                    spec: '无',
                    unit: '无',
                    count: 1,
                    unitPrice: '',
                    amountExcludingTax: '',
                    taxRat: '',
                    tax: '',
                    amount: '',
                });
                // this.currentSettlementDetail = type === 'add' ? '' : Clone(this.order.items[index]);
                // this.showSettlementDetailDialog = true;
            },
            deleteSettlementDetail(index) {
                if (this.order.items) {
                    this.order.items.splice(index, 1);
                }
            },
            // updateSettlementDetail(type, val) {
            //     if (type === 'add') {
            //         this.order.items.push(val);
            //     } else {
            //         this.order.items = this.order.items.map((item) => {
            //             if (item.id === val.id) {
            //                 item = val;
            //             }
            //             return item;
            //         });
            //     }
            // },
            async openPrintSettingsDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'purchase-settlement' }).generateDialogAsync({ parent: this });
            },
            async handlePrint(printParams) {
                const type = printParams[0];
                const { InventoryPrintApi } = await InventoryPrintApiModule();
                if (type === this.distributePrintOptions.SETTLEMENT_APPLICATION.value) {
                    InventoryPrintApi.printSettlementApplication(this.order);
                } else {
                    InventoryPrintApi.printSettlementReview(this.order);
                }
            },
            // 表单项校验
            validateEmpty(val, callback) {
                const tempVal = val.trim();
                if (tempVal !== '') {
                    callback({
                        validate: true,
                    });
                }
            },
            // 税额 = 价税合计 - 金额
            handleRowInputChange(e, val) {
                const {
                    amount = 0,
                    amountExcludingTax = 0,
                } = val;
                val.tax = amount - amountExcludingTax;
            },
        },
    };
</script>
