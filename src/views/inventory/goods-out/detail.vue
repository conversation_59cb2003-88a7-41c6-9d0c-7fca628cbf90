<template>
    <frame-dialog
        v-if="showDialog"
        v-model="showDialog"
        class="auto-form"
        show-title-append
        :title="dialogTitleOption.title"
        :order-no="dialogTitleOption.orderNo"
        :status-name="dialogTitleOption.statusName"
        :tag-config="dialogTitleOption.tagConfig"
        :right-width="rightWidth"
        :dialog-config="dialogConfig"
    >
        <abc-form
            v-abc-loading.coverOpaque="!order"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-form-item-group is-excel>
                        <abc-descriptions
                            :column="3"
                            :label-width="90"
                            grid
                            size="large"
                            background
                            stretch-last-item
                        >
                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :span="1"
                                label="出库人"
                            >
                                <span>{{ order?.createdUser?.name || '-' }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :span="1"
                                label="出库门店"
                            >
                                <span>{{ clinicName(order?.fromOrgan) }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="multiPharmacyCanUse"
                                content-class-name="ellipsis"
                                :span="1"
                                label="出库库房"
                            >
                                <span>{{ order?.pharmacy?.name || '-' }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="order?.type === GOODS_OUT_ORDER_TYPE.DEPART_CONSUME"
                                content-class-name="ellipsis"
                                :span="1"
                                label="使用人"
                            >
                                <span>{{ doctorName }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="order?.type === GOODS_OUT_ORDER_TYPE.OTHER_OUT"
                                content-class-name="ellipsis"
                                :span="1"
                                label="科室"
                            >
                                <span>{{ order?.toOrgan?.name || '-' }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="order?.type === GOODS_OUT_ORDER_TYPE.OTHER_OUT"
                                content-class-name="ellipsis"
                                :span="1"
                                label="使用人"
                            >
                                <span>{{ order?.toUser?.name || '-' }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                label="备注"
                            >
                                <abc-popover
                                    placement="top-start"
                                    trigger="hover"
                                    theme="yellow"
                                    show-on-overflow
                                >
                                    <template slot="reference">
                                        <abc-text tag="div" class="ellipsis">
                                            {{ commentText }}
                                        </abc-text>
                                    </template>
                                    <div>
                                        {{ commentText }}
                                    </div>
                                </abc-popover>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form-item-group>
                </abc-layout-header>

                <abc-layout-content>
                    <abc-table
                        v-if="order"
                        ref="tableRef"
                        type="excel"
                        cell-size="large"
                        :render-config="headerConfig"
                        :data-list="order.list"
                        empty-size="small"
                        :custom-tr-key="(e)=>e.id"
                        :show-hover-tr-bg="false"
                    >
                        <template #shortId="{ trData: { goods } }">
                            <abc-table-cell class="ellipsis">
                                <overflow-tooltip :content="goods.shortId">
                                </overflow-tooltip>
                            </abc-table-cell>
                        </template>

                        <!--药品名称-->
                        <template
                            #cadn="{
                                trData: {
                                    goods, pharmacy
                                }
                            }"
                        >
                            <display-name-cell
                                :goods="goods"
                                :hover-config="{
                                    openDelay: 500,
                                    showPrice: true,
                                    showShebaoCode: true,
                                    pharmacyNo: pharmacy ? pharmacy.no : order.pharmacy.no
                                }"
                            ></display-name-cell>
                        </template>

                        <!--批次-->
                        <template #batchId="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="(row.stock && row.stock.batchId) || ''">{{ (row.stock && row.stock.batchId) || '' }}</span>
                            </abc-table-cell>
                        </template>
                        <!--库房-->
                        <template #pharmacy="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="(row.pharmacy && row.pharmacy.name) || ''">{{ (row.pharmacy && row.pharmacy.name) || '' }}</span>
                            </abc-table-cell>
                        </template>
                        <!--批号 不可修改-->
                        <template #batchNo="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="(row.stock && row.stock.batchNo) || ''">{{ (row.stock && row.stock.batchNo) || '' }}</span>
                            </abc-table-cell>
                        </template>

                        <!--效期-->
                        <template #expiryDate="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="(row.stock && row.stock.expiryDate) || ''">{{ (row.stock && row.stock.expiryDate) || '' }}</span>
                            </abc-table-cell>
                        </template>

                        <!-- 出库数量 -->
                        <template #count="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title.ellipsis="formatGoodsStock(row.packageCount, row.pieceCount, row.goods)">
                                </span>
                            </abc-table-cell>
                        </template>

                        <!--进价-->
                        <template #packageCostPrice="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <data-permission-control :placeholder="packageCostPricePlaceholder">
                                    <span
                                        v-if="row.inItem"
                                        v-abc-title.ellipsis="`${paddingMoney(row.inItem.useUnitCostPrice)}/${row.inItem.useUnit}`"
                                    ></span>
                                    <span
                                        v-else
                                        v-abc-title.ellipsis="`${
                                            paddingMoney(row.packageCostPrice)
                                        }/${
                                            row.goods.packageUnit || row.goods.pieceUnit
                                        }`"
                                    ></span>
                                </data-permission-control>
                            </abc-table-cell>
                        </template>

                        <!--金额-->
                        <template #totalPrice="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <data-permission-control>
                                    <abc-popover
                                        v-if="!row.stock?.batchId"
                                        class="inventory-popper-wrapper ellipsis"
                                        placement="bottom-end"
                                        trigger="hover"
                                        theme="yellow"
                                        :disabled="disabledLossReportPriceHover"
                                        @show="onPopoverShow(row)"
                                    >
                                        <span slot="reference" v-abc-title.ellipsis="paddingMoney(row.totalCost)" style="text-decoration: underline;"></span>
                                        <div class="order-table-wrapper">
                                            <div class="table-header">
                                                <div class="batch-no">
                                                    批次
                                                </div>
                                                <div class="batch-no">
                                                    生产批号
                                                </div>
                                                <div class="date">
                                                    效期
                                                </div>
                                                <div class="count">
                                                    出库数量
                                                </div>
                                                <div class="package-price">
                                                    金额
                                                </div>
                                            </div>
                                            <div v-abc-loading="popoverLoading" class="table-body">
                                                <div v-for="tempItem in row.detail" :key="tempItem.batchId" class="table-tr">
                                                    <div class="batch-no ellipsis" :title="tempItem.batchId">
                                                        {{ tempItem.batchId }}
                                                    </div>
                                                    <div class="batch-no ellipsis" :title="tempItem.batchNo">
                                                        {{ tempItem.batchNo }}
                                                    </div>
                                                    <div class="date ellipsis" :title="tempItem.expiryDate">
                                                        {{ tempItem.expiryDate }}
                                                    </div>
                                                    <div v-abc-title.ellipsis="formatGoodsStock(tempItem.packageCount, tempItem.pieceCount, row.goods)" class="count">
                                                    </div>
                                                    <inventory-order-fixed-hover-popover
                                                        class="package-price"
                                                        :order-type="CorrectOrderTypeEnum.GoodsOut"
                                                        is-revise-order-hover
                                                        :order-item="getBatchData(row, tempItem.batchId)"
                                                        :order-id="row.id"
                                                    >
                                                        <abc-text
                                                            v-abc-title.ellipsis="paddingMoney(tempItem.totalCost)"
                                                            :theme="getBatchAmountTheme(row.fixedStockOrderList, tempItem.batchId)"
                                                        >
                                                        </abc-text>
                                                    </inventory-order-fixed-hover-popover>
                                                </div>
                                                <div v-if="!row.detail?.length" style="color: #7a8794; text-align: center;">
                                                    暂无数据
                                                </div>
                                            </div>
                                        </div>
                                    </abc-popover>
                                    <inventory-order-fixed-hover-popover
                                        v-else
                                        :order-type="CorrectOrderTypeEnum.GoodsOut"
                                        is-revise-order-hover
                                        :order-item="row"
                                        :order-id="row.id"
                                    >
                                        <abc-text
                                            v-abc-title.ellipsis="paddingMoney(row.totalCost)"
                                            :theme="getAmountTheme(row)"
                                        >
                                        </abc-text>
                                    </inventory-order-fixed-hover-popover>
                                </data-permission-control>
                            </abc-table-cell>
                        </template>

                        <!--报损原因-->
                        <template #lossReportCause="{ trData: row }">
                            <abc-popover
                                placement="top-start"
                                trigger="hover"
                                theme="yellow"
                                show-on-overflow
                            >
                                <template slot="reference">
                                    <abc-table-cell class="ellipsis">
                                        <abc-text>
                                            {{ row.outReason }}
                                        </abc-text>
                                    </abc-table-cell>
                                </template>
                                <div>
                                    {{ row.outReason }}
                                </div>
                            </abc-popover>
                        </template>

                        <template #footer>
                            <abc-flex flex="1" justify="flex-end">
                                <abc-space :size="4" style="padding-right: 12px;">
                                    <abc-text theme="gray">
                                        品种
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.kindCount }}
                                    </abc-text>
                                    <abc-text theme="gray">
                                        ，数量
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ moneyDigit(order.count, 4, false) }}
                                    </abc-text>
                                    <data-permission-control>
                                        <abc-space :size="4">
                                            <abc-text theme="gray">
                                                ，金额
                                            </abc-text>
                                            <abc-text theme="black">
                                                {{ order.amount | formatMoney(false) }}
                                            </abc-text>
                                        </abc-space>
                                    </data-permission-control>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <template #right>
            <abc-layout>
                <approval-flow v-if="!!approvalViewModel.approvalDetail" :inst-detail="approvalViewModel.approvalDetail"></approval-flow>
            </abc-layout>
        </template>

        <abc-flex slot="footer" justify="space-between" style="min-height: 32px;">
            <logs-v3-popover v-if="logs.length" :logs="order.logs"></logs-v3-popover>
            <div v-else></div>
            <abc-space>
                <template v-if="order">
                    <template v-if="canApprove">
                        <abc-button
                            :loading="approvalViewModel.loadingAgree"
                            :disabled="approvalViewModel.loadingReject"
                            @click="handleApprovalAgree"
                        >
                            同意
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            theme="danger"
                            :loading="approvalViewModel.loadingReject"
                            :disabled="approvalViewModel.loadingAgree"
                            @click="handleApprovalReject"
                        >
                            驳回
                        </abc-button>
                    </template>
                    <template v-if="order.status === GOODS_OUT_STATUS.CONFIRM">
                        <abc-button
                            type="primary"
                            :loading="btnLoading"
                            @click="confirmOrderPrev('pass')"
                        >
                            确认出库
                        </abc-button>
                        <abc-button theme="danger" variant="ghost" @click="confirmOrderPrev('fail')">
                            驳回出库
                        </abc-button>
                    </template>
                    <template v-else>
                        <abc-button v-if="canReOrder" variant="ghost" @click="reOrderHandler">
                            修改并重新发起
                        </abc-button>
                        <abc-button
                            v-if="canRevoke"
                            :loading="btnLoading"
                            variant="ghost"
                            theme="danger"
                            @click="revokeHandler"
                        >
                            撤回
                        </abc-button>
                        <abc-check-access>
                            <abc-button variant="ghost" @click="exportExcel">
                                导出
                            </abc-button>
                        </abc-check-access>

                        <abc-check-access>
                            <print-dropdown
                                :loading="printBtnLoading"
                                @print="print"
                                @select-print-setting="openPrintConfigSettingDialog"
                            ></print-dropdown>
                        </abc-check-access>
                    </template>
                </template>

                <abc-button variant="ghost" @click="showDialog = false">
                    关闭
                </abc-button>
            </abc-space>
        </abc-flex>

        <abc-dialog
            v-if="showConfirmDialog"
            v-model="showConfirmDialog"
            title="确认"
            custom-class="goods-confirm"
        >
            <div class="dialog-content clearfix">
                <template v-if="confirmStatus === 1">
                    <p class="confirm-result">
                        确认结果：<span class="confirm-success">确认出库</span>
                    </p>
                    <p class="confirm-result">
                        确认出库后将会实时更新库存信息
                    </p>
                </template>

                <template v-else>
                    <p class="confirm-result">
                        确认结果：<span class="confirm-refuse">驳回</span>
                    </p>

                    <abc-form
                        ref="checkForm"
                        label-position="left"
                        :label-width="100"
                    >
                        <abc-form-item required style="margin-bottom: 0;">
                            <abc-textarea
                                v-model="rejectComment"
                                :width="312"
                                :height="50"
                                :maxlength="100"
                                placeholder="请输入驳回原因"
                            >
                            </abc-textarea>
                        </abc-form-item>
                    </abc-form>
                    <p class="confirm-result" style="margin-top: 16px;">
                        总部可在驳回单据内修改并重新发起
                    </p>
                </template>
            </div>
            <template slot="footer">
                <div class="dialog-footer">
                    <abc-button style="margin-left: auto;" :loading="btnLoading" @click="confirmHandle">
                        确定
                    </abc-button>
                    <abc-button type="blank" @click="showConfirmDialog = false">
                        取消
                    </abc-button>
                </div>
            </template>
        </abc-dialog>
    </frame-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';

    import GoodsAPI from 'api/goods/index';
    import StockOutApi from 'api/goods/stock-out';
    import {
        moneyDigit, paddingMoney,
    } from '@/utils';
    import { formatGoodsStock } from './common.js';
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';

    import {
        clinicName,
        goodsFullName, goodsHoverTitle, goodsSpec, goodsTotalCostPrice, isChineseMedicine,
    } from 'src/filters/goods';
    import GoodsCommon from '../common';

    import Clone from 'utils/clone.js';

    import totalInfo from '../mixins/total-info.js';
    import DeleteGoodsHandler from '../mixins/delete-goods-handler';
    import AbcPrinter from '@/printer';
    import { ABCPrintConfigKeyMap } from '@/printer/constants.js';
    import {
        CorrectOrderTypeEnum,
        GOODS_OUT_ORDER_TYPE,
        GOODS_OUT_ORDER_TYPE_NAME,
        GOODS_OUT_STATUS,
    } from '../constant.js';

    import dialogAutoWidth from 'views/inventory/mixins/dialog-auto-width.js';
    import GoodsTableV3Mixins from 'views/inventory/mixins/goods-table-v3.js';

    import PrintDropdown from 'views/print/print-dropdown';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';

    import {
        DataPermissionMixin,
        createDataPermissionControl,
        DATA_PERMISSION_CONTROL_SCENE,
    } from 'views/inventory/common/data-permission-control';
    import ApprovalFlow from '@/views-pharmacy/components/approval-flow/index.vue';
    import useApprovalOrder from '@/views-pharmacy/common/hooks/approval-order';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    import OverflowTooltip from '@/components/overflow-tooltip.vue';
    import { TagHelper } from 'utils/tag-helper';
    import useReviseOrder from 'views/inventory/hooks/useReviseOrder';

    const { orderMainNameText } = getViewDistributeConfig().Inventory;

    export default {
        name: 'OrderDetail',

        components: {
            ApprovalFlow,
            PrintDropdown,
            // 数据权限控制组件，根据场景生成
            DataPermissionControl: createDataPermissionControl(DATA_PERMISSION_CONTROL_SCENE.damage),
            FrameDialog,
            DisplayNameCell,
            LogsV3Popover,
            OverflowTooltip,
            InventoryOrderFixedHoverPopover: () => import('views/inventory/components/inventory-order-fixed-hover-popover.vue'),
        },

        mixins: [
            totalInfo,
            DeleteGoodsHandler,
            GoodsCommon,
            dialogAutoWidth,
            GoodsTableV3Mixins,
            DataPermissionMixin,
        ],

        props: {
            visible: Boolean,
            orderId: {
                type: [String, Number],
                default: '',
            },
            title: String,
            outType: Number,
            goodsId: {
                type: [String, Number],
                default: '',
            },
            gspInstId: {
                type: String,
                default: '',
            },
        },

        setup() {
            const {
                viewModel,
                canApprove,
                fetchApprovalDetail,
                approvalAgree,
                approvalReject,
            } = useApprovalOrder();

            const {
                hasRevise,
            } = useReviseOrder();

            return {
                hasRevise,

                approvalViewModel: viewModel,
                canApprove,
                fetchApprovalDetail,
                approvalAgree,
                approvalReject,
            };
        },

        data() {
            return {
                GOODS_OUT_STATUS,
                loading: false,
                searchKey: '',
                order: null,
                cloneOrder: {},
                comment: '',
                isFirstPrint: true,
                showConfirmDialog: false,
                confirmStatus: 1,
                rejectComment: '',
                btnLoading: false,
                popoverLoading: false,
                printBtnLoading: false,
                showDialog: this.visible,
                packageCostPricePlaceholder: () => (
                    <div></div>
                ),
            };
        },

        computed: {
            CorrectOrderTypeEnum() {
                return CorrectOrderTypeEnum;
            },
            GOODS_OUT_ORDER_TYPE() {
                return GOODS_OUT_ORDER_TYPE;
            },
            ...mapGetters(['currentClinic', 'multiPharmacyCanUse','userInfo','isChainAdmin']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            disabledLossReportPriceHover() {
                return !this.viewDistributeConfig.Inventory.isShowLossReportPriceHover;
            },
            contentStyle() {
                return {
                    'overflow-y': 'auto',
                };
            },
            dialogConfig() {
                return {
                    size: 'hugely',
                    responsive: true,
                };
            },
            headerConfig() {
                return {
                    list: [
                        {
                            label: `${orderMainNameText}编码`,
                            key: 'shortId',
                            style: {
                                width: '90px',
                            },
                        },
                        {
                            label: `${orderMainNameText}名称`,
                            key: 'cadn',
                            style: {
                                flex: 2,
                                width: '120px',
                                // minWidth: '160px',
                            },
                        },
                        {
                            label: '批次',
                            key: 'batchId',
                            style: {
                                width: '90px',
                            },
                        },
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            style: {
                                width: '90px',
                            },
                        },
                        {
                            label: '效期',
                            key: 'expiryDate',
                            style: {
                                width: '100px',
                            },
                        },
                        {
                            label: '进价',
                            key: 'packageCostPrice',
                            style: {
                                width: '90px',
                                // minWidth: '100px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',

                        },
                        {
                            label: '出库数量',
                            key: 'count',
                            width: 80,
                            style: {
                                width: '80px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',

                        },
                        {
                            label: '金额',
                            key: 'totalPrice',
                            width: 80,
                            style: {
                                width: '80px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                        },
                        {
                            label: '报损原因',
                            key: 'lossReportCause',
                            style: {
                                width: '90px',
                                maxWidth: '90px',
                                textAlign: 'left',
                            },
                        },
                    ].filter((config) => {
                        if (config.key === 'lossReportCause') {
                            return this.isLossReportOut;
                        }
                        return true;
                    }),
                };
            },
            logs() {
                return this.order?.logs ?? [];
            },

            commentText() {
                let commentText = '-';
                const { logs } = this.order || {};
                if (logs?.length) {
                    commentText = logs[logs.length - 1]?.comment || '-';
                }
                return commentText;
            },
            doctorName() {
                if (this.order?.toOrgan) {
                    return `${this.order.toOrgan?.name ?? '-'}/${this.order?.toUser?.name ?? '-'}`;
                }
                return this.order?.toUser?.name ?? '-';
            },
            dialogTitleOption() {
                const title = this.title ? this.title : `${GOODS_OUT_ORDER_TYPE_NAME[this.outType]}单`;
                let orderNo = '';
                let statusName = '';
                let tagConfig = TagHelper.ING_TAG;

                if (this.order) {
                    orderNo = this.order.orderNo;

                    if (
                        this.order.status === GOODS_OUT_STATUS.CONFIRM &&
                        this.order.fromOrgan &&
                        (this.order.fromOrgan.name || this.order.fromOrgan.shortName)
                    ) {
                        statusName = `待${this.order.fromOrgan.shortName || this.order.fromOrgan.name}确认`;
                        tagConfig = TagHelper.TODO_TAG;// 子店打开为待确认状态可操作，总部会打开form.vue
                    }
                    if (this.order.status === GOODS_OUT_STATUS.REVIEW) {
                        statusName = '审批中';
                        tagConfig = this.canApprove ? TagHelper.TODO_TAG : TagHelper.ING_TAG;
                    }
                    if (this.order.status === GOODS_OUT_STATUS.REVOKE) {
                        statusName = '已撤回';
                        tagConfig = TagHelper.CANCEL_TAG;
                    }
                    if (this.order.status === GOODS_OUT_STATUS.REFUSE) {
                        statusName = '已驳回';
                        tagConfig = TagHelper.REFUSE_TAG;
                    }
                }
                return {
                    title,
                    orderNo,
                    statusName,
                    tagConfig,
                };
            },
            // 是否为单据发起方，需要后端记录applyClinicId
            isApplyClinic() {
                return this.clinicId === this.order?.applyClinicId;
            },
            allowReOrder() {
                return this.viewDistributeConfig.Inventory.allowReOrder;
            },
            // 已撤回/已拒绝状态，发起方可以修改并重新发起
            canReOrder() {
                // 如果不允许重新发起
                if (!this.allowReOrder) {
                    return false;
                }
                if ((this.order?.status === GOODS_OUT_STATUS.REFUSE || this.order?.status === GOODS_OUT_STATUS.REVOKE)) {
                    return this.isApplyClinic;
                }
                return false;
            },
            // 待审核/待确定状态，发起方可以撤回单据
            canRevoke() {
                // 如果不允许重新发起
                if (!this.allowReOrder) {
                    return false;
                }
                // 新判断逻辑
                // if (this.order?.status === GOODS_OUT_STATUS.REVIEW || this.order?.status === GOODS_OUT_STATUS.CONFIRM) {
                //     return this.isApplyClinic;
                // }
                const isMySelf = this.order.createdUser.id === this.userInfo.id;
                // 发起方是总部
                if (this.order?.status === GOODS_OUT_STATUS.CONFIRM) {
                    return this.isChainAdmin && isMySelf;
                }

                // 发起方是门店
                if (this.order?.status === GOODS_OUT_STATUS.REVIEW) {
                    return !this.isChainAdmin && isMySelf;
                }

                return false;
            },
            goodsDescriptionsColumns() {
                const {
                    type,pharmacy, createdUser, fromOrgan, toOrgan, toUser, logs,
                } = this.order || {};
                let commentText = '-';

                if (logs && logs.length) {
                    commentText = logs[logs.length - 1]?.comment || '-';
                }
                return [
                    {
                        prop: 'userName',
                        label: '出库人：',
                        value: createdUser?.name,
                    },
                    {
                        prop: 'clinicName',
                        label: '出库门店：',
                        value: clinicName(fromOrgan) ,
                        style: {
                            maxWidth: '210px',
                        },
                    },
                    {
                        prop: 'pharmacy',
                        label: '出库库房：',
                        value: pharmacy?.name,
                        style: {
                            maxWidth: '140px',
                        },
                    },
                    {
                        // 科室/使用人
                        prop: 'consumer',
                        label: '使用人：',
                        style: {
                            maxWidth: '160px',
                        },
                    },
                    {
                        prop: 'department',
                        label: '科室：',
                        value: toOrgan?.name,
                        style: {
                            maxWidth: '160px',
                        },
                    },
                    {
                        prop: 'doctor',
                        label: '使用人：',
                        value: toUser?.name,
                        style: {
                            maxWidth: '160px',
                        },
                    },
                    {
                        prop: 'comment',
                        label: '备注：',
                        value: commentText ,
                        style: {
                            maxWidth: '210px',
                        },
                    },

                ].filter((item) => {
                    if (item.prop === 'pharmacy') {
                        return this.multiPharmacyCanUse;
                    }
                    if (item.prop === 'doctor' || item.prop === 'department') {
                        return type === GOODS_OUT_ORDER_TYPE.OTHER_OUT;
                    }
                    if (item.prop === 'consumer') {
                        return type === GOODS_OUT_ORDER_TYPE.DEPART_CONSUME;
                    }
                    return true;
                });
            },

            rightWidth() {
                if (this.gspInstId) return 320;
                return 0;
            },
            isLossReportOut() {
                return this.order?.type === GOODS_OUT_ORDER_TYPE.LOSS_OUT;
            },
        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
                this.$emit('update:visible', val);
                if (!val) {
                    this.$emit('close');
                }
            },
        },
        created() {
            this.fetchOrder();
            if (this.gspInstId) {
                this.fetchApprovalDetail(this.gspInstId);
            }
        },

        methods: {
            moneyDigit,
            clinicName,
            goodsFullName,
            goodsSpec,
            goodsTotalCostPrice,
            isChineseMedicine,
            paddingMoney,
            formatGoodsStock,
            getAmountTheme(item) {
                return item.fixedStockOrderList?.length ? 'warning-light' : 'black';
            },
            getBatchAmountTheme(fixedStockOrderList = [], batchId) {
                return fixedStockOrderList.find((item) => item.list?.[0]?.batchId === batchId) ? 'warning-light' : 'gray';
            },
            getBatchData(item, batchId) {
                const _item = { ...item };
                _item.fixedStockOrderList = item.fixedStockOrderList?.filter((e) => e.list?.[0]?.batchId === batchId) ?? [];

                return _item;
            },
            async onPopoverShow(item) {
                // 可能为可能为空数组，说明请求过了
                if (item.detail) return;

                try {
                    this.popoverLoading = true;
                    const { data } = await GoodsAPI.fetchGoodsBatchList(item.goodsId,{
                        actionType: 10,
                        orderId: item.orderId,
                        orderItemId: item.id,
                        fromOrganId: this.order.fromOrganId,
                    });

                    this.$set(item,'detail',data?.rows ?? []);
                } catch (e) {
                    console.log(e);
                } finally {

                    this.popoverLoading = false;
                }


            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'goods-out' }).generateDialogAsync({ parent: this });
            },

            async exportExcel() {
                return StockOutApi.exportById(this.orderId);
            },
            confirmOrderPrev(type) {
                if (type === 'pass') {
                    this.confirmStatus = 1;
                }
                if (type === 'fail') {
                    this.confirmStatus = 0;
                }
                this.showConfirmDialog = true;
            },
            async confirmHandle() {
                if (this.confirmStatus === 1) {
                    await this.confirmOrder();
                }
                if (this.confirmStatus === 0) {
                    this.$refs.checkForm.validate(async (val) => {
                        if (val) {
                            await this.rejectOrder();
                        }
                    });
                }
            },

            /**
             * @desc 确认出库单
             * <AUTHOR>
             * @date 2018/11/21 20:18:17
             */
            async confirmOrder() {
                try {
                    this.btnLoading = true;
                    await StockOutApi.confirmOrder(this.orderId, {
                        comment: '',
                        lastModifiedDate: this.order.lastModifiedDate,
                    });
                    this.btnLoading = false;
                    this.showDialog = false;
                    this.$emit('refresh');
                } catch (e) {
                    this.btnLoading = false;
                    if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail);
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail);
                    } else if (e.code === 962) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前出库单已被确认，已经刷新到新版本，请再次确认',
                            onClose: () => {
                                this.fetchOrder();
                            },
                        });
                    } else if (e.code === 12010) {
                        const shortageMedicineTips = [];
                        const goodsStock = (e && e.detail) || [];
                        const { length } = goodsStock;

                        goodsStock.forEach((item, index) => {
                            let str = '';
                            str = `${item.goods.medicineCadn || item.goods.name || ''}`;
                            if (length - 1 === index) {
                                str += ' 库存不足';
                            }
                            shortageMedicineTips.push(str);
                        });
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: shortageMedicineTips,
                            onClose: () => {
                                this.fetchOrder();
                            },
                        });
                    } else if (e.code === 470) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${e.message}`,
                        });
                    }
                }
            },

            /**
             * @desc 拒绝出库单
             * <AUTHOR>
             * @date 2019/09/17 09:59:47
             */
            async rejectOrder() {
                try {
                    this.btnLoading = true;
                    await StockOutApi.rejectOrder(this.orderId, {
                        comment: this.rejectComment,
                        lastModifiedDate: this.order.lastModifiedDate,
                    });
                    this.btnLoading = false;
                    this.showDialog = false;
                    this.$emit('refresh');
                } catch (e) {
                    this.btnLoading = false;
                }
            },

            async fetchOrder() {
                if (!this.orderId) return false;
                this.loading = true;
                this.order = await StockOutApi.getById(this.orderId);

                if (this.goodsId) {
                    this.sortOrderList();
                }
                this.isFirstPrint = true;
                this.loading = false;
            },
            print() {
                let printData = Clone(this.order);
                // 是否产生修正
                const hasRevise = this.hasRevise(printData?.externalFlag);
                this.printBtnLoading = false;

                const printAction = async () => {
                    try {
                        printData.comment = '';
                        if (printData.logs && printData.logs.length) {
                            printData.comment = printData.logs[printData.logs.length - 1]?.comment || '';
                        }
                        printData.multiPharmacyCanUse = !!this.multiPharmacyCanUse;

                        if (hasRevise) {
                            this.printBtnLoading = true;
                            // 获取合并后数据
                            const fullOrder = await StockOutApi.getById(this.orderId, {
                                needMergedOrder: 1,
                            });

                            printData = {
                                ...fullOrder,
                                comment: printData.comment || '',
                                multiPharmacyCanUse: printData.multiPharmacyCanUse,
                            };
                        }

                        if (!this.sceneVisible[DATA_PERMISSION_CONTROL_SCENE.damage]) {
                            printData = this.filterPrintData(
                                printData,
                                [
                                    'amount',
                                    'amountExcludingTax',
                                    {
                                        destinationField: 'list',
                                        fields: [
                                            'totalCost',
                                            'totalCostE',
                                            'useTotalCostPrice',
                                            'useTotalCostPriceE',
                                            'packageCostPrice',
                                            {
                                                destinationField: 'stock',
                                                fields: ['packageCostPrice'],
                                            },
                                            {
                                                destinationField: 'inItem',
                                                fields: ['useUnitCostPrice'],
                                            },
                                        ],
                                    },
                                ],
                            );
                        }

                        AbcPrinter.abcPrint({
                            templateKey: window.AbcPackages.AbcTemplates.goodsOut,
                            printConfigKey: ABCPrintConfigKeyMap.CK,
                            data: printData,
                        });

                    } catch (e) {
                        console.error('打印失败:', e);
                        this.$Toast({
                            type: 'error',
                            message: '打印失败，请重试',
                        });
                    } finally {
                        this.printBtnLoading = false;
                    }
                };

                // 保持原有的确认逻辑
                if (hasRevise) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '该出库单存在修改过的药品，将基于正确出库信息打印',
                        onConfirm: () => {
                            printAction();
                        },
                    });
                } else {
                    printAction();
                }
            },
            sortOrderList() {
                const newOrderList = Clone(this.order.list);
                newOrderList.sort((a, b) => {
                    return (b.goodsId === this.goodsId) - (a.goodsId === this.goodsId);
                });
                this.order.list = Clone(newOrderList);
            },
            formatGoodsNameSpec(goods) {
                if (!goods) return '';
                return `${goodsHoverTitle(goods)} ${goodsSpec(goods)}`;
            },
            reOrderHandler() {
                // this.showDialog = false;
                this.$emit('resubmit', {
                    orderId: this.order?.id,
                    outType: this.order?.type,
                });
            },
            revokeHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确认撤回后单据将失效，可在此单据内修改并重新发起',
                    onConfirm: async () => {
                        await this.revoke();
                    },
                });
            },
            async revoke() {
                try {
                    this.btnLoading = true;
                    await StockOutApi.revokeOrder(this.orderId);
                    this.btnLoading = false;
                    this.fetchOrder();
                    this.$emit('refresh',false, '', false);

                } catch (e) {
                    this.btnLoading = false;
                }
            },

            async handleApprovalAgree() {
                const response = await this.approvalAgree({
                    gspInstId: this.gspInstId,
                });
                if (response.status === false) {
                    return;
                }
                this.showDialog = false;
                this.$emit('confirm');
                this.$emit('refresh');
            },

            async handleApprovalReject() {
                const response = await this.approvalReject(this.gspInstId);
                if (response.status === false) {
                    return;
                }
                this.showDialog = false;
                this.$emit('confirm');
                this.$emit('refresh');
            },
        },
    };
</script>
