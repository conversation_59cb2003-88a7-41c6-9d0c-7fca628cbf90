<template>
    <frame-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitleOption.title"
        :order-no="dialogTitleOption.orderNo"
        :status-name="dialogTitleOption.statusName"
        :tag-config="dialogTitleOption.tagConfig"
        :right-width="rightWidth"
        show-title-append
        class="auto-form"
        :dialog-config="dialogConfig"
    >
        <abc-form
            ref="createForm"
            v-abc-loading.coverOpaque="loading"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-form-item-group is-excel>
                        <abc-descriptions
                            :column="3"
                            :label-width="90"
                            grid
                            size="large"
                            background
                            stretch-last-item
                        >
                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :span="1"
                                label="出库人"
                            >
                                <span>{{ order?.createdUser?.name || userInfo?.name }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :content-padding="isChainAdmin && isAdd && [GOODS_OUT_ORDER_TYPE.LOSS_OUT, GOODS_OUT_ORDER_TYPE.UNQUALIFIED].includes(outType) ? '0px' : ''"
                                :span="1"
                                label="出库门店"
                            >
                                <template v-if="isChainAdmin && [GOODS_OUT_ORDER_TYPE.LOSS_OUT, GOODS_OUT_ORDER_TYPE.UNQUALIFIED].includes(outType)">
                                    <abc-form-item v-if="isAdd" required :show-red-dot="false">
                                        <clinic-select
                                            v-model="cacheFromOrganId"
                                            :show-all-clinic="false"
                                            :filter-chain-clinics="filterChainClinics"
                                            :disabled="filterChainClinics || isGspReportLoss"
                                            :no-icon="(filterChainClinics || isGspReportLoss)"
                                            @change="changeClinic"
                                        ></clinic-select>
                                    </abc-form-item>
                                    <span v-else v-abc-title=" order.fromOrgan && (order.fromOrgan.shortName || order.fromOrgan.name) "></span>
                                </template>
                                <span
                                    v-else
                                    v-abc-title="isUpdate ? order.fromOrgan && (order.fromOrgan.shortName || order.fromOrgan.name) : currentClinic?.clinicName"
                                ></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="multiPharmacyCanUse"
                                content-class-name="ellipsis"
                                :span="1"
                                label="出库库房"
                            >
                                <span>{{ order?.pharmacy?.name }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="outType === GOODS_OUT_ORDER_TYPE.DEPART_CONSUME"
                                content-class-name="ellipsis"
                                :content-padding="isAdd ? '0px' : ''"
                                :span="1"
                                label="使用人"
                            >
                                <abc-form-item
                                    v-if="isAdd"
                                    class="apply-doctor-form-item"
                                    required
                                >
                                    <abc-cascader
                                        v-if="hasDepartment"
                                        v-model="doctorSelector"
                                        class="custom-cascader"
                                        :props="{
                                            children: 'childs',
                                            label: 'name',
                                            value: 'id'
                                        }"
                                        placeholder="请选择"
                                        separation="/"
                                        :options="doctors"
                                        @change="consumerChange"
                                    >
                                    </abc-cascader>

                                    <abc-select
                                        v-else
                                        v-model="order.toUserId"
                                        placeholder="请选择"
                                        clearable
                                        @change="consumerChange"
                                    >
                                        <abc-option
                                            v-for="doc in doctors"
                                            :key="doc.id"
                                            :label="doc.name"
                                            :value="doc.id"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="doctorName"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="outType === GOODS_OUT_ORDER_TYPE.OTHER_OUT"
                                content-class-name="ellipsis"
                                :content-padding="isAdd ? '0px' : ''"
                                :span="1"
                                label="出库科室"
                            >
                                <abc-form-item v-if="isAdd">
                                    <abc-select
                                        v-model="order.toOrganId"
                                        :max-height="250"
                                        clearable
                                    >
                                        <abc-option
                                            v-for="item in departmentsOptions"
                                            :key="item.id"
                                            :label="item.name"
                                            :value="item.id"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="`${order.toOrgan && order.toOrgan.name || '-'}`"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="outType === GOODS_OUT_ORDER_TYPE.OTHER_OUT"
                                content-class-name="ellipsis"
                                :content-padding="isAdd ? '0px' : ''"
                                :span="1"
                                label="使用人"
                            >
                                <abc-form-item v-if="isAdd">
                                    <abc-select
                                        v-model="order.toUserId"
                                        :max-height="250"
                                        :fetch-suggestions="handleSearchDoctor"
                                        with-search
                                        clearable
                                        @change="doctorChange"
                                    >
                                        <abc-option
                                            v-for="doc in currentEmployees"
                                            :key="doc.employeeId"
                                            :label="doc.employeeName"
                                            :value="doc.employeeId"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <span v-else v-abc-title="`${order.toUser && order.toUser.name || '-'}`"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :content-padding="isAdd ? '0px' : ''"
                                label="备注"
                            >
                                <abc-form-item v-if="isAdd">
                                    <abc-input
                                        v-model="order.comment"
                                        :disabled="isGspReportLoss"
                                        :max-length="200"
                                    ></abc-input>
                                </abc-form-item>
                                <span v-else :title="order.comment">{{ order.comment || '-' }}</span>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        :render-config="headerConfig"
                        :data-list="order.list"
                        empty-size="small"
                        :custom-tr-key="createRowKey"
                        :custom-tr-class="()=>`abc-table-tr--out-focus`"
                        child-key="batchs"
                        cell-size="large"
                        :show-checked="false"
                        :show-hover-tr-bg="false"
                        need-delete-confirm
                        :support-delete-tr="!isGspReportLoss && isAdd"
                        @delete-tr="deleteTr"
                    >
                        <template v-if="!isGspReportLoss && (isAdd || isShowRightBtn)" #topHeader>
                            <abc-flex
                                flex="1"
                                align="center"
                                justify="space-between"
                            >
                                <goods-auto-complete-cover-title
                                    v-if="!isGspReportLoss && isAdd"
                                    class="entry-medicine back-focus-to-autocomplete"
                                    is-out
                                    :placeholder="`请输入${orderMainNameText}名称或扫码添加`"
                                    :search.sync="searchKey"
                                    :width="460"
                                    :clinic-id="order.fromOrganId"
                                    :pharmacy-no="order.pharmacy.no"
                                    :only-stock="true"
                                    :need-search-no-stock="false"
                                    enable-local-search
                                    size="medium"
                                    :next-input-auto-focus="false"
                                    @selectGoods="selectGoods"
                                >
                                    <abc-icon slot="prepend" icon="n-add-line-medium" color="var(--abc-color-T3)"></abc-icon>
                                </goods-auto-complete-cover-title>

                                <abc-button
                                    v-if="!isGspReportLoss && isShowRightBtn"
                                    variant="ghost"
                                    theme="default"
                                    :count="warnTotal"
                                    @click="handleEarlyClick"
                                >
                                    效期预警药品
                                </abc-button>
                            </abc-flex>
                        </template>

                        <template
                            #shortId="{
                                trData: row, parentData
                            }"
                        >
                            <abc-table-cell v-if="!parentData" class="ellipsis">
                                <overflow-tooltip :content="row.goods.shortId">
                                </overflow-tooltip>
                            </abc-table-cell>
                        </template>

                        <template
                            #cadn="{
                                trData: row, parentData
                            }"
                        >
                            <display-name-cell
                                v-if="!parentData"
                                :goods="row.goods"
                                :hover-config="{
                                    openDelay: 500,
                                    showPrice: true,
                                    showShebaoCode: true,
                                    pharmacyNo: order.pharmacy ? order.pharmacy.no : pharmacyNo
                                }"
                            ></display-name-cell>
                        </template>

                        <template
                            #batchId="{
                                trData: row, parentData
                            }"
                        >
                            <abc-table-cell v-if="isUpdate" class="ellipsis">
                                <span
                                    :title="(row.stock && row.stock.batchId) || ''"
                                >
                                    {{ (row.stock && row.stock.batchId) || '' }}
                                </span>
                            </abc-table-cell>

                            <abc-form-item v-else-if="!parentData" :required="needValidateGoodsOutBatch">
                                <batch-select
                                    v-model="row.batchs"
                                    :goods="row.goods"
                                    style="height: 100%;"
                                    :disabled="isGspReportLoss"
                                    :options="row.batchOptions"
                                    :data-permission-scene="DATA_PERMISSION_CONTROL_SCENE.damage"
                                    @change="(select) => changeBatchs(select, row)"
                                ></batch-select>
                            </abc-form-item>

                            <!--批次数据-->
                            <abc-table-cell v-else>
                                <span
                                    class="ellipsis"
                                    :title="row.batchId"
                                >
                                    {{ row.batchId }}
                                </span>
                            </abc-table-cell>
                        </template>

                        <!--生产批号-->
                        <template #batchNo="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-if="isUpdate" :title="(row.stock && row.stock.batchNo) || ''">{{ (row.stock && row.stock.batchNo) || '' }}</span>
                                <span v-else :title="row.batchNo">{{ row.batchNo }}</span>
                            </abc-table-cell>
                        </template>
                        <!--效期-->
                        <template #expiryDate="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-if="isUpdate" :title="(row.stock && row.stock.expiryDate) || ''">{{ (row.stock && row.stock.expiryDate) || '' }}</span>
                                <span v-else :title="row.expiryDate">{{ row.expiryDate }}</span>
                            </abc-table-cell>
                        </template>
                        <!--当前库存-->
                        <template #currentCount="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span
                                    v-abc-title="formatGoodsStock(
                                        row.goods.packageCount,
                                        row.goods.pieceCount,
                                        row.goods
                                    )"
                                ></span>
                            </abc-table-cell>
                        </template>
                        <!--数量-->
                        <template
                            #count="{
                                trData: row, parentData
                            }"
                        >
                            <template v-if="isAdd">
                                <abc-form-item
                                    v-if="!row.batchs || !row.batchs.length"
                                    :required="!row.packageCount && !row.pieceCount"
                                    :validate-event="(_, callback)=>validateStock(row, parentData ? 1 : 0, callback)"
                                    style="margin: 0;"
                                >
                                    <abc-flex v-if="isEditLossReportBatch || parentData" style="height: 100%;">
                                        <template v-if="!isChineseMedicine(row.goods)">
                                            <abc-input
                                                key="input1"
                                                v-model.number="row.packageCount"
                                                v-abc-focus-selected
                                                class="count-input"
                                                :input-custom-style="{ textAlign: 'center' }"
                                                :width="74"
                                                :disabled="isGspReportLoss"
                                                type="number"
                                                :config="getConfig(row.goods)"
                                                @enter="enterEvent"
                                            >
                                                <div slot="appendInner">
                                                    {{ row.goods.packageUnit }}
                                                </div>
                                            </abc-input>
                                        </template>
                                        <template v-if="!unitEqual(row.goods) || isChineseMedicine(row.goods)">
                                            <abc-input
                                                key="input2"
                                                v-model.number="row.pieceCount"
                                                v-abc-focus-selected
                                                :input-custom-style="{ textAlign: 'center' }"
                                                :width="74"
                                                :disabled="isGspReportLoss"
                                                type="number"
                                                class="count-input"
                                                :config="getConfig(row.goods)"
                                                @enter="enterEvent"
                                            >
                                                <div slot="appendInner">
                                                    {{ row.goods.pieceUnit }}
                                                </div>
                                            </abc-input>
                                        </template>
                                    </abc-flex>
                                </abc-form-item>

                                <abc-form-item
                                    v-else
                                    :validate-event="(_, callback)=>validateTotalStock(row, callback)"
                                >
                                    <!--通过value的值控制校验-->
                                    <abc-input :value="compareInventoryCount(row) ? '' : 'ps'" style="visibility: hidden;"></abc-input>
                                    <abc-flex
                                        align="center"
                                        justify="flex-end"
                                        style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; padding: 0 8px; line-height: 16px;"
                                    >
                                        <span
                                            v-abc-title.ellipsis="getBatchsTotalCount(row.batchs, row.goods)"
                                            :style="{
                                                color: compareInventoryCount(row) ? 'var(--abc-color-Y2)' : 'var(--abc-color-T1)'
                                            }"
                                        ></span>
                                    </abc-flex>
                                </abc-form-item>
                            </template>

                            <abc-table-cell v-else class="ellipsis">
                                <abc-flex align="center" justify="flex-end">
                                    <span v-abc-title="formatGoodsStock(row.packageCount, row.pieceCount, row.goods)"></span>
                                </abc-flex>
                            </abc-table-cell>
                        </template>
                        <!--金额-->
                        <template
                            #totalPrice="{
                                trData: row, parentData
                            }"
                        >
                            <template v-if="isCanSeeDamageGoodsPriceInInventory">
                                <abc-table-cell v-if="!parentData" class="ellipsis">
                                    <span :title="calcTotalPrice(row) | formatMoney(false)">
                                        {{ calcTotalPrice(row) | formatMoney(false) }}
                                    </span>
                                </abc-table-cell>
                                <abc-table-cell v-else class="ellipsis">
                                    <span :title="calcBatchPrice(row, parentData) | formatMoney(false)">
                                        {{ calcBatchPrice(row, parentData) | formatMoney(false) }}
                                    </span>
                                </abc-table-cell>
                            </template>
                            <template v-else>
                                <abc-table-cell>
                                    <span></span>
                                </abc-table-cell>
                            </template>
                        </template>
                        <!--报损原因-->
                        <template
                            #lossReportCause="{
                                trData: row, parentData
                            }"
                        >
                            <template v-if="!isGspReportLoss && isAdd">
                                <abc-form-item v-if="isEditLossReportBatch || parentData" class="ellipsis">
                                    <loss-report-cause
                                        v-if="!row.batchs || !row.batchs.length"
                                        :key="`key-batch-${row.goods.id}`"
                                        v-model="row.outReason"
                                        @enter="enterEvent"
                                    >
                                    </loss-report-cause>
                                </abc-form-item>
                            </template>
                            <abc-popover
                                v-else
                                placement="top-start"
                                trigger="hover"
                                theme="yellow"
                                show-on-overflow
                            >
                                <template slot="reference">
                                    <abc-table-cell class="ellipsis">
                                        <abc-text>
                                            {{ row.outReason }}
                                        </abc-text>
                                    </abc-table-cell>
                                </template>
                                <div>
                                    {{ row.outReason }}
                                </div>
                            </abc-popover>
                        </template>
                        <template #footer>
                            <table-footer
                                :list="order.list"
                                :show-amount="needValidateGoodsOutBatch && isCanSeeDamageGoodsPriceInInventory"
                                :calc-total-price="calcTotalPrice"
                            ></table-footer>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <template #right>
            <abc-layout>
                <approval-flow v-if="!!approvalViewModel.approvalDetail" :inst-detail="approvalViewModel.approvalDetail"></approval-flow>
            </abc-layout>
        </template>

        <template slot="footer">
            <abc-flex justify="space-between">
                <logs-v3-popover v-if="logs.length" :logs="logs"></logs-v3-popover>
                <div v-else>
                    <div v-if="draftId">
                        <abc-button
                            type="danger"
                            :loading="deleteDraftButtonLoading"
                            :disabled="saveDraftButtonLoading"
                            @click="deleteDraftHandler"
                        >
                            删除
                        </abc-button>
                    </div>
                </div>
                <abc-space>
                    <template v-if="canApprove && isShowCanApprove">
                        <abc-button
                            :loading="approvalViewModel.loadingAgree"
                            :disabled="approvalViewModel.loadingReject"
                            @click="handleApprovalAgree"
                        >
                            同意
                        </abc-button>
                        <abc-button
                            type="danger"
                            :loading="approvalViewModel.loadingReject"
                            :disabled="approvalViewModel.loadingAgree"
                            @click="handleApprovalReject"
                        >
                            驳回
                        </abc-button>
                    </template>
                    <template v-if="order.status === GOODS_OUT_STATUS.REVIEW && isChainAdmin && !isShowCanApprove">
                        <abc-button @click="reviewOrderHandle('pass')">
                            通过
                        </abc-button>
                        <abc-button type="danger" @click="reviewOrderHandle('fail')">
                            驳回
                        </abc-button>
                    </template>
                    <template v-else>
                        <template v-if="isAdd">
                            <abc-button
                                :loading="reviewBtnLoading"
                                :disabled="!order?.list?.length || disableReportLoss"
                                @click="submit"
                            >
                                {{ submitBtnText }}
                            </abc-button>
                            <abc-button
                                v-if="!isGspReportLoss"
                                type="blank"
                                :loading="saveDraftButtonLoading"
                                :disabled="deleteDraftButtonLoading || disableSaveDraftButton"
                                @click="saveDraftHandler()"
                            >
                                保存草稿
                            </abc-button>
                        </template>
                        <template v-else>
                            <abc-button
                                v-if="canRevoke && !isShowCanApprove"
                                type="danger"
                                :loading="btnLoading"
                                @click="revokeHandler"
                            >
                                撤回
                            </abc-button>
                        </template>
                    </template>
                    <abc-button type="blank" @click="closeDialog">
                        关闭
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>

        <abc-dialog
            v-if="showReviewDialog"
            v-model="showReviewDialog"
            title="审核"
            custom-class="goods-confirm"
            content-styles="width:480px;"
        >
            <div class="dialog-content clearfix">
                <p v-if="reviewData.pass" class="confirm-result">
                    审核结果：<span class="confirm-success">通过</span>
                </p>
                <p v-else class="confirm-result">
                    审核结果：<span class="confirm-refuse">驳回</span>
                </p>

                <p v-if="reviewData.pass" class="confirm-result">
                    出库单审核通过后，将会改变{{ order.fromOrgan && order.fromOrgan.name }}库存
                </p>
                <template v-else>
                    <abc-form ref="checkForm" label-position="left" :label-width="100">
                        <abc-form-item required style="margin-bottom: 0;" :validate-event="validateComment">
                            <abc-edit-div
                                v-model="reviewData.comment"
                                :maxlength="100"
                                responsive
                                placeholder="请输入驳回原因"
                                style="height: 50px;"
                            ></abc-edit-div>
                        </abc-form-item>
                    </abc-form>
                    <p class="confirm-result" style="margin-top: 16px;">
                        门店可在驳回单据内修改并重新发起
                    </p>
                </template>
            </div>
            <template slot="footer">
                <div class="dialog-footer">
                    <abc-button style="margin-left: auto;" :loading="reviewBtnLoading" @click="reviewHandle">
                        确定
                    </abc-button>
                    <abc-button type="blank" @click="showReviewDialog = false">
                        取消
                    </abc-button>
                </div>
            </template>
        </abc-dialog>

        <abc-dialog
            v-if="earlyWarningVisible"
            v-model="earlyWarningVisible"
            title="效期预警药品"
            append-to-body
            responsive
            size="hugely"
            :disabled-keyboard="disabledKeyboard"
            @open="pushDialogName"
            @close="popDialogName"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-select
                        v-model="search"
                        :width="200"
                        @change="doSearch"
                    >
                        <abc-option
                            v-for="o in searchOptions"
                            :key="o.value"
                            :label="o.label"
                            :value="o.value"
                        ></abc-option>
                    </abc-select>
                </abc-layout-header>

                <abc-layout-content>
                    <abc-table
                        v-abc-loading="warnLoading"
                        type="pro"
                        :render-config="renderConfig"
                        :data-list="warnList"
                        class="warn-choose-form"
                        :need-selected="false"
                    >
                        <template #checked="{ trData: item }">
                            <abc-table-cell>
                                <abc-checkbox v-model="item.checked">
                                </abc-checkbox>
                            </abc-table-cell>
                        </template>

                        <template #goodsDispName="{ trData: item }">
                            <abc-table-cell class="ellipsis" :title="formatGoodsNameSpec(item.goods)">
                                <span class="cadn">{{ item.goods | goodsFullName }}</span>
                                <span class="spec">{{ item.goods | goodsSpec }}</span>
                            </abc-table-cell>
                        </template>

                        <template #manufacturer="{ trData: item }">
                            <abc-table-cell>{{ item.goods?.manufacturer ?? '' }}</abc-table-cell>
                        </template>

                        <template #batchId="{ trData: item }">
                            <abc-table-cell>{{ item.batch?.batchId ?? '' }}</abc-table-cell>
                        </template>

                        <template #batchNo="{ trData: item }">
                            <abc-table-cell>{{ item.batch?.batchNo ?? '' }}</abc-table-cell>
                        </template>

                        <template #expiryDate="{ trData: item }">
                            <abc-table-cell> {{ item.batch?.expiryDate ?? '' }}</abc-table-cell>
                        </template>

                        <template #count="{ trData: item }">
                            <abc-table-cell>
                                {{
                                    formatGoodsStock(
                                        item.batch?.packageCount,
                                        item.batch?.pieceCount,
                                        {
                                            ...item.batch, ...item
                                        }
                                    )
                                }}
                            </abc-table-cell>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>

            <template slot="footer">
                <div class="dialog-footer">
                    <abc-button style="margin-left: auto;" :disabled="!addBtnVisible" @click="add">
                        加入出库单
                    </abc-button>
                    <abc-button type="blank" @click="earlyWarningVisible = false">
                        取消
                    </abc-button>
                </div>
            </template>
        </abc-dialog>
    </frame-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import Big from 'big.js';

    import SettingAPI from 'api/settings';
    import GoodsAPI from 'api/goods';
    import RegistrationsAPI from 'api/registrations';
    import StockOutAPI from 'api/goods/stock-out';
    import EnterEvent from 'views/common/enter-event';
    import AutoFocus from '../mixins/auto-focus';
    import dialogAutoWidth from 'views/inventory/mixins/dialog-auto-width.js';
    import GoodsTableV3Mixins from 'views/inventory/mixins/goods-table-v3.js';

    const BatchSelect = () => import('@/views/inventory/common/btach-select/batch-select');
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';

    import DeleteGoodsHandler from '../mixins/delete-goods-handler';
    import Draft from './draft.js';
    import {
        createGUID, getSafeNumber, isNotNull, moneyDigit, paddingMoney,
    } from '@/utils';
    import Clone from '../../../utils/clone';
    import { isEqual } from 'utils/lodash';
    import {
        createFormConfigNew,
        createOutFormTableConfigNew,
        formatGoodsNameSpec,
        formatGoodsStock,
        uniqueCount,
        validateComment,
    } from './common';
    import {
        complexCount, isChineseMedicine,
    } from '@/filters';
    import { GoodsTypeEnum } from '@abc/constants';
    import {
        GOODS_OUT_ORDER_TYPE, GOODS_OUT_ORDER_TYPE_NAME, GOODS_OUT_STATUS,
    } from '../constant.js';
    import {
        showAssignGoodsCount, unitEqual,
    } from 'views/inventory/goods-utils';
    import ClinicAPI from 'api/clinic';

    const GoodsAutoCompleteCoverTitle = () => import('views/inventory/common/goods-auto-complete-cover-title.vue');
    import {
        DATA_PERMISSION_CONTROL_SCENE,
    } from 'views/inventory/common/data-permission-control';
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';
    import ApprovalFlow from '@/views-pharmacy/components/approval-flow/index.vue';
    import useApprovalOrder from '@/views-pharmacy/common/hooks/approval-order';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    import OverflowTooltip from '@/components/overflow-tooltip.vue';
    import { TagHelper } from 'utils/tag-helper';
    import LossReportCause from 'views/inventory/goods-out/dialog/loss-report-cause.vue';
    const TableFooter = () => import('@/views/inventory/goods-out/table-footer.vue');

    export default {
        name: 'OrderForm',

        components: {
            LossReportCause,
            ApprovalFlow,
            FrameDialog,
            ClinicSelect,
            BatchSelect,
            GoodsAutoCompleteCoverTitle,
            DisplayNameCell,
            LogsV3Popover,
            OverflowTooltip,
            TableFooter,
        },

        mixins: [
            EnterEvent,
            AutoFocus,
            Draft,
            DeleteGoodsHandler,
            dialogAutoWidth,
            GoodsTableV3Mixins,
        ],

        props: {
            visible: Boolean,
            outType: Number,
            orderId: [String, Number],
            draftId: [String, Number],
            goodsId: [String, Number],
            gspInstId: {
                type: String,
                default: '',
            },
            isResubmit: Boolean,
            pharmacyNo: Number,
            pharmacyName: String,
            gspReportLossGood: {
                type: Object,
                default: () => {
                    return {
                    };
                },
            },
            submitBtn: {
                type: String,
                default: '确定出库',
            },
            submitBtnReview: {
                type: String,
                default: '提交出库审核',
            },
            reportLossSubmitNoticeText: {
                type: String,
                default: '报损出库后将会立即更新系统库存',
            },
            reportLossSubmitNoticeTextReview: {
                type: String,
                default: '出库单需要总部审核，将锁定相关库存，审核通过立即出库',
            },
        },

        setup() {
            const {
                viewModel,
                canApprove,
                fetchApprovalDetail,
                approvalAgree,
                approvalReject,
            } = useApprovalOrder();

            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('效期预警药品');


            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                approvalViewModel: viewModel,
                canApprove,
                fetchApprovalDetail,
                approvalAgree,
                approvalReject,
            };
        },

        data() {
            return {
                GOODS_OUT_STATUS,
                GOODS_OUT_ORDER_TYPE,
                disableReportLoss: false,
                loading: false,
                searchKey: '',
                searchEmployeeName: '',
                suppliers: [],
                customClinics: [],
                cacheFromOrganId: '',
                currentItem: null,
                order: {
                    type: this.outType,
                    fromOrganId: '',
                    toOrganId: '',
                    toUserId: '',
                    list: [],
                    orderNo: '',
                    status: '',
                    comment: '',
                    pharmacy: {
                        no: this.pharmacyNo,
                        name: this.pharmacyName,
                        type: 0,
                    },
                    toUser: {
                        id: '',
                        name: '',
                    },
                },
                logs: [],
                goodsIds: [], // 查询库存的 goodsId
                goodsStock: [],
                showReviewDialog: false,
                confirmStatus: 0, // 审核状态
                reviewData: {
                    list: [],
                    pass: false,
                    lastModifiedDate: '',
                    comment: '',
                },
                reviewBtnLoading: false,
                btnLoading: false,

                // 近效期功能
                earlyWarningVisible: false,

                searchOptions: [
                    {
                        label: '效期预警药品', value: '',
                    },
                    {
                        label: '近3个月过期', value: 90,
                    },
                    {
                        label: '近2个月过期', value: 60,
                    },
                    {
                        label: '近1个月过期', value: 30,
                    },
                    {
                        label: '近1周过期', value: 7,
                    },
                    {
                        label: '已过期', value: 0,
                    },
                ],
                search: '',

                warnList: [],
                warnTotal: 0,
                selectedIds: [],
                warnLoading: false,
                departmentsOptions: [],// 科室
                employees: [],// 员工
                hasDepartment: false,
                doctors: [],
                doctorSelector: [],

                DATA_PERMISSION_CONTROL_SCENE,
            };
        },

        computed: {
            ...mapGetters([
                'currentClinic',
                'subClinics',
                'userInfo',
                'clinicConfig',
                'isChainAdmin',
                'goodsConfig',
                'multiPharmacyCanUse',
                'isCanSeeDamageGoodsPriceInInventory',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isEditLossReportBatch() {
                return this.viewDistributeConfig.Inventory.isEditLossReportBatch;
            },
            isGspReportLoss() {
                return !!this.gspReportLossGood?.id;
            },
            isLossReportOrder() {
                return [GOODS_OUT_ORDER_TYPE.LOSS_OUT, GOODS_OUT_ORDER_TYPE.UNQUALIFIED].includes(this.outType);
            },
            isShowRightBtn() {
                return this.isLossReportOrder && this.isAdd;
            },
            contentStyle() {
                return {
                    padding: 0,
                    'overflow-y': 'auto',
                };
            },
            dialogConfig() {
                return {
                    size: 'hugely',
                    responsive: true,
                    'before-close': this.closeDialog,
                };
            },
            renderConfig() {
                return {
                    list: createFormConfigNew(this.orderMainNameText),
                };
            },
            reportLossErrText() {
                return this.viewDistributeConfig.Inventory.reportLossErrText;
            },
            // 连锁总部 报损需要过滤子店
            filterChainClinics() {
                const { filterChainClinic } = this.viewDistributeConfig.Inventory;
                return this.isChainAdmin && this.isLossReportOrder && filterChainClinic && !this.isGspReportLoss;
            },
            needValidateGoodsOutBatch() {
                return this.viewDistributeConfig.Inventory.needValidateGoodsOutBatch;
            },
            headerConfig() {
                return {
                    list: createOutFormTableConfigNew(this.orderMainNameText).filter((config) => {
                        if (config.key === 'lossReportCause') {
                            return this.isLossReportOrder;
                        }
                        if (config.key === 'totalPrice') {
                            return this.needValidateGoodsOutBatch;
                        }
                        return true;
                    }),
                };
            },
            orderMainNameText() {
                return this.viewDistributeConfig.Inventory.orderMainNameText;
            },
            isUpdate() {
                return this.orderId && !this.isResubmit;
            },
            isAdd() {
                return !this.isUpdate;
            },
            doctorName() {
                if (this.order.toOrgan) {
                    return `${this.order.toOrgan?.name ?? ''}/${this.order?.toUser?.name ?? ''}`;
                }
                return this.order?.toUser?.name ?? '';
            },
            submitBtnText() {
                if ([GOODS_OUT_ORDER_TYPE.OTHER_OUT, GOODS_OUT_ORDER_TYPE.DEPART_CONSUME, GOODS_OUT_ORDER_TYPE.PRODUCTION_OUT].includes(this.outType)) {
                    return this.submitBtn;
                }
                // 总部给门店出库需要门店确认，给自己不需要
                if (this.isChainAdmin) {
                    return this.clinicId !== this.order?.fromOrganId ? '发送门店确认' : this.submitBtn;
                }
                // 门店出库如果开启审核需要总部审核
                return this.stockOutChainReview ? this.submitBtnReview : this.submitBtn;

            },

            addBtnVisible() {
                return this.warnList.some((o) => !!o.checked);
            },
            stockOutChainReview() {
                return this.goodsConfig?.chainReview?.stockOutChainReview;
            },
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },
            currentSubClinicsArray() {
                return this.subClinics || [];
            },
            showDialog: {
                get() {
                    return this.visible;
                },
                set(v) {
                    this.$emit('close');
                    this.$emit('update:visible', v);
                },
            },

            dialogTitleOption() {
                const title = `${GOODS_OUT_ORDER_TYPE_NAME[this.outType]}单`;
                let orderNo = '';
                let statusName = '';
                let tagConfig = TagHelper.ING_TAG;

                if (this.isUpdate && this.order.orderNo) {
                    orderNo = this.order.orderNo;

                    if (
                        this.order.status === GOODS_OUT_STATUS.CONFIRM &&
                        this.order.fromOrgan &&
                        (this.order.fromOrgan.shortName || this.order.fromOrgan.name)
                    ) {
                        statusName = `待${this.order.fromOrgan.shortName || this.order.fromOrgan.name}确认`;
                    }

                    if (this.order.status === GOODS_OUT_STATUS.REVIEW) {
                        statusName = '待审核';
                        tagConfig = this.canReview ? TagHelper.TODO_TAG : TagHelper.ING_TAG;
                    }

                    if (this.order.status === GOODS_OUT_STATUS.REVOKE) {
                        statusName = '已撤回';
                        tagConfig = TagHelper.CANCEL_TAG;
                    }
                    if (this.order.status === GOODS_OUT_STATUS.REFUSE) {
                        statusName = '已驳回';
                        tagConfig = TagHelper.REFUSE_TAG;
                    }
                }
                return {
                    title,
                    orderNo,
                    statusName,
                    tagConfig,
                };
            },

            // 是否为单据发起方，需要后端记录applyClinicId
            isApplyClinic() {
                return this.clinicId === this.order?.applyClinicId;
            },
            canReview() {
                if (this.canApprove && this.isShowCanApprove) return true;
                if (this.order.status === GOODS_OUT_STATUS.REVIEW && this.isChainAdmin && !this.isShowCanApprove) return true;
                return false;
            },
            // 发起方可以撤回单据
            canRevoke() {
                // 新判断逻辑
                // if (this.order?.status === GOODS_OUT_STATUS.REVIEW || this.order?.status === GOODS_OUT_STATUS.CONFIRM) {
                //     return this.isApplyClinic;
                // }


                // 发起方是总部
                if (this.order?.status === GOODS_OUT_STATUS.CONFIRM) {
                    return this.isChainAdmin;
                }
                // 发起方是门店
                if (this.order?.status === GOODS_OUT_STATUS.REVIEW) {
                    return !this.isChainAdmin;
                }

                return false;
            },
            kindCount() {
                if (!this.order) return '';
                return uniqueCount(this.order.list);
            },
            /**
             * @desc 计算总的数量，小单位需要根据换算关系转换成大单位数量
             * count = pieceCount / pieceNum + packageCount;
             * <AUTHOR>
             * @date 2019/12/24
             */
            totalCount() {
                let count = 0;
                this.order &&
                    this.order.list.forEach((item) => {
                        let pieceCount = 0;
                        let packageCount = 0;
                        if (item.batchs && item.batchs.length) {
                            item.batchs.forEach((batch) => {
                                pieceCount += Number(batch.pieceCount);
                                packageCount += Number(batch.packageCount);
                            });
                        } else {
                            pieceCount = +item.pieceCount;
                            packageCount = +item.packageCount;
                        }
                        pieceCount =
                            item.goods.packageUnit !== item.goods.pieceUnit ? pieceCount / +item.goods.pieceNum : 0;
                        count += packageCount + pieceCount;
                    });
                return count;
            },
            goodsDescriptionsColumns() {
                return [
                    {
                        prop: 'userName',
                        label: '出库人：',
                        value: this.order?.createdUser?.name || this.userInfo?.name,
                        style: {
                            maxWidth: '120px',
                        },
                    },
                    {
                        prop: 'clinicName',
                        label: '出库门店：',
                        style: {
                            maxWidth: '210px',
                        },
                    },
                    {
                        prop: 'pharmacy',
                        label: '出库库房：',
                        value: this.order?.pharmacy?.name,
                        style: {
                            maxWidth: '140px',
                        },
                    },
                    {
                        // 使用级联组件，科室/人
                        prop: 'consumer',
                        label: '使用人：',
                        style: {
                            maxWidth: '176px',
                        },
                    },
                    {
                        prop: 'department',
                        label: '出库科室：',
                        style: {
                            maxWidth: '170px',
                        },
                    },
                    {
                        prop: 'doctor',
                        label: '使用人：',
                        style: {
                            maxWidth: '176px',
                        },
                    },
                    {
                        prop: 'comment',
                        label: '备注：',
                        style: {
                            maxWidth: '210px',
                        },
                    },
                    {
                        prop: 'right-btn',
                        label: '效期预警药品：',
                        isRight: true,
                        style: {
                            'margin-left': 'auto',
                        },
                    },
                ].filter((item) => {
                    if (item.prop === 'doctor' || item.prop === 'department') {
                        return this.outType === GOODS_OUT_ORDER_TYPE.OTHER_OUT;
                    }
                    if (item.prop === 'pharmacy') {
                        return this.multiPharmacyCanUse;
                    }
                    if (item.prop === 'consumer') {
                        return this.outType === GOODS_OUT_ORDER_TYPE.DEPART_CONSUME;
                    }
                    if (item.prop === 'right-btn') {
                        return this.isShowRightBtn;
                    }
                    return true;
                });
            },
            currentEmployees() {
                if (!this.searchEmployeeName) {
                    return this.employees;
                }
                return this.employees.filter((e) => e.employeeName.includes(this.searchEmployeeName));
            },
            disableSaveDraftButton() {
                return isEqual(this.order, this._cacheOrderDraft);
            },

            rightWidth() {
                if (this.gspInstId) return 320;
                return 0;
            },

            isShowCanApprove() {
                return this.viewDistributeConfig.Inventory.isShowGoodsOutCanApprove;
            },
        },

        watch: {
            'order.fromOrganId': {
                handler(val) {
                    if (!val) return;
                    if (this.isShowRightBtn) {
                        this.handleSearch((data) => {
                            this.warnTotal = data.total;
                        });
                    }
                },
                immediate: true,
            },
        },

        async created() {
            try {
                if (this.gspInstId) {
                    this.fetchApprovalDetail(this.gspInstId);
                }
                await this.fetchData();

                if (this.outType === GOODS_OUT_ORDER_TYPE.OTHER_OUT) {
                    await this.fetchClinicDepartments();
                    await this.fetchEmployeeByModuleId();
                }
                if (this.outType === GOODS_OUT_ORDER_TYPE.DEPART_CONSUME) {
                    await this.fetchDoctors();
                }

                if (this.goodsId) {
                    this.sortOrderList();
                }
                if (!this.draftId) {
                    this.cacheFromOrganId = this.currentClinic && this.currentClinic.clinicId;
                    this.order.fromOrganId = this.cacheFromOrganId;
                }
                if (this.isGspReportLoss) {
                    this.order.comment = `不合格品:${this.gspReportLossGood.relatedOrders.orderNo || '-'}`;
                    this.order.fromOrganId = this.gspReportLossGood.clinicId || this.currentClinic?.clinicId || '';
                    this.cacheFromOrganId = this.gspReportLossGood.clinicId || this.currentClinic?.clinicId || '';
                    this.selectGoods(this.gspReportLossGood);
                }
            } catch (error) {
                console.error(error);
            } finally {
                this.order = { ...this.order };// 触发计算属性
                this._cacheOrderDraft = Clone(this.order);
            }
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },

        methods: {
            moneyDigit,
            paddingMoney,
            validateComment,
            formatGoodsNameSpec,
            formatGoodsStock,
            isChineseMedicine,
            unitEqual,
            getConfig(goods) {
                return {
                    formatLength: (this.isChineseMedicine(goods) || [GoodsTypeEnum.GOODS, GoodsTypeEnum.MATERIAL].includes(goods.type)) ? 2 : 0,
                    max: 10000000,
                    supportZero: false,
                };
            },
            setCurrentItem(row) {
                this.currentItem = row;
            },
            onCancel() {
                this.setCurrentItem(null);
            },
            onDeleteItem(parent, row) {
                const arr = parent.batchs;
                const idx = arr.findIndex((e) => this.createRowKey(e) === this.createRowKey(row));
                if (idx !== -1) {
                    arr?.splice(idx, 1);
                }
                this.onCancel();
            },
            deleteTr(rowIndex, childIndex) {
                if (isNotNull(rowIndex) && isNotNull(childIndex)) {
                    const arr = this.order.list[rowIndex]?.batchs ?? [];
                    arr.splice(childIndex, 1);
                } else {
                    this.order.list.splice(rowIndex, 1);
                }
            },
            add() {
                this.earlyWarningVisible = false;
                this.loading = true;

                const result = this.warnList.filter((o) => o.checked)
                    .reduce((res, cur) => {
                        const { id } = cur.goods;

                        const index = res.findIndex((n) => n.goods.id === id);
                        if (index < 0) {
                            res.push({
                                goods: cur.goods,
                                batchs: [{
                                    goods: {
                                        ...cur.goods, ...cur.batch,
                                    },
                                    ...cur.batch,
                                }],
                                batchOptions: cur.batchs.map((o) => {
                                    return {
                                        goods: {
                                            ...cur.goods, ...o,
                                        },
                                        ...o,
                                    };
                                }),
                                packageCount: '',
                                pieceCount: '',
                            });
                        } else {
                            res[index].batchs.push({
                                goods: {
                                    ...cur.goods, ...cur.batch,
                                },
                                ...cur.batch,
                            });
                        }

                        return res;
                    }, []);

                const choosdIds = result.map((o) => o.goods.id);
                const orderList = this.order.list.filter((o) => !choosdIds.includes(o.goods.id));

                this.order.list = [];

                const l = [...orderList, ...result];
                this.setOrderList(l.length, 0, l, () => {
                    this.loading = false;
                });
            },
            // 获取科室
            async fetchClinicDepartments() {
                const { data } = await SettingAPI.clinic.fetchClinicDepartments();
                this.departmentsOptions = data?.data?.rows || [];
            },
            async fetchEmployeeByModuleId() {
                const { data } = await ClinicAPI.fetchEmployeeByModuleId({
                    // moduleId: [MODULE_ID_MAP.inventory],
                });
                this.employees = data;
            },
            createRowKey(row) {
                // batchs数据key
                if (row.batchId && !row?.batchs?.length) return `${row.id}_${row.batchId}`;

                return row.id || `${row.goods?.id}`;
            },
            // 开启虚拟列表，预先知道每一项的高度，计算表格每一行的高度方法
            getItemHeight(item) {
                let count = 1;
                if (item.batchs?.length) {
                    count += item.batchs?.length;
                }
                return count * 45;
            },
            // 时间分片
            setOrderList(curTotal, curIndex, list, cb) {
                if (curTotal <= 0) {
                    cb();

                    return false;
                }
                //每页多少条
                const pageCount = Math.min(curTotal, 20);
                this._timer = setTimeout(() => {
                    for (let i = 0; i < pageCount; i++) {
                        this.order.list.push(list[curIndex + i]);
                    }

                    this.setOrderList(curTotal - pageCount, curIndex + pageCount, list, cb);
                }, 0);
            },

            async handleSearch(callback) {
                try {
                    const { data } = await GoodsAPI.loadWarnGoods({
                        expireWarnDays: this.search,
                        pharmacyNo: this.order.pharmacy?.no,
                        clinicId: this.order.fromOrganId,
                    });
                    this.warnList = data.list.reduce((res, cur) => {
                        const arr = cur.batchs.reduce((o, m) => {
                            if (m.expiredFlag) {
                                o.push({
                                    batch: m,
                                    checked: false,
                                    ...cur,
                                    goods: {
                                        ...cur.goods,
                                        outPackageCount: cur.outPackageCount,
                                        outPieceCount: cur.outPieceCount,
                                        packageCount: cur.packageCount,
                                        pieceCount: cur.pieceCount,
                                    },
                                });
                            }

                            return o;
                        }, []);

                        res = [...res, ...arr];
                        return res;
                    }, []);

                    if (callback) {
                        callback(data);
                    }

                } catch (err) {
                    console.error(err);

                    if (this.warnLoading) this.warnLoading = false;
                }
            },
            handleEarlyClick() {
                this.warnLoading = true;

                this.selectedIds = this.order.list.reduce((res, cur) => {
                    if (cur.batchs && cur.batchs.length > 0) {
                        const ids = cur.batchs.map((o) => `${o.batchId}`);

                        res = [...res, ...ids];
                    }

                    return res;
                }, []);


                this.handleSearch(() => {
                    this.warnList.forEach((o) => {
                        if (this.selectedIds.includes(`${o.batch.batchId}`)) {
                            o.checked = true;
                        } else {
                            o.checked = false;
                        }
                    });

                    this.warnLoading = false;
                });

                this.earlyWarningVisible = true;
            },

            doSearch() {
                this.warnLoading = true,

                this.handleSearch(() => {
                    this.warnList.forEach((o) => {
                        if (this.selectedIds.includes(`${o.batch.batchId}`)) {
                            o.checked = true;
                        } else {
                            o.checked = false;
                        }
                    });

                    this.warnLoading = false;
                });
            },

            getBatchsTotalCount(batchs, goods) {
                let packageCount = 0;
                let pieceCount = 0;

                batchs?.forEach((item) => {
                    packageCount = Big(packageCount).plus(item.packageCount || 0).toNumber();
                    pieceCount = Big(pieceCount).plus(item.pieceCount || 0).toNumber();
                });

                let tempStock = Object.assign({}, goods);
                if (goods.dismounting && !isChineseMedicine(goods)) {
                    const count = Math.floor(pieceCount / goods.pieceNum);
                    packageCount += count;
                    pieceCount = Big(pieceCount).mod(goods.pieceNum || 1).toNumber();
                }
                if (isChineseMedicine(goods)) {
                    pieceCount = Big(packageCount).plus(pieceCount).toNumber();
                    packageCount = 0;
                }
                tempStock = Object.assign(tempStock, {
                    packageCount, pieceCount,
                });
                return complexCount(tempStock);
            },
            async fetchDoctors() {
                const res = await RegistrationsAPI.fetchDepartmentDoctors();
                const { data } = res.data;
                if (data) {
                    this.hasDepartment = data.hasDepartment;
                    // 有科室需要处理数据结构
                    if (data.hasDepartment) {
                        const _arr = [];
                        const _obj = {};
                        data.doctors.forEach((doc) => {
                            if (!_obj[doc.departmentId + doc.departmentName]) {
                                _arr.push({
                                    id: doc.departmentId,
                                    name: doc.departmentName,
                                    childs: [],
                                });
                                if (this.order.toOrganId === doc.departmentId) {
                                    this.doctorSelector.push({
                                        label: doc.departmentName,
                                        value: doc.departmentId,
                                    });
                                }
                                _obj[doc.departmentId + doc.departmentName] = true;
                            }
                        });
                        data.doctors.forEach((doc) => {
                            _arr.forEach((item) => {
                                if (doc.departmentId === item.id) {
                                    item.childs.push({
                                        id: doc.employeeId,
                                        name: doc.employeeName,
                                        unitPrice: doc.unitPrice,
                                    });
                                    if (this.order.toOrganId === doc.departmentId && this.order.toUserId === doc.employeeId) {
                                        this.doctorSelector.push({
                                            label: doc.employeeName,
                                            value: doc.employeeId,
                                        });
                                    }
                                }
                                if (item.childs.length) {
                                    item.search = true;
                                    item.searchFn = this.filterMethod;
                                }
                            });
                        });
                        this.doctors = _arr;
                    } else {
                        this.doctors = data.doctors.map((it) => {
                            return {
                                id: it.employeeId,
                                name: it.employeeName,
                                unitPrice: it.unitPrice,
                            };
                        });
                    }
                }
            },
            filterMethod(nodes, val) {
                return nodes.filter((node) => {
                    return node.label.includes(val);
                });
            },
            handleSearchDoctor(key) {
                this.searchEmployeeName = key;
            },
            consumerChange(doctor) {
                if (this.hasDepartment) {
                    const [organ, user] = doctor;
                    this.order.toOrganId = organ?.value ?? '';
                    this.order.toUserId = user?.value ?? '';
                    if (this.order.toUser) {
                        this.order.toUser.id = this.order.toUserId;
                        this.order.toUser.name = user?.label ?? '';
                    }
                } else {
                    this.order.toUserId = doctor;
                    const doctorObj = this.doctors.find((d) => d.id === doctor);
                    if (this.order.toUser) {
                        this.order.toUser.id = this.order.toUserId;
                        this.order.toUser.name = doctorObj?.name ?? '';
                    }
                }
            },
            doctorChange(doctor) {
                const doctorObj = this.employees.find((d) => d.employeeId === doctor);
                if (this.order.toUser) {
                    this.order.toUser.id = doctorObj?.employeeId ?? '';
                    this.order.toUser.name = doctorObj?.employeeName ?? '';
                }
            },
            /**
             * @desc 出库切换门店
             * 并且已有选择的药品，在切换门店时给出二次确认，如果确认切换，则清空已选择的药品数据
             * <AUTHOR>
             * @date 2019/12/23
             */
            changeClinic(val) {
                console.log(val);

                if (val !== this.order.fromOrganId && this.order.list && this.order.list.length) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '切换出库门店，已选择的药品将清空',
                        onConfirm: () => {
                            this.order.list = [];
                            this.order.fromOrganId = val;
                        },
                        onCancel: () => {
                            this.cacheFromOrganId = this.order.fromOrganId;
                        },
                    });
                } else {
                    this.order.fromOrganId = val;
                }
            },

            async selectGoods(goods) {
                const goodsId = goods.id;

                const { data: goodsObj } = await GoodsAPI.goods(
                    goodsId,
                    this.order.fromOrganId,
                    {
                        withStock: 1,
                        withShebaoCode: 1,
                        pharmacyNo: this.order?.pharmacy?.no,
                    },
                );
                if (goodsObj.packageCount === 0 && goodsObj.pieceCount === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: this.reportLossErrText,
                    });
                    return false;
                }
                let tempBatches = await this.fetchGoodsBatch(goodsId);

                // 处理多批次的数据
                tempBatches =
                    tempBatches?.map((item) => {
                        // 组件每个批次的药品信息，用于显示每个批次的库存数据
                        item.goods = {
                            id: goodsObj.id,
                            type: goodsObj.type,
                            subType: goodsObj.subType,
                            dismounting: goodsObj.dismounting,
                            packageUnit: goodsObj.packageUnit,
                            pieceUnit: goodsObj.pieceUnit,
                            pieceNum: goodsObj.pieceNum,
                            packageCostPrice: goodsObj.packageCostPrice,
                            packageCount: item.packageCount,
                            pieceCount: item.pieceCount,
                            outPackageCount: item.outPackageCount || 0,
                            outPieceCount: item.outPieceCount || 0,
                            lockingPackageCount: item.lockingPackageCount || 0,
                            lockingPieceCount: item.lockingPieceCount || 0,
                        };
                        item.checked = false;
                        item.packageCount = '';
                        item.pieceCount = '';
                        item.outPackageCount = item.goods.outPackageCount;
                        item.outPieceCount = item.goods.outPieceCount;
                        item.lockingPackageCount = item.goods.lockingPackageCount;
                        item.lockingPieceCount = item.goods.lockingPieceCount;
                        item.packageCostPrice = item.packageCostPrice || item.goods.packageCostPrice;
                        item.keyId = createGUID();
                        return item;
                    }) || [];
                const repeatIndex = this.order.list.findIndex((item) => {
                    return item.goods.id === goods.id;
                });
                if (repeatIndex > -1) {
                    this.sortOrderDesc(goods.id);
                }
                if (repeatIndex === -1) {
                    this.order.list.push({
                        goods: Clone(goodsObj),
                        pieceCount: '',
                        packageCount: '',
                        batchOptions: Clone(tempBatches),
                        batchs: [],
                    });
                }
                this.searchKey = '';
                this.$nextTick(() => {
                    const $tableTr = this.$refs.tableRef?.$el?.querySelectorAll('.abc-table-tr--out-focus') ?? [];
                    console.log('报损可聚焦行数据', $tableTr);
                    const lastTr = $tableTr[$tableTr.length - 1];
                    if (lastTr) {
                        const inputElement = lastTr.querySelector('.count-input input');
                        if (inputElement) {
                            inputElement.focus();
                        }
                    }
                });
                if (this.isGspReportLoss) {
                    const currentReportLoss = this.order.list[0].batchOptions?.filter((item) => {
                        return item.goodsId === this.gspReportLossGood.id && item.batchId === this.gspReportLossGood.batchId;
                    }) || [];
                    console.log('currentReportLoss=', currentReportLoss);
                    if (currentReportLoss?.[0]) {
                        const {
                            packageCount = 0,
                            pieceCount = 0,
                            outReason = '',
                            // packageUnit = '',
                            // pieceUnit = '',
                            // pieceNum = 0, // 用于计算
                        } = this.gspReportLossGood;
                        currentReportLoss[0].packageCount = packageCount || '';
                        currentReportLoss[0].pieceCount = pieceCount || '';
                        currentReportLoss[0].outReason = outReason || '';
                        this.order.list[0].batchs = currentReportLoss;
                    } else {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: `当前批次（${this.gspReportLossGood.batchId}）可报损出库库存量为0`,
                            onConfirm: () => {
                            },
                        });
                    }
                }
            },

            /**
             * @desc 有可能是编辑情况需要获取数据
             * <AUTHOR>
             * @date 2018/11/24 15:57:40
             */
            async fetchData() {
                if (!this.orderId) return false;

                this.loading = true;
                const {
                    type,
                    logs,
                    fromOrgan,
                    toOrgan,
                    lastModifiedDate,
                    list,
                    orderNo,
                    status,
                    toUser,
                    pharmacy,
                    createdUser,
                } = await StockOutAPI.getById(this.orderId);
                this.order.type = type;
                this.order.pharmacy = pharmacy ?? {};
                this.cacheFromOrganId = fromOrgan.id;
                this.order.fromOrganId = fromOrgan.id;
                this.order.toOrganId = toOrgan ? toOrgan.id : null;
                this.order.toUserId = toUser ? toUser.id : null;
                this.order.lastModifiedDate = lastModifiedDate;
                this.order.orderNo = orderNo;
                this.order.status = status;
                this.order.fromOrgan = fromOrgan;
                this.order.toOrgan = toOrgan;
                this.order.toUser = toUser;
                this.order.list = list.map((item) => {
                    item.pieceCount = +item.pieceCount;
                    item.packageCount = +item.packageCount;
                    return item;
                });
                this.order.createdUser = createdUser;
                this.reviewData.lastModifiedDate = lastModifiedDate;
                if (this.isUpdate) {
                    this.logs = logs || [];
                }
                // 取日志中用户填写的备注
                if (logs && logs.length) {
                    this.order.comment = logs[logs.length - 1].comment ?? '';
                } else {
                    this.order.comment = '';
                }
                await this.updateStock();
                this.loading = false;
            },

            /**
             * @desc 提交前验证
             * <AUTHOR>
             * @date 2018/11/24 15:55:33
             */
            submit() {
                this.$refs.createForm.validate(async (val) => {
                    if (val) {
                        if (this.isLossReportOrder) {
                            let title = '';
                            if (this.stockOutChainReview && !this.isChainAdmin) {
                                title = this.reportLossSubmitNoticeTextReview;
                            } else {
                                title = this.reportLossSubmitNoticeText;
                            }

                            if (this.isChainAdmin) {
                                const subClinic = this.currentSubClinicsArray.find((item) => {
                                    return item.id === this.order.fromOrganId;
                                });
                                const name = subClinic && (subClinic.shortName || subClinic.name);

                                if (this.order.fromOrganId !== this.clinicId) {
                                    title = this.isUpdate ?
                                        `修改出库单后，需要 ${name} 确认` :
                                        `出库单需要 ${name} 确认`;
                                }
                            }

                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                content: title,
                                onConfirm: () => {
                                    this.isUpdate ? this.updateOrder() : this.createOrder();
                                },
                            });
                        } else {
                            this.isUpdate ? this.updateOrder() : this.createOrder();
                        }
                    }
                });
            },
            generateListForSubmit() {
                return this.order.list.map((item) => {
                    if (item.batchs && item.batchs.length) {
                        const tempBatchs = item.batchs.map((batch) => {
                            return {
                                goodsId: item.goods.id,
                                batchId: batch.batchId,
                                stockId: batch.stockId,
                                pieceCount: batch.pieceCount,
                                packageCount: batch.packageCount,
                                outReason: batch.outReason,
                            };
                        });
                        return {
                            goodsId: item.goods.id,
                            pieceCount: item.pieceCount,
                            packageCount: item.packageCount,
                            outReason: item.outReason,
                            batchs: tempBatchs,
                        };
                    }
                    // if (!item.batchs.length) {
                    //     return {
                    //         goodsId: item.goods.id,
                    //         pieceCount: item.pieceCount || 0,
                    //         packageCount: item.packageCount || 0,
                    //         batchs: [{
                    //             id: item._batchId,
                    //             stockId: item._stockId,
                    //             packageCount: item.packageCount,
                    //             pieceCount: item.pieceCount,
                    //         }],
                    //     };
                    // }
                    return {
                        goodsId: item.goods.id,
                        pieceCount: item.pieceCount,
                        packageCount: item.packageCount,
                        outReason: item.outReason,
                    };
                });
            },
            /**
             * @desc 创建出库单
             * <AUTHOR>
             * @date 2018/11/21 19:45:21
             * @params
             * @return
             */
            async createOrder(isForceSubmit = false) {
                this.reviewBtnLoading = true;
                const {
                    type, comment, fromOrganId, toOrganId, toUserId, pharmacy,
                } = this.order;

                try {
                    const params = {
                        type,
                        comment,
                        fromOrganId,
                        toOrganId,
                        toUserId,
                        list: this.generateListForSubmit(),
                    };
                    if (this.multiPharmacyCanUse) {
                        params.pharmacyNo = pharmacy?.no;
                    }
                    // 不合格品报损出库不能支持修改不能支持保存草稿 只可以查看
                    if (this.isGspReportLoss) {
                        params.relatedOrders = [this.gspReportLossGood.relatedOrders];
                    }
                    // 强制提交创建
                    if (isForceSubmit) params.forceSubmit = 1;
                    if (this._isCloudDraft) {
                        // 创建时删除云草稿
                        params.outOrderDraftId = this.draftId;
                    }
                    await StockOutAPI.createOrder(params);

                    this.clearDraft('goods-out', this.draftId);
                    this.$emit('refresh', true, 'add');
                    this.showDialog = false;
                    this.reviewBtnLoading = false;
                } catch (e) {
                    this.reviewBtnLoading = false;
                    if (e.code === 12010) {
                        const shortageMedicineTips = [];
                        const goodsStock = (e && e.detail) || [];
                        const { length } = goodsStock;

                        this.order.list = this.order.list.map((item) => {
                            const newGoods = goodsStock.find((goods) => {
                                return goods.currentStock.goodsId === item.goods.id;
                            });
                            if (newGoods) {
                                const {
                                    packageCount, pieceCount,
                                } = newGoods.currentStock || {};
                                item.goods.packageCount = +packageCount || 0;
                                item.goods.pieceCount = +pieceCount || 0;
                            }
                            return item;
                        });

                        goodsStock.forEach((item, index) => {
                            let str = '';
                            str = `${item.goods.medicineCadn || item.goods.name || ''}`;
                            if (length - 1 === index) {
                                str += ' 库存不足';
                            }
                            shortageMedicineTips.push(str);
                        });

                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: shortageMedicineTips,
                            onClose: () => {
                                this.fetchData();
                                this.$refs.createForm.validate();
                            },
                        });
                    } else if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goods.id !== e.detail.goodsId);
                        });
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goodsId !== e.detail.id);
                        });
                    } else if (e.code === 12814) {
                        // 当前草稿已删除、已提交，需要重新提交单据
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                            confirmText: '确认',
                            onConfirm: () => this.createOrder(true),
                        });
                    }
                }
            },

            /**
             * @desc 没有确认状态 修改出库单
             * <AUTHOR>
             * @date 2018/11/21 18:44:16
             */
            async updateOrder() {
                try {
                    const {
                        lastModifiedDate,
                        list,
                        comment,
                    } = this.order;
                    this.reviewBtnLoading = true;
                    await StockOutAPI.updateOrder(this.orderId, {
                        comment,
                        lastModifiedDate,
                        list: list.map((item) => {
                            if (!item.goodsId) {
                                return {
                                    ...item,
                                    goodsId: item.goods.id,
                                };
                            }
                            return item;
                        }),
                    });
                    this.$emit('refresh', true);
                    this.reviewBtnLoading = false;
                    this.showDialog = false;
                } catch (e) {
                    this.reviewBtnLoading = false;
                    if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goods.id !== e.detail.goodsId);
                        });
                    }
                    if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goodsId !== e.detail.id);
                        });
                    }
                    if (e.code === 962) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前出库单已被修改，已经刷新到新版本，请再次确认',
                            onClose: () => {
                                this.fetchData();
                            },
                        });
                    } else if (e.code === 12010) {
                        const shortageMedicineTips = [];
                        const goodsStock = (e && e.detail) || [];
                        const { length } = goodsStock;

                        this.order.list = this.order.list.map((item) => {
                            const newGoods = goodsStock.find((goods) => {
                                return goods.id === item.goods.id;
                            });
                            const {
                                packageCount, pieceCount,
                            } = newGoods || {};
                            item.goods.packageCount = +packageCount || 0;
                            item.goods.pieceCount = +pieceCount || 0;
                        });

                        goodsStock.forEach((item, index) => {
                            let str = '';
                            str = `${item.goods.medicineCadn || item.goods.name || ''}`;
                            if (length - 1 === index) {
                                str += ' 库存不足';
                            }
                            shortageMedicineTips.push(str);
                        });

                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: shortageMedicineTips,
                            onClose: () => {
                                this.$refs.createForm.validate();
                                this.fetchData();
                            },
                        });
                    }
                }
            },

            validateStock({
                packageCount, pieceCount, goods = {},
            }, depth, callback) {
                const {
                    outPackageCount, outPieceCount, pieceNum,
                } = goods;

                const flag = new Big(getSafeNumber(pieceCount))
                    .plus(new Big(getSafeNumber(packageCount)).times(getSafeNumber(pieceNum)))
                    .gt(new Big(getSafeNumber(outPieceCount)).plus(new Big(getSafeNumber(outPackageCount)).times(getSafeNumber(pieceNum))));

                if (flag) {
                    // 没有批次
                    if (depth === 0) {
                        callback({
                            validate: false, message: `超出可出总量${showAssignGoodsCount(goods,'out')}(锁定${showAssignGoodsCount(goods,'locking')})`,
                        });
                    } else {
                        callback({
                            validate: false, message: `超出批次可出库存${showAssignGoodsCount(goods,'out')}(锁定${showAssignGoodsCount(goods,'locking')})`,
                        });
                    }
                    return;
                }
                callback({ validate: true });
            },
            // 校验可出总量
            validateTotalStock(row, callback) {
                if (this.compareInventoryCount(row)) {
                    return callback({
                        validate: false, message: `累计出库数量超出可出总量${showAssignGoodsCount(row.goods,'out')}（锁定${showAssignGoodsCount(row.goods,'locking')}）`,
                    });
                }
                callback({ validate: true });
            },
            compareInventoryCount(row) {
                let pieceCount = 0;
                let packageCount = 0;
                row.batchs?.forEach((item) => {
                    pieceCount += +item.pieceCount || 0;
                    packageCount += +item.packageCount || 0;
                });

                const {
                    outPackageCount, outPieceCount, pieceNum,
                } = row.goods;// 取出当前药品可出数量，因为锁定的数量不是锁定到批次的

                const flag = new Big(getSafeNumber(pieceCount))
                    .plus(new Big(getSafeNumber(packageCount)).times(getSafeNumber(pieceNum)))
                    .gt(new Big(getSafeNumber(outPieceCount)).plus(new Big(getSafeNumber(outPackageCount)).times(getSafeNumber(pieceNum))));

                return flag;
            },
            sortOrderDesc(id) {
                const newOrderList = [];
                this.order.list.forEach((item) => {
                    newOrderList.push(item);
                });
                newOrderList.sort((a, b) => {
                    return (a.goods.id === id) - (b.goods.id === id);
                });
                this.order.list = Clone(newOrderList);
            },
            sortOrderList() {
                const newOrderList = [];
                this.order.list.forEach((item) => {
                    newOrderList.push(item);
                });
                newOrderList.sort((a, b) => {
                    return (b.goods.id === this.goodsId) - (a.goods.id === this.goodsId);
                });
                this.order.list = Clone(newOrderList);
            },
            // 获取药品的当前库存
            async fetchGoodsStock() {
                this.loading = true;
                const goodsSet = new Set();
                // 获取不重复的goodsIds
                this.order.list.forEach((item) => {
                    goodsSet.add(item.goods.id);
                });
                this.goodsIds = Array.from(goodsSet);
                const clinicId = this.order.fromOrganId || this.clinicId;
                if (this.goodsIds.length) {
                    try {
                        const { data } = await GoodsAPI.fetchGoodIdsBatchs(this.goodsIds, clinicId, this.order?.pharmacy?.no, this.order?.pharmacy?.type);
                        this.loading = false;
                        return data.list || [];
                    } catch (e) {
                        this.loading = false;
                    }
                } else {
                    this.loading = false;
                    return [];
                }
            },
            /**
             * @desc 刷新草稿列表中的库存信息
             * <AUTHOR>
             * @date 2019/12/25
             */
            async updateStock() {
                this.loading = true;
                const currentStock = await this.fetchGoodsStock();

                const currentStockMap = new Map();

                currentStock.forEach((item) => {

                    // list 更新最新的库存批次信息
                    const batchOptions = [];
                    const batchOptionMap = new Map();

                    item.batchs?.forEach((batch) => {

                        const newBatch = Clone(batch);
                        newBatch.goods = Clone(item.goods); // 药品基本信息
                        // goods 里面packageCunt pieceCount 是当前批次库存
                        newBatch.goods.packageCount = batch.packageCount;
                        newBatch.goods.pieceCount = batch.pieceCount;
                        // 可出数量
                        newBatch.goods.outPackageCount = batch.outPackageCount || 0;
                        newBatch.goods.outPieceCount = batch.outPieceCount || 0;
                        // 锁定数量
                        newBatch.goods.lockingPackageCount = batch.lockingPackageCount || 0;
                        newBatch.goods.lockingPieceCount = batch.lockingPieceCount || 0;
                        newBatch.packageCount = '';
                        newBatch.pieceCount = '';
                        newBatch.checked = false;
                        batchOptions.push(newBatch);
                        batchOptionMap.set(`${newBatch.batchId}`, newBatch);
                    });

                    currentStockMap.set(`${item.goodsId}`, {
                        ...item,
                        batchOptions,
                        batchOptionMap,
                    });
                });

                let tempList = Clone(this.order.list);
                tempList = tempList.map((item) => {
                    const goodsStock = currentStockMap.get(`${item.goods.id}`);
                    const newItem = Clone(item);
                    if (goodsStock) {
                        // 更新 order.list 每个item 的药品信息
                        newItem.goods.type = goodsStock.goods.type;
                        newItem.goods.subType = goodsStock.goods.subType;
                        newItem.goods.pieceNum = goodsStock.goods.pieceNum;
                        newItem.goods.packageUnit = goodsStock.goods.packageUnit;
                        newItem.goods.pieceUnit = goodsStock.goods.pieceUnit;
                        newItem.goods.packageCount = goodsStock.goods.packageCount || goodsStock.packageCount;
                        newItem.goods.pieceCount = goodsStock.goods.pieceCount || goodsStock.pieceCount;
                        newItem.goods.outPackageCount = goodsStock.goods.outPackageCount || goodsStock.outPackageCount;
                        newItem.goods.outPieceCount = goodsStock.goods.outPieceCount || goodsStock.outPieceCount;
                        newItem.goods.lockingPackageCount = goodsStock.goods.lockingPackageCount || goodsStock.lockingPackageCount;
                        newItem.goods.lockingPieceCount = goodsStock.goods.lockingPieceCount || goodsStock.lockingPieceCount;
                        newItem.goods.medicineDosage = goodsStock.goods.medicineDosage;
                        newItem.goods.medicineDosageNum = goodsStock.goods.medicineDosageNum;
                        newItem.goods.medicineDosageUnit = goodsStock.goods.medicineDosageUnit;
                        // 更新 order.list 每个item 的所有批次信息
                        newItem.batchOptions = Clone(goodsStock.batchOptions);
                        // 更新 order.list 每个item 的选中批次信息
                        newItem.batchs = newItem.batchs?.map((batch) => {
                            const newBatch = goodsStock.batchOptionMap.get(`${batch.batchId}`);

                            if (newBatch) {
                                batch.checked = true;
                                batch.batchNo = newBatch.batchNo;
                                batch.expiryDate = newBatch.expiryDate;
                                batch.packageCostPrice = newBatch.packageCostPrice;

                                batch.goods.type = newBatch.goods.type;
                                batch.goods.subType = newBatch.goods.subType;
                                batch.goods.pieceNum = newBatch.goods.pieceNum;
                                batch.goods.packageUnit = newBatch.goods.packageUnit;
                                batch.goods.pieceUnit = newBatch.goods.pieceUnit;
                                batch.goods.packageCount = newBatch.goods.packageCount;
                                batch.goods.pieceCount = newBatch.goods.pieceCount;
                                batch.goods.outPackageCount = newBatch.goods.outPackageCount;
                                batch.goods.outPieceCount = newBatch.goods.outPieceCount;
                                batch.goods.lockingPackageCount = newBatch.goods.lockingPackageCount;
                                batch.goods.lockingPieceCount = newBatch.goods.lockingPieceCount;
                                batch.goods.medicineDosage = newBatch.goods.medicineDosage;
                                batch.goods.medicineDosageNum = newBatch.goods.medicineDosageNum;
                                batch.goods.medicineDosageUnit = newBatch.goods.medicineDosageUnit;
                            } else {
                                batch.goods.packageCount = 0;
                                batch.goods.pieceCount = 0;
                            }
                            return batch;
                        }) ?? [];

                    }
                    return newItem;
                });
                this.order.list = Clone(tempList);

                this._cacheOrderDraft = Clone(this.order);
                this.loading = false;
            },
            async reviewOrder() {
                try {
                    this.reviewBtnLoading = true;
                    this.reviewData.list = this.order.list.map((item) => {
                        return {
                            id: item.id,
                            goodsId: item.goodsId,
                            stockId: item.stockId,
                            stockInId: item.stockInId,
                            pieceCount: item.pieceCount,
                            packageCount: item.packageCount,
                        };
                    });
                    await StockOutAPI.review(this.orderId, this.reviewData);
                    this.showReviewDialog = false;
                    this.showDialog = false;
                    this.reviewBtnLoading = false;
                    this.$emit('refresh');
                } catch (e) {
                    this.reviewBtnLoading = false;
                    if (e.code === 962) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前出库单已被修改，请再次确认',
                            onClose: () => {
                                this.$emit('refresh');
                            },
                        });
                    } else if (e.code === 12010) {
                        const shortageMedicineTips = [];
                        const goodsStock = (e && e.detail) || [];
                        const { length } = goodsStock;

                        goodsStock.forEach((item, index) => {
                            let str = '';
                            str = `${item.goods.medicineCadn || item.goods.name || ''}`;
                            if (length - 1 === index) {
                                str += ' 库存不足';
                            }
                            shortageMedicineTips.push(str);
                        });

                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: shortageMedicineTips,
                            onClose: () => {
                                this.fetchData();
                                this.$refs.createForm.validate();
                            },
                        });
                    } else if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail);
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail);
                    } else if (e.code === 470) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${e.message}`,
                        });
                    }
                }
            },
            reviewOrderHandle(type) {
                if (type === 'pass') {
                    this.$refs.createForm.validate((val) => {
                        if (val) {
                            this.showReviewDialog = true;
                            this.reviewData.pass = true;
                        }
                    });
                } else {
                    this.showReviewDialog = true;
                    this.reviewData.pass = false;
                }
            },
            reviewHandle() {
                if (!this.reviewData.pass) {
                    this.$refs.checkForm.validate((val) => {
                        if (val) {
                            this.reviewOrder();
                        }
                    });
                } else {
                    this.reviewOrder();
                }
            },
            revokeHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确认撤回后单据将失效，可在此单据内修改并重新发起',
                    onConfirm: async () => {
                        await this.revoke();
                    },
                });
            },
            async revoke() {
                try {
                    this.btnLoading = true;
                    await StockOutAPI.revokeOrder(this.orderId);
                    this.btnLoading = false;
                    this.fetchData();
                    this.$emit('refresh', false, '', false);
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            /**
             * @desc 获取商品的批次信息
             * <AUTHOR>
             * @date 2019/12/16
             */
            async fetchGoodsBatch(id) {
                try {
                    const clinicId = this.order.fromOrganId || this.clinicId;
                    const params = {
                        all: '',
                        clinicId,
                    };
                    if (this.isGspReportLoss) {
                        params.batchId = this.gspReportLossGood.batchId;
                    }
                    if (this.multiPharmacyCanUse) {
                        params.pharmacyNo = this.order?.pharmacy?.no;
                    }
                    const { data } = await GoodsAPI.fetchGoodsBatch(id, params);
                    return data.rows || [];
                } catch (e) {
                    console.log(e);
                }
            },
            changeBatchs(batchs, item) {
                if (batchs && batchs.length) {
                    if (item.outReason) {
                        this.$set(batchs[0], 'outReason', item.outReason);
                        this.$set(batchs[0], 'packageCount', item.packageCount);
                        this.$set(batchs[0], 'pieceCount', item.pieceCount);
                    }
                    this.$set(item,'packageCount','');
                    this.$set(item,'pieceCount','');
                    this.$set(item,'outReason','');
                }
            },
            calcTotalPrice(row) {
                if (!row || !row.batchs || !row.batchs.length) {
                    return 0;
                }

                let totalPrice = new Big(0);

                // 遍历所有批次，累加批次金额
                row.batchs.forEach((batch) => {
                    // 调用批次金额计算方法
                    const batchPrice = this.calcBatchPrice(batch, row) || 0;

                    // 累加总价
                    totalPrice = totalPrice.plus(batchPrice);
                });

                // 返回数字
                return totalPrice.toNumber();
            },
            calcBatchPrice(batch, row) {
                if (!batch) {
                    return 0;
                }

                const packageCostPrice = batch.packageCostPrice || 0;
                const packageCount = batch.packageCount || 0;
                const pieceCount = batch.pieceCount || 0;
                const pieceNum = row && row.goods && row.goods.pieceNum ? row.goods.pieceNum : 1;

                // 计算总数量（大单位 + 小单位/换算比例）
                const totalCount = Big(packageCount).plus(Big(pieceCount).div(pieceNum));

                // 计算批次价格 = 单价 * 总数量
                const batchPrice = Big(packageCostPrice).times(totalCount);

                // 返回数字
                return batchPrice.toNumber();
            },
            // 点击 x 关闭 提示内容有变化是否保存草稿,
            closeDialog() {
                const compareKey = ['list', 'type', 'comment', 'fromOrganId', 'toOrganId', 'toUserId'];
                const equal = compareKey.filter((item) => {
                    return !isEqual(this.order[item], this._cacheOrderDraft[item]);
                });
                const confirmTitle = this.draftId ? '草稿信息发生变动，是否保存？' : '是否需要保存为草稿？';
                if (equal && equal.length && this.isAdd) {
                    const vm = this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: confirmTitle,
                        showConfirm: false,
                        showCancel: false,
                        footerPrepend: () => {
                            return (
                                <abc-space>
                                    <abc-button
                                        onClick={() => {
                                            this.saveDraftHandler();
                                            vm.close();// 手动关闭
                                        }}
                                    >
                                        保存
                                    </abc-button>
                                    <abc-button
                                        type="blank"
                                        onClick={() => {
                                            this.closeDraftHandler('goods-out');
                                            this.showDialog = false;
                                            vm.close();// 手动关闭
                                        }}
                                    >
                                        不保存
                                    </abc-button>
                                </abc-space>
                            );
                        },
                    });
                } else {
                    this.showDialog = false;
                }
            },

            async handleApprovalAgree() {
                const response = await this.approvalAgree({
                    gspInstId: this.gspInstId,
                });
                console.log('response', response);
                if (response.status === false) {
                    return;
                }
                this.$emit('confirm');
                this.$emit('refresh');
                this.showDialog = false;
            },

            async handleApprovalReject() {
                const response = await this.approvalReject(this.gspInstId);
                if (response.status === false) {
                    return;
                }
                this.$emit('confirm');
                this.$emit('refresh');
                this.showDialog = false;
            },
        },
    };
</script>

<style lang="scss" scoped>
.warn-choose-form {
    .spec {
        font-size: 12px;
        line-height: 14px;
        color: #7a8794;
    }
}

.custom-cascader {
    height: 100%;

    ::v-deep .abc-input-wrapper {
        height: 100%;
    }

    ::v-deep .abc-input__inner {
        padding-right: 26px !important;
    }
}
</style>
