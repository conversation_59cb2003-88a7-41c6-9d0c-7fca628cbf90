.full-inventory-wrapper {
    .check-step-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 70px;
        border-bottom: 1px dashed $P6;

        .step-item {
            line-height: 22px;
            color: $T2;

            &.current-step {
                color: $G1;
            }
        }

        .next-arrow {
            margin: 0 16px;
            font-size: 14px;
            color: $P1;
        }
    }

    .check-content-wrapper {
        text-align: center;

        .download-item {
            width: 260px;
            margin: 80px auto 0;
            text-align: left;

            .item {
                margin-bottom: 28px;

                .abc-button {
                    margin-left: 16px;
                }

                > p {
                    margin-top: 4px;
                    font-size: 12px;
                    line-height: 20px;
                    color: $T2;
                }
            }
        }

        .upload-item {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;

            .up-load-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 152px;
                height: 72px;
                margin-top: 24px;
                cursor: pointer;
                border: 1px solid $P6;
                border-radius: var(--abc-border-radius-small);

                > input {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    cursor: pointer;
                    opacity: 0;
                }

                .cis-icon-upload_file_hover {
                    font-size: 34px;
                    color: $P1;
                }

                > p {
                    margin-left: 16px;
                    font-size: 12px;
                    color: #8d9aa8;
                }

                &:hover {
                    border-color: $P1;

                    .cis-icon-upload_file_hover {
                        color: $T2;
                    }

                    > p {
                        color: $T2;
                    }
                }
            }

            .error-wrapper {
                flex: 1;

                .export-fail {
                    margin-top: 80px;

                    .cis-icon-Attention {
                        font-size: 32px;
                        color: $Y2;
                    }

                    .fail-title {
                        margin: 16px 0 8px 0;
                        font-size: 16px;
                        font-weight: bold;
                        line-height: 24px;
                        color: $T1;
                    }

                    > p {
                        font-size: 14px;
                        line-height: 22px;
                        color: $T2;
                    }
                }

                .fail-table {
                    margin: 20px auto 0;
                    cursor: pointer;

                    .fail-err-detail {
                        color: $Y2;
                    }
                }
            }

            .check-file-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 152px;
                height: 72px;
                padding: 0 8px;
                margin-top: 24px;
                margin-right: 12px;
                cursor: pointer;
                border: 1px solid $P6;
                border-radius: var(--abc-border-radius-small);

                &:hover {
                    .cis-icon-delete_file {
                        display: inline-block;
                    }
                }

                .cis-icon-delete_file {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    display: none;
                    color: #d0d1d9;
                    cursor: pointer;
                }

                > img {
                    width: 36px;
                    height: 40px;
                }

                > p {
                    font-size: 12px;
                    line-height: 16px;
                }

                .abc-progress-wrapper {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    z-index: -1;
                    height: 16px;

                    .percentage {
                        height: 16px;
                        line-height: 16px;
                    }

                    .progress {
                        height: 16px;
                    }
                }
            }
        }

        .success-item {
            margin: 80px auto 0;
            text-align: center;

            .cis-icon-chosen {
                font-size: 32px;
                color: $G2;
            }

            .success-title {
                margin: 16px auto 8px;
                font-size: 16px;
                line-height: 24px;
                color: $T1;
            }

            .total-info {
                font-size: 14px;
                line-height: 22px;

                > span {
                    margin-right: 20px;
                    color: $T2;

                    &:last-child {
                        margin-right: 0;
                    }

                    > span {
                        color: $T1;

                        &.green {
                            color: $G1;
                        }

                        &.red {
                            color: #ff3333;
                        }
                    }
                }
            }
        }
    }

    .full-inventory-footer {
        .abc-button-danger {
            position: absolute;
            top: 0;
            left: 0;
        }

        .check-file {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
        }
    }

    .check-form-footer {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 14px;
        line-height: 32px;
        color: #8d9aa8;
    }
}

.goods-check-mission-wrapper {
    .check-mode-btn {
        display: flex;
        flex-direction: column;

        .tips {
            display: block;
            margin-top: 8px;
            font-size: 12px;
            color: $T2;
        }

        .abc-button {
            display: flex;
            flex-direction: column;
            height: 66px;
            margin-left: 0;
        }
    }
}
