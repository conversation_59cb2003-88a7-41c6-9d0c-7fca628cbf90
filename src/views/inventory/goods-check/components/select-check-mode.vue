<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        custom-class="goods-check-mission-wrapper"
        title="选择盘点模式"
        :disabled-keyboard="disabledKeyboard"
        @open="pushDialogName"
        @close="popDialogName"
    >
        <div class="check-mode-btn">
            <abc-option-card
                :width="412"
                :height="72"
                icon="s-user-small-color"
                :selectable="false"
                title="单人盘点"
                description="药品/物资储存点固定，一人即可完成盘点工作"
                @click="openDialog(0)"
            ></abc-option-card>
            <abc-option-card
                style="margin-top: 12px;"
                :width="412"
                :height="72"
                icon="s-role-color"
                :selectable="false"
                title="多人盘点"
                description="药品/物资存在多个储存点，需多人协同完成盘点工作"
                @click="openDialog(1)"
            ></abc-option-card>
        </div>

        <create-check-mission
            v-if="showCheckMission"
            v-model="showCheckMission"
            :check-mode="checkMode"
            :pharmacy-no="pharmacyNo"
            @refresh="refreshHandler"
        ></create-check-mission>
    </abc-dialog>
</template>

<script>
    import CreateCheckMission from './add-check-mission';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    export default {
        name: 'SelectCheckMode',
        components: {
            CreateCheckMission,
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            pharmacyNo: Number,
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('选择盘点类型');

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,
            };
        },
        data() {
            return {
                checkMode: 0, // 0 单人盘点 1 多人盘点
                showCheckMission: false,
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },
        methods: {
            openDialog(mode) {
                this.checkMode = mode;
                this.showCheckMission = true;
            },
            refreshHandler() {
                this.$emit('refresh');
                this.showDialog = false;
            },
        },
    };
</script>
