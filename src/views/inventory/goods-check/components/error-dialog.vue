<template>
    <abc-dialog v-if="showDialog" v-model="showDialog" class="check-error-dialog" append-to-body>
        <div class="error-content">
            <div class="info">
                <span class="name ellipsis">{{ goods.name }}</span>
                <span style="font-size: 12px; line-height: 20px;">{{ goods.shortId }}</span>
            </div>
            <div class="short-id"></div>
            <div class="tips">该药品已在被添加到盘点单中，请删除后重新上传</div>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button type="blank" @click="showDialog = false">关闭</abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    export default {
        name: 'ErrorDialog',
        props: {
            value: {
                type: Boolean,
            },
            goods: {
                type: Object,
            },
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },
    };
</script>

<style lang="scss">
    .check-error-dialog {
        .error-content {
            display: flex;
            align-content: center;
            .info {
                width: 200px;
                display: flex;
                align-content: center;
                .name {
                    color: #000000;
                    max-width: 150px;
                }
                > span {
                    color: #7a8794;
                    align-items: center;
                }
            }
            .tips {
                color: #ff9933;
                margin-left: 16px;
            }
        }
    }
</style>
