<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        title="提示"
        content-styles="width: 600px; max-height: 900px"
    >
        <div style=" margin-bottom: 16px; color: #ff9933;">
            {{ message }}
        </div>
        <ul v-if="goodsArray && goodsArray.length">
            <abc-table-common :config="tableConfig" :data="goodsArray">
                <template #name="{ item }">
                    {{ item.medicineCadn || item.name }}
                </template>
                <template #type="{ item }">
                    {{ item | goodsTypeName }}
                </template>
                <template #stock="{ item }">
                    {{ item | complexCount }}
                </template>
                <template #dimension="{ item: { checkBatchType } }">
                    {{ checkBatchType }}
                </template>
                <template #mission="{ item: { taskName } }">
                    {{ taskName }}
                </template>
                <template #owner="{ item: { ownerName } }">
                    {{ ownerName }}
                </template>
                <template #cadn="{ item: { goods } }">
                    {{ goods && (goods.medicineCadn || goods.name) }}
                </template>
                <template #reason="{ item }">
                    <abc-popover theme="yellow">
                        <span v-for="(it, index) in item.msgs" slot="reference" :key="index">{{ it }}</span>
                        <div class="order-list-wrapper">
                            <li v-for="(it, index) in item.msgs" :key="index">
                                <span>{{ it }}</span>
                            </li>
                        </div>
                    </abc-popover>
                </template>

                <template #afterCadn="{ item: { goodsNow } }">
                    {{ goodsNow.medicineCadn || goodsNow.name }}
                </template>
                <!--修改前-->
                <template #before="{ item: { goodsBefore } }">
                    {{ goodsBefore | goodsSpec }}
                </template>
                <!--修改后-->
                <template #after="{ item: { goodsNow } }">
                    {{ goodsNow | goodsSpec }}
                </template>
                <!--批次-->
                <template #batch="{ item: { batchNo } }">
                    {{ batchNo || '' }}
                </template>
                <!--盘点数量-->
                <template #count="{ item }">
                    {{ formatStock(item.goodsNow, item.packageCount, item.pieceCount) }}
                </template>
            </abc-table-common>
        </ul>
        <div slot="footer" class="dialog-footer">
            <template v-if="errorCode === 12238">
                <abc-button
                    type="danger"
                    style="margin-right: auto;"
                    @click="clearStock"
                >
                    线下无以上药品，盘点为0
                </abc-button>
                <abc-button v-if="taskId === parentTaskId" @click="keepCurrentStock">
                    保持账面库存
                </abc-button>
            </template>
            <template v-if="errorCode === 12243">
                <abc-button @click="ignoreHandler">
                    继续
                </abc-button>
            </template>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { formatStock } from '../common';
    export default {
        name: 'UncheckGoodsDialog',
        props: {
            value: {
                type: Boolean,
            },
            messageDetail: {
                type: Object,
            },
            taskId: {
                type: [String, Number],
            },
            parentTaskId: {
                type: [String, Number],
            },
        },
        computed: {
            errorCode() {
                return this.messageDetail.code;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            dialogTitle() {
                return this.errorCode === 12238 ? '本任务未盘点药品' : '提示';
            },
            tableConfig() {
                if (this.errorCode === 12504) {
                    return [
                        {
                            label: '药品名称',
                            prop: 'cadn',
                            width: 40,
                            titleStyle: {
                                textAlign: 'left',
                            },
                            bodyStyle: {
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '导入失败原因',
                            prop: 'reason',
                            width: 50,
                            titleStyle: {
                                textAlign: 'left',
                            },
                            bodyStyle: {
                                textAlign: 'left',
                            },
                        },
                    ];
                }
                if (this.errorCode === 12243) {
                    return [
                        {
                            label: '药品名称',
                            prop: 'afterCadn',
                            width: 40,
                            titleStyle: {
                                textAlign: 'left',
                            },
                            bodyStyle: {
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '修改前',
                            prop: 'before',
                            width: 50,
                            titleStyle: {
                                textAlign: 'left',
                            },
                            bodyStyle: {
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '修改后',
                            prop: 'after',
                            width: 50,
                            titleStyle: {
                                textAlign: 'left',
                            },
                            bodyStyle: {
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '生产批号',
                            prop: 'batch',
                            width: 50,
                            titleStyle: {
                                textAlign: 'left',
                            },
                            bodyStyle: {
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '盘点数量',
                            prop: 'count',
                            width: 50,
                            titleStyle: {
                                textAlign: 'left',
                            },
                            bodyStyle: {
                                textAlign: 'left',
                            },
                        },
                    ];
                }
                return this.errorCode === 12236 ?
                    [
                        {
                            prop: 'name',
                            label: '药品名称',
                            width: 180,
                        },
                        {
                            prop: 'dimension',
                            label: '盘点维度',
                            width: 90,
                        },
                        {
                            prop: 'mission',
                            label: '子任务',
                            width: 90,
                        },
                        {
                            prop: 'owner',
                            label: '任务负责人',
                            width: 90,
                        },
                    ] :
                    [
                        {
                            prop: 'name',
                            label: '药品名称',
                            width: 200,
                        },
                        {
                            prop: 'type',
                            label: '类型',
                            width: 50,
                        },
                        {
                            prop: 'stock',
                            label: '账面库存',
                            width: 90,
                        },
                    ];
            },
            goodsArray() {
                if (this.messageDetail.detail instanceof Array) {
                    return this.messageDetail.detail || [];
                }
                return [];
            },
            message() {
                return this.messageDetail.message;
            },
        },
        methods: {
            formatStock,
            /**
             * @desc 账面数量清空
             */
            clearStock() {
                const data = this.goodsArray.map((item) => {
                    return {
                        batchs: [
                            {
                                goodsId: item.goodsId,
                                beforePieceCount: item.pieceCount,
                                beforePackageCount: item.packageCount,
                                pieceCount: 0,
                                packageCount: 0,
                                pieceNum: item.pieceNum,
                                batchId: null,
                            },
                        ],
                    };
                });
                this.$emit('clear', data);
                this.showDialog = false;
            },
            /**
             * @desc 保持当前库存
             */
            keepCurrentStock() {
                const data = this.goodsArray.map((item) => {
                    return {
                        batchs: [
                            {
                                goodsId: item.goodsId,
                                beforePieceCount: item.pieceCount,
                                beforePackageCount: item.packageCount,
                                pieceCount: item.pieceCount,
                                packageCount: item.packageCount,
                                pieceNum: item.pieceNum,
                                batchId: null,
                            },
                        ],
                    };
                });
                this.$emit('keep', data);
                this.showDialog = false;
            },
            /**
             * @desc 继续盘点
             */
            ignoreHandler() {
                this.$emit('ignoreSpec');
            },
        },
    };
</script>
