<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        title="提示"
        content-styles="width: 600px"
    >
        <div style=" margin-bottom: 16px; color: #ff9933;">
            {{ message }}
        </div>
        <ul>
            <abc-table-common :config="tableConfig" :data="goodsArray">
                <template #name="{ item }">
                    {{ item.medicineCadn || item.name }}
                </template>
                <template #type="{ item }">
                    {{ item | goodsTypeName }}
                </template>
                <template #stock="{ item }">
                    {{ item | complexCount }}
                </template>
            </abc-table-common>
        </ul>
        <div slot="footer" class="dialog-footer">
            <abc-button @click="clearStock">
                全部盘为0
            </abc-button>
            <abc-button @click="keepCurrentStock">
                保持账面库存
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    export default {
        name: 'UncheckGoodsDialog',
        props: {
            value: {
                type: Boolean,
            },
            messageDetail: {
                type: Object,
            },
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            tableConfig() {
                return [
                    {
                        prop: 'name',
                        label: '药品名称',
                        width: 200,
                    },
                    {
                        prop: 'type',
                        label: '类型',
                        width: 50,
                    },
                    {
                        prop: 'stock',
                        label: '账面库存',
                        width: 90,
                    },
                ];
            },
            goodsArray() {
                return this.messageDetail.detail || [];
            },
            message() {
                return this.messageDetail.message;
            },
        },
        methods: {
            /**
             * @desc 账面数量清空
             */
            clearStock() {
                const data = this.goodsArray.map((item) => {
                    return {
                        batchs: [
                            {
                                goodsId: item.goodsId,
                                beforePieceCount: item.pieceCount,
                                beforePackageCount: item.packageCount,
                                pieceCount: 0,
                                packageCount: 0,
                                pieceNum: item.pieceNum,
                                batchId: null,
                            },
                        ],
                    };
                });
                this.$emit('clear', data);
            },
            /**
             * @desc 保持当前库存
             */
            keepCurrentStock() {
                const data = this.goodsArray.map((item) => {
                    return {
                        batchs: [
                            {
                                goodsId: item.goodsId,
                                beforePieceCount: item.pieceCount,
                                beforePackageCount: item.packageCount,
                                pieceCount: item.pieceCount,
                                packageCount: item.packageCount,
                                pieceNum: item.pieceNum,
                                batchId: null,
                            },
                        ],
                    };
                });
                this.$emit('keep', data);
            },
        },
    };
</script>
