<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle"
        class="auto-form goods-check-wrapper"
        :before-close="closeDialog"
        :disabled-keyboard="disabledKeyboard"
        v-bind="dialogConfig"
        @open="pushDialogName"
        @close="popDialogName"
    >
        <template slot="title">
            <slot name="title">
                <abc-layout>
                    <abc-section>
                        <abc-space>
                            <abc-title level="1">
                                {{ dialogTitle }}
                            </abc-title>

                            <template v-if="currentDraftId">
                                <abc-tag-v2
                                    shape="square"
                                    size="medium"
                                    theme="warning"
                                    variant="outline"
                                >
                                    草稿
                                </abc-tag-v2>

                                <!-- 云草稿状态显示 -->
                                <template v-if="isShowCloudStatus">
                                    <abc-tooltip
                                        :content="cloudStatusText === '保存失败，点此重试' ? '点击或按下 Alt+S 可再次保存' : cloudStatusText"
                                    >
                                        <abc-space
                                            direction="horizontal"
                                            :size="6"
                                            style="margin-left: 16px; cursor: pointer;"
                                            @click="cloudStatusType === 'danger' ? saveDraftToCloud() : null"
                                        >
                                            <!-- 根据状态显示不同图标 -->
                                            <abc-icon
                                                v-if="cloudStatusIcon === 'loading'"
                                                icon="s-cloud-line"
                                                color="var(--abc-color-T3)"
                                            ></abc-icon>
                                            <abc-icon
                                                v-else-if="cloudStatusIcon === 'success'"
                                                icon="s-cloudsaved-line"
                                                color="var(--abc-color-G2)"
                                            ></abc-icon>
                                            <abc-icon
                                                v-else-if="cloudStatusIcon === 'error'"
                                                icon="s-cloudsavedfailed-line"
                                                color="var(--abc-color-R6)"
                                            ></abc-icon>

                                            <!-- 根据状态显示不同文本 -->
                                            <abc-text
                                                size="normal"
                                                :theme="cloudStatusType === 'success' ? 'gray-light' :
                                                       cloudStatusType === 'danger' ? 'danger' :
                                                       'gray-light'"
                                            >
                                                {{ cloudStatusText }}
                                            </abc-text>
                                        </abc-space>
                                    </abc-tooltip>
                                </template>
                            </template>
                        </abc-space>
                    </abc-section>
                </abc-layout>
            </slot>
        </template>
        <abc-form
            ref="createForm"
            v-abc-loading="loading"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-tips-card-v2
                        v-if="isDraftOrderChange"
                        :icon="true"
                        :title="false"
                        theme="warning"
                        variant="outline"
                        align="center"
                        :border-radius="true"
                        :custom-icon="{ name: 's-alert-fill' }"
                        style="margin-bottom: 16px;"
                    >
                        盘点单已在其他设备修改
                        <template #operate>
                            <abc-button
                                shape="square"
                                variant="text"
                                theme="primary"
                                size="small"
                                :disabled="false"
                            >
                                立即更新
                            </abc-button>
                        </template>
                    </abc-tips-card-v2>
                    <abc-form-item-group is-excel>
                        <abc-descriptions
                            v-if="multiPharmacyCanUse || (isFullCheckOrder && checkRangeText) || !taskId"
                            :column="3"
                            :label-width="90"
                            grid
                            size="large"
                            background
                        >
                            <abc-descriptions-item
                                v-if="multiPharmacyCanUse"
                                content-class-name="ellipsis"
                                :span="1"
                                label="盘点库房"
                            >
                                <span>{{ order?.pharmacy?.name }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="isFullCheckOrder && checkRangeText"
                                content-class-name="ellipsis"
                                :span="1"
                                label="盘点范围"
                            >
                                <span>{{ checkRangeText }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="!taskId"
                                content-class-name="ellipsis"
                                content-padding="0px"
                                :span="commentSpan"
                                label="备注"
                            >
                                <abc-form-item>
                                    <abc-input
                                        :value="order.comment"
                                        :max-length="200"
                                        @change="commentChange"
                                    ></abc-input>
                                </abc-form-item>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        :render-config="headerConfig"
                        :data-list="order.list"
                        empty-size="small"
                        :custom-tr-key="createRowKey"
                        :custom-tr-class="createTrClassName"
                        child-key="batchs"
                        :show-checked="false"
                        :show-hover-tr-bg="false"
                        support-delete-tr
                        need-delete-confirm
                        :enable-virtual-list="enableVirtualList"
                        :virtual-list-config="{
                            bufferSize: 7,
                            rowHeight: 49
                        }"
                        cell-size="large"
                        @delete-tr="deleteTr"
                        @sort="sortCountChange"
                    >
                        <template #topHeader>
                            <abc-flex flex="1" align="center" justify="space-between">
                                <goods-auto-complete-cover-title
                                    ref="goodsAutoCompleteRef"
                                    class="entry-medicine back-focus-to-autocomplete"
                                    :placeholder="`请输入${orderMainNameText}名称或扫码添加`"
                                    :search="searchKey"
                                    is-out
                                    :only-stock="true"
                                    :need-search-no-stock="false"
                                    :enable-barcode-detector="!isDialogShowing"
                                    :clinic-id="clinicId"
                                    :width="460"
                                    :custom-type-id-list="curCustomTypeIdList"
                                    :type-id-list="curTypeIdList"
                                    :pharmacy-no="order.pharmacy?.no"
                                    :type="searchParams.type"
                                    :sub-type="searchParams.subType"
                                    :c-m-spec="searchParams.cMSpec"
                                    is-close-validate
                                    :next-input-auto-focus="false"
                                    :enable-local-search="enableLocalSearch"
                                    size="medium"
                                    :custom-error-handler="customErrorHandler"
                                    @selectGoods="selectGoods"
                                    @traceableCodeEnter="traceableCodeEnter"
                                >
                                    <abc-icon slot="prepend" icon="n-add-line-medium" color="var(--abc-color-T3)"></abc-icon>
                                </goods-auto-complete-cover-title>

                                <abc-space v-if="isFullCheckOrder">
                                    <div class="select-file-btn">
                                        <input
                                            ref="excelInput"
                                            type="file"
                                            class="select-file"
                                            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                            @change="changeFileHandler"
                                        />
                                        <abc-button variant="ghost" theme="default" @click="handleImportClick">
                                            从 Excel 导入盘点结果
                                        </abc-button>
                                    </div>

                                    <abc-button variant="ghost" theme="default" @click="downCheckTemplate">
                                        下载 Excel 模版
                                    </abc-button>
                                </abc-space>
                            </abc-flex>
                        </template>
                        <!--药品名称-->
                        <template
                            #name="{
                                trData: row, parentData
                            }"
                        >
                            <display-name-cell
                                v-if="!parentData"
                                :goods="row.goods"
                                :hover-config="{
                                    openDelay: 500,
                                    showPrice: true,
                                    showShebaoCode: true,
                                    pharmacyNo: order.pharmacy ? order.pharmacy.no : pharmacyNo
                                }"
                            ></display-name-cell>
                        </template>
                        <!--药品编码-->
                        <template
                            #shortId="{
                                trData: row, parentData
                            }"
                        >
                            <abc-table-cell v-if="!parentData" class="ellipsis">
                                <overflow-tooltip :content="row.goods.shortId">
                                </overflow-tooltip>
                            </abc-table-cell>
                        </template>
                        <!--类型-->
                        <template
                            #goodsType="{
                                trData: row, parentData
                            }"
                        >
                            <abc-table-cell v-if="!parentData" class="ellipsis">
                                <span
                                    v-abc-title.ellipsis="transGoodsTypeName(row.goods)"
                                ></span>
                            </abc-table-cell>
                        </template>

                        <!--二级分类-->
                        <template
                            #grade="{
                                trData: row, parentData
                            }"
                        >
                            <abc-table-cell v-if="!parentData" class="ellipsis">
                                <span
                                    class="ellipsis"
                                    :title="row.goods.customTypeName || ''"
                                >{{ row.goods.customTypeName || '' }}</span>
                            </abc-table-cell>
                        </template>

                        <!--批次-->
                        <template
                            #batch="{
                                trData: row, parentData
                            }"
                        >
                            <abc-form-item v-if="!parentData" :required="isRequiredGoodsCheckBatch">
                                <batch-select
                                    v-model="row.batchs"
                                    style="height: 100%;"
                                    :goods="row.goods"
                                    :goods-id="row.goods.id"
                                    :pharmacy-no="order.pharmacy?.no"
                                    :multi-pharmacy-can-use="!!multiPharmacyCanUse"
                                    :data-permission-scene="DATA_PERMISSION_CONTROL_SCENE.check"
                                    @change="(select) => changeBatchs(select, row)"
                                ></batch-select>
                            </abc-form-item>

                            <abc-table-cell v-else class="ellipsis">
                                <span
                                    :title="row.batchId"
                                >
                                    {{ row.batchId }}
                                </span>
                            </abc-table-cell>
                        </template>

                        <!--生产批号-->
                        <template
                            #batchNo="{
                                trData: row, parentData
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <span v-if="parentData" :title="row.batchNo">
                                    {{ row.batchNo }}
                                </span>
                            </abc-table-cell>
                        </template>
                        <!--账面数量-->
                        <template
                            #currentCount="{
                                trData: row, parentData: parent
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <abc-popover
                                    placement="top"
                                    trigger="hover"
                                    theme="yellow"
                                    :disabled="!row.showTips || !draftId || !changeCount(
                                        row.beforePieceCount,
                                        row.beforePackageCount,
                                        row.goods || parent.goods,
                                        row.draftBeforePieceCount,
                                        row.draftBeforePackageCount
                                    )"
                                    class="ellipsis"
                                >
                                    <template
                                        v-if="!parent && row.batchs.length === 0"
                                        slot="reference"
                                    >
                                        <abc-text
                                            v-abc-title="formatStock(row.goods, row.beforePackageCount, row.beforePieceCount)"
                                            :theme="draftChangeCountClass(row)"
                                        ></abc-text>
                                    </template>
                                    <template
                                        v-if="parent && parent.batchs.length"
                                        slot="reference"
                                    >
                                        <abc-text
                                            v-abc-title="formatStock(parent.goods, row.beforePackageCount, row.beforePieceCount)"
                                            :theme="draftChangeCountClass(row, parent.goods)"
                                        ></abc-text>
                                    </template>
                                    <span>{{ draftChangeCount(row, parent) }}</span>
                                </abc-popover>
                            </abc-table-cell>
                        </template>

                        <!--实际数量-->
                        <template
                            #trueCount="{
                                trData: row, parentData: parent
                            }"
                        >
                            <template v-if="!parent && row.batchs.length === 0">
                                <abc-form-item
                                    class="check-true-count form-item-flex"
                                    :required="isEmptyCount(row)"
                                    style="margin: 0;"
                                >
                                    <abc-flex v-if="isEditCheckBatch || parent" style="height: 100%;">
                                        <template v-if="!isChineseMedicine(row.goods)">
                                            <goods-count-autocomplete
                                                :value="row.packageCount"
                                                class="count-input"
                                                :handle-input="()=>{row.showTips = false}"
                                                :goods-unit="row.goods?.packageUnit"
                                                :goods-count="calcGoodsCompleteCount(row, parent).packageCount"
                                                :input-config="getConfig(row.goods)"
                                                :style=" { height: '100%' }"
                                                @select="(val) => handleSelectCount(val, row, row.goods, 'packageCount')"
                                                @enter="enterEvent"
                                                @change="handleChangeCount($event, row, row.goods, 'packageCount')"
                                            >
                                            </goods-count-autocomplete>
                                        </template>
                                        <template v-if="!unitEqual(row.goods) || isChineseMedicine(row.goods)">
                                            <goods-count-autocomplete
                                                :value="row.pieceCount"
                                                class="count-input"
                                                :handle-input="()=>{row.showTips = false}"
                                                :goods-unit="row.goods?.pieceUnit"
                                                :goods-count="calcGoodsCompleteCount(row, parent).pieceCount"
                                                :input-config="getConfig(row.goods)"
                                                :style=" { height: '100%' }"
                                                @select="(val) => handleSelectCount(val, row, row.goods, 'pieceCount')"
                                                @enter="enterEvent"
                                                @change="handleChangeCount($event, row, row.goods, 'pieceCount')"
                                            >
                                            </goods-count-autocomplete>
                                        </template>
                                    </abc-flex>
                                </abc-form-item>
                            </template>
                            <template v-else-if="parent && parent.batchs.length">
                                <abc-form-item class="check-true-count form-item-flex" :required="isEmptyCount(row, parent.goods)" style="margin: 0;">
                                    <abc-flex style="height: 100%;">
                                        <template v-if="!isChineseMedicine(parent.goods)">
                                            <goods-count-autocomplete
                                                :value="row.packageCount"
                                                class="count-input"
                                                :handle-input="()=>{row.showTips = false}"
                                                :goods-unit="parent.goods?.packageUnit"
                                                :goods-count="calcGoodsCompleteCount(row, parent).packageCount"
                                                :input-config="getConfig(parent.goods)"
                                                :style=" { height: '100%' }"
                                                @select="(val) => handleSelectCount(val, row, parent.goods, 'packageCount', parent)"
                                                @enter="enterEvent"
                                                @change="handleChangeCount($event, row, parent.goods, 'packageCount')"
                                            >
                                            </goods-count-autocomplete>
                                        </template>
                                        <template v-if="!unitEqual(parent.goods) || isChineseMedicine(parent.goods)">
                                            <goods-count-autocomplete
                                                :value="row.pieceCount"
                                                class="count-input"
                                                :handle-input="()=>{row.showTips = false}"
                                                :goods-unit="parent.goods?.pieceUnit"
                                                :goods-count="calcGoodsCompleteCount(row, parent).pieceCount"
                                                :input-config="getConfig(parent.goods)"
                                                :style=" { height: '100%' }"
                                                @select="(val) => handleSelectCount(val, row, parent.goods, 'pieceCount', parent)"
                                                @enter="enterEvent"
                                                @change="handleChangeCount($event, row, parent.goods, 'pieceCount')"
                                            >
                                            </goods-count-autocomplete>
                                        </template>
                                    </abc-flex>
                                </abc-form-item>
                            </template>
                        </template>
                        <!--盈亏数量-->
                        <template
                            #fullOrLass="{
                                trData: row, parentData: parent
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <template v-if="!parent && row.batchs.length === 0">
                                    <template v-if="isEditCheckBatch">
                                        <abc-text v-abc-title.ellipsis="changeCountText(row)" :theme="changeCountTheme(row)">
                                        </abc-text>
                                    </template>
                                </template>
                                <template v-if="parent && parent.batchs.length">
                                    <abc-text v-abc-title.ellipsis="changeCountText(row, parent.goods)" :theme="changeCountTheme(row, parent.goods)">
                                    </abc-text>
                                </template>
                            </abc-table-cell>
                        </template>

                        <template
                            #changeCostPrice="{
                                trData: row, parentData: parent
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <data-permission-control>
                                    <abc-text
                                        v-if="parent"
                                        v-abc-title.ellipsis="inventoryFormatMoney(row.totalCostPriceChange)"
                                        :theme="calcCountTheme(row.totalCostPriceChange)"
                                    >
                                    </abc-text>
                                </data-permission-control>
                            </abc-table-cell>
                        </template>

                        <template
                            #changePrice="{
                                trData: row, parentData: parent
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <abc-text
                                    v-if="parent"
                                    v-abc-title.ellipsis="inventoryFormatMoney(row.totalPriceChange)"
                                    :theme="calcCountTheme(row.totalPriceChange)"
                                ></abc-text>
                            </abc-table-cell>
                        </template>

                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item, parentData: parent
                            }"
                        >
                            <template v-if="!parent && item.batchs.length === 0">
                                <template v-if="isEditCheckBatch">
                                    <!--<traceable-code-cell-->
                                    <!--    v-if="!parent && item.batchs.length === 0"-->
                                    <!--    :ref="`traceableCodeCellRef${item.keyId}`"-->
                                    <!--    v-model="item.traceableCodeList"-->
                                    <!--    :goods="item.goods"-->
                                    <!--    :item="item"-->
                                    <!--    :goods-count="getTransUnitCount(item, item.goods)"-->
                                    <!--    :scene-type="'goodsCheck'"-->
                                    <!--    :need-validate="validateCell"-->
                                    <!--    :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"-->
                                    <!--    @triggerFormValidate="triggerFormValidate"-->
                                    <!--&gt;</traceable-code-cell>-->
                                    <display-traceable-code-manager
                                        v-if="!parent && item.batchs.length === 0"
                                        v-model="item.traceableCodeList"
                                        :goods="item.goods"
                                        :item="item"
                                        :goods-count="getTransUnitCount(item, item.goods)"
                                        :scene-type="'goodsCheck'"
                                        :need-validate="validateCell"
                                        :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                                        @triggerFormValidate="triggerFormValidate"
                                    >
                                    </display-traceable-code-manager>
                                </template>
                            </template>

                            <!--<traceable-code-cell-->
                            <!--    v-else-if="parent && parent.batchs.length"-->
                            <!--    :ref="`traceableCodeCellRef${item.keyId}`"-->
                            <!--    v-model="item.traceableCodeList"-->
                            <!--    :goods="parent.goods"-->
                            <!--    :item="item"-->
                            <!--    :goods-count="getTransUnitCount(item, parent.goods)"-->
                            <!--    :scene-type="'goodsCheck'"-->
                            <!--    :need-validate="validateCell"-->
                            <!--    :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"-->
                            <!--    @triggerFormValidate="triggerFormValidate"-->
                            <!--&gt;</traceable-code-cell>-->
                            <display-traceable-code-manager
                                v-else-if="parent && parent.batchs.length"
                                v-model="item.traceableCodeList"
                                :goods="parent.goods"
                                :item="item"
                                :goods-count="getTransUnitCount(item, parent.goods)"
                                :scene-type="'goodsCheck'"
                                :need-validate="validateCell"
                                :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                                @triggerFormValidate="triggerFormValidate"
                            >
                            </display-traceable-code-manager>
                        </template>

                        <template #footer>
                            <abc-flex
                                flex="1"
                                align="center"
                                justify="flex-end"
                                style="padding: 0 12px;"
                            >
                                <abc-space v-if="order.list.length" :size="4">
                                    <abc-text theme="gray">
                                        品种
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ kindCount }}
                                    </abc-text>

                                    <template v-if="showTotalPriceChange">
                                        <data-permission-control>
                                            <span>
                                                <abc-text theme="gray">
                                                    ，盈亏总金额(进价)
                                                </abc-text>
                                                <abc-text theme="black">
                                                    {{ totalCostPriceChange | formatMoney(false) }}
                                                </abc-text>
                                            </span>
                                        </data-permission-control>

                                        <abc-text theme="gray">
                                            ，盈亏总金额(售价)
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ totalPriceChange | formatMoney(false) }}
                                        </abc-text>
                                    </template>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <template v-if="taskId">
                <abc-button
                    v-if="!isFinishedSubTask"
                    :disabled="buttonLoading || noGoods"
                    :loading="saveLoading"
                    type="blank"
                    style="margin-right: auto;"
                    @click="saveCheckForm"
                >
                    保存为草稿
                </abc-button>
                <abc-button
                    :loading="buttonLoading"
                    :disabled="saveLoading || noGoods"
                    @click="finishCheckForm()"
                >
                    完成本次任务
                </abc-button>
                <abc-button type="blank" :disabled="buttonLoading" @click="closeDialog">
                    关闭
                </abc-button>
            </template>
            <template v-else>
                <abc-button
                    v-if="draftId"
                    type="danger"
                    :loading="deleteDraftButtonLoading"
                    :disabled="saveDraftButtonLoading"
                    @click="deleteDraftHandler"
                >
                    删除
                </abc-button>
                <abc-button
                    v-else
                    type="danger"
                    :disabled="buttonLoading"
                    @click="stopCheck"
                >
                    终止盘点
                </abc-button>

                <div style="display: flex; align-items: center; margin-left: auto;">
                    <abc-button
                        type="primary"
                        :loading="buttonLoading"
                        :disabled="order.list.length === 0"
                        @click="submitCheckForm"
                    >
                        {{ submitBtnText }}
                    </abc-button>
                    <!--                    <abc-button-->
                    <!--                        type="blank"-->
                    <!--                        :loading="saveDraftButtonLoading"-->
                    <!--                        :disabled="deleteDraftButtonLoading || disableSaveDraftButton"-->
                    <!--                        @click="saveDraftHandler()"-->
                    <!--                    >-->
                    <!--                        保存草稿-->
                    <!--                    </abc-button>-->
                    <abc-button type="blank" :disabled="buttonLoading" @click="closeDialog">
                        关闭
                    </abc-button>
                </div>
            </template>
        </div>

        <abc-dialog
            v-if="showCheckDialog"
            v-model="showCheckDialog"
            title="账面数量变动确认"
            content-styles="width:960px"
            append-to-body
            @open="pushDialogName('账面数量变动确认')"
            @close="popDialogName('账面数量变动确认')"
        >
            <abc-form ref="confirmForm" class="v3-goods-wrapper">
                <abc-p style="margin-bottom: 16px;">
                    以下药品物资盘点期间账面数量变动，请确认实际数量是否准确：
                </abc-p>
                <abc-table type="excel" custom :data-list="submitDataList">
                    <abc-table-header>
                        <abc-table-td :width="220" style="flex: 1;">
                            <span>药品名称</span>
                        </abc-table-td>
                        <abc-table-td :width="100">
                            <span>批次</span>
                        </abc-table-td>
                        <abc-table-td :width="100">
                            <span>生产批号</span>
                        </abc-table-td>
                        <abc-table-td :width="120" align="right">
                            <span>当前账面数量</span>
                        </abc-table-td>
                        <abc-table-td :width="120" align="right">
                            <span>变动账面数量</span>
                        </abc-table-td>
                        <abc-table-td :width="108">
                            <span>实际数量</span>
                        </abc-table-td>
                        <abc-table-td :width="40">
                        </abc-table-td>
                        <abc-table-td :width="80" align="right">
                            <span>盈亏数量</span>
                        </abc-table-td>
                    </abc-table-header>
                    <abc-table-body>
                        <abc-table-tr v-for="item in submitDataList" :key="item.keyId">
                            <abc-table-td custom-td :width="220" style=" flex: 1; flex-direction: column; align-items: flex-start; justify-content: center; padding: 0 10px;">
                                <div class="value bold ellipsis" style="width: 100%;">
                                    {{ item.goods | goodsFullName }}
                                </div>
                                <div class="ellipsis" style="width: 100%;">
                                    <span class="label-small">{{ item.goods | goodsSpec }}</span>
                                    <span class="label-small">{{ item.goods.manufacturer || '' }}</span>
                                </div>
                            </abc-table-td>
                            <abc-table-td :width="100">
                                {{ item.batchId }}
                            </abc-table-td>
                            <abc-table-td :width="100">
                                {{ item.batchNo }}
                            </abc-table-td>
                            <abc-table-td :width="120" align="right">
                                <span v-abc-title.ellipsis="formatStock(item.goods, item.beforePackageCount, item.beforePieceCount)">
                                </span>
                            </abc-table-td>
                            <abc-table-td :width="120" align="right">
                                <span v-abc-title.ellipsis="draftChangeCount(item, null, true)">
                                </span>
                            </abc-table-td>
                            <abc-table-td custom-td :width="74">
                                <abc-form-item v-if="!isChineseMedicine(item.goods)" :required="isNull(item.pieceCount)">
                                    <abc-input v-model="item.packageCount" size="medium">
                                        <span slot="appendInner">{{ item.goods?.packageUnit ?? '' }}</span>
                                    </abc-input>
                                </abc-form-item>
                            </abc-table-td>
                            <abc-table-td custom-td :width="74">
                                <abc-form-item v-if="!unitEqual(item.goods) || isChineseMedicine(item.goods)" :required="isNull(item.packageCount)">
                                    <abc-input v-model="item.pieceCount" size="medium">
                                        <span slot="appendInner">{{ item.goods?.pieceUnit ?? '' }}</span>
                                    </abc-input>
                                </abc-form-item>
                            </abc-table-td>
                            <abc-table-td :width="80" align="right">
                                <span v-abc-title.ellipsis="changeCountText(item)" :class="changeCountClass(item)"></span>
                            </abc-table-td>
                        </abc-table-tr>
                    </abc-table-body>
                </abc-table>
            </abc-form>

            <div slot="footer" class="dialog-footer">
                <abc-button
                    :loading="buttonLoading"
                    @click="handleConfirm"
                >
                    确定
                </abc-button>
                <abc-button
                    type="blank"
                    :disabled="buttonLoading"
                    @click="showCheckDialog = false"
                >
                    关闭
                </abc-button>
            </div>
        </abc-dialog>

        <abc-dialog
            v-if="warnVisible"
            v-model="warnVisible"
            class="check-warn-wrapper"
            title="提示"
            content-styles="height: auto; min-height: 184px;max-height: 580px;width: 580px;"
        >
            <p class="medicine-info">
                {{ goodsInfo | goodsFullName }} 存在未确认的调拨单，需要调入方确认后可进行盘点
            </p>
            <div v-for="(item, index) in orderList" :key="index" class="order-wrapper">
                <p>
                    <span>{{ item.orderNo }}</span>
                    <span>{{ item.countStr }}</span>
                </p>
                <p>
                    <span>{{ item.fromOrganName }}</span>
                    <span><i class="iconfont cis-icon-Arrow_Rgiht_"></i></span>
                    <span>{{ item.toOrganName }}</span>
                </p>
            </div>
        </abc-dialog>

        <down-check-template-dialog
            v-if="showDownTemplateDialog"
            v-model="showDownTemplateDialog"
            :goods-sub-type="searchParams.subType"
            :goods-type="searchParams.type"
            :goods-c-m-spec="searchParams.cMSpec"
            :custom-type-id-list="curCustomTypeIdList"
            :type-id-list="curTypeIdList"
            :pharmacy-no="order.pharmacy && order.pharmacy.no"
        ></down-check-template-dialog>

        <check-error-dialog
            v-if="showErrorDialog"
            v-model="showErrorDialog"
            :goods="curRepeatGoods"
        ></check-error-dialog>

        <error-goods-dialog
            v-if="showErrorGoods"
            v-model="showErrorGoods"
            :message-detail="errorMessageDetail"
            :task-id="taskId"
            :parent-task-id="parentTaskId"
            @clear="clearHandler"
            @keep="keepHandler"
        ></error-goods-dialog>

        <dialog-undisposed-goods
            v-if="showUndisposedGoodsDialog"
            v-model="showUndisposedGoodsDialog"
            :pharmacy-no="order.pharmacy ? order.pharmacy.no : pharmacyNo"
            :error-list="undisposedGoodsList"
            @createOrder="handleCreate"
        ></dialog-undisposed-goods>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import Big from 'big.js';

    import GoodsAPI from 'api/goods';
    import StockCheckAPI from 'api/goods/check';

    import EnterEvent from 'views/common/enter-event';
    import AutoFocus from '../mixins/auto-focus';
    import DeleteGoodsHandler from '../mixins/delete-goods-handler';
    import GoodsTableV3Mixins from 'views/inventory/mixins/goods-table-v3.js';
    import Draft from 'views/inventory/mixins/draft.js';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import useCloudDraft from '@/hooks/business/use-cloud-draft';

    import {
        calcCountChange, formatStock, getCheckRange, isEmptyCount, missionStatus,
    } from './common';

    import {
        complexCount, goodsTypeName, isChineseMedicine,
    } from 'src/filters/goods.js';
    import totalInfo from '../mixins/total-info.js';
    import Clone from '../../../utils/clone';
    import {
        inventoryFormatMoney, unitEqual,
    } from 'views/inventory/goods-utils';

    import BatchSelect from './components/batch-select';
    import DownCheckTemplateDialog from './components/download-template-dialog';
    import CheckErrorDialog from './components/error-dialog';
    import ErrorGoodsDialog from './components/error-goods-dialog';
    import GoodsCountAutocomplete from './components/goods-count-autocomplete.vue';
    import {
        createGUID, getSafeNumber, isNotNull, isNull,
    } from '@/utils';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    const GoodsAutoCompleteCoverTitle = () => import('views/inventory/common/goods-auto-complete-cover-title.vue');
    // import GoodsAutoCompleteCoverTitle from 'views/inventory/common/goods-auto-complete-cover-title.vue';
    import { GOODS_CHECK_STATUS } from 'views/inventory/constant';
    import {
        isEqual,
    } from 'utils/lodash';
    import { GoodsTypeEnum } from '@abc/constants';

    const { orderMainNameText } = getViewDistributeConfig().Inventory;

    import {
        DataPermissionMixin,
        createDataPermissionControl,
        DATA_PERMISSION_CONTROL_SCENE,
    } from 'views/inventory/common/data-permission-control';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import { businessTypeConst } from '@/views-pharmacy/common/constants';
    import OverflowTooltip from '@/components/overflow-tooltip.vue';
    import TraceCode, {
        SceneTypeEnum, TraceableCodeTypeEnum, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import TraceCodeSelectGoodsDialog from '@/service/trace-code/dialog-trace-code-select-goods';
    import Logger from 'utils/logger';

    const DialogUndisposedGoods = () => import('views/inventory/goods-check/components/undisposed-goods.dialog.vue');
    const tableConfig = Object.freeze([
        {
            label: `${orderMainNameText}编码`,
            prop: 'shortId',
            tdClass: 'is-disabled',
            flex: 2,
            thStyle: {
                minWidth: '100px',
            },
            tdStyle: {
                minWidth: '100px',
            },
        },
        {
            label: `${orderMainNameText}名称`,
            prop: 'name',
            flex: 2,
            tdClass: 'is-disabled',
            thStyle: {
                minWidth: '120px',
            },
            tdStyle: {
                minWidth: '120px',
            },
        },
        {
            label: '类型',
            prop: 'goodsType',
            width: 70,
            tdClass: 'is-disabled',
        },
        {
            label: '二级分类',
            prop: 'grade',
            tdClass: 'is-disabled',
            flex: 1,
            thStyle: {
                minWidth: '80px',
            },
            tdStyle: {
                minWidth: '80px',
            },
        },
        {
            label: '批次',
            prop: 'batch',
            width: 110,
            tdStyle: {
                padding: '0px',
            },
            tdClass: 'is-disabled',
        },
        {
            label: '生产批号',
            prop: 'batchNo',
            width: 90,
            tdClass: 'is-disabled',
        },
        {
            label: '账面数量',
            prop: 'currentCount',
            width: 100,
            justifyContent: 'flex-end',
            tdClass: 'is-disabled',
        },

        {
            label: '实际数量',
            prop: 'trueCount',
            width: 150,
            tdStyle: {
                padding: '0px',
            },
            justifyContent: 'flex-end',
            tdClass: 'is-disabled',
            on: {
                click: (e) => {
                    console.log('click');
                    const inputs = e.target.querySelectorAll('.abc-input__inner');
                    inputs[0]?.focus();
                },
            },
        },

        {
            label: '盈亏数量',
            prop: 'fullOrLass',
            width: 100,
            justifyContent: 'flex-end',
            tdClass: 'is-disabled',
        },
        {
            label: '',
            width: 36,
            prop: 'delete',
            justifyContent: 'center',
        },
    ]);

    // const HeaderHeight = 96;
    // const MinItemHeight = 48;
    export default {
        name: 'OrderForm',
        components: {
            GoodsAutoCompleteCoverTitle,
            BatchSelect,
            DownCheckTemplateDialog,
            CheckErrorDialog,
            ErrorGoodsDialog,
            GoodsCountAutocomplete,
            DisplayNameCell,
            DialogUndisposedGoods,
            OverflowTooltip,
            DataPermissionControl: createDataPermissionControl(DATA_PERMISSION_CONTROL_SCENE.check),
            // TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
            DisplayTraceableCodeManager: () => import('views/inventory/components/traceable-code/display-traceable-code-manager.vue'),
        },
        mixins: [
            EnterEvent,
            AutoFocus,
            totalInfo,
            DeleteGoodsHandler,
            GoodsTableV3Mixins,
            Draft,
            DataPermissionMixin,
        ],

        props: {
            value: Boolean,
            isFullCheck: { // 是否是全量盘点
                type: Boolean,
                default: false,
            },
            taskId: {
                type: String,
                default: '',
            },
            draftId: {
                type: String,
                default: '',
            },
            parentTaskId: {
                type: String,
                default: '',
            },
            goodsType: {
                type: [ String, Number ],
                default: '',
            },
            goodsSubType: {
                type: [ String, Number ],
                default: '',
            },
            goodsCMSpec: {
                type: String,
                default: '',
            },
            // 多选二级分类
            customTypeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 多选一级分类
            typeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 多选二级分类名称
            customTypeNameList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 多选一级分类名称
            typeNameList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            reSubmitOrderId: {
                type: [String, Number],
                default: '',
            },
            // 单人全量盘点选择的类型
            selectedTypes: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            pharmacyNo: {
                type: [Number, String],
            },
            pharmacyName: String,
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('单据层弹窗');

            // 云草稿管理
            const {
                saveDraftButtonLoading,
                isShowCloudStatus,
                cloudStatusText,
                cloudStatusIcon,
                cloudStatusType,
                saveDraftToCloud,
                deleteCloudDraft,
                setCloudSyncStatus,
                startAutoSave,
                stopAutoSave,
                clearDraftStatus,
            } = useCloudDraft({
                draftType: 'goods-check',
                createDraftData: function(extraData = {}) {
                    // 这里需要在methods中实现createDraftData方法
                    return this.createDraftData(extraData);
                },
                onSaveSuccess: function(draft) {
                    this.$emit('refresh', true, 'add');
                },
                onSaveError: function(error) {
                    console.error('云草稿保存失败:', error);
                },
                onDeleteSuccess: function(draftId) {
                    this.showDialog = false;
                },
                onDeleteError: function(error) {
                    console.error('云草稿删除失败:', error);
                },
            });

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,
                // 云草稿相关
                saveDraftButtonLoading,
                isShowCloudStatus,
                cloudStatusText,
                cloudStatusIcon,
                cloudStatusType,
                saveDraftToCloud,
                deleteCloudDraft,
                setCloudSyncStatus,
                startAutoSave,
                stopAutoSave,
                clearDraftStatus,
            };
        },
        data() {
            return {
                DATA_PERMISSION_CONTROL_SCENE,
                orderMainNameText,
                missionStatus,
                buttonLoading: false,
                saveLoading: false,
                searchKey: '',
                currentItem: null,// 当前编辑项
                order: {
                    comment: '',
                    pharmacy: {
                        no: this.pharmacyNo,
                        name: this.pharmacyName,
                        type: 0,
                    },
                    list: [],
                },
                kindsNum: 0,
                totalCostPriceChange: 0,
                totalPriceChange: 0,
                warnVisible: false, // 药品禁止盘点的dialog
                orderList: [], // 药品所在的调拨list
                goodsInfo: null,
                showBatchSelectDialog: false,
                showUndisposedGoodsDialog: false,
                showDownTemplateDialog: false,
                showCheckDialog: false,
                showErrorDialog: false,
                curRepeatGoods: null,

                showErrorGoods: false,
                errorMessageDetail: {},
                loading: false,
                maxCount: 0, // 用于位置sortId 的递增，全量盘点支持盈亏数量的排序
                searchParams: {
                    type: this.goodsType || '',
                    subType: this.goodsSubType || '',
                    cMSpec: this.goodsCMSpec || '',
                },

                isFullCheckOrder: this.isFullCheck,
                // startIndex: 0,
                // showNumber: 30,
                tableConfig: [],
                curCustomTypeIdList: [],
                curTypeIdList: [],
                curCustomTypeNameList: [],
                curTypeNameList: [],
                checkRangeText: '', // 盘点范围

                undisposedGoodsList: [], // 未盘点商品
                exportButtonLoading: false,// 未盘点商品导出
                needToReview: false, // 是否需要审批
                showTraceableCodeModal: false,
                showTraceableCodePopover: false,
                validateCell: false,
                // 草稿单发生改变在其他设备触发的修改导致
                isDraftOrderChange: false,
                currentDraftId: this.draftId,
            };
        },

        computed: {
            ...mapGetters([
                'userInfo',
                'subClinics',
                'currentClinic',
                'clinicConfig',
                'isChainSubStore',
                'goodsConfig',
                'multiPharmacyCanUse',
                'isChainAdmin',
                'goodsAllTypes',
                'traceCodeConfig',
                'isStrictCountWithTraceCodeCollect',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isEditCheckBatch() {
                return this.viewDistributeConfig.Inventory.isEditCheckBatch;
            },
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            dialogConfig() {
                return {
                    size: 'hugely',
                    responsive: true,
                    'append-to-body': true,
                    'auto-focus': false,
                };
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsCheck;
            },
            // 控制批次必填校验及盈亏金额列展示
            isRequiredGoodsCheckBatch() {
                return this.viewDistributeConfig.Inventory.isRequiredGoodsCheckBatch;
            },
            // 药店展示，暂时和必填批次开关
            showTotalPriceChange() {
                return this.isRequiredGoodsCheckBatch;
            },
            goodsCheckOrderAddGoodsAllBatch() {
                return this.viewDistributeConfig.Inventory.goodsCheckOrderAddGoodsAllBatch;
            },
            isSupportGoodsCheckApprovalProcess() {
                return this.viewDistributeConfig.Inventory.isSupportGoodsCheckApprovalProcess;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('close');
                    this.$emit('input', v);
                },
            },
            headerConfig() {
                return {
                    list: [
                        {
                            label: `${orderMainNameText}编码`,
                            key: 'shortId',
                            style: {
                                width: '80px',
                                maxWidth: '80px',
                            },
                        },
                        {
                            label: `${orderMainNameText}名称`,
                            key: 'name',
                            flex: 1,
                            style: {
                                flex: 2,
                                width: '200px',
                            },
                            tdClass: 'is-disabled',
                        },
                        {
                            label: '类型',
                            key: 'goodsType',
                            style: {
                                width: '74px',
                                maxWidth: '74px',
                            },
                            tdClass: 'is-disabled',
                        },
                        {
                            label: '二级分类',
                            key: 'grade',
                            style: {
                                width: '72px',
                                maxWidth: '72px',
                            },
                        },
                        {
                            label: '批次',
                            key: 'batch',
                            width: 120,
                            style: {
                                width: '120px',
                            },
                            tdClass: 'is-disabled',
                        },
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            width: 90,
                            style: {
                                width: '90px',
                            },
                            tdClass: 'is-disabled',
                        },
                        {
                            label: '账面数量',
                            key: 'currentCount',
                            style: {
                                width: '80px',
                                maxWidth: '80px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                        },

                        {
                            label: '实际数量',
                            key: 'trueCount',
                            style: {
                                width: '150px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',
                            on: {
                                click: (e) => {
                                    console.log('click');
                                    const inputs = e.target.querySelectorAll('.abc-input__inner');
                                    inputs[0]?.focus();
                                },
                            },
                        },

                        {
                            label: '盈亏数量',
                            key: 'fullOrLass',
                            width: 100,
                            style: {
                                width: '100px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                            tdClass: 'is-disabled',
                        },
                        {
                            label: '盈亏药品追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                        {
                            label: '盈亏金额(进价)',
                            key: 'changeCostPrice',
                            style: {
                                width: '110px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                        },
                        {
                            label: '盈亏金额(售价)',
                            key: 'changePrice',
                            style: {
                                width: '120px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        if (item.key === 'changeCostPrice' || item.key === 'changePrice') {
                            return this.showTotalPriceChange;
                        }
                        return true;
                    }),
                };
            },
            enableLocalSearch() {
                // 没有查询未指定二级分类的药品
                return this.curCustomTypeIdList.every((id) => id > 0);
            },
            // 手动控制开启虚拟列表的条件
            enableVirtualList() {
                return this.order.list?.reduce((res, item) => {
                    const len = item.batchs?.length ?? 0;
                    res += (len + 1);
                    return res;
                }, 0) > 40;
            },
            isDialogShowing() {
                return this.showTraceableCodeModal || this.showTraceableCodePopover;
            },
            submitDataList() {
                // 不是草稿打开不需要提示
                if (!this.draftId) return [];
                return this.order.list.reduce((res, item) => {
                    const { goods } = item;

                    // 批次数据打平
                    if (item.batchs?.length) {
                        item.batchs.forEach((batch) => {
                            if (batch.showTips) {
                                batch.goods = goods;
                                res.push(batch);
                            }
                        });
                    } else {
                        if (item.showTips) {
                            res.push(item);
                        }
                    }

                    return res;
                }, []);
            },
            stockCheckChainReview() {
                if (this.isSupportGoodsCheckApprovalProcess) {
                    return this.needToReview;
                }
                return this.goodsConfig?.chainReview?.stockCheckChainReview && this.isChainSubStore;
            },
            submitBtnText() {
                if (this.isSupportGoodsCheckApprovalProcess) {
                    return '提交';
                }
                return this.stockCheckChainReview ? '提交盘点审核' : '确定';
            },
            dialogTitle() {
                let prefix = '';
                if (this.multiPharmacyCanUse && this.order?.pharmacy?.name) {
                    prefix = `${this.order.pharmacy.name}的`;
                }
                return `${prefix}盘点单`;
            },
            dialogContentStyles() {
                let sty = 'padding: 0 24px; ';
                sty += `width: ${window.screen.width <= 1024 ? 1008 : 1200}px;`;
                return sty;
            },

            currentSubClinicsArray() {
                return this.subClinics || [];
            },
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },

            goodsIdMap() {
                const tempMap = new Map();
                this.order.list.forEach((item) => {
                    tempMap.set(item.goods.id, {
                        name: item.goods.medicineCadn || item.goods.name,
                        id: item.goods.id,
                        shortId: item.goods.shortId,
                    });
                });
                return tempMap;
            },
            noGoods() {
                return !this.order || !this.order.list || !this.order.list.length;
            },
            taskInfo() {
                return this.order && this.order.taskInfo || {};
            },
            isFinishedSubTask() {
                return this.taskInfo.status === missionStatus.finished;
            },
            commentSpan() {
                let span = 3;
                if (this.multiPharmacyCanUse) {
                    span--;
                }
                if (this.isFullCheckOrder && this.checkRangeText) {
                    span--;
                }
                return span;
            },
            disableSaveDraftButton() {
                return isEqual(this.order, this._cacheOrderDraft);
            },
            // table滚动后校验或聚焦输入框的回调时间，根据是否开启虚拟列表来决定大小
            timeout() {
                return this.order.list?.length >= 60 ? 1000 : 0;
            },
        },
        async created() {
            // 通过盘点范围限制搜索范围
            this.curCustomTypeIdList = this.customTypeIdList;
            this.curTypeIdList = this.typeIdList;
            this.curTypeNameList = this.typeNameList;
            this.tableConfig = Clone(tableConfig);

            if (this.isFullCheckOrder) {
                this.tableConfig[ 8 ].sortable = true;
            }
            if (this.selectedTypes) {
                this.checkRangeText = getCheckRange(this.typeNameList?.map(this.transGoodsClassificationName), this.createCustomTypeNameList(this.curCustomTypeIdList));
            }
            if (this.draftId) {
                await this.fetchDraft('goods-check', this.draftId, (draft) => {
                    const comment = draft.comment?.reverse()?.[0]?.content ?? '';

                    return {
                        ...draft,
                        comment,
                        list: draft.list?.map((item, index) => {
                            if (item.batchs?.length === 1 && (!item.batchs[0].batchId && !item.batchs[0].stockId)) {
                                const {
                                    beforePieceCount,
                                    beforePackageCount,// 当前草稿的库存量--后续会更新
                                    draftBeforePieceCount,
                                    draftBeforePackageCount,// 上次草稿的库存量--不会更新
                                    pieceCount,
                                    packageCount,// 用户输入的库存量
                                    batchId,
                                    stockId,
                                    id,
                                } = item.batchs[0];
                                return {
                                    keyId: createGUID(),
                                    _batchId: id,
                                    _stockId: stockId || batchId,
                                    sortId: index,
                                    goods: item.goods,
                                    beforePieceCount,
                                    beforePackageCount,
                                    draftBeforePieceCount,
                                    draftBeforePackageCount,
                                    cacheBeforePieceCount: beforePieceCount,
                                    cacheBeforePackageCount: beforePackageCount,// 缓存打开时的库存量--不会更新
                                    pieceCount,
                                    packageCount,
                                    batchs: [],
                                    traceableCodeList: item.traceableCodeList || [],
                                    showTips: true,// 对初识数据标记，以显示库存量变化popover
                                };
                            }
                            return {
                                ...item,
                                keyId: createGUID(),
                                draftBeforePieceCount: item.beforePieceCount,
                                draftBeforePackageCount: item.beforePackageCount,
                                cacheBeforePieceCount: item.beforePieceCount,
                                cacheBeforePackageCount: item.beforePackageCount,// 缓存打开时的库存量--不会更新
                                sortId: index,
                                batchs: (item.batchs || []).map((batch) => {
                                    return {
                                        ...batch,
                                        keyId: createGUID(),
                                        showTips: true,
                                    };
                                }),
                                showTips: true,
                            };
                        }) || [],
                    };
                });

                this.processStockCheckScope(this.order.stockCheckScope);

                // 本地草稿更新账面数量
                if (!this._isCloudDraft) {
                    await this.updateBatchs(true, true);
                }
            }
            if (this.taskId) {
                await this.fetchData();
            }
            if (this.reSubmitOrderId) {
                await this.fetchOrder();
            }
            if (this.showTotalPriceChange) {
                this.calcTotalBatchPrice();
            }
            this.fetchApprovalByBusinessType();
            this.order = { ...this.order };

            this._cacheOrderDraft = Clone(this.order);
            this._orderClientUniqKey = createGUID();

            // 启动云草稿自动保存
            if (!this.taskId) {
                this.startAutoSave(() => {
                    this.autoSaveDraftHandler();
                }, 30000); // 30秒自动保存一次
            }
        },
        beforeDestroy() {
            // 停止云草稿自动保存
            this.stopAutoSave();
            // 清除草稿状态
            this.clearDraftStatus();
        },
        methods: {
            inventoryFormatMoney,
            isNull,
            isChineseMedicine,
            isEmptyCount,
            unitEqual,
            formatStock,
            transGoodsTypeName(goods) {
                return this.transGoodsClassificationName(goodsTypeName(goods));
            },
            // 主要是为了处理未指定类型
            createCustomTypeNameList(customTypeIdList = []) {

                return customTypeIdList.map((id) => this.getCustomTypeName(id));
            },
            getCustomTypeName(id) {
                if (id < 0) {
                    const item = this.goodsAllTypes.find((item) => Number(item.id) === Math.abs(id));
                    return `${item?.name}未指定`;
                }

                for (const typeItem of this.goodsAllTypes) {
                    for (const customTypeItem of typeItem.children) {
                        if (String(customTypeItem.id) === String(id)) return customTypeItem.name;
                    }
                }
            },
            validateVirtualListData(list = [], tableRef) {
                try {
                    let valid = true;
                    let invalidIndex = -1;

                    for (let i = 0; i < list.length; i++) {
                        const item = list[i];

                        if (item.batchs?.length) {
                            if (item.batchs.some((batch) => {
                                return (isNull(batch.packageCount) && isNull(batch.pieceCount));
                            })) {
                                valid = false;
                                invalidIndex = i;
                                break;
                            }
                        } else {
                            if (this.isRequiredGoodsCheckBatch || (isNull(item.packageCount) && isNull(item.pieceCount))) {
                                valid = false;
                                invalidIndex = i;
                                break;
                            }
                        }
                    }

                    if (!valid) {
                        tableRef?.scrollToElement({
                            index: invalidIndex,
                            top: 47,
                            time: 60,
                            behavior: 'instant',
                        });
                    }

                    return valid;
                } catch (e) {
                    console.error(e);
                    this.buttonLoading = false;
                    this.$Toast({
                        message: '盘点提交失败',
                        type: 'error',
                    });
                    Logger.reportAnalytics('goods-business', {
                        key: 'validateVirtualListData',
                        value: '盘点提交-数据校验失败',
                        error: e,
                    });
                    return false;
                }
            },
            submitCheckForm() {
                this.validateCell = false;
                this.buttonLoading = true;
                this.validateVirtualListData(this.order.list, this.$refs.tableRef);

                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.createForm.validate(async (val) => {
                        if (val) {
                            try {
                                await this.updateBatchs();

                                this.$nextTick(() => {
                                    if (this.submitDataList?.length) {
                                        this.showCheckDialog = true;
                                        this.buttonLoading = false;
                                    } else {
                                        this.submit();
                                    }
                                });
                            } catch (e) {
                                console.error(e);
                                this.buttonLoading = false;
                                this.$Toast({
                                    message: '盘点提交失败',
                                    type: 'error',
                                });
                                Logger.reportAnalytics('goods-business', {
                                    key: 'submitCheckForm',
                                    value: '盘点提交-创建盘点单失败',
                                    error: e,
                                });
                            }
                        } else {
                            this.buttonLoading = false;
                        }
                    });
                }, this.timeout);

            },
            // 更新批次库存量数据-调用时机在 1保存草稿 和 2提交 时还有 3本地草稿打开 也会调用一次。
            async updateBatchs(needLoading = false, updateDraftBeforeCount = false) {
                const goodsSet = new Set();
                // 获取不重复的goodsIds
                this.order.list.forEach((item) => {
                    goodsSet.add(item.goods.id);
                });
                const goodsIds = Array.from(goodsSet);
                if (goodsIds.length) {
                    if (needLoading) this.loading = true;
                    try {
                        const { data } = await GoodsAPI.fetchGoodIdsBatchs(goodsIds, this.clinicId, this.order.pharmacy?.no);

                        const batches = data.list || [];

                        const currentStockMap = new Map();

                        batches.forEach((item) => {

                            // list 更新最新的库存批次信息
                            const batchOptions = [];
                            const batchOptionMap = new Map();

                            item.batchs?.forEach((batch) => {
                                const newBatch = Clone(batch);
                                newBatch.checked = false;
                                batchOptions.push(newBatch);
                                batchOptionMap.set(`${newBatch.batchId || newBatch.stockId}`, newBatch);
                            });

                            currentStockMap.set(`${item.goodsId}`, {
                                ...item,
                                batchOptions,
                                batchOptionMap,
                            });
                        });

                        this.order.list = this.order.list.map((item) => {
                            const {
                                packageCount, pieceCount, batchOptionMap,
                            } = currentStockMap.get(`${item.goodsId || item.goods.id}`);

                            item.batchs?.forEach((b) => {
                                const batch = batchOptionMap.get(`${b.batchId || b.stockId}`);

                                // 有批次才判断
                                if (batch) {
                                    // 本地草稿需要更新这个
                                    if (updateDraftBeforeCount) {
                                        // 模拟后端保存的上次账面数量
                                        b.draftBeforePackageCount = b.beforePackageCount || 0;
                                        b.draftBeforePieceCount = b.beforePieceCount || 0;
                                        // 缓存打开时的库存量--不会更新
                                        b.cacheBeforePackageCount = batch.packageCount || 0;
                                        b.cacheBeforePieceCount = batch.pieceCount || 0;
                                        b.showTips = true;
                                    } else {
                                        if (b.beforePackageCount !== batch.packageCount || item.beforePieceCount !== batch.pieceCount) {
                                            b.showTips = true;// 变化了都要提示
                                        } else {
                                            // 数量没主动修改过并且和上次草稿保存的库存量比较变化了
                                            if (b.showTips && (b.draftBeforePackageCount !== batch.packageCount || b.draftBeforePieceCount !== batch.pieceCount)) {
                                                b.showTips = true;
                                            } else {
                                                b.showTips = false;
                                            }
                                        }
                                    }

                                    // 更新当前库存量
                                    b.beforePackageCount = batch.packageCount || 0;
                                    b.beforePieceCount = batch.pieceCount || 0;
                                    b.packageCostPrice = batch.packageCostPrice;
                                    b.packagePrice = batch.packagePrice || item.packagePrice;
                                    b.batchNo = batch.batchNo;
                                }
                            });

                            // 草稿数量变化都要提示
                            if (updateDraftBeforeCount) {
                                // 模拟后端保存的上次账面数量
                                item.draftBeforePackageCount = item.beforePackageCount || 0;
                                item.draftBeforePieceCount = item.beforePieceCount || 0;
                                // 缓存打开时的库存量--不会更新
                                item.cacheBeforePackageCount = packageCount || 0;
                                item.cacheBeforePieceCount = pieceCount || 0;
                                item.showTips = true;
                            } else {
                                // 提交前的查询更新-判断当前库存量是否盘点期间变化
                                if (item.beforePackageCount !== packageCount || item.beforePieceCount !== pieceCount) {
                                    item.showTips = true;// 变化了都要提示
                                } else {
                                    // 和上次草稿保存的库存量比较
                                    if (item.showTips && (item.draftBeforePackageCount !== packageCount || item.draftBeforePieceCount !== pieceCount)) {
                                        item.showTips = true;
                                    } else {
                                        item.showTips = false;
                                    }
                                }
                            }

                            // 更新最新账面数量
                            item.beforePackageCount = packageCount || 0;
                            item.beforePieceCount = pieceCount || 0;

                            return item;
                        });

                    } catch (e) {
                        console.error('error', e);
                        this.buttonLoading = false;
                        this.saveDraftButtonLoading = false;
                        Logger.reportAnalytics('goods-business', {
                            key: 'updateBatchs',
                            value: '盘点提交-更新批次数据失败',
                            error: e,
                        });
                    } finally {
                        this.loading = false;
                    }
                }
            },
            // 点击 x 关闭 提示内容有变化是否保存草稿,
            closeDialog() {
                const confirmTitle = (this.draftId || this._taskDraftId) ? '草稿信息发生变动，是否保存？' : '是否需要保存为草稿？';
                if (!isEqual(this.order, this._cacheOrderDraft) && !this.orderId) {
                    const vm = this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: confirmTitle,
                        showConfirm: false,
                        showCancel: false,
                        footerPrepend: () => {
                            return (
                                <abc-space>
                                    <abc-button
                                        onClick={() => {
                                            // 多人盘点保存草稿
                                            if (this.taskId) {
                                                this.saveCheckForm();
                                            } else {
                                                this.saveDraftHandler();
                                            }
                                            vm.close();// 手动关闭
                                        }}
                                    >
                                        保存
                                    </abc-button>
                                    <abc-button
                                        type="blank"
                                        onClick={() => {
                                            this.closeDraftHandler();
                                            this.showDialog = false;
                                            vm.close();// 手动关闭
                                        }}
                                    >
                                        不保存
                                    </abc-button>
                                </abc-space>
                            );
                        },
                    });
                } else {
                    this.showDialog = false;
                }
            },
            commentChange(v) {
                this.order.comment = v;
                this.autoSaveDraftAsync();
            },
            async initCollectCodeCountList(list = [], goods) {
                const resList = await TraceCode.getMaxTraceCountList({
                    scene: TraceCodeScenesEnum.INVENTORY,
                    dataList: list,
                    getGoodsInfo: (item) => goods ?? item.goods,
                    getUnitInfo: (item) => {
                        const res = this.getTransUnitCount(item, goods ?? item.goods);
                        return {
                            ...res,
                            traceableCodeList: (item.traceableCodeList || []).map((traceableCode) => {
                                const {
                                    count, hisPackageCount, hisPieceCount, ...rest
                                } = traceableCode;
                                return rest;
                            }),
                        };
                    },
                    createKeyId: this.createRowKey,
                });

                resList.forEach((e) => {
                    const item = list.find((i) => this.createRowKey(i) === e.keyId);
                    if (item) {
                        if (this.isStrictCountWithTraceCodeCollect && TraceCode.isSupportTraceCodeForceCheckStock()) {
                            this.$set(item, '_maxTraceCodeCount', e.traceableCodeNum);
                            this.$set(item, '_isTransformable', e.isTransformable);
                        }
                        this.$set(item, '_traceableCodeMaxNum', e.traceableCodeMaxNum);

                        if (e.list) {
                            const traceableCodeList = item.traceableCodeList ?? [];
                            e.list.forEach((codeInfo) => {
                                const findTraceableCode = traceableCodeList.find((traceableCodeItem) => traceableCodeItem.no === codeInfo.no);
                                if (findTraceableCode) {
                                    this.$set(findTraceableCode, 'hisMaxPackageCount', codeInfo.hisMaxPackageCount ?? Number.MAX_SAFE_INTEGER);
                                }
                            });
                        }
                    }
                });
            },
            initNoTraceCodeList(item, goodsInfo) {
                const goods = goodsInfo ?? item.goods;
                if (this.isEnableTraceableCode) {
                    // 对无码商品初始化追溯码
                    if (TraceCode.isSupplementNoCodeGoods(goods)) {
                        item.traceableCodeList = [];
                        const traceableCodeList = TraceCode.mergeNoTraceCodeList({
                            ...item,
                            ...this.getTransUnitCount(item, goods),
                            goods,
                        });
                        this.$set(item, 'traceableCodeList', traceableCodeList);
                    } else if (TraceCode.isNullCodeGoods(goods)) {
                        this.$set(item, 'traceableCodeList', []);
                    } else if (!TraceCode.isNoTraceCodeGoods(goods)) {
                        // 如果不是无码，需要清除已采集的无码追溯码
                        const traceableCodeList = (item.traceableCodeList || []).filter((it) => {
                            if (isNotNull(it.traceableCodeNoInfo?.type)) {
                                return it.traceableCodeNoInfo.type !== TraceableCodeTypeEnum.NO_CODE;
                            }
                            if (isNotNull(it.type)) {
                                return it.type !== TraceableCodeTypeEnum.NO_CODE;
                            }
                            return true;
                        });
                        this.$set(item, 'traceableCodeList', traceableCodeList);
                    }
                }
            },
            async handleSelectCount(val, item, goods, key, parent) {
                item.showTips = false;
                item[key] = val;

                if (this.isEnableTraceableCode) {
                    // 如果盈亏数量为 0, 则清空已采集的追溯码
                    const goodsCountInfo = this.getTransUnitCount(item, goods);
                    if (goodsCountInfo.unitCount === 0 && item.traceableCodeList && item.traceableCodeList.length) {
                        item.traceableCodeList = [];
                    }
                }

                if (parent && this.showTotalPriceChange) {
                    this.calcChangeCostPrice(item, parent.goods);
                    this.calcTotalBatchPrice();
                }
            },
            async handleChangeCount(val, item, goods, key) {
                item[key] = val;
                // 开启强校验时才实时计算
                if (this.isEnableTraceableCode) {
                    // 如果盈亏数量为 0, 则清空已采集的追溯码
                    const goodsCountInfo = this.getTransUnitCount(item, goods);
                    if (goodsCountInfo.unitCount === 0 && item.traceableCodeList && item.traceableCodeList.length) {
                        item.traceableCodeList = [];
                    } else {
                        item.goods = goods;
                        await this.initCollectCodeCountList([item]);

                        this.initNoTraceCodeList(item);
                    }
                }

                if (this.showTotalPriceChange) {
                    this.calcChangeCostPrice(item, goods);
                    this.calcTotalBatchPrice();
                }
                this.autoSaveDraftAsync();
            },
            autoSaveDraftAsync() {
                clearTimeout(this._timer);

                this._timer = setTimeout(() => {
                    this.autoSaveDraftHandler();
                }, 1000);
            },
            // 云草稿更新
            autoSaveDraftHandler() {
                // 多人盘点不走自动保存逻辑
                if (this.taskId) return;

                // 设置保存中状态
                this.setCloudSyncStatus('saving', '自动保存中...');

                // 使用云草稿hooks进行自动保存
                this.saveDraftToCloud({
                    list: this.transOrderList(),
                }).then(() => {
                    // 保存成功后的状态已在hooks中处理
                }).catch((error) => {
                    console.error('自动保存失败:', error);
                    this.setCloudSyncStatus('error', '保存失败，点此重试');
                });
            },
            /**
             * @desc 草稿数据创建
             * <AUTHOR>
             * @date 2024/9/24 上午11:15
             * @param {Object} obj 额外参数
             */
            createDraftData(obj = {}) {
                const draft = {
                    keyId: createGUID(),
                    kindCount: this.kindCount,
                    statusName: '草稿',
                    status: GOODS_CHECK_STATUS.DRAFT,
                    lastModifiedDate: new Date(),
                    lastModified: new Date(),
                    created: new Date(),
                    order: Clone(this.order),
                    pharmacy: this.order.pharmacy,
                    pharmacyNo: this.order.pharmacy.no,
                    comment: this.order.comment,
                    createdUser: {
                        id: this.userInfo.id,
                        name: this.userInfo.name,
                    },
                    stockCheckScope: this.isFullCheckOrder ? {
                        customTypeIdList: this.curCustomTypeIdList,
                        typeIdList: this.curTypeIdList,
                        typeNameList: this.curTypeNameList,
                        customTypeNameList: this.curCustomTypeNameList,
                    } : null,
                    orderClientUniqKey: this._orderClientUniqKey,
                    ...obj,
                };

                return draft;
            },
            async saveDraftHandler(isForceSave = false) {
                // 使用云草稿hooks保存
                await this.saveDraftToCloud({
                    list: this.transOrderList(),
                }, isForceSave);
            },
            async deleteDraftHandler() {
                // 使用云草稿hooks删除
                await this.deleteCloudDraft(this.draftId);
            },
            createRowKey(row) {
                if (row.batchId && !row?.batchs?.length) return `${row.id || row.keyId}_${row.batchId}`;

                return row.keyId || row.id || row.goods?.id;
            },
            createTrClassName(row) {
                // if (row.batchId && !row.batchs?.length) {
                //     return '';
                // }
                // return 'focus-row';
                return `abc-table-tr--${this.createRowKey(row)}`;
            },
            getItemHeight(item) {
                return (item.batchs?.length ?? 0) * 45 + 45;
            },
            async fetchApprovalByBusinessType() {
                try {
                    const data = await GoodsAPI.fetchApprovalByBusinessType(businessTypeConst.goodsStockCheck);
                    if (this.isChainAdmin) {
                        // 总部看自己
                        this.needToReview = !!data?.chainStatus;
                    } else if (this.isSingleStore) {
                        // 单店看门店
                        this.needToReview = !!data?.clinicStatus;
                    } else {
                        // 连锁子店门店总部都要看
                        this.needToReview = !!data?.clinicStatus || !!data?.chainStatus;
                    }
                    console.log('data=', data);
                } catch (e) {
                    console.log(e);
                }
            },
            async fetchOrder() {
                try {
                    this.loading = true;
                    const { data } = await StockCheckAPI.fetchOrderHasBatches(this.reSubmitOrderId);
                    const cacheList = data.list.map((item) => {
                        item.keyId = createGUID();

                        ///默认批次
                        if (item.batchs && item.batchs.length && !item.batchs[0].batchId) {
                            return {
                                ...item,
                                batchs: [],
                                pieceCount: item.batchs[0].pieceCount,
                                packageCount: item.batchs[0].packageCount,
                                beforePieceCount: item.batchs[0].beforePieceCount,
                                beforePackageCount: item.batchs[0].beforePackageCount,
                            };
                        }
                        return item;
                    });

                    // 初始化追溯码
                    await this.initSummaryTraceCode(cacheList);

                    this.order.list = cacheList;
                    // 带回盘点范围
                    this.order.stockCheckScope = data.stockCheckScope;
                    this.processStockCheckScope(data.stockCheckScope);
                    if (data.checkPharmacy) {
                        this.order.pharmacy = data.checkPharmacy;
                    }
                    if (data?.logs?.length) {
                        this.order.comment = data.logs[data.logs.length - 1]?.comment ?? '';
                    }
                    this.loading = false;
                } catch (e) {
                    if (e.code === 12236) {
                        this.showErrorGoods = true;
                        this.errorGoodsMessage = e;
                    }
                    this.loading = false;
                    console.warn('获取盘点单详情报错', e);
                }
            },
            async fetchGoodsBatchs(goodsId) {
                try {
                    const params = {
                        all: 1,
                    };
                    if (this.multiPharmacyCanUse) {
                        params.pharmacyNo = this.pharmacyNo;
                    }
                    const { data } = await GoodsAPI.fetchGoodsBatch(goodsId, params);
                    return data?.rows ?? [];
                } catch (e) {
                    return [];
                }
            },
            processStockCheckScope(stockCheckScope) {
                if (stockCheckScope) {
                    this.isFullCheckOrder = true;
                    this.$set(this.tableConfig[ 8 ], 'sortable', true);
                    this.searchParams.type = this.order.stockCheckScope.type;
                    this.searchParams.subType = this.order.stockCheckScope.subType;
                    this.searchParams.cMSpec = this.order.stockCheckScope.cMSpec;
                    this.curCustomTypeIdList = this.order.stockCheckScope.customTypeIdList || [];
                    this.curTypeIdList = this.order.stockCheckScope.typeIdList || [];
                    this.curCustomTypeNameList = this.createCustomTypeNameList(this.curCustomTypeIdList);
                    this.curTypeNameList = this.order.stockCheckScope.typeNameList || [];

                    this.checkRangeText = getCheckRange(this.curTypeNameList.map(this.transGoodsClassificationName), this.curCustomTypeNameList);
                }
            },
            // 搜索无数据，自定义错误处理
            customErrorHandler(goodsInfo) {
                let {
                    // eslint-disable-next-line prefer-const
                    typeId, customTypeId, noStocks,
                } = goodsInfo;
                // 全量盘点前端判断是否在盘点范围内
                if (this.isFullCheckOrder) {
                    let isExceed = false;

                    // customTypeId不存在或者为"0"，为空，代表不指定二级分类，值为typeId取负数
                    if (isNull(customTypeId) || customTypeId === '0') {
                        customTypeId = String(-typeId);
                    }

                    // eslint-disable-next-line eqeqeq
                    if (this.curTypeIdList.find((id) => id == typeId) || this.curCustomTypeIdList.find((id) => id == customTypeId)) {
                        isExceed = false;
                    } else {
                        isExceed = true;
                    }

                    if (isExceed) {
                        this.$alert({
                            type: 'warn',
                            title: '超出盘点范围',
                            content: '此商品不在本次盘点范围内',
                            onClose: () => {
                                this.searchKey = '';
                                this.$refs.goodsAutoCompleteRef?.handleClear();
                            },
                        });
                        return;
                    }
                }

                // 没入过库
                if (noStocks) {
                    this.$alert({
                        type: 'warn',
                        title: '商品未入库',
                        content: '此商品未入过库，无法进行盘点',
                        onClose: () => {
                            this.searchKey = '';
                            this.$refs.goodsAutoCompleteRef?.handleClear();
                        },
                    });
                }
            },
            traceableCodeEnter(keywordTraceableCodeNoInfo) {
                console.log('traceableCodeEnter', keywordTraceableCodeNoInfo);

                this.showTraceableCodeModal = true;
                new TraceCodeSelectGoodsDialog({
                    value: true,
                    title: '请选择追溯码关联的商品',
                    desc: '追溯码关联的商品',
                    placeholder: '搜索该追溯码关联的商品',
                    keywordTraceableCodeNoInfo,
                    onConfirm: this.handleConfirmBindGoods,
                    onClose: () => {
                        this.showTraceableCodeModal = false;
                    },
                }).generateDialogAsync({
                    parent: this,
                });
            },
            // 药品标识码绑定goods成功后的回调
            handleConfirmBindGoods(goods, keywordTraceableCodeNoInfo) {
                console.log('handleConfirmBindGoods', goods, keywordTraceableCodeNoInfo);
                this.selectGoods(goods, {
                    keywordTraceableCodeNoInfo,
                });
            },
            async selectGoods(goods, res) {
                this.warnVisible = false;
                this.goodsInfo = null;

                if (goods.noStocks) {
                    this.$alert({
                        type: 'warn',
                        title: `${goods.medicineCadn} 没有入库`,
                        content: [ '需先将药品入库后才能执行盘点' ],
                    });
                } else {
                    this.orderList = await this.hasTrans(goods.id);
                    if (this.orderList.length) {
                        this.goodsInfo = goods;
                        this.warnVisible = true;
                        return;
                    }
                    // const repeatGoods = this.isRepeatGoods(goods);
                    const repeatGoodsIndex = this.order.list.findIndex((item) => {
                        return item.goods.id === goods.id;
                    });

                    // 扫追溯码走下面逻辑
                    if (this.isEnableTraceableCode && res?.keywordTraceableCodeNoInfo) {
                        // const item = TraceCode.findCanAddTraceCodeItem({
                        //     traceableCodeNoInfo: res.keywordTraceableCodeNoInfo.traceableCodeNoInfo,
                        //     dataList: this.order.list,
                        //     goodsInfo: goods,
                        //     getUnitInfo(item) {
                        //         return {
                        //             unitCount: item.useCount,
                        //             unit: item.useUnit,
                        //         };
                        //     },
                        // });
                        // 有匹配的药品，追加追溯码
                        // if (item) {
                        // item.traceableCodeList = item.traceableCodeList || [];
                        // if (item.traceableCodeList.find((e) => e.no === res.keywordTraceableCodeNoInfo.no)) {
                        //     this.$Toast({
                        //         message: '采集失败：该追溯码已采集，无法重复采集',
                        //         type: 'error',
                        //     });
                        //     return;
                        // }
                        // 盘点不自动采集追溯码
                        // item.traceableCodeList.push({
                        //     ...res.keywordTraceableCodeNoInfo,
                        //     traceableCodeNoInfo: res.keywordTraceableCodeNoInfo,
                        // });
                        // }

                        // 扫码重复药品不自动添加了
                        if (repeatGoodsIndex > -1) {
                            this.focusInput(repeatGoodsIndex);
                            return;
                        }

                    }

                    if (repeatGoodsIndex > -1 && !this.isFullCheckOrder) {
                        this.sortOrderDesc(goods.id);
                    }

                    if (repeatGoodsIndex === -1) {
                        const { data: goodsObj } = await GoodsAPI.goods(goods.id, this.clinicId,{
                            pharmacyNo: this.order?.pharmacy?.no,
                        });
                        let batchs = [];

                        this.maxCount++;

                        // 临时盘点和全量盘点都带出全部批次数据
                        if (this.goodsCheckOrderAddGoodsAllBatch) {
                            batchs = await this.fetchGoodsBatchs(goods.id);
                            batchs.forEach((batch) => {
                                batch.beforePieceCount = batch.pieceCount;
                                batch.beforePackageCount = batch.packageCount;
                                batch.pieceCount = undefined;
                                batch.packageCount = undefined;
                            });
                            batchs = batchs.filter((item) => {
                                return item.beforePieceCount || item.beforePackageCount;
                            });
                            if (res?.traceableCodeList.length) {
                                res.traceableCodeList.forEach((traceableCode) => {
                                    const batch = batchs.find((item) => item.batchId === traceableCode.batchId);
                                    if (batch) {
                                        batch.checked = true;
                                        batchs = [batch];
                                    }
                                });
                            }
                            if (!batchs.some((item) => item.checked)) {
                                batchs.forEach((batch) => {
                                    batch.checked = true;
                                });
                            }
                            if (!batchs.some((item) => item.checked)) {
                                this.$Toast({
                                    message: '请选择批次',
                                    type: 'error',
                                });
                            }
                        }

                        const tempGoods = {
                            keyId: createGUID(),
                            sortId: this.maxCount,
                            goodsId: goodsObj.id,
                            goods: goodsObj,
                            pieceCount: '',
                            packageCount: '',
                            beforePieceCount: goodsObj.pieceCount,
                            beforePackageCount: goodsObj.packageCount,
                            batchs,
                        };

                        // if (this.isEnableTraceableCode) {
                        //     tempGoods.traceableCodeList = res?.keywordTraceableCodeNoInfo ? [{
                        //         ...res.keywordTraceableCodeNoInfo,
                        //     }] : [];
                        //
                        //     if (batchs.length) {
                        //         batchs[0].traceableCodeList = res?.keywordTraceableCodeNoInfo ? [{
                        //             ...res.keywordTraceableCodeNoInfo,
                        //             traceableCodeNoInfo: res.keywordTraceableCodeNoInfo,
                        //         }] : [];
                        //     }
                        //
                        // }

                        this.order.list.push(tempGoods);
                        this.autoSaveDraftAsync();
                    }
                    this.$nextTick(() => {
                        if (repeatGoodsIndex > -1 && this.isFullCheckOrder) {
                            this.focusInput(repeatGoodsIndex);
                        } else {
                            this.focusInput(this.order.list.length - 1, true);
                        }
                    });
                }
                this.searchKey = '';
                this.$refs.goodsAutoCompleteRef?.handleClear();
            },
            // 滚动table,聚焦输入框
            focusInput(index, isScroll = true) {
                if (isScroll) {
                    this.$refs.tableRef?.scrollToElement?.({
                        index,
                        top: 47,
                        time: 60,
                        behavior: 'instant',
                    });
                }
                if (this.focusTimer) {
                    clearTimeout(this.focusTimer);
                    this.focusTimer = null;
                }
                this.focusTimer = setTimeout(() => {
                    const row = this.order.list[index];
                    let keyId = '';

                    if (row.batchs?.length) {
                        keyId = this.createRowKey(row.batchs[0]);
                    } else {
                        keyId = this.createRowKey(row);
                    }

                    const $tableTr = this.$refs.tableRef.$el.querySelector(`.abc-table-tr--${keyId}`);
                    $tableTr && $tableTr.querySelector('.count-input input')?.focus?.();
                    // this.autoFocus((isLast ? '' : index + 1), false, '.count-input input', '.abc-table-tr.focus-row');
                }, this.timeout);
            },
            /**
             * @desc  判断药品是否被锁库
             * <AUTHOR>
             * @date 2019/06/19 11:55:17
             * @params
             * @return
             */
            async hasTrans(goodsId) {
                try {
                    const { data } = await StockCheckAPI.hasTrans(goodsId);
                    return data?.rows ?? [];
                } catch (e) {
                    const { message } = e;
                    message && this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: message,
                    });
                    return [];
                }
            },

            /**
             * @desc 根据用户输入的实际数量，计算盈亏
             * <AUTHOR>
             * @date 2018/11/24 13:36:25
             */
            changeCount(pieceCount, packageCount, goods, beforePieceCount, beforePackageCount) {
                if (pieceCount === '' && packageCount === '') return '';
                if (!goods) return '无法计算盈亏';

                const pieceNum = +goods.pieceNum || 1;
                const goodsPieceCount = +beforePieceCount || 0;
                const goodsPackageCount = +beforePackageCount || 0;

                pieceCount = +pieceCount || 0;
                packageCount = +packageCount || 0;
                const inputCount = Big(pieceCount).plus(Big(packageCount).times(pieceNum));
                const beforeCount = Big(goodsPieceCount).plus(Big(goodsPackageCount).times(pieceNum));

                return Big(inputCount).minus(beforeCount).toNumber();
            },

            changeCountTheme(item, goods) {
                const tempGoods = item.goods || goods;
                // 产品要求没填数量时不展示盈亏变化量，之前是当成0在计算
                if (isNull(item.pieceCount) && isNull(item.packageCount)) return 'black';

                const change = this.changeCount(item.pieceCount, item.packageCount, tempGoods, item.beforePieceCount, item.beforePackageCount);
                if (change > 0) {
                    return 'success';
                }
                if (change < 0) {
                    return 'danger';
                }
                return 'black';
            },
            calcCountTheme() {
                // 暂时不需要颜色，参数val
                // if (val > 0) {
                //     return 'success';
                // }
                // if (val < 0) {
                //     return 'danger';
                // }
                return 'black';
            },
            calcTotalBatchPrice() {
                try {
                    const obj = this.order.list.reduce((res, item) => {
                        const {
                            batchs = [],
                        } = item;

                        let rowCostPriceChange = 0;
                        let rowPriceChange = 0;

                        // 计算行盈亏金额
                        batchs.forEach((batch) => {
                            rowCostPriceChange = Big(getSafeNumber(rowCostPriceChange)).plus(getSafeNumber(batch.totalCostPriceChange)).toNumber();
                            rowPriceChange = Big(getSafeNumber(rowPriceChange)).plus(getSafeNumber(batch.totalPriceChange)).toNumber();
                        });
                        // 更新到行数据-后续可能会展示
                        this.$set(item, 'totalCostPriceChange', rowCostPriceChange);
                        this.$set(item, 'totalPriceChange', rowPriceChange);

                        // 计算总盈亏金额
                        res.totalCostPriceChange = Big(res.totalCostPriceChange).plus(rowCostPriceChange).toNumber();
                        res.totalPriceChange = Big(res.totalPriceChange).plus(rowPriceChange).toNumber();

                        return res;
                    }, {
                        totalCostPriceChange: 0,
                        totalPriceChange: 0,
                    });

                    this.totalCostPriceChange = obj.totalCostPriceChange;
                    this.totalPriceChange = obj.totalPriceChange;
                } catch (e) {
                    console.error(e);
                    this.totalCostPriceChange = 0;
                    this.totalPriceChange = 0;
                }
            },
            /**
             * @desc 根据用计算盈亏结果 判断 + - 符号
             * <AUTHOR>
             * @date 2018/11/24 13:36:25
             */
            changeCountClass(item, goods) {
                const tempGoods = item.goods || goods;
                // 产品要求没填数量时不展示盈亏变化量，之前是当成0在计算
                if (isNull(item.pieceCount) && isNull(item.packageCount)) return '';

                const change = this.changeCount(item.pieceCount, item.packageCount, tempGoods, item.beforePieceCount, item.beforePackageCount);
                if (change > 0) {
                    return 'green';
                }
                if (change < 0) {
                    return 'red';
                }
                return '';
            },
            draftChangeCountClass(item, goods) {
                if (!this.draftId) return '';
                if (!item.showTips) return '';
                const tempGoods = item.goods || goods;
                const change = this.changeCount(item.beforePieceCount, item.beforePackageCount, tempGoods, item.draftBeforePieceCount, item.draftBeforePackageCount);
                if (change) {
                    return 'warning-light';
                }
                return '';
            },
            /**
             * @desc 根据用计算盈亏结果 展示文案+样式
             * <AUTHOR>
             * @date 2018/11/24 13:45:23
             */
            changeCountText(item, goods, changeCount) {
                const tempGoods = item.goods || goods;
                if (isNull(changeCount)) {
                    // 产品要求没填数量时不展示盈亏变化量，之前是当成0在计算
                    if (isNull(item.pieceCount) && isNull(item.packageCount)) return '-';
                }

                let change = changeCount || this.changeCount(item.pieceCount, item.packageCount, tempGoods, item.beforePieceCount, item.beforePackageCount);
                // console.count('~~~~~change');
                if (isNull(change)) return '-';

                let sign = '';
                if (change > 0) {
                    sign = '+';
                } else if (change < 0) {
                    sign = '-';
                }
                change = Math.abs(change);

                let pieceNum, pieceCount, packageCount;
                // 中药的数量都是保存在pieceCount中
                if (isChineseMedicine(tempGoods)) {
                    // 大单位数据清 0 防止重复拼接（如果packageCount有值，pieceCount无值的中药）
                    packageCount = 0;
                    pieceCount = change;
                } else {
                    pieceNum = +tempGoods.pieceNum || 1;
                    pieceCount = Big(change).mod(pieceNum);
                    packageCount = Math.floor(change / pieceNum);
                }
                return sign + complexCount({
                    pieceCount, packageCount, goods: tempGoods,
                });
            },
            draftChangeCount(row, parent, onlyChangeCount = false) {
                const goods = row.goods || parent.goods;
                // 变动账面数量展示
                if (onlyChangeCount) {
                    const count = this.changeCount(
                        row.beforePieceCount,
                        row.beforePackageCount,
                        goods,
                        row.cacheBeforePieceCount,
                        row.cacheBeforePackageCount,
                    );
                    return `${this.changeCountText(row, goods, count)}`;
                }

                const changeCount = this.changeCount(
                    row.beforePieceCount,
                    row.beforePackageCount,
                    goods,
                    row.draftBeforePieceCount,
                    row.draftBeforePackageCount,
                );

                return `较上次草稿保存数量变化：${
                    this.formatStock(goods, row.draftBeforePackageCount, row.draftBeforePieceCount)
                }→${
                    this.formatStock(goods, row.beforePackageCount, row.beforePieceCount)
                }（${
                    this.changeCountText(row, goods, changeCount)
                }）`;
            },
            getConfig(goods) {
                if (this.isChineseMedicine(goods) || [GoodsTypeEnum.GOODS, GoodsTypeEnum.MATERIAL].includes(goods.type)) {
                    return {
                        size: 'medium',
                        config: {
                            formatLength: 2,
                            max: 10000000,
                            supportZero: true,
                        },
                    };
                }
                return {
                    size: 'medium',
                    config: {
                        formatLength: 0,
                        max: 10000000,
                        supportZero: true,
                    },
                };
            },
            calcGoodsCompleteCount(row, parent) {
                const goods = row.goods || parent.goods;
                // 非草稿或者草稿没有变化时返回
                if (!this.draftId || !this.draftChangeCountClass(row, goods)) {
                    return {
                        packageCount: row.beforePackageCount,
                        pieceCount: row.beforePieceCount,
                    };
                }

                try {
                    const _isChineseMedicine = this.isChineseMedicine(goods);
                    const changeCount = Number(this.changeCount(
                        row.beforePieceCount,
                        row.beforePackageCount,
                        goods,
                        row.draftBeforePieceCount,
                        row.draftBeforePackageCount,
                    ));
                    const originCount = Number(this.changeCount(
                        row.pieceCount,
                        row.packageCount,
                        goods,
                        0,
                        0,
                    ));
                    const count = (isNaN(originCount) ? 0 : originCount) + (isNaN(changeCount) ? 0 : changeCount);

                    const packageCount = _isChineseMedicine ? 0 : Math.floor(count / goods.pieceNum);
                    const pieceCount = _isChineseMedicine ? count : count % goods.pieceNum;

                    return {
                        packageCount,
                        pieceCount,
                    };
                } catch (e) {
                    console.error('calcGoodsCompleteCount', e);
                    return {
                        packageCount: row.beforePackageCount,
                        pieceCount: row.beforePieceCount,
                    };
                }
            },
            /**
             * @desc 需要考虑批次的盈亏数量
             */
            calcTotalCountChange(item) {
                if (item.batchs && item.batchs.length) {
                    let totalCount = 0;
                    item.batchs.forEach((batch) => {
                        const { goods } = item;
                        let curPieceCount = 0;
                        let curPackageCount = 0;
                        let sign = '';
                        let change = this.changeCount(
                            batch.pieceCount,
                            batch.packageCount,
                            goods,
                            batch.beforePieceCount,
                            batch.beforePackageCount,
                        );
                        if (change > 0) {
                            sign = '+';
                        } else if (change < 0) {
                            sign = '-';
                        }
                        change = Math.abs(change);
                        if (isChineseMedicine(goods)) {
                            curPackageCount = 0;
                            curPieceCount = change;
                        } else {
                            const { pieceNum } = goods;
                            curPieceCount = change % pieceNum;
                            curPackageCount = Math.floor(change / pieceNum);
                        }
                        const curCount = curPackageCount + curPieceCount;
                        totalCount += Number(sign + curCount) || 0;
                    });
                    return totalCount;
                }
                return calcCountChange(item);

            },
            // 盈亏金额（进价、售价）
            calcChangeCostPrice(row, goods) {
                const change = this.changeCount(
                    row.pieceCount,
                    row.packageCount,
                    goods,
                    row.beforePieceCount,
                    row.beforePackageCount,
                );
                if (typeof change === 'number') {
                    // 当前批次价格
                    const costPrice = Big(getSafeNumber(row.packageCostPrice)).div(goods.pieceNum || 1).times(change).toNumber();
                    // TODO: 当前药品售价-药店暂时不支持进价加成先不考虑
                    const packagePrice = row.packagePrice || goods.packagePrice;
                    const price = Big(getSafeNumber(packagePrice)).div(goods.pieceNum || 1).times(change).toNumber();

                    // console.log('calcChangeCostPrice',costPrice, price);
                    this.$set(row, 'totalCostPriceChange', costPrice);
                    this.$set(row, 'totalPriceChange', price);
                } else {
                    this.$set(row, 'totalCostPriceChange', 0);
                    this.$set(row, 'totalPriceChange', 0);
                }
            },
            /**
             * @desc 根据盈亏数量进行排序
             */
            sortCountChange({
                orderType, orderBy,
            }) {
                if (orderBy === 'fullOrLass') {
                    let tempList = Clone(this.order.list);
                    if (!orderType) {
                        tempList.sort((a, b) => {
                            return a.sortId - b.sortId;
                        });
                    } else {
                        const emptyCountList = this.filterEmptyCount(this.order.list);
                        const notEmptyCountList = this.filterNotEmptyCount(this.order.list);

                        if (orderType === 'asc') {
                            // 升序排序  没有填写的数据放在最前面
                            notEmptyCountList.sort((a, b) => {
                                return this.calcTotalCountChange(a) - this.calcTotalCountChange(b);
                            });
                        } else if (orderType === 'desc') {
                            // 降序排序 没有填写的数据放在最前面
                            notEmptyCountList.sort((a, b) => {
                                return this.calcTotalCountChange(b) - this.calcTotalCountChange(a);
                            });
                        }
                        tempList = emptyCountList.concat(notEmptyCountList);
                    }
                    this.order.list = Clone(tempList);
                }
            },
            // 过滤出没有填写的数量的商品
            filterEmptyCount() {
                const tempList = this.order.list.filter((item) => {
                    if (item.batchs && item.batchs.length) {
                        const emptyIndex = item.batchs.findIndex((batch) => {
                            return this.isEmptyCount(batch, item.goods);
                        });
                        return emptyIndex > -1;
                    }
                    return this.isEmptyCount(item);

                });
                return tempList;
            },
            // 过滤出已经填写的数量的商品
            filterNotEmptyCount() {
                const tempList = this.order.list.filter((item) => {
                    if (item.batchs && item.batchs.length) {
                        const emptyIndex = item.batchs.findIndex((batch) => {
                            return this.isEmptyCount(batch, item.goods);
                        });
                        return emptyIndex === -1;
                    }
                    return !this.isEmptyCount(item);

                });
                return tempList;
            },

            // findEmptyCountIndex() {
            //     const index = this.order.list.findIndex((item) => {
            //         if (item.batchs && item.batchs.length) {
            //             const emptyIndex = item.batchs.findIndex((batch) => {
            //                 return this.isEmptyCount(batch, item.goods);
            //             });
            //             return emptyIndex > -1;
            //         }
            //         return this.isEmptyCount(item);
            //
            //     });
            //     return index;
            // },
            /**
             * @desc 变动确认弹窗确认按钮事件
             * <AUTHOR>
             * @date 2024/7/8 下午2:19
             */
            handleConfirm() {
                this.$refs.confirmForm.validate((val) => {
                    if (val) {
                        this.submit();
                    }
                });
            },
            /**
             * @desc 提交 验证
             * <AUTHOR>
             * @date 2018/11/24 14:03:15
             * @params
             * @return
             */
            async submit(submitFlag) {
                this.buttonLoading = true;
                this.validateCell = false;
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.createForm.validate(async (val) => {
                        if (val) {
                            if (this.isEnableTraceableCode) {
                                try {
                                    let validateTraceCodeMaxCountNum = 0;
                                    this.order.list.forEach((item) => {
                                        if (item.batchs && item.batchs.length) {
                                            item.batchs.forEach((batch) => {
                                                const isValidate = TraceCode.validateTraceCodeMaxCount({
                                                    traceableCodeList: batch.traceableCodeList,
                                                    traceableCodeMaxNum: batch._traceableCodeMaxNum,
                                                });
                                                if (!isValidate) validateTraceCodeMaxCountNum++;
                                            });
                                        } else {
                                            const isValidate = TraceCode.validateTraceCodeMaxCount({
                                                traceableCodeList: item.traceableCodeList,
                                                traceableCodeMaxNum: item._traceableCodeMaxNum,
                                            });
                                            if (!isValidate) validateTraceCodeMaxCountNum++;
                                        }
                                    });
                                    if (validateTraceCodeMaxCountNum) {
                                        this.$confirm({
                                            type: 'warn',
                                            title: '追溯码采集提示',
                                            content: `${validateTraceCodeMaxCountNum} 个药品追溯码采集条数不符合医保要求`,
                                            confirmText: '去修改',
                                            showCancel: false,
                                        });
                                        this.showCheckDialog = false;
                                        this.buttonLoading = false;
                                        return;
                                    }

                                    const {
                                        flag, errorList, firstErrorIndex,
                                    } = await TraceCode.validate({
                                        scene: TraceCodeScenesEnum.INVENTORY,
                                        dataList: this.order.list,
                                        getGoodsInfo: (item) => item.goods,
                                        getUnitInfo: (item) => this.getTransUnitCount(item, item.goods),
                                        createKeyId: this.createRowKey,
                                        sceneType: SceneTypeEnum.GOODS_CHECK,
                                        needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckStock(),
                                    });
                                    if (!flag) {
                                        this.$confirm({
                                            type: 'warn',
                                            title: '追溯码采集风险提醒',
                                            content: errorList.map((it) => {
                                                const {
                                                    count,
                                                    warnTips,
                                                } = it;
                                                return `有 ${count} 个商品${warnTips}`;
                                            }),
                                            confirmText: '去修改',
                                            cancelText: '仍要提交',
                                            disabledKeyboard: true,
                                            showClose: false,
                                            onConfirm: () => {
                                                this.validateCell = true;
                                                this.showCheckDialog = false;
                                                this.buttonLoading = false;

                                                this.$refs.tableRef?.scrollToElement?.({
                                                    index: firstErrorIndex,
                                                    top: 47,
                                                    time: 60,
                                                    behavior: 'instant',
                                                });

                                                // eslint-disable-next-line abc/no-timer-id
                                                setTimeout(() => {
                                                    this.$refs.createForm.validate();
                                                }, this.timeout);
                                            },
                                            onCancel: () => {
                                                this.submitConfirm(submitFlag);
                                            },
                                        });
                                        return;
                                    }

                                    /**
                                     * @吴蔚: 盘点暂时不校验已采等于应采
                                     */
                                    // const traceableCodeCountValidateName = [];
                                    // for (let i = 0; i < (this.order.list || []).length; i++) {
                                    //     const item = this.order.list[i];
                                    //     const {
                                    //         goods, traceableCodeList, batchs,
                                    //     } = item;
                                    //     if (Array.isArray(batchs) && batchs.length) {
                                    //         for (let j = 0; j < batchs.length; j++) {
                                    //             const batch = batchs[j];
                                    //             const { traceableCodeList: batchTraceableCodeList } = batch;
                                    //             const {
                                    //                 unitCount, unit,
                                    //             } = this.getTransUnitCount(batch, goods);
                                    //             const validateRes = TraceCode.validateCollectedCountInInventory({
                                    //                 goods,
                                    //                 traceableCodeList: batchTraceableCodeList,
                                    //                 unitCount,
                                    //                 unit,
                                    //             });
                                    //             if (!validateRes.validate && !traceableCodeCountValidateName.some((name) => name === (goods.displayName || goods.medicineCadn || goods.name))) {
                                    //                 traceableCodeCountValidateName.push(goods.displayName || goods.medicineCadn || goods.name);
                                    //             }
                                    //         }
                                    //     } else {
                                    //         const {
                                    //             unitCount, unit,
                                    //         } = this.getTransUnitCount(item, goods);
                                    //         const validateRes = TraceCode.validateCollectedCountInInventory({
                                    //             goods,
                                    //             traceableCodeList,
                                    //             unitCount,
                                    //             unit,
                                    //         });
                                    //         if (!validateRes.validate) {
                                    //             traceableCodeCountValidateName.push(goods.displayName || goods.medicineCadn || goods.name);
                                    //         }
                                    //     }
                                    // }
                                    // if (traceableCodeCountValidateName.length) {
                                    //     this.$confirm({
                                    //         type: 'warn',
                                    //         title: '追溯码采集风险提醒',
                                    //         content: `${traceableCodeCountValidateName.join('、')}的采集数量必须等于入库数量`,
                                    //         confirmText: '去修改',
                                    //         cancelText: '仍要提交',
                                    //         disabledKeyboard: true,
                                    //         showClose: false,
                                    //         onConfirm: () => {
                                    //             this.showCheckDialog = false;
                                    //             this.buttonLoading = false;
                                    //         },
                                    //         onCancel: () => {
                                    //             this.submitConfirm(submitFlag);
                                    //         },
                                    //     });
                                    //     return;
                                    // }
                                } catch (e) {
                                    console.error(e);
                                    this.buttonLoading = false;
                                    this.$Toast({
                                        message: '盘点提交失败',
                                        type: 'error',
                                    });
                                    Logger.reportAnalytics('goods-business', {
                                        key: 'traceCodeValidate',
                                        value: '盘点提交-校验追溯码失败',
                                        error: e,
                                    });
                                    return;
                                }
                            }

                            this.submitConfirm(submitFlag);
                        } else {
                            this.buttonLoading = false;
                        }
                    });
                }, this.timeout);
            },
            submitConfirm(submitFlag) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: this.stockCheckChainReview ?
                        '提交后将进入审批流程，审批通过后完成盘点，确认提交吗？' :
                        '确认提交盘点单吗？',
                    onConfirm: () => {
                        // console.log('submitFlag', submitFlag);
                        this.createOrder(submitFlag);
                    },
                    onCancel: () => {
                        this.buttonLoading = false;
                    },
                });
            },
            getUnitCount(item, goods) {

                // 盈亏数量
                const count = this.changeCount(item.pieceCount, item.packageCount, goods, item.beforePieceCount, item.beforePackageCount);
                let unitCount = '', unit = '', packageCount = '', pieceCount = '';
                // 拆零
                if (Math.abs(count) < goods.pieceNum) {
                    unit = goods.pieceUnit;
                    unitCount = Math.abs(count);
                    pieceCount = unitCount;
                } else {
                    unit = goods.packageUnit;
                    unitCount = Math.ceil(Math.abs(count / goods.pieceNum));
                    packageCount = Math.floor(Math.abs(count / goods.pieceNum));
                    pieceCount = Math.abs(count % goods.pieceNum);
                }

                return {
                    useExternalCount: this.isStrictCountWithTraceCodeCollect,
                    packageCount,
                    pieceCount,
                    unitCount,
                    unit,
                    label: '盈亏数量',
                    countLabel: '盈亏数量',
                    maxCount: item._maxTraceCodeCount,
                    isTrans: item._isTransformable,
                };
            },
            getTransUnitCount(item, goods) {
                // 盈亏数量
                const count = this.changeCount(item.pieceCount, item.packageCount, goods, item.beforePieceCount, item.beforePackageCount);
                let unit = goods.pieceUnit, unitCount = Math.abs(count), packageCount = '', pieceCount = '';

                // 拆零
                if (Math.abs(count) < goods.pieceNum) {
                    pieceCount = unitCount;
                } else {
                    packageCount = Math.floor(Math.abs(count / goods.pieceNum));
                    pieceCount = Math.abs(count % goods.pieceNum);

                    if (pieceCount === 0) {
                        unit = goods.packageUnit;
                        unitCount = packageCount;
                    }
                }


                return {
                    useExternalCount: this.isStrictCountWithTraceCodeCollect,
                    packageCount,
                    pieceCount,
                    unitCount,
                    unit,
                    label: '盈亏数量',
                    countLabel: '盈亏数量',
                    maxCount: item._maxTraceCodeCount,
                    isTrans: item._isTransformable,
                };
            },
            transOrderList(list = this.order.list) {
                return list.map((item) => {
                    const batchs = this.transBatches(item);
                    return {
                        batchs,
                    };
                });
            },

            /**
             * @desc 开始盘点
             * <AUTHOR>
             * @date 2018/11/23 12:37:02
             */
            async createOrder(submitFlag = 0, isForceSubmit = false, transList) {
                this.buttonLoading = true;
                const { comment } = this.order;
                const list = transList || this.transOrderList();
                const clearType = '';
                const postData = {
                    comment,
                    list,
                    clearType,
                };
                if (this.isFullCheckOrder) {
                    postData.type = this.searchParams.type;
                    postData.subType = this.searchParams.subType;
                    postData.cMSpec = this.searchParams.cMSpec;
                    postData.isCheckScope = 1; // 全量盘点开启盘点范围检查
                    postData.submitFlag = submitFlag;
                    postData.stockCheckScope = {
                        customTypeIdList: this.curCustomTypeIdList,
                        typeIdList: this.curTypeIdList,
                    };
                } else {
                    postData.isCheckScope = 0;
                }
                if (this.order.stockCheckScope) {
                    postData.type = this.searchParams.type;
                    postData.subType = this.searchParams.subType;
                    postData.cMSpec = this.searchParams.cMSpec;
                    postData.typeIdList = this.curTypeIdList;
                    postData.customTypeIdList = this.curCustomTypeIdList;
                    postData.isCheckScope = 1; // 全量盘点开启盘点范围检查
                    postData.submitFlag = submitFlag;
                }
                // 强制提交创建
                if (isForceSubmit) postData.forceSubmit = 1;
                postData.pharmacyNo = this.order?.pharmacy?.no;
                postData.orderClientUniqKey = this._orderClientUniqKey;
                if (this._isCloudDraft) {
                    // 创建时删除云草稿
                    postData.checkOrderDraftId = this.draftId;
                }
                try {
                    await StockCheckAPI.createOrder(postData);
                    // 清除异步草稿任务
                    clearTimeout(this._timer);
                    // 创建成功后清除草稿
                    this.clearDraft('goods-check', this._draftId || this.draftId);
                    this.$emit('refresh', true, 'add');
                    this.showDialog = false;
                } catch (e) {
                    console.log('创建盘点单错误',e);
                    if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goodsId !== e.detail.goodsId);
                        });
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goodsId !== e.detail.id);
                        });
                    } else if (e.code === 12238) {
                        // 存在未盘点药品
                        this.undisposedGoodsList = e.detail;
                        this.showUndisposedGoodsDialog = true;
                    } else if (e.code === 12505) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else if (e.code === 12162) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                        this.$emit('refresh', true, 'add');
                        this.showDialog = false;
                    } else if (e.code === 12814) {
                        // 当前草稿已删除、已提交，需要重新提交单据
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                            confirmText: '确认',
                            onConfirm: () => this.createOrder(submitFlag, true),
                        });
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                    }
                } finally {
                    this.buttonLoading = false;
                }
            },
            async handleCreate(list = [], stopLoading) {
                await this.createOrder(0, true, this.transOrderList([...this.order.list, ...list]));
                if (typeof stopLoading === 'function')stopLoading();
            },
            /**
             * @desc 转换批次信息   多个批次 存在batchs 数组中上传
             *                      默认批次 batchId 为null
             * <AUTHOR>
             * @date 2019/11/14 17:54:03
             * @params
             * @return
             */
            transBatches(goodsItem) {
                let res = [];
                const batches = goodsItem.batchs || [];
                const { goods } = goodsItem;
                if (batches && batches.length) {
                    res = batches.map((item) => {
                        return {
                            goodsId: goods.id,
                            pieceNum: goods.pieceNum,
                            batchId: item.batchId,
                            packageCount: item.packageCount,
                            pieceCount: item.pieceCount,
                            beforePackageCount: item.beforePackageCount,
                            beforePieceCount: item.beforePieceCount,
                            traceableCodeList: this.isEnableTraceableCode ? TraceCode.transCodeList(item.traceableCodeList) : [],
                        };
                    }).filter((item) => item.batchId);
                } else {
                    res = [
                        {
                            goodsId: goods.id,
                            batchId: null,
                            pieceNum: goods.pieceNum,
                            packageCount: goodsItem.packageCount,
                            pieceCount: goodsItem.pieceCount,
                            beforePackageCount: goodsItem.beforePackageCount,
                            beforePieceCount: goodsItem.beforePieceCount,
                            traceableCodeList: this.isEnableTraceableCode ? TraceCode.transCodeList(goodsItem.traceableCodeList) : [],
                        },
                    ];
                }
                return res;
            },
            stopCheck() {
                if (this.order.list.length) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '终止盘点将清空本次盘点数据',
                        onConfirm: () => {
                            this.showDialog = false;
                        },
                    });
                } else {
                    this.showDialog = false;
                }
            },
            // 重复的药品靠底
            sortOrderDesc(id) {
                const newOrderList = Clone(this.order.list);
                newOrderList.sort((a, b) => {
                    return (a.goods.id === id) - (b.goods.id === id);
                });
                this.order.list = Clone(newOrderList);
            },
            setCurrentItem(row) {
                this.currentItem = row;
            },
            onCancel() {
                this.setCurrentItem(null);
            },
            // 删除数据
            deleteBatch(parent, row) {
                const arr = parent.batchs;
                const idx = arr.findIndex((e) => this.createRowKey(e) === this.createRowKey(row));
                if (idx !== -1) {
                    arr?.splice(idx, 1);
                }
                this.onCancel();
            },
            deleteTr(rowIndex, childIndex) {
                if (isNotNull(rowIndex) && isNotNull(childIndex)) {
                    const arr = this.order.list[rowIndex]?.batchs ?? [];
                    arr.splice(childIndex, 1);
                } else {
                    this.order.list.splice(rowIndex, 1);
                }
                if (this.showTotalPriceChange) {
                    this.calcTotalBatchPrice();
                }
            },
            changeBatchs(batchs, item) {
                if (batchs && batchs.length) {
                    item.packageCount = '';
                    item.pieceCount = '';
                }
            },

            /** ********* 盘点表格相关 ***********/
            /**
             * @desc 下载盘点模板
             */
            downCheckTemplate() {
                this.showDownTemplateDialog = true;
            },

            handleImportClick() {
                this.$refs.excelInput.click();
            },

            /**
             * @desc 上传盘点数据
             */
            async changeFileHandler(e) {
                const file = e.target.files[ 0 ];
                const fileName = file.name;
                const ext = fileName.substr(fileName.lastIndexOf('.') + 1)?.toLocaleLowerCase();
                if (ext !== 'xlsx') {
                    return;
                }
                const postData = new FormData();
                postData.append('file', file);
                postData.append('typeIdList', this.typeIdList || []);
                postData.append('customTypeIdList', this.customTypeIdList || []);
                postData.append('pharmacyNo', this.order?.pharmacy?.no);

                await this.importOrder(postData);
                e.target.value = '';
            },
            async importOrder(postData) {
                try {
                    this.loading = true;
                    const { data } = await StockCheckAPI.importOrder(postData);
                    if (data.list) {
                        for (let i = 0; i < data.list.length; i++) {
                            const flag = this.isRepeatGoods(data.list[ i ].goods);
                            if (flag) {
                                this.curRepeatGoods = this.goodsIdMap.get(data.list[ i ].goods.id);
                                this.showErrorDialog = true;
                                this.loading = false;
                                return;
                            }
                        }
                        const fileData = data.list.map((item, index) => {

                            const usePackageCount = !isChineseMedicine(item.goods);
                            const usePieceCount = !unitEqual(item.goods) || isChineseMedicine(item.goods);

                            if (item.batchs && item.batchs.length === 1 && !item.batchs[ 0 ].batchId) {
                                const {
                                    beforePieceCount,
                                    beforePackageCount,
                                    pieceCount,
                                    packageCount,
                                    packagePrice,
                                    packageCostPrice,
                                } = item.batchs[ 0 ];
                                return {
                                    keyId: createGUID(),
                                    sortId: this.maxCount + index,
                                    goods: item.goods,
                                    beforePieceCount,
                                    beforePackageCount,
                                    packageCostPrice,
                                    packagePrice,
                                    pieceCount: usePieceCount ? pieceCount : '',
                                    packageCount: usePackageCount ? packageCount : '',
                                    batchs: [],
                                };
                            }
                            return {
                                keyId: createGUID(),
                                sortId: this.maxCount + index,
                                ...item,

                                beforePieceCount: item.goods.pieceCount,
                                beforePackageCount: item.goods.packageCount,

                                batchs: item.batchs.map((batch) => {
                                    return {
                                        keyId: createGUID(),
                                        ...batch,
                                        pieceCount: usePieceCount ? batch.pieceCount : '',
                                        packageCount: usePackageCount ? batch.packageCount : '',
                                    };
                                }),
                            };

                        });

                        await this.initCollectCodeCountList(fileData);
                        for (const item of fileData) {
                            if (item.batchs && item.batchs.length) {
                                await this.initCollectCodeCountList(item.batchs, item.goods);
                            }
                        }

                        this.order.list = this.order.list.concat(fileData);

                        if (this.showTotalPriceChange) {
                            this.order.list.forEach((item) => {
                                item.batchs?.forEach((batch) => {
                                    this.calcChangeCostPrice(batch, item.goods);
                                });
                            });
                            this.calcTotalBatchPrice();
                        }
                        this.order.blankCount = data.blankCount;
                        this.order.kindCount = data.kindCount;
                        this.loading = false;
                    }
                } catch (e) {
                    console.warn('上传盘点任务失败', e);
                    if (e.code === 12504 || e.code === 12503) {
                        this.errorMessageDetail = e;
                        this.showErrorGoods = true;
                    } else {
                        this.$Toast.error(e.message || '上传盘点任务失败');
                    }
                } finally {
                    this.loading = false;
                }
            },
            isRepeatGoods(goods) {
                if (!this.goodsIdMap.get(goods.id)) {
                    return false;
                }
                return true;
            },

            /** ************ 多人盘点 ******************/
            async fetchData() {
                try {
                    this.loading = true;
                    const { data } = await StockCheckAPI.fetchGoodsListWithBatchsByTaskId(this.taskId, { parentTaskId: this.parentTaskId });

                    // 初始化追溯码
                    await this.initSummaryTraceCode(data.list);

                    this.order = data;
                    this.order.list = this.order.list.map((item, index) => {
                        if (item.batchs && item.batchs.length === 1 && !item.batchs[ 0 ].batchId) {
                            return {
                                keyId: createGUID(),
                                sortId: index,
                                goods: item.goods,
                                beforePieceCount: item.batchs[ 0 ].beforePieceCount,
                                beforePackageCount: item.batchs[ 0 ].beforePackageCount,
                                pieceCount: item.batchs[ 0 ].pieceCount,
                                packageCount: item.batchs[ 0 ].packageCount,
                                batchs: [],
                            };
                        }
                        return {
                            sortId: index,
                            ...item,
                        };

                    });
                    this.maxCount = this.order.list && this.order.list.length || 0;
                    // 子任务里面有数据说明保存过了
                    if (this.maxCount) {
                        this._taskDraftId = createGUID();
                    }

                    const { taskInfo } = data;
                    if (taskInfo && taskInfo.stockCheckScope) {
                        this.searchParams.type = taskInfo.stockCheckScope.type;
                        this.searchParams.subType = taskInfo.stockCheckScope.subType;
                        this.searchParams.cMSpec = taskInfo.stockCheckScope.cMSpec;
                        this.curCustomTypeIdList = taskInfo.stockCheckScope.customTypeIdList || [];
                        this.curTypeIdList = taskInfo.stockCheckScope.typeIdList || [];
                        this.checkRangeText = getCheckRange(taskInfo.stockCheckScope.typeNameList?.map(this.transGoodsClassificationName), this.createCustomTypeNameList(this.curCustomTypeIdList));
                    }
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                    console.error('获取盘点信息错误', e);
                }
            },
            // 多人盘点保存草稿
            async saveCheckForm() {
                this.$refs.createForm.validate(async (val) => {
                    if (val) {
                        try {
                            this.saveLoading = true;
                            const postData = {
                                taskId: this.taskId,
                                parentTaskId: this.parentTaskId,
                                list: this.transOrderList(),
                            };
                            await StockCheckAPI.saveGoodsList(this.taskId, postData);
                            this.$emit('refresh',false, 'add');
                            this.saveLoading = false;
                            this.showDialog = false;
                        } catch (e) {
                            this.saveLoading = false;
                            console.warn('保存盘点单错误', e);
                        }
                    }
                }, (item) => {
                    const { classList } = item.$el;
                    // 保存草稿不校验数量
                    if (classList.contains('check-true-count')) {
                        return true;
                    }
                    return false;
                });
            },

            async finishCheckForm() {
                const tips = this.isFinishedSubTask ? '确定后修改的内容即提交，创建人将汇总完成本次盘点' : '确定后即完成子任务，创建人将汇总完成本次盘点';
                this.$refs.createForm.validate(async (val) => {
                    if (val) {
                        this.loading = true;
                        this.buttonLoading = true;
                        const postData = {
                            taskId: this.taskId,
                            parentTaskId: this.parentTaskId,
                            list: this.transOrderList(),
                        };
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: tips,
                            onConfirm: async () => {
                                try {
                                    await StockCheckAPI.finishCheckMission(this.taskId, postData);
                                    this.$emit('refresh',false, 'add');
                                    this.buttonLoading = false;
                                    this.showDialog = false;
                                    this.loading = false;
                                } catch (e) {
                                    this.buttonLoading = false;
                                    this.loading = false;
                                    console.warn('完成盘点任务错误', e);
                                }
                            },
                            onCancel: () => {
                                this.buttonLoading = false;
                                this.loading = false;
                            },
                        });
                    }
                });
            },

            /**
             * @desc 清空账面数量
             */
            async clearHandler() {
                await this.submit(1);
            },
            /**
             * @desc 保持账面数量
             */
            async keepHandler() {
                await this.submit(2);
            },

            triggerFormValidate() {
                this.$refs.createForm?.validate?.();
            },

            /**
             * 汇总初始化追溯码
             * @param {Object[]} list
             * @return {Promise<void>}
             */
            async initSummaryTraceCode(list) {
                if (Array.isArray(list) && list.length) {
                    try {
                        let totalBatchs = [...list];
                        for (const item of list) {
                            // 补充无码标识
                            this.initNoTraceCodeList(item);
                            const { unit } = this.getTransUnitCount(item, item.goods);
                            TraceCode.initTraceableCodeListInInventory(item.traceableCodeList, item.goods, null, unit);
                            // 如果有多批次
                            if (Array.isArray(item.batchs) && item.batchs.length) {
                                for (const batch of item.batchs) {
                                    batch.goods = item.goods;
                                    // 补充无码标识
                                    this.initNoTraceCodeList(batch, item.goods);
                                    const { unit: batchUnit } = this.getTransUnitCount(batch, item.goods);
                                    TraceCode.initTraceableCodeListInInventory(batch.traceableCodeList, item.goods, null, batchUnit);
                                }
                                totalBatchs = totalBatchs.concat(item.batchs);
                            }
                        }
                        // 初始化追溯码
                        await this.initCollectCodeCountList(totalBatchs);
                    } catch (error) {
                        console.error('初始化追溯码失败', error);
                    }
                }
            },
        },

    };
</script>
