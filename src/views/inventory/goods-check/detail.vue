<template>
    <frame-dialog
        v-if="showDialog"
        v-model="showDialog"
        class="auto-form goods-check-wrapper"
        :class="{ 'detail-check-wrapper': coworkTaskId }"
        show-title-append
        :title="dialogTitleOption.title"
        :order-no="dialogTitleOption.orderNo"
        :status-name="dialogTitleOption.statusName"
        :tag-config="dialogTitleOption.tagConfig"
        :right-width="rightWidth"
        :dialog-config="dialogConfig"
    >
        <div v-if="coworkTaskId" slot="title" style="width: 90%;">
            <abc-tabs-v2
                v-model="curTaskId"
                :option="subJobs"
                size="huge"
                :need-animation="false"
                :adaptation="true"
                :border="false"
                @change="changeSubTab"
            ></abc-tabs-v2>
        </div>
        <abc-form
            ref="createForm"
            v-abc-loading="loading"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-form-item-group is-excel>
                        <abc-descriptions
                            :column="3"
                            :label-width="90"
                            grid
                            size="large"
                            background
                        >
                            <abc-descriptions-item
                                v-if="multiPharmacyCanUse"
                                content-class-name="ellipsis"
                                :span="1"
                                label="盘点库房"
                            >
                                <span>{{ order?.pharmacy?.name }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="checkRangeText"
                                content-class-name="ellipsis"
                                :span="1"
                                label="盘点范围"
                            >
                                <span>{{ checkRangeText }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :span="1"
                                label="盘点人"
                            >
                                <span>{{ order?.createdUser?.name }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :span="1"
                                label="盘点门店"
                            >
                                <span>{{ clinicName(order?.organ) }}</span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :span="commentSpan"
                                label="备注"
                            >
                                <!--不支持修改备注，可能会放开-->
                                <!--<abc-form-item-->
                                <!--    v-if="canFinishCheck"-->
                                <!--&gt;-->
                                <!--    <abc-input-->
                                <!--        v-model="postFinishCheckData.comment"-->
                                <!--        :max-length="200"-->
                                <!--    ></abc-input>-->
                                <!--</abc-form-item>-->
                                <!--<span v-else>{{ commentText }}</span>-->
                                <span :title="commentText">{{ commentText }}</span>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        cell-size="large"
                        :render-config="headerConfig"
                        :data-list="dataList"
                        empty-size="small"
                        :custom-tr-key="(e)=>e.id"
                        :show-hover-tr-bg="false"
                        :scroll-load-config="{
                            fetchData: fetchOrder,
                            total: order?.totalCount ?? dataList.length,
                        }"
                    >
                        <!--药品编码-->
                        <template #shortId="{ trData: { goods } }">
                            <abc-table-cell class="ellipsis">
                                <overflow-tooltip :content="goods.shortId">
                                </overflow-tooltip>
                            </abc-table-cell>
                        </template>
                        <!--药品名称-->
                        <template #name="{ trData: { goods } }">
                            <display-name-cell
                                :goods="goods"
                                :hover-config="{
                                    openDelay: 500,
                                    showPrice: true,
                                    showShebaoCode: true,
                                    pharmacyNo: order.pharmacy ? order.pharmacy.no : ''
                                }"
                            ></display-name-cell>
                        </template>
                        <!--类型-->
                        <template #goodsType="{ trData: { goods } }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title.ellipsis="transGoodsTypeName(goods)"></span>
                            </abc-table-cell>
                        </template>
                        <!--二级分类-->
                        <template #grade="{ trData: { goods } }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="goods.customTypeName || ''">
                                    {{ goods.customTypeName || '' }}
                                </span>
                            </abc-table-cell>
                        </template>
                        <!--批次-->
                        <template #batch="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="row.batchId || ''">{{ row.batchId || '' }}</span>
                            </abc-table-cell>
                        </template>

                        <!--生产批号-->
                        <template #batchNo="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="row.batchNo || ''">
                                    {{ row.batchNo || '' }}
                                </span>
                            </abc-table-cell>
                        </template>

                        <!--账面数量-->
                        <template #currentCount="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span class="ellipsis" :title="accountCount(row)">
                                    {{ accountCount(row) }}
                                </span>
                            </abc-table-cell>
                        </template>

                        <!--实际数量-->
                        <template #trueCount="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span :title="row | complexCount" class="ellipsis">
                                    {{ row | complexCount }}
                                </span>
                            </abc-table-cell>
                        </template>

                        <!--盈亏-->
                        <template #fullOrLass="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <abc-text :theme="changeCountClass(row)" :title="changeCountText(row)">
                                    {{ changeCountText(row) }}
                                </abc-text>
                            </abc-table-cell>
                        </template>

                        <template #changeCostPrice="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <data-permission-control>
                                    <abc-popover
                                        class="inventory-popper-wrapper"
                                        placement="bottom-end"
                                        trigger="click"
                                        theme="yellow"
                                        :disabled="!(row.detail && row.detail.length)"
                                    >
                                        <inventory-order-fixed-hover-popover
                                            slot="reference"
                                            :order-type="CorrectOrderTypeEnum.GoodsCheck"
                                            :order-item="row"
                                            is-revise-order-hover
                                        >
                                            <abc-text
                                                v-abc-title.ellipsis="inventoryFormatMoney(row.totalCostPriceChange)"
                                                :theme="getAmountTheme(row)"
                                                :style="{ 'text-decoration': row.detail?.length ? 'underline' : 'none' }"
                                            >
                                            </abc-text>
                                        </inventory-order-fixed-hover-popover>
                                        <template v-if="row.detail && row.detail.length">
                                            <div class="order-table-wrapper">
                                                <div class="table-header">
                                                    <div class="batch-no">
                                                        批次
                                                    </div>
                                                    <div class="date">
                                                        生产批号
                                                    </div>
                                                    <div class="package-price">
                                                        账面数量
                                                    </div>
                                                    <div class="count">
                                                        实际数量
                                                    </div>
                                                    <div class="package-price">
                                                        盈亏数量
                                                    </div>
                                                    <div class="package-price">
                                                        盈亏金额(进价)
                                                    </div>
                                                </div>
                                                <div class="table-body">
                                                    <div v-for="tempItem in row.detail" :key="tempItem.batchId" class="table-tr">
                                                        <div class="batch-no ellipsis" :title="tempItem.batchId">
                                                            {{ tempItem.batchId }}
                                                        </div>
                                                        <div class="date ellipsis" :title="tempItem.batchNo">
                                                            {{ tempItem.batchNo }}
                                                        </div>
                                                        <div
                                                            v-abc-title.ellipsis="accountCount(tempItem, row.goods)"
                                                            class="package-price"
                                                        >
                                                        </div>
                                                        <div
                                                            v-abc-title.ellipsis="formatGoodsStock(
                                                                tempItem.packageCount,
                                                                tempItem.pieceCount,
                                                                row.goods
                                                            )"
                                                            class="count"
                                                        >
                                                        </div>
                                                        <div v-abc-title.ellipsis="changeCountText(tempItem, row.goods)" class="package-price">
                                                        </div>
                                                        <div v-abc-title.ellipsis="inventoryFormatMoney(tempItem.totalCostPriceChange)" class="package-price">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </abc-popover>
                                </data-permission-control>
                            </abc-table-cell>
                        </template>

                        <template #changePrice="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title.ellipsis="inventoryFormatMoney(row.totalPriceChange)"></span>
                            </abc-table-cell>
                        </template>

                        <template
                            #detail="{
                                trData: {
                                    subJobs,
                                    goods
                                }
                            }"
                        >
                            <abc-table-cell class="ellipsis">
                                <abc-popover
                                    v-if="subJobs && subJobs.length"
                                    trigger="hover"
                                    placement="bottom-end"
                                    theme="yellow"
                                >
                                    <abc-text slot="reference" theme="primary-light" style="cursor: pointer;">
                                        详情
                                    </abc-text>
                                    <div class="order-list-wrapper">
                                        <p v-for="job in subJobs" :key="job.name">
                                            <span>{{ job.taskName }} </span>
                                            <span style="margin-left: 16px;">
                                                <template
                                                    v-if="isChineseMedicine(goods)"
                                                >
                                                    {{ job.pieceCount }}{{ goods.pieceUnit }}
                                                </template>
                                                <template v-else>
                                                    {{ job.packageCount }}{{ goods.packageUnit }}
                                                    {{ job.pieceCount }}{{ goods.pieceUnit }}
                                                </template>
                                            </span>
                                        </p>
                                    </div>
                                </abc-popover>
                            </abc-table-cell>
                        </template>

                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item,
                            }"
                        >
                            <traceable-code-cell
                                v-model="item.traceableCodeList"
                                :goods="item.goods"
                                :goods-count="{
                                    label: '实际数量',
                                    value: item.packageCount || 0
                                }"
                                :scene-type="'goodsCheck'"
                                readonly
                            ></traceable-code-cell>
                        </template>
                        <!--品种，数量，总计-->
                        <template #footer>
                            <abc-flex
                                flex="1"
                                align="center"
                                justify="flex-end"
                                style="padding: 0 12px;"
                            >
                                <abc-space v-if="order?.list?.length" :size="4">
                                    <abc-text theme="gray">
                                        品种
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ kindCount }}
                                    </abc-text>

                                    <data-permission-control>
                                        <div>
                                            <abc-text theme="gray">
                                                ，盈亏总金额(进价)
                                            </abc-text>
                                            <abc-text theme="black">
                                                {{ order.totalCostPriceChange | formatMoney(false) }}
                                            </abc-text>
                                        </div>
                                    </data-permission-control>

                                    <abc-text theme="gray">
                                        ，盈亏总金额(售价)
                                    </abc-text>
                                    <abc-text theme="black">
                                        {{ order.totalPriceChange | formatMoney(false) }}
                                    </abc-text>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <error-goods-dialog
            v-if="showErrorGoods"
            v-model="showErrorGoods"
            :message-detail="errorGoodsMessage"
            @clear="clearGoodStockHandler"
            @keep="keepGoodsStockHandler"
            @ignoreSpec="ignoreSpecHandler"
        ></error-goods-dialog>

        <template #right>
            <abc-layout>
                <approval-flow v-if="!!approvalViewModel.approvalDetail" :inst-detail="approvalViewModel.approvalDetail"></approval-flow>
            </abc-layout>
        </template>

        <div slot="footer" class="dialog-footer" style="min-height: 32px;">
            <logs-v3-popover v-if="logs.length" :logs="logs" style="margin-right: auto;"></logs-v3-popover>

            <abc-space>
                <template v-if="canFinishCheck">
                    <abc-button :loading="btnLoading" @click="handleSubmit">
                        完成盘点
                    </abc-button>
                    <abc-button type="blank" :disabled="btnLoading" @click="showDialog = false">
                        取消
                    </abc-button>
                </template>
                <template v-else>
                    <abc-button
                        v-if="showReinitiateButton && (isRevoked || isRefused) && !isChainAdmin"
                        type="blank"
                        @click="handleReapply"
                    >
                        修改并重新发起
                    </abc-button>


                    <template v-if="gspInstId">
                        <template v-if="canApprove">
                            <abc-button
                                :loading="approvalViewModel.loadingAgree"
                                :disabled="approvalViewModel.loadingReject"
                                @click="handleApprovalAgree"
                            >
                                同意
                            </abc-button>
                            <abc-button
                                type="danger"
                                variant="ghost"
                                :loading="approvalViewModel.loadingReject"
                                :disabled="approvalViewModel.loadingAgree"
                                @click="handleApprovalReject"
                            >
                                驳回
                            </abc-button>
                        </template>
                    </template>

                    <template v-else>
                        <template
                            v-if="isReview && isChainAdmin"
                        >
                            <abc-button type="primary" :loading="reviewLoading" @click="handleReview(true)">
                                通过
                            </abc-button>
                            <abc-button type="danger" :loading="reviewLoading" @click="handleReview(false)">
                                驳回
                            </abc-button>
                        </template>
                    </template>

                    <template v-if="isOvered">
                        <abc-check-access>
                            <print-dropdown
                                v-if="isMainMission"
                                :loading="printBtnLoading"
                                @print="print"
                                @select-print-setting="openPrintConfigSettingDialog"
                            ></print-dropdown>
                        </abc-check-access>

                        <abc-check-access v-if="isMainMission">
                            <abc-button
                                type="blank"
                                @click="exportExcel"
                            >
                                导出
                            </abc-button>
                        </abc-check-access>
                    </template>
                    <abc-button
                        v-if="isReview && !isChainAdmin && !gspInstId"
                        type="danger"
                        :loading="revokeLoading"
                        @click="revokeHandler"
                    >
                        撤回
                    </abc-button>
                    <abc-button type="blank" @click="showDialog = false">
                        关闭
                    </abc-button>
                </template>
            </abc-space>
        </div>

        <review-dialog
            v-if="showReviewDialog"
            v-model="showReviewDialog"
            :is-pass="isOrderPass"
            @review="handleSubmitReview"
        ></review-dialog>

        <dialog-undisposed-goods
            v-if="showUndisposedGoodsDialog"
            v-model="showUndisposedGoodsDialog"
            :pharmacy-no="order?.pharmacy?.no"
            :error-list="undisposedGoodsList"
            @createOrder="handleCreate"
        ></dialog-undisposed-goods>
    </frame-dialog>
</template>

<script type="text/ecmascript-6">
    import StockCheckAPI from 'api/goods/check';

    import {
        clinicName, complexCount, goodsTypeName, isChineseMedicine,
    } from 'src/filters/goods.js';
    import { formatGoodsStock } from '../goods-out/common';
    import {
        CorrectOrderTypeEnum, GOODS_CHECK_STATUS,
    } from 'views/inventory/constant.js';
    import { mapGetters } from 'vuex';
    import { inventoryFormatMoney } from 'views/inventory/goods-utils.js';
    import { getCheckRange } from 'views/inventory/goods-check/common.js';

    import OverflowTooltip from '@/components/overflow-tooltip.vue';
    import ErrorGoodsDialog from './components/error-goods-dialog';
    import ReviewDialog from './components/review-dialog.vue';

    import totalInfo from '../mixins/total-info.js';
    import dialogAutoWidth from 'views/inventory/mixins/dialog-auto-width.js';
    import GoodsTableV3Mixins from 'views/inventory/mixins/goods-table-v3.js';

    import Clone from 'utils/clone';
    import AbcPrinter from '@/printer';
    import { ABCPrintConfigKeyMap } from '@/printer/constants.js';
    import PrintDropdown from 'views/print/print-dropdown';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    const { orderMainNameText } = getViewDistributeConfig().Inventory;
    import {
        DataPermissionMixin,
        createDataPermissionControl,
        DATA_PERMISSION_CONTROL_SCENE,
    } from 'views/inventory/common/data-permission-control';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    import useApprovalOrder from '@/views-pharmacy/common/hooks/approval-order';
    import GoodsAPI from 'api/goods';
    import { businessTypeConst } from '@/views-pharmacy/common/constants';
    import useReviseOrder from 'views/inventory/hooks/useReviseOrder';
    const DialogUndisposedGoods = () => import('views/inventory/goods-check/components/undisposed-goods.dialog.vue');
    const FrameDialog = () => import('@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue');
    const ApprovalFlow = () => import('@/views-pharmacy/components/approval-flow/index.vue');

    const tableConfig = [
        {
            label: `${orderMainNameText}编码`,
            prop: 'shortId',
            flex: 2,
            thStyle: {
                minWidth: '80px',
            },
            tdStyle: {
                minWidth: '80px',
            },
        },
        {
            label: `${orderMainNameText}名称`,
            prop: 'name',
            flex: 2,
            thStyle: {
                minWidth: '100px',
            },
            tdStyle: {
                minWidth: '100px',
            },
        },
        {
            label: '类型',
            prop: 'goodsType',
            width: 70,
        },
        {
            label: '二级分类',
            prop: 'grade',
            flex: 1,
            thStyle: {
                minWidth: '80px',
            },
            tdStyle: {
                minWidth: '80px',
            },
        },
        {
            label: '批次',
            prop: 'batch',
            flex: 1,
            thStyle: {
                minWidth: '80px',
            },
            tdStyle: {
                minWidth: '80px',
            },
        },
        {
            label: '生产批号',
            prop: 'batchNo',
            flex: 1,
            thStyle: {
                minWidth: '80px',
            },
            tdStyle: {
                minWidth: '80px',
            },
        },
        {
            label: '账面数量',
            prop: 'currentCount',
            justifyContent: 'flex-end',
            flex: 1,
            thStyle: {
                minWidth: '80px',
            },
            tdStyle: {
                minWidth: '80px',
            },
        },

        {
            label: '实际数量',
            prop: 'trueCount',
            width: 80,
            justifyContent: 'flex-end',
        },

        {
            label: '盈亏数量',
            prop: 'fullOrLass',
            width: 80,
            justifyContent: 'flex-end',
        },
        {
            label: '盈亏金额(进价)',
            prop: 'changeCostPrice',
            width: 110,
            thStyle: { 'padding': '0 10px 0 0' },
            justifyContent: 'flex-end',
        },
        {
            label: '盈亏金额(售价)',
            prop: 'changePrice',
            width: 110,
            thStyle: { 'padding': '0 10px 0 0' },
            justifyContent: 'flex-end',
        },
    ];

    const mulCheckConfig = [ {
        label: '',
        prop: 'detail',
        width: 40,
        justifyContent: 'center',
        tdStyle: { 'padding': '0px' },
    } ];

    export default {
        name: 'OrderDetail',
        components: {
            ApprovalFlow,
            ErrorGoodsDialog,
            ReviewDialog,
            PrintDropdown,
            DisplayNameCell,
            LogsV3Popover,
            DialogUndisposedGoods,
            FrameDialog,
            OverflowTooltip,
            DataPermissionControl: createDataPermissionControl(DATA_PERMISSION_CONTROL_SCENE.check),
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
            InventoryOrderFixedHoverPopover: () => import('views/inventory/components/inventory-order-fixed-hover-popover.vue'),
        },
        mixins: [
            totalInfo,
            dialogAutoWidth,
            GoodsTableV3Mixins,
            DataPermissionMixin,
        ],
        props: {
            value: Boolean,
            orderId: [ String, Number ],
            goodsId: [ String, Number ],
            taskId: [ String, Number ],
            parentTaskId: [ String, Number ],
            coworkTaskId: [ String, Number ],
            gspInstId: {
                type: String,
                default: '',
            },
            showReinitiateButton: {
                type: Boolean,
                default: true,
            },
        },
        setup() {
            const {
                viewModel,
                canApprove,
                fetchApprovalDetail,
                approvalAgree,
                approvalReject,
            } = useApprovalOrder();

            const {
                isReviseTotalPrice,
                hasRevise,
            } = useReviseOrder();

            return {
                approvalViewModel: viewModel,
                canApprove,
                fetchApprovalDetail,
                approvalAgree,
                approvalReject,

                isReviseTotalPrice,
                hasRevise,
            };
        },
        data() {
            return {
                CorrectOrderTypeEnum,
                GOODS_CHECK_STATUS,
                showDialog: this.value,
                order: null,
                isFirstPrint: true,
                loading: false,
                printBtnLoading: false,
                offset: 0,
                limit: 100,
                curTaskId: '',

                showErrorGoods: false,
                errorGoodsMessage: {},
                mulMissionsInfo: null,
                btnLoading: false,
                orderNo: '',

                postFinishCheckData: {
                    sig: '',
                    comment: '',
                    parentTaskId: this.parentTaskId, // 父任务ID
                    submitFlag: 0, // 默认值 0 ，1, 未盘点药品全部盘为0, 2, 未盘点药品保持账面库存
                    submitSpecFlag: 1, // 是否校验规格已被修改 默认需要校验
                },
                needToReview: false,
                isOrderPass: false,
                showReviewDialog: false,
                reviewLoading: false,
                revokeLoading: false,
                checkRangeText: '', // 盘点范围
                undisposedGoodsList: [], // 未处理的药品列表
                showUndisposedGoodsDialog: false,
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin', 'goodsConfig', 'isChainSubStore', 'multiPharmacyCanUse', 'traceCodeConfig']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            needTransGoodsClassificationName() {
                return this.viewDistributeConfig.needTransGoodsClassificationName;
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsCheck;
            },
            rightWidth() {
                if (this.gspInstId) return 320;
                return 0;
            },
            canFinishCheck() {
                return this.taskId === this.parentTaskId && this.parentTaskId;
            },
            dialogConfig() {
                return {
                    responsive: true,
                    'append-to-body': true,
                };
            },
            isSupportGoodsCheckApprovalProcess() {
                return this.viewDistributeConfig.Inventory.isSupportGoodsCheckApprovalProcess;
            },
            stockCheckChainReview() {
                if (this.isSupportGoodsCheckApprovalProcess) {
                    return this.needToReview;
                }
                return this.goodsConfig?.chainReview?.stockCheckChainReview && this.isChainSubStore;
            },
            commentText() {
                let commentText = '-';
                const { logs } = this.order || {};
                if (logs?.length) {
                    commentText = logs[logs.length - 1]?.comment || '-';
                }
                return commentText;
            },
            commentSpan() {
                let span = 1;
                if (this.multiPharmacyCanUse && this.checkRangeText) {
                    span = 2;
                }
                if (this.multiPharmacyCanUse || this.checkRangeText) {
                    span = 3;
                }
                return span;
            },
            logs() {
                return this.order?.logs ?? [];
            },
            headerConfig() {
                return {
                    list: [
                        {
                            label: `${orderMainNameText}编码`,
                            key: 'shortId',
                            style: {
                                width: '100px',
                            },
                        },
                        {
                            label: `${orderMainNameText}名称`,
                            key: 'name',
                            style: {
                                flex: 2,
                                width: '160px',
                            },
                        },
                        {
                            label: '类型',
                            key: 'goodsType',
                            style: {
                                width: '80px',
                            },
                        },
                        {
                            label: '二级分类',
                            key: 'grade',
                            style: {
                                width: '80px',
                            },
                        },
                        {
                            label: '批次',
                            key: 'batch',
                            style: {
                                width: '90px',
                            },
                        },
                        {
                            label: '生产批号',
                            key: 'batchNo',
                            style: {
                                width: '80px',
                            },
                        },
                        {
                            label: '账面数量',
                            key: 'currentCount',
                            style: {
                                width: '90px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                        },

                        {
                            label: '实际数量',
                            key: 'trueCount',
                            style: {
                                width: '90px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                        },

                        {
                            label: '盈亏数量',
                            key: 'fullOrLass',
                            style: {
                                width: '100px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                        },
                        {
                            label: '盈亏药品追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                        {
                            label: '盈亏金额(进价)',
                            key: 'changeCostPrice',
                            // thStyle: { 'padding': '0 10px 0 0' },
                            style: {
                                width: '110px',
                                textAlign: 'right',
                            },
                            justifyContent: 'flex-end',
                        },
                        {
                            label: '盈亏金额(售价)',
                            key: 'changePrice',
                            style: {
                                width: '120px',
                                textAlign: 'right',
                            },
                            // thStyle: { 'padding': '0 10px 0 0' },
                            justifyContent: 'flex-end',
                        },
                        {
                            label: '',
                            key: 'detail',
                            style: {
                                width: '48px',
                                'padding': '0px',
                            },
                            justifyContent: 'center',
                        },
                    ].filter((item) => {
                        if (item.key === 'detail') {
                            return this.taskId && (this.taskId === this.parentTaskId);
                        }
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
            dataList() {
                return this.order?.list ?? [];
            },
            dialogTitle() {
                if (this.coworkTaskId) return ' ';
                let prefix = '';
                if (this.multiPharmacyCanUse && this.order?.pharmacy?.name) {
                    prefix = `${this.order.pharmacy.name}的`;
                }
                const { Y2 } = this.$store.state?.theme?.style ?? {};
                if (this.order) {
                    if (this.isReview) {
                        return `${prefix}盘点单 ${this.order.orderNo || ''} <span class="goods-small-title" style="color: ${Y2}">待总部审核</span>`;
                    }
                    if (this.isRevoked) {
                        return `${prefix}盘点单 ${this.order.orderNo || ''} <span class="goods-small-title">已撤回</span>`;
                    }
                    if (this.isRefused) {
                        return `${prefix}盘点单 ${this.order.orderNo || ''} <span class="goods-small-title">已驳回</span>`;
                    }
                    return `${prefix}盘点单 ${this.order.orderNo || ''}`;
                }
                return `${prefix}盘点单`;

            },
            dialogTitleOption() {
                let title = '盘点单';
                let statusName = '';
                const tagConfig = {
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                };

                if (this.multiPharmacyCanUse && this.order?.pharmacy?.name) {
                    title = `${this.order.pharmacy.name}的盘点单`;
                }

                if (this.isReview) {
                    statusName = '待审核';
                    tagConfig.theme = 'primary';
                    tagConfig.variant = this.canApprove ? 'light-outline' : 'outline';
                }

                if (this.isRefused) {
                    statusName = '已驳回';
                    tagConfig.theme = 'danger';
                }

                if (this.isRevoked) {
                    statusName = '已撤回';
                    tagConfig.theme = 'default';
                }

                return {
                    title,
                    orderNo: this.order?.orderNo ?? '',
                    statusName,
                    tagConfig,
                };
            },
            subJobs() {
                let tabs = [];
                if (this.mulMissionsInfo) {
                    tabs = this.mulMissionsInfo.subTaskInfoItems.map((item) => {
                        return {
                            value: item.taskId,
                            label: item.taskName,
                            parentTaskId: this.mulMissionsInfo.taskInfo.parentTaskId,
                            isAll: false,
                        };
                    });
                }

                tabs.unshift({
                    value: this.orderId,
                    label: `盘点单${this.orderNo || ''}`,
                    isAll: true,
                });
                return tabs;
            },
            isMainMission() {
                if (this.coworkTaskId) {
                    return this.orderId === this.curTaskId;
                }
                return this.orderId;
            },
            /**
             * @desc 待审核
             */
            isReview() {
                return this.order?.status === GOODS_CHECK_STATUS.REVIEW;
            },
            /**
             * @desc 已拒绝
             */
            isRefused() {
                return this.order?.status === GOODS_CHECK_STATUS.REFUSE;
            },
            // 已撤回
            isRevoked() {
                return this.order?.status === GOODS_CHECK_STATUS.WITH_DRAW;
            },
            isOvered() {
                return (this.order?.status === GOODS_CHECK_STATUS.FINISHED || this.order?.status === GOODS_CHECK_STATUS.REFUSE);
            },

        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
                if (!val) {
                    this.$emit('close');
                }
            },
        },
        async created() {
            this._tableConfig = Clone(tableConfig);
            if (this.taskId && (this.taskId === this.parentTaskId)) {
                this._tableConfig = this._tableConfig.concat(mulCheckConfig);
            }
            if (this.coworkTaskId) {
                this.fetchMissionDetail();
            }
            this.curTaskId = this.orderId;
            this.fetchApprovalByBusinessType();
            this.fetchOrder();
            if (this.gspInstId) {
                this.fetchApprovalDetail(this.gspInstId);
            }
        },

        methods: {
            clinicName,
            isChineseMedicine,
            formatGoodsStock,
            inventoryFormatMoney,
            transGoodsTypeName(goods) {
                return this.transGoodsClassificationName(goodsTypeName(goods));
            },
            getAmountTheme(item) {
                return item.fixedStockOrderList?.length ? 'warning-light' : 'black';
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'goods-check' }).generateDialogAsync({ parent: this });
            },
            async fetchMissionDetail() {
                try {
                    const { data } = await StockCheckAPI.fetchSubCheckMission(this.coworkTaskId, '');
                    this.mulMissionsInfo = data;
                } catch (e) {
                    console.warn('获取任务列表失败', e);
                }
            },
            async fetchApprovalByBusinessType() {
                try {
                    const data = await GoodsAPI.fetchApprovalByBusinessType(businessTypeConst.goodsStockCheck);
                    if (this.isChainAdmin) {
                        // 总部看自己
                        this.needToReview = !!data?.chainStatus;
                    } else if (this.isSingleStore) {
                        // 单店看门店
                        this.needToReview = !!data?.clinicStatus;
                    } else {
                        // 连锁子店门店总部都要看
                        this.needToReview = !!data?.clinicStatus || !!data?.chainStatus;
                    }
                    console.log('data=', data);
                } catch (e) {
                    console.log(e);
                }
            },

            async fetchOrder() {
                try {
                    this.loading = true;
                    if (this.orderId) {
                        const order = await StockCheckAPI.getOrderById(this.orderId, {
                            batchs: 1,
                            limit: this.limit,
                            offset: this.offset,
                        });

                        if (order) {
                            const list = this.offset === 0 ? order.list : [...this.order?.list ?? [], ...order.list ?? []];
                            this.order = {
                                ...order,
                                list,
                                totalCount: order.totalCount || list.length,
                            };
                            this.offset += this.limit;
                            this.orderNo = order.orderNo;
                            if (this.order.stockCheckScope) {
                                this.checkRangeText = getCheckRange(this.order.stockCheckScope.typeNameList?.map(this.transGoodsClassificationName), this.order.stockCheckScope.customTypeNameList);
                            }
                        }
                    }
                    if (this.taskId) {
                        await this.fetchDataByTaskId(this.taskId, this.parentTaskId);
                    }
                    this.isFirstPrint = true;
                    if (this.goodsId) {
                        this.sortOrderList();
                    }
                    this.loading = false;
                } catch (e) {
                    if (e.code === 12236) {
                        this.showErrorGoods = true;
                        this.errorGoodsMessage = e;
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message || '网络错误',
                                type: 'error',
                            });
                        }
                    }
                    this.loading = false;
                    console.error('获取盘点单详情报错', e);
                }
            },
            async fetchDataByTaskId(taskId, parentTaskId) {
                const { data } = await StockCheckAPI.fetchGoodsListByTaskId(taskId, { parentTaskId });
                this.order = data;
                this.postFinishCheckData.sig = data.sig;
                if (data.taskInfo && data.taskInfo.stockCheckScope) {
                    this.checkRangeText = getCheckRange(data.taskInfo.stockCheckScope.typeNameList?.map(this.transGoodsClassificationName), data.taskInfo.stockCheckScope.customTypeNameList);
                }
            },
            /**
             * @desc 获取子任务的盘点详情
             */
            changeSubTab(index, item) {
                this.curTaskId = item.value;
                if (item.isAll) {
                    this.offset = 0;
                    this.fetchOrder();
                } else {
                    this.fetchDataByTaskId(this.curTaskId, item.parentTaskId);
                }
            },

            accountCount(that, goods = '') {
                const tempGoods = goods || that.goods;
                return complexCount({
                    goods: tempGoods,
                    pieceCount: that.beforePieceCount,
                    packageCount: that.beforePackageCount,
                });
            },
            /**
             * @desc 根据用户输入的实际数量，计算盈亏
             * <AUTHOR>
             * @date 2018/11/24 13:36:25
             */
            changeCount({
                pieceCount, packageCount,beforePieceCount,beforePackageCount, pieceNum,
            }) {
                if (pieceCount === '' && packageCount === '') return '';

                const _pieceNum = pieceNum || 1;
                const goodsPieceCount = +beforePieceCount || 0;
                const goodsPackageCount = +beforePackageCount || 0;

                pieceCount = +pieceCount || 0;
                packageCount = +packageCount || 0;

                return (pieceCount + packageCount * _pieceNum) - (goodsPieceCount + goodsPackageCount * _pieceNum);
            },

            /**
             * @desc 根据用计算盈亏结果 判断class
             * <AUTHOR>
             * @date 2018/11/24 13:36:25
             */
            changeCountClass(item) {
                const change = this.changeCount(item);
                if (change > 0) {
                    return 'success';
                } if (change < 0) {
                    return 'danger';
                }
                return 'black';
            },

            /**
             * @desc 根据用计算盈亏结果 展示文案+样式  展示批次的盈亏数量需要传 goods 信息
             * <AUTHOR>
             * @date 2018/11/24 13:45:23
             */
            changeCountText(item, goods = '') {
                const tempGoods = goods || item.goods;
                let change = this.changeCount(item);
                // console.log(change,'~~~~~change');
                if (change === '') return '';

                let sign = '';
                if (change > 0) {
                    sign = '+';
                } else if (change < 0) {
                    sign = '-';
                }
                change = Math.abs(change);

                let pieceNum, pieceCount, packageCount;
                // 中药的数量都是保存在pieceCount中
                if (isChineseMedicine(tempGoods)) {
                    // 大单位数据清 0 防止重复拼接（如果packageCount有值，pieceCount无值的中药）
                    packageCount = 0;
                    pieceCount = change;
                } else {
                    pieceNum = +tempGoods.pieceNum || 1;
                    pieceCount = change % pieceNum;
                    packageCount = Math.floor(change / pieceNum);
                }
                return sign + complexCount({
                    pieceCount, packageCount, goods: tempGoods,
                }, true);
            },

            exportExcel() {
                StockCheckAPI.exportById(this.orderId);
            },
            /**
             * @desc 获取盘点单打印数据
             * <AUTHOR>
             * @date 2019/11/15 01:00:06
             */
            async fetchPrintData(needMergedOrder) {
                try {
                    const data = await StockCheckAPI.getOrderById(this.orderId,{
                        batchs: 1,
                        print: 1,
                        needMergedOrder,
                    });
                    return data;
                } catch (e) {
                    console.log(e);
                }
            },
            /**
             * @desc  打印盘点清单
             * <AUTHOR>
             * @date 2018/11/24 14:57:28
             */
            print() {
                if (!this.orderId) return false;
                this.printBtnLoading = false;
                let printData = null;
                // 是否产生修正
                const hasRevise = this.hasRevise(this.order?.externalFlag);

                const printAction = async () => {
                    try {
                        this.printBtnLoading = true;
                        printData = await this.fetchPrintData(hasRevise ? 1 : 0);

                        if (printData.logs && printData.logs.length) {
                            printData.comment = printData.logs[printData.logs.length - 1]?.comment || '';
                        }
                        printData.multiPharmacyCanUse = !!this.multiPharmacyCanUse;
                        printData.needTransGoodsClassificationName = this.needTransGoodsClassificationName;

                        if (!this.sceneVisible[DATA_PERMISSION_CONTROL_SCENE.check]) {
                            printData = this.filterPrintData(
                                printData,
                                [
                                    'totalCostPriceChange',
                                    {
                                        destinationField: 'list',
                                        fields: [
                                            'totalCostPriceChange',
                                        ],
                                    },
                                ],
                            );
                        }

                        AbcPrinter.abcPrint({
                            templateKey: window.AbcPackages.AbcTemplates.goodsCheck,
                            printConfigKey: ABCPrintConfigKeyMap.PD,
                            data: printData,
                        });

                    } catch (e) {
                        console.error('打印失败:', e);
                        this.$Toast({
                            type: 'error',
                            message: '打印失败，请重试',
                        });
                    } finally {
                        this.printBtnLoading = false;
                    }
                };

                // 保持原有的确认逻辑
                if (hasRevise) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '该盘点单存在修改过的药品，将基于正确盘点信息打印',
                        onConfirm: () => {
                            printAction();
                        },
                    });
                } else {
                    printAction();
                }
            },
            sortOrderList() {
                const newOrderList = [];
                this.order.list.forEach((item) => {
                    newOrderList.push(item);
                });
                newOrderList.sort((a, b) => {
                    return (b.goodsId === this.goodsId) - (a.goodsId === this.goodsId);
                });
                this.order.list = Clone(newOrderList);
            },
            /**
             * @desc 转换批次信息   多个批次 存在batchs 数组中上传
             *                      默认批次 batchId 为null
             * <AUTHOR>
             * @date 2019/11/14 17:54:03
             * @params
             * @return
             */
            transBatches(goodsItem) {
                let res = [];
                const batches = goodsItem.batchs || [];
                const { goods } = goodsItem;
                if (batches && batches.length) {
                    res = batches.map((item) => {
                        return {
                            goodsId: goods.id,
                            pieceNum: goods.pieceNum,
                            batchId: item.batchId,
                            packageCount: item.packageCount,
                            pieceCount: item.pieceCount,
                            beforePackageCount: item.beforePackageCount,
                            beforePieceCount: item.beforePieceCount,
                        };
                    });
                } else {
                    res = [
                        {
                            goodsId: goods.id,
                            batchId: null,
                            pieceNum: goods.pieceNum,
                            packageCount: goodsItem.packageCount,
                            pieceCount: goodsItem.pieceCount,
                            beforePackageCount: goodsItem.beforePackageCount,
                            beforePieceCount: goodsItem.beforePieceCount,
                        },
                    ];
                }
                return res;
            },
            async handleCreate(list = [], stopLoading) {
                // 未盘点药品处理完成不再校验
                this.postFinishCheckData.isCheckScope = 0;
                this.postFinishCheckData.list = list.map((item) => {
                    const batchs = this.transBatches(item);
                    return {
                        batchs,
                    };
                });

                await this.ignoreSpecHandler();
                if (typeof stopLoading === 'function')stopLoading();
            },
            /**
             * @desc 创建者完成盘点任务
             */
            async finishCheck() {
                this.btnLoading = true;
                try {
                    await StockCheckAPI.collectCheckMission(this.taskId, this.postFinishCheckData);
                    this.showDialog = false;
                    this.$emit('finishCheck');
                    this.btnLoading = false;
                } catch (e) {
                    if (e.code === 12238) {
                        this.undisposedGoodsList = e.detail;
                        this.showUndisposedGoodsDialog = false;
                        this.$nextTick(() => {
                            this.showUndisposedGoodsDialog = true;
                        });
                    } else if (e.code === 12236 || e.code === 12243) {
                        this.showErrorGoods = true;
                        this.errorGoodsMessage = e;
                    } else if (e.code === 12235) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                            onClose: () => {
                                this.offset = 0;
                                this.fetchOrder();
                            },
                        });
                    } else if (e.code === 477 || e.code === 12505) {
                        let content = [];
                        content =
                            e.detail &&
                            e.detail.goodsList &&
                            e.detail.goodsList.map((item) => {
                                return item.medicine_cadn || item.name || '';
                            });
                        const contentStr = content && content.join('，');
                        this.$alert({
                            type: 'warn',
                            title: e.message,
                            content: [ contentStr ],
                        });
                    } else if (e.code === 12015) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else if (e.code === 12162) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                    }
                    console.error('盘点汇总失败', e);
                } finally {
                    this.btnLoading = false;
                    // 清除额外添加的数据
                    delete this.postFinishCheckData.isCheckScope;
                    delete this.postFinishCheckData.list;
                }
            },
            /**
             * @desc 全部盘为0
             */
            clearGoodStockHandler() {
                this.postFinishCheckData.submitFlag = 1;
                this.finishCheck();
            },
            /**
             * @desc 全部保持账面库存
             */
            keepGoodsStockHandler() {
                this.postFinishCheckData.submitFlag = 2;
                this.finishCheck();
            },
            ignoreSpecHandler() {
                this.postFinishCheckData.submitSpecFlag = 0;
                return this.finishCheck();
            },

            handleSubmit() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: this.stockCheckChainReview ?
                        '提交后将进入审批流程，审批通过后完成盘点，确认提交吗？' :
                        '确认提交盘点单吗？',
                    onConfirm: () => {
                        this.finishCheck();
                    },
                });
            },

            revokeHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确认撤回后单据将失效，可在此单据内修改并重新发起',
                    onConfirm: () => {
                        this.revoke();
                    },
                });
            },

            async revoke() {
                try {
                    this.revokeLoading = true;
                    await StockCheckAPI.revokeOrder(this.orderId);
                    this.revokeLoading = false;
                    this.offset = 0;
                    this.fetchOrder();
                    this.$emit('refresh',false, '', false);
                } catch (e) {
                    this.revokeLoading = false;
                }
            },
            /**
             * @desc 修改并重新发起
             */
            handleReapply() {
                this.$emit('resubmit', this.orderId);
            },
            handleReview(isPass) {
                this.isOrderPass = isPass;
                this.showReviewDialog = true;
            },
            async handleSubmitReview(data) {
                await this.submitReview(data);
            },
            async submitReview(postData) {
                try {
                    this.reviewLoading = true;
                    await StockCheckAPI.reviewOrder(this.orderId, postData);
                    this.$emit('refresh');
                    this.showDialog = false;
                    this.reviewLoading = false;
                } catch (e) {
                    if (e.code === 12162) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    }
                    this.reviewLoading = false;
                }
            },
            async handleApprovalAgree() {
                const response = await this.approvalAgree({
                    gspInstId: this.gspInstId,
                });
                if (response.status === false) {
                    return;
                }
                this.showDialog = false;
                this.$emit('confirm');
                this.$emit('refresh');
                this.$Toast({
                    message: '操作成功',
                    type: 'success',
                });
            },

            async handleApprovalReject() {
                const response = await this.approvalReject(this.gspInstId);
                if (response.status === false) {
                    return;
                }
                this.showDialog = false;
                this.$emit('confirm');
                this.$emit('refresh');
                this.$Toast({
                    message: '操作成功',
                    type: 'success',
                });
            },
        },

    };
</script>
