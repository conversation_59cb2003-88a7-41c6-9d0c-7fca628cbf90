<template>
    <abc-dialog
        v-if="showSetTabDialog"
        v-model="showSetTabDialog"
        title="设置本次调价规则"
        custom-class="tabs-price-dialog"
        :before-close="closeDialog"
        append-to-body
        :auto-focus="false"
        content-styles="width: 462px;"
    >
        <abc-form ref="form">
            <div class="tabs-price-dialog-info">
                <div class="tabs-price-dialog-info-line">
                    <div class="line-label">
                        新售价
                    </div>
                    <div class="line-text text-flex">
                        <abc-select
                            v-model="tabCheck"
                            no-icon
                            :width="[TabValueEnum.InsurancePrice, TabValueEnum.ListingPrice].includes(tabCheck) ? 102 : 74"
                            :inner-width="104"
                            custom-class="line-text-select"
                            only-bottom-border
                            :input-style="{
                                'text-align': 'center'
                            }"
                            @change="tabSelect"
                        >
                            <abc-option
                                v-for="(item,index) in tabRenderList"
                                :key="index"
                                style="padding: 8px 4px !important; text-align: center !important;"
                                :label="item.name"
                                :value="item.key"
                            >
                            </abc-option>
                        </abc-select>
                        <div v-if="tabCheck === 1 || tabCheck === 2" class="line-button">
                            <abc-select
                                v-if="tabCheck === 1"
                                v-model="priceMode"
                                no-icon
                                only-bottom-border
                                style="margin: 0 12px;"
                                :width="30"
                                :input-style="{
                                    'text-align': 'center', 'font-size': '14px'
                                }"
                            >
                                <abc-option
                                    key="rise"
                                    style="min-height: 22px; padding: 2px 4px !important; font-size: 14px; line-height: 14px; text-align: center !important;"
                                    label="+"
                                    :value="1"
                                >
                                </abc-option>
                                <abc-option
                                    key="decline"
                                    style="min-height: 22px; padding: 2px 4px !important; font-size: 14px; line-height: 14px; text-align: center !important;"
                                    label="-"
                                    :value="0"
                                >
                                </abc-option>
                            </abc-select>
                            <abc-icon
                                v-else
                                icon="a-plus13px"
                                size="12"
                                style="margin-right: 4px; margin-left: 8px;"
                            ></abc-icon>
                            <span>{{ tabName }}</span>
                            <abc-icon icon="a-cross13px" size="12" style="margin-right: 8px; margin-left: 4px;"></abc-icon>
                            <abc-form-item
                                :validate-event="validatePriceProportion"
                                style="margin: 0;"
                            >
                                <abc-input
                                    v-model="priceProportion"
                                    v-abc-focus-selected
                                    class="priceProportion"
                                    :width="50"
                                    :config="{
                                        max: 999,
                                    }"
                                    only-bottom-border
                                    type="number"
                                    :input-custom-style="{
                                        'text-align': 'left',
                                    }"
                                >
                                    <span slot="appendInner">%</span>
                                </abc-input>
                            </abc-form-item>
                        </div>
                    </div>
                </div>
                <div v-if="tabCheck === 1 || tabCheck === 2" class="tabs-price-dialog-info-line">
                    <div class="line-label">
                    </div>
                    <div class="line-text text-flex">
                        <div class="line-button">
                            <span style="padding-right: 4px;">保留</span>
                            <abc-select
                                v-model="scaleType"
                                no-icon
                                only-bottom-border
                                :width="80"
                                :input-style="{
                                    'text-align': 'center', 'font-size': '14px'
                                }"
                            >
                                <abc-option
                                    key="ceil"
                                    label="2位小数"
                                    :value="1"
                                >
                                </abc-option>
                                <abc-option
                                    key="round"
                                    label="3位小数"
                                    :value="2"
                                ></abc-option>
                                <abc-option
                                    key="floor"
                                    label="4位小数"
                                    :value="3"
                                >
                                </abc-option>
                            </abc-select>
                            <span style="padding: 4px;">，</span>
                            <abc-select
                                v-model="roundingMode"
                                no-icon
                                only-bottom-border
                                :width="84"
                                :input-style="{
                                    'text-align': 'center', 'font-size': '14px'
                                }"
                            >
                                <abc-option
                                    key="ceil"
                                    label="向上取整"
                                    :value="1"
                                >
                                </abc-option>
                                <abc-option
                                    key="round"
                                    label="四舍五入"
                                    :value="2"
                                ></abc-option>
                                <abc-option
                                    key="floor"
                                    label="向下取整"
                                    :value="3"
                                >
                                </abc-option>
                            </abc-select>
                        </div>
                    </div>
                </div>
                <div class="tabs-price-dialog-info-line">
                    <div class="line-label">
                        生效门店
                    </div>
                    <div class="line-text">
                        {{ chainShopList }}
                    </div>
                    <template v-if="isChainAdmin && isAllSetPrice && !isAllClinics">
                        <abc-popover
                            trigger="hover"
                            style="margin-left: 6px;"
                            placement="bottom-start"
                            theme="yellow"
                            :popper-style="{ marginLeft: '-1px' }"
                            width="120"
                        >
                            <div style="max-width: 600px;">
                                <span style="color: #7a8794;">生效门店</span><br />
                                <span>{{ shopChainList }}</span><br />
                                <span
                                    style="display: inline-block; margin-top: 12px; color: #7a8794;"
                                >剩余门店可自主定价，因此不受本次调价影响。</span><br />
                            </div>
                            <div slot="reference">
                                <i class="iconfont cis-icon-info_bold text-mark"></i>
                            </div>
                        </abc-popover>
                    </template>
                </div>
            </div>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button type="primary" :disabled="disabledButton" @click="save">
                确定
            </abc-button>
            <abc-button type="blank" @click="showSetTabDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { isNumber } from '@/utils';

    export const TabValueEnum = Object.freeze({
        CurrentPrice: 1,
        LastPrice: 2,
        SetPrice: 0,
        InsurancePrice: 3,
        ListingPrice: 6,
    });
    export const tabList = [
        {
            key: TabValueEnum.CurrentPrice,
            name: '当前售价',
        },
        {
            key: TabValueEnum.LastPrice,
            name: '最近进价',
        },
        {
            key: TabValueEnum.SetPrice,
            name: '手动输入',
        },
        {
            key: TabValueEnum.InsurancePrice,
            name: '调至医保限价',
        },
        {
            key: TabValueEnum.ListingPrice,
            name: '调至挂网价',
        },
    ];
    export default {
        name: 'SetPriceAdjustmentDialog',
        props: {
            value: Boolean,
            shopChainList: String,
            chainShopList: String,
        },
        data() {
            return {
                priceMode: 1, // 涨价还是降价
                priceProportion: '',//价格比例
                tabList,
                TabValueEnum,
                tabCheck: 1,//当前选中的值
                roundingMode: 1,// 1=向上取整、2=四舍五入、3=向下取整
                scaleType: 1,//1=2位小数、2=3位小数、3=4位小数
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin', //是连锁总部
                'currentClinic',
                'priceAdjustmentTabType',
                'goodsConfig',
                'priceAdjustmentRatio',
                'priceAdjustmentMode',
                'priceAdjustmentRoundingMode',
                'priceAdjustmentScaleType',
                'isEnableListingPrice',
            ]),
            disabledButton() {
                return this.tabCheck === this.priceAdjustmentTabType &&
                    this.priceMode === this.priceAdjustmentMode &&
                    this.roundingMode === this.priceAdjustmentRoundingMode &&
                    this.scaleType === this.priceAdjustmentScaleType &&
                    (isNumber(this.priceProportion) ?
                        isNumber(this.priceAdjustmentRatio) ? this.priceProportion === Math.abs(this.priceAdjustmentRatio) : false :
                        true);
            },
            showSetTabDialog: { //展示设置规则的弹窗
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            isAllSetPrice() { // 是否允许门店自己调价
                return this.goodsConfig?.subClinicPrice?.subSetPrice;
            },
            isAllClinics() { // 是否全部门店都可以自主调价
                return this.goodsConfig?.subClinicPrice?.subSetPriceAllClinics;
            },
            tabName() {
                return this.tabList.find((item) => {
                    return item.key === this.tabCheck;
                })?.name ?? '';
            },
            tabRenderList() {
                return this.tabList.filter((item) => {
                    if (item.key === TabValueEnum.InsurancePrice) {
                        return !this.isChainAdmin;
                    }
                    if (item.key === TabValueEnum.ListingPrice) {
                        return this.isEnableListingPrice;
                    }
                    return true;
                });
            },
        },
        created() {
            this.tabCheck = this.priceAdjustmentTabType;
            this.priceMode = this.priceAdjustmentMode;
            this.roundingMode = this.priceAdjustmentRoundingMode;
            this.scaleType = this.priceAdjustmentScaleType;
            if (isNumber(this.priceAdjustmentRatio)) this.priceProportion = Math.abs(this.priceAdjustmentRatio);//可能为负数，表示下调比例
        },
        mounted() {
            this.$nextTick(() => {
                window.setTimeout(() => {
                    $('.priceProportion').find('input').focus();
                }, 50);

            });
        },
        methods: {
            validatePriceProportion(value, callback) {
                if (this.tabCheck === 1 || this.tabCheck === 2) {
                    if (!isNumber(value)) {
                        return callback({
                            validate: false,
                            message: '不能为空',
                        });
                    }
                }
                return callback({ validate: true });
            },
            async save() {
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        let radio = this.tabCheck === 1 || this.tabCheck === 2 ? this.priceProportion : 0;

                        if (this.tabCheck === 1) {
                            // 当前售价模式下，radio为正数表示上调价格为负数表示下调价格
                            radio = this.priceMode ? +this.priceProportion : -this.priceProportion;
                            await this.$store.dispatch('setPriceMode', this.priceMode);
                        }
                        await this.$store.dispatch('setPriceAdjustmentTabType', this.tabCheck);
                        await this.$store.dispatch('setPriceRatio', radio);
                        await this.$store.dispatch('setRoundingMode', this.roundingMode);
                        await this.$store.dispatch('setScaleType', this.scaleType);


                        this.$emit('setPriceList', {
                            radio,
                            tabCheck: this.tabCheck,
                        });
                    }
                });
            },
            tabSelect(value) {
                if (value === this.priceAdjustmentTabType) {
                    // 相同的选择，使用上次的值
                    this.priceProportion = Math.abs(this.priceAdjustmentRatio);// 当前调价比例输入框不区分符号。保存的时候才根据 priceMode区分存储值。
                } else {
                    this.priceProportion = '';
                }
                this.tabCheck = value;
            },
            closeDialog() {
                this.showSetTabDialog = false; //
            },
        },
    };
</script>

<style lang="scss" scoped>
@import 'src/styles/theme';

.abc-dialog.tabs-price-dialog {
    width: 462px;

    .tabs-price-dialog-info {
        .tabs-price-dialog-info-line {
            display: flex;
            width: 100%;
            height: 32px;
            margin-top: 0;
            font-size: 14px;
            font-weight: normal;
            line-height: 32px;

            .line-label {
                width: 88px;
                min-width: 88px;
                color: #7a8795;
            }

            .text-flex {
                display: flex;
                flex: 1;
                align-items: center;
                //max-width: 290px !important;
                padding-right: 2px;

                .line-button {
                    display: flex;
                    align-items: center;
                }
            }

            .line-text {
                //max-width: 253px;
                margin-left: 0;
                overflow: hidden;
                color: $S1;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            div {
                .text-mark {
                    margin-left: 0;
                    color: #dadbe0;
                    cursor: pointer;
                }
            }
        }
    }

    .abc-dialog-footer {
        padding: 10px 24px !important;
    }
}
</style>
