<template>
    <abc-dialog
        v-if="showDialog"
        ref="abc-dialog"
        v-model="showDialog"
        custom-class="price-adjustment-dialog"
        header-style="padding-top:0px;padding-bottom:0px;"
        :before-close="closeDialog"
        append-to-body
        :auto-focus="false"
        responsive
        size="hugely"
        title="批量调价"
    >
        <div slot="title">
            <abc-tabs-v2
                v-model="type"
                :option="tabOptions"
                size="huge"
                :need-animation="false"
                :adaptation="true"
                :border="false"
                @change="handleTabChange"
            ></abc-tabs-v2>
        </div>
        <abc-layout v-if="type === 2" preset="dialog-table">
            <abc-layout-content @layout-mounted="handleMounted">
                <abc-table
                    :loading="historyLoading"
                    :render-config="historyGoodsHeaderConfig"
                    empty-size="small"
                    :data-list="historyList"
                    @handleClickTr="openHistoryInfo"
                >
                    <template #topHeader>
                        <abc-flex flex="1" align="center" justify="space-between">
                            <goods-auto-complete
                                :placeholder="isEyeglasses ? '眼镜名称/条形码' : '药品名称/条形码'"
                                :with-stock="false"
                                :only-stock="onlyStock"
                                :clear-search-key="false"
                                :pharmacy-no="pharmacyNo"
                                :clinic-id="isChainAdmin ? '' : currentClinic?.clinicId"
                                :search.sync="searchKeyHistory"
                                enable-local-search
                                clearable
                                size="medium"
                                :width="460"
                                @selectGoods="selectHistoryGoods"
                                @clear="clearSearch"
                            >
                                <abc-search-icon slot="prepend"></abc-search-icon>
                            </goods-auto-complete>

                            <abc-button
                                variant="ghost"
                                theme="default"
                                icon="n-upload-line"
                                @click="exportExcel"
                            >
                                导出
                            </abc-button>
                        </abc-flex>
                    </template>

                    <template #created="{ trData: item }">
                        <abc-table-cell class="ellipsis">
                            <span :title="item.created">{{ item.created|parseTime('y-m-d h:i:s') }}</span>
                        </abc-table-cell>
                    </template>
                    <template #createdUser="{ trData: item }">
                        <abc-table-cell class="ellipsis">
                            <span :title="item.createdUser && item.createdUser.name">{{
                                item.createdUser && item.createdUser.name ? item.createdUser.name : '-'
                            }}</span>
                        </abc-table-cell>
                    </template>
                    <template #kindCount="{ trData: item }">
                        <abc-table-cell class="ellipsis">
                            <span :title="item.kindCount">{{ item.kindCount || 0 }}</span>
                        </abc-table-cell>
                    </template>
                    <template #opType="{ trData: item }">
                        <abc-table-cell class="ellipsis">
                            <span v-if="item.opType">{{
                                tabList.find(i => {
                                    return i.key === item.opType
                                }).name
                            }} {{
                                item.opType !== 3 ? item.upPercent > 0 ? `上调${item.upPercent}%` : `下调${Math.abs(item.upPercent)}%` : ''
                            }}</span>
                            <span v-else>手动输入</span>
                        </abc-table-cell>
                    </template>
                    <template #modifyClinic="{ trData: item }">
                        <abc-table-cell class="ellipsis">
                            <span v-if="item.affectedClinicList" :title="getAffectedClinicList(item.affectedClinicList)">
                                {{ getAffectedClinicList(item.affectedClinicList) }}
                            </span>
                            <span v-else>{{ item.modifyClinic.shortName || item.modifyClinic.name }}</span>
                        </abc-table-cell>
                    </template>

                    <template #footer>
                        <div style="padding: 12px;">
                            <abc-pagination
                                :show-total-page="true"
                                :count="total"
                                :pagination-params="pageParams"
                                @current-change="pageTo"
                            >
                            </abc-pagination>
                        </div>
                    </template>
                </abc-table>
            </abc-layout-content>
        </abc-layout>
        <!--批量调价-->
        <div v-if="type === 1" class="price-adjustment-dialog-nel">
            <div class="price-adjustment-dialog-top">
                <div class="top-left">
                    <div class="price-title-text">
                        <span class="text-key">生效门店:</span>
                        <span v-if="!overTitle" class="text-label" style="max-width: 200px;">
                            {{ chainShopList }}
                        </span>
                        <span v-else id="text-over">
                            {{ chainShopList }}
                        </span>
                        <template v-if="isShowShopIcon">
                            <abc-popover
                                trigger="hover"
                                style="margin-left: auto;"
                                placement="bottom"
                                theme="yellow"
                                offset="10"
                                width="120"
                            >
                                <div style="max-width: 300px;">
                                    <span style="color: #7a8794;">生效门店:</span>
                                    <span>{{ shopChainList }}</span>
                                </div>
                                <div slot="reference">
                                    <i class="iconfont cis-icon-info_bold text-mark"></i>
                                </div>
                            </abc-popover>
                        </template>
                    </div>
                    <div class="price-title-text">
                        <span class="text-key">新售价:</span>
                        <span class="text-label" style="max-width: 300px;">{{ newPriceTabType }}</span>
                    </div>
                </div>
                <div class="top-right">
                    <abc-button variant="text" size="small" @click="modifySet">
                        修改
                    </abc-button>
                </div>
            </div>
            <abc-form ref="priceAdjustmentForm" class="v3-goods-wrapper" style="height: calc(100% - 48px);">
                <goods-table-v3
                    ref="tableV3Ref"
                    :header-config="goodsHeaderConfig"
                    :data-list="tableList"
                    :popover-config="{
                        popperStyle: {
                            'margin-left': '-10px'
                        }
                    }"
                    include-scroll-bar
                    :loading="defaultLoading"
                    style="min-height: 77px; margin-top: 16px;"
                >
                    <!--内容-->
                    <template #name="{ row }">
                        <div class="ellipsis">
                            <span v-abc-title="goodsFullName(row.goods)"></span>
                            <span class="label-small">{{ row.specifications || '' }}</span>
                            <span class="label-small">{{ row.manufacturer || '' }}</span>
                        </div>
                    </template>
                    <template #type="{ row }">
                        <span v-abc-title.ellipsis="getMedicalType(row)"></span>
                    </template>
                    <template #secondaryClassification="{ row }">
                        <span v-abc-title.ellipsis="getMedicalSecondary(row)"></span>
                    </template>
                    <template #supplier="{ row }">
                        <span v-abc-title.ellipsis="row.supplier || '-'"></span>
                    </template>
                    <template #packageCostPrice="{ row }">
                        <span v-abc-title.ellipsis="row.packageCostPrice ? moneyDigit(row.packageCostPrice, 5) : '-'"></span>
                    </template>
                    <template #priceLimit="{ row }">
                        <span v-abc-title.ellipsis="getPriceLimit(row) || '-'"></span>
                    </template>
                    <template #chainPackagePrice="{ row }">
                        <span
                            v-if="!chineseMedicineType.includes(row.typeId)"
                            v-abc-title.ellipsis="moneyDigit(row.chainPackagePrice, 5)"
                        ></span>
                        <span v-else style="padding: 0 6px; text-align: right;">-</span>
                    </template>
                    <template #latestPurchasePrice="{ row }">
                        <span v-abc-title.ellipsis="row.goods.dismounting ? moneyDigit(row.latestPurchasePrice, 5) : '-'"></span>
                    </template>
                    <template #listingPrice="{ row }">
                        <span v-abc-title.ellipsis="row.listingPrice ? moneyDigit(row.listingPrice, 5) : '-'"></span>
                    </template>
                    <template
                        #priceAdjustment="
                            {
                                row,rowIndex: index
                            }"
                    >
                        <abc-form-item
                            v-if="!chineseMedicineType.includes(row.typeId)"
                            :ref="`isPackagePrice${index}`"
                            style="width: 104px;"
                            :validate-event="isPackagePriceErr(row,index)"
                            @mouseenter.native="isPackagePriceErr(row,index)"
                        >
                            <abc-tooltip
                                placement="top-end"
                                :content="isPackagePriceWarnText(row)"
                                :disabled="(Number(row.priceAdjustment) === 0 && isNeedCheckSellPriceNotZero && isSupportShebaoPay(row.goods)) || !(!isPackagePriceErrStyle(row) && isPackagePriceWarnText(row))"
                            >
                                <!--判断是否是警告而非错误-->
                                <!---->
                                <abc-input
                                    v-model="row.priceAdjustment"
                                    v-abc-focus-selected
                                    type="number"
                                    :config="{
                                        formatLength: 4, supportZero: true
                                    }"
                                    class="price-input"
                                    :class="{
                                        'price_input_error': isPackagePriceErrStyle(row),'price_input_warn': !isPackagePriceErrStyle(row) && isPackagePriceWarnText(row)
                                    }"
                                    :input-custom-style="{ textAlign: 'right' }"
                                    @enter="enterEvent($event, true)"
                                    @change="checkPackagePricePercent(row, index)"
                                ></abc-input><!--判断是否是警告而非错误-->
                            </abc-tooltip>
                        </abc-form-item>
                        <div v-else style="padding: 0 10px; text-align: right;">
                            -
                        </div>
                    </template>
                    <template
                        #latestPriceAdjustment="
                            {
                                row,rowIndex: index
                            }"
                    >
                        <abc-form-item
                            v-if="row.goods.dismounting"
                            :ref="`isPrice${index}`"
                            :validate-event="isPriceErr(row,index)"
                            style="width: 104px;"
                            @mouseenter.native="isPriceErr(row,index)"
                        >
                            <abc-tooltip
                                placement="top-end"
                                :content="isPriceWarnText(row)"
                                :disabled="(isNotNull(row.latestPriceAdjustment) && Number(row.latestPriceAdjustment) === 0 && isNeedCheckSellPriceNotZero && isSupportShebaoPay(row.goods)) || !(!isPriceErrStyle(row) && isPriceWarnText(row))"
                            >
                                <!--判断是否是警告而非错误-->
                                <abc-input
                                    v-model="row.latestPriceAdjustment"
                                    v-abc-focus-selected
                                    :config="{
                                        formatLength: 4, supportZero: true
                                    }"
                                    type="number"
                                    class="price-input"
                                    :class="{
                                        'price_input_error': isPriceErrStyle(row),'price_input_warn': !isPriceErrStyle(row) && isPriceWarnText(row)
                                    }"
                                    :input-custom-style="{ textAlign: 'right' }"
                                    @enter="enterEvent($event, true)"
                                    @change="checkPricePercent(row, index)"
                                ></abc-input><!--判断是否是警告而非错误-->
                            </abc-tooltip>
                        </abc-form-item>
                        <div v-else style="padding: 0 10px; text-align: right;">
                            -
                        </div>
                    </template>
                    <template #profit="{ row }">
                        <span v-abc-title.ellipsis="isNull(row.profit) ? '-' : `${row.profit}%`"></span>
                    </template>
                    <template #delete="{ row }">
                        <abc-delete-icon @delete="deleteTable(row)"></abc-delete-icon>
                    </template>
                </goods-table-v3>

                <div class="v3-goods-table-footer" style="margin-bottom: 0;">
                    <goods-auto-complete-cover-title
                        class="entry-medicine price-adjustment-medicine"
                        :placeholder="`请输入需要调价的${orderMainNameText}名称`"
                        :search.sync="searchKey"
                        :width="372"
                        :only-stock="onlyStock"
                        :goods-all-types="goodsAllTypesList"
                        :clinic-id="isChainAdmin ? '' : currentClinic?.clinicId"
                        :is-price-adjustment="true"
                        :pharmacy-no="pharmacyNo"
                        :inorder-config="0"
                        :price-type="PriceType.PRICE"
                        enable-local-search
                        @selectGoods="selectGoods"
                    >
                        <abc-icon slot="prepend" icon="n-add-line-medium" color="var(--abc-color-T3)"></abc-icon>
                    </goods-auto-complete-cover-title>
                    <abc-button
                        type="ghost"
                        style="height: 32px; margin-right: auto; margin-left: 8px;"
                        @click="showAddMedicineDialog = true"
                    >
                        批量添加{{ isEyeglasses ? '眼镜' : '药品' }}
                    </abc-button>
                </div>
            </abc-form>
        </div>
        <!--批量调价-->
        <div v-if="type === 1" slot="footer" class="dialog-footer">
            <abc-button
                type="primary"
                :loading="submitBtnLoading"
                :disabled="tableList.length <= 0"
                @click="updatePrice"
            >
                执行调价
            </abc-button>
            <abc-button type="blank" @click="closeDialog">
                取消
            </abc-button>
        </div>
        <!--设置调价模式弹窗-->
        <set-price-adjustment-dialog
            v-if="showSetTabDialog"
            v-model="showSetTabDialog"
            :shop-chain-list="shopChainList"
            :chain-shop-list="chainShopList"
            @setPriceList="setPriceList"
        >
        </set-price-adjustment-dialog>
        <!--设置调价模式弹窗-->
        <!--批量添加药品弹窗-->
        <batch-add-goods-dialog
            v-if="showAddMedicineDialog"
            v-model="showAddMedicineDialog"
            :goods-all-types="goodsAllTypesList"
            :is-eyeglasses="isEyeglasses"
            :price-type="PriceType.PRICE"
            @setGoodsList="setGoodsList"
        >
        </batch-add-goods-dialog>
        <!--批量添加药品弹窗-->
        <!--批量添加历史详情弹窗-->
        <set-price-adjustment-history-dialog
            v-if="showSetPriceAdjustmentHistoryDialog"
            v-model="showSetPriceAdjustmentHistoryDialog"
            :order-id="orderId"
            :only-stock="onlyStock"
            :is-eyeglasses="isEyeglasses"
            :goods-all-types-list="goodsAllTypesList"
        >
        </set-price-adjustment-history-dialog>
        <!--批量添加历史详情弹窗-->
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import SetPriceAdjustmentDialog, {
        tabList, TabValueEnum,
    } from './set-price-adjustment-dialog.vue'; // 设置调价规则弹窗
    import SetPriceAdjustmentHistoryDialog from './set-price-adjustment-history-dialog.vue'; // 历史调价详情页面
    import BatchAddGoodsDialog from './batch-add-goods-dialog'; // 批量添加弹窗
    import EnterEvent from 'views/common/enter-event';

    const GoodsAutoCompleteCoverTitle = () => import('views/inventory/common/goods-auto-complete-cover-title');
    const GoodsAutoComplete = () => import('views/inventory/common/goods-auto-complete');
    const GoodsTableV3 = () => import('views/inventory/components/v3goods/goods-table.vue');

    import {
        goodsSpec, isChineseMedicine,
    } from '@/filters';
    import GoodsV3API from 'api/goods/index-v3.js';
    import GoodsAPI from 'api/goods/index.js';
    import Clone from 'utils/clone';
    import {
        isNull,
        moneyDigit,
        paddingMoney,
        isNumber, isNotNull, isSupportShebaoPay,
    } from '@/utils';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import { PriceType } from 'views/common/inventory/constants';

    const chineseMedicineType = [14, 15];// 中药类型

    export default {
        name: 'PriceAdjustmentDialog',
        components: {
            SetPriceAdjustmentDialog,
            SetPriceAdjustmentHistoryDialog,
            GoodsAutoCompleteCoverTitle,
            BatchAddGoodsDialog,
            GoodsAutoComplete,
            GoodsTableV3,
        },
        mixins: [EnterEvent],
        props: {
            value: Boolean,
            pharmacyNo: Number,
            isEyeglasses: Boolean,
            goodsAllTypes: { // 系统所有的药品物资商品分类以及二级分类 12
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                PriceType,
                showSetPriceAdjustmentHistoryDialog: false,
                orderMainNameText: '',// 口腔管家和普通门诊
                shopList: [], // 连锁总店有权限的门店
                goodsIdList: [],//药品
                historyLoading: false,//历史
                defaultLoading: false,//批量
                searchKey: '', // 搜索关键词
                searchKeyHistory: '',//历史搜索关键词
                searchKeyHistoryGoodId: '',//历史搜索药物ID
                orderId: '',//当前读取的历史记录
                chineseMedicineType,// 中药
                tabList, // 调价模式
                type: 1, //当前页面
                showSetTabDialog: false,// 是否展示弹窗
                tableList: [], // 调价药物信息列表
                historyList: [],// 批量调价历史列表
                historyGoodsHeaderConfig: {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: this.isEyeglasses ? '眼镜' : '药品/物资种类',
                            key: 'kindCount',
                            style: {
                                width: '150px',
                            },
                        },
                        {
                            label: '调价规则',
                            key: 'opType',
                            style: {
                                width: '250px',
                            },
                        },
                        {
                            label: '调价影响门店',
                            key: 'modifyClinic',
                            style: {
                                width: '350px',
                            },
                        },
                        {
                            label: '调价时间',
                            style: {
                                width: '250px',
                            },
                            key: 'created',
                        },
                        {
                            label: '操作人',
                            key: 'createdUser',
                            style: {
                                width: '150px',
                            },
                        },
                    ],
                },
                showAddMedicineDialog: false,// 是否展示批量添加药物弹窗
                consItemObject: {},
                priceDownMedicine: 0,//统计数据1
                priceNoRules: 0,//统计数据2
                overTitle: false, //是否内容展示超出的图标
                offset: 0,
                limit: 8,
                total: 0,
                pageSize: 100,//分页设置
                scrollConfig: { //页面滚动配置
                    maxHeight: 360,
                    tableWidth: 1222,
                },
                submitBtnLoading: false,
                tabOptions: [
                    {
                        value: 1,
                        label: '批量调价',
                    },
                    {
                        value: 2,
                        label: '历史调价记录',
                    },
                ],
            };
        },
        computed: {
            ...mapGetters([
                'isFirstLookPriceAdjustment',
                'isChainAdmin', //是连锁总部
                'isChain', // 是连锁
                'isSingleStore', //是单店
                'currentClinic',
                'goodsConfig',
                'goodsConfigPriceAdjustmentNoPowerClinicsId',
                'goodsConfigPriceAdjustmentNoPowerClinicsName',
                'priceAdjustmentTabType', //当前调价模式
                'priceAdjustmentRatio',// 当前比例
                'priceAdjustmentMode',// 当前售价模式下，是上调价格还是下调价格
                'priceAdjustmentScaleType',// 小数保留位数
                'priceAdjustmentRoundingMode',// 舍入方式
                'isEnableListingPrice',// 是否启用挂网价
                'isNeedCheckSellPriceNotZero',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            goodsHeaderConfig() {
                return [
                    {
                        label: this.isEyeglasses ? '眼镜名称' : '药品名称',
                        prop: 'name',
                        flex: 1,
                        tdClass: 'is-disabled',
                    },
                    {
                        label: '一级分类',
                        width: 88,
                        prop: 'type',
                        tdClass: 'is-disabled',
                    },
                    {
                        label: '二级分类',
                        width: 88,
                        prop: 'secondaryClassification',
                        tdClass: 'is-disabled',
                    },
                    {
                        label: '最近供应商',
                        width: 108,
                        prop: 'supplier',
                        tdClass: 'is-disabled',

                    },
                    {
                        label: '最近进价',
                        width: 80,
                        prop: 'packageCostPrice',
                        tdClass: 'is-disabled',
                        justifyContent: 'flex-end',
                    },
                    {
                        label: '医保限价(整)',
                        width: 100,
                        prop: 'priceLimit',
                        tdClass: 'is-disabled',
                        // 只在医保限价时才显示
                        priceAdjustmentTabType: 3,
                        justifyContent: 'flex-end',
                    },
                    {
                        label: '当前售价(整)',
                        width: 100,
                        prop: 'chainPackagePrice',
                        tdClass: 'is-disabled',
                        justifyContent: 'flex-end',
                    },
                    {
                        label: '当前售价(零)',
                        width: 100,
                        prop: 'latestPurchasePrice',
                        tdClass: 'is-disabled',
                        justifyContent: 'flex-end',
                    },
                    {
                        label: '挂网价',
                        width: 80,
                        prop: 'listingPrice',
                        tdClass: 'is-disabled',
                        justifyContent: 'flex-end',
                        hidden: !this.isEnableListingPrice,
                    },
                    {
                        label: '新售价(整)',
                        thStyle: {
                            'justify-content': 'flex-end',
                        },
                        width: 90,
                        prop: 'priceAdjustment',
                        tdStyle: {
                            'justify-content': 'flex-end',
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                    },
                    {
                        label: '新售价(零)',
                        thStyle: {
                            'justify-content': 'flex-end',
                        },
                        width: 90,
                        prop: 'latestPriceAdjustment',
                        tdStyle: {
                            'justify-content': 'flex-end',
                            padding: '0px',
                        },
                        tdClass: 'is-disabled',
                    },
                    {
                        label: '调价后毛利',
                        width: 90,
                        prop: 'profit',
                        tdClass: 'is-disabled',
                    },
                    {
                        label: '',
                        width: 40,
                        prop: 'delete',
                        justifyContent: 'center',
                    },
                ].filter((col) => {
                    return !col.hidden && (col.priceAdjustmentTabType === undefined || col.priceAdjustmentTabType === this.priceAdjustmentTabType);
                });//表单数据列表
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            onlyStock() {
                if ((this.isChainAdmin && this.isChain) || !this.isChain) {
                    return false;
                }
                return true;
            },
            isShowShopIcon() {
                return !this.overTitle && this.isChainAdmin && this.isAllSetPrice && !this.isAllClinics;
            },
            goodsAllTypesList() {
                return this.goodsAllTypes && this.goodsAllTypes.filter((item) => {
                    const {
                        goodsType, goodsSubType,
                    } = item;
                    // 调价规则是医保限价只暂示西药、中药颗粒、饮片、中成药
                    if (this.priceAdjustmentTabType === 3) {
                        return (
                            goodsType === GoodsTypeEnum.MEDICINE ||
                            (goodsType === GoodsTypeEnum.MATERIAL && goodsSubType === GoodsSubTypeEnum[goodsType].MedicalMaterials)
                        );
                    }
                    /// 其他规则过滤后勤材料和固定资产
                    return !(goodsType === GoodsTypeEnum.MATERIAL && (
                        goodsSubType === GoodsSubTypeEnum[goodsType].LogisticalMaterials ||
                        goodsSubType === GoodsSubTypeEnum[goodsType].FixedAssets
                    ));
                }) || [];
            },
            chainShopList() {
                if (!this.isChainAdmin) {
                    return this.currentClinic?.clinicName;
                }
                if (!this.isAllSetPrice && this.isChainAdmin) {
                    return '全部门店';
                }
                if (this.isAllClinics && this.isChainAdmin) {
                    return '总部';
                }
                return this.shopChainList;
            },
            newPriceTabType() { // 获取当前调价的状态
                const tabName = this.tabList.find((item) => {
                    return item.key === this.priceAdjustmentTabType;
                })?.name ?? '';
                // 手动输入、调整至医保限价、调整至挂网价直接展示名称
                if ([TabValueEnum.SetPrice, TabValueEnum.InsurancePrice, TabValueEnum.ListingPrice].includes(this.priceAdjustmentTabType)) {
                    return tabName;
                }
                return `${tabName} ${this.priceAdjustmentMode ? '+' : '-'} ${tabName} x ${Math.abs(this.priceAdjustmentRatio || 0)}%`;
            },
            isAllClinics() { // 是否全部门店都可以自主调价
                return this.goodsConfig?.subClinicPrice?.subSetPriceAllClinics;
            },
            shopChainList() {
                return this.shopList.join('，') || '全部门店';
            },
            isAllSetPrice() { // 是否允许门店自己调价
                return this.goodsConfig?.subClinicPrice?.subSetPrice;
            },
            maxPricePercent() { //总部设置上限
                return this.goodsConfig?.subClinicPrice?.maxPricePercent || 0;
            },
            minPricePercent() { //总部设置下限
                return this.goodsConfig?.subClinicPrice?.minPricePercent || 0;
            },
            pageParams() { // 获取分页数据
                const {
                    limit: pageSize, offset,
                } = this;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
        },
        async created() {
            this.orderMainNameText = this.isEyeglasses ? '眼镜' : this.viewDistributeConfig.Inventory.orderMainNameText;
            // 如果不允许自主定价
            if (this.isAllClinics) {
                this.shopList = ['暂无门店'];
            }
            if (this.isChainAdmin) {
                this.shopList = this.goodsConfigPriceAdjustmentNoPowerClinicsName;
            }
        },
        mounted() {
            this.$nextTick(() => {
                if (this.isFirstLookPriceAdjustment || !isNumber(this.priceAdjustmentRatio)) {
                    this.$store.dispatch('setFirstPriceAdjustment', false);
                    this.showSetTabDialog = true;
                }
                this.focusMedicineSearch();
                window.setTimeout(() => {
                    if (document.getElementById('text-over') && document.getElementById('text-over').offsetHeight <= 20) {
                        this.overTitle = true;
                    }
                }, 500);
            });
        },
        methods: {
            isNull,
            isNotNull,
            moneyDigit,
            paddingMoney,
            isSupportShebaoPay,
            getPriceLimit(row) {
                if (!isNull(row.priceLimit)) return row.priceLimit;
                return row.shebao?.priceLimit ?? '';
            },
            async pageTo(page) {
                this.offset = (page - 1) * this.limit;
                if (this.searchKeyHistoryGoodId) {
                    await this.fetchHistoryList(this.searchKeyHistoryGoodId);
                } else {
                    await this.fetchHistoryList();//
                }
            },
            // 零售价整装改变后计算当前行的数据
            async checkPackagePricePercent(item, index) {
                const priceCalculateItem = await this.pricesCalculate([item], Number(item.priceAdjustment));
                this.tableList[index].profit = priceCalculateItem?.[0]?.calProfitRat?.profitRat ?? '';
                this.tableList[index].chainPackagePriceFlag = true;
            },
            // 零售价拆零改变后
            async checkPricePercent(Item, index) {
                // 中药需要特殊处理，没有整包
                if (chineseMedicineType.includes(Item.typeId)) {
                    const priceCalculateItem = await this.pricesCalculate([Item], Number(Item.latestPriceAdjustment));
                    this.tableList[index].profit = priceCalculateItem?.[0]?.calProfitRat?.profitRat ?? '';
                }
                this.tableList[index].latestPurchasePriceFlag = true;
            },
            //同意更新价格
            async updatePriceSure() {
                try {
                    this.submitBtnLoading = true;
                    const { data } = await GoodsV3API.updatePriceAdjustment(this.consItemObject);
                    if (data.code === 200) {
                        this.$emit('updatePriceSuccess', this.tableList.length);
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.submitBtnLoading = false;
                }
            },
            // 设置批量调价参数
            async setUpdatePriceParams() {
                const consItemList = {
                    clinicId: this.currentClinic.clinicId || 0,
                    opType: this.priceAdjustmentTabType,
                    upPercent: Number(this.priceAdjustmentRatio),
                    sourceType: 1,
                    list: [],
                };
                this.priceDownMedicine = 0; // 校验数据
                this.priceNoRules = 0;
                for (let i = 0; i < this.tableList.length; i++) {
                    const consItem = {
                        goodsId: this.tableList[i].goodsId, //
                        keyId: `${i}`,
                        updatePackagePrice: {
                            'afterPrice': this.tableList[i].priceAdjustment || 0,
                            'beforePrice': this.tableList[i].chainPackagePrice,
                        },
                        updatePiecePrice: {
                            'afterPrice': this.tableList[i].latestPriceAdjustment || 0,
                            'beforePrice': this.tableList[i].latestPurchasePrice,
                        },
                        lastSupplierName: this.tableList[i].supplier,
                    };
                    if (chineseMedicineType.includes(this.tableList[i].typeId)) {
                        consItem.updatePackagePrice = consItem.updatePiecePrice;// 中药调价小包覆盖大包
                    }
                    if (Number(this.tableList[i].packageCostPrice)) {
                        consItem.packageCostPrice = this.tableList[i].packageCostPrice;
                    }
                    // 整包价格是否修改过
                    if (this.tableList[i].chainPackagePriceFlag) {
                        consItem.updatePackagePrice.opType = 0;
                        consItem.updatePackagePrice.upPercent = null;
                    } else {
                        consItem.updatePackagePrice.opType = this.tableList[i].chainPackagePriceInfo.opType;
                        consItem.updatePackagePrice.upPercent = Number(this.tableList[i].chainPackagePriceInfo.profit);
                    }
                    // 零售价格是否修改过
                    if (this.tableList[i].latestPurchasePriceFlag) {
                        consItem.updatePiecePrice.opType = 0;
                        consItem.updatePiecePrice.upPercent = null;
                    } else {
                        consItem.updatePiecePrice.opType = this.tableList[i].latestPurchasePriceInfo.opType;
                        consItem.updatePiecePrice.upPercent = Number(this.tableList[i].latestPurchasePriceInfo.profit);
                    }

                    if (this.tableList[i].chainPackagePriceInfo.opType === 2) { // 进价需要传实际进价
                        consItem.updatePackagePrice.beforePrice = this.tableList[i].chainPackagePriceInfo.beforePrice || 0;
                    }
                    if (this.tableList[i].latestPurchasePriceInfo.opType === 2) { // 进价拆零需要传实际进价
                        consItem.updatePiecePrice.beforePrice = this.tableList[i].latestPurchasePriceInfo.beforePrice || 0;
                    }
                    if (this.priceAdjustmentTabType === TabValueEnum.ListingPrice && this.tableList[i].listingPrice) {
                        consItem.listingPackagePrice = this.tableList[i].listingPrice;
                    }

                    if (Number(this.tableList[i].packageCostPrice) > Number(this.tableList[i].priceAdjustment) && !chineseMedicineType.includes(this.tableList[i].typeId)) {
                        this.priceDownMedicine += 1;
                    }


                    // 为中药应该不校验整的价格
                    if (chineseMedicineType.includes(this.tableList[i].typeId)) {
                        if ((Number(this.tableList[i].latestPurchasePrice) * 0.4) > Number(this.tableList[i].latestPriceAdjustment) ||
                            (Number(this.tableList[i].latestPurchasePrice) * 1.4) < Number(this.tableList[i].latestPriceAdjustment)) {
                            this.priceNoRules += 1;
                        }
                    // 如果不能拆零卖,不校验拆零价格
                    } else if (!this.tableList[i].dismounting) {
                        if ((Number(this.tableList[i].chainPackagePrice) * 0.4) > Number(this.tableList[i].priceAdjustment) ||
                            (Number(this.tableList[i].chainPackagePrice) * 1.4) < Number(this.tableList[i].priceAdjustment)) {
                            this.priceNoRules += 1;
                        }
                    } else {
                        // 超过原本价格百分之40%或者低于40%
                        if ((Number(this.tableList[i].chainPackagePrice) * 0.4) > Number(this.tableList[i].priceAdjustment) ||
                            (Number(this.tableList[i].chainPackagePrice) * 1.4) < Number(this.tableList[i].priceAdjustment) ||
                            (Number(this.tableList[i].latestPurchasePrice) * 0.4) > Number(this.tableList[i].latestPriceAdjustment) ||
                            (Number(this.tableList[i].latestPurchasePrice) * 1.4) < Number(this.tableList[i].latestPriceAdjustment)) {
                            this.priceNoRules += 1;
                        }
                    }

                    if (this.priceAdjustmentTabType === 1 || this.priceAdjustmentTabType === 2) {
                        consItem.updatePackagePrice.scaleType = this.priceAdjustmentScaleType;
                        consItem.updatePackagePrice.roundingMode = this.priceAdjustmentRoundingMode;
                        consItem.updatePiecePrice.scaleType = this.priceAdjustmentScaleType;
                        consItem.updatePiecePrice.roundingMode = this.priceAdjustmentRoundingMode;
                    }

                    consItemList.list.push(consItem);
                }

                if (this.isChainAdmin) {
                    consItemList.affectedClinicIdList = this.goodsConfigPriceAdjustmentNoPowerClinicsId;
                }
                if (this.priceAdjustmentTabType === 1 || this.priceAdjustmentTabType === 2) {
                    consItemList.scaleType = this.priceAdjustmentScaleType;
                    consItemList.roundingMode = this.priceAdjustmentRoundingMode;
                }
                this.consItemObject = consItemList;
                this.$nextTick(() => {
                    const content = [];
                    if (this.priceDownMedicine || this.priceNoRules) {
                        if (this.priceDownMedicine) {
                            content.push(`${this.priceDownMedicine}种${this.isEyeglasses ? '眼镜' : '药品'}的新售价低于进价`);
                        }
                        if (this.priceNoRules) {
                            content.push(`${this.priceNoRules}种${this.isEyeglasses ? '眼镜' : '药品'}的调价幅度超过当前售价的40%`);
                        }
                    }
                    const h = this.$createElement;
                    if (content.length) {
                        this.message = this.$confirm({
                            type: 'warn',
                            title: '调价确认',
                            size: 'small',
                            confirmText: '确认调价',
                            onConfirm: () => {
                                this.updatePriceSure();
                            },
                            content: h('abc-flex', {
                                attrs: {
                                    vertical: true,
                                    gap: 'small',
                                },
                            }, content.map((item) => {
                                return h('abc-text', {
                                    attrs: { size: 'mini' },
                                }, item);
                            })),
                            showIcon: false,
                        });
                    } else {
                        this.message = this.$modal({
                            type: 'warn',
                            preset: 'confirm',
                            title: '调价确认',
                            onConfirm: () => {
                                this.updatePriceSure();
                            },
                            content: `确认后调价单内${this.isEyeglasses ? '眼镜' : '药品'}售价将立刻生效`,
                            showIcon: false,
                        });
                    }
                });
            },
            // 批量更新价格
            async updatePrice() {
                this.$refs.priceAdjustmentForm.validate((val) => {
                    if (val) {
                        this.setUpdatePriceParams();
                    }
                });
            },
            // 聚焦到某一行的方法
            focusMedicineSearch() {
                $(this.$el).find('.entry-medicine').find('input').focus();
            },
            // 获取调价列表
            getAffectedClinicList(List) {
                let listText = ''; //
                if (List.length) {
                    listText = List.map((item) => {
                        return item.shortName || item.name;
                    }).join('，');
                }
                return listText;
            },
            // 打开历史详情
            openHistoryInfo(item) {
                this.orderId = item.id;
                this.showSetPriceAdjustmentHistoryDialog = true;
            },
            // 售价输入框错误样式校验
            isPackagePriceErrStyle(item) { //
                if (item.priceAdjustment === null || item.priceAdjustment === undefined || item.priceAdjustment === '') { //不存在则错误
                    return true;
                }
                const { goods } = item;
                const maxCount = Number(moneyDigit((Number(goods.chainPackagePrice) * this.maxPricePercent) / 100, 5));
                const minCount = Number(moneyDigit((Number(goods.chainPackagePrice) * this.minPricePercent) / 100, 5));
                if (this.isChain && !this.isChainAdmin && (Number(item.priceAdjustment) < minCount || Number(item.priceAdjustment) > maxCount)) {
                    return true;
                }
                return false;
            },
            // 零售价输入框错误样式校验
            isPriceErrStyle(item) { //
                if (item.latestPriceAdjustment === null || item.latestPriceAdjustment === undefined || item.latestPriceAdjustment === '') { //不存在则错误
                    return true;
                }
                const { goods } = item;
                const maxCount = Number(moneyDigit((Number(goods.chainPiecePrice) * this.maxPricePercent) / 100, 5));
                const minCount = Number(moneyDigit((Number(goods.chainPiecePrice) * this.minPricePercent) / 100, 5));
                if (this.isChain && !this.isChainAdmin && (Number(item.latestPriceAdjustment) < minCount || Number(item.latestPriceAdjustment) > maxCount)) {
                    return true;
                }
                return false;
            },
            // form表单校验
            isPackagePriceErr(item, index) { //
                if (item.priceAdjustment === null || item.priceAdjustment === undefined || item.priceAdjustment === '') { //不存在则错误
                    if (this.$refs[`isPackagePrice${index}`]) {
                        this.$refs[`isPackagePrice${index}`].validateMessage = '请输入调整后新售价';
                    }
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '请输入调整后新售价',
                        });
                    };
                }
                const { goods } = item;
                const maxCount = Number(moneyDigit((Number(goods.chainPackagePrice) * this.maxPricePercent) / 100, 5));
                const minCount = Number(moneyDigit((Number(goods.chainPackagePrice) * this.minPricePercent) / 100, 5));
                if (this.isChain && !this.isChainAdmin && (Number(item.priceAdjustment) < minCount || Number(item.priceAdjustment) > maxCount)) {
                    // 处理总部定价为0时，计算出药品的范围是0-0导致无法调价的情况。
                    if (Number(goods.chainPackagePrice)) {
                        console.log(minCount,maxCount);
                        if (this.$refs[`isPackagePrice${index}`]) {
                            this.$refs[`isPackagePrice${index}`].validateMessage = `总部允许的定价范围为${minCount}-${maxCount}`;
                        }
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: `总部允许的定价范围为${minCount}-${maxCount}`,
                            });
                        };
                    }
                }
                if (Number(item.priceAdjustment) === 0 && this.isNeedCheckSellPriceNotZero && isSupportShebaoPay(item.goods)) {
                    if (this.$refs[`isPackagePrice${index}`]) {
                        this.$refs[`isPackagePrice${index}`].validateMessage = '医保要求售价必填且不能为 0';
                    }
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '医保要求售价必填且不能为 0',
                        });
                    };
                }
                return (_, callback) => {
                    callback({
                        validate: true,
                    });
                };
            },
            // form表单校验
            isPriceErr(item, index) { // form表单校验
                if (item.latestPriceAdjustment === null || item.latestPriceAdjustment === undefined || item.latestPriceAdjustment === '') { //不存在则错误
                    if (this.$refs[`isPrice${index}`]) {
                        this.$refs[`isPrice${index}`].validateMessage = '请输入调整后新售价(零)';
                    }
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '请输入调整后新售价(零)',
                        });
                    };
                }
                const { goods } = item;
                const maxCount = Number(moneyDigit((Number(goods.chainPiecePrice) * this.maxPricePercent) / 100, 5));
                const minCount = Number(moneyDigit((Number(goods.chainPiecePrice) * this.minPricePercent) / 100, 5));
                if (this.isChain && !this.isChainAdmin && (Number(item.latestPriceAdjustment) < minCount || Number(item.latestPriceAdjustment) > maxCount)) {
                    // 处理总部定价为0时，计算出药品的范围是0-0导致无法调价的情况。
                    if (Number(goods.chainPiecePrice)) {
                        if (this.$refs[`isPrice${index}`]) {
                            this.$refs[`isPrice${index}`].validateMessage = `总部允许的定价范围为${minCount}-${maxCount}`;
                        }
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: `总部允许的定价范围为${minCount}-${maxCount}`,
                            });
                        };
                    }
                }
                if (isNotNull(item.latestPriceAdjustment) && Number(item.latestPriceAdjustment) === 0 && this.isNeedCheckSellPriceNotZero && isSupportShebaoPay(item.goods)) {
                    if (this.$refs[`isPrice${index}`]) {
                        this.$refs[`isPrice${index}`].validateMessage = '医保要求售价必填且不能为 0';
                    }
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '医保要求售价必填且不能为 0',
                        });
                    };
                }
                return (_, callback) => {
                    callback({
                        validate: true,
                    });
                };
            },
            //校验和文案
            isPackagePriceWarnText(item) {
                if (Number(item.packageCostPrice) > Number(item.priceAdjustment)) {
                    return '售价低于进价';
                }
                if (this.priceAdjustmentTabType === 1 && Number(item.chainPackagePrice) <= 0) {
                    return `该${this.isEyeglasses ? '眼镜' : '药品物资'}最近当前售价为0 请手动填写`;
                }
                if (this.priceAdjustmentTabType === 2 && item.goods.lastPackageCostPrice <= 0) {
                    return `该${this.isEyeglasses ? '眼镜' : '药品物资'}最近进价为0 请手动填写`;
                }
                if (this.priceAdjustmentTabType === 3) {
                    if (item.goods.shebaoPayMode === 2) return '当前设置为医保不允许支付';
                    if (item.goods.priceLimit) {
                        if (!item.goods.shebao?.nationalCode) {
                            return '未对医保码';
                        }
                    } else {
                        if (!item.goods.shebao?.priceLimit) {
                            return '医保未明确限价';
                        }
                    }
                }
                if (this.priceAdjustmentTabType === TabValueEnum.ListingPrice) {
                    if (!item.goods.listingPrice) {
                        return '医保未明确挂网价';
                    }
                }
                if (!chineseMedicineType.includes(item.typeId) && (Number(item.chainPackagePrice) * 0.4) > Number(item.priceAdjustment)) {
                    return '新售价(整装)低于当前售价40%，请检查后执行调价';
                }
                if (!chineseMedicineType.includes(item.typeId) && (Number(item.chainPackagePrice) * 1.4) < Number(item.priceAdjustment)) {
                    return '新售价(整装)高于当前售价40%，请检查后执行调价';
                }
                return '';
            },
            //警告文案和校验
            isPriceWarnText(item) {
                if (this.priceAdjustmentTabType === 1 && Number(item.chainPackagePrice) <= 0) {
                    return `该${this.isEyeglasses ? '眼镜' : '药品物资'}最近当前售价为0 请手动填写`;
                }
                if (this.priceAdjustmentTabType === 2 && item.goods.lastPackageCostPrice <= 0) {
                    return `该${this.isEyeglasses ? '眼镜' : '药品物资'}最近进价为0 请手动填写`;
                }
                if (this.priceAdjustmentTabType === 3) {
                    if (item.goods.shebaoPayMode === 2) return '当前设置为医保不允许支付';
                    if (item.goods.priceLimit) {
                        if (!item.goods.shebao?.nationalCode) {
                            return '未对医保码';
                        }
                    } else {
                        if (!item.goods.shebao?.priceLimit) {
                            return '医保未明确限价';
                        }
                    }
                }
                if (this.priceAdjustmentTabType === TabValueEnum.ListingPrice) {
                    if (!item.goods.listingPrice) {
                        return '医保未明确挂网价';
                    }
                }
                if (item.goods.dismounting && (Number(item.latestPurchasePrice) * 0.4) > Number(item.latestPriceAdjustment)) {
                    return '新售价(拆零)低于当前售价40%，请检查后执行调价';
                }
                if (item.goods.dismounting && (Number(item.latestPurchasePrice) * 1.4) < Number(item.latestPriceAdjustment)) {
                    return '新售价(拆零)高于当前售价40%，请检查后执行调价';
                }
                return '';
            },
            // 获取当前商品名称
            goodsFullName(goods) {
                if (!goods) return '';
                let name;
                switch (goods.type) {
                    case 1:
                        name = goods.medicineCadn;
                        if (goods.name) {
                            if (name) {
                                name += `（${goods.name}）`;
                            } else {
                                name = `${goods.name}`;
                            }
                        }
                        return name;
                    default:
                        return goods.name;
                }
            },

            //批量算费接口
            async pricesCalculate(ItemList, HandPrice) {
                const params = {
                    list: [],
                };
                for (let i = 0; i < ItemList.length; i++) {
                    const item = ItemList[i];
                    const constParams = {
                        calProfitRat: { // 毛利率计算 手动改价或者手动输入传这个包
                            'packageCostPrice': item.packageCostPrice,
                        },
                        goodsId: item.goodsId,
                    };
                    const isChineseGoods = isChineseMedicine(item.goods);

                    const upPercent = this.priceAdjustmentTabType !== 3 ? +this.priceAdjustmentRatio : 0;

                    if (this.priceAdjustmentTabType && !HandPrice && item.latestPurchasePrice) { //
                        constParams.calPiecePrice = { // 小包价格(零售)
                            'beforePrice': isChineseGoods && this.priceAdjustmentTabType === 3 ? (item.priceLimit || item.latestPurchasePrice) : item.latestPurchasePrice,
                            upPercent,
                        };
                    }
                    if (this.priceAdjustmentTabType && !HandPrice) {
                        // 计算类型 1 按piecePrice /packagePrice 算 ；2 按packagePrice/pieceNum算 ;3只算利润率；4医保限价计算 6挂网价计算
                        constParams.calType = this.priceAdjustmentTabType === 3 ? 4 : this.priceAdjustmentTabType;
                        constParams.calPackagePrice = { // 大包价格(整售)
                            'beforePrice': this.priceAdjustmentTabType === TabValueEnum.ListingPrice ?
                                (item.listingPrice || item.chainPackagePrice) :
                                this.priceAdjustmentTabType === 3 ? (item.priceLimit || item.chainPackagePrice) : item.chainPackagePrice,
                            upPercent,
                        };
                    } else {
                        constParams.calType = 3;
                        // 手输如果手填了价格那么用已经填写的价格做计算，否则用原本的价格
                        constParams.calProfitRat.packagePrice = HandPrice;
                    }
                    if ([TabValueEnum.LastPrice, TabValueEnum.InsurancePrice, TabValueEnum.ListingPrice].includes(this.priceAdjustmentTabType)) {
                        constParams.pieceNum = item.pieceNum;
                    }
                    if (this.priceAdjustmentTabType === 1 || this.priceAdjustmentTabType === 2) {
                        constParams.scaleType = this.priceAdjustmentScaleType;
                        constParams.roundingMode = this.priceAdjustmentRoundingMode;
                    }
                    params.list.push(constParams);
                }
                const { data } = await GoodsV3API.pricesCalculate(params);
                return data.list;
            },
            // 切换了批量调价的模式后，除非是手输否则 价格需要重新计算 批量算费
            async setPriceList({
                tabCheck,
                radio,
            } = {}) {
                if ((tabCheck) && this.tableList.length) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '规则已调整，调价单的新售价将全部更新',
                        onConfirm: async () => {
                            this.showSetTabDialog = false;
                            const costItemList = Clone(this.tableList);
                            for (let i = 0; i < costItemList.length; i++) {
                                costItemList[i].priceAdjustment = null;
                                costItemList[i].latestPriceAdjustment = null;
                                costItemList[i].profit = 0;
                                costItemList[i].chainPackagePriceFlag = false;//是否手动修改
                                costItemList[i].latestPurchasePriceFlag = false;
                            }
                            const priceCalculateItem = await this.pricesCalculate(costItemList);
                            const falseList = [];//这是失败列表

                            for (let i = 0; i < priceCalculateItem.length; i++) {
                                const {
                                    calResult, calPackagePrice, calPiecePrice, calProfitRat,
                                } = priceCalculateItem[i];
                                if (!calResult) {//calResult为1 表示失败
                                    costItemList[i].priceAdjustment = calPackagePrice.afterPrice;

                                    costItemList[i].profit = calProfitRat.profitRat;
                                    costItemList[i].chainPackagePriceInfo = {
                                        opType: tabCheck,//默认手输入
                                        profit: radio,//默认0
                                        beforePrice: calPackagePrice.beforePrice,
                                    };
                                    costItemList[i].latestPurchasePriceInfo = {
                                        opType: tabCheck,//默认手输入
                                        profit: radio,//默认0
                                    };
                                    // 判断零售是否存在 存在才修改值
                                    if (calPiecePrice) {
                                        costItemList[i].latestPriceAdjustment = calPiecePrice.afterPrice;
                                        costItemList[i].latestPurchasePriceInfo.beforePrice = calPiecePrice.beforePrice;
                                    } else {
                                        costItemList[i].latestPriceAdjustment = 0;
                                    }
                                } else {
                                    falseList.push(i);
                                }
                            }
                            this.$nextTick(() => {
                                this.tableList = Clone(costItemList);
                                if (falseList.length) { //失败列表有数据则高亮
                                    this.$nextTick(() => {
                                        this.focusLine(falseList[0]);
                                    });
                                }
                            });
                        },
                    });

                } else {
                    this.showSetTabDialog = false;
                }

            },
            //批量添加药物/商品
            async setGoodsList(goodsList) {
                this.defaultLoading = true;
                const costItemList = [];
                const goodsIdList = [];
                for (let i = 0; i < goodsList.length; i++) {
                    const goods = goodsList[i];
                    if (!this.goodsIdList.includes(goods.id) && goods.priceType !== PriceType.PKG_PRICE_MAKEUP) {
                        const goodsLine = this.createGoodsLine(goods);
                        goodsIdList.push(goods.id);
                        costItemList.push(goodsLine);
                    }
                }
                if (costItemList.length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: `选择的${this.isEyeglasses ? '眼镜' : '药品/物资'}已存在于调价表中`,
                    });
                    this.defaultLoading = false;
                    return false;
                }
                const falseList = [];
                try { //任何步骤异常都不再执行
                    if (this.priceAdjustmentTabType) { //非手动
                        const priceCalculateItem = await this.pricesCalculate(costItemList);
                        for (let i = 0; i < priceCalculateItem.length; i++) {
                            const costItem = costItemList[i];
                            const {
                                calPackagePrice, calPiecePrice, calProfitRat, calResult,
                            } = priceCalculateItem[i];
                            // 判断是否正确
                            if (!calResult) {
                                costItem.priceAdjustment = calPackagePrice.afterPrice;
                                costItem.profit = calProfitRat.profitRat;
                                costItem.chainPackagePriceInfo = {
                                    opType: this.priceAdjustmentTabType,//默认手输入
                                    profit: this.priceAdjustmentRatio,//默认0
                                    beforePrice: calPackagePrice.beforePrice,
                                };
                                costItem.latestPurchasePriceInfo = {
                                    opType: this.priceAdjustmentTabType,//默认手输入
                                    profit: this.priceAdjustmentRatio,//默认0
                                };
                                if (calPiecePrice) {
                                    costItem.latestPriceAdjustment = calPiecePrice.afterPrice;
                                    costItem.latestPurchasePriceInfo.beforePrice = calPiecePrice.beforePrice;
                                } else {
                                    costItem.latestPriceAdjustment = 0;
                                }

                            } else {
                                falseList.push(costItem.goodsId);
                            }
                        }
                    }
                    const len = this.tableList.length;
                    this.tableList = this.tableList.concat(costItemList);
                    this.goodsIdList = this.goodsIdList.concat(goodsIdList);
                    // 批量添加的逻辑，锁定第一行
                    this.$nextTick(() => {
                        if (costItemList && falseList.length === 0) {
                            this.focusLine(len);
                        } else {
                            const i = this.getArrayIndex(this.tableList, falseList[0]);
                            if (i >= 0) {
                                // 需要抛提示
                                this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: `${falseList.length}种商品调价未生效，请手动调整`,
                                });
                                this.focusLine(i);
                            }
                        }
                    });
                } catch (e) {
                    console.log(e);
                } finally {
                    this.defaultLoading = false;
                }
            },
            // 拉取调价历史数据
            async fetchHistoryList(goodsId) {
                this.historyLoading = true;
                const params = {}; //
                if (!this.isChainAdmin) {
                    params.clinicId = this.currentClinic.clinicId;
                }
                if (goodsId) {
                    params.goodsId = goodsId;
                }
                params.limit = this.limit;
                params.offset = this.offset;
                let hisData = null;
                try {
                    const { data } = await GoodsV3API.fecthPriceAdjustment(params);
                    hisData = data;
                } catch (e) {
                    console.log(e);
                } finally {
                    hisData.rows = hisData.rows || [];
                    this.total = hisData.total;
                    this.historyList = hisData.rows;
                    this.historyLoading = false;
                }
            },
            getMedicalType(item) {
                const goodsAllType = this.goodsAllTypesList.find((i) => {
                    return `${i.id}` === `${item.typeId}`;
                });
                return (goodsAllType && goodsAllType.name) || '-';
            },
            getMedicalSecondary(item) {
                let resultInfo = '-';
                try {
                    if (this.goodsAllTypesList?.length && item?.typeId) {
                        const contItem = this.goodsAllTypesList.find((i) => {
                            return `${i.id}` === `${item.typeId}`;
                        }) || {};
                        if (contItem && contItem.customTypes) {
                            const custom = contItem.customTypes.find((i) => {
                                return `${i.id}` === `${item.customTypeId}`;
                            });
                            resultInfo = (custom && custom.name) || '-';
                        }
                    }
                } catch (e) {
                    console.log(e);
                }
                return resultInfo;
            },
            clearSearch() {
                this.searchKeyHistory = '';
                this.searchKeyHistoryGoodId = '';
                this.fetchHistoryList();
            },
            // 删除列表中某个数据
            deleteTable(item) {
                this.tableList = this.tableList.filter((i) => {
                    return i.goodsId !== item.goodsId;
                });
                this.goodsIdList = this.goodsIdList.filter((i) => {
                    return i !== item.goodsId;
                });
            },
            // 选择药品/物资
            selectHistoryGoods(goods) {
                this.searchKeyHistory = this.goodsFullName(goods);
                this.searchKeyHistoryGoodId = goods.goodsId || goods.id;
                this.offset = 0;
                this.fetchHistoryList(goods.goodsId || goods.id);
            },
            // 导出
            async exportExcel() {
                const params = {}; //
                if (!this.isChainAdmin) {
                    params.clinicId = this.currentClinic.clinicId;
                }
                if (this.searchKeyHistoryGoodId) {
                    params.goodsId = this.searchKeyHistoryGoodId;
                }
                // params.limit = this.limit;
                // params.offset = this.offset;
                await GoodsV3API.exportPriceAdjustment(params).catch((e) => {
                    const { message } = e;
                    this.$Toast({
                        message,
                        type: 'error',
                        duration: 1000,
                    });
                });
            },
            getArrayIndex(List, item) {
                let i = List.length;
                while (i--) {
                    if (List[i].goodsId === item) {
                        return i;
                    }
                }
                return -1;
            },
            // 选中药物后的回执 单个药物添加
            async selectGoods(goods) {
                this.searchKey = '';
                if (this.goodsIdList.includes(goods.id)) {
                    const i = await this.getArrayIndex(this.tableList, goods.id);
                    if (i >= 0) {
                        this.$nextTick(() => {
                            this.focusLine(i);
                        });
                    }
                    return false;
                }
                // 为了医保调价，单个药品单独查询 shebao信息
                const { data } = await GoodsAPI.goods(goods.id, this.currentClinic.id, { withShebaoCode: 1 });
                Object.assign(goods, data);

                const goodsLine = this.createGoodsLine(goods);
                if (this.priceAdjustmentTabType) { //非手动
                    const priceCalculateItem = await this.pricesCalculate([goodsLine]); //一行单个药物调价
                    const {
                        calResult, calPackagePrice, calProfitRat, calPiecePrice,
                    } = priceCalculateItem[0];
                    if (!calResult) { // 0正确 1错误
                        goodsLine.priceAdjustment = calPackagePrice.afterPrice;
                        goodsLine.profit = calProfitRat.profitRat;
                        goodsLine.chainPackagePriceInfo = {
                            opType: this.priceAdjustmentTabType,//默认手输入
                            profit: this.priceAdjustmentRatio,//默认0
                            beforePrice: calPackagePrice.beforePrice, // 存储调价前价格
                        };
                        goodsLine.latestPurchasePriceInfo = {
                            opType: this.priceAdjustmentTabType,//默认手输入
                            profit: this.priceAdjustmentRatio,//默认0
                            beforePrice: 0,
                        };
                        // 判断零售价是否存在
                        if (calPiecePrice) {
                            goodsLine.latestPriceAdjustment = calPiecePrice.afterPrice;
                            goodsLine.latestPurchasePriceInfo.beforePrice = calPiecePrice.beforePrice;//存储调价前零售价格
                        } else {
                            goodsLine.latestPriceAdjustment = 0;
                        }
                    }
                }
                // 手动的不计算毛利率，只在修改值的时候计算毛利率
                this.tableList = this.tableList.concat(goodsLine);
                this.goodsIdList.push(goods.id);
                // 根据新增的逻辑，非手动算费规律下添加药品仍然聚焦搜索弹窗，手动算费下直接聚焦当前药品第一个输入框
                if (this.priceAdjustmentTabType) {
                    this.$nextTick(() => {
                        // 产品体验-保证新加药品可见
                        this.$refs.tableV3Ref.scrollTo(99999);
                        this.focusMedicineSearch();
                    });
                } else {
                    this.$nextTick(() => {
                        window.setTimeout(() => {
                            this.focusLine(this.tableList.length - 1);
                        }, 50); //
                    });
                }
            },
            // 聚焦设置的某行
            focusLine(value) {
                const Line = $('.v3-goods-table').find('.table-content-li');
                const $inputs = $(Line[value]).find('input');
                $inputs.each((index, element) => {
                    if (index === 0) {
                        element.focus();//测试
                    }
                });
            },
            modifySet() {
                this.showSetTabDialog = true;
            },
            closeDialog() {
                if (this.tableList.length) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '当前列表内已经添加调价数据,是否关闭？',
                        onConfirm: async () => {
                            this.showDialog = false;
                        },
                    });
                } else {
                    this.showDialog = false;
                }
            },
            handleMounted(data) {
                this.limit = data.paginationLimit || 10;

                if (this.searchKeyHistoryGoodId) {
                    this.fetchHistoryList(this.searchKeyHistoryGoodId);
                } else {
                    this.fetchHistoryList();
                }
            },
            handleTabChange() {
                this.$nextTick(() => {
                    this.$refs['abc-dialog']?.updateDialogHeight();
                });
            },
            createGoodsLine(goods) {
                return {
                    goodsId: goods.id,
                    pieceNum: goods.pieceNum,
                    name: goods.medicineCadn || goods.name,
                    type: goods.type,
                    typeId: goods.typeId,
                    customTypeId: goods.customTypeId,
                    secondaryClassification: goods.subType,
                    manufacturer: goods.manufacturer,
                    specifications: goodsSpec(goods),
                    supplier: goods.lastStockInOrderSupplier,
                    packageCostPrice: goods.packageCostPrice,
                    priceLimit: goods.priceLimit,
                    chainPackagePrice: goods.packagePrice,// 当前售价-整
                    latestPurchasePrice: goods.piecePrice,// 当前售价-零
                    chainPackagePriceFlag: false,//是否手动修改整包
                    latestPurchasePriceFlag: false,//是否手动修改零售
                    listingPrice: goods.listingPrice,// 挂网价
                    chainPackagePriceInfo: {
                        opType: 0,//默认手输入
                        profit: 0,//默认0
                        beforePrice: 0,
                    },
                    latestPurchasePriceInfo: {
                        opType: 0,//默认手输入
                        profit: 0,//默认0
                        beforePrice: 0,
                    },
                    priceAdjustment: null,// 新售价-整
                    latestPriceAdjustment: null,// 新售价-整
                    profit: 0,
                    goods,
                };
            },
        },
    };
</script>

<style lang="scss" scoped>
@import 'src/styles/theme';

.price-adjustment-dialog {
    max-width: 1280px !important;
    margin: 0 auto;

    .price-tabs {
        display: flex;
        height: 46px;
        font-size: 16px;
        font-weight: bold;
        line-height: 46px;
        color: #687481;

        .tab {
            display: inline-block;
            height: 46px;
            font-size: 16px;
            line-height: 46px;
            text-align: center;
            cursor: pointer;

            &:first-child {
                margin-right: 40px;
            }
        }

        .tab-active {
            color: $theme1;
            border-bottom: 3px solid $theme2;
        }
    }

    .price-adjustment-dialog-nel {
        width: 100%;
        height: 100%;

        .price-adjustment-dialog-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 48px;
            padding: 0 16px;
            background: #fafbfc;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);

            .top-left {
                display: flex;
                align-items: center;
                height: 48px;

                .price-title-text {
                    display: flex;
                    align-items: center;
                    margin-left: 64px;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 20px;

                    &:first-child {
                        margin-left: 0;
                    }

                    .text-key {
                        display: inline-block;
                        padding-right: 6px;
                        color: $T2;
                    }

                    #text-over {
                        display: inline-block;
                        max-width: 200px;
                        height: auto;
                        color: $S1;
                    }

                    .text-label {
                        display: inline-block;
                        max-width: 150px;
                        overflow: hidden;
                        color: $S1;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .text-mark {
                        margin-left: 6px;
                        color: $P3;
                        cursor: pointer;
                    }
                }
            }
        }

        .price-adjustment-goods-table {
            margin-top: 16px;

            .goods-table-name {
                width: 100%;

                span {
                    display: block;
                    max-width: 280px;
                    height: 20px;
                    margin-top: 4px;
                    overflow: hidden;
                    line-height: 20px;
                    text-align: left;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                    &:last-child {
                        height: 16px;
                        margin-top: 2px;
                        font-size: 12px;
                        line-height: 16px;
                        color: #7a8794;
                    }
                }
            }

            .price-input {
                width: 100%;

                ::v-deep .abc-input__inner {
                    width: 100% !important;
                    height: 44px;
                    text-align: right;
                    border: none;
                    border-radius: 0 !important;

                    &:focus {
                        background-color: $S2;
                        border: 1px solid #0270c9 !important;
                    }
                }
            }

            .latest-price-adjustment-no-nel {
                width: 104px;
                padding-right: 4px;
                text-align: right;
                background: #fafbfc;
            }

            .delete-button {
                position: relative;
                display: inline-block;
                width: 20px;
                height: 20px;

                .button-icon-del {
                    position: absolute;
                    left: 5px;
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    line-height: 19px;
                    color: #aab4bf;
                    text-align: center;
                    border-radius: 50%;

                    &:hover {
                        background-color: $B4;
                    }
                }
            }

            .price_input_error {
                ::v-deep .abc-input__inner {
                    background-color: #fef7e9;
                    border: 1px solid #ff9933 !important;
                }
            }

            .price_input_warn {
                ::v-deep .abc-input__inner {
                    color: #ff9933 !important;
                }
            }

            .price-adjustment-medicine {
                ::v-deep .abc-input__inner {
                    height: 32px;
                }
            }
        }

        &-history {
            width: 100%;
            height: 100%;

            .price-adjustment-dialog-nel-history-top {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 30px;
                background: $S2;
            }

            .price-adjustment-goods-table-history {
                margin-top: 16px;
            }
        }
    }
}

.price-adjustment-sure-dialog {
    .price-adjustment-sure-box {
        text-align: left;

        .sure-box-1 {
            font-size: 16px;
            font-weight: bold;
        }

        .sure-box-2 {
            padding-left: 20px;
            font-size: 13px;
        }

        .sure-box-3 {
            padding-left: 20px;
            font-size: 13px;
            color: $P1;
        }
    }
}
</style>
