<template>
    <abc-dialog
        v-if="showSetPriceAdjustmentHistoryDialog"
        v-model="showSetPriceAdjustmentHistoryDialog"
        title="调价详情"
        custom-class="set-price-history-info-dialog"
        :before-close="closeDialog"
        append-to-body
        :auto-focus="false"
        content-styles="width: 1200px;padding: 16px 24px 24px 24px;min-height:400px;max-height:600px;overflow-y:scroll;"
    >
        <div class="set-price-history-info-dialog-box">
            <div class="set-price-history-info-dialog-box-top">
                <goods-auto-complete
                    class="abc-autocomplete-search"
                    :placeholder="isEyeglasses ? '眼镜名称/条形码' : '药品名称/条形码'"
                    :with-stock="false"
                    :only-stock="onlyStock"
                    :pharmacy-no="pharmacyNo"
                    :clear-search-key="false"
                    :clinic-id="isChainAdmin ? '' : currentClinic?.clinicId"
                    :search.sync="searchKeyHistory"
                    enable-local-search
                    @selectGoods="selectHistoryGoods"
                >
                    <abc-search-icon slot="prepend"></abc-search-icon>
                    <div slot="append" class="search-icon" @click="clearSearch">
                        <i v-if="searchKeyHistory" class="iconfont cis-icon-cross_small"></i>
                    </div>
                </goods-auto-complete>
            </div>
            <div class="price-adjustment-history-dialog-top">
                <div class="top-left">
                    <div class="price-title-text">
                        <span class="text-key">调价影响门店:</span>
                        <span class="text-label" style="max-width: 200px;">
                            <span v-if="hisInfo.affectedClinicList" :title="getAffectedClinicList(hisInfo.affectedClinicList)">
                                {{ getAffectedClinicList(hisInfo.affectedClinicList) }}
                            </span>
                            <span v-else>
                                {{ (hisInfo.modifyClinic && hisInfo.modifyClinic.name) || '-' }}
                            </span>
                        </span>
                    </div>
                    <div class="price-title-text">
                        <span class="text-key">新售价:</span>
                        <span class="text-label" style="max-width: 300px;">{{ priceAdjustmentModel }}</span>
                    </div>
                </div>
            </div>
            <goods-table
                custom-class="price-adjustment-history-goods-table-history"
                :is-show-line-hover="true"
                :table-list="historyList"
                :config="historyGoodsHeaderConfig"
                :loading="historyLoading"
            >
                <template slot="name" slot-scope="{ item }">
                    <div class="goods-table-name">
                        <span :title="goodsFullName(item.goods)">{{ goodsFullName(item.goods) }}</span>
                        <span>{{ goodsSpecSet(item.goods) }}&nbsp;&nbsp;{{ item.goods.manufacturer ? item.goods.manufacturer : '' }}</span>
                    </div>
                </template>
                <template slot="type" slot-scope="{ item }">
                    <span :title="getMedicalType(item.goods)">{{ getMedicalType(item.goods) }}</span>
                </template>
                <template slot="supplier" slot-scope="{ item }">
                    <span :title="item.lastSupplierName">{{ item.lastSupplierName ? item.lastSupplierName : '-' }}</span>
                </template>
                <template slot="priceAdjustmentBefore" slot-scope="{ item }">
                    <span :title="item.beforePackagePrice">{{ item.beforePackagePrice ? item.beforePackagePrice : '-' }}</span>
                </template>
                <template slot="latestPriceAdjustmentBefore" slot-scope="{ item }">
                    <span :title="item.beforePiecePrice">{{ item.beforePiecePrice ? item.beforePiecePrice : '-' }}</span>
                </template>
                <template slot="priceAdjustment" slot-scope="{ item }">
                    <span :title="item.afterPackagePrice">{{ item.afterPackagePrice ? item.afterPackagePrice : '-' }}</span>
                </template>
                <template slot="latestPriceAdjustment" slot-scope="{ item }">
                    <span :title="item.afterPiecePrice">{{ item.afterPiecePrice ? item.afterPiecePrice : '-' }}</span>
                </template>
                <template slot="profit" slot-scope="{ item }">
                    <span :title="item.profitRat">{{ item.profitRat ? `${item.profitRat}%` : '-' }}</span>
                </template>
            </goods-table>
        </div>
    </abc-dialog>
</template>

<script>
    import GoodsV3API from 'api/goods/index-v3.js';
    import goodsTable from 'views/inventory/common/goods-table-list/goods-table';
    import { goodsSpec } from '@/filters';
    import { mapGetters } from 'vuex';
    import { tabList } from './set-price-adjustment-dialog.vue';

    const GoodsAutoComplete = () => import('views/inventory/common/goods-auto-complete');

    export default {
        name: 'SetPriceAdjustmentHistoryDialog',
        components: {
            GoodsAutoComplete,
            goodsTable,
        }, // 12
        props: {
            value: Boolean,
            onlyStock: Boolean,
            isEyeglasses: Boolean,
            pharmacyNo: Number,
            orderId: [String,Number],
            goodsAllTypesList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                searchKeyHistory: '',
                newPriceTabType: '-',
                historyList: [],
                hisInfo: {},
                historyGoodsHeaderConfig: [
                    {
                        name: this.isEyeglasses ? '眼镜名称' : '药品名称',
                        titleStyle: {
                            textAlign: 'left',
                            padding: '0 6px',
                        },
                        width: 350,
                        props: 'name',
                        bodyStyle: {
                            textAlign: 'left',
                            padding: '0 4px',
                        },
                        isShowHover: false,
                        isHidden: true,
                    },
                    {
                        name: '类型',
                        titleStyle: {
                            textAlign: 'left',
                            padding: '0 6px',
                        },
                        width: 94,
                        props: 'type',
                        bodyStyle: {
                            textAlign: 'left',
                            padding: '0 4px',
                        },
                        isShowHover: false,
                        isHidden: true,
                    },
                    {
                        name: '最近供应商',
                        titleStyle: {
                            textAlign: 'left',
                            padding: '0 6px',
                        },
                        width: 128,
                        props: 'supplier',
                        bodyStyle: {
                            textAlign: 'left',
                            padding: '0 4px',
                        },
                        isShowHover: false,
                        isHidden: true,
                    },
                    {
                        name: '原售价(整)',
                        titleStyle: {
                            textAlign: 'right',
                            padding: '0 6px',
                        },
                        width: 140,
                        props: 'priceAdjustmentBefore',
                        bodyStyle: {
                            textAlign: 'right',
                            padding: '0 4px',
                        },
                        isShowHover: true,
                    },
                    {
                        name: '原售价(零)',
                        titleStyle: {
                            textAlign: 'right',
                            padding: '0 6px',
                        },
                        width: 140,
                        props: 'latestPriceAdjustmentBefore',
                        bodyStyle: {
                            textAlign: 'right',
                            padding: '0 4px',
                        },
                        isShowHover: true,
                    },
                    {
                        name: '新售价(整)',
                        titleStyle: {
                            textAlign: 'right',
                            padding: '0 6px',
                        },
                        width: 140,
                        props: 'priceAdjustment',
                        bodyStyle: {
                            textAlign: 'right',
                            padding: '0 4px',
                        },
                        isShowHover: true,
                    },
                    {
                        name: '新售价(零)',
                        titleStyle: {
                            textAlign: 'right',
                            padding: '0 6px',
                        },
                        width: 140,
                        props: 'latestPriceAdjustment',
                        bodyStyle: {
                            textAlign: 'right',
                            padding: '0 4px',
                        },
                        isShowHover: true,
                    },
                    {
                        name: '调价后毛利',
                        titleStyle: {
                            textAlign: 'right',
                            padding: '0 6px',
                        },
                        width: 114,
                        props: 'profit',
                        bodyStyle: {
                            textAlign: 'right',
                            padding: '0 4px',
                        },
                        isShowHover: false,
                        isHidden: true,
                    },
                ],
                tabList, // 调价模式
                historyLoading: false,
            };
        },
        computed: {
            showSetPriceAdjustmentHistoryDialog: { //展示设置规则的弹窗
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            priceAdjustmentModel() {
                if (this.hisInfo && this.hisInfo.opType) {
                    const priceAdjustmentTypeName = (this.tabList.find((item) => {return item.key === this.hisInfo.opType;}).name);
                    const upPercent = this.hisInfo.upPercent || 0;
                    if (this.hisInfo.opType === 3) return priceAdjustmentTypeName;
                    // 根据upPercent值是正数还是负数，判断是上调价格还是下调价格
                    return `${priceAdjustmentTypeName} ${upPercent > 0 ? '+' : '-'} ${priceAdjustmentTypeName} x ${Math.abs(upPercent)}%` || '手动输入';
                }
                return (this.hisInfo.opType && this.tabList.find((item) => {return item.key === this.hisInfo.opType;}).name) || '手动输入';
            },
            ...mapGetters([
                'isChainAdmin', //是连锁总部
                'currentClinic',
            ]),
        },
        created() {
            this.fetchHistoryList();
        },
        methods: {
            goodsFullName(goods) {
                if (!goods) return '';
                let name;
                switch (goods.type) {
                    case 1:
                        name = goods.medicineCadn;
                        if (goods.name) {
                            if (name) {
                                name += `（${goods.name}）`;
                            } else {
                                name = `${goods.name}`;
                            }
                        }
                        return name;
                    default:
                        return goods.name;
                }
            },
            goodsSpecSet(item) {
                return goodsSpec(item);
            },
            getMedicalType(item) {
                const goodsAllType = this.goodsAllTypesList.find((i) => {return String(i.id) === String(item.typeId);});
                return (goodsAllType && goodsAllType.name) || '-';
            },
            getAffectedClinicList(List) {
                let listText = ''; //
                if (!List) {
                    return listText;
                }
                listText = List.map((item) => {return item.name;}).join(',');
                return listText;
            },
            clearSearch() {
                this.searchKeyHistory = '';
                this.fetchHistoryList();
            },
            selectHistoryGoods(goods) {
                this.searchKeyHistory = goods.medicineCadn;
                this.fetchHistoryList(goods.goodsId || goods.id);
            },
            closeDialog() {
                this.showSetPriceAdjustmentHistoryDialog = false;
            },
            // 查询历史详情
            async fetchHistoryList(goodsId = '') {
                this.historyLoading = true;
                const params = {
                    orderId: this.orderId,
                };
                if (goodsId) {
                    params.goodsId = goodsId;
                }
                let historyInfo = null;
                try {
                    const { data } = await GoodsV3API.getHistoryInfo(params);
                    historyInfo = data;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.hisInfo = historyInfo;
                    this.historyList = historyInfo.list || [];
                    this.historyLoading = false;
                }


            },
        },
    };
</script>

<style lang="scss" scoped>
@import 'src/styles/theme';

.set-price-history-info-dialog {
    width: 1200px;
    margin: 0 auto;

    .set-price-history-info-dialog-box {
        width: 100%;
        height: auto;

        .set-price-history-info-dialog-box-top {
            display: flex;
            align-items: center;
            height: 30px;
            background: $S2;
        }

        .price-adjustment-history-dialog-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 48px;
            padding: 0 16px;
            margin-top: 16px;
            background: #fafbfc;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);

            .top-left {
                display: flex;
                align-items: center;
                height: 48px;

                .price-title-text {
                    display: flex;
                    align-items: center;
                    margin-left: 64px;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 20px;

                    &:first-child {
                        margin-left: 0;
                    }

                    .text-key {
                        display: inline-block;
                        padding-right: 6px;
                        color: $T2;
                    }

                    .text-label {
                        display: inline-block;
                        max-width: 150px;
                        overflow: hidden;
                        color: $S1;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .text-mark {
                        margin-left: 6px;
                        color: #dadbe0;
                        cursor: pointer;
                    }
                }
            }
        }

        .price-adjustment-history-goods-table-history {
            margin-top: 16px;

            .goods-table-name {
                width: 100%;

                span {
                    display: block;
                    max-width: 280px;
                    height: 20px;
                    margin-top: 4px;
                    overflow: hidden;
                    line-height: 20px;
                    text-align: left;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                    &:last-child {
                        height: 16px;
                        margin-top: 2px;
                        font-size: 12px;
                        line-height: 16px;
                        color: #7a8794;
                    }
                }
            }
        }
    }
}
</style>
