@import 'src/styles/mixin';

// 以下代码都是 goods-descriptions的样式
.v3-goods-descriptions {
    box-sizing: border-box;
    width: 100%;
    //min-height: 48px;
    margin: 0 0 16px;
    background: #ffffff;
    border: 1px solid $P1;
    border-radius: var(--abc-border-radius-small);

    &.is-disabled {
        cursor: default;
        background-color: #f9fafc;

        .abc-input__inner {
            background-color: transparent !important;
        }
    }

    .abc-form-item {
        margin: 0 !important;
    }

    .popper-content {
        box-sizing: border-box;
        //justify-content: space-between;
        height: 100%;
        min-height: 46px;
        padding: 13px 12px;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        letter-spacing: 0;

        .descriptions-item {
            display: flex;
            align-items: center;

            .label {
                font-size: 14px;
                font-weight: 400;
                color: $T2;
                white-space: nowrap;
            }

            .value {
                font-size: 14px;
                font-weight: 400;
                color: $T1;
            }
        }
    }
}

// 以下代码都是复写 abc-excel-table的样式
.v3-goods-table {
    margin-bottom: 16px !important;
    overflow: hidden;
    border-radius: var(--abc-border-radius-small) !important;// 解决表格边框圆角瑕疵

    .table-header {
        height: 40px !important;
        overflow: hidden;
        border-bottom: none !important;

        .th.sortable {
            cursor: pointer;
            user-select: none;
        }

        .th.required {
            .label::after {
                display: inline-block;
                margin-left: 4px;
                font-family: SimSun;
                font-size: 14px;
                line-height: 1;
                color: #ff9933;
                content: "*";
            }
        }

        .sort-bar {
            display: inline-block;
            width: 12px;
            margin-left: 0;
            text-align: center;
            cursor: pointer;

            .inner {
                display: inline-block;
                width: 8px;
                height: 10px;

                .sort-asc {
                    display: flex;
                    height: 4px;
                    margin-bottom: 2px;

                    span.up {
                        display: inline-block;

                        @include triangle(8px, 4px, $P1, 'up');

                        &:hover {
                            border-bottom-color: $T2;
                        }

                        &.selected {
                            border-bottom-color: $theme2;
                        }
                    }
                }

                .sort-desc {
                    display: flex;
                    height: 4px;

                    span.down {
                        display: inline-block;

                        @include triangle(8px, 4px, $P1, 'down');

                        &:hover {
                            border-top-color: $T2;
                        }

                        &.selected {
                            border-top-color: $theme2;
                        }
                    }
                }
            }
        }

        .th_scrollbar {
            width: 10px;
            height: 100%;
        }
    }

    .table-body {
        position: relative;
        height: calc(100% - 40px) !important;
        min-height: 45px !important;
        overflow: auto;
        border-top: 1px solid $P1;

        .row-popper {
            height: 100%;
        }

        .li {
            .tr {
                height: 45px !important;
                border-top: 1px solid $P6;

                .td:not(:last-child) {
                    border-right-style: solid !important;
                }

                .td:not(.td-custom-input) {
                    .abc-form-item input,
                    .abc-input__inner {
                        height: 44px !important;
                    }

                    .abc-form-item.is-error {
                        .abc-input__inner,
                        input {
                            background-color: #fef7e9 !important;
                        }
                    }
                }

                .td {
                    position: relative;
                    //overflow: hidden;

                    .cover-base-border {
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: 0;
                        left: 0;
                        pointer-events: none;
                        background: transparent;
                    }

                    .cover-top-border {
                        top: -1px;
                        border-top: 1px solid #ffffff;
                    }

                    .cover-left-border {
                        left: -1px;
                        border-left: 1px solid #ffffff;
                    }

                    .cover-top-left-border {
                        top: -1px;
                        left: -1px;
                        border-top: 1px solid #ffffff;
                        border-left: 1px solid #ffffff;
                    }

                    .f9fafc {
                        border-color: #f9fafc;
                    }

                    &.is-disabled {
                        cursor: default;
                        background-color: #f9fafc;

                        .abc-input__inner {
                            background-color: #ffffff !important;
                        }
                    }

                    .is-disabled {
                        cursor: default;
                        background-color: #f9fafc;
                    }

                    .full-flex {
                        display: flex;
                        align-items: center;
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }

        // 去掉第一行li的tr的border-top
        & > .li:first-of-type > .row-popper > .tr {
            height: 44px !important;
            border-top: none !important;
        }

        .scrolling-container .actual-content > .li:first-of-type > .row-popper > .tr {
            height: 44px !important;
            border-top: none !important;
        }

        .form-item-flex {
            .abc-form-item-content {
                display: flex;
            }
        }
    }

    &.is-disabled {
        cursor: default;
        background-color: #f9fafc;

        .table-header .th {
            background-color: #f9fafc !important;
        }

        .abc-input__inner {
            background-color: #ffffff !important;
        }
    }

    &.include-scroll-bar {
        .table-body {
            @include scrollBar;
        }
    }
}

// 以下代码都是goods-change-log的样式
.v3-goods-change-log {
    padding-top: 16px;
    margin: auto 14px 16px;
    border-top: 1px dashed $P1;

    @include scrollBar();

    .log {
        display: flex;
        align-items: flex-start;
        height: 14px;
        margin-bottom: 8px;
        font-size: 12px;
        line-height: 16px;
        color: $T2;

        &:last-child {
            margin-bottom: 0;
        }

        .log-time {
            color: $T2;
        }

        .log-action {
            color: $T2;
        }

        .log-remark {
            display: flex;
            max-width: 675px;
            color: $T3;
            word-break: break-all;
        }

        .log-detail {
            color: $theme1;
            cursor: pointer;
        }

        .supplier-log {
            position: relative;
            color: $T1;
            cursor: pointer;

            .detail-info {
                background-color: #ffffff;

                span {
                    display: block;
                    line-height: 16px;
                }
            }

            &:hover {
                .detail-info {
                    display: inline-block;
                }
            }
        }

        .detail-info {
            position: absolute;
            bottom: 25px;
            left: 13px;
            display: none;
            padding: 16px;
            line-height: 14px;
            color: #000000;
            white-space: nowrap;
            cursor: pointer;
            background: #ffffff;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);
            box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
            transform: translateX(-40%);

            &::after {
                position: absolute;
                bottom: -5px;
                left: 40%;
                display: block;
                content: ' ';
                transform: translateX(-50%);

                @include triangle(10px, 6px, #fff, 'down');
            }

            &::before {
                position: absolute;
                bottom: -6px;
                left: 40%;
                display: block;
                content: ' ';
                transform: translateX(-50%);

                @include triangle(10px, 6px, #d9dbe3, 'down');
            }
        }
    }

    .view-all-log {
        width: 100%;
        height: 20px;

        span {
            font-size: 12px;
            line-height: 20px;
            color: $theme1;
            cursor: pointer;
        }
    }
}

// v3新样式
.v3-goods-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;

    // 大小包装数量输入框
    .count-input {
        display: inline-flex;
        align-items: center;

        .abc-input__inner {
            padding: 3px 24px 3px 3px !important;
            text-align: center !important;
            border-radius: 0 !important;
        }

        .unit-append-label {
            width: 24px;
            min-width: 24px;
            max-width: 24px;
            font-size: 14px;
            text-align: center;
        }
    }

    .v3-goods-table-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        min-height: 32px;
        max-height: 32px;
        margin-bottom: 16px;

        .v3-table-footer-right {
            display: flex;

            li > .value {
                font-weight: 500;
            }

            li:not(:last-child) {
                margin-right: 20px;
            }
        }
    }

    // 文字样式
    .label {
        font-size: 14px;
        font-weight: 400;
        color: $T2;
    }

    .label-small {
        @extend .label;

        font-size: 12px;
    }

    .value {
        font-size: 14px;
        font-weight: 400;
        color: $T1;

        &.bold {
            font-weight: bold;
        }
    }

    .blue {
        color: $theme2;
    }

    .red {
        color: $R2;
    }

    .green {
        color: $G1;
    }

    .yellow {
        color: $Y2;
    }
}

// table-popover样式
.custom-popover {
    &.dark {
        padding: 4px 10px;
        margin-bottom: 6px;
        color: #ffffff;
        background-color: #000000;

        .popper__arrow::after {
            border-top-color: #000000 !important;
        }
    }

    .edit-popover {
        display: flex;
        align-items: center;
        height: 20px;

        .split-line {
            width: 1px;
            height: 14px;
            margin: 0 8px;
            background-color: $P6;
        }
    }
}
