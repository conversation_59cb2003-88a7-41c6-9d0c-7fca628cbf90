<template>
    <abc-popover
        ref="social"
        :disabled="disabled"
        :popper-style="{
            padding: 0, maxHeight: '740px',
        }"
        placement="bottom-start"
        theme="white"
        trigger="click"
    >
        <abc-button
            slot="reference"
            :disabled="disabled"
            :type="filterParamsText ? 'blank' : 'ghost'"
            icon="n-filter-line"
        >
            <span
                v-abc-title.ellipsis="filterParamsText || '筛选'"
                :style="{
                    display: 'inline-block',
                    color: filterParamsText ? '#000' : '#7a8794',
                    maxWidth: maxWidth,
                }"
            >
            </span>
        </abc-button>

        <div class="goods-social-filter">
            <div class="goods-social-filter-box">
                <ul v-if="showGoodsBasicFilter" class="filter-options-wrapper">
                    <li>
                        <label :style="{ width: labelWidth }" class="label-title">商品状态</label>
                        <abc-checkbox-group v-model="currentDisableStock" style="width: 300px; height: 20px;">
                            <abc-checkbox :label="0" style="width: 66px;">
                                使用中
                            </abc-checkbox>
                            <abc-checkbox :label="1" style="width: 66px;">
                                已停用
                            </abc-checkbox>
                        </abc-checkbox-group>
                    </li>
                    <li>
                        <label :style="{ width: labelWidth }" class="label-title">处方药/OTC</label>
                        <abc-checkbox-group v-model="curParams.otcType" style="width: 400px; height: 20px;">
                            <abc-checkbox
                                v-for="(item, index) in GoodsModelOptions.otcType"
                                :key="index"
                                :label="item.value"
                                style="width: 66px;"
                            >
                                {{ item.label }}
                            </abc-checkbox>
                            <abc-checkbox :label="-1" style="width: 66px;">
                                未指定
                            </abc-checkbox>
                        </abc-checkbox-group>
                    </li>
                    <li>
                        <label :style="{ width: labelWidth }" class="label-title">条码</label>
                        <abc-checkbox-group v-model="currentHasBarCode" style="width: 300px; height: 20px;">
                            <abc-checkbox
                                :label="1"
                                style="width: 66px;"
                            >
                                有条码
                            </abc-checkbox>
                            <abc-checkbox :label="0" style="width: 66px;">
                                无条码
                            </abc-checkbox>
                        </abc-checkbox-group>
                    </li>
                    <li>
                        <label :style="{ width: labelWidth }" class="label-title">库存</label>
                        <abc-checkbox-group v-model="currentOnlyStock" style="width: 300px; height: 20px;">
                            <abc-checkbox :label="1" style="width: 66px;">
                                有库存
                            </abc-checkbox>
                            <abc-checkbox :label="2" style="width: 66px;">
                                无库存
                            </abc-checkbox>
                        </abc-checkbox-group>
                    </li>
                    <li v-if="showMemberPrice">
                        <label :style="{ width: labelWidth }" class="label-title">会员价</label>
                        <abc-checkbox-group v-model="currentMemberPrice" style="width: 300px; height: 20px;">
                            <abc-checkbox :label="1" style="width: 66px;">
                                有会员价
                            </abc-checkbox>
                            <abc-checkbox :label="0" style="width: 66px;">
                                无会员价
                            </abc-checkbox>
                        </abc-checkbox-group>
                    </li>
                </ul>
                <div v-if="showGoodsBasicFilter && (showShebaoFilter || showGoodsFilter || showEyeGlassesFilter)" class="goods-filter-split-line"></div>
                <ul v-if="showShebaoFilter" class="filter-options-wrapper">
                    <li>
                        <label :style="{ width: labelWidth }" class="label-title">
                            {{ isShowDrugSupervisionInfo ? '医保' : '' }}对码
                        </label>
                        <abc-checkbox v-model="curParams.sbNationalMatched" type="number">
                            已对码
                        </abc-checkbox>
                        <abc-checkbox v-model="curParams.sbNationalNotMatched" type="number">
                            未对码
                            <span class="tips-count">
                                <span v-if="shebaoCount.shebaoNationalNotMatchCount">{{ shebaoCount.shebaoNationalNotMatchCount | filterCount }}</span>
                            </span>
                        </abc-checkbox>
                        <abc-checkbox v-model="curParams.sbNationalNotPermit" type="number">
                            {{ needTranferNoUseShebao ? '无医保码' : '不刷医保/暂无编码' }}
                        </abc-checkbox>
                    </li>

                    <li v-if="isShowDrugSupervisionInfo">
                        <label :style="{ width: labelWidth }" class="label-title">药监对码</label>
                        <abc-checkbox v-model="curParams.centerCodeMatched" type="number">
                            已对码
                        </abc-checkbox>
                        <abc-checkbox v-model="curParams.centerCodeNotMatched" type="number">
                            未对码
                            <span class="tips-count">
                                <span v-if="shebaoCount.centerCodeNotMatchCount">{{ shebaoCount.centerCodeNotMatchCount | filterCount }}</span>
                            </span>
                        </abc-checkbox>
                        <abc-checkbox v-model="curParams.centerCodeNoCode" type="number">
                            无药监码
                        </abc-checkbox>
                    </li>

                    <li v-if="visibleWarningDictExpired">
                        <label :style="{ width: labelWidth }" class="label-title">失效状态</label>
                        <abc-checkbox v-model="curParams.sbExpired" type="number">
                            已失效
                            <span class="tips-count">
                                <span v-if="shebaoExpireCount">{{ shebaoExpireCount | filterCount }}</span>
                            </span>
                        </abc-checkbox>
                        <abc-checkbox v-model="curParams.sbNotExpired" type="number">
                            未失效
                        </abc-checkbox>
                        <abc-checkbox v-model="curParams.sbGoingExpired" type="number">
                            即将失效
                            <span class="tips-count" style="left: 58px;">
                                <span v-if="shebaoGoingExpireCount">{{ shebaoGoingExpireCount | filterCount }}</span>
                            </span>
                        </abc-checkbox>
                    </li>

                    <li>
                        <label :style="{ width: labelWidth }" class="label-title">等级</label>
                        <abc-checkbox-group v-model="medicalFeeGrade" style="height: 20px;">
                            <abc-checkbox :label="1" style="width: 66px;">
                                甲类
                            </abc-checkbox>
                            <abc-checkbox :label="2" style="width: 66px;">
                                乙类
                            </abc-checkbox>
                            <abc-checkbox :label="3" style="width: 66px;">
                                丙类
                            </abc-checkbox>
                            <abc-checkbox :label="100" style="width: 66px;">
                                其他
                            </abc-checkbox>
                        </abc-checkbox-group>
                    </li>

                    <li v-if="isShowShebaoPayMode">
                        <label :style="{ width: labelWidth }" class="label-title">{{ shebaoFilterPayModeText }}</label>
                        <abc-checkbox-group v-model="shebaoPayMode" style="width: 300px; height: 20px;">
                            <abc-checkbox :label="0" style="width: 66px;">
                                {{ shebaoFilterPayModeAllowOptionText }}
                            </abc-checkbox>
                            <abc-checkbox v-if="isShowAcctFilterItem" :label="1" style="width: 66px;">
                                优先个账
                            </abc-checkbox>
                            <abc-checkbox :label="2" style="width: 66px;">
                                {{ shebaoFilterPayModeNotAllowOptionText }}
                            </abc-checkbox>
                        </abc-checkbox-group>
                    </li>

                    <li>
                        <label :style="{ width: labelWidth }" class="label-title">限价</label>
                        <abc-checkbox v-model="curParams.sbLimitPrice" type="number">
                            有限价
                        </abc-checkbox>
                    </li>
                    <li v-if="isEnableListingPrice">
                        <label :style="{ width: labelWidth }" class="label-title">挂网价</label>
                        <abc-checkbox v-model="curParams.sbListingPrice" type="number">
                            有挂网价
                        </abc-checkbox>
                    </li>
                </ul>
                <div v-if="showShebaoFilter && (showGoodsFilter || showEyeGlassesFilter)" class="goods-filter-split-line"></div>
                <abc-form
                    v-if="showGoodsFilter"
                    :label-width="92"
                    item-no-margin
                    label-position="top"
                    style="display: flex; flex-wrap: wrap; gap: 8px 24px; max-width: 720px; padding: 12px;"
                >
                    <abc-form-item-group grid :grid-column-count="3">
                        <template v-for="col in goodsCols">
                            <abc-form-item
                                v-if="col.type === 'BUSINESS_SCOPE_LIST'"
                                :key="col.key"
                                :label="col.label"
                            >
                                <business-scope-cascader
                                    v-model="businessScopeList"
                                    :config="{
                                        width: 100,
                                        multiple: true,
                                        collapse: true,
                                    }"
                                    @change="handleBusinessScopeChange"
                                >
                                </business-scope-cascader>
                            </abc-form-item>

                            <abc-form-item
                                v-else-if="col.type === 'GOODS_TAG'"
                                :key="col.key"
                                :label="col.label"
                            >
                                <goods-tag-select v-model="tagId"></goods-tag-select>
                            </abc-form-item>

                            <abc-form-item
                                v-else-if="col.type === 'FIRST_IN_DATE'"
                                :key="col.key"
                                :label="col.label"
                                grid-column="span 1"
                            >
                                <!--                            style="width: 200px"-->
                                <abc-date-picker
                                    v-model="selectDate"
                                    type="daterange"
                                    placeholder=""
                                    clearable
                                    hide-shortcut-name
                                    :describe-list="describeList"
                                    :picker-options="pickerOptions"
                                    @change="handleFirstInDateChange"
                                >
                                </abc-date-picker>
                            </abc-form-item>

                            <!--<abc-form-item-->
                            <!--    v-else-if="col.type === 'STORAGE_TYPE'"-->
                            <!--    :key="col.key"-->
                            <!--    :label="col.label"-->
                            <!--&gt;-->
                            <!--    <storage-select-->
                            <!--        v-model="curParams[col.key]"-->
                            <!--        :popover-config="{-->
                            <!--            placement: 'bottom-start'-->
                            <!--        }"-->
                            <!--        :input-config="{-->
                            <!--            placeholder: '',-->
                            <!--            width: 100,-->
                            <!--            clearable: true,-->
                            <!--        }"-->
                            <!--        :panel-style="{-->
                            <!--            width: '206px',-->
                            <!--        }"-->
                            <!--        :suggestions="storageTypeLabels"-->
                            <!--        :show-search-icon="false"-->
                            <!--    >-->
                            <!--    </storage-select>-->
                            <!--</abc-form-item>-->

                            <abc-form-item
                                v-else-if="col.type === 'select'"
                                :key="col.key"
                                :label="col.label"
                            >
                                <abc-select
                                    v-model="curParams[col.key]"
                                    :inner-width="col.innerWidth || 100"
                                    :multiple="col.multiple"
                                    :fetch-suggestions="col.fetchSuggestions"
                                    :with-search="col.withSearch"
                                    clearable
                                    multi-label-mode="text"
                                >
                                    <abc-option
                                        v-for="it in col.options"
                                        :key="it.value"
                                        :label="it.label"
                                        :value="it.value"
                                    ></abc-option>
                                    <abc-option v-if="!['hasBarCode', 'hasDrugIdentificationCode', 'dosageFormType'].includes(col.key)" :value="-1" label="未指定"></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-form-item
                                v-else
                                :key="col.key"
                                :label="col.label"
                            >
                                <abc-input
                                    v-model="curParams[col.key]"
                                    clearable
                                >
                                </abc-input>
                            </abc-form-item>
                        </template>
                    </abc-form-item-group>
                </abc-form>
                <div v-if="showGoodsFilter && showEyeGlassesFilter" class="goods-filter-split-line"></div>
                <abc-form
                    v-if="showEyeGlassesFilter"
                    ref="formRef"
                    :label-width="92"
                    label-position="top"
                    style="display: flex; flex-wrap: wrap; gap: 8px 24px; max-width: 648px; padding: 12px;"
                >
                    <abc-form-item-group grid :grid-column-count="3">
                        <template v-for="key in filterKeys">
                            <abc-form-item
                                v-if="key === 'brandName'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-input v-model="curParams.spuGoodsCondition.brandName" clearable></abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'refractiveIndex'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-select v-model="curParams.spuGoodsCondition.refractiveIndex" clearable>
                                    <abc-option
                                        v-for="it in options.refractive"
                                        :key="it.value"
                                        :label="it.label"
                                        :value="it.value"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'customType'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-select v-model="curParams.spuGoodsCondition.customType" clearable>
                                    <abc-option
                                        v-for="it in options.tab"
                                        :key="it.value"
                                        :label="it.label"
                                        :value="it.value"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'addLight'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-input
                                    v-model="curParams.spuGoodsCondition.addLight"
                                    v-abc-focus-selected
                                    :config="inputConfig"
                                    :input-custom-style="{ textAlign: 'left' }"
                                    type="money"
                                >
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'wearCycle'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-select v-model="curParams.spuGoodsCondition.wearCycle" clearable>
                                    <abc-option
                                        v-for="it in options.wearCycle"
                                        :key="it.value"
                                        :label="it.label"
                                        :value="it.value"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'material'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-input v-model="curParams.spuGoodsCondition.material" clearable></abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'spherical'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-input
                                    v-model="curParams.spuGoodsCondition.spherical"
                                    v-abc-focus-selected
                                    :config="inputConfig"
                                    :input-custom-style="{ textAlign: 'left' }"
                                    type="money"
                                >
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'focalLength'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-input
                                    v-model="curParams.spuGoodsCondition.focalLength"
                                    v-abc-focus-selected
                                    :config="inputConfig"
                                    :input-custom-style="{ textAlign: 'left' }"
                                    type="money"
                                >
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'lenticular'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-input
                                    v-model="curParams.spuGoodsCondition.lenticular"
                                    v-abc-focus-selected
                                    :config="inputConfig"
                                    :input-custom-style="{ textAlign: 'left' }"
                                    type="money"
                                >
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'spec'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-input v-model="curParams.spuGoodsCondition.spec" clearable></abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'color'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <abc-input v-model="curParams.spuGoodsCondition.color" clearable></abc-input>
                            </abc-form-item>
                            <abc-form-item
                                v-if="key === 'goodsTag'"
                                :key="key"
                                :label="SkuFilterKeyNameMap[key]"
                                style="margin: 0;"
                            >
                                <goods-tag-select v-model="tagId"></goods-tag-select>
                            </abc-form-item>
                        </template>
                    </abc-form-item-group>
                </abc-form>
            </div>
            <div class="filter-footer">
                <abc-button style="margin-left: auto;" @click="changeHandler">
                    应用
                </abc-button>
                <abc-button type="blank" @click="resetHandler">
                    清空
                </abc-button>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import {
        mapState, mapGetters,
    } from 'vuex';
    import GoodsAPIV3 from 'api/goods/index-v3';

    import { PharmacyTypeEnum } from 'views/common/enum.js';
    import {
        SkuFilterKeyNameMap, SkuType,
    } from 'views/inventory/eyeglass/constant.js';
    import {
        SPECIES_FILE_FILTER_PARAMS,
        SOCIAL_PARAMS_KAY_NAME_MAP,
        SOCIAL_MEDICAL_FEE_GRADE_NAME_MAP,
    } from 'views/inventory/constant.js';
    import clone from 'utils/clone.js';
    import {
        isNotNull, isNull,
    } from 'utils/index.js';
    import BusinessScopeCascader from 'views/inventory/goods/archives/components/businessScopeCascader.vue';
    import {
        dosageFormType,
        GoodsModelOptions, StorageTypeLabels,
    } from 'views/common/inventory/constants.js';
    import GoodsTagSelect from 'views/inventory/goods/archives/components/goods-tag-select.vue';
    import useDateRangePicker from '@/hooks/abc-ui/use-date-range-picker';
    import useDictionary, { CatalogueEnum } from '@/hooks/business/use-dictionary';
    // import StorageSelect from 'components/input-selectable-panel/index.vue';
    export default {
        name: 'GoodsFilterPopover',
        components: {
            GoodsTagSelect,
            // StorageSelect,
            BusinessScopeCascader,
        },
        filters: {
            filterCount(val) {
                if (val > 99) {
                    return '99+';
                }
                return val;
            },
        },
        props: {
            params: {
                type: Object,
                required: true,
            },
            maxWidth: {
                type: String,
                default: '200px',
            },
            shebaoCount: {
                type: Object,
            },
            includeZeroStock: {
                type: Number,
            },
            includeDisableStock: {
                type: Number,
            },
            isSupportDrugIdentificationCode: Boolean,
            isSupportPosition: Boolean,
            isSupportFirstInDate: Boolean,
            isStockGoods: {
                type: Boolean,
                default: false,
            },
            pharmacyType: {
                type: Number,
            },
            itemKeys: {
                type: Array,
                default() {
                    return [];
                },
            },
            showEyeGlassesFilter: {
                type: Boolean,
                default: false,
            },
            // 展示基础选项
            showGoodsBasicFilter: {
                type: Boolean,
                default: false,
            },
            // 展示会员价
            showMemberPrice: {
                type: Boolean,
                default: false,
            },
            showShebaoFilter: {
                type: Boolean,
                default: true,
            },
            showGoodsFilter: {
                type: Boolean,
                default: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            needTranferNoUseShebao: {
                type: Boolean,
                default: false,
            },
            isShowShebaoPayMode: {
                type: Boolean,
                default: true,
            },
            handleParams: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            // 使用日期范围选择器 hooks
            const {
                selectDate,
                describeList,
                pickerOptions,
                clearDate,
            } = useDateRangePicker();

            const { getDictionaryByCatalogueName } = useDictionary({ GoodsAPIV3 });

            return {
                selectDate,
                describeList,
                pickerOptions,
                clearDate,
                getDictionaryByCatalogueName,
            };
        },
        data() {
            return {
                currentDisableStock: [],
                currentOnlyStock: [],
                currentHasBarCode: [],
                currentMemberPrice: [],
                PharmacyTypeEnum,
                SkuFilterKeyNameMap,
                GoodsModelOptions,
                curParams: clone(SPECIES_FILE_FILTER_PARAMS),
                // curIncludeZeroStock: 1,
                // curIncludeDisableStock: 1,
                medicalFeeGrade: [],
                shebaoPayMode: [],
                businessScopeList: [],
                businessScopeName: '',
                goodsTagNames: '',
                dosageFormTypeName: '',
                options: {
                    refractive: [
                        {
                            label: '1.49', value: 1.49,
                        },
                        {
                            label: '1.50', value: 1.50,
                        },
                        {
                            label: '1.53', value: 1.53,
                        },
                        {
                            label: '1.54', value: 1.54,
                        },
                        {
                            label: '1.55', value: 1.55,
                        },
                        {
                            label: '1.56', value: 1.56,
                        },
                        {
                            label: '1.59', value: 1.59,
                        },
                        {
                            label: '1.60', value: 1.60,
                        },
                        {
                            label: '1.61', value: 1.61,
                        },
                        {
                            label: '1.67', value: 1.67,
                        },
                        {
                            label: '1.70', value: 1.70,
                        },
                        {
                            label: '1.71', value: 1.71,
                        },
                        {
                            label: '1.73', value: 1.73,
                        },
                        {
                            label: '1.74', value: 1.74,
                        },
                        {
                            label: '1.76', value: 1.76,
                        },
                        {
                            label: '1.80', value: 1.80,
                        },
                        {
                            label: '1.90', value: 1.90,
                        },
                    ],
                    wearCycle: [
                        {
                            label: '日抛', value: '日抛',
                        },
                        {
                            label: '双周抛', value: '双周抛',
                        },
                        {
                            label: '月抛', value: '月抛',
                        },
                        {
                            label: '季抛', value: '季抛',
                        },
                        {
                            label: '半年抛', value: '半年抛',
                        },
                        {
                            label: '年抛', value: '年抛',
                        },
                    ],
                    tab: [
                        {
                            label: '成品', value: SkuType.Finished,
                        },
                        {
                            label: '定制', value: SkuType.CustomTailor,
                        },
                    ],
                    dosageFormTypeList: [],
                },
            };
        },
        computed: {
            dosageFormTypeList() {
                return this.options.dosageFormTypeList.filter((item) => {
                    return item.label.includes(this.dosageFormTypeName);
                });
            },
            storageTypeLabels() {
                return StorageTypeLabels;
            },
            labelWidth() {
                return (this.showGoodsFilter || this.showEyeGlassesFilter || this.showGoodsBasicFilter) ? '78px' : '40px';
            },
            /**
             * warningDictExpired 医保目录失效提醒
             * warningPriceExceed 限价提醒
             * warningDaysInAdvance 过期天数提醒
             */
            ...mapState('socialPc', ['warningDictExpired', 'warningPriceExceed', 'warningShebaoNotMatched']),
            ...mapGetters(['supplierList', 'isEnableListingPrice', 'clinicBasicConfig']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            SOCIAL_PAY_MODE_NAME_MAP() {
                return {
                    0: this.shebaoFilterPayModeAllowOptionText,
                    1: '优先个账',
                    2: this.shebaoFilterPayModeNotAllowOptionText,
                };
            },
            shebaoFilterPayModeText() {
                return this.viewDistributeConfig.Inventory.shebaoFilterPayModeText;
            },
            isSupportSupplierGoodsBind() {
                // 支持并对接了供应商
                return this.viewDistributeConfig.Inventory.isSupportSupplierGoodsBind && this.supplierList.some((e) => e.abcSupplierId);
            },
            isSupportBusinessScopeInGoodsArchives() {
                return this.viewDistributeConfig.Inventory.isSupportBusinessScopeInGoodsArchives;
            },
            shebaoFilterPayModeAllowOptionText() {
                return this.viewDistributeConfig.Inventory.shebaoFilterPayModeAllowOptionText;
            },
            shebaoFilterPayModeNotAllowOptionText() {
                return this.viewDistributeConfig.Inventory.shebaoFilterPayModeNotAllowOptionText;
            },
            // 医保目录失效，支持地区：重庆
            visibleWarningDictExpired() {
                /**
                 * @desc 失效状态不展示了，强制自费需求修改
                 * <AUTHOR>
                 * @date 2022/5/24 15:24
                 */
                return false;
                // return this.$abcSocialSecurity.config.isChongqingGb
            },
            // 零售价高于限价，支持地区：重庆
            visibleWarningPriceExceed() {
                return this.$abcSocialSecurity.config.isChongqingGb ||
                    this.$abcSocialSecurity.config.isZhejiangHangzhou;
            },
            // 商品标签
            tagId: {
                get() {
                    return this.curParams.tagId?.map((item) => ({ tagId: item }));
                },
                set(v) {
                    const tagId = v.map((item) => item.tagId);
                    const names = v.map((item) => item.name).join(',');
                    this.goodsTagNames = names;
                    this.curParams.tagId = tagId;
                },
            },
            filterParamsText() {
                const sbMatchParams = ['sbNationalMatched', 'sbNationalNotMatched', 'sbNationalNotPermit'];
                const centerCodeMatchParams = ['centerCodeMatched', 'centerCodeNotMatched', 'centerCodeNoCode'];

                let res = '';
                const sbMatchText = sbMatchParams.reduce((acc, cur) => {
                    if (this.params[cur]) {
                        return `${acc ? `${acc},${SOCIAL_PARAMS_KAY_NAME_MAP[cur]}` : `${SOCIAL_PARAMS_KAY_NAME_MAP[cur]}`}`;
                    }
                    return acc ;
                }, '');
                if (sbMatchText) {
                    res += `${res ? `/医保对码：${this.handleShebaoStr(sbMatchText)}` : `医保对码：${this.handleShebaoStr(sbMatchText)}`}`;
                }

                const centerCodeMatchText = centerCodeMatchParams.reduce((acc, cur) => {
                    if (this.params[cur]) {
                        return `${acc ? `${acc},${SOCIAL_PARAMS_KAY_NAME_MAP[cur]}` : `${SOCIAL_PARAMS_KAY_NAME_MAP[cur]}`}`;
                    }
                    return acc ;
                }, '');
                if (centerCodeMatchText) {
                    res += `${res ? `/药监对码：${centerCodeMatchText}` : `药监对码：${centerCodeMatchText}`}`;
                }

                const sbLimitPriceText = this.params.sbLimitPrice ? SOCIAL_PARAMS_KAY_NAME_MAP.sbLimitPrice : '';
                if (sbLimitPriceText) {
                    res += `${res ? `/医保限价：${sbLimitPriceText}` : `医保限价：${sbLimitPriceText}`}`;
                }

                const sbListingPriceText = this.params.sbListingPrice ? SOCIAL_PARAMS_KAY_NAME_MAP.sbListingPrice : '';
                if (sbListingPriceText) {
                    res += `${res ? `/医保挂网价：${sbListingPriceText}` : `医保挂网价：${sbListingPriceText}`}`;
                }

                let feeGradeText = '';
                if (this.params.medicalFeeGrade?.length) {
                    feeGradeText = this.params.medicalFeeGrade.reduce((acc, cur) => {
                        acc += `${SOCIAL_MEDICAL_FEE_GRADE_NAME_MAP[cur] || '其他'},`;
                        return acc;
                    }, '').slice(0, -1);
                }
                if (feeGradeText) {
                    res += `${res ? `/医保等级:${feeGradeText}` : `医保等级:${feeGradeText}`}`;
                }
                let payModeText = '';
                if (this.params.shebaoPayMode?.length) {
                    payModeText = this.params.shebaoPayMode.reduce((acc, cur) => {
                        acc += `${this.SOCIAL_PAY_MODE_NAME_MAP[cur]},`;
                        return acc;
                    }, '').slice(0, -1);
                }
                if (payModeText) {
                    res += `${res ? `/医保支付:${payModeText}` : `医保支付:${payModeText}`}`;
                }

                if (this.params.otcType?.length) {
                    res += `${res ? '/' : ''}处方药/OTC:${this.getSelectedLabels(GoodsModelOptions.otcType, this.params.otcType)}`;
                }

                if (this.params.businessScopeId?.length) {
                    res += res ? `/所属经营范围:${this.businessScopeName}` : `所属经营范围:${this.businessScopeName}`;
                }

                if (this.params.tagId?.length && this.goodsTagNames) {
                    res += res ? `/标签:${this.goodsTagNames}` : `标签:${this.goodsTagNames}`;
                }

                if (this.params.baseMedicineType?.length) {
                    res += `${res ? '/' : ''}基药:${this.getSelectedLabels(GoodsModelOptions.baseDrug, this.params.baseMedicineType)}`;
                }

                if (this.params.maintainType?.length) {
                    res += `${res ? '/' : ''}养护分类:${this.getSelectedLabels(GoodsModelOptions.conserve, this.params.maintainType)}`;
                }

                if (this.handleParams) {
                    if (isNotNull(this.params.hasBarCode) && this.params.hasBarCode !== undefined && this.params.hasBarCode?.length !== 0) {
                        const str = this.params.hasBarCode?.length > 1 ? '全部' : Number(this.params.hasBarCode) === 1 ? '有条码' : '无条码';
                        res += `${res ? `/是否有条码:${str}` : `是否有条码:${str}`}`;
                    }

                    if (isNotNull(this.params.memberPriceFlag) && this.params.memberPriceFlag !== undefined && this.params.memberPriceFlag?.length !== 0) {
                        const str = this.params.memberPriceFlag?.length > 1 ? '全部' : Number(this.params.memberPriceFlag) === 1 ? '有会员价' : '无会员价';
                        res += `${res ? `/是否有会员价:${str}` : `是否有会员价:${str}`}`;
                    }

                    if (isNotNull(this.params.onlyStock) && this.params.onlyStock !== undefined && this.params.onlyStock !== 0 && this.params.onlyStock?.length !== 0) {
                        const str = this.params.onlyStock?.length > 1 ? '全部' : Number(this.params.onlyStock) === 1 ? '有库存' : '无库存';
                        res += `${res ? `/是否有库存:${str}` : `是否有库存:${str}`}`;
                    }
                    // 0 不看已停用=使用中 1 已停用
                    if (isNotNull(this.params.disable) && this.params.disable !== undefined && this.params.disable?.length !== 0) {
                        const str = this.params.disable?.length > 1 ? '全部' : Number(this.params.disable) === 1 ? '已停用' : '使用中';
                        res += `${res ? `/商品状态:${str}` : `商品状态:${str}`}`;
                    }
                } else {
                    if (isNotNull(this.params.hasBarCode) && this.params.hasBarCode !== undefined) {
                        const str = `${this.params.hasBarCode}`?.length > 1 ? '全部' : Number(this.params.hasBarCode) === 1 ? '有条码' : '无条码';
                        res += `${res ? `/是否有条码:${str}` : `是否有条码:${str}`}`;
                    }

                    if (isNotNull(this.params.memberPriceFlag) && this.params.memberPriceFlag !== undefined) {
                        const str = `${this.params.memberPriceFlag}`?.length > 1 ? '全部' : Number(this.params.memberPriceFlag) === 1 ? '有会员价' : '无会员价';
                        res += `${res ? `/是否有会员价:${str}` : `是否有会员价:${str}`}`;
                    }

                    if (isNotNull(this.params.onlyStock) && this.params.onlyStock !== undefined && this.params.onlyStock !== 0) {
                        const str = `${this.params.onlyStock}`?.length > 1 ? '全部' : Number(this.params.onlyStock) === 1 ? '有库存' : '无库存';
                        res += `${res ? `/是否有库存:${str}` : `是否有库存:${str}`}`;
                    }
                    // 0 不看已停用=使用中 1 已停用
                    if (isNotNull(this.params.disable) && this.params.disable !== undefined) {
                        const str = `${this.params.disable}`?.length > 1 ? '全部' : Number(this.params.disable) === 1 ? '已停用' : '使用中';
                        res += `${res ? `/商品状态:${str}` : `商品状态:${str}`}`;
                    }
                }

                if (this.params.storage) {
                    res += `${res ? `/存储条件:${this.params.storage}` : `存储条件:${this.params.storage}`}`;
                }
                if (this.params.erpCode) {
                    res += `${res ? `/ERP编码:${this.params.erpCode}` : `ERP编码:${this.params.erpCode}`}`;
                }
                if (this.params.remark) {
                    res += `${res ? `/备注:${this.params.remark}` : `备注:${this.params.remark}`}`;
                }

                if (this.params.position) {
                    res += `${res ? `/柜号:${this.params.position}` : `柜号:${this.params.position}`}`;
                }

                if (this.params.dosageFormType?.length) {
                    const dosageFormTypeName = this.getSelectedLabels(this.options.dosageFormTypeList, this.params.dosageFormType);
                    res += `${res ? `/剂型:${dosageFormTypeName}` : `剂型:${dosageFormTypeName}`}`;
                }

                if (this.params.manufacturer) {
                    res += `${res ? `/生产厂家:${this.params.manufacturer}` : `生产厂家:${this.params.manufacturer}`}`;
                }

                if (this.params.firstInStartDate) {
                    res += `${res ? `/首次入库时间:${this.params.firstInStartDate}-${this.params.firstInEndDate}` : `首次入库时间:${this.params.firstInStartDate}-${this.params.firstInEndDate}`}`;
                }

                if (isNotNull(this.params.hasDrugIdentificationCode)) {
                    let str = '';
                    if (this.params.hasDrugIdentificationCode === 0) {
                        str = '标记无码';
                    } else if (this.params.hasDrugIdentificationCode === -1) {
                        str = '无标识码';
                    } else if (this.params.hasDrugIdentificationCode === 1) {
                        str = '有标识码';
                    }
                    res += `${res ? `/是否有标识码状态:${str}` : `是否有标识码状态:${str}`}`;
                }

                if (this.params.spuGoodsCondition) {
                    const spuText = Object.entries(this.params.spuGoodsCondition).reduce((res, [k, v]) => {
                        if (!isNull(v)) {
                            if (k === 'customType') {
                                v = v === SkuType.Finished ? '成品' : '定制';
                            }
                            res += `${SkuFilterKeyNameMap[k]}:${v} / `;
                        }
                        return res;
                    }, '').slice(0, -2);// 去掉最后一个斜杠和空格


                    if (spuText) {
                        res += `${res ? `/${spuText}` : `${spuText}`}`;
                    }
                }

                return res;
            },
            // 获取已超限的提醒数量，市目录 + 省目录 + 国家目录
            shebaoOverPriceCount() {
                return this.shebaoCount.shebaoCityOverPriceCount + this.shebaoCount.shebaoProvinceOverPriceCount + this.shebaoCount.shebaoNationalOverPriceCount;
            },
            // 获取即将超限的提醒数量，市目录 + 省目录 + 国家目录
            shebaoGoingOverPriceCount() {
                return this.shebaoCount.shebaoCityGoingOverPriceCount + this.shebaoCount.shebaoProvinceGoingOverPriceCount + this.shebaoCount.shebaoNationalGoingOverPriceCount;
            },
            // 获取已失效的提醒数量，市目录 + 省目录 + 国家目录
            shebaoExpireCount() {
                return this.shebaoCount.shebaoCityExpiredCount + this.shebaoCount.shebaoProvinceExpiredCount + this.shebaoCount.shebaoNationalExpiredCount;
            },
            // 获取即将超限的提醒数量，市目录 + 省目录 + 国家目录
            shebaoGoingExpireCount() {
                return this.shebaoCount.shebaoCityGoingExpireCount + this.shebaoCount.shebaoProvinceGoingExpireCount + this.shebaoCount.shebaoNationalGoingExpireCount;
            },


            filterKeys() {
                const itemKeys = clone(this.itemKeys);
                itemKeys.push('goodsTag');
                if (this.isStockGoods) return itemKeys;
                if (!this.subType) return ['brandName', 'refractiveIndex', 'customType', 'addLight', 'wearCycle', 'material', 'spherical', 'focalLength', 'lenticular', 'spec', 'color', 'goodsTag'];

                let keys = [];

                if (this.subType === 1) {
                    keys = ['spherical', 'lenticular'];
                }
                if (this.subType === 2) {
                    keys = ['color'];
                }
                if (this.subType === 4) {
                    keys = ['focalLength', 'color'];
                }
                if (this.subType === 5) {
                    keys = ['focalLength'];
                }
                if (this.subType === 3 || this.subType === 6) {
                    keys = ['spec'];
                }

                return [...new Set(itemKeys.concat(keys))];
            },
            goodsCols() {
                return [
                    {
                        label: '剂型',
                        key: 'dosageFormType',
                        type: 'select',
                        withSearch: true,
                        multiple: true,
                        options: this.dosageFormTypeList,
                        fetchSuggestions: this.handleSearch,
                    },
                    {
                        label: '生产厂家',
                        key: 'manufacturer',
                        type: 'input',
                    },
                    {
                        label: '所属经营范围',
                        key: 'businessScopeList',
                        type: 'BUSINESS_SCOPE_LIST',
                        hidden: !this.isSupportBusinessScopeInGoodsArchives,
                    },
                    {
                        label: '处方药/OTC',
                        key: 'otcType',
                        type: 'select',
                        multiple: true,
                        innerWidth: 120,
                        options: GoodsModelOptions.otcType,
                        hidden: this.showGoodsBasicFilter,
                    },
                    {
                        label: '基药',
                        key: 'baseMedicineType',
                        type: 'select',
                        multiple: true,
                        innerWidth: 120,
                        options: GoodsModelOptions.baseDrug,

                    },
                    {
                        label: '标识码状态（追溯码）',
                        key: 'hasDrugIdentificationCode',
                        type: 'select',
                        options: [
                            {
                                label: '有标识码', value: 1,
                            },
                            {
                                label: '无标识码', value: -1,
                            },
                            {
                                label: '标记无码', value: 0,
                            },
                        ],
                        innerWidth: 120,
                        hidden: !this.isSupportDrugIdentificationCode,
                    },
                    {
                        label: '养护分类',
                        key: 'maintainType',
                        type: 'select',
                        multiple: true,
                        innerWidth: 120,
                        options: GoodsModelOptions.conserve,
                    },
                    {
                        label: '存储条件',
                        key: 'storage',
                        type: 'input',
                    },
                    {
                        label: '是否有条码',
                        key: 'hasBarCode',
                        type: 'select',
                        options: [
                            {
                                label: '有条码', value: 1,
                            },
                            {
                                label: '无条码', value: 0,
                            },
                        ],
                        hidden: this.showGoodsBasicFilter,
                    },
                    {
                        label: 'ERP编码',
                        key: 'erpCode',
                        hidden: !this.isSupportSupplierGoodsBind,
                    },
                    {
                        label: '柜号',
                        key: 'position',
                        type: 'input',
                        hidden: !this.isSupportPosition,
                    },
                    {
                        label: '标签',
                        key: 'tagId',
                        type: 'GOODS_TAG',
                        hidden: this.showEyeGlassesFilter,
                    },
                    {
                        label: '首次入库时间',
                        key: 'firstInDate',
                        type: 'FIRST_IN_DATE',
                        hidden: !this.isSupportFirstInDate,
                    },
                    {
                        label: '备注',
                        key: 'remark',
                    },

                ].filter((col) => !col.hidden);
            },
            inputConfig() {
                return {
                    max: 100, formatLength: 2, supportNegative: true, supportZero: true,
                };
            },
            isHebeiArea() {
                return this.clinicBasicConfig.addressProvinceId.startsWith('13');
            },
            isShowDrugSupervisionInfo() {
                return this.viewDistributeConfig.Inventory.isSupportDrugAdministrationCenterCode && this.isHebeiArea;
            },
            isShowAcctFilterItem() {
                return this.viewDistributeConfig.Inventory.isShowAcctFilterItem;
            },
        },
        watch: {
            params: {
                handler() {
                    this.initParmas();
                    this.fetchDosageFormTypeList();
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            async fetchDosageFormTypeList() {
                const res = await this.getDictionaryByCatalogueName(CatalogueEnum.BUSINESS_SCOPE);
                const list = res?.[CatalogueEnum.DOSAGE_FORM_TYPE]?.map((e) => {
                    return {
                        label: e.name,
                        value: e.typeId,
                    };
                }) ?? dosageFormType;
                this.options.dosageFormTypeList = list;
            },
            handleSearch(name) {
                this.dosageFormTypeName = name;
            },
            handleShebaoStr(str) {
                return (this.needTranferNoUseShebao && str === '不允许医保支付') ? '无医保码' : str;
            },
            // 通过选中的值获取对应的label-处方药、基药、养护分类使用
            getSelectedLabels(options = [], values = []) {
                const labelMap = options.reduce((acc, cur) => {
                    acc[cur.value] = cur.label;
                    return acc;
                }, {});

                const labels = values.reduce((acc, cur) => {
                    if (cur === -1) {
                        acc += '未指定,';
                    } else {
                        acc += `${labelMap[cur]},`;
                    }
                    return acc;
                }, '').slice(0, -1);

                return labels;
            },
            visibleShebaoPayModeSelfCost() {
                return this.viewDistributeConfig.Inventory.visibleShebaoPayModeSelfCost();
            },
            handleBusinessScopeChange(businessScopeList = [], name) {
                this.curParams.businessScopeId = businessScopeList.map((e) => e.id);
                this.businessScopeName = name;
            },
            handleFirstInDateChange(date) {
                this.curParams.firstInStartDate = date?.[0] ?? '';
                this.curParams.firstInEndDate = date?.[1] ?? '';
            },
            changeHandler() {
                // const onlyStock = Number(!this.curIncludeZeroStock);
                // const disable = this.curIncludeDisableStock === 0 ? 0 : '';
                const {
                    medicalFeeGrade, shebaoPayMode, currentDisableStock, currentOnlyStock, currentHasBarCode, currentMemberPrice,
                } = this;
                let params = {
                    ...this.curParams,
                    medicalFeeGrade,
                    shebaoPayMode,
                    // onlyStock,
                    // disable,
                };
                if (this.showGoodsBasicFilter) {
                    if (this.handleParams) {
                        params = {
                            ...params,
                            onlyStock: currentOnlyStock || [],
                            disable: currentDisableStock || [],
                            // 数组转字符串
                            hasBarCode: currentHasBarCode || [],
                        };
                        if (this.showMemberPrice) {
                            params = {
                                ...params,
                                memberPriceFlag: currentMemberPrice || [],
                            };
                        }
                    } else {
                        params = {
                            ...params,
                            onlyStock: currentOnlyStock.length === 0 ? 0 : currentOnlyStock.length === 2 ? currentOnlyStock.join(',') : currentOnlyStock[0],
                            disable: currentDisableStock.length === 0 ? undefined : currentDisableStock.length === 2 ? currentDisableStock.join(',') : currentDisableStock[0],
                            // 数组转字符串
                            hasBarCode: currentHasBarCode.length === 0 ? undefined : currentHasBarCode.length === 2 ? currentHasBarCode.join(',') : currentHasBarCode[0],
                        };
                        if (this.showMemberPrice) {
                            params = {
                                ...params,
                                memberPriceFlag: currentMemberPrice.length === 0 ? undefined : currentMemberPrice.length === 2 ? currentMemberPrice.join(',') : currentMemberPrice[0],
                            };
                        }
                    }
                }
                this.$emit('change', params);
                this.close();
            },
            initParmas() {
                this.curParams = clone(this.params);
                console.log('this.curParams', this.curParams);
                // this.curIncludeZeroStock = !Number(this.params?.onlyStock);// 含0库存
                // this.curIncludeDisableStock = isNull(this.params?.disable) ? 1 : 0; // 含停用
                this.medicalFeeGrade = this.params?.medicalFeeGrade?.slice() || [];
                this.shebaoPayMode = this.params?.shebaoPayMode?.slice() || [];
                if (this.showGoodsBasicFilter) {
                    if (this.handleParams) {
                        this.currentOnlyStock = this.params?.onlyStock || [];
                        this.currentDisableStock = this.params?.disable || [];
                        // 字符串转数组
                        this.currentHasBarCode = this.params?.hasBarCode || [];
                        if (this.showMemberPrice) {
                            this.currentMemberPrice = this.params?.memberPriceFlag || [];
                        }
                    } else {
                        this.currentOnlyStock = (this.params?.onlyStock === undefined || this.params?.onlyStock === 0) ? [] : `${this.params.onlyStock}`?.length === 1 ? [Number(this.params.onlyStock)] : (this.params.onlyStock?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                        this.currentDisableStock = this.params?.disable === undefined ? [] : `${this.params.disable}`?.length === 1 ? [Number(this.params.disable)] : (this.params?.disable?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                        // 字符串转数组
                        this.currentHasBarCode = this.params?.hasBarCode === undefined ? [] : `${this.params.hasBarCode}`?.length === 1 ? [Number(this.params.hasBarCode)] : (this.params.hasBarCode?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                        if (this.showMemberPrice) {
                            this.currentMemberPrice = this.params?.memberPriceFlag === undefined ? [] : `${this.params.memberPriceFlag}`?.length === 1 ? [Number(this.params.memberPriceFlag)] : (this.params.memberPriceFlag?.split(',')?.map((i) => {
                                return Number(i);
                            }) || []);
                        }
                    }
                }
            },
            resetHandler() {
                this.curParams = clone({
                    ...this.params,
                    ...SPECIES_FILE_FILTER_PARAMS,
                });
                this.medicalFeeGrade = [];
                this.shebaoPayMode = [];
                this.businessScopeList = [];
                this.businessScopeName = '';
                this.tagId = [];
                this.goodsTagNames = '';
                this.currentDisableStock = [];
                this.currentOnlyStock = [];
                this.currentHasBarCode = [];
                this.currentMemberPrice = [];
                this.clearDate();
                this.changeHandler();
            },
            close() {
                this.$refs.social.doClose();
            },
        },

    };
</script>
<style lang="scss">
    @import 'src/styles/theme';

    .goods-social-filter {
        &-box {
            max-height: 670px;
            overflow-y: auto;
        }

        .filter-options-wrapper {
            padding: 16px 12px 4px;

            > li {
                display: flex;
                align-items: center;
                height: 20px;
                min-height: 20px;
                margin-bottom: 12px;

                .label-title {
                    width: 40px;
                    margin-right: 24px;
                    color: $T2;
                }

                //&:hover {
                //    background-color: $P4;
                //}

                .abc-checkbox-wrapper {
                    margin-right: 42px;

                    & + .abc-checkbox-wrapper {
                        margin-left: 0;
                    }

                    .abc-checkbox__label {
                        position: relative;
                        color: $T1;

                        .tips-count {
                            position: absolute;
                            top: 0;
                            left: 46px;
                        }
                    }

                    &:last-child {
                        margin-right: 0;
                    }
                }

                .tips-count {
                    display: inline-flex;
                    padding: 0 3px;
                    font-size: 12px;
                    color: #ffffff;
                    background: $Y2;
                    border-radius: var(--abc-border-radius-small);
                }
            }
        }

        .filter-footer {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            text-align: right;
            border-top: 1px solid $P6;
        }

        .goods-filter-split-line {
            width: calc(100% - 24px);
            height: 1px;
            margin-left: 12px;
            background-color: $P4;
        }
    }
</style>
