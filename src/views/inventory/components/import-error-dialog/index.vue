<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        :title="errorTitle"
        append-to-body
        :content-styles="errContentStyles"
    >
        <template v-if="errorCode === 12100 && errorList">
            <abc-layout preset="dialog-table">
                <abc-list
                    :data-list="errorList"
                    :height="400"
                    :show-divider="true"
                    :hover-border="true"
                    :scrollable="true"
                    :no-hover-border="false"
                    :no-top-border="true"
                    :no-close-border="false"
                    :divider-config="{
                        size: 'normal', theme: 'light', margin: 'none', variant: 'solid',
                    }"
                    :need-selected="false"
                    :show-icon="false"
                    :readonly="true"
                    size="large"
                    :enable-virtual-list="enableVirtualList"
                    :virtual-list-config="virtualListConfig"
                >
                    <template
                        #default="{
                            item
                        }"
                    >
                        <abc-flex
                            v-if="item.goods"
                            justify="flex-start"
                            align="center"
                            gap="middle"
                            style="max-width: 500px;"
                        >
                            <abc-text size="normal" style="white-space: nowrap;" theme="black">
                                {{ item.rowIndex }}行
                            </abc-text>
                            <abc-text
                                size="normal"
                                class="ellipsis"
                                style="min-width: 60px;"
                                theme="black"
                                :title="item.goods?.shortId"
                            >
                                {{ item.goods?.shortId || '' }}
                            </abc-text>
                            <abc-text
                                size="normal"
                                class="ellipsis"
                                theme="black"
                                :title="item.goods?.displayName"
                            >
                                {{ item.goods?.displayName || '' }}
                            </abc-text>
                            <abc-text
                                size="mini"
                                class="ellipsis"
                                theme="gray"
                                :title="`${item.goods?.displaySpec || ''}${item.goods?.manufacturer || ''}`"
                            >
                                {{ item.goods?.displaySpec || '' }}
                                {{ item.goods?.manufacturer || '' }}
                            </abc-text>
                        </abc-flex>

                        <abc-flex
                            v-else
                            justify="flex-start"
                            align="center"
                            gap="middle"
                            style="max-width: 500px;"
                        >
                            <abc-text
                                size="normal"
                                class="ellipsis"
                                theme="black"
                                :title="item.name"
                            >
                                {{ item.name || '' }}
                            </abc-text>
                            <abc-text
                                size="normal"
                                class="ellipsis"
                                theme="black"
                                :title="item.extraMsg"
                            >
                                {{ item.extraMsg || '' }}
                            </abc-text>
                        </abc-flex>
                    </template>

                    <template #append="{ item }">
                        <abc-flex style="max-width: 400px;">
                            <abc-text
                                v-if="item.msgs?.length"
                                size="normal"
                                theme="warning-light"
                                :title="item.msgs"
                                class="ellipsis"
                            >
                                {{ item.msgs.join(',') }}
                            </abc-text>
                        </abc-flex>
                    </template>
                </abc-list>
            </abc-layout>
        </template>

        <abc-flex slot="footer" justify="flex-end">
            <abc-button @click="visible = false">
                {{ errorCode === 12100 ? '我知道了' : '确定' }}
            </abc-button>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    export default {
        name: 'ImportErrorDialog',
        props: {
            errorCode: {
                type: [String,Number],
                default: '',
            },
            errorTitle: {
                type: String,
                default: '导入失败',
            },
            errorList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                visible: false,
                errPageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                virtualListConfig: {
                    rowHeight: 43,// 32height+1border
                    bufferSize: 10,
                    bufferLoad: false,
                },
            };
        },
        computed: {
            enableVirtualList() {
                return this.errorList.length > 60;
            },
            errContentStyles() {
                return `width: ${this.errorCode === 12100 ? '960px' : '400px'};
                        padding: ${
                    this.errorCode === 12100 ? '0 0 0 16px' : '24px'
                }`;
            },
            tableData() {
                const start =
                    this.errPageParams.pageIndex * this.errPageParams.pageSize;
                return (
                    this.errorList &&
                    this.errorList.slice(start, start + this.errPageParams.pageSize)
                );
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.onClose && this.onClose();
                    this.destroyElement();
                }
            },
        },
        methods: {
            changePage(index) {
                this.errPageParams.pageIndex = index - 1;
            },
            destroyElement() {
                this.$destroy();
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
                console.log('ImportErrorDialog已销毁');
            },
        },
    };
</script>
