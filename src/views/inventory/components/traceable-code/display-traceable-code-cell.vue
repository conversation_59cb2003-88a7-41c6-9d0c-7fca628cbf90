<template>
    <abc-form-item
        class="display-traceable-code-cell-trigger"
        :validate-event="validateEvent"
        data-cy="abc-trace-code-popover-reference"
        placement="top"
        :error-style="isItemUsageOverLimit ? { backgroundColor: 'var(--abc-color-LY4)' } : {}"
    >
        <abc-tooltip
            v-bind="tooltipConfig"
        >
            <abc-input
                :value="textConfig.text"
                readonly
                :disabled="disabled || !needCollect"
                :class="{ 'custom-readonly': readonly }"
                :data-theme="textConfig.theme"
                adaptive-width
                :tabindex="-1"
            >
                <template v-if="isStrictCountWithTraceCodeCollect ? false : showError" #appendInner>
                    <abc-tooltip
                        placement="top"
                        :content="warningTips"
                        :disabled="!warningTips"
                    >
                        <abc-icon :size="16" icon="n-alert-fill" color="var(--abc-color-Y2)"></abc-icon>
                    </abc-tooltip>
                </template>
            </abc-input>
        </abc-tooltip>
    </abc-form-item>
</template>

<script>
    import TraceCode, {
        SceneTypeEnum, TraceableCodeListItemErrorType, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import { isChineseMedicine } from '@/filters';
    import { mapGetters } from 'vuex';
    import TraceCodeLimitDialog from '@/service/trace-code/dialog-limit-trace-code';
    import cloneDeep from 'lodash.clonedeep';
    import {
        isNotNull, isNull,
    } from '@/utils';
    import Big from 'big.js';

    export default {
        name: 'DisplayTraceableCodeCell',
        props: {
            value: {
                type: Array,
                default: () => [],
            },
            dataItem: Object,
            goods: {
                type: Object,
                default: () => ({}),
            },
            goodsCount: {
                type: Object,
                default: () => ({
                    label: '采购数量',
                    countLabel: '采购数量',
                    unitCount: 0,
                    maxCount: 0,
                    unit: '',
                    isTrans: false,// 后端是否换算过
                }),
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            offset: {
                type: Number,
                default: 0,
            },
            sceneType: {
                type: String,
            },
            needValidate: {
                type: Boolean,
                default: false,
            },
            isStrictCountWithTraceCodeCollect: {
                type: Boolean,
                default: false,
            },
            popoverRenderControlled: {
                type: Boolean,
                default: false,
            },
            item: {
                type: Object,
                default: () => ({}),
            },
            needValidateDrugIdentificationCode: {
                type: Boolean,
                default: false,
            },
            isReturn: {
                type: Boolean,
                default: false,
            },
            needValidateTraceCodeCount: {
                type: Boolean,
                default: false,
            },
            isImportFromTraceCode: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                isItemUsageOverLimit: false, // 触发了超限校验
                collectedTraceCodeCount: '', // 展示已采数量+单位
            };
        },
        computed: {
            ...mapGetters(['traceCodeConfig']),
            traceableCodeList: {
                get() {
                    return this.value || [];
                },
                set(val) {
                    this.$emit('input', val || []);
                },
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            // 特殊判断，盘点无盈亏
            noProfitOrLoss() {
                return this.sceneType === SceneTypeEnum.GOODS_CHECK && this.goodsCount.label === '盈亏数量' && this.goodsCount.unitCount === 0;
            },
            needCollect() {
                if (!this.goods) return false;
                return TraceCode.isSupportTraceCode(this.goods.typeId);
            },
            goodsMaxCount() {
                if (TraceCode.isNoTraceCodeGoods(this.goods) && isChineseMedicine(this.goods)) {
                    return 1;
                }
                return this.goodsCount.maxCount;
            },
            warningInfo() {
                return TraceCode.validateDataItem({
                    sceneType: this.sceneType,
                    dataItem: this.dataItem || {
                        traceableCodeList: this.traceableCodeList,
                        _maxTraceCodeCount: this.goodsMaxCount,
                        _isTransformable: this.goodsCount.isTrans,
                    },
                    getUnitInfo: () => {
                        return {
                            ...this.goodsCount,
                            countLabel: this.goodsCount.label,
                        };
                    },
                    productInfo: this.goods,
                });
            },
            warningTips() {
                if (!this.needCollect || this.disabled || !this.needValidate || this.noProfitOrLoss) return '';
                return this.warningInfo.warnTips;
            },
            hoverTips() {
                if (this.noProfitOrLoss) {
                    return '无需采集：盈亏数量为 0 时，在库追溯码无需变更';
                }

                if (this.isStrictCountWithTraceCodeCollect) {
                    return this.warningTips;
                }

                return '';
            },
            errorTips() {
                return this.warningInfo.errorTips || '';
            },
            tooltipConfig() {
                const disableHoverCard = !this.hoverTips;
                return {
                    content: this.hoverTips,
                    disabled: disableHoverCard || !!this.errorTips || this.disabled,
                    placement: this.isStrictCountWithTraceCodeCollect ? 'top' : 'bottom-start',
                    maxWidth: this.isStrictCountWithTraceCodeCollect ? 296 : 134,
                    openDelay: 500,
                };
            },
            // 特殊无码商品-无固定值后端默认是空字符串
            isNullCodeGoods() {
                return TraceCode.isNullCodeGoods(this.goods);
            },
            textConfig() {
                let text = '';
                let theme = '';
                if (this.needCollect && !this.isNullCodeGoods) {
                    if (this.noProfitOrLoss) {
                        if (this.traceableCodeList.length) {
                            text = `已采: ${this.collectedTraceCodeCount}`;
                            theme = 'primary-light';
                        } else if (!this.goods?.shortId && this.isImportFromTraceCode) {
                            text = '请关联档案';
                            theme = 'gray-light';
                        } else {
                            text = '无需采集';
                            theme = 'gray-light';
                        }
                    } else {
                        if (!this.traceableCodeList.length) {
                            text = '未采集';
                            theme = 'warning-light';
                        } else {
                            text = `已采: ${this.collectedTraceCodeCount}`;
                            theme = 'primary-light';
                        }
                    }
                } else {
                    if (!this.goods?.shortId && this.isImportFromTraceCode) {
                        text = '请关联档案';
                        theme = 'gray-light';
                    } else {
                        text = '无需采集';
                        theme = 'gray-light';
                    }
                }

                return {
                    text,
                    theme,
                };
            },
            renderList() {
                return TraceCode.initTraceableCodeList(this.traceableCodeList, this.goods, this.sceneType);
            },
            showWarnIcon() {
                return this.renderList.some((item) => item.status !== TraceableCodeListItemErrorType.NORMAL);
            },
            // 输入框后异常展示
            showError() {
                return (this.warningTips || this.showWarnIcon) && !this.disabled;
            },
        },
        watch: {
            traceableCodeList: {
                handler() {
                    this.calcCollectedCount();
                },
                deep: true,
                immediate: true,
            },
            'goodsCount.unit': {
                handler() {
                    this.calcCollectedCount();
                },
            },
        },
        methods: {
            /**
             * 更新追溯码的上限
             * @return {Promise<void>}
             */
            async handleUpdateHisMaxPackageCount() {
                const list = this.traceableCodeList.map((traceableCode) => {
                    const {
                        goodsInfo, ...restCode
                    } = traceableCode ?? {};
                    return restCode;
                });
                const resList = await TraceCode.getMaxTraceCountList({
                    scene: TraceCodeScenesEnum.PHARMACY,
                    dataList: [{
                        traceableCodeList: list,
                    }],
                    getGoodsInfo: () => this.goods,
                    getUnitInfo: (it) => ({
                        ...this.goods,
                        traceableCodeList: it.traceableCodeList,
                    }),
                });
                resList.forEach((it) => {
                    if (it.list && it.list.length) {
                        it.list.forEach((codeInfo) => {
                            const findTraceableCode = this.traceableCodeList.find((traceableCode) => traceableCode.no === codeInfo.no);
                            if (findTraceableCode) {
                                findTraceableCode.hisMaxPackageCount = codeInfo.hisMaxPackageCount ?? Number.MAX_SAFE_INTEGER;
                            }
                        });
                    }
                });
                this.$emit('triggerFormValidate');
            },
            handleToDetail(item) {
                new TraceCodeLimitDialog({
                    traceCodeInfo: item,
                    goods: this.goods,
                    onConfirm: () => {
                        this.handleUpdateHisMaxPackageCount();
                    },
                }).generateDialogAsync({ parent: this });
            },
            validateEvent(_, callback) {
                if (this.needValidate && this.showError) {
                    this.isItemUsageOverLimit = false;
                    callback({
                        validate: false,
                        message: this.warningInfo.errorTips || '',
                    });
                    return;
                }

                /**
                 * 校验追溯码采集数量必须等于入库量
                 */
                if (
                    !this.disabled &&
                    !this.readonly &&
                    this.isEnableTraceableCode &&
                    this.isStrictCountWithTraceCodeCollect &&
                    TraceCode.isSupportTraceCodeForceCheckStock() &&
                    this.needValidateTraceCodeCount
                ) {
                    const validateRes = TraceCode.validateCollectedCountInInventory({
                        goods: this.goods,
                        traceableCodeList: this.traceableCodeList,
                        unitCount: this.goodsCount.unitCount,
                        unit: this.goodsCount.unit,
                    });
                    if (!validateRes.validate) {
                        this.isItemUsageOverLimit = false;
                        callback({
                            validate: false,
                            message: validateRes.message,
                        });
                        return;
                    }
                }

                if (!this.disabled && !this.readonly && this.isEnableTraceableCode) {
                    // 老数据没有 hisPackageCount 和 hisPieceCount，不校验
                    if (!TraceCode.isCompatibleHistoryData({ traceableCodeList: this.traceableCodeList }, true)) {
                        for (let i = 0; i < this.traceableCodeList.length; i++) {
                            const traceableCode = this.traceableCodeList[i];
                            if (!traceableCode.hisPackageCount && !traceableCode.hisPieceCount) {
                                this.isItemUsageOverLimit = false;
                                callback({
                                    validate: false,
                                    message: '追溯码采集数量不能为0',
                                });
                                return;
                            }
                        }
                    }

                    if (this.isReturn) {
                        const isValidate = TraceCode.validateReturnTraceCodeCount({
                            traceableCodeList: this.traceableCodeList, goods: this.goods, goodsCount: this.goodsCount,
                        }).validate;
                        if (!isValidate) {
                            callback({
                                validate: false,
                                message: '追溯码采集数量不能超过退货数量',
                            });
                            return;
                        }
                    }

                    // 强校验追溯码超限
                    for (let i = 0; i < this.traceableCodeList.length; i++) {
                        const traceableCode = this.traceableCodeList[i];
                        const leftCount = TraceCode.getTraceCodeLeftCountInInventory(traceableCode, this.goods) - TraceCode.getTraceCollectCodeCountBySmall(this.goods, traceableCode);
                        if (leftCount < 0) {
                            this.isItemUsageOverLimit = true;
                            callback({
                                validate: false,
                                validateComponent: () => (
                                    <abc-flex gap="4" vertical align="end">
                                        <abc-tips icon theme="warning">追溯码入库量超上限（上限{ TraceCode.displayFormatLeftPieceUnitInInventory(traceableCode, this.goods) }，本次入库{ traceableCode.count }{ traceableCode.unit }）</abc-tips>
                                        <abc-button variant="text" size="small" onClick={() => this.handleToDetail(traceableCode)}>
                                            点此修正追溯码上限
                                        </abc-button>
                                    </abc-flex>
                                ),
                            });
                            return;
                        }
                    }
                }

                callback({
                    validate: true,
                });
            },
            /**
             * 更新采集数量信息
             */
            calcCollectedCount() {
                if (isChineseMedicine(this.goods)) {
                    this.collectedTraceCodeCount = this.traceableCodeList.length ?? 0;
                } else if (TraceCode.isCompatibleHistoryData({ traceableCodeList: this.traceableCodeList }, true)) {
                    const collectedCount = this.traceableCodeList.reduce((total, cur) => {
                        const currentCount = isNull(cur.count) ? 1 : cur.count;
                        return Big(total).plus(currentCount).toNumber();
                    }, 0);
                    this.collectedTraceCodeCount = `${collectedCount}`;
                } else {
                    const cacheTraceableCodeList = cloneDeep(this.traceableCodeList);
                    cacheTraceableCodeList.forEach((traceableCode) => {
                        if (isNotNull(traceableCode.hisPackageCount) && traceableCode.count !== traceableCode.hisPackageCount) {
                            traceableCode.count = traceableCode.hisPackageCount;
                        } else if (isNotNull(traceableCode.hisPieceCount) && traceableCode.count !== traceableCode.hisPieceCount) {
                            traceableCode.count = traceableCode.hisPieceCount;
                        }
                    });
                    const countInfo = TraceCode.getTraceCodeCollectCountInfo({
                        traceableCodeList: cacheTraceableCodeList,
                        unit: this.sceneType === SceneTypeEnum.GOODS_CHECK ? this.goods.packageUnit : this.goodsCount.unit,
                        productInfo: this.goods,
                    }, TraceCodeScenesEnum.INVENTORY);
                    if (this.goodsCount.unit === countInfo.packageUnit || this.sceneType === SceneTypeEnum.GOODS_CHECK) {
                        this.collectedTraceCodeCount = `${countInfo.collectBigCount ? `${countInfo.collectBigCount}${countInfo.packageUnit}` : ''}${countInfo.collectSmallCount ? `${countInfo.collectSmallCount}${countInfo.pieceUnit}` : ''}`;
                    } else {
                        const collectedPackageCountToPieceCount = countInfo.collectBigCount ? Big(countInfo.collectBigCount).times(this.goods.pieceNum ?? 1).toNumber() : 0;
                        const collectedTraceCodePieceCount = Big(collectedPackageCountToPieceCount).plus(countInfo.collectSmallCount ?? 0).toNumber();
                        this.collectedTraceCodeCount = `${collectedTraceCodePieceCount}${countInfo.pieceUnit}`;
                    }
                }
            },
        },
    };
</script>

<style lang="scss">
.display-traceable-code-cell-trigger {
    width: 100%;
    height: 100%;

    .abc-input-wrapper.is-readonly {
        .abc-input__inner {
            cursor: pointer;
        }
    }

    .abc-input-wrapper.custom-readonly {
        .abc-input__inner {
            border-bottom-color: transparent !important;
        }
    }

    .abc-input-wrapper[data-theme='warning-light'] {
        .abc-input__inner {
            color: var(--abc-color-Y2);
        }
    }

    .abc-input-wrapper[data-theme='primary-light'] {
        .abc-input__inner {
            color: var(--abc-color-B1);
        }
    }

    .abc-input-wrapper[data-theme='gray-light'] {
        .abc-input__inner {
            color: var(--abc-color-T3);
        }
    }
}
</style>
