<template>
    <div class="filing-tool-excel-wrapper">
        <abc-form>
            <goods-list :config="_tableConfig" :data="tableData">
                <template v-for="propItem in _tableConfig">
                    <div :slot="`${propItem.prop}Expend`" :key="propItem.prop" style="display: inline-block;">
                        <span v-if="propItem.showRedDot" style="color: #ff9933;">*</span>
                        <abc-dropdown v-if="propItem.supportChangeCol" @change="val => handleColumnChange(val, propItem.prop)">
                            <abc-icon slot="reference" icon="triangle" size="14"></abc-icon>
                            <abc-dropdown-item
                                v-for="(item, index) in colTitles"
                                :key="`${item + index}`"
                                :value="item"
                                :label="item"
                            ></abc-dropdown-item>
                        </abc-dropdown>
                    </div>
                </template>

                <!--            序号-->
                <div slot="rowNo" slot-scope="{ item }" class="row-line">
                    {{ item.sort }}
                    <abc-tooltip
                        v-if="handleShowErrorInfo(item, '', true).error"
                        placement="right"
                        :content="handleShowErrorInfo(item, '', true).message"
                        :visible-arrow="false"
                    >
                        <div class="warn-icon-wrapper">
                            <abc-icon icon="Attention" size="14"></abc-icon>
                        </div>
                    </abc-tooltip>
                </div>
                <!--            药品编码-->
                <div slot="shortId" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'shortId')">
                        <abc-input v-model="item.shortId" @change="handleChangeData(item, 'shortId')"></abc-input>
                    </abc-form-item>
                </div>
                <!--            国药准字-->
                <div slot="medicineNmpn" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'medicineNmpn')">
                        <abc-input v-model="item.medicineNmpn" @change="handleChangeData(item, 'medicineNmpn')">
                        </abc-input>
                    </abc-form-item>
                </div>
                <!--            条形码-->
                <div slot="barCode" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'barCode')">
                        <abc-input v-model="item.barCode" @change="handleChangeData(item, 'barCode')">
                        </abc-input>
                    </abc-form-item>
                </div>
                <!--            通用名-->
                <div slot="medicineCadn" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'medicineCadn')">
                        <abc-input v-model="item.medicineCadn" @change="handleChangeData(item, 'medicineCadn')">
                        </abc-input>
                    </abc-form-item>
                </div>
                <!--            生产厂家-->
                <div slot="manufacturer" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'manufacturer')">
                        <abc-input v-model="item.manufacturer" @change="handleChangeData(item, 'manufacturer')">
                        </abc-input>
                    </abc-form-item>
                </div>
                <!--类型-->
                <div slot="type" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'type')">
                        <abc-select
                            v-model="item.typeName"
                            :inner-width="80"
                            width="100%"
                            @change="(val) => handleChangeTypeName(val, item)"
                        >
                            <abc-option
                                v-for="itemType in GoodsTypeNameEnum"
                                :key="itemType.label"
                                :value="itemType.label"
                                :label="itemType.label"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                </div>
                <!--            二级分类-->
                <div slot="customType" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'customType')">
                        <abc-input v-model="item.customType" @change="handleChangeData(item, 'customType')"></abc-input>
                    </abc-form-item>
                </div>
                <!--            规格-->
                <div slot="spec" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'componentContentNum')">
                        <abc-input
                            v-model="item.componentContentNum"
                            :width="30"
                            :disabled="!isWesternMedicine(item)"
                            @change="handleChangeData(item, 'componentContentNum')"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item :error="handleShowErrorInfo(item, 'componentContentUnit')">
                        <select-usage
                            v-model="item.componentContentUnit"
                            :disabled="!isWesternMedicine(item)"
                            style="width: 34px;"
                            type="dosageUnit"
                            placement="bottom-start"
                            @input="handleChangeData(item, 'componentContentUnit')"
                        >
                        </select-usage>
                    </abc-form-item>
                    <div>:</div>
                    <abc-form-item :error="handleShowErrorInfo(item, 'medicineDosageNum')">
                        <abc-input
                            v-model="item.medicineDosageNum"
                            :disabled="isChineseMedicine(item)"
                            :width="30"
                            @change="handleChangeData(item, 'medicineDosageNum')"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item :error="handleShowErrorInfo(item, 'medicineDosageUnit')">
                        <select-usage
                            v-model="item.medicineDosageUnit"
                            style="width: 34px;"
                            type="dosageUnit"
                            :disabled="isChineseMedicine(item)"
                            placement="bottom-start"
                            @input="handleChangeData(item, 'medicineDosageUnit')"
                        >
                        </select-usage>
                    </abc-form-item>
                    <div>*</div>
                    <abc-form-item :error="handleShowErrorInfo(item, 'pieceNum')">
                        <abc-input
                            v-model="item.pieceNum"
                            :disabled="isChineseMedicine(item)"
                            :width="30"
                            @change="handleChangeData(item, 'pieceNum')"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item :error="handleShowErrorInfo(item, 'pieceUnit')">
                        <select-usage
                            v-model="item.pieceUnit"
                            :type="isChineseMedicine(item) ? 'chpUnit' : 'dosageFormUnit'"
                            placement="bottom-start"
                            style="width: 28px;"
                            @input="handleChangeData(item, 'pieceUnit')"
                        >
                        </select-usage>
                    </abc-form-item>
                    <div>/</div>
                    <abc-form-item :error="handleShowErrorInfo(item, 'packageUnit')">
                        <select-usage
                            v-model="item.packageUnit"
                            style="width: 28px;"
                            type="dosageFormUnit"
                            placement="bottom-start"
                            :disabled="isChineseMedicine(item)"
                            @input="handleChangeData(item, 'packageUnit')"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>
                <!--            销售价-->
                <div slot="packagePrice" slot-scope="{ item }" class="row-line">
                    <abc-form-item>
                        <abc-input
                            v-model="item.packagePrice"
                            type="money"
                            :config="{
                                formatLength: 2 ,supportZero: true
                            }"
                            @change="handleChangeData(item, 'packagePrice')"
                        ></abc-input>
                    </abc-form-item>
                </div>
                <!--            拆零销售-->
                <div slot="dismounting" slot-scope="{ item }" class="row-line">
                    <abc-form-item>
                        <abc-select v-model="item.dismounting" :width="80" @change="handleChangeData(item, 'dismounting')">
                            <abc-option :value="1" label="是"></abc-option>
                            <abc-option :value="0" label="否"></abc-option>
                        </abc-select>
                    </abc-form-item>
                </div>
                <!--            拆零销售价-->
                <div slot="piecePrice" slot-scope="{ item }" class="row-line">
                    <abc-form-item>
                        <abc-input
                            v-model="item.piecePrice"
                            :config="{
                                formatLength: 2 ,supportZero: true
                            }"
                            type="money"
                            @change="handleChangeData(item, 'piecePrice')"
                        ></abc-input>
                    </abc-form-item>
                </div>
                <!--            初始库存-->
                <div slot="stockCount" slot-scope="{ item }" class="row-line">
                    <abc-form-item :error="handleShowErrorInfo(item, 'stockPackageCount')">
                        <abc-input
                            v-model="item.stockPackageCount"
                            :disabled="isChineseMedicine(item)"
                            :width="24"
                            type="number"
                            @change="handleChangeData(item, 'stockPackageCount')"
                        >
                        </abc-input>
                    </abc-form-item>
                    <div style="width: 14px; min-width: 14px;">
                        {{ item.packageUnit }}
                    </div>
                    <abc-form-item :error="handleShowErrorInfo(item, 'stockPieceCount')">
                        <abc-input
                            v-model="item.stockPieceCount"
                            type="number"
                            :width="24"
                            @change="handleChangeData(item, 'stockPieceCount')"
                        >
                        </abc-input>
                    </abc-form-item>
                    <div style="width: 14px; min-width: 14px;">
                        {{ item.pieceUnit }}
                    </div>
                </div>
                <!--            进价-->
                <div slot="packageCostPrice" slot-scope="{ item }" class="row-line">
                    <abc-form-item>
                        <abc-input v-model="item.packageCostPrice" @change="handleChangeData(item, 'packageCostPrice')"></abc-input>
                    </abc-form-item>
                </div>
                <!--            进项税-->
                <div slot="inTaxRat" slot-scope="{ item }" class="row-line">
                    <abc-form-item>
                        <abc-input v-model="item.inTaxRat" @change="handleChangeData(item, 'inTaxRat')"></abc-input>
                    </abc-form-item>
                </div>
                <!--            销项税-->
                <div slot="outTaxRat" slot-scope="{ item }" class="row-line">
                    <abc-form-item>
                        <abc-input v-model="item.outTaxRat" @change="handleChangeData(item, 'outTaxRat')"></abc-input>
                    </abc-form-item>
                    <div class="delete-icon-wrapper" @click="deleteHandle(item)">
                        <abc-icon icon="close_popup_grey_hov" size="16"></abc-icon>
                    </div>
                </div>
            </goods-list>
        </abc-form>
    </div>
</template>

<script>
    import GoodsList from 'views/inventory/common/goods-list.vue';
    import SelectUsage from 'views/layout/select-group/index.vue';

    import {
        TABLE_CONFIG, GoodsTypeNameEnum,
    } from 'views/inventory/filing-tool/constant.js';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    export default {
        name: 'FilingExcel',
        components: {
            GoodsList,
            SelectUsage,
        },
        props: {
            tableData: {
                type: Array,
                required: true,
            },
            colTitles: {
                type: Array,
                required: true,
            },
        },
        data() {
            return {
                GoodsTypeNameEnum,
                TABLE_CONFIG,
            };
        },
        created() {
            this._tableConfig = TABLE_CONFIG;
        },

        methods: {
            handleChangeTypeName(val, item) {
                const typeObj = GoodsTypeNameEnum.find((item) => {
                    return item.label === val;
                });
                if (typeObj) {
                    item.type = typeObj.type;
                    item.subType = typeObj.subType;
                    item.cMSpec = typeObj.cMSpec;
                    if (typeObj.label === '中药饮片' || typeObj.label === '中药颗粒') {
                        item.stockPackageCount = '';
                        this.handleColumnChange(item, 'stockPackageCount');

                    }
                }
                this.$emit('change-type', item);
            },
            deleteHandle(item) {
                this.$emit('delete-item', item);
            },
            handleColumnChange(colTitle, tableProp) {
                this.$emit('change-column', colTitle, tableProp);
            },
            isWesternMedicine(goods) {
                return goods.type === GoodsTypeEnum.MEDICINE &&
                    (goods.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine ||
                        goods.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM);
            },
            isChineseMedicine(goods) {
                return goods.type === GoodsTypeEnum.MEDICINE &&
                    (goods.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine);
            },
            handleChangeData(item,prop) {
                this.$emit('change-item', item, prop);
            },

            /**
             * @desc 找到指定的单元错误显示
             * <AUTHOR>
             * @date 2022-12-23 10:03:34
             * isRowError 是否显示整行提示的错误
             */
            handleShowErrorInfo(item, prop, isRowError = false) {
                const { columnMsgs } = item?.errorInfo || [];
                const errorItem = columnMsgs?.find((item) => {
                    if (isRowError) {
                        return !item.columnName || item.columnName === 'duplicate';
                    }
                    if (prop) {
                        return item.columnName === prop;
                    }
                });
                if (errorItem) {
                    return {
                        error: true,
                        message: errorItem.message,
                    };
                }
                return {
                    error: false,
                };
            },

            /**
             * @desc 修复错误后更新渲染
             * <AUTHOR>
             * @date 2022-12-23 10:03:50
             * @params
             * @return
             */
            updateErrorInfo() {

            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.filing-tool-excel-wrapper {
    .goods-table-wrapper {
        .table .table-header {
            padding: 0 8px;
        }

        .table-content .td .abc-form-item {
            width: 100%;
            height: 100%;

            .abc-form-item-content {
                width: 100%;
                height: 100%;
            }
        }

        .table-content .td .spec .abc-form-item {
            width: auto;
        }
    }

    .table-content-li {
        .delete-icon-wrapper {
            .abc-icon {
                color: transparent;
            }
        }

        &:hover {
            .delete-icon-wrapper {
                .abc-icon {
                    color: $T3;
                }
            }
        }
    }

    .row-line {
        position: relative;
        display: flex;
        align-items: center;
        height: 46px;

        &.spec {
            .abc-input-wrapper {
                width: auto;

                .abc-input__inner {
                    padding: 3px;
                }
            }
        }

        .warn-icon-wrapper {
            position: absolute;
            top: 14px;
            left: -28px;
        }

        .delete-icon-wrapper {
            position: absolute;
            top: 12px;
            right: -28px;
        }
    }

    .abc-input-wrapper,
    .abc-select-wrapper {
        width: 100%;
        height: 100%;

        .abc-input__inner {
            height: 100%;
            padding: 3px;
            border-color: transparent;
            border-radius: 0;
        }
    }

    .abc-dropdown-wrapper {
        display: inline-block;
        width: 16px;
    }
}
</style>
