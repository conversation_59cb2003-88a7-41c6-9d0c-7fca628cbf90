<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        class="filing-tool-dialog-wrapper"
        title="ABC批量建档工具"
        content-styles="max-width: 1420px;min-width: 440px;min-height: 180px"
        @close-dialog="handleCancel"
    >
        <div v-if="importStep === FilingToolStepEnum.BEFORE_IMPORT" class="filing-tool-tips">
            <abc-icon
                icon="info"
                size="14"
                color="#5199F8"
                style="margin-right: 6px;"
            ></abc-icon>
            请根据数据列属性，按照规范命名表头，没有可不填。
            <abc-popover
                :visible-arrow="false"
                trigger="hover"
                theme="yellow"
                placemenc="bottom-start"
            >
                <abc-icon
                    slot="reference"
                    icon="info"
                    size="14"
                    color="#AAB4BF"
                    style="margin-left: 6px;"
                ></abc-icon>
                <div style="width: 400px;">
                    <p>
                        表头名规范：
                    </p>
                    <p>
                        药品编码、国药准字、条形码、通用名、生产厂家、规格、销售价、拆零价、进价、进项税、销项税、类型、二级分类、
                        拆零销售、整包库存、拆零库存、容量、容量单位、剂量、剂量单位、制剂数量、制剂单位、包装单位
                    </p>
                    <p>特殊表头解释：</p>
                    <p>
                        类型（可选项：西药、中药饮片、中药颗粒，中成药）
                    </p>
                    <p>二级分类（自定义分类，如丸剂、片剂、注射剂等）</p>
                    <p>拆零销售（是否允许拆零销售，是或否）</p>
                    <p>整包库存（包装规格的药品数量，如5盒）</p>
                    <p>拆零库存（拆零销售商品的拆零数量，如8支）</p>
                </div>
            </abc-popover>
        </div>
        <div v-abc-loading="pageLoading">
            <div v-if="importStep === FilingToolStepEnum.BEFORE_IMPORT" style="padding-top: 24px;">
                <p class="import-goods-type">
                    上传档案
                </p>
                <div v-if="!excelData" class="upload-icon" @click="handleUpload">
                    <abc-icon
                        icon="a-plus13px"
                        size="16"
                        color="#7a8794"
                    ></abc-icon>

                    <input
                        ref="excelInput"
                        type="file"
                        style="opacity: 0;"
                        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        @change="changeFileHandler"
                    />
                </div>
                <div v-else class="filing-excel-wrapper">
                    <abc-image :src="xlsImg" :width="56" :height="56"></abc-image>
                    <abc-button type="text" @click="handleDeleteFile">
                        删除
                    </abc-button>
                </div>
            </div>

            <template v-if="importStep === FilingToolStepEnum.SHOW_DRAFT">
                <h5 style="margin-bottom: 24px; font-size: 16px;">
                    有未完成的建档草稿，是否继续编辑？
                </h5>
                <div class="draft-table-info">
                    <div class="draft-header">
                        <div>
                            上传时间
                        </div>
                        <div>
                            总上传数据
                        </div>
                        <div>
                            待处理数据
                        </div>
                        <div>
                            操作人
                        </div>
                    </div>
                    <div class="draft-content">
                        <div>
                            {{ draftData.created }}
                        </div>
                        <div>
                            {{ draftData.totalCount }}
                        </div>
                        <div>
                            {{ draftData.dirtyCount }}
                        </div>
                        <div>
                            {{ draftData.createdBy }}
                        </div>
                    </div>
                </div>
            </template>

            <template v-if="importStep === FilingToolStepEnum.SHOW_TABLE">
                <div v-if="errData && errData.length" class="err-table-data">
                    {{ errData.length }}条异常数据
                    <abc-button type="text" @click="handleToErrorPage">
                        跳转至异常页
                    </abc-button>
                </div>
                <!--                <abc-button @click="checkExcel">-->
                <!--                    检查数据-->
                <!--                </abc-button>-->
                <filing-table
                    :table-data="tableData"
                    :col-titles="colTitles"
                    @change-column="handleColumnChange"
                    @change-item="handleUpdateItem"
                    @change-type="handleUpdateItemType"
                    @delete-item="handleDeleteItem"
                ></filing-table>
                <abc-pagination
                    show-total-page
                    :pagination-params="paginationParams"
                    :count="paginationParams.count"
                    @current-change="handlePageChange"
                ></abc-pagination>
            </template>
        </div>

        <div slot="footer" class="dialog-footer" style="display: flex;">
            <template v-if="importStep === FilingToolStepEnum.SHOW_DRAFT">
                <abc-button
                    :loading="importLoading"
                    @click="handleEditDraft"
                >
                    编辑
                </abc-button>
                <abc-button type="blank" @click="handleDeleteDraft">
                    放弃
                </abc-button>
            </template>
            <template v-else>
                <abc-button
                    v-if="importStep !== FilingToolStepEnum.SHOW_TABLE"
                    :disabled="!excelData"
                    :loading="importLoading"
                    @click="importExcel"
                >
                    导入
                </abc-button>
                <abc-button
                    v-if="importStep == FilingToolStepEnum.SHOW_TABLE"
                    :loading="importLoading"
                    @click="handleCreateGoods"
                >
                    完成建档
                </abc-button>
                <abc-button type="blank" @click="handleCancel">
                    取消
                </abc-button>
            </template>
        </div>
    </abc-dialog>
</template>

<script>
    import FilingToolAPI from 'api/filing-tool/index.js';
    import ClinicAPI from 'api/clinic.js';

    import { useAbcSellerInfoStore } from 'views/inventory/filing-tool/seller-store.js';

    import { mapGetters } from 'vuex';

    import xlsImg from 'src/assets/images/<EMAIL>';
    import FilingTable from 'views/inventory/filing-tool/filing-table.vue';
    import {
        GoodsTypeNameEnum, FilingToolStepEnum,
    } from 'views/inventory/filing-tool/constant.js';

    import { NavigateHelper } from '@/core/index.js';


    export default {
        name: 'Index',
        components: {
            FilingTable,
        },
        props: {
            value: Boolean,
            onConfirm: {
                type: Function,
                default: () => {},
            },
            onCancel: {
                type: Function,
                default: () => {},
            },
            onClose: {
                type: Function,
                default: () => {},
            },
        },
        data() {
            return {
                FilingToolStepEnum,
                visible: false,

                pageLoading: false,

                importStep: 1,

                isLogin: true, // 企业微信是否登陆过

                draftData: {},

                goodsType: 1,
                excelId: '',
                excelData: null,
                tableData: [],
                xlsImg,
                hasExcel: false,
                importLoading: false,
                statusTimer: null,
                createTimer: null,
                colTitles: [],

                fetchParams: {
                    excelId: '',
                    limit: 10,
                    offset: 0,
                },
                paginationParams: {
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },

                errData: [],

            };
        },

        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            abcSellerUserId() {
                return this.abcSellerStore?.abcSellerInfo?.id;
            },
        },
        created() {
            this.abcSellerStore = useAbcSellerInfoStore();
            this.checkLogin();
            this.initProcess();
        },
        beforeDestroy() {
            clearTimeout(this.statusTimer);
            clearTimeout(this.createTimer);
        },
        methods: {
            async checkLogin() {
                if (this.abcSellerUserId) return;
                const wwLoginCode = this.$route?.query?.code || '';
                if (!wwLoginCode) {
                    NavigateHelper.navigateToFilingToolOaAuth(this.currentClinic);
                } else {
                    try {
                        const { data } = await ClinicAPI.fetchABCSeller(wwLoginCode);
                        this.abcSellerStore.setSellerInfo(data.user);
                    } catch (e) {
                        console.log('扫码登录的错误', e);
                        if (e.code === 10001) {
                            NavigateHelper.navigateToFilingToolOaAuth(this.currentClinic);
                        } else {
                            this.handleClose();
                            this.$router.replace({
                                name: 'goods',
                            });
                        }
                    }

                }
            },


            async initProcess() {
                await this.fetchDraftExcel();
            },

            /**
             * @desc 获得草稿
             * <AUTHOR>
             * @date 2022-12-02 16:47:16
             */
            async fetchDraftExcel() {
                try {
                    this.pageLoading = true;
                    const data = await FilingToolAPI.fetchDraftExcel();
                    const excelId = data?.data?.excelId;
                    if (excelId) {
                        this.draftData = {
                            created: data?.data?.created,
                            createdBy: data?.data?.created_by,
                            dirtyCount: data?.data?.dirty_count,
                            totalCount: data?.data?.total_count,
                            excelId: data?.data?.excelId,
                        };
                        this.fetchParams.excelId = excelId;
                        this.importStep = FilingToolStepEnum.SHOW_DRAFT;
                    } else {
                        this.importStep = FilingToolStepEnum.BEFORE_IMPORT;
                    }
                } catch (e) {
                    this.importStep = FilingToolStepEnum.BEFORE_IMPORT;
                } finally {
                    this.pageLoading = false;
                }
            },

            /**
             * @desc 删除草稿
             * <AUTHOR>
             * @date 2022-12-20 14:18:15
             * @params
             * @return
             */
            async handleDeleteDraft() {
                try {
                    this.$confirm({
                        type: 'warn',
                        title: '放弃草稿后无法恢复，是否确定放弃？',
                        onConfirm: async () => {
                            await FilingToolAPI.deleteDraftExcel(this.fetchParams.excelId);
                            this.fetchParams.excelId = '';
                            this.tableData = [];
                            this.paginationParams.count = 0;
                            this.colTitles = [];
                            this.importStep = FilingToolStepEnum.BEFORE_IMPORT;
                        },
                    });
                } catch (e) {
                    console.warn('删除草稿失败',e);
                }
            },

            async handleEditDraft() {
                try {
                    this.pageLoading = true;
                    await this.fetchExcelData();
                    await this.checkExcel();
                    this.importStep = FilingToolStepEnum.SHOW_TABLE;
                    this.pageLoading = false;
                } catch (e) {
                    this.pageLoading = false;
                }
            },

            handleCancel() {
                this.onCancel();
                this.handleClose();
            },
            handleClose() {
                clearTimeout(this.statusTimer);
                clearTimeout(this.createTimer);
                this.onClose();
                this.$destroy();
                this.$el?.parentNode?.removeChild(this.$el);
                this.showDialog = false;
            },
            handleDeleteFile() {
                this.excelData = null;
            },
            handleUpload() {
                this.$refs.excelInput.click();
            },
            async changeFileHandler(e) {
                const file = e.target.files[ 0 ];
                const fileName = file.name;
                const ext = fileName.substr(fileName.lastIndexOf('.') + 1)?.toLocaleLowerCase();
                if (ext !== 'xlsx') {
                    this.$alert({
                        type: 'warn',
                        title: '文件格式错误',
                        content: '请上传xlsx格式的文件',
                    });
                    return;
                }
                const postData = new FormData();
                postData.append('file', file);
                postData.append('userId', this.abcSellerUserId);
                e.target.value = '';
                this.excelData = postData;
            },
            /**
             * @desc 上传表格
             * <AUTHOR>
             * @date 2022-12-02 17:20:19
             */
            async importExcel() {
                this.importLoading = true;
                try {
                    const res = await FilingToolAPI.uploadExcel(this.excelData);
                    const excelId = res?.data?.id;
                    const { code } = res;
                    if (excelId && code) {
                        this.fetchParams.excelId = excelId;
                        await this.fetchExcelStatus();
                    } else {
                        this.$alert({
                            type: 'warn',
                            content: res?.data?.message,
                        });
                    }
                    this.importLoading = false;
                } catch (e) {
                    this.importLoading = false;
                    console.warn('错误信息', e);
                }
            },
            /**
             * @desc 查询表格状态
             * <AUTHOR>
             * @date 2022-12-02 17:20:32
             */
            async fetchExcelStatus() {
                this.pageLoading = true;
                this.importLoading = true;
                try {
                    const { data } = await FilingToolAPI.fetchExcelStatus(this.fetchParams.excelId);
                    if (data.status !== 1 && data.status !== -1) {
                        this.statusTimer = setTimeout(() => {
                            clearTimeout(this.statusTimer);
                            this.fetchExcelStatus();
                        }, 5000);
                    } else if (data.status === -1) {
                        this.$alert({
                            type: 'warn',
                            content: data?.message,
                        });
                        clearTimeout(this.statusTimer);
                        this.pageLoading = false;
                        this.importLoading = false;
                    } else {
                        clearTimeout(this.statusTimer);
                        await this.fetchExcelData();
                        await this.checkExcel();
                        this.importStep = FilingToolStepEnum.SHOW_TABLE;
                    }
                } catch (e) {
                    clearTimeout(this.statusTimer);
                    this.pageLoading = false;
                    this.importLoading = false;
                    console.error('查询状态失败', e);
                }
            },

            /**
             * @desc 获取表格数据
             * <AUTHOR>
             * @date 2022-12-02 17:16:48
             */
            async fetchExcelData() {
                this.pageLoading = true;
                try {
                    const { data } = await FilingToolAPI.fetchExcelData(this.fetchParams);
                    let excelData = data?.data || [];
                    excelData = excelData.map((item) => {
                        const goodsTypeObj = this.findGoodsTypeName(item);
                        item.typeName = goodsTypeObj?.label;
                        item.errorInfo = null;
                        item.componentContentUnit = item.componentContentUnit ?? '';
                        item.componentContentNum = item.componentContentNum ?? '';
                        return item;
                    });

                    this.tableData = excelData;
                    this.paginationParams.count = data.total;

                    this.updateExcelError();

                    const { data: titleData } = await FilingToolAPI.fetchExcelTitle(this.fetchParams.excelId);

                    this.colTitles = titleData || [];
                    this.pageLoading = false;
                } catch (e) {
                    this.pageLoading = false;
                }
            },


            /**
             * @desc 交换列
             * <AUTHOR>
             * @date 2022-12-05 15:03:46
             * @params colTitle对应的表头名称
             * @params tableProp 表格值的属性
             * @return
             */
            async handleColumnChange(colTitle, tableProp) {
                await FilingToolAPI.fetchChangeColumn(colTitle, tableProp,this.fetchParams.excelId);
                this.fetchParams.offset = 0;
                this.fetchExcelStatus();
            },

            /**
             * @desc 更新表格的数据
             * <AUTHOR>
             * @date 2022-12-06 15:15:21
             * @params 更新的row
             * @params 更新的属性
             */
            async handleUpdateItem(row, key) {
                const id = row?.id || '';
                const rowIndex = row?.sort;
                this.errData = this.errData.filter((err) => {
                    err.columnMsgs = err?.columnMsgs?.filter((col) => {
                        return col.columnName !== key || col.columnName || col.columnName !== 'duplicate';
                    });
                    if (!err?.columnMsgs?.length) {
                        return err.rowIndex !== rowIndex;
                    }
                });
                this.updateExcelError();
                const value = row[key];
                if (id) {
                    try {
                        await FilingToolAPI.updateExcelData({
                            modifies: [
                                {
                                    id,
                                    value,
                                    key,
                                },
                            ],
                        });
                    } catch (e) {
                        console.error('更新表格数据失败', e);
                    }
                }
            },
            async handleUpdateItemType(item) {
                const id = item?.id;
                if (id) {
                    try {
                        await FilingToolAPI.updateExcelData({
                            modifies: [
                                {
                                    id,
                                    value: item.type,
                                    key: 'type',
                                },
                                {
                                    id,
                                    value: item.cMSpec,
                                    key: 'cMSpec',
                                },
                                {
                                    id,
                                    value: item.subType,
                                    key: 'subType',
                                },
                            ],
                        });
                    } catch (e) {
                        console.error('更新表格数据失败', e);
                    }
                }
            },

            /**
             * @desc 删除一行数据
             * <AUTHOR>
             * @date 2022-12-06 15:32:12
             */
            async handleDeleteItem(item) {
                const index = item?.sort ?? null;
                if (index !== null) {
                    try {
                        await FilingToolAPI.deleteExcelItem(this.fetchParams.excelId, index);
                        await this.fetchExcelData();
                    } catch (e) {
                        console.error('删除数据失败',e);
                    }
                }

            },

            async handlePageChange(pageIndex) {
                this.fetchParams.offset = (pageIndex - 1) * this.paginationParams.pageSize;
                await this.fetchExcelData();
            },

            findGoodsTypeName(goods) {
                const value = GoodsTypeNameEnum.find((item) => {
                    if (goods.cMSpec) {
                        return item.cMSpec === goods.cMSpec && item.type === goods.type && item.subType === goods.subType;
                    }
                    return item.type === goods.type && item.subType === goods.subType;
                });
                return value;
            },

            updateExcelError() {
                this.tableData?.forEach((item) => {
                    const errorInfo = this.errData?.find((error) => {
                        return error.rowIndex === item.sort;
                    });
                    item.errorInfo = errorInfo;
                });
            },
            /**
             * @desc 检查导入的excel数据是否正确
             * <AUTHOR>
             * @date 2022-12-20 11:25:48
             * @params
             * @return
             */
            async checkExcel() {
                this.pageLoading = true;
                try {
                    await FilingToolAPI.checkOrCreateGoods(this.fetchParams.excelId, 1);
                    await this.getCheckOrCreateStatus(1);
                } catch (e) {
                    this.pageLoading = false;
                }
            },
            /**
             * @desc 完成建档，创建物资
             * <AUTHOR>
             * @date 2022-12-06 17:43:42
             */
            async handleCreateGoods() {
                try {
                    this.pageLoading = true;
                    await FilingToolAPI.checkOrCreateGoods(this.fetchParams.excelId, 0);
                    await this.getCheckOrCreateStatus(0);
                } catch (e) {
                    this.pageLoading = false;
                }
            },

            /**
             * @desc 查询建档或者检查的状态
             * <AUTHOR>
             * @date 2022-12-22 16:15:56
             * @params opType 0 建档 1 检查
             * @return
             */
            async getCheckOrCreateStatus(opType) {
                this.pageLoading = true;
                this.importLoading = true;
                try {
                    const { data } = await FilingToolAPI.getCheckOrCreateStatus(this.fetchParams.excelId, opType);
                    if (data.status !== 10) {
                        this.createTimer = setTimeout(() => {
                            clearTimeout(this.createTimer);
                            this.getCheckOrCreateStatus(opType);
                        }, 5000);
                    } else {
                        this.importLoading = false;
                        this.pageLoading = false;
                        clearTimeout(this.createTimer);
                        if (data.errorDetailList?.length) {
                            this.errData = data.errorDetailList;
                            this.updateExcelError();
                            this.importStep = FilingToolStepEnum.SHOW_TABLE;
                            return;
                        }
                        if (!opType) {
                            this.$Toast({
                                type: 'success',
                                message: '建档完成',
                            });
                            if (typeof this.onConfirm === 'function') {
                                this.onConfirm();
                            }
                            this.handleClose();
                        }
                    }
                } catch (e) {
                    clearTimeout(this.createTimer);
                    this.pageLoading = false;
                    this.importLoading = false;
                }
            },

            /**
             * @desc 跳转到异常数据的指定页
             * <AUTHOR>
             * @date 2022-12-23 10:05:14
             */
            handleToErrorPage() {
                const firstError = this.errData[0];
                if (firstError) {
                    const page = Math.ceil(firstError.rowIndex / this.paginationParams.pageSize);
                    if ((page - 1) * this.fetchParams.limit !== this.fetchParams.offset) {
                        this.handlePageChange(page);
                    }
                }
            },
        },
    };
</script>
<style lang="scss">
.filing-tool-dialog-wrapper {
    .draft-table-info {
        border: 1px solid $P6;
        border-radius: var(--abc-border-radius-small);

        .draft-header,
        .draft-content {
            display: flex;
            align-items: center;

            > div {
                flex: 1;
                width: 150px;
                height: 42px;
                padding: 12px 8px;
                border-right: 1px solid $P6;

                &:last-child {
                    border-right: none;
                }
            }
        }

        .draft-header {
            border-bottom: 1px solid $P6;
        }
    }

    .filing-tool-tips {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        width: 100%;
        padding: 8px 0  8px 24px;
        color: #459eff;
        background-color: #e6eff8;
    }

    .import-goods-type {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 400;
    }

    .upload-icon {
        position: relative;
        width: 56px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        border: 1px solid $P6;
        border-radius: var(--abc-border-radius-small);

        .delete-icon-position {
            position: absolute;
            top: -12px;
            right: -12px;
        }
    }

    .err-table-data {
        display: inline-block;
        padding: 8px;
        margin-bottom: 12px;
        color: #ffffff;
        background-color: $Y3;
        border-radius: var(--abc-border-radius-small);
    }

    .filing-excel-wrapper {
        position: relative;
    }
}
</style>

