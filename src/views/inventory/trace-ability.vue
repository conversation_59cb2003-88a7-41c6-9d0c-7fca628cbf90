<template>
    <abc-layout preset="page-table" class="trace-ability-wrapper--list">
        <abc-layout-header>
            <abc-flex justify="space-around" style="width: 100%;">
                <trace-code-goods-autocomplete
                    ref="traceCodeGoodsAutocomplete"
                    :search.sync="searchKey"
                    :pharmacy-no="fetchParams.pharmacyNo"
                    :profit-classification-list="profitClassificationList"
                    :split-by-not-initial-stock="isChainSubStore"
                    @fetch-data="(val)=>{
                        _searchData(val)
                    }"
                    @focus="stopBarcodeDetect"
                    @blur="startBarcodeDetect"
                    @select="handleSelect"
                    @clear="clearSelect"
                    @enter="handleGoodsAutocompleteEnter"
                ></trace-code-goods-autocomplete>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                :data-list="dataList"
                :loading="loading"
                :class="{
                    'trace-ability-wrapper--list_table': disabled,
                }"
                :render-config="renderConfig"
                :empty-content="emptyContent"
                :empty-show-icon="!disabled"
            >
                <template #topHeader>
                    <abc-space direction="horizontal" size="small">
                        <abc-space direction="horizontal" :size="24">
                            <clinic-select
                                v-if="isChainAdmin"
                                v-model="fetchParams.clinicId"
                                clearable
                                placeholder="总部/门店"
                                @change="initGoods('')"
                            ></clinic-select>
                            <biz-mixed-selection-filter
                                v-model="status"
                                type="radio"
                                :options="warningList"
                                :gap="24"
                                static-number-theme="gray-light"
                                @change="_fetchData('')"
                            ></biz-mixed-selection-filter>
                        </abc-space>
                    </abc-space>
                </template>
                <!--                    <template #availableLimit="{ trData: item }">-->
                <!--                        <abc-form-item @click.native="openTraceCode(item)">-->
                <!--                            <abc-input-->
                <!--                                v-model="item.availableLimit"-->
                <!--                                :disabled="isChainAdmin"-->
                <!--                                :class="{-->
                <!--                                    'trace-code-input': !isChainAdmin,-->
                <!--                                }"-->
                <!--                                readonly-->
                <!--                            ></abc-input>-->
                <!--                        </abc-form-item>-->
                <!--                    </template>-->
                <!--                    <template #remainingQuantity="{ trData: item }">-->
                <!--                        <abc-form-item @click.native="openTraceCode(item)">-->
                <!--                            <abc-input-->
                <!--                                v-model="item.remainingQuantity"-->
                <!--                                :disabled="isChainAdmin"-->
                <!--                                :class="{-->
                <!--                                    'trace-code-input': !isChainAdmin,-->
                <!--                                }"-->
                <!--                                readonly-->
                <!--                            ></abc-input>-->
                <!--                        </abc-form-item>-->
                <!--                    </template>-->
                <template #status="{ trData: item }">
                    <abc-table-cell>
                        <abc-tag-v2 :min-width="52" :theme="item.status === TRACE_CODE_STATUS.USED ? 'default' : 'success'" :variant="item.status === TRACE_CODE_STATUS.USED ? 'ghost' : 'outline'">
                            {{ item.status === TRACE_CODE_STATUS.USED ? '已出库' : '在库' }}
                        </abc-tag-v2>
                    </abc-table-cell>
                </template>
                <template #operation="{ trData: item }">
                    <abc-table-cell>
                        <abc-button variant="text" @click="handleToDetail(item)">
                            详情
                        </abc-button>
                    </abc-table-cell>
                </template>
            </abc-table>
            <div v-if="disabled" class="trace-ability-wrapper--list_table-grey">
                <abc-text theme="gray-light" style="display: inline-block; margin-top: 168px;">
                    请在上方输入追溯码/药名等信息，进行查询
                </abc-text>
            </div>
        </abc-layout-content>
        <abc-layout-footer>
            <div v-if="disabled" style="width: 100%; height: 32px;"></div>
            <abc-pagination
                v-else
                :count="count"
                :pagination-params="pageParams"
                :show-total-page="false"
                @current-change="pageTo"
            >
                <abc-flex
                    v-if="count > 0"
                    slot="tipsContent"
                    gap="4"
                    align="center"
                    style="margin-left: auto;"
                >
                    <abc-text theme="gray">
                        共
                    </abc-text>
                    <abc-text>{{ count }}</abc-text>
                    <abc-text theme="gray">
                        条
                    </abc-text>
                </abc-flex>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>
<script type="text/ecmascript-6">
    import {
        mapGetters,
    } from 'vuex';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';
    import { debounce } from 'utils/lodash';
    import { TRACE_CODE_STATUS } from 'views/inventory/constant.js';
    import TraceCodeUsageDetailDialog from '@/service/trace-code/components/trace-code-usage-detail-dialog';
    import fecha from 'utils/fecha';
    import { DATE_TIME_FORMATE } from '@/assets/configure/constants';
    import TraceCodeLimitDialog from '@/service/trace-code/dialog-limit-trace-code';
    import TraceCodeGoodsAutocomplete from '@/views-pharmacy/charge/components/trace-code-goods-autocomplete.vue';
    import BarcodeDetectorV2 from 'utils/barcode-detector-v2';
    import { isBarcode } from '@/utils';
    import TraceCode from '@/service/trace-code/service';
    import GoodsApi from 'api/goods/index';
    import {
        goodsSpec,isChineseMedicine,
    } from '@/filters';
    import BizMixedSelectionFilter from '@/components-composite/biz-mixed-selection-filter';

    export default {
        name: 'TraceAbility',
        components: {
            ClinicSelect,
            TraceCodeGoodsAutocomplete,
            BizMixedSelectionFilter,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            pharmacyNo: {
                type: [Number, String],
                default: null,
            },
            pharmacyType: {
                type: Number,
                default: null,
            },
        },
        data() {
            return {
                loading: false,
                TRACE_CODE_STATUS,
                searchKey: '',
                status: '',
                isViewed: false,
                fetchParams: {
                    clinicId: '',
                    offset: 0,
                    limit: 10,
                    pharmacyNo: '',
                },
                outStockCount: 0,
                hasStockCount: 0,
                disabled: true,
                count: 0,
                no: '',
                dataList: [],
                goodsId: '',
                testGoods: JSON.parse('{"goodsVersion":0,"id":"ffffffff00000000186109100eb32000","goodsId":"ffffffff00000000186109100eb32000","status":1,"name":null,"displayName":"阿莫西林胶囊","displaySpec":"5mg*10支/盒","organId":"ffffffff0000000010602b7009ed4000","typeId":12,"type":1,"subType":1,"manufacturer":"吉林省金诺","pieceNum":10,"pieceUnit":"支","packageUnit":"盒","dismounting":0,"medicineCadn":"阿莫西林胶囊","medicineNmpn":"H22020200","medicineDosageNum":5,"medicineDosageUnit":"mg","specType":0,"position":null,"chainPackagePrice":13,"chainPiecePrice":1.3,"piecePrice":1.3,"packagePrice":13,"packageCostPrice":1,"minPackagePrice":13,"maxPackagePrice":13,"totalSalePrice":13,"priceType":1,"subClinicPriceFlag":1,"inTaxRat":0,"outTaxRat":0,"pieceCount":9,"packageCount":1030172,"dispGoodsCount":"1030172盒9支","stockPackageCount":1030062,"dispStockGoodsCount":"1030062盒","availablePackageCount":1030067,"outPieceCount":9,"outPackageCount":1030166,"dispOutGoodsCount":"1030166盒9支","prohibitPieceCount":9,"prohibitPackageCount":105,"dispProhibitGoodsCount":"105盒9支","lockingPackageCount":6,"dispLockingGoodsCount":"6盒","lastPackageCostPrice":1,"needExecutive":0,"hospitalNeedExecutive":0,"shortId":"000011","composeUseDismounting":0,"composeSort":0,"disableComposePrint":0,"createdUserId":"6e45706922a74966ab51e4ed1e604641","lastModifiedUserId":"6e45706922a74966ab51e4ed1e604641","lastModifiedDate":"2023-12-28T10:42:23Z","combineType":0,"bizRelevantId":null,"inspectionSite":0,"medicalFeeGrade":1,"shebaoNationalCode":"XJ01CAA040X006010205580","disable":0,"chainDisable":0,"v2DisableStatus":0,"chainV2DisableStatus":0,"disableSell":0,"customTypeName":"颗粒剂","isSell":1,"customTypeId":1000853,"chainPackageCostPrice":1,"manufacturerFull":"吉林省金诺药业有限公司","medicineDosageForm":"胶囊剂","chainId":"ffffffff0000000010602b7009ed4000","shebao":{"goodsId":"ffffffff00000000186109100eb32000","goodsType":1,"payMode":0,"medicineNum":"000011","medicalFeeGrade":1,"nationalCode":"XJ01CAA040X006010205580","nationalCodeId":"14403000041262","shebaoPieceNum":20,"shebaoPieceUnit":"袋","shebaoPackageUnit":"盒","shebaoMedicineNmpn":"国药准字H53021820","restriction":"限儿童及吞咽困难患者","socialName":"阿莫西林干混悬剂","standardCode":"86905580000016","limitUnitType":0,"limitUnit":"盒"},"profitRat":92.31,"lastStockInId":*********,"lastStockInOrderSupplier":"盘点入库","pharmacyType":0,"pharmacyNo":0,"pharmacyName":"本地药房","pharmacyGoodsStockList":[{"pharmacyName":"本地药房","pharmacyNo":0,"lastPackageCostPrice":1,"stockPieceCount":0,"stockPackageCount":1030062,"availablePackageCount":1030067,"availablePieceCount":0,"esInorder":1}],"defaultInOutTax":0,"dispenseAveragePackageCostPrice":12.01865,"shebaoPayMode":0,"restriction":"限儿童及吞咽困难患者","deviceInnerFlag":1,"feeComposeType":0,"feeTypeId":"12","usePieceUnitFlag":0,"copiedFlag":0,"coopFlag":0,"cloudSupplierFlag":0,"nationalCode":"XJ01CAA040X006010205580","nationalCodeId":"14403000041262","minExpiryDate":"2025-10-31","expiredWarnMonths":3,"dangerIngredient":0,"keyId":"ffffffff00000000350ade7c69f50002","goodsBatchInfoList":[{"batchId":"*********","pieceNum":10,"pharmacyType":0,"pharmacyNo":0,"expiryDate":"2025-10-31","supplierId":"ffffffff00000000108225180a874000","supplierName":"ABC","batchNo":"22","packageCostPrice":13,"packagePrice":13,"piecePrice":1.3,"packageCount":998,"dispGoodsCount":"998盒","stockPackageCount":993,"dispStockGoodsCount":"993盒","lockingPackageCount":6,"dispLockingGoodsCount":"6盒","cutTotalPieceCount":10,"cutPackageCount":1,"totalSalePrice":13,"lockInfo":{"lockId":"3822111860004716546","lockingPackageCount":1,"lockLeftTotalPieceCount":10,"pharmacyType":0,"pharmacyNo":0},"status":0,"expiredWarnFlag":1}],"lastMonthSellCount":0,"isPreciousDevice":0,"traceableCodeNoInfoList":[{"no":"8123456","drugIdentificationCode":"8123456","traceableCodeType":1,"type":0},{"no":"8346933","drugIdentificationCode":"8346933","traceableCodeType":1,"type":0},{"no":"8349847","drugIdentificationCode":"8349847","traceableCodeType":1,"type":0}],"specificationMatchStatus":0,"cMSpec":""}'),
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isChainAdmin',
                'isChainSubStore',
            ]),
            emptyContent() {
                if (this.disabled) {
                    return '';
                }
                return '暂无数据';
            },
            ...mapGetters('viewDistribute', ['viewDistributeConfig' ]),
            warningList() {
                return [ {
                    label: '在库',
                    value: 0,
                    statisticsNumber: this.hasStockCount,
                },{
                    label: '已出库',
                    value: 1,
                    statisticsNumber: this.outStockCount,
                }];
            },
            profitClassificationList() {
                return this.$abcPage?.$store?.profitClassificationList || [];
            },
            pageParams() {
                const {
                    limit: pageSize, offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            label: '门店',
                            key: 'organName',
                            style: {
                                minWidth: '100px',
                                flex: 1,
                            },
                            hidden: !this.isChainAdmin,
                        },
                        {
                            label: '追溯码',
                            key: 'no',
                            style: {
                                width: '188px',
                                flex: 1,
                            },
                        },
                        {
                            label: '状态',
                            key: 'status',
                            style: {
                                width: '100px',
                                maxWidth: '100px',
                                textAlign: 'center',
                            },
                        },
                        {
                            label: '可用上限',
                            key: 'availableLimit',
                            style: {
                                minWidth: '100px',
                                width: '100px',
                                textAlign: 'right',
                                maxWidth: '120px',
                            },
                            customRender: (h, row) => {
                                return (
                                    <abc-table-cell>
                                        <div>
                                            {!this.isChineseMedicine(row.goods) ? row?.availableLimit : '-'}</div>
                                    </abc-table-cell>
                                );
                            },
                        },
                        {
                            label: '剩余量',
                            key: 'remainingQuantity',
                            style: {
                                minWidth: '100px',
                                width: '100px',
                                textAlign: 'right',
                                maxWidth: '120px',
                            },
                            customRender: (h, row) => {
                                return (
                                    <abc-table-cell>
                                        <div>
                                            {!this.isChineseMedicine(row.goods) ? row?.remainingQuantity : '-'}</div>
                                    </abc-table-cell>
                                );
                            },
                        },
                        {
                            label: '药名',
                            key: 'medicineName',
                            style: {
                                minWidth: '120px',
                                flex: 1,
                            },
                            dataFormatter: (_,row) => {
                                return row.medicineName || row.displayName;
                            },
                        },
                        // 规格
                        {
                            label: '规格',
                            key: 'spec',
                            style: {
                                minWidth: '120px',
                                flex: 1,
                            },
                        },
                        {
                            label: '厂家',
                            key: 'manufacturer',
                            style: {
                                minWidth: '120px',
                                flex: 1,
                            },
                        },
                        {
                            label: '入库单号',
                            key: 'stockInOrderNo',
                            style: {
                                minWidth: '160px',
                                maxWidth: '160px',
                            },
                            dataFormatter: (_,row) => {
                                return row.stockInOrderNo || '-';
                            },
                        },
                        {
                            label: '最近动作',
                            key: 'lastAction',
                            style: {
                                minWidth: '90px',
                                maxWidth: '90px',
                            },
                        },
                        {
                            label: '最近动作时间',
                            key: 'lastActionTime',
                            style: {
                                width: '152px',
                                maxWidth: '152px',
                            },
                            dataFormatter: (_,row) => {
                                if (!row.lastActionTime) {
                                    return '';
                                }
                                return fecha.format(row.lastActionTime, DATE_TIME_FORMATE);
                            },
                        },
                        {
                            label: '操作',
                            key: 'operation',
                            style: {
                                width: '80px',
                                maxWidth: '80px',
                                textAlign: 'center',
                            },
                            hidden: this.isChainAdmin,
                        },
                    ],
                };
            },
        },

        watch: {
            pharmacyNo: {
                handler(no) {
                    if (this.isChainAdmin) {
                        this.fetchParams.pharmacyNo = '';
                    } else {
                        this.fetchParams.pharmacyNo = no;
                    }
                },
                immediate: true,
            },
            searchKey(val) {
                if (!val) {
                    this.clearSelect();
                }
            },
        },
        created() {
            this._fetchData = debounce(this.initGoods, 200, true);
            this._searchData = debounce(this.searchGoods, 200, true);
        },
        mounted() {
            this.startBarcodeDetect();
        },
        beforeDestroy() {
            this.stopBarcodeDetect();
        },
        methods: {
            isChineseMedicine,
            startBarcodeDetect() {
                if (this.startCharging) return;
                if (this._barcodeDetector) return;
                this._barcodeDetector = BarcodeDetectorV2.getInstance();
                this._barcodeDetector.startDetect(this.handleBarcode, true);
            },
            stopBarcodeDetect() {
                if (!this._barcodeDetector) return;
                this._barcodeDetector.stopDetect(this.handleBarcode);
                this._barcodeDetector = null;
            },
            async handleSelect(options) {
                this.no = ''; // 选择goodsId就需要清理no
                this.goodsId = options?.goods?.id || '';
                this.searchKey = options?.goods?.medicineCadn || options?.goods?.name;
                await this.fetchData();
            },
            async clearSelect() {
                this.goodsId = '';
                this.no = '';
                this.outStockCount = 0;
                this.hasStockCount = 0;
                this.fetchParams.clinicId = '';
                this.fetchParams.offset = 0;
                this.dataList = [];
                this.disabled = true;
                this.count = '';
            },
            async handleBarcode(e, barcode) {
                this.$nextTick(async () => {
                    this.$refs.traceCodeGoodsAutocomplete.$refs.goodsAutocomplete.keyword = barcode;
                    this.goodsId = ''; // 选择no 就得清理goodsId;
                    this.no = barcode;
                    this._fetchData();
                });
            },
            handleGoodsAutocompleteEnter(e) {
                const barcode = e.target.value;
                // 判断是否为医保编码（以大写 C、X、Z、T 开头）
                const isMedicalInsuranceCode = barcode && /^[CXZT]/.test(barcode);
                if (!isMedicalInsuranceCode && (isBarcode(barcode) || TraceCode.isTraceableCode(barcode))) {
                    this.handleBarcode(e, barcode);
                }
            },
            handleToDetail(item) {
                new TraceCodeUsageDetailDialog({
                    code: {
                        ...item,
                        no: item.no,
                    },
                    // 主要是需要productInfo
                    traceCodeUseInfo: {
                        productInfo: item.goods || this.testGoods,
                    },
                    onSaveSuccess: async () => {
                        this.$Toast({
                            message: '修改追溯码使用信息成功',
                            type: 'success',
                        });
                        await this.fetchData();
                    },
                }).generateDialogAsync({ parent: this });
            },
            async handleMounted(data) {
                this.fetchParams.limit = data.paginationLimit;
                // await this.fetchData();
            },
            async initGoods() {
                this.fetchParams.offset = 0;
                await this.fetchData();
            },
            async searchGoods(barcode = '') {
                this.fetchParams.offset = 0;
                this.no = barcode;
                await this.fetchData();
            },
            initOffset() {
                this.fetchParams.offset = 0;
                this.fetchData();
            },

            handleFilterChange() {
                console.log('防抖');
                // 更新筛选参数
                this.fetchParams.offset = 0;
                this.fetchData();
            },
            openTraceCode(item) {
                if (this.isChainAdmin) {
                    return;
                }
                return new Promise((resolve) => {
                    new TraceCodeLimitDialog({
                        title: '请确认追溯码可用上限',
                        isShowTips: false,
                        showClose: false,
                        isShowLeftCountSet: true,
                        traceCodeInfo: {
                            ...item,
                            no: item.no,
                        },
                        cancelText: '取消',
                        goods: item.goods || this.testGoods,
                        onConfirm: async () => {
                            // 更新当前列表
                            await this.fetchData();
                            resolve();
                        },
                        onClose: () => {
                            resolve();
                        },
                    }).generateDialogAsync({ parent: this });
                });
            },

            async fetchData() {
                this.loading = true;
                try {
                    const {
                        clinicId = '', offset = 0, limit = 10,
                    } = this.fetchParams;
                    const params = {
                        clinicId,
                        offset,
                        limit,
                        no: this.no,
                        goodsId: this.no ? '' : this.goodsId,
                        status: this.status,
                    };
                    if (this.no || this.goodsId) {
                        // 实际项目中应调用API获取数据
                        const { data } = await GoodsApi.fetchTraceCodeList(params);
                        this.dataList = data.page?.rows?.map((item) => {
                            return {
                                ...item,
                                lastAction: item.lastAction,
                                medicineName: item.goods?.displayName || item?.goods?.medicineCadn || item.goods?.name || '',
                                spec: item.goods?.displaySpec || goodsSpec(item.goods),
                                manufacturer: item.goods?.manufacturer || '',
                                lastActionTime: item.lastActionTime,
                                stockInOrderNo: item.inStockOrderIds?.join(',') || '',
                                availableLimit: `${item.maxHisPackageCount}${item.goods.packageUnit}`,
                                remainingQuantity: (item.goods?.pieceUnit !== item.goods?.packageUnit && item.goods.dismounting) ? `${item.leftPackageCount}${item.goods.packageUnit}${item.leftPieceCount}${item.goods.pieceUnit}` : `${item.leftPackageCount}${item.goods.packageUnit}`,
                            };
                        }) || [];
                        this.count = data.page?.total || 0;
                        this.outStockCount = data.outStock || 0;
                        this.hasStockCount = data.inStock || 0;
                    }

                    console.log('请求参数：', params);
                    // 这里仅作演示，实际应从API获取数据
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                    this.disabled = false;
                }
            },

            async pageTo(page) {
                this.fetchParams.offset = (page - 1) * this.fetchParams.limit;
                await this.fetchData();
            },
        },

    };
</script>
<style lang="scss">
@import 'styles/abc-common.scss';
@import 'styles/mixin.scss';

.trace-ability-wrapper--list {
    .trace-code-input {
        .abc-input__inner {
            cursor: pointer !important;
        }
    }

    &_table {
        position: relative;

        &-grey {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            display: flex;
            justify-content: space-around;
            width: 100%;
            height: 300px;
            background-color: rgba(255, 255, 255, 0.7);
        }
    }
}
</style>
