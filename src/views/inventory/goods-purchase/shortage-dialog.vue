<template>
    <AbcDialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle"
        class="shortage-wrapper"
        :content-styles="`width:626px;height:640px;`"
    >
        <div>
            <abc-select
                v-model="selectedType"
                :width="160"
                style="margin-bottom: 16px"
                @change="changeGoodsTypeHandler"
            >
                <abc-option
                    v-for="(option, index) in _purchaseTypes"
                    :key="index"
                    :label="option.label"
                    :value="option.value"
                ></abc-option>
            </abc-select>
        </div>
        <div v-abc-loading="loading" class="shortage-content">
            <ul v-if="shortageList.length" class="table">
                <li class="shortage-title">
                    <div class="shortage-name">
                        <AbcCheckbox v-model="checkedAll" style="color: #000" @change="changeAllChecked"
                            >药品名称
                        </AbcCheckbox>
                    </div>
                    <div class="shortage-spec">
                        <span>规格</span>
                    </div>
                    <div class="shortage-supplier">
                        <span>最近供应商</span>
                    </div>
                    <!--                    <div class="shortage-type">-->
                    <!--                        <span>类型</span>-->
                    <!--                    </div>-->
                    <div class="shortage-stock">
                        <span>剩余</span>
                    </div>
                </li>
                <li v-for="item in shortageList" :key="item.id">
                    <label class="shortage-li" style="display: flex">
                        <div class="ellipsis shortage-name">
                            <AbcCheckbox
                                v-model="item.checked"
                                style="color: #000; font-weight: 400"
                                @change="(val) => changeItemChecked(val, item)"
                            >
                                {{ item | goodsFullName }}
                            </AbcCheckbox>
                        </div>
                        <div class="ellipsis shortage-spec" :title="item | goodsSpec">
                            <span>{{ item | goodsSpec }}</span>
                        </div>

                        <div
                            class="ellipsis shortage-supplier"
                            style="width: 128px"
                            :title="item.lastStockInOrderSupplier"
                        >
                            <span>{{ item.lastStockInOrderSupplier }}</span>
                        </div>

                        <!--                        <div class="shortage-type">-->
                        <!--                            <span>{{ item | goodsTypeName }}</span>-->
                        <!--                        </div>-->
                        <div class="ellipsis shortage-stock" :title="item | complexCount">
                            <span>{{ item | complexCount }}</span>
                        </div>
                    </label>
                </li>
            </ul>

            <div v-else ref="empty" class="table-empty">
                <div class="icon"><i class="iconfont cis-icon-kongkucun"></i></div>
                <div class="label">暂无库存告警药品</div>
            </div>
        </div>

        <div slot="footer">
            <AbcButton type="primary" :disabled="btnDisabled" @click="addToOrder"> 创建采购申请 </AbcButton>
            <AbcButton type="blank" @click="showDialog = false"> 取消 </AbcButton>
        </div>

        <OrderForm
            v-if="showForm"
            v-model="showForm"
            :selected-type="selectedType"
            :shortage-goods="selectedGoods"
            @refresh="refreshHandler"
            @closeDialog="closeDialog"
        ></OrderForm>
    </AbcDialog>
</template>

<script>
    import GoodsBaseApi from 'api/goods';
    import { mapGetters } from 'vuex';
    import OrderForm from '../../purchase/order-list/form';
    import { PURCHASE_GOODS_TYPES_VALUE } from './common';
    export default {
        name: 'ShortageDialog',
        components: {
            OrderForm,
        },
        props: {
            value: Boolean,
        },
        data() {
            return {
                loading: false,
                showForm: false,
                shortageList: [],
                // 请求参数
                selectedType: '全部类型', // 选择的采购类型
                fetchParams: {
                    clinicId: '',
                    offset: 0,
                    limit: 200,
                    type: '',
                    subType: '',
                    cMSpec: '',
                },
                checkedAll: false,
                selectedMap: new Map(),
                selectedGoods: [], // 选中的需要采购药品
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),

            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            dialogTitle() {
                return this.shortageList.length ? `库存告警药品（${this.shortageList.length}）` : '库存告警药品';
            },
            btnDisabled() {
                return (
                    this.shortageList.findIndex((item) => {
                        return item.checked;
                    }) === -1
                );
            },
        },
        created() {
            this.fetchData();

            this._purchaseTypes = [
                {
                    label: '全部类型',
                    value: '全部类型',
                },
                {
                    label: '西药',
                    value: '西药',
                },
                {
                    label: '中成药',
                    value: '中成药',
                },
                {
                    label: '中药饮片',
                    value: '中药饮片',
                },
                {
                    label: '中药颗粒',
                    value: '中药颗粒',
                },
                {
                    label: '物资',
                    value: '物资',
                },
                {
                    label: '商品',
                    value: '商品',
                },
            ];
        },
        methods: {
            /**
             * @desc 获取库存告警药品  GoodsBaseApi.shortagesList({
                                    clinicId,
                                    offset,
                                    limit
                                })
             * <AUTHOR>
             * @date 2018/11/14 18:36:45
             */
            async fetchData() {
                this.loading = true;
                try {
                    this.fetchParams.clinicId = this.currentClinic.clinicId;
                    const res = await GoodsBaseApi.shortagesList(this.fetchParams);
                    this.shortageMCount = res.count; // 告警药品的数目
                    this.shortageList = res.rows.map((item) => {
                        this.$set(item, 'checked', false);
                        return item;
                    });
                    this.checkedAll = false;
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },
            /**
             * @desc 切换采购类型 重新获取库存告警药品
             * <AUTHOR>
             * @date 2020-08-17 20:54:17
             */
            async changeGoodsTypeHandler(val) {
                const { type, subType, cMSpec } = PURCHASE_GOODS_TYPES_VALUE[val];
                this.fetchParams.type = type;
                this.fetchParams.subType = subType;
                this.fetchParams.cMSpec = cMSpec;
                this.selectedMap.clear();
                await this.fetchData();
            },

            /**
             * @desc 创建采购申请
             */
            addToOrder() {
                this.selectedGoods = [...this.selectedMap.values()];
                this.showForm = true;
            },
            /**
             * @desc 关闭弹窗，更新列表
             * <AUTHOR>
             * @date 2020-08-18 17:02:33
             * @params
             * @return
             */
            refreshHandler(data) {
                this.$emit('refresh', data);
                this.showDialog = false;
            },
            /**
             * @desc 保存草稿后，更新外面的采购列表，获取采购草稿列表
             */
            closeDialog() {
                this.$emit('close');
                this.showDialog = false;
            },
            /**
             * @desc 全选药品
             * <AUTHOR>
             * @date 2020/2/28
             * @params
             * @return
             */
            changeAllChecked(val) {
                this.shortageList.forEach((item) => {
                    item.checked = val;
                    // val 为true 需要检查是否重复存在药品，不存在就添加进去
                    if (val) {
                        if (!this.selectedMap.has(item.id)) {
                            this.selectedMap.set(item.id, item);
                        }
                    } else {
                        if (this.selectedMap.has(item.id)) {
                            this.selectedMap.delete(item.id);
                        }
                    }
                    return item;
                });
            },
            changeItemChecked(val, item) {
                if (val) {
                    if (!this.selectedMap.has(item.id)) {
                        this.selectedMap.set(item.id, item);
                    }
                } else {
                    if (this.selectedMap.has(item.id)) {
                        this.selectedMap.delete(item.id);
                    }
                }
                this.checkedAll =
                    this.shortageList.findIndex((item) => {
                        return !item.checked;
                    }) === -1;
            },
        },
    };
</script>
<style lang="scss" rel="stylesheet/scss">
    @import 'src/styles/theme.scss';
    .shortage-wrapper {
        .shortage-content {
            .shortage-title {
                display: flex;
                height: 32px;
                align-items: center;
                background-color: $P5;
                border-top: 1px solid $P6;
                border-bottom: 1px solid $P6;
                padding: 0 12px;
            }

            .table-empty {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                top: 150px;

                .icon {
                    height: 50px;
                    overflow: hidden;
                    text-align: center;

                    i {
                        color: #ddd;
                        font-size: 50px;
                    }
                }

                .label {
                    text-align: center;
                    margin-top: 8px;
                    color: rgb(170, 170, 170);
                }
            }

            .shortage-name {
                flex: 1;
            }
            .shortage-spec {
                width: 100px;
                min-width: 100px;
                max-width: 100px;
            }
            .shortage-supplier {
                width: 100px;
                min-width: 100px;
                max-width: 100px;
                color: $T2;
            }
            .shortage-type {
                width: 60px;
                min-width: 60px;
                max-width: 60px;
                text-align: right;
                color: $T2;
            }
            .shortage-stock {
                width: 50px;
                min-width: 50px;
                max-width: 50px;
                text-align: right;
                color: #ff9933;
            }
        }

        .shortage-li {
            cursor: pointer;
            display: flex;
            align-items: center;
            padding: 0 12px;
            border-bottom: 1px solid #dadbe0;
            color: $T2;
            height: 48px;

            &:hover {
                cursor: pointer;
                background-color: #e9f2fe;
            }
        }
        .title {
            color: #000;
            font-weight: 500;
        }

        span {
            display: inline-block;
            font-size: 14px;
        }
        .shortage-spec,
        .shortage-sup {
            color: #687481;
            padding-left: 8px;
            padding-right: 8px;
        }

        .shortage-curr {
            width: 70px;
            color: #f93;
            text-align: right;
            padding-right: 8px;
            padding-left: 8px;

            &::after {
                content: ' ';
                display: block;
                clear: both;
            }
        }
    }
</style>
