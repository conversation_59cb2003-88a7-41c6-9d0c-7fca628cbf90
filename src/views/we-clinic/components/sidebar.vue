<template>
    <div class="we-clinic-sidebar">
        <abc-menu
            v-model="currentIndex"
            badge-variant="round"
            @click="selectItem"
        >
            <template v-for="navItem in menuList">
                <template v-if="navItem.children && navItem.children.length">
                    <abc-sub-menu
                        :key="navItem.name"
                        :icon="navItem.icon"
                        :index="navItem.path"
                        :count="navItem.count || 0"
                        :value="navItem.name"
                    >
                        <abc-text v-if="navItem.tip" slot="appendInner" :theme="navItem.path === currentIndex ? 'white' : 'warning-light'">
                            {{ navItem.tip }}
                        </abc-text>
                        <abc-menu-item
                            v-for="navChildItem in navItem.children"
                            :key="navChildItem.name"
                            :icon="navChildItem.icon"
                            :index="navChildItem.path"
                            :count="navChildItem.count || 0"
                        >
                            {{ navChildItem.name }}
                        </abc-menu-item>
                    </abc-sub-menu>
                </template>
                <abc-menu-item
                    v-else
                    :key="navItem.name"
                    :icon="navItem.icon"
                    :index="navItem.path"
                    :count="navItem.count || 0"
                >
                    <abc-text v-if="navItem.tip" slot="appendInner" :theme="navItem.path === currentIndex ? 'white' : 'warning-light'">
                        {{ navItem.tip }}
                    </abc-text>
                    {{ navItem.name }}
                </abc-menu-item>
            </template>
        </abc-menu>
    </div>
</template>

<script>
    import { WeClinicRouterNameKeys } from 'views/we-clinic/core/routes';
    import ModulePermission from 'views/permission/module-permission';
    import { mapGetters } from 'vuex';

    export default {
        name: 'SideBar',
        mixins: [ ModulePermission ],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                currentIndex: '',
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'isSingleStore',
                'isChainSubStore',
                'cisMallWaitSendTodo',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig', 'featureTreatOnline', 'featureContinueMR']),
            supportWeShopPurchase() {
                return this.viewDistributeConfig.WeClinic.supportWeShopPurchase;
            },
            NavLists() {
                const list = [];
                this.menuList.forEach((item) => {
                    if (item.children?.length) {
                        list.push(...item.children);
                    }
                });
                return this.menuList.concat(list);
            },
            menuList() {
                const { isChainSubStore } = this;

                const weShopChildren = isChainSubStore ? [
                    {
                        name: '商品管理',
                        id: '401',
                        isActive: 0,
                        routeName: 'goods-manage',
                        path: 'shop/goods-manage/goods-list',
                    },
                    {
                        name: '订单管理',
                        id: '403',
                        isActive: 0,
                        routeName: 'order-manage',
                        path: 'shop/order-manage',
                        count: this.cisMallWaitSendTodo,
                    },
                ] : [{

                         name: '商品管理',
                         id: '401',
                         isActive: 0,
                         routeName: 'goods-manage',
                         path: 'shop/goods-manage/goods-list',
                     },
                     {
                         name: '商品评价',
                         id: '402',
                         isActive: 0,
                         routeName: 'goods-comment',
                         path: 'shop/goods-comment/comment-list',
                     },
                     {
                         name: '订单管理',
                         id: '403',
                         isActive: 0,
                         routeName: 'order-manage',
                         path: 'shop/order-manage',
                         count: this.cisMallWaitSendTodo,
                     },
                     {
                         name: '营销活动',
                         id: '405',
                         isActive: 0,
                         routeName: 'marketing-activities',
                         path: 'shop/marketing-activities/we-shop-member',
                     },
                     {
                         name: '配送管理',
                         id: '404',
                         isActive: 0,
                         routeName: 'distribution-manage',
                         path: 'shop/distribution-manage/service-store',
                     }];

                const WeShopOption = this.supportWeShopPurchase ? (this.isWeShopReady ? {
                    name: '微商城',
                    id: '400',
                    icon: 's-n-weishop-line1',
                    isActive: 0,
                    up: true,
                    routeName: WeClinicRouterNameKeys.shop,
                    children: weShopChildren,
                    visible: true,
                } : {
                    name: '微商城',
                    id: '400',
                    icon: 's-n-weishop-line1',
                    isActive: 0,
                    up: true,
                    routeName: WeClinicRouterNameKeys.shopIntro,
                    path: 'shop-intro',
                    children: [],
                    visible: true,
                    tip: '未开通',
                }) : { visible: false };

                return [
                    {
                        name: '首页',
                        id: '100',
                        icon: 's-n-weizhensuo-line1',
                        isActive: 0,
                        up: false,
                        routeName: WeClinicRouterNameKeys.home,
                        path: 'home',
                        children: [],
                    },
                    // 这里先隐藏
                    // {
                    //     name: '推广',
                    //     id: '200',
                    //     icon: 'n-extend-line',
                    //     isActive: 0,
                    //     up: false,
                    //     routeName: WeClinicRouterNameKeys.promotion,
                    //     path: 'promotion',
                    //     children: [],
                    //     visible: (this.isChainAdmin || this.isSingleStore),
                    // },
                    {
                        name: '装修',
                        id: '300',
                        icon: 's-n-renovation-line1',
                        isActive: 0,
                        up: false,
                        routeName: WeClinicRouterNameKeys.decoration,
                        children: [
                            {
                                name: `微${this.$app.institutionTypeWording}主页`,
                                id: WeClinicRouterNameKeys.decorationHomePage,
                                isActive: 0,
                                routeName: WeClinicRouterNameKeys.decorationHomePage,
                                path: 'decoration/home-page',
                            },
                            {
                                name: '门店主页',
                                id: WeClinicRouterNameKeys.decorationStorePage,
                                isActive: 0,
                                routeName: WeClinicRouterNameKeys.decorationStorePage,
                                path: 'decoration/store-page',
                            },
                            {
                                name: '服务项目列表',
                                id: WeClinicRouterNameKeys.decorationServiceProjectList,
                                isActive: 0,
                                routeName: WeClinicRouterNameKeys.decorationServiceProjectList,
                                path: 'decoration/service-project-list',
                            },
                            {
                                name: '查找医生列表',
                                id: WeClinicRouterNameKeys.decorationFindDoctorList,
                                isActive: 0,
                                routeName: WeClinicRouterNameKeys.decorationFindDoctorList,
                                path: 'decoration/find-doctor-list',
                            },
                            {
                                name: '在线咨询列表',
                                id: WeClinicRouterNameKeys.decorationOnlineConsultationList,
                                isActive: 0,
                                routeName: WeClinicRouterNameKeys.decorationOnlineConsultationList,
                                path: 'decoration/online-consultation-list',
                            },
                            {
                                name: '门诊预约列表',
                                id: WeClinicRouterNameKeys.decorationOutpatientAppointmentList,
                                isActive: 0,
                                routeName: WeClinicRouterNameKeys.decorationOutpatientAppointmentList,
                                path: 'decoration/outpatient-appointment-list',
                            },
                            {
                                name: `${this.viewDistributeConfig.WeClinic.therapyUseName}预约列表`,
                                id: WeClinicRouterNameKeys.decorationTherapyAppointmentList,
                                isActive: 0,
                                routeName: WeClinicRouterNameKeys.decorationTherapyAppointmentList,
                                path: 'decoration/therapy-appointment-list',
                            },
                            {
                                name: '风格设置',
                                id: WeClinicRouterNameKeys.decorationStyleSetting,
                                isActive: 0,
                                routeName: WeClinicRouterNameKeys.decorationStyleSetting,
                                path: 'decoration/style-setting',
                            },
                        ],
                        visible: (this.isChainAdmin || this.isSingleStore),
                    },
                    WeShopOption,
                    {
                        name: '在线咨询',
                        id: '500',
                        icon: 's-n-online-consultution-line',
                        isActive: 0,
                        up: false,
                        routeName: WeClinicRouterNameKeys.onlineConsultation,
                        path: 'online-consultation',
                        children: [],
                        visible: (this.isChainAdmin || this.isSingleStore) && this.featureTreatOnline,
                    },
                    {
                        name: '在线续方',
                        id: '600',
                        icon: 's-n-online-prescription-line',
                        isActive: 0,
                        up: false,
                        routeName: WeClinicRouterNameKeys.onlinePrescription,
                        path: 'online-prescription',
                        children: [],
                        visible: (this.isChainAdmin || this.isSingleStore) && this.featureContinueMR,
                    },
                    {
                        name: '坐诊表',
                        id: '700',
                        icon: 's-n-sitting-diagnosisform-line1',
                        isActive: 0,
                        up: false,
                        routeName: WeClinicRouterNameKeys.visitTable,
                        path: 'visit-table',
                        children: [],
                        visible: (this.isChainAdmin || this.isSingleStore),
                    },
                    {
                        name: '基础设置',
                        id: '800',
                        icon: 's-n-basic-settings-line',
                        isActive: 0,
                        up: false,
                        routeName: WeClinicRouterNameKeys.baseSetting,
                        path: 'base-setting',
                        children: [],
                        visible: (this.isChainAdmin || this.isSingleStore),
                    },
                ].filter((item) => item.visible !== false);
            },
            enableWeShop() {
                return this.$abcPage.$store.state.enableWeShop;
            },
            isWeShopReady() {
                return this.$abcPage.$store.state.isWeShopReady;
            },
            weShopAuditStatus() {
                return this.$abcPage.$store.state.weShopAuditStatus;
            },
        },
        watch: {
            '$route.path': {
                handler(val) {
                    if (val) {
                        this.currentIndex = this.NavLists.find((item) => this.comparePath(item))?.path;
                    }
                },
                immediate: true,
            },
        },
        methods: {
            comparePath(val) {
                const { path } = this.$route;
                return path.includes(`/we-clinic/${val.path}`);
            },
            selectItem(index) {
                const name = this.NavLists.find((item) => item.path === index)?.routeName;
                if (name) {
                    this.redirectTo(name);
                }
            },
            redirectTo(name) {
                this.$router.push({
                    name,
                });
            },
        },
    };
</script>

<style lang="scss">
@import "~styles/theme.scss";
@import "~styles/mixin.scss";

.we-clinic-sidebar {
    height: 100%;
    padding: 10px 0 10px 10px;
    overflow-x: hidden;
    overflow-y: scroll;
    background-color: #f5f7fa;

    @include scrollBar;
}
</style>
