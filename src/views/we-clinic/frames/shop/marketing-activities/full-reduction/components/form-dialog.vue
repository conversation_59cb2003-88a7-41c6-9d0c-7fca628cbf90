<template>
    <div>
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            :auto-focus="!id"
            :title="title"
            content-styles="height:604px;"
            size="xlarge"
        >
            <abc-flex gap="large" vertical style="margin-bottom: 24px;">
                <abc-tips-card-v2 theme="primary" align="center">
                    满减返活动只对实物商品生效
                </abc-tips-card-v2>
            </abc-flex>
            <abc-form
                ref="discountForm"
                v-abc-loading="loading"
                :label-width="80"
                class="full-reduction-coupon-form"
                item-block
                item-no-margin
                label-position="left"
            >
                <biz-setting-form :label-width="56" no-limit-width>
                    <biz-setting-form-group>
                        <biz-setting-form-item label="活动名称">
                            <abc-form-item
                                :validate-event="validateName"
                                required
                            >
                                <abc-input
                                    v-model="postData.name"
                                    v-abc-focus-selected
                                    :disabled="isTakeOff"
                                    :max-length="20"
                                    :width="240"
                                    show-max-length-tips
                                    type="text"
                                    data-cy="full-reduction-coupon-name-input"
                                    @input="filterEmoji"
                                >
                                </abc-input>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="活动时间">
                            <abc-radio-group v-model="postData.isForever">
                                <abc-radio :disabled="isTakeOff" :label="1" data-cy="full-reduction-coupon-forever-radio">
                                    永久有效
                                </abc-radio>
                                <abc-flex gap="8">
                                    <abc-radio
                                        :disabled="isTakeOff"
                                        :label="0"
                                        data-cy="full-reduction-coupon-assign-date-radio"
                                    >
                                        指定时间
                                    </abc-radio>
                                    <abc-form-item v-if="postData.isForever === 0" required>
                                        <abc-date-picker
                                            v-model="validDate"
                                            type="daterange"
                                            clearable
                                            :disabled="isTakeOff"
                                            :picker-options="pickerOptions"
                                            data-cy="full-reduction-coupon-assign-date-picker"
                                            @change="handleValidDateChange"
                                        >
                                        </abc-date-picker>
                                    </abc-form-item>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <marketing-select-card-item
                            has-divider
                            label="活动对象"
                            edit-btn-text="选择会员"
                            :tag-width="148"
                            :tag-data.sync="postData.memberTypes"
                            :get-icon-function="() => 's-role-color'"
                            :is-show-card="postData.isSpecifyParticipants === 1"
                            :disabled="isTakeOff"
                            @openDialog="showMemberDialog = true"
                        >
                            <abc-text
                                v-if="!postData.isSpecifyParticipants && !postData.memberTypes.length && memberError"
                                slot="tips"
                                size="mini"
                                theme="warning-light"
                            >
                                未选择指定会员
                            </abc-text>
                            <template #radio-group>
                                <abc-radio-group
                                    v-model="postData.isSpecifyParticipants"
                                    :disabled="isTakeOff"
                                >
                                    <abc-radio
                                        :label="0"
                                        :disabled="isTakeOff"
                                        data-cy="full-reduction-coupon-all-patients"
                                    >
                                        所有人
                                    </abc-radio>
                                    <abc-radio
                                        :label="1"
                                        :disabled="isTakeOff"
                                        data-cy="full-reduction-coupon-assign-patients"
                                    >
                                        指定会员
                                    </abc-radio>
                                </abc-radio-group>
                            </template>
                        </marketing-select-card-item>

                        <biz-setting-form-item label-line-height-size="small" label="参与范围">
                            <abc-form-item>
                                <abc-flex :gap="8" style="width: 100%;" vertical>
                                    <abc-flex style="width: 100%;" align="start">
                                        <select-we-shop-goods-type
                                            :type-list="activityTypeList"
                                            :goods-list="activityGoodsList"
                                            :disabled="isTakeOff"
                                            :goods-type="ShopGoodsTypes.PHYSICAL_GOODS"
                                            :show-type-tips="false"
                                            :show-goods-tips="false"
                                            @change-goods-list="handleChangeGoodsList"
                                            @change-type-list="handleChangeTypeList"
                                        >
                                        </select-we-shop-goods-type>
                                        <abc-flex style="height: 24px;" align="center">
                                            <abc-checkbox
                                                v-model="postData.onlyForOriginalPrice"
                                                customer-style="margin-left: auto;"
                                                :disabled="isTakeOff"
                                                style="margin-right: 0;"
                                                type="number"
                                            >
                                                仅购买原价商品时可用
                                            </abc-checkbox>
                                        </abc-flex>
                                    </abc-flex>
                                    <select-we-shop-goods-list
                                        :table-data-list.sync="tableDataList"
                                        :disabled="isTakeOff"
                                        :goods-error="goodsError"
                                        :goods-type="ShopGoodsTypes.PHYSICAL_GOODS"
                                    ></select-we-shop-goods-list>
                                </abc-flex>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="优惠设置">
                            <abc-descriptions
                                v-for="(item, index) in postData.giftRules"
                                :key="`id${index}`"
                                :column="1"
                                :label-width="88"
                                grid
                                :need-input-style="false"
                                size="large"
                                label-vertical-align="center"
                            >
                                <template #title>
                                    <abc-flex justify="space-between" align="center" style="width: 100%;">
                                        <div>{{ `${computeLevel(index + 1)}` }}级优惠</div>
                                        <abc-delete-icon
                                            v-if="!isTakeOff && postData.giftRules && postData.giftRules.length > 1"
                                            theme="dark"
                                            @delete="deleteDiscount(index)"
                                        ></abc-delete-icon>
                                    </abc-flex>
                                </template>
                                <abc-descriptions-item label="优惠类型">
                                    <abc-flex justify="space-between">
                                        <abc-form-item
                                            :validate-event="orderThresholdValidate(index, item.orderThresholdPrice)"
                                            required
                                        >
                                            <abc-space size="middle">
                                                <div>
                                                    <abc-text style="margin-right: 8px;">
                                                        {{ item.isCycle ? '每' : '' }}满
                                                    </abc-text>
                                                    <abc-input
                                                        v-model="item.orderThresholdPrice"
                                                        v-abc-focus-selected
                                                        :config="{
                                                            max: 1000000,
                                                            formatLength: 2,
                                                        }"
                                                        :disabled="isTakeOff"
                                                        :width="88"
                                                        data-cy="order-threshold-price-input"
                                                        type="money"
                                                    >
                                                        <span slot="append">元</span>
                                                    </abc-input>
                                                </div>
                                                <div>
                                                    <abc-checkbox
                                                        v-model="item.isDiscounted"
                                                        :label="0"
                                                        :disabled="isTakeOff"
                                                        type="number"
                                                        data-cy="is-discounted-checkbox"
                                                    >
                                                        满减
                                                    </abc-checkbox>
                                                    <abc-checkbox
                                                        v-model="item.isGiftCoupon"
                                                        :label="1"
                                                        :disabled="isTakeOff"
                                                        type="number"
                                                        data-cy="is-gift-coupon-checkbox"
                                                    >
                                                        满返
                                                    </abc-checkbox>
                                                    <abc-checkbox
                                                        v-model="item.isGiftGoods"
                                                        :label="2"
                                                        :disabled="isTakeOff"
                                                        type="number"
                                                        data-cy="is-gift-goods-checkbox"
                                                    >
                                                        满赠
                                                    </abc-checkbox>
                                                </div>
                                            </abc-space>
                                        </abc-form-item>
                                        <abc-flex align="center">
                                            <abc-tooltip
                                                placement="top-end"
                                                theme="black"
                                                size="small"
                                                :disabled="!disabledCircle(index)"
                                                content="只可将最高级优惠设置成循环累计"
                                            >
                                                <abc-checkbox
                                                    v-model="item.isCycle"
                                                    :disabled="disabledCircle(index)"
                                                    style="margin-right: 0;"
                                                    type="number"
                                                    data-cy="is-cycle-checkbox"
                                                >
                                                    循环累计
                                                </abc-checkbox>
                                            </abc-tooltip>

                                            <abc-tooltip-info placement="bottom-end">
                                                <div>
                                                    <p>* 循环累计：可设置每满X元，进行满减/满返/满赠</p>
                                                    <p style="font-size: 12px; color: #96a4b3;">
                                                        例：若设置每满100元，立减10元。消费200元时，将立减20元/满返/满赠
                                                    </p>
                                                    <br />
                                                    <p>* 设置多级优惠时，循环累计只可在最高级优惠上设置应用</p>
                                                    <div style="font-size: 12px; color: #96a4b3;">
                                                        <p>
                                                            例：若设置一级优惠满100元，立减10元，二级优惠每满200元，立减20元
                                                        </p>
                                                        <p>若消费110元，将立减10元；</p>
                                                        <p>若消费210元，将立减20元；</p>
                                                        <p>若消费310元，将立减20元；</p>
                                                        <p>若消费410元，将立减40元</p>
                                                    </div>
                                                </div>
                                            </abc-tooltip-info>
                                            <abc-text
                                                v-if="item.noDiscount"
                                                theme="warning-light"
                                                size="mini"
                                            >
                                                未选择优惠项
                                            </abc-text>
                                        </abc-flex>
                                    </abc-flex>
                                </abc-descriptions-item>
                                <abc-descriptions-item content-padding="0" label="优惠内容">
                                    <template v-if="item.isDiscounted">
                                        <abc-form-item
                                            :validate-event="giftRuleValidate(index, item.discountedPrice)"
                                            required
                                        >
                                            <abc-space size="middle" style="padding: 8px 10px;">
                                                <abc-tag-v2
                                                    shape="round"
                                                    theme="danger"
                                                    size="mini"
                                                    variant="outline"
                                                >
                                                    满减
                                                </abc-tag-v2>
                                                <abc-space>
                                                    <span>立减</span>
                                                    <abc-input
                                                        v-model="item.discountedPrice"
                                                        v-abc-focus-selected
                                                        :config="{
                                                            max: 1000000,
                                                            formatLength: 2,
                                                        }"
                                                        :disabled="isTakeOff"
                                                        :width="88"
                                                        type="money"
                                                        data-cy="discounted-price-input"
                                                    >
                                                        <span slot="append">元</span>
                                                    </abc-input>
                                                </abc-space>
                                            </abc-space>
                                        </abc-form-item>
                                    </template>

                                    <abc-divider v-if="item.isDiscounted && (item.isGiftCoupon || item.isGiftGoods)" margin="none" variant="dashed"></abc-divider>

                                    <template v-if="item.isGiftCoupon">
                                        <abc-space :size="12" align="baseline" style="min-height: 40px; padding: 8px 10px;">
                                            <abc-tag-v2
                                                shape="round"
                                                theme="danger"
                                                size="mini"
                                                variant="outline"
                                            >
                                                满返
                                            </abc-tag-v2>
                                            <abc-flex :gap="12" align="baseline">
                                                <abc-button
                                                    v-if="!isTakeOff"
                                                    class="add-product"
                                                    type="text"
                                                    data-cy="gift-coupon-add-button"
                                                    style="flex-shrink: 0;"
                                                    @click="openGiftCouponDialog(item.giftCouponList, index)"
                                                >
                                                    添加
                                                </abc-button>
                                                <abc-flex
                                                    v-if="item.giftCouponList?.length"
                                                    wrap="wrap"
                                                    gap="middle"
                                                >
                                                    <label-input-v2
                                                        v-for="(coupon, couponIndex) in item.giftCouponList"
                                                        :key="coupon.promotionId"
                                                        v-model="coupon.count"
                                                        :config="{
                                                            max: 100,
                                                        }"
                                                        :disabled="isTakeOff"
                                                        :is-take-off="coupon.status === 9 || coupon.status === 19"
                                                        :name="coupon.promotionName"
                                                        :status-name="getStatusName(coupon.promotionStatus)"
                                                        icon="s-commodity-color"
                                                        unit="张"
                                                        data-cy="gift-coupon"
                                                        @delete="deleteGiftCoupon(item.giftCouponList, couponIndex)"
                                                    ></label-input-v2>
                                                </abc-flex>

                                                <abc-text
                                                    v-if="showGiftCouponError(item)"
                                                    theme="warning-light"
                                                    size="mini"
                                                >
                                                    未添加优惠券
                                                </abc-text>
                                            </abc-flex>
                                        </abc-space>
                                    </template>

                                    <abc-divider v-if="item.isGiftCoupon && item.isGiftGoods" margin="none" variant="dashed"></abc-divider>

                                    <template v-if="item.isGiftGoods">
                                        <abc-space :size="12" align="baseline" style="min-height: 40px; padding: 8px 10px;">
                                            <abc-tag-v2
                                                shape="round"
                                                theme="danger"
                                                size="mini"
                                                variant="outline"
                                            >
                                                满赠
                                            </abc-tag-v2>
                                            <abc-flex :gap="12" align="baseline">
                                                <abc-button
                                                    v-if="!isTakeOff"
                                                    class="add-product"
                                                    type="text"
                                                    data-cy="gift-goods-add-button"
                                                    style="flex-shrink: 0;"
                                                    @click="addGiftGoods(item.giftGoodsList, index)"
                                                >
                                                    添加
                                                </abc-button>
                                                <abc-flex
                                                    v-if="item.giftGoodsList?.length"
                                                    wrap="wrap"
                                                    gap="middle"
                                                >
                                                    <label-input-v2
                                                        v-for="(goods, giftIndex) in item.giftGoodsList"
                                                        :key="goods.goodsId"
                                                        v-model="goods.count"
                                                        :disabled="isTakeOff"
                                                        :name="goods.goodsItem ? goods.goodsItem.name : goods.name"
                                                        :unit="formatUnit(goods)"
                                                        icon="s-commodity-color"
                                                        data-cy="gift-goods"
                                                        @delete="deleteGiftGoods(item.giftGoodsList, giftIndex)"
                                                    ></label-input-v2>
                                                </abc-flex>
                                                <abc-text
                                                    v-if="showGiftGoodsError(item)"
                                                    theme="warning-light"
                                                    size="mini"
                                                >
                                                    未添加赠品
                                                </abc-text>
                                            </abc-flex>
                                        </abc-space>
                                    </template>
                                </abc-descriptions-item>
                            </abc-descriptions>
                            <div v-if="postData.giftRules.length < 3 && !isTakeOff" class="add-preferential-level">
                                <abc-tooltip
                                    placement="top-start"
                                    theme="black"
                                    size="small"
                                    :disabled="!hasCircle"
                                    :content="addDiscountTips"
                                >
                                    <abc-button
                                        type="text"
                                        @click="addDiscount"
                                    >
                                        添加{{ computeLevel(postData.giftRules.length + 1) }}级优惠
                                    </abc-button>
                                </abc-tooltip>

                                <abc-text theme="gray" size="mini">
                                    每级优惠不叠加，如：满足二级优惠条件后则不再享有一级优惠。最多支持三级优惠。
                                </abc-text>
                            </div>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>
            <div slot="footer" class="dialog-footer">
                <abc-button
                    v-if="isActive || isNoActive"
                    style="margin-right: auto;"
                    variant="ghost"
                    theme="danger"
                    @click="stopPromotion"
                >
                    终止
                </abc-button>
                <abc-button
                    v-if="isTakeOff"
                    style="margin-right: auto;"
                    variant="ghost"
                    theme="danger"
                    @click="handleDeleteActive"
                >
                    删除
                </abc-button>
                <abc-button v-if="!isTakeOff" :loading="btnLoading" @click="submit">
                    确定
                </abc-button>
                <abc-button
                    variant="ghost"
                    theme="primary"
                    @click="showDialog = false"
                >
                    取消
                </abc-button>
            </div>
        </abc-dialog>
        <!--选择赠品-->
        <select-we-shop-goods-dialog
            v-if="showGiftGoodsDialog"
            v-model="showGiftGoodsDialog"
            :select-goods-list="selectedGiftGoods"
            :goods-type="ShopGoodsTypes.PHYSICAL_GOODS"
            @change="changeGiftGoods"
        ></select-we-shop-goods-dialog>


        <!--选择优惠券-->
        <select-coupon-dialog
            v-if="showGiftCouponDialog"
            v-model="showGiftCouponDialog"
            :selected="selectedGiftCoupon"
            source="we-shop"
            @change="changeGiftCoupon"
        >
        </select-coupon-dialog>

        <member-type-transfer
            v-if="showMemberDialog"
            v-model="showMemberDialog"
            :selecteds="postData.memberTypes"
            @confirm="(list) => postData.memberTypes = list"
        >
        </member-type-transfer>
    </div>
</template>

<script>
    import MarketingSelectCardItem from 'views/marketing/components/marketing-select-card-item.vue';

    const SelectCouponDialog = () => import('views/marketing/components/select-coupon-dialog/index');
    import SelectWeShopGoodsType from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-type.vue';

    import {
        level, PromotionStatus,
    } from 'views/marketing/data/containts';

    import WeShopAPI from 'api/we-shop';
    import EmojiFilter from 'utils/emoji-filter';
    import Clone from 'utils/clone';
    import { GOODS_DISCOUNT_TYPE } from 'views/we-clinic/frames/shop/marketing-activities/constants';
    import { ActivityTypeEnum } from 'views/marketing/constants';
    import SelectWeShopGoodsList
        from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-list.vue';
    import { ShopGoodsTypes } from 'views/we-clinic/frames/shop/goods-manage/goods-list/constant';
    import {
        BizSettingForm, BizSettingFormGroup, BizSettingFormItem,
    } from '@/components-composite/setting-form';
    import LabelInputV2 from 'views/marketing/components/label-input-v2/index.vue';
    import MemberTypeTransfer from 'components/member-type-transfer/index.vue';
    const selectWeShopGoodsDialog = () => import('views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-dialog.vue');

    const giftRuleTypes = {
        'isDiscounted': 0, // 满减
        'isGiftCoupon': 1, // 满返
        'isGiftGoods': 2, // 满赠
    };

    export default {
        components: {
            MemberTypeTransfer,
            MarketingSelectCardItem,
            LabelInputV2,
            BizSettingFormItem,
            BizSettingFormGroup,
            BizSettingForm,
            SelectWeShopGoodsList,
            SelectCouponDialog,
            SelectWeShopGoodsType,
            selectWeShopGoodsDialog,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            id: {
                type: String,
                default: '',
            },
            isCopy: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                ShopGoodsTypes,
                GOODS_DISCOUNT_TYPE,
                giftRuleTypes,
                isSpecifyParticipants: 1,
                postData: {
                    name: '',
                    type: ActivityTypeEnum.FULL_REDUCTION,
                    isForever: 1, // 是否永久有效
                    beginDate: '',
                    endDate: '',
                    isSpecifyParticipants: 0, // 是否指定参与人范围
                    onlyForOriginalPrice: 1, // 是否仅购买原价商品时可用
                    memberTypes: [],
                    goodsList: [],
                    giftRules: [
                        {
                            id: '', // 优惠规则id
                            includeGiftTypes: [], // 0：满减；1：满返；2：满赠
                            orderThresholdPrice: '', // 订单达到金额
                            discountedPrice: '', // 优惠金额
                            isDiscounted: 0, // 是否有满减
                            isGiftCoupon: 0, // 是否有满减
                            isGiftGoods: 0, // 是否有满减
                            giftCouponList: [],
                            giftGoodsList: [],
                            isCycle: 0, // 是否循环立减
                            noDiscount: false,
                            noGiftCoupon: false,
                            noGiftGoods: false,
                        },
                    ],
                },
                validDate: [],
                pickerOptions: {
                    disabledDate: this.disabledBeginDate,
                    yearRange: {
                        end: 2038,
                    },
                },
                tableDataList: [], // 参与范围表格数据
                loading: false,
                btnLoading: false,
                memberError: false,

                // 优惠设置 满减 参数
                showGiftGoodsDialog: false,
                giftGoodsIndex: -1,
                selectedGiftGoods: [],
                // 优惠设置 满返 参数
                showGiftCouponDialog: false,
                showMemberDialog: false,
                giftCouponIndex: -1,
                selectedGiftCoupon: [],

                goodsError: false,
                selectError: false,

                showGoodsListDialog: false,
                expectGoodsList: [],
                expectItemTypeId: '',
                expectGoodsTypeId: '',
                expectSubGoodsTypeId: '',
                showReverseType: [1, 2, 3, 4, 7],
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            title() {
                return this.id && !this.isCopy ? '编辑满减返活动' : '新增满减返活动';
            },
            isTakeOff() {
                return this.postData.status === PromotionStatus.TAKE_OFF;
            },
            isNoActive() {
                return this.postData.status === PromotionStatus.NO_ACTIVE;
            },
            isActive() {
                return this.postData.status === PromotionStatus.ACTIVE;
            },
            hasCircle() {
                if (!this.postData.giftRules || !this.postData.giftRules.length) return false;
                const lastGiftRules = this.postData.giftRules[this.postData.giftRules.length - 1];
                return !!lastGiftRules.isCycle;
            },
            addDiscountTips() {
                if (this.postData.giftRules) {
                    return `无法添加。若需添加${
                        this._capitalNumber[this.postData.giftRules.length + 1]
                    }级优惠，需要取消${
                        this._capitalNumber[this.postData.giftRules.length]
                    }级优惠的循环累计设置（只可将最高级优惠设置成循环累计）`;
                }
                return '';
            },
            activityGoodsList() {
                return this.tableDataList.filter((item) => item.goodsDiscountType === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT);
            },
            activityTypeList() {
                return this.tableDataList.filter((item) => item.goodsDiscountType === GOODS_DISCOUNT_TYPE.CATEGORY);
            },
        },
        async created() {
            this._capitalNumber = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
            if (this.id) {
                this.fetchData();
            }
        },
        methods: {
            handleChangeRadioGroup(val) {
                this.isSpecifyParticipants = val;
                this.postData.isSpecifyParticipants = this.isSpecifyParticipants ? 0 : 1;
            },
            checkedDiscounted(item) {
                return item?.includeGiftTypes?.includes(giftRuleTypes.isDiscounted);
            },
            checkedGiftCoupon(item) {
                return item?.includeGiftTypes?.includes(giftRuleTypes.isGiftCoupon);
            },
            checkedGiftGoods(item) {
                return item?.includeGiftTypes?.includes(giftRuleTypes.isGiftGoods);
            },
            // 选择满减的单位展示
            formatUnit(goods) {
                if (goods) {
                    return goods.stockUnit || '件';
                }
                return '';
            },
            filterEmoji(val) {
                this.postData.name = EmojiFilter(val);
            },
            getStatusName(status) {
                let name = '';
                switch (status) {
                    case 1:
                        name = '';
                        break;
                    case 9:
                        name = '停止发券';
                        break;
                    case 19:
                        name = '已失效';
                        break;
                    default:
                        name = '已失效';
                        break;
                }
                return name;
            },
            disabledBeginDate(time) {
                const isEndTime = new Date(2038, 0, 18).getTime();
                let flag = false;
                const oneDayTimestamp = 24 * 60 * 60 * 1000;
                if (this.postData.endDate) {
                    flag = time.getTime() >= new Date(this.postData.endDate).getTime() - oneDayTimestamp;
                }
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            disabledEndDate(time) {
                const isEndTime = new Date(2038, 0, 18).getTime();
                let flag = false;
                const oneDayTimestamp = 24 * 60 * 60 * 1000;
                if (this.postData.beginDate) {
                    flag = time.getTime() <= new Date(this.postData.beginDate).getTime() - oneDayTimestamp;
                }
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            async fetchData() {
                this.loading = true;
                try {
                    let { data } = await WeShopAPI.fetchMallPromotionsGiftsDetail(this.id);
                    if (this.isCopy) {
                        data = this.copyHandler(data);
                    } else {
                        data.giftRules = data.giftRules.map((item) => {
                            item.noDiscount = false;
                            item.noGiftGoods = false;
                            item.noGiftCoupon = false;
                            item.isDiscounted = item.includeGiftTypes.includes(giftRuleTypes.isDiscounted);
                            item.isGiftCoupon = item.includeGiftTypes.includes(giftRuleTypes.isGiftCoupon);
                            item.isGiftGoods = item.includeGiftTypes.includes(giftRuleTypes.isGiftGoods);
                            return item;
                        });
                    }
                    // 处理时间
                    if (data.beginDate && data.endDate) {
                        this.validDate = [data.beginDate, data.endDate];
                    }
                    this.postData = data;
                    this.postData.beginDate = this.postData.beginLocalDate;
                    this.postData.endDate = this.postData.endLocalDate;
                    this.postData.goodsList = (this.postData.goodsList || []).map((item) => {
                        return {
                            ...item,
                            ...(item.type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT && { goodsId: item.relatedValue || '' }),
                            ...(item.type === GOODS_DISCOUNT_TYPE.CATEGORY && { typeId: item.relatedValue || '' }),
                            name: item.relatedName || '',
                            goodsDiscountType: item.type,
                        };
                    });
                    this.tableDataList = Clone(this.postData.goodsList);
                    this.isSpecifyParticipants = this.postData?.isSpecifyParticipants ? 1 : 0;
                    this.postData.memberTypes = (data.memberTypes || []).map((item) => {
                        return {
                            id: item.memberTypeId,
                            name: item.memberTypeName,
                        };
                    });
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },
            copyHandler(data) {
                data.name = '';
                delete data.status;
                delete data.id;
                if (!data.isForever) {
                    data.beginDate = '';
                    data.endDate = '';
                }
                data.giftRules = data.giftRules.map((item) => {
                    item.noDiscount = false;
                    item.noGiftGoods = false;
                    item.noGiftCoupon = false;
                    delete item.id;
                    return item;
                });
                data.goodsList = data.goodsList.map((item) => {
                    if (item.exceptItems?.length) {
                        item.exceptItems = item.exceptItems.map((exceptItem) => {
                            delete exceptItem.id;
                            return exceptItem;
                        });
                    }
                    delete item.id;
                    return item;
                });
                return data;
            },
            disabledCircle(index) {
                return this.isTakeOff || index !== this.postData.giftRules.length - 1;
            },
            validateName(val, callback) {
                if (!val.trim()) {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
                callback({
                    validate: true,
                });
            },
            orderThresholdValidate(index, val) {
                if (Number(val) <= 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能填0',
                        });
                    };
                }
                if (index === 1) {
                    if (
                        this.postData.giftRules[0] &&
                        Number(this.postData.giftRules[0].orderThresholdPrice) >= Number(val)
                    ) {
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: '必须比一级优惠大',
                            });
                        };
                    }
                }
                if (index === 2) {
                    if (
                        this.postData.giftRules[1] &&
                        Number(this.postData.giftRules[1].orderThresholdPrice) >= Number(val)
                    ) {
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: '必须比二级优惠大',
                            });
                        };
                    }
                }
            },
            giftRuleValidate(index, val) {
                if (Number(val) <= 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能填0',
                        });
                    };
                }
                if (
                    this.postData.giftRules[index] &&
                    Number(val) > this.postData.giftRules[index].orderThresholdPrice
                ) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能填大于满减金额',
                        });
                    };
                }
                if (index === 1) {
                    if (
                        this.postData.giftRules[0] &&
                        Number(this.postData.giftRules[0].discountedPrice) >= Number(val)
                    ) {
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: '必须比一级优惠大',
                            });
                        };
                    }
                }
                if (index === 2) {
                    if (
                        this.postData.giftRules[1] &&
                        Number(this.postData.giftRules[1].discountedPrice) >= Number(val)
                    ) {
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: '必须比二级优惠大',
                            });
                        };
                    }
                }
            },

            checked(item) {
                return item.selects.map((item) => item.name);
            },

            validate(val) {
                if (Number(val) <= 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '优惠门槛不能为0',
                        });
                    };
                }
            },

            // 计算几级优惠，最多三级根据数组的长度计算并翻译成大写
            computeLevel(index) {
                const levelItem = level.find((item) => item.level === index);
                return levelItem ? levelItem.alias : '一';
            },
            // 添加x级优惠
            addDiscount() {
                if (this.hasCircle) return false;
                const newLevel = {
                    orderThresholdPrice: '',
                    discountedPrice: '',
                    includeGiftTypes: [],
                    isCycle: 0, // 是否循环立减
                    giftCouponList: [],
                    giftGoodsList: [],
                    noDiscount: false,
                    noGiftCoupon: false,
                    noGiftGoods: false,
                    isDiscounted: 0, // 是否有满减
                    isGiftCoupon: 0, // 是否有满返
                    isGiftGoods: 0, // 是否有满减
                };
                this.postData.giftRules.push(newLevel);
            },
            deleteDiscount(index) {
                this.postData.giftRules.splice(index, 1);
            },
            addGiftGoods(giftGoodsList, index) {
                this.showReverseType = [1, 2, 3, 4, 7, 11];
                this.giftGoodsIndex = index;
                this.selectedGiftGoods = (giftGoodsList || []).map((item) => {
                    return {
                        ...item,
                        name: (item.goodsItem ? item.goodsItem.name : item.name) || '',
                        goodsDiscountType: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                    };
                });
                this.showGiftGoodsDialog = true;
            },
            changeGiftGoods(giftGoodsList) {
                this.postData.giftRules[this.giftGoodsIndex].giftGoodsList = giftGoodsList;
            },
            deleteGiftGoods(giftGoodsList, index) {
                giftGoodsList.splice(index, 1);
            },
            openGiftCouponDialog(giftCouponList, index) {
                this.giftCouponIndex = index;
                this.selectedGiftCoupon = giftCouponList?.map((item) => {
                    return {
                        ...item,
                        name: item.promotionName || '',
                        status: item.promotionStatus || '',
                    };
                }) || [];
                this.showGiftCouponDialog = true;
            },
            changeGiftCoupon(list) {
                this.postData.giftRules[this.giftCouponIndex].giftCouponList = list?.map((item) => {
                    return {
                        ...item,
                        promotionName: item.name,
                        promotionStatus: item.status,
                    };
                });
            },
            deleteGiftCoupon(giftCouponList, index) {
                giftCouponList.splice(index, 1);
            },
            hasSelectMemberTypes() {
                return !this.postData.isSpecifyParticipants || (this.postData.memberTypes && this.postData.memberTypes.length);
            },
            showGiftCouponError(item) {
                return item.noGiftCoupon && (!item.giftCouponList || !item.giftCouponList.length);
            },
            showGiftGoodsError(item) {
                return item.noGiftGoods && (!item.giftGoodsList || !item.giftGoodsList.length);
            },
            hasDiscount(item) {
                return item.isDiscounted || item.isGiftCoupon || item.isGiftGoods;
            },
            submit() {
                this.goodsError = false;
                this.$refs.discountForm.validate(async (valid) => {
                    if (valid) {
                        if (this.postData.isSpecifyParticipants && !this.postData.memberTypes?.length) {
                            this.memberError = true;
                            return false;
                        }
                        if (!this.tableDataList?.length) {
                            this.goodsError = true;
                            return false;
                        }

                        let errFlag = false;
                        for (let i = 0, len = this.postData.giftRules.length; i < len; i++) {
                            const giftRule = this.postData.giftRules[i];
                            giftRule.noDiscount = false;
                            if (!this.hasDiscount(giftRule)) {
                                giftRule.noDiscount = true;
                                errFlag = true;
                            }
                            giftRule.noGiftCoupon = false;
                            if (this.checkedGiftCoupon(giftRule) && !giftRule.giftCouponList.length) {
                                giftRule.noGiftCoupon = true;
                                errFlag = true;
                            }
                            giftRule.noGiftGoods = false;
                            if (this.checkedGiftGoods(giftRule) && !giftRule.giftGoodsList.length) {
                                giftRule.noGiftGoods = true;
                                errFlag = true;
                            }
                        }
                        if (errFlag) return false;
                        const data = this.transPostData();
                        this.btnLoading = true;
                        try {
                            const isConflict = await this.checkPromotion(data);
                            if (isConflict) {
                                this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: `活动时间、对象相同时，同一分类/单品的满减返活动，只能存在一个。
                                            冲突商品已用 <i class="iconfont cis-icon-Attention conflict-icon"></i> 标识。`,
                                    onClose: () => {
                                        this.btnLoading = false;
                                    },
                                });
                            } else {
                                if (!this.id || this.isCopy) {
                                    await this.createPromotion(data);
                                } else {
                                    await this.updatePromotion(data);
                                }
                            }
                            this.btnLoading = false;
                        } catch (error) {
                            console.error(error);
                            this.btnLoading = false;
                        }
                    }
                });
            },
            transPostData() {
                let {
                    giftRules,
                    goodsList,
                } = this.postData;

                goodsList = this.tableDataList?.map((item) => {
                    const exceptGoodsIds = item.exceptItems?.map((it) => it.goodsId || item.id);
                    const { type } = item;
                    let relatedValue = '';
                    if (item.id) {
                        relatedValue = item.relatedValue;
                    } else if (type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT) {
                        relatedValue = item.goodsId || '';
                    } else {
                        relatedValue = item.typeId || '';
                    }

                    return {
                        id: item.id || '',
                        type,
                        relatedName: item.id ? item.relatedName : item.name || '',
                        relatedValue,
                        exceptGoodsIds,
                    };
                });
                giftRules = giftRules.map((item) => {
                    let {
                        giftCouponList, giftGoodsList,
                    } = item;

                    giftCouponList = item.isGiftCoupon ? giftCouponList.map((it) => {
                        return {
                            promotionId: it.promotionId || it.id,
                            count: it.count,
                        };
                    }) : [];
                    giftGoodsList = item.isGiftGoods ? giftGoodsList.map((it) => {
                        return {
                            goodsId: it.goodsId,
                            count: it.count,
                        };
                    }) : [];
                    const includeGiftTypes = [item.isDiscounted ? 0 : null, item.isGiftCoupon ? 1 : null, item.isGiftGoods ? 2 : null].filter((it) => it !== null);

                    return {
                        discountedPrice: item.discountedPrice,
                        orderThresholdPrice: item.orderThresholdPrice,
                        isCycle: item.isCycle,
                        includeGiftTypes,
                        giftCouponList,
                        giftGoodsList,

                    };
                });

                const memberTypeIds = this.postData.memberTypes.map((item) => item.id);

                const data = Object.assign({}, this.postData, {
                    giftRules, goodsList, memberTypeIds,
                });
                delete data.memberTypes;
                return data;
            },
            // 创建满减活动
            async createPromotion(data) {
                try {
                    await WeShopAPI.createMallPromotionsGifts(data);
                    this.btnLoading = false;
                    this.$emit('update-list', true);
                    this.$Toast({
                        type: 'success',
                        message: '创建成功',
                    });
                    this.showDialog = false;
                } catch (e) {
                    this.btnLoading = false;
                    if (e.code === 28001) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${e.message}`,
                        });
                    }
                }
            },
            // 更新满减活动
            async updatePromotion(data) {
                try {
                    await WeShopAPI.updateMallPromotionsGifts(this.id, data);
                    this.$emit('update-list');
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.btnLoading = false;
                    this.showDialog = false;
                } catch (e) {
                    this.btnLoading = false;
                    if (e.code === 28001) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${e.message}`,
                        });
                    }
                }
            },
            // 活动冲突检查
            async checkPromotion(postData) {
                try {
                    const { data } = await WeShopAPI.checkMallPromotionsGiftsConflict(postData);
                    const { rows } = data;
                    if (rows?.length) {
                        rows.forEach((item) => {
                            this.tableDataList.forEach((goods) => {
                                if (`${goods.relatedValue}` === item.relatedValue) {
                                    this.$set(goods, 'isConflict', true);
                                    this.$set(goods, 'conflictMessage', item?.conflictAlertMessage || '');
                                }
                            });
                        });
                        return true;
                    }
                    return false;
                } catch (e) {
                    console.log('checkPromotion error', e);
                }
            },

            async stopPromotion() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '结束活动后不能恢复，是否确定结束？',
                    onConfirm: async () => {
                        await WeShopAPI.endMallPromotionsGifts(this.id);
                        this.$Toast({
                            type: 'success',
                            message: '操作成功',
                        });
                        this.$emit('update-list');
                        this.showDialog = false;
                    },
                });
            },
            async handleDeleteActive() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除？',
                    onConfirm: async () => {
                        await WeShopAPI.deleteMallPromotionsGifts(this.id);
                        this.$Toast({
                            type: 'success',
                            message: '删除成功',
                        });
                        this.$emit('update-list', true);
                        this.showDialog = false;
                    },
                });
            },
            goodsItemsCount(goodItem) {
                return (goodItem.count || 0) - (goodItem.exceptItems ? goodItem.exceptItems.length : 0) || 1;
            },
            goodsItemsTotalCount() {
                let count = 0;
                this.tableDataList.forEach((item) => {
                    if (item.goodsDiscountType === GOODS_DISCOUNT_TYPE.CATEGORY) {
                        count = count + this.goodsItemsCount(item);
                    } else {
                        count++;
                    }
                });
                return count;
            },
            handleChangeGoodsList(selectedList) {
                const goodsList = selectedList.map((item) => {
                    return {
                        ...item,
                        type: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                        relatedValue: item.goodsId,
                    };
                });
                this.tableDataList = [...this.activityTypeList, ...goodsList];
            },
            handleChangeTypeList(selectedList) {
                const typeList = Clone(this.activityTypeList);
                selectedList.forEach((item) => {
                    if (!typeList.find((it) => it.typeId === item.typeId)) {
                        this.$set(item, 'exceptItems', []);
                        this.$set(item, 'type', GOODS_DISCOUNT_TYPE.CATEGORY);
                        this.$set(item, 'relatedValue', item.typeId);
                        delete item.id;
                        typeList.push(item);
                    }
                });

                typeList.forEach((item, index) => {
                    if (!selectedList.some((it) => it.typeId === item.typeId)) {
                        typeList.splice(index, 1);
                    }
                });
                this.tableDataList = [...typeList, ...this.activityGoodsList];
            },
            handleValidDateChange(Date) {
                this.postData.beginDate = Date[0] || '';
                this.postData.endDate = Date[1] || '';
            },
            handleGoodsListChange() {
                this.goodsError = false;
            },
        },
    };
</script>
