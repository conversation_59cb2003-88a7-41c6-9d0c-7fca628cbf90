<template>
    <div class="full-reduction-coupon-wrapper">
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            :title="title"
            :auto-focus="!id"
            size="xlarge"
            custom-class="coupon-form-dialog"
        >
            <abc-form
                ref="discountForm"
                v-abc-loading="loading"
                item-block
                item-no-margin
                label-position="left"
                class="full-reduction-coupon-form"
            >
                <biz-setting-form :label-width="56" :no-limit-width="true">
                    <biz-setting-form-group>
                        <biz-setting-form-item has-divider label="优惠券名">
                            <abc-form-item required>
                                <abc-input
                                    v-model="postData.name"
                                    v-abc-focus-selected
                                    type="text"
                                    :width="340"
                                    :max-length="20"
                                    show-max-length-tips
                                    :disabled="isInvalid"
                                    @input="filterEmoji"
                                ></abc-input>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item has-divider label="使用范围">
                            <select-we-shop-goods-type
                                :type-list="activityTypeList"
                                :goods-list="activityGoodsList"
                                :disabled="isInvalid || isTakeOff || isActiveReceive"
                                type-tips=""
                                goods-tips=""
                                @change-goods-list="handleChangeGoodsList"
                                @change-type-list="handleChangeTypeList"
                            >
                                <template #table>
                                    <select-we-shop-goods-list
                                        :table-data-list.sync="tableDataList"
                                        :disabled="isInvalid || isTakeOff || isActiveReceive"
                                        :goods-error="goodsError"
                                    ></select-we-shop-goods-list>
                                </template>
                            </select-we-shop-goods-type>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="面值" required>
                            <abc-form-item required>
                                <abc-input
                                    v-model="postData.coupon.discountedPrice"
                                    v-abc-focus-selected
                                    :width="60"
                                    type="money"
                                    :config="{
                                        max: 9999999,
                                        formatLength: 2,
                                    }"
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                >
                                    <span slot="append">元</span>
                                </abc-input>
                            </abc-form-item>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="使用门槛"
                            required
                            has-divider
                        >
                            <abc-form-item required :validate-event="validateOrderThreshold">
                                <abc-radio-group v-model="orderThresholdStatus" @change="changeOrderThresholdStatus">
                                    <abc-radio :disabled="isInvalid || isTakeOff || isActiveReceive" :label="0">
                                        无门槛
                                    </abc-radio>

                                    <abc-radio :disabled="isInvalid || isTakeOff || isActiveReceive" :label="1">
                                        <span>满</span>
                                        <abc-input
                                            v-model="postData.coupon.orderThresholdPrice"
                                            v-abc-focus-selected
                                            type="money"
                                            :width="80"
                                            :config="{
                                                max: 100000,
                                                formatLength: 2,
                                            }"
                                            :disabled="!orderThresholdStatus || isInvalid || isActiveReceive"
                                        >
                                            <span slot="append">元</span>
                                        </abc-input>
                                        <abc-text>可用</abc-text>
                                    </abc-radio>
                                </abc-radio-group>
                            </abc-form-item>
                            <div>
                                <abc-checkbox
                                    v-model="postData.onlyForOriginalPrice"
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                    type="number"
                                >
                                    仅购买原价商品时可用
                                </abc-checkbox>
                            </div>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="使用数量">
                            <abc-radio-group v-model="postData.coupon.maxUseCountPerOrder">
                                <abc-radio
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                    :label="1"
                                >
                                    单次消费仅限使用 1 张
                                </abc-radio>
                                <abc-radio
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                    :label="-1"
                                >
                                    单次消费不限使用数量
                                </abc-radio>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="使用期限" has-divider :use-inner-layout="false">
                            <abc-form-item :validate-event="validateValidDays">
                                <abc-radio-group
                                    v-model="postData.coupon.validType"
                                    @change="changeValidType"
                                >
                                    <abc-flex vertical gap="12">
                                        <abc-radio :label="0" :disabled="isInvalid || isActiveReceive">
                                            <abc-text>领券后</abc-text>
                                            <abc-input
                                                v-model="postData.coupon.validDays"
                                                v-abc-focus-selected
                                                type="number"
                                                :width="50"
                                                style="margin: 0 4px;"
                                                :config="{
                                                    max: 2000,
                                                }"
                                                :input-custom-style="{
                                                    textAlign: 'center',
                                                }"
                                                :disabled="isInvalid || isTakeOff || isActiveReceive || !!postData.coupon.validType"
                                            >
                                                <span slot="append">天</span>
                                            </abc-input>
                                            <abc-text>内</abc-text>
                                            <abc-popover
                                                trigger="hover"
                                                placement="top"
                                                style="display: inline-flex;"
                                                theme="yellow"
                                            >
                                                <abc-icon
                                                    slot="reference"
                                                    icon="info_bold"
                                                    size="14"
                                                    :color="$store.state.theme.style.P3"
                                                >
                                                </abc-icon>
                                                <div>
                                                    <p>有效期按自然天计算。</p>
                                                    <p>举例：如设置领券当日起2天内可用，用户在1月21日14:00时领取优惠券，</p>
                                                    <p>则该优惠券的可用时间为1月21日的14:00:00至1月22日的23:59:59。</p>
                                                </div>
                                            </abc-popover>
                                        </abc-radio>
                                        <abc-flex gap="8">
                                            <abc-radio :label="1" :disabled="isInvalid || isTakeOff || isActiveReceive">
                                                指定日期
                                            </abc-radio>
                                            <abc-form-item v-if="postData.coupon.validType" required>
                                                <abc-date-picker
                                                    v-model="validDate"
                                                    type="daterange"
                                                    clearable
                                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                                    :picker-options="pickerOptions"
                                                    @change="handleValidDateChange"
                                                >
                                                </abc-date-picker>
                                            </abc-form-item>
                                        </abc-flex>
                                    </abc-flex>
                                </abc-radio-group>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="发行总量" required class="reduction-coupon-item">
                            <abc-form-item required>
                                <abc-input
                                    v-model="postData.coupon.totalCount"
                                    v-abc-focus-selected
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                    placeholder="最多1000000张，发行后只能增加不能减少"
                                    type="number"
                                    :width="300"
                                    :config="{
                                        max: 1000000,
                                    }"
                                ></abc-input>
                                <abc-button
                                    v-if="!isInvalid && !isTakeOff"
                                    variant="text"
                                    theme="primary"
                                    size="small"
                                    style="margin-left: 8px;"
                                    @click="handleAddIssuance"
                                >
                                    增加
                                </abc-button>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <marketing-select-card-item
                            label="领取范围"
                            edit-btn-text="选择会员"
                            :tag-width="148"
                            :tag-data.sync="postData.memberTypes"
                            :get-icon-function="() => 's-role-color'"
                            :is-show-card="!!postData.isSpecifyParticipants"
                            :item-display-name="(item) => item.organ ? item.organ?.name : item.name"
                            :disabled="isTakeOff || isInvalid"
                            @openDialog="showMemberDialog = true"
                        >
                            <template
                                v-if="postData.isSpecifyParticipants && !postData.memberTypes.length && memberError"
                                #tips
                            >
                                未选择指定会员
                            </template>
                            <template #radio-group>
                                <abc-radio-group
                                    v-model="postData.isSpecifyParticipants"
                                    :disabled="isTakeOff || isInvalid"
                                >
                                    <abc-radio
                                        :label="0"
                                        :disabled="isTakeOff || isInvalid"
                                    >
                                        所有患者
                                    </abc-radio>
                                    <abc-radio
                                        :label="1"
                                        :disabled="isTakeOff || isInvalid"
                                    >
                                        指定会员
                                    </abc-radio>
                                </abc-radio-group>
                            </template>
                        </marketing-select-card-item>

                        <biz-setting-form-item
                            label="领取数量"
                            :validate-event="validateLimitCount"
                            required
                            has-divider
                        >
                            <abc-radio-group
                                v-model="postData.coupon.isLimitObtainCount"
                                @change="changeLimitObtainCount"
                            >
                                <abc-radio :label="0" :disabled="isInvalid || isTakeOff">
                                    <span>不限数量</span>
                                </abc-radio>

                                <abc-radio :label="1" :disabled="isInvalid || isTakeOff">
                                    <span>每人</span>
                                    <abc-input
                                        v-model="postData.coupon.obtainCountPerUser"
                                        v-abc-focus-selected
                                        :width="80"
                                        type="number"
                                        :input-custom-style="{ textAlign: 'center' }"
                                        :config="{ max: 10000 }"
                                        max-length="10000"
                                        :disabled="!postData.coupon.isLimitObtainCount || isInvalid || isTakeOff"
                                    ></abc-input>
                                    <span>张</span>
                                </abc-radio>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="免费领取" has-divide>
                            <biz-setting-form-item-tip :tip="isOpenMp ? `开启后，用户可在微${$app.institutionTypeWording}【我的-优惠券】中可免费领取` : ''">
                                <abc-checkbox
                                    v-model="postData.coupon.isFreeObtain"
                                    :disabled="true"
                                    :class="{ 'abc-tipsy--n': !isOpenMp }"
                                    :data-tipsy="`需要开通微${$app.institutionTypeWording}`"
                                    type="number"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>

                            <abc-form-item v-if="postData.coupon.isFreeObtain" required>
                                <abc-date-picker
                                    v-model="obtainDate"
                                    type="daterange"
                                    clearable
                                    :disabled="isTakeOff || isInvalid"
                                    :picker-options="pickerOptions"
                                    @change="handleObtainDateChange"
                                >
                                </abc-date-picker>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <div class="supplement">
                            <div class="coupon-box">
                                <abc-tag-v2
                                    class="preview-tag"
                                    variant="dark"
                                    theme="warning"
                                    size="tiny"
                                >
                                    预览
                                </abc-tag-v2>
                                <img class="preview-img" src="../../../../../../assets/images/marketing-coupon.png" alt="" />
                                <abc-flex justify="space-between" align="center" style="height: 26px;">
                                    <abc-text style="flex-shrink: 0;" size="large" bold>
                                        {{ postData.name }}
                                    </abc-text>
                                    <div class="money">
                                        <span style="font-size: 14px;">¥</span>
                                        <span style="font-size: 26px; font-weight: 500;">{{ postData.coupon.discountedPrice }}</span>
                                    </div>
                                </abc-flex>

                                <abc-flex justify="space-between" align="flex-end">
                                    <abc-text v-if="postData.coupon.validType" style="flex-shrink: 0;" size="mini">
                                        {{ postData.coupon.validBegin }}-{{ postData.coupon.validEnd }}
                                    </abc-text>
                                </abc-flex>

                                <abc-divider variant="dashed"></abc-divider>

                                <abc-flex vertical>
                                    <abc-text
                                        v-if="postData.coupon.orderThresholdPrice"
                                        size="mini"
                                        theme="gray-light"
                                    >
                                        满{{ postData.coupon.orderThresholdPrice }}元可用
                                    </abc-text>
                                    <abc-text
                                        v-if="!postData.coupon.validType"
                                        size="mini"
                                        theme="gray-light"
                                        class="show-max-line-two"
                                    >
                                        有效期：领券后 {{ postData.coupon.validDays || '' }} 天内
                                    </abc-text>
                                    <abc-text
                                        v-if="useProductTips"
                                        size="mini"
                                        theme="gray-light"
                                        class="show-max-line-two"
                                    >
                                        适用商品：{{ useProductTips }}
                                    </abc-text>
                                    <abc-text size="mini" theme="gray-light" class="show-max-line-two">
                                        {{ limitTips }}
                                    </abc-text>
                                    <abc-text
                                        v-if="postData.coupon.remark"
                                        size="mini"
                                        theme="gray-light"
                                        class="show-max-line-two"
                                    >
                                        补充说明：{{ postData.coupon.remark }}
                                    </abc-text>
                                </abc-flex>
                            </div>
                            <abc-textarea
                                v-model="postData.coupon.remark"
                                :width="341"
                                :height="200"
                                :disabled="isInvalid"
                                placeholder="补充说明"
                                :maxlength="maxLength"
                                show-max-length-tips
                            >
                            </abc-textarea>
                        </div>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <div slot="footer" class="dialog-footer">
                <div class="finish">
                    <template v-if="postData.id">
                        <abc-button
                            v-if="!isTakeOff && !isInvalid"
                            variant="ghost"
                            theme="danger"
                            @click="stopConfirm"
                        >
                            停止发券
                        </abc-button>
                        <abc-button
                            v-if="!isInvalid"
                            variant="ghost"
                            theme="danger"
                            @click="cancelConfirm"
                        >
                            作废
                        </abc-button>
                    </template>
                </div>
                <abc-button v-if="!isInvalid" :disabled="btnLoading" @click="confirm">
                    确定
                </abc-button>
                <abc-button variant="ghost" @click="showDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
        <member-type-transfer
            v-if="showMemberDialog"
            v-model="showMemberDialog"
            :selecteds="postData.memberTypes"
            @confirm="(list) => postData.memberTypes = list"
        >
        </member-type-transfer>
        <increase-issuance-dialog
            v-if="showAddIssuance"
            v-model="showAddIssuance"
            :count="postData.coupon.totalCount"
            @cancel="handleHide"
            @save="handleUpdateCoupon"
        ></increase-issuance-dialog>
    </div>
</template>

<script>
    import { parseTime } from 'utils/index';
    import EmojiFilter from 'utils/emoji-filter';
    import { mapGetters } from 'vuex';

    import ClinicAPI from 'api/clinic';
    import {
        isChainSubClinic,
    } from 'src/views/common/clinic.js';
    import {
        level, PromotionStatus,
    } from 'views/marketing/data/containts';
    import IncreaseIssuanceDialog from 'views/marketing/coupon/increase-issuance-dialog.vue';
    import WeShopAPI from 'api/we-shop';
    import MarketingAPI from 'api/marketing';
    import SelectWeShopGoodsType
        from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-type.vue';
    import { GOODS_DISCOUNT_TYPE } from 'views/we-clinic/frames/shop/marketing-activities/constants';
    import Clone from 'utils/clone';
    import SelectWeShopGoodsList
        from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-list.vue';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';
    import BizSettingFormItemTip from '@/components-composite/setting-form/src/views/tip.vue';
    import MarketingSelectCardItem from '@/views/marketing/components/marketing-select-card-item.vue';
    import AbcUiThemeMixin from 'views/common/abc-ui-theme-mixin';
    import MemberTypeTransfer from 'components/member-type-transfer/index.vue';

    const oneDayTimestamp = 24 * 60 * 60 * 1000;

    export default {

        components: {
            MemberTypeTransfer,
            SelectWeShopGoodsList,
            IncreaseIssuanceDialog,
            SelectWeShopGoodsType,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            MarketingSelectCardItem,
            BizSettingFormItemTip,
        },
        mixins: [AbcUiThemeMixin],
        props: {
            id: {
                required: true,
            },
            value: Boolean,
            isCopy: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                GOODS_DISCOUNT_TYPE,
                memberError: false,
                goodsError: false,
                allMemberCardTypeTotal: 0,
                showAddIssuance: false,
                maxLength: 200,
                obtainDate: [],
                validDate: [],
                showMemberDialog: false,
                loading: false,
                btnLoading: false,
                postData: {
                    name: '',
                    type: '',
                    onlyForOriginalPrice: 1,
                    isSpecifyParticipants: 0, // 是否指定参与人范围
                    clinics: [],
                    memberTypes: [],
                    goodsList: [],
                    coupon: {
                        totalCount: '',
                        leftCount: 0,
                        usedCount: 0,
                        maxUseCountPerOrder: 1,
                        orderThresholdPrice: '',
                        validBegin: null,
                        validEnd: null,
                        obtainBeginDate: null,
                        obtainEndDate: null,
                        validDays: '',
                        obtainCountPerUser: '',
                        isFreeObtain: 1,
                        discountedPrice: '',
                        isLimitObtainCount: 0,
                        validType: 0,
                        remark: '',
                    },
                },
                tableDataList: [], // 参与范围表格数据
                orderThresholdStatus: 0,
                totalCount: 0,
                leftCount: 0,
                pickerOptions: {
                    disabledDate: (date) => {
                        return date < new Date(new Date().getTime() - 60 * 60 * 1000 * 24);
                    },
                },
            };
        },
        computed: {
            ...mapGetters(['isOpenMp']),
            title() {
                return this.id && !this.isCopy ? '编辑优惠券' : '新增优惠券';
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            validDays: {
                get() {
                    return this.postData.coupon.validDays ? 0 : 1;
                },
                set(val) {
                    if (!val) {
                        this.postData.coupon.validDays = 7;
                    } else {
                        this.postData.coupon.validDays = 0;
                    }
                },
            },
            obtainCountPerUser: {
                get() {
                    return this.postData.coupon.obtainCountPerUser ? 1 : 0;
                },
                set(val) {
                    this.postData.coupon.obtainCountPerUser = val;
                },
            },
            useProductTips() {
                return this.postData.goodsList
                    .map((item) => {
                        return item.name || item.goods?.medicineCadn || item.goods?.name;
                    })
                    .join('、');
            },
            limitOrganTips() {
                let str = '* 适用门店：';
                if (this.postData.isAllClinics) {
                    str += '所有门店';
                } else {
                    str += `${this.participateOrganTips(this.postData.clinics)}可用`;
                }
                return str;
            },
            limitTips() {
                let str = '* 领取限制：';

                if (this.participatePatientTips && this.participatePatientTips !== '全部会员') {
                    str += `仅${this.participatePatientTips}可领取，`;
                }

                if (this.postData.coupon.isLimitObtainCount) {
                    str += `每人限领${this.postData.coupon.obtainCountPerUser || ''}张`;
                } else {
                    str += '不限领取数量';
                }
                return str;
            },
            participatePatientTips() {
                const { memberTypes } = this.postData;
                const { allMemberCardTypeTotal } = this;
                if (memberTypes.length === 0 || this.postData.isAllPatients) {
                    return '';
                }
                if (memberTypes.length && memberTypes.length === allMemberCardTypeTotal) {
                    return '全部会员';
                }
                if (memberTypes.length && memberTypes.length !== allMemberCardTypeTotal) {
                    const showApplictors = memberTypes.slice(0, 1);
                    const showApplictorsNames = showApplictors.map((item) => item.name);
                    const names = showApplictorsNames.map((item) =>
                        (item && item.length > 10 ? `${item.substring(0, 10)}...` : item),
                    );
                    if (memberTypes.length > 1) {
                        return `${names.join('、')}等${memberTypes.length}个会员卡`;
                    }
                    return `${names.join('、')}`;

                }
                return '';
            },
            selectGoodsList() {
                return this.postData.goodsList.map((item) => {
                    return {
                        ...item,
                        productId: item.goodsId,
                        goods: item.goods,
                        productSubType: item.goodsType,
                        productType: item.goodsSubType,
                    };
                });
            },

            /**
             * @desc 可发券的状态，除优惠内容（见面金额）外，都可以编辑
             * <AUTHOR>
             * @date 2020/04/17 09:33:21
             */
            isActive() {
                return this.postData.status === PromotionStatus.ACTIVE;
            },

            /**
             * @desc 可发券状态，优惠券领取数>0(只可以编辑 名称，增发数量，领取范围，领取数量，免费领取开关，补充说明)
             * <AUTHOR>
             * @date 2020/04/21 18:48:37
             */
            isActiveReceive() {
                return this.isActive && this.totalCount > this.leftCount;
            },

            /**
             * @desc 下架，停止发券，除优惠内容、领取范围、限领数量、免费领取开关&时间外，都可以编辑
             * <AUTHOR>
             * @date 2020/04/17 09:52:21
             */
            isTakeOff() {
                return this.postData.status === PromotionStatus.TAKE_OFF;
            },

            /**
             * @desc 作废，已经失效 都不可以编辑
             * <AUTHOR>
             * @date 2020/04/17 09:49:43
             */
            isInvalid() {
                return this.postData.status === PromotionStatus.INVALID;
            },

            discountPriceTips() {
                let str = '* ';
                if (this.orderThresholdStatus) {
                    str += `满 ${this.postData.coupon.orderThresholdPrice} 减免 ${this.postData.coupon.discountedPrice} 元`;
                } else {
                    str += `减免 ${this.postData.coupon.discountedPrice} 元`;
                }
                return str;
            },
            validDaysTips() {
                let str = '';
                if (this.postData.coupon.validType) {
                    str += `${this.dateFormat(this.postData.coupon.validBegin)}至${this.dateFormat(
                        this.postData.coupon.validEnd,
                    )}`;
                } else {
                    str += `领券后 ${this.postData.coupon.validDays || ''} 天内`;
                }
                return str;
            },

            activityGoodsList() {
                return this.tableDataList.filter((item) => item.goodsDiscountType === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT);
            },
            activityTypeList() {
                return this.tableDataList.filter((item) => item.goodsDiscountType === GOODS_DISCOUNT_TYPE.CATEGORY);
            },
        },

        async created() {
            if (this.id) {
                this.fetchDetail();
            }

            try {
                const { total = 0 } = await MarketingAPI.getMemberCardTypeDetailList();
                this.allMemberCardTypeTotal = total;
            } catch (error) {
                this.isLoading = false;
            }
        },
        methods: {
            // 获取详情
            async fetchDetail() {
                this.loading = true;
                let data = await WeShopAPI.marketing.getCouponDetail(this.id);
                if (this.isCopy) {
                    data = this.copyHandler(data);
                } else {
                    // 默认数据，不能为0，后台提交后null或者'' 会转成0，需要转化
                    data.coupon.obtainCountPerUser = data.coupon.obtainCountPerUser || '';
                    data.coupon.orderThresholdPrice = data.coupon.orderThresholdPrice || '';
                    this.leftCount = data.coupon.leftCount;
                    this.totalCount = data.coupon.totalCount;
                }


                // 处理指定时间
                if (data.coupon.validBegin && data.coupon.validEnd) {
                    this.validDate = [data.coupon.validBegin, data.coupon.validEnd];
                }

                if (data.coupon.obtainBeginDate && data.coupon.obtainEndDate) {
                    this.obtainDate = [data.coupon.obtainBeginDate, data.coupon.obtainEndDate];
                }

                this.postData = data;

                const {
                    obtainBeginLocalDate,
                    obtainEndLocalDate,
                    validBeginLocalDate,
                    validEndLocalDate,
                } = this.postData.coupon || {};
                this.postData.coupon.obtainBeginDate = obtainBeginLocalDate;
                this.postData.coupon.obtainEndDate = obtainEndLocalDate;
                this.postData.coupon.validBegin = validBeginLocalDate;
                this.postData.coupon.validEnd = validEndLocalDate;

                this.postData.memberTypes = data.memberTypes?.map((item) => {
                    return {
                        name: item.memberTypeName,
                        id: item.memberTypeId,
                    };
                });
                this.postData.goodsList = (this.postData.goodsList || []).map((item) => {
                    return {
                        ...item,
                        ...(item.type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT && { goodsId: item.relatedValue || '' }),
                        ...(item.type === GOODS_DISCOUNT_TYPE.CATEGORY && { typeId: item.relatedValue || '' }),
                        name: item.relatedName || '',
                        goodsDiscountType: item.type,
                    };
                });
                this.tableDataList = Clone(this.postData.goodsList);
                if (this.postData.coupon.orderThresholdPrice > 0) {
                    this.orderThresholdStatus = 1;
                }
                this.loading = false;
            },
            /**
             * @desc 清空对应的id name 时间等信息
             * <AUTHOR>
             * @date 2020-07-30 20:46:31
             * @params
             * @return
             */
            copyHandler(data) {
                data.name = '';
                delete data.status;
                delete data.id;
                if (!data.isForever) {
                    data.beginDate = '';
                    data.endDate = '';
                }
                data.goodsList = data.goodsList.map((item) => {
                    delete item.id;
                    if (item.exceptItems && item.exceptItems.length) {
                        item.exceptItems = item.exceptItems.map((exceptItem) => {
                            delete exceptItem.id;
                            return exceptItem;
                        });
                    }
                    return item;
                });
                console.log(data);
                return data;
            },
            filterEmoji(val) {
                this.postData.name = EmojiFilter(val);
            },
            changeOrderThresholdStatus() {
                if (!this.orderThresholdStatus) {
                    this.postData.coupon.orderThresholdPrice = '';
                }
            },

            validateOrderThreshold(coupon, callback) {
                const { orderThresholdPrice } = this.postData.coupon;
                if (this.orderThresholdStatus === 1 && !orderThresholdPrice) {
                    callback({
                        validate: false,
                        message: '使用门槛不能为空',
                    });
                    return false;
                }
                callback({ validate: true });
            },
            /**
             * @desc 每人领取数量不超过发行总量
             * <AUTHOR>
             * @date 2020/4/20
             * @params
             * @return
             */
            validateLimitCount(coupon, callback) {
                const {
                    isLimitObtainCount, obtainCountPerUser, totalCount,
                } = this.postData.coupon;

                const flag = +obtainCountPerUser <= +totalCount;
                if (isLimitObtainCount === 1 && obtainCountPerUser === '') {
                    callback({
                        validate: false,
                        message: '领取数量不能为空',
                    });
                    return false;
                } if (isLimitObtainCount && !flag) {
                    callback({
                        validate: false,
                        message: '每人领取数量不能超过发行总量',
                    });
                }
                callback({ validate: true });
            },
            validateValidDays(coupon, callback) {
                const {
                    validType, validDays,
                } = this.postData.coupon;
                if (validType === 0) {
                    if (validDays === '') {
                        callback({
                            validate: false,
                            message: '使用期限不能为空' ,
                        });
                        return false;
                    }
                    if (+validDays === 0) {
                        callback({
                            validate: false,
                            message: '使用期限必须大于0',
                        });
                        return false;
                    }
                }
                callback({ validate: true });
            },
            validateDate(endDate, callback) {
                const { validEnd } = this.postData.coupon;
                if (!validEnd || !endDate) {
                    callback({ validate: true });
                    return false;
                }

                const endDateTimes = new Date(endDate.replace(/-/g, '/')).getTime();
                const validEndTimes = new Date(validEnd.replace(/-/g, '/')).getTime();
                if (endDateTimes > validEndTimes) {
                    callback({
                        validate: false,
                        message: '领取日期结束时间不能大于使用期限结束时间',
                    });
                    return false;
                }
                callback({ validate: true });
            },

            disabledValidBegin(time) {
                let flag = false;
                if (this.postData.coupon.validEnd) {
                    flag = time.getTime() >= new Date(this.postData.coupon.validEnd).getTime() - oneDayTimestamp;
                }
                const isEndTime = new Date(2038, 0, 18).getTime();
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            disabledValidEnd(time) {
                let flag = false;
                if (this.postData.coupon.validBegin) {
                    flag = time.getTime() < new Date(this.postData.coupon.validBegin).getTime() - oneDayTimestamp;
                }
                const isEndTime = new Date(2038, 0, 18).getTime();
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            disabledBeginDate(time) {
                let flag = false;
                if (this.postData.coupon.obtainEndDate) {
                    flag = time.getTime() >= new Date(this.postData.coupon.obtainEndDate).getTime() - oneDayTimestamp;
                }
                const isEndTime = new Date(2038, 0, 18).getTime();
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            disabledEndDate(time) {
                let flag = false;
                if (this.postData.coupon.obtainBeginDate) {
                    flag = time.getTime() <= new Date(this.postData.coupon.obtainBeginDate).getTime() - oneDayTimestamp ;
                }
                const isEndTime = new Date(2038, 0, 18).getTime();
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },

            changeLimitObtainCount() {
                if (!this.postData.coupon.isLimitObtainCount) {
                    this.postData.coupon.obtainCountPerUser = null;
                }
            },

            changeValidType() {
                if (this.postData.coupon.validType) {
                    this.postData.coupon.validDays = null;
                }
            },

            participateOrganTips(clinics) {
                if (!clinics?.length) {
                    return [];
                }
                const names = [];
                clinics.forEach((item) => {
                    let name = item.name || item.shortName;
                    name = name && name.length > 8 ? `${name.substring(0, 8)}...` : name;
                    names.push(name);
                });
                const { length } = names;
                if (names.length > 2) {
                    names.length = 2;
                }
                if (names.length > 1) {
                    return `${names.join('、')}等${length}个门店`;
                }
                return `${names.join('、')}`;

            },

            confirm() {
                this.$refs.discountForm.validate(async (valid) => {
                    if (valid) {
                        if (this.postData.isSpecifyParticipants && !this.postData.memberTypes?.length) {
                            this.memberError = true;
                            return false;
                        }
                        if (!this.tableDataList?.length) {
                            this.goodsError = true;
                            // const dom = document.querySelector('.abc-dialog-body');
                            // this.$nextTick(() => {
                            //     dom.scrollTop = document.querySelector('.error').scrollTop;
                            // });
                            return false;
                        }
                        this.submit();
                    }
                });
            },
            async submit() {
                try {
                    this.btnLoading = true;
                    const params = Object.assign({},
                                                 {
                                                     memberTypeIds: this.postData.memberTypes.map((item) => item.id),
                                                 },
                                                 this.postData,
                    );

                    params.goodsList = this.tableDataList?.map((item) => {
                        const exceptGoodsIds = item.exceptItems?.map((it) => it.goodsId || item.id);
                        const { type } = item;
                        let relatedValue = '';
                        if (item.id) {
                            relatedValue = item.relatedValue;
                        } else if (type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT) {
                            relatedValue = item.goodsId || '';
                        } else {
                            relatedValue = item.typeId || '';
                        }

                        return {
                            id: item.id || '',
                            type,
                            relatedName: item.id ? item.relatedName : item.name || '',
                            relatedValue,
                            exceptGoodsIds,
                        };
                    });

                    if (!params.coupon.orderThresholdPrice) {
                        params.coupon.orderThresholdPrice = 0;
                    }
                    if (this.id && !this.isCopy) {
                        await WeShopAPI.marketing.editCoupon(params);
                    } else {
                        await WeShopAPI.marketing.addCoupon(params);
                    }
                    this.btnLoading = false;
                    this.showDialog = false;
                    this.$emit('refresh', this.id ? 'edit' : 'add');
                } catch (e) {
                    this.btnLoading = false;
                    console.error(e);
                    this.$Toast({
                        type: 'error',
                        message: e.message || '操作失败',
                    });
                }
            },

            stopConfirm() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content:
                        '停止发券后，顾客将无法再领取优惠券。已领取的优惠券，在有效期内还能继续使用，但无法再编辑优惠券内容。确定停止发券？',
                    onConfirm: () => {
                        this.stopSubmit();
                    },
                });
            },
            async stopSubmit() {
                if (!this.id) return false;
                try {
                    await WeShopAPI.marketing.stopCoupon(this.id);
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.showDialog = false;
                    this.$emit('refresh', this.id ? 'edit' : 'add');
                } catch (e) {
                    console.error(e);
                }
            },

            cancelConfirm() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '优惠券作废后，顾客将无法再领取优惠券，已领取的优惠券也将作废，无法使用。是否确定作废？',
                    onConfirm: () => {
                        this.cancelSubmit();
                    },
                });
            },
            async cancelSubmit() {
                if (!this.id) return false;
                try {
                    await WeShopAPI.marketing.invalidCoupon(this.id);
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.showDialog = false;
                    this.$emit('refresh', this.id ? 'edit' : 'add');
                } catch (e) {
                    console.error(e);
                }
            },

            deleteConfirm() {
                if (!this.id) return false;
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除？',
                    onConfirm: async () => {
                        this.deleteSubmit();
                    },
                });
            },
            async deleteSubmit() {
                try {
                    await WeShopAPI.marketing.deleteCoupon(this.id);
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.showDialog = false;
                    this.$emit('refresh', 'add');
                } catch (e) {
                    console.error(e);
                }
            },

            checked(item) {
                return item.selects.map((item) => item.name);
            },

            // 时间显示格式化
            dateFormat(dateString) {
                if (!dateString) {
                    return '';
                }
                return parseTime(new Date(dateString), 'y-m-d', true);
            },
            // 计算几级优惠，最多三级根据数组的长度计算并翻译成大写
            computeLevel(index) {
                const levelItem = level.find((item) => item.level === index);
                return levelItem ? levelItem.alias : '一';
            },
            // 删除优惠券
            handleDeleteCoupon(index) {
                this.postData.giftRules = this.postData.giftRules.filter((item, i) => i !== index);
                this.$emit('change', this.postData);
            },

            async handleFinish() {
                await this.$emit('finished', this.postData);
            },

            async handleDeleteActive() {
                await this.$emit('delete', this.postData);
            },
            handleAddIssuance() {
                this.showAddIssuance = true;
            },
            handleHide() {
                this.showAddIssuance = false;
            },
            handleUpdateCoupon(val) {
                this.showAddIssuance = false;
                this.postData.coupon.totalCount = Number(this.postData.coupon.totalCount) + Number(val);
                this.$emit('change', this.postData);
            },

            /**
             * 拉取连锁下门店列表
             * <AUTHOR>
             * @date 2020-09-25
             */
            async fetchChainClinics() {
                try {
                    const { data } = await ClinicAPI.chainClinicV3();
                    this.chainSubClinicList = (data.rows || []).filter((item) => isChainSubClinic(item));
                } catch (error) {
                    console.log('fetchChainClinics error', error);
                }
            },
            handleChangeGoodsList(selectedList) {
                const goodsList = selectedList.map((item) => {
                    return {
                        ...item,
                        type: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                        relatedValue: item.goodsId,
                    };
                });
                this.tableDataList = [...this.activityTypeList, ...goodsList];
            },
            handleChangeTypeList(selectedList) {
                const typeList = Clone(this.activityTypeList);
                selectedList.forEach((item) => {
                    if (!typeList.find((it) => it.typeId === item.typeId)) {
                        this.$set(item, 'exceptItems', []);
                        this.$set(item, 'type', GOODS_DISCOUNT_TYPE.CATEGORY);
                        this.$set(item, 'relatedValue', item.typeId);
                        delete item.id;
                        typeList.push(item);
                    }
                });

                typeList.forEach((item, index) => {
                    if (!selectedList.some((it) => it.typeId === item.typeId)) {
                        typeList.splice(index, 1);
                    }
                });
                this.tableDataList = [...typeList, ...this.activityGoodsList];
            },
            handleValidDateChange(Date) {
                this.postData.coupon.validBegin = Date[0] || '';
                this.postData.coupon.validEnd = Date[1] || '';
            },
            handleObtainDateChange(Date) {
                this.postData.coupon.obtainBeginDate = Date[0] || '';
                this.postData.coupon.obtainEndDate = Date[1] || '';
            },
        },
    };
</script>
