<template>
    <div class="we-shop-discount-form">
        <abc-form ref="discountForm">
            <abc-dialog
                v-if="visible"
                ref="discountFormDialog"
                :title="title"
                :value="visible"
                content-styles="height: 600px;"
                size="xlarge"
                @input="(val) => $emit('visible', val)"
            >
                <abc-flex :gap="16" vertical>
                    <abc-flex v-if="showConflictTips || (showConflictTips && isMemberActivity)" :gap="4" vertical>
                        <abc-tips-card-v2 v-if="showConflictTips" theme="warning">
                            部分产品折扣设置与其它活动冲突，导致失效
                            <template #operate>
                                <abc-button class="abc-button-small" type="text" @click="showDiscountConflictView">
                                    详情
                                </abc-button>
                            </template>
                        </abc-tips-card-v2>
                        <abc-tips-card-v2 v-if="showConflictTips && isMemberActivity" theme="warning">
                            该折扣为会员折扣，请前往会员管理处修改折扣内容
                        </abc-tips-card-v2>
                    </abc-flex>
                    <div v-abc-loading="isLoading">
                        <biz-setting-form :no-limit-width="true">
                            <biz-setting-form-group>
                                <biz-setting-form-item label-line-height-size="medium" label="活动名称">
                                    <div>
                                        <abc-input
                                            v-model="postData.activityData.name"
                                            :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                            :max-length="20"
                                            :width="328"
                                            show-max-length-tips
                                            type="text"
                                        ></abc-input>
                                    </div>
                                </biz-setting-form-item>
                                <biz-setting-form-item label="活动时间" use-inner-layout>
                                    <abc-radio-group
                                        v-model="postData.activityData.isForever"
                                        :item-block="true"
                                    >
                                        <abc-radio
                                            :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                            :label="1"
                                            class="span-bold"
                                        >
                                            永久有效
                                        </abc-radio>
                                        <abc-radio
                                            :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                            :label="0"
                                            class="span-bold"
                                            style="height: 32px;"
                                        >
                                            <abc-flex :gap="8" align="center">
                                                指定时间
                                                <div
                                                    v-if="postData.activityData.isForever === 0"
                                                    :disabled="postData.activityData.isForever === 1 || isOnlyShow"
                                                >
                                                    <abc-date-picker
                                                        v-model="postData.activityData.dateRange"
                                                        :disabled="postData.activityData.isForever === 1 || isOnlyShow"
                                                        :picker-options="pickerOptions"
                                                        :placeholder="'全部时间'"
                                                        clearable
                                                        width="240"
                                                        type="daterange"
                                                        value-format="YYYY-MM-DD"
                                                        @change="changeDate"
                                                    >
                                                    </abc-date-picker>
                                                </div>
                                            </abc-flex>
                                        </abc-radio>
                                    </abc-radio-group>
                                </biz-setting-form-item>
                                <marketing-select-card-item
                                    label="活动对象"
                                    :tag-data.sync="postData.activityData.memberTypes"
                                    :get-icon-function="() => 's-role-color'"
                                    :is-show-card="postData.patientType === 1"
                                    :tag-width="143"
                                    :item-display-name="(item) => item.memberType ? item.memberType?.memberTypeName : item.name"
                                    :disabled="postData.patientType === 0 || (isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                    :edit-btn-text="'选择会员'"
                                    @openDialog="showSelectMemberDialog = true"
                                >
                                    <template
                                        v-if="postData.patientType !== 0 &&
                                            isShowErrorNoChoiceMember &&
                                            postData.activityData.memberTypes.length === 0"
                                        #tips
                                    >
                                        <abc-text theme="danger">
                                            未选择指定会员
                                        </abc-text>
                                    </template>
                                    <template #radio-group>
                                        <abc-radio-group
                                            v-model="postData.patientType"
                                            :item-block="true"
                                            :disabled="(isMemberTypeGrant && !isFromCopy)"
                                        >
                                            <abc-radio
                                                :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                                :label="0"
                                            >
                                                所有{{ viewDistributeConfig?.roleLabel || '患者' }}
                                            </abc-radio>
                                            <abc-radio
                                                :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                                :label="1"
                                            >
                                                指定会员
                                            </abc-radio>
                                        </abc-radio-group>
                                    </template>
                                </marketing-select-card-item>
                                <biz-setting-form-item
                                    vertical
                                    label="活动范围"
                                    :use-inner-layout="false"
                                >
                                    <select-we-shop-goods-type
                                        :type-list="activityTypeList"
                                        :goods-list="activityGoodsList"
                                        :show-goods-tips="false"
                                        :show-type-tips="false"
                                        @change-goods-list="handleChangeGoodsList"
                                        @change-type-list="handleChangeTypeList"
                                    >
                                        <template #table>
                                            <select-we-shop-promotion-goods-list
                                                :disabled="isMemberActivity"
                                                :table-data-list.sync="postData.activityData.goodsList"
                                                :is-member-type-in-discount="isMemberActivity"
                                                :show-discount-radio="true"
                                                :show-label-input="true"
                                                :tips="'选择参与活动的项目范围'"
                                                show-except-items
                                            ></select-we-shop-promotion-goods-list>
                                        </template>
                                    </select-we-shop-goods-type>
                                </biz-setting-form-item>
                            </biz-setting-form-group>
                        </biz-setting-form>
                    </div>
                </abc-flex>
                <abc-flex slot="footer" justify="space-between">
                    <div>
                        <abc-button
                            v-if="activityId && !isFromCopy && postData.activityData.type !== 0 && !isLoading"
                            theme="danger"
                            variant="ghost"
                            @click="stopCheck"
                        >
                            {{ !activityIsFinish ? '终止' : '删除' }}
                        </abc-button>
                    </div>
                    <abc-space>
                        <abc-button v-if="!isOnlyShow" :disabled="isMemberActivity" @click="save">
                            保存
                        </abc-button>
                        <abc-button variant="ghost" @click="cancel">
                            取消
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </abc-dialog>
            <member-type-transfer
                v-if="showSelectMemberDialog"
                v-model="showSelectMemberDialog"
                :selecteds="postData.activityData.memberTypes"
                @confirm="changeMember"
            >
            </member-type-transfer>
            <discount-conflict-view
                v-if="isShowDiscountConflictView"
                :all-member-card-type-total="allMemberCardTypeTotal"
                :conflict-list="conflictData"
                :visible="isShowDiscountConflictView"
                @visible="(val) => (isShowDiscountConflictView = val)"
            ></discount-conflict-view>
        </abc-form>
    </div>
</template>

<script>
    import WeShopAPI from 'api/we-shop';
    import ClinicAPI from 'api/clinic';
    import MarketingAPI from 'api/marketing';
    import { parseTime } from 'utils/index';
    import {
        emojiExits, filterStrSpace,
    } from 'utils/validate';
    import { mapGetters } from 'vuex';
    import { isChainSubClinic } from 'views/common/clinic.js';
    import discountApi from 'src/api/discount-api.js';
    import { GoodsTypeEnum } from '@abc/constants';
    import {
        enlargeDiscount, reduceDiscount,
    } from 'views/marketing/util.js';
    import AbcAccess from '@/access/utils.js';
    import DiscountConflictView from 'views/marketing/discount/discount-conflict-view.vue';
    import { GOODS_DISCOUNT_TYPE } from 'views/we-clinic/frames/shop/marketing-activities/constants';
    import Clone from 'utils/clone';
    import BizSettingFormItem from '@/components-composite/setting-form/src/views/item.vue';
    import BizSettingForm from '@/components-composite/setting-form/src/views/index.vue';
    import BizSettingFormGroup from '@/components-composite/setting-form/src/views/group.vue';
    import MarketingSelectCardItem from 'views/marketing/components/marketing-select-card-item.vue';
    import SelectWeShopGoodsType
        from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-type.vue';
    import SelectWeShopPromotionGoodsList from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-promotion-goods-list.vue';

    const MemberTypeTransfer = () => import('components/member-type-transfer/index.vue');

    export default {
        components: {
            SelectWeShopGoodsType,
            MarketingSelectCardItem,
            BizSettingFormGroup,
            BizSettingForm,
            BizSettingFormItem,
            DiscountConflictView,
            MemberTypeTransfer,
            SelectWeShopPromotionGoodsList,
        },
        props: {
            activityId: String,
            visible: Boolean,
            isFromCopy: Boolean,
            isOnlyShow: Boolean,
            noDiscountGoodsList: Array,
        },
        data() {
            return {
                showWeShopGoodsListDialog: false,
                showWeShopTypeDialog: false,
                accessKey: AbcAccess.accessMap.MARKET_DISCOUNT,
                showSelectClinicsTransfer: false,
                postData: {
                    timeType: 0,
                    patientType: 0,
                    organType: 0,
                    onlyOriginalPrice: 1,
                    activityData: {
                        name: '',
                        dateRange: [],
                        beginDate: null,
                        endDate: null,
                        isForever: 1,
                        memberTypes: [],
                        goodsList: [],
                        goodsCount: 0,
                        type: 1,
                        status: 1,
                    },
                },
                pickerOptions: {
                    disabledDate: (date) => {
                        const isEndTime = new Date(2038, 0, 18).getTime();
                        return date < new Date(new Date().getTime() - 60 * 60 * 1000 * 24) || date.getTime() > isEndTime;
                    },
                    yearRange: {
                        end: 2038,
                    },
                },
                pickerOptions2: {
                    disabledDate: this.secoundDate,
                    yearRange: {
                        end: 2038,
                    },
                },
                allMemberCardTypeTotal: 0,
                discountTypeList: [],
                isShowAddSingleGoodView: false,
                showSelectMemberDialog: false,
                isShowDiscountConflictView: false,
                isShowSelectGoodsView: false,
                isShowErrorNoChoiceMember: false,
                isShowErrorNoChoiceOrgan: false,
                conflictData: [],
                isInit: false,
                isLoading: false,
                selectGoodsType: [GoodsTypeEnum.MEDICINE,
                                  GoodsTypeEnum.MATERIAL,
                                  GoodsTypeEnum.EXAMINATION,
                                  GoodsTypeEnum.OTHER,
                                  GoodsTypeEnum.GOODS,
                                  GoodsTypeEnum.EYEGLASSES,
                                  GoodsTypeEnum.COMPOSE],

                chainSubClinicList: [], // 连锁下子店列表
            };
        },
        computed: {
            ...mapGetters(['isChain']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            activityGoodsList() {
                return this.postData.activityData.goodsList.filter((item) => item.goodsDiscountType === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT);
            },
            activityTypeList() {
                return this.postData.activityData.goodsList.filter((item) => item.goodsDiscountType === GOODS_DISCOUNT_TYPE.CATEGORY);
            },
            title () {
                if (this.isFromCopy || !this.activityId) {
                    return '新建折扣活动';
                }
                return '编辑折扣活动';

            },
            isMemberTypeGrant() {
                return this.postData.activityData.type === 0;
            },
            activityIsFinish() {
                return this.postData.activityData.status === 9;
            },
            showConflictTips() {
                return this.conflictData && this.conflictData.length > 0;
            },
            isMemberActivity() {
                return this.postData.activityData.type === 0;
            },
            // 可选的门店 - 没有到期
            availableSubClinics() {
                return this.chainSubClinicList.filter((item) => AbcAccess.checkAvailableByEdition(this.accessKey, item.edition));
            },
            // 是否禁用选择全部门店
            disabledAllSubClinic() {
                return (
                    this.chainSubClinicList.length === 0 ||
                    this.chainSubClinicList.length !== this.availableSubClinics.length
                );
            },
        },
        watch: {
            disabledAllSubClinic: {
                handler(newValue) {
                    if (newValue && this.isChain) {
                        // 当禁用全部门店时，默认选中指定门店
                        this.postData.organType = 1;
                    }
                },
                immediate: true,
            },
        },
        async created() {
            try {
                this.isLoading = true;

                await Promise.all([
                    this.fetchChainClinics(),
                    this.fetchMemberCardTypeCount(),
                    this.fetchActivityData(),
                    this.checkConflictData(),
                ]);
            } catch (e) {
                //
            }
            this.isLoading = false;
            this.isInit = true;
        },
        methods: {
            changeMember(list) {
                this.postData.activityData.memberTypes = list;
            },
            update(index, value) {
                this.discountTypeList[index].discount = value;
            },
            secoundDate(date) {
                const isEndTime = new Date(2038, 0, 18).getTime();
                return (
                    date <
                    (this.postData.activityData.beginDate ?
                        new Date(this.postData.activityData.beginDate).getTime() :
                        new Date(new Date().getTime() - 60 * 60 * 1000 * 24)) || date.getTime() > isEndTime
                );
            },
            dateFormat(dateString) {
                if (!dateString) {
                    return '';
                }
                return parseTime(new Date(dateString), 'y-m-d', true);
            },
            async save() {
                this.loading = true;
                const data = {
                    name: this.postData.activityData.name,
                    isForever: this.postData.activityData.isForever,
                    isSpecifyParticipants: this.postData.patientType,
                    memberTypeIds: this.postData.activityData.memberTypes.map((item) => {
                        return item.id;
                    }),
                    goodsList: this.postData.activityData.goodsList.map((item) => {
                        const formatDiscount = reduceDiscount(item.discount, 10, 3);
                        const exceptGoodsIds = item.exceptItems?.map((it) => it.goodsId || item.id);
                        const { type } = item;
                        let relatedValue = '';
                        if (item.id) {
                            relatedValue = item.relatedValue;
                        } else if (type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT) {
                            relatedValue = item.goodsId || '';
                        } else {
                            relatedValue = item.typeId || '';
                        }

                        return {
                            id: item.id || '',
                            type,
                            relatedValue,
                            discount: item.discountType ? item.discount : formatDiscount,
                            discountType: item.discountType || 0,
                            exceptGoodsIds,
                        };
                    }) || [],
                    type: 1,
                };

                if (!filterStrSpace(this.postData.activityData.name)) {
                    this.$Toast({
                        message: '未填写活动名称',
                        type: 'error',
                    });
                    return;
                }
                if (emojiExits(this.postData.activityData.name)) {
                    this.$Toast({
                        message: '活动名称中存在非法字符',
                        type: 'error',
                    });
                    return;
                }

                if (!this.postData.activityData.goodsList.length) {
                    this.$Toast({
                        message: '折扣商品不能为空',
                        type: 'error',
                    });
                    return;
                }

                if (this.postData.activityData.isForever === 0) {
                    if (!this.postData.activityData.beginDate || !this.postData.activityData.endDate) {
                        this.$Toast({
                            message: '未设置活动时间',
                            type: 'error',
                        });
                        return;
                    }
                    if (
                        new Date(this.postData.activityData.beginDate).getTime() >
                        new Date(this.postData.activityData.endDate).getTime()
                    ) {
                        this.$Toast({
                            message: '设置活动开始时间大于结束时间',
                            type: 'error',
                        });
                        return;
                    }
                    data.beginDate = parseTime(new Date(this.postData.activityData.beginDate), 'y-m-d', true);
                    data.endDate = parseTime(new Date(this.postData.activityData.endDate), 'y-m-d', true);
                }
                if (this.postData.patientType === 1 && this.postData.activityData.memberTypes.length === 0) {
                    this.isShowErrorNoChoiceMember = true;
                    return;
                } if (this.postData.patientType !== 1) {
                    data.memberTypeIds = [];
                }
                if (this.postData.organType !== 1) {
                    data.clinicIds = [];
                }

                this.discountTypeList.forEach((item) => {
                    const obj = JSON.parse(JSON.stringify(item));
                    obj.discount = parseFloat((parseFloat(obj.discount) / 10).toFixed(2));
                });
                try {
                    if (this.activityId && !this.isFromCopy) {
                        await WeShopAPI.marketing.updateDiscountPromotionUsingPUT(data, this.activityId);
                        this.$Toast({
                            message: '更新活动成功',
                            type: 'success',
                        });
                    } else {
                        await WeShopAPI.marketing.createDiscountPromotionUsingPOST(data);
                        this.$Toast({
                            message: '创建活动成功',
                            type: 'success',
                        });
                    }
                    this.$emit('refresh');
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                    return;
                }
                this.$emit('visible', false);
            },
            cancel() {
                this.$emit('visible', false);
            },
            stopCheck() {
                if (this.activityIsFinish) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '删除活动后无法恢复，是否确认？',
                        onConfirm: () => {
                            this.stop();
                        },
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '终止活动后无法再次开启，是否确认？',
                        onConfirm: () => {
                            this.stop();
                        },
                    });
                }
            },
            async stop() {
                try {
                    this.loading = true;
                    if (this.activityIsFinish) {
                        await WeShopAPI.marketing.deleteDiscountPromotionUsingDELETE(this.activityId);
                        this.$Toast({
                            message: '删除活动成功',
                            type: 'success',
                        });
                    } else {
                        await WeShopAPI.marketing.endDiscountPromotionUsingPUT(this.activityId);
                        this.$Toast({
                            message: '终止活动成功',
                            type: 'success',
                        });
                    }
                    this.$emit('refresh');
                } catch (e) {
                    this.loading = false;
                    return;
                }
                this.loading = false;
                this.$emit('visible', false);
            },
            showDiscountConflictView() {
                this.isShowDiscountConflictView = true;
            },
            /**
             * 拉取连锁下门店列表
             * <AUTHOR>
             * @date 2020-09-25
             */
            async fetchChainClinics() {
                try {
                    const { data } = await ClinicAPI.chainClinicV3();
                    this.chainSubClinicList = (data.rows || []).filter((item) => isChainSubClinic(item));
                } catch (error) {
                    console.log('fetchChainClinics error', error);
                }
            },

            async fetchMemberCardTypeCount() {
                try {
                    const { total = 0 } = await MarketingAPI.getMemberCardTypeDetailList();
                    this.allMemberCardTypeTotal = total;
                } catch (error) {
                    console.log('fetchMemberCardType error', error);
                }
            },
            async fetchActivityData() {
                if (this.activityId) {
                    this.postData.activityData = await WeShopAPI.marketing.getDiscountPromotionUsingGET(this.activityId);
                    if (this.isFromCopy) {
                        this.postData.activityData.name = '';
                    }
                    if (this.postData.activityData.isForever === 0) {
                        this.$set(this.postData.activityData, 'dateRange', [
                            new Date(this.postData.activityData.beginDate),
                            new Date(this.postData.activityData.endDate),
                        ]);
                    } else {
                        this.$set(this.postData.activityData, 'dateRange', []);
                    }
                    this.postData.activityData.beginDate = this.postData.activityData.beginLocalDate;
                    this.postData.activityData.endDate = this.postData.activityData.endLocalDate;
                    this.postData.activityData.goodsList = this.postData.activityData?.goodsList?.map((item) => {
                        const formatDiscount = enlargeDiscount(item.discount, 10, 2);
                        return {
                            ...item,
                            name: item.relatedName || '',
                            ...(item.type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT && { goodsId: item.relatedValue || '' }),
                            ...(item.type === GOODS_DISCOUNT_TYPE.CATEGORY && { typeId: item.relatedValue || '' }),
                            goodsDiscountType: item.type,
                            discount: item.discountType ? item.discount : formatDiscount,
                            discountType: item.discountType || 0,
                        };
                    }) || [];
                    this.postData.patientType = this.postData.activityData.memberTypes.length === 0 ? 0 : 1;
                    this.postData.activityData.memberTypes = this.postData.activityData.memberTypes.map((item) => {
                        return {
                            ...item,
                            id: item.memberTypeId,
                            name: item.memberTypeName,
                        };
                    });
                }
            },
            async checkConflictData() {
                if (this.activityId && !this.isFromCopy && !this.isOnlyShow) {
                    this.conflictData = await discountApi.queryPromotionDiscountConflictsUsingGET(this.activityId);
                }
            },
            handleChangeGoodsList(selectedList) {
                const goodsList = selectedList.map((item) => {
                    return {
                        ...item,
                        type: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                        relatedValue: item.goodsId,
                        discountType: item.discountType || 0,
                    };
                });
                this.postData.activityData.goodsList = [...this.activityTypeList, ...goodsList];
                this.handleUpdateGoodsTypeList();
            },
            handleChangeTypeList(selectedList) {
                const typeList = Clone(this.activityTypeList);
                selectedList.forEach((item) => {
                    if (!typeList.find((it) => it.typeId === item.typeId)) {
                        this.$set(item, 'exceptItems', []);
                        this.$set(item, 'type', GOODS_DISCOUNT_TYPE.CATEGORY);
                        this.$set(item, 'relatedValue', item.typeId);
                        this.$set(item, 'discountType', 0);
                        delete item.id;
                        typeList.push(item);
                    }
                });

                typeList.forEach((item, index) => {
                    if (!selectedList.some((it) => it.typeId === item.typeId)) {
                        typeList.splice(index, 1);
                    }
                });
                this.postData.activityData.goodsList = [...typeList, ...this.activityGoodsList];
                this.handleUpdateGoodsTypeList();
            },
            changeDate(date) {
                this.postData.activityData.beginDate = date[0];
                this.postData.activityData.endDate = date[1];
            },
            handleUpdateGoodsTypeList() {
                this.$nextTick(() => {
                    const el = this.$refs.discountFormDialog.$el;
                    el.querySelector('.abc-dialog-body').scrollTop = el.scrollHeight;
                });
            },
        },
    };
</script>
