<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle"
        content-styles="padding: 0;"
        append-to-body
    >
        <abc-transfer-v2
            :data="doctors"
            node-key="id"
            transfer-tree-bg="#F9FAFC"
            show-check-all
            leaf-icon="s-user-color"
            :default-checked-keys="defaultCheckedKeys"
            @confirm="handleConfirm"
            @cancel="handleCancel"
        >
        </abc-transfer-v2>
    </abc-dialog>
</template>

<script>
    import { McConfigAPI } from 'views/settings/micro-clinic/core/mc-config-api';

    let cacheDoctors = null;

    export default {
        name: 'DoctorRecommendDialog',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            defaultDoctorList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                doctors: [],

                defaultCheckedKeys: this.defaultDoctorList.map((doctor) => doctor.id),
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            dialogTitle() {
                return '医生推荐';
            },
        },
        mounted() {
            this.fetchDoctors();
        },
        methods: {
            async fetchDoctors() {
                try {
                    if (cacheDoctors) {
                        this.doctors = cacheDoctors;
                        return;
                    }
                    let { doctors = [] } = await McConfigAPI.getMcConfigDoctorListInfoUsingGET();
                    doctors = doctors.map((doctor) => {
                        const newData = {
                            ...doctor,
                            label: doctor.doctorName,
                            id: doctor.doctorId,
                            name: doctor.doctorName,
                        };
                        delete newData.doctorName;
                        delete newData.doctorId;
                        return newData;
                    });
                    this.doctors = doctors;
                    cacheDoctors = doctors;
                } catch (error) {
                    console.error(error);
                }
            },

            handleConfirm(result) {
                this.$emit('confirm', result);
                this.showDialog = false;
            },

            handleCancel() {
                this.showDialog = false;
            },
        },
    };
</script>
