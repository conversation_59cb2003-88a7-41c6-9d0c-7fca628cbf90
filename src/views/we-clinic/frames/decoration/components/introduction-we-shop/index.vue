<template>
    <div v-if="supportWeShopPurchase" class="introduction-we-shop">
        <abc-flex align="center" gap="large">
            <img class="introduction-we-shop_icon" src="~assets/images/weclinic/icon-we-shop.png" alt="商城图标" />
            <abc-flex
                flex="1"
                vertical
                justify="space-between"
                :gap="4"
            >
                <abc-flex justify="space-between" align="center">
                    <span class="introduction-we-shop_title">微商城</span>
                    <span class="introduction-we-shop_price">
                        <abc-money :value="weShopOpenFee"></abc-money>/年
                    </span>
                </abc-flex>
                <abc-flex justify="space-between" align="center">
                    <span class="introduction-we-shop_des">依托微{{ $app.institutionTypeWording }}，牢牢锁定私域流量，完成预约-就医-购药的全流程闭环，助力营收双倍增长！</span>
                    <abc-button type="paymentp" size="small" @click="handleClickShowIntroDialog">
                        查看介绍
                    </abc-button>
                </abc-flex>
            </abc-flex>
        </abc-flex>
    </div>
</template>
<script lang="ts">
    import { mapGetters } from 'vuex';

    export default {
        name: 'IntroductionWeShop',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            weShopOpenFee() {
                return this.$abcPage.$store.state.weShopOpenFee;
            },
            supportWeShopPurchase() {
                return this.viewDistributeConfig.WeClinic.supportWeShopPurchase;
            },
        },
        methods: {
            handleClickShowIntroDialog() {
                this.$emit('showIntroDialog');
            },
        },
    };
</script>

<style lang="scss">
.introduction-we-shop {
    padding: 16px 24px;
    background: $Y4;
    border-radius: var(--abc-border-radius-small);

    .introduction-we-shop_icon {
        width: 56px;
        height: 56px;
    }

    .introduction-we-shop_title {
        font-size: 16px;
    }

    .introduction-we-shop_price {
        font-size: 18px;
    }

    .introduction-we-shop_des {
        font-size: 12px;
    }
}
</style>
