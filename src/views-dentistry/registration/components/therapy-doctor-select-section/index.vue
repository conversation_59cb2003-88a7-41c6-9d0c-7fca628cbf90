<template>
    <div class="therapy-doctor-select-section-wrapper">
        <abc-select-input
            :value="!postData.doctorName ? '' : postData.doctorName"
            clearable
            :visible-popper.sync="visiblePopper"
            :width="inputWidth"
            :disabled="disabled"
            :size="size"
            placeholder="理疗师"
            :data-cy="`${PrimaryDataCyKey}-doctor-select`"
            :popper-width="702"
            :popper-max-height="360"
            class="therapy-doctor-department-components-wrapper"
            :popper-class="popperClass"
            :close-on-click-outside="handleCloseOnClickOutside"
            @update:visiblePopper="handlePanelVisibleChange"
            @clear="handleClear"
            @enter="enterEvent"
        >
            <template #reference>
                <abc-flex
                    v-if="postData.doctorName"
                    align="center"
                    style="height: 100%; padding: 0 16px;"
                >
                    <abc-text theme="black" bold class="ellipsis">
                        {{ postData.doctorName }}
                    </abc-text>
                </abc-flex>
            </template>
            <abc-flex vertical class="panel-content-wrapper">
                <abc-flex class="departments-week-schedule-wrapper" data-cy="abc-departments-week-schedule-wrapper">
                    <biz-week-schedule
                        :class="{
                            'week-schedule-wrapper': true,
                            'content-empty': !weekScheduleData.length,
                        }"
                        data-cy="abc-week-schedule-wrapper"
                        :prepend-width="320"
                        :data="weekScheduleData"
                        schedule-key="shifts"
                        empty-text="没有可预约理疗师"
                        :loading="weekScheduleLoading"
                        :content-max-height="360"
                        :start-date="startDate"
                        variant="fill"
                        :pagination="pagination"
                        :enable-persistent-scrollbar="true"
                        @prev="handlePrev"
                        @next="handleNext"
                    >
                        <template #headerPrepend>
                            <abc-search
                                v-model="doctorSearchKey"
                                class="week-schedule-search"
                                :width="186"
                                placeholder="搜索理疗师"
                                @search="_handleDoctorSearchInput"
                                @clear="handleDoctorSearchClear"
                            ></abc-search>
                            <abc-flex align="center" justify="flex-end" style="width: 100%; height: 100%; padding-right: 20px;">
                                <abc-text size="mini" theme="gray">
                                    {{ currentWeekInfo }}
                                </abc-text>
                            </abc-flex>
                        </template>
                        <template #bodyPrepend="{ data }">
                            <biz-week-schedule-cell>
                                <abc-flex class="doctor-item">
                                    <abc-flex align="center" gap="8">
                                        <abc-text v-abc-title.ellipsis="data.doctorName || ''" tag="div" class="doctor-name">
                                        </abc-text>
                                    </abc-flex>
                                    <abc-flex align="center" class="doctor-info">
                                        <abc-text
                                            v-abc-title.ellipsis="getPracticeInfo(data)"
                                            theme="gray-light"
                                            size="small"
                                            class="doctor-practice-info"
                                        ></abc-text>
                                    </abc-flex>
                                </abc-flex>
                            </biz-week-schedule-cell>
                        </template>
                        <template
                            #default="{
                                data, config, row
                            }"
                        >
                            <biz-week-schedule-cell
                                class="schedule-shift-cell"
                                :date="config.date"
                                :disabled="judgeCurrentShiftIsDisabled(data)"
                            >
                                <div class="schedule-content-wrapper" @click.stop="handleClickShift(row, data)">
                                    <abc-flex justify="center" align="center" class="shift-cell-wrapper">
                                        <abc-text size="mini" :theme="shiftStatusTheme(data)">
                                            {{ shiftStatusWording(data) }}
                                        </abc-text>
                                    </abc-flex>
                                </div>
                            </biz-week-schedule-cell>
                        </template>
                    </biz-week-schedule>
                </abc-flex>
                <div v-if="visiblePanel" class="mask-wrapper">
                    <abc-flex justify="center" align="center" class="mask-content">
                        <order-no-select-wrapper
                            v-if="isFixOrderMode"
                            :post-data="postData"
                            :doctor-no-info-list="doctorNoInfoList"
                            :visible-panel="visiblePanel"
                            :selected-order-info="selectedOrderInfo"
                            :registration-type="registrationType"
                            :all-doctors="allDoctors"
                            source="week-schedule"
                            :selected-date="currentSelectedShift?.workingDate"
                            :current-selected-shift="currentSelectedShift"
                            @confirm="handleSelectNoConfirm"
                            @cancel="visiblePanel = false"
                        ></order-no-select-wrapper>

                        <therapy-time-range-select-popper
                            v-else
                            :visible-panel="visiblePanel"
                            :time-range-list="timeRangeList"
                            :service-duration-time="serviceDurationTime"
                            :service-min-minutes="serviceMinMinutes"
                            :service-max-minutes="serviceMaxMinutes"
                            show-footer-btn
                            @confirm="handleTherapyTimeRangeConfirm"
                            @cancel="visiblePanel = false"
                        >
                        </therapy-time-range-select-popper>
                    </abc-flex>
                </div>
            </abc-flex>
        </abc-select-input>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        BizWeekSchedule, BizWeekScheduleCell,
    } from '@/components-composite/biz-week-schedule';
    import Clone from 'utils/clone';
    import { parseTime } from '@/utils';
    import RegistrationsAPI from '@/api/registrations/index.js';
    import {
        debounce, isEqual, 
    } from 'utils/lodash';
    import { RevisitStatus } from '@/assets/configure/constants';
    import {
        RESERVATION_MODE_TYPE,
        SERVICE_TYPE_ENUM,
    } from 'views/settings/registered-reservation/constant';
    import { BusinessType } from 'views/registration/common/constants';
    import TherapyTimeRangeSelectPopper from '@/views-dentistry/registration/time-range-select/therapy-time-range-select-popper.vue';
    import TherapyTimeRangeListMixin from '@/views-dentistry/registration/time-range-select/therapy-time-range-list-mixin';
    import OrderNoSelectWrapper from '@/views-dentistry/registration/components/order-no-select-section/order-no-select-wrapper.vue';
    import AbcSearch from 'components/abc-search/index.vue';
    import {
        getWeekEndDate, getWeekStartDate, nextDate, prevDate, toDate, 
    } from '@abc/utils-date';

    export default {
        name: 'DoctorSelectSection',
        components: {
            TherapyTimeRangeSelectPopper,
            BizWeekScheduleCell,
            BizWeekSchedule,
            OrderNoSelectWrapper,
            AbcSearch,
        },
        mixins: [
            TherapyTimeRangeListMixin,
        ],
        props: {
            inputWidth: {
                type: Number,
                default: 385,
            },
            size: {
                type: String,
                default: 'medium',
            },
            registrationType: {
                type: Number,
                default: BusinessType.REGISTRATION,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            PrimaryDataCyKey: {
                type: String,
                default: '',
            },
            departmentsList: {
                type: Array,
                default: () => [],
            },
            postData: {
                type: Object,
                default: () => ({}),
            },
            revisitStatus: {
                type: Number,
                default: RevisitStatus.FIRST,
            },
            registrationId: {
                type: String,
                default: '',
            },
            registrationProductIds: {
                type: Array,
                default: () => [],
            },
            orderNoAndTime: {
                type: String,
                default: '',
            },
            allDoctors: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                visiblePopper: false,
                employeeIds: [],
                doctorSearchKey: '',
                dateRange: [],
                weekScheduleData: [],
                weekScheduleLoading: false,
                currentSelectedShift: null,
                timeOfDayOptions: ['上午', '下午', '晚上'],
                selectedTimeOfDay: '',
                selectedOrderInfo: null,
                timeOfDayList: [],
                doctorNoInfoList: [],
                serviceDurationTime: 0,
                serviceMinMinutes: 0,
                serviceMaxMinutes: 0,
                visiblePanel: false,
                currentPivotDate: toDate(new Date()),
            };
        },
        computed: {
            ...mapGetters(['therapyReservationConfig']),
            popperClass() {
                let str = 'therapy-doctor-department-popper-wrapper';
                if (this.visiblePanel) {
                    str += ' has-mask';
                }
                return str;
            },
            pagination() {
                return {
                    visible: true,
                    prevDisable: false,
                    nextDisable: false,
                };
            },
            searchDoctorList() {
                return this.allDoctors.filter((item) => {
                    return (
                        (item.doctorName && item.doctorName.indexOf(this.doctorSearchKey) > -1) ||
                        (item.namePy && item.namePy.toLocaleLowerCase().indexOf(this.doctorSearchKey) > -1) ||
                        (item.namePyFirst && item.namePyFirst.toLocaleLowerCase().indexOf(this.doctorSearchKey) > -1)
                    );
                });
            },
            startDate() {
                if (!this.dateRange?.length) {
                    return parseTime(new Date(this.currentPivotDate), 'y-m-d', true);
                }
                // eslint-disable-next-line no-unused-vars
                const [ start, end ] = this.dateRange;

                return start;
            },
            currentWeekInfo() {
                const start = parseTime(getWeekStartDate(toDate(this.startDate)), 'y-m-d', true);
                const now = new Date(start);
                const year = now.getFullYear();
                const month = now.getMonth() + 1;
                return `${year} 年 ${month} 月`;
            },
            isFixOrderMode() {
                // modeType 0: 固定号源模式 1: 灵活时间模式
                return this.therapyReservationConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            // 是否是精确时间模式
            isAccurateTime() {
                return this.isFixOrderMode && this.therapyReservationConfig?.serviceType === SERVICE_TYPE_ENUM.ACCURATE_TIME;
            },
            showReserveProduct() {
                return !this.isFixOrderMode && !!this.therapyReservationConfig.showReserveProduct;
            },
            timeRangeList() {
                const list = this.doctorNoInfoList.map((item) => item.list || []).flat();
                const { length } = list;
                for (let i = 0; i < length; i++) {
                    const curItem = list[i];
                    const nextItem = list[i + 1];

                    curItem.reserveStart = this.convertTimeStr2Date(curItem.start);
                    curItem.reserveEnd = this.convertTimeStr2Date(curItem.end);

                    if (!nextItem) {
                        // 下一个不存在，则 curItem 是连续时段最后一个 item
                        curItem.isLast = true;
                        continue;
                    }

                    if (curItem.end !== nextItem.start) {
                        curItem.isLast = true;
                    }
                }
                return list;
            },
        },
        watch: {
            orderNoAndTime: {
                handler(val) {
                    if (val) {
                        const arr = val.split('-') || [];
                        this.selectedOrderInfo = {
                            orderNo: +arr[0] || '',
                            start: arr[1] || '',
                            end: arr[2] || '',
                            timeOfDay: arr[3] || '',
                        };
                    } else {
                        this.selectedOrderInfo = null;
                    }
                },
                immediate: true,
                deep: true,
            },
            'postData.reserveDate': {
                handler(val) {
                    this.currentPivotDate = toDate(val) || new Date();
                },
                immediate: true,
            },
        },
        created() {
            this._handleDoctorSearchInput = debounce(this.handleDoctorSearchInput, 200, true);
        },
        methods: {
            isEqual,
            handlePanelVisibleChange(val) {
                this.visiblePanel = false;
                if (val) {
                    this.handleWeekScheduleData();
                } else {
                    this.weekScheduleLoading = false;
                }
            },
            handleClear() {
                this.$emit('clear');
            },
            handleCloseOnClickOutside(e) {
                if (!this.visiblePopper) return;

                const eventPath = e?.path || (e?.composedPath?.());
                const flag = eventPath?.some((item) => item.className?.includes('order-no-wrapper') || item.className?.includes('time-range-panel-wrapper'));
                return !flag;
            },
            async handleClickShift(row, data) {
                if (this.judgeCurrentShiftIsDisabled(data)) {
                    return;
                }
                this.currentSelectedShift = {
                    ...data,
                    doctorInfo: Clone(row),
                };
                if (this.isFixOrderMode) {
                    // 处理当前的号源信息
                    const list = this.currentSelectedShift?.registrationCategoryScheduleIntervals || [];
                    this.doctorNoInfoList = list.find((item) => item.registrationCategory === this.postData.registrationCategory)?.scheduleIntervals || [];
                } else {
                    const {
                        registrationCategoryScheduleIntervals,
                    } = this.currentSelectedShift;

                    const registrationCategoryScheduleItem = registrationCategoryScheduleIntervals?.find((item) => item.registrationCategory === this.postData.registrationCategory) || {};
                    const {
                        scheduleIntervals,
                        serviceMinMinutes,
                        serviceMaxMinutes,
                    } = registrationCategoryScheduleItem;
                    this.doctorNoInfoList = scheduleIntervals || [];
                    this.serviceDurationTime = this.serviceMinMinutes = serviceMinMinutes || 0;
                    this.serviceMaxMinutes = serviceMaxMinutes || 240;
                }
                this.visiblePanel = true;
            },
            handleSelectNoConfirm(noInfo) {
                const {
                    doctorInfo,
                    workingDate,
                } = this.currentSelectedShift || {};
                const orderInfo = Clone({
                    ...noInfo,
                    doctorInfo,
                    workingDate,
                });
                this.selectedDoctorInfo = Clone(doctorInfo);
                this.$emit('selected-order-confirm', orderInfo);
                this.doctorSearchKey = '';
                this.expandWeekSchedule = false;
                this.visiblePanel = false;
                this.visiblePopper = false;
            },
            handleTherapyTimeRangeConfirm(data) {
                const {
                    doctorInfo,
                    workingDate,
                } = this.currentSelectedShift;

                this.$emit('selected-time-range-confirm', {
                    doctorNoInfoList: this.doctorNoInfoList,
                    serviceDurationTime: this.serviceDurationTime,
                    serviceMaxMinutes: this.serviceMaxMinutes,
                    serviceMinMinutes: this.serviceMinMinutes,
                    doctorInfo,
                    workingDate,
                    ...data,
                });
                this.visiblePanel = false;
                this.visiblePopper = false;
            },
            handleDoctorSearchInput() {
                this.handleWeekScheduleData();
            },
            handleDoctorSearchClear() {
                this.doctorSearchKey = '';
                this.handleWeekScheduleData();
            },
            getPracticeInfo(doctor) {
                const newArr = doctor.practiceInfo?.map((item) => {
                    if (item.title) {
                        const newArr = item.title.split('|') || [];
                        const len = newArr.length;
                        return newArr[len - 1];
                    }
                })?.filter((it) => Boolean(it)) || [];

                return newArr.join('、');
            },
            handleWeekScheduleData() {
                const list = (this.doctorSearchKey ? this.searchDoctorList : this.allDoctors) || [];
                this.employeeIds = list.map((item) => item.doctorId);
                this.weekScheduleData = Clone(list);

                this.fetchRegistrationsDoctorShifts();
            },
            getParams() {
                return {
                    departmentId: '',
                    employeeIds: this.employeeIds || [],
                    start: this.startDate,
                    registrationType: this.registrationType,
                    registrationCategory: this.postData.registrationCategory,
                };
            },
            async fetchRegistrationsDoctorShifts() {
                const params = this.getParams();
                try {
                    if (this.doctorSearchKey && !this.employeeIds?.length) return;

                    this.weekScheduleLoading = true;
                    const { data } = await RegistrationsAPI.fetchRegistrationsDoctorShifts(params);
                    if (isEqual(params, this.getParams())) {
                        this.weekScheduleLoading = false;
                        if (!this.doctorSearchKey) {
                            this.weekScheduleData = this.allDoctors.map((item) => {
                                const currentItem = data?.rows?.[0]?.rows?.find((it) => it.doctorId === item.doctorId);
                                if (currentItem) {
                                    item.shifts = currentItem.shifts;
                                }
                                return item;
                            });
                        } else {
                            const list = [];
                            data?.rows?.forEach((item) => {
                                if (item.rows?.length) {
                                    item.rows.forEach((it) => {
                                        list.push(it);
                                    });
                                }
                            });

                            this.weekScheduleData = this.searchDoctorList.map((item) => {
                                const currentItem = list?.find((it) => it.doctorId === item.doctorId);
                                if (currentItem) {
                                    item.shifts = currentItem.shifts;
                                }
                                return item;
                            });
                        }
                    }
                } catch (e) {
                    console.log('fetchRegistrationsDoctorShifts error', e);
                    if (isEqual(params, this.getParams())) {
                        this.weekScheduleLoading = false;
                    }
                }
            },
            getDateRange(date) {
                const start = parseTime(getWeekStartDate(date), 'y-m-d', true);
                const end = parseTime(getWeekEndDate(date),'y-m-d', true);
                console.log(['getDateRange', start, end]);
                return [start, end];
            },
            handlePrev() {
                this.currentPivotDate = prevDate(this.currentPivotDate, 7);
                this.dateRange = this.getDateRange(this.currentPivotDate);
                this.fetchRegistrationsDoctorShifts();
            },
            handleNext() {
                this.currentPivotDate = nextDate(this.currentPivotDate, 7);
                this.dateRange = this.getDateRange(this.currentPivotDate);
                this.fetchRegistrationsDoctorShifts();
            },

            judgeCurrentShiftIsDisabled(shift) {
                if (!shift) return false;

                const {
                    restCountToday,
                    canReserve,
                    registrationCategoryScheduleIntervals,
                } = shift;

                if (this.isFixOrderMode) {
                    return restCountToday === 0 || canReserve === 0;
                }
                const registrationCategoryScheduleItem = registrationCategoryScheduleIntervals?.find((item) => item.registrationCategory === this.postData.registrationCategory) || {};
                return !registrationCategoryScheduleItem?.scheduleIntervals?.length;
            },

            shiftStatusWording(shift) {
                if (!shift) return '';

                const {
                    canReserve,
                    registrationCategoryScheduleIntervals,
                    restCountToday,
                } = shift || {};

                if (canReserve === 0) {
                    return '';
                }
                const currentRegistrationCategoryScheduleIntervals = registrationCategoryScheduleIntervals?.find((item) => item.registrationCategory === this.postData.registrationCategory);
                const {
                    scheduleIntervals,
                } = currentRegistrationCategoryScheduleIntervals || {};

                if (!this.isFixOrderMode) {
                    if (scheduleIntervals?.length) {
                        return '有排班';
                    }

                    return '未排班';
                }


                if (scheduleIntervals?.length) {
                    return restCountToday ? `余 ${restCountToday}` : '约满';
                }

                return '未排班';
            },
            shiftStatusTheme(shift) {
                if (shift) {
                    const {
                        registrationCategoryScheduleIntervals,
                        restCountToday,
                    } = shift || {};
                    const currentRegistrationCategoryScheduleIntervals = registrationCategoryScheduleIntervals?.find((item) => item.registrationCategory === this.postData.registrationCategory);
                    const {
                        scheduleIntervals,
                    } = currentRegistrationCategoryScheduleIntervals || {};


                    return (scheduleIntervals?.length && restCountToday) ? 'primary' : 'gray-light';
                }
                return 'gray-light';
            },
            enterEvent(e) {
                if (!this.visiblePopper) {
                    this.visiblePopper = true;
                    this.handlePanelVisibleChange(true);
                    return;
                }

                this.visiblePopper = false;
                this.handlePanelVisibleChange(false);
                this.$nextTick(() => {
                    this.$emit('enterEvent', e);
                });
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/abc-common.scss';

.therapy-doctor-select-section-wrapper {
    .therapy-doctor-department-components-wrapper {
        .doctor-info-wrapper {
            .department,
            .doctor {
                width: 56px;
            }
        }
    }
}

.therapy-doctor-department-popper-wrapper {
    left: -194px !important;
    overflow-y: hidden;

    &.has-mask {
        border: none !important;
    }

    .panel-content-wrapper {
        position: relative;
        width: 100%;
        height: 100%;

        .mask-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 99999;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);

            .mask-content {
                position: relative;
                display: flex;
                justify-content: center;
                width: 100%;
                height: 100%;
                padding: 26px 0;

                .order-no-select-wrapper,
                .therapy-time-range-select-popper-wrapper {
                    z-index: 999999;
                    width: 530px;
                    background: var(--abc-color-cp-white);
                    border-radius: var(--abc-border-radius-small);
                }
            }
        }
    }

    .departments-week-schedule-wrapper {
        width: 100%;
        height: 408px;

        .week-schedule-wrapper {
            flex: 1;

            &:not(.content-empty) {
                .composite-week-schedule__body {
                    border-bottom: 1px solid var(--abc-color-P8);
                }
            }

            .week-schedule-search {
                position: absolute;
                top: 0;
                left: 0;

                .abc-input__inner {
                    height: 47px;
                    border: none !important;
                    box-shadow: unset !important;

                    &:not([disabled]):not(.is-disabled):not([readonly]):not(.is-readonly):focus {
                        border: none !important;
                        box-shadow: unset !important;
                    }
                }
            }

            .doctor-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 100%;

                .doctor-name {
                    width: 98px;
                    text-overflow: unset !important;
                }

                .doctor-info {
                    .doctor-practice-info {
                        width: 65px;
                        text-align: left;
                        text-overflow: unset !important;
                    }
                }
            }

            .schedule-shift-cell {
                .schedule-content-wrapper {
                    width: 100%;
                    height: 100%;

                    .shift-cell-wrapper {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }
}

.therapy-schedule-cell-popover-wrapper {
    .order-no-wrapper {
        width: 314px;
        height: 286px;
        background-color: var(--abc-color-cp-white);
        border: 1px solid var(--abc-color-P8);
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.1);

        .time-of-day-wrapper {
            width: 120px;
            height: 100%;
            padding: var(--abc-paddingTB-s) var(--abc-paddingLR-s);
            border-right: 1px solid var(--abc-color-P8);

            .time-of-day-item {
                height: 38px;
                padding: 0 var(--abc-paddingTB-ml) 0 var(--abc-paddingTB-l);
                cursor: pointer;
                border-radius: var(--abc-border-radius-mini);

                & + .time-of-day-item {
                    margin-top: 2px;
                }

                &-selected {
                    background-color: var(--abc-color-B4);
                }

                &:hover:not(.time-of-day-item-selected) {
                    background-color: var(--abc-color-cp-grey2);
                }
            }
        }

        .order-list-wrapper {
            position: relative;
            flex: 1;

            .order-no-list {
                height: calc(100% - 40px);
                padding: var(--abc-paddingTB-s) var(--abc-paddingLR-s);
                overflow: auto;

                .order-no-item {
                    height: 38px;
                    padding: 0 var(--abc-paddingTB-l);
                    cursor: pointer;
                    border-radius: var(--abc-border-radius-mini);

                    & + .order-no-item {
                        margin-top: 2px;
                    }

                    .order-no {
                        width: 35px;
                    }

                    .residue-count,
                    .icon-wrapper {
                        margin-left: 8px;
                    }

                    &-selected {
                        background-color: var(--abc-color-B3);

                        .order-no,
                        .order-time-range,
                        .residue-count {
                            color: var(--abc-color-S2);
                        }
                    }

                    &:hover:not(.order-no-item-selected) {
                        background-color: var(--abc-color-cp-grey2);
                    }
                }
            }

            .btn-wrapper {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 40px;
                background-color: var(--abc-color-cp-grey2);
                border-top: 1px solid var(--abc-color-P8);
            }
        }
    }
}
</style>
