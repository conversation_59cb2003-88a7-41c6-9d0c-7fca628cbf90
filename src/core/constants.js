export const AppTabId = Object.freeze({
    HOSPITAL_DASHBOARD: 0, // 住院工作台
    HOSPITAL_DOCTOR: 1, // 住院医生站
    HOSPITAL_NURSE: 2, // 住院护士站
    TREATMENT: 3, // 门诊执行
    REGISTRATION: 4, // 挂号预约
    OUTPATIENT: 5, // 门诊
    CHILD_HEALTH: 6, // 儿保
    CHARGE: 7, // 收费
    PHARMACY: 8, // 药房
    EXAMINATION: 9, // 检验
    INSPECT: 10, // 检查
    PURCHASE: 11, // 采购
    CRM: 12, // 患者
    SOCIAL: 13, // 医保
    STATISTICS: 14, // 统计
    MARKETING: 15, // 营销
    SETTING: 16, // 管理
    INVENTORY: 17, // 库存
    SCRM: 18, // 企微管家
    MALL: 19, // 商城
    HOSPITAL_SUPPLY_CENTER: 20, // 供应中心
    PHYSICAL_EXAMINATION_INTEGRATED: 21,//体检综合工作站
    PHYSICAL_EXAMINATION_CASHIER: 22,//体检收费站
    PHYSICAL_EXAMINATION_ASSESSMENT: 25,//体检总评医生站
    PHYSICAL_EXAMINATION_ORDER: 26,//体检订单管理
    MEDICAL_RECORD_MANAGEMENT: 27, // 病案管理
    GSP: 28, // GSP
    WE_CLINIC: 29, // 微医院
});

export const ModuleNameMapToTabId = Object.freeze({
    'cashier': AppTabId.CHARGE,
    'pharmacy': AppTabId.PHARMACY,
    'social-inventory': AppTabId.SOCIAL,
    'outpatient': AppTabId.OUTPATIENT,
});
export const CMSModuleNamePath = Object.freeze({
    'cashier': '/cashier',
    'pharmacy': '/pharmacy',
    'social-inventory': '/social/inventory/inventory',
    'outpatient': '/outpatient',
});
export const CMSModuleNamePathHospital = Object.freeze({
    'cashier': '/his-charge/cashier',
    'pharmacy': '/his-pharmacy/pharmacy',
    'social-inventory': '/social/inventory/inventory',
    'outpatient': '/his-outpatient/outpatient',
});

export const AppTabName = Object.freeze({
    [AppTabId.HOSPITAL_DASHBOARD]: '工作台',
    [AppTabId.HOSPITAL_DOCTOR]: '住院医生站',
    [AppTabId.HOSPITAL_NURSE]: '住院护士站',
    [AppTabId.TREATMENT]: '门诊护士站',
    [AppTabId.REGISTRATION]: '预约工作站',
    [AppTabId.OUTPATIENT]: '门诊医生站',
    [AppTabId.CHILD_HEALTH]: '儿保门诊',
    [AppTabId.CHARGE]: '收费工作站',
    [AppTabId.PHARMACY]: '药房工作站',
    [AppTabId.EXAMINATION]: '检验工作站',
    [AppTabId.INSPECT]: '检查工作站',
    [AppTabId.PURCHASE]: '采购管理',
    [AppTabId.CRM]: '患者管理',
    [AppTabId.SOCIAL]: '医保管理',
    [AppTabId.STATISTICS]: '统计分析',
    [AppTabId.MARKETING]: '营销管理',
    [AppTabId.SETTING]: '系统设置',
    [AppTabId.INVENTORY]: '库存管理',
    [AppTabId.SCRM]: '企微管家',
    [AppTabId.MALL]: '直采商城',
    [AppTabId.HOSPITAL_SUPPLY_CENTER]: '采购管理',
    [AppTabId.PHYSICAL_EXAMINATION_INTEGRATED]: '综合工作站',
    [AppTabId.PHYSICAL_EXAMINATION_CASHIER]: '体检收费站',
    [AppTabId.PHYSICAL_EXAMINATION_ASSESSMENT]: '总检医生站',
    [AppTabId.PHYSICAL_EXAMINATION_ORDER]: '订单管理',
    [AppTabId.MEDICAL_RECORD_MANAGEMENT]: '病案管理',
    [AppTabId.GSP]: 'GSP',
    [AppTabId.WE_CLINIC]: '微医院',
});

export const AppScene = Object.freeze({
    HIS_PC: 0, // 诊所管家pc
    HIS_APP: 1, // 诊所管家app
    HIS_TO_SCRM: 9, // his跳转到scrm
    SCRM_WEB: 10, // scrm-pc
    SCRM_QW: 11, // scrm-企微
    MALL_WEAPP: 20, // 商城小程序
    SOCIAL: 30, // 医保独立端pc
    SCREENSHOT_UPLOAD: 40, // 影像截图上传
});
