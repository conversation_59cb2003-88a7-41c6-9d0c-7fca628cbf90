import 'requestidlecallback-polyfill';
import { AbcDatePickerBar } from '@abc/ui-pc';
const { DatePickerBarOptions } = AbcDatePickerBar;
import { ModalFunc as AbcModal } from '@abc/ui-pc';
import { ToastFunc as Toast } from '@abc/ui-pc';
import * as feEngineCore from 'MfFeEngine/core';
import * as repository from 'MfFeEngine/repository';

import 'normalize.css/normalize.css'; // normalize.css 样式格式化
import { pinia } from 'store/pinia.js';
import fetch, {
    cancelPendingRequest, addSignToRequest,
} from 'utils/fetch';
import { exportFileByAxios } from 'utils/excel';
import 'core-js/stable'; // polyfill
import 'regenerator-runtime/runtime'; // polyfill
import Vue from 'vue';
import VueRouter from 'vue-router';
import App from './App.vue';
import './abc-ui-regist';
import components from './components/';
import * as directives from './directive'; // 全局vue directives
import * as filters from './filters'; // 全局vue filter
import {
    create as createRouter, initAppTabId,
} from './router';
import store from './store';
import { watchUserClinicChange } from './views/common/login-optimize';

import 'views/statistics/index.scss'; //统计模块
import './styles/statistics.scss';
// ABC 微前端
import ExpireAlert from '@/views/edition/expire-alert/index';
import { getModuleConfig } from '@modules/config';
import {
    ABCPlatform, MFEError,
} from 'abc-micro-frontend';
import Service from './service/index';
// 打印模板文件初始化
import { loadAbcPrint } from '@/printer/print-init/index.js';
import abcRegulatory from '@/regulatory';
// 社保注入
import lifecycle from '@/lifecycle';
import { ABCPrintConfigKeyMap } from '@/printer/constants';
import AbcPrinter from '@/printer/index.js';
import abcSocialSecurity from '@/social-security';
import PropertyAPI from 'api/property/index.js';
import AbcEventBus from 'utils/event-bus';

import {
    BaseApp, NavigateHelper,
} from '@/core/index.js';
import {
    isChainSubClinic, isHospital, isSingleClinic,
} from 'views/common/clinic.js';

import AbcSocket from 'views/common/single-socket.js';
// LIS
import abcPcLis from '@/lis';
import { MultiTabLisServer } from '@/lis/MultiTabLisServer.js';
import { MultiTabLisClient } from '@/lis/MultiTabLisClient';
import { TodoService } from '@/service/todo';
import Logger from 'utils/logger';
import i18n from '@/i18n/index.js';
import { initI18n } from '@/i18n/modify-i18n-messages.js';
import { AbcMedicalImagingViewerService } from '@/service/abcMedicalImagingViewer';
import {
    loadWindowManager, canUseLisPlugin,
} from 'utils/load-window-manager';
import { bindRelationType } from '@/views-pharmacy/common/constants';

import trackLogger, { TRACK_EVENTS } from 'utils/track-logger';
// 提供给社保的依赖
import * as AbcUI from '@abc/ui-pc';
import AbcChargeService from '@/service/charge';
import { pdfLodopPrint } from '@/printer/utils/index';
import ABCPrinterConfig from '@/printer/config.js';
import {
    navigateToSocialAccountCheckingNew,
    navigateToPharmacy,
} from '@/core/navigate-helper';
import BizMixedSelectionFilter from '@/components-composite/biz-mixed-selection-filter';
import {
    BizSettingLayout,
    BizSettingContent,
    BizSettingFooter,
    BizSettingSidebar,
    BizFillRemainHeight,
} from '@/components-composite/setting-form-layout/index.js';
import AbcFileUploader from '@/components/abc-file-uploader/index.vue';
import AbcTraceCodeCollectionPanel from '@/service/trace-code/components/collection-panel/index.vue';
import * as AbcPharmacyConstants from 'views/pharmacy/constants.js';

import {
    BizSettingForm,
    BizSettingFormGroup,
    BizSettingFormItem,
    BizSettingFormItemTip,
    BizSettingFormItemIndent,
    BizSettingFormHeader,
} from '@/components-composite/setting-form/index.js';
import BizPatientSelector from '@/views/layout/patient/patient-section';
import BizDataStatisticsCard from '@/components-composite/biz-data-statistics-card';
import { AnnouncementDialog } from 'views/layout/announcement/notice-dialog/index.js';
import {
    BizValueAddedCard, BizVersionTips,
} from '@/components-composite/index.js';
import { FunctionalDialog } from 'views/common/functional-dialog';
import SignService from 'utils/sign-service'; // 签名服务
import DecodeService from '@/service/decode';

console.info('构建时间：', process.env.buildInfo && process.env.buildInfo.BUILD_TIME);

const { currentClinic } = store.getters;
if (currentClinic && (isHospital(currentClinic) || !isChainSubClinic(currentClinic) && !isSingleClinic(currentClinic))) {
    NavigateHelper.navigateToAppIndex(currentClinic);
}
initAppTabId();
class ClinicApp extends BaseApp {
    onInit() {
        trackLogger.record(TRACK_EVENTS.APP_ON_INIT);
        const { socket } = AbcSocket.getSocket();

        feEngineCore.init({
            network: fetch,
            socket,
        });
        trackLogger.record(TRACK_EVENTS.FE_ENGINE_INIT_END);

        lifecycle.beforeCreate();
        Vue.use(abcRegulatory);
        Vue.use(abcSocialSecurity);

        const moduleConfig = getModuleConfig(process.env.BUILD_ENV);
        Vue.use(ABCPlatform, {
            Vue,
            VueRouter,
            store,
            fetch,
            AbcNavigateHelper: {
                navigateToPharmacy,
                navigateToSocialAccountCheckingNew,
            },
            addSignToRequest,
            exportFileByAxios,
            moduleConfig,
            ExpireAlert,
            Confirm: AbcModal.confirm,
            AbcModal,
            Toast,
            DatePickerBarOptions,
            AbcPrinter,
            ABCPrintConfigKeyMap,
            PropertyAPI,
            AbcUI,
            AbcChargeService,
            pdfLodopPrint,
            ABCPrinterConfig,
            AbcTraceCodeCollectionPanel,
            AbcPharmacyConstants,
            BizSetting: {
                BizMixedSelectionFilter,
                BizSettingLayout,
                BizSettingContent,
                BizSettingFooter,
                BizSettingSidebar,
                BizFillRemainHeight,
                BizSettingForm,
                BizSettingFormGroup,
                BizSettingFormItem,
                BizSettingFormItemTip,
                BizSettingFormItemIndent,
                BizSettingFormHeader,
            },
            BizComponent: {
                BizPatientSelector,
                BizDataStatisticsCard,
                AbcFileUploader,
                BizValueAddedCard,
                BizVersionTips,
            },
            AnnouncementDialog,
            FunctionalDialog,
            SignService, // 签名服务
        });
        watchUserClinicChange();

        Vue.use(AbcEventBus);

        // 注册全局 filter
        Object.keys(filters).forEach((key) => {
            Vue.filter(key, filters[key]);
        });

        // 注册全局指令
        Object.keys(directives).forEach((key) => {
            Vue.directive(key, directives[key]);
        });

        // 全局组件注册
        Object.keys(components).forEach((key) => {
            Vue.component(key, components[key]);
        });


        //是否客户端环境判断，注册window.ipcRendererInit方法，在客户端环境完成初始化后，会调用
        window.ipcRendererInit = () => {
            store.commit('SET_ELECTRON', true);
        };
        if (window.electronFlag) {
            window.ipcRendererInit();
        }
    }

    async onBoot() {
        trackLogger.record(TRACK_EVENTS.APP_ON_BOOT_START);
        this.store = store;
        await store.dispatch('updateUserActionByCookie');
        // 拉取用户基本信息、社保 Config 信息，空中药房开关，统计-慢病，需要在 router 生成前获取
        await store.dispatch('acFetchUserInfo');
        // 进entry界面由于没有 当前登录的clinic信息，不需要拉取config

        // 云检版本需要根据版本分发差异，先拉取版本信息
        await store.dispatch('edition/acFetchEditionConf');

        await Promise.all([
            store.dispatch('acFetchCurrentClinicInfo'),
            store.dispatch('acGetClinicJoined'),
            store.dispatch('initGoodsConfig'),
            store.dispatch('fetchChainBasic'),
            store.dispatch('fetchClinicBasic'),
            store.dispatch('fetchCurrentClinicConfig'),
            store.dispatch('fetchClinicBasicConfig'),
            store.dispatch('socialPc/acInitSocialConfig').then(() => abcSocialSecurity.initNational()),
            store.dispatch('regulatoryPc/acInitRegulatoryConfig').then(() => abcRegulatory.initRegulatory()),

            // 需要根据配置信息组装路由
            store.dispatch('examination/fetchExaminationSettings'),
            store.dispatch('inspect/fetchInspectSettings'),
            store.dispatch('coPharmacyClinic/getCoClinicCountInfo', bindRelationType.CLINIC),
            store.dispatch('getTraceCodeCollectionOpenConfig'),
        ]);
        trackLogger.record(TRACK_EVENTS.APP_ON_BOOT_DISPATCH_END);
        try {
            await abcRegulatory.initRegulatoryData();
        } catch (e) {
            console.error('abcRegulatory.initRegulatoryData error ', e);
        }
        trackLogger.record(TRACK_EVENTS.APP_ON_BOOT_REGULATORY_END);


        if (canUseLisPlugin(this.store)) {
            Vue.use(abcPcLis);
        }
        trackLogger.record(TRACK_EVENTS.APP_ON_BOOT_LIS_END);

        let moduleIds = [];
        let roleIds = [];
        const { userInfo } = store.getters;
        const { currentClinic } = store.getters;
        if (userInfo && userInfo.moduleIds) {
            moduleIds = userInfo.moduleIds.split(',');
            roleIds = userInfo.roleIds;
        }

        const noModuleAuthList = ['/buying']; // 验证权限白名单

        this.router = createRouter({
            moduleIds, roleIds, currentClinic, store,
        });
        this.router.beforeEach(async (to, from, next) => {
            cancelPendingRequest();
            try {
                if (store.getters.userInfo && store.getters.clinics) { // 判断是否有 userId clinics
                    if (store.getters.userInfo.moduleIds) {
                        store.dispatch('initRefreshToken');
                        await this.pageHandler(to, from, next);
                    } else {
                        console.log('没有moduleIds', store.getters.userInfo.moduleIds);
                        if (noModuleAuthList.indexOf(to.path) !== -1) { // 在验证权限白名单，直接进入
                            next();
                        } else {
                            NavigateHelper.navigateToLogin();
                        }
                    }
                } else {
                    console.log('没有用户和clinic信息', store.getters.userInfo, store.getters.clinics);
                    NavigateHelper.navigateToLogin();
                }
            } catch (err) {
                console.log('登录报错', err);
                NavigateHelper.navigateToLogin();
            }
        });
        this.router.afterEach(() => {
            // 老布局
            $('.app-wrapper > .container-wrapper').scrollTop(0, 0);
            // 使用abc-container的需要切换路由后滚动到顶部
            $('#abc-container #abc-container-center').scrollTop(0, 0);
        });
        trackLogger.record(TRACK_EVENTS.ROUTER_INIT_END);

        // 初始化 TodoService
        const { socket } = AbcSocket.getSocket();
        const todoService = new TodoService(socket, this.store);
        this.registerService(TodoService.NAME, todoService);

        //设置DecodeService key
        DecodeService.setKey(this.store.getters['theme/curTypeId']);

        // 更改i18n配置
        await initI18n(i18n);

        this.vm = window._vue = new Vue({
            router: this.router,
            store,
            pinia,
            i18n,
            render: (h) => h(App),
        });
        this.vm.$mount('#app');
        // window._vue.$abcPlatform.registerGlobalComponents('Schedules', Schedules);
        // 注册后 可以通过 this.$abcPlatform.service.xxx 或者 window.$platform.service.xxx 调用，例如：
        // this.$abcPlatform.service.wallet.payOrder(id, data);
        this.vm.$abcPlatform.registerService(Service);
        this.vm.$abcPlatform.registerErrorHandler((err) => {
            if (err.type === MFEError.ERR_MODULE_NOT_FOUND) {
                Logger.error({
                    scene: 'LOGOUT',
                    err,
                    data: 'ERR_MODULE_NOT_FOUND',
                });
                NavigateHelper.navigateToLogin();
            }
        });
        this.vm.$abcPlatform.boot(this.vm);
        const { clinicBasic } = store.getters;
        if (clinicBasic.isEnableAIExperience) {
            const viewDistributeConfig = store.getters['viewDistribute/viewDistributeConfig'];
            const { Outpatient } = viewDistributeConfig;
            const { routeBasePath } = Outpatient;
            this.router.replace({
                path: `${routeBasePath}outpatient`,
            });
        }
        trackLogger.record(TRACK_EVENTS.APP_ON_BOOT_END);
    }

    onAfterBoot() {
        super.onAfterBoot();
        const goodsRepoInstance = repository.GoodsRepositoryService.getInstance();

        window.requestIdleCallback(async () => {
            trackLogger.record(TRACK_EVENTS.APP_REQUEST_IDLE_CALLBACK);

            // 初始化 PrintManager
            const {
                chainBasic, clinicBasic,
            } = store.getters;
            loadAbcPrint({
                isEnableDesktopPrint: chainBasic.isEnableDesktopPrint, printConfig: clinicBasic.printConfig,
            });

            goodsRepoInstance.init();
            AbcMedicalImagingViewerService.getInstance().start();
            loadWindowManager(this.store);

            if (canUseLisPlugin(this.store)) {
                MultiTabLisServer.getInstance().init(); // 加载LIS插件
                MultiTabLisClient.getInstance().init();
            } else {
                MultiTabLisClient.getInstance().init();
            }
        }, {
            timeout: 2000,
        });
    }

}

trackLogger.record(TRACK_EVENTS.NEW_APP);

new ClinicApp(Vue).boot().catch((error) => {
    console.log('error', error);
    NavigateHelper.navigateToLogin();
});
