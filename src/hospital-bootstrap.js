import * as feEngineCore from 'MfFeEngine/core';
import * as repository from 'MfFeEngine/repository';
import Vue from 'vue';
import VueRouter from 'vue-router';
import './abc-ui-regist';
import Hospital from './Hospital.vue';
import { create as createRouter } from './router/biz-hospital.js';
import Store from './store/hospital.js';
import 'normalize.css/normalize.css'; // normalize.css 样式格式化
import * as filters from './filters'; // 全局vue filter
import * as directives from './directive'; // 全局vue directives
import components from './components/';
import { ToastFunc as Toast } from '@abc/ui-pc';
import { ModalFunc as AbcModal } from '@abc/ui-pc';
import 'core-js/stable'; // polyfill
import 'regenerator-runtime/runtime'; // polyfill
import fetch, {
    cancelPendingRequest, addSignToRequest,
} from 'utils/fetch';
import { exportFileByAxios } from 'utils/excel';
import { watchUserClinicChange } from './views/common/login-optimize';
import { AbcDatePickerBar } from '@abc/ui-pc'; const { DatePickerBarOptions } = AbcDatePickerBar;
import { pinia } from 'store/pinia.js';

import 'views/statistics/index.scss'; //统计模块
import './styles/statistics.scss';

// ABC 微前端
import {
    ABCPlatform, MFEError,
} from 'abc-micro-frontend';
import { getModuleConfig } from '@modules/config';
import Service from './service/index';
import ExpireAlert from '@/views/edition/expire-alert/index';
// 打印模板文件初始化
import { loadAbcPrint } from '@/printer/print-init/index.js';
// 社保注入
import abcSocialSecurity from '@/social-security';
import lifecycle from '@/lifecycle';
import AbcEventBus from 'utils/event-bus';
import AbcPrinter from '@/printer/index.js';
import { ABCPrintConfigKeyMap } from '@/printer/constants';
import PropertyAPI from 'api/property/index.js';
import {
    AppTabId, BaseApp, NavigateHelper,
} from '@/core/index.js';
import { isHospital } from 'views/common/clinic.js';
import abcRegulatory from '@/regulatory';
import AbcSocket from 'views/common/single-socket.js';
import {
    AppTabConfig, getAppTabIdByPathname,
} from '@/core/config.js';
import abcPcLis from '@/lis/index.js';
import { MultiTabLisClient } from '@/lis/MultiTabLisClient.js';
import { MultiTabLisServer } from '@/lis/MultiTabLisServer.js';
import { TodoService } from '@/service/todo';
import i18n from '@/i18n';
import { AbcMedicalImagingViewerService } from '@/service/abcMedicalImagingViewer';
import { initI18n } from '@/i18n/modify-i18n-messages';
// 提供给社保的依赖
import * as AbcUI from '@abc/ui-pc';
import AbcChargeService from '@/service/charge';
import { pdfLodopPrint } from '@/printer/utils/index';
import ABCPrinterConfig from '@/printer/config.js';
import {
    BizSettingLayout,
    BizSettingContent,
    BizSettingFooter,
    BizSettingSidebar,
    BizFillRemainHeight,
} from '@/components-composite/setting-form-layout/index.js';

import {
    BizSettingForm,
    BizSettingFormGroup,
    BizSettingFormItem,
    BizSettingFormItemTip,
    BizSettingFormItemIndent,
    BizSettingFormHeader,
} from '@/components-composite/setting-form/index.js';

import BizPatientSelector from '@/views/layout/patient/patient-section';
import BizDataStatisticsCard from '@/components-composite/biz-data-statistics-card';
import { AnnouncementDialog } from 'views/layout/announcement/notice-dialog/index.js';
import AbcFileUploader from '@/components/abc-file-uploader/index.vue';
import BizMixedSelectionFilter from '@/components-composite/biz-mixed-selection-filter';
import {
    BizValueAddedCard, BizVersionTips,
} from '@/components-composite/index';
import { FunctionalDialog } from 'views/common/functional-dialog';
import SignService from 'utils/sign-service'; // 签名服务
import DecodeService from '@/service/decode';

console.info('构建时间：', process.env.buildInfo && process.env.buildInfo.BUILD_TIME);

if (Store.getters.currentClinic && !isHospital(Store.getters.currentClinic)) {
    NavigateHelper.navigateToAppIndex(Store.getters.currentClinic);
}

window.appTabId = getAppTabIdByPathname(location.pathname);

class HospitalApp extends BaseApp {
    async onInit() {
        const { socket } = AbcSocket.getSocket();
        feEngineCore.init({
            network: fetch,
            socket,
        });
        const goodsRepoInstance = repository.GoodsRepositoryService.getInstance();
        goodsRepoInstance.init();

        lifecycle.beforeCreate();
        if (window.appTabId === AppTabId.HOSPITAL_DASHBOARD) {
            Vue.use(abcPcLis);
        }
        Vue.use(abcSocialSecurity);
        Vue.use(abcRegulatory);
        const moduleConfig = getModuleConfig(process.env.BUILD_ENV);
        Vue.use(ABCPlatform, {
            Vue,
            VueRouter,
            routeBase: '/hospital/',
            store: Store,
            fetch,
            addSignToRequest,
            exportFileByAxios,
            moduleConfig,
            ExpireAlert,
            Confirm: AbcModal.confirm,
            AbcModal,
            Toast,
            DatePickerBarOptions,
            AbcPrinter,
            ABCPrintConfigKeyMap,
            PropertyAPI,
            NavigateHelper,
            AbcUI,
            AbcChargeService,
            pdfLodopPrint,
            ABCPrinterConfig,
            BizSetting: {
                BizMixedSelectionFilter,
                BizSettingLayout,
                BizSettingContent,
                BizSettingFooter,
                BizSettingSidebar,
                BizFillRemainHeight,
                BizSettingForm,
                BizSettingFormGroup,
                BizSettingFormItem,
                BizSettingFormItemTip,
                BizSettingFormItemIndent,
                BizSettingFormHeader,
            },
            BizComponent: {
                BizPatientSelector,
                BizDataStatisticsCard,
                AbcFileUploader,
                BizValueAddedCard,
                BizVersionTips,
            },
            AnnouncementDialog,
            FunctionalDialog,
            SignService, // 签名服务
        });

        watchUserClinicChange();

        Vue.use(AbcEventBus);

        // 注册全局 filter
        Object.keys(filters).forEach((key) => {
            Vue.filter(key, filters[key]);
        });

        // 注册全局指令
        Object.keys(directives).forEach((key) => {
            Vue.directive(key, directives[key]);
        });

        // 全局组件注册
        Object.keys(components).forEach((key) => {
            Vue.component(key, components[key]);
        });

        //是否客户端环境判断，注册window.ipcRendererInit方法，在客户端环境完成初始化后，会调用
        window.ipcRendererInit = () => {
            Store.commit('SET_ELECTRON', true);
        };
        if (window.electronFlag) {
            window.ipcRendererInit();
        }
    }

    async onBoot() {
        this.createStore(Store);

        await this.prepareAppConfig();

        try {
            await abcRegulatory.initRegulatoryData();
        } catch (e) {
            console.error('abcRegulatory.initRegulatoryData error ', e);
        }

        this.createRouter();

        // 初始化 TodoService
        const { socket } = AbcSocket.getSocket();
        const todoService = new TodoService(socket, this.store);
        this.registerService(TodoService.NAME, todoService);

        //设置DecodeService key
        DecodeService.setKey(this.store.getters['theme/curTypeId']);

        // 更改i18n配置
        await initI18n(i18n);

        this.vm = window._vue = new Vue({
            router: this.router,
            store: this.store,
            pinia,
            i18n,
            render: (h) => h(Hospital),
        });
        this.vm.$mount('#app');
        // window._vue.$abcPlatform.registerGlobalComponents('Schedules', Schedules);
        // 注册后 可以通过 this.$abcPlatform.service.xxx 或者 window.$platform.service.xxx 调用，例如：
        // this.$abcPlatform.service.wallet.payOrder(id, data);
        this.vm.$abcPlatform.registerService(Service);
        this.vm.$abcPlatform.registerErrorHandler((err) => {
            if (err.type === MFEError.ERR_MODULE_NOT_FOUND) {
                NavigateHelper.navigateToLogin();
            }
        });
        this.vm.$abcPlatform.boot(this.vm);

        // 工作台作为服务端加载 lis 插件
        if (this.store.state.viewDistribute.viewDistributeConfig.appTabId === AppTabId.HOSPITAL_DASHBOARD) {
            MultiTabLisServer.getInstance().init(); // 加载LIS插件
        } else {
            // 其他 tab 使用客户端加载 lis 插件
            MultiTabLisClient.getInstance().init();
        }

        // 初始化 PrintManager
        const {
            chainBasic, clinicBasic,
        } = this.store.getters;
        loadAbcPrint({
            isEnableDesktopPrint: chainBasic.isEnableDesktopPrint, printConfig: clinicBasic.printConfig,
        });
    }

    onAfterBoot() {
        super.onAfterBoot();
        AbcMedicalImagingViewerService.getInstance().start();
    }

    /**
     * 创建 store
     * @param store
     */
    createStore(store) {
        this.store = store;
    }

    /**
     * 准备应用配置数据
     */
    async prepareAppConfig() {
        await this.store.dispatch('updateUserActionByCookie');
        // 拉取用户基本信息、社保 Config 信息，空中药房开关，统计-慢病，需要在 router 生成前获取
        await this.store.dispatch('acFetchUserInfo');
        const promiseArr = [
            this.store.dispatch('acFetchCurrentClinicInfo'),
            this.store.dispatch('acGetClinicJoined'),
            this.store.dispatch('initGoodsConfig'),
            this.store.dispatch('fetchChainBasic'),
            this.store.dispatch('fetchClinicBasic'),
            this.store.dispatch('fetchCurrentClinicConfig'),
            this.store.dispatch('fetchClinicBasicConfig'),
            this.store.dispatch('socialPc/acInitSocialConfig'),
            this.store.dispatch('regulatoryPc/acInitRegulatoryConfig'),
            this.store.dispatch('edition/acFetchEditionConf'),

            // 需要根据配置信息组装路由
            this.store.dispatch('examination/fetchExaminationSettings'),
            this.store.dispatch('inspect/fetchInspectSettings'),
            this.store.dispatch('getTraceCodeCollectionOpenConfig'),
            this.store.dispatch('socialPc/acInitSocialConfig').then(() => abcSocialSecurity.initNational()),
        ];
        return Promise.all(promiseArr);
    }

    /**
     * 创建路由
     * @returns {VueRouter}
     */
    createRouter() {
        const { store } = this;
        let moduleIds = [];
        let roleIds = [];
        const { userInfo } = store.getters;
        const { currentClinic } = store.getters;
        if (userInfo && userInfo.moduleIds) {
            moduleIds = userInfo.moduleIds.split(',');
            roleIds = userInfo.roleIds;
        }
        this.router = createRouter({
            moduleIds, roleIds, currentClinic, store,
        });
        // 路由前置拦截
        this.router.beforeEach(async (to, from, next) => {
            cancelPendingRequest();
            try {
                if (store.getters.userInfo && store.getters.clinics && store.getters.userInfo.moduleIds) { // 判断是否有 userId clinics
                    store.dispatch('initRefreshToken');
                    await this.pageHandler(to, from, next);
                } else {
                    NavigateHelper.navigateToLogin();
                }
            } catch (err) {
                NavigateHelper.navigateToLogin();
            }
        });
        this.router.afterEach(() => {
            // 使用abc-container的需要切换路由后滚动到顶部
            $('#abc-container #abc-container-center').scrollTop(0, 0);
        });
    }

    /**
     * @desc 统一初始化page
     * <AUTHOR>
     * @date 2022-11-28 11:08:39
     */
    async pageHandler(to, from, next) {
        if (this.isExpired) {
            const isExpiredContent = to.path.startsWith('/social') || to.path.startsWith('/statistics');
            if (this.canViewExpiredContent && isExpiredContent) {
                //  可见过期内容
            } else if (to.meta?.visibleOnExpired) {
                // 过期始终可见
            } else {
                location.href = '/hospital/dashboard';
                return;
            }
        }
        if (to.name === '@error') {
            next();
            return;
        }

        // TODO，护士站/医生站需要处理病区 id，科室 id
        if (this.store.state.viewDistribute.viewDistributeConfig.appTabId === AppTabId.HOSPITAL_NURSE) {
            const wardId = await this.store.dispatch('hospitalGlobal/selectWardAreaId', to.params.wardId);
            if (!wardId) {
                next({
                    name: '@error',
                    query: {
                        from: encodeURIComponent(to.path),
                        msg: '您还未加入病区，请联系管理员',
                    },
                    replace: true,
                });
                return;
            }
            if (wardId !== to.params.wardId) {
                to.params.wardId = wardId;
                next({
                    ...to,
                });
                return;
            }
        }

        if (this.store.state.viewDistribute.viewDistributeConfig.appTabId === AppTabId.HOSPITAL_DOCTOR) {
            const departmentId = await this.store.dispatch('hospitalGlobal/selectDepartmentId', to.params.departmentId);
            if (!departmentId) {
                next({
                    name: '@error',
                    query: {
                        from: encodeURIComponent(to.path),
                        msg: '您还未加入科室，请联系管理员',
                    },
                    replace: true,
                });
                return;
            }
            if (departmentId !== to.params.departmentId) {
                to.params.departmentId = departmentId;
                next({
                    ...to,
                });
                return;
            }
        }

        if (this.store.state.viewDistribute.viewDistributeConfig.appTabId === AppTabId.INVENTORY) {
            const stockRoomId = await this.store.dispatch('selectStockRoomId', to.params.stockRoomId);
            if (!stockRoomId) {
                next({
                    name: '@error',
                    query: {
                        from: encodeURIComponent(to.path),
                        msg: '您还没有加入任何库房，请联系管理员',
                    },
                    replace: true,
                });
                return;
            }
            if (stockRoomId !== to.params.stockRoomId) {
                // 没有stockRoomId是错误的路由，跳转到正确的路由
                if (!to.params.stockRoomId) {
                    location.href = `/${AppTabConfig[AppTabId.INVENTORY].pathPrefix}/${stockRoomId}`;
                } else {
                    to.params.stockRoomId = stockRoomId;
                    const resolvedRoute = this.router.resolve(to);
                    location.href = resolvedRoute.href;
                }
                return;
            }
        }

        const res = to.matched.find((it) => {
            return it.meta?.pageAsyncClass;
        });

        if (res) {
            try {
                const { pageAsyncClass } = res.meta;
                // 初始化 page
                const PageClass = await pageAsyncClass();

                const curPage = this.getPage();
                if (curPage instanceof PageClass.default) {
                    const newPage = new PageClass.default;
                    if (newPage.instanceKey && newPage.instanceKey === curPage.instanceKey) {
                        console.debug('[pageHandler] instanceKey 一致，不处理', newPage.name);
                        next();
                        return;
                    }
                }
                const pageInstance = new PageClass.default;
                this.addPage(pageInstance);
                await pageInstance.init();
                next();
            } catch (e) {
                console.error(e);
                next(false);
            }
            return;
        }
        // 没有 pageAsyncClass，需要将当前 page 置空，（比如点击去了商城，social 等子模块）
        this.addPage(null);

        next();
    }
}


new HospitalApp(Vue).boot().catch((error) => {
    console.log('error', error);
    NavigateHelper.navigateToLogin();
});
