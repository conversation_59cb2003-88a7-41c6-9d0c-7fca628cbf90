<template>
    <abc-card class="biz-patient-card" :style="computedStyles" style="padding: 16px 0 0 10px; overflow-y: scroll;">
        <!-- 患者信息区域 -->
        <!-- 标签+基础信息维护 -->
        <div style="padding: 0 6px;">
            <!-- 标签和就诊信息 -->
            <biz-tag-selector
                v-if="!isAddPatient"
                class="patient-card__tag-wrapper"
                :patient-id="patientId"
                :origin-labels="originLabels"
                :add-patient="isAddPatient"
                :view-distribute-config="viewDistributeConfig"
                :is-single-store="isSingleStore"
                :current-clinic="currentClinic"
                :crm-a-p-i="crmAPI"
                :patient-info="patientInfo"
                @change-patient-tags="(info)=> $emit('change-patient-tags', info)"
                @updateTagsByFetch="(flag)=> $emit('updateTagsByFetch', flag)"
            >
                <abc-flex :gap="8" style="height: 20px; margin-right: 6px;" align="center">
                    <img
                        v-if="displayMember"
                        class="patient-card__tag-wrapper-logo"
                        src="../images/v-yellow-square.png"
                        alt="vip"
                    />
                    <img
                        v-if="displayCharge"
                        class="patient-card__tag-wrapper-logo"
                        src="../images/<EMAIL>"
                        alt="arrears"
                    />
                    <img
                        v-if="displayWechat"
                        class="patient-card__tag-wrapper-logo"
                        src="../images/wechat.png"
                        alt="wechat"
                    />
                    <img
                        v-if="displayQiWei"
                        class="patient-card__tag-wrapper-logo"
                        src="../images/qiwei.png"
                        alt="qiwei"
                    />
                </abc-flex>
            </biz-tag-selector>
            <div class="patient-card__statistics">
                <abc-text>门诊 {{ patientBaseInfo.outpatientCount || 0 }} 次，消费 {{ currencySymbol }}{{ formatMoney(patientBaseInfo.cumulativeAmount || 0) }}</abc-text>
                <abc-text v-if="isNeedDisCharge" theme="danger">
                    ，（欠费 {{ currencySymbol }}{{ formatMoney(patientInfo.owePayedAmount || 0) }})
                </abc-text>
                <abc-text v-if="patientInfo && patientInfo.mobile && needPatientBaseComponentShowTags">
                    ，手机 {{ patientInfoMobile }}
                </abc-text>
            </div>

            <abc-divider
                size="normal"
                theme="light"
                variant="dashed"
                style="margin: 12px 0;"
            ></abc-divider>
            <abc-flex
                style="width: 100%; height: 26px; margin-bottom: 4px;"
                align="center"
                justify="space-between"
            >
                <abc-text :bold="true" size="small">
                    基础信息
                </abc-text>
                <abc-space>
                    <abc-button
                        v-if="!canEdit"
                        size="small"
                        :width="44"
                        variant="ghost"
                        @click="canEdit = true"
                    >
                        编辑
                    </abc-button>
                    <template v-else>
                        <abc-button
                            size="small"
                            :disabled="!isModify"
                            :loading="saveLoading"
                            :width="44"
                            @click="saveCrm"
                        >
                            保存
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            size="small"
                            :width="44"
                            @click="cancelCrm"
                        >
                            取消
                        </abc-button>
                    </template>
                </abc-space>
            </abc-flex>
            <!-- 基础信息 -->
            <abc-form
                v-if="canEdit"
                ref="patientForm"
                label-position="left"
                class="biz-patient-card-form"
                item-no-margin
                style="margin-top: 8px;"
                :label-width="52"
            >
                <abc-flex class="header-info">
                    <abc-space :size="4">
                        <abc-form-item label="" :custom-label-style="customLabelStyle" required>
                            <abc-input
                                v-model.trim="editingPatient.name"
                                class="patient-name"
                                :width="87"
                                :max-length="40"
                                :distinguish-half-angle-length="true"
                                trim
                                size="tiny"
                                type="text"
                                placeholder="新患者"
                                @enter="saveCrm"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item :custom-label-style="customLabelStyle">
                            <abc-select
                                v-model="editingPatient.sex"
                                :width="42"
                                custom-class="sex-select"
                                size="tiny"
                                @enter="saveCrm"
                            >
                                <abc-option value="男" label="男"></abc-option>
                                <abc-option value="女" label="女"></abc-option>
                            </abc-select>
                        </abc-form-item>
                        <abc-space is-compact compact-block :border-style="false">
                            <abc-form-item
                                label=""
                                show-red-dot
                                :required="!secondInputAge && secondInputAge !== 0"
                                :validate-event="firstUnit === '月' ? validateMonth : ()=>{}"
                            >
                                <abc-input
                                    v-model.number="firstInputAge"
                                    v-abc-focus-selected
                                    :width="29"
                                    :input-custom-style="{
                                        padding: '6px 0 6px 6px !important', 'text-align': 'center'
                                    }"
                                    type="number"
                                    size="tiny"
                                    :config="{
                                        supportZero: true, max: firstMax
                                    }"
                                    @change="changeAge"
                                >
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item>
                                <abc-select
                                    v-model="firstUnit"
                                    :width="24"
                                    size="tiny"
                                    custom-class="unit-select"
                                    no-icon
                                    class="unit-select"
                                    @change="handleUnit"
                                >
                                    <abc-option
                                        v-for="it in firstUnitArray"
                                        :key="it.id"
                                        :style="{ 'padding': '3px' }"
                                        :label="it.name"
                                        :value="it.name"
                                    >
                                    </abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-form-item
                                :required="!firstInputAge && firstInputAge !== 0"
                                :validate-event="firstUnit === '岁' ? validateMonth : validateDay"
                            >
                                <abc-input
                                    v-model.number="secondInputAge"
                                    v-abc-focus-selected
                                    :width="52"
                                    size="tiny"
                                    class="age-input__wrapper"
                                    type="number"
                                    :config="{
                                        supportZero: true, max: secondMax
                                    }"
                                    @change="changeAge"
                                >
                                    <span slot="appendInner">{{ secondUnit }}</span>
                                </abc-input>
                            </abc-form-item>
                        </abc-space>
                        <abc-form-item
                            label=""
                            :validate-event="handleMobileValidate"
                            :required="requireConfig.mobile.required"
                        >
                            <input-mobile-encrypt
                                v-model.trim="editingPatient.mobile"
                                :country-code.sync="editingPatient.countryCode"
                                auto-width
                                size="tiny"
                                placeholder="手机号"
                                :enable-encrypt="!isCanSeePatientMobile"
                            >
                            </input-mobile-encrypt>
                        </abc-form-item>
                    </abc-space>
                </abc-flex>
                <abc-divider
                    size="normal"
                    theme="light"
                    variant="dashed"
                ></abc-divider>
                <abc-form-item-group
                    grid
                    grid-gap="8px 16px"
                    :grid-column-count="2"
                >
                    <abc-form-item
                        label="证件"
                        :custom-label-style="customLabelStyle"
                        grid-column="span 2"
                        :validate-event="_validateIdCard"
                        :required="requireConfig.certificates.required"
                    >
                        <abc-input-style :adaptive-width="false">
                            <abc-certificates-type
                                ref="crm-id-card"
                                v-model.trim="editingPatient.idCard"
                                size="tiny"
                                :cert-type-width="78"
                                :width="0"
                                :max-length="18"
                                :cert-type-adaptive-width="true"
                                :max-slice-len="7"
                                :auto-width="true"
                                :cert-type.sync="editingPatient.idCardType"
                            ></abc-certificates-type>
                        </abc-input-style>
                    </abc-form-item>
                    <abc-form-item label="生日" :custom-label-style="customLabelStyle" :required="requireConfig.birthday.required">
                        <birthday-picker
                            v-model="editingPatient.birthday"
                            size="tiny"
                            value-format="yyyy-MM-dd"
                            @change="changeBirthday"
                        ></birthday-picker>
                    </abc-form-item>
                    <abc-form-item label="档案号" :custom-label-style="customLabelStyle" :required="requireConfig.sn.required">
                        <abc-input
                            v-model="editingPatient.sn"
                            size="tiny"
                            :max-length="18"
                            @enter="saveCrm"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item label="来源" :custom-label-style="customLabelStyle" :required="requireConfig.sourceInfo.required">
                        <abc-popover
                            v-model="showRecommendation"
                            placement="top"
                            trigger="manual"
                            theme="yellow"
                            :disabled="disabledCascaderPopover"
                        >
                            <abc-cascader
                                slot="reference"
                                ref="visit-source-cascader"
                                :value="currentCascaderValue"
                                :props="{
                                    children: 'children',
                                    label: 'name',
                                    value: 'id'
                                }"
                                separation="-"
                                :clearable="true"
                                :options="sourceList"
                                placeholder=""
                                :disabled="disabledCascader"
                                @change="$emit('handleCascaderValueChange')"
                                @panel-visible="$emit('handleCascaderVisible')"
                                @reference-mouse-enter="$emit('handleCascaderMouseEnter')"
                                @reference-mouse-leave="$emit('handleCascaderMouseLeave')"
                            >
                                <div class="visit-source-edit-wrapper">
                                    <abc-icon
                                        v-if="isClinicAdmin"
                                        icon="set"
                                        size="14"
                                        color="#94979B"
                                        class="icon"
                                        @click="handleVisitSourceEdit"
                                    ></abc-icon>

                                    <abc-popover
                                        v-else
                                        trigger="hover"
                                        placement="top-start"
                                        :popper-style="{
                                            zIndex: 99999
                                        }"
                                        theme="yellow"
                                    >
                                        <abc-icon
                                            slot="reference"
                                            icon="set"
                                            size="14"
                                            color="#94979B"
                                        ></abc-icon>

                                        <span>修改本次推荐请联系管理员</span>
                                    </abc-popover>
                                </div>
                            </abc-cascader>
                            <div v-if="isAddPatient">
                                新客消费后，活动奖励将发放给此推荐人
                            </div>
                            <div v-else-if="disabledCascader">
                                {{ disabledModifyFirstSourceText }}
                            </div>
                        </abc-popover>
                    </abc-form-item>
                    <abc-form-item label="民族" :custom-label-style="customLabelStyle" :required="requireConfig.ethnicity.required">
                        <abc-select
                            v-model="editingPatient.ethnicity"
                            size="tiny"
                            :max-length="10"
                            :max-height="234"
                            support-inverse
                        >
                            <abc-option
                                v-for="item in nationOptions"
                                :key="item"
                                :value="item"
                                :label="item"
                                :panel-max-height="200"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item label="婚姻" :custom-label-style="customLabelStyle" :required="requireConfig.marital.required">
                        <abc-select
                            v-model="editingPatient.marital"
                            custom-class="profession-options"
                            :max-height="234"
                            size="tiny"
                        >
                            <abc-option
                                v-for="item in maritalOptions"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                                :panel-max-height="200"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item label="体重" :custom-label-style="customLabelStyle" :required="requireConfig.weight.required">
                        <abc-input
                            v-model="editingPatient.weight"
                            size="tiny"
                            :max-length="3"
                        >
                            <span slot="appendInner">kg</span>
                        </abc-input>
                    </abc-form-item>
                    <abc-form-item label="职业" :custom-label-style="customLabelStyle" :required="requireConfig.profession.required">
                        <abc-input
                            v-model="editingPatient.profession"
                            size="tiny"
                            :max-length="32"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item label="单位" :custom-label-style="customLabelStyle" :required="requireConfig.company.required">
                        <abc-input
                            v-model="editingPatient.company"
                            size="tiny"
                            :max-length="128"
                        ></abc-input>
                    </abc-form-item>
                    <abc-tooltip
                        placement="right"
                        content="公众号未授权"
                        :arrow-offset="10"
                        :disabled="!!isOpenMp"
                    >
                        <abc-form-item label="微信" :custom-label-style="customLabelStyle" @click.native="handleClickWX">
                            <abc-input
                                v-model="wxNickName"
                                size="tiny"
                                :max-length="10"
                                readonly
                                placeholder="未绑定"
                            >
                                <abc-icon
                                    slot="appendInner"
                                    icon="Code"
                                    color="#005ed9"
                                    size="12"
                                ></abc-icon>
                            </abc-input>
                        </abc-form-item>
                    </abc-tooltip>
                    <abc-form-item label="企微" :custom-label-style="customLabelStyle">
                        <abc-input
                            v-model="scrmInfo"
                            size="tiny"
                            :max-length="10"
                            readonly
                            placeholder="未绑定"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item label="家长名" :custom-label-style="customLabelStyle">
                        <abc-input
                            v-model="editingPatient.parentName"
                            size="tiny"
                            :max-length="20"
                            @enter="saveCrm"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item label="到店" :custom-label-style="customLabelStyle">
                        <abc-input
                            v-model="editingPatient.visitReason"
                            size="tiny"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item label="建档" :custom-label-style="customLabelStyle">
                        <abc-date-picker
                            v-model="editingPatient.created"
                            size="tiny"
                            :disabled="true"
                            value-format="yyyy-MM-dd"
                        ></abc-date-picker>
                    </abc-form-item>
                    <abc-form-item label="医保号" :custom-label-style="customLabelStyle">
                        <abc-input
                            v-model="shebaoCardInfo"
                            size="tiny"
                            :disabled="true"
                            @enter="saveCrm"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item
                        label="备注"
                        :custom-label-style="customLabelStyle"
                        grid-column="span 2"
                        :required="requireConfig.remark.required"
                    >
                        <abc-input
                            v-model="editingPatient.remark"
                            :max-length="300"
                            size="tiny"
                        ></abc-input>
                    </abc-form-item>
                    <abc-form-item
                        v-if="hasConsultant"
                        grid-column="span 2"
                        :required="requireConfig.consultantId.required"
                    >
                        <abc-select
                            v-model="editingPatient.consultantId"
                            :max-height="234"
                            size="tiny"
                            :inner-width="380"
                            with-search
                            clearable
                            :fetch-suggestions="handleConsultantSearch"
                        >
                            <abc-option
                                v-for="item in consultantOptions"
                                :key="item.employeeId"
                                :label="item.employeeName"
                                :value="item.employeeId"
                                :panel-max-height="200"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item label="地址" :required="requireConfig.address.required" grid-column="span 2">
                        <abc-address-selector
                            v-model="editingPatient.address"
                            size="tiny"
                        ></abc-address-selector>
                    </abc-form-item>
                    <abc-form-item
                        label=""
                        grid-column="span 2"
                        :required="requireConfig.address.required"
                        style="padding-left: 52px;"
                        :max-length="300"
                    >
                        <abc-input
                            v-model="editingPatient.addressDetail"
                            size="tiny"
                            placeholder="请输入详细地址"
                        ></abc-input>
                    </abc-form-item>
                </abc-form-item-group>
            </abc-form>
            <abc-descriptions
                v-else
                :column="2"
                size="small"
                :bordered="false"
                label-align="left"
                :label-width="40"
                :label-margin="12"
                content-padding="0"
            >
                <abc-descriptions-item :content-style="contentStyle" label="证件" :span="2">
                    <template v-if="patientInfo.idCard">
                        【{{ patientInfo.idCardType || '身份证' }}】{{ patientInfo.idCard }}
                    </template>
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="生日">
                    {{ patientInfo.birthday || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="档案号">
                    {{ patientInfo.sn || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="来源">
                    {{ patientInfo.patientSource && patientInfo.patientSource.name || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="民族">
                    {{ patientInfo.ethnicity || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="婚姻">
                    {{ patientInfo.marital || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="体重">
                    {{ patientInfo.weight ? `${patientInfo.weight}kg` : '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="职业">
                    {{ patientInfo.profession || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="单位">
                    {{ patientInfo.company || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="微信">
                    {{ isBindWX ? patientInfo.wxNickName || '已绑定' : '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="企微">
                    {{ getScrmInfo(patientInfo.scrmInfo) }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="家长名">
                    {{ patientInfo.parentName || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="到店">
                    {{ patientInfo.visitReason || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="建档">
                    {{ patientInfo.created ? handleParseTime(patientInfo.created, 'y-m-d', true) : '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="医保号">
                    {{ patientInfo.shebaoCardInfo && patientInfo.shebaoCardInfo.cardNo || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="备注" :span="2">
                    {{ patientInfo.remark || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item
                    v-if="hasConsultant"
                    :content-style="contentStyle"
                    label="咨询师"
                    :span="2"
                >
                    {{ patientInfo.consultantName || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="地址" :span="2">
                    {{ formatAddress(patientInfo.address) || '' }}
                </abc-descriptions-item>
            </abc-descriptions>

            <abc-divider
                size="normal"
                theme="light"
                variant="dashed"
                style="margin: 12px 0;"
            ></abc-divider>
            <abc-flex
                style="width: 100%; height: 26px; margin-bottom: 4px;"
                align="center"
                justify="space-between"
            >
                <abc-space :size="10" align="center">
                    <abc-text :bold="true" size="small">
                        医保信息
                    </abc-text>
                    <abc-text v-if="lastModified" theme="gray" size="mini">
                        更新于{{ lastModified }}
                    </abc-text>
                </abc-space>
                <patient-social
                    v-if="!!shebaoInfo"
                    v-model="visibleSocialInfo"
                    :abc-social-security="abcSocialSecurity"
                    style="display: inline-flex;"
                    :shebao-card-info="shebaoInfo"
                >
                    <abc-button
                        variant="ghost"
                        size="small"
                        :disabled="disabledShebaoBtn"
                        :width="44"
                        class="crm-shebao-info_box-title-btn"
                        @click="visibleSocialInfo = !visibleSocialInfo"
                    >
                        更多
                    </abc-button>
                </patient-social>
            </abc-flex>
            <!-- 医保信息 -->
            <abc-descriptions
                v-if="isOpenSocial"
                :column="2"
                size="small"
                :label-width="40"
                :label-margin="12"
                :bordered="false"
                label-align="left"
                content-padding="0"
            >
                <abc-descriptions-item :content-style="contentStyle" label="险种">
                    {{ shebaoInfo.extend && shebaoInfo.extend.insutypeWording || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="类别">
                    {{ shebaoInfo.feeType || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="单位">
                    {{ shebaoInfo.company || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="区划">
                    {{ shebaoInfo.extend && shebaoInfo.extend.insuplcAdmdvsWording || '' }}
                </abc-descriptions-item>
                <abc-descriptions-item :content-style="contentStyle" label="慢特病" :span="2">
                    {{ chronicDiseaseMaintainInfo }}
                </abc-descriptions-item>
                <abc-descriptions-item label="统筹" :span="2">
                    <abc-money v-if="shebaoInfo.extend && shebaoInfo.extend.annualFundPay" :value="shebaoInfo.extend.annualFundPay"></abc-money><abc-text v-if="annualFundPayText" size="small" theme="gray">
                        {{ annualFundPayText }}
                    </abc-text>
                </abc-descriptions-item>
            </abc-descriptions>

            <abc-divider
                size="normal"
                theme="light"
                variant="dashed"
                style="margin: 12px 0 0 0;"
            ></abc-divider>
        </div>
        <slot></slot>
        <!-- 底部功能入口列表 -->
        <abc-flex vertical :gap="4" style="padding: 8px 0;">
            <abc-flex
                v-if="hasMemberModule"
                :justify="'flex-start'"
                :align="'center'"
                :gap="'middle'"
                style="width: 100%; padding: 8px 6px;"
                class="patient-card__function-item"
                @click="!isMember ? handleFunctionClick('open-vip') : handleFunctionClick('handleMemberCharge')"
            >
                <abc-icon icon="s-vip-color" :size="20"></abc-icon>
                <abc-text v-if="!isMember">
                    办理会员
                </abc-text>
                <abc-text v-else>
                    {{ memberName }}
                </abc-text>
                <abc-flex style="margin-left: auto;" :gap="8" align="center">
                    <abc-text theme="gray">
                        {{ getMemberTotalPrice(memberInfo) }}
                    </abc-text>
                    <abc-icon icon="n-right-line-medium" color="#7A8794"></abc-icon>
                </abc-flex>
            </abc-flex>
            <abc-flex
                :justify="'flex-start'"
                :align="'center'"
                :gap="'middle'"
                style="width: 100%; padding: 8px 6px;"
                class="patient-card__function-item"
                @click="handleFunctionClick('handleExchangeChargePoint')"
            >
                <abc-icon icon="s-integral-color" :size="20"></abc-icon>
                <abc-text>积分</abc-text>
                <abc-flex style="margin-left: auto;" :gap="8" align="center">
                    <abc-text theme="gray">
                        {{ points }}
                    </abc-text>
                    <abc-icon icon="n-right-line-medium" color="#7A8794"></abc-icon>
                </abc-flex>
            </abc-flex>
            <abc-flex
                :justify="'flex-start'"
                :align="'center'"
                :gap="'middle'"
                style="width: 100%; padding: 8px 6px;"
                class="patient-card__function-item"
                @click="couponsList.length ? handleFunctionClick('handleCouponList') : handleFunctionClick('handleDistributeCoupon')"
            >
                <abc-icon icon="s-coupon-color" :size="20"></abc-icon>
                <abc-text>优惠券</abc-text>
                <abc-flex style="margin-left: auto;" :gap="8" align="center">
                    <abc-text v-if="couponsList.length" theme="gray" class="ellipsis">
                        {{ couponsListString }}
                    </abc-text>
                    <abc-text v-else theme="gray">
                        暂无优惠券
                    </abc-text>
                    <abc-icon icon="n-right-line-medium" color="#7A8794"></abc-icon>
                </abc-flex>
            </abc-flex>
            <abc-flex
                v-if="isOpenFamilyDoctor"
                :justify="'flex-start'"
                :align="'center'"
                :gap="'middle'"
                style="width: 100%; padding: 8px 6px;"
                class="patient-card__function-item"
                @click.native="openFamilyDoctor"
            >
                <abc-icon icon="s-doctor-2-color" :size="20"></abc-icon>
                <abc-text>家庭医生</abc-text>
                <abc-flex style="margin-left: auto;" :gap="8" align="center">
                    <abc-text v-if="servicePackage" theme="gray" class="ellipsis">
                        服务包：{{ servicePackage }}
                    </abc-text>
                    <abc-text v-else theme="gray">
                        还未签约家庭医生
                    </abc-text>
                    <abc-icon icon="n-right-line-medium" color="#7A8794"></abc-icon>
                </abc-flex>
            </abc-flex>
            <template v-if="isAllowUseChildHealth">
                <template v-if="bindHealthFiles.length">
                    <abc-flex
                        v-for="item in bindHealthFiles"
                        :key="item.title"
                        :justify="'flex-start'"
                        :align="'center'"
                        :gap="'middle'"
                        style="width: 100%; padding: 8px 6px;"
                        class="patient-card__function-item"
                        @click="handleFunctionClick('children-health')"
                    >
                        <abc-icon icon="s-child-color" :size="20"></abc-icon>
                        <abc-text>儿保记录</abc-text>
                        <abc-flex style="margin-left: auto;" :gap="8" align="center">
                            <abc-text theme="gray" class="ellipsis">
                                {{ childFileInfo }}
                            </abc-text>
                            <abc-icon icon="n-right-line-medium" color="#7A8794"></abc-icon>
                        </abc-flex>
                    </abc-flex>
                </template>
                <abc-flex
                    v-else
                    :justify="'flex-start'"
                    :align="'center'"
                    :gap="'middle'"
                    style="width: 100%; padding: 8px 6px;"
                    class="patient-card__function-item"
                    @click.native="openHealth"
                >
                    <abc-icon icon="s-child-color" :size="20"></abc-icon>
                    <abc-text>儿保记录</abc-text>
                    <abc-flex style="margin-left: auto;" :gap="8" align="center">
                        <abc-text theme="gray">
                            添加健康档案
                        </abc-text>
                        <abc-icon icon="n-right-line-medium" color="#7A8794"></abc-icon>
                    </abc-flex>
                </abc-flex>
            </template>
            <abc-flex
                v-for="(item, index) in cardList"
                :key="index"
                :justify="'flex-start'"
                :align="'center'"
                :gap="'middle'"
                style="width: 100%; padding: 8px 6px;"
                class="patient-card__function-item"
                @click.native="onClickCard(item)"
            >
                <abc-icon icon="s-card-color" :size="20"></abc-icon>
                <abc-text>{{ item.cardName }}</abc-text>
                <abc-flex style="margin-left: auto;" :gap="8" align="center">
                    <abc-text theme="gray">
                        {{ handleCardBalance(item) }}
                    </abc-text>
                    <abc-icon icon="n-right-line-medium" color="#7A8794"></abc-icon>
                </abc-flex>
            </abc-flex>
            <abc-flex
                v-if="hasMemberCardAdminModule"
                :justify="'flex-start'"
                :align="'center'"
                :gap="'middle'"
                style="width: 100%; padding: 8px 6px;"
                class="patient-card__function-item"
                @click="handleFunctionClick('new-card')"
            >
                <abc-icon icon="s-add-color" :size="20"></abc-icon>
                <abc-link>办理新卡</abc-link>
            </abc-flex>
        </abc-flex>
        <div
            v-if="showCover"
            class="base-card-cover"
            :style="{
                height: `${coverHeight + 2}px` ,top: !!coverTop && `${coverTop - 4}px`
            }"
        ></div>
        <div v-if="showCover" :class="['dialog-box',{ 'dialog-wx-box': visibleQrCode }]" :style="{ 'margin-top': `${coverTop}px` }">
            <dialog-bind-files
                v-if="visibleBindFiles"
                v-model="visibleBindFiles"
                :crm-a-p-i="crmAPI"
                :patient-info="patientInfo"
                @success="handleBindFilesSuccess"
            ></dialog-bind-files>
            <dialog-family-doctor
                v-if="visibleFamilyDoctor"
                v-model="visibleFamilyDoctor"
                :family-doctor="familyDoctor"
                :crm-a-p-i="crmAPI"
                :patient-info="patientInfo"
                @unbind-success="fetchPatientOverview"
                @success="fetchPatientOverview"
            ></dialog-family-doctor>
            <dialog-qr-code
                v-if="visibleQrCode"
                v-model="visibleQrCode"
                :crm-a-p-i="crmAPI"
                :patient="patientInfo"
                @input="visibleQrCode = false; showLiItem = false"
                @update-info="handleUpdateInfo"
            ></dialog-qr-code>
            <dialog-unbind-wx
                v-if="visibleUnbindWX"
                v-model="visibleUnbindWX"
                :crm-a-p-i="crmAPI"
                :patient-info="patientInfo"
                :patient-id="patientInfo.id"
                @input="visibleUnbindWX = false; showLiItem = false"
                @success="handleUpdatePostData"
            ></dialog-unbind-wx>
            <dialog-card-info
                v-if="visibleCardInfo"
                ref="card-info"
                :support-one-click-billing="supportOneClickBilling"
                :value="visibleCardInfo"
                :allow-recharge-card="allowRechargeCard"
                :card="card"
                :currency-symbol="currencySymbol"
                :patient-info="patientInfo"
                :card-list="cardList"
                :max-height="maxHeight"
                @handleReCharge="handleReCharge"
                @fetch-card-info="handleUpdateCardInfo"
            ></dialog-card-info>
        </div>
    </abc-card>
</template>

<script>
    import BizTagSelector from '@/components-composite/biz-tag-selector';
    import { formatDate } from '@tool/date';
    import PatientSocial from './package-social.vue';
    import InputMobileEncrypt from './input-mobile-encrypt.vue';
    import {
        validateMobile,
    } from '@/common/utils/validate';
    import { handleParseTime } from '@/common/utils.js';
    import {
        maritalOptions, nationOptions,
    } from '@/common/constants/option';
    import {
        isEqual, clone, formatMoney,
    } from '@abc/utils';
    import BirthdayPicker from './birthday-picker.vue';
    import { isNumeric } from '@abc/utils';
    export default {
        name: 'BizPatientCard',
        components: {
            InputMobileEncrypt,
            PatientSocial,
            BizTagSelector,
            BirthdayPicker,
            DialogBindFiles: () => import('./dialog-bind-files.vue'),
            // eslint-disable-next-line vue/no-unused-components
            DialogUnbindWx: () => import('./dialog-unbind-wx.vue'),
            // eslint-disable-next-line vue/no-unused-components
            DialogQrCode: () => import('./dialog-qr-code.vue'),
            DialogFamilyDoctor: () => import('./dialog-family-doctor.vue'),
            DialogCardInfo: () => import('./dialog-card-info.vue'),
        },
        props: {
            abcSocialSecurity: {
                type: Object,
                default: null,
            },
            // 是否支持一键开出卡项剩余项目功能
            supportOneClickBilling: {
                type: Boolean,
                default: false,
            },
            familyDoctor: {
                type: Object,
                default: null,
            },
            isOpenFamilyDoctor: {
                type: Boolean,
                default: true,
            },
            // 开通医保
            isOpenSocial: {
                type: Boolean,
                default: true,
            },
            allowRechargeCard: {
                type: Boolean,
                default: true,
            },
            patientId: {
                type: [String, Number],
                default: '',
            },
            coverTop: {
                type: Number,
                default: 0,
            },
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
            consultantList: {
                type: Array,
                default: () => [],
            },
            isAddPatient: {
                type: Boolean,
                default: false,
            },
            hasConsultant: {
                type: Boolean,
                default: false,
            },
            userInfo: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            showRecommendation: {
                type: Boolean,
                default: false,
            },
            crmConfigList: {
                type: Array,
                default: () => [],
            },
            originLabels: {
                type: Array,
                default: () => [],
            },
            viewDistributeConfig: {
                type: Object,
                default: () => ({}),
            },
            isSingleStore: {
                type: Boolean,
                default: false,
            },
            currentClinic: {
                type: Object,
                default: () => ({}),
            },
            crmAPI: {
                type: Function,
                default: null,
            },
            currencySymbol: {
                type: String,
                default: '¥',
            },
            needPatientBaseComponentShowTags: {
                type: Boolean,
                default: false,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: false,
            },
            // 就诊来源列表
            sourceList: {
                type: Array,
                default: () => [],
            },
            cascaderValue: {
                type: Array,
                default: () => [],
            },
            // store
            disabledModifyFirstSourceText: {
                type: String,
                default: '',
            },
            isEnableChildHealth: {
                type: Number,
                default: 0,
            },
            hasMemberCardAdminModule: {
                type: Boolean,
                default: false,
            },
            hasMemberModule: {
                type: Boolean,
                default: false,
            },
            hasChargeModule: {
                type: Boolean,
                default: false,
            },
            isOpenMp: {
                type: Boolean,
                default: false,
            },
            cardWidth: {
                type: [Number, String],
                default: 460,
            },
            cardHeight: {
                type: [Number, String],
                default: 778,
            },
        },
        data() {
            return {
                maritalOptions,
                nationOptions,
                showLiItem: false,
                visibleQrCode: false,
                visibleBindFiles: false,
                visibleUnbindWX: false,
                visibleCardInfo: false,
                maxHeight: 0,
                visibleFamilyDoctor: false,
                card: null,
                currentCardItem: null,
                params: {
                    wx: 1,
                    childCareRecords: 1,
                    promotionCardList: 1,
                    showFamilyDoctor: 1,
                },
                // 统计中拉取的患者服务信息
                patientBaseInfo: {
                    outpatientCount: '',
                    payCount: '',
                    cumulativeAmount: '',
                    owePayedAmount: '',
                },
                firstUnitArray: [{
                    id: 1,
                    name: '岁',
                }, {
                    id: 2,
                    name: '月',
                }],
                visibleSocialInfo: false,
                currentTab: 'basic',
                tabOptions: [
                    {
                        label: '基础信息',
                        value: 'basic',
                    },
                    {
                        label: '就诊记录',
                        value: 'record',
                    },
                ],
                showTagPopover: false,
                tags: ['高血压', '糖尿病'],
                canEdit: false,
                editingPatient: {
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    birthday: '',
                },
                cacheEditingPatient: {},
                secondMax: 11,
                firstInputAge: '',
                firstUnit: '岁',
                secondInputAge: '',
                secondUnit: '月',
                firstMax: 199,
                consultantSearchKey: '',
                couponsList: [],
                couponsArr: [],
                saveLoading: false,
                coverHeight: 0,
            };
        },
        computed: {
            customLabelStyle() {
                return {
                    fontSize: '13px',
                };
            },
            computedStyles() {
                return {
                    position: 'relative',
                    height: isNumeric(this.cardHeight) ? `${this.cardHeight}px` : this.cardHeight,
                    width: isNumeric(this.cardWidth) ? `${this.cardWidth}px` : this.cardWidth,
                };
            },
            showCover() {
                return this.visibleQrCode || this.visibleUnbindWX || this.visibleBindFiles || this. visibleFamilyDoctor || this.visibleCardInfo;
            },
            isClinicAdmin() {
                return this.userInfo?.roleId === 1;
            },
            isMember() {
                return this.patientInfo?.isMember;
            },
            memberInfo() {
                return this.patientInfo?.memberInfo || {};
            },
            memberName() {
                return this.memberInfo.memberTypeInfo?.memberTypeName || '普通患者';
            },
            childFileInfo() {
                const {
                    childCareArchives,
                    childCareRecords,
                } = this.patientInfo || {};
                if (childCareArchives === 1) {
                    // 已关联儿童健康档案
                    if (childCareRecords?.bodyGrowthData) {
                        // 生长记录
                        const {
                            height,
                            weight,
                        } = childCareRecords.bodyGrowthData;
                        return `身高 ${height || 0}cm，体重 ${weight || 0}kg`;
                    }
                }
                return '暂无记录';
            },
            // 已经绑定的健康档案
            bindHealthFiles() {
                const healthFiles = [];
                const {
                    childCareArchives,
                    childCareRecords,
                } = this.patientInfo || {};
                if (childCareArchives === 1) {
                    // 已关联儿童健康档案
                    let growthCont = '无';
                    if (childCareRecords && childCareRecords.bodyGrowthData) {
                        // 生长记录
                        const {
                            bmi,
                            headSize,
                            height,
                            isBregmaClose,
                            lowerBodyLength,
                            teeth,
                            upperBodyLength,
                            weight,
                        } = childCareRecords.bodyGrowthData;
                        const strs = [];
                        height && strs.push(`身高${height}cm`);
                        weight && strs.push(`体重${weight}kg`);
                        bmi && strs.push(`BMI ${bmi}`);
                        headSize && strs.push(`头围${headSize}cm`);
                        upperBodyLength && strs.push(`上部量${upperBodyLength}cm`);
                        lowerBodyLength && strs.push(`下部量${lowerBodyLength}cm`);
                        isBregmaClose && strs.push(`前囟${isBregmaClose === 1 ? '开启' : '闭合'}`);
                        teeth && strs.push(`牙齿${teeth}颗`);

                        growthCont = strs.join('/');
                    }
                    let evaluationCont = '发育评测：无';
                    if (childCareRecords && childCareRecords.evaluationData) {
                        const {
                            evaluationName,
                            result,
                        } = childCareRecords.evaluationData;
                        const scores = result.map(
                            (item) => `${parseFloat(item.score)}分/${parseFloat(item.totalScore)}分`,
                        );
                        evaluationCont = `${evaluationName}：${scores.join(' ')}`;
                    }
                    const item = {
                        type: 'child',
                        title: '儿童健康档案',
                        contents: [`生长记录：${growthCont}`, evaluationCont],
                    };
                    healthFiles.push(item);
                }
                return healthFiles;
            },
            isAllowUseChildHealth() {
                return this.isEnableChildHealth === 1 && this.patientInfo?.age?.year < 18;
            },
            servicePackage() {
                return this.patientInfo?.familyDoctorAbstract?.servicePackName;
            },
            couponsListString() {
                const arr = this.groupBy(this.couponsList, (item) => item.name);
                return arr.map((item) => {
                    return `${item[0]?.name || ''}(${item.length})`;
                }).join('、');
            },
            points() {
                return this.patientInfo?.points || 0;
            },
            isBindWX() {
                return this.patientInfo?.wxBindStatus >= 2;
            },
            wxNickName() {
                return this.isBindWX ? this.editingPatient.wxNickName || '已绑定' : '';
            },
            shebaoCardInfo() {
                return this.editingPatient.shebaoCardInfo || '';
            },
            scrmInfo() {
                return this.getScrmInfo(this.editingPatient.scrmInfo);
            },
            isModify() {
                const postData = clone(this.editingPatient);
                const oldPostData = clone(this.cacheEditingPatient);
                delete postData?.wxNickName;
                delete postData?.wxBindStatus;
                delete oldPostData?.wxNickName;
                delete oldPostData?.wxBindStatus;
                delete postData?.address?.addressPostcode;
                delete oldPostData?.address?.addressPostcode;
                delete postData?.address?.fullAddress;
                delete oldPostData?.address?.fullAddress;
                if (postData?.address) {
                    postData.address = this.convertEmptyStringToNull(postData?.address);
                }
                if (oldPostData?.address) {
                    oldPostData.address = this.convertEmptyStringToNull(oldPostData?.address);
                }
                if (['',null].includes(postData?.addressDetail)) {
                    postData.addressDetail = null;
                }
                if (['',null].includes(oldPostData?.addressDetail)) {
                    oldPostData.addressDetail = null;
                }
                return !isEqual(postData, oldPostData);
            },
            currentCascaderValue: {
                get() {
                    return this.cascaderValue;
                },
                set(val) {
                    this.$emit('update:cascaderValue', val);
                },
            },
            disabledCascader() {
                return this.disabledModifyFirstSource && !this.isAddPatient;
            },
            disabledModifyFirstSource() {
                return !!this.disabledModifyFirstSourceText;
            },
            disabledCascaderPopover() {
                if (this.isAddPatient) {
                    return !this.isExistPromotionActivity;
                }
                return !this.disabledModifyFirstSource;
            },
            consultantOptions() {
                if (!this.consultantSearchKey) {
                    return this.consultantList;
                }
                return this.consultantList.filter((item) => {
                    return (
                        (item.employeeName && item.employeeName.indexOf(this.consultantSearchKey) > -1) ||
                        (item.employeeNamePy && item.employeeNamePy.toLocaleLowerCase().indexOf(this.consultantSearchKey) > -1) ||
                        (item.employeeNamePyFirst && item.employeeNamePyFirst.toLocaleLowerCase().indexOf(this.consultantSearchKey) > -1)
                    );
                });
            },
            requireConfig() {
                const model = {
                    consultantId: {
                        required: false,
                    },
                    mobile: {
                        required: true,
                    },
                    idCard: {
                        required: true,
                    },
                    profession: {
                        required: false,
                    },
                    birthday: {
                        required: false,
                    },
                    address: {
                        required: false,
                    },
                    ethnicity: {
                        required: false,
                    },
                    weight: {
                        required: false,
                    },
                    remark: {
                        required: false,
                    },
                    visitReason: {
                        required: false,
                    },
                    marital: {
                        required: false,
                    },
                    sourceInfo: {
                        required: false,
                    },
                    certificates: {
                        required: false,
                    },
                    company: {
                        required: false,
                    },
                    sn: {
                        required: false,
                    },
                    pastHistory: {
                        required: false,
                    },
                };
                for (const key in model) {
                    if (model.hasOwnProperty(key)) {
                        model[key].required = !!this.crmConfigList?.[key]?.required || false; // 修改 required 的值
                    }
                }
                return model;
            },
            contentStyle() {
                return {
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    wordBreak: 'keep-all',
                    whiteSpace: 'nowrap',
                };
            },
            disabledShebaoBtn() {
                return !this.shebaoInfo?.name;
            },
            memoryList() {
                return [
                    {
                        name: '黄金会员',
                    },
                ];
            },
            cardList() {
                return [
                    {
                        cardName: '测试卡',
                        cardBalance: '12.00',
                    },
                ];
            },
            chronicDiseaseMaintainInfo() {
                return this.shebaoInfo.extend?.chronicDiseaseMaintainInfo?.map((item) => {return item.label;})?.join(',') || '';
            },
            lastModified() {
                if (this.shebaoInfo.lastModified) {
                    return formatDate(this.shebaoInfo.lastModified, 'YYYY-MM-DD HH:mm');
                }
                return '';
            },
            annualFundPayText() {
                if (this.shebaoInfo.extend?.annualFundPay && this.shebaoInfo.lastModified) {
                    return `（${formatDate(this.shebaoInfo.lastModified, 'YYYY')}年度本机构累计结算）`;
                }
                return '';
            },
            patientInfoMobile() {
                const { mobile = '' } = this.patientInfo;
                return this.isCanSeePatientMobile ? mobile : this.encryptMobile(mobile);
            },
            // 是否显示会员标识
            displayMember() {
                return this.patientInfo.isMember;
            },
            // 是否显示欠费标识
            displayCharge() {
                return this.patientInfo.owePayedAmount > 0;
            },
            // 是否显示微信标识
            displayWechat() {
                return this.patientInfo.hasBindWechat;
            },
            // 是否显示企微标识
            displayQiWei() {
                return this.patientInfo.hasBindQiWei;
            },
            // 是否显示欠费金额
            isNeedDisCharge() {
                return this.patientBaseInfo?.owePayedAmount && this.hasChargeModule;
            },
            // 医保信息
            shebaoInfo() {
                return this.patientInfo?.shebaoInfo || {};
            },
        },
        watch: {
            patientInfo: {
                handler(val) {
                    if (val) {
                        const {
                            year, month, day,
                        } = this.patientInfo?.age || {};
                        if (year || month || day) {
                            this.handleUnionAge(year, month, day);
                        }
                        this.editingPatient = {
                            ...val,
                        };
                        this.cacheEditingPatient = clone(this.editingPatient);
                    }
                },
                deep: true,
                immediate: true,
            },
            firstUnit(val) {
                if (val === '岁') {
                    this.firstMax = 199;
                    this.secondMax = 11;
                } else {
                    this.firstMax = 11;
                    this.secondMax = 30;
                }
            },
        },
        created() {
            this.init();
        },
        methods: {
            handleParseTime,
            handleReCharge() {
                this.$emit('handleReCharge', this.card, this.cardList);
            },
            init() {
                this.fetchPatientCardsInfo();
                this.fetchObtainedCoupons();
            },
            async handleUpdateCardInfo() {
                const patientId = this.patientInfo?.id || this.patientId;
                await Promise.all([
                    this.fetchPatientCardDetail(this.currentCardItem),
                    this.fetchPatientCardsInfo(patientId),
                ]);
            },
            async onClickCard(item) {
                await this.fetchPatientCardDetail(item);
                this.handleBasic();
                this.visibleCardInfo = true;
            },
            async fetchPatientCardDetail(item) {
                this.currentCardItem = item;
                if (!item?.id) return;
                try {
                    const { data } = await this.crmAPI.fetchPatientCardDetail(item.id);
                    const {
                        cardInfo,
                        patientPresents,
                        cardBalance,
                        isOverdue = false,
                    } = data;

                    this.card = {
                        ...cardInfo,
                        id: item.id,
                    };

                    this.card.patientPresents = patientPresents;
                    this.card.cardBalance = cardBalance || 0;
                    this.card.isOverdue = isOverdue;
                } catch (e) {
                    console.log(e);
                }
            },
            openHealth() {
                this.handleBasic();
                this.visibleBindFiles = true;
            },
            openFamilyDoctor() {
                this.handleBasic();
                this.visibleFamilyDoctor = true;
            },
            handleBasic() {
                this.showLiItem = true;
                const {
                    clientHeight,
                    offsetHeight,
                    scrollTop,
                } = this.$el;
                this.maxHeight = clientHeight;
                this.coverHeight = offsetHeight;
                this.coverTop = scrollTop;
            },
            async handleClickWX() {
                if (!this.isOpenMp) {
                    return;
                }
                if (this.isAddPatient && !this.editingPatient?.id) {
                    try {
                        const { data } = await this.crmAPI.fetchMatchedPatients({
                            name: this.editingPatient.name,
                            mobile: this.editingPatient.mobile,
                            idCard: this.editingPatient.idCard,
                        });
                        if (data?.rows?.length) {
                            let msg = '';
                            if (this.editingPatient.name && this.editingPatient.mobile) {
                                msg = '该患者信息（姓名和手机号或）已经被注册';
                            }
                            if (this.editingPatient.idCard) {
                                msg = '该患者信息（身份证）已经被注册';
                            }
                            this.$Toast({
                                message: msg,
                                type: 'error',
                            });
                            return;
                        }
                    } catch (e) {
                        console.error('fetchMatchedPatients error', e);
                    }
                }
                !this.isBindWX ? this.handleShowQrCode() : this.handleVisible();
            },
            handleVisible() {
                this.handleBasic();
                this.$nextTick(() => {
                    this.visibleUnbindWX = true;
                });
            },
            handleShowQrCode() {
                if (!this.editingPatient.name.trim()) {
                    this.$Toast({
                        message: '患者姓名不能为空',
                        type: 'error',
                    });
                    return false;
                }
                this.handleBasic();
                this.$nextTick(() => {
                    this.visibleQrCode = true;
                });
            },
            handleVisitSourceEdit() {
                this.$refs['visit-source-cascader'][0].outside();
                this.$emit('handleVisitSourceEdit');
            },
            handleBindFilesSuccess() {
                this.visibleBindFiles = false;
                this.fetchPatientOverview();
            },
            async fetchPatientOverview() {
                try {
                    const patientId = this.patientInfo?.id || this.patientId;
                    if (patientId) {
                        const { data } = await this.crmAPI.fetchPatientOverview(patientId, this.params);
                        this.$emit('update-patient-info', Object.assign({}, this.patientInfo, data));
                    }
                } catch (error) {
                    console.log('fetchPatientOverview error', error);
                }
            },
            async handleUpdateInfo(val) {
                if (this.isAddPatient) {
                    this.editingPatient.id = val.id;
                    this.editingPatient.wxNickName = val.wxBindStatus >= 2 ? val.wxNickName || '已绑定' : '';
                } else {
                    const { data } = await this.crmAPI.fetchPatientOverview(this.patientInfo.id, this.params);
                    this.editingPatient.wxNickName = this.editingPatient.wxNickName = data.wxNickName;
                    this.editingPatient.wxBindStatus = data.wxBindStatus;
                    this.editingPatient.wxNickName = data.wxNickName;
                }
                this.editingPatient.wxBindStatus = val.wxBindStatus;
                this.editingPatient.name = val.name;
                this.editingPatient.sex = val.sex;
                this.editingPatient.mobile = val.mobile;
                // 若新建没填sn，则扫码会自动生成，覆盖，用户可继续修改
                this.editingPatient.sn = this.editingPatient.sn ? this.editingPatient.sn : val.sn;
                this.editingPatient.birthday = val.birthday;
                this.changeBirthday(this.editingPatient.birthday);
            },
            async handleUpdatePostData() {
                this.visibleUnbindWX = false;
                if (this.isAddPatient) {
                    this.editingPatient.wxBindStatus = 0;
                    this.editingPatient.wxNickName = '';
                } else {
                    const { data } = await this.crmAPI.fetchPatientOverview(this.patientInfo.id, this.params);
                    this.editingPatient.wxBindStatus = this.editingPatient.wxBindStatus = data.wxBindStatus;
                    this.editingPatient.wxNickName = data.wxNickName;
                }
                this.$nextTick(() => {
                    this.visibleQrCode = true;
                });
            },
            async confirm() {
                this.saveLoading = true;
                try {
                    let params = clone(this.editingPatient);
                    params = {
                        ...params,
                        ...params.address,
                        addressDetail: params.addressDetail,
                    };
                    delete params.address;

                    if (this.patientInfo?.id) {
                        await this.crmAPI.updatePatientInfo(this.patientInfo.id, params);
                        this.editor = false;
                        this.$Toast({
                            message: '修改成功',
                            type: 'success',
                        });
                        this.$emit('change-patient', this.patientInfo);
                        this.$emit('update-patient-info', this.editingPatient);
                        this.cacheEditingPatient = clone(this.editingPatient);
                        this.canEdit = false;
                    } else {
                        const { data } = this.patientInfo?.id ? await this.crmAPI.updatePatientInfo(this.patientInfo.id,params) : await this.crmAPI.insertPatientInfo(params);
                        this.$Toast({
                            message: '添加成功',
                            type: 'success',
                        });
                        this.$emit('add-patient', data);
                        this.$emit('update-patient-info', this.editingPatient);
                        this.cacheEditingPatient = clone(this.editingPatient);
                        this.canEdit = false;
                    }

                } catch (error) {
                    console.log('onClickSave error', error);
                    if (error?.code === 10409 || error?.code === 409) {
                        // 存在已经被共享的会员
                        this.$Toast({
                            message: error.message,
                            type: 'error',
                        });
                    } else if (error?.code === 13993) {
                        // 身份证被注册
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: error.message,
                        });
                    } else if (error?.code === 13992) {
                        // 该患者信息（姓名和手机号）已经被注册
                        this.$Toast({
                            message: error.message,
                            type: 'error',
                        });
                    }
                }
                this.saveLoading = false;
            },
            async fetchPatientBaseInfo() {
                if (this.crmAPI) {
                    try {
                        const patientId = this.patientInfo?.id || this.patientId;
                        const { data: res } = await this.crmAPI.fetchPatientBaseInfo(patientId);
                        const {
                            outpatientCount = 0,
                            cumulativeAmount = 0,
                            payCount = 0,
                            owePayedAmount = 0,
                        } = res && res.rows[0] || {};
                        this.patientBaseInfo.outpatientCount = outpatientCount;
                        this.patientBaseInfo.payCount = payCount;
                        this.patientBaseInfo.cumulativeAmount = cumulativeAmount;
                        this.patientBaseInfo.owePayedAmount = owePayedAmount;
                    } catch (e) {
                        console.log(e);
                    }
                }
            },
            convertEmptyStringToNull(obj) {
                for (const key in obj) {
                    if (typeof obj[key] === 'object' && obj[key] !== null) {
                        obj[key] = this.convertEmptyStringToNull(obj[key]); // 递归调用处理嵌套对象
                    } else if (obj[key] === '') { // 只处理需要对比的值 接口可能会返回null也可能返回''
                        obj[key] = null;
                    }
                }
                return obj;
            },
            _validateIdCard(values, callback) {
                const [certType = '', certNo = ''] = values;
                if (!this.requireConfig?.certificates?.required && !certNo) {
                    return callback({ validate: true });
                }
                if (!certNo) {
                    return callback({ validate: false });
                }
                return this.$refs?.['crm-id-card'].validateCertNo(certType, certNo, callback);
            },
            handleCardBalance(item) {
                if (!item.cardBalance) {
                    return `余额 ${this.currencySymbol}0.00`;
                }
                return `余额 ${this.currencySymbol}${Number(item.cardBalance).toFixed(2)}`;
            },
            getMemberTotalPrice(memberInfo) {
                return `余 ${this.currencySymbol}${Number(memberInfo.principal + memberInfo.present).toFixed(2)}`;
            },
            changeBirthday(birthday) {
                if (birthday) {
                    const {
                        year,
                        month,
                        day,
                    } = this.birthday2age(birthday);
                    this.handleUnionAge(year, month, day);
                } else {
                    // 清空操作
                    this.editingPatient.age.year = 0;
                    this.editingPatient.age.month = 0;
                    this.editingPatient.age.day = 0;
                }
            },
            birthday2age(birthday, today) {
                const birth = new Date(`${birthday} 00:00:00`);
                const now = today ? new Date(today) : new Date();

                const birthYear = birth.getFullYear();
                const birthMonth = birth.getMonth() + 1;
                const birthDay = birth.getDate();

                let nowYear = now.getFullYear();
                let nowMonth = now.getMonth() + 1;
                const nowDay = now.getDate();

                let day = nowDay - birthDay;

                if (day < 0) {
                    // 当前月天数小于出生天数，最终的天数 = 出生天数到出生月底的天数 + 当前月天数
                    const tmp = new Date(birthYear, birthMonth, 0);
                    nowMonth -= 1;
                    day += tmp.getDate();
                }

                if (nowMonth < birthMonth) {
                    nowYear -= 1;
                    nowMonth += 12;
                }
                const month = nowMonth - birthMonth;
                const year = nowYear - birthYear;

                return {
                    year, month, day,
                };
            },
            handleUnionAge(year, month, day, postData = this.patientInfo) {
                if (year) {
                    this.firstInputAge = postData.age.year = year || '';
                    this.secondInputAge = postData.age.month = month || '';
                    postData.age.day = day;
                    this.firstUnit = '岁';
                    this.secondUnit = '月';
                } else {
                    postData.age.year = '';
                    this.firstInputAge = postData.age.month = month || '';
                    if (!month) {
                        this.secondInputAge = postData.age.day = day || 0;
                    } else {
                        this.secondInputAge = postData.age.day = day || '';
                    }
                    this.firstUnit = '月';
                    this.secondUnit = '天';
                }
            },
            async fetchPatientCardsInfo() {
                if (this.crmAPI) {
                    try {
                        const patientId = this.patientInfo?.id || this.patientId;
                        const { data } = await this.crmAPI?.fetchPatientCardsInfo(patientId) || {};
                        this.cardList = data?.rows || [];
                    } catch (e) {
                        console.log(e);
                    }
                }
            },
            async fetchObtainedCoupons() {
                this.couponsArr = [];
                if (this.crmAPI) {
                    const {
                        id,
                        chainId,
                    } = this.patientInfo;
                    try {
                        if (!id) return;
                        const { data = {} } = await this.crmAPI.fetchObtainedCoupons(chainId, id, 0, 1000);
                        if (id !== this.patientInfo?.id) return;
                        this.couponsList = data?.rows || [];
                        const arr = this.groupBy(this.couponsList, (item) => item.name);
                        arr.forEach((item) => {
                            const obj = {
                                ...item[0], len: item.length,
                            };
                            this.couponsArr.push(obj);
                        });
                    } catch (e) {
                        console.log(e);
                    }
                }
            },
            groupBy(list, fn) {
                const groups = {};
                list.forEach((o) => {
                    const group = JSON.stringify(fn(o));
                    groups[group] = groups[group] || [];
                    groups[group].push(o);
                });
                return Object.keys(groups).map((group) => {
                    return groups[group];
                });
            },
            getScrmInfo(value) {
                return value?.customerCorpUserRelates?.map((item) => {
                    return item.employeeName || '未知员工';
                })?.join(',') || '';
            },
            handleConsultantSearch(key) {
                this.consultantSearchKey = key;
            },
            age2birthday(age) {
                let {
                    year, month, day,
                } = age;
                year = year || 0;
                month = Math.min(11, month || 0);
                day = Math.min(30, day || 0);
                const time = new Date();
                let birthYear = time.getFullYear() - year;
                let birthMonth = time.getMonth() + 1 - month;
                let birthDay = time.getDate() - day;
                if (birthDay < 1) {
                    birthMonth -= 1;
                    const handleMonth = new Date(birthYear, birthMonth, 0).getDate();
                    birthDay += handleMonth;
                }
                if (birthMonth < 1) {
                    birthYear -= 1;
                    birthMonth += 12;
                }
                const birthday = new Date(birthYear, birthMonth - 1, birthDay);
                return handleParseTime(birthday, 'y-m-d', true);
            },
            handleUnit(value) {
                if (value === '岁') {
                    this.secondUnit = '月';
                    this.secondMax = 11;
                } else {
                    this.secondUnit = '天';
                    this.firstMax = 11;
                    this.secondMax = 30;
                }
                this.changeAge();
            },
            changeAge() {
                if (this.firstUnit === '岁') {
                    this.editingPatient.age.year = this.firstInputAge;
                    this.editingPatient.age.month = this.secondInputAge;
                } else {
                    this.editingPatient.age.year = '';
                    this.editingPatient.age.month = this.firstInputAge;
                    this.editingPatient.age.day = this.secondInputAge || 0;
                }
                const {
                    year,month,day,
                } = this.editingPatient.age;
                // day为0的校验要放开
                if (!year && !month && !day && day !== 0) return;
                this.$set(this.editingPatient, 'birthday', this.age2birthday(this.editingPatient.age));
            },
            validateMobile,
            validateMonth(value, callback) {
                if (!this.editingPatient.age.month) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-1])$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            validateDay(value, callback) {
                if (!this.editingPatient.age.day) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-9]|2[0-9]|30)$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多30天',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            handleMobileValidate(values, callback) {
                const [countryCode = '', mobile = ''] = values;
                if (!countryCode) {
                    return callback({
                        validate: false, message: '无法确认手机号所在地区，请联系ABC客服！',
                    });
                }

                // 若是加密手机号则使用对应的明文进行校验
                const mobileStr = this.isCanSeePatientMobile ? mobile : `${this.editingPatient.mobile || ''}` ;
                this.validateMobile(mobileStr, callback, this.editingPatient.countryCode);
            },
            cancelCrm() {
                // 取消编辑时重置为原始数据
                this.editingPatient = clone(this.cacheEditingPatient);
                this.canEdit = false;
            },
            saveCrm() {
                this.$refs.patientForm.validate((val) => {
                    if (val) {
                        // 保存编辑后的患者信息
                        if (this.crmAPI) {
                            this.confirm();
                        } else {
                            this.$emit('update-patient-info', this.editingPatient);
                            this.cacheEditingPatient = clone(this.editingPatient);
                            this.canEdit = false;
                        }
                    }
                });
            },
            encryptMobile(value) {
                if (!value) return '';
                let reg = '';
                let encryptMobile = '';

                if (value.length === 11) {
                    reg = /(\d{3})\d{4}(\d{4})/;
                    encryptMobile = `${value}`.replace(reg, '$1****$2');
                } else {
                    // 从第二位开始隐藏三位
                    encryptMobile = `${value[0]}***${value.substring(4)}`;
                }

                return encryptMobile;
            },
            handleFunctionClick(type) {
                // 统一处理点击事件，并向父组件传递事件类型
                this.$emit('function-click', type);
            },
            // 关闭标签
            handleCloseTag(tag) {
                this.$emit('close-tag', tag);
            },
            // 标签变更
            handleTagChange(tag) {
                this.$emit('change-tag', tag);
                this.showTagPopover = false;
            },
            // 点击标签选择框外部
            handleClickOutside() {
                this.showTagPopover = false;
            },
            // 格式化金额
            formatMoney,
            formatAddress(address) {
                if (!address) return '';

                const {
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                    addressDetail,
                } = address;

                return `${addressProvinceName || ''}${addressCityName ? ` / ${addressCityName}` : ''}${addressDistrictName ? ` / ${addressDistrictName}` : ''}${addressDetail ? ` ${addressDetail}` : ''}`;
            },
            isEmpty(obj) {
                return !obj || Object.keys(obj).length === 0;
            },
            formatChronicDisease(shebaoInfo) {
                if (!shebaoInfo || !shebaoInfo.extend || !shebaoInfo.extend.chronicDiseaseMaintainInfo) {
                    return '';
                }
                return shebaoInfo.extend.chronicDiseaseMaintainInfo
                    .map((item) => item.label)
                    .join('、');
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/abc-common.scss";

.biz-patient-card {
    position: relative;

    .biz-patient-card-form {
        .abc-form-item {
            .abc-form-item-label {
                .label-name {
                    font-size: 13px !important;
                }
            }
        }
    }

    .abc-form-item {
        .abc-form-item-label {
            .label-name {
                font-size: 13px !important;
            }
        }
    }

    &::-webkit-scrollbar {
        width: 10px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: var(--abc-color-p6);
        border-radius: 5px;
    }

    &::-webkit-scrollbar-track {
        background-color: transparent;
    }

    .ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: keep-all;
        white-space: nowrap;
    }

    .dialog-box {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 9999999;
        box-sizing: border-box;
        width: calc(100% - 24px);
        height: auto;
        background: #ffffff;
        border-radius: var(--abc-border-radius-small);
        transform: translate(-50%, -50%);

        &.dialog-wx-box {
            width: 183px;
            height: 211px;
        }
    }

    .base-card-cover {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        width: 100%;
        height: 100vh;
        background: #000000;
        opacity: 0.3;
    }

    .patient-card__tag-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;

        &-logo {
            width: 20px;
            height: 20px;
        }
    }

    .patient-card__tag-popper {
        max-width: 360px;
        padding: 16px;
    }

    .patient-card__tag-box {
        width: 320px;
        max-height: 400px;
        overflow-y: auto;
    }

    .patient-card__statistics {
        margin-top: 12px;
        font-size: 14px;
        color: var(--abc-color-text-primary);

        &-owe {
            color: var(--abc-color-error);
        }
    }

    .patient-card__function-item {
        cursor: pointer;
        border-radius: var(--abc-border-radius-small);
        transition: background-color 0.3s ease;

        &:hover {
            background-color: #f2f4f7;
        }
    }
}
</style>
