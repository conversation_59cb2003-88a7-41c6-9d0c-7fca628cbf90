@import 'src/styles/mixin.scss';

:root {
    --setting-form-sidebar-width: 460px;
    --setting-form-header-height: 56px;
}

.setting-layout-wrapper {
    position: relative;
    display: flex;
    height: 100%;

    //内容
    .setting-layout-content-wrapper {
        display: flex;
        flex: 1;
        flex-direction: column;
        overflow: hidden;

        .setting-layout-content {
            max-height: 100%;
            padding: var(--abc-paddingTB-xxl) 14px 32px var(--abc-paddingTB-xxl);
            overflow: auto;
            scrollbar-gutter: stable;

            @include scrollBar();

            &.has-footer {
                max-height: calc(100% - 81px);
            }
        }
    }

    //侧边栏
    .setting-layout-sidebar {
        position: relative;
        width: var(--setting-form-sidebar-width);
        height: 100%;
        overflow: auto;
        background-color: var(--abc-color-P5);
        scrollbar-gutter: stable;
        border-left: 1px solid $P6;

        .setting-layout-sidebar-preview-icon {
            position: absolute;
            top: 0;
            left: 0;

            .sidebar-preview-icon {
                width: 48px;
                height: 48px;
            }
        }

        @include scrollBar(false, 0);
    }
}

@media screen and (max-width: 1440px) {
    .setting-layout-wrapper {
        .setting-layout-sidebar {
            &.open-zoom {
                zoom: 0.64;
            }
        }
    }
}

@media screen and (min-width: 1440px) {
    .setting-layout-wrapper {
        .setting-layout-sidebar {
            &.open-zoom {
                zoom: 0.73;
            }
        }
    }
}

@media screen and (min-width: 1600px) {
    .setting-layout-wrapper {
        .setting-layout-sidebar {
            &.open-zoom {
                zoom: 1;
            }
        }
    }
}
