<template>
    <div class="biz-quick-list-wrapper">
        <!--搜索-->
        <div class="quick-search-wrapper" data-cy="quick-search">
            <slot name="search"></slot>
        </div>

        <abc-flex
            class="quick-operate-wrapper"
            data-cy="quick-operate"
            justify="space-between"
            align="center"
        >
            <slot name="operate"></slot>
        </abc-flex>

        <!--列表-->
        <div class="quick-content-wrapper" data-cy="quick-list-items">
            <slot name="quickListTop"></slot>

            <div
                v-if="enableVirtual"
                v-abc-loading.middle.gray.noCover="loading"
                class="quick-list-small"
                style="display: flex; flex-direction: column; padding: 0; overflow: initial;"
            >
                <ul v-if="quickListData.length" ref="virtualListUl" style="flex: 1; height: 0;">
                    <abc-virtual-list
                        ref="virtualList"
                        class="quick-list-virtual"
                        need-footer
                        :is-auto-height="false"
                        :scroll-bar-always-show="false"
                        :data-list="quickListData"
                        v-bind="virtualListConfig"
                        :total="quickListTotal"
                        :fetch-data="scrollLoadFunc"
                    >
                        <template #default="{ item }">
                            <slot name="quickListItem" :item="item"></slot>
                        </template>

                        <template #loadingArea>
                            <li class="scroll-loading">
                                加载中
                            </li>
                        </template>

                        <template #noMoreArea>
                            <div class="no-more">
                                没有更多了
                            </div>
                        </template>
                    </abc-virtual-list>
                </ul>

                <div v-if="showEmpty && !loading && !quickListData.length" class="no-patient">
                    暂无患者
                </div>
            </div>
            <div
                v-else
                v-abc-loading.middle.gray.noCover="loading"
                v-abc-scroll-loader="{
                    methods: scrollLoadFunc, isLast: isLast
                }"
                class="quick-list-small"
            >
                <slot></slot>
                <div v-if="!showEmpty && isLast" class="no-more">
                    没有更多了
                </div>

                <div v-if="showEmpty && !loading && !(quickListData && quickListData.length)" class="no-patient">
                    暂无患者
                </div>
            </div>
        </div>

        <!--quick-list小工具-->
        <div v-if="(tools && tools.length) || $slots.quickFooter || $slots.customTools" class="quick-footer-wrapper">
            <slot v-if="$slots.quickFooter" name="quickFooter"></slot>
            <template v-else>
                <div class="quick-footer-title">
                    <abc-tabs-v2
                        v-model="tabValue"
                        size="middle"
                        data-cy="quick-footer-tabs"
                        :custom-gap="8"
                        :disable-indicator="quickFooterTabsOption.length === 1"
                        :border-style="{ borderBottom: 'none' }"
                        :option="quickFooterTabsOption"
                        @change="handleTabChange"
                    ></abc-tabs-v2>
                </div>
                <div class="quick-footer-content">
                    <div v-if="tabValue === 0" class="tools-wrapper">
                        <slot name="customTools"></slot>
                        <template v-if="tools.length">
                            <div v-for="item in tools" :key="`${item.label }group`" class="entry-item-group">
                                <div
                                    :key="item.label"
                                    class="entry-item"
                                    :data-cy="`custom-tools-${item.label}`"
                                    @click="item.handler()"
                                >
                                    <abc-icon
                                        v-if="item.icon"
                                        :icon="item.icon"
                                        :size="item.iconSize || 20"
                                        :color="item.iconColor"
                                    ></abc-icon>
                                    <div class="content">
                                        {{ item.label }}
                                    </div>
                                    <div class="describe" :style="item.customDescribeStyle">
                                        <render-props v-if="item.describeRender" :render="item.describeRender"></render-props>
                                        <template v-else>
                                            {{ item.describe }}
                                        </template>
                                        <abc-icon icon="s-b-right-line-medium" size="16" class="right-arrow-icon"></abc-icon>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                    <slot name="customToolsContent" :tab-value="tabValue"></slot>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'BizQuickList',
        components: {
            RenderProps: {
                name: 'RenderProps',
                functional: true,
                props: {
                    value: Object,
                    render: Function,
                },
                render: (createElement, context) => {
                    const {
                        render, value,
                    } = context.props;
                    if (typeof render === 'function') {
                        return render(createElement, value);
                    }
                    return '';
                },
            },
        },
        props: {
            /**
             * 配置下面的小工具栏
             * {
             *   label: '上机检验',
             *   describe: '仪器上机检验',
             *   icon: 's-flask-color',
             *   iconColor: '#0072F9',
             *   handler: () => {
             *      console.log('');
             *   },
             * }
             * */
            tools: {
                type: Array,
            },

            loading: {
                type: Boolean,
            },
            isLast: {
                type: Boolean,
            },
            /**
             * showEmpty && !loading
             * 就会展示 “暂无患者”
             * !showEmpty && isLast
             * 就会展示 “没有更多了”
             * */
            showEmpty: {
                type: Boolean,
            },
            quickFooterTabs: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            defaultToolTab: {
                type: Number,
                default: 0,
            },
            // 开启虚拟列表
            enableVirtual: {
                type: Boolean,
                default: false,
            },
            // 开启虚拟列表后需要传入
            quickListData: {
                type: Array,
            },
            // 开启虚拟列表后需要传入
            quickListTotal: {
                type: Number,
            },
            virtualListConfig: {
                type: Object,
                default: () => {
                    return {
                        visibleCount: 20,
                        rowHeight: 48,
                        bufferSize: 40,
                    };
                },
            },
            // 滚动加载函数
            scrollLoadFunc: {
                type: Function,
            },
        },
        data() {
            return {
                tabValue: this.defaultToolTab,
            };
        },
        computed: {
            quickFooterTabsOption() {
                const arr = [{
                    label: '小工具',
                    value: 0,
                }];
                if (this.quickFooterTabs) {
                    return arr.concat(this.quickFooterTabs);
                }
                return arr;
            },
        },

        methods: {
            handleScrollLoadData(e) {
                this.$emit('scroll-load', e);
            },
            handleTabChange(value) {
                this.$emit('change-footer-tab', value);
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/mixin.scss";

.biz-quick-list-wrapper {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #ffffff;

    .abc-scrollbar {
        flex: 1;
    }

    &::after {
        display: table;
        clear: both;
        content: "";
    }

    .quick-list-header {
        position: absolute;
        top: 0;
        left: 0;
        z-index: $index1;
        width: 100%;
        height: 64px;
        font-size: 18px;
        line-height: 64px;
        color: var(--abc-color-T1);
        border-bottom: 1px solid $abcLayoutDividerColor;
    }

    .quick-search-wrapper {
        z-index: 1;
        display: flex;
        align-items: center;
        width: 100%;
        height: 56px;
        padding: 0 16px;
        line-height: 48px;
        border-bottom: 1px solid $abcLayoutDividerColor;
        border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;

        .search-patient-wrapper {
            position: relative;
            display: flex;
            flex: 1;
            align-items: center;
            width: 0;
            height: 32px;
            line-height: 32px;
            border-radius: var(--abc-border-radius-small);

            .cis-icon-cross_small {
                position: absolute;
                top: 6px;
                right: 6px;
                z-index: 3;
                width: 20px;
                height: 20px;
                line-height: 20px;
                color: var(--abc-color-P1);
                text-align: center;
                cursor: pointer;
                border-radius: 20px;

                &:hover {
                    color: var(--abc-color-T3);
                    background-color: var(--abc-color-P4);
                }
            }
        }

        .abc-input-wrapper {
            flex: 1;
            background-color: #ffffff;

            .prepend-input {
                z-index: 3;

                .abc-icon {
                    color: var(--abc-color-T3);
                }
            }

            .abc-input__inner {
                padding: 0 0 0 30px;
            }

            .append-inner-input {
                padding: 0 3px;
            }
        }

        .open-order-btn {
            margin-left: 6px;
        }

        button.call-btn {
            width: 32px;
            min-width: 32px;
            margin-left: 0;
        }

        .search-result-tips {
            display: flex;
            justify-content: center;
            height: 48px;
            padding: 0;
            font-size: 12px;
            line-height: 48px;
            color: #7a8794;
            text-align: center;
        }
    }

    .quick-operate-wrapper {
        width: 100%;
        min-height: 41px;
        padding: 0 12px;
        border-bottom: 1px solid var(--abc-color-layout-divider-color);

        &.quick-list-tabs-6-padding {
            padding-left: 6px;
        }

        .search-tips {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 36px;
            padding: 0;
            font-size: 12px;
            color: var(--abc-color-T2);

            i {
                font-size: 14px;
                color: #8d9aa8;
            }
        }

        .abc-tabs {
            display: flex;
            align-items: center;
            height: 100%;
            border-bottom: none;
        }

        .air-pharmacy {
            margin-left: 24px;
            font-size: 14px;
            color: var(--abc-color-T2);
            cursor: pointer;

            &:hover {
                color: var(--abc-color-T1);
            }
        }

        .abc-tabs-item,
        .air-pharmacy {
            position: relative;
            display: flex;
            align-items: center;
            height: 36px;
            line-height: 1;

            .abc-tabs-number-dot {
                position: absolute;
                top: 11px;
                right: unset;
                left: 29px;
                height: 16px;
                padding: 0;
                margin-left: 1px;
                font-family: Roboto-Medium, Roboto;
                font-size: 12px;
                //font-weight: 500;
                line-height: 16px;
                color: #ff3333;
                background-color: transparent;
            }

            & + .abc-tabs-item {
                margin-left: 24px;
            }

            &:hover {
                color: var(--abc-color-T1);
            }

            &.abc-tabs-item-active {
                font-weight: bold;
            }
        }

        .air-pharmacy .abc-tabs-number-dot {
            left: 57px;
        }

        .track {
            flex: 1;
        }

        .select-doctor,
        .select-device {
            &.select-device-14-size {
                .abc-input__inner {
                    font-size: 14px;
                }
            }

            .abc-input__inner {
                font-size: 12px;
                color: var(--abc-color-T2);
                text-align: right;
                border: 0;
            }

            &.is-focus .abc-input__inner {
                border: 0 !important;
                box-shadow: none !important;
            }

            &.is-hover {
                input {
                    border: none !important;
                }
            }

            &.icon-color {
                .abc-input__inner {
                    &::placeholder {
                        color: var(--abc-color-T2);
                    }
                }

                .abc-icon {
                    color: var(--abc-color-P2);

                    &:hover {
                        color: var(--abc-color-T2);
                    }
                }
            }

            &.placehold-color {
                .abc-input__inner {
                    color: var(--abc-color-T1);
                }
            }
        }

        .ql-filter-wrapper {
            margin-left: auto;

            .filter-ql {
                font-size: 12px;
                font-weight: 400;
                line-height: 14px;
                color: var(--abc-color-T2);
                cursor: pointer;

                &:hover {
                    .ql-filter-icon {
                        color: #000000 !important;
                    }
                }
            }
        }

        .select-device,
        .abc-date-picker {
            margin-top: -1px;
        }

        .date-picker {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            height: 36px;
            margin-left: auto;

            .date-picker-reference {
                display: flex;
                align-items: center;
            }
        }

        .date-picker-str {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: var(--abc-color-T2);
            text-align: right;
            cursor: pointer;

            &.is-select-other-day {
                color: var(--abc-color-T1);
            }

            &.date-picker-str-eclipse {
                >span:first-child {
                    width: calc(100% - 16px);
                    max-width: calc(100% - 16px);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            .iconfont {
                position: static;
                color: var(--abc-color-T2);
                background-color: transparent;
                transform: translate3d(0, 0%, 0);

                &:hover {
                    color: var(--abc-color-T2);
                }
            }
        }

        i {
            font-size: 14px;
            color: var(--abc-color-T3);
        }

        .quick-list-tabs_right_part {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: flex-end;

            .examination-date-picker {
                margin-left: 0;
            }
        }

        .hospital-device {
            display: inline-flex;
            align-items: center;
            padding: 4px 6px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
                background: var(--abc-color-P6);
                border-radius: var(--abc-border-radius-small);
            }

            .device-name {
                max-width: 76px;
                margin-right: 2px;
                margin-left: 6px;
                overflow: hidden;
                font-size: 14px;
                font-weight: 500;
                line-height: 20px;
                color: var(--abc-color-T1);
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .device-todo-count {
                display: inline-flex;
                align-items: center;
                height: 14px;
                padding: 0 4px;
                font-size: 12px;
                color: #ffffff;
                background-color: var(--abc-color-R2);
                border-radius: 7px;
            }
        }
    }

    .quick-content-wrapper {
        display: flex;
        flex: 1;
        flex-direction: column;
        height: 0;

        .ql-filter-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: auto;
            height: 28px;
            padding: 5px 8px;
            margin: 2px 12px;
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            color: var(--abc-color-T2);
            background-color: var(--abc-color-cp-grey4);
            border-radius: var(--abc-border-radius-small);

            &:first-child {
                margin-top: 6px;
            }
        }
    }

    .quick-footer-wrapper {
        display: flex;
        flex-direction: column;
        height: 240px;

        .quick-footer-title {
            display: flex;
            align-items: center;
            padding: 0 8px;
            background-color: #ffffff;
            border-top: 1px solid var(--abc-color-layout-divider-color);
            border-bottom: 1px solid var(--abc-color-layout-divider-color);

            > h5 {
                font-weight: bold;
            }
        }

        .quick-footer-content {
            flex: 1;
            height: 0;

            .tools-wrapper {
                height: 100%;
                padding: 6px 0 6px 10px;
                overflow-y: scroll;

                @include scrollBar;
            }

            .call-open-btn {
                width: 100%;
            }

            .wait-pay-order-count {
                display: flex;
                align-items: center;
                justify-content: center;
                min-width: 16px;
                height: 16px;
                padding: 0 4px;
                font-size: 12px;
                color: #ffffff;
                background-color: var(--abc-color-R2);
                border-radius: 8px;
            }

            .entry-item-group {
                position: relative;

                &:not(:last-child)::after {
                    position: absolute;
                    bottom: 0;
                    left: 2px;
                    width: calc(100% - 4px);
                    height: 1px;
                    content: "";
                    background-color: var(--abc-color-P4);
                }
            }

            .entry-item {
                display: flex;
                align-items: center;
                height: 44px;
                padding: 0 10px;
                cursor: pointer;
                border-radius: var(--abc-border-radius-small);

                &:hover {
                    background-color: var(--abc-color-P4);
                }

                & + .entry-item {
                    margin-top: 4px;
                }

                img {
                    width: 22px;
                    height: 22px;
                }

                .content {
                    flex: 1;
                    margin-left: 6px;

                    span {
                        margin-left: 12px;
                        font-size: 12px;
                        color: var(--abc-color-T2);
                    }
                }

                .describe {
                    display: flex;
                    align-items: center;
                    max-width: 180px;
                    height: 20px;
                    font-size: 12px;
                    color: var(--abc-color-T2);

                    .right-arrow-icon {
                        margin-left: 6px;
                        color: var(--abc-color-T3);
                    }
                }

                .iconfont {
                    color: var(--abc-color-T3);
                }
            }
        }
    }

    .quick-list {
        position: relative;
        flex: 1;
        padding: 6px 0 6px 6px;
        overflow-y: scroll;

        @include scrollBar;

        .no-more {
            width: 100%;
            height: 40px;
            font-size: 12px;
            line-height: 40px;
            color: var(--abc-color-T3);
            text-align: center;
            //border-top: 1px solid #F4F5F6;
        }

        .scroll-loading {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: left;
            padding-left: 120px;
            font-size: 12px;

            &::after {
                display: inline-block;
                width: 0;
                overflow: hidden;
                vertical-align: bottom;
                content: "\2026";
                -webkit-animation: ellipsis steps(4, end) 900ms infinite;
                animation: ellipsis steps(4, end) 900ms infinite;
            }
        }

        :root .scroll-loading {
            margin-right: 8px;
        }
    }
    // 默认间距
    .quick-list-virtual {
        padding: 6px 0 6px 6px;
    }

    // 执行/挂号/门诊/收费/药房
    .quick-list-small {
        position: relative;
        flex: 1;
        height: 0;
        padding: 6px 0 6px 6px;
        overflow-y: scroll;

        @include scrollBar;

        .no-more {
            width: 100%;
            height: 40px;
            font-size: 12px;
            line-height: 40px;
            color: var(--abc-color-T3);
            text-align: center;
            //border-top: 1px solid #F4F5F6;
        }

        .scroll-loading {
            display: flex;
            align-items: center;
            padding-left: 126px;
            font-size: 12px;

            &::after {
                display: inline-block;
                width: 0;
                overflow: hidden;
                vertical-align: bottom;
                content: "\2026";
                -webkit-animation: ellipsis steps(4, end) 900ms infinite;
                animation: ellipsis steps(4, end) 900ms infinite;
            }
        }

        :root .scroll-loading {
            margin-right: 8px;
        }

        .treat-off-line {
            margin-top: 39%;
            text-align: center;

            img {
                width: 64px;
                height: 64px;
            }

            h3 {
                font-size: 14px;
                line-height: 24px;
                color: var(--abc-color-T3);
            }

            p {
                height: 20px;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                color: var(--abc-color-T2);
            }
        }

        .flip-list-move {
            transition: transform 0.1s;

            &.active {
                z-index: 1993;
            }
        }
    }

    .abc-loading-wrapper .loading-spinner,
    .no-patient {
        top: 30%;
    }

    .no-patient {
        position: absolute;
        left: 50%;
        height: auto;
        font-size: 14px;
        line-height: 1;
        color: var(--abc-color-T3);
        text-align: center;
        transform: translateX(-50%);
    }

    .patient-info-outpatient-history-wrapper {
        height: 38%;
    }
}

@media screen and (min-width: 1440px) {
    .biz-quick-list-wrapper {
        .quick-search-wrapper {
            padding: 0 16px;
        }

        .quick-footer-wrapper .quick-footer-title,
        .quick-operate-wrapper {
            padding: 0 12px;
        }

        .quick-list-small,
        .quick-list-virtual,
        .quick-list {
            padding: 6px 0 6px 6px;
        }
    }
}

@media screen and (min-width: 1600px) {
    .biz-quick-list-wrapper {
        .quick-search-wrapper {
            padding: 0 20px;
        }

        .quick-footer-wrapper .quick-footer-title,
        .quick-operate-wrapper {
            padding: 0 16px;
        }

        .quick-list-small,
        .quick-list-virtual,
        .quick-list,
        .patient-outpatient-history {
            padding: 6px 0 6px 10px;
        }
    }
}

@media screen and (min-width: 1920px) {
    .biz-quick-list-wrapper {
        .quick-search-wrapper {
            padding: 0 20px;
        }

        .quick-operate-wrapper,
        .quick-footer-wrapper .quick-footer-title {
            padding: 0 16px;
        }

        .quick-list-small,
        .quick-list-virtual,
        .quick-list,
        .patient-outpatient-history {
            padding: 6px 0 6px 10px;
        }
    }
}
</style>
