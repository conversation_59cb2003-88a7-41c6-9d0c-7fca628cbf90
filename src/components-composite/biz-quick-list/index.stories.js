import BizQuickListItem from '../biz-quick-list-item/index.js';
import BizQuickList from './src/views/index.vue';

export default {
    title: 'Components/列表组件/患者列表-QuickList',
    component: BizQuickList,
};

export const base = {
    render: () => ({
        components: {
            BizQuickList,
            BizQuickListItem,
        },

        data() {
            return {
                scrollParamsKey: '',
                tools: [
                    {
                        iconUrl: '',
                        label: '上机检验',
                        describe: '仪器上机检验',
                        icon: 's-flask-color',
                        iconColor: '#0072F9',
                        handler: () => {
                            console.log('上机检验');
                        },
                    },{
                        iconUrl: '',
                        label: '快速开单',
                        describe: '开临时检验单',
                        icon: 's-order-color',
                        iconColor: '#20B150',
                        handler: () => {
                            console.log('快速开单');
                        },
                    },{
                        iconUrl: '',
                        label: 'ABC云检订单',
                        icon: 's-cloudinspection-color',
                        iconColor: '#F87A44',
                        handler: () => {
                            console.log('ABC云检订单');
                        },
                    },
                ],
                loading: false,
                quickList: [],
                selectedItem: null,
            };
        },

        created() {
            this.loading = true;
            // eslint-disable-next-line abc/no-timer-id
            setTimeout(() => {
                this.quickList = [
                    {
                        id: 1,
                        name: '王祖鑫',
                        orderNo: '202410170001',
                        created: '14:08',
                    },
                    {
                        id: 2,
                        name: '诗羽',
                        orderNo: '202410170001',
                        created: '14:08',
                    },
                    {
                        id: 3,
                        name: '匿名患者测试口腔',
                        orderNo: '202410170001',
                        created: '14:08',
                    },
                ];
                this.select(this.quickList[0]);
                this.loading = false;
            }, 1000);
        },

        methods: {
            select(item) {
                this.selectedItem = item;
            },
        },

        template: `
            <div style="width: 320px; height: 1000px;border:1px solid var(--abc-color-layout-divider-color)">
                <BizQuickList
                    :tools="tools"
                    :loading="loading"
                    :showEmpty="quickList.length === 0"
                >
                    <template #search>
                        <abc-input
                            v-model.trim="scrollParamsKey"
                            type="text"
                            placeholder="搜索检验单"
                            style="width: 100%;"
                            clearable
                        >
                            <abc-search-icon slot="prepend"></abc-search-icon>
                        </abc-input>
                    </template>

                    <template #operate>
                        <abc-text bold>全部</abc-text>
                        <abc-space>
                            <abc-button
                                theme="primary"
                                variant="text"
                                size="small"
                            >
                                多选
                            </abc-button>
                            <abc-dropdown size="tiny">
                                <abc-button
                                    slot="reference"
                                    theme="primary"
                                    variant="text"
                                    size="small"
                                >
                                    操作
                                </abc-button>

                                <abc-dropdown-item label="打印样本条码"
                                                   value="print-barcode"></abc-dropdown-item>

                                <abc-dropdown-item
                                    label="打印检验报告"
                                    value="print-report"
                                ></abc-dropdown-item>

                                <abc-dropdown-item
                                    label="完成检验"
                                    value="finish-report"
                                ></abc-dropdown-item>

                                <abc-dropdown-item
                                    label="完成审核"
                                    value="check-report"
                                ></abc-dropdown-item>
                            </abc-dropdown>
                        </abc-space>
                    </template>

                    <template #default>
                        <ul>
                            <biz-quick-list-item
                                v-for="item in quickList"
                                :key="item.id"
                                :quick-item="item"
                                :is-active="selectedItem && selectedItem.id === item.id"
                                show-status
                                with-describe
                                @select="select(item)"
                            >

                                <template #patient-name-append>
                                    {{ item.name }}
                                </template>

                                <template #content>
                                    <abc-tag-v2
                                        shape="square"
                                        theme="success"
                                        variant="outline"
                                        size="tiny"
                                    >
                                        云
                                    </abc-tag-v2>
                                </template>

                                <template #status>
                                    待检
                                </template>

                                <template #abstract>
                                    {{ item.orderNo }}
                                </template>

                                <template #date>
                                    {{ item.created }}
                                </template>
                            </biz-quick-list-item>
                        </ul>
                    </template>

                    <template #customTools>
                        <div class="entry-item-group">
                            <div class="entry-item">
                                <div class="content">
                                    自定义 tool
                                </div>
                            </div>
                        </div>
                    </template>
                </BizQuickList>
            </div>
            `,
    }),

    name: '基础用法',
};
