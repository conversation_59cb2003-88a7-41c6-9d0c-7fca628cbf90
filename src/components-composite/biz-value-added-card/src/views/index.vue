<template>
    <div :class="['biz-value-added-card-wrapper',`biz-value-added-card-wrapper-${theme}`]">
        <abc-card
            :padding-size="'medium'"
            :radius-size="'small'"
            :shadow="false"
            :border="false"
            :style="customStyle"
        >
            <abc-flex
                style="width: 100%; min-height: 50px;"
                justify="space-between"
                align="center"
                :gap="16"
            >
                <abc-flex
                    justify="flex-start"
                    align="center"
                    gap="large"
                >
                    <abc-flex
                        v-if="needIcon"
                        align="center"
                        justify="center"
                        class="biz-value-added-card-prepend"
                    >
                        <slot v-if="$slots.prepend" name="prepend"></slot>
                        <abc-icon
                            v-else
                            :icon="icon"
                            size="30"
                            :color="iconColor"
                        ></abc-icon>
                    </abc-flex>


                    <abc-flex
                        style="width: 100%;"
                        justify="flex-start"
                        align="center"
                        gap="large"
                    >
                        <abc-flex
                            vertical
                            justify="center"
                            align="flex-start"
                            gap="small"
                        >
                            <abc-flex
                                style="width: 100%;"
                                justify="flex-start"
                                align="center"
                                gap="large"
                            >
                                <abc-text size="large" bold>
                                    {{ title }}
                                </abc-text>
                            </abc-flex>

                            <slot name="content">
                                <abc-text theme="gray">
                                    {{ content }}
                                </abc-text>
                            </slot>
                        </abc-flex>
                    </abc-flex>
                </abc-flex>

                <abc-flex :align="(!plainBtnText && isShowOpen) ? 'center' : 'end'" :gap="(isShowOpen || plainBtnText) ? 8 : 0">
                    <biz-marketing-button
                        v-if="btnText"
                        :size="isShowOpen ? 'normal' : 'large'"
                        width="100px"
                        :button-text="btnText"
                        @click="$emit('click')"
                    >
                    </biz-marketing-button>
                    <abc-flex vertical :gap="4" align="center">
                        <abc-space v-if="isShowOpen" :size="4">
                            <template v-if="openStatus === openStatusEnums.PENDING">
                                <abc-icon icon="s-time-fill" size="14" color="var(--abc-color-Y2)"></abc-icon>
                                <abc-text theme="warning-light" bold>
                                    {{ openStatusLabel[openStatus] }}
                                </abc-text>
                            </template>
                            <template v-if="openStatus === openStatusEnums.SUCCESS">
                                <abc-icon icon="s-check-circle-small-fill" size="14" color="var(--abc-color-G2)"></abc-icon>
                                <abc-text theme="success-light" bold>
                                    {{ openStatusLabel[openStatus] }}
                                </abc-text>
                            </template>
                        </abc-space>
                        <biz-marketing-button
                            v-if="plainBtnText"
                            :size="isShowOpen ? 'normal' : 'large'"
                            variant="ghost"
                            width="100px"
                            :button-text="plainBtnText"
                            @click="$emit('plain-click')"
                        >
                        </biz-marketing-button>
                    </abc-flex>
                </abc-flex>
            </abc-flex>
        </abc-card>
    </div>
</template>

<script type="text/ecmascript-6">
    const openStatusEnums = Object.freeze({
        WAIT: 0,
        PENDING: 1,
        SUCCESS: 2,
    });
    const openStatusLabel = Object.freeze({
        [openStatusEnums.PENDING]: '开通中',
        [openStatusEnums.SUCCESS]: '已开通',
    });
    import BizMarketingButton from '@/components-composite/biz-marketing-button/src/views/index.vue';
    export default {
        name: 'BizValueAddedCard',
        components: { BizMarketingButton },

        props: {
            title: {
                type: String,
                default: '',
            },
            content: {
                type: String,
                default: '',
            },
            /**
             * 1:开通中 2:未开通 默认:0不显示
             */
            openStatus: {
                type: Number,
                default: 0,
            },
            btnText: {
                type: String,
                default: '',
            },
            plainBtnText: {
                type: String,
                default: '',
            },
            needIcon: {
                type: Boolean,
                default: true,
            },
            icon: {
                type: String,
                default: '',
            },
            iconColor: {
                type: String,
                default: '',
            },
            theme: {
                type: String,
                default: 'yellow',
                validator: (value) => ['yellow','blue'].indexOf(value) !== -1,
            },
            customStyle: {
                type: Object,
                default: () => {},
            },
        },
        data() {
            return {
                openStatusEnums,
                openStatusLabel,
            };
        },
        computed: {
            isShowOpen() {
                return [openStatusEnums.PENDING,openStatusEnums.SUCCESS].indexOf(this.openStatus) !== -1;
            },
        },
    };
</script>

<style lang="scss">
.biz-value-added-card-wrapper {
    &-yellow {
        .abc-card-wrapper {
            background: var(--abc-color-Y4, #fff4ea);
            background-image: url("../assets/images/biz-value-added-card-yellow.png");
            background-repeat: no-repeat;
            background-position: right center;
            background-size: 317px 100%;
            border: 1px solid var(--abc-color-Y5, #ffebd6);
        }
    }

    &-blue {
        .abc-card-wrapper {
            background: var(--abc-color-B7, #f1f6fe);
            background-image: url("../assets/images/biz-value-added-card-blue.png");
            background-repeat: no-repeat;
            background-position: right center;
            background-size: 317px 100%;
            border: 1px solid var(--abc-color-Theme8, #e0efff);
        }
    }

    .biz-value-added-card-prepend {
        width: 50px;
        min-width: 50px;
        height: 50px;
        background: #ffffff;
        border: 1px solid var(--abc-color-P8, #eaedf1);
        border-radius: var(--abc-border-radius-small, 6px);
    }
}
</style>


