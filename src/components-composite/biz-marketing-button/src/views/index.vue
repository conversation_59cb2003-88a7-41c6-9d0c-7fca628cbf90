<template>
    <div
        class="marketing-button-wrapper"
        :style="{
            zIndex: zIndex,
            ...wrapperStyle,
        }"
    >
        <!-- 独立的深红色椭圆元素 -->
        <div v-if="discountText && !disabled" class="discount-tag-ellipse"></div>

        <div v-if="discountText && !disabled" class="discount-tag">
            <abc-text size="mini" theme="white" bold>
                {{ discountText }}
            </abc-text>
        </div>

        <!-- 右上角亮色块，仅在light模式下显示 -->
        <div v-if="variant === 'light'" class="light-top-right">
            <div class="light-ellipse"></div>
            <div class="light-line"></div>
        </div>

        <!-- 左下角亮色块，仅在light模式下显示 -->
        <div v-if="variant === 'light'" class="light-bottom-left">
            <div class="light-ellipse"></div>
            <div class="light-line"></div>
        </div>

        <abc-button
            variant="ghost"
            theme="primary"
            :size="buttonSize"
            :width="buttonWidth"
            :class="[
                'price-button',
                { 'price-button-light': variant === 'light' },
                { 'price-button-ghost': variant === 'ghost' },
                { 'price-button-xlarge': size === 'xlarge' },
                { 'disabled': disabled },
            ]"
            :disabled="disabled"
            @click="$emit('click')"
        >
            <abc-text
                :size="size === 'xlarge' ? 'large' : ''"
                :bold="size === 'xlarge'"
                theme="black"
            >
                {{ buttonText }}
            </abc-text>
        </abc-button>
    </div>
</template>

<script>
    export const PropsSize = [
        'small',
        'normal',
        'large',
        'xlarge',
    ];
    export default {
        name: 'BizMarketingButton',
        props: {
            /**
             * 按钮尺寸
             * normal 普通(渐变) light 亮 ghost 暗
             */
            variant: {
                type: String,
                default: 'normal',
            },
            /**
             * 按钮尺寸
             */
            size: {
                type: String,
                default: 'large',
                validator: (value) => PropsSize.indexOf(value) !== -1,
            },
            /**
             * 按钮宽度
             */
            width: {
                type: [String, Number],
                default: '100%',
            },
            /**
             * 禁用
             */
            disabled: {
                type: Boolean,
                default: false,
            },
            /**
             * 按钮文本
             */
            buttonText: {
                type: String,
                default: '',
            },
            /**
             * 折扣文本
             */
            discountText: {
                type: String,
                default: '',
            },
            zIndex: {
                type: Number,
                default: 1,
            },
            customStyle: {
                type: Object,
                default: () => {},
            },
        },
        computed: {
            buttonWidth() {
                return typeof width === 'number' ? `${this.width}px` : this.width;
            },
            buttonSize() {
                if (this.size === 'xlarge') {
                    return 'large';
                }
                return this.size;
            },
            wrapperStyle() {
                return {
                    width: typeof this.width === 'number' || /^\d+(\.\d+)?$/.test(this.width) ? `${this.width}px` : this.width,
                    ...this.customStyle,
                };
            },
        },
    };
</script>

<style lang="scss" scoped>
.marketing-button-wrapper {
    position: relative;

    .price-button {
        background: linear-gradient(90.29deg, #efc584 0.29%, #f1d0a1 99.81%);
        border: none;

        &::after {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 100%;
            pointer-events: none;
            content: '';
            border-radius: inherit;
            transition: background-color 0.2s;
        }

        &:hover {
            background: linear-gradient(90deg, #ffe0a3 0%, #ffe7ba 100%);

            &::after {
                background: rgba(255, 255, 255, 0.2);
            }
        }

        &:active::after {
            background: rgba(0, 0, 0, 0.04);
        }

        &-light {
            box-shadow: 0 1px 0 0 #ffffff99 inset, 0 -1px 0 0 #ffffff99 inset;
        }

        &-ghost {
            background: var(--abc-color-cp-white);
            border: 1px solid var(--abc-color-P7);

            > span {
                color: var(--abc-color-Y6);
            }

            &:hover {
                background: var(--abc-color-Y4);
                border-color: var(--abc-color-Y6);

                &::after {
                    background: rgba(255, 255, 255, 0);
                }
            }

            &:active {
                background: var(--abc-color-Y5);

                &::after {
                    background: rgba(255, 255, 255, 0);
                }
            }
        }

        &-xlarge {
            height: 48px;
            border-radius: var(--abc-border-radius-small);
        }

        &.disabled {
            background: var(--abc-color-cp-grey2);
            border: 1px solid var(--abc-color-P7);

            &:active::after {
                background: rgba(0, 0, 0, 0);
            }

            &:hover::after {
                background: rgba(0, 0, 0, 0);
            }

            > span {
                color: var(--abc-color-T3);
            }
        }
    }

    .discount-tag {
        position: absolute;
        top: -9px;
        left: -11px;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 18px;
        padding: 0 5px;
        background: #ff5100;
        border-radius: var(--abc-border-radius-mini);

        /* 左下角深红色椭圆已改为独立元素 */
    }

    /* 独立的深红色椭圆元素 */
    .discount-tag-ellipse {
        position: absolute;
        top: -1px;
        left: -11px;
        z-index: -1; /* 设置最低层级，确保在按钮下面 */
        width: 27px;
        height: 13px;
        background: #ff996a;
        border-radius: 50%;
    }

    /* 右上角亮色块 */
    .light-top-right {
        position: absolute;
        top: 0;
        right: 0;
        width: 128px;
        height: 25px;
        pointer-events: none;
        border-radius: var(--abc-border-radius-small);

        .light-ellipse {
            right: -2px;
            bottom: 10px;
        }

        .light-line {
            top: 0;
            right: 0;
        }
    }

    /* 左下角亮色块 */
    .light-bottom-left {
        position: absolute;
        bottom: 1px;
        left: 0;
        width: 128px;
        height: 25px;
        pointer-events: none;
        border-radius: var(--abc-border-radius-small);

        .light-ellipse {
            top: 10px;
            left: -2px;
        }

        .light-line {
            bottom: 0;
            left: 0;
        }
    }

    /* 亮色块中的椭圆形 */
    .light-ellipse {
        position: absolute;
        z-index: 1;
        width: 120px;
        height: 50px;
        background: #ffffff;
        filter: blur(4px);
        border-radius: 50%;
        opacity: 0.27;
    }

    /* 亮色块中的线条 */
    .light-line {
        position: absolute;
        z-index: 1;
        width: 100px;
        height: 1px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #ffffff 51.5%, rgba(255, 255, 255, 0) 97%);
    }
}
</style>

