import i18n from '@/i18n';
import AbcS<PERSON><PERSON><PERSON>oader from '@/directive/scroll-loader';
import AbcAutoFocus from '@/directive/auto-focus';
import AbcFocusSelected from '@/directive/focus-select';
import AbcMoney from '@/components/abc-money';

import * as filters from '@/common/filters';

import BizChargeStatPopover from './biz-charge-stat-popover';
import BizCrmTrack from './biz-crm-track';
import BizCustomHeader from './biz-custom-header';
import BizCustomizationOptions from './biz-customization-options';
import BizDataStatisticsCard from './biz-data-statistics-card';
import BizDispenserSelector from './biz-dispenser-selector';
import BizEmployeePanelSelector from './biz-employee-panel-selector';
import BizExamBusinessTag from './biz-exam-business-tag';
import BizExpressAddressSelector from './biz-express-address-selector';
import BizGoodsInfoTagGroup from './biz-goods-info-tag-group';
import { BizGoodsSelectDialog } from './biz-goods-select-dialog';
import BizGoodsTypeCascader from './biz-goods-type-cascader';
import BizGridPanelSelector from './biz-grid-panel-selector';
import BizInspectReportList from './biz-inspect-report-list';
import BizMarketingButton from './biz-marketing-button';
import BizMixedSelectionFilter from './biz-mixed-selection-filter';
import BizOutpatientHistoryCard from './biz-outpatient-history-card';
import BizPanelSelector from './biz-panel-selector';
import BizPatientCard from './biz-patient-card';
import BizProjectMultipleSelect from './biz-project-multiple-select';
import BizQuickList from './biz-quick-list';
import BizQuickListItem from './biz-quick-list-item';
import BizQuickOptionsPanel from './biz-quick-options-panel';
import BizSelectTabs from './biz-select-tabs';
import BizTagSelector from './biz-tag-selector';
import BizValueAddedCard from './biz-value-added-card';
import BizVersionTips from './biz-version-tips';
import BizWeekSchedule from './biz-week-schedule';
import BizSettingForm, {
    BizSettingFormGroup,
    BizSettingFormItem,
    BizSettingFormItemTip,
    BizSettingFormItemIndent,
    BizSettingFormHeader,
} from './setting-form';
import BizSettingLayout, {
    BizSettingContent,
    BizSettingFooter,
    BizSettingSidebar,
    BizFillRemainHeight,
} from './setting-form-layout';


const components = [
    BizChargeStatPopover,
    BizCrmTrack,
    BizCustomHeader,
    BizCustomizationOptions,
    BizDataStatisticsCard,
    BizDispenserSelector,
    BizEmployeePanelSelector,
    BizExamBusinessTag,
    BizExpressAddressSelector,
    BizGoodsInfoTagGroup,
    // BizGoodsSelectDialog, 函数式调用
    BizGoodsTypeCascader,
    BizGridPanelSelector,
    BizInspectReportList,
    BizMarketingButton,
    BizMixedSelectionFilter,
    BizOutpatientHistoryCard,
    BizPanelSelector,
    BizPatientCard,
    BizProjectMultipleSelect,
    BizQuickList,
    BizQuickListItem,
    BizQuickOptionsPanel,
    BizSelectTabs,
    BizTagSelector,
    BizValueAddedCard,
    BizVersionTips,
    BizWeekSchedule,
    BizSettingForm,
    BizSettingLayout,
];

export function setAbcTheme(theme = 'default') {
    document.documentElement.setAttribute('data-abc-theme', theme);
}


const install = function (Vue, options) {
    Object.keys(filters).forEach((key) => {
        Vue.filter(key, filters[ key ]);
    });

    Vue.directive('AbcScrollLoader', AbcScrollLoader);
    Vue.directive('AbcAutoFocus', AbcAutoFocus);
    Vue.directive('AbcFocusSelected', AbcFocusSelected);
    Vue.component(AbcMoney.name, AbcMoney);

    components.forEach((component) => {
        Vue.use(component);
    });

    // 声明 theme css 变量，组件内都应该用 css 变量
    let theme = 'default';
    if (options && options.theme) {
        theme = options.theme;
    }
    setAbcTheme(theme);
};

export {
    i18n,
    AbcMoney,
    BizChargeStatPopover,
    BizCrmTrack,
    BizCustomHeader,
    BizCustomizationOptions,
    BizDataStatisticsCard,
    BizDispenserSelector,
    BizEmployeePanelSelector,
    BizExamBusinessTag,
    BizExpressAddressSelector,
    BizGoodsInfoTagGroup,
    BizGoodsSelectDialog,
    BizGoodsTypeCascader,
    BizGridPanelSelector,
    BizInspectReportList,
    BizMarketingButton,
    BizMixedSelectionFilter,
    BizOutpatientHistoryCard,
    BizPanelSelector,
    BizPatientCard,
    BizProjectMultipleSelect,
    BizQuickList,
    BizQuickListItem,
    BizQuickOptionsPanel,
    BizSelectTabs,
    BizTagSelector,
    BizValueAddedCard,
    BizVersionTips,
    BizWeekSchedule,
    // 这里把所有子组件都导出
    BizSettingForm,
    BizSettingFormGroup,
    BizSettingFormItem,
    BizSettingFormItemTip,
    BizSettingFormItemIndent,
    BizSettingFormHeader,
    // 这里把所有子组件都导出
    BizSettingLayout,
    BizSettingContent,
    BizSettingFooter,
    BizSettingSidebar,
    BizFillRemainHeight,
};

export default {
    install,
};
