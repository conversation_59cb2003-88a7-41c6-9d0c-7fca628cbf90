<template>
    <div class="select-tabs-wrapper">
        <abc-select
            v-model="comValue"
            :width="width"
            :max-width="maxWidth"
            :max-height="maxHeight"
            :inner-width="innerWidth"
            :placeholder="placeholder"
            :input-style="inputStyle"
            :trigger-icon="triggerIcon"
            :data-cy="dataCy"
            :clearable="false"
            :disabled="disabled"
            :size="size"
            @input="handleChange"
        >
            <template #prepend>
                <abc-icon
                    :icon="selectedOption.icon || 's-inbox-color'"
                    :size="iconSize"
                ></abc-icon>
            </template>

            <abc-option
                v-for="(op) in options"
                :key="op.value"
                :value="op.value"
                :label="op.label"
                :disabled="!!op.disabled"
            ></abc-option>
        </abc-select>
    </div>
</template>

<script type="text/ecmascript-6">
    export default {
        name: 'BizSelectTabs',
        props: {
            value: [Number, String, Boolean],
            /**
             * 大小，支持 large/medium/small/tiny
             */
            size: {
                type: String,
                default: '',
            },
            width: {
                type: Number,
                default: 148,
            },
            placeholder: {
                type: String,
                default: '',
            },
            dataCy: {
                type: String,
                default: '',
            },
            options: {
                type: Array,
                default: () => [],
            },
            /**
             * 下拉选项最大宽度
             */
            maxWidth: {
                type: [ Number, String ],
            },
            /**
             * 下拉选项最大高度
             */
            maxHeight: {
                type: [ Number, String ],
                default: 308,
            },
            innerWidth: {
                type: [ Number, String ],
                default: 240,
            },
            inputColor: {
                type: String,
                default: '#005ED9',
            },
            /**
             * 触发icon
             */
            triggerIcon: {
                type: String,
                default: 's-triangle-select-color',
            },
            disabled: Boolean,
        },
        computed: {
            comValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            selectedOption() {
                return this.options.find((item) => item.value === this.value) ?? {};
            },
            inputStyle() {
                const mapping = {
                    'tiny': {
                        fontSize: '13px',
                        paddingLeft: '26px',
                        paddingRight: '22px',
                    },
                    'small': {
                        fontSize: '14px',
                        paddingLeft: '28px',
                        paddingRight: '23px',
                    },
                    'medium': {
                        fontSize: '14px',
                        paddingLeft: '40px',
                        paddingRight: '28px',
                    },
                    'large': {
                        fontSize: '16px',
                        paddingLeft: '40px',
                        paddingRight: '28px',
                    },
                    'default': {
                        fontSize: '14px',
                        paddingLeft: '30px',
                        paddingRight: '24px',
                    },
                };
                return {
                    color: this.inputColor,
                    fontWeight: 600,
                    ...(mapping[this.size] || mapping.default),
                };
            },
            iconSize() {
                const mapping = {
                    'tiny': 14,
                    'small': 14,
                    'medium': 16,
                    'large': 18,
                };
                return mapping[this.size] || 16;
            },
        },
        methods: {
            handleChange(val) {
                this.$emit('change', val);
            },
        },
    };
</script>

<style lang="scss">
.select-tabs-wrapper {
    .abc-select-wrapper {
        .abc-select-trigger-icon {
            right: 8px;
        }
    }

    .abc-select-tiny-wrapper {
        .prepend-select {
            width: 26px;
        }

        .abc-select-trigger-icon {
            right: 6px;
        }
    }

    .abc-select-small-wrapper {
        .prepend-select {
            width: 28px;
        }

        .abc-select-trigger-icon {
            right: 7px;
        }
    }

    .abc-select-medium-wrapper {
        .abc-select-trigger-icon {
            right: 12px;
        }
    }

    .abc-select-large-wrapper {
        .abc-select-trigger-icon {
            right: 12px;
        }
    }
}
</style>


