<template>
    <abc-popover
        placement="bottom-start"
        :visible-arrow="false"
        trigger="manual"
        :value="editor"
        popper-class="crm-module__package-label__abc-label-edit"
    >
        <slot slot="reference"></slot>
        <template v-if="editor">
            <div v-abc-auto-focus class="box">
                <abc-input
                    v-model.trim="labelName"
                    placeholder="请输入"
                    :width="160"
                    :max-length="10"
                    type="text"
                ></abc-input>
                <div class="handle">
                    <div
                        :class="{
                            save: true,
                            disabled: disabledSave,
                        }"
                        @click="clickSave"
                    >
                        保存
                    </div>
                    <div class="cancel" @click="$emit('cancel')">
                        取消
                    </div>
                    <div v-if="showDel" class="delete" @click="$emit('delete')">
                        删除
                    </div>
                </div>
            </div>
        </template>
    </abc-popover>
</template>

<script>
    export default {
        props: {
            editor: Boolean,
            showDel: {
                type: Boolean,
                default: true,
            },
            value: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                labelName: '',
            };
        },
        computed: {
            disabledSave() {
                if (!this.labelName || this.value === this.labelName) {
                    return true;
                }
                return false;

            },
        },
        watch: {
            value(newValue) {
                this.labelName = newValue;
            },
        },
        methods: {
            clickSave() {
                if (this.disabledSave) {
                    return;
                }
                this.$emit('save', this.labelName, () => {
                    this.labelName = '';
                });
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

.crm-module__package-label__abc-label-edit {
    padding: 0;
    background-color: #ffffff;
    border: 0;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06);
    transform: translateY(-3px);

    .box {
        @include flex(row, flex-start, center);

        height: 32px;
        overflow: hide;

        .abc-input-wrapper > input {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .handle {
            @include flex(row, flex-start, stretch);

            box-sizing: border-box;
            height: 32px;
            line-height: 32px;
            border: 1px solid $P1;
            border-left: 0;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;

            .save {
                width: 36px;
                height: 30px;
                font-size: 13px;
                line-height: 30px;
                color: $theme1;
                text-align: center;
                cursor: pointer;
                background-color: #ffffff;

                &:hover {
                    background-color: $P4;
                }
            }

            .cancel {
                @extend .save;

                color: $T1;
            }

            .delete {
                @extend .save;

                color: #ff3333;
            }

            .disabled {
                color: $T2;
                cursor: not-allowed;
                background-color: $P6;
            }

            & > div:last-child {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }
    }
}
</style>
