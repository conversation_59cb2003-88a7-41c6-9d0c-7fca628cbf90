<template>
    <div class="biz-panel-selector-wrapper">
        <abc-flex
            vertical
            :gap="8"
        >
            <abc-flex justify="space-between">
                <abc-button
                    variant="text"
                    size="small"
                    @click="onClickAdd"
                >
                    {{ addText }}
                </abc-button>
                <template v-if="!isMatchAll">
                    <abc-button
                        v-if="!isDeleting"
                        variant="text"
                        size="small"
                        :disabled="!selectedList.length"
                        @click="isDeleting = true"
                    >
                        删除
                    </abc-button>
                    <abc-button
                        v-else
                        variant="text"
                        size="small"
                        @click="isDeleting = false"
                    >
                        退出删除
                    </abc-button>
                </template>
            </abc-flex>


            <abc-flex v-if="isMatchAll">
                <abc-tag-v2
                    shape="square"
                    size="huge"
                    theme="default"
                    variant="outline"
                    :min-width="80"
                >
                    全部类型
                </abc-tag-v2>
            </abc-flex>
            <abc-card v-else>
                <div class="select-list-wrapper" :class="{ 'has-config-item': selectedList.length }">
                    <template v-if="selectedList.length">
                        <abc-tag-v2
                            v-for="(item, index) in selectedList"
                            :key="item.id"
                            shape="square"
                            size="huge"
                            theme="default"
                            variant="outline"
                            :closable="closeable"
                            close-resident
                            :min-width="80"
                            :icon="icon"
                            :style="{
                                width: `${nameTextWidth + 36}px`,
                            }"
                            @close="deleteItem(index)"
                        >
                            <abc-text
                                siz="normal"
                                tag="div"
                                class="ellipsis"
                                :style="{
                                    'min-width': '42px',
                                    'width': `${nameTextWidth}px`,
                                }"
                                :title="formatItemName(item)"
                            >
                                {{ formatItemName(item) }}
                            </abc-text>
                            <template v-if="customRenderIcon" #icon>
                                <img
                                    v-if="item.enableIcon"
                                    class="icon-img"
                                    style="width: 14px; height: 14px; margin-right: 4px;"
                                    :src="item.enableIcon"
                                    alt=""
                                />
                            </template>
                        </abc-tag-v2>
                    </template>
                    <abc-content-empty
                        v-else
                        top="19px"
                        value="暂无数据"
                        :show-icon="false"
                    ></abc-content-empty>
                </div>
            </abc-card>
        </abc-flex>
    </div>
</template>

<script type="text/ecmascript-6">
    export default {
        name: 'BizPanelSelector',

        props: {
            /**
             * 选中的列表，支持 v-model 双向绑定
             * @type {Array<Employee>} Employee: { id: string, name: string, namePy: string, namePyFirst: string }
             */
            value: {
                type: Array,
                default: () => [],
            },
            /**
             * 添加按钮的文本
             * @type {string}
             * @default '添加'
             */
            addText: {
                type: String,
                default: '添加',
            },
            /**
             * tag图标
             * @type {string}
             */
            icon: {
                type: String,
                default: '',
            },
            /**
             * item名称format函数
             * @type {Function}
             */
            formatItemName: {
                type: Function,
                default: (item) => item.name,
            },
            customRenderIcon: Boolean,
            maxNameTextLength: {
                type: Number,
                default: 3,
            },
            /**
             * 匹配全部时显示全部
             * @type {Number}
             */
            isMatchAll: {
                type: Number,
                default: 0,
                validator(v) {
                    return v === 0 || v === 1;
                },
            },
            /**
             * 是否可以删除全部
             * @type {Boolean}
             */
            canDeleteAll: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                selectEmployeeDialog: false,
                isDeleting: false,
            };
        },

        computed: {
            selectedList: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            nameTextWidth() {
                return this.maxNameTextLength * 14;
            },
            closeable() {
                if (this.canDeleteAll) {
                    return !!(this.isDeleting && this.selectedList.length);
                }
                return !!(this.isDeleting && this.selectedList.length > 1);
            },
        },

        methods: {
            onClickAdd() {
                this.isDeleting = false;
                this.$emit('add');
            },
            deleteItem(index) {
                const deletedItem = this.selectedList[index];
                this.selectedList.splice(index, 1);
                this.$emit('deleteItem', deletedItem);
            },
        },
    };
</script>

<style lang="scss" scoped>
    @import "src/styles/mixin.scss";

    .biz-panel-selector-wrapper {
        .select-list-wrapper {
            min-height: 92px;
            max-height: 210px;
            padding: 10px 0 10px 10px;
            overflow-y: scroll;

            @include scrollBar(true);

            &.has-config-item {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
        }
    }
</style>
