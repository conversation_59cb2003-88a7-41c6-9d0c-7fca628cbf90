<template>
    <track-card-model custom-class="crm-module__package-track__item-retail" @click.native="$emit('look-detail', info.actionAbstract.chargeSheetId)">
        <template slot="img">
            <abc-icon size="20" icon="s-currency-color"></abc-icon>
        </template>
        <template slot="title">
            零售
        </template>
        <template slot="dates">
            {{ info.recordHappened | formatCacheTime }}
        </template>
        <template slot="section">
            <div class="item">
                <span>门店：</span>
                <span>{{ info.actionAbstract.clinicName }}</span>
            </div>
            <div class="item">
                <span>项目：</span>
                <span>{{ info.actionAbstract.items }}项</span>
            </div>
            <div class="item">
                <span>付费：</span>
                <span v-if="fee"><abc-money :value="fee"></abc-money></span>
                <span v-else><abc-money :value="0"></abc-money></span>
            </div>
        </template>
    </track-card-model>
</template>

<script>
    import trackCardModel from './track-card-model';
    export default {
        name: 'ItemRetail',
        components: {
            trackCardModel,
        },
        props: {
            info: Object,
        },
        computed: {
            fee() {
                return this.info.actionAbstract.amount || this.info.actionAbstract.receivedFee || 0;
            },
        },
    };
</script>

