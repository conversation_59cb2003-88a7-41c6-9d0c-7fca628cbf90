<template>
    <track-card-model custom-class="crm-module_register-appointment--card-box" @click.native="openAppointmentCardFromTrack">
        <template slot="img">
            <abc-icon size="20" icon="s-calendar-1-color"></abc-icon>
        </template>
        <template slot="title">
            预约
        </template>
        <template slot="dates">
            {{ info.recordHappened | formatCacheTime }}
        </template>
        <template slot="section">
            <div class="item">
                <span>医生：</span>
                <span>{{ registerAppointmentPatient.doctorName }}</span>
            </div>
            <div class="item">
                <span>项目：</span>
                <span>{{ registerAppointmentPatient.project ? registerAppointmentPatient.project : '--' }}</span>
            </div>
            <div class="item">
                <span>门店：</span>
                <span>{{ registerAppointmentPatient.clinicName }}</span>
            </div>
        </template>
    </track-card-model>
</template>

<script>
    import trackCardModel from './track-card-model';
    // import { getRegisterAppointmentPatient } from 'utils/register-appointment-patient-model';
    export default {
        name: 'ItemRegisterAppointmentCard',
        components: {
            trackCardModel,
        },
        props: {
            info: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        computed: {
            registerAppointmentPatient() {
                if (!this.info) {
                    return {
                        doctorName: '--',
                        project: '--',
                        clinicName: '--',
                    };
                }
                return this.getRegisterAppointmentPatient(this.info.actionAbstract);
            },
        },
        methods: {
            getRegisterAppointmentPatient(patient) {
                // 如果没有病人信息，返回空数据
                if (!patient) {
                    return null;
                }
                let project = '';
                if (patient?.registrationProducts?.length) {
                    project = patient.registrationProducts.map((item) => {
                        return item.displayName;
                    }).join(',');
                }

                const registerAppointmentPatient = {
                    ...patient,
                    applyAuditStatus: patient?.applyAuditStatus || 0,
                    patientName: patient?.name || patient?.patientName,
                    doctorName: patient?.doctorName || '',
                    project: patient?.formItemProducts || project || '',
                    clinicName: patient?.clinicName || '',
                    clinicId: patient?.clinicId || '',
                    payStatusV2: patient?.payStatusV2 || 0,
                    appointmentTime: {
                        dateStr: patient?.reserveDate || '',
                        dateTimeStr: '' ,
                        // 精确预约模式
                        dateTimeSection: patient?.reserveEnd ? `${patient?.reserveStart}~${patient?.reserveEnd}` : `${patient?.reserveStart}`,
                    },
                };

                return registerAppointmentPatient;
            },
            // 打开预约升级小卡片
            openAppointmentCardFromTrack() {
                this.$emit('openAppointmentCardFromTrack', this.info);
            },
        },
    };
</script>
