<template>
    <div
        v-abc-loading.middle.gray.noCover="loading"
        v-abc-scroll-loader="{
            methods: fetchRevisitQuickList, isLast
        }"
        class="crm-track-wrapper"
    >
        <div v-if="showDataList.length !== 0" class="track-list-item">
            <abc-list
                size="large"
                :create-key="(e)=>{
                    return e.id
                }"
                show-divider
                :no-hover-border="false"
                style="overflow-y: auto !important;"
                :custom-padding="[0, 0]"
                :data-list="showDataList"
            >
                <template
                    #default="{
                        item
                    }"
                >
                    <item-outpatient
                        v-if="item.action === ActionStatus.OUTPATIENT_STATUS"
                        :key="item.id"
                        :info="item"
                        style="width: 100%;"
                        @look-detail="handleOpenDetail('handleShowOutpatientInfo', ...arguments)"
                    ></item-outpatient>
                    <item-retail
                        v-if="item.action === ActionStatus.RETAIL_STATUS"
                        :key="item.id"
                        :info="item"
                        style="width: 100%;"
                        @look-detail="handleOpenDetail('handleShowChargeInfo', ...arguments)"
                    ></item-retail>
                    <item-visit
                        v-if="item.action === ActionStatus.FOLLOW_UP_STATUS"
                        :key="item.id"
                        :info="item"
                        style="width: 100%;"
                        @look-detail="handleOpenDetail('handleShowVisitInfo', ...arguments)"
                    ></item-visit>
                    <item-register-appointment-card
                        v-if="item.action === ActionStatus.REGISTER_APPOINTMENT_STATUS"
                        :key="item.id"
                        :info="item"
                        style="width: 100%;"
                        @openAppointmentCardFromTrack="openAppointmentCardFromTrack"
                    >
                    </item-register-appointment-card>
                    <item-in-hospital
                        v-if="item.action === ActionStatus.INHOSPITAL_STATUS"
                        :key="item.id"
                        :info="item"
                        style="width: 100%;"
                        @look-detail="item => handleOpenDetail('handleShowInHospitalInfo', item)"
                    ></item-in-hospital>
                </template>
            </abc-list>
        </div>
        <div v-if="showDataList.length === 0 && !loading" class="no-data">
            <span>暂无数据</span>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import ItemRetail from './item-retail.vue';
    import ItemVisit from './item-visit.vue';
    import ItemRegisterAppointmentCard from './item-register-appointment-card.vue';
    import ItemOutpatient from './item-outpatient.vue';
    import ItemInHospital from '@/components-composite/biz-crm-track/src/views/item-in-hospital.vue';

    export const ActionStatus = {
        OUTPATIENT_STATUS: '门诊',
        RETAIL_STATUS: '零售',
        FOLLOW_UP_STATUS: '随访',
        REGISTER_APPOINTMENT_STATUS: '预约',
        EXECUTE_STATUS: '执行',
        INSPECT_STATUS: '检查',
        EXAMINATION_STATUS: '检验',
        FOLLOWER_STATUS: '跟进记录',
        CONSULTANT_PLAN: '治疗方案',
        INHOSPITAL_STATUS: '住院',
        PE_STATUS: '体检',
    };
    export default {
        name: 'BizCrmTrack',
        components: {
            ItemInHospital,
            ItemOutpatient,
            ItemRegisterAppointmentCard,
            ItemVisit,
            ItemRetail,
        },
        props: {
            patientId: {
                type: String,
                default: '',
            },
            dataList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            isLast: Boolean,
            loading: Boolean,
            nonePadding: Boolean,
            filterList: {
                type: Array,
                default: () => {
                    return [
                        ActionStatus.OUTPATIENT_STATUS,
                        ActionStatus.RETAIL_STATUS,
                        ActionStatus.FOLLOW_UP_STATUS,
                        ActionStatus.REGISTER_APPOINTMENT_STATUS,
                        ActionStatus.INHOSPITAL_STATUS,
                    ];
                },
            },
        },
        data() {
            return {
                ActionStatus,
            };
        },
        computed: {
            // 显示的轨迹
            showDataList() {
                return this.dataList.filter(
                    (item) => {
                        return this.filterList.includes(item.action);
                    },
                );
            },
        },
        methods: {
            handleOpenDetail(type, item) {
                this.$emit('handleOpenDetail', type, item);
            },
            fetchRevisitQuickList() {
                this.$emit('fetchRevisitQuickList');
            },
            openAppointmentCardFromTrack(item) {
                this.$emit('openAppointmentCardFromTrack', item);
            },
        },
    };
</script>
<style lang="scss">
@import 'src/styles/abc-common.scss';
@import 'src/styles/mixin.scss';

.crm-track-wrapper {
    box-sizing: border-box;
    width: 100%;
    padding: 0 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: $S2;

    @include scrollBar;

    .track-list-item {
        .crm-module__package-track__item-outpatient,
        .crm-module__package-track__item-retail,
        .crm-module__package-track__item-visit,
        .track-card-model {
            &:not(:last-child) {
                border-bottom: 1px solid $P4;
            }

            &:last-child {
                border-bottom: none;
            }
        }
    }

    .no-data {
        height: 260px;
        color: $T3;
        border: none;

        @include flex(row, center, center);
    }
}
</style>


