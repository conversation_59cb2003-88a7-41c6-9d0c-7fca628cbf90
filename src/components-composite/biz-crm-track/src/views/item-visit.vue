<template>
    <track-card-model custom-class="crm-module__package-track__item-visit" @click.native="$emit('look-detail', info.actionAbstract.revisitId)">
        <template slot="img">
            <abc-icon size="20" icon="s-follow-up-color"></abc-icon>
        </template>
        <template slot="title">
            随访
        </template>
        <template slot="dates">
            {{ formatCacheTime(info.recordHappened) }}
        </template>
        <template slot="section">
            <div class="item">
                <span>门店：</span>
                <span>{{ info.actionAbstract.clinicName }}</span>
            </div>
            <div v-if="!!info.actionAbstract.outpatientDiagnosis" class="item">
                <span>诊断：</span>
                <span>{{ info.actionAbstract.outpatientDiagnosis }}</span>
            </div>
            <div class="item">
                <span>目标：</span>
                <span>{{ info.actionAbstract.target }}</span>
            </div>
            <div class="item">
                <span>结果：</span>
                <span>{{ info.actionAbstract.result }}</span>
            </div>
        </template>
    </track-card-model>
</template>

<script>
    import trackCardModel from './track-card-model';
    export default {
        name: 'ItemVisit',
        components: {
            trackCardModel,
        },
        props: {
            info: {
                type: Object,
                default: () => {},
            },
        },
        methods: {
            // 随访格式特殊处理，只展示日期，因为自动随访无法确定时间点，所以执行时间都是00:00，所以不展示时间点
            formatCacheTime(date) {
                //时间格式转换
                if (typeof date === 'string') {
                    if (date.length >= 19) {
                        date = date.slice(0, 19);
                        if (date.indexOf('T') !== -1) {
                            date += 'Z';
                        } else {
                            date = date.replace(/-/g, '/');
                        }
                    }
                }
                const d = new Date(date);
                const today = new Date();
                today.setHours(0);
                today.setMinutes(0);
                today.setMinutes(0);
                const month = (+d.getMonth() + 1) > 9 ? +d.getMonth() + 1 : `0${d.getMonth() + 1}`;
                const day = +d.getDate() > 9 ? d.getDate() : `0${d.getDate()}`;
                return `${d.getFullYear()}-${month}-${day}`;
            },
        },
    };
</script>
