export function AbcSetTheme(themeObj, el) {
    if (!themeObj) return;
    el = el || document.documentElement;

    if (themeObj.scrollBarBackgroundColor === '#000000') {
        themeObj.scrollBarBackgroundColor = 'rgba(0,0,0,.2)';
        themeObj.scrollBarHoverBackgroundColor = 'rgba(0,0,0,.5)';
    } else {
        themeObj.scrollBarBackgroundColor = 'rgba(255,255,255,.2)';
        themeObj.scrollBarHoverBackgroundColor = 'rgba(255,255,255,.5)';
    }

    themeObj.headerItemHoverBC = 'transparent';
    if (themeObj.headerFontColor === '#FFFFFF') {
        themeObj.headerItemHoverBC = 'rgba(255,255,255,.12)';
        themeObj.headerRoundBorderColor = 'rgba(255,255,255,.2)';
        themeObj.headerItemHoverBC = 'rgba(255,255,255,.15)';
        themeObj.headerItemActiveBC = 'rgba(255,255,255,.25)';
    }
    if (themeObj.headerFontColor === '#000000') {
        themeObj.headerItemHoverBC = 'rgba(0,0,0,.08)';
        themeObj.headerRoundBorderColor = 'rgba(0,0,0,.2)';
        themeObj.headerItemHoverBC = 'rgba(0,0,0,.15)';
        themeObj.headerItemActiveBC = 'rgba(0,0,0,.25)';
    }

    for (const themeObjKey in themeObj) {
        if (themeObj.hasOwnProperty(themeObjKey)) {
            el.style.setProperty(`--${themeObjKey}`, themeObj[themeObjKey]);
        }
    }
}
