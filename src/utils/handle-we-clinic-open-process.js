import {
    OpenProcessStatusCode, OpenProcessStatusCodeTipsText,
} from 'src/views/settings/micro-clinic/constant';
import { formatDate } from '@abc/utils-date';
import { WeappStatus } from '@/views/we-clinic/data';
import { McComponentAPI } from 'views/settings/micro-clinic/core/mc-component-api';
import { CmsResourceType } from '@/service/cms/constant.js';
import { windowOpen } from '@/core/navigate-helper';
// 获取备案消息码
export async function geyMcWeappIcpEnTranceInfo(isH5 = true) {
    const responseObject = {
        filingsStatus: 0,
        filingsErrCode: 0,
        filingsErrMsg: '',
        funcInfos: [],
        auditData: [],
        weappVerifyInfo: null,
    };
    // H5版本不用查看此类消息
    if (isH5) {
        return responseObject;
    }
    try {
        const { mpWeappViewList = [] } = await McComponentAPI.getAuthedMpWeappUsingGET();
        const res = mpWeappViewList.find((item) => {return item.type === 10;}) || {};
        responseObject.filingsStatus = res?.icpEntranceInfo?.statusCode || 0;
        responseObject.filingsErrCode = res?.icpEntranceInfo?.errcode || 0;
        responseObject.filingsErrMsg = res?.icpEntranceInfo?.errmsg || '';
        responseObject.auditData = res?.icpEntranceInfo?.auditData || [];
        responseObject.weappVerifyInfo = res?.weappVerifyInfo || null;
        responseObject.funcInfos = res?.funcInfos || [];
        return responseObject;
    } catch (e) {
        console.log(e);
        return responseObject;
    }
}
// 获取临期时间
export function getNearByTime(time) {
    const specifiedDate = new Date();
    const timeDiff = Math.abs(new Date(time) - specifiedDate);
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    return {
        days,
        nearByDays: formatDate(new Date(time), 'YYYY-MM-DD'),
    };
}
// 获取小程序认证流程 TODO 存量用户需要增加认证接口获取认证时间 认证状态
export async function getMcWeappAuthInfo(isH5 = true) {
    const responseObject = {
        status: OpenProcessStatusCode.AUTH_NEED, // 待认证
        time: '', // 认证到期时间，没有时间就是未认证，临期需要提醒
        filingsStatus: 0,
    };
    // H5版本不用查看此类消息
    if (isH5) {
        return responseObject;
    }
    try {
        const data = await geyMcWeappIcpEnTranceInfo(isH5);
        responseObject.status = data?.weappVerifyInfo?.isQualificationVerified ? OpenProcessStatusCode.AUTH_SUCCESS : OpenProcessStatusCode.AUTH_NEED; // 待认证
        let time = data?.weappVerifyInfo?.annualReviewEndTime;
        if (time) {
            time = formatDate(new Date(time), 'YYYY-MM-DD');
        }
        responseObject.time = time; // 认证到期时间，没有时间就是未认证，临期需要提醒
        responseObject.filingsStatus = data.filingsStatus;
        return responseObject;
    } catch (e) {
        console.log(e);
        return responseObject;
    }
}

// 转换备案表格
export function handleFilingTable(table) {
    return table.map((item) => {
        return {
            ...item,
            text: item.error || '',
            reason: item.keyName || '',
            notice: item.suggest || '',
        };
    });
}

// 拉取备案驳回原因表格
export async function fetchFilingRefuseTable() {
    const res = await geyMcWeappIcpEnTranceInfo(false);
    const table = res?.auditData || [];
    try {
        return handleFilingTable(table);
    } catch (e) {
        console.log(e);
        return handleFilingTable([]);
    }
}

// 转换认证状态步骤为可读的授权状态
export function transWeappAuthStatus(status) {
    let handleStatus = 1;
    switch (status) {
        case WeappStatus.AUTH_CHECK:
            handleStatus = 2;
            break;
        case WeappStatus.AUTH_REFUSE:
            handleStatus = 3;
            break;
        case WeappStatus.AUTH_FAIL:
            handleStatus = 4;
            break;
        default:
            break;
    }
    return handleStatus;
}

// 接口获取的状态转换认证状态步骤为可读的授权状态
export function transWeappAuthStatusByApi(status) {
    let handleStatus = 1;
    switch (status) {
        case OpenProcessStatusCode.AUTH_CHECK:
            handleStatus = 2;
            break;
        case OpenProcessStatusCode.AUTH_REFUSE:
            handleStatus = 3;
            break;
        case OpenProcessStatusCode.AUTH_FAIL:
            handleStatus = 4;
            break;
        default:
            break;
    }
    return handleStatus;
}

// 获取认证步骤 存量用户
export function getWeappAuthStepByApi(status) {
    let handleStep = 1;
    switch (status) {
        case OpenProcessStatusCode.AUTH_CHECK:
        case OpenProcessStatusCode.AUTH_REFUSE:
        case OpenProcessStatusCode.AUTH_FAIL:
            break;
        case OpenProcessStatusCode.AUTH_SUCCESS:
            handleStep = 2;
            break;
        default:
            break;
    }
    return handleStep;
}

// 获取备案步骤 存量用户
export function getWeappFilingStep(status) {
    let handleStep = 1;
    switch (status) {
        case OpenProcessStatusCode.FILING_WAIT_CHECK:
        case OpenProcessStatusCode.FILING_REFUSE:
            handleStep = 2;
            break;
        case OpenProcessStatusCode.FILING_WAIT_CHECK_BY_ADMIN:
        case OpenProcessStatusCode.FILING_REFUSE_BY_ADMIN:
            handleStep = 3;
            break;
        case OpenProcessStatusCode.FILING_ADMIN_SUCCESS:
            handleStep = 4;
            break;
        default:
            break;
    }
    return handleStep;
}

// 只转换成功和失败的状态
export function handleAuthCodeByWeappStatus(weappStatus) {
    let status = -1;
    switch (weappStatus) {
        case WeappStatus.AUTH_REFUSE:
            status = OpenProcessStatusCode.AUTH_REFUSE;
            break;
        case WeappStatus.AUTH_FAIL:
            status = OpenProcessStatusCode.AUTH_FAIL;
            break;
        case WeappStatus.AUTH_SUCCESS:
            status = OpenProcessStatusCode.AUTH_SUCCESS;
            break;
        default:
            break;
    }
    return status;
}

// 转换备案状态 失败和成功
export async function handleFilingCodeByWeappStatus(weappStatus) {
    let status = -1;
    switch (weappStatus) {
        case WeappStatus.FILING_REFUSE:
            status = OpenProcessStatusCode.FILING_REFUSE;
            break;
        case WeappStatus.FILING_REFUSE_BY_ADMIN:
            status = OpenProcessStatusCode.FILING_REFUSE_BY_ADMIN;
            break;
        case WeappStatus.FILING_ADMIN_SUCCESS:
            status = OpenProcessStatusCode.FILING_ADMIN_SUCCESS;
            break;
        case WeappStatus.FILING_WAIT_CHECK_BY_ADMIN:
            status = OpenProcessStatusCode.FILING_WAIT_CHECK_BY_ADMIN;
            break;
        default:
            break;
    }
    return status;
}

// 获取开通流程中的认证和备案消息 TODO 这里要考虑兼容存量用户的操作 可能认证和备案同时进行 所以备案状态不能够取weappstatus的状态做判断
// 要单独拉接口获取备案中的
export async function fetchWarnTips(isH5 = true, weappStatus, isOpenMp = false) {
    const tipsCode = [];
    if (isH5) {
        return tipsCode;
    }
    const resIcp = await getMcWeappAuthInfo(isH5);
    // 获取备案状态
    if ([2,3,4,5].includes(resIcp.filingsStatus)) {
        tipsCode.push(resIcp.filingsStatus);
    }
    // 已经开通微诊所了
    if (isOpenMp) {
        // 但不在备案流程中 需要提示4-1日前完成备案
        if (resIcp.filingsStatus !== 0 && ![2,3,4,5,6].includes(resIcp.filingsStatus)) {
            tipsCode.push(OpenProcessStatusCode.FILING_NOTICE);
        }
    }
    // 从状态转换获取限制的拒绝和失败类型的认证消息增量 存量用户不可以用这套
    const authCode = handleAuthCodeByWeappStatus(weappStatus);
    if (authCode > 0) {
        tipsCode.push(authCode);
    }
    // 临期消息查看 提前两周通知客户

    if (resIcp.time && getNearByTime(resIcp.time).days < 14) {
        tipsCode.push(OpenProcessStatusCode.AUTH_NEAR_BY);
        // 已经开通微诊所了
    } else if (isOpenMp && resIcp.status === OpenProcessStatusCode.AUTH_NEED) {
        tipsCode.push(OpenProcessStatusCode.AUTH_NOTICE);
    }
    return tipsCode;

}

// 跳转微信公众平台
export function goToWechat() {
    windowOpen('https://mp.weixin.qq.com');
}

// 小程序发布失败 跳转处理常见弹窗 TODO
export function goToHandleCommonQuestion() {
    windowOpen('https://mp.weixin.qq.com');
}

// 查看认证教程
export function openAuthLead() {
    windowOpen('https://docs.qq.com/doc/DUVJmYmhFZ2liZXdN');
}

// 查看备案教程
export function openFilingLead() {
    windowOpen('https://docs.qq.com/doc/DUXVQUnZtcEp3VldM');
}

// 查看备案驳回常见原因
export function lookReason() {
    windowOpen('https://docs.qq.com/doc/DUUp2dGVFeWNHQ0pL');
}

// 获取当前状态流转 TODO 存量接入
export function getStatus() {
    return 1;
}

// 获取认证状态 TODO 存量接入
export function authCode() {
    return null;
}

// 获取备案状态
export async function filingCode(isH5) {
    const res = await geyMcWeappIcpEnTranceInfo(isH5);
    return res.filingsStatus || 0;
}

// 翻译消息中的状态 0 认证 1 备案 2 发布 3通知
export function transferStatusByMsgStatus(status, type = 0) {
    if (type === 1) {
        if (status === 0) {
            return OpenProcessStatusCode.WARN_NOTICE;
        }
        return status;
    }
    let handleStatus = 1;
    if (type === 0) {
        switch (status) {
            case 3:
                handleStatus = OpenProcessStatusCode.AUTH_REFUSE;
                break;
            case 4:
                handleStatus = OpenProcessStatusCode.AUTH_SUCCESS;
                break;
            case 5:
                handleStatus = OpenProcessStatusCode.AUTH_FAIL;
                break;
            default:
                break;
        }
    }
    if (type === 2) {
        switch (status) {
            case 2:
                handleStatus = OpenProcessStatusCode.CODE_PUBLISHED;
                break;
            case 3:
                handleStatus = OpenProcessStatusCode.CODE_FAIL;
                break;
            default:
                break;
        }
    }

    if (type === 3) {
        handleStatus = OpenProcessStatusCode.WARN_NOTICE;
    }
    return handleStatus;
}

export function handleWeClinicMsgTitle(push, name = '诊所') {
    let typeId = 0;
    if (CmsResourceType.MC_FILING_TOP === push.type) {
        typeId = 1;
    } else if (CmsResourceType.MC_RELEASE_TOP === push.type) {
        typeId = 2;
    }
    const status = this.transferStatusByMsgStatus(push.extendData?.status, typeId);
    const failReason = push.extendData?.failReason || '';
    const WeClinicMsgMap = {
        [OpenProcessStatusCode.CODE_FAIL]: {
            title: `【重要通知】小程序发布失败,失败原因${failReason.replace(/<br>/g, '')},请根据提示进行处理，也可以联系abc客服`,
            color: '#ff9933',
        },
        [OpenProcessStatusCode.CODE_PUBLISHED]: {
            title: `恭喜你微${name}开通成功，完善必要功能开通就可以正常使用了`,
            color: '#1ec761',
        },
        [OpenProcessStatusCode.AUTH_REFUSE]: {
            title: '【重要通知】小程序认证被打回，请前往微信公众平台，进入【公众号设置-账号详情-认证情况】重新填写材料',
            color: '#ff9933',
        },
        [OpenProcessStatusCode.AUTH_FAIL]: {
            title: '【重要通知】小程序认证失败，请前往微信公众平台，进入【公众号设置-账号详情-认证情况】重新填写材料',
            color: '#ff9933',
        },
        [OpenProcessStatusCode.AUTH_SUCCESS]: {
            title: '小程序认证成功，请前往继续完成开通微诊所后续流程',
            color: '#1ec761',
        },
        [OpenProcessStatusCode.FILING_REFUSE]: {
            title: '【重要通知】小程序备案，微信平台初审不通过',
            color: '#ff9933',
        },
        [OpenProcessStatusCode.FILING_REFUSE_BY_ADMIN]: {
            title: '【重要通知】小程序备案，管局审核不通过',
            color: '#ff9933',
        },
        [OpenProcessStatusCode.FILING_WAIT_CHECK_BY_ADMIN]: {
            title: '【重要通知】小程序初审通过，请小程序负责人注意查收工信部核验短信，务必24小时内完成短信核验',
            color: '#ff9933',
        },
        [OpenProcessStatusCode.FILING_ADMIN_SUCCESS]: {
            title: '恭喜你小程序已备案成功',
            color: '#1ec761',
        },
        [OpenProcessStatusCode.WARN_NOTICE]: {
            title: '重要通知',
        },
    };

    return {
        title: WeClinicMsgMap[status]?.title || '',
        color: WeClinicMsgMap[status]?.color || '',
    };
}

// 处理微诊所消息弹窗内容
export function handleWeClinicMsgBody(status = 1, allowGoToWeClinic = false, failReason = '', name = '诊所') {
    const defaultParams = {
        title: '',
        desc: '',
        success: 0, // 0 成功 3 警告 2 错误 1 成功待执行下一步
    };
    const allowGoToWeClinicText = allowGoToWeClinic ? '' : '「总部」';
    const WeClinicMsgMap = {
        [OpenProcessStatusCode.CODE_FAIL]: {
            title: '小程序发布失败',
            desc: `失败原因:${failReason}<br/>请根据提示进行处理，也可以联系ABC客服协助处理`,
            success: 1,
            handleWeappArrangeVisible: true,
        },
        [OpenProcessStatusCode.CODE_PUBLISHED]: {
            title: `恭喜你微${name}开通成功，完善必要功能开通就可以正常使用了`,
            desc: `恭喜你小程序已经发布成功，请前往${allowGoToWeClinicText}微${name}设置完善必要的功能`,
            success: 0,
            handlePerfectVisible: true,
        },
        [OpenProcessStatusCode.AUTH_REFUSE]: {
            title: '小程序认证被打回',
            desc: '请前往微信公众平台，进入【公众号设置-账号详情-认证情况】重新填写材料',
            success: 3,
            goToWechatVisible: true,
        },
        [OpenProcessStatusCode.AUTH_FAIL]: {
            title: '小程序认证失败',
            desc: '请前往微信公众平台，进入【公众号设置-账号详情-认证情况】查看失败原因，并按要求重新提交认证',
            success: 2,
            goToWechatVisible: true,
        },
        [OpenProcessStatusCode.AUTH_SUCCESS]: {
            title: '小程序认证成功，请前往备案',
            desc: `恭喜你小程序已认证成功，请前往${allowGoToWeClinicText}微诊所设置继续完成开通微诊所后续流程`,
            success: 0,
            handleOpenVisible: true,
        },
        [OpenProcessStatusCode.FILING_REFUSE]: {
            title: '小程序备案，微信平台初审不通过',
            desc: '请查看驳回原因，并按照要求修改或补充材料，重新提交审核',
            success: 3,
            handleFilingVisible: true,
        },
        [OpenProcessStatusCode.FILING_REFUSE_BY_ADMIN]: {
            title: '小程序备案，管局审核不通过',
            desc: '请查看驳回原因，并按照要求修改或补充材料，重新提交审核',
            success: 2,
            handleFilingVisible: true,
        },
        [OpenProcessStatusCode.FILING_WAIT_CHECK_BY_ADMIN]: {
            title: '小程序初审通过，请尽快完成短信核验',
            desc: '小程序初审通过，请小程序负责人按要求尽快完成短信核验',
            handleFilingVisible: true,
            success: 1,
        },
        [OpenProcessStatusCode.FILING_ADMIN_SUCCESS]: {
            title: '小程序备案成功',
            desc: '恭喜你小程序已备案成功',
            handleFilingSuccessVisible: true,
            success: 0,
        },
        [OpenProcessStatusCode.WARN_NOTICE]: {
            title: '重要通知',
            success: 3,
            desc: '<span style="color: #ff9933;">腾讯官方要求，所有小程序必须尽快完成认证和备案，否则将被下架处理</span>' +
                '请管理员根据提示，在4月1日之前尽快完成认证和备案',
            handleAuthAndFilingVisible: true,
        },
    };

    return WeClinicMsgMap[status] || defaultParams;
}


// 处理创建小程序按钮开通状态
export function handleCreatedBtnText(status = null, authCode = null) {
    let text = '创建';
    let tip = '';
    let color = '#ff9933';
    if ([
        WeappStatus.AUDIT_REJECT,
        WeappStatus.AUTH_FAIL,
        WeappStatus.FILING_REFUSE,
        WeappStatus.FILING_REFUSE_BY_ADMIN,
    ].includes(authCode)) {
        color = '#ff3366';
    }
    switch (status) {
        case 1:
            text = '待授权';
            tip = OpenProcessStatusCodeTipsText[authCode] || '';
            break;
        case 2:
            text = '待认证';
            break;
        case 3:
            text = '认证中';
            tip = OpenProcessStatusCodeTipsText[authCode] || '';
            break;
        case 4:
            text = '待备案';
            break;
        case 5:
            text = '备案中';
            tip = OpenProcessStatusCodeTipsText[authCode] || '';
            break;
        case 6:
            break;
        default:
            break;
    }
    return {
        text,
        tip,
        color,
    };
}

// 处理home页开通按钮状态
export function handleHomeOpenMicroBtnText(status = null) {
    let text = '立即开通';
    let theme = 'success';
    switch (status) {
        case 1:
            text = '待上传资料';
            theme = 'warning';
            break;
        case 2:
            text = '待授权'; // 授权小程序认证
            theme = 'warning';
            break;
        case 3:
            text = '审核中'; // 授权小程序认证
            theme = 'warning';
            break;
        case 4:
            text = '待认证';
            theme = 'warning';
            break;
        case 5:
            text = '认证中'; // 小程序认证
            theme = 'warning';
            break;
        case 6:
            text = '待备案';
            theme = 'warning';
            break;
        case 7:
            text = '备案中';
            theme = 'warning';
            break;
        case 8:
            text = '发布中';
            theme = 'warning';
            break;
        default:
            break;
    }
    return {
        text,
        theme,
    };
}
