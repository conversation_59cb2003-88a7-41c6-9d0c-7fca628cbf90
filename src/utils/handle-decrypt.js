import {
    isNotNull, clone,
} from 'utils/index';
import DecodeService from '@/service/decode';

/**
 * 解密对象中指定字段的值
 * @param {Object} obj 要解密的对象
 * @param {Array} keys 需要解密的字段数组
 * @returns {Promise<void>}
 */
export async function decryptObjectFields(obj, keys) {
    await Promise.all(
        keys.map(async (key) => {
            if (isNotNull(obj[key])) {
                obj[key] = await DecodeService.aecSignForDec(obj[key]);
            }
        }),
    );
}

export async function handleDecrypt(list = null, typeId, decryptKeys) {
    // 统一的输入验证 !list用于过滤null
    if (!list || (!Array.isArray(list) && typeof list !== 'object')) {
        return list;
    }

    // 处理单个对象
    if (!Array.isArray(list)) {
        const result = clone(list);
        await decryptObjectFields(result, decryptKeys, typeId);
        return result;
    }

    // 处理空数组
    if (list.length === 0) {
        return list;
    }

    // 处理数组：并行解密所有项目的所有字段
    const decryptPromises = list.map(async (item) => {
        const decryptedItem = { ...item };
        await decryptObjectFields(decryptedItem, decryptKeys, typeId);
        return decryptedItem;
    });

    return Promise.all(decryptPromises);
}
