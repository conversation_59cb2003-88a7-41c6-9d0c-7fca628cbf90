// 管理员
export const ROLE_ADMIN_ID = 0;
// 门诊医生
export const ROLE_DOCTOR_ID = 1;
// 护士
export const ROLE_NURSE_ID = 2;
// 检验师
export const ROLE_SURVEYOR_ID = 3;
// 门诊检验师
export const ROLE_OUTPATIENT_SURVEYOR_ID = 117;
// 医院检验师
export const ROLE_HOSPITAL_SURVEYOR_ID = 118;
// 体检检验师
export const ROLE_PHYSICAL_SURVEYOR_ID = 119;
// 理疗师、治疗师
export const ROLE_PHYSIOTHERAPIST_ID = 4;
// 医助
export const ROLE_DOCTOR_ASSIST_ID = 5;
// 其他
export const ROLE_CLERK_ID = 6;
// 视光师
export const ROLE_OPTOMETRY_ID = 9;
// 检查师
export const ROLE_INSPECT_ID = 10;
// 咨询师
export const ROLE_CONSULTANT_ID = 11;
// 住院医生
export const ROLE_HOSPITAL_DOCTOR_ID = 102;
// 住院护士
export const ROLE_HOSPITAL_NURSE_ID = 104;
// 收费员
export const ROLE_CASHIER_ID = 110;
// 发药员
export const ROLE_DISPENSER_ID = 111;
// 库存管理员
export const ROLE_STOCK_MANAGER_ID = 7;
// 总检医生
export const ROLE_PH_CHECK_DOCTOR_ID = 116;
//药师
export const ROLE_PHARMACY_DOCTOR = 207;

// 本地搜索开关配置
// 已切换场景的药品搜索默认值-库存、眼科-门诊收费诊疗项目等，【默认值0表示使用前端搜索】
export const LocalSearchFlag = 0b00000001;
// 未切换场景的药品搜索默认值-诊所门诊收费搜药等，【默认值0表示使用后端搜索】
export const RemoteSearchFlag = 0b00000010;

// 追溯码采集强校验
export const TraceCodeCollectStrictCountFlag = 0x20000;

// 强锁批次开关不能修改
export const BIT_FLAG_LOCK_BATCH_NOT_MODIFY = 0x80000;


export const ROLE_DOCTOR = 114; // 医生
export const ROLE_EXAMINATION_DOCTOR_ID = 112; // 体检医生

/**
 * @desc 是否有医生权限
 * <AUTHOR>
 * @date 2023-11-04 14:03:35
 */
export const ROLE_DOCTOR_LIST = [
    ROLE_DOCTOR,
    ROLE_DOCTOR_ID,
    ROLE_HOSPITAL_DOCTOR_ID,
    ROLE_PHYSIOTHERAPIST_ID,
    ROLE_EXAMINATION_DOCTOR_ID,
];


export const ROLES_ARRAY = Object.freeze([
    {
        id: ROLE_DOCTOR_ID,
        name: '医生',
        sort: 1,
    },
    {
        id: ROLE_NURSE_ID,
        name: '护士',
        sort: 2,
    },
    {
        id: ROLE_SURVEYOR_ID,
        name: '检验师',
        sort: 3,
    },
    {
        id: ROLE_INSPECT_ID,
        name: '检查技师',
        sort: 4,
    },
    {
        id: ROLE_PHYSIOTHERAPIST_ID,
        name: '理疗师',
        sort: 5,
    },
    {
        id: ROLE_DOCTOR_ASSIST_ID,
        name: '医助',
        sort: 6,
    },
    {
        id: ROLE_CLERK_ID,
        name: '其他',
        sort: 99,
    },
    {
        id: ROLE_OPTOMETRY_ID,
        name: '视光师',
        sort: 8,
    },
    {
        id: ROLE_HOSPITAL_DOCTOR_ID,
        name: '住院医生',
        sort: 9,
    },
    {
        id: ROLE_HOSPITAL_NURSE_ID,
        name: '住院护士',
        sort: 10,
    },
    {
        id: ROLE_CASHIER_ID,
        name: '收费员',
        sort: 11,
    },
    {
        id: ROLE_DISPENSER_ID,
        name: '发药员',
        sort: 12,
    },
    {
        id: ROLE_STOCK_MANAGER_ID,
        name: '库管',
        sort: 13,
    },
]);

export const RouterScope = Object.freeze(
    {
        SINGLE_STORE: 1,
        CHAIN_SUB: 2,
        CHAIN_ADMIN: 4,
        OUT_OF_SCOPE: 0,
    },
);

// id	parent_id	name	sort	flag
// 1	0	挂号	0	3
// 2	0	门诊	0	3
// 3	0	收费	0	3
// 4	0	药房	0	3
// 5	0	患者	0	3
// 6	0	统计	0	7
// 7	0	管理	0	7
// 8	0	库存	0	7
// 9	0	检验	0	3
// 10	0	护士站	0	3
// 11	0	会员	0	7
// 12	0	门诊代录	0	3
// 13	0	营销	0	5
// 14	0	微诊所	0	7
// 101	8	药品/物资	0	7
// 102	8	采购	0	7
// 103	8	入库	0	7
// 104	8	出库	0	7
// 105	8	调拨	0	7
// 106	8	盘点	0	7
// 107	8	供应商	0	7
// 108	8	结算申请	0	7
// 109	8	结算审核	0	7
// 201	6	经营统计	0	7
// 202	6	患者统计	0	7
// 203	6	药品统计	0	7
// 204	6	医务统计	0	7
// 205	6	会员统计	0	7
// 1001	201	经营概况	0	7
// 1002	201	门店收入	0	7
// 1003	201	人员收入	0	7
// 1004	201	药品收入	0	7
// 1005	201	检查/检验收入	0	7
// 1006	201	治疗/理疗收入	0	7
// 1007	201	套餐收入	0	7
// 1008	201	挂号统计	0	7
// 1009	201	收银统计	0	7
// 1010	201	收银明细	0	7
// 1011	201	财务报表	0	7
// 1012	201	提成报表	0	7
// 2001	202	患者概述	0	7
// 2002	202	复购统计	0	7
// 2003	202	复购分析	0	7
// 2004	202	患者清单	0	7
// 3001	203	药品概况	0	7
// 3002	203	销售统计	0	7
// 3003	203	采购统计	0	7
// 3004	203	入库统计	0	7
// 3005	203	供应商统计	0	7
// 3006	203	出库统计	0	7
// 3007	203	调拨统计	0	7
// 3008	203	盘盈盘亏	0	7
// 3009	203	进销存清单	0	7
// 3010	203	结算统计	0	7
// 4001	204	门诊清单	0	7
// 4002	204	执行清单	0	7
// 5001	205	会员明细	0	7
// 5002	205	交易流水	0	7

export const MODULE_ID_MAP = Object.freeze({
    globalModule: '0',

    registration: '1',
    outpatient: '2',
    cashier: '3',
    pharmacy: '4',
    patient: '5',
    statistics: '6',
    setting: '7',
    inventory: '8',
    examination: '9', // 检验
    nurse: '10', // 执行
    member: '11',
    marketing: '13', // 营销
    weClinic: '14', // 微诊所
    /**
     * [
     *     {
     *         "id": 1402,
     *         "parentId": 14,
     *         "name": "装修",
     *         "sort": 2
     *     },
     *     {
     *         "id": 1403,
     *         "parentId": 14,
     *         "name": "微商城",
     *         "purchaseItemKey": "micro-mart",
     *         "sort": 3,
     *         "children": [
     *             {
     *                 "id": 140301,
     *                 "parentId": 1403,
     *                 "name": "商品管理",
     *                 "purchaseItemKey": "micro-mart",
     *                 "sort": 1
     *             },
     *             {
     *                 "id": 140302,
     *                 "parentId": 1403,
     *                 "name": "商品评价",
     *                 "purchaseItemKey": "micro-mart",
     *                 "sort": 2
     *             },
     *             {
     *                 "id": 140303,
     *                 "parentId": 1403,
     *                 "name": "订单管理",
     *                 "purchaseItemKey": "micro-mart",
     *                 "sort": 3
     *             },
     *             {
     *                 "id": 140304,
     *                 "parentId": 1403,
     *                 "name": "营销活动",
     *                 "purchaseItemKey": "micro-mart",
     *                 "sort": 4
     *             },
     *             {
     *                 "id": 140305,
     *                 "parentId": 1403,
     *                 "name": "配送管理",
     *                 "purchaseItemKey": "micro-mart",
     *                 "sort": 5
     *             }
     *         ]
     *     },
     *     {
     *         "id": 1404,
     *         "parentId": 14,
     *         "name": "在线咨询",
     *         "purchaseItemKey": "online-consultation",
     *         "sort": 4
     *     },
     *     {
     *         "id": 1405,
     *         "parentId": 14,
     *         "name": "在线续方",
     *         "purchaseItemKey": "online-sustain-prescription",
     *         "sort": 5
     *     },
     *     {
     *         "id": 1406,
     *         "parentId": 14,
     *         "name": "坐诊表",
     *         "sort": 6
     *     },
     *     {
     *         "id": 1407,
     *         "parentId": 14,
     *         "name": "基础设置",
     *         "sort": 7
     *     }
     * ]
     */
    weClinicSubModule: {
        // 装修
        decoration: '1402',
        // 微商城
        shop: '1403',
        // 商品管理
        goodsManage: '140301',
        // 商品评价
        goodsComments: '140302',
        // 订单管理
        orderManage: '140303',
        // 营销活动
        marketing: '140304',
        // 配送管理
        distributionManage: '140305',
        // 在线咨询
        onlineConsultation: '1404',
        // 在线续方
        onlinePrescription: '1405',
        // 坐诊表
        visitTable: '1406',
        // 基础设置
        baseSetting: '1407',
    },
    childHealth: '15',
    inspect: '17', // 检查
    social: '16',
    scrm: '18', // scrm
    oralProcess: '19', // 义齿加工
    patientVisit: '20', // 口腔-随访
    physicalExamination: '23', // 体检系统

    mall: '99999', // 商城

    goods: '51209', // 原101
    purchase: '102',
    goodsIn: '51210', // 原103
    goodsOut: '104',
    goodsTrans: '105',
    goodsCheck: '106',
    goodsProductionOut: '110',
    goodsStatistics: '51215',
    goodsTraceAbility: '116',
    supplier: '107',
    traceAbility: '116',
    settlementApplication: '108',
    settlementReview: '109',
    inventoryStock: '51209', // 库存
    inventoryPurchaseIn: '51210', // 采购入库
    inventoryApplyIn: '51211', // 领用
    inventoryLossOut: '51213', // 报损

    operationStatistics: '201',
    patientStatistics: '202',
    medicineStatistics: '203',
    medicalStatistics: '204',
    memberStatistics: '205',
    crmPatientVisit: '302',
    crmCommunicate: '303',
    crmCardAdmin: '304',

    marketingReferrer: 1301, //  老带新
    marketingMember: 1302, // 会员
    marketingMemberSetting: 130201, // 会员
    marketingCard: 1303, // 卡项
    marketingCardSetting: 130301, // 卡项设置
    marketingDiscount: 1304, // 折扣活动
    marketingCoupon: 1305, // 优惠券
    marketingFullReduction: 1306, // 满减满赠
    marketingIntegral: 1307, //积分
    marketingMessagePush: 1308, //消息推送

    crm: '301',
    crmVisit: '302', //患者随访
    crmMemberCardAdmin: '304', //会员卡管理
    summaryOperationStatistics: 1001,
    clinicIncomeStatistics: 1002,
    employeeIncomeStatistics: 1003,
    medicineIncomeStatistics: 1004,
    examinationIncomeStatistics: 1005,
    treatmentIncomeStatistics: 1006,
    composeIncomeStatistics: 1007,
    registrationStatistics: 1008,
    cashierStatistics: 1009,
    cashierDetailStatistics: 1010,
    financialStatistics: 1011,
    commissionStatistics: 1012,
    settingSub: {
        // 诊所设置
        clinicSetting: '701',
        // 诊疗项目
        treatmentItems: '702',
        // 连环设置
        chainStoreSetting: '733',
        // 定价和税率
        priceAndTaxRate: '703',
        // 预约设置
        reservationSetting: '704',
        // 开出设置
        releaseRuleSetting: '705',
        // 门诊设置
        outpatientSetting: '51706',
        // 收费设置
        chargeSetting: '51712',
        // 执行站设置
        treatmentSetting: '708',
        // 检验设置
        examinationSetting: '709',
        // 检查设置
        inspectSetting: '710',
        // 库存设置
        stockSetting: '51714',
        // 发药/加工/配送
        dispenseSetting: '712',
        // 患者设置
        patientSetting: '713',
        // 模板设置
        templateSetting: '714',
        // 打印设置
        printSetting: '51713',
        // 字段设置
        fieldLayoutSetting: '732',
        // 信息安全
        dataPermissionSetting: '716',
        // 操作日志
        operationLog: '717',
        // 体检系统
        physicalExamination: '718',
        // LIS系统
        lisSystem: '719',
        // 企微管家
        scrm: '720',
        // 聚合支付
        aggregatePayment: '70909',
        // 空中药房
        airPharmacySetting: '51717',
        // 儿保系统
        childProtectSystem: '723',
        // 慢病管理
        chronicDiseaseManage: '724',
        // 区域检查检验中心
        areaInspectionCenter: '725',
        // 叫号设置
        callNumberSetting: '726',
        // 追溯码采集
        traceCodeCollection: '727',
        // 家庭医生
        familyDoctor: '728',
        // 卫建上报
        regulatory: '730',
        // 自助服务机
        selfServiceMachine: '51718',
        // 产品中心
        productCenter: '51719',
    },

    hospitalDoctorStation: '501', // 住院医生站
    hospitalDoctorBed: '50101', // 住院医生站-床位
    hospitalDoctorMedicalPrescription: '50102', // 住院医生站-医嘱病历
    hospitalDoctorConsultation: '50103', // 住院医生站-会诊
    hospitalDoctorStatistics: '50104', // 住院医生站-统计
    hospitalDoctorSettings: '50105', // 住院医生站-管理

    hospitalNurseStation: '502', // 住院护士站
    hospitalNurseBed: '50201', // 住院护士站-床位
    hospitalNursePrescription: '50202', // 住院护士站-医嘱
    hospitalNurseNursing: '50203', // 住院护士站-护理
    hospitalNurseMedicine: '50204', // 住院护士站-药品
    hospitalNurseDaily: '50206', // 住院护士站-日常
    hospitalNurseCost: '50207', // 住院护士站-费用
    // hospitalNurseStatistics: '50208', // 住院护士站-统计
    hospitalNurseStatistics: '50209', // 住院护士站-统计
    hospitalNurseSettings: '50210', // 住院护士站-管理


    hospitalChargeStation: '507', // 收费
    hospitalChargeHospital: '50702', // 收费-住院
    hospitalChargeRegister: '50703', // 收费-入院登记

    hospitalPharmacyStation: '508', // 药房
    hospitalPharmacyHospital: '50802', // 药房-住院

    hospitalExaminationSampleCollection: '50901', // 检验工作站-样本采集
    hospitalExaminationSampleVerify: '50902', // 检验工作站-样本核收
    hospitalExaminationSampleExamination: '50903', // 检验工作站-样本检验

    hospitalInspectRegistration: '51001', // 检查工作站-预约登记
    hospitalInspectDiagnosis: '51002', // 检查工作站-检查诊断
    hospitalInspectSchedule: '51003', // 检查工作站-检查排班
    // 系统设置
    hospitalSettingConfig: '51704', // 机构/人员设置
    hospitalSettingAdviceAndFee: '51705', // 医嘱/费用设置
    hospitalSettingOutpatient: '51706', // 门诊设置
    hospitalSettingLive: '51707', // 住院设置
    hospitalSettingPhysicalExamination: '51708', // 体检系统设置
    hospitalSettingSchedule: '51709', // 排班设置
    hospitalSettingPaper: '51710', // 文书设置
    hospitalSettingInspect: '51711', // 医技设置
    hospitalSettingCashier: '51712', // 收费设置
    hospitalSettingPrint: '51713',// 打印设置
    hospitalSettingInventory: '51714', // 进销存设置
    hospitalSettingOperate: '51715', // 运营管理设置
    hospitalSettingWeClinic: '51716', // 微医院设置
    hospitalSettingAirPharmacy: '51717',// 空中药房
    hospitalSettingService: '51718',// 自助服务机
    hospitalSettingProduct: '51719',// 产品中心
    hospitalSettingRegular: '729', // 合规监管

    // 供应中心
    hospitalSupplyCenter: '519', // 供应中心
    hospitalSupplyCenterGoods: '51901', // 品种档案
    hospitalSupplyCenterPurchase: '51902', // 采购
    hospitalSupplyCenterOfflinePlan: '51903', // 线下采集计划

    // 库存管理
    hospitalInventoryDepartmentOut: '51212', // 科室消耗
    hospitalInventoryOtherOut: '51214', // 其它出库

    commonSetting: '51701', // 通用设置
    outpatientSetting: '51702', // 门诊设置
    hospitalSetting: '51703', // 住院设置

    hospitalOutpatientStation: '505', // 门诊医生站

    hospitalDashboard: 0,

    physicalExaminationOrder: '523', //订单管理
    physicalExaminationOrderTeam: '52302', //团检
    physicalExaminationOrderIndividual: '52301', //个检

    physicalExaminationIntegrated: '522', //综合工作站
    physicalExaminationIntegratedCheckUp: '52201', //今日体检
    physicalExaminationIntegratedKanban: '52202', //今日看板
    physicalExaminationIntegratedReportManage: '52203', //报告管理
    physicalExaminationIntegratedPublicHealthSync: '52204', //公卫上传

    physicalExaminationCashier: '413', //体检收费站

    physicalExaminationAssessment: '521', //总评医生站

    hospitalMedicalRecordManagement: '520', // 医院病案管理

    // 药店管家
    bizPharmacyCharge: '3', // 零售
    bizPharmacyChargeSubModule: {
        retail: '70101', // 零售
        record: '70102', // 零售单
        summary: '70103', // 对账
    },
    bizPharmacyInventory: '8', // 库存
    bizPharmacyInventorySubModule: {
        goods: '51209', //商品
        buyIn: '70202', //采购
        traceAbility: '116',
        buyInSubModule: {
            purchaseClaimGoods: '7020201', // 要货
            purchaseRequireGoods: '51210', // 采购
            purchaseTakeDelivery: '7020203', // 收货
            purchaseAcceptanceCheck: '7020204', // 验收
            purchaseDelivery: '7020208', // 发货
            purchaseGoodsIn: '103', // 入库
            purchaseReturnGoods: '104', // 退货
            purchaseSettlement: '108', // 结算
        },
        apply: '51211', //领用
        breakage: '51213', //报损
        check: '106', //盘点
        supplier: '107', //供应商
        adjustPrice: '70206', //调价
        trans: '70207', //调拨
    },
    bizPharmacyCrm: '11', // 会员
    bizPharmacyMarketing: '13', // 营销
    bizPharmacySocial: '16', // 医保
    bizPharmacyGSP: '706', // GSP
    bizPharmacyGSPSubModule: {
        primaryBusiness: '70601', // 首营
        primaryBusinessSubModule: {
            goods: '7060101', // 商品首营
            supplier: '7060102', // 供应商首营
        },
        check: '70602', // 验收
        checkSubModule: {
            qualified: '7060201', // 验收记录
            unqualified: '7060202', // 验收不合格记录
            transportRecord: '7060203',
            medicalDeviceQualified: '7060204', // 验收记录
            medicalDeviceUnqualified: '7060205', // 验收不合格记录
        },
        store: '70603', // 储存
        storeSubModule: {
            conserve: '7060301', // 药品养护记录
            humiture: '7060302', // 温湿度记录
            environment: '7060303', // 陈列环境检查
            clearFunnel: '7060304', // 清斗记录
            installFunnel: '7060305', // 装斗记录
            conserveMedicalDevice: '7060306', // 器械养护记录
        },
        afterSale: '70604', // 售后
        afterSaleSubModule: {
            adverseReactions: '7060401', // 不良反应记录
            recall: '7060402', // 召回记录
            recover: '7060403', // 追回记录
        },
        person: '70605', // 人员
        personSubModule: {
            health: '7060501',
            train: '7060502',
        },
        specialMedicine: '70606', // 特药
        specialMedicineSubModule: {
            rx: '7060601',// 处方药
            hemp: '7060602',// 含麻醉
            pieceSale: '7060603', // 拆零药品
        },
        suspiciousQuality: '70607', // 质量可疑
        unqualified: '70608', // 不合格
        reportLoss: '70609', // 报损
        destroy: '70610', // 销毁
        destroySubModule: { // 销毁申请
            destroyApply: '7061001,',
            destroyDone: '7061002',
        },
    },
    bizPharmacyStatistics: '6', // 报表
    bizPharmacyStatisticsSubModule: {
        taking: '201', //营业收入
        performance: '206', //业绩统计
        inventory: '203', //库存统计
        market: '211', //营销统计
        medicalInsurance: '208', //医保统计
        extraMoney: '212', //提成报表
    },
    bizPharmacySetting: '7', // 设置
    bizPharmacySettingSubModule: {
        organSetting: '51704', // 机构设置
        storeSetting: '70913', // 门店管理
        scheduleSetting: '51709', // 排班设置
        priceAndTaxRateSetting: '70903', // 定价和税率设置
        chargeSetting: '51712', // 零售设置
        approveSetting: '70907', // 审批设置
        wareHouseSetting: '51714', // 库存设置
        memberSetting: '130201', // 会员设置
        printSetting: '51713', // 打印设置
        aggregatePaymentSetting: '70909', // 聚合支付设置
        productCenterSetting: '51719', // 产品中心设置
        dataPermission: '70911', // 数据权限设置
    },


    //     医院管家
    //     供应中心
    // +---------+------+
    // |module_id|name  |
    // +---------+------+
    // |519      |供应商中心 |
    // |51901    |品种档案  |
    // |51902    |采购    |
    // |51903    |线下采集计划|
    // |107      |供应商   |
    // |108      |结算申请  |
    // |109      |结算审核  |
    // +---------+------+

    //     医院管家
    //     库存管理
    // +---------+----+
    // |module_id|name|
    // +---------+----+
    // |8        |库存管理|
    // |105      |调拨  |
    // |106      |盘点  |
    // |51209    |库存  |
    // |51210    |采购入库|
    // |51211    |领用  |
    // |51212    |科室消耗|
    // |51213    |报损  |
    // |51214    |其他出库|
    // +---------+----+

    //     诊所管家
    //     库存管理
    // +---------+----+
    // |module_id|name|
    // +---------+----+
    // |8        |库存  |
    // |105      |调拨  |
    // |106      |盘点  |
    // |107      |供应商 |
    // |108      |结算申请|
    // |109      |结算审核|
    // |51209    |库存  |
    // |51210    |采购入库|
    // |51211    |领用  |
    // |51213    |报损  |
    // +---------+----+
});

export const GOODS_TYPE_ID_MAP = {
    compose: 8, // 套餐

    medicine: 1,
    westernMedicine: 12, // 西药
    chineseMedicinePieces: 14, // 中药饮片
    chineseMedicineParticles: 15, // 中药颗粒
    chinesePatentMedicine: 16, // 中成药


    material: 2, // 物资
    medicalMaterial: 17, // 医疗器械
    logisticMaterial: 18, // 后勤材料
    fixedAssets: 19,//固定资产

    examination: 20, // 检验
    inspection: 21, // 检查

    treatment: 22, // 治疗
    physiotherapy: 23, // 理疗
    treatmentOther: 24, // 其他项目


    commodity: 7, // 商品
    homemadeProduct: 25, // 自制成品
    healthyMedicine: 26, // 保健药品
    healthyFood: 27, // 保健食品
    otherProducts: 28, // 其他商品
};

// 目录或文件的拥有者类型 - owner_type
export const CATALOGUE_FILE_OWNER_TYPE = Object.freeze({
    CHAIN: 0, // 连锁公用
    CLINIC: 1, // 诊所公用
    PERSONAL: 2, // 个人专用
    DEPARTMENT: 3, // 部门专用
    SYSTEM: 4, // 系统公用
});

export const CATALOGUE_FILE_OWNER_TYPE_NAME_MAP = Object.freeze({
    [CATALOGUE_FILE_OWNER_TYPE.CHAIN]: '全院公用',
    [CATALOGUE_FILE_OWNER_TYPE.CLINIC]: '全院公用',
    [CATALOGUE_FILE_OWNER_TYPE.PERSONAL]: '个人专用',
    [CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT]: '科室公用',
    [CATALOGUE_FILE_OWNER_TYPE.SYSTEM]: '系统公用',
});

export const CATALOGUE_FILE_OWNER_SHORT_TYPE_NAME_MAP = Object.freeze({
    [CATALOGUE_FILE_OWNER_TYPE.CHAIN]: '全院',
    [CATALOGUE_FILE_OWNER_TYPE.CLINIC]: '全院',
    [CATALOGUE_FILE_OWNER_TYPE.PERSONAL]: '个人',
    [CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT]: '科室',
    [CATALOGUE_FILE_OWNER_TYPE.SYSTEM]: '系统',
});

// 目录或文件的场景类型 - type
export const TemplateSceneTypeEnum = Object.freeze({
    /**
     * 住院-医嘱模版
    */
    HOSPITAL_ADVICE: 0,
    /**
     * 住院-病例文书
    */
    HOSPITAL_EMR_MEDICAL_DOC: 1,
    /**
     * 住院-病例词条
    */
    HOSPITAL_EMR_ENTRY: 2,
    /*
     * RIS-检查报告
    */
    RIS_EXAM_REPORT: 3,
    /**
     * 病历
    */
    MEDICAL_RECORD: 4,
    /**
     * 处方
    */
    PRESCRIPTION: 5,
    /**
     * 诊疗
    */
    DIAGNOSIS_TREATMENT: 6,
    /**
     * 检查报告-CT
    */
    INSPECTION_REPORT_CT: 7,
    /**
     * 检查报告-DR
    */
    INSPECTION_REPORT_DR: 8,
    /**
     * 检查报告-CR
    */
    INSPECTION_REPORT_CR: 9,
    /**
     * 检查报告-透视
    */
    INSPECTION_REPORT_TS: 10,
    /**
     * 检查报告-心电图
    */
    INSPECTION_REPORT_XDT: 11,
    /**
     * 检查报告-超声骨密度
    */
    INSPECTION_REPORT_GMD: 12,
    /**
     * 检查报告-试光类
    */
    INSPECTION_REPORT_SGL: 13,
    /**
     * 检查报告-彩超
     */
    INSPECTION_REPORT_CC: 14,
    /**
     * 检查报告-MG
     */
    INSPECTION_REPORT_MG: 25,
    /**
     * 检查报告-MR
    */
    INSPECTION_REPORT_MR: 15,
    /**
     * 检查报告-内窥镜
    */
    INSPECTION_REPORT_WCJ: 16,
    /**
     * 检查报告-B超
    */
    INSPECTION_REPORT_BC: 17,
    /**
     * 检查报告-脑电图
    */
    INSPECTION_REPORT_NDT: 18,
    /**
     * 检查报告-其他
    */
    INSPECTION_REPORT_OTHER: 19,
    /**
     * 儿保-健康报告-看护情况
     */
    CHILD_GROWTH_CARE: 20,
    /**
     * 门诊-病例文书
     */
    OUTPATIENT_EMR_MEDICAL_DOC: 21,
    /**
     * 门诊-病例词条
     */
    OUTPATIENT_EMR_ENTRY: 22,
    /**
     * 检查报告-C13/14
     */
    INSPECTION_REPORT_C13_14: 26,
    /**
     * 检查报告-X射线骨密度
     */
    INSPECTION_REPORT_MDB_X: 27,
});
// Category
export const TemplateCategoryEnum = Object.freeze({
    /**
     * 连锁公用
     */
    PUBLIC_CHAIN: 0,
    /**
     * 诊所公用
     */
    PUBLIC_CLINIC: 1,
    /**
     * 个人专用
     */
    PERSONAL: 2,
    /**
     * 科室公用
     */
    DEPARTMENT: 3,
    /**
     * 经典方剂
     */
    CLASSIC_PRESCRIPTION: 4,
    /**
     * 临床验方
     */
    CLINICAL_PRESCRIPTION: 5,

});

export const CATALOGUE_CATEGORY_TYPE_NAME = Object.freeze({
    [TemplateCategoryEnum.PUBLIC_CHAIN]: '连锁公用',
    [TemplateCategoryEnum.PUBLIC_CLINIC]: '全院公用',
    [TemplateCategoryEnum.PERSONAL]: '个人专用',
    [TemplateCategoryEnum.DEPARTMENT]: '科室公用',
});

export const TemplateCategoryMapToOwnerType = {
    [TemplateCategoryEnum.PERSONAL]: CATALOGUE_FILE_OWNER_TYPE.PERSONAL,
    [TemplateCategoryEnum.DEPARTMENT]: CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT,
    [TemplateCategoryEnum.PUBLIC_CLINIC]: CATALOGUE_FILE_OWNER_TYPE.CLINIC,
    [TemplateCategoryEnum.PUBLIC_CHAIN]: CATALOGUE_FILE_OWNER_TYPE.CHAIN,
};

// 检验检查类型
export const EXAMINATION_TYPE = {
    EXAMINATION: 1,
    INSPECT: 2,
};

export const INSPECT_DEVICE_TYPE = {
    CT: 1,
    DR: 2,
    MR: 9,
    '彩超': 8,
    '心电图': 5,
    '内窥镜': 10,
    '其他': 13,
    '未知': 0,
    1: 'CT',
    2: 'DR',
    9: 'MR',
    8: '彩超',
    5: '心电图',
    10: '内窥镜',
    13: '其他',
    0: '未知',
    6: '超声骨密度',
    MDB: 6, // 超声骨密度
    C13_14: 26,
};
export const InspectDeviceTypeEnum = Object.freeze({
    UN_KNOW: 0,
    CT: 1,
    DR: 2,
    ECG: 5,
    MDB: 6,
    CDFI: 8,
    MR: 9,
    EGD: 10,
    OTHER: 13,
});
export const InspectDeviceLabel = Object.freeze({
    [InspectDeviceTypeEnum.CT]: 'CT',
    [InspectDeviceTypeEnum.DR]: 'DR',
    [InspectDeviceTypeEnum.MR]: 'MR',
    [InspectDeviceTypeEnum.CDFI]: '彩超',
    [InspectDeviceTypeEnum.ECG]: '心电图',
    [InspectDeviceTypeEnum.EGD]: '内窥镜',
    [InspectDeviceTypeEnum.MDB]: '超声骨密度',
    [InspectDeviceTypeEnum.OTHER]: '其他',
    [InspectDeviceTypeEnum.UN_KNOW]: '未知',
});

export const _CURRENT_STOCK_ROOM_ID_ = '_current_stock_room_id_';


export const ABC_CLIENT_TAG = 'ABCClinicDesktop';

// 默认打印配置
export const PRINT_DEFAULT_CONFIG_KEY = {
    NORMAL: 'normal', // 通用的打印默认配置
    PHARMACY: 'pharmacy', // 药店的打印默认配置
};

export const MEDICINE_USAGE = Object.freeze({
    // 口服
    oral: ['口服', '含服', '嚼服', '晨服', '餐前服', '餐中服', '餐后服', '睡前服'],
    // 注射
    injections: ['静脉注射', '肌内注射', '腔内注射', '皮下注射', '皮内注射', '穴位注射', '局部注射', '局部麻醉', '超声透药'],
    // 外用
    external: ['溶媒用', '外用', '滴眼', '滴鼻', '滴耳', '口腔喷入', '鼻腔喷入', '含漱', '涂抹', '塞肛', '直肠给药', '阴道给药'],
    // 输液
    infusion: ['静脉滴注', '直肠滴注', '入壶静滴', '输液冲管', '鼻饲', '膀胱给药'],
    // 雾化
    atomization: ['雾化吸入'],
});


export const PROTOCOL = Object.freeze({
    ABCYUN: 'abcyun:',
    HTTP: 'http:',
    HTTPS: 'https:',
});


export const RELATIVE_STATUS = Object.freeze({
    ACTIVE: 1,
    DEACTIVE: 90,
});


export const BUSINESS_SCOPE_TYPE = Object.freeze({
    OUTPATIENT: 1,
    HOSPITAL: 1 << 1,
});

// 价格展示类型
export const HOSPITAL_PRICE_SCOPE_TYPE = Object.freeze({
    SOURCE: 0, // 原始价格
    SHEBAO: 1, // 社保价格
    SETTLE: 2, // 结算价格
});

// 结算价格类型
export const HOSPITAL_PRICE_SETTLE_TYPE = Object.freeze({
    SOURCE: 0,
    SHEBAO: 1,
});
