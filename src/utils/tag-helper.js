export class TagHelper {
    // 获取待办标签，表示需要操作的
    static TODO_TAG = Object.freeze({
        theme: 'primary',
        variant: 'light-outline',
    });

    // 获取进行中的标签，表示正在进行的，例如：待办、待审核、待收货、待确认
    static ING_TAG = Object.freeze({
        theme: 'primary',
        variant: 'outline',
    });

    // 获取拒绝的标签，表示被拒绝的，例如：不合格、部分合格、已驳回
    static REFUSE_TAG = Object.freeze({
        theme: 'danger',
        variant: 'outline',
    });

    // 获取草稿的标签，表示草稿状态
    static DRAFT_TAG = Object.freeze({
        theme: 'warning',
        variant: 'outline',
    });

    // 获取完成的标签，表示已完成的，例如：已收货、已入库、已生效
    static COMPLETE_TAG = Object.freeze({
        theme: 'success',
        variant: 'outline',
    });

    // 获取取消的标签，表示已取消的，例如：撤回、撤销、取消
    static CANCEL_TAG = Object.freeze({
        theme: 'default',
        variant: 'ghost',
    });
}
