<template>
    <abc-dialog
        v-model="dialogVisible"
        title="样本采集"
        size="xlarge"
        content-styles="height: 476px"
        append-to-body
    >
        <template #title-append>
            <abc-text
                v-if="baseFormData && baseFormData.applySheetNo"
                size="normal"
                theme="gray"
                style="margin-left: 8px;"
            >
                {{ baseFormData.applySheetNo }}
            </abc-text>
        </template>
        <abc-form
            ref="form"
            v-abc-loading="loading"
            label-position="left"
            :label-width="84"
            item-no-margin
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <template v-if="isExamination">
                        <abc-form-item :required="isSampleInfoEditable">
                            <abc-flex justify="space-between">
                                <patient-section
                                    :key="patient.id"
                                    v-model="patient"
                                    :loading="patientLoading"
                                    :disabled="!isExamination"
                                    class="select-patient"
                                    :class="{ 'no-add-patient': !isExamination }"
                                    size="small"
                                    style="width: 366px;"
                                    @change-patient="handlePatientChange"
                                ></patient-section>

                                <abc-select
                                    v-model="baseFormData.operatorId"
                                    placeholder="采集人"
                                    :width="183"
                                >
                                    <abc-option
                                        v-for="option in collectionPersons"
                                        :key="option.employeeId"
                                        :label="option.employeeName"
                                        :value="option.employeeId"
                                    ></abc-option>
                                </abc-select>
                            </abc-flex>
                        </abc-form-item>
                    </template>

                    <template v-else>
                        <abc-form-item>
                            <abc-flex justify="space-between">
                                <abc-autocomplete
                                    ref="abcAutoCompleteRef"
                                    v-model.trim="editApplySheetId"
                                    :width="366"
                                    inner-width="500px"
                                    :delay-time="0"
                                    :async-fetch="true"
                                    :fetch-suggestions="handleSearchApplySheet"
                                    :max-length="20"
                                    :auto-focus-first="false"
                                    :disabled="!!baseFormData.applySheetNo"
                                    placeholder="申请单/门诊号/住院号"
                                    clearable
                                    @clear="handleClearForm"
                                    @enterEvent="handleSelectApplySheet"
                                >
                                    <abc-icon slot="prepend" icon="n-search-line"></abc-icon>
                                    <template slot="suggestion-header">
                                        <div
                                            class="sample-collection_suggestion-title"
                                            :style="{ paddingRight: suggestionExistScrollBar ? '22px' : '12px' }"
                                        >
                                            <span style="flex: 1; margin-right: 5px;">
                                                申请单号
                                            </span>

                                            <span style="width: 60px; margin-right: 5px;">
                                                姓名
                                            </span>

                                            <span style="width: 45px; margin-right: 5px;">
                                                性别
                                            </span>

                                            <span style="width: 100px; margin-right: 5px;">
                                                年龄
                                            </span>

                                            <span style="width: 50px; margin-right: 5px;">
                                                渠道
                                            </span>

                                            <span style="width: 80px;">
                                                申请日期
                                            </span>
                                        </div>
                                    </template>

                                    <template slot="suggestions" slot-scope="props">
                                        <div
                                            class="suggestions-item"
                                            :class="{ selected: props.index == props.currentIndex }"
                                            @click="handleSelectApplySheet(props.suggestion)"
                                        >
                                            <div style="flex: 1; margin-right: 5px;">
                                                {{ props.suggestion.applySheetNo }}
                                            </div>

                                            <div style="width: 60px; margin-right: 5px;">
                                                {{ props.suggestion.name }}
                                            </div>

                                            <div style="width: 45px; margin-right: 5px;">
                                                {{ props.suggestion.sex }}
                                            </div>

                                            <div style="width: 100px; margin-right: 5px;">
                                                {{ props.suggestion.age.year }}岁
                                                {{ props.suggestion.age.month }}月
                                                {{ props.suggestion.age.day }}天
                                            </div>

                                            <div style="width: 50px; margin-right: 5px;">
                                                {{ BusinessType[props.suggestion.businessType] }}
                                            </div>

                                            <div style="width: 80px;">
                                                {{ formatDate(props.suggestion.created) }}
                                            </div>
                                        </div>
                                    </template>
                                </abc-autocomplete>

                                <abc-select
                                    v-model="baseFormData.operatorId"
                                    placeholder="采集人"
                                    :width="183"
                                >
                                    <abc-option
                                        v-for="option in collectionPersons"
                                        :key="option.employeeId"
                                        :label="option.employeeName"
                                        :value="option.employeeId"
                                    ></abc-option>
                                </abc-select>
                            </abc-flex>
                        </abc-form-item>
                    </template>

                    <physical-examination-card
                        v-if="isPhysicalExamination"
                        :pe-sheet-detail="peSheetDetail"
                        :patient="patient"
                        :employee-list="employeeList"
                        style="margin-top: 16px;"
                    ></physical-examination-card>

                    <abc-form-item-group is-excel>
                        <edit-table
                            style="margin-top: 16px;"
                            v-bind.sync="sampleInfo"
                            :editable="isSampleInfoEditable"
                            :business-type="baseFormData.businessType"
                        ></edit-table>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <sample-table
                        :data-source="tableData"
                        :empty-opt="{ label: '暂无数据' }"
                        :show-content-empty="!loading"
                    ></sample-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <abc-flex slot="footer" justify="space-between" align="center">
            <abc-checkbox
                v-model="collectionConfig.continueCollection"
                :style="{ marginRight: '24px' }"
            >
                连续采集
            </abc-checkbox>

            <abc-space>
                <abc-button
                    v-if="isShowCollectBtn"
                    :disabled="confirmDisabled"
                    :loading="confirmLoading"
                    @click="handleCollectionFinish"
                >
                    完成采集
                    <span v-if="isExamination">
                        、核收
                    </span>
                </abc-button>

                <abc-button
                    v-else
                    :disabled="createApplySheetDisabled"
                    :loading="confirmLoading"
                    @click="handleFinishCreateApplySheet"
                >
                    完成开单
                </abc-button>

                <abc-button
                    v-if="!isExamination"
                    type="blank"
                    :disabled="!finishExaminationItems.length"
                    @click="cancelCollectionDialogVisible = true"
                >
                    取消采集
                </abc-button>

                <abc-button
                    type="blank"
                    :disabled="printDisabled"
                    @click="handlePrintBarCode"
                >
                    打印条码
                </abc-button>

                <!-- 打印设置 -->
                <abc-button type="blank" @click="openPrintConfigSettingDialog">
                    <abc-icon icon="n-settings-line"></abc-icon>
                </abc-button>
            </abc-space>
        </abc-flex>

        <!-- 取消采集 -->
        <cancel-collection-dialog
            v-model="cancelCollectionDialogVisible"
            :finish-examination-items="finishExaminationItems"
            @refresh="handleCancelRefresh"
        ></cancel-collection-dialog>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import PatientSection from 'views/layout/patient/patient-section/index.vue';
    import EditTable from './edit-table.vue';
    import { SampleExaminationAPI } from '@/api/hospital/examination/sample-examination';
    import HospitalExaminationAPI from '@/api/hospital/examination';
    import RegistrationAPI from '@/api/registrations/index';
    import {
        CollectionStatus,
        CollectionStatusOptions,
        BusinessType,
        BusinessTypeOptions,
        EnumOperateWay,
    } from '../utils/constant';
    import SampleTable from './sample-table.vue';
    import CancelCollectionDialog from './cancel-collection-dialog.vue';
    import BarcodeDetector from '@/utils/barcode-detector';
    import { formatDate } from '@abc/utils-date';
    import {
        MODULE_ID_MAP, EXAMINATION_TYPE,
    } from 'utils/constants';
    import AutocompleteScrollMixin from '@/views/common/autocomplete-scroll-mixin';
    import PhysicalExaminationCard from
    '@/views-hospital/inspect-diagnosis/components/inspect-report/components/physical-examination-card.vue';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    const PrintExaminationApiModule = () => import('@/printer/print-api/examination');

    export default {
        name: 'SampleCollectionDialog',

        components: {
            PatientSection,
            EditTable,
            SampleTable,
            CancelCollectionDialog,
            PhysicalExaminationCard,
        },

        mixins: [AutocompleteScrollMixin],

        inject: {
            $abcPage: {
                default: () => {},
            },
        },

        props: {
            value: {
                type: Boolean,
                default: false,
            },
            // 样本编号 - 扫码传入
            sampleNo: {
                type: String,
                default: '',
            },
            // 申请单id - 点击列表传入
            applySheetId: {
                type: String,
                default: '',
            },
            // 申请单号 - 扫码传入
            applySheetNo: {
                type: String,
                default: '',
            },
            // 场景： 采集-collect | 检验-examination
            scene: {
                type: String,
                default: 'collect',
            },
            collectWay: {
                type: String,
                default: '',
            },
        },

        data() {
            return {
                BusinessTypeOptions,
                BusinessType,
                loading: false,
                patientLoading: false,
                confirmLoading: false,

                // 非扫码，手动输入检验单id
                editApplySheetId: '',
                baseFormData: {
                    applySheetNo: '',
                    businessType: '',
                    operatorId: '',
                },

                patient: {
                    id: '', // 患者id
                    name: '', // 患者姓名
                    sex: '男', // 患者性别
                    age: {
                        month: null,
                        year: null,
                        day: null,
                    }, // 患者年龄
                    mobile: '', // 患者手机号
                    isAttention: 0, // 患者是否关注公众号
                    wxBindStatus: null, // 微信关注绑定状态
                    wxStatus: null, // 新微信关注绑定状态
                    isMember: null,
                },
                sampleInfo: {
                    no: '',
                    applyDoctor: '',
                    applySubject: '',
                    applyTime: '',
                    diagnosis: [],
                    examinations: {
                        displayText: '',
                        examinationItems: [],
                    },
                    bedNumber: '',
                    wardAreaName: '',
                },
                tableData: [],
                // 采集配置
                collectionConfig: {
                    withPrint: false,
                    continueCollection: false,
                },
                // 采集人
                collectionPersons: [],
                // 取消采集
                cancelCollectionDialogVisible: false,

                // 生成检验单需要的信息的提示
                createExamSheetTip: '',

                // 生成检验单的loading
                createExamSheetLoading: false,

                // 当前使用的检验单详情查询方式
                detailFrom: '',

                peSheetDetail: {},
            };
        },

        computed: {
            ...mapGetters([
                'userInfo',
            ]),
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input',val);
                },
            },
            isExamination() {
                return this.scene === 'examination';
            },
            confirmDisabled() {
                return !this.tableData.filter((item) => !item.finished && item.checked).length;
            },
            printDisabled() {
                return !this.tableData.length;
            },
            createApplySheetDisabled() {
                return !this.patient.id || !this.sampleInfo.examinations.examinationItems.length;
            },
            finishExaminationItems() {
                return this.tableData.filter((item) => item.finished).map((item) => ({
                    ...item,
                    finished: false,
                    checked: false,
                }));
            },

            isCollectByApplySheetId() {
                return this.baseFormData.applySheetNo?.length;
            },

            // 样本信息是否可编辑
            isSampleInfoEditable() {
                return this.isExamination && !this.isCollectByApplySheetId;
            },

            // 显示采集按钮
            isShowCollectBtn() {
                return !this.isExamination || this.tableData.length;
            },

            isPhysicalExamination() {
                return this.baseFormData.businessType === BusinessType.physicalExamination;
            },

            employeeList() {
                return this.$abcPage.$store.state.employeeList;
            },
        },

        watch: {
            // 监听样本编号的变化获取检验单详情（不关闭弹窗继续扫码的情况）
            sampleNo(v) {
                if (!v) {
                    return;
                }
                this.handleGetDetailBySampleNo(this.sampleNo);
            },
            // 监听申请单号的变化获取检验单详情（不关闭弹窗继续扫码的情况）
            applySheetNo(v) {
                if (!v) {
                    return;
                }
                this.handleGetDetailByApplySheetNo(this.applySheetNo);
            },
            value: {
                handler(val) {
                    if (val) {
                        if (this.isExamination) {
                            this.handleClearForm();
                        }
                    }
                },
                immediate: true,
            },
            'baseFormData.applySheetNo': {
                handler(v) {
                    if (!v) {
                        this.editApplySheetId = '';
                        return;
                    }
                    if (this.isExamination) {
                        this.editApplySheetId = v;
                    } else {
                        const type = BusinessType[this.baseFormData.businessType] ?? '';
                        const { patient } = this;
                        this.editApplySheetId =
                            `${patient.name || ''} / ${patient.sex || ''} / ${patient.age?.year ?? 0}岁 / ${type}`;
                    }
                },
                deep: true,
            },
        },

        async mounted() {
            this.loading = true;
            try {
                // 获取采集人员列表并设置默认采集人
                this.collectionPersons = await this.getCollectionRole();
                this.baseFormData.operatorId = this.userInfo.id;

                if (this.isExamination) {
                    // 检验模块启用扫码监听
                    this._cancelListenScanEvent = this.listenScanEvent();
                    this.loading = false;
                    return;
                }

                // 非检验模式下，根据操作方式获取详情
                if (!this.collectWay) {
                    this.loading = false;
                    return;
                }

                // 根据扫码方式和可用参数获取详情
                await this.fetchDetailByCollectWay();
            } catch (e) {
                console.error('初始化样本采集对话框失败:', e);
            } finally {
                this.loading = false;
            }
        },

        beforeDestroy() {
            this._cancelListenScanEvent && this._cancelListenScanEvent();
            if (!this.isExamination) {
                window.removeEventListener('keydown', this.debounceHandleEnterKeyDown);
            }
        },

        methods: {
            /**
             * 根据采集方式和可用参数获取详情
             */
            async fetchDetailByCollectWay() {
                if (this.collectWay === EnumOperateWay.scan) {
                    // 扫码方式
                    if (this.sampleNo) {
                        return this.handleGetDetailBySampleNo(this.sampleNo);
                    }
                    if (this.applySheetNo) {
                        return this.handleGetDetailByApplySheetNo(this.applySheetNo);
                    }
                } else if (this.collectWay === EnumOperateWay.sheet) {
                    // 申请单方式
                    if (this.sampleNo) {
                        return this.handleGetDetailBySampleNo(this.sampleNo, false);
                    }
                    if (this.applySheetId) {
                        return this.handleGetDetailByApplyApplySheetId(this.applySheetId);
                    }
                }
            },

            // 获取采集人
            async getCollectionRole() {
                try {
                    const res = await RegistrationAPI.fetchlistByCondition({
                        moduleIds: [ MODULE_ID_MAP.hospitalExaminationSampleCollection, MODULE_ID_MAP.examination ], // 暂无采集模块id
                    });
                    return res.data.rows || [];
                } catch (error) {
                    console.error('获取采集人错误：',error);
                }
            },

            // 使用申请单id获取检验单详情
            async getExamSheetDetailByApplySheetId(applySheetId) {
                try {
                    const res = await SampleExaminationAPI.getSampleRecordDetailByExamApplySheetId(applySheetId);
                    return res.data;
                } catch (error) {
                    console.log('使用申请单id获取检验单详情错误：',error);
                }
                return {};
            },

            // 使用样本条码获取检验单详情
            async getExamSheetDetailByOrderNo(orderNo) {
                try {
                    const res = await SampleExaminationAPI.getSampleRecordDetailByExamOrderNo(orderNo, 1);
                    return res.data;
                } catch (error) {
                    console.error('使用样本条码获取检验单详情错误：',error);
                }
                return {};
            },

            // 使用申请单号查询申请单id
            async getApplySheetIdByApplyNo(keyword) {
                try {
                    const res = await SampleExaminationAPI.searchExamApplySheet({
                        limit: 5,
                        offset: 0,
                        keyword,
                        type: EXAMINATION_TYPE.EXAMINATION,
                    });
                    return res?.data?.rows?.[0]?.id || '';
                } catch (error) {
                    console.log('使用申请单号搜索申请单错误：',error);
                }
                return '';
            },

            // 处理扫样本编号事件
            async handleGetDetailBySampleNo(sampleNo, isCheckCurrent = true) {
                if (!sampleNo) {
                    console.error('样本编号不存在，无法获取检验单详情');
                    return;
                }
                this.detailFrom = 'sampleNo';
                this.loading = true;

                const detail = await this.getExamSheetDetailByOrderNo(sampleNo);

                // 扫样本编号时选中当前样本
                const setCurSampleNoChecked = (item) => (
                    item.orderNo === this.sampleNo ||
                    item.sampleStatus === CollectionStatus.finish
                );

                const {
                    formData,
                    patient,
                    sampleInfo,
                    sampleList,
                    peSheetDetail,
                } = this.examinationSheetDetailAdapter(
                    detail,
                    isCheckCurrent ? setCurSampleNoChecked : undefined,
                );

                this.baseFormData = formData;
                this.patient = patient;
                this.sampleInfo = sampleInfo;
                this.tableData = sampleList;
                this.peSheetDetail = peSheetDetail;

                this.loading = false;
            },

            // 处理扫申请单号事件
            async handleGetDetailByApplySheetNo(applySheetNo) {
                if (!applySheetNo) {
                    console.error('申请单号不存在，无法获取检验单详情');
                    return;
                }
                this.detailFrom = 'applySheetNo';
                this.loading = true;
                const applySheetId = await this.getApplySheetIdByApplyNo(applySheetNo);
                if (!applySheetId) {
                    this.loading = false;
                    return;
                }
                const detail = await this.getExamSheetDetailByApplySheetId(applySheetId);

                const {
                    formData,
                    patient,
                    sampleInfo,
                    sampleList,
                    peSheetDetail,
                } = this.examinationSheetDetailAdapter(detail);

                this.baseFormData = formData;
                this.patient = patient;
                this.sampleInfo = sampleInfo;
                this.tableData = sampleList;
                this.peSheetDetail = peSheetDetail;

                this.loading = false;
            },

            async handleGetDetailByApplyApplySheetId(applySheetId) {
                if (!applySheetId) {
                    console.error('申请单id不存在，无法获取检验单详情');
                    return;
                }
                this.detailFrom = 'applySheetId';
                this.loading = true;
                const detail = await this.getExamSheetDetailByApplySheetId(applySheetId);

                const {
                    formData,
                    patient,
                    sampleInfo,
                    sampleList,
                    peSheetDetail,
                } = this.examinationSheetDetailAdapter(detail);

                this.baseFormData = formData;
                this.patient = patient;
                this.sampleInfo = sampleInfo;
                this.tableData = sampleList;
                this.peSheetDetail = peSheetDetail;

                this.loading = false;
            },

            // 模糊搜索 - 患者检验单
            async handleSearchApplySheet(keyword, callback) {
                // 只搜索前100条，简化交互
                const { data: { rows = [] } } = await SampleExaminationAPI.searchExamApplySheet({
                    limit: 100,
                    offset: 0,
                    keyword,
                    type: EXAMINATION_TYPE.EXAMINATION,
                });
                const res = (rows || []).map((item) => ({
                    id: item.id,
                    applySheetNo: item.no,
                    name: item.patient?.name,
                    sex: item.patient?.sex || '',
                    age: item.patient?.age || {
                        day: 0,
                        month: 0,
                        year: 0,
                    },
                    created: item.created,
                    businessType: item.businessType,
                }));
                return callback(res);
            },

            // 采集完成
            async handleCollectionFinish() {
                if (this.confirmDisabled) {
                    return;
                }
                this.confirmLoading = true;
                try {
                    const finishSampleStatus = this.isExamination ? CollectionStatus.verified : CollectionStatus.finish;
                    const postData = this.covertFormDataToPostData(finishSampleStatus);
                    await SampleExaminationAPI.saveSampleRecord(postData);
                    this.$emit('confirm');
                } catch (error) {
                    console.log('完成采集错误：',error);
                }

                this.confirmLoading = false;

                // 连续采集，不关闭清空表单
                if (this.collectionConfig.continueCollection) {
                    this.handleClearForm();
                } else {
                    this.dialogVisible = false;
                }
            },

            /**
             * 完成开单
             */
            handleFinishCreateApplySheet() {
                this.$refs.form.validate(async (v) => {
                    if (v) {
                        this.confirmLoading = true;
                        try {
                            const examSheetPostData = {
                                samplerId: this.baseFormData.operatorId,
                                // https://www.tapd.cn/tapd_fe/47644659/story/detail/1147644659001085038
                                // 可配置快速开单是否收费，之前医院都不收费，这里没有开单人的交互，所以这里直接将采集人默认为开单人
                                sellerId: this.baseFormData.operatorId,
                                patient: this.patient,
                                businessType: this.baseFormData.businessType,
                                doctorId: this.sampleInfo.applyDoctor,
                                doctorDepartmentId: this.sampleInfo.applySubject,
                                diagnosisInfos: [{
                                    toothNos: null,
                                    value: this.sampleInfo.diagnosis,
                                }],
                                examinationFormItems: this.sampleInfo.examinations.examinationItems.map((item) => ({
                                    name: item.name,
                                    productId: item.goodsId,
                                    unitCount: 1,
                                    sampleStatus: CollectionStatus.wait, // 创建检验单时直接进入已审核状态
                                })),
                                productSubType: 1, // 检验
                            };

                            const { data } = await HospitalExaminationAPI.createExamSheet(examSheetPostData);
                            const rows = data?.rows || [];
                            // 创建检验申请单后返回根据项目信息生成的样本信息，进行样本采集核收操作
                            this.tableData = (rows || [])?.map((item) => ({
                                ...item,
                                id: item.id,
                                checked: true,
                                finished: false,
                                no: item.orderNo || '',
                                sampleType: item.sampleType || '',
                                tube: item.samplePipe || '',
                                examinationName: item.examinationName || '',
                                status: item.sampleStatus,
                                statusName: CollectionStatusOptions.find((c) => c.value === item.sampleStatus).label,
                                rejectReason: '',
                            }));

                        } catch (error) {
                            console.log('创建检验单错误：',error);
                        }
                        this.confirmLoading = false;
                    }
                });
            },

            // 打印条形码
            async handlePrintBarCode() {
                const printSampleBarcodeList = this.tableData.map((item) => ({
                    ...item,
                    patient: this.patient,
                }));

                const { ExaminationPrintApi } = await PrintExaminationApiModule();
                ExaminationPrintApi.printExaminationTag('', { rows: printSampleBarcodeList });
                // try {
                //     const res = {
                //         templateKey: window.AbcPackages.AbcTemplates.examinationTag,
                //         printConfigKey: ABCPrintConfigKeyMap.examinationTag,
                //         data: {
                //             rows: printSampleBarcodeList,
                //         },
                //     };
                //     AbcPrinter.abcPrint(res);
                //
                // } catch (error) {
                //     console.log('检验样本条码打印错误：',error);
                // }
            },

            // 选择申请单，使用申请单id查询
            handleSelectApplySheet(examItem) {
                this.handleGetDetailByApplyApplySheetId(examItem.id);
            },

            // 清空表单 - 连续采集
            handleClearForm() {
                let businessType;
                if (this.isExamination) {
                    businessType = BusinessType.examination;
                } else {
                    businessType = '';
                }
                // 非扫码，手动输入检验单id
                this.baseFormData = {
                    applySheetNo: '',
                    businessType,
                    operatorId: this.userInfo.id,
                };
                this.patient = {
                    id: '', // 患者id
                    name: '', // 患者姓名
                    sex: '男', // 患者性别
                    age: {
                        month: null,
                        year: null,
                        day: null,
                    }, // 患者年龄
                    mobile: '', // 患者手机号
                    isAttention: 0, // 患者是否关注公众号
                    wxBindStatus: null, // 微信关注绑定状态
                    wxStatus: null, // 新微信关注绑定状态
                    isMember: null,
                };
                this.sampleInfo = {
                    no: '',
                    applyDoctor: '',
                    applySubject: '',
                    applyTime: '',
                    diagnosis: [],
                    examinations: {
                        displayText: '',
                        examinationItems: [],
                    },
                };
                this.tableData = [];
                this.peSheetDetail = {};
            },

            /**
             * 取消采集后刷新当前列表和采集列表
             */
            handleCancelRefresh() {
                const detailFromMap = {
                    'sampleNo': () => this.handleGetDetailBySampleNo(this.sampleNo, false),
                    'applySheetNo': () => this.handleGetDetailByApplySheetNo(this.baseFormData.applySheetNo),
                    'applySheetId': () => this.handleGetDetailByApplyApplySheetId(this.applySheetId),
                };

                if (this.detailFrom) {
                    detailFromMap[this.detailFrom]();
                }

                this.$emit('confirm');
            },

            // 清空患者时清空表单信息 - 只在检验场景
            handlePatientChange(patient) {
                if (!this.isExamination) {
                    return;
                }
                if (!patient.id) {
                    this.handleClearForm();
                }
            },

            // 数据适配
            examinationSheetDetailAdapter(data, setChecked = () => true) {
                const formData = {
                    applySheetNo: data.examinationApplySheetNo,
                    businessType: data.businessType,
                    operatorId: this.userInfo.id,
                };

                const patient = data.patient || {
                    id: '', age: {},
                };

                const sampleInfo = {
                    no: data.patientOrderNumber,
                    applyDoctor: data.doctorName || '',
                    applySubject: data.doctorDepartmentName || '',
                    applyTime: data.created || '',
                    diagnosis: data.diagnosisInfos,
                    examinations: {
                        displayText: data.examinationName,
                        examinationItems: [],
                    },
                    bedNumber: data.bedNumber || '',
                    wardAreaName: data.wardAreaName || '',
                };

                const sampleList = (data?.list || [])?.map((item) => ({
                    ...item,
                    id: item.id,
                    checked: setChecked(item),
                    finished: this.isExamination ? (
                        item.sampleStatus === CollectionStatus.verified
                    ) : [CollectionStatus.finish, CollectionStatus.verified].includes(item.sampleStatus),
                    no: item.orderNo || '',
                    sampleType: item.sampleType || '',
                    tube: item.samplePipe || '',
                    examinationName: item.examinationName || '',
                    status: item.sampleStatus,
                    statusName: CollectionStatusOptions.find((c) => c.value === item.sampleStatus).label,
                    rejectReason: (item.rejectReason?.list || []).join('，') + (item.rejectReason?.remark || ''),
                }));

                const peSheetDetail = data.peSheetSimpleView || {};

                return {
                    formData,
                    patient,
                    sampleInfo,
                    sampleList,
                    peSheetDetail,
                };
            },

            // 提交前的数据格式转换
            covertFormDataToPostData(status) {
                const formData = {
                    list: [],
                    operatorId: this.baseFormData.operatorId,
                    sampleScene: 0, // 0-样本采集 1-样本核收
                };
                formData.list = this.tableData.filter(
                    (item) => !item.finished && item.checked,
                ).map((item) => ({
                    id: item.id,
                    sampleStatus: status,
                }));

                return formData;
            },

            /**
             * 组件内扫码事件监听，在样本检验模块启用
             * @return fn => 取消事件监听的函数 || 空函数
             */
            listenScanEvent() {
                this._barcodeDetector = new BarcodeDetector((_, code) => {
                    this.handleGetDetailByApplySheetNo(code);
                }, true);
                this._barcodeDetector.startDetect();

                return () => this._barcodeDetector.stopDetect();
            },

            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'sample-collection' }).generateDialogAsync({ parent: this });
            },

            formatDate,
        },
    };
</script>

<style lang="scss">
.sample-collection_suggestion-title {
    display: flex;
    justify-content: space-around;
    height: 28px;
    padding: 0 12px 0 10px;
    line-height: 28px;
    color: #626d77;
    background-color: #f5f7fb;
    //border-bottom: 1px solid #dadbe0;
}
</style>>
