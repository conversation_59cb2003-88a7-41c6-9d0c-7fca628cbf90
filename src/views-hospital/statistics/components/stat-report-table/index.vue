<template>
    <div class="pro-report-table-wrapper">
        <div ref="reportTableCardHeader" class="report-table-card-header">
            <abc-flex justify="center" class="report-table-card-header-title">
                {{ reportTitle }}
            </abc-flex>
            <div v-if="reportSubTitle" class="report-table-card-header-subTitle" v-html="reportSubTitle"></div>
            <abc-p v-else :small="true" class="report-table-card-header-subTitle">
                统计时间：{{ dateFilter$?.begin || '-' }} ～ {{ dateFilter$?.end || '-' }}
            </abc-p>
        </div>
        <abc-table-fixed2
            ref="tableFixed2Ref"
            :loading="loading"
            :data="tableData"
            :min-height="minHeight"
            :header="tableHeader"
            :summary-method="handleSummaries"
            :show-total="showTotal"
            :empty-opt="{ label: '暂无数据' }"
            summary-background-color="#ffffff"
            stable-layout
        >
        </abc-table-fixed2>
    </div>
</template>
<script>
    export default {
        name: 'StatReportTable',
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
            table: {
                type: Object,
                default: () => ({}),
            },
            tableHeader: {
                type: Array,
                default: () => [],
            },
            reportTitle: {
                type: String,
                default: '',
            },
            reportSubTitle: {
                type: String,
                default: '',
            },
            dateFilter$: {
                type: Object,
                default: () => {},
            },
            showTotal: {
                type: Boolean,
                default: false,
            },
            maxHeight: {
                type: [Number, String],
                default: '',
            },
            /**
             * Table 的最小高度。合法的值为数字或者单位为 px 的高度。
             */
            minHeight: {
                type: [Number, String],
                default: 'auto',
            },
        },
        computed: {
            tableData() {
                return this.table?.tableData || [];
            },
            summaryData() {
                return this.table?.summaryData || {};
            },
        },
        methods: {
            getHtml() {
                const header = this.$refs.reportTableCardHeader;
                const parentElement = this.$refs.tableFixed2Ref.$el;
                const table = parentElement.querySelector('.table-scroll-header');
                const tableBody = parentElement.querySelector('.table-tbody').getElementsByTagName('tbody')[0];
                table.appendChild(tableBody);
                table.setAttribute('data-type','complex-table');
                return header.outerHTML + table.outerHTML;
            },
            handleSummaries(data, col) {
                return col?.type === 'money' ?
                    parseFloat(this.summaryData[col.prop] || 0).toFixed(2) :
                    this.summaryData[col.prop];
            },
        },
    };
</script>

<style scoped lang="scss">
.pro-report-table-wrapper {
    .abc-fixed-table {
        font-size: 8px;
        border-radius: 0;

        ::v-deep table {
            th {
                font-weight: 400;
                color: var(--abc-color-T1, #000000);
                word-break: normal;
                white-space: normal;
            }

            .is-row-last {
                border-right: 1px solid transparent;
            }

            td {
                div.cell {
                    padding: 2px;
                }

                &:last-child {
                    border-right: 1px solid transparent;
                }
            }

            .tr-summaries {
                font-weight: bold;
            }
        }

        ::v-deep .abc-table__header-wrapper {
            background: var(--abc-color-cp-grey3, #f5f7fb);
            border-radius: 0;
        }

        ::v-deep .abc-table__body-wrapper {
            font-weight: 350;
        }
    }
}
</style>
