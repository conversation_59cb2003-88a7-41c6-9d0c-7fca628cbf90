<template>
    <div>
        <abc-popover
            v-if="isExportLoading"
            ref="exportPopover"
            trigger="hover"
            placement="bottom"
            :disabled="!isExportingLongTime && !isExportWaiting"
            :popper-style="{
                'top': '125px','right': '20px'
            }"
            style="display: inline-flex; vertical-align: top;"
            theme="yellow"
        >
            <template #reference>
                <abc-check-access>
                    <abc-button
                        class="export-btn"
                        variant="ghost"
                        @click="debounceExportClick"
                    >
                        <div class="export-btn-content">
                            <abc-loading small no-cover></abc-loading>
                            <span style=" margin-left: 18px; color: #7a8794;">{{ exportWording }}</span>
                        </div>
                    </abc-button>
                </abc-check-access>
            </template>

            <div v-if="isExportingLongTime">
                <p>可能等待时间较长，你可以稍后回来下载</p>
            </div>
            <div v-if="isExportWaiting">
                <p>前面有{{ queuesNumber }}人排队导出，请稍后下载</p>
            </div>
        </abc-popover>
        <abc-popover
            v-else
            ref="exportOvertimePopover"
            placement="top"
            visible-arrow
            :disabled="exportStatus !== ExportStatus.OVER_DATE"
            style="display: inline-block;"
            trigger="click"
            :value="exportStatus === ExportStatus.OVER_DATE "
            popper-class="stat-toolbar__export-over-date"
            theme="custom"
        >
            <template #reference>
                <abc-check-access>
                    <abc-button
                        class="export-btn"
                        variant="ghost"
                        :icon="canExportDownload ? 'arrow_bottom' : 'n-upload-line'"
                        @click="debounceExportClick"
                    >
                        {{ exportWording }}
                    </abc-button>
                </abc-check-access>
            </template>

            <div v-if="exportStatus === ExportStatus.OVER_DATE" style="margin-top: 4px;" @click="hideOverTimeExportTips">
                <p>文件过期，请重新导出</p>
            </div>
        </abc-popover>
    </div>
</template>

<script>
    import {
        ExportStatus, STAT_EVENT_KEY,
    } from '../constant';
    import { debounce } from 'utils/lodash';
    import { checkMonthRange } from 'views/statistics/common/util';
    import { ExportTableDialog } from 'views/statistics/common/export-table-dialog/index.js';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import { exportTableKeyMap } from 'views/statistics/core/services/export/constant';


    export default {
        name: 'ExportButton',
        props: {
            isMergedExport: {
                type: Boolean,
                default: false,
            },
            limitExportDate: {
                type: Boolean,
                default: true,
            },
            exportTaskType: {
                type: String,
                default: '',
            },
            handleExport: {
                type: Function,
                default: () => {},
            },
            dateFilter: {
                type: Object,
                default() {
                    return {
                        begin: '',
                        end: '',
                        dateRange: ['', ''],
                    };
                },
            },
        },
        data() {
            return {
                ExportStatus,
                exportStatus: ExportStatus.NONE,
                queuesNumber: null, //导出等待人数
                isExportWaiting: false, //是否正在排队
            };
        },
        computed: {
            isExportLoading() {
                return (
                    this.exportStatus === ExportStatus.GENERATING ||
                    this.exportStatus === ExportStatus.GENERATING_LONG_TIME
                );
            },
            isExportingLongTime() {
                return this.exportStatus === ExportStatus.GENERATING_LONG_TIME;
            },
            exportWording() {
                let wording = '导出';
                // eslint-disable-next-line default-case
                switch (this.exportStatus) {
                    case ExportStatus.NONE:
                    case ExportStatus.DOWNLOADED:
                        wording = '导出';
                        break;

                    case ExportStatus.GENERATING:
                    case ExportStatus.GENERATING_LONG_TIME:
                        wording = '导出中';
                        break;
                    case ExportStatus.GENERATED:
                        wording = '点击下载';
                        break;
                    case ExportStatus.FAILED:
                        wording = '导出失败';
                        break;
                }
                return wording;
            },
            canExportDownload() {
                return this.exportStatus === ExportStatus.GENERATED;
            },
        },
        created() {
            this.debounceExportClick = debounce(this.handleExportClick, 200, true);
            if (this.exportTaskType) {
                this.exportService = new ExportService();
                // 检查导出任务
                this.checkExportTask();
            }
        },
        mounted() {
            this.$abcEventBus.$on(STAT_EVENT_KEY.CHECK_EXPORT_TASK, () => {
                this.timer = setTimeout(() => {
                    this.checkExportTask();
                }, 50);
            }, this);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            checkExportTask() {
                this.exportService.checkExportStatus(
                    this.exportTaskType,
                    (data) => {
                        this.exportStatus = ExportStatus.GENERATING;
                        this.queuesNumber = data.queuesNumber;
                        this.isExportWaiting = !!this.queuesNumber;
                        if (this.isExportWaiting) {
                            this.showExportTips();
                        }
                    },
                    () => {
                        this.exportStatus = ExportStatus.GENERATING_LONG_TIME;
                        // 超时后，提示
                        this.showExportTips();
                    },
                    (data) => {
                        this.exportStatus = ExportStatus.GENERATED;
                        // 10s 内，直接下载
                        if (new Date() - new Date(data.created) <= 10000) {
                            this.downloadExport();
                        }
                    },
                    () => {
                        this.exportStatus = ExportStatus.DOWNLOADED;
                    },
                    () => {
                        this.exportStatus = ExportStatus.FAILED;
                    },
                    () => {
                        this.exportStatus = ExportStatus.OVER_DATE;
                    },
                );
            },
            showExportTips() {
                this.$nextTick(() => {
                    if (this.$refs.exportPopover) {
                        this.$refs.exportPopover.doShow();
                    }
                });
            },
            hideOverTimeExportTips() {
                this.$refs.exportOvertimePopover?.doClose();
            },
            async handleExportClick() {
                const {
                    begin, end,
                } = this.dateFilter;

                // 正在导出
                if (this.isExportLoading) {
                    return;
                }

                // 已经生成，点击进入下载
                if (this.canExportDownload) {
                    this.downloadExport();
                    return;
                }

                // 时间范围检查
                if (this.limitExportDate && begin && end && !checkMonthRange(begin, end, 12)) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content:
                            '仅支持导出一年以内的数据，请重新选择时间',
                    });
                    return;
                }

                let reportKeys = null;
                // // 选择导出表格
                if (exportTableKeyMap[this.exportTaskType] && this.isMergedExport) {
                    reportKeys = await this.getSelectedReportKeys();
                    // 没有选择，中断逻辑
                    if (!reportKeys) {
                        return;
                    }
                }
                await this.handleExportHandler(this.exportStatus,reportKeys);
            },

            async handleExportHandler(exportStatus,reportKeys) {
                const res = await this.handleExport(exportStatus,reportKeys);
                if (res) {
                    this.timer = setTimeout(() => {
                        this.checkExportTask();
                    }, 50);
                }
            },

            // 如果返回 null 表示用户点击了取消
            async getSelectedReportKeys() {
                const {
                    begin, end,
                } = this.dateFilter;
                const timeDiff = Math.abs(new Date(end).getTime() - new Date(begin).getTime());

                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

                return new Promise((resolve) => {
                    new ExportTableDialog({
                        taskType: this.exportTaskType,
                        daysDiff,
                        onSuccess: (keys) => {
                            resolve(keys);
                        },
                        onCancel: () => resolve(null),
                    }, 'export-table-dialog').generateDialog();
                });
            },

            async downloadExport() {
                const res = await this.exportService.downloadExport(this.exportTaskType);
                if (res?.status?.code === '4001') {
                    this.exportStatus = ExportStatus.OVER_DATE;
                } else {
                    this.exportStatus = ExportStatus.DOWNLOADED;
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';
@import 'src/styles/mixin';

.export-btn .export-btn-content {
    position: relative;
    width: 60px;

    .loading-spinner {
        position: absolute;
        top: 0;
        left: -9px;
        width: 30px;
        text-align: center;
    }
}

.stat-toolbar__export-over-date {
    padding: 4px;
    color: $S2;
    background-color: orange;
    border-radius: var(--abc-border-radius-small);
    box-shadow: $boxShadowContent;

    p {
        line-height: 20px;
    }
}
</style>
