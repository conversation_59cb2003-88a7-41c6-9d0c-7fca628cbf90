<template>
    <abc-popover
        ref="statFoldedFilter"
        :popper-style="{
            padding: '0', width: '370px'
        }"
        placement="bottom-end"
        theme="white"
        trigger="click"
    >
        <abc-button
            slot="reference"
            :type="filterParamsText ? 'blank' : 'ghost'"
            :style="{
                maxWidth: '200px',
            }"
            icon="n-filter-line"
        >
            <span
                v-abc-title.ellipsis="filterParamsText || '筛选'"
                :style="{
                    display: 'inline-block',
                    color: filterParamsText ? '#000' : '#7a8794',
                    maxWidth: '150px',
                }"
            >
            </span>
        </abc-button>


        <abc-form item-no-margin>
            <abc-flex :gap="16" class="filter-wrapper" wrap="wrap">
                <abc-form-item
                    v-for="(tool) in foldedFilterTools"
                    :key="(selectKey ?? tableKey) + tool.valueKey"
                    :label="formatPlaceholder(tool.placeholder)"
                >
                    <stat-filter-template
                        :tool="{
                            ...tool, width: 160
                        }"
                        :filter-params="foldedFilterParams"
                        :is-folded="true"
                    ></stat-filter-template>
                </abc-form-item>
            </abc-flex>
        </abc-form>
        <div class="filter-footer">
            <abc-button style="margin-left: auto;" @click="handleFilter">
                筛选
            </abc-button>
            <abc-button variant="ghost" @click="handleClear">
                清空
            </abc-button>
        </div>
    </abc-popover>
</template>

<script>
    import StatFilterTemplate
        from '@/views-hospital/statistics/components/stat-toolbar/components/stat-filter-template.vue';
    import { clone } from '@abc/utils';
    import { resolveToFilterParams } from '@/views-hospital/statistics/helper/utils';

    export default {
        name: 'StatFoldedFilterPopover',
        components: { StatFilterTemplate },
        props: {
            filterTools: {
                type: Array,
                default: () => [],
            },
            filterParams: {
                type: Object,
                default: () => ({}),
            },
            tableKey: {
                type: String,
                default: '',
            },
            selectKey: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                filterParamsText: '',

                foldedFilterParams: {},
                realFoldedFilterParams: {}, //排除isNewParams=true的参数 是为了统计目前筛选了的条件个数
                cachedFoldedFilterParams: {}, //为了清空 应该是一个纯净的params
                foldedFilterTools: [],
            };
        },
        computed: {
            filterParamsCount() {
                return Object.entries(this.realFoldedFilterParams).filter(([key, value]) => {
                    // 特殊处理 stockHaveChange 属性，当值为 0 时也视为空
                    if (key === 'stockHaveChange' && !value) return false;

                    // 如果值为 null 或 undefined，返回 false
                    if (value == null) return false;

                    // 如果是数组，检查是否为空数组
                    if (Array.isArray(value)) return value.length > 0;

                    // 如果是对象，递归检查所有属性是否都为空
                    if (typeof value === 'object') {
                        return Object.values(value).some((v) => {
                            if (v == null) return false;
                            if (Array.isArray(v)) return v.length > 0;
                            if (typeof v === 'object') return Object.keys(v).length > 0;
                            if (typeof v === 'string') return v.trim() !== '';
                            return true;
                        });
                    }

                    // 如果是字符串，检查是否为空字符串
                    if (typeof value === 'string') return value.trim() !== '';

                    // 其他类型（数字、布尔值等）都认为是有效的
                    return true;
                }).length;
            },
        },
        watch: {
            'filterTools': {
                handler() {
                    this.init();
                },
                immediate: true,
            },
        },
        methods: {
            init() {
                this.foldedFilterTools = this.filterTools.map((tool) => {
                    return {
                        ...tool,
                        changeMethod: this.handleFilterChange,
                    };
                });
                const baseParams = resolveToFilterParams(this.foldedFilterTools ?? []);
                this.foldedFilterParams = { ...baseParams };
                this.cachedFoldedFilterParams = clone(this.foldedFilterParams);
                Object.keys(baseParams).forEach((key) => {
                    if (key in this.filterParams) {
                        this.foldedFilterParams[key] = this.filterParams[key];
                    }
                });
                this.realFoldedFilterParams = clone(this.foldedFilterParams);
                this.refreshFilterParamsText();
            },
            handleFilterChange(filterValueKey,val,_,isNewParams = false) {
                if (isNewParams) {
                    //比较特殊 如果Cascade选择器tool.needOriValue为true，则需要新添加参数
                    this.$set(this.cachedFoldedFilterParams, filterValueKey, undefined);
                } else {
                    this.$set(this.realFoldedFilterParams, filterValueKey, val);
                }
                this.$set(this.foldedFilterParams, filterValueKey, val);
            },
            handleFilter() {
                this.$emit('filter', this.foldedFilterParams);
                this.refreshFilterParamsText();
                this.close();
            },
            handleClear() {
                this.foldedFilterParams = {
                    ...this.foldedFilterParams,
                    ...clone(this.cachedFoldedFilterParams),
                };
                this.realFoldedFilterParams = clone(this.foldedFilterParams);

                this.$emit('filter', this.foldedFilterParams);
                this.filterParamsText = '';
                this.close();
            },
            close() {
                this.$refs.statFoldedFilter.doClose();
            },
            formatPlaceholder(val) {
                if (typeof val === 'string') {
                    return val;
                }
                if (typeof val === 'object') {
                    return Object.values(val).join('-');
                }
            },
            refreshFilterParamsText() {
                this.filterParamsText = this.filterParamsCount ? `已筛选${this.filterParamsCount}项` : '';
            },
        },
    };
</script>
<style scoped lang="scss">
.filter-wrapper {
    min-height: 100px;
    max-height: 740px;
    padding: 16px;
}

.filter-footer {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    text-align: right;
    border-top: 1px solid $P6;
}
</style>
