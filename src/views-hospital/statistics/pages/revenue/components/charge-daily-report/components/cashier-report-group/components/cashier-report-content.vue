<template>
    <abc-flex
        :gap="24"
        vertical="vertical"
        style="margin: 12px 0;"
        class="statistics__cashier-report-table-content"
    >
        <div v-for="item in details" :key="item.key">
            <custom-table
                v-if="item.type === CASHIER_REPORT_TYPE.CUSTOM_TABLE"
                :detail="item"
            >
            </custom-table>

            <pro-cashier-table
                v-if="item.type === CASHIER_REPORT_TYPE.FIXED_TABLE"
                :module-key="item.key"
                :data="item.value.data ?? []"
                :header="handleHeader(item.value.header ?? [])"
                :total-info="item.value.total ?? {}"
                :summary-method="(data,col)=>handleSummaries(data,col,item.value.summary)"
                :summary-data="item.value.summary ?? {}"
                :show-pagination="false"
            >
            </pro-cashier-table>
        </div>
    </abc-flex>
</template>

<script>
    import {
        CASHIER_REPORT_TYPE,
    } from '../helper/constant';
    import CustomTable from './custom-table/index.vue';
    import { resolveHeader } from '../helper/utils';
    import ProCashierTable
        from './pro-cashier-table';

    export default {
        name: 'CashierReportContent',
        components: {
            ProCashierTable,
            CustomTable,
        },
        props: {
            enableModules: {
                type: Array,
                default: () => [],
            },
            details: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                CASHIER_REPORT_TYPE,
            };
        },
        methods: {
            handleSummaries(data, col,summaryData) {
                return col?.type === 'money' ?
                    parseFloat(summaryData[col.prop] || 0).toFixed(2) :
                    summaryData[col.prop];
            },
            handleHeader(header) {
                return resolveHeader(header);
            },
        },
    };
</script>

<style lang="scss">
.statistics__cashier-report-table-content {
    .abc-fixed-table {
        font-size: 12px;
        border-radius: 0;

        table {
            td {
                height: 28px !important;

                div.cell {
                    padding: 2px 6px;
                }
            }

            th {
                height: 28px !important;
                padding: 0 6px;
                font-weight: bold;
                color: var(--abc-color-T1);
            }
        }

        tbody {
            tr:last-child {
                border-bottom: none;
            }
        }

        .abc-table__header-wrapper {
            background: var(--abc-color-cp-grey3, #f5f7fb) !important;
            border-radius: 0;

            .table-scroll-header {
                width: 100%;
            }
        }
    }

    .custom-report-table {
        width: 100%;
        table-layout: fixed;

        &.no-border-table {
            td {
                border: none;

                &:nth-child(2) {
                    padding-left: 22px;
                }

                &:nth-child(3) {
                    padding-left: 78px;
                }
            }
        }

        td {
            padding: 7px 6px;
            overflow: hidden;
            font-size: 12px;
            line-height: 13px;
            text-overflow: ellipsis;
            white-space: nowrap;
            border: 1px solid $P6;
        }

        .title-td {
            font-size: 12px;
            font-weight: bold;
            line-height: 13px;
            background-color: $P5;
        }

        .report-table-container-type {
            padding-top: 4px;
        }
    }
}
</style>
