<!--医生业绩-->
<template>
    <stat-table-layout
        ref="tableLayout"
        :table-key="tableKey"
        :table-layout-config="tableLayoutConfig"
        :is-need-footer="isNeedFooter"
        :is-enable-pagination="isEnablePagination"
        :handle-table-mounted="handleTableMounted"
        @table-prepared="handleTablePrepared"
        @page-changed="handlePageChanged"
    >
        <template #header>
            <stat-toolbar
                :feature-configs="featureConfigs"
                :print-disabled="printBtnDisabled"
                :filter-params="{
                    tableCategory,...filterParams
                }"
                :table-key="tableKey"
                :handle-export="handleExport"
                @folded-filter="handleFoldedFilter"
                @dimension-change="handleDimensionChange"
                @refresh="restTable"
            >
                <template v-if="tableCategory === TABLE_CATEGORY_ENUM.OUTPATIENT_BILLING" #custom>
                    <abc-popover
                        placement="bottom-start"
                        trigger="click"
                        theme="white"
                        width="370px"
                        :visible-arrow="false"
                        style="display: inline-block;"
                        custom-class="revenue-stat__rule-popover"
                        @hide="revenueRulePopoverVisible = false"
                    >
                        <ul
                            slot="reference"
                            class="revenue-stat__input-filter-selector"
                            :class="{ 'is-show': revenueRulePopoverVisible }"
                            @click="revenueRulePopoverVisible = !revenueRulePopoverVisible"
                        >
                            <li>
                                <abc-icon icon="positive_1" :color="isIncludingRegistration ? '#1EC761' : '#aab4bf'"></abc-icon>
                                <span>{{ $t('registrationFeeName') }}</span>
                            </li>
                            <li>
                                <abc-icon icon="positive_1" :color="isContainOthersWriterAchievement ? '#1EC761' : '#aab4bf'"></abc-icon>
                                <span>{{ `开单业绩 (${isContainOthersWriterAchievement ? '' : '不'}含代录)` }}</span>
                            </li>
                            <li>
                                <abc-icon icon="positive_1" :color="isIncludingWriter ? '#1EC761' : '#aab4bf'"></abc-icon>
                                <span>代录业绩</span>
                            </li>
                        </ul>
                        <abc-form
                            item-block
                            :label-width="70"
                            label-position="left"
                        >
                            <div class="revenue-stat__rule-list-wrapper">
                                <abc-form-item
                                    label="业绩范围"
                                    :custom-label-style="{
                                        color: '#7a8794',
                                    }"
                                    style="margin-bottom: 12px;"
                                >
                                    <abc-checkbox v-model="isIncludingRegistration" @change="loadTableData">
                                        包含{{ $t('registrationFeeName') }}
                                    </abc-checkbox>
                                </abc-form-item>
                                <abc-form-item
                                    label="开单业绩"
                                    style="display: flex; align-items: flex-start; margin-bottom: 12px;"
                                >
                                    <abc-radio-group v-model="isContainOthersWriterAchievement" style="margin-top: -2px;" @change="loadTableData">
                                        <abc-radio :label="1">
                                            <span class="rule-item-text">包含他人代录产生的业绩</span>
                                        </abc-radio>
                                        <p style=" margin: 6px 0 12px 0; font-size: 12px; color: #7a8794;">
                                            统计A的开单业绩时，B为A产生的代录业绩会计入
                                        </p>
                                        <abc-radio :label="0">
                                            <span class="rule-item-text">不含他人代录产生的业绩</span>
                                        </abc-radio>
                                        <p style="margin: 6px 0 0 0; font-size: 12px; color: #7a8794;">
                                            统计A的开单业绩时，B为A产生的代录业绩不会计入
                                        </p>
                                    </abc-radio-group>
                                </abc-form-item>
                                <abc-form-item label="代录业绩" style="margin-bottom: 12px;">
                                    <abc-checkbox v-model="isIncludingWriter" @change="loadTableData">
                                        展示代录人的业绩
                                    </abc-checkbox>
                                </abc-form-item>

                                <div v-if="!isContainOthersWriterAchievement && !isIncludingWriter" class="revenue-stat__rule-err-tips">
                                    <abc-icon icon="Attention" color="#fd9800" size="12"></abc-icon>
                                    未统计代录产生的业绩，合计可能少于门店实际开单金额
                                </div>
                            </div>
                        </abc-form>
                    </abc-popover>
                </template>
            </stat-toolbar>
        </template>
        <template #content>
            <component
                :is="curComponent"
                ref="table"
                :loading="tablePresenter?.loading ?? false"
                :render-config="tablePresenter?.tableRenderConfig"
                :data-list="curDataList"
                :is-enable-pagination="isEnablePagination"
                :summary="tablePresenter.table?.summaryData"
                @sortChange="handleTableSortChange"
            >
            </component>
        </template>
    </stat-table-layout>
</template>

<script>
    import StatTableLayout from '@/views-hospital/statistics/components/stat-table-layout.vue';
    import StatToolbar from '@/views-hospital/statistics/components/stat-toolbar/index.vue';
    import StatNormalTable from '@/views-hospital/statistics/components/stat-normal-table.vue';

    import {
        TABLE_CATEGORY_ENUM,
        tableCategoryOptions, tableInstanceConfigEnum,
    } from '@/views-hospital/statistics/pages/revenue/components/doctor-performance/helper/constant';
    import { resolveToFilterParams } from '@/views-hospital/statistics/helper/utils';
    import usePagination from '@/hooks/abc-ui/use-table-pagination';
    import { mapGetters } from 'vuex';
    import { clone } from '@abc/utils';
    import {
        tableCategoryLabelEnum,
    } from '@/views-hospital/statistics/pages/revenue/components/pharmacy-performance/helper/constant';
    import { AmountDialog } from 'views/statistics/common/amount-dialog';

    export default {
        name: 'DoctorPerformance',
        components: {
            StatTableLayout,StatToolbar,StatNormalTable,
        },
        props: {
            dateFilter: {
                type: Object,
                default: () => ({}),
            },
        },
        setup() {
            const {
                pageParams, setPageSize, changePageIndex, resetPageIndex,setPageTotal,
            } = usePagination();
            return {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            };
        },
        data() {
            return {
                TABLE_CATEGORY_ENUM,
                tableCategory: '',
                tablePresenter: null, //table实例
                isTablePrepared: false, //表格是否准备好 即有header后重新计算paginationLimit
                filterParams: {},
                tableCustomConfig: {}, //table配置 包含filter工具
                revenueRulePopoverVisible: false,
            };
        },
        computed: {
            ...mapGetters(['currentClinic','statisticsIsRevenueKpiIncludingRegistration','statisticsIsContainOthersWriterAchievement','statisticsIsRevenueKpiIncludingWriter']),
            isIncludingRegistration: {
                get() {
                    return this.statisticsIsRevenueKpiIncludingRegistration;
                },

                set(val) {
                    this.$store.dispatch('setIsRevenueKpiIncludingRegistration', val);
                },
            },
            isContainOthersWriterAchievement: {
                get() {
                    return +this.statisticsIsContainOthersWriterAchievement;
                },

                set(val) {
                    this.$store.dispatch('setIsContainOthersWriterAchievement', val);
                },
            },
            isIncludingWriter: {
                get() {
                    return this.statisticsIsRevenueKpiIncludingWriter;
                },

                set(val) {
                    this.$store.dispatch('setIsRevenueKpiIncludingWriter', val);
                },
            },
            clinicId() {
                return this.currentClinic ? this.currentClinic.clinicId : '';
            },
            curComponent() {
                return StatNormalTable;
            },
            tableKey() {
                return this.tableCustomConfig?.tableKey ;
            },
            comDateFilter: {
                get() {
                    return this.dateFilter;
                },
                set(val) {
                    this.$emit('update:dateFilter', val);
                },
            },
            isEnablePagination() {
                return this.tableCustomConfig?.isEnablePagination ?? false;
            },
            isNeedFooter() {
                return this.tableCustomConfig?.isNeedFooter ?? false;
            },
            printBtnDisabled() {
                const data = this.tablePresenter?.tableData;
                return !(Array.isArray(data) && data.length !== 0);
            },
            curDataList() {
                const tableData = this.tablePresenter?.table?.tableData ?? [];
                return !this.isEnablePagination ? tableData : (this.isTablePrepared ? tableData.slice(0,this.pageParams.limit) : []);
            },
            featureConfigs() {
                const options = clone(tableCategoryOptions).filter((item) => !item.hidden);
                const base = [{
                    type: 'selectTabs',
                    valueKey: 'tableCategory',
                    options,
                    changeMethod: this.handleFilterChange,
                }];
                const filterTools = this.tableCustomConfig?.filterTools ?? [];
                const customConfig = filterTools.map((item) => ({
                    ...item,
                    changeMethod: this.handleFilterChange,
                }));
                return {
                    ...this.tableCustomConfig,
                    filterTools: [...base,...customConfig],
                };
            },
            tableLayoutConfig() {
                return {
                    pageParams: this.pageParams,
                    table: this.tablePresenter?.table ?? {},
                };
            },
        },
        created() {
            this.initTable(TABLE_CATEGORY_ENUM.ALL);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        mounted() {
            this.$abcEventBus.$on('amount-setting-success', () => this.loadTableData(), this);
        },
        methods: {
            async handleTableMounted(data = {}) {
                if (this.isEnablePagination) {
                    this.isTablePrepared = false;
                    await this.setPageSize(data.paginationLimit);
                    if (this.tablePresenter) {
                        await this.loadTableData();
                        const { table } = this.tablePresenter;
                        return new Promise((resolve) => {
                            resolve(table);
                        });
                    }
                } else {
                    this.isTablePrepared = true;
                    await this.loadTableData();
                }
            },
            handleTablePrepared(data) {
                this.setPageSize(data.paginationLimit);
                this.isTablePrepared = true;
            },

            //初始化table
            async initTable(val) {
                //获取table实例
                this.tablePresenter = tableInstanceConfigEnum[val] && new tableInstanceConfigEnum[val](this);
                if (!this.tablePresenter) {
                    console.error(tableCategoryLabelEnum[val],'表格不存在');
                    return;
                }
                await this.tablePresenter.init();
                this.tableCategory = val;
            },
            setTableCustomConfig(config,isInit = false) {
                this.tableCustomConfig = config;
                if (isInit) {
                    //根据filterTools处理成真正的请求参数
                    this.filterParams = resolveToFilterParams(this.tableCustomConfig?.filterTools ?? []);
                }
            },
            async loadTableData(isRestPage = true) {
                if (isRestPage) {
                    this.resetPageIndex();
                }
                await this.tablePresenter.loadTableData();
                //获取table-renderConfig
                this.tablePresenter.createRenderConfig();
            },
            async handleFilterChange(filterValueKey,val,needReset = true) {
                if (filterValueKey === 'tableCategory') {
                    if (val === this.tableCategory) return;
                    await this.initTable(val);
                } else {
                    this.filterParams[filterValueKey] = val;
                    if (filterValueKey === 'dateFilter') {
                        this.comDateFilter = val;
                    }
                    if (['clinicId','dateFilter'].includes(filterValueKey)) {
                        this.tablePresenter.refreshFeatureConfig && this.tablePresenter.refreshFeatureConfig();
                    }
                    if (needReset) {
                        await this.restTable();
                    }
                }
            },
            handleFoldedFilter(foldedFilterParams) {
                this.filterParams = {
                    ...this.filterParams,
                    ...foldedFilterParams,
                };
                this.restTable();
            },
            async restTable() {
                this.isTablePrepared = false;
                await this.loadTableData();
                const { table } = this.tablePresenter;
                const { tableHeader = [] } = table ?? {};
                if (tableHeader && tableHeader.length) {
                    this.$refs.tableLayout.recalculatePagePaginationLimit && this.$refs.tableLayout.recalculatePagePaginationLimit();
                }
            },
            handlePageChanged(data) {
                this.changePageIndex(data);
                this.loadTableData(false);
            },
            async handleExport() {
                if (this.tablePresenter.export) {
                    return this.tablePresenter.export();
                }
            },
            handleDimensionChange(val) {
                this.tablePresenter.setDimension && this.tablePresenter.setDimension(val);
                this.tablePresenter.refreshFeatureConfig && this.tablePresenter.refreshFeatureConfig();
            },

            handleTableSortChange(val) {
                if (this.tableCustomConfig.isSupportSort) {
                    this.filterParams.sortConfig = {
                        orderBy: val.orderBy,
                        orderType: val.orderType,
                    };
                    this.loadTableData();
                }
            },

            handleOpenAmountDialog(clinicId) {
                new AmountDialog(
                    {
                        successHandle: () => {
                            this.loadTableData();
                        },
                        clinicId,
                    },
                    'amount-dialog',
                ).generateDialog({ parent: this });
            },
        },
    };
</script>


<style lang="scss">
@import 'src/styles/theme.scss';
@import "src/styles/mixin.scss";

.revenue-stat__input-filter-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 317px;
    height: 32px;
    padding: 0 6px;
    cursor: pointer;
    border: 1px solid $P1;
    border-radius: var(--abc-border-radius-small);

    &:hover {
        border: 1px solid $theme3;
    }

    &.is-show {
        border: 1px solid #0270c9;
        box-shadow: 0 0 0 2px #c3e0fe;
    }

    .abc-icon {
        width: 22px;
        margin-right: 0;
        line-height: 1;
        text-align: center;
    }

    li {
        margin-right: 16px;

        span {
            margin-left: -4px;
        }
    }
}

.revenue-stat__rule-popover {
    display: inline-block;

    .revenue-stat__rule-list-wrapper {
        margin-bottom: 30px;

        .abc-radio {
            height: 48px;
        }

        .rule-item-text {
            color: #000000;
        }
    }
}
</style>
