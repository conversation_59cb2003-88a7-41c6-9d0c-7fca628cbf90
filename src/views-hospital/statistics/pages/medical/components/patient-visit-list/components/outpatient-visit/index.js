import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { isEqual } from 'utils/lodash';
import { formatAge } from '@/utils';
import API from '@/views-hospital/statistics/api/selection';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import StatAPI from 'api/stat';
import infectiousDiseases from '@/assets/configure/infectious-diseases.js';
import SettingAPI from 'api/settings';
import { formatObstetricalHistory2Str } from 'src/views/outpatient/common/medical-record/utils';
import { freq2Han } from '@/filters';
import { AstEnum } from '@/views-hospital/medical-prescription/utils/constants.js';

export default class OutpatientVisitStatementReportTable extends StatBaseTable {
    constructor(...args) {
        super(...args);
        this.departmentList = [];

        this.renderTypeMap = {
            'diagnosis': 'diagnosisRender',
            'obstetricalHistory': 'obstetricalHistoryRender',
            'prescription': 'prescriptionRender',
        };
    }
    // 由产品.设计提供的静态配置, 开发只能修改key、renderType
    static staticConfig = {
        'hasInnerBorder': true,
        'hasHeaderBorder': true,
        'list': [{
            'label': '门诊日期',
            'key': 'created',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '诊号',
            'key': 'orderNo',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '患者姓名',
            'key': 'patientName',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '性别',
            'key': 'patientSex',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '78px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '年龄',
            'key': 'patientAge',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '56px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '身份证号',
            'key': 'idNumber',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '挂号收费员',
            'key': 'registrationChargeBy',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '110px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '门诊收费员',
            'key': 'outpatientChargeBy',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '110px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '随访人',
            'key': 'revisitExecutor',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '84px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '联系电话',
            'key': 'patientMobile',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '住址',
            'key': 'address',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '职业',
            'key': 'profession',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '体重',
            'key': 'patientWeight',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '家长名',
            'key': 'parentName',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '工作单位',
            'key': 'company',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '137px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '初诊/复诊',
            'key': 'diagnoseStatus',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '患者来源',
            'key': 'patientSource',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '医生',
            'key': 'doctorName',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '医生科室',
            'key': 'departmentName',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '医助',
            'key': 'medicalAssistance',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '主诉',
            'key': 'chiefComplaint',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '个人史',
            'key': 'personalHistory',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '现病史',
            'key': 'presentHistory',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '既往史',
            'key': 'pastHistory',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '月经婚育史',
            'key': 'obstetricalHistory',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '流行病史',
            'key': 'epidemiologicalHistory',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '体格检查(主要症状与体征)',
            'key': 'physicalExamination',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '望闻切诊',
            'key': 'chineseExamination',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '脉象',
            'key': 'pulse',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '舌象',
            'key': 'tongue',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '口腔检查',
            'key': 'oralExaminations',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '辅助检查',
            'key': 'auxiliaryExaminations',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '辨证论治',
            'key': 'syndromeTreatment',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '诊断',
            'key': 'diagnosis',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '辨证',
            'key': 'syndrome',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '治法',
            'key': 'therapy',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '诊疗项目',
            'key': 'outpatientProductItem',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
            customRender: (h, row) => {
                const { outpatientProductItem } = row;
                if (Array.isArray(outpatientProductItem) && outpatientProductItem.length > 0) {
                    let str = '';
                    outpatientProductItem.forEach((item,index) => {
                        str += `${item.tooth}   ${item.name} ${item.unitCount}*${item.unit}`;
                        if (index !== outpatientProductItem.length - 1) {
                            str += '，';
                        }
                    });
                    return (
                        <AbcPopover
                            trigger="hover"
                            placement="top-start"
                            width="auto"
                            theme="yellow"
                            popperStyle={{ padding: '16px' }}
                        >
                            <abc-table-cell slot="reference">
                                <span class="ellipsis">{str}</span>
                            </abc-table-cell>
                            <abc-flex style={{ width: '240px' }} vertical gap={8}>
                                {
                                    outpatientProductItem.map((item) => (
                                        <abc-flex vertical>
                                            <abc-flex justify="space-between">
                                                <abc-flex vertical>
                                                    <abc-text>{item.tooth}</abc-text>
                                                    <abc-text>{item.name}</abc-text>
                                                </abc-flex>
                                                <abc-flex>
                                                    <abc-text>{item.unitCount}</abc-text>
                                                    <abc-text>{item.unit}</abc-text>
                                                </abc-flex>
                                            </abc-flex>
                                            <abc-text theme="gray" class="ellipsis" title={item.remark}
                                                style={{ width: '240px' }}>{item.remark}</abc-text>
                                        </abc-flex>
                                    ))
                                }
                            </abc-flex>
                        </AbcPopover>
                    );
                }
                return <abc-table-cell>{outpatientProductItem}</abc-table-cell>;
            },
        },{
            'label': '处方',
            'key': 'prescription',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '医嘱事项',
            'key': 'doctorAdvice',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '费用',
            'key': 'fee',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '费别',
            'key': 'shebaoChargeType',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '就诊推荐',
            'key': 'visitSourceName',
            'description': '用于患者每次到店原因',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '诊断日期',
            'key': 'diagnosisTime',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '处置',
            'key': 'disposals',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '200px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '就诊备注',
            'key': 'visitSourceRemark',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '体温',
            'key': 'temperature',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '血压',
            'key': 'bloodPressure',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '血糖',
            'key': 'bloodSugar',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '发病日期',
            'key': 'symptomTime',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '患者备注',
            'key': 'patientRemark',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        },{
            'label': '民族',
            'key': 'ethnicity',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '90px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        }],
    };
    static decryptKeys = [
        'patientName', 'patientSn', 'idNumber', 'patientMobile', 'address', 'company', 'diagnosis', 'chiefComplaint', 'pastHistory','prescriptionWesternForms','prescriptionExternalTreatForms','prescriptionChineseForms','prescriptionInfusionForms','prescriptionGlassesForms',
    ];
    createRenderList() {
        return {
            diagnosisRender: (h, row) => {
                let isShowInfectiousDiseaseWarn = false; // 是否显示传染病警告
                const result = {
                    classA: [], classB: [], classC: [],
                }; // 诊断中的传染病列表
                const diagnosisList = row.diagnosis.toString().split('，'); // 诊断中的疾病列表
                const infectedDiseaseGrade = Object.keys(infectiousDiseases);
                infectedDiseaseGrade.forEach((item) => {
                    infectiousDiseases[item].forEach((ele) => {
                        const resDiagnosis = diagnosisList.find((diagnosis) => diagnosis === ele);
                        if (resDiagnosis) {
                            isShowInfectiousDiseaseWarn = true;
                            result[item].push(resDiagnosis);
                        }
                    });
                });

                const directives = [
                    {
                        name: 'abc-title',
                        value: row.diagnosis.toString().trim(),
                        modifiers: {
                            ellipsis: true,
                        },
                    },
                ];
                if (isShowInfectiousDiseaseWarn) {
                    const gradeFilter = (val) => {
                        if (val === 'classA') return '甲类';
                        if (val === 'classB') return '乙类';
                        return '丙类';
                    };
                    return <abc-table-cell>
                        <abc-flex align={'center'} gap={8}>
                            <abc-popover
                                trigger="hover"
                                theme="yellow"
                                placeholder={'传染病'}
                            >
                                <abc-flex slot={'reference'} align={'center'}>
                                    <abc-icon
                                        icon="Attention"
                                        color="$Y2"
                                        size="16"
                                    ></abc-icon>
                                </abc-flex>
                                <abc-flex vertical gap={4}>
                                    <abc-text theme={'gray'}>诊断中含有三类传染病诊断</abc-text>
                                    {
                                        Object.keys(result).map((item) => {
                                            if (!result[item].length) return null;
                                            return (
                                                <abc-flex key={item} gap={2}>
                                                    <abc-text theme={'gray'}>{gradeFilter(item)}：</abc-text>
                                                    <div>
                                                        {
                                                            result[item].map((ele, index) => {
                                                                return (
                                                                    <div key={index}>
                                                                        <span>{ele}</span>
                                                                    </div>
                                                                );
                                                            })
                                                        }
                                                    </div>
                                                </abc-flex>
                                            );
                                        })
                                    }
                                </abc-flex>
                            </abc-popover>
                            <div style={{ flex: 1 }} {...{ directives }}></div>
                        </abc-flex>
                    </abc-table-cell>;
                }
                return <abc-table-cell>
                    <div { ...{ directives } }></div>
                </abc-table-cell>;
            },
            obstetricalHistoryRender: (h, row) => {
                const displayValue = row.obstetricalHistory && row.obstetricalHistory !== '-' ? row.obstetricalHistory : '';
                const htmlText = formatObstetricalHistory2Str(displayValue);
                return displayValue && displayValue !== '-' ?
                    <abc-table-cell>
                        <div domPropsInnerHTML={htmlText}></div>
                    </abc-table-cell> : '-';
            },
            prescriptionRender: (h, row) => {
                const text = this.formatPrescriptionOneLine(row);
                return <abc-popover
                    trigger="hover"
                    placement="top-start"
                    width="auto"
                    theme="yellow"
                    disabled={!text}
                >
                    <abc-table-cell slot="reference">
                        <abc-text class="ellipsis">{text}</abc-text>
                    </abc-table-cell>
                    <div class="ms-list-popper">
                        {row.prescriptionWesternForms &&
                            row.prescriptionWesternForms.map((EN, index) => {
                                let title = null;
                                if (
                                    EN.prescriptionFormItems &&
                                    EN.prescriptionFormItems.length > 0
                                ) {
                                    title = (
                                        <div class="title">
                                            西药处方{index + 1}
                                        </div>
                                    );
                                }

                                const content = (
                                    <ul class="content">
                                        {EN.prescriptionFormItems &&
                                            EN.prescriptionFormItems.map(
                                                (it, index) => {
                                                    return (
                                                        <li key={index}>
                                                            <span
                                                                class="ellipsis"
                                                                style="width: 200px;"
                                                            >
                                                                {it.name ?
                                                                    it.name :
                                                                    it.medicine_cadn}
                                                            </span>
                                                            <span
                                                                class="ellipsis"
                                                                style="width: 160px;"
                                                            >
                                                                {it.usage ?
                                                                    `${it.usage}，` :
                                                                    ''}
                                                                {it.freq ?
                                                                    `${it.freq}，` :
                                                                    ''}
                                                                {it.dosage +
                                                                    it.dosage_unit}
                                                            </span>
                                                            <span>
                                                                {it.unit_count +
                                                                it.unit}
                                                            </span>
                                                        </li>
                                                    );
                                                },
                                            )}
                                    </ul>
                                );
                                return (
                                    <div key={EN.id} class="prescription">
                                        {title}
                                        {content}
                                    </div>
                                );
                            })}
                        {row.prescriptionInfusionForms &&
                            row.prescriptionInfusionForms.map((PN, index) => {
                                let title = null;
                                if (
                                    PN.prescriptionFormItems &&
                                    PN.prescriptionFormItems.length > 0
                                ) {
                                    title = (
                                        <div class="title">
                                            输液处方{index + 1}
                                        </div>
                                    );
                                }

                                const content = (
                                    <ul class="content">
                                        {PN.prescriptionFormItems.map(
                                            (it, index) => {
                                                return (
                                                    <li key={index}>
                                                        <span
                                                            class="ellipsis"
                                                            style="width: 200px;"
                                                        >
                                                            {it.name ?
                                                                it.name :
                                                                it.medicine_cadn}
                                                        </span>
                                                        <span
                                                            class="ellipsis"
                                                            style="width: 160px;"
                                                        >
                                                            {it.usage ?
                                                                `${it.usage}，` :
                                                                ''}
                                                            {it.freq ?
                                                                `${it.freq}，` :
                                                                ''}
                                                            {it.dosage +
                                                                it.dosage_unit}
                                                        </span>
                                                        <span>
                                                            {it.unit_count +
                                                            it.unit}
                                                        </span>
                                                    </li>
                                                );
                                            },
                                        )}
                                    </ul>
                                );
                                return (
                                    <div key={PN.id} class="prescription">
                                        {title}
                                        {content}
                                    </div>
                                );
                            })}
                        {row.prescriptionChineseForms &&
                            row.prescriptionChineseForms.map((CN, index) => {
                                let title = null;
                                if (
                                    CN.prescriptionFormItems &&
                                    CN.prescriptionFormItems.length > 0
                                ) {
                                    title = (
                                        <div class="title">
                                            中药处方{index + 1}
                                        </div>
                                    );
                                }

                                const content = (
                                    <ul
                                        class="content"
                                        style="white-space: normal;"
                                    >
                                        {CN.prescriptionFormItems.map(
                                            (it, index) => {
                                                const style = `width: 36px; font-size: 14px; margin-right: ${
                                                    (index + 1) % 4 !== 0 ?
                                                        '9px' :
                                                        ''
                                                }`;
                                                return (
                                                    <li
                                                        key={index}
                                                        style="display: inline-block;"
                                                    >
                                                        <span
                                                            class="ellipsis"
                                                            style="width: 44px;"
                                                        >
                                                            {it.name ?
                                                                it.name :
                                                                it.medicine_cadn}
                                                        </span>
                                                        <span
                                                            class="ellipsis"
                                                            style="width: 36px; text-align: center; margin-right: 4px;"
                                                        >
                                                            {(it.unit_count ||
                                                                '0') +
                                                            (it.unit ||
                                                                'g')}
                                                        </span>
                                                        <span
                                                            class="text"
                                                            style={style}
                                                        >
                                                            {it.special_requirement ?
                                                                `(${it.special_requirement})` :
                                                                ''}
                                                        </span>
                                                    </li>
                                                );
                                            },
                                        )}
                                    </ul>
                                );

                                let usage = null;
                                if (
                                    CN.requirement ||
                                    CN.freq ||
                                    CN.dailyDosage ||
                                    CN.usageLevel
                                ) {
                                    usage = (
                                        <p
                                            class="layout__usage-tip"
                                            style="margin-bottom:12px"
                                        >
                                            <strong>[用法用量]</strong>
                                            {CN.doseCount ?
                                                `共${CN.doseCount}剂，` :
                                                ''}
                                            {CN.requirement || ''}
                                            {CN.dailyDosage || ''}
                                            {CN.freq ? `，${CN.freq}` : ''}
                                            {CN.usageLevel ? `，${CN.usageLevel}` : ''}
                                        </p>
                                    );
                                }

                                return (
                                    <div key={CN.id} class="prescription">
                                        {title}
                                        {content}
                                        {usage}
                                    </div>
                                );
                            })}
                        {row.prescriptionExternalTreatForms &&
                            row.prescriptionExternalTreatForms.map(
                                (external, index) => {
                                    let title = null;
                                    if (
                                        external.prescriptionFormItems &&
                                        external.prescriptionFormItems.length >
                                        0
                                    ) {
                                        title = (
                                            <div class="title">
                                                外治处方{index + 1}
                                            </div>
                                        );
                                    }

                                    const content = (
                                        <ul
                                            class="content"
                                            style="white-space: normal;"
                                        >
                                            {external.prescriptionFormItems.map(
                                                (it, index,arr) => {
                                                    const isLast = index === arr.length - 1;
                                                    const style = `width: 100%; font-size: 12px; margin-right: ${
                                                        (index + 1) % 4 !== 0 ?
                                                            '9px' :
                                                            ''
                                                    }`;
                                                    const liStyle = `flex-direction: column;  border-bottom: ${
                                                        isLast ?
                                                            'none' :
                                                            '1px dashed #e6eaee'
                                                    };margin-bottom:${isLast ? '0' : '4px'};padding-bottom:${isLast ? '0' : '4px'}`;
                                                    //穴位
                                                    let acupoints = '';
                                                    it.acupointsList?.forEach((formItem) => {
                                                        acupoints += `[${formItem.position ?? '-'}]${formItem.name ?? '-'}；`;
                                                    });
                                                    //药品
                                                    let externalGoodsItems = '';
                                                    it.externalGoodsItemsList?.forEach((formItem) => {
                                                        externalGoodsItems += `${(formItem.name || formItem.medicineCadn) ?? '-'} ${formItem.unitCount}${formItem.unit}；`;
                                                    });
                                                    return (
                                                        <li
                                                            key={index}
                                                            style={liStyle}
                                                        >
                                                            <span
                                                                class="ellipsis"
                                                                style={style}
                                                            >
                                                                {it.name}&nbsp;&nbsp;
                                                                {it.unit_count}{it.unit}
                                                            </span>
                                                            <span
                                                                style="max-width:'400px';font-size:'12px'"
                                                            >
                                                                {externalGoodsItems}
                                                            </span>
                                                            <span
                                                                style="max-width:'400px';font-size:'12px'"
                                                            >
                                                                {acupoints}
                                                            </span>
                                                        </li>
                                                    );
                                                },
                                            )}
                                        </ul>
                                    );

                                    return (
                                        <div key={external.id} class="prescription">
                                            {title}
                                            {content}
                                        </div>
                                    );
                                },
                            )}
                        {
                            row.prescriptionGlassesForms && row.prescriptionGlassesForms.glassesParams ? (
                                <div class="prescription">
                                    <div class="title">配镜处方</div>
                                    <div class={`glasses-form-wrapper ${row.prescriptionGlassesForms.glassesType ? 'is-contact' : ''}`} >
                                        <div class="glasses-form-wrapper-content">
                                            {
                                                row.prescriptionGlassesForms.glassesParams.map((item) => {
                                                    return <div class={`glasses-form-item ${item.name === '基底' && (item.leftEyeValue || item.rightEyeValue) ? 'is-base' : ''}`}>
                                                        <span class={`${!row.prescriptionGlassesForms.glassesType ? '' : 'is-frame'}`} >{item.name}</span>
                                                        <span class={`${!row.prescriptionGlassesForms.glassesType ? '' : 'is-frame'}`} title={item.rightEyeValue}>{item.rightEyeValue}{item.rightEyeValue ? item.unit : ''}</span>
                                                        <span class={`${!row.prescriptionGlassesForms.glassesType ? '' : 'is-frame'}`} title={item.leftEyeValue}>{item.leftEyeValue}{item.leftEyeValue ? item.unit : ''}</span>
                                                    </div>;
                                                })
                                            }
                                        </div>
                                        <div class="glasses-form-wrapper-footer">
                                            <span class="ellipsis"
                                                style={{
                                                    maxWidth: `${row.prescriptionGlassesForms.glassesParams.length * 48 - 150}px`,
                                                }}>备注：{row.prescriptionGlassesForms.requirement}</span>
                                            <span class="ellipsis">验光师： {row.prescriptionGlassesForms.optometristName}</span>
                                        </div>
                                    </div>
                                </div>
                            ) : null
                        }
                    </div>
                </abc-popover>;
            },
        };
    }

    async refreshFeatureConfig() {
        await this.fetchEmployeeSelection();
        await this.fetchClinicDepartments();
        await super.refreshFeatureConfig();
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin', 'subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'outpatient-list',
            tableKey: 'outpatient-visit-statement',
            isEnablePagination: true,
            isEnableSetting: true,
            isEnableExport: !this.view?.isForbiddenExport,
            settingOptions: [
                {
                    text: '设置展示字段',
                    value: '',
                    isOpen: false,
                    groupName: '',
                },
            ],
            filterTools: [
                {
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    initData: this.view.comDateFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: 'patientSelector',
                    valueKey: 'patientId',
                    placeholder: '搜索患者',
                    clinicId: '',
                    width: 120,
                },
                {
                    type: 'select',
                    valueKey: 'employeeId',
                    valueWithLabel: true,
                    clearable: true,
                    placeholder: '医生',
                    withSearch: true,
                    width: 120,
                    options: this.employeeList || [],
                    isHidden: permission.isChainAdmin,
                },
                {
                    type: 'select',
                    valueKey: 'departmentId',
                    clearable: true,
                    placeholder: '科室',
                    withSearch: true,
                    width: 120,
                    options: this.departmentList,
                },
                {
                    type: 'checkbox',
                    valueKey: 'isInfectiousDiseases',
                    checkboxType: 'number',
                    label: '仅看传染病患者',
                },
            ].filter((item) => !item.isHidden),
        };
    }

    createFetchParams(filter = {}) {
        const {
            dateFilter: {
                begin, end,
            } = {},
            clinicId = '',
            employeeId = '',
            patientId = '',
            isInfectiousDiseases = 0,
            departmentId,
        } = this.view.filterParams;
        const employees = employeeId ? [
            {
                id: employeeId.split('-valueWithLabel-')[0],
                name: employeeId.split('-valueWithLabel-')[1],
            },
        ] : [];
        const params = {
            clinicId,
            beginDate: begin,
            endDate: end,
            scope: this.scope,
            employees: filter.isExport ? employees : JSON.stringify(employees),
            patientId,
            isInfectiousDiseases,
            pageIndex: this.view.pageParams.pageIndex,
            pageSize: this.view.pageParams.pageSize,
            departmentId,
            e: 1,
        };
        if (filter.isExport) {
            const { permission } = usePermission('enablePatientMobile');
            Object.assign(params, {
                enablePatientMobile: permission.enablePatientMobile,
            });
        }
        return params;
    }

    async loadTableData() {
        const params = this.createFetchParams();
        this.loading = true;
        try {
            const { data } = await StatAPI.getOutPatientList(params);
            if (isEqual(params, this.createFetchParams())) {
                if (this.view.curTypeId) {
                    const list = await this.handleEncryptTableList(data.list ?? [],OutpatientVisitStatementReportTable.decryptKeys,this.view.curTypeId);
                    this.setTableInfo({
                        tableInfo: {
                            header: data.header,
                            data: (list || []).map((item) => ({
                                ...item,
                                patientAge: formatAge(item.patientAge),
                            })),
                            total: { count: data.count },
                        },
                    });
                } else {
                    this.setTableInfo({
                        tableInfo: {
                            header: data.header,
                            data: (data.list || []).map((item) => ({
                                ...item,
                                patientAge: formatAge(item.patientAge),
                            })),
                            total: { count: data.count },
                        },
                    });
                }
            }
        } catch (err) {
            console.log(err);
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];
        const { staticConfig } = OutpatientVisitStatementReportTable;

        const renderTypeList = this.createRenderList();
        this.tableRenderConfig = resolveRenderConfig(
            header,
            renderTypeList,
            staticConfig,
            this.renderTypeMap,
            this.headerRenderTypeMap,
            this.headerAppendRenderTypeMap,
        );
    }

    async fetchEmployeeSelection() {
        const {
            beginDate, endDate,clinicId, scope,
        } = this.createFetchParams();
        try {
            const { data } = await API.employee.getOutpatientEmployeeSelection({
                scope,
                beginDate,
                endDate,
                clinicId,
            });
            this.employeeList = (data ?? []).map((item) => ({
                label: item.name,
                value: item.id,
            }));
        } catch (e) {
            console.log(e);
            this.employeeList = [];
        }
    }

    async fetchClinicDepartments() {
        try {
            const { data } = await SettingAPI.clinic.fetchClinicDepartments({
                showDisable: 1,
            });
            this.departmentList = data.data.rows.map((item) => {
                return {
                    value: item.id,
                    label: item.name,
                };
            });
        } catch (e) {
            console.log(e);
            this.departmentList = [];
        }
    }
    formatPrescriptionOneLine(item) {
        if (
            item.prescriptionChineseForms && item.prescriptionChineseForms.length === 0 &&
            item.prescriptionWesternForms && item.prescriptionWesternForms.length === 0 &&
            item.prescriptionInfusionForms && item.prescriptionInfusionForms.length === 0 &&
            item.prescriptionExternalTreatForms && item.prescriptionExternalTreatForms.length === 0 &&
            !item?.prescriptionGlassesForms
        ) {
            return '-';
        }
        let res = '';
        item.prescriptionWesternForms && item.prescriptionWesternForms.forEach((form) => {
            form.prescriptionFormItems.forEach((formItem) => {
                res += `${formItem.name} ${formItem.usage}${formItem.ast === AstEnum.PI_SHI ? '(皮试)' : ''}，${
                    freq2Han(formItem.freq) ? `${freq2Han(formItem.freq)}，` : ''
                }每次${formItem.dosage}${formItem.dosage_unit} ${formItem.unit_count}${formItem.unit}`;
            });
        });

        item.prescriptionInfusionForms && item.prescriptionInfusionForms.forEach((form) => {
            form.prescriptionFormItems.forEach((formItem) => {
                res += `${formItem.name} ${formItem.usage}${formItem.ast === AstEnum.PI_SHI ? '(皮试)' : ''}，${
                    freq2Han(formItem.freq) ? `${freq2Han(formItem.freq)}，` : ''
                }每次${formItem.dosage}${formItem.dosage_unit} ${formItem.unit_count}${formItem.unit}`;
            });
        });

        item.prescriptionChineseForms && item.prescriptionChineseForms.forEach((form) => {
            form.prescriptionFormItems.forEach((formItem) => {
                res += `${formItem.name} ${formItem.unit_count}${formItem.unit || 'g'}${
                    formItem.special_requirement ? `(${formItem.special_requirement})` : ''
                }\n`;
            });
        });

        item.prescriptionExternalTreatForms && item.prescriptionExternalTreatForms.forEach((form) => {
            form.prescriptionFormItems.forEach((formItem) => {
                res += `${formItem.name} ${formItem.unit_count}${formItem.unit}；`;
            });
        });


        const glassesForms = item?.prescriptionGlassesForms?.glassesParams ? this.formatGlassHeader(item?.prescriptionGlassesForms, true) : '';

        glassesForms?.glassesParams?.filter((it) => it.rightEyeValue)?.forEach((formItem, index) => {
            res += `${index === 0 ? '右眼：' : ''}${formItem.name}${formItem.rightEyeValue}${formItem.unit}，`;
        });
        glassesForms?.glassesParams?.filter((it) => it.leftEyeValue)?.forEach((formItem, index) => {
            res += `${index === 0 ? '左眼：' : ''}${formItem.name}${formItem.leftEyeValue}${formItem.unit}`;
        });

        return res;
    }
}
