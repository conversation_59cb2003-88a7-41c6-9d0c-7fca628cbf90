<template>
    <stat-table-layout
        :table-key="tableKey"
        :table-layout-config="tableLayoutConfig"
        :is-enable-pagination="isEnablePagination"
        :handle-table-mounted="handleTableMounted"
        @table-prepared="handleTablePrepared"
        @page-changed="handlePageChanged"
    >
        <template #header>
            <stat-toolbar
                :feature-configs="featureConfigs"
                :print-disabled="printBtnDisabled"
                :filter-params="{
                    tableCategory,
                    ...filterParams
                }"
                :handle-export="handleExport"
                :table-key="tableKey"
                @setting-item-click="handleOpenCustomHeaderDialog"
                @folded-filter="handleFoldedFilter"
            ></stat-toolbar>
        </template>
        <template #content>
            <component
                :is="curComponent"
                ref="table"
                :loading="tablePresenter?.loading ?? false"
                :render-config="tablePresenter?.tableRenderConfig"
                :data-list="curDataList"
                :filter-params="filterParams"
                :total-info="tablePresenter?.totalInfo"
                :summary-data="tablePresenter?.summaryData ?? {}"
                :page-params="pageParams"
            >
            </component>
        </template>
    </stat-table-layout>
</template>

<script>
    import StatToolbar from '@/views-hospital/statistics/components/stat-toolbar/index.vue';
    import StatNormalTable from '@/views-hospital/statistics/components/stat-normal-table.vue';
    import StatTableLayout from '@/views-hospital/statistics/components/stat-table-layout.vue';

    import {
        REPORT_TABLE_SCOPE,
        TABLE_CATEGORY_ENUM,
        tableCategoryOptions,
        tableInstanceConfigEnum,
    } from '@/views-hospital/statistics/pages/medical/components/patient-visit-list/helper/constant';
    import { resolveToFilterParams } from '@/views-hospital/statistics/helper/utils';
    import usePagination from '@/hooks/abc-ui/use-table-pagination';
    import { mapGetters } from 'vuex';
    import { clone } from '@abc/utils';
    import { BizCustomHeader } from '@/components-composite/biz-custom-header';
    import TableHeaderAPI from 'views/statistics/core/api/table-header';

    export default {
        name: 'PatientVisitListReport',
        components: {
            StatToolbar,
            StatNormalTable,
            StatTableLayout,
        },
        props: {
            dateFilter: {
                type: Object,
                default: () => ({}),
            },
        },
        setup() {
            const {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            } = usePagination();
            return {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            };
        },
        data() {
            return {
                tableCategory: '',
                tablePresenter: null, //table实例
                isTablePrepared: false, //表格是否准备好 即有header后重新计算paginationLimit
                filterParams: {}, //请求筛选参数
                tableCustomConfig: {}, //table配置 包含filter工具
            };
        },
        computed: {
            ...mapGetters(['currentClinic','isForbiddenExport']),
            ...mapGetters('viewDistribute',['viewDistributeConfig']),
            ...mapGetters('theme', ['curTypeId']),
            clinicId() {
                return this.currentClinic ? this.currentClinic.clinicId : '';
            },
            comDateFilter: {
                get() {
                    return this.dateFilter;
                },
                set(val) {
                    this.$emit('update:dateFilter', val);
                },
            },
            curComponent() {
                const mapping = {
                    [TABLE_CATEGORY_ENUM.OUTPATIENT]: StatNormalTable,
                    [TABLE_CATEGORY_ENUM.INPATIENT]: StatNormalTable,
                };
                return mapping[this.tableCategory];
            },
            tableKey() {
                return this.tableCustomConfig?.tableKey ;
            },
            isEnablePagination() {
                return this.tableCustomConfig?.isEnablePagination ?? false;
            },
            printBtnDisabled() {
                const data = this.tablePresenter?.tableData;
                return !(Array.isArray(data) && data.length !== 0);
            },
            curDataList() {
                const tableData = this.tablePresenter?.table?.tableData ?? [];
                return !this.isEnablePagination ? tableData : (this.isTablePrepared ? tableData.slice(0,this.pageParams.limit) : []);
            },
            featureConfigs() {
                const options = clone(tableCategoryOptions);
                const base = [{
                    type: 'selectTabs',
                    valueKey: 'tableCategory',
                    options,
                    changeMethod: this.handleFilterChange,
                }];
                const filterTools = this.tableCustomConfig?.filterTools ?? [];
                const customConfig = filterTools.map((item) => ({
                    ...item,
                    changeMethod: this.handleFilterChange,
                }));
                return {
                    ...this.tableCustomConfig,
                    filterTools: [...base, ...customConfig],
                };
            },
            tableLayoutConfig() {
                return {
                    pageParams: this.pageParams,
                    table: this.tablePresenter?.table ?? {},
                };
            },
            tableHeaderKey() {
                const {
                    outpatientLog: {
                        showOralExamination, showOphthalmology,
                    },
                } = this.viewDistributeConfig.Statistics;
                if (showOralExamination) return 'stat.oral.outpatient.list';
                if (showOphthalmology) return 'stat.eye.outpatient.list';
                return 'stat.hospital.outpatient.list';
            },
        },
        created() {
            this.initTable(TABLE_CATEGORY_ENUM.OUTPATIENT);
        },
        methods: {
            async handleTableMounted(data = {}) {
                if (this.isEnablePagination) {
                    this.isTablePrepared = false;
                    await this.setPageSize(data.paginationLimit);
                    if (this.tablePresenter) {
                        await this.loadTableData();
                        const { table } = this.tablePresenter;
                        return new Promise((resolve) => {
                            resolve(table);
                        });
                    }
                } else {
                    this.isTablePrepared = true;
                    await this.loadTableData();
                }
            },
            handleTablePrepared(data) {
                this.setPageSize(data.paginationLimit);
                this.isTablePrepared = true;
            },

            //初始化table
            async initTable(val) {
                //获取table实例
                this.tablePresenter = tableInstanceConfigEnum[val] && new tableInstanceConfigEnum[val](this, REPORT_TABLE_SCOPE[val]);
                if (!this.tablePresenter) return;
                await this.tablePresenter.init();
                this.tableCategory = val;
            },
            setTableCustomConfig(config, isInit = false) {
                this.tableCustomConfig = config;
                if (isInit) {
                    //根据filterTools处理成真正的请求参数
                    const filterTools = this.tableCustomConfig?.filterTools ?? [];
                    const _filterParams = resolveToFilterParams(filterTools);
                    const withSelectItem = filterTools.find((item) => !!item.withSelect);
                    if (withSelectItem) {
                        const {
                            valueSelectKey: key, initSelectData: value,
                        } = withSelectItem;
                        const obj = { [key]: value };
                        this.filterParams = Object.assign(_filterParams,obj);
                    } else {
                        this.filterParams = _filterParams;
                    }
                }
            },
            async loadTableData(isRestPage = true) {
                if (isRestPage) {
                    this.resetPageIndex();
                }
                await this.tablePresenter.loadTableData();
                //获取table-renderConfig
                this.tablePresenter.createRenderConfig();
            },
            async handleFilterChange(filterValueKey, val, needReset = true) {
                if (filterValueKey === 'tableCategory') {
                    if (val === this.tableCategory) return;
                    await this.initTable(val);
                } else {
                    this.filterParams[filterValueKey] = val;
                    if (filterValueKey === 'dateFilter') {
                        this.comDateFilter = val;
                    }
                    this.tablePresenter.refreshFeatureConfig && this.tablePresenter.refreshFeatureConfig();
                    if (needReset) {
                        this.loadTableData();
                    }
                }
            },
            handleFoldedFilter(foldedFilterParams) {
                this.filterParams = {
                    ...this.filterParams,
                    ...foldedFilterParams,
                };
                this.loadTableData();
            },
            handlePageChanged(data) {
                this.changePageIndex(data);
                this.loadTableData(false);
            },
            async handleExport() {
                if (this.tablePresenter.export) {
                    return this.tablePresenter.export();
                }
            },
            handleOpenCustomHeaderDialog() {
                new BizCustomHeader({
                    value: true,
                    tableKey: this.tableHeaderKey,
                    titleName: '运营分析 - 门诊日志',
                    mode: 'draggle',
                    finishFunc: this.loadTableData,
                    tableHeaderApi: TableHeaderAPI,
                }).generateDialog({ parent: this });
            },
        },
    };
</script>

<style lang="scss">
.ms-list-popper {
    max-height: 640px;
    overflow-y: auto;
    overflow-y: overlay;
    font-size: 12px;
    color: $T2;

    .prescription {
        border-bottom: 1px solid $P6;

        &:last-child {
            margin-top: 4px;
            border-bottom: none;
        }

        .title {
            color: #000000;
        }

        .content {
            padding: 4px 0;

            li {
                display: flex;
                justify-content: space-between;
            }
        }
    }

    .glasses-form-wrapper {
        max-width: 532px;
        margin-top: 4px;
        border: 1px solid $P6;

        &.is-contact {
            max-width: 100%;
        }

        &-content {
            display: flex;
            width: 100%;

            .glasses-form-item {
                display: flex;
                flex-direction: column;
                border-right: 1px solid $P6;

                &:last-child {
                    border-right: none;
                }

                span {
                    display: inline-block;
                    min-width: 40px;
                    max-width: 70px;
                    height: 24px;
                    padding-right: 8px;
                    padding-left: 8px;
                    overflow: hidden;
                    line-height: 24px;
                    text-align: center;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    border-bottom: 1px solid $P6;

                    &.is-frame {
                        width: 100px;
                    }

                    &.is-base {
                        max-width: 130px;
                    }

                    &:last-child {
                        border-bottom: none;
                    }
                }
            }
        }

        &-footer {
            display: flex;
            justify-content: space-between;
            width: 100%;
            height: 24px;
            padding: 0 10px;
            line-height: 24px;
            border-top: 1px solid $P6;
        }
    }
}
</style>
