<template>
    <div style="height: 100%;">
        <stat-table-layout
            :table-key="tableKey"
            :table-layout-config="tableLayoutConfig"
            :is-enable-pagination="isEnablePagination"
            :handle-table-mounted="handleTableMounted"
            @table-prepared="handleTablePrepared"
            @page-changed="handlePageChanged"
        >
            <template #header>
                <stat-toolbar
                    :feature-configs="featureConfigs"
                    :print-disabled="printBtnDisabled"
                    :filter-params="{
                        tableCategory,
                        ...filterParams
                    }"
                    :handle-export="handleExport"
                    :table-key="tableKey"
                    @folded-filter="handleFoldedFilter"
                    @setting-item-click="handleOpenCustomHeaderDialog"
                >
                    <template #custom>
                        <abc-button
                            :type="filterCrmParamsCount ? 'blank' : 'ghost'"
                            :style="{
                                maxWidth: '200px',
                            }"
                            icon="n-filter-line"
                            @click="visibleCrmFilter = true"
                        >
                            <span
                                v-abc-title.ellipsis="filterCrmParamsCount ? `已筛选${filterCrmParamsCount}项` : '筛选'"
                                :style="{
                                    display: 'inline-block',
                                    color: filterCrmParamsCount ? '#000' : '#7a8794',
                                    maxWidth: '150px',
                                }"
                            >
                            </span>
                        </abc-button>
                    </template>
                </stat-toolbar>
            </template>
            <template #content>
                <component
                    :is="curComponent"
                    ref="table"
                    :loading="tablePresenter?.loading ?? false"
                    :render-config="tablePresenter?.tableRenderConfig"
                    :data-list="curDataList"
                    :filter-params="filterParams"
                    :total-info="tablePresenter?.totalInfo"
                    :summary-data="tablePresenter?.summaryData ?? {}"
                    :page-params="pageParams"
                >
                </component>
            </template>
        </stat-table-layout>
        <dialog-filter
            v-if="visibleCrmFilter"
            v-model="visibleCrmFilter"
            is-from-stat
            :filter-config="initFilterCrmConfig"
            @change="onChangeFilterConfig"
        ></dialog-filter>
    </div>
</template>

<script>
    import StatToolbar from '@/views-hospital/statistics/components/stat-toolbar/index.vue';
    import StatNormalTable from '@/views-hospital/statistics/components/stat-normal-table.vue';
    import StatTableLayout from '@/views-hospital/statistics/components/stat-table-layout.vue';

    import {
        REPORT_TABLE_SCOPE,
        TABLE_CATEGORY_ENUM,
        tableInstanceConfigEnum,
    } from '@/views-hospital/statistics/pages/medical/components/patient-list/helper/constant';
    import { resolveToFilterParams } from '@/views-hospital/statistics/helper/utils';
    import usePagination from '@/hooks/abc-ui/use-table-pagination';
    import { mapGetters } from 'vuex';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import DialogFilter from 'views/crm/common/package-filter/index.vue';
    import MixinFilter from 'views/crm/common/package-filter/mixin-filter';
    import { isNotNull } from '@/lis/common/tools';
    import { BizCustomHeader } from '@/components-composite/biz-custom-header';
    import TableHeaderAPI from 'views/statistics/core/api/table-header';
    const { patientStat } = getViewDistributeConfig().Statistics;

    export default {
        name: 'PatientListReport',
        components: {
            DialogFilter,
            StatToolbar,
            StatNormalTable,
            StatTableLayout,
        },
        mixins: [MixinFilter],
        props: {
            dateFilter: {
                type: Object,
                default: () => ({}),
            },
        },
        setup() {
            const {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            } = usePagination();
            return {
                pageParams,
                setPageSize,
                changePageIndex,
                resetPageIndex,
                setPageTotal,
            };
        },
        data() {
            return {
                tableCategory: '',
                tablePresenter: null, //table实例
                isTablePrepared: false, //表格是否准备好 即有header后重新计算paginationLimit
                filterParams: {}, //请求筛选参数
                tableCustomConfig: {}, //table配置 包含filter工具
                visibleCrmFilter: false,
                filterCrmConfig: null,
            };
        },
        computed: {
            ...mapGetters(['currentClinic','isChainAdmin','isSingleStore','isForbiddenExport']),
            ...mapGetters('viewDistribute',['viewDistributeConfig']),
            ...mapGetters('theme', ['curTypeId']),
            clinicId() {
                if (this.isChainAdmin) {
                    return this.filterParams.clinicId;
                }
                return this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },
            comDateFilter: {
                get() {
                    return this.dateFilter;
                },
                set(val) {
                    this.$emit('update:dateFilter', val);
                },
            },
            curComponent() {
                return StatNormalTable;
            },
            tableKey() {
                return this.tableCustomConfig?.tableKey ;
            },
            isEnablePagination() {
                return this.tableCustomConfig?.isEnablePagination ?? false;
            },
            printBtnDisabled() {
                const data = this.tablePresenter?.tableData;
                return !(Array.isArray(data) && data.length !== 0);
            },
            curDataList() {
                const tableData = this.tablePresenter?.table?.tableData ?? [];
                return !this.isEnablePagination ? tableData : (this.isTablePrepared ? tableData.slice(0,this.pageParams.limit) : []);
            },
            featureConfigs() {
                const filterTools = this.tableCustomConfig?.filterTools ?? [];
                const customConfig = filterTools.map((item) => ({
                    ...item,
                    changeMethod: this.handleFilterChange,
                }));
                return {
                    ...this.tableCustomConfig,
                    filterTools: customConfig,
                };
            },
            tableLayoutConfig() {
                return {
                    pageParams: this.pageParams,
                    table: this.tablePresenter?.table ?? {},
                };
            },
            tableHeaderKey() {
                return patientStat.plistCustomStatHeaderTableKey;
            },
            filterCrmInfo() {
                return this.createFilterParams(this.filterCrmConfig);
            },
            filterCrmParamsCount() {
                return this. filterCrmInfo.labels?.length ?? 0;
            },
            initFilterCrmConfig() {
                return {
                    ...this.filterCrmConfig,
                    createDate: {
                        startDate: this.filterParams.dateFilter.begin,
                        endDate: this.filterParams.dateFilter.end,
                    },
                    activeClinicId: this.clinicId,
                };
            },
        },
        created() {
            this.initTable(TABLE_CATEGORY_ENUM.PATIENT_LIST);
        },
        methods: {
            async handleTableMounted(data = {}) {
                if (this.isEnablePagination) {
                    this.isTablePrepared = false;
                    await this.setPageSize(data.paginationLimit);
                    if (this.tablePresenter) {
                        await this.loadTableData();
                        const { table } = this.tablePresenter;
                        return new Promise((resolve) => {
                            resolve(table);
                        });
                    }
                } else {
                    this.isTablePrepared = true;
                    await this.loadTableData();
                }
            },
            handleTablePrepared(data) {
                this.setPageSize(data.paginationLimit);
                this.isTablePrepared = true;
            },

            //初始化table
            async initTable(val) {
                //获取table实例
                this.tablePresenter = tableInstanceConfigEnum[val] && new tableInstanceConfigEnum[val](this, REPORT_TABLE_SCOPE[val]);
                if (!this.tablePresenter) return;
                await this.tablePresenter.init();
                this.tableCategory = val;
            },
            setTableCustomConfig(config, isInit = false) {
                this.tableCustomConfig = config;
                if (isInit) {
                    //根据filterTools处理成真正的请求参数
                    this.filterParams = resolveToFilterParams(this.tableCustomConfig?.filterTools ?? []);
                }
            },
            async loadTableData(isRestPage = true) {
                if (isRestPage) {
                    this.resetPageIndex();
                }
                await this.tablePresenter.loadTableData();
                //获取table-renderConfig
                this.tablePresenter.createRenderConfig();
            },
            async handleFilterChange(filterValueKey, val, needReset = true) {
                if (filterValueKey === 'tableCategory') {
                    if (val === this.tableCategory) return;
                    await this.initTable(val);
                } else {
                    this.filterParams[filterValueKey] = val;
                    if (filterValueKey === 'dateFilter') {
                        this.comDateFilter = val;
                    }
                    if (filterValueKey === 'dateFilter' || filterValueKey === 'clinicId') {
                        this.filterParams.notCrm = true;
                    }
                    this.tablePresenter.refreshFeatureConfig && this.tablePresenter.refreshFeatureConfig();
                    if (needReset) {
                        this.loadTableData();
                    }
                }
            },
            handleFoldedFilter(foldedFilterParams) {
                this.filterParams = {
                    ...this.filterParams,
                    ...foldedFilterParams,
                };
                this.loadTableData();
            },
            handlePageChanged(data) {
                this.changePageIndex(data);
                this.loadTableData(false);
            },
            async handleExport() {
                if (this.tablePresenter.export) {
                    return this.tablePresenter.export();
                }
            },
            handleOpenCustomHeaderDialog() {
                new BizCustomHeader({
                    value: true,
                    tableKey: this.tableHeaderKey,
                    titleName: '运营分析 - 患者清单',
                    mode: 'draggle',
                    finishFunc: this.loadTableData,
                    tableHeaderApi: TableHeaderAPI,
                }).generateDialog({ parent: this });
            },

            /**
             * desc [修改筛选条件]
             */
            onChangeFilterConfig(filterConfig) {
                this.filterCrmConfig = filterConfig;
                const {
                    createDate = {},activeClinicId = '',
                } = this.filterCrmConfig;
                const {
                    startDate,endDate,
                } = createDate;
                this.comDateFilter = {
                    begin: startDate,
                    end: endDate,
                };
                this.filterParams.dateFilter = {
                    begin: startDate,
                    end: endDate,
                };
                this.filterParams.clinicId = activeClinicId;
                this.visibleCrmFilter = false;
                Object.assign(this.filterParams, { crmFilter: this.handleFilterCrmInfoParams() });
                this.filterParams.notCrm = false;
                this.loadTableData();
            },

            handleFilterCrmInfoParams() {
                const {
                    minAge,maxAge,wxPatientQuery,sourceFrom,sourceId,consultantId,medicalPlanStatus,medicalPlanType,dutyTherapistId,primaryTherapistId,
                } = this.filterCrmInfo.params;
                const params = {
                    ...this.filterCrmInfo.params,
                    consultantId: isNotNull(consultantId) ? [consultantId] : consultantId,
                    medicalPlanStatus: isNotNull(medicalPlanStatus) ? [medicalPlanStatus] : medicalPlanStatus,
                    medicalPlanType: isNotNull(medicalPlanType) ? [medicalPlanType] : medicalPlanType,
                    dutyTherapistId: isNotNull(dutyTherapistId) ? [dutyTherapistId] : dutyTherapistId,
                    primaryTherapistId: isNotNull(primaryTherapistId) ? [primaryTherapistId] : primaryTherapistId,
                    minAge: Number(minAge) ?? undefined,
                    maxAge: Number(maxAge) ?? undefined,
                    wxBind: wxPatientQuery === true ? 1 : (wxPatientQuery === false ? 0 : undefined),
                    sourceIdFroms: (sourceFrom || sourceId) ? [
                        {
                            sourceFrom,
                            sourceId,
                        },
                    ] : [],
                };
                delete params.wxPatientQuery;
                delete params.sourceFrom;
                delete params.sourceId;
                return params;
            },
        },
    };
</script>
