import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import StatApi from 'api/stat';

export default class PatientListStatementReportTable extends StatBaseTable {
    // 由产品.设计提供的静态配置, 开发只能修改key、renderType
    static staticConfig = {
        'hasInnerBorder': true,
        'hasHeaderBorder': true,
        'list': [{
            'label': '创建日期',
            'key': 'created',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '姓名',
            'key': 'name',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '性别',
            'key': 'sex',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '48px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '年龄',
            'key': 'age',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '56px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '手机号',
            'key': 'mobile',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '档案号',
            'key': 'sn',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '身份证',
            'key': 'idCard',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '180px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '婚否',
            'key': 'marital',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '56px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '职业',
            'key': 'profession',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '民族',
            'key': 'ethnicity',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '56px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '家庭住址',
            'key': 'address',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'center',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '首诊来源',
            'key': 'visitSource',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '到店原因',
            'key': 'visitReason',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '患者标签',
            'key': 'patientTag',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '最近就诊',
            'key': 'lastOutpatientDate',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '累计消费',
            'key': 'allAmount',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '门诊就诊次数合计',
            'key': 'visitNum',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '门诊费用合计',
            'key': 'allCumulativeAmount',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '住院次数合计',
            'key': 'hospitalVisitNum',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '110px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '住院费用合计',
            'key': 'hospitalAllCumulativeAmount',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '110px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '首评治疗师',
            'key': 'primaryTherapistName',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '责任治疗师',
            'key': 'dutyTherapistName',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '既往史',
            'key': 'pastHistory',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '过敏史',
            'key': 'allergicHistory',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '体重',
            'key': 'weight',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'right',
            },
            'headerStyle': {
                'textAlign': 'center',
            },
        },{
            'label': '备注',
            'key': 'remark',
            'sortable': false,
            'style': {
                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
            },
        }, {
            'label': '生日',
            'key': 'birthday',
            'position': 25,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
            },
        },
        {
            'label': '工作单位',
            'key': 'company',
            'position': 26,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
            },
        },
        {
            'label': '微信绑定',
            'key': 'wxBind',
            'position': 27,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
                textAlign: 'center',
            },
        },
        {
            'label': '会员类型',
            'key': 'memberType',
            'position': 28,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
                textAlign: 'center',
            },
        },
        {
            'label': '门店新老客',
            'key': 'clinicCustomers',
            'position': 29,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
                textAlign: 'center',
            },
        },
        {
            'label': '门诊类型',
            'key': 'outpatientType',
            'position': 30,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
                textAlign: 'center',
            },
        },
        {
            'label': '最近预约时间',
            'key': 'lastReserveDate',
            'position': 31,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
                textAlign: 'center',
            },
        },
        {
            'label': '最近执行时间',
            'key': 'lastExecuteTime',
            'position': 32,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
                textAlign: 'center',
            },
        },
        {
            'label': '活跃门店',
            'key': 'archiveClinic',
            'position': 33,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
            },
        },
        {
            'label': '就诊医生/初复诊',
            'key': 'outpatientInfo',
            'position': 33,
            'sortable': 0,
            'style': {
                'flex': '1',
                'width': '160px',
                'minWidth': '',
                'maxWidth': '',
                textAlign: 'center',
            },
        },
        ],
    };

    static decryptKeys = ['name', 'mobile', 'sn', 'idCard', 'email', 'address', 'company'];

    async init() {
        await Promise.all([
            super.init(),
        ]);
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin','subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'patient-stat',
            tableKey: 'patient-list-statement',
            isEnablePagination: true,
            isEnableSetting: true,
            isEnableExport: !this.view?.isForbiddenExport,
            settingOptions: [
                {
                    text: '设置展示字段',
                    value: '',
                    isOpen: false,
                    groupName: '',
                },
            ],
            filterTools: [
                {
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    label: '建档日期',
                    initData: this.view.comDateFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '活跃门店',
                    width: 120,
                    options: (subClinics ?? []).filter((item) => item.label !== '总部'),
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: 'patientSelector',
                    valueKey: 'patientId',
                    placeholder: '搜索患者',
                    clinicId: '',
                    width: 120,
                },
            ].filter((item) => !item.isHidden),
        };
    }

    createFetchParams() {
        const { permission } = usePermission('isChainAdmin');
        const {
            dateFilter: {
                begin, end,
            } = {},
            patientId,
            crmFilter,
            notCrm,
        } = this.view.filterParams;
        const baseParams = {
            activeClinicId: this.queryClinicId(),
            beginCreatedDate: begin,
            endCreatedDate: end,
            scope: this.scope,
            patientId,
            offset: this.view.pageParams.offset,
            limit: this.view.pageParams.limit,
        };
        const params = notCrm ? {
            ...crmFilter,
            ...baseParams,
        } : {
            ...baseParams,
            ...crmFilter,
        };
        params.activeClinicId = permission.isChainAdmin ? params.activeClinicId : this.queryClinicId();
        return params;
    }

    async loadTableData() {
        const params = this.createFetchParams();
        this.loading = true;
        try {
            const data = await StatApi.getPatientStat(params,{ e: 1 });
            if (this.view.curTypeId) {
                const list = await this.handleEncryptTableList(data.data ?? [],PatientListStatementReportTable.decryptKeys,this.view.curTypeId);
                this.setTableInfo({
                    tableInfo: {
                        ...data,
                        data: list,
                    },
                });
            } else {
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (err) {
            console.log(err);
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];
        const {
            renderTypeList,staticConfig,
        } = PatientListStatementReportTable;
        this.tableRenderConfig = resolveRenderConfig(
            header,
            renderTypeList,
            staticConfig,
            this.renderTypeMap,
            this.headerRenderTypeMap,
            this.headerAppendRenderTypeMap,
        );
    }
}
