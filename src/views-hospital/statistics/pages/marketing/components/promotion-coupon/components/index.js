import { isEqual } from 'utils/lodash';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import {
    DIMENSION_ENUM, dimensionApiEnum, dimensionLabelEnum,
} from '@/views-hospital/statistics/pages/marketing/components/promotion-coupon/components/constant';
import { ListTableConfig } from '@/views-hospital/statistics/pages/marketing/components/promotion-coupon/components/list';
import { RecordTableConfig } from '@/views-hospital/statistics/pages/marketing/components/promotion-coupon/components/record';
import PromotionAPI from 'views/statistics/core/api/promotion';

// 门诊挂号
export default class PromotionCouponStatisticsTable extends StatBaseTable {
    constructor(...args) {
        super(...args);
        this.dimension = DIMENSION_ENUM.LIST;

        this.couponList = [];
        this.chargeTypeList = [];
    }

    async refreshFeatureConfig() {
        await this.fetchFilterList();
        super.refreshFeatureConfig();
    }
    async fetchFilterList() {
        try {
            const {
                beginDate, endDate, clinicId,
            } = this.createFetchParams();
            const { data } = await PromotionAPI.coupon.selectCondition({
                beginDate,
                endDate,
                clinicId,
            });
            this.couponList = data.promotion.map((item) => ({
                value: item.promotion_id,
                label: item.promotion_name,
            }));
            this.chargeTypeList = data.chargeType.map((item) => ({
                value: item.charge_no,
                label: item.charge_type,
            }));
        } catch (e) {
            this.couponList = [];
            this.chargeTypeList = [];
        }
    }

    createStaticConfig() {
        const mapping = {
            [DIMENSION_ENUM.LIST]: ListTableConfig,
            [DIMENSION_ENUM.RECORD]: RecordTableConfig,
        };
        return mapping[this.dimension];
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin','subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'promotion-discount',
            tableKey: `promontion-coupon-${this.dimension}`,
            isEnablePagination: true,
            filterTools: [
                {
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    initData: this.view.comDateFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                    initData: this.queryClinicId(),
                },
                {
                    type: 'patientSelector',
                    valueKey: 'patientId',
                    placeholder: '搜索患者',
                    clearable: true,
                    width: 120,
                },
                {
                    type: 'select',
                    valueKey: 'chargeType',
                    clearable: true,
                    placeholder: '类型',
                    width: 120,
                    options: this.chargeTypeList,
                    isHidden: this.dimension === DIMENSION_ENUM.LIST,
                },
                {
                    type: 'select',
                    valueKey: 'promotionId',
                    clearable: true,
                    placeholder: '优惠券',
                    width: 120,
                    options: this.couponList,
                },
            ].filter((item) => !item.isHidden),
            dimensions: {
                options: [
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.LIST],
                        value: DIMENSION_ENUM.LIST,
                    },
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.RECORD],
                        value: DIMENSION_ENUM.RECORD,
                    },
                ],
                curDimension: this.dimension,
            },
        };
    }

    createFetchParams() {
        const {
            dateFilter: {
                begin, end,
            },
            clinicId,
            promotionId,
            chargeType,
            patientId,
        } = this.view.filterParams;
        return {
            beginDate: begin,
            endDate: end,
            clinicId,
            promotionId,
            chargeType,
            patientId,
            offset: this.view.pageParams.offset,
            size: this.view.pageParams.pageSize,
        };
    }

    async loadTableData() {
        const params = this.createFetchParams();
        this.loading = true;
        try {
            const { data } = await PromotionAPI.coupon[dimensionApiEnum[this.dimension]](params);
            const afterParams = this.createFetchParams();
            if (isEqual(params, afterParams)) {
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (e) {
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];
        const { renderTypeList } = PromotionCouponStatisticsTable;

        const staticConfig = this.createStaticConfig();
        this.tableRenderConfig = resolveRenderConfig(
            header,
            renderTypeList,
            staticConfig,
            this.renderTypeMap,
            this.headerRenderTypeMap,
            this.headerAppendRenderTypeMap,
        );
    }

    setDimension(val) {
        this.dimension = val;
        this.refreshFeatureConfig();
    }
}
