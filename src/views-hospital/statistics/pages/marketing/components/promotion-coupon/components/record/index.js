export const RecordTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    'list': [{
        'label': '持券人姓名',
        'key': 'patientName',
        'pinned': false,
        'position': 1,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '92px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '手机号',
        'key': 'patientMobile',
        'pinned': false,
        'position': 2,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '交易类型',
        'key': 'chargeType',
        'pinned': false,
        'position': 3,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '券名称',
        'key': 'couponName',
        'pinned': false,
        'position': 4,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '168px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '使用数量',
        'key': 'useCount',
        'pinned': false,
        'position': 5,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '交易时间',
        'key': 'usedDate',
        'pinned': false,
        'position': 6,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '消费门店',
        'key': 'clinicName',
        'pinned': false,
        'position': 7,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '优惠券抵扣金额',
        'key': 'discountPrice',
        'pinned': false,
        'position': 8,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
        'colType': 'money',
    },{
        'label': '实收金额',
        'key': 'actualMoney',
        'pinned': false,
        'position': 9,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
        'colType': 'money',
    },{
        'label': '操作人',
        'key': 'operator',
        'pinned': false,
        'position': 9,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    },{
        'label': '开单人',
        'key': 'sellerName',
        'pinned': false,
        'position': 9,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
    }],
});
