export const ListTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    'list': [{
        'label': '持券人姓名',
        'key': 'patientName',
        'pinned': false,
        'position': 1,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '手机号',
        'key': 'patientMobile',
        'pinned': false,
        'position': 2,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '优惠券名称',
        'key': 'promotionName',
        'pinned': false,
        'position': 3,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '领取时间',
        'key': 'created',
        'pinned': false,
        'position': 4,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '领取数量',
        'key': 'receivedQuantity',
        'pinned': false,
        'position': 5,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '使用数量',
        'key': 'usedQuantity',
        'pinned': false,
        'position': 6,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '过期/作废数量',
        'key': 'expiredQuantity',
        'pinned': false,
        'position': 7,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '剩余有效数量',
        'key': 'remainingQuantity',
        'pinned': false,
        'position': 8,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '到期时间',
        'key': 'validEnd',
        'pinned': false,
        'position': 9,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    }],
});
