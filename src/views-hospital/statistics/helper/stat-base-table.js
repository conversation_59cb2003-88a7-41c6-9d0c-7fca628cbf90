import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import useTable from '@/views-hospital/statistics/hooks/useTable';
import Vue from 'vue';
import ExportService from 'views/statistics/core/services/export/export-service';
import {
    isNotNull, tryJSONParse,
} from '@/utils';
import DecodeService from '@/service/decode';

export default class StatBaseTable {
    constructor(view, scope) {
        this.view = view;
        this.scope = scope;
        const {
            table,setTableInfo,
        } = useTable();
        this.table = table;
        this.tableRenderConfig = Vue.observable({});
        this.setTableInfo = setTableInfo;
        this.loading = false;

        // key与renderType的映射关系
        this.renderTypeMap = {};
        // key与headerRenderType的映射关系
        this.headerRenderTypeMap = {};
        // key与headerAppendRenderType的映射关系
        this.headerAppendRenderTypeMap = {};
    }

    static renderTypeList = {};

    // 由产品.设计提供的静态配置, 开发只能修改key、renderType
    static staticConfig = {
    };

    async init() {
        //初始化请求参数 例如mapGetter之类的
        //初始化筛选参数
        this.initFeatureConfig();
    }

    initFeatureConfig() {
        //设置筛选配置
        this.view.setTableCustomConfig(this.createFeatureConfigs(),true);
        //请求筛选参数 包括一次性的和需要更新的筛选项目
        this.initFilterOptions();
    }
    initFilterOptions() {
        this.refreshFeatureConfig();
    }

    refreshFeatureConfig() {
        this.view.setTableCustomConfig(this.createFeatureConfigs());
    }

    createFeatureConfigs() {
        throw new Error('请重写此方法');
    }

    queryClinicId() {
        const {
            permission,
        } = usePermission('isChainAdmin','isSingleStore','currentClinic');
        const {
            isChainAdmin,isSingleStore,currentClinic,
        } = permission;
        if (isChainAdmin) {
            return this.view.filterParams.clinicId;
        }
        return isSingleStore ? '' : currentClinic?.clinicId;
    }

    queryChainId() {
        const {
            permission,
        } = usePermission('currentClinic');
        const { currentClinic } = permission;
        return currentClinic && currentClinic.chainId;
    }

    loadTableData() {
        throw new Error('请重写此方法');
    }

    // abc-table组件的配置
    createRenderConfig() {

    }

    createFetchParams() {
        return {};
    }

    async export() {
        this.exportService = new ExportService();
        const params = this.createFetchParams({ isExport: true });
        try {
            await this.exportService.startExport(this.view.tableCustomConfig?.exportTaskType, {
                ...params,
            });
        } catch (e) {
            console.error(e);
            return false;
        }
        return true;
    }

    async handleEncryptTableList(list = [],decryptKeys = []) {
        if (!list || list.length === 0) {
            return [];
        }
        // 处理所有需要解密的字段
        const decryptAllFields = async () => {
            const decryptedData = {};

            // 为每个需要解密的字段创建解密 Promise
            for (const key of decryptKeys) {
                const decryptPromises = list.map((item) => {
                    // 检查字段是否存在且有值
                    if (isNotNull(item[key])) {
                        return DecodeService.aecSignForDec(item[key]);
                    }
                    // 如果字段不存在或为空，返回原值或空字符串
                    return Promise.resolve(item[key] || '');
                }) || [];

                // 等待当前字段的所有解密操作完成
                decryptedData[key] = await Promise.all(decryptPromises);
            }

            return decryptedData;
        };

        // 等待所有字段的解密操作完成
        const allDecryptedData = await decryptAllFields();

        return list.map((item, index) => {
            // 为当前项应用所有解密后的数据
            const decryptedFields = {};
            decryptKeys.forEach((key) => {
                if (allDecryptedData[key] && allDecryptedData[key][index] !== undefined) {
                    decryptedFields[key] = tryJSONParse(allDecryptedData[key][index]);
                }
            });

            return {
                ...item,
                ...decryptedFields,
            };
        }) || [];
    }
}
