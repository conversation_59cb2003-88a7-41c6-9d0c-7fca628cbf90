<template>
    <abc-popover
        v-model="showPopper"
        :open-delay="200"
        :placement="placement"
        trigger="manual"
        theme="white"
        :width="popperWidth"
        class="patient-list-wrapper"
        :visible-arrow="false"
        :popper-style="{ padding: '4px 0 0 0' }"
        @show="fetchWaitAllocatedOrTransferPatientList"
    >
        <div
            slot="reference"
            class="patient-list-reference"
            :class="{ 'show-popper': showPopper }"
            @click="trigger"
        >
            <slot></slot>
        </div>
        <div
            v-abc-loading="loading"
            v-abc-click-outside="outside"
            class="patient-list-popover-content"
            :class="{ 'show-footer': isShowAddPatient }"
        >
            <ul v-if="patientList.length">
                <template v-if="waitAllocatedPatientList.length">
                    <li class="title-item">
                        <span class="name ellipsis">待分配</span>
                    </li>
                    <li
                        v-for="item in waitAllocatedPatientList"
                        :key="item.id"
                        :class="{ 'is-selected': currentItemId }"
                        @click="selectPatient(item)"
                    >
                        <span class="name ellipsis">{{ item.patient && item.patient.name || '' }}</span>
                        <span class="sex">{{ item.patient && item.patient.sex || '' }}</span>
                        <span class="age">
                            {{ getAgeInfo(item) }}
                        </span>
                        <span class="date">{{ item.patient && item.patient.date || '' }}</span>
                        <span v-if="item.inpatientTime" class="time">{{ formatDate(item.inpatientTime, 'MM-DD HH:mm') }}</span>
                        <span v-else class="time"></span>
                        <span :title="item.departmentName || ''" class="departmentName ellipsis">
                            {{ item.departmentName || '' }}
                        </span>
                    </li>
                </template>
                <template v-if="waitTransferPatientList.length">
                    <li class="title-item">
                        <span class="name ellipsis">待转科</span>
                    </li>
                    <li
                        v-for="item in waitTransferPatientList"
                        :key="item.id"
                        :class="{ 'is-selected': currentItemId }"
                        @click="selectPatient(item)"
                    >
                        <span class="name ellipsis">{{ item.patient && item.patient.name }}</span>
                        <span class="sex">{{ item.patient && item.patient.sex }}</span>
                        <span class="age">
                            {{ getAgeInfo(item) }}
                        </span>
                        <span class="date">{{ item.patient && item.patient.date }}</span>
                        <span v-if="item.inpatientTime" class="time">{{ formatDate(item.inpatientTime, 'MM-DD HH:mm') }}</span>
                        <span v-else class="time"></span>
                        <span class="departmentName">
                            {{ item.departmentName || '' }}
                        </span>
                    </li>
                </template>
            </ul>
            <div v-else class="empty">
                暂无待分配患者
            </div>
            <div v-if="isShowAddPatient" class="add-new-patient">
                <abc-tooltip :disabled="!isDisableAddPatient" placement="top" content="病区已停用不可新增入院患者">
                    <abc-button
                        :disabled="isDisableAddPatient"
                        type="text"
                        icon="plus_thin"
                        @click="addPatient"
                    >
                        新增入院患者
                    </abc-button>
                </abc-tooltip>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import { formatDate } from '@abc/utils-date';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { HospitalQLSceneType } from 'utils/constants-hospital';
    import { HospitalStatusEnum } from '@/views-hospital/register/utils/constants';
    import { mapState } from 'vuex';
    import { RELATIVE_STATUS } from 'utils/constants';

    export default {
        name: 'PatientList',
        props: {
            popperWidth: {
                type: [Number,String],
                default: 248,
            },
            isShowAddPatient: {
                type: Boolean,
                default: false,
            },
            placement: {
                type: String,
                default: 'bottom-start',
            },
        },
        data() {
            return {
                formatDate,
                showPopper: false,
                currentItemId: null,
                params: {
                    departmentId: '',
                    limit: 200,
                    offset: 0,
                    sceneType: HospitalQLSceneType.WAIT_ALLOCATE_BED,
                    status: '',
                    wardId: '',
                },
                loading: false,
                patientList: [], // 待分配和待转科患者列表
            };
        },
        computed: {
            ...mapState('hospitalGlobal', ['currentWardAreaId', 'currentWardAreaStatus']),
            isDisableAddPatient() {
                return this.currentWardAreaStatus === RELATIVE_STATUS.DEACTIVE;
            },
            // 待分配患者列表(包含出院召回)
            waitAllocatedPatientList() {
                const {
                    REGISTERED, DISCHARGE_CALLBACK,
                } = HospitalStatusEnum;
                return this.patientList?.filter((item) => [REGISTERED, DISCHARGE_CALLBACK].includes(item.status)) || [];
            },
            // 转科患者列表
            waitTransferPatientList() {
                return this.patientList?.filter((item) => item.status === HospitalStatusEnum.TRANSFER_DEPARTMENT_PROCESS) || [];
            },
        },
        methods: {
            async fetchWaitAllocatedOrTransferPatientList() {
                try {
                    this.loading = true;
                    this.params.wardId = this.currentWardAreaId;
                    const { data } = await PatientOrderAPI.getInHospitalPatientList(this.params) || {};
                    // 去除患者后的脏数据
                    this.patientList = data?.rows?.filter((item) => !!item.patient) || [];
                } catch (e) {
                    console.log('fetchPatientList Error');
                } finally {
                    this.loading = false;
                }
            },
            getAgeInfo(item) {
                const {
                    year, month, day,
                } = item?.patient?.age || {};
                if (year) {
                    return `${year}岁`;
                }
                if (month) {
                    return `${month}月`;
                }
                if (day) {
                    return `${day}天`;
                }
                return '';
            },
            trigger() {
                this.showPopper = !this.showPopper;
            },
            outside() {
                this.showPopper = false;
            },
            selectPatient(item) {
                this.$emit('select-patient',item,this.patientList);
                this.showPopper = false;
            },
            addPatient() {
                this.$emit('add-patient');
                this.showPopper = false;
            },
        },
    };
</script>

<style lang="scss" rel="stylesheet/scss">
@import 'src/styles/mixin.scss';
@import 'src/styles/theme.scss';

.patient-list-popover-content {
    box-sizing: border-box;
    width: 368px;
    height: 240px;
    overflow-y: auto;
    overflow-y: overlay;

    @include scrollBar;

    ul {
        li {
            display: flex;
            align-items: center;
            height: 36px;
            padding: 0 12px;
            font-size: 14px;
            color: $S1;
            cursor: pointer;
            border-bottom: 1px solid $B4;

            &.title-item {
                height: 20px;
                line-height: 20px;

                span {
                    color: $T2;
                }
            }

            span {
                display: inline-block;
                height: 14px;
                font-size: 14px;
                font-weight: 400;
                line-height: 14px;
                color: $S1;

                &.name {
                    width: 64px;
                }

                &.sex {
                    width: 14px;
                    margin: 0 8px;
                }

                &.age {
                    width: 31px;
                    margin-right: 8px;
                }

                &.departmentName {
                    flex: 1;
                    margin-left: 8px;
                    text-align: left;
                }
            }

            &:not(.title-item):hover,
            &.is-selected {
                background-color: #e5f2ff;
            }
        }
    }

    .empty {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: $T2;
    }

    &.show-footer {
        padding-bottom: 36px;

        .add-new-patient {
            position: absolute;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 36px;
            font-size: 14px;
            font-weight: 400;
            color: $B1;
            cursor: pointer;
            background-color: $S2;
            border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);
            box-shadow: 0 -2px 4px 0 rgba(0, 0, 0, 0.08);

            .abc-icon {
                margin-right: 9px;
            }
        }
    }
}
</style>
