<template>
    <div class="hospital-patient-info-wrapper">
        <div class="edit-table">
            <abc-form ref="form">
                <abc-descriptions :column="tableColumn" :label-width="96" grid>
                    <template #title>
                        <div class="table-header-info">
                            <div class="left">
                                患者信息
                            </div>
                            <div
                                v-if="isShowSocialReadCardIcon"
                                class="read-panel"
                                @click="handleOpenReadPanel"
                            >
                                <abc-icon icon="read-card" size="14"></abc-icon>
                            </div>
                            <div v-if="!noChangeData && patientOrderId && !readonly" class="right">
                                <abc-button
                                    type="text"
                                    style="color: #005ed9;"
                                    size="small"
                                    @click="handleUpdatePatientInfo"
                                >
                                    保存
                                </abc-button>
                                <abc-button
                                    type="text"
                                    style="color: #7a8794;"
                                    size="small"
                                    @click="handleCancel"
                                >
                                    取消
                                </abc-button>
                            </div>
                        </div>
                    </template>
                    <abc-descriptions-item label="姓名" content-class-name="patient-name-item">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            justify="space-between"
                            align="center"
                        >
                            <span>{{ postData.name }}</span>
                            <span>{{ postData.sex }}</span>
                        </abc-flex>
                        <template v-else>
                            <abc-form-item required class="patient-name">
                                <patient-autocomplete
                                    v-if="newAdd"
                                    ref="autoComplete"
                                    v-model="postData.name"
                                    :max-length="40"
                                    :distinguish-half-angle-length="true"
                                    :can-edit="!isFromOutpatient"
                                    :readonly="!!postData.id"
                                    :disabled="(!isFromNurse && !isFromChargeRegister) || (isFromNurse && allowEdit)"
                                    @selectPatient="selectPatient"
                                    @enter="enterEvent"
                                >
                                </patient-autocomplete>
                                <abc-input
                                    v-else
                                    v-model="postData.name"
                                    :max-length="40"
                                    :distinguish-half-angle-length="true"
                                    :disabled="!isCanModifyNameInCrm"
                                    @enter="enterEvent"
                                >
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item required class="patient-sex">
                                <abc-select
                                    v-model="postData.sex"
                                    :adaptive-width="true"
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                >
                                    <abc-option label="男" value="男"></abc-option>
                                    <abc-option label="女" value="女"></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </template>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="出生日期">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.birthday }}
                        </abc-flex>
                        <abc-form-item v-else :required="requireConfig.birthday.required">
                            <birthday-picker
                                v-model="postData.birthday"
                                placeholder=""
                                :clearable="false"
                                :disabled="disabled"
                                @change="changeBirthday"
                                @enter="enterEvent"
                            >
                            </birthday-picker>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="年龄">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                            :gap="8"
                        >
                            <span>{{ postData.age.year }}岁</span>
                            <span>{{ postData.age.month }}月</span>
                            <span>{{ postData.age.day }}天</span>
                        </abc-flex>
                        <template v-else>
                            <abc-form-item
                                :required="!postData.age.month && !postData.age.day && postData.age.day !== 0"
                                class="patient-age"
                            >
                                <abc-input
                                    v-model.number="postData.age.year"
                                    class="age"
                                    type="number"
                                    :width="52"
                                    :disabled="disabled"
                                    :config="{
                                        supportZero: true, max: 199
                                    }"
                                    @enter="enterEvent"
                                    @input="changeAge"
                                >
                                </abc-input>
                                <span class="input-append-unit">岁</span>
                            </abc-form-item>
                            <abc-form-item
                                :required="!postData.age.year && !postData.age.day && postData.age.day !== 0"
                                class="patient-age"
                                :validate-event="validateMonth"
                            >
                                <abc-input
                                    v-model.number="postData.age.month"
                                    class="age"
                                    type="number"
                                    :width="44"
                                    :disabled="disabled"
                                    :config="{
                                        supportZero: true, max: 11
                                    }"
                                    @enter="enterEvent"
                                    @input="changeAge"
                                >
                                </abc-input>
                                <span class="input-append-unit">月</span>
                            </abc-form-item>
                            <abc-form-item
                                :required="!postData.age.month && !postData.age.year"
                                class="patient-age"
                                :validate-event="validateDay"
                            >
                                <abc-input
                                    v-model.number="postData.age.day"
                                    class="age"
                                    type="number"
                                    :width="46"
                                    :disabled="disabled"
                                    :config="{
                                        supportZero: true, max: 30
                                    }"
                                    @enter="enterEvent"
                                    @input="changeAge"
                                >
                                </abc-input>
                                <span class="input-append-unit">天</span>
                            </abc-form-item>
                        </template>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="出生地">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ addressStr(postData.birthAddress) }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item>
                                <abc-address-selector
                                    v-model.trim="postData.birthAddress"
                                    clearable
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-address-selector>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="籍贯">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ addressStr(postData.ancestralAddress) }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item>
                                <abc-address-selector
                                    v-model.trim="postData.ancestralAddress"
                                    clearable
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-address-selector>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="国籍">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.nationality }}
                        </abc-flex>
                        <abc-form-item v-else required>
                            <abc-select
                                v-model="postData.nationality"
                                :adaptive-width="true"
                                clearable
                                :disabled="disabled"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in nationalityOptions"
                                    :key="item.label"
                                    :value="item.label"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="民族">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.ethnicity }}
                        </abc-flex>
                        <abc-form-item v-else :required="requireConfig.ethnicity.required">
                            <abc-select
                                v-model="postData.ethnicity"
                                :adaptive-width="true"
                                clearable
                                :disabled="disabled"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in ethnicityOptions"
                                    :key="item"
                                    :value="item"
                                    :label="item"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="婚姻">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ maritalStr(postData.marital) }}
                        </abc-flex>
                        <abc-form-item v-else :required="requireConfig.marital.required">
                            <abc-select
                                v-model="postData.marital"
                                :adaptive-width="true"
                                clearable
                                :disabled="disabled"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in maritalOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="证件">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.idCard }}
                        </abc-flex>
                        <abc-form-item v-else :validate-event="_validateIdCard" :required="requireConfig.certificates.required">
                            <abc-certificates-type
                                ref="crm-id-card"
                                v-model.trim="postData.idCard"
                                :cert-type.sync="postData.idCardType"
                                :cert-type-width="78"
                                auto-width
                                class="bed-hospital-id-card-input"
                                :disabled="disabled || (!newAdd && !isCanModifyIdCardInCrm)"
                                :is-disabled-cert-type="disabled || (!newAdd && !isCanModifyIdCardInCrm)"
                                @enter="enterEvent"
                                @input="handleIdCardInput"
                            ></abc-certificates-type>
                        </abc-form-item>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="手机号">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.mobile }}
                        </abc-flex>
                        <abc-form-item v-else :validate-event="validateMobile" :required="requireConfig.mobile.required">
                            <abc-input
                                v-model.trim="postData.mobile"
                                :max-length="11"
                                type="phone"
                                :disabled="disabled"
                                @enter="enterEvent"
                            ></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="医保号" content-class-name="display-item ellipsis" :class="{ 'shebao-card-info-disabled': disabled }">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.shebaoCardInfo?.cardNo || '' }}
                        </abc-flex>
                        <div v-else-if="postData.shebaoCardInfo && postData.shebaoCardInfo.cardNo" class="text ellipsis">
                            {{ postData.shebaoCardInfo.cardNo }}
                        </div>
                        <div v-else class="shebao-card-info ellipsis">
                            读卡后绑定
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="来源">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ cascaderStr(cascaderValue) }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item :required="requireConfig.sourceInfo.required">
                                <abc-cascader
                                    ref="visit-source-cascader"
                                    v-model="cascaderValue"
                                    :class="{
                                        'hospital-visit-source-cascader-disabled': disabled,
                                    }"
                                    :props="{
                                        children: 'children',
                                        label: 'name',
                                        value: 'id'
                                    }"
                                    :adaptive-width="true"
                                    panel-width="154px"
                                    clearable
                                    placeholder=""
                                    separation="-"
                                    :disabled="disabled"
                                    :options="sourceList"
                                    @enter="enterEvent"
                                >
                                    <div class="visit-source-edit-wrapper">
                                        <abc-icon
                                            v-if="isClinicAdmin"
                                            icon="set"
                                            size="14"
                                            color="#94979B"
                                            class="icon"
                                            @click="handleVisitSourceEdit"
                                        ></abc-icon>

                                        <abc-popover
                                            v-else
                                            trigger="hover"
                                            placement="top-start"
                                            :popper-style="{
                                                zIndex: 99999
                                            }"
                                            theme="yellow"
                                        >
                                            <abc-icon
                                                slot="reference"
                                                icon="set"
                                                size="14"
                                                color="#94979B"
                                            ></abc-icon>

                                            <span>修改本次推荐请联系管理员</span>
                                        </abc-popover>
                                    </div>
                                </abc-cascader>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="户口地址">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ addressStr(postData.registerAddress) }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item>
                                <abc-address-selector
                                    v-model.trim="postData.registerAddress"
                                    clearable
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-address-selector>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="详细地址">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.registerAddress.addressDetail }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item>
                                <abc-input
                                    v-model.trim="postData.registerAddress.addressDetail"
                                    :max-length="50"
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="户口邮编">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.registerAddress.addressPostcode }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item :validate-event="validatePostCode">
                                <abc-input
                                    v-model.trim="postData.registerAddress.addressPostcode"
                                    :max-length="6"
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="家庭住址" content-class-name="address-item">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ addressStr(postData.address) }}
                        </abc-flex>
                        <abc-form-item v-else :required="requireConfig.address.required">
                            <abc-address-selector
                                v-model="postData.address"
                                clearable
                                :disabled="disabled"
                                @enter="enterEvent"
                            ></abc-address-selector>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="详细地址">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.address.addressDetail }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item :required="requireConfig.address.required">
                                <abc-input
                                    v-model.trim="postData.address.addressDetail"
                                    :max-length="50"
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="家庭邮编">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.address.addressPostcode }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item :validate-event="validatePostCode">
                                <abc-input
                                    v-model.trim="postData.address.addressPostcode"
                                    :max-length="6"
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="家庭电话">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.familyMobile }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item :validate-event="validateMobile">
                                <abc-input
                                    v-model.trim="postData.familyMobile"
                                    :max-length="11"
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="邮箱">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.email }}
                        </abc-flex>
                        <abc-form-item v-else :validate-event="validateEmail">
                            <abc-input
                                v-model="postData.email"
                                @enter="enterEvent"
                            ></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="身高">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.height }}
                        </abc-flex>
                        <abc-form-item v-else>
                            <abc-input
                                v-model="postData.height"
                                type="number"
                                :config="{
                                    max: 300,
                                    formatLength: 2
                                }"
                                @enter="enterEvent"
                            >
                                <template #appendInner>
                                    cm
                                </template>
                            </abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="体重">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.weight }}
                        </abc-flex>
                        <abc-form-item v-else>
                            <abc-input
                                v-model="postData.weight"
                                @enter="enterEvent"
                            >
                                <template #appendInner>
                                    kg
                                </template>
                            </abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="人群分类">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.crowdCategory }}{{ (postData.crowdCategory === '其他' && postData.crowdCategoryRemark) ? `(${postData.crowdCategoryRemark})` : '' }}
                        </abc-flex>
                        <abc-form-item v-else>
                            <abc-space
                                v-if="postData.crowdCategory === '其他'"
                                is-compact
                                compact-block
                                border-style="solid"
                            >
                                <abc-form-item>
                                    <abc-select
                                        v-model="postData.crowdCategory"
                                        :width="144"
                                        :max-height="234"
                                        @enter="enterEvent"
                                    >
                                        <abc-option
                                            v-for="item in populationAttributeOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                            :width="144"
                                            :panel-max-height="200"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item>
                                    <abc-input v-model="postData.crowdCategoryRemark" :max-length="60"></abc-input>
                                </abc-form-item>
                            </abc-space>
                            <abc-select
                                v-else
                                v-model="postData.crowdCategory"
                                :width="144"
                                :max-height="234"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in populationAttributeOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                    :width="144"
                                    :panel-max-height="200"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="职业">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.profession }}
                        </abc-flex>
                        <abc-form-item v-else required>
                            <abc-select
                                v-model="postData.profession"
                                :adaptive-width="true"
                                clearable
                                :disabled="disabled"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in professionOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="单位">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.company }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item :required="requireConfig.company.required">
                                <abc-input
                                    v-model.trim="postData.company"
                                    :max-length="50"
                                    :disabled="disabled"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="单位电话">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.companyMobile }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item :validate-event="validateMobile">
                                <abc-input
                                    v-model.trim="postData.companyMobile"
                                    :disabled="disabled"
                                    :max-length="11"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="单位地址">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ addressStr(postData.companyAddress) }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item>
                                <abc-address-selector
                                    v-model="postData.companyAddress"
                                    :disabled="disabled"
                                    clearable
                                    @enter="enterEvent"
                                ></abc-address-selector>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="详细地址">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.companyAddress.addressDetail }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item>
                                <abc-input
                                    v-model.trim="postData.companyAddress.addressDetail"
                                    :disabled="disabled"
                                    :max-length="50"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="单位邮编">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.companyAddress.addressPostcode }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item :validate-event="validatePostCode">
                                <abc-input
                                    v-model.trim="postData.companyAddress.addressPostcode"
                                    :disabled="disabled"
                                    :max-length="6"
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="联系人">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.contactName }}
                        </abc-flex>
                        <abc-form-item v-else required>
                            <abc-input
                                v-model.trim="postData.contactName"
                                :disabled="disabled"
                                :max-length="20"
                                @enter="enterEvent"
                            ></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="联系人电话">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.contactMobile }}
                        </abc-flex>
                        <abc-form-item v-else required :validate-event="validateMobile">
                            <abc-input
                                v-model.trim="postData.contactMobile"
                                :disabled="disabled"
                                :max-length="11"
                                type="phone"
                                @enter="enterEvent"
                            ></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="联系人关系">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.contactRelation }}
                        </abc-flex>
                        <abc-form-item v-else required>
                            <abc-select
                                v-model="postData.contactRelation"
                                :adaptive-width="true"
                                :fetch-suggestions="handleSearchRelationOptions"
                                with-search
                                class="select-contact-relation"
                                clearable
                                :disabled="disabled"
                                @enter="enterEvent"
                                @change="handleChangeContactRelation"
                            >
                                <abc-option
                                    v-for="item in currentRelationOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="联系人地址" :span="tableColumn === 4 ? 2 : 1">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ addressStr(postData.contactAddress) }}
                        </abc-flex>
                        <div v-else class="value">
                            <abc-form-item>
                                <abc-address-selector
                                    v-model="postData.contactAddress"
                                    :disabled="disabled"
                                    clearable
                                    @enter="enterEvent"
                                ></abc-address-selector>
                            </abc-form-item>
                        </div>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="详细地址" :span="tableColumn === 4 ? 2 : 1">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.contactAddress.addressDetail }}
                        </abc-flex>
                        <abc-form-item v-else>
                            <abc-input
                                v-model.trim="postData.contactAddress.addressDetail"
                                :max-length="50"
                                :disabled="disabled"
                                @enter="enterEvent"
                            ></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>

                    <abc-descriptions-item label="备注" :span="tableColumn" content-class-name="remark-item">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.remark }}
                        </abc-flex>
                        <abc-form-item v-else :required="requireConfig.remark.required">
                            <abc-input
                                v-model.trim="postData.remark"
                                :disabled="disabled"
                                :max-length="50"
                            ></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                </abc-descriptions>
            </abc-form>
        </div>

        <!-- 就诊来源管理弹窗 -->
        <visit-source-dialog
            v-if="isShowVisitSourceDialog"
            :is-show.sync="isShowVisitSourceDialog"
            :patient-source-type="patientSourceType"
            @close="isShowVisitSourceDialog = false"
        ></visit-source-dialog>
    </div>
</template>

<script>
    import PatientAutocomplete from '@/views/layout/patient/patient-autocomplete/patient-autocomplete';
    import BirthdayPicker from '@/views/layout/birthday-picker/birthday-picker';
    import { DEFAULT_CERT_TYPE } from '@/views/crm/constants';
    import inputSelect from 'views/common/input-select';

    import {
        age2birthday, birthday2age, formatAge, getMarital, parseTime,
    } from 'utils/index';
    import {
        validateMobile, validateEmail,
    } from 'utils/validate';
    import ethnicityOptions from '@/assets/configure/nation-row';
    import nationalityOptions from '@/assets/configure/nationality';
    import {
        maritalOptions, professionOptions, contactRelationOptions, populationAttributeOptions,
    } from '@/views/crm/data/options';
    import { RecommendService } from '@/service/recommend';
    import {
        mapGetters, mapActions,
    } from 'vuex';
    import Clone from 'utils/clone';
    import CrmAPI from 'api/crm';
    import propertyAPI from 'api/property';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { isEqual } from 'utils/lodash';
    import MixinModulePermission from 'views/permission/module-permission';
    import VisitSourceDialog from 'views/registration/visit-source-dialog';
    import IdCardReaderService from 'views/layout/read-card/id-card-reader/id-card-reader-service';

    import { convertEmptyStringToNull } from '@/utils/convert-empty-string-to-null.js';
    const defaultData = {
        id: '',
        name: '',
        sex: '男',
        birthday: '',
        age: {
            year: '',
            month: '',
            day: '',
        },
        // 出生地
        birthAddress: {
            addressCityId: '',
            addressCityName: '',
            addressDetail: '',
            addressDistrictId: '',
            addressDistrictName: '',
            addressGeo: '',
            addressPostcode: '',
            addressProvinceId: '',
            addressProvinceName: '',
        },
        // 籍贯
        ancestralAddress: {
            addressCityId: '',
            addressCityName: '',
            addressDetail: '',
            addressDistrictId: '',
            addressDistrictName: '',
            addressGeo: '',
            addressPostcode: '',
            addressProvinceId: '',
            addressProvinceName: '',
        },
        // 户口地址
        registerAddress: {
            addressCityId: '',
            addressCityName: '',
            addressDetail: '',
            addressDistrictId: '',
            addressDistrictName: '',
            addressGeo: '',
            addressPostcode: '',
            addressProvinceId: '',
            addressProvinceName: '',
        },
        // 单位地址
        companyAddress: {
            addressCityId: '',
            addressCityName: '',
            addressDetail: '',
            addressDistrictId: '',
            addressDistrictName: '',
            addressGeo: '',
            addressPostcode: '',
            addressProvinceId: '',
            addressProvinceName: '',
        },
        // 单位电话
        companyMobile: '',
        // 家庭住址
        address: {
            addressCityId: '',
            addressCityName: '',
            addressDetail: '',
            addressProvinceId: '',
            addressProvinceName: '',
            addressDistrictId: '',
            addressDistrictName: '',
            addressGeo: '',
            addressPostcode: '',
        },
        // 家庭电话
        familyMobile: '',
        // 联系人地址
        contactAddress: {
            addressCityId: '',
            addressCityName: '',
            addressDetail: '',
            addressDistrictId: '',
            addressDistrictName: '',
            addressGeo: '',
            addressPostcode: '',
            addressProvinceId: '',
            addressProvinceName: '',
        },
        nationality: '中国', // 国籍，默认中国
        ethnicity: '', // 民族
        marital: '',
        company: '',
        profession: '',
        sourceId: null,
        sourceFrom: null,
        idCard: '',
        idCardType: '',
        mobile: '',
        shebaoCardInfo: null,
        contactName: '',
        contactMobile: '',
        contactRelation: '',
        remark: '',
        weight: '',
        height: '',
        crowdCategory: '',
        crowdCategoryRemark: '',
    };

    export default {
        name: 'PatientInfo',
        components: {
            PatientAutocomplete,
            BirthdayPicker,
            VisitSourceDialog,
        },
        mixins: [
            inputSelect,
            MixinModulePermission,
        ],
        provide() {
            return {
                main: this,
            };
        },
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            newAdd: {
                type: Boolean,
                default: false,
            },
            isFromOutpatient: {
                type: Boolean,
                default: false,
            },
            isFromNurse: {
                type: Boolean,
                default: false,
            },
            isFromChargeRegister: {
                type: Boolean,
                default: false,
            },
            isEditPatient: {
                type: Boolean,
                default: false,
            },
            patientInfo: {
                type: Object,
                default: () => {
                },
            },
            tableColumn: {
                type: Number,
                default: 3,
            },
            patientHospitalList: {
                type: Object,
                default: () => {
                },
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            patientModify: {
                type: Boolean,
                default: false,
            },
            allowEdit: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                formatAge,
                ethnicityOptions,
                nationalityOptions,
                populationAttributeOptions,
                maritalOptions,
                professionOptions,
                contactRelationOptions,
                searchRelationKey: '',
                sourceList: [],
                cascaderValue: [],
                isCorrectIdCard: false, // 身份证是否校验成功
                postData: Clone(defaultData),
                cachePostData: null,
                isShowVisitSourceDialog: false,
                patientSourceType: [],
            };
        },
        computed: {
            ...mapGetters(['isEnableIdCardReader', 'isCanModifyNameInCrm', 'isCanModifyIdCardInCrm']),
            ...mapGetters('crm', ['crmConfigList']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            modifyCrmNameConfirmText() {
                return this.viewDistributeConfig.CRM.modifyCrmNameConfirmText;
            },
            requireConfig() {
                const model = {
                    certificates: {
                        required: false,
                    },
                    ethnicity: {
                        required: false,
                    },
                    marital: {
                        required: false,
                    },
                    address: {
                        required: false,
                    },
                    company: {
                        required: false,
                    },
                    remark: {
                        required: false,
                    },
                    birthday: {
                        required: false,
                    },
                    mobile: {
                        required: false,
                    },
                    sourceInfo: {
                        required: false,
                    },
                };
                // 根据crmConfigList设置必填状态
                for (const key in model) {
                    if (model.hasOwnProperty(key)) {
                        model[key].required = !!this.crmConfigList?.[key]?.required || false;
                    }
                }
                return model;
            },
            // 是否有修改信息
            noChangeData() {
                const postData = Clone(this.postData);
                const cachePostData = Clone(this.cachePostData);
                if (postData?.address) {
                    postData.address = convertEmptyStringToNull(postData?.address);
                }
                if (cachePostData?.address) {
                    cachePostData.address = convertEmptyStringToNull(cachePostData?.address);
                }
                if (['', null].includes(postData?.addressDetail)) {
                    postData.addressDetail = null;
                }
                if (['', null].includes(cachePostData?.addressDetail)) {
                    cachePostData.addressDetail = null;
                }
                return isEqual(postData, cachePostData);
            },
            showPatientSource() {
                if (!this.patientInfo?.patientSource) return '';

                return this.initCascaderData(this.patientInfo?.patientSource).map((o) => o.label).join(' - ');
            },
            showAddress() {
                const {
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                    addressDetail,
                } = this.patientDetailInfo?.address || {};
                const arr = [];
                if (addressProvinceName) arr.push(addressProvinceName);
                if (addressCityName) arr.push(addressCityName);
                if (addressDistrictName) arr.push(addressDistrictName);
                if (arr.length !== 0) {
                    return `${arr.join('/')}  ${addressDetail}`;
                }
                return '-';
            },
            addressDetail: {
                get() {
                    return this.postData.address?.addressDetail || '';
                },
                set(val) {
                    this.postData.address.addressDetail = val;
                },
            },
            patientDetailInfo() {
                return this.getPostData();
            },
            isShowSocialReadCardIcon() {
                return IdCardReaderService.isEnable() && !this.isEditPatient && !this.patientOrderId;
            },
            currentRelationOptions() {
                if (!this.searchRelationKey) {
                    return this.contactRelationOptions;
                }
                return this.contactRelationOptions.filter((option) => option.label.includes(this.searchRelationKey));
            },
        },
        watch: {
            patientInfo: {
                async handler(val) {
                    await this.getListSource();
                    if (val) {
                        this.postData = this.getPostData();
                        this.cascaderValue = this.initCascaderData(this.patientInfo?.patientSource);
                        this.cachePostData = Clone(this.postData);
                    }
                },
                deep: true,
                immediate: true,
            },
            noChangeData(val) {
                this.$emit('update:patientModify', !!val);
            },
            cascaderValue(val) {
                if (val.length) {
                    if (val.length > 2) {
                        this.postData.sourceId = val[val.length - 2].value;
                        this.postData.sourceFrom = val[val.length - 1] ? val[val.length - 1].value : null;
                    } else if (['顾客推荐', '员工推荐', '医生推荐', '转诊医生'].includes(val[0].label)) {
                        this.postData.sourceId = val[0].value;
                        this.postData.sourceFrom = val[1] ? val[1].value : null;
                    } else {
                        this.postData.sourceId = val[val.length - 1].value;
                        this.postData.sourceFrom = null;

                    }
                } else {
                    this.postData.sourceId = null;
                    this.postData.sourceFrom = null;
                }
            },
            'postData.registerAddress.addressDistrictId': {
                async handler(val) {
                    const postalCode = await this.getAddressPostcode(val);
                    this.$set(this.postData.registerAddress, 'addressPostcode', postalCode);
                },
            },
            'postData.address.addressDistrictId': {
                async handler(val) {
                    const postalCode = await this.getAddressPostcode(val);
                    this.$set(this.postData.address, 'addressPostcode', postalCode);

                },
            },
            'postData.companyAddress.addressDistrictId': {
                async handler(val) {
                    const postalCode = await this.getAddressPostcode(val);
                    this.$set(this.postData.companyAddress, 'addressPostcode', postalCode);
                },
            },
        },
        methods: {
            ...mapActions('crm', ['fetchCrmConfigList']),
            validateMobile,
            validateEmail,
            _validateIdCard(values, callback) {
                const [certType = '', certNo = ''] = values;
                if (!certNo) {
                    return callback({ validate: !this.requireConfig.certificates.required });
                }
                return this.$refs?.['crm-id-card']?.validateCertNo(certType, certNo, callback);
            },
            parseTime,
            getMarital,
            selectPatient(patient) {
                if (patient?.id) {
                    this.getPatientInfo(patient.id);
                    this.fetchInHospitalList(patient.id);
                } else {
                    this.postData = Clone(defaultData);
                    this.cascaderValue = this.initCascaderData({
                        chainId: '',
                        id: '',
                        name: '',
                        sourceId: '',
                        sourceFrom: '',
                    });
                    this.$emit('clear-crm');
                }
            },
            async getPatientInfo(paramsPatientId) {
                try {
                    const patientId = this.patientId || paramsPatientId;
                    const { data } = await CrmAPI.fetchPatientOverview(patientId);
                    if (data) {
                        data.patientSource = data.patientSource || {
                            chainId: '',
                            id: '',
                            name: '',
                            sourceId: '',
                            sourceFrom: '',
                        };
                        const {
                            birthAddress,
                            ancestralAddress,
                            registerAddress,
                            address,
                            companyAddress,
                            contactAddress,
                            nationality,
                        } = Clone(defaultData);
                        data.birthAddress = data.birthAddress || birthAddress;
                        data.ancestralAddress = data.ancestralAddress || ancestralAddress;
                        data.registerAddress = data.registerAddress || registerAddress;
                        data.address = data.address || address;
                        data.companyAddress = data.companyAddress || companyAddress;
                        data.contactAddress = data.contactAddress || contactAddress;
                        data.nationality = data.nationality || nationality;
                        this.postData = Object.assign(this.postData, data);
                        this.cascaderValue = this.initCascaderData(data.patientSource);
                        this.$emit('change-crm', patientId);
                    }
                } catch (e) {
                    console.log('getPatientInfo Error');
                }
            },
            async fetchInHospitalList(patientId) {
                try {
                    const { data } = await PatientOrderAPI.getInHospitalList({ patientId }) || {};
                    this.$emit('update:patient-hospital-list', data);
                } catch (error) {
                    console.log('获取住院次数&历史失败', error);
                }
            },
            getPostData(detailInfo = null) {
                const postData = Clone(defaultData);
                const patientData = this.patientInfo || detailInfo;
                if (patientData) {
                    const {
                        id,
                        name,
                        mobile,
                        sex,
                        idCard,
                        idCardType,
                        company,
                        contactName,
                        contactMobile,
                        contactRelation,
                        marital,
                        weight,
                        profession,
                        sn,
                        age,
                        patientSource,
                        shebaoCardInfo,
                        address,
                        remark,
                        nationality, // 国籍
                        ethnicity, // 民族
                        birthAddress,
                        ancestralAddress,
                        registerAddress,
                        companyAddress,
                        contactAddress,
                        companyMobile,
                        familyMobile,
                        height,
                        email,
                        crowdCategory,
                        crowdCategoryRemark,
                    } = Clone(patientData);
                    let { birthday } = patientData;
                    if (!birthday && (age.year || age.month || age.day)) {
                        birthday = age2birthday(age);
                    }
                    postData.id = id || '';
                    postData.name = name || '';
                    postData.mobile = mobile || '';
                    postData.sex = sex || '男';
                    postData.birthday = birthday || '';
                    postData.idCard = idCard || '';
                    postData.idCardType = idCardType || DEFAULT_CERT_TYPE;
                    postData.company = company || '';
                    postData.contactName = contactName || '';
                    postData.contactMobile = contactMobile || '';
                    postData.contactRelation = contactRelation || '';
                    postData.nationality = nationality || postData.nationality;
                    postData.ethnicity = ethnicity || '';
                    postData.marital = marital || '';
                    postData.weight = weight || '';
                    postData.profession = profession || '';
                    postData.sn = sn || '';
                    postData.remark = remark || '';
                    postData.shebaoCardInfo = shebaoCardInfo || null;
                    postData.companyMobile = companyMobile;
                    postData.familyMobile = familyMobile;
                    postData.height = height || '';
                    postData.email = email || '';
                    postData.crowdCategory = crowdCategory || '';
                    postData.crowdCategoryRemark = crowdCategoryRemark || '';
                    if (age) {
                        postData.age.year = age.year || 0;
                        postData.age.month = age.month || 0;
                        postData.age.day = age.day || 0;
                    }
                    if (idCard) {
                        this.correctIdCard(idCard);
                    }
                    if (patientSource) {
                        postData.sourceId = patientSource.id || '';
                        postData.sourceFrom = patientSource.sourceFrom || null;
                    }
                    if (address) {
                        postData.address = address;
                    }
                    if (birthAddress) {
                        postData.birthAddress = birthAddress;
                    }
                    if (ancestralAddress) {
                        postData.ancestralAddress = ancestralAddress;
                    }
                    if (registerAddress) {
                        postData.registerAddress = registerAddress;
                    }
                    if (companyAddress) {
                        postData.companyAddress = companyAddress;
                    }
                    if (contactAddress) {
                        postData.contactAddress = contactAddress;
                    }
                }
                return postData;
            },

            validateMonth(value, callback) {
                if (!this.postData.age.month) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-1])$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            validateDay(value, callback) {
                if (!this.postData.age.day) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-9]|2[0-9]|30)$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多30天',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            changeBirthday(birthday) {
                if (birthday) {
                    const {
                        year,
                        month,
                        day,
                    } = birthday2age(birthday);
                    this.handleUnionAge(year, month, day);
                } else {
                    // 清空操作
                    this.postData.age.year = 0;
                    this.postData.age.month = 0;
                    this.postData.age.day = 0;
                }
            },
            handleUnionAge(year, month, day) {
                if (year) {
                    this.postData.age.year = year || 0;
                    this.postData.age.month = month || 0;
                    this.postData.age.day = day || 0;
                } else {
                    this.postData.age.year = 0;
                    this.postData.age.month = month || 0;
                    this.postData.age.day = day || 0;
                }
            },
            changeAge() {
                const {
                    year,
                    month,
                    day,
                } = this.postData.age;
                if (!year && !month && !day) return;
                this.postData.birthday = age2birthday(this.postData.age);
            },
            handleIdCardInput(val) {
                if (![15, 18].includes(val?.length) || this.postData.idCardType !== DEFAULT_CERT_TYPE) return;
                if (val) {
                    this.correctIdCard(val, this.postData.idCardType);
                    this.$nextTick(() => {
                        if (this.isCorrectIdCard) {
                            this.handleBirthday(val, val?.length);
                            this.handleSex(val);
                        }
                    });
                }
            },
            // 校验身份证是否正确
            correctIdCard(idCard, idCardType) {
                this._validateIdCard([idCardType, idCard], (res) => {
                    this.isCorrectIdCard = res.validate;
                });
            },

            handleBirthday(data, len) {
                const idBirthday = this.getIdBirthday(data, len);
                this.postData.birthday = idBirthday;
                this.changeBirthday(idBirthday);
            },
            handleSex(val) {
                let genderCode;
                if (val.length === 15) {
                    genderCode = val.charAt(val.length - 1);
                } else {
                    genderCode = val.charAt(val.length - 2);
                }
                this.postData.sex = genderCode % 2 === 0 ? '女' : '男';
            },
            getIdBirthday(data, len) {
                let arr;
                //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
                if (len === 15) {
                    const reg15 = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
                    arr = data.match(reg15);
                } else {
                    //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
                    const reg18 = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
                    arr = data.match(reg18);
                }
                const year = arr[2];
                const month = arr[3];
                const day = arr[4];
                const idBirthday = len === 15 ? `${`19${year}-${month}-${day}`}` : `${`${year}-${month}-${day}`}`;
                return idBirthday;
            },
            handleUpdatePatientInfo() {
                const data = Clone(this.postData);
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        if (this.postData?.id && this.postData?.name !== this.cachePostData?.name) {
                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                confirmText: '确定修改',
                                content: this.modifyCrmNameConfirmText,
                                onConfirm: () => {
                                    this.$emit('update-patient-info', data);
                                },
                            });
                            return;
                        }
                        this.$emit('update-patient-info', data);
                    }
                });
            },
            handleCancel() {
                this.postData = this.getPostData();
                this.postData = Clone(this.cachePostData);
                this.cascaderValue = this.initCascaderData(this.patientInfo?.patientSource);
                this.$refs.form.clearValidate();
            },
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();

                }
                this.patientSourceType = this.sourceList = RecommendService.getInstance().cascaderOptions;
            },

            initCascaderData(patientSource) {
                if (!patientSource) return [];

                const {
                    id,
                    sourceFromName,
                    sourceFrom,
                    name,
                } = patientSource;

                return RecommendService.getInstance().initCascaderValue({
                    visitSourceId: id,
                    visitSourceName: name,
                    visitSourceFrom: sourceFrom,
                    visitSourceFromName: sourceFromName,
                });
            },

            async fetchAllDoctor() {
                await RecommendService.getInstance().structureOriginOptions();
                this.patientSourceType = this.sourceList = RecommendService.getInstance().cascaderOptions;
            },

            handleVisitSourceEdit() {
                this.$refs['visit-source-cascader'].outside();
                this.isShowVisitSourceDialog = true;
            },

            /**
             * @desc 支持回车进入下一个
             */
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                const inputs = $('.hospital-patient-info-wrapper .abc-input__inner').not(':disabled');
                let targetIndex = -1;
                if (e.target.className === 'select-inner-input') {
                    [...inputs].forEach((item, index) => {
                        if (item?.parentNode?.className?.includes('select-contact-relation')) {
                            targetIndex = index;
                        }
                    });
                } else {
                    targetIndex = inputs.index(e.target);
                }

                let nextInput = inputs[targetIndex + 1];

                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }

                nextInput && this.$nextTick(() => {
                    nextInput.focus();
                });
            },

            async handleOpenReadPanel() {
                const {
                    shebaoCardInfo,
                    patient,
                } = await IdCardReaderService.read({
                    confirmText: '确定',
                });
                if (patient && patient.id) {
                    this.selectPatient(patient);
                } else {
                    this.postData = Object.assign(this.postData, {
                        name: shebaoCardInfo.name,
                        sex: shebaoCardInfo.sex,
                        birthday: shebaoCardInfo.birthday,
                        idCard: shebaoCardInfo.idCardNo,
                        shebaoCardInfo,
                    });
                }
            },

            // 搜索联系人关系
            handleSearchRelationOptions(key) {
                this.searchRelationKey = key;
            },

            handleChangeContactRelation(relation) {
                // 没有联系人地址时默认填充家庭地址
                if (relation === '本人') {
                    if (!this.postData.contactAddress.addressProvinceId &&
                        !this.postData.contactAddress.addressCityId &&
                        !this.postData.contactAddress.addressDistrictId
                    ) {
                        this.postData.contactAddress = Clone(this.postData.address);
                    }
                }
            },
            validatePostCode(value, callback) {
                value = value.trim();
                if (!value) {
                    callback({
                        validate: true,
                    });
                } else {
                    callback({
                        message: '输入正确的邮编',
                        validate: /^\d{6}$/.test(value),
                    });
                }
            },
            // 获取邮编
            async getAddressPostcode(regionId) {
                try {
                    if (!regionId) return '';
                    const { data } = await propertyAPI.getAddressPostcode(regionId);
                    const { postalCode } = data || {};
                    return postalCode || '';
                } catch (e) {
                    console.error(e);
                    return '';
                }
            },
            //获取住址
            addressStr(address) {
                let addressStr = '';
                if (address) {
                    const {
                        addressProvinceName,
                        addressCityName,
                        addressDistrictName,
                    } = address;
                    addressStr = (addressProvinceName || '') + (addressCityName || '') + (addressDistrictName || '');
                }
                return addressStr;
            },
            maritalStr(marital) {
                const findItem = this.maritalOptions.find((item) => item.value === marital);
                return findItem?.label || '';
            },
            cascaderStr(cascader) {
                let cascaderStr = '';
                if (cascader && cascader.length) {
                    cascaderStr = `${cascader[0].label} `;
                    if (cascader[1]) {
                        cascaderStr += `- ${cascader[1].label} `;
                    }
                }
                return cascaderStr;
            },
        },
        created() {
            this.fetchCrmConfigList();
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.hospital-patient-info-wrapper {
    width: 100%;

    .abc-descriptions__title {
        height: 40px;
    }

    .hospital-visit-source-cascader-disabled {
        .cis-icon-dropdown_triangle {
            display: none !important;
        }
    }

    .table-header-info {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .read-panel {
            cursor: pointer;
        }

        .right {
            font-weight: 400;
        }
    }

    .edit-table {
        .abc-descriptions-item__content:not(.display-item) {
            padding: 0 !important;
        }

        .shebao-card-info {
            color: $T2;

            &-disabled {
                cursor: not-allowed;
            }
        }

        .patient-name-item {
            display: flex;

            .abc-form-item {
                &.patient-name {
                    flex: 1;

                    .abc-form-item-content {
                        .abc-autocomplete-wrapper.is-disabled {
                            .abc-input__inner {
                                cursor: not-allowed !important;
                                background-color: $S2 !important;
                            }
                        }
                    }
                }

                &.patient-sex {
                    width: 46px;

                    .abc-input__inner {
                        border-left: 1px dashed $P6;
                    }
                }
            }
        }

        .address-item {
            height: 32px;
        }

        .abc-form-item {
            margin: 0;

            &:not(.patient-age) {
                width: 100%;
            }

            .birthday-picker {
                .abc-date-picker {
                    width: 100%;

                    .abc-date-picker__input {
                        .abc-input__inner {
                            width: 100% !important;
                            padding-right: 8px;
                            padding-left: 8px;
                        }
                    }
                }

                .cis-icon-calendar {
                    display: none;
                }
            }

            .address-selector,
            .abc-input-wrapper {
                width: 100% !important;
            }

            .abc-input__inner {
                padding-right: 9px;
                padding-left: 9px;
                border: 1px solid transparent;
                border-radius: 0;
            }

            .input-append-unit {
                position: absolute;
                top: 0;
                right: 0;
                z-index: 2;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 100%;
            }

            .abc-form-item-content {
                .is-disabled {
                    cursor: not-allowed;

                    .abc-input__inner {
                        cursor: not-allowed !important;
                        background-color: $S2 !important;
                    }

                    .cis-icon-dropdown_triangle {
                        display: none !important;
                    }
                }
            }
        }
    }

    .display-table {
        .abc-descriptions-wrapper {
            background-color: #f9fafc;
        }

        .name-sex-wrapper {
            display: flex;
            height: 32px;
            padding: 0 10px !important;
            line-height: 32px;

            .name-sex {
                display: flex;
                justify-content: space-between;
                width: 100%;

                .patient-name {
                    flex: 1;
                }

                .patient-sex {
                    width: 46px;
                    padding-left: 8px;
                    border-left: 1px dashed $P6;
                }
            }
        }
    }

    .item-readonly {
        flex: 1;
        height: 100%;
        padding: 0 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .bed-hospital-id-card-input {
        ::v-deep .cert-type-select {
            width: 78px !important;
        }

        .cert-type-select {
            width: 78px !important;
        }
    }
}
</style>
