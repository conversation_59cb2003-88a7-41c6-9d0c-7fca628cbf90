<template>
    <div
        ref="card-item"
        class="bed-patient-card-wrapper"
        @click="handleSelectBed"
    >
        <div class="bed-patient-card-body">
            <div class="card-header">
                <div class="left">
                    <div v-if="isUseed" class="patient-info">
                        <div v-if="patientInfo && patientInfo.sex" class="img-wrapper">
                            <img v-if="patientInfo.sex === '女'" src="~assets/images/hospital/<EMAIL>" />
                            <img v-else src="~assets/images/hospital/<EMAIL>" />
                        </div>
                        <abc-flex vertical :gap="2" style="padding-left: 12px;">
                            <div class="patient-name">
                                {{ patientInfo && patientInfo.name }}
                            </div>
                            <abc-space style="font-size: 12px; line-height: 14px;">
                                <div v-if="patientInfo && patientInfo.age" class="patient-age">
                                    {{ patientInfo && formatAge(patientInfo.age, {
                                        monthYear: 12, dayYear: 1
                                    }) }}
                                </div>
                                <div class="fee-type">
                                    {{ feeTypeDisplayValue }}
                                </div>
                            </abc-space>
                        </abc-flex>
                    </div>
                    <div v-if="isUse" class="is-lock" @click.stop="handleBedReserve">
                        <abc-icon
                            icon="lock"
                            size="14"
                            color="#7a8794"
                        ></abc-icon>
                    </div>
                    <div v-if="canUse" class="unlock-status">
                        <abc-icon
                            icon="unlock"
                            size="14"
                            color="#aab4bf"
                            @click.stop="handleBedReserve"
                        ></abc-icon>
                    </div>
                </div>
                <abc-flex vertical :gap="2">
                    <abc-flex align="center">
                        <div class="bed-ward-room-name" style="margin-right: 6px;">
                            {{ bedInfo.wardRoomName }}
                        </div>
                        <div class="bed-no">
                            {{ `${bedInfo.bedNo}`.padStart(2, 0) }}
                        </div>
                    </abc-flex>
                    <div class="bed-ward-room-name" style="text-align: right;">
                        {{ patientOrderHospital && patientOrderHospital.no }}
                    </div>
                </abc-flex>
            </div>
            <div class="card-body">
                <template v-if="isUseed">
                    <div class="bed-patient-card-content">
                        <div class="row diagnosis-info">
                            <div class="left">
                                <label class="label">诊断:</label>
                                <div class="value diagnosis" @mouseenter="getTextWidth($event,diagnosisInfo)">
                                    <abc-popover
                                        :width="`${diagnosisWidth}px`"
                                        placement="top-start"
                                        trigger="hover"
                                        theme="yellow"
                                        :open-delay="500"
                                        :arrow-offset="-10"
                                        popper-class="diagnosis-reference"
                                        :disabled="isDisabled"
                                    >
                                        <span slot="reference">{{ diagnosisInfo }}</span>
                                        <div>{{ diagnosisInfo }}</div>
                                    </abc-popover>
                                </div>
                            </div>
                            <div class="right">
                                {{ patientOrderHospital && patientOrderHospital.inpatientTime | parseTime('y-m-d', true) }}入
                            </div>
                        </div>
                        <div class="row in-hospital-info">
                            <div class="left">
                                <div class="doctor-name">
                                    <label class="label">医生:</label>
                                    <span class="value ellipsis">{{ patientOrderHospital && patientOrderHospital.doctorName }}</span>
                                </div>
                                <div class="nurse-name">
                                    <label class="label">护士:</label>
                                    <span class="value ellipsis">{{ patientOrderHospital && patientOrderHospital.nurseName }}</span>
                                </div>
                            </div>

                            <div class="right">
                                <span v-if="patientOrderHospital && patientOrderHospital.inpatientDays" class="inpatient-days">{{ `${ patientOrderHospital && patientOrderHospital.inpatientDays } 天` }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="bed-patient-card-footer">
                        <div class="footer-left">
                            <patient-status-tag v-if="!newOrOutTags.length" :tag-id="PatientOrderHospitalTagEnum.NEW_ENTRANTS" disabled></patient-status-tag>
                            <template v-else>
                                <patient-status-tag v-for="item in newOrOutTags" :key="item.id" :tag-id="item.id"></patient-status-tag>
                            </template>
                            <patient-status-tag v-if="!careLevelTags.length" :tag-id="PatientOrderHospitalTagEnum.PRIMARY_CARE" disabled></patient-status-tag>
                            <template v-else>
                                <patient-status-tag v-for="item in careLevelTags" :key="item.id" :tag-id="item.id"></patient-status-tag>
                            </template>
                            <patient-status-tag v-if="!criticalTags.length" :tag-id="PatientOrderHospitalTagEnum.CONDITION_ZHONG" disabled></patient-status-tag>
                            <template v-else>
                                <patient-status-tag v-for="item in criticalTags" :key="item.id" :tag-id="item.id"></patient-status-tag>
                            </template>
                            <patient-status-tag v-if="!arrearsTags.length" :tag-id="PatientOrderHospitalTagEnum.ARREARS" disabled></patient-status-tag>
                            <template v-else>
                                <patient-status-tag v-for="item in arrearsTags" :key="item.id" :tag-id="item.id"></patient-status-tag>
                            </template>
                        </div>
                        <div class="footer-right">
                            <patient-status-tag v-for="item in todoTags" :key="item.id" :tag-id="item.id"></patient-status-tag>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="use-status can-use-status">
                        <img v-if="canUse" class="bed-img" src="~assets/images/hospital/<EMAIL>" />
                        <template v-if="isUse">
                            <div class="use-reason">
                                {{ reserveReasonObj[bedInfo.reserveReason] }}
                            </div>
                            <div class="time">
                                {{ `${formatDate(bedInfo.reserveBeginTime, 'MM/DD HH:mm')}` }}
                                <span v-if="bedInfo.reserveEndTime">{{ `~ ${formatDate(bedInfo.reserveEndTime,'MM/DD HH:mm')}` }}</span>
                            </div>
                        </template>
                        <template v-else-if="isDisabledBed">
                            <div class="disabled-status">
                                <div class="use-reason">
                                    已停用
                                </div>
                                <div class="disabled-desc">
                                    {{ '如需启用请前往“管理”设置' }}
                                </div>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
    import PatientStatusTag from 'views/layout/patient/patient-status-tag';

    import {
        bedUseStatusEnum, reserveReasonObj, PatientOrderHospitalTagEnum, FeeTypeNameEnum,
    } from '@/views-hospital/beds/utils/constant.js';
    import { formatAge } from 'utils/index';
    import {
        formatDate, parseTime,
    } from '@abc/utils-date';

    export default {
        name: 'BedPatientCard',
        components: {
            PatientStatusTag,
        },
        filters: {
            parseTime,
        },
        props: {
            bedInfo: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                formatAge,
                formatDate,
                PatientOrderHospitalTagEnum,
                bedUseStatusEnum,
                reserveReasonObj,
                diagnosisWidth: 0,
                isDisabled: true,
            };
        },
        computed: {
            patientOrderHospital() {
                return this.bedInfo?.patientOrderHospital;
            },
            diagnosisInfo() {
                let str = '';
                this.patientOrderHospital?.primaryDiagnosisInfos?.[0]?.value?.forEach((item) => {
                    str += item.name;
                });
                return str;
            },
            patientInfo() {
                return this.bedInfo?.patientOrderHospital?.patient;
            },
            tags() {
                const {
                    WAIT_CHECKED,WAIT_EXECUTE,
                } = PatientOrderHospitalTagEnum;
                return this.patientOrderHospital?.tags?.filter((item) => ![WAIT_CHECKED,WAIT_EXECUTE].includes(item.id)) || [];
            },
            newOrOutTags() {
                const {
                    NEW_ENTRANTS,LEAVE_HOSPITAL,
                } = PatientOrderHospitalTagEnum;
                return this.patientOrderHospital?.tags?.filter((item) => [NEW_ENTRANTS,LEAVE_HOSPITAL].includes(item.id)) || [];
            },
            careLevelTags() {
                const {
                    PREMIUM_CARE,PRIMARY_CARE,SECONDARY_CARE,TERTIARY_CARE,
                } = PatientOrderHospitalTagEnum;
                return this.patientOrderHospital?.tags?.filter((item) => [PREMIUM_CARE,PRIMARY_CARE,SECONDARY_CARE,TERTIARY_CARE].includes(item.id)) || [];
            },
            criticalTags() {
                const {
                    CONDITION_WEI,CONDITION_ZHONG,
                } = PatientOrderHospitalTagEnum;
                return this.patientOrderHospital?.tags?.filter((item) => [CONDITION_WEI,CONDITION_ZHONG].includes(item.id)) || [];
            },
            arrearsTags() {
                return this.patientOrderHospital?.tags?.filter((item) => item.id === PatientOrderHospitalTagEnum.ARREARS) || [];
            },
            todoTags() {
                const {
                    WAIT_CHECKED,WAIT_EXECUTE,
                } = PatientOrderHospitalTagEnum;
                return this.patientOrderHospital?.tags?.filter((item) => [WAIT_CHECKED,WAIT_EXECUTE].includes(item.id)) || [];
            },
            isDisabledBed() {
                return !this.bedInfo?.enableStatus;
            },
            canUse() {
                return this.bedInfo?.useStatus === bedUseStatusEnum.CAN_USE && !this.isDisabledBed;
            },
            isUse() {
                return this.bedInfo?.useStatus === bedUseStatusEnum.IS_USE;
            },
            isUseed() {
                return this.bedInfo?.useStatus === bedUseStatusEnum.IS_USEED;
            },
            feeTypeDisplayValue() {
                if (!this.patientOrderHospital) {
                    return '';
                }
                if (this.patientOrderHospital.feeTypeName === FeeTypeNameEnum.SELF_PAY) {
                    return '自费';
                }
                if (this.patientOrderHospital.feeTypeName === FeeTypeNameEnum.NATIONAL_MEDICAL_INSURANCE) {
                    const insutypeWordingShort = this.patientOrderHospital.shebaoCardInfo?.extend?.insutypeWordingShort;
                    let str = '医保';
                    if (insutypeWordingShort) {
                        str += `(${insutypeWordingShort})`;
                    }
                    return str;
                }
                return '自费';
            },
        },
        methods: {
            handleBedReserve() {
                this.$emit('bed-reserve');
            },
            handleSelectBed() {
                // 停用 / 留用
                if (this.isDisabledBed || this.isUse) return;

                if (this.canUse) {
                    this.$emit('to-assign-patient');
                } else {
                    this.$emit('selected');
                }
            },
            getTextWidth(e,text,fontSize = 12) {
                this.diagnosisWidth = e.target.offsetWidth;
                const _span = document.createElement('span');
                _span.innerText = text;
                _span.setAttribute('style', `position: absolute; font-size: ${fontSize}px`);
                document.body.appendChild(_span);
                const width = parseFloat(getComputedStyle(_span).width);
                document.body.removeChild(_span);
                this.isDisabled = parseFloat(getComputedStyle(e.target).width) >= width;
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.bed-patient-card-wrapper {
    display: flex;
    flex-direction: column;
    height: 141px;
    cursor: pointer;
    background-color: $S2;
    border: 1px solid $abcCardBorderColor;
    border-radius: 5px;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06);

    &:hover {
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.1);

        .bed-patient-card-body {
            .card-header {
                .left {
                    .unlock-status {
                        .abc-icon {
                            display: inline-block;
                        }
                    }
                }
            }

            .card-body {
                .can-use-status {
                    .abc-icon {
                        display: inline-block;
                    }
                }
            }
        }
    }

    .bed-patient-card-body {
        display: flex;
        flex: 1;
        flex-direction: column;

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 32px;
            margin: 11px 12px 0;

            .left {
                .patient-info {
                    display: flex;
                    align-items: center;

                    .img-wrapper {
                        min-width: 32px;
                        max-width: 32px;
                        min-height: 32px;
                        max-height: 32px;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .patient-name {
                        max-width: 75px;
                        height: 16px;
                        overflow: hidden;
                        font-size: 15px;
                        font-weight: bold;
                        line-height: 16px;
                        color: $S1;
                    }

                    .patient-age,
                    .fee-type {
                        font-size: 12px;
                        color: $T2;
                    }
                }

                .unlock-status {
                    .abc-icon {
                        display: none;
                    }
                }

                .is-lock,
                .unlock-status {
                    padding: 5px;
                    border-radius: var(--abc-border-radius-small);

                    &:hover {
                        background-color: $P4;
                    }

                    &:active {
                        background-color: $P6;
                    }
                }
            }

            .bed-no {
                font-size: 15px;
                font-weight: bold;
                line-height: 16px;
                color: $S1;
                text-align: right;
            }

            .bed-ward-room-name {
                font-size: 12px;
                line-height: 14px;
                color: var(--abc-color-T2);
            }
        }

        .card-body {
            flex: 1;

            .use-status,
            .can-use-status,
            .disabled-status {
                height: 100%;
            }

            .bed-patient-card-content {
                padding: 16px 0 11px;
                margin: 0 12px;
                font-size: 12px;
                font-weight: 400;
                border-bottom: 1px solid $P4;

                .row {
                    display: flex;
                    font-size: 12px;
                    line-height: 16px;

                    .label {
                        margin-right: 8px;
                        line-height: 16px;
                        color: $T2;
                        cursor: pointer;
                    }

                    .value {
                        color: $T2;

                        &.diagnosis {
                            flex: 1;

                            @include ellipsis;

                            div:first-child {
                                @include ellipsis;
                            }
                        }
                    }

                    &.in-hospital-info {
                        display: flex;
                        justify-content: space-between;

                        .left {
                            display: flex;

                            .doctor-name,
                            .nurse-name {
                                display: inline-flex;

                                .value {
                                    width: 60px;
                                }
                            }

                            .doctor-name {
                                margin-right: 8px;
                            }
                        }

                        .right {
                            .inpatient-days {
                                font-size: 12px;
                                font-weight: 400;
                                color: $T2;
                            }
                        }
                    }
                }

                .diagnosis-info {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 6px;

                    .left {
                        display: flex;
                        flex: 1;
                        overflow: hidden;
                    }

                    .right {
                        margin-left: 8px;
                        font-size: 12px;
                        font-weight: 400;
                        color: $T2;
                    }
                }
            }

            .use-status,
            .disabled-status {
                display: flex;
                flex-direction: column;
                align-items: center;

                .bed-img {
                    width: 32px;
                    height: 20px;
                    margin-top: 22px;
                }

                .use-reason {
                    height: 20px;
                    margin-top: 12px;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    color: $S1;
                }

                .time,
                .disabled-desc {
                    height: 12px;
                    margin-top: 4px;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 16px;
                    color: $T3;
                }
            }

            .can-use-status {
                .abc-icon {
                    display: none;
                }
            }

            .bed-patient-card-footer {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 34px;
                padding: 8px 12px;
                line-height: 18px;

                .footer-left,
                .footer-right {
                    display: flex;

                    .patient-status-tag-wrapper {
                        & + .patient-status-tag-wrapper {
                            margin-left: 4px;
                        }
                    }
                }
            }
        }
    }
}
</style>
