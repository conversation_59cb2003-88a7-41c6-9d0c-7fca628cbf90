<template>
    <abc-popover
        ref="bed-filter-popover"
        placement="bottom-start"
        trigger="click"
        theme="white"
        width="562px"
        :visible-arrow="false"
        :popper-style="{ padding: 0 }"
        @hide="closeBedFilterPopover"
    >
        <div slot="reference" class="filter-icon-wrapper">
            <span class="text-desc" :class="{ 'text-desc2': !notFilterParams }">筛选</span>
            <span v-if="isShowIcon('男','sex')" class="filter-icon male">男</span>
            <span v-if="isShowIcon('女','sex')" class="filter-icon female">女</span>
            <span v-if="isShowIcon(PatientOrderHospitalTagEnum.PREMIUM_CARE,'level')" class="filter-icon te-hu">特</span>
            <span v-if="isShowIcon(PatientOrderHospitalTagEnum.PRIMARY_CARE,'level')" class="filter-icon one-hu">I </span>
            <span v-if="isShowIcon(PatientOrderHospitalTagEnum.SECONDARY_CARE,'level')" class="filter-icon two-hu">II</span>
            <span v-if="isShowIcon(PatientOrderHospitalTagEnum.TERTIARY_CARE,'level')" class="filter-icon three-hu">III</span>
            <span v-if="isShowIcon(PatientOrderHospitalTagEnum.MODERATE_FALL_RISK,'score')" class="filter-icon middle-die">跌</span>
            <span v-if="isShowIcon(PatientOrderHospitalTagEnum.HIGH_FALL_RISK,'score')" class="filter-icon high-die">跌</span>
            <span v-if="isShowIcon(PatientOrderHospitalTagEnum.RISK_OF_PIPELINE_SLIPPING,'score')" class="filter-icon pipeline-slipping-score">管</span>
            <span v-if="isShowIcon(bedUseStatusEnum.IS_USE,'bedStatus')" class="filter-icon"><abc-icon icon="lock" size="14" color="#7a8795"></abc-icon></span>
            <span v-if="isShowIcon(bedUseStatusEnum.CAN_USE,'bedStatus')" class="filter-icon"><abc-icon icon="unlock" size="14" color="#d9dbe3"></abc-icon></span>
            <span v-if="isShowIcon(bedUseStatusEnum.IS_USEED,'bedStatus')" class="filter-icon"><abc-icon icon="patient" size="14" color="#aab4c0"></abc-icon></span>
            <abc-icon icon="dropdown_triangle" size="14" color="#aab4bf"></abc-icon>
        </div>

        <div class="bed-filter">
            <ul class="filter-options-wrapper">
                <li v-for="item in filterList" :key="item.id">
                    <label class="label-title">{{ item.title }}</label>
                    <div
                        v-for="options in item.optionsList"
                        :key="options.label"
                        class="item"
                        @click="handleSelect(options,item.type)"
                    >
                        <span class="value" :class="{ 'is-select': isSelect(options.value,item.type) }">
                            {{ options.label }}
                            <span v-if="options.count" class="count">({{ options.count }})</span>
                        </span>
                    </div>
                </li>
            </ul>

            <div class="filter-footer">
                <abc-button size="small" style="margin-left: auto;" @click="changeHandler">
                    筛选
                </abc-button>
                <abc-button size="small" type="blank" @click="resetHandler">
                    清空
                </abc-button>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import {
        PatientOrderHospitalTagEnum,bedUseStatusEnum,
    } from '@/views-hospital/beds/utils/constant';
    import Clone from 'utils/clone';

    const InitStatus = Object.freeze({
        sex: [],
        level: [],
        score: [],
        bedStatus: [],
    });

    export default {
        name: 'BedFilterPopper',
        props: {
            filterStatus: {
                type: Object,
                required: true,
            },
            bedList: {
                type: Array,
                default: () => [],
            },
            isNurseBed: {
                type: Boolean,
                default: false,
            },
            doctorId: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                bedUseStatusEnum,
                PatientOrderHospitalTagEnum,
                status: null,
                cacheStatus: null,
            };
        },
        computed: {
            notFilterParams() {
                const {
                    sex = [],level = [],score = [],bedStatus = [],
                } = this.filterStatus;
                return !(sex.length || level.length || score.length || bedStatus.length);
            },
            maleCount() {
                if (this.isNurseBed) {
                    return (this.bedList?.filter((item) => item?.patientOrderHospital?.patient?.sex === '男') || []).length;
                }
                let arr = [];
                this.bedList.forEach((wardArea) => {
                    let list = wardArea.beds?.filter((item) => item?.patientOrderHospital?.patient?.sex === '男') || [];
                    if (this.doctorId) {
                        list = list.filter((item) => item.patientOrderHospital?.doctorId === this.doctorId);
                    }
                    arr = [...arr,...list];
                });
                return arr.length;
            },
            femaleCount() {
                if (this.isNurseBed) {
                    return (this.bedList?.filter((item) => item?.patientOrderHospital?.patient?.sex === '女') || []).length;
                }
                let arr = [];
                this.bedList.forEach((wardArea) => {
                    let list = wardArea.beds?.filter((item) => item?.patientOrderHospital?.patient?.sex === '女') || [];
                    if (this.doctorId) {
                        list = list.filter((item) => item.patientOrderHospital?.doctorId === this.doctorId);
                    }
                    arr = [...arr,...list];
                });
                return arr.length;
            },
            filterList() {
                const arr = [{
                    id: 2,
                    title: '患者性别',
                    type: 'sex',
                    optionsList: [{
                        label: '男性',
                        value: '男',
                        count: this.maleCount,
                        icon: 'patient',
                        iconColor: '#58a0ff',
                    },{
                        label: '女性',
                        value: '女',
                        count: this.femaleCount,
                        icon: 'patient',
                        iconColor: '#ff6082',
                    }],
                },{
                    id: 3,
                    title: '护理等级',
                    type: 'level',
                    optionsList: [{
                        label: '特级护理',
                        value: PatientOrderHospitalTagEnum.PREMIUM_CARE,
                        count: this.getCareCount(PatientOrderHospitalTagEnum.PREMIUM_CARE),
                        customRemarkClass: 'te-hu',
                        customRemark: '特',
                    },{
                        label: '一级护理',
                        value: PatientOrderHospitalTagEnum.PRIMARY_CARE,
                        count: this.getCareCount(PatientOrderHospitalTagEnum.PRIMARY_CARE),
                        customRemarkClass: 'one-hu',
                        customRemark: 'I',
                    },{
                        label: '二级护理',
                        value: PatientOrderHospitalTagEnum.SECONDARY_CARE,
                        count: this.getCareCount(PatientOrderHospitalTagEnum.SECONDARY_CARE),
                        customRemarkClass: 'two-hu',
                        customRemark: 'II',
                    },{
                        label: '三级护理',
                        value: PatientOrderHospitalTagEnum.TERTIARY_CARE,
                        count: this.getCareCount(PatientOrderHospitalTagEnum.TERTIARY_CARE),
                        customRemarkClass: 'three-hu',
                        customRemark: 'III',
                    }],
                },
                //    产品体验（本次不支持就隐藏）
                //     {
                //     id: 4,
                //     title: '护理评分',
                //     type: 'score',
                //     optionsList: [{
                //         label: '中度跌倒风险',
                //         value: PatientOrderHospitalTagEnum.MODERATE_FALL_RISK,
                //         count: this.getCareCount(PatientOrderHospitalTagEnum.MODERATE_FALL_RISK),
                //         customRemarkClass: 'middle-die',
                //         customRemark: '跌',
                //     },{
                //         label: '重度跌倒风险',
                //         value: PatientOrderHospitalTagEnum.HIGH_FALL_RISK,
                //         count: this.getCareCount(PatientOrderHospitalTagEnum.HIGH_FALL_RISK),
                //         customRemarkClass: 'high-die',
                //         customRemark: '跌',
                //     },{
                //         label: '管路滑脱评分<9',
                //         value: PatientOrderHospitalTagEnum.RISK_OF_PIPELINE_SLIPPING,
                //         count: this.getCareCount(PatientOrderHospitalTagEnum.RISK_OF_PIPELINE_SLIPPING),
                //         customRemarkClass: 'pipeline-slipping-score',
                //         customRemark: '管',
                //     }],
                // }
                ];
                if (this.isNurseBed) {
                    arr.push({
                        id: 5,
                        title: '床位状态',
                        type: 'bedStatus',
                        optionsList: [{
                            label: '锁定',
                            value: bedUseStatusEnum.IS_USE,
                            count: this.getBedStatusCount(bedUseStatusEnum.IS_USE),
                            icon: 'lock',
                            iconColor: '#7a8795',
                        },{
                            label: '可分配床位',
                            value: bedUseStatusEnum.CAN_USE,
                            count: this.getBedStatusCount(bedUseStatusEnum.CAN_USE),
                            icon: 'unlock',
                            iconColor: '#d9dbe3',
                        },{
                            label: '已分配',
                            value: bedUseStatusEnum.IS_USEED,
                            count: this.getBedStatusCount(bedUseStatusEnum.IS_USEED),
                            icon: 'patient',
                            iconColor: '#aab4c0',
                        }],
                    });
                }
                return arr;
            },
        },
        watch: {
            filterStatus: {
                handler() {
                    this.initStatus();
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            getCareCount(tagId) {
                if (this.isNurseBed) {
                    return (this.bedList?.filter((item) => item?.patientOrderHospital?.tags?.find((tagItem) => tagItem.id === tagId)) || []).length;
                }
                let arr = [];
                this.bedList.forEach((wardArea) => {
                    let list = wardArea.beds?.filter((item) => item?.patientOrderHospital?.tags?.find((tagItem) => tagItem.id === tagId)) || [];
                    if (this.doctorId) {
                        list = list.filter((item) => item.patientOrderHospital?.doctorId === this.doctorId);
                    }
                    arr = [...arr,...list];
                });
                return arr.length;
            },
            getBedStatusCount(useStatus) {
                return (this.bedList?.filter((item) => item?.useStatus === useStatus) || []).length;
            },
            showIcon(type) {
                let flag = false;
                switch (type) {
                    case 'sex':
                    case 'bedStatus':
                        flag = true;
                        break;
                    case 'level':
                    case 'score':
                        flag = false;
                        break;
                    default:
                        break;
                }
                return flag;
            },
            initStatus() {
                this.status = Clone(this.filterStatus);
                this.cacheStatus = Clone(this.filterStatus);
            },
            handleSelect(item,type) {
                const index = this.status[type].indexOf(item.value);
                if (index === -1) {
                    this.status[type].push(item.value);
                } else {
                    this.status[type].splice(index,1);
                }
            },
            isSelect(val,type) {
                return this.status[type].indexOf(val) !== -1;
            },
            isShowIcon(val,type) {
                return this.filterStatus[type].indexOf(val) !== -1;
            },
            changeHandler() {
                this.$emit('change', this.status);
                this.close();
            },
            resetHandler() {
                this.status = { ...InitStatus };
                this.changeHandler();
            },
            closeBedFilterPopover() {
                this.status = Clone(this.cacheStatus);
            },
            close() {
                this.$refs['bed-filter-popover'].doClose();
            },
        },

    };
</script>
<style lang="scss">
@import 'src/styles/theme';
@import 'styles/abc-common.scss';

.filter-icon-wrapper {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    font-size: 0;
    cursor: pointer;
    border: 1px solid $P1;
    border-radius: var(--abc-border-radius-small);

    &:hover {
        background-color: $P4;
    }

    .text-desc {
        width: 28px;
        height: 14px;
        margin-left: 8px;
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        color: $T2;

        &.text-desc {
            margin-right: 6px;
        }
    }

    .abc-icon {
        width: 21px;
        line-height: 1;
        text-align: center;
    }

    .filter-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 22px;
        height: 24px;
        padding: 0 4px;
        font-size: 14px;
        background-color: $P4;
        border-radius: var(--abc-border-radius-small);

        &:first-child {
            margin-left: 4px;
        }

        & + span {
            margin-left: 2px;
        }

        &.male {
            color: #58a0ff;
        }

        &.female {
            color: #ff6082;
        }

        &.te-hu {
            color: #ff3029;
        }

        &.one-hu,
        &.two-hu,
        &.three-hu {
            padding-top: 1px;
            font-family: MySTZhongsong;
            font-weight: bold;
        }

        &.one-hu {
            color: #ff6914;
        }

        &.two-hu {
            color: #ffc900;
        }

        &.three-hu {
            color: #96d800;
        }

        &.middle-die {
            color: #ff9a1e;
        }

        &.high-die {
            color: #ff3029;
        }

        &.pipeline-slipping-score {
            color: #00d0bf;
        }
    }
}

.bed-filter {
    .filter-options-wrapper {
        padding: 12px 16px;

        > li {
            display: flex;
            align-items: center;
            height: 28px;
            margin-bottom: 8px;
            line-height: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .label-title {
                width: 56px;
                margin-right: 18px;
                font-size: 14px;
                font-weight: bold;
                color: $T2;
            }

            .item {
                & + .item {
                    margin-left: 4px;
                }

                .value {
                    display: flex;
                    align-items: center;
                    padding: 4px 6px;
                    font-size: 14px;
                    font-weight: 400;
                    color: $S1;

                    .count {
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        margin-left: 4px;
                        font-size: 14px;
                        font-weight: 400 !important;
                        color: $T2;
                    }

                    &:hover,
                    &.is-select {
                        cursor: pointer;
                        border-radius: var(--abc-border-radius-small);
                    }

                    &:hover {
                        background-color: $P4;
                    }

                    &.is-select {
                        background-color: $P6;
                    }
                }
            }
        }
    }

    .filter-footer {
        display: flex;
        align-items: center;
        padding: 10px 12px 10px 0;
        text-align: right;
        border-top: 1px solid $P4;

        .abc-button + .abc-button {
            margin-left: 8px;
        }
    }
}
</style>
