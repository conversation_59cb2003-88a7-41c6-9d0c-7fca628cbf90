<template>
    <div class="be-in-hospital-info-wrapper">
        <div class="edit-table">
            <abc-form ref="form">
                <abc-descriptions :column="tableColumn" :label-width="96" grid>
                    <template #title>
                        <div class="table-header-info">
                            <div class="left">
                                住院信息
                                <inpatient-history-popover :inpatient-history="inpatientHistory">
                                    <span class="hospital-record-count">{{ inpatientCount }}</span>
                                </inpatient-history-popover>
                            </div>
                            <div v-if="!noChangeData && patientOrderId && !readonly" class="right">
                                <abc-button
                                    type="text"
                                    style="color: #005ed9;"
                                    size="small"
                                    @click="handleUpdateInPatientInfo"
                                >
                                    保存
                                </abc-button>
                                <abc-button
                                    type="text"
                                    style="color: #7a8794;"
                                    size="small"
                                    @click="handleCancel"
                                >
                                    取消
                                </abc-button>
                            </div>
                        </div>
                    </template>
                    <template v-if="isBedDetail || isRegister">
                        <abc-descriptions-item label="入院时间">
                            <abc-form-item required>
                                <abc-date-time-picker
                                    v-model="postData.inpatientTime"
                                    class="date-time"
                                    date-placeholder=""
                                    time-placeholder=""
                                    :clearable="false"
                                    :disabled="disabled || needEdit "
                                    :space="0"
                                    :show-icon="false"
                                    @enter="enterEvent"
                                >
                                </abc-date-time-picker>
                            </abc-form-item>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="科室" content-class-name="display-item">
                            {{ detailInfo.departmentName }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="病区" content-class-name="display-item ellipsis">
                            {{ detailInfo.wardName }}
                        </abc-descriptions-item>
                        <abc-descriptions-item v-if="isBedDetail" label="床位" content-class-name="display-item bed-no">
                            <span>{{ detailInfo.bedNo }}</span>
                            <bed-transfer-record-popover v-if="bedTransferRecordList.length" :bed-transfer-record-list="bedTransferRecordList">
                                <span class="record">转移记录</span>
                            </bed-transfer-record-popover>
                        </abc-descriptions-item>
                    </template>
                    <template v-else>
                        <abc-descriptions-item label="入院时间">
                            <abc-flex
                                v-if="readonly"
                                class="item-readonly"
                                align="center"
                            >
                                {{ postData.inpatientTimeRequest }}
                            </abc-flex>
                            <abc-form-item v-else required>
                                <abc-date-time-picker
                                    v-model="postData.inpatientTime"
                                    class="date-time"
                                    date-placeholder=""
                                    time-placeholder=""
                                    :clearable="false"
                                    :disabled="disabled || needEdit "
                                    :space="0"
                                    :show-icon="false"
                                    @enter="enterEvent"
                                >
                                </abc-date-time-picker>
                            </abc-form-item>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="科室">
                            <abc-flex
                                v-if="readonly"
                                class="item-readonly"
                                align="center"
                            >
                                {{ departmentStr(postData.departmentId) }}
                            </abc-flex>
                            <abc-form-item v-else required>
                                <abc-select
                                    v-model="postData.departmentId"
                                    :disabled="disabled || needEdit "
                                    placement="top"
                                    @change="changeDepartment"
                                    @enter="enterEvent"
                                >
                                    <abc-option
                                        v-for="item in departmentsList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="病区">
                            <abc-flex
                                v-if="readonly"
                                class="item-readonly"
                                align="center"
                            >
                                {{ wardAreaStr(postData.wardId) }}
                            </abc-flex>
                            <abc-form-item v-else required>
                                <abc-select
                                    v-model="postData.wardId"
                                    class="ward-area"
                                    :disabled="isFromNurseBed || needEdit || disabled"
                                    placement="top"
                                    :show-empty="true"
                                    :empty-text="!postData.departmentId ? '请先选择科室' : '当前科室无病区'"
                                    @enter="enterEvent"
                                >
                                    <abc-option
                                        v-for="item in wardAreaList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>
                    </template>
                    <abc-descriptions-item
                        v-if="!newAdd"
                        label="住院号"
                        :span="1"
                        content-class-name="display-item"
                    >
                        {{ detailInfo.no }}
                    </abc-descriptions-item>
                    <abc-descriptions-item>
                        <abc-space slot="label">
                            结算费别
                            <abc-tooltip-info placement="bottom">
                                <div style="max-width: 250px;">
                                    费别为医保的患者，若在「医保管理」--「设置」--「限价设置」中设置了医保限价规则，将按设置的限价规则计费
                                </div>
                            </abc-tooltip-info>
                        </abc-space>
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ feeTypeStr(postData.feeTypeName) }}
                        </abc-flex>
                        <abc-form-item v-else>
                            <abc-select
                                v-model="postData.feeTypeName"
                                :adaptive-width="true"
                                clearable
                                :disabled="disabled"
                                placement="top"
                                :show-value="feeTypeDisplayValue"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in feeTypeOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item v-if="!isBedDetail" label="建议押金">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ postData.adviceDeposit }}
                        </abc-flex>
                        <abc-form-item v-else>
                            <abc-input
                                v-model.trim="postData.adviceDeposit"
                                type="money"
                                :disabled="disabled"
                                :input-custom-style="{ 'text-align': 'left' }"
                                :config="{
                                    formatLength: 2, max: 99999999, supportZero: true
                                }"
                                @enter="enterEvent"
                            ></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="入院方式">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ inpatientWayStr(postData.inpatientWay) }}
                        </abc-flex>
                        <abc-form-item v-else>
                            <abc-select
                                v-model="postData.inpatientWay"
                                clearable
                                :disabled="disabled"
                                placement="top"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in inpatientWayOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="入院病情">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ inpatientConditionStr(postData.inpatientCondition) }}
                        </abc-flex>
                        <abc-form-item v-else>
                            <abc-select
                                v-model="postData.inpatientCondition"
                                clearable
                                :disabled="disabled"
                                placement="top"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in inpatientConditionOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="入院途径">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ inpatientSourceStr(postData.inpatientCondition) }}
                        </abc-flex>
                        <abc-form-item v-else required>
                            <abc-select
                                v-model="postData.inpatientSource"
                                clearable
                                :disabled="disabled || needEdit"
                                placement="top"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in inpatientSourceOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="门诊医生">
                        <abc-flex
                            v-if="readonly"
                            class="item-readonly"
                            align="center"
                        >
                            {{ inPatientDetailInfo.outpatientDoctorName }}
                        </abc-flex>

                        <abc-form-item v-else required>
                            <abc-select
                                v-model="postData.outpatientDoctorId"
                                :disabled="disabled || needEdit"
                                placement="top"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="item in outpatientDoctorsList"
                                    :key="item.employeeId"
                                    :label="item.employeeName"
                                    :value="item.employeeId"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <template v-if="!newAdd">
                        <abc-descriptions-item label="门诊诊断" :span="isBedDetail ? 1 : 2" content-class-name="display-item ellipsis">
                            <div v-abc-title.ellipsis="outpatientDiagnosisInfo"></div>
                        </abc-descriptions-item>
                    </template>
                    <template v-else>
                        <abc-descriptions-item :span="newAdd ? 3 : 1" label="门诊诊断">
                            <abc-form-item class="diagnosis-info">
                                <extend-diagnosis-infos
                                    v-model="postData.preDiagnosisInfos"
                                    :disabled="disabled"
                                    fixed
                                    :must-match-disease-code="true"
                                >
                                </extend-diagnosis-infos>
                            </abc-form-item>
                        </abc-descriptions-item>
                    </template>
                    <template v-if="isBedDetail">
                        <abc-descriptions-item label="护理等级" content-class-name="display-item">
                            {{ nurseOfCareStr }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="住院医生">
                            <abc-form-item>
                                <abc-select
                                    v-model="postData.doctorId"
                                    clearable
                                    :disabled="disabled"
                                    placement="top"
                                    @enter="enterEvent"
                                >
                                    <abc-option
                                        v-for="item in doctorsList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="住院诊断" content-class-name="display-item ellipsis">
                            <div v-abc-title.ellipsis="inHospitalDiagnosisInfo"></div>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="主管护士">
                            <abc-form-item>
                                <abc-select
                                    v-model="postData.nurseId"
                                    clearable
                                    :disabled="disabled"
                                    placement="top"
                                    @enter="enterEvent"
                                >
                                    <abc-option
                                        v-for="item in nursesList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>
                    </template>
                </abc-descriptions>
            </abc-form>
        </div>
    </div>
</template>

<script>
    import Clone from 'utils/clone';
    import { parseTime } from 'utils/index';
    import inputSelect from 'views/common/input-select';
    import {
        inpatientWayOptions,
        inpatientSourceOptions,
        inpatientConditionOptions,
        feeTypeOptions,
        inpatientWayObj,
        inpatientSourceObj,
        inpatientConditionObj,
        PatientOrderHospitalTagEnum,
        InpatientSourceEnum, FeeTypeNameEnum,
    } from '@/views-hospital/beds/utils/constant.js';

    import { formatDate } from '@abc/utils-date';
    import ExtendDiagnosisInfos from 'views/outpatient/common/medical-record/extend-diagnosis-infos';
    import WardAreaAPI from 'api/hospital/setting/ward-area';
    import InpatientHistoryPopover from '@/views-hospital/beds/components/inpatient-history-popover';
    import BedTransferRecordPopover from '@/views-hospital/beds/components/bed-transfer-record-popover';
    import ClinicAPI from 'api/clinic';
    import { mapState } from 'vuex';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { isEqual } from 'utils/lodash';
    import SettingAPI from 'api/settings';
    import {
        BUSINESS_SCOPE_TYPE, ROLE_HOSPITAL_DOCTOR_ID,
    } from 'utils/constants';

    const defaultData = {
        id: '',
        inpatientTime: '',
        departmentId: '',
        wardId: '', // 病区
        feeTypeName: '', // 费别
        adviceDeposit: '', // 建议押金
        inpatientWay: '', // 入院方式
        inpatientCondition: '', // 入院病情
        inpatientSource: '', // 入院途径
        outpatientDoctorId: '',
        doctorId: '',
        nurseId: '',
        no: '', // 住院号
        preDiagnosisInfos: [{
            value: [{
                code: '',
                diseaseType: '',
                name: '',
            }],
        }],
    };

    export default {
        name: 'BeInHospitalInfo',
        components: {
            ExtendDiagnosisInfos,
            InpatientHistoryPopover,
            BedTransferRecordPopover,
        },
        mixins: [
            inputSelect,
        ],
        props: {
            isEditInPatient: {
                type: Boolean,
                default: false,
            },
            needEdit: {
                type: Boolean,
                default: false,
            },
            formSource: {
                type: String,
                default: '',
            },
            outpatientDoctorId: {
                type: String,
                default: '',
            },
            patientId: {
                type: String,
                default: '',
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            patientHospitalList: {
                type: Object,
                default: () => ({}),
            },
            inPatientDetailInfo: {
                type: Object,
                default: () => ({}),
            },
            newAdd: {
                type: Boolean,
                default: false,
            },
            isBedDetail: {
                type: Boolean,
                default: false,
            },
            tableColumn: {
                type: Number,
                default: 3,
            },
            extendDiagnosisInfos: {
                type: Array,
                default: () => [{ value: [] }],
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            bedHospitalModify: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                formatDate,
                inpatientWayObj,
                inpatientWayOptions,
                inpatientSourceObj,
                inpatientSourceOptions,
                inpatientConditionObj,
                inpatientConditionOptions,
                InpatientSourceEnum,
                feeTypeOptions,
                departmentsList: [],
                wardAreaList: [],
                postData: Clone(defaultData),
                outpatientDoctorsList: [],
                doctorsList: [],
                nursesList: [],
                bedTransferRecordList: [], // 床位转移记录
            };
        },
        computed: {
            ...mapState('hospitalGlobal', ['currentWardAreaId']),

            feeTypeDisplayValue() {
                if (!this.inPatientDetailInfo) {
                    return '';
                }
                if (this.postData.feeTypeName === FeeTypeNameEnum.SELF_PAY) {
                    return '自费';
                }
                if (this.postData.feeTypeName === FeeTypeNameEnum.NATIONAL_MEDICAL_INSURANCE) {
                    const insutypeWordingShort = this.inPatientDetailInfo.shebaoCardInfo?.extend?.insutypeWordingShort;
                    let str = '医保';
                    if (insutypeWordingShort) {
                        str += `(${insutypeWordingShort})`;
                    }
                    return str;
                }
                return '自费';
            },

            isFromOutpatient() {
                return this.formSource === 'outpatient';
            },
            isFromNurseBed() {
                return this.formSource === 'nurseBed';
            },
            isRegister() {
                return this.formSource === 'register';
            },
            isFromChargeRegister() {
                return this.formSource === 'chargeRegister';
            },
            noChangeData() {
                const postData = Clone(this.postData);
                const cachePostData = Clone(this.cachePostData);
                return isEqual(postData, cachePostData);
            },
            outpatientDiagnosisInfo() {
                let str = '';
                this.detailInfo?.preDiagnosisInfos?.[0]?.value?.forEach((item) => {
                    str += item.name;
                });
                return str;
            },
            inHospitalDiagnosisInfo() {
                let str = '';
                this.detailInfo?.primaryDiagnosisInfos?.[0]?.value?.forEach((item) => {
                    str += item.name;
                });
                return str;
            },
            detailInfo() {
                return Clone({
                    ...defaultData,...this.inPatientDetailInfo,
                });
            },
            nurseOfCareStr() {
                const {
                    PREMIUM_CARE,PRIMARY_CARE, SECONDARY_CARE, TERTIARY_CARE,
                } = PatientOrderHospitalTagEnum;
                const arr = [PREMIUM_CARE,PRIMARY_CARE, SECONDARY_CARE, TERTIARY_CARE];
                let str = '';
                this.detailInfo?.tags?.forEach((item) => {
                    if (arr.includes(item.id)) {
                        str = item.name;
                    }
                });
                return str || '';
            },
            // 入院次数
            inpatientCount() {
                let str = '入院：';
                if (this.inPatientDetailInfo?.id) {
                    const {
                        timesOfYear,
                        times,
                        inpatientYear,
                    } = this.inPatientDetailInfo;
                    if (timesOfYear) {
                        str += `${inpatientYear}年第${timesOfYear}次/`;
                    }
                    return times > 0 ? `${str}累计第${times}次` : '';
                }

                const {
                    currentYearTotal,
                    total,
                    inpatientYear,
                } = this.patientHospitalList || {};
                if (currentYearTotal) {
                    str += `${inpatientYear}年第${currentYearTotal}次/`;
                }
                return total > 0 ? `${str}累计第${total + 1}次` : '';
            },
            // 住院历史
            inpatientHistory() {
                return this.patientHospitalList?.rows || [];
            },
        },
        watch: {
            inPatientDetailInfo: {
                handler(val) {
                    if (val) {
                        this.postData = this.getPostData();
                        this.cachePostData = Clone(this.postData);
                        this.fetchWardAreaListByDepartmentId(this.postData?.departmentId);
                        if (this.postData?.wardId) {
                            Promise.all([
                                this.getWardAreaHospitalNurses(this.postData?.wardId),
                            ]);
                        }
                    } else if (this.newAdd) {
                        this.postData = this.getPostData();
                        this.postData.inpatientTime = formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
                        this.cachePostData = Clone(this.postData);
                        this.wardAreaList = [];
                        this.doctorsList = [];
                        this.nursesList = [];
                        if (this.isFromNurseBed) {
                            this.$nextTick(async () => {
                                await this.fetchWardAreaList();

                                this.wardAreaList.forEach((item) => {
                                    if (item.id === this.currentWardAreaId) {
                                        this.postData.wardId = item.id;
                                    }
                                });

                                await this.fetchDepartmentByWardAreaId(this.currentWardAreaId);
                                if (this.departmentsList?.length === 1) {
                                    this.postData.departmentId = this.departmentsList?.[0]?.id;
                                }
                            });
                        }
                    }
                },
                immediate: true,
                deep: true,
            },
            noChangeData(val) {
                this.$emit('update:bedHospitalModify', !!val);
            },
            'postData.departmentId': {
                handler(val) {
                    val && this.getHospitalDoctorsByDepartmentId(val);
                },
                immediate: true,
            },
        },
        async created() {
            if (this.newAdd) {
                this.postData.inpatientTime = formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
                this.postData.preDiagnosisInfos = Clone(this.extendDiagnosisInfos);

                if (this.isFromOutpatient) {
                    this.postData.inpatientSource = InpatientSourceEnum.MEN_ZHEN;
                    this.postData.outpatientDoctorId = this.outpatientDoctorId;
                    await this.fetchHospitalDepartments();
                }

                if (this.isFromChargeRegister) {
                    await this.fetchHospitalDepartments();
                }

                if (this.isFromNurseBed) {
                    await this.fetchWardAreaList();

                    this.wardAreaList.forEach((item) => {
                        if (item.id === this.currentWardAreaId) {
                            this.postData.wardId = item.id;
                        }
                    });

                    await this.fetchDepartmentByWardAreaId(this.currentWardAreaId);
                    if (this.departmentsList?.length === 1) {
                        this.postData.departmentId = this.departmentsList?.[0]?.id;
                    }
                }
            } else {
                await this.fetchHospitalDepartments();
            }

            await this.getHospitalOutpatientDoctors();

            if (this.isBedDetail) {
                await this.fetchBedTransferRecords();
            }
        },
        methods: {
            parseTime,

            // 拉取床位转移记录
            async fetchBedTransferRecords() {
                try {
                    const { data } = await PatientOrderAPI.getHospitalLog(this.patientOrderId);
                    this.bedTransferRecordList = data?.rows ?? [];
                } catch (e) {
                    console.log('fetchRecordList Error');
                }
            },
            async fetchHospitalDepartments() {
                try {
                    const { data } = await SettingAPI.clinic.fetchClinicDepartments({
                        businessScope: BUSINESS_SCOPE_TYPE.HOSPITAL,
                    });
                    this.departmentsList = data?.data?.rows || [];
                } catch (e) {
                    console.log('fetchHospitalDepartments Error', e);
                }
            },

            async fetchDepartmentByWardAreaId(wardAreaId) {
                try {
                    const { data } = await WardAreaAPI.getDepartmentByWardAreaId(wardAreaId) || {};
                    this.departmentsList = data?.rows ?? [];
                } catch (e) {
                    console.log('fetchDepartments Error',e);
                }
            },

            changeDepartment(val) {
                if (this.isFromNurseBed) return;
                this.postData.wardId = '';
                this.wardAreaList = [];
                !!val && this.fetchWardAreaListByDepartmentId(val);
            },

            async fetchWardAreaListByDepartmentId(departmentId) {
                try {
                    const { data } = await WardAreaAPI.getWardAreaListByDepartmentId(departmentId);
                    this.wardAreaList = data?.rows ?? [];
                } catch (e) {
                    console.log('getWardAreaListByDepartmentId Error');
                }
            },

            async fetchWardAreaList() {
                try {
                    const { data } = await WardAreaAPI.getWardAreaList();
                    this.wardAreaList = data?.rows ?? [];
                } catch (e) {
                    console.log('getWardAreaList Error',e);
                }
            },

            // 获取门诊医生列表
            async getHospitalOutpatientDoctors() {
                try {
                    const { data } = await ClinicAPI.fetchHospitalOutpatientDoctors();
                    this.outpatientDoctorsList = data?.rows || [];
                } catch (e) {
                    console.log(e);
                }
            },

            async getHospitalDoctorsByDepartmentId(departmentId) {
                try {
                    const { data } = await WardAreaAPI.getEmployeeListByDepartmentId({
                        businessScope: BUSINESS_SCOPE_TYPE.HOSPITAL,
                        departmentId,
                        roles: [ROLE_HOSPITAL_DOCTOR_ID],
                    }) || {};
                    this.doctorsList = (data?.rows || []).map((it) => ({
                        id: it.employeeId,
                        name: it.employeeName,
                    }));
                } catch (e) {
                    console.log(e);
                }
            },

            async getWardAreaHospitalNurses(wardAreaId) {
                try {
                    const { data } = await WardAreaAPI.getWardAreaHospitalNurses(wardAreaId) || {};
                    this.nursesList = data?.rows || [];
                } catch (e) {
                    console.log(e);
                }
            },

            getPostData() {
                const postData = Clone(defaultData);
                if (this.inPatientDetailInfo) {
                    const {
                        inpatientTime,
                        departmentId,
                        wardId,
                        feeTypeName,
                        adviceDeposit,
                        inpatientWay,
                        inpatientCondition,
                        inpatientSource,
                        outpatientDoctorId,
                        doctorId,
                        nurseId,
                        preDiagnosisInfos,
                    } = this.inPatientDetailInfo || {};
                    postData.inpatientTime = formatDate(inpatientTime,'YYYY-MM-DD HH:mm') || '';
                    postData.departmentId = departmentId || '';
                    postData.wardId = wardId || '';
                    postData.feeTypeName = feeTypeName || '';
                    postData.adviceDeposit = adviceDeposit || '';
                    postData.inpatientWay = inpatientWay || '';
                    postData.inpatientCondition = inpatientCondition || '';
                    postData.inpatientSource = inpatientSource || '';
                    postData.outpatientDoctorId = outpatientDoctorId || '';
                    postData.doctorId = doctorId || '';
                    postData.nurseId = nurseId || '';
                    postData.preDiagnosisInfos = Clone(preDiagnosisInfos) || Clone(this.extendDiagnosisInfos);
                }
                return postData;
            },

            handleUpdateInPatientInfo() {
                const data = Clone(this.postData);
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        // 如果将费目类别改为医保
                        // 需要confirm确认
                        const next = () => {
                            this.$emit('update-in-patient-info',data);
                        };
                        if (this.postData.feeTypeName !== this.cachePostData.feeTypeName && this.postData.feeTypeName === FeeTypeNameEnum.NATIONAL_MEDICAL_INSURANCE) {
                            this.$confirm({
                                title: '费别修改确认',
                                content: '修改费别为医保后，将按医保设置中设置的限价规则计费，确认修改？',
                                type: 'warn',
                                onConfirm: async () => {
                                    next();
                                },
                            });
                        } else {
                            next();
                        }
                    }
                });
            },
            handleCancel() {
                this.postData = Clone(this.cachePostData);
            },

            /**
             * @desc 支持回车进入下一个
             */
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                const inputs = $('.be-in-hospital-info-wrapper .abc-input__inner').not(':disabled');
                const targetIndex = inputs.index(e.target);
                let nextInput = inputs[targetIndex + 1];

                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }

                nextInput && this.$nextTick(() => {
                    nextInput.focus();
                });
            },
            departmentStr(department) {
                const findItem = this.departmentsList.find((item) => item.id === department);
                return findItem?.name || '';
            },
            wardAreaStr(warArea) {
                const findItem = this.wardAreaList.find((item) => item.id === warArea);
                return findItem?.name || '';
            },
            feeTypeStr(feeType) {
                const findItem = this.feeTypeOptions.find((item) => item.value === feeType);
                return findItem?.label || '自费';
            },
            inpatientWayStr(inpatientWay) {
                const findItem = this.inpatientWayOptions.find((item) => item.value === inpatientWay);
                return findItem?.label || '';
            },
            inpatientConditionStr(inpatientCondition) {
                const findItem = this.inpatientConditionOptions.find((item) => item.value === inpatientCondition);
                return findItem?.label || '';
            },
            inpatientSourceStr(inpatientSource) {
                const findItem = this.inpatientSourceOptions.find((item) => item.value === inpatientSource);
                return findItem?.label || '';
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.be-in-hospital-info-wrapper {
    width: 100%;

    .abc-descriptions__title {
        height: 40px;
    }

    .table-header-info {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .left {
            display: inline-flex;

            .hospital-record-count {
                margin-left: 20px;
                font-size: 12px;
                font-weight: normal;
                color: $G2;
                cursor: pointer;
            }
        }

        .right {
            font-weight: 400;
        }
    }

    .edit-table {
        .abc-descriptions-item__content:not(.display-item) {
            padding: 0 !important;
        }

        .display-item {
            cursor: not-allowed;
        }

        .abc-form-item {
            width: 100%;
            margin: 0;

            .abc-form-item-content {
                .is-disabled {
                    cursor: not-allowed;

                    .abc-input__inner {
                        cursor: not-allowed !important;
                        background-color: $S2 !important;
                    }

                    .cis-icon-dropdown_triangle {
                        display: none !important;
                    }
                }
            }

            .abc-input-wrapper {
                width: 100% !important;
            }

            .abc-input__inner {
                padding-right: 9px;
                padding-left: 9px;
                border: 1px solid transparent;
                border-radius: 0;
            }

            &.diagnosis-info {
                .abc-form-item-content {
                    height: 100%;

                    [disabled] {
                        cursor: not-allowed;
                    }
                }

                .aide-diagnosis-wrapper {
                    height: 100%;
                }

                .abc-input__inner {
                    height: 100%;
                }

                .diagnosis-social-info-wrapper {
                    .abc-input__inner {
                        padding: 6px 9px;
                        font-size: 14px;
                        line-height: 18px;
                        background-color: transparent;

                        &:focus {
                            position: relative;
                            z-index: 2;
                            border-color: $theme1;
                        }
                    }
                }
            }

            .abc-date-time-picker {
                .abc-date-picker {
                    width: 60%;

                    .abc-date-picker__input {
                        .abc-input__inner {
                            width: 100% !important;
                            padding-right: 9px;
                            padding-left: 9px;
                        }
                    }

                    .cis-icon-calendar {
                        display: none;
                    }
                }

                .abc-time-picker {
                    width: 40%;
                    border-left: 1px dashed $P6;

                    .abc-input__inner {
                        width: 100% !important;
                    }
                }
            }
        }
    }

    .display-table {
        .abc-descriptions-wrapper {
            background-color: #f9fafc;
        }
    }

    .bed-no {
        display: flex;
        justify-content: space-between;

        .record {
            font-size: 14px;
            font-weight: 400;
            color: $theme1;
            cursor: pointer;
        }
    }
}

.option-item-wrapper {
    .empty-list {
        height: 32px;
        padding: 6px 12px;
        color: $T2;
    }
}

.item-readonly {
    flex: 1;
    height: 100%;
    padding: 0 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
