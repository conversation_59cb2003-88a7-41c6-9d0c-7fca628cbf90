<template>
    <div ref="suggestions" class="hospital-bed-select-wrapper">
        <div v-if="options.length" class="hospital-bed-select-content">
            <template v-for="item in options">
                <abc-popover
                    :key="item.id"
                    placement="bottom-start"
                    trigger="hover"
                    theme="yellow"
                    :close-delay="0"
                    :popper-style="{ 'z-index': 10000 }"
                >
                    <div
                        slot="reference"
                        class="item"
                        :class="[{ 'is-selected': currentValue && currentValue == item.id },
                                 { 'not-use': item.useStatus !== bedUseStatusEnum.CAN_USE || !item.enableStatus }]"
                        @click.stop.prevent="selectItem(item)"
                    >
                        {{ `${item.bedNo}`.padStart(2,'0') }}
                    </div>
                    <div>
                        房间：{{ item.wardRoomName }}
                    </div>
                </abc-popover>
            </template>
        </div>
        <div v-else class="empty-bed-list">
            无可用床位
        </div>
        <div class="footer" @click="handleClose">
            <abc-icon
                icon="delete_circle"
                size="16"
                color="#005ed9"
                style="margin-right: 8px;"
            ></abc-icon>
            关闭
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import Popper from 'utils/vue-popper';
    import { mapGetters } from 'vuex';
    import { bedUseStatusEnum } from '@/views-hospital/beds/utils/constant';

    export default {
        name: 'BedNoOptions',
        mixins: [Popper],
        props: {
            visible: Boolean,
            currentValue: [String, Number],
            options: Array,
        },

        data() {
            return {
                bedUseStatusEnum,
            };
        },
        computed: {
            ...mapGetters([
                'westernMedicineConfig',
            ]),
            GroupSelect() {
                let parent = this.$parent;
                while (parent && parent.$options && parent.$options.name !== 'BedNoSelect') {
                    parent = parent.$parent;
                }
                return parent;
            },
        },
        watch: {
            visible: {
                immediate: true,
                handler(val) {
                    this.showPopper = val;
                },
            },
        },
        mounted() {
            this.$parent.popperElm = this.popperElm = this.$refs.suggestions;
            this.referenceElm = this.$parent.$refs.abcinput;
        },

        beforeDestroy() {
            this.doDestroy();
        },
        methods: {
            selectItem(item) {
                if (item.useStatus !== bedUseStatusEnum.CAN_USE || !item.enableStatus) return;
                this.GroupSelect.$emit('handleOptionClick', item.id);
            },

            handleClose() {
                this.GroupSelect.closePopper();
            },
        },

    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.hospital-bed-select-wrapper {
    position: absolute;
    top: 24px;
    left: 0;
    z-index: 9999;
    box-sizing: border-box;
    width: 350px;
    padding: 0;
    margin-top: 2px;
    background-color: $S2;
    border: 1px solid $P3;
    border-radius: var(--abc-border-radius-small);
    box-shadow: $boxShadowContent;

    .hospital-bed-select-content {
        display: flex;
        flex-wrap: wrap;
        padding: 5px;
        font-size: 14px;

        div {
            &:not(:nth-child(10n + 1)) {
                .item {
                    margin-left: 2px;
                }
            }
        }

        .item {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 32px;
            height: 28px;
            color: $T1;
            cursor: pointer;
            -moz-user-select: none; /* 火狐 */
            -webkit-user-select: none; /* webkit浏览器 */
            -ms-user-select: none; /* IE10 */
            user-select: none;
            border-radius: 2px;

            &:hover:not(.not-use) {
                color: $theme1;
                background-color: $P4;
            }

            &.is-selected {
                color: $theme1;
            }

            &.not-use {
                color: $T3;
            }
        }
    }

    .empty-bed-list {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 48px;
        color: $T2;
        text-align: center;
    }

    .footer {
        height: 40px;
        font-size: 14px;
        font-weight: bold;
        line-height: 40px;
        color: $theme1;
        text-align: center;
        cursor: pointer;
        background-color: #f7f7f7;
    }
}
</style>
