<template>
    <abc-popover
        ref="heightPopoverRef"
        trigger="click"
        :z-index="10000"
        :visible-arrow="false"
        placement="bottom-start"
        theme="white"
        :disabled="disabled"
        popper-class="height-popover-wrapper"
    >
        <template slot="reference">
            <slot name="default"></slot>
        </template>
        <div class="height-record-wrapper">
            <div class="tabs">
                <div
                    v-for="(tab,tabIndex) in heightRecord.tabs"
                    :key="tabIndex"
                    class="tab"
                    :class="{ 'is-selected': currentHeightRecord.selectedTab === tab.id }"
                    @click="handleSelectedHeightTab(tab)"
                >
                    {{ tab.name }}
                </div>
            </div>
            <div class="list" :class="{ 'has-border': fromDaily }">
                <div
                    v-for="(item,index) in currentHeightRecord.list"
                    :key="index"
                    :class="{ 'has-border': fromDaily }"
                    class="list-item"
                >
                    <div class="count" @click=" handleClick(item)">
                        {{ item }}
                    </div>
                </div>
            </div>
        </div>
    </abc-popover>
</template>


<script>
    import {
        heightRecord,
    } from '@/views-hospital/daily/components/temperature/config';
    export default {
        name: 'HeightPopover',
        props: {
            unit: {
                type: String,
                default: '',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            fromDaily: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                heightRecord,
                currentHeightRecord: {
                    selectedTab: 1,
                    list: heightRecord.list,
                },
            };
        },
        methods: {
            handleSelectedHeightTab(tab) {
                this.currentHeightRecord.selectedTab = tab.id;
                this.currentHeightRecord.list = tab.value;
            },
            handleClick(val) {
                this.$emit('click', val);
                this.$refs.heightPopoverRef.doClose();
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.height-popover-wrapper {
    padding: 0;

    .height-record-wrapper {
        display: flex;
        flex-wrap: wrap;
        width: 528px;

        .tabs {
            width: 114px;

            .tab {
                height: 26px;
                padding-left: 6px;
                margin-bottom: 12px;
                line-height: 26px;
                cursor: pointer;

                &.is-selected {
                    background-color: #eff3f6;
                }

                &:hover {
                    background-color: #eff3f6;
                }
            }
        }

        .list {
            display: flex;
            flex: 1;
            flex-wrap: wrap;
            margin-left: 8px;

            &.has-border {
                border-left: 1px solid #eff3f6;
            }

            .list-item {
                display: inline-block;
                width: 45px;
                height: 30px;
                line-height: 30px;

                &.has-border {
                    border-right: 1px solid #eff3f6;
                    border-bottom: 1px solid #eff3f6;
                }
            }

            .count {
                display: inline-block;
                width: 45px;
                height: 30px;
                text-align: center;

                &:hover {
                    cursor: pointer;
                    background-color: $P4;
                }

                &:active {
                    font-weight: bold;
                    background-color: #e5f2ff;
                }
            }
        }

        .unit {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
            color: #d9dbe3;
        }
    }
}
</style>
