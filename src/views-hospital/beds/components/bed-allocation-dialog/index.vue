<template>
    <abc-dialog
        v-if="bedAllocationDialogVisible"
        v-model="bedAllocationDialogVisible"
        title="床位分配"
        append-to-body
        content-styles="width: 376px; padding:24px; min-height: 232px;"
        custom-class="first-bed-allocation-form-dialog"
    >
        <abc-form
            ref="firstBedAllocationForm"
            label-position="left"
            label-width="84"
        >
            <abc-form-item label="病区">
                <span class="inpatient-area ellipsis">{{ wardAreaName }}</span>
            </abc-form-item>
            <abc-form-item label="科室">
                <span class="inpatient-area ellipsis">{{ departmentName }}</span>
            </abc-form-item>
            <abc-form-item label="患者" required>
                <span v-if="isDischargeRecall">{{ patientName }}</span>
                <abc-select
                    v-else
                    v-model="postData.patientId"
                    :width="244"
                    placeholder="请选择"
                    @change="handleSelectPatient"
                >
                    <template v-if="waitAllocatedPatientList.length">
                        <abc-option
                            disabled
                            :value="null"
                            style="color: #8d9aa8;"
                            class="patient-list-item"
                        >
                            待分配
                        </abc-option>
                        <abc-option
                            v-for="(item,index) in waitAllocatedPatientList"
                            :key="item.patient.id + index"
                            :label="item.patient.name"
                            :value="item.patientId"
                            class="patient-list-item"
                        >
                            <span class="name ellipsis">{{ item.patient.name }}</span>
                            <span class="sex">{{ item.patient.sex }}</span>
                            <span class="age">
                                {{ getAgeInfo(item) }}
                            </span>
                            <span v-if="item.inpatientTime" class="time">{{ formatDate(item.inpatientTime, 'MM-DD HH:mm') }}</span>
                        </abc-option>
                    </template>
                    <template v-if="waitTransferPatientList.length">
                        <abc-option
                            disabled
                            :value="null"
                            style="color: #8d9aa8;"
                            class="patient-list-item"
                        >
                            待转科
                        </abc-option>
                        <abc-option
                            v-for="(item,index) in waitTransferPatientList"
                            :key="item.patient.id + index"
                            :label="item.patient.name"
                            :value="item.patient.id"
                            class="patient-list-item"
                        >
                            <span class="name ellipsis">{{ item.patient.name }}</span>
                            <span class="sex">{{ item.patient.sex }}</span>
                            <span class="age">
                                {{ getAgeInfo(item) }}
                            </span>
                            <span v-if="item.inpatientTime" class="time">{{ formatDate(item.inpatientTime, 'MM-DD HH:mm') }}</span>
                        </abc-option>
                    </template>
                </abc-select>
            </abc-form-item>
            <abc-form-item label="床位分配" required>
                <bed-no-select
                    v-model="bedId"
                    :width="244"
                    show-icon
                    :options="bedNoList"
                ></bed-no-select>
            </abc-form-item>
            <abc-form-item label="入院时间" required>
                <abc-date-time-picker
                    v-model="postData.inpatientTime"
                    class="date-picker"
                    :editable="true"
                    :date-width="150"
                    :time-width="90"
                    :time-step="5"
                >
                </abc-date-time-picker>
            </abc-form-item>
            <abc-form-item label="住院医生" required>
                <abc-select v-model="postData.doctorId" :width="244" placeholder="请选择">
                    <abc-option
                        v-for="item in doctorsList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></abc-option>
                </abc-select>
            </abc-form-item>
            <abc-form-item label="主管护士">
                <abc-select v-model="postData.nurseId" :width="244" placeholder="请选择">
                    <abc-option
                        v-for="item in nursesList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></abc-option>
                </abc-select>
            </abc-form-item>
            <template v-if="!isDischargeRecall">
                <div class="inline-show">
                    <abc-form-item style="margin-bottom: 0;" class="temperature-item">
                        <abc-select
                            v-model="temperatureTypeText"
                            reference-mode="text"
                            :reference-active-color="$store.state.theme.style.T2"
                            :width="56"
                            :max-width="56"
                        >
                            <abc-option
                                v-for="item in temperatureTypeOptions"
                                :key="item"
                                :label="item"
                                :value="item"
                            ></abc-option>
                        </abc-select>
                        <temperature-popover @click="(val) => handlePopover(val,'temperature')">
                            <abc-input
                                v-model="postData.temperature"
                                :width="46"
                                type="number"
                                :config="{
                                    max: 42,
                                    supportZero: false,
                                    formatLength: 1,
                                }"
                                class="append-input-wrapper"
                            >
                                <span slot="append">℃</span>
                            </abc-input>
                        </temperature-popover>
                    </abc-form-item>
                    <abc-form-item label="血压" :label-width="32">
                        <blood-pressure-popover
                            ref="bloodRef"
                            @click="(val,type)=>handlePopover(val,type)"
                            @hide="isShowBloodPressurePopover = false"
                            @show="isShowBloodPressurePopover = true"
                        >
                            <div>
                                <abc-tooltip
                                    placement="top"
                                    theme="black"
                                    :arrow-offset="40"
                                    :offset="20"
                                    :max-width="234"
                                    :disabled="validateBloodPressureValue || isShowBloodPressurePopover"
                                >
                                    <template #content>
                                        <div style="text-align: center;">
                                            数据异常，收缩压不能小于舒张压（收缩压/舒张压）
                                        </div>
                                    </template>
                                    <abc-input
                                        v-model="bloodPressureValue"
                                        :width="70"
                                        :input-custom-style="{
                                            textAlign: 'center',color: (validateBloodPressureValue || isShowBloodPressurePopover) ? '#000' : '#ff9933'
                                        }"
                                        class="append-input-wrapper"
                                        @click="handleShowBloodPressurePopover"
                                    >
                                        <span slot="append">mmHg</span>
                                    </abc-input>
                                </abc-tooltip>
                            </div>
                        </blood-pressure-popover>
                    </abc-form-item>
                </div>
                <div class="inline-show">
                    <abc-form-item label="身高" style="margin-bottom: 0;">
                        <height-popover @click="(val)=>handlePopover(val,'height')">
                            <abc-input
                                v-model="postData.height"
                                :width="42"
                                type="number"
                                :config="{
                                    formatLength: 2, max: 1000
                                }"
                                class="append-input-wrapper"
                            >
                                <span slot="append">cm</span>
                            </abc-input>
                        </height-popover>
                    </abc-form-item>
                    <abc-form-item label="体重" :label-width="32">
                        <weight-popover @click="(val)=>handlePopover(val,'weight')">
                            <abc-input
                                v-model="postData.weight"
                                type="number"
                                :config="{
                                    formatLength: 2, max: 1000
                                }"
                                :width="97"
                                class="append-input-wrapper"
                            >
                                <span slot="append">kg</span>
                            </abc-input>
                        </weight-popover>
                    </abc-form-item>
                </div>
            </template>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button :loading="btnLoading" @click="handleConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="handleCancel">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import BedNoSelect from '@/views-hospital/beds/components/bed-no-select/index';
    import TemperaturePopover from './temperature-popover';
    import HeightPopover from './height-popover';
    import WeightPopover from './weight-popover';
    import BloodPressurePopover from './blood-pressure-popover';

    import { formatDate } from '@abc/utils-date';
    import Clone from '@/utils/clone';

    import { bedUseStatusEnum } from '@/views-hospital/beds/utils/constant';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { HospitalStatusEnum } from '@/views-hospital/register/utils/constants';
    import { HospitalQLSceneType } from 'utils/constants-hospital';
    import WardAreaAPI from 'api/hospital/setting/ward-area';
    import {
        BUSINESS_SCOPE_TYPE, ROLE_HOSPITAL_DOCTOR_ID,
    } from 'utils/constants';
    import bloodPressurePopover
        from '@/views-hospital/beds/components/bed-allocation-dialog/blood-pressure-popover.vue';
    import { validateMobile } from 'utils/validate';

    export default {
        name: 'FirstBedAllocationDialog',
        components: {
            BedNoSelect,
            TemperaturePopover,
            HeightPopover,
            WeightPopover,
            BloodPressurePopover,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            selectedPatientInfo: {
                type: Object,
                default: () => {},
            },
            bedInfo: {
                type: Object,
                default: () => {},
            },
            bedService: {
                type: Object,
                required: true,
            },
            patientList: {
                type: Array,
                default: () => [],
            },
            isDischargeRecall: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                formatDate,
                bedUseStatusEnum,
                btnLoading: false,
                bedId: '',
                wardAreaId: '',
                wardAreaName: '',
                departmentName: '',
                temperatureTypeText: '腋温',
                temperatureTypeOptions: ['腋温','口温','肛温'],
                patientName: '',
                postData: {
                    patientId: '',
                    temperatureType: 1, // 1-腋温 2-口温 3-肛温
                    temperature: '', // 体温
                    departmentId: '', // 科室id
                    diastolicPressurePm: '', // 舒张压
                    doctorId: '',
                    height: '', // 身高
                    weight: '', //体重
                    nurseId: '',
                    patientOrderId: '',
                    systolicPressure: '', // 收缩压
                    type: 0, // 0: 护士站首次分配；1: 转科首次分配
                    inpatientTime: '',
                },
                params: {
                    departmentId: '',
                    limit: 200,
                    offset: 0,
                    sceneType: HospitalQLSceneType.WAIT_ALLOCATE_BED,
                    status: '',
                    wardId: '',
                },
                allPatientList: [],
                doctorsList: [],
                nursesList: [],
                isShowBloodPressurePopover: false,
                cachePostData: null,
                bloodPressureList: [
                    {
                        'label': '收缩压',
                        'type': 'systolicPressure',
                        'value': [
                            82, 84, 86, 88, 90, 92, 94, 96, 98, 100,
                            102, 104, 106, 108, 110, 112, 114, 116, 118, 120,
                            122, 124, 126, 128, 130, 132, 134, 136, 138, 140,
                            142, 144, 146, 148, 150, 152, 154, 156, 158, 160,
                            162, 164, 166, 168, 170, 172, 174, 176, 178, 180,
                        ],
                    },
                    {
                        'label': '舒张压',
                        'type': 'diastolicPressurePm',
                        'value': [
                            52, 54, 56, 58, 60, 62, 64, 66, 68, 70,
                            72, 74, 76, 78, 80, 82, 84, 86, 88, 90,
                            92, 94, 96, 98, 100, 102, 104, 106, 108, 110,
                        ],
                    },
                ],
            };
        },
        computed: {
            bloodPressurePopover() {
                return bloodPressurePopover;
            },
            bedNoList() {
                return this.$abcPage.$store.bedNoList;
            },
            // 待分配患者列表(包含出院召回)
            waitAllocatedPatientList() {
                const {
                    REGISTERED, DISCHARGE_CALLBACK,
                } = HospitalStatusEnum;
                return this.allPatientList?.filter((item) => [REGISTERED, DISCHARGE_CALLBACK].includes(item.status)) || [];
            },
            // 转科患者列表
            waitTransferPatientList() {
                return this.allPatientList?.filter((item) => item.status === HospitalStatusEnum.TRANSFER_DEPARTMENT_PROCESS) || [];
            },
            bedAllocationDialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            validateBloodPressureValue() {
                const {
                    systolicPressure,diastolicPressurePm,
                } = this.postData;
                return Number(systolicPressure) >= Number(diastolicPressurePm);
            },
            bloodPressureValue: {
                get() {
                    return `${this.postData.systolicPressure}/${this.postData.diastolicPressurePm}`;
                },
                set(val) {
                    const arr = val.split('/');
                    this.postData.systolicPressure = arr[0] || '';
                    this.postData.diastolicPressurePm = arr[1] || '';
                },
            },
        },
        watch: {
            'postData.departmentId': {
                handler(val) {
                    val && this.getHospitalDoctorsByDepartmentId(val);
                },
                immediate: true,
            },
        },
        created() {
            if (this.bedInfo?.id) {
                this.bedId = this.bedInfo.id;
                this.params.wardId = this.wardAreaId = this.bedInfo?.wardAreaId || '';
                this.wardAreaName = this.bedInfo?.wardAreaName || '';
                this.departmentName = this.bedInfo?.departmentName || '';
            } else {
                this.params.wardId = this.wardAreaId = this.selectedPatientInfo?.wardId || '';
                this.wardAreaName = this.selectedPatientInfo?.wardName || '';
                // selectedPatientInfo 可以为null
                this.departmentName = this.selectedPatientInfo?.departmentName || '';
                this.allPatientList = Clone(this.patientList);
            }
            if (this.selectedPatientInfo?.patient?.id) {
                this.postData.patientId = this.selectedPatientInfo?.patient?.id;
                this.patientName = this.selectedPatientInfo?.patient?.name;
                this.postData.departmentId = this.selectedPatientInfo?.departmentId;
                this.postData.inpatientTime = formatDate(this.selectedPatientInfo?.inpatientTime, 'YYYY-MM-DD HH:mm');
                this.postData.patientOrderId = this.selectedPatientInfo?.id;
                this.handlePostDataType(this.selectedPatientInfo);
            }
            this.bedId && this.fetchWaitAllocatedOrTransferPatientList();
            this.getWardAreaHospitalNurses();
        },
        methods: {
            validateMobile,
            async fetchWaitAllocatedOrTransferPatientList() {
                try {
                    this.loading = true;
                    const { data } = await PatientOrderAPI.getInHospitalPatientList(this.params) || {};
                    // 去除患者后的脏数据
                    this.allPatientList = data?.rows?.filter((item) => !!item.patient) || [];
                } catch (e) {
                    console.log('fetchPatientList Error');
                } finally {
                    this.loading = false;
                }
            },
            getAgeInfo(item) {
                const {
                    year, month, day,
                } = item?.patient?.age || {};
                if (year) {
                    return `${year}岁`;
                }
                if (month) {
                    return `${month}月`;
                }
                if (day) {
                    return `${day}天`;
                }
                return '';
            },
            async getHospitalDoctorsByDepartmentId(departmentId) {
                try {
                    const { data } = await WardAreaAPI.getEmployeeListByDepartmentId({
                        businessScope: BUSINESS_SCOPE_TYPE.HOSPITAL,
                        departmentId,
                        roles: [ROLE_HOSPITAL_DOCTOR_ID],
                    }) || {};
                    this.doctorsList = (data?.rows || []).map((it) => ({
                        id: it.employeeId,
                        name: it.employeeName,
                    }));
                } catch (e) {
                    console.log(e);
                }
            },
            async getWardAreaHospitalNurses() {
                try {
                    const { data } = await WardAreaAPI.getWardAreaHospitalNurses(this.wardAreaId) || {};
                    this.nursesList = data?.rows || [];
                } catch (e) {
                    console.log(e);
                }
            },
            handleSelectPatient(patientId) {
                const selectedPatientInfo = this.allPatientList?.find((item) => item?.patientId === patientId);
                if (selectedPatientInfo) {
                    this.postData.departmentId = selectedPatientInfo?.departmentId;
                    this.postData.patientOrderId = selectedPatientInfo?.id;
                    this.postData.inpatientTime = formatDate(selectedPatientInfo?.inpatientTime, 'YYYY-MM-DD HH:mm');
                    this.departmentName = selectedPatientInfo?.departmentName || '';
                    this.handlePostDataType(selectedPatientInfo);
                }
            },
            handlePostDataType(selectedPatientInfo) {
                const {
                    REGISTERED, TRANSFER_DEPARTMENT_PROCESS, DISCHARGE_CALLBACK,
                } = HospitalStatusEnum;
                // 待分配
                if (selectedPatientInfo.status === REGISTERED) {
                    this.postData.type = 0;
                }
                // 转科中
                if (selectedPatientInfo.status === TRANSFER_DEPARTMENT_PROCESS) {
                    this.postData.type = 1;
                }
                // 出院召回
                if (selectedPatientInfo.status === DISCHARGE_CALLBACK) {
                    this.postData.type = 2;
                }
            },
            handleConfirm() {
                this.$refs.firstBedAllocationForm.validate((valid) => {
                    if (valid) {
                        this.handleSubmit();
                    }
                });
            },

            async handleSubmit() {
                this.btnLoading = true;
                try {
                    const obj = {
                        '腋温': 1,
                        '口温': 2,
                        '肛温': 3,
                    };
                    this.postData.temperatureType = obj[this.temperatureTypeText];

                    await this.bedService.bedAssign(this.bedId,this.postData);
                    this.$emit('success');
                    this.bedAllocationDialogVisible = false;
                    this.$Toast({
                        message: '床位分配成功',
                        type: 'success',
                    });
                } catch (error) {
                    if (error?.code === 400) {
                        this.$Toast({
                            type: 'error',
                            message: error.message,
                        });
                    }
                } finally {
                    this.btnLoading = false;
                }
            },

            handleCancel() {
                this.bedAllocationDialogVisible = false;
            },
            handlePopover(val,type) {
                this.postData[type] = val;
            },
            handleShowBloodPressurePopover() {
                this.isShowBloodPressurePopover = true;
                this.$nextTick(() => {
                    if (this.$refs.bloodRef) {
                        this.$refs.bloodRef.visible = true;
                    }
                });
            },
        },

    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.first-bed-allocation-form-dialog {
    .inline-show {
        display: inline-flex;
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }

        .abc-form-item:nth-child(2) {
            margin-left: 12px;

            .abc-form-item-label {
                width: 28px !important;
                margin-right: 8px !important;
            }
        }

        .append-input-wrapper.abc-input-wrapper {
            .abc-input__inner {
                border-right-width: 0;

                &:focus,
                &:hover {
                    border-right-width: 1px;
                }
            }

            .append-input {
                background-color: transparent;
            }
        }
    }

    .abc-form-item {
        align-items: flex-start;
        margin-right: 0;
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }

        &.temperature-item {
            .abc-form-item-content {
                display: flex;

                .abc-select-wrapper {
                    margin-right: 28px;
                }
            }
        }

        .inpatient-area {
            display: inline-block;
            width: 244px;
        }
    }

    .abc-form-item-label {
        display: flex;
        align-items: center;
        height: 32px;
        line-height: 1;

        &:first-child {
            height: 20px;
            line-height: 20px;
        }
    }
}

.patient-list-item {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 12px;
    font-size: 14px;
    color: $S1;
    cursor: pointer;
    border-bottom: 1px solid $B4;

    &.abc-option-item:first-child {
        border-bottom: 1px solid $B4;
    }

    span {
        display: inline-block;
        height: 14px;
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        color: $S1;

        &.name {
            width: 58px;
        }

        &.sex {
            width: 14px;
            margin: 0 8px;
        }

        &.age {
            width: 31px;
            margin-right: 8px;
        }
    }
}
</style>
