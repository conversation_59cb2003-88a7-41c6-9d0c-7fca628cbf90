<template>
    <main ref="tableWrapper" class="bed-exam bed-exam__global-wrapper">
        <section class="filter-bar">
            <abc-date-picker
                v-model="filterParams.selectDate"
                type="daterange"
                style="margin-right: 8px;"
                placeholder="日期范围"
            >
            </abc-date-picker>

            <abc-select
                v-model="filterParams.status"
                :width="223"
                placeholder="状态"
                clearable
            >
                <abc-option
                    v-for="e in statusOptions"
                    :key="e.value"
                    v-bind="e"
                ></abc-option>
            </abc-select>
        </section>

        <section class="exam-list">
            <table-exam
                :list="dataSource"
                :type="type"
                :loading="loading"
                :show-content-empty="!pageLoading"
                :fill-reference-el="fillReferenceEl"
                @view-report="handleLookReport"
                @view-image="handleLookImage"
            ></table-exam>
        </section>

        <report-detail-dialog
            v-if="dialogVisible"
            v-model="dialogVisible"
            :patient-id="patientId"
            :examination-id="curExamSheetId"
            :type="curExamType"
        ></report-detail-dialog>
    </main>
</template>

<script>
    import { EXAMINATION_STATUS as INSPECT_STATUS } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import { EXAMINATION_STATUS } from '@/views/examination/util/constants';
    import { formatDate } from '@abc/utils-date';
    import API from '@/api/crm';
    const ReportDetailDialog = () => import('src/views/outpatient/common/report-detail-dialog.vue');
    import { EXAMINATION_TYPE } from '@/utils/constants';
    import TableExam from '@/views/layout/tables/table-exam/index.vue';
    import ExaminationAPI from 'api/examination';
    import { medicalImagingViewerDialogService } from '@/medical-imaging-viewer/store/medical-imaging-viewer-dialog';
    import {
        medicalImagingViewerDialogService as medicalImagingViewerDialogServiceV2,
    } from '@/medical-imaging-viewer-v2/store/medical-imaging-viewer-dialog.js';
    import {
        dcm4cheeType, DEVICE_TYPE_NAME,
    } from '@/views-hospital/inspect-setting/utils/constant';
    import { AbcMedicalImagingViewerService } from '@/service/abcMedicalImagingViewer';
    import Logger from 'utils/logger';
    import { mapGetters } from 'vuex';
    import InspectAPI from 'api/hospital/inspect';

    const inspectStatusOptions = [
        {
            label: '待检查', value: INSPECT_STATUS.WAIT,
        },
        {
            label: '待编辑', value: INSPECT_STATUS.WAIT_WRITE,
        },
        {
            label: '待审核', value: INSPECT_STATUS.WAIT_CHECK,
        },
        {
            label: '已审核', value: INSPECT_STATUS.CHECKED,
        },
        {
            label: '已退款', value: INSPECT_STATUS.REFOUND,
        },
    ];

    const examinationStatusOptions = [
        {
            label: '待检验', value: EXAMINATION_STATUS.WAIT,
        },
        {
            label: '待查看', value: EXAMINATION_STATUS.WAIT_VISIT,
        },
        {
            label: '待审核', value: EXAMINATION_STATUS.WAIT_CHECK,
        },
        {
            label: '已审核', value: EXAMINATION_STATUS.CHECKED,
        },
        {
            label: '已退款', value: EXAMINATION_STATUS.REFOUND,
        },
    ];

    export default {
        name: 'BedInspect',
        components: {
            ReportDetailDialog,
            TableExam,
        },
        props: {
            type: {
                type: Number,
                default: 1,
            },
            patientId: {
                type: String,
                default: '',
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            fillReferenceEl: {
                type: HTMLElement,
                default: null,
            },
            pageLoading: {
                type: Boolean,
                default: false,
            },
            isPageLoading: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                EXAMINATION_TYPE,
                loading: false,

                dataSource: [],
                total: 0,
                filterParams: {
                    limit: 999,
                    offset: 0,
                    status: '',
                    selectDate: [],
                },

                dialogVisible: false,
                curExamSheetId: '',
                curExamType: EXAMINATION_TYPE.INSPECT,
                tableHeight: undefined,
                isChangeType: false,
            };
        },
        computed: {
            ...mapGetters(['isEnablePacsUpgrade']),
            statusOptions() {
                return this.type === 1 ? examinationStatusOptions : inspectStatusOptions;
            },
        },
        watch: {
            filterParams: {
                handler() {
                    this.handleFetchTableDataSource();
                },
                deep: true,
            },
            type: {
                handler(val,oldVal) {
                    if (val !== oldVal) {
                        this.isChangeType = true;
                    }
                    this.resetFilterParams();
                },
            },
            patientOrderId: {
                handler() {
                    this.handleFetchTableDataSource(this.isPageLoading);
                },
                immediate: true,
            },
        },
        methods: {
            async handleFetchTableDataSource(isPageLoading = false) {
                if (isPageLoading || this.isPageLoading && this.isChangeType) {
                    this.$emit('update:pageLoading',true);
                } else {
                    this.loading = true;
                }

                const filterParams = {
                    ...this.filterParams,
                    type: this.type,
                    beginDate: formatDate(this.filterParams.selectDate[0], 'YYYY-MM-DD'),
                    endDate: formatDate(this.filterParams.selectDate[1], 'YYYY-MM-DD'),
                    patientOrderId: this.patientOrderId,
                };

                delete filterParams.selectDate;

                try {
                    const res = await API.fetchExamination(this.patientId, filterParams);

                    this.dataSource = res.data?.examinationSheetPatients.map((item) => ({
                        ...item,
                        wait: this.type === 1 ? (
                            [ EXAMINATION_STATUS.WAIT, EXAMINATION_STATUS.WAIT_VISIT, EXAMINATION_STATUS.WAIT_CHECK].includes(item.status)
                        ) : (
                            [ INSPECT_STATUS.WAIT, INSPECT_STATUS.WAIT_WRITE, INSPECT_STATUS.WAIT_CHECK].includes(item.status)
                        ),
                        done: this.type === 1 ? (
                            [ EXAMINATION_STATUS.CHECKED, EXAMINATION_STATUS.REFOUND ].includes(item.status)
                        ) : (
                            [ INSPECT_STATUS.CHECKED, INSPECT_STATUS.REFOUND ].includes(item.status)
                        ),
                    })) || [];
                    this.total = res.data?.totalCount || 0;
                } catch (error) {
                    console.log(error);
                }

                this.loading = false;
                this.$emit('update:pageLoading',false);
                this.isChangeType = false;
            },

            handleLookReport(examSheetId, examType) {
                this.curExamSheetId = examSheetId;
                this.dialogVisible = true;
                this.curExamType = examType;
            },
            handleLookImage(examSheetId, examType) {
                this.curExamSheetId = examSheetId;
                this.curExamType = examType;
                this.fetchDetail(examSheetId).then((res) => {
                    if (dcm4cheeType.includes(res.deviceType)) {
                        const applySheetNo = res.examinationApplySheetNo || '';
                        if (applySheetNo) {
                            this.previewDicom(applySheetNo);
                        }
                    } else if (res.deviceType === DEVICE_TYPE_NAME['彩超']) {
                        const { imageFiles } = res.examinationSheetReport;
                        if (imageFiles && imageFiles.length > 0) {
                            this.previewImage(imageFiles[0],imageFiles);
                        } else {
                            this.$Toast({
                                type: 'error',
                                message: '该报告暂无影像数据',
                            });
                        }
                    }
                }).catch((err) => {
                    console.log(err);
                });
            },

            handlePageChange(page) {
                this.filterParams = {
                    ...this.filterParams,
                    offset: this.filterParams.limit * (page - 1),
                };
            },

            resetFilterParams() {
                this.dataSource = [];
                this.filterParams = {
                    limit: this.filterParams.limit,
                    offset: 0,
                    status: '',
                    selectDate: [],
                };
            },

            calcTableHeight() {
                const totalHeight = this.$refs.tableWrapper?.clientHeight;

                return totalHeight - 48;
            },

            async fetchDetail(id) {
                try {
                    const { data } = await ExaminationAPI.fetchDetailInterface(id);
                    return data;
                } catch (error) {
                    console.error('拉取报告详情失败：', error);
                }
            },
            previewImage(file,imageList) {
                medicalImagingViewerDialogService.previewImageAttachmentNoFileValidate({
                    attachments: imageList,
                    attachment: file,
                });
            },
            async previewDicom(applyNo) {
                try {
                    await AbcMedicalImagingViewerService.getInstance().start();

                    let completed = false;
                    let study = [];
                    if (this.isEnablePacsUpgrade) {
                        const { data } = await InspectAPI.getStudyCompleteByAccessionNumber(applyNo);
                        if (data.rows && data.rows.length > 0) {
                            study = data.rows;
                            completed = true;
                        }
                    } else {
                        const {
                            completed: _completed, study: _study,
                        } = await AbcMedicalImagingViewerService
                            .getInstance()
                            .getBusinessService()
                            .getInstance()
                            .checkStudyCompleteByAccessionNumber(applyNo);
                        completed = _completed;
                        study = _study;
                    }

                    if (!completed) {
                        this.$Toast({
                            type: 'error',
                            message: '该报告暂无影像数据',
                        });
                        return;
                    }

                    if (!study.length || !study[0].StudyInstanceUID) {
                        this.$Toast({
                            type: 'error',
                            message: '该报告暂无影像数据',
                        });
                    }

                    medicalImagingViewerDialogServiceV2.openDialog({
                        studyInstanceUid: study[0].StudyInstanceUID,
                        patientId: this.patientId,
                        isEnablePacsUpgrade: !!this.isEnablePacsUpgrade,
                    });
                } catch (e) {
                    Logger.error({
                        scene: 'GET_STUDY_ERROR',
                        err: e,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
    .bed-exam__global-wrapper {
        height: 100%;

        .filter-bar {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }

        .wait {
            color: $B1;
        }

        .done {
            color: $T2;
        }
    }
</style>
