@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.abc-hospital-pharmacy-container {
    position: relative;

    .hospital-pharmacy-container {
        .record-wrapper {
            overflow: hidden;
            opacity: 1;
            transition: all 0.3s;

            &.hide {
                height: 0;
                opacity: 0;
            }
        }

        .record-item {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 12px;
            color: #7a8794;

            > span {
                display: inline-block;

                &.date {
                    width: 120px;
                }

                &.name {
                    width: 80px;
                }

                &.type {
                    width: 50px;
                    margin-right: 16px;
                }

                &.content {
                    flex: 1;
                    min-width: 0;
                }
            }
        }

        .tag-fixed {
            position: absolute;
            top: 50%;
            right: 10px;
            display: flex;
            background: #ffffff;
            transform: translateY(-50%);

            .remark {
                max-width: 60px;
            }
        }
    }
}
