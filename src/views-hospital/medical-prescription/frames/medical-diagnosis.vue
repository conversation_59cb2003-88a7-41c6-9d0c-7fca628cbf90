<template>
    <div class="main-content medical-diagnosis-content">
        <div class="medical-diagnosis-toolbar">
            <div class="left">
                <abc-space>
                    <abc-select
                        v-model="activeHistory"
                        style="margin-left: 0;"
                        placeholder="本次住院"
                        inner-width="280"
                        width="170"
                        clearable
                        :input-style="diagnosisHistoryStyle"
                        @change="handleTypeChange"
                    >
                        <div
                            v-for="(d, index) in diagnosisHistoryOptions"
                            :key="d.outpatientOrderId + index"
                        >
                            <abc-option
                                :value="index"
                                :label="`${d.hospitalStatusName } ${d.diagnosedTime }`"
                            >
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <div style="display: flex; align-items: center;">
                                        <span>{{ d.typeName }}</span>
                                        <span
                                            :title="d.diseaseName"
                                            style="
                                            display: inline-block;
                                            width: 100px;
                                            margin-left: 10px;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                            word-break: keep-all;
                                            white-space: nowrap;"
                                        >{{ d.diseaseName }}</span>
                                    </div>
                                    <div style="display: inline-block; color: #7a8794;">
                                        {{ d.diagnosedTime }}
                                    </div>
                                </div>
                            </abc-option>
                        </div>
                    </abc-select>
                    <abc-tips-card-v2
                        v-if="mustAtLeastOneWesternDisease && !hasWesternDiagnosis"
                        theme="warning"
                    >
                        医保结算要求必须开立至少一条西医诊断，若不使用医保结算请忽略此提示。
                    </abc-tips-card-v2>
                </abc-space>
            </div>
            <template v-if="!disabledDiagnosis && !isOldDiagnosis">
                <div v-if="!diagnosisNotCanUsed" class="right">
                    <abc-button @click="handleDiagnosisBtnClick('add')">
                        下诊断
                    </abc-button>
                    <abc-button
                        v-if="false"
                        type="danger"
                        :disabled="!selectedDiagnosis.length"
                        @click="handleDelete"
                    >
                        删除
                    </abc-button>
                </div>
                <div v-else>
                    <abc-button class="right" @click="handleDiagnosisBtnClick('edit')">
                        修改诊断
                    </abc-button>
                </div>
            </template>
        </div>

        <diagnosis-table
            style="margin-top: 16px;"
            :loading="loading"
            :show-content-empty="!pageLoading && !loading"
            :data-list.sync="diagnosisTableData"
            :show-checkbox="false"
            :fill-reference-el="fillReferenceEl"
            :enable-drag-sort="enableDragSort"
            @change-checked="handleSelectedChange"
            @sort-change="handleSortChange"
        >
        </diagnosis-table>

        <add-medical-diagnosis-dialog
            v-if="addDiagnosisDialogVisible"
            v-model="addDiagnosisDialogVisible"
            :title="diagnosisDialogTitle"
            :patient-order-id="patientOrderId"
            :patient="patient"
            :doctor="doctor"
            :existed-diagnosis-list="diagnosisTableData"
            @refresh="getTableData"
        ></add-medical-diagnosis-dialog>
    </div>
</template>
<script>
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import {
        parseTime, createGUID,
    } from '@/utils/index';
    import { HospitalQLSceneType } from 'utils/constants-hospital.js';
    import { SettleQLTabEnum } from '@/views-hospital/charge-hospital/utils/index.js';

    import DiagnosisTable from '@/views/layout/tables/table-diagnosis/index.vue';

    export default {
        components: {
            AddMedicalDiagnosisDialog: () => import('@/views-hospital/medical-prescription/components/add-medial-diagnosis-dialog/index.vue'),
            DiagnosisTable,
        },
        props: {
            patientOrderId: {
                type: String,
                required: true,
            },
            patientId: {
                type: String,
                default: '',
            },
            disabledDiagnosis: {
                type: Boolean,
                default: false,
            },
            patientStatusTags: {
                type: Array,
                default: () => [],
            },
            patient: {
                type: Object,
                default: () => ({}),
            },
            doctor: {
                type: Object,
                default: () => ({}),
            },
            fillReferenceEl: {
                type: HTMLElement,
                default: null,
            },
            pageLoading: {
                type: Boolean,
                default: false,
            },
            isPageLoading: {
                type: Boolean,
                default: false,
            },
            enableDragSort: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                outpatientOrderId: '',
                activeHistory: -1,
                loading: false,
                diagnosisTableData: [],
                selectedDiagnosis: [],
                addDiagnosisDialogVisible: false,
                diagnosisDialogTitle: '下诊断',
                diagnosisHistoryOptions: [],
                key: '',
                localPatientOrderId: this.patientOrderId,
                currentHistory: '',
            };
        },
        computed: {
            mustAtLeastOneWesternDisease() {
                return this.$abcSocialSecurity.isOpenSocial && this.$abcSocialSecurity.config.isNeedHisDiagnosisWithICD10;
            },
            hasWesternDiagnosis() {
                return this.diagnosisTableData.some((x) => x.category === 0);
            },
            diagnosisHistoryStyle() {
                if (this.outpatientOrderId) {
                    return {
                        background: '#FFF4EA',
                        color: '#FF9933',
                        border: 0,
                    };
                }
                return {};
            },
            diagnosisNotCanUsed() {
                return +this.$route.query.qlTab === SettleQLTabEnum.OUT_HOSPITAL ||
                    !!this.patientStatusTags?.find((item) => +item.id === HospitalQLSceneType.DOCTOR_DISCHARGE);
            },
            isOldDiagnosis() {
                return this.activeHistory !== -1 && this.activeHistory !== '' ;
            },
        },
        watch: {
            patientOrderId: {
                async handler(val) {
                    this.localPatientOrderId = val;
                    await this.getTableData(this.isPageLoading);
                    await this.getDiagnosisHistoryOptions();
                },
                immediate: true,
            },
        },

        methods: {
            handleDelete() {
                this.$modal({
                    type: 'warn',
                    preset: 'alert',
                    title: '删除诊断',
                    onConfirm: this.handleDeleteConfirm,
                    content: `诊断删除后不可恢复，是否确定删除选中的${this.selectedDiagnosis?.length}条诊断?`,
                });
            },
            async handleSortChange(list) {
                try {
                    const { patientOrderId } = this;
                    const items = list.map((item, index) => {
                        return {
                            ...item,
                            sort: index,
                        };
                    });
                    await MedicalPrescriptionAPI.createdDiagnosis({
                        items,
                        patientOrderId,
                    });
                    // 更新排序
                    await this.getTableData(false, false);
                } catch (e) {
                    console.log(e);
                }
            },
            async handleDeleteConfirm() {
                try {
                    const { patientOrderId } = this;
                    const items = this.selectedDiagnosis.map((item) => {
                        return {
                            id: item.id,
                            diseaseCode: item.diseaseCode,
                            diseaseName: item.diseaseName,
                        };
                    });
                    const res = await MedicalPrescriptionAPI.deleteDiagnosis({
                        items,
                        patientOrderId,
                    });
                    if (res) {
                        this.getTableData();
                        this.$Toast({
                            message: '删除成功',
                            type: 'success',
                        });
                    }

                } catch (err) {
                    console.log(err);
                }

            },
            handleSelectedChange(selectedList) {
                this.selectedDiagnosis = selectedList;
            },
            handleDiagnosisBtnClick(type = 'add') {
                if (type === 'edit') {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '患者已办理出院，如需修改诊断，请在修改后调整相关文书并重新归档上报。',
                        onConfirm: () => {
                            this.diagnosisDialogTitle = '修改诊断';
                            this.addDiagnosisDialogVisible = true;
                        },
                        confirmText: '继续修改',
                    });
                }
                if (type === 'add') {
                    this.diagnosisDialogTitle = '下诊断';
                    this.addDiagnosisDialogVisible = true;
                }
            },
            async getTableData(isPageLoading = false, withLoading = true) {
                if (isPageLoading) {
                    this.$emit('update:pageLoading',withLoading && true);
                } else {
                    this.loading = withLoading && true;
                }
                try {
                    const {
                        localPatientOrderId, outpatientOrderId,
                    } = this;
                    const list = await MedicalPrescriptionAPI.getDiagnosisList({
                        patientOrderId: localPatientOrderId,
                        outpatientOrderId,
                    });
                    this.key = createGUID();
                    this.diagnosisTableData = list || [];

                } catch (err) {
                    console.log(err);
                } finally {
                    this.loading = false;
                    this.$emit('update:pageLoading',false);
                }
            },
            async getDiagnosisHistoryOptions() {
                try {
                    const list = await MedicalPrescriptionAPI.getDiagnosisHistoryList(this.patientId, this.patientOrderId);
                    if (list?.length) {
                        this.diagnosisHistoryOptions = list.map((item) => {
                            return {
                                ...item,
                                outpatientOrderId: item.outpatientOrderId,
                                diseaseName: item.diseaseName,
                                diagnosedTime: parseTime(item.diagnosedTime, 'y-m-d', true),
                                hospitalStatusName: item.type !== 30 ? (item.typeName === '本次住院' ? item.typeName : '过往住院') : '过往门诊',
                            };
                        }).filter((item) => item.typeName !== '本次住院');
                    }

                } catch (err) {
                    console.log(err);
                }
            },
            handleTypeChange(index) {
                this.activeHistory = index;
                if (index !== '') {
                    this.outpatientOrderId = this.diagnosisHistoryOptions[+index].outpatientOrderId;
                    this.localPatientOrderId = this.diagnosisHistoryOptions[+index].patientOrderId;
                } else {
                    this.outpatientOrderId = '';
                    this.localPatientOrderId = this.patientOrderId;
                }
                this.getTableData();
            },
        },
    };
</script>

<style  lang="scss">
    .medical-diagnosis-content {
        .medical-diagnosis-toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;

            .left {
                .abc-select-wrapper {
                    margin-left: 8px;

                    &:hover {
                        border: 0 !important;
                    }
                }

                .diagnosis-history-item {
                    display: flex;
                    justify-content: space-between;
                }
            }

            .right {
                display: flex;
                align-items: center;
            }
        }

        .abc-fixed-table.medical-diagnosis-table {
            position: relative;
            overflow: auto;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);

            tbody tr {
                &:last-child {
                    border-bottom: none;
                }
            }

            .detail-table-container::after {
                border-bottom: none;
            }

            .table-title {
                height: 33px;
                padding-right: 10px;
                line-height: 33px;
                border-top: none;
            }

            .table-tr {
                height: 40px;
                padding-right: 10px;
                line-height: 40px;
                border-right: none;
                border-left: none;

                &:last-child {
                    border-bottom: none;
                }
            }

            .label-describe {
                color: $T2;
            }

            .table-td {
                padding: 0 2px;
            }

            .table-empty {
                top: 50%;

                .label {
                    margin-top: 0;
                    font-size: 14px;
                    color: $T3;
                }

                .icon {
                    display: none;
                }
            }

            .abc-table__header-wrapper {
                border-bottom: 1px solid $P1;

                table th {
                    padding-left: 0;
                    color: $T2;
                    border: none;

                    &:first-child {
                        width: 36px;
                        padding: 0;
                    }
                }
            }

            .abc-table__body-wrapper,
            .abc-table__footer-wrapper {
                table tr {
                    border-right: none;
                    border-left: none;
                }

                tbody td {
                    border-right: none !important;
                    border-left: none;
                }
            }

            .abc-table__footer-wrapper {
                background-color: #fafbfc;

                tbody tr {
                    border: none;
                    border-top: 1px solid $P1;
                }
            }

            table td div.cell,
            table th div.cell {
                padding: 0;
                text-overflow: unset;
            }
        }
    }
</style>
