<template>
    <abc-dialog
        v-model="dialogVisible"
        :title="title"
        custom-class="examination_medical-prescription--dialog"
        content-styles="width: 820px; padding:0px 0px; height: 432px;"
    >
        <abc-tabs
            slot="title"
            v-model="currentTab"
            :option="tabOptions"
            @change="changeTab"
        ></abc-tabs>
        <div class="examination_medical-prescription--dialog-body">
            <div class="examination_medical-prescription--dialog-body-left">
                <ul v-if="currentTab === 0">
                    <li
                        v-for="(item,index) in itemCategory"
                        :key="index"
                        :class="{ 'current-item': currentExaminationType === item.value }"
                        @click="changeType(item.value, index)"
                    >
                        {{ item.name }}
                    </li>
                </ul>
                <ul v-if="currentTab === 1">
                    <li
                        v-for="(item,index) in DEVICE_HANDLE"
                        :key="index"
                        :class="{ 'current-item': currentInspectType === item.value }"
                        @click="changeInspectType(item.value, index)"
                    >
                        {{ item.label }}
                    </li>
                </ul>
            </div>
            <div class="examination_medical-prescription--dialog-body-right">
                <div class="examination_medical-prescription--dialog-body-right-search">
                    <abc-input
                        ref="searchKey"
                        v-model="searchKey"
                        placeholder="搜索项目"
                        :width="320"
                        :icon="searchKey ? 'cis-icon-cross_small' : ''"
                        @input="_fetchGoods"
                        @icon-click="clean"
                    >
                        <abc-icon slot="prepend" icon="search"></abc-icon>
                    </abc-input>
                </div>
                <div v-abc-loading="loading" class="examination_medical-prescription--dialog-body-right-list-box">
                    <div v-for="(item,num) in examinationList" :key="num">
                        <div :id="`examination_medical-${num}`" class="examination_medical-prescription--dialog-body-right-title">
                            {{ item.name }}
                        </div>
                        <div class="examination_medical-prescription--dialog-body-right-list">
                            <div
                                v-for="it in item.rows"
                                :key="it.examinationId"
                                class="examination_medical-prescription--dialog-body-right-list-line"
                                :class="{ 'is-current': currentTags.includes(it.id) }"
                                @click="setExaminationList(it)"
                            >
                                <div class="examination_medical-prescription--dialog-body-right-list-line-name">
                                    {{ it.name }}
                                </div>
                                <div class="examination_medical-prescription--dialog-body-right-list-line-type">
                                    <template v-if="medicalFeeGrade2Str(it.medicalFeeGrade)">
                                        {{ `[${medicalFeeGrade2Str(it.medicalFeeGrade)}]` }}
                                    </template>
                                </div>
                                <div class="examination_medical-prescription--dialog-body-right-list-line-price">
                                    {{ $t('currencySymbol') }}{{ Number(it.packagePrice).toFixed(2) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <div class="dialog-footer-tags">
                <div ref="dialogFooterTags" class="dialog-footer-tags-info">
                    <abc-tag-group>
                        <abc-tag-v2
                            v-for="tag in tags"
                            :key="tag.label"
                            size="small"
                            theme="success"
                            shape="round"
                            closable
                            @close="handleClose(tag)"
                        >
                            {{ tag.label }}
                        </abc-tag-v2>
                    </abc-tag-group>
                </div>
            </div>
            <template v-if="flag">
                <abc-popover
                    width="348px"
                    placement="top"
                    trigger="hover"
                    theme="white"
                >
                    <div>
                        <abc-tag-group>
                            <abc-tag-v2
                                v-for="tag in tags"
                                :key="tag.label"
                                size="small"
                                theme="success"
                                shape="round"
                                closable
                                @close="handleClose(tag)"
                            >
                                {{ tag.label }}
                            </abc-tag-v2>
                        </abc-tag-group>
                    </div>
                    <div slot="reference" class="dialog-footer-more">
                        更多...
                    </div>
                </abc-popover>
            </template>


            <abc-button @click="handleSave">
                确定
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import treatmentConfig from '@/assets/configure/treatment-config.js';
    import { mapGetters } from 'vuex';
    import {
        GoodsTypeEnum, GoodsSubTypeEnum,
    } from '@abc/constants';
    import { debounce } from 'utils/lodash';
    import { medicalFeeGrade2Str } from '@/filters';
    import { DEVICE_TYPE } from '@/views-hospital/inspect-setting/utils/constant';
    import GoodsV3API from 'api/goods/index-v3';

    const { EYE_DEVICE_TYPE } = require('@/views-hospital/inspect-regist/utils/constant');
    export default {
        name: 'ExaminationMedicalPrescription',

        props: {
            value: {
                type: Boolean,
                default: false,
            },
            title: {
                type: String,
                default: '检验医嘱',
            },
            currentTabChoice: {
                type: Number,
                default: 0,
            },
        },
        data() {
            const { itemCategory } = treatmentConfig;
            return {
                itemCategory,
                GoodsTypeEnum,
                DEVICE_TYPE,
                DEVICE_HANDLE: [
                    ...DEVICE_TYPE,
                    {
                        label: '眼科检查', value: EYE_DEVICE_TYPE,
                    },
                ],
                GoodsSubTypeEnum,
                loading: false,
                flag: false,
                tabOptions: [
                    {
                        label: '检验项目',
                        value: 0,
                    },
                    {
                        label: '检查项目',
                        value: 1,
                    },
                ],
                goodsList: [],
                currentTab: 0,
                searchKey: '',
                currentExaminationType: 1,
                currentInspectType: 1,
                examinationTypeList: [

                ],
                tags: [
                ],
                examinationList: [

                ],
                examinationMedicalPrescription: [

                ],
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            dialogVisible: {
                set(val) {
                    this.$emit('input', val);
                },
                get() {
                    return this.value;
                },
            },
            currentTags() {
                return this.tags.map((item) => {
                    return item.id;
                });
            },
        },
        watch: {
            tags: {
                handler(val) {
                    console.log(val?.length, this.$refs.dialogFooterTags);
                    const height = this.$refs.dialogFooterTags?.clientHeight || 34;
                    this.flag = height > 34;
                },
                immediate: true,
            },
        },
        async created() {
            this.currentTab = this.currentTabChoice;
            this._fetchGoods = debounce(this.fetchGoods, 200, true);
            await this.fetchGoods();
        },
        methods: {
            clean() {
                this.searchKey = '';
                this._fetchGoods();
            },
            medicalFeeGrade2Str(medicalFeeGrade) {
                return medicalFeeGrade2Str(medicalFeeGrade || '');
            },
            handleSave() {
                this.$emit('addExaminationMedicalPrescription', this.examinationMedicalPrescription);
                this.dialogVisible = false;
            },
            async fetchGoods() {
                const params = {
                    clinicId: this.currentClinic.clinicId,
                    key: this.searchKey,
                    jsonType: [{
                        type: this.GoodsTypeEnum.EXAMINATION, subType: [this.GoodsSubTypeEnum[this.GoodsTypeEnum.EXAMINATION].Inspect],
                    }],
                    cMSpec: '',
                    sex: '',
                    age: {},
                    offset: 0,
                    limit: 499,
                    withDomainMedicine: 1,
                };

                if (this.currentTab === 1) {
                    params.jsonType = [{
                        type: this.GoodsTypeEnum.EXAMINATION, subType: [this.GoodsSubTypeEnum[this.GoodsTypeEnum.EXAMINATION].Test],
                    }];
                }
                this.loading = true;
                try {
                    const { data } = await GoodsV3API.searchGoods(params);
                    const list = data?.list || [];
                    let handleList = [];
                    if (this.currentTab === 0) {
                        handleList = this.itemCategory.map((item) => {
                            return {
                                name: item.label,
                                rows: list.filter((it) => {
                                    const { bizExtensions = {} } = it;
                                    const itemCategory = bizExtensions?.itemCategory || 0;
                                    return Number(itemCategory) === item.value;
                                }).sort((a,b) => {
                                    return a?.name.localeCompare(b?.name);
                                }),
                            };
                        });
                    } else {
                        handleList = this.DEVICE_HANDLE.map((item) => {
                            return {
                                name: item.label,
                                rows: item.label === '眼科检查' ? list.filter((item) => {
                                    return item?.extendSpec === '20' || item?.extendSpec === 20 ;
                                }) : list.filter((it) => {
                                    const { bizExtensions = {} } = it;
                                    const itemCategory = bizExtensions?.itemCategory || 0;
                                    return Number(itemCategory) === item.value;

                                }).sort((a,b) => {
                                    return a?.name.localeCompare(b?.name);
                                }),
                            };
                        });
                    }
                    this.examinationList = handleList;
                    console.log(this.examinationList);
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            handleClose(tag) {
                const index = this.tags.findIndex((t) => t.id === tag.id);
                if (index !== -1) {
                    this.tags.splice(index, 1);
                    this.examinationMedicalPrescription = this.examinationMedicalPrescription.filter((i) => { return i.id !== tag.id;});
                }
            },
            setExaminationList(item) {
                const { tags } = this;
                if (tags.find((i) => {return i.id === item.id;})) {
                    this.tags = tags.filter((i) => { return i.id !== item.id;});
                    this.examinationMedicalPrescription = this.examinationMedicalPrescription.filter((i) => { return i.id !== item.id;});
                    return false;
                }
                tags.push({
                    label: item.name, id: item.id,
                });
                this.examinationMedicalPrescription.push(item);
                this.$nextTick(() => {
                    this.$refs.searchKey.focus();
                });
            },
            changeTab() {
                this.searchKey = '';
                this._fetchGoods();
            },
            changeType(type, num) {
                this.currentExaminationType = type;
                document.getElementById(`examination_medical-${num}`).scrollIntoView({
                    behavior: 'instant', block: 'start', inline: 'nearest',
                });

            },
            changeInspectType(type, num) {
                this.currentInspectType = type;
                document.getElementById(`examination_medical-${num}`).scrollIntoView({
                    behavior: 'instant', block: 'start', inline: 'nearest',
                });
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.examination_medical-prescription--dialog {
    .abc-dialog-header {
        padding: 0 16px !important;
        background-color: $S2 !important;

        .abc-dialog-headerbtn {
            background-color: $S2;
        }
    }

    .abc-tabs {
        border-bottom: none !important;

        .abc-tabs-item {
            &:last-child {
                margin-left: 24px !important;
            }
        }
    }

    &-body {
        display: flex;
        width: 100%;
        height: 100%;

        &-left {
            width: 150px;
            height: 100%;
            padding-top: 4px;
            background: #f9fafc;
            box-shadow: inset -1px 0 0 0 #e6eaee;

            @include scrollBar();

            > ul {
                > li {
                    width: 100%;
                    height: 32px;
                    padding: 0 16px;
                    font-size: 13px;
                    line-height: 32px;
                    color: $S1;

                    &:hover {
                        cursor: pointer;
                        background-color: $P4;
                    }
                }

                .current-item {
                    color: #005ed9;

                    &:hover {
                        background-color: $P6 !important;
                    }
                }
            }
        }

        &-right {
            flex: 1;
            height: 100%;

            &-search {
                width: 100%;
                height: 52px;
                padding: 12px 0 8px 12px;
            }

            &-list-box {
                width: 100%;
                max-height: 380px;
                overflow-y: scroll;

                @include scrollBar(false, 2px);
            }

            &-title {
                width: calc(100% - 40px);
                height: 30px;
                margin-bottom: 8px;
                margin-left: 20px;
                font-size: 14px;
                font-weight: bold;
                line-height: 30px;
                color: $T2;
                border-bottom: 1px solid $P4;
            }

            &-list {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                align-content: flex-start;
                align-items: flex-start;
                justify-content: flex-start;
                width: calc(100% - 2px);
                height: auto;
                padding: 0 8px 0 12px;
                margin-bottom: 32px;

                &-line {
                    display: flex;
                    align-items: center;
                    width: 320px;
                    height: 36px;
                    padding: 0 8px;
                    margin-bottom: 4px;
                    font-size: 14px;
                    color: $S1;
                    cursor: pointer;
                    background: $S2;
                    border: 1px solid $S2;
                    border-radius: 5px;

                    &:nth-child(2n) {
                        margin-left: 4px;
                    }

                    .abc-icon {
                        visibility: hidden;
                    }

                    &:hover {
                        background-color: $P4;
                    }

                    &-name {
                        width: 197px;
                        height: 20px;
                        line-height: 20px;

                        @include ellipsis;
                    }

                    &-price {
                        width: 63px;
                        height: 20px;
                        margin-left: 6px;
                        line-height: 20px;
                        text-align: right;

                        @include ellipsis;
                    }

                    &-type {
                        width: 37px;
                        height: 20px;
                        margin-left: 0;
                        line-height: 20px;
                        color: $T2;
                        text-align: right;
                    }
                }

                .is-current {
                    color: #1ec761;
                    background: #eefff5;
                    border: 1px solid #1ec761;

                    .abc-icon {
                        visibility: visible;
                    }
                }

                @include scrollBar();
            }
        }
    }

    .abc-dialog-footer {
        .dialog-footer {
            justify-content: space-between;

            &-more {
                width: 36px;
                height: 28px;
                margin-right: 8px;
                font-size: 12px;
                line-height: 28px;
                color: #1ec761;
                cursor: pointer;
            }

            &-tags {
                display: flex;
                align-items: flex-start;
                max-width: 660px;
                height: 34px;
                margin-right: 8px;
                overflow: hidden;

                &-info {
                    display: flex;
                    flex-wrap: nowrap;
                    align-content: center;
                    width: 660px;
                    min-height: 34px;

                    .abc-tag-group-wrapper {
                        box-sizing: border-box;
                        align-items: center;

                        .abc-tag-wrapper {
                            flex-shrink: 0;
                            margin-top: 8px;
                            margin-bottom: 6px !important;
                        }
                    }
                }
            }
        }
    }
}

.dialog-footer-tags {
    display: flex;
    align-items: center;

    ::v-deep .abc-tag-group-wrapper {
        box-sizing: border-box;
        align-items: center;

        .abc-tag-wrapper {
            flex-shrink: 0;
            margin-top: 8px;
            margin-bottom: 6px !important;
        }
    }
}
</style>
