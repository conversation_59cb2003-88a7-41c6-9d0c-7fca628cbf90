<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        :title="dialogTitle"
        class="advice-print-record-dialog-wrapper"
        :content-styles="contentStyles"
        custom-class="advice-print-record-dialog-preview"
        custom-top="0"
        append-to-body
        :auto-focus="false"
        @open="onDialogOpen"
    >
        <abc-flex style="height: 100%;">
            <div class="advice-print-record-dialog-iframe-wrapper">
                <iframe
                    v-if="previewHTML"
                    frameborder="0"
                    width="100%"
                    height="100%"
                    class="advice-print-record-dialog-iframe-content"
                    :srcdoc="previewHTML"
                ></iframe>
            </div>

            <abc-flex vertical class="advice-print-record-dialog-right">
                <abc-flex class="advice-print-record-dialog-list-title-wrapper">
                    <abc-text size="large" theme="black">
                        打印记录
                    </abc-text>
                </abc-flex>

                <abc-list
                    v-if="recordList && recordList.length"
                    v-slot="{
                        item, isSelected
                    }"
                    :data-list="recordList"
                    need-selected
                    show-divider
                    style="padding-left: 10px;"
                    @click-item="onClickItem"
                >
                    <abc-flex
                        vertical
                        :gap="8"
                        class="advice-print-record-dialog-list-wrapper"
                        :class="isSelected ? 'is-selected' : ''"
                    >
                        <abc-text size="normal">
                            {{ getDisplayTitle(item) }}
                        </abc-text>

                        <abc-flex justify="space-between" align="center" style="width: 100%;">
                            <abc-text size="mini" :theme="isSelected ? 'white' : 'gray'">
                                {{ item.printEmployeeName }}
                            </abc-text>

                            <abc-text size="mini" :theme="isSelected ? 'white' : 'gray'">
                                {{ item.printTime | parseTime('y.m.d h:i', true) }}
                            </abc-text>
                        </abc-flex>
                    </abc-flex>
                </abc-list>

                <abc-content-empty
                    v-else
                    value="暂无打印记录"
                    :show-icon="false"
                    top="24%"
                >
                </abc-content-empty>
            </abc-flex>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import { parseTime } from '@abc/utils-date';
    import ABCPrinterConfig from '@/printer/config';
    import { ABCPrintConfigKeyMap } from '@/printer/constants';
    import { MedicalAdviceTypeEnum } from '@/views-hospital/medical-prescription/utils/constants';

    export default {
        name: 'AdvicePrintRecordDialog',
        filters: {
            parseTime,
        },
        props: {
            patientOrderId: {
                type: String,
                default: '',
            },
            printCategory: {
                type: Number,
                default: 0,
            },
            contentStylesProp: {
                type: String,
                default: '',
            },
            adviceType: {
                type: Number,
                default: 10,
            },
            patientHospitalInfo: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            clinicName: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                visible: false,
                recordList: [],
                previewHTML: '',
            };
        },
        computed: {
            contentStyles() {
                return `height: calc(100% - 41px);${this.contentStylesProp}`;
            },
            dialogTitle() {
                return this.adviceType === MedicalAdviceTypeEnum.LONG_TIME ? '长期医嘱打印记录' : '临时医嘱打印记录';
            },
        },
        methods: {
            async onDialogOpen() {
                const { data } = await MedicalPrescriptionAPI.fetchAdvicePrintRecordList({
                    patientOrderId: this.patientOrderId, printCategory: this.printCategory,
                });
                const { rows = [] } = data || {};
                this.recordList = rows;

                this.$nextTick(() => {
                    const firstItem = document.querySelectorAll('.advice-print-record-dialog-preview .abc-normal-list-item')?.[0];
                    firstItem?.click?.();
                });
            },
            getDisplayTitle(item) {
                if (!item) return '';
                const {
                    pageIndexes = [], printType = 0,
                } = item;
                let str = printType ? '续打：' : '打印全部医嘱：';
                for (let i = 0; i < pageIndexes.length; i++) {
                    str += `第${pageIndexes[i] + 1}页`;
                    if (i < pageIndexes.length - 1) {
                        str += '、';
                    }
                }
                return str;
            },
            async onClickItem(e, item) {
                if (!item.id) return;
                const { data } = await MedicalPrescriptionAPI.fetchAdvicePrintRecordDetail(item.id);

                const printConfig = ABCPrinterConfig.getPrintConfigByKey(ABCPrintConfigKeyMap.hospitalAdviceShandong);
                const adviceInfo = {
                    adviceGroups: [],
                    printPageLastRecords: data.pages || [],
                };
                const printInstance = new window.AbcPackages.AbcPrint({
                    template: window.AbcPackages.AbcTemplates.hospitalAdviceShandong,
                    page: {
                        size: 'A4',
                        orientation: 1,
                        pageHeightLevel: null,
                        pageSizeReduce: printConfig.pageSizeReduce,
                        customStyles: {
                            margin: '12px auto',
                            boxShadow: '0px 0px 4px 2px rgba(0, 0, 0, 0.15)',
                            zoom: 0.83,
                        },
                    },
                    originData: {
                        ...adviceInfo,
                        adviceType: this.adviceType,
                        patientHospitalInfo: this.patientHospitalInfo,
                        clinicName: this.clinicName,
                        patientOrderId: this.patientOrderId,
                        printCategory: this.printCategory,
                        // 标识渲染的是打印记录
                        isPrintRecord: true,
                    },
                });

                await printInstance.init();
                this.previewHTML = await printInstance.splitPreview() || '';
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/mixin.scss';

@media screen and (min-height: 1080px) {
    .advice-print-record-dialog-wrapper .advice-print-record-dialog-preview.abc-dialog {
        height: 870px;
    }
}

@media screen and (min-height: 801px) and (max-height: 1079px) {
    .advice-print-record-dialog-wrapper .advice-print-record-dialog-preview.abc-dialog {
        height: 720px;
    }
}

@media screen and (max-height: 800px) {
    .advice-print-record-dialog-wrapper .advice-print-record-dialog-preview.abc-dialog {
        height: 630px;
    }
}

.advice-print-record-dialog-preview {
    min-height: 630px;

    .advice-print-record-dialog-iframe-wrapper {
        width: calc(100% - 272px);
        height: 100%;
        overflow: hidden;
        background-color: var(--abc-color-P3);
    }

    .advice-print-record-dialog-iframe-content {
        @include scrollBar();
    }

    .advice-print-record-dialog-right {
        position: relative;
        width: 272px;
        height: 100%;
    }

    .advice-print-record-dialog-list-title-wrapper {
        padding: 16px;
        border-bottom: 1px solid var(--abc-color-P8);
    }

    .advice-print-record-dialog-list-wrapper {
        width: 100%;
        padding: 5px 0;
        border-radius: var(--abc-border-radius-mini);

        &.is-selected {
            color: var(--abc-color-T4) !important;
        }
    }
}
</style>
