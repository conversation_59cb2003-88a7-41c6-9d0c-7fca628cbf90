<template>
    <abc-popover
        ref="relatedItem"
        class="hospital-related-product-wrapper"
        :theme="isRelated ? 'white' : 'yellow'"
        placement="bottom-end"
        :disabled="disabledPopover"
        :visible-arrow="false"
        :popper-style="{ padding: isRelated ? 0 : '10px' }"
    >
        <div slot="reference" class="ellipsis">
            {{ isMedicine ? medicineDetailStr : productRelateDetailStr }}
        </div>
        <div class="hospital-related-product__popover">
            <template v-if="isRelated">
                <abc-section style="padding: 16px;">
                    <abc-table
                        ref="table"
                        :render-config="renderConfig"
                        :data-list="goodsList"
                        :need-selected="false"
                        header-size="small"
                        cell-size="small"
                        theme="white"
                        type="pro"
                        style="max-height: 200px;"
                    >
                        <template #medicalFeeGrade="{ trData: row }">
                            <medical-fee-grade-td
                                v-if="getShebaoNationalCode(row.productInfo)"
                                :item="row"
                                :width="63"
                                :is-can-self-funded="false"
                                :show-pay-type-select="showPayTypeSelect"
                                class="table-td"
                                style="width: 100%;"
                                @change-pay-type="(val)=>setPayType(val, row)"
                            >
                            </medical-fee-grade-td>
                            <span v-else></span>
                        </template>
                        <template #footer>
                            <abc-flex flex="1" justify="flex-end">
                                <abc-p gray>
                                    共<span style="color: #000000;">{{ goodsList.length }}</span>条，
                                </abc-p>

                                <abc-space :size="4">
                                    <abc-p gray>
                                        单价合计
                                    </abc-p>
                                    <abc-p style="padding-right: 12px; color: #000000;">
                                        {{ formatMoney(amount ?? 0, false) }}
                                    </abc-p>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-section>
                <abc-flex
                    flex="1"
                    justify="flex-end"
                    align="center"
                    class="table-footer-btn"
                >
                    <!--<abc-button-->
                    <!--    type="primary"-->
                    <!--    @click="handleSave"-->
                    <!--&gt;-->
                    <!--    确定-->
                    <!--</abc-button>-->

                    <abc-button type="blank" @click="handleCancel">
                        关闭
                    </abc-button>
                </abc-flex>
            </template>
            <template v-else>
                <ul>
                    <li v-for="(item, index) in feeComposeList" :key="`${item.goodsId + index }`" class="relate-item__detail">
                        <span style="flex: 1; min-width: 100px;">{{ item | formatGoodsName }}</span>
                        <span v-if="adviceType === MedicalAdviceTypeEnum.ONE_TIME" class="desc">{{ usageInfoStr(item) }}</span>
                    </li>
                </ul>
            </template>
        </div>
    </abc-popover>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { MedicalAdviceTypeEnum } from '@/views-hospital/medical-prescription/utils/constants.js';
    import {
        formatMoney, medicalFeeGrade2Str,
    } from '@/filters/index.js';
    import MedicalFeeGradeTd from '@/views-hospital/medical-prescription/components/edit-medical-advice-table/components/medical-fee-grade-td.vue';
    import { isNotNull } from '@/utils';

    export default {
        name: 'RelateProduct',
        components: { MedicalFeeGradeTd },
        props: {
            advice: {
                type: Object,
                required: true,
            },
            adviceType: [String, Number],
            usage: String,
            isRelated: Boolean,
            isMedicine: Boolean,
        },
        data() {
            return {
                MedicalAdviceTypeEnum,
                tempGoodsIdMedicalInsurancePayTypes: [], // 临时商品使用医疗费用等级集合
            };
        },
        computed: {
            ...mapGetters(['infusionRelatedConfigs']),
            goodsList() {
                const goodsList = [];
                if (this.isMedicine) {
                    goodsList.unshift(...this.advice.items);
                }
                goodsList.push(...this.feeComposeList);
                return goodsList;
            },
            amount() {
                return this.goodsList.reduce((total, item) => {
                    return total + Number((item.productInfo?.packagePrice || item.packagePrice) ?? 0);
                }, 0);
            },
            /**
             * @desc 药品的收费项目详情
             * <AUTHOR>
             * @date 2023-03-13 15:09:17
             * @params
             * @return
             */
            medicineDetailStr() {
                const count = this.advice.items?.length;
                const nameArray = [];
                nameArray.push(this.advice.items[0].medicineCadn || this.advice.items[0].name);
                return `${count}项：${nameArray.join('、')}`;
            },
            feeComposeList() {
                const feeComposeRuleItems = this.advice.ruleItems?.filter((item) => {
                    // item.type === 1 是作为关联项的医嘱，
                    // item.type === 0 是手动添加的医嘱
                    return item.type;
                }) || [];
                const feeComposeRuleMap = new Map(feeComposeRuleItems.map((item) => [item.goodsId, item]));
                const goods = this.advice.items[0];
                const { feeComposeList = [] } = goods;
                if (!Array.isArray(feeComposeList)) {
                    return [];
                }
                feeComposeList.forEach((item) => {
                    this.$set(item, 'productInfo', {
                        medicalFeeGrade: item.medicalFeeGrade,
                        shebaoPayMode: item.shebaoPayMode,
                        shebaoNationalCode: item.shebaoNationalCode,
                    });
                    const matchedItem = feeComposeRuleMap.get(item.id);
                    if (matchedItem) {
                        this.$set(item, 'medicalInsurancePayType', matchedItem.medicalInsurancePayType);
                    }
                });
                return feeComposeList;
            },
            productRelateDetailStr() {
                const res = [];
                if (this.feeComposeList?.length) {
                    this.feeComposeList.forEach((item) => {
                        res.push(item.displayName);
                    });
                    return `${res.length}项: ${res.join('、')}`;
                }
                return `1项：${this.advice.items[0].name}`;
            },
            disabledPopover() {
                if (this.isRelated) return false;
                return this.feeComposeList.length === 0;
            },
            /**
             * @desc 能否选择 是否自费 payType
             */
            showPayTypeSelect() {
                return (
                    this.$abcSocialSecurity.isOpenSocial &&
                    (
                        this.$abcSocialSecurity.config.isZhejiangHangzhou || this.$abcSocialSecurity.config.isLiaoningPanjin
                    )
                );
            },
            renderConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: '项目名称',
                            key: 'displayName',
                            style: {
                                textAlign: 'left',
                                flex: 1,
                                minWidth: '160px',
                            },
                            customRender(h, row) {
                                return (
                                    <div class="table-cell ellipsis" title={row.medicineCadn || row.productInfo?.displayName || ''} style="min-height:32px">
                                        {row.medicineCadn || row.productInfo?.displayName || row.name || ''}
                                    </div>
                                );
                            },
                        },
                        {
                            label: '医保',
                            key: 'medicalFeeGrade',
                            slot: true,
                            style: {
                                textAlign: 'center',
                                width: '64px',
                                maxWidth: '64px',
                            },
                        },
                        {
                            label: '单价',
                            key: 'packagePrice',
                            colType: 'money',
                            style: {
                                textAlign: 'right',
                                width: '98px',
                                maxWidth: '98px',
                            },
                            customRender(h, row) {
                                return (
                                    <div class="table-cell" style="min-height:32px">
                                        {formatMoney((row.productInfo?.packagePrice || row.packagePrice) ?? 0, false)}
                                    </div>
                                );
                            },
                        },
                    ],
                };
            },
        },
        mounted() {
            //根据ruleItems的medicalInsurancePayType生产默认商品使用医疗费用等级集合
            this.generateDefaultGoodsIdMedicalInsurancePayTypes();
        },
        methods: {
            formatMoney,
            medicalFeeGrade2Str,
            generateDefaultGoodsIdMedicalInsurancePayTypes() {
                this.advice.ruleItems?.forEach((it) => {
                    if (it.medicalInsurancePayType !== null && it.medicalInsurancePayType !== undefined) {
                        this.tempGoodsIdMedicalInsurancePayTypes.push({
                            goodsId: it.goodsId,
                            medicalInsurancePayType: it.medicalInsurancePayType,
                        });
                    }
                });
                this.$emit('change-pay-type', this.tempGoodsIdMedicalInsurancePayTypes);
            },
            getUnitCount(item) {
                let unitCount = 0;
                if (item.unitCount) {
                    unitCount = item.unitCount || 1;
                } else {
                    unitCount = item.composePackageCount || item.composePieceCount || 1;
                }
                return +unitCount.toFixed(2);
            },
            getUnit(item) {
                let unit = '';
                if (item.unit) {
                    unit = item.unit || '';
                } else {
                    unit = item.composeUseDismounting ? item.pieceUnit : item.packageUnit;
                }
                return unit || '';
            },
            usageInfoStr(item) {
                const _arr = [];
                _arr.push(this.getUnitCount(item) + this.getUnit(item));
                _arr.push(`共${this.$t('currencySymbol')} ${formatMoney(item.composePrice)}`);
                return _arr.join('，');
            },
            handleSave() {
                this.$emit('change-pay-type',this.tempGoodsIdMedicalInsurancePayTypes);
                this.handleCancel();
            },
            handleCancel() {
                this.tempGoodsIdMedicalInsurancePayTypes = [];
                this.$refs.relatedItem.doClose();
            },
            setPayType(val,row) {
                this.$set(row, 'medicalInsurancePayType', val);
                const newObj = {
                    goodsId: row.goodsId,
                    medicalInsurancePayType: val,
                };
                const index = this.tempGoodsIdMedicalInsurancePayTypes.findIndex((item) => item.goodsId === newObj.goodsId);
                index !== -1 ? this.tempGoodsIdMedicalInsurancePayTypes[index] = newObj : this.tempGoodsIdMedicalInsurancePayTypes.push(newObj);

                this.$emit('change-pay-type',this.tempGoodsIdMedicalInsurancePayTypes);
            },
            getShebaoNationalCode(productInfo) {
                const {
                    shebaoNationalCode, shebao,
                } = productInfo || {};
                if (isNotNull(shebaoNationalCode)) return shebaoNationalCode;
                const { nationalCode } = shebao || {};
                if (isNotNull(nationalCode)) return nationalCode;
                return '';
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.hospital-related-product-wrapper {
    width: 100%;
}

.hospital-related-product__popover {
    font-size: 12px;

    .relate-item__detail {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        line-height: 16px;

        &:last-child {
            margin-bottom: 0;
        }

        .desc {
            margin-left: auto;
            color: $T2;
        }
    }

    .pay-type-text {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 14px;
    }

    .table-footer-btn {
        height: 56px;
        padding: 0 16px;
        border-top: var(--abc-border-1, 1px) solid var(--abc-color-P8, #eaedf1);
    }
}
</style>
