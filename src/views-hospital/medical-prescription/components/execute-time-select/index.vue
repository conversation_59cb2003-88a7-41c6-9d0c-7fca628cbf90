<template>
    <abc-popover
        ref="executeItem"
        theme="custom"
        class="medical-execute-time__select-wrapper"
        placement="bottom-start"
        :visible-arrow="false"
        :disabled="hiddenPopover"
        popper-class="medical-execute-time__popover-wrapper"
    >
        <div slot="reference" class="execute-time-str ellipsis">
            {{ getTimeStr }}
        </div>
        <div
            v-if="freqInfo && freqInfo.code"
            v-abc-click-outside="closeHandler"
        >
            <execute-item-table ref="executeTimeTable" :freq-info="freqInfo"></execute-item-table>
            <div class="execute-item-change-wrapper">
                <div class="execute-item-table__title">
                    首日执行时间
                </div>
                <template v-if="isWeekExecute">
                    <div
                        v-for="(item, index) in dailyTimings"
                        :key="index"
                        class="execute-item-table__table"
                    >
                        <div class="execute-item-table__td">
                            <abc-checkbox :label="item.timings[0]" :value="getCheckStatus(item.timings[0])" @change="(val) => handleChange(val, item.timings[0])"></abc-checkbox>
                        </div>
                        <div class="execute-item-table__week-td">
                            {{ week[item.weekday] }}
                        </div>
                        <div class="execute-item-table__week-time">
                            {{ item.timings[0] }}
                        </div>
                    </div>
                </template>
                <template v-else>
                    <abc-checkbox-group v-model="firstDayTimings">
                        <div v-for="(item, index) in timings" :key="index" class="execute-item-table__time-table">
                            <div class="execute-item-table__td">
                                <abc-checkbox :label="item" @change="handleChangeDay"></abc-checkbox>
                            </div>
                            <div class="execute-item-table__week-time">
                                {{ item }}
                            </div>
                        </div>
                    </abc-checkbox-group>
                </template>
            </div>

            <div class="select-footer">
                <abc-button size="small" @click="handleConfirm">
                    确定
                </abc-button>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import ExecuteItemTable
        from '@/views-hospital/settings-common/frames/doctor-advice-step-setting/execute-item-table.vue';
    import clone from 'utils/clone.js';
    import {
        getExecuteStr, getIsWeekExecute,
    } from '@/views-hospital/settings-hospital/utils/execute-time.js';
    import { ST_FREQ } from '@/views-hospital/medical-prescription/utils/constants.js';

    export default {
        name: 'Index',
        components: {
            ExecuteItemTable,
        },
        props: {
            freqInfo: {
                type: Object,
                required: true,
            },
            disabled: Boolean,
        },
        data() {
            return {
                timings: [],
                dailyTimings: [],
                firstDayTimings: [],
            };
        },
        computed: {
            getTimeStr() {
                if (this.freqInfo?.code === ST_FREQ) return '立即执行';
                let res = '';
                if (this.freqInfo.firstDayTimings?.length) {
                    res = `首日${this.freqInfo.firstDayFrequency}次，`;
                }
                return `${res}${getExecuteStr(this.freqInfo)}`;
            },

            isWeekExecute() {
                return getIsWeekExecute(this.freqInfo.code);
            },

            hiddenPopover() {
                return this.disabled || !this.freqInfo || this.freqInfo.code === ST_FREQ;
            },
            week() {
                return ['', '周一', '周二','周三','周四','周五','周六', '周日'];
            },
        },
        watch: {
            freqInfo: {
                handler() {
                    if (!this.freqInfo) return;
                    this.dailyTimings = this.freqInfo?.dailyTimings || [];
                    if (!this.isWeekExecute) {
                        this.timings = this.dailyTimings[0]?.timings || [];
                    }
                    this.firstDayTimings = clone(this.freqInfo?.firstDayTimings) || [];
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            handleCancel() {
                this.$refs.executeItem.doClose();
            },
            handleConfirm() {
                // const firstDayFrequency = this.firstDayTimings?.length || 0;
                // this.$emit('confirm', this.firstDayTimings, firstDayFrequency);
                this.$emit('confirm');
                this.handleCancel();
            },
            getCheckStatus(item) {
                return this.firstDayTimings.findIndex((time) => {
                    return time === item;
                }) > -1;
            },
            handleChange(isChecked, value) {
                if (this.isWeekExecute) {
                    this.firstDayTimings = [];
                    if (isChecked) {
                        this.firstDayTimings = [value];
                    }
                    this.handleChangeDay();
                }
            },
            handleChangeDay() {
                this.$nextTick(() => {
                    this.freqInfo.firstDayTimings = clone(this.firstDayTimings);
                    this.freqInfo.firstDayFrequency = this.freqInfo.firstDayTimings.length || 0;
                });
            },
            closeHandler() {
                if (this.$refs?.executeTimeTable?.$refs?.timePicker) {
                    const timePickerIndex = this.$refs?.executeTimeTable?.$refs?.timePicker?.findIndex((item) => {
                        return item.showPopper;
                    });
                    if (timePickerIndex > -1) {
                        return;
                    }
                }
                this.$refs.executeItem.showPopper = false;
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/mixin.scss';

.medical-execute-time__select-wrapper {
    width: 100%;
    height: 100%;

    .execute-time-str {
        width: 100%;
        height: 100%;
        padding: 10px;
    }
}

.medical-execute-time__popover-wrapper {
    width: 262px;
    max-height: 500px;
    padding: 12px;
    padding-right: 2px;
    overflow: scroll;
    background: #ffffff;
    border: 1px solid #b7b9c2;
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

    @include scrollBar();

    .execute-item-change-wrapper {
        width: 100%;
        margin-top: 16px;
        font-size: 14px;
        color: $T2;

        .execute-item-table__title {
            height: 20px;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 20px;
            color: $T2;
        }

        .execute-item-table__table,
        .execute-item-table__time-table {
            display: flex;
            width: 100%;
            height: 32px;
            font-size: 14px;
            border: 1px solid $P6;
            border-top: none;
        }

        .execute-item-table__table {
            &:nth-child(2) {
                border-top: 1px solid $P6;
            }
        }

        .execute-item-table__time-table {
            &:nth-child(1) {
                border-top: 1px solid $P6;
            }
        }

        .execute-item-table__td {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 32px;
            color: $T2;
            text-align: center;
            border-right: 1px solid $P6;
        }

        .execute-item-table__week-td {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            border-right: 1px solid $P6;
        }

        .execute-item-table__week-time {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: center;
            height: 32px;
        }
    }

    .select-footer {
        display: flex;
        justify-content: end;
        margin-top: 12px;
    }
}
</style>
