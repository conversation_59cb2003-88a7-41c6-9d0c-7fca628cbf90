<template>
    <div
        ref="goodsRemark"
        v-abc-click-outside="closePopper"
        :style="inputWrapperStyle"
        :class="[
            'abc-input-wrapper',
            'hospital__advice-remark-wrapper',
            size ? `abc-input-${ size }-wrapper` : '',
            {
                'is-disabled': disabled,
                'is-focus': isFocus || showPopper,
            },
        ]"
        @click="clickSelect"
    >
        <input
            ref="abcinput"
            v-model="currentValue"
            :class="[
                'abc-input__inner',
                { 'text-center': textCenter },
                { 'show-prepend': showPrepend },
                { 'show-append': showAppend }
            ]"
            :style="inputStyle"
            :tabindex="tabindex"
            :disabled="disabled"
            :readonly="readonly"
            :maxlength="maxLength"
            :placeholder="showPrepend ? '' : placeholder"
            :title="currentValue"
            @blur="handleBlur"
            @focus="handleFocus"
            @keydown.enter="handelEnter"
            @keydown.delete="handleDelete"
            @keydown.tab="closePopper"
            @keydown.down.prevent="down"
            @keydown.up.prevent="up"
            @keydown.left="left"
            @keydown.right="right"
        />



        <div v-if="showAppend" class="icon-meridian-wrapper" @click.stop="$emit('icon-click')">
            <abc-icon icon="meridian"></abc-icon>
        </div>
        <div v-if="showPrepend" class="remark-prepend-info" :style="{ 'left': `${calcPaddingLeftStyle }px` }">
            {{ prependInfo }}
        </div>
        <div class="input-tag" :style="{ 'width': `${calcPaddingLeftStyle }px` }">
            <supplement-icon v-if="hasSupplementTag"></supplement-icon>
            <source-tag
                v-for="item in renderTags"
                :key="item.name"
                :name="item.name"
                :color="item.color"
                style="margin-right: 4px;"
                @click.native="handleClickTag"
            ></source-tag>
            <psychotropic-narcotic-type v-if="operateTags.length" :tags="operateTags"></psychotropic-narcotic-type>
        </div>


        <advice-remarks
            v-if="showPopper"
            ref="childComponent"
            :visible.sync="showPopper"
            :current-value="currentValue"
            :placement="placement"
            :remark-type="remarkType"
            :show-no-charge="showNoCharge"
            :tag-types="tags"
            :is-need-supplement-history="isNeedSupplementHistory"
        ></advice-remarks>
    </div>
</template>

<script type="text/ecmascript-6">
    import common from 'components/common/form';
    import AdviceRemarks from '@/views-hospital/medical-prescription/components/advice-remark/advice-remarks.vue';
    import SourceTag from 'views/layout/source-tag.vue';
    import SupplementIcon from '@/views-hospital/medical-prescription/components/supplement-icon.vue';
    import {
        AdviceTagEnum, AdviceTagEnumTEXT,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import clone from 'utils/clone.js';

    import $style from 'src/styles/theme.module.scss';
    import PsychotropicNarcoticType from 'views/layout/psychotropic-narcotic-type.vue';

    export default {
        name: 'AdviceRemark',
        components: {
            PsychotropicNarcoticType,
            AdviceRemarks,
            SourceTag,
            SupplementIcon,
        },
        directives: {
            focus: {
                // 指令的定义
                inserted(el) {
                    el.addEventListener('click', () => {
                        $(el).prev()[0].focus();
                    });
                },
            },
        },
        mixins: [common],
        props: {
            value: [String, Number],
            placeholder: String,
            tabindex: [Number, String],
            width: Number,
            disabled: Boolean,
            readonly: {
                type: Boolean,
                default: false,
            },
            index: Number,
            placement: {
                type: String,
                default: 'bottom-end',
            },
            size: String,
            maxLength: {
                type: [Number, String],
                default: 20,
            },

            // 聚焦自动展开options
            focusShowOptions: {
                type: Boolean,
                default: false,
            },
            textCenter: {
                type: Boolean,
                default: false,
            },
            inputStyle: [Object, String],

            showNoCharge: {
                type: Boolean,
                default: false,
            },
            remarkType: {
                type: Number,
                required: true,
            },
            prependInfo: String,
            showAppend: Boolean,
            isNeedSupplementHistory: {
                type: Boolean,
                default: true,
            },
            tagTypes: Array,
        },
        data() {
            return {
                showPopper: false,
                isFocus: false,
                hoverPIndex: null,
                showSupplementaryTag: false,
                isNeedConfirm: false,
                tags: [],
                AdviceTagEnum,
            };
        },
        computed: {
            currentValue: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            inputWrapperStyle() {
                let width = '';
                if (typeof this.width === 'number') {
                    width = `${this.width}px`;
                } else {
                    width = this.width;
                }
                return {
                    width , paddingLeft: this.calcPaddingLeftStyle > 0 ? `${this.calcPaddingLeftStyle + 4}px` : `${this.calcPaddingLeftStyle}px`,
                };
            },
            showPrepend() {
                return this.prependInfo;
            },
            calcPaddingLeftStyle() {
                let left = 0;
                if (this.hasOperateIng) {
                    left += 44;
                }
                if (this.hasOperateAfter) {
                    left += 44;
                }
                if (this.hasSupplementTag) {
                    left += 22;
                }
                if (this.renderTags.length) {
                    left += 22;
                }
                if (left > 56) {
                    return 56;
                }
                return left;
            },
            hasSupplementTag() {
                return this.tags.includes(AdviceTagEnum.SUPPLEMENT);
            },
            // 术中
            hasOperateIng() {
                return this.tags.includes(AdviceTagEnum.OPERATE_ING);
            },
            // 术后
            hasOperateAfter() {
                return this.tags.includes(AdviceTagEnum.OPERATE_AFTER);
            },
            operateTags() {
                return this.tags.filter((it) => it === AdviceTagEnum.OPERATE_AFTER || it === AdviceTagEnum.OPERATE_ING);
            },
            renderTags() {
                const res = [];
                this.tags.forEach((item) => {
                    if (item === AdviceTagEnum.URGENT) {
                        res.push({
                            name: AdviceTagEnumTEXT[AdviceTagEnum.URGENT],
                            color: $style.R1,
                        });
                    }
                });
                return res;
            },
        },
        watch: {
            tagTypes: {
                handler(val) {
                    this.tags = val?.length ? clone(val) : [];
                },
            },
        },

        created() {
            this.$on('handleOptionClick', this.handleOptionSelect);
            this.$on('handleTagTypeClick', this.handleTagTypeClick);
        },
        beforeDestroy() {
            this.$off('handleOptionClick', this.handleOptionSelect);
            this.$off('handleTagTypeClick', this.handleTagTypeClick);
        },
        methods: {
            setTags(tag) {
                let isNeedConfirm = false;
                if (this.tags.includes(tag)) {
                    this.tags = this.tags.filter((item) => {return item !== tag;});
                    if (tag === this.AdviceTagEnum.SUPPLEMENT) {
                        this.$Toast({
                            message: '已取消补开',
                            type: 'info',
                        });
                    }
                    if (tag === this.AdviceTagEnum.URGENT) {
                        this.$Toast({
                            message: '已取消加急',
                            type: 'info',
                        });
                    }
                } else {
                    // 术中和术后是互斥的, 已经选了其中一个, 再选另一个就会先取消前一个
                    if (tag === AdviceTagEnum.OPERATE_ING && this.tags.includes(AdviceTagEnum.OPERATE_AFTER)) {
                        this.tags = this.tags.filter((it) => it !== AdviceTagEnum.OPERATE_AFTER);
                    }
                    if (tag === AdviceTagEnum.OPERATE_AFTER && this.tags.includes(AdviceTagEnum.OPERATE_ING)) {
                        this.tags = this.tags.filter((it) => it !== AdviceTagEnum.OPERATE_ING);
                    }
                    this.tags.push(tag);
                    if (tag === this.AdviceTagEnum.SUPPLEMENT) {
                        this.$emit('change-tags', this.tags);
                        isNeedConfirm = true;
                    }
                }
                this.showPopper = false;
                this.$emit('change-tags', this.tags, isNeedConfirm);
            },
            handleClickTag() {
                this.$nextTick(() => {
                    this.$refs.abcinput.focus();
                    this.$refs.abcinput.selectionStart = 0;
                    this.$refs.abcinput.selectionEnd = this.$refs.abcinput.value.length;
                });
            },
            up(e) {
                if (this.readonly) {
                    e.preventDefault();
                }

                if (!this.showPopper) {
                    this.$emit('up', e);
                    return;
                }
                e.stopPropagation();
                this.$refs.childComponent?.up();
            },

            down(e) {
                if (this.readonly) {
                    e.preventDefault();
                }

                if (!this.showPopper) {
                    this.$emit('down', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.down();
            },

            left(e) {
                if (this.readonly) {
                    e.preventDefault();
                }
                // 当下拉框展开的时候不能左右键切走
                if (!this.showPopper) {
                    this.$emit('left', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.left();

            },

            right(e) {
                if (this.readonly) {
                    e.preventDefault();
                }
                // 当下拉框展开的时候不能左右键切走
                if (!this.showPopper) {
                    this.$emit('right', e);
                    return;
                }

                e.stopPropagation();
                this.$refs.childComponent?.right();
            },

            handleFocus(event) {
                this.isFocus = true;
                this.$emit('focus', event);
                if (this.focusShowOptions && !this.disabled) {
                    this._isFocusShow = true;
                    this.showPopper = true;
                }
            },

            handleBlur(event) {
                this.isFocus = false;
                this.$emit('blur', event);
            },
            handleDelete(event) {
                this.isFocus = true;
                this.$emit('delete', event);
            },

            clickSelect() {
                if (this.disabled) return false;
                /**
                 * @desc 解决focus click冲突问题
                 * 用户点击 会先触发focus事件 后执行click事件
                 * 开启了 focusShowOptions focus事件就已经展开options，后执行的click事件不再响应
                 * <AUTHOR>
                 * @date 2022-01-26 17:35:58
                 */
                if (this._isFocusShow) {
                    this._isFocusShow = false;
                    return false;
                }
                this.showPopper = !this.showPopper;
            },

            handelEnter(event) {
                if (this.showPopper) {
                    this.$refs.childComponent?.enter();
                    this.$emit('enter', event);
                    this.showPopper = false;
                } else {
                    this.showPopper = !this.showPopper;
                }
            },

            closePopper() {
                this.showPopper = false;
            },

            handleOptionSelect(val) {
                val !== this.value && this.$emit('change', val, this.index);
                this.formItem && this.formItem.$emit('formFieldChange', val);
                this.currentValue = val;
                this.showPopper = false;
            },
            handleTagTypeClick(val) {
                this.setTags(val);
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';

.hospital__advice-remark-wrapper {
    position: relative;
    cursor: pointer;

    .abc-input__inner {
        width: 100%;
        height: 28px;
        padding: 3px 4px;
        line-height: 1;
        text-align: left;
        cursor: pointer;

        &.text-center {
            padding: 3px 0;
            text-align: center;
        }

        &.show-prepend {
            padding-left: 62px !important;
        }

        &.show-append {
            padding-right: 24px !important;
        }
    }

    .remark-prepend-info {
        position: absolute;
        top: 10px;
        left: 0;
        z-index: 3;
        max-width: 60px;
        padding-left: 4px;
        overflow: hidden;
        color: $G1;
        white-space: nowrap;
    }

    .icon-meridian-wrapper {
        position: absolute;
        top: 8px;
        right: 2px;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        min-width: 24px;
        height: 24px;
        color: $P1;
        background-color: transparent;
        border-color: transparent;
        border-radius: 16px;

        &:hover {
            color: #96a4b3;
            background-color: $P4;
        }
    }

    .input-tag {
        position: absolute;
        top: 0;
        left: 4px;
        z-index: 3;
        display: flex;
        align-items: center;
        height: 100%;
        overflow: hidden;
    }
}
</style>
