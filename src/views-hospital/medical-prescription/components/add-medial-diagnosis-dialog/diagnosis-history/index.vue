<template>
    <div class="medical-diagnosis__history-wrapper">
        <ul v-if="historyList && historyList.length">
            <li v-for="(history, index) in historyList" :key="index" @click="handleExpandHistory(index)">
                <div class="medical-diagnosis-history-header" :class="{ 'is-expand': historyList[index].isExpand && activeIndex === index }" @click="getDiagnosisDetail(history)">
                    <div class="medical-diagnosis-history-header__left" :class="{ 'is-expand': historyList[index].isExpand && activeIndex === index }">
                        <span>{{ history.typeName }}</span>
                        <span :title="history.diseaseName">{{ history.diseaseName }}</span>
                    </div>
                    <div class="medical-diagnosis-history-header__right" :class="{ 'is-expand': historyList[index].isExpand && activeIndex === index }">
                        <span class="name">
                            {{ history.doctorName }}
                        </span>
                        <span class="time">
                            {{ history.diagnosedTime | parseTime('m-d') }}
                        </span>
                        <abc-icon
                            v-if="historyList[index].isExpand && activeIndex === index"
                            style="margin-right: 14px;"
                            icon="dropdown_line_up"
                            color="#96a4b3"
                            size="14"
                        ></abc-icon>
                        <abc-icon
                            v-else
                            style="margin-right: 14px;"
                            icon="dropdown_line"
                            color="#96a4b3"
                            size="14"
                        ></abc-icon>
                    </div>
                </div>
                <div v-if="historyList[index].isExpand" class="medical-diagnosis-history-content">
                    <div
                        v-for="(item, itemIndex) in histories"
                        :key="itemIndex"
                        class="diagnosis-item"
                    >
                        <span class="diagnosis-type"> {{ item.typeName }}</span>

                        <div
                            v-for="(diagnosis) in item.list"
                            :key="diagnosis.id"
                            class="diagnosis-result"
                        >
                            <div
                                class="diagnosis-content"
                                @mouseleave="handleMouseLeave"
                                @mouseenter="handleMouseEnter(diagnosis)"
                            >
                                <span :title="diagnosis.diseaseStr">{{ diagnosis.diseaseStr }}</span>
                                <abc-button
                                    v-if="showCopy && currentDiagnosisId === diagnosis.id"
                                    type="text"
                                    size="small"
                                    style=" width: 40px; font-size: 12px;"
                                    @click="handleCopy(diagnosis)"
                                >
                                    复制
                                </abc-button>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
        <div v-else class="no-data">
            暂无诊断历史
        </div>
    </div>
</template>

<script>
    import { parseTime, createGUID } from 'utils/index';
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
    import { DiagnosisTypeTextEnum } from '@/views-hospital/medical-prescription/utils/constants.js';
    export default {
        components: {
        },
        props: {
            diagnosisHistory: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                DiagnosisTypeTextEnum,
                historyList: [],
                showCopy: false,
                currentDiagnosisId: -1,
                isExpand: false,
                activeIndex: 0,
                histories: [],
            };
        },
        watch: {
            diagnosisHistory: {
                handler(list) {
                    this.historyList = list.map((item) => {
                        return {
                            ...item,
                            isExpand: this.$set(item, 'isExpand', false),
                        };
                    });
                    if (this.historyList?.length) {
                        this.handleExpandHistory(this.activeIndex);
                        this.getDiagnosisDetail(this.historyList[0]);
                    }
                },
                immediate: true,
            },
        },
        methods: {
            parseTime,
            handleMouseEnter(diagnosis) {
                this.currentDiagnosisId = diagnosis.id;
                this.showCopy = true;
            },
            handleMouseLeave() {
                this.currentDiagnosisId = -1;
                this.showCopy = false;
            },
            handleCopy(diagnosis) {
                // 发病日期和诊断日期默认当前时间
                diagnosis.sickTime = parseTime(new Date(), 'y-m-d', true),
                diagnosis.diagnosedTime = parseTime(new Date(), 'y-m-d', true),
                this.$emit('copy-diagnosis', diagnosis);
            },
            handleExpandHistory(index) {
                this.activeIndex = index;
                this.historyList[index].isExpand = !this.historyList[index].isExpand;
                this.historyList.forEach((item, idx) => {
                    if (idx !== this.activeIndex) {
                        item.isExpand = false;
                    }
                });
            },
            calcDiseaseName(diagnosis) {
                const {
                    diseaseName, diagnosedStatus, isPrimary, tcmSyndromeType = [],
                } = diagnosis;
                const tcmSyndromeTypeText = tcmSyndromeType?.map((it) => it.name)?.join(',');
                if (isPrimary) {
                    return `${diseaseName || ''}[主]${tcmSyndromeTypeText?.length ? `[证型]${tcmSyndromeTypeText}` : ''}`;
                }
                return `${diseaseName}${diagnosedStatus ? '?' : ''}${tcmSyndromeTypeText?.length ? `[证型]${tcmSyndromeTypeText}` : ''}`;
            },
            async getDiagnosisDetail(historyItem) {
                const {
                    type,
                    patientOrderId,
                    outpatientOrderId,
                } = historyItem;
                const params = {};
                if (type === 30) {
                    params.outpatientOrderId = outpatientOrderId;
                } else {
                    params.patientOrderId = patientOrderId;
                }
                try {
                    const res = await MedicalPrescriptionAPI.getDiagnosisList({
                        ...params,
                    });
                    if (res) {
                        this.histories = res?.reduce((acc, item) => {
                            const existingGroup = acc.find((group) => group.type === item.type);
                            if (existingGroup) {
                                item.diseaseStr = this.calcDiseaseName(item);
                                item.id = createGUID();
                                existingGroup.list.push(item);
                            } else {
                                acc.push({
                                    type: item.type,
                                    typeName: item.typeName,
                                    diseaseName: item.diseaseName,
                                    list: [{
                                        ...item,
                                        id: createGUID(),
                                        diseaseStr: this.calcDiseaseName(item),
                                    }],
                                });
                            }
                            return acc;
                        }, []);
                    }

                } catch (err) {
                    console.log(err);
                }
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme";
@import "src/styles/mixin";

.medical-diagnosis__history-wrapper {
    height: 584px;
    overflow-x: hidden;
    overflow-y: auto;
    overflow-y: overlay;
    background-color: #f9fafc;

    @include scrollBar();

    .medical-diagnosis-history-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44px;
        padding: 0 16px;
        padding-right: 0;
        font-size: 13px;
        background-color: #f3f5f7;
        border-bottom: 1px solid $P6;

        &.is-expand {
            font-weight: bold;
            background-color: #f9fbfc;
        }

        &__left {
            display: flex;
            align-items: center;

            &.is-expand {
                font-weight: bold;
            }

            span {
                display: inline-block;
                max-width: 100px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                &:first-child {
                    margin-right: 5px;
                }
            }
        }

        &__right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: $T2;

            &.is-expand {
                font-weight: bold;
                color: #000000;
            }

            .time {
                margin: 0 10px;
                margin-right: 5px;
            }
        }
    }

    .medical-diagnosis-history-content {
        width: 310px;
        max-height: 200px;
        padding: 12px 16px;
        margin-top: -4px;
        overflow-y: auto;
        background-color: #f3f5f7;
        border-bottom: 1px solid $P6;

        .diagnosis-item {
            margin-top: 10px;

            &:hover {
                cursor: pointer;
            }

            &:first-child {
                margin-top: 0;
            }

            span {
                display: block;
                line-height: 22px;
            }

            .diagnosis-type {
                font-size: 13px;
                color: #000000;
            }

            .diagnosis-result {
                display: flex;
                justify-content: space-between;
                font-size: 13px;
                color: $T2;

                .diagnosis-content {
                    display: flex;
                    justify-content: space-between;
                    width: 100%;
                    height: 22px;

                    span {
                        display: inline-block;
                        max-width: 220px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }

    > .no-data {
        margin-top: 200px;
        font-size: 14px;
        color: $T3;
        text-align: center;
    }

    .scroll-loading {
        display: flex;
        align-items: center;
        height: 32px;
        padding-left: 126px;
        font-size: 12px;
        color: $T2;

        &::after {
            display: inline-block;
            width: 0;
            overflow: hidden;
            vertical-align: bottom;
            content: "\2026";
            -webkit-animation: ellipsis steps(4, end) 900ms infinite;
            animation: ellipsis steps(4, end) 900ms infinite;
        }
    }
}
</style>
