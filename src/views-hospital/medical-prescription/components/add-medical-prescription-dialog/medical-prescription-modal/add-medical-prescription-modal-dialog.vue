<template>
    <abc-dialog
        v-model="dialogVisible"
        title="保存为医嘱模板"
        custom-class="add-medical-prescription-modal_dialog--box"
        :append-to-body="true"
        content-styles="width: 384px; padding:24px 24px; height: 200px;"
    >
        <div class="add-medical-prescription-modal_dialog--box-body">
            <div class="add-medical-prescription-modal_dialog--box-body-line">
                <div class="label">
                    模板名
                </div>
                <abc-input v-model="modelName" :width="252"></abc-input>
            </div>
            <div class="add-medical-prescription-modal_dialog--box-body-line">
                <div class="label">
                    目录
                </div>
                <div style="display: flex; align-items: center; width: 252px; height: 32px;">
                    <abc-radio v-model="typeValue" :label="2">
                        个人
                    </abc-radio>
                    <abc-radio v-model="typeValue" :label="3">
                        科室
                    </abc-radio>
                </div>
            </div>
            <div class="add-medical-prescription-modal_dialog--box-body-line">
                <div class="label">
                </div>
                <div style="width: 252px;">
                    <abc-popover
                        ref="catalogues-selector"
                        class="catalogues-selector"
                        trigger="click"
                        placement="bottom"
                        width="auto"
                        theme="white"
                        :visible-arrow="false"
                        :popper-style="{ padding: 0 }"
                    >
                        <div slot="reference" style="padding-bottom: 4px;">
                            <abc-input v-model="parentNodeName" :width="254" readonly></abc-input>
                        </div>
                        <div v-show="folder.length" class="catalogues-folder-popover" style="width: 254px;">
                            <div class="abc-tree-node-content">
                                <div
                                    class="custom-node-wrapper custom-node-content"
                                    style="cursor: pointer;"
                                    @click="selectFolder({
                                        id: null, name: addRootTitle
                                    })"
                                >
                                    <div class="node-name" style="padding-left: 12px;">
                                        <img src="~assets/images/<EMAIL>" alt="" />
                                        <span>{{ addRootTitle }}</span>
                                    </div>
                                </div>
                            </div>

                            <abc-tree
                                :data="folder"
                                :max-depth="40"
                                :indent="18"
                                @node-click="selectFolder"
                            >
                                <template #default="{ node }">
                                    <div class="custom-node-wrapper" style="cursor: pointer;">
                                        <img src="~assets/images/<EMAIL>" alt="" />
                                        <span>{{ node.name }}</span>
                                    </div>
                                </template>
                            </abc-tree>
                        </div>
                    </abc-popover>
                </div>
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <abc-button :disabled="disabled" @click="handleSave">
                确定
            </abc-button>
            <abc-button type="blank" @click="handleCancel">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { CategoryTemplateAPI } from 'api/catalogue-template';
    import { mapGetters } from 'vuex';

    export default {
        name: 'AddMedicalPrescriptionModalDialog',
        props: {
            value: Boolean,
            adviceGroups: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            departmentId: String,
        },
        data() {
            return {
                modelName: '',
                typeValue: 2,
                menuNameId: '',
                folder: [],
                parentNodeName: '',
                postInfo: {
                    parentId: '',
                },
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            ...mapGetters([ 'userInfo' ]),
            disabled() {
                return !this.modelName && !this.menuNameId;
            },
            dialogVisible: {
                set(val) {
                    this.$emit('input', val);
                },
                get() {
                    return this.value;
                },
            },
            addRootTitle() {
                return this.typeValue === 2 ? '个人模板' : '科室模板';
            },
        },
        watch: {
            typeValue(val) {
                if (val) {
                    this.folder = [];
                    this.parentNodeName = '';
                    this.initFolder();
                }
            },
        },
        async created() {
            await this.initFolder();
        },
        methods: {
            selectFolder(node) {
                if (!node?.id) {
                    if (!this.folder?.length) {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: '暂无子目录是否去创建目录?',
                            onConfirm: async () => {
                                this.dialogVisible = false;
                                this.$emit('createModelMenu');
                            },
                        });
                    } else {
                        this.$Toast({
                            message: '创建文件不能选择根结点',
                            type: 'error',
                        });
                    }
                    return;
                }
                this.$refs['catalogues-selector'].showPopper = false;
                this.postInfo.parentId = node.id;
                this.parentNodeName = node.name;
            },
            async initFolder() {
                const params = {
                    type: 0,
                    category: this.typeValue,
                };
                try {
                    const { data } = await CategoryTemplateAPI.getCategoryList(params);
                    const { children } = data;
                    this.folder = this.filterCatalogueFile(children || []);
                } catch (e) {
                    console.log(e);
                }
            },
            filterCatalogueFile(list, isFolder = 1) {
                if (!list?.length) {
                    return list;
                }
                return list.map((item) => {
                    let children = item?.children || [];
                    if (children?.length) {
                        children = children?.filter((i) => {
                            return i.isFolder === isFolder;
                        });
                        children = children.map((i) => {
                            if (i?.children?.length) {
                                i.children = this.filterCatalogueFile(i.children);
                            }
                            return {
                                ...i,
                            };
                        });
                    }
                    return {
                        ...item,
                        children,
                    };
                }).filter((item) => {
                    return item.isFolder === isFolder;
                });
            },
            async handleSave() {
                if (!this.modelName) {
                    this.$Toast({
                        type: 'error',
                        message: '请设置模板名称',
                    });
                    return;
                }
                if (!this.parentNodeName) {
                    this.$Toast({
                        type: 'error',
                        message: '请选择文件夹',
                    });
                    return;
                }
                const params = {
                    category: this.typeValue,
                    file: this.adviceGroups || {},
                    isFolder: 0, // 0 文件 1 目录
                    name: this.modelName,
                    ownerId: this.typeValue === 3 ? this.departmentId : this.userInfo.id,
                    ownerType: this.typeValue,
                    parentId: this.postInfo.parentId,
                    sort: 0,
                    type: 0,
                };
                const { data } = await CategoryTemplateAPI.createCatalogue(params);
                if (data) {
                    this.$Toast({
                        type: 'success',
                        message: '创建成功',
                    });
                    this.$emit('refreshModel');
                    this.$nextTick(() => {
                        this.dialogVisible = false;
                    });
                }
            },
            handleCancel() {
                this.dialogVisible = false;
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.add-medical-prescription-modal_dialog--box {
    &-body {
        width: 100%;

        &-line {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 32px;
            margin-bottom: 16px;

            .label {
                height: 32px;
                font-size: 14px;
                line-height: 32px;
                color: $T2;
            }

            .catalogues-folder-popover {
                width: 254px;
                max-height: 264px;
                padding: 0;
                overflow-y: auto;

                .custom-node-wrapper.is-disabled {
                    color: $T3;
                }

                .abc-tree-node-content:hover {
                    background-color: var(--abc-color-cp-grey4);
                }
            }
        }
    }
}
</style>
