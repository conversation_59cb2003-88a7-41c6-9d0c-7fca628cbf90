<template>
    <div class="medical-prescription_table--box">
        <div class="medical-prescription_table--box-list abc-hospital-table">
            <div class="table-header">
                <div class="th" style="width: 82px; padding-left: 10px; text-align: left;">
                    类型
                </div>
                <div class="th" style="width: 252px; padding-left: 10px; text-align: left;">
                    医嘱名称
                </div>
                <div class="th" style="width: 132px; padding-left: 10px; text-align: left;">
                    单次剂量
                </div>
                <div class="th" style="width: 52px; padding-left: 10px; text-align: left;">
                    用法
                </div>
                <div class="th" style="width: 52px; padding-left: 10px; text-align: left;">
                    频率
                </div>
                <div class="th" style="width: 52px; padding-left: 10px; text-align: left;">
                    天数
                </div>
                <div class="th" style="width: 104px; padding-left: 10px; text-align: left;">
                    备注
                </div>
                <div class="th" style="width: 72px; padding-left: 10px; text-align: left;">
                    总量
                </div>
                <div class="th" style="width: 132px; padding-left: 10px; text-align: left;">
                    执行科室
                </div>
                <div class="th" style="flex: 1; padding-left: 10px; text-align: left;">
                    执行时间
                </div>
            </div>
            <div class="table-body" :class="{ 'table-body-empty': list.length === 0 }">
                <template v-if="list.length > 0">
                    <div
                        v-for="group in list"
                        :key="group.id"
                        class="group"
                    >
                        <div
                            v-for="item in group.advices"
                            :key="item.id"
                            class="tr"
                            style="width: 100%;"
                        >
                            <template>
                                <div class="td" style="width: 82px; padding-left: 10px; text-align: left;">
                                    {{ getType(item.type) || '' }}
                                </div>
                                <div class="td" style="width: 252px; padding-left: 10px; text-align: left;">
                                    <template v-if="item.adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_GRANULES || item.adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_TABLETS">
                                        {{ adviceName(item.adviceRule) }}
                                    </template>
                                    <template v-else>
                                        {{ getName(item.adviceRule) }}<span v-if="item.adviceRule.type === AdviceRuleType.WESTERN_MEDICINE" style="margin-left: 8px; font-size: 12px; color: #7a8794;">{{ item.adviceRule.westernPrimaryItemSpec || '' }}</span>
                                    </template>
                                </div>
                                <template v-if="item.adviceRule.type === AdviceRuleType.INSPECTION || item.adviceRule.type === AdviceRuleType.ASSAY">
                                    <div class="td" style="width: 288px; padding-left: 10px; text-align: center;">
                                        选择检查部位
                                    </div>
                                </template>
                                <template v-else-if="item.adviceRule.type === AdviceRuleType.TRANSFER_DEPARTMENT">
                                    <div class="td" style="width: 288px; padding-left: 10px; text-align: center;">
                                        选择转科科室
                                    </div>
                                </template>
                                <template v-else-if="item.adviceRule.type === AdviceRuleType.DISCHARGE_WITH_MEDICINE">
                                    <div class="td" style="width: 288px; padding-left: 10px; text-align: center;">
                                        选择出院原因
                                    </div>
                                </template>
                                <template v-else-if="item.adviceRule.type === AdviceRuleType.TRANSFER_WITH_MEDICINE">
                                    <div class="td" style="width: 288px; padding-left: 10px; text-align: center;">
                                        选择转院原因
                                    </div>
                                </template>
                                <template v-else-if="item.adviceRule.type === AdviceRuleType.NURSE_LEVEL">
                                    <div class="td" style="width: 288px; padding-left: 10px; text-align: center;">
                                        护理等级
                                    </div>
                                </template>
                                <template v-else-if="isMaterialsAdvice(item)">
                                    <div class="td" style="width: 288px; padding-left: 10px; text-align: center;"></div>
                                </template>
                                <template v-else>
                                    <div v-if="item.adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_GRANULES || item.adviceRule.type === AdviceRuleType.CHINESE_MEDICINE_TABLETS" class="td" style="width: 132px; padding-left: 10px; text-align: left;">
                                        {{ chineseUsageStr(item.adviceRule) }}
                                    </div>
                                    <div v-else class="td" style="width: 132px; padding-left: 10px; text-align: left;">
                                        {{ getSingleDosage(item) }}
                                    </div>
                                    <div class="td" style="width: 52px; padding-left: 10px; text-align: left;">
                                        {{ group.usage || '' }}
                                    </div>
                                    <div class="td" style="width: 52px; padding-left: 10px; text-align: left;">
                                        {{ group.freq || '' }}
                                    </div>
                                    <div class="td" style="width: 52px; padding-left: 10px; text-align: left;">
                                        {{ group.days ? `${group.days}天` : '' }}
                                    </div>
                                </template>
                                <div v-if="item.adviceRule.type === AdviceRuleType.NURSE_LEVEL" class="td" style="width: 104px; padding-left: 10px; text-align: left;">
                                    {{ getAcupointStr(item) }}
                                </div>
                                <div v-else class="td" style="width: 104px; padding-left: 10px; text-align: left;">
                                    {{ item.adviceRule.remark || '' }}
                                </div>
                                <div class="td" style="width: 72px; padding-left: 10px; text-align: left;">
                                    {{ getDosage(item, group) || '' }}
                                </div>
                                <div class="td" style="width: 132px; padding-left: 10px; text-align: left;">
                                    {{ getExecuteDepartment(item) || '' }}
                                </div>
                                <div class="td" style="flex: 1; padding-left: 10px; text-align: left; border-right: none;">
                                    {{ getTimeStr(item.adviceRule.freqInfo, group.freq) }}
                                </div>
                            </template>
                        </div>
                        <div
                            v-if="group.advices && group.advices.length > 1"
                            class="group-line"
                            :style="{ height: `${(group.advices.length - 1) * 40 }px` }"
                        ></div>
                    </div>
                </template>
                <abc-content-empty
                    v-else
                    top="10px"
                    :icon-size="20"
                    value="暂无数据"
                ></abc-content-empty>
            </div>
        </div>
    </div>
</template>

<script>
    import {
        AdviceDispenseType,
        AdviceRuleType,
        MedicalAdviceTypeEnum,
        MedicalAdviceTypeStr, ST_FREQ,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import { getExecuteStr } from '@/views-hospital/settings-hospital/utils/execute-time';
    import { mapGetters } from 'vuex';

    export default {
        name: 'MedicalPrescriptionTable',
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            list: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                MedicalAdviceTypeStr,
                AdviceRuleType,
            };
        },
        computed: {
            ...mapGetters(['enableLocalPharmacyList']),
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo || {};
            },
        },
        methods: {
            getType(item) {
                const obj = {
                    [MedicalAdviceTypeEnum.ONE_TIME]: '临时',
                    [MedicalAdviceTypeEnum.LONG_TIME]: '长期',
                    [MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE]: '出院带药',
                };
                return obj[item] || '';
            },
            getName(item) {
                return item?.name || '';
            },
            getAcupointStr(item) {
                const len = item.adviceRule?.treatmentSites?.length || 0;
                return len > 3 ? `${item.adviceRule.treatmentSites.slice(0,3).join(',')}等${len}穴位` :
                    item.adviceRule?.treatmentSites?.join(',');
            },
            getDosage(item, group) {
                const { dispenseType } = group;
                const {
                    dosageCount = '',
                    dosageUnit = '',
                    singleDispenseCount = '',
                    singleDispenseUnit = '',
                } = item?.adviceRule || {};
                if (dispenseType === AdviceDispenseType.SINGLE) {
                    return singleDispenseCount ? `${singleDispenseCount}${singleDispenseUnit}` : '';
                }
                return dosageCount ? `${dosageCount}${dosageUnit}` : '';
            },
            chineseUsageStr(item) {
                return `${item?.usage},${item?.dailyDosage},${item?.freq},${item?.singleDosageCount}`;
            },
            getSingleDosage(item) {
                const {
                    singleDosageCount = '', singleDosageUnit = '',
                } = item?.adviceRule || {};
                if (singleDosageCount) {
                    return `${singleDosageCount}${singleDosageUnit}`;
                }
                return '';
            },
            getTimeStr(freqInfo, code) {
                if (!freqInfo) {
                    return '';
                }
                if (freqInfo?.code === ST_FREQ) return '立即执行';
                let res = '';
                if (freqInfo.firstDayTimings?.length) {
                    res = `首日${freqInfo.firstDayFrequency}次，`;
                }
                return `${res}${getExecuteStr({
                    ...freqInfo,code,
                })}`;
            },
            adviceName(item) {
                const type = item.type === AdviceRuleType.CHINESE_MEDICINE_GRANULES ? '中药颗粒' : '中药饮片';
                return `${type} 共${item.dosageCount}剂`;
            },
            getExecuteDepartment(item) {
                const advice = item.adviceRule;
                const adviceRuleType = advice.type;
                if (adviceRuleType === AdviceRuleType.WESTERN_MEDICINE || adviceRuleType === AdviceRuleType.CHINESE_MEDICINE_TABLETS || adviceRuleType === AdviceRuleType.CHINESE_MEDICINE_GRANULES || this.isMaterialsAdvice(item)) {
                    const { pharmacyNo } = advice;
                    const pharmacyItem = this.enableLocalPharmacyList.find((it) => it.no === pharmacyNo);
                    return pharmacyItem && pharmacyItem.name ? pharmacyItem.name : '自备';
                }
                if (adviceRuleType === AdviceRuleType.INSPECTION || adviceRuleType === AdviceRuleType.ASSAY) {
                    const ownItem = advice.ruleItems.filter((it) => {
                        return !it.type;
                    });
                    return ownItem[0]?.extendInfo?.executeDepartments?.[0]?.name || '';
                }
                if (
                    adviceRuleType === AdviceRuleType.NURSE ||
                    adviceRuleType === AdviceRuleType.NURSE_LEVEL ||
                    adviceRuleType === AdviceRuleType.TREATMENT ||
                    adviceRuleType === AdviceRuleType.PHYSIOTHERAPY ||
                    adviceRuleType === AdviceRuleType.TRANSFER_DEPARTMENT ||
                    adviceRuleType === AdviceRuleType.DISCHARGE_WITH_MEDICINE ||
                    adviceRuleType === AdviceRuleType.TRANSFER_WITH_MEDICINE ||
                    adviceRuleType === AdviceRuleType.CONSULTATION ||
                    adviceRuleType === AdviceRuleType.POSTOPERATIVE
                ) {
                    return this.curPatientHospitalInfo.wardName || '';
                }
                // 手术医嘱的科室来源于手术单, 医嘱模板不保存手术单
                if (adviceRuleType === AdviceRuleType.SURGERY) {
                    return '';
                }
            },
            isMaterialsAdvice(item) {
                const materialsAdviceRuleTypes = [
                    AdviceRuleType.MEDICINE_MATERIAL,
                    AdviceRuleType.SELF_PRODUCT,
                    AdviceRuleType.HEALTH_MEDICINE,
                    AdviceRuleType.HEALTH_FOOD,
                    AdviceRuleType.OTHER_PRODUCT,
                ];
                return materialsAdviceRuleTypes.includes(item.adviceRule.type);
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/abc-common.scss";

.medical-prescription_table--box {
    width: 100%;

    &-list {
        .table-body {
            min-height: 40px !important;
        }

        .table-body-empty {
            min-height: 100px !important;

            .abc-content-empty-wrapper {
                ::v-deep .empty-icon {
                    transform: scale(0.5);
                }

                ::v-deep .empty-label {
                    margin-top: -8px;
                    font-size: 12px;
                }
            }
        }

        .group {
            position: relative;
            width: 100%;
            min-height: 40px;

            .group-line {
                position: absolute;
                top: 20px;
                left: 320px;
                display: block;
                width: 7px;
                content: '';
                border-top: 2px solid #8f8f8f;
                border-right: 2px solid #8f8f8f;
                border-bottom: 2px solid #8f8f8f;
            }

            .tr {
                display: flex;
                align-items: center;
                width: 100%;
                height: 40px;
                line-height: 40px;
                color: #8d9aa8;
                background: #f5f7fb !important;
                background-color: $T2;
                border-bottom: 1px solid $P6;
            }

            .td {
                color: #333333 !important;
                border-right: 1px dashed $P6;
            }

            .advice-content {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .gray {
                    >span {
                        display: inline;
                        margin: 0 2px;
                    }
                }

                >span:first-child {
                    display: inline-block !important;
                    cursor: pointer;

                    &:hover,
                    &.active {
                        color: $B1;

                        span.gray {
                            color: $B1;
                        }
                    }

                    @include ellipsis;
                }

                .ast-tag {
                    display: inline-block;
                    width: 56px;
                    margin-right: 20px;
                    line-height: 24px;
                    text-align: center;
                    background: $P4;
                    border-radius: var(--abc-border-radius-small);

                    &.green {
                        color: $G2;
                    }

                    &.red {
                        color: #ff0000;
                    }

                    &.blue {
                        color: #004c97;
                    }
                }
            }
        }
    }
}
</style>
