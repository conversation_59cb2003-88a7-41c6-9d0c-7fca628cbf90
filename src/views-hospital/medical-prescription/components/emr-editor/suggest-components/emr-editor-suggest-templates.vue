<template>
    <div class="emr-editor-suggest-templates">
        <div v-if="templateList.length" class="emr-editor__templates">
            <div
                v-for="templateIt in templateList"
                :key="templateIt.id"
                class="template-item"
                @click="handleNodeClick(templateIt)"
            >
                <abc-space>
                    <abc-image :src="templateImage" :height="16" :width="16"></abc-image>
                    <span>{{ templateIt.name }}</span>
                </abc-space>
                <div class="template-owner">
                    {{ CATALOGUE_FILE_OWNER_SHORT_TYPE_NAME_MAP[templateIt.permissions[0]?.ownerType] }}
                </div>
            </div>
        </div>
        <div v-else class="sidebar-no-data-wrapper">
            暂无数据
        </div>
        <div class="add-new">
            <abc-flex
                align="center"
                justify="center"
                :style="{
                    borderRight: `1px solid ${ $store.state.theme.style.P6}`
                }"
                @click="handleClickSaveTemplate"
            >
                <abc-space>
                    <abc-icon icon="save"></abc-icon>
                    <span>保存模板</span>
                </abc-space>
            </abc-flex>
            <abc-flex align="center" justify="center" @click="handleClickTemplateSetting">
                <abc-space>
                    <abc-icon icon="setting1"></abc-icon>
                    <span>模板设置</span>
                </abc-space>
            </abc-flex>
        </div>
        <emr-editor-template-settings-dialog
            v-if="showDialog"
            v-model="showDialog"
            :business-type="businessType"
            @update="initData"
        ></emr-editor-template-settings-dialog>
        <save-common-template-dialog
            v-if="showSaveCommonTemplateDialog"
            v-model="showSaveCommonTemplateDialog"
            :medical-id="medicalId"
            :medical-doc-id="medicalDocId"
            :template-setting="templateSetting"
            :default-name="medicalName"
            :emr-editor="emrEditor"
            :business-type="businessType"
            @confirm="initData"
        ></save-common-template-dialog>
    </div>
</template>

<script>
    import templateImage from '@/assets/images/hospital/medicinal/<EMAIL>';

    import EmrEditorTemplateSettingsDialog
        from '@/views-hospital/medical-prescription/components/emr-editor/template-settings/dialog.vue';

    import EmrAPI from 'api/hospital/emr/index.js';
    import {
        TemplateSceneTypeEnum,
        CATALOGUE_FILE_OWNER_TYPE,
        CATALOGUE_FILE_OWNER_SHORT_TYPE_NAME_MAP,
    } from 'utils/constants.js';

    import { mapGetters } from 'vuex';
    import CopyPaste from '@/views-hospital/medical-prescription/utils/copy-paste.js';
    import { MedicalDocumentBusinessType } from '@/views-hospital/nursing/common/constants.js';
    import {
        EmrOpenTemplateViewerEventKey, EmrSuggestTabType,
    } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants.js';
    import SaveCommonTemplateDialog
        from '@/views-hospital/medical-prescription/components/emr-editor/save-components/save-common-template-dialog';

    export default {
        name: 'EmrEditorSuggestTemplates',
        components: {
            EmrEditorTemplateSettingsDialog,
            SaveCommonTemplateDialog,
        },
        props: {
            templateId: {
                type: String,
                default: '',
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            departmentId: {
                type: String,
                default: '',
            },
            businessType: {
                type: Number,
                default: MedicalDocumentBusinessType.HOSPITAL,
            },
            businessId: {
                type: String,
                default: '',
            },
            medicalId: {
                type: String,
                default: '',
            },
            medicalDocId: {
                type: String,
                default: '',
            },
            medicalName: {
                type: String,
                default: '',
            },
            templateSetting: {
                type: Object,
                default: () => ({}),
            },
            emrEditor: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                templateImage,
                CATALOGUE_FILE_OWNER_SHORT_TYPE_NAME_MAP,
                CATALOGUE_FILE_OWNER_TYPE,
                currentOperationFolderNodeId: 1,
                templateTreeData: [],

                words: [],
                currentNodeId: null,
                curEditNodeId: null,
                isEditing: false,
                curNodeName: '',
                isShowViewer: false,

                templateList: [],
                templateListMap: new Map(),
                showDialog: false,
                showSaveCommonTemplateDialog: false,
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'userInfo']),
            sceneType() {
                return this.businessType === MedicalDocumentBusinessType.HOSPITAL ?
                    TemplateSceneTypeEnum.HOSPITAL_EMR_MEDICAL_DOC :
                    TemplateSceneTypeEnum.OUTPATIENT_EMR_MEDICAL_DOC;
            },
        },
        watch: {
            medicalId: {
                handler(val) {
                    if (this.templateListMap.has(val)) {
                        this.templateList = this.templateListMap.get(val);
                    } else {
                        this.initData();
                    }
                },
                immediate: true,
            },
        },
        methods: {
            async initData() {
                try {
                    if (!this.medicalId) return;
                    const { data } = await EmrAPI.fetchMedicalDocumentDetail(this.medicalId);
                    this.templateListMap.set(this.medicalId, data.emrTemplateViews);
                    this.templateList = this.templateListMap.get(this.medicalId);
                } catch (e) {
                    this.templateList = [];
                }
            },
            /**
             * @desc 由于共享页面的问题，文书列表中可能会切换不同的文书类型，不同的文书类型存在不同的模板，
             * 因此需要争对medicalId建立一个map映射模板列表，缓存模板列表
             * <AUTHOR>
             * @date 2023-09-06 15:23:24
             * @params
             * @return
             */
            handleNodeClick(node) {
                this.$abcEventBus.$emit(EmrOpenTemplateViewerEventKey, {
                    templateId: node.id,
                    type: EmrSuggestTabType.Template,
                });
                return false;
            },
            // https://stackoverflow.com/questions/51805395/navigator-clipboard-is-undefined
            async handleCopy() {
                const textToCopy = this.$refs.copyContent.textContent;
                try {
                    await CopyPaste.write(textToCopy);
                    this.$Toast({
                        type: 'success',
                        message: '已复制',
                    });
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: '复制失败',
                    });
                }
            },
            handleClose() {
                this.isShowViewer = false;
            },
            handleConfirm(content) {
                this.isShowViewer = false;
                this.$emit('confirm', content);
            },
            handleClickTemplateSetting() {
                this.showDialog = true;
            },
            handleClickSaveTemplate() {
                this.showSaveCommonTemplateDialog = true;
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.emr-editor-suggest-templates {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-top: 4px;

    .sidebar-no-data-wrapper {
        position: absolute;
        top: 28%;
        width: 100%;
        height: auto;
        font-size: 14px;
        line-height: 1;
        color: $T3;
        text-align: center;
    }

    .emr-editor__templates {
        padding-bottom: 44px;
        overflow-y: auto;

        .template-item {
            display: flex;
            align-items: center;
            padding: 12px 18px;
            cursor: pointer;

            .abc-space-item {
                display: inline-flex;
            }

            &:hover {
                background-color: #f0f3f6;
            }
        }
    }

    .template-owner {
        margin-left: auto;
        color: $T2;
    }

    .add-new {
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        width: 100%;
        height: 44px;
        color: #005ed9;
        cursor: pointer;
        background-color: #ffffff;
        border-top: 1px solid $P6;

        > * {
            width: 50%;
            height: 100%;
        }
    }
}
</style>
