<template>
    <div v-abc-loading="loading" class="emr-editor-view-template-suggest__content">
        <div class="view-template-header">
            <div class="view-template-op">
                <abc-space :size="8">
                    <abc-image :src="imgSrc" :width="24" :height="24"></abc-image>
                    <span style="font-size: 14px; font-weight: 700; line-height: 24px;">{{ name }}</span>
                </abc-space>
                <abc-tooltip placement="top" content="不同文书类型不可复制" :disabled="!disableMedicalDocReplace">
                    <abc-button
                        style="margin-left: auto;"
                        :disabled="!templateContent || disableMedicalDocReplace"
                        @click="handleUseTemplate"
                    >
                        {{ EmrSuggestTabType.Template === type ? '使用模板' : '复制替换' }}
                    </abc-button>
                </abc-tooltip>

                <abc-button type="blank" @click="handleClose">
                    取消
                </abc-button>
            </div>
            <abc-space
                v-if="EmrSuggestTabType.Template === type"
                direction="vertical"
                style="padding-left: 32px;"
                align="start"
                :size="4"
            >
                <abc-space>
                    <abc-icon :size="12" icon="folder" color="#7A8794"></abc-icon>
                    <p style="font-size: 12px; color: #7a8794;">
                        所属类别
                    </p>
                    <p style="font-size: 12px;">
                        {{ CATALOGUE_FILE_OWNER_TYPE_NAME_MAP[templatePermissions?.[0]?.ownerType] }}/{{ medicalTypeName }}
                    </p>
                </abc-space>
                <abc-space>
                    <abc-icon :size="12" icon="updatetime" color="#7A8794"></abc-icon>
                    <p style=" font-size: 12px; color: #7a8794;">
                        更新时间
                    </p>
                    <p style="font-size: 12px;">
                        {{ parseTime(lastModified, 'y-m-d h:i', true) }}
                    </p>
                </abc-space>
            </abc-space>
            <abc-space
                v-else
                direction="vertical"
                style="padding-left: 32px;"
                align="start"
                :size="4"
            >
                <abc-space>
                    <abc-icon :size="12" icon="avatar_pic" color="#7A8794"></abc-icon>
                    <p style="min-width: 48px; font-size: 12px; color: #7a8794;">
                        创建人
                    </p>
                    <p style="font-size: 12px;">
                        {{ createdName }}
                    </p>
                </abc-space>
                <abc-space>
                    <abc-icon :size="12" icon="time" color="#7A8794"></abc-icon>
                    <p style=" font-size: 12px; color: #7a8794;">
                        创建时间
                    </p>
                    <p style="font-size: 12px;">
                        {{ parseTime(created, 'y-m-d h:i', true) }}
                    </p>
                </abc-space>
            </abc-space>
        </div>
        <div ref="container" class="view-suggest__content">
            <emr-editor
                v-if="templateContent"
                ref="emrEditor"
                :style="{
                    zoom
                }"
                :value="templateContent"
                :page-size="templateSetting.pageSize"
                :page-size-reduce="templateSetting.pageSizeReduce"
                :editable="false"
                @initCareTableDataHandler="handleInitCareTableDataHandler"
            ></emr-editor>
        </div>
    </div>
</template>

<script>
    import docImage from 'assets/images/hospital/icon-emr-template.png';
    import EditorUse from '@/assets/images/hospital/medical-document-share.png';
    import { mergeCommonTemplate } from '@/views-hospital/medical-prescription/components/emr-editor/common/tools';
    import EmrAPI from 'api/hospital/emr';
    import {
        AbcEmrEditorService,
    } from '@/views-hospital/medical-prescription/components/emr-editor/AbcEmrEditorService.js';
    import {
        CATALOGUE_FILE_OWNER_TYPE_NAME_MAP,
    } from 'utils/constants.js';

    import { EmrSuggestTabType } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants.js';

    import { parseTime } from 'utils/index.js';

    export default {
        name: 'EmrEditorViewerSuggest',
        components: {
            EmrEditor: AbcEmrEditorService.loadEmrEditor,
        },
        props: {
            templateId: {
                type: String,
                default: '',
            },
            canUse: {
                type: Boolean,
                default: true,
            },
            businessId: String,
            businessType: Number,
            medicalDocId: String,
            medicalId: String,
            type: String,
            disableMedicalDocReplace: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                EmrSuggestTabType,
                CATALOGUE_FILE_OWNER_TYPE_NAME_MAP,
                templateContent: null,
                templateSetting: null,
                templatePermissions: [],
                lastModified: '',
                medicalTypeName: '',
                name: null,
                loading: true,
                createdName: '',
                created: '',
                zoom: 1,
            };
        },
        computed: {
            imgSrc() {
                return this.type === EmrSuggestTabType.Template ? docImage : EditorUse;
            },
        },
        created() {
            this.initData();
        },
        methods: {
            parseTime,
            async initData() {
                if (this.type === EmrSuggestTabType.Template) {
                    await this.fetchData();
                } else if (this.type === EmrSuggestTabType.Medical) {
                    await this.fetchMedicalDocDetail(this.medicalId, this.medicalDocId);
                }
            },
            async fetchData() {
                try {
                    if (!this.templateId) return;
                    const { data } = await EmrAPI.fetchMedicalTemplateDetail(this.templateId);
                    const {
                        content,
                        name,
                        permissions,
                        lastModified,
                        medicalTypeName,
                    } = data;

                    if (!content) {
                        return this.$Toast({
                            message: '模版已被移除',
                            type: 'error',
                        });
                    }
                    await mergeCommonTemplate(content.templateContent?.content);
                    this.templateContent = content.templateContent;
                    this.templateSetting = content.templateSetting;
                    this.medicalTypeName = medicalTypeName;
                    this.name = name;
                    this.templatePermissions = permissions;
                    this.lastModified = lastModified;
                    await this.$nextTick();
                    const { width: containerWidth } = this.$refs.container.getBoundingClientRect();
                    const { width: emrContainerWidth } = this.$refs.emrEditor.$el.getBoundingClientRect();
                    this.zoom = (containerWidth - 32) / emrContainerWidth;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            /**
             * @desc 获取文书详情
             * <AUTHOR>
             * @date 2023-09-03 14:24:19
             * @params
             * @return
             */
            async fetchMedicalDocDetail(medicalId, medicalDocId = undefined) {
                if (!medicalId) return;
                try {
                    const { data } = await EmrAPI.fetchMedicalDocDetail(this.businessType, this.businessId, medicalId, medicalDocId, 0);

                    if (data.medicalDocDetailViews.length) {

                        const curMedicalRecord = data.medicalDocDetailViews[0] ?? [];
                        const {
                            permissions, name, content, lastModified,medicalTypeName,createdName,created,
                        } = curMedicalRecord;
                        this.templateContent = content.templateContent;
                        this.templateSetting = content.templateSetting;
                        this.lastModified = lastModified;
                        this.medicalTypeName = medicalTypeName;
                        this.createdName = createdName;
                        this.created = created;

                        this.name = name;
                        this.templatePermissions = permissions;
                        await this.$nextTick();
                        const { width: containerWidth } = this.$refs.container.getBoundingClientRect();
                        const { width: emrContainerWidth } = this.$refs.emrEditor.$el.getBoundingClientRect();
                        this.zoom = (containerWidth - 32) / emrContainerWidth;
                    }
                } catch (e) {
                    console.log('获取文书详情错误', e);
                } finally {
                    this.loading = false;
                }
            },
            handleClose() {
                this.$emit('close');
            },
            handleUseTemplate() {
                this.$emit('confirm', {
                    type: this.type,
                    templateContent: this.templateContent,
                    templateSetting: this.templateSetting,
                });
            },

            //  fix: https://www.tapd.cn/tapd_fe/47644659/bug/detail/1147644659001084609
            async handleInitCareTableDataHandler(setCareTableData) {
                const list = await EmrAPI.fetchNursingRecordList(this.businessId);
                setCareTableData({
                    data: list,
                    options: {},
                });
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.emr-editor-view-template-suggest__content {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1993;
    display: flex;
    flex-direction: column;
    width: 50%;
    height: 100%;
    background-color: #ffffff;
    box-shadow: -8px 0 8px 0 #00000026;

    .view-template-header {
        height: 100px;
        padding: 12px 16px;
        border-bottom: 1px solid $P6;

        .view-template-op {
            display: flex;
            align-items: center;

            .abc-space-item {
                display: inline-flex;
            }
        }
    }

    .view-suggest__content {
        flex: 1;
        padding: 24px 35px 0;
        overflow-x: hidden;
        overflow-y: auto;
        overflow-y: overlay;
        background: #f3f5f7;

        .emr-editor-wrapper {
            margin-top: 0;
        }
    }

    .view-operation {
        position: absolute;
        top: 30px;
        right: 30px;
        z-index: 1;
        display: flex;
        width: 110px;
        padding: 8px 12px;
        border: 1px solid #e6eaee;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);

        .close-btn {
            color: $T2;
        }

        &_small {
            width: 62px;
        }
    }
}
</style>
