<template>
    <div class="emr-editor__exam-item-wrapper">
        <div class="inspect-item__header" :class="{ 'is-expand': isExpand }" @click="handleOpenExpand">
            <span class="ellipsis" :title="data.examinationName">{{ data.examinationName }}</span>
            <abc-space :size="4" style="margin-left: auto;" class="header__time">
                <span>{{ ExaminationStatusTextEnum[data.status] }}</span>
                <span>{{ data.reportTime ? parseTime(data.reportTime, 'm-d', true) : '' }}</span>
                <span>{{ data.reportTime ? parseTime(data.reportTime, 'h:i', true) : '' }}</span>
                <div>
                    <abc-icon v-if="isExpand" icon="dropdown_line_up" color="#CED0DA"></abc-icon>
                    <abc-icon v-else icon="dropdown_line" color="#CED0DA"></abc-icon>
                </div>
            </abc-space>
        </div>

        <div
            v-if="isExpand"
            v-abc-loading.small="loading"
            class="inspect-item__content"
        >
            <template v-if="showUnUpload && !loading">
                <div class="inspect__un-upload">
                    <div class="sidebar-no-data-wrapper">
                        检查结果暂未上传
                    </div>
                </div>
            </template>
            <template v-else>
                <inspect-detail
                    v-if="!loading"
                    :is-show-print="false"
                    is-show-copy-btn
                    :out-detail="data.examDetail"
                    :out-study="data.examDetail.study"
                    :sub-type="data.examDetail.subType"
                ></inspect-detail>
            </template>
        </div>
    </div>
</template>

<script>
    import { parseTime } from '@abc/utils-date';
    import CopyPaste from '@/views-hospital/medical-prescription/utils/copy-paste.js';
    import {
        EXAMINATION_STATUS, ExaminationStatusTextEnum,
    } from '@/views/examination/util/constants.js';
    import { INSPECT_EVENT_KEY } from '@/views-hospital/inspect-diagnosis/utils/constant';
    import InspectDetail from '@/views-hospital/inspect-diagnosis/components/inspect-assist/inspect-history/inspect-detail/index.vue';
    import { formatDiagnosisAdviceItems } from '@/views-hospital/inspect-diagnosis/utils';

    // 检查
    export default {
        name: 'ExamItem',
        components: {
            InspectDetail,
        },
        props: {
            data: {
                type: Object,
                default: () => ({
                    examDetail: {},
                    study: [],
                }),
            },
            disabledOperate: {
                type: Boolean,
                default: false,
            },
            loading: Boolean,
        },
        data() {
            return {
                ExaminationStatusTextEnum,
                curLabel: 0,
                isChecked: true,
                isExpand: false,
                historyDetailVis: false,
            };
        },
        computed: {
            examinationSheetReport() {
                return this.data?.examDetail?.examinationSheetReport || null;
            },
            showUnUpload() {
                return this.data?.examDetail?.status !== EXAMINATION_STATUS.CHECKED;
            },
            isLoading() {
                return !!this.data?.examDetail?.id;
            },
        },
        created() {
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.COPY_HISTORY_KEY, ({
                key, value,
            }) => {
                let v = value;
                if (key === 'diagnosisEntryItems') {
                    v = formatDiagnosisAdviceItems(v);
                }
                CopyPaste.write(`${v}`);
            },this);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            parseTime,
            handleOpenExpand() {
                if (!this.data.examDetail.id) {
                    this.$emit('fetch-detail');
                }
                this.isExpand = !this.isExpand;
            },
        },

    };
</script>
<style lang="scss">
.emr-editor__exam-item-wrapper {
    .inspect-item__header {
        display: flex;
        align-items: center;
        padding: 12px;
        line-height: 20px;
        color: #000000;
        cursor: pointer;
        border-bottom: 1px solid $P6;

        > span {
            max-width: 224px;
        }

        .header__time {
            font-size: 12px;
            color: $T2;
        }
    }

    .inspect-item__content {
        position: relative;
        min-height: 84px;
        padding: 12px;
        background: var(--abc-color-cp-grey2);
        border-bottom: 1px solid $P6;

        .inspect__un-upload {
            position: relative;
            height: 62px;

            .sidebar-no-data-wrapper {
                top: 50%;
                transform: translateY(-50%);
            }
        }

        .content__item {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .item__title {
            display: flex;
            align-items: center;

            .abc-button-text {
                margin-left: auto;
                font-size: 12px;
            }
        }

        .item__desc {
            margin-top: 6px;
            font-size: 12px;
            line-height: 16px;
            color: $T2;
        }
    }
}
</style>
