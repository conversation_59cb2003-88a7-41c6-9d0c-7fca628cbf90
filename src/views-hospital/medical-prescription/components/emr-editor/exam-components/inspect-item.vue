<template>
    <div class="emr-editor__inspect-item-wrapper">
        <div class="inspect-item__header" :class="{ 'is-expand': isExpand }" @click="handleOpenExpand">
            <span class="ellipsis" :title="data.examinationName">{{ data.examinationName }}</span>
            <abc-space :size="4" style="margin-left: auto;" class="header__time">
                <abc-text v-if="!showUnUpload && abnormalCount" theme="warning">
                    {{ abnormalCount }}项异常
                </abc-text>
                <span>{{ ExaminationStatusTextEnum[data.status] }}</span>
                <span>{{ data.reportTime ? parseTime(data.reportTime, 'm-d', true) : '' }}</span>
                <span>{{ data.reportTime ? parseTime(data.reportTime, 'h:i', true) : '' }}</span>
                <abc-icon v-if="isExpand" icon="dropdown_line_up" color="#CED0DA"></abc-icon>
                <abc-icon v-else icon="dropdown_line" color="#CED0DA"></abc-icon>
            </abc-space>
        </div>
        <div
            v-if="isExpand"
            v-abc-loading.small="loading"
            class="inspect-item__content"
            :style="{
                minHeight: '36px',
            }"
        >
            <template v-if="showUnUpload && !loading">
                <div class="inspect__un-upload">
                    <div class="sidebar-no-data-wrapper">
                        检验结果暂未上传
                    </div>
                </div>
            </template>
            <template v-else-if="!!itemsValue.length && !loading">
                <abc-flex
                    v-for="item in itemsValue"
                    :key="item.id"
                    align="center"
                    class="content__item"
                    :class="{ 'disabled': disabledOperate }"
                    justify="space-between"
                    @click="handleQuickInsert(item)"
                >
                    <span>
                        {{ item.name }}
                    </span>
                    <abc-flex align="center">
                        <nature-display :item="item"></nature-display>
                        <span>
                            {{ item.value ? `${calValue2(item)} ${item.unit ?? ''}` : '' }}
                        </span>
                    </abc-flex>
                </abc-flex>
            </template>
        </div>
    </div>
</template>

<script>
    import { parseTime } from '@abc/utils-date';
    import NatureDisplay from 'views/examination/components/nature-display.vue';
    import {
        calNature, calValue2,
    } from 'utils/format-examination.js';
    import { EXAMINATION_RESULT_ENUM } from '@abc/constants';
    import {
        ABNORMAL_FLAG,
        EXAMINATION_STATUS,
        ExaminationStatusTextEnum,
    } from '@/views/examination/util/constants.js';
    // 检验
    export default {
        name: 'InspectItem',
        components: {
            NatureDisplay,
        },
        props: {
            data: {
                type: Object,
                required: true,
                default: () => ({}),
            },
            disabledOperate: {
                type: Boolean,
                default: true,
            },
            loading: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                ExaminationStatusTextEnum,
                isExpand: false,
                itemsValue: [],
            };
        },
        computed: {
            showUnUpload() {
                return this.data?.examDetail?.status !== EXAMINATION_STATUS.CHECKED;
            },
            // 异常指标数量
            abnormalCount() {
                return this.itemsValue.filter((it) => calNature(it) !== EXAMINATION_RESULT_ENUM.NONE || it.abnormalFlag !== ABNORMAL_FLAG.normal).length;
            },
        },
        watch: {
            'data.examDetail.itemsValue': {
                handler(val) {
                    if (Array.isArray(val)) {
                        this.itemsValue = val.map((it) => ({
                            ...it, checked: true,
                        }));
                    }
                },
                deep: true,
                immediate: true,
            },
        },
        created() {
            this.handleOpenExpand();
        },
        methods: {
            calValue2,
            parseTime,
            handleOpenExpand() {
                if (!this.data.examDetail) {
                    this.$emit('fetch-detail');
                }
                this.isExpand = !this.isExpand;
            },
            handleQuickInsert(it) {
                if (this.disabledOperate) return;
                const value = `${it.name} ${it.value ? `${this.calValue2(it)} ${it.unit ?? ''}` : ''}`;
                this.$emit('quick-insert', value);
            },
        },
    };
</script>

<style lang="scss">
.emr-editor__inspect-item-wrapper {
    .inspect-item__header {
        display: flex;
        align-items: center;
        padding: 12px;
        line-height: 20px;
        color: #000000;
        cursor: pointer;
        border-bottom: 1px solid $P6;

        > span {
            max-width: 224px;
        }

        .header__time {
            font-size: 12px;
            color: $T2;
        }
    }

    .inspect-item__content {
        position: relative;
        background: var(--abc-color-cp-grey2);
        border-bottom: 1px solid $P6;

        .content__item {
            padding: 8px 12px;
            font-size: 13px;
            line-height: 20px;
            color: $T2;
            cursor: url("~assets/images/cursor-insert.svg"), pointer;

            &:hover {
                color: $T1;
                background: $P8;
            }

            &.disabled {
                cursor: unset;
            }
        }
    }

    .inspect__un-upload {
        position: relative;
        height: 62px;

        .sidebar-no-data-wrapper {
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .inspect__content {
        display: flex;
    }
}
</style>
