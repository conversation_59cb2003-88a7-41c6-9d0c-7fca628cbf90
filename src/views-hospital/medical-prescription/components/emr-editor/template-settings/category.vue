<template>
    <div class="emr-template-setting-catalog-wrapper">
        <div class="emr-template-setting__title">
            <abc-input
                v-model="keyword"
                :width="332"
                placeholder="搜索文书"
                @input="handleSearch"
            >
                <abc-search-icon slot="prepend"></abc-search-icon>
            </abc-input>
        </div>
        <abc-tree
            :indent="18"
            draggable
            :data.sync="renderTree"
            :selecteds="catalogSelectedId"
        >
            <template
                #default="{
                    node,
                }"
            >
                <div class="template-setting__customer-node" @click.stop="handleClickNode(node)">
                    <abc-space>
                        <div class="icon-wrapper">
                            <img
                                v-if="node.isFolder"
                                src="~assets/images/<EMAIL>"
                                alt="文书目录"
                            />
                            <img
                                v-else
                                src="~assets/images/<EMAIL>"
                                alt="文件"
                            />
                        </div>

                        <div class="node-content" :title="node.name">
                            {{ node.name }}
                        </div>

                        <div class="node-append" :title="node.append">
                            {{ node.append }}
                        </div>
                    </abc-space>
                </div>
            </template>
        </abc-tree>
    </div>
</template>

<script>
    import clone from 'utils/clone.js';
    export default {
        name: 'TemplateCategory',
        props: {
            catalogList: {
                type: Array,
                default: () => [],
            },
            selectedNode: Object,
        },
        data() {
            return {
                keyword: '',
                catalogSelectedId: '',
                renderTree: [],
            };
        },
        watch: {
            catalogList: {
                handler(val) {
                    this.setCatalogListKeyword(val);
                },
            },
            selectedNode: {
                handler(val) {
                    this.catalogSelectedId = [val.id];
                },
                immediate: true,
            },
        },
        methods: {
            handleClickNode(node) {
                this.$emit('change-node', node);
            },
            handleSearch() {
                this.setCatalogListKeyword(this.catalogList);
            },

            setCatalogListKeyword(catalogList) {
                const keywords = this.keyword.trim();
                if (keywords) {
                    this.renderTree = catalogList.map((group) => {
                        const children = group.children.filter((item) => {
                            return item.name.indexOf(keywords) > -1;
                        });
                        return {
                            ...group,
                            children,
                        };
                    }).filter((group) => {
                        return !!group.children.length;
                    });
                } else {
                    this.renderTree = clone(catalogList);
                }

            },
        },
    };
</script>

<style lang="scss">
@import "styles/theme.scss";
@import "styles/mixin.scss";

.emr-template-setting-catalog-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;

    .emr-template-setting__title {
        display: flex;
        justify-content: space-between;
        height: 56px;
        padding: 12px  16px;
        border-bottom: 1px solid $P6;
    }

    .abc-tree-wrapper {
        flex: 1;
        height: 0;
        overflow-y: auto;
        overflow-y: overlay;

        @include scrollBar;

        .template-setting__customer-node {
            display: flex;
            align-items: center;
            cursor: pointer;

            .icon-wrapper {
                width: 14px;
                margin-right: 0;

                img {
                    width: 14px;
                    height: 14px;
                    margin-right: 0;
                }
            }

            .node-content {
                flex: 1;
                color: $T1;
            }

            .node-append {
                color: $T2;
            }

            .node-handler {
                position: absolute;
                top: 50%;
                right: 6px;
                display: none;
                transform: translateY(-50%);
            }

            &:hover {
                .node-handler {
                    display: block;
                }
            }
        }
    }
}
</style>
