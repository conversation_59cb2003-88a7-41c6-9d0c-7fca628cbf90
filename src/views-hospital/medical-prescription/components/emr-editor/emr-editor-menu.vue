<template>
    <div v-if="editor" class="emr-editor-menu">
        <div class="emr-editor-menu_groups">
            <div v-for="group in menuGroups" :key="group.id" class="emr-editor-menu_group">
                <template v-for="menu in group.children">
                    <!--下拉选项-->
                    <abc-dropdown
                        v-if="menu.dropdown"
                        :key="menu.icon"
                        v-model="menu.show"
                        custom-class="emr-editor-menu_dropdown-wrapper"
                        @change="(dropdown) => dropdown.command(editor)"
                    >
                        <abc-tooltip
                            slot="reference"
                            :content="menu.tooltip"
                            placement="bottom"
                            :disabled="!menu.tooltip"
                            :open-delay="500"
                        >
                            <div
                                class="emr-editor-menu_item"
                                :class="{ 'emr-editor-menu_item-active': menu.active && menu.active(editor) }"
                                @click="menu.command && menu.command(editor)"
                            >
                                <div v-if="menu.prevIcon" class="emr-editor-menu_item-prepend">
                                    <abc-icon :size="14" :icon="menu.prevIcon"></abc-icon>
                                </div>

                                <div class="emr-editor-menu_item-content">
                                    <div class="emr-editor-menu_item-icon">
                                        <abc-icon
                                            v-if="menu.icon || menu.activeIcon"
                                            :size="14"
                                            :icon="menu.activeIcon ? menu.activeIcon(editor) : menu.icon"
                                        ></abc-icon>
                                        <span v-else-if="menu.activeLabel" class="emr-editor-menu_item-text"> {{ menu.activeLabel(editor) }} </span>
                                        <span v-else-if="menu.label" class="emr-editor-menu_item-text">{{
                                            menu.label
                                        }}</span>
                                    </div>
                                    <div class="emr-editor-menu_item-underline"></div>
                                </div>
                                <div class="emr-editor-menu_item-append">
                                    <abc-icon v-if="menu.dropdown" :size="14" icon="dropdown_line"></abc-icon>
                                </div>
                            </div>
                        </abc-tooltip>
                        <abc-dropdown-item
                            v-for="dropdown in menu.dropdown"
                            :key="dropdown.value"
                            :value="dropdown"
                            :label="dropdown.label"
                            class="emr-editor-menu_dropdown-item"
                        >
                            <label
                                id="__img__"
                                class="emr-editor-menu_dropdown-item-wrapper"
                                @click="(e) => {
                                    if(dropdown.type === 'File') {
                                        e.stopPropagation()
                                    }
                                }"
                            >
                                <input
                                    v-if="dropdown.type === 'File'"
                                    type="file"
                                    style="display: none;"
                                    accept="image/*"
                                    @change="e => handleFileChange(e, editor, menu, dropdown)"
                                />
                                <abc-popover
                                    v-if="dropdown.type === 'Table' && menu.show"
                                    placement="right-start"
                                    trigger="hover"
                                >

                                    <div slot="reference" style="padding: 6px 12px;">
                                        <abc-icon v-if="dropdown.icon" :size="14" :icon="dropdown.icon"></abc-icon>
                                        <abc-icon
                                            v-if="dropdown.active && dropdown.active(editor)"
                                            :size="14"
                                            icon="positive_1"
                                        ></abc-icon>
                                        <span v-if="dropdown.label" class="emr-editor-menu_dropdown-item-label">{{
                                            dropdown.label
                                        }}</span>
                                    </div>

                                    <div
                                        class="emr-editor-menu_dropdown-table-wrapper"
                                        @mousedown="dropdown.command && dropdown.command(editor, selectCol, selectRow)"
                                    >
                                        <div class="emr-editor-menu_dropdown-table-title">
                                            {{ selectCol }}×{{ selectRow }} 表格
                                        </div>
                                        <div
                                            class="emr-editor-menu_dropdown-table-content"
                                            @mousemove="handleMouseMove"
                                        >
                                            <div v-for="row in 6" :key="row" class="emr-editor-menu_dropdown-table-row">
                                                <div
                                                    v-for="col in 10"
                                                    :key="col"
                                                    class="emr-editor-menu_dropdown-table-col"
                                                    :class="{ 'emr-editor-menu_dropdown-table-col-active': selectCol >= col && selectRow >= row }"
                                                >

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </abc-popover>

                                <span v-else style="padding: 6px 12px;">
                                    <abc-icon v-if="dropdown.icon" :size="14" :icon="dropdown.icon"></abc-icon>
                                    <abc-icon
                                        v-if="dropdown.active && dropdown.active(editor)"
                                        :size="14"
                                        icon="positive_1"
                                    ></abc-icon>
                                    <span v-if="dropdown.label" class="emr-editor-menu_dropdown-item-label">{{
                                        dropdown.label
                                    }}</span>
                                </span>
                            </label>
                        </abc-dropdown-item>
                    </abc-dropdown>


                    <!--色值卡片-->
                    <div
                        v-else-if="menu.colorDropdown"
                        :key="menu.icon"
                        class="emr-editor-menu_item emr-editor-menu_item-split"
                        :class="{ 'emr-editor-menu_item-active': menu.active && menu.active(editor) }"
                        style="padding-bottom: 3px;"
                    >
                        <div v-if="menu.prevIcon" class="emr-editor-menu_item-prepend">
                            <abc-icon :size="14" :icon="menu.prevIcon"></abc-icon>
                        </div>
                        <div
                            class="emr-editor-menu_item-content"
                            @click="menu.command && menu.command(editor, menu.activeColor)"
                        >
                            <div class="emr-editor-menu_item-icon">
                                <abc-icon v-if="menu.icon" :size="12" :icon="menu.icon"></abc-icon>
                                <span v-else-if="menu.label" class="emr-editor-menu_item-text">{{ menu.label }}</span>
                            </div>
                            <div class="emr-editor-menu_item-underline" style="margin-top: -2px;">
                                <div
                                    class="emr-editor-menu_item-active-color"
                                    :style="{ background: menu.activeColor }"
                                ></div>
                            </div>
                        </div>
                        <abc-dropdown v-model="menu.dropdownVisible">
                            <div slot="reference" class="emr-editor-menu_item-append">
                                <abc-icon :size="14" icon="dropdown_line"></abc-icon>
                            </div>

                            <div class="emr-editor-menu_item-color-dropdown">
                                <div
                                    v-for="(sub, subIndex) in menu.colorDropdown.group"
                                    :key="subIndex"
                                    class="emr-editor-menu_item-color-dropdown-group"
                                >
                                    <div
                                        v-for="it in sub"
                                        :key="it.value"
                                        class="emr-editor-menu_item-color-dropdown-item"
                                        :style="{ background: it.value }"
                                        @click="handleClickDropdownColor(menu.colorDropdown.setColorCommand, it, editor, menu)"
                                    >
                                    </div>
                                </div>
                                <div class="emr-editor-menu_item-color-dropdown-button">
                                    <abc-button
                                        type="blank"
                                        size="small"
                                        style="width: 100%;"
                                        @click="handleClickDropdownColorDefaultButton(menu.colorDropdown.defaultButton.command, editor, menu)"
                                    >
                                        {{ menu.colorDropdown.defaultButton.label }}
                                    </abc-button>
                                </div>
                            </div>
                        </abc-dropdown>
                    </div>

                    <!--普通按钮-->
                    <abc-tooltip
                        v-else
                        :key="menu.icon"
                        placement="bottom"
                        :disabled="!menu.tooltip"
                        :content="menu.tooltip"
                        :open-delay="500"
                    >
                        <div
                            :key="menu.icon"
                            class="emr-editor-menu_item"
                            :class="{ 'emr-editor-menu_item-active': menu.active && menu.active(editor) }"
                            @click="menu.command && menu.command(editor)"
                        >
                            <div v-if="menu.prevIcon" class="emr-editor-menu_item-prepend">
                                <abc-icon :size="14" :icon="menu.prevIcon"></abc-icon>
                            </div>
                            <div class="emr-editor-menu_item-content">
                                <div class="emr-editor-menu_item-icon">
                                    <abc-icon v-if="menu.icon" :size="14" :icon="menu.icon"></abc-icon>
                                    <span v-else-if="menu.label" class="emr-editor-menu_item-text">{{ menu.label }}</span>
                                </div>
                                <div class="emr-editor-menu_item-underline"></div>
                            </div>
                        </div>
                    </abc-tooltip>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
    import {
        EditorDesignMenusGroup, EditorModeEnum, EditorUseMenusGroup,
    } from '@abc-emr-editor/constants';
    import Clone from 'utils/clone';

    export default {
        name: 'EmrEditorMenu',
        props: {
            editor: {
                type: Object,
                default: null,
            },
            mode: {
                type: String,
                default: EditorModeEnum.Use,
            },
        },
        data() {
            return {
                menuGroups: this.mode === EditorModeEnum.Design ? Clone(EditorDesignMenusGroup) : Clone(EditorUseMenusGroup),
                selectCol: 1,
                selectRow: 1,
                vm: null,
            };
        },
        mounted() {
            this.vm = this;
        },
        methods: {
            handleClickDropdownColor(command, item, editor, dropdown) {
                dropdown.dropdownVisible = false;
                command(editor, item.value);
                dropdown.activeColor = item.value;
            },
            handleClickDropdownColorDefaultButton(command, editor, dropdown) {
                dropdown.dropdownVisible = false;
                command(editor);
                dropdown.activeColor = '#000';
            },
            handleMouseMove(e) {
                const {
                    x, y,
                } = e.currentTarget.getBoundingClientRect();
                const offsetX = e.pageX - x;
                const offsetY = e.pageY - y;
                this.selectCol = Math.max(1, Math.ceil(offsetX / 16));
                this.selectRow = Math.max(1, Math.ceil(offsetY / 16));
            },
            handleFileChange(e, editor, menu, dropdown) {
                dropdown.onFileChange(this, editor, e, () => {
                    menu.show = false;
                });
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.emr-editor-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    background-color: #ffffff;
    border-bottom: 1px solid $P6;

    &_groups {
        display: flex;
    }

    &_group {
        position: relative;
        display: flex;

        &::after {
            position: absolute;
            top: 6px;
            right: 0;
            display: block;
            width: 1px;
            height: 14px;
            content: '';
            background: $P6;
        }

        &:last-child::after {
            display: none;
        }

        > div {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .abc-dropdown-wrapper {
        width: auto;
    }

    .reference {
        display: flex;
        align-items: center;
    }

    &_item {
        display: flex;
        align-items: center;
        height: 24px;
        padding: 0 6px;
        margin: 0 5px;
        cursor: pointer;

        &:hover {
            background: $P6;
            border-radius: var(--abc-border-radius-small);
        }

        &-prepend {
            margin-right: 4px;
        }

        &-text {
            font-size: 13px;
            white-space: nowrap;
        }

        &-append {
            color: $T3;
        }

        &-active {
            background: #eff3f6;
            border-radius: var(--abc-border-radius-small);
        }

        &-active-color {
            width: 12px;
            height: 2px;
            background: #000000;
            outline: 1px solid $P6;
        }

        &-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            white-space: nowrap;
        }

        &-icon {
            line-height: 1;
        }
    }
}

.emr-editor-menu_item-color-dropdown {
    width: 206px;

    .emr-editor-menu_item-color-dropdown-group {
        padding: 8px;

        &:not(:first-child) {
            border-top: 1px solid $P4;
        }

        .emr-editor-menu_item-color-dropdown-item {
            box-sizing: border-box;
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-left: 4px;
            cursor: pointer;
            border: 1px solid #00000014;

            &:nth-of-type(8n + 1) {
                margin-left: 0;
            }

            &:nth-of-type(n + 8) {
                margin-top: 4px;
            }

            &:hover {
                outline: 1px solid #000000;
            }
        }
    }

    .emr-editor-menu_item-color-dropdown-button {
        padding: 0 8px 8px;
    }
}

.emr-editor-menu_dropdown-wrapper {
    .abc-dropdown-item {
        display: flex;
        align-items: center;
        height: 32px;
    }
}

.emr-editor-menu_dropdown-item {
    padding: 0 !important;
}

.emr-editor-menu_dropdown-item-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;

    .abc-icon:nth-of-type(2) {
        margin-right: 8px;
        margin-left: 24px;
        color: $theme1;
    }
}

.emr-editor-menu_dropdown-table-wrapper {
    position: absolute;
    width: 184px;
    height: 134px;
    padding: 12px;
    margin-top: -1px;
    margin-left: 11px;
    font-size: 0;
    background: #ffffff;
    border: 1px solid #dadbe0;
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.15);

    .emr-editor-menu_dropdown-table-title {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 12px;
        color: $T2;
    }

    .emr-editor-menu_dropdown-table-row {
        margin-bottom: 2px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .emr-editor-menu_dropdown-table-col {
        box-sizing: border-box;
        display: inline-block;
        width: 14px;
        height: 14px;
        margin-right: 2px;
        border: 1px solid rgba(0, 0, 0, 0.08);

        &:last-child {
            margin-right: 0;
        }

        &-active {
            background: #9adeff;
        }
    }
}
</style>
