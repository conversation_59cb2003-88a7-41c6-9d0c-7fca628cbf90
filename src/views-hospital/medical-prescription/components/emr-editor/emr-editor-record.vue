<template>
    <div class="emr-editor-record-wrapper">
        <abc-cascader
            v-if="isShare"
            :width="100"
            :options="renderOptions"
            reference-mode="icon"
        >
        </abc-cascader>
        <abc-dropdown
            v-else
            :max-width="240"
        >
            <div slot="reference">
                <slot name="referenceEl">
                </slot>
            </div>

            <abc-dropdown-item
                v-for="item in history"
                :key="item.dataId"
                placement="left-bottom"
            >
                <abc-space size="middle" @click="handleClickDropdownItem(item)">
                    <span style="color: #7a8794;">{{ parseTime(item.created, 'y-m-d h:i', true) }}</span>
                    <span>{{ item.createdName }}</span>
                </abc-space>
            </abc-dropdown-item>
        </abc-dropdown>
    </div>
</template>

<script>
    import { formatDate } from '@abc/utils-date';
    import { parseTime } from '@/utils';
    export default {
        name: 'EmrEditorRecord',
        props: {
            isShare: {
                type: Boolean,
                default: false,
            },
            history: Array,
        },
        data() {
            return {
                renderOptions: [
                    {
                        label: '查房记录',
                        value: 1,
                        children: [
                            {
                                createdName: '张智霖',
                                created: new Date(),
                                name: '张智霖',
                                value: 2,
                            },
                        ],
                        customRender (item) {
                            return `
                                <div>
                                    <span style="color:#7A8794">${parseTime(item.created, 'y-m-d h:i', true)}</span>
                                    <span>${item.createdName}</span>
                                </div>
                            `;
                        },
                    },
                ],
            };
        },
        methods: {
            formatDate,
            parseTime,
            handleClickDropdownItem(item) {
                this.$emit('item-detail', item);
            },
        },
    };
</script>

