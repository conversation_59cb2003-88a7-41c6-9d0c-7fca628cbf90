<template>
    <div class="emr-paperwork-list-wrapper">
        <abc-flex
            v-if="isSharedPage"
            justify="space-between"
            align="center"
            class="emr-paperwork-list__title"
        >
            <abc-text theme="gray">
                文书列表
            </abc-text>
        </abc-flex>

        <abc-flex
            v-else
            align="center"
            class="emr-paperwork-list__title"
            style="width: 100%; padding: 8px 12px; border-bottom: none;"
            :gap="4"
        >
            <abc-text theme="gray" size="mini">
                创建时间：{{ parseTime(singleRecord.recordTime || new Date(), 'y-m-d h:i', true) }}
            </abc-text>
            <abc-icon
                v-if="singleRecord.id"
                class="emr-paperwork-list__btn"
                icon="s-edit-line"
                color="var(--abc-color-T2)"
                size="12"
                @click="handleEditCreateTime(singleRecord)"
            >
            </abc-icon>
        </abc-flex>


        <div v-if="isSharedPage" class="emr-paperwork-list__item-wrapper">
            <div
                v-for="item in emrTemplateRecordList"
                :key="item.id"
                class="emr-paperwork-list__item"
                :class="{ 'is-selected': curMedicalDocId === item.id }"
                @click="handleSelectMedicalDocItem(item)"
            >
                <p class="emr-paperwork-list__item-date">
                    <abc-space>
                        <abc-text>{{ parseTime(item.recordTime || new Date(), 'y-m-d h:i', true) }}</abc-text>
                        <abc-icon
                            v-if="item.id"
                            icon="s-edit-line"
                            size="small"
                            class="emr-paperwork-list__btn"
                            @click.stop="handleEditCreateTime(item)"
                        ></abc-icon>
                    </abc-space>
                </p>
                <div class="emr-paperwork-list__item-detail">
                    <span class="ellipsis">{{ item.name }}{{ item.name }}{{ item.name }}</span>
                    <span class="ellipsis" style=" min-width: 56px; margin-left: auto;">{{ item.createdName || userInfoName }}</span>
                </div>
            </div>
        </div>

        <abc-dialog
            v-if="isShowEditTimeDialog"
            v-model="isShowEditTimeDialog"
            title="修改创建时间"
            size="small"
            append-to-body
            :auto-focus="false"
        >
            <abc-form>
                <abc-form-item style="margin: 0;" label="创建时间">
                    <abc-date-time-picker
                        v-model="currentEditTime"
                        style="width: 100%;"
                        is-compact
                        :hour-start="0"
                        adaptive-width
                        :date-width="194"
                        :time-width="120"
                        :clearable="false"
                        :picker-options="pickerOptions"
                    ></abc-date-time-picker>
                </abc-form-item>
            </abc-form>
            <div slot="footer" class="dialog-footer">
                <abc-button :disabled="!currentEditTime" @click="handleConfirmEditTime">
                    确定
                </abc-button>
                <abc-button variant="ghost" @click="handleCancelEdit">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </div>
</template>

<script>
    import { parseTime } from 'utils/index.js';
    import { mapGetters } from 'vuex';
    import { formatDate } from '@abc/utils-date';

    export default {
        name: 'EmrPaperworkList',
        props: {
            emrTemplateRecordList: {
                type: Array,
                required: true,
            },
            curMedicalDocId: {
                type: String,
                default: '',
            },
            isSharedPage: {
                type: Boolean,
                default: false,
            },
            minDate: {
                type: Date,
                default: () => new Date(),
            },
        },
        data() {
            return {
                isShowEditTimeDialog: false,
                currentEditItem: null,
                currentEditTime: null,
            };
        },
        computed: {
            ...mapGetters(['userInfo']),
            userInfoName() {
                return this.userInfo?.name;
            },
            singleRecord() {
                return this.emrTemplateRecordList[0] || {};
            },
            pickerOptions() {
                return {
                    disabledDate: (date) => {
                        return formatDate(date, null) < formatDate(this.minDate, null) || date > new Date();
                    },
                };
            },
        },
        methods: {
            parseTime,
            handleSelectMedicalDocItem(item) {
                this.$emit('change-item', item);
            },
            handleEditCreateTime(item) {
                this.isShowEditTimeDialog = true;
                this.currentEditItem = item;
                this.currentEditTime = item.recordTime;
            },
            handleConfirmEditTime() {
                this.$emit('edit-time',{
                    id: this.currentEditItem.id,
                    recordTime: formatDate(this.currentEditTime, 'YYYY-MM-DD HH:mm:ss'),
                    next: () => {
                        this.isShowEditTimeDialog = false;
                    },
                });
            },
            handleCancelEdit() {
                this.isShowEditTimeDialog = false;
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/theme.scss';
@import "src/styles/mixin.scss";

.emr-paperwork-list-wrapper {
    width: 252px;

    .emr-paperwork-list__btn {
        cursor: pointer;
        visibility: hidden;
    }

    .emr-paperwork-list__title {
        width: 220px;
        padding: 4px 12px 4px 16px;
        white-space: nowrap;
        border-bottom: 1px solid $P6;

        &:hover {
            .emr-paperwork-list__btn {
                visibility: visible;
            }
        }
    }

    .emr-paperwork-list__item-wrapper {
        padding-top: 4px;
        padding-right: 12px;
        overflow-y: auto;
        overflow-y: overlay;

        @include scrollBar(8px);
    }

    .emr-paperwork-list__item {
        height: 48px;
        padding: 6px 16px;
        cursor: pointer;
        border-radius: 0 6px 6px 0;

        &:hover {
            background-color: $P6;

            .emr-paperwork-list__btn {
                visibility: visible;
            }
        }

        &.is-selected {
            background-color: #d4e7fd;

            .emr-paperwork-list__item-date,
            .emr-paperwork-list__item-detail {
                color: #005ed9;
            }

            .emr-paperwork-list__item-detail {
                font-weight: 500;
            }
        }
    }

    .emr-paperwork-list__item-date {
        font-size: 12px;
        line-height: 16px;
        color: $T2;
    }

    .emr-paperwork-list__item-detail {
        display: flex;
        line-height: 20px;
        color: $T1;
    }
}
</style>
