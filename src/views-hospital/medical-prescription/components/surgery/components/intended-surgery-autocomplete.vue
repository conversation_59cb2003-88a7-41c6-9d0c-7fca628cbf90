<template>
    <abc-autocomplete
        v-model="itemCache"
        :fetch-suggestions="queryIntendedSurgeryItemAsync"
        :show-suggestions="false"
        :async-fetch="true"
        :keyboard-event="['/']"
        focus-show
        :delay-time="10"
        size="large"
        :placeholder="isDisabled ? '' : '搜索手术操作名称或编码'"
        :disabled="isDisabled"
        class="intended-surgery-autocomplete"
        @enterEvent="selectIntendedSurgeryItem"
    >
        <template slot="suggestion-header">
            <abc-flex class="intended-surgery-item-title-wrapper">
                <abc-flex align="center" class="intended-surgery-item-title" style="flex: 1;">
                    操作名称
                </abc-flex>
                <abc-flex align="center" class="intended-surgery-item-title" style="width: 166px;">
                    编码
                </abc-flex>
            </abc-flex>
        </template>

        <template slot="suggestions" slot-scope="props">
            <dt
                class="intended-surgery-suggestion-item"
                @mousedown="selectIntendedSurgeryItem(props.suggestion)"
            >
                <abc-flex align="center" class="intended-surgery-item-title" style="flex: 1;">
                    {{ props.suggestion.name }}
                </abc-flex>
                <abc-flex align="center" class="intended-surgery-item-title" style="width: 166px;">
                    {{ props.suggestion.code }}
                </abc-flex>
            </dt>
        </template>

        <div slot="prepend" class="intended-surgery-search-icon">
            <slot name="icon">
                <i class="iconfont cis-icon-plus"></i>
            </slot>
        </div>
    </abc-autocomplete>
</template>

<script>
    import SocialApi from 'api/social';
    import { mapGetters } from 'vuex';

    export default {
        name: 'IntendedSurgeryAutocomplete',
        props: {
            isDisabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                itemCache: '', // 拟施手术搜索值
            };
        },
        computed: {
            ...mapGetters(['clinicBasicConfig']),
        },
        methods: {
            async queryIntendedSurgeryItemAsync(queryString, callback) {
                queryString = queryString.trim();

                if (!queryString) {
                    callback([]);
                    return false;
                }

                try {
                    const params = {
                        'cis-his-type': this.clinicBasicConfig.hisType || '',
                        keyword: queryString,
                    };
                    const { data } = await SocialApi.searchOperation(params);
                    if (!data) {
                        callback([]);
                        return false;
                    }

                    const operationInfos = data.operationInfos || [];

                    callback(operationInfos);
                } catch (e) {
                    console.error('查询手术操作失败\n', e);
                    callback([]);
                }
            },
            async selectIntendedSurgeryItem(selected) {
                if (!selected) return false;

                const params = {
                    shebaoDictOperationList: [{
                        code: selected.code,
                        name: selected.name,
                    }],
                };
                const { data } = await SocialApi.queryOperation(params);
                const operationInfos = data?.operationInfos || [];
                const res = operationInfos[0];

                if (!res) return false;

                this.$emit('selected', res);
                this.itemCache = '';
            },
        },
    };
</script>

<style lang="scss">
.intended-surgery-autocomplete {
    input::placeholder {
        font-size: 14px !important;
    }
}

.intended-surgery-item-title-wrapper {
    height: 24px;
    font-size: 12px;
    line-height: 24px;
    color: #626d77;
    background-color: var(--abc-color-P5);
    border-bottom: 1px solid var(--abc-color-P3);
}

.intended-surgery-item-title {
    padding: 0 12px;
}

.intended-surgery-suggestion-item {
    display: flex;
    align-items: center;
    min-height: 36px;
    overflow: hidden;
    white-space: nowrap;
    cursor: pointer;
    user-select: none;
    background-color: #ffffff;
    border-bottom: 1px solid var(--abc-color-P8);
    outline: 0;

    &:hover {
        background-color: var(--abc-color-P4);
    }
}

.intended-surgery-search-icon {
    i::before {
        color: var(--abc-color-P1);
    }
}
</style>
