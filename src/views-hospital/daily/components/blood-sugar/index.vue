<template>
    <div class="hospital-daily__blood-sugar">
        <filter-bar
            :date-filter.sync="dateFilter"
            :disabled="isNotChangeData"
            :enable-time-filter="false"
            :patient-id.sync="patientId"
            :patient-options="effectivePatientList"
            :save-loading="saveLoading"
            @date-change="handleDateChange"
            @save-btn-click="handleSave"
            @patient-change="handlePatientChange"
            @export-btn-click="handleExport"
        >
            <template #setting>
                <abc-dropdown placement="bottom-end" style="margin-left: 8px;">
                    <abc-button
                        slot="reference"
                        variant="ghost"
                        icon="setting1"
                    >
                    </abc-button>
                    <abc-dropdown-item>
                        <div @click="handleOpenCustomHeaderDialog">
                            <span>设置展示字段</span>
                        </div>
                    </abc-dropdown-item>
                    <abc-dropdown-item>
                        <div @click="handleClickSignSetting">
                            <span>设置签字行</span>
                        </div>
                    </abc-dropdown-item>
                </abc-dropdown>
            </template>
        </filter-bar>

        <div class="hospital-daily__blood-sugar-table">
            <abc-table-fixed2
                ref="tableFixed2Ref"
                style="margin-top: 16px;"
                :loading="loading"
                :data="tableListByConfig"
                :class="{ 'blood-sugar-table': !loading }"
                :max-height="tableHeight"
                :min-height="tableHeight"
                :header="tableRenderHeader"
                :cell-click-handler="handleCellClick"
                :empty-opt="{ label: '暂无数据' }"
            >
            </abc-table-fixed2>
        </div>

        <blood-sugar-trend-dialog
            v-if="bloodSugarTrendDialogVisible"
            v-model="bloodSugarTrendDialogVisible"
            :patient-order-id="patientOrderId"
        ></blood-sugar-trend-dialog>
    </div>
</template>

<script>
    import FilterBar from '@/views-hospital/daily/components/filter-bar/index.vue';
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone.js';
    import {
        parseTime,
    } from '@abc/utils-date';
    import NurseTherapyAPI from '@/api/nurse/nurse-therapy.js';
    import { resolveHeader } from 'views/statistics/utils.js';
    import BloodSugarTrendDialog from './dialog/blood-sugar-trend-dialog.vue';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { calcTableHeight } from '@/views-hospital/daily/utils/index.js';
    import ClinicAPI from 'api/clinic';
    import { mapGetters } from 'vuex';
    import BloodSignSettingDialog
        from '@/views-hospital/nursing/components/nursing-daily-blood-sugar/components/blood-sign-setting-dialog';
    import propertyAPI from 'api/property';
    import { BizCustomHeader } from '@/components-composite/biz-custom-header';
    import TableHeaderAPI from 'views/statistics/core/api/table-header';

    export default {
        components: {
            FilterBar,
            BloodSugarTrendDialog,
        },
        props: {
            defaultPatientId: {
                type: String,
                default: '',
            },
            defaultPatientOrderId: {
                type: String,
                default: '',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                loading: true,
                bloodSugarTrendDialogVisible: false,
                tableHeader: [],
                tableData: [],
                // 原始数据
                originalTableData: [],
                dateFilter: parseTime(new Date, 'y-m-d', true),
                patientId: this.defaultPatientId,
                patientOrderId: this.defaultPatientOrderId,
                params: {
                    measuringTime: '',
                    patientOrderIds: [],
                },
                patientList: [],

                tableHeight: 500,
                saveLoading: false,
                employeeList: [],
                config: {},

                curEditPatientOrderId: '',
                curEditProp: '',
                curEditType: -1,
            };
        },
        computed: {
            ...mapGetters(['userInfo']),
            userId() {
                return this.userInfo?.id;
            },
            isShowSignLine() {
                //签字行 0:显示签字行 1:不显示
                return this.config?.signLine === 0;
            },
            isNotChangeData() {
                return isEqual(this.tableData,this.originalTableData);
            },
            tableListByConfig() {
                //signWay: 0:显示签字行 1:不显示
                const { signLine } = this.config;
                //type: 0 血糖(name)  1 签名(select)
                return signLine === 1 ? this.tableData.filter((item) => item.type !== 1) : this.tableData;
            },
            tableRenderHeader() {
                return resolveHeader(this.tableHeader, this.renderTypeList);
            },
            effectivePatientList() {
                const date = new Date(this.params.measuringTime);
                const after = new Date(date.getTime() + 24 * 60 * 60 * 1000);
                return this.patientList.filter((item) => ((new Date(item.inpatientTime) <= after) && !item.dischargeTime) || item.dischargeTime && new Date(item.dischargeTime) >= date);
            },
            renderTypeList() {
                const {
                    disabled,curEditPatientOrderId,curEditProp,curEditType,
                } = this;
                return {
                    inputRender: (h, row, col) => {
                        const { type } = row; //type: 0 血糖(name)  1 签名(select)
                        const displayValue = row[col.prop];
                        if (type === 0) {
                            if (row.patientOrderId === curEditPatientOrderId && col.prop === curEditProp && row.type === curEditType) {
                                const inputConfig = {
                                    min: null, // 最少输入多少位，type 为 number/money 时生效
                                    max: 200, // 最多输入多少位，type 为 number/money 时生效
                                    formatLength: 1, // 支持小数点后几位，type 为 number/money 时生效
                                    supportZero: false, // 是否支持零，type 为 number/money 时生效
                                    supportFraction: false, // 是否支持分数，type 为 number/money 时生效
                                    supportNegative: false, // 是否支持负数，type 为 number/money 时生效
                                };
                                // 血酮支持 2 位小数
                                if (col.prop === 'bloodSugarBloodKetone') {
                                    inputConfig.formatLength = 2;
                                }
                                return (<abc-input value={displayValue}
                                            type="number"
                                            max-length={6}
                                            disabled={disabled}
                                            config={inputConfig}
                                            onChange={(val) => this.handleChange(val, row, col)}>
                                </abc-input>);
                            }
                            return <abc-flex align="center" class="cell edit-cell">{displayValue}</abc-flex>;
                        }
                        if (type === 1) {
                            if (row.patientOrderId === curEditPatientOrderId && col.prop === curEditProp && row.type === curEditType) {
                                return (
                                    <abc-select
                                        value={displayValue}
                                        adaptive-width
                                        disabled={disabled}
                                        clearable
                                        no-icon
                                        size="medium"
                                        onChange={(val) => this.handleSelectChange(val, row, col)}>
                                        {
                                            this.employeeList.map((item) => {
                                                return <abc-option label={item.name} value={item.id}></abc-option>;
                                            })
                                        }
                                    </abc-select>
                                );
                            }
                            return <abc-flex align="center" class="cell edit-cell">{this.formatEmployeeName(displayValue)}</abc-flex>;
                        }
                    },
                    patientRender: (h, row) => {
                        const { patientOrderId = '' } = row;
                        const patientName = this.patientList.find((item) => item.id === patientOrderId)?.name;
                        const patientBedNo = this.patientList.find((item) => item.id === patientOrderId)?.bedNo;

                        return (
                            <abc-flex class="cell" style="height:100%;padding:0" align="center">
                                <abc-flex style="border-right: 1px solid #e6eaee;height:100%;width:55%;cursor:pointer;color:#005eD9;padding:0 6px" vertical="vertical" justify="center">
                                     <span>{patientBedNo >= 10 ? patientBedNo : `0${patientBedNo}`}床</span>
                                    <span>{patientName}</span>
                                </abc-flex>
                                <abc-flex vertical="vertical" align="center" style="height:100%;flex:1;fontWeight:bold">
                                    <abc-text theme="gray" style="flex:1;display:flex;align-items:center">血糖值<br/>mmol/L</abc-text>
                                    {this.isShowSignLine ? (<abc-flex style="border-top: 1px solid #e6eaee;flex:1;width:100%;color:#7a8794;" align="center" justify="center">签字</abc-flex>
                                    ) : null}
                                </abc-flex>
                            </abc-flex>
                        );
                    },
                };
            },
        },
        async created() {
            this.params.measuringTime = `${this.dateFilter} 00:00`;

            // 支持单个患者试图
            if (!this.defaultPatientId) {
                await this.getPatientList();
            } else {
                this.params.patientOrderIds = [this.defaultPatientOrderId];
            }
            await this.fetchClinicEmployeeList();
            await this.fetchConfig();

            this.getTableData();
        },
        mounted() {
            this.tableHeight = calcTableHeight(this.$refs.tableFixed2Ref.$el, 31);
        },
        methods: {
            handleCellClick(row, col) {
                if (col.prop !== 'patientName') {
                    this.curEditPatientOrderId = row.patientOrderId;
                    this.curEditType = row.type;
                    this.curEditProp = col.prop;
                    return;
                }
                this.bloodSugarTrendDialogVisible = true;
                this.patientOrderId = row.patientOrderId;
            },
            async getPatientList() {
                try {
                    const res = await PatientOrderAPI.getInHospitalPatientList({
                        limit: 200,
                        departmentId: '',
                        sceneType: 6,
                        wardId: this.$route.params.wardId,
                    });
                    const patientList = res?.data?.rows?.filter((item) => item.bedNo)?.sort((a, b) => (+a.bedNo) - (+b.bedNo))?.map((it) => {
                        return {
                            id: it.id,
                            name: it?.patient?.name,
                            bedNo: it.bedNo,
                            dischargeTime: it?.dischargeTime,
                            inpatientTime: it.inpatientTime,
                        };
                    });
                    patientList.forEach((item) => {
                        const isExist = this.patientList.find((it) => it.id === item.id);
                        if (!isExist) {
                            this.patientList.push({
                                ...item,
                            });
                        }
                    });
                    this.params.patientOrderIds = this.patientList.map((item) => item.id);
                } catch (err) {
                    console.log(err);
                }

            },
            async handleExport() {
                const params = {
                    patientOrderIds: this.params.patientOrderIds,
                    measuringTime: this.params.measuringTime,
                };
                const res = await NurseTherapyAPI.bloodSugarExport(params);
                if (res) {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
                    if ('download' in document.createElement('a')) {
                        const link = document.createElement('a');
                        link.download = `血糖单_${this.params.measuringTime}.xls`;
                        link.style.display = 'none';
                        link.href = URL.createObjectURL(blob);
                        document.body.appendChild(link);
                        link.click();
                        URL.revokeObjectURL(link.href);
                        document.body.removeChild(link);
                    } else {
                        navigator.msSaveBlob(blob);
                    }
                }
            },
            handlePatientChange() {
                if (this.patientId) {
                    this.params.patientOrderIds = [this.patientId];
                    this.params.patientOrderId = this.patientId;
                } else {
                    this.params.patientOrderIds = this.patientList.map((item) => item.id);
                    this.params.patientOrderId = '';
                }
                this.getTableData();
            },
            handleChange(val, row, col) {
                this.tableData?.forEach((item) => {
                    if ((item.patientOrderId === row.patientOrderId) && item.type === row.type) {
                        let fixedCount = 1;
                        if (col.prop === 'bloodSugarBloodKetone') {
                            fixedCount = 2;
                        }
                        this.$set(item, [col.prop], val && typeof +val === 'number' ? parseFloat(val || 0).toFixed(fixedCount) : val);
                        this.handleSelectChange(this.userId,{
                            ...row,type: 1,
                        },col,'input');
                    }
                });
            },
            handleSelectChange(val, row, col,way) {
                this.tableData?.forEach((item) => {
                    if ((item.patientOrderId === row.patientOrderId) && item.type === row.type) {
                        //如果血糖值输入导致的触发签名select，且输入框有值，不进行赋值
                        if (way === 'input' && item[col.prop]) return;
                        this.$set(item, [col.prop], val);
                    }
                });
            },
            handleDateChange() {
                this.params.measuringTime = `${this.dateFilter} 00:00`;
                this.getTableData();
            },
            getDiffDataForm(newData = [], oldData = []) {
                const arr = [];
                newData?.forEach((item, index) => {
                    if (!isEqual(item, oldData[index])) {
                        arr.push({
                            ...item,
                        });
                    }
                });

                const cacheArr = clone(arr);
                cacheArr.forEach((item) => {
                    const flag = `${item.patientOrderId}-${item.measuringTime}`;
                    const { type } = item;
                    const findItem = arr.find((it) => `${it.patientOrderId}-${it.measuringTime}` === flag && it.type === (1 - type));
                    if (!findItem) {
                        const findNewItem = newData.find((it) => `${it.patientOrderId}-${it.measuringTime}` === flag && it.type === (1 - type));
                        if (findNewItem) {
                            arr.push({
                                ...findNewItem,
                            });
                        }
                    }
                });

                return arr;
            },
            async handleSave() {
                const originalTableData = [
                    {
                        bloodSugarAfterNoon_afterDinnerOneHourBloodSugar: '',
                        bloodSugarAfterNoon_afterDinnerTwoHourBloodSugar: '',
                        bloodSugarAfterNoon_AfterNoonAfterMedicineBloodSugar: '',
                        id: '',
                        measuringTime: '',
                        bloodSugarAfterNoon_beforeDinnerBloodSugar: '',
                        bloodSugarAm_afterBreakfastOneHourBloodSugar: '',
                        bloodSugarAm_afterBreakfastTwoHourBloodSugar: '',
                        bloodSugarAm_afterMedicineBloodSugar: '',
                        bloodSugarAm_beforeBreakfastBloodSugar: '',
                        bloodSugarMorn_wakeUpBloodSugar: '',
                        bloodSugarNight_beforeSleepBloodSugar: '',
                        bloodSugarNight_sleepingBloodSugar: '',
                        bloodSugarNoon_afterLunchOneHourBloodSugar: '',
                        bloodSugarNoon_afterLunchTwoHourBloodSugar: '',
                        bloodSugarNoon_beforeLunchBloodSugar: '',
                        bloodSugarNoon_noonAfterMedicineBloodSugar: '',
                        bloodSugarRandomBloodSugar: '',
                        bloodSugarBloodKetone: '',
                    },
                ];
                this.saveLoading = true;
                const originData = this.originalTableData?.length ? this.originalTableData : originalTableData;
                const params = this.getDiffDataForm(this.tableData, originData);
                try {
                    const res = await NurseTherapyAPI.createBloodSugarRecord({
                        nurseBloodSugar: params,
                    });
                    if (res) {
                        this.$Toast({
                            message: '保存成功',
                            type: 'success',
                        });
                        this.getTableData();
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.saveLoading = false;
                }
            },
            handleOpenCustomHeaderDialog() {
                new BizCustomHeader({
                    value: true,
                    tableKey: 'nurse.daily.bloodSugar',
                    titleName: '血糖单',
                    mode: 'draggle',
                    showFixed: false,
                    finishFunc: this.getTableData,
                    tableHeaderApi: TableHeaderAPI,
                }).generateDialog({ parent: this });
            },
            handleClickSignSetting() {
                new BloodSignSettingDialog({
                    config: {
                        updateSuccessHandler: this.fetchConfig,
                        isHiddenPrint: true,
                    },
                }).generateDialogAsync({ parent: this });
            },
            async getTableData() {
                try {
                    this.loading = true;
                    const res = await NurseTherapyAPI.getBloodSugarRecordByTime({
                        ...this.params,
                    });
                    if (res) {
                        this.tableData = this.initTableData(res?.data?.result);
                        this.originalTableData = clone(this.tableData);
                        this.tableHeader = res?.data?.tableHeaderEmployeeItemsByTableKey || [];
                        // 单人试图不需要患者头
                        !this.defaultPatientId && this.tableHeader.splice(0, 0, {
                            label: '患者',
                            prop: 'patientName',
                            renderType: 'patientRender',
                            width: 170,
                            groupBy: 'patientOrderId',
                        });
                    }
                } catch (err) {
                    console.log(err);
                } finally {
                    this.loading = false;
                }

            },
            // 进入页面的时候，根据患者数量，初始化表格数据条数
            initTableData(originTableData = []) {
                let newTableData = [];
                const arr = [];
                const tableItem = {
                    bloodSugarAfterNoon_afterDinnerOneHourBloodSugar: '',
                    bloodSugarAfterNoon_afterDinnerTwoHourBloodSugar: '',
                    bloodSugarAfterNoon_AfterNoonAfterMedicineBloodSugar: '',
                    id: '',
                    measuringTime: this.params.measuringTime,
                    bloodSugarAfterNoon_beforeDinnerBloodSugar: '',
                    bloodSugarAm_afterBreakfastOneHourBloodSugar: '',
                    bloodSugarAm_afterBreakfastTwoHourBloodSugar: '',
                    bloodSugarAm_afterMedicineBloodSugar: '',
                    bloodSugarAm_beforeBreakfastBloodSugar: '',
                    bloodSugarMorn_wakeUpBloodSugar: '',
                    bloodSugarNight_beforeSleepBloodSugar: '',
                    bloodSugarNight_sleepingBloodSugar: '',
                    bloodSugarNoon_afterLunchOneHourBloodSugar: '',
                    bloodSugarNoon_afterLunchTwoHourBloodSugar: '',
                    bloodSugarNoon_beforeLunchBloodSugar: '',
                    bloodSugarNoon_noonAfterMedicineBloodSugar: '',
                    bloodSugarRandomBloodSugar: '',
                };
                if (this.patientId) {
                    newTableData = originTableData?.length && originTableData || [{
                        ...tableItem,
                        patientOrderId: this.defaultPatientOrderId || this.patientId,
                        patientId: '',
                    }];

                } else {
                    this.effectivePatientList?.forEach((patient) => {
                        //type: 0 血糖(name)  1 签名(select)
                        arr.push({
                                     ...tableItem,
                                     patientOrderId: patient.id,
                                     patientId: '',
                                     type: 0,
                                 },
                                 {
                                     ...tableItem,
                                     patientOrderId: patient.id,
                                     patientId: '',
                                     type: 1,
                                 },
                        );
                    });

                    newTableData = arr;

                    if (originTableData?.length) {
                        originTableData.forEach((item) => {
                            arr.forEach((newItem, index) => {
                                if (item.patientOrderId === newItem.patientOrderId && item.type === newItem.type) {
                                    newTableData[index] = item;
                                }
                            });
                        });
                    }
                }

                return newTableData;
            },

            async fetchConfig() {
                try {
                    const { data } = await propertyAPI.getV3('clinicBasic.daily.bloodSugar', 'clinic') || {};
                    this.config = data;
                } catch (e) {
                    console.log(e);
                }
            },

            async fetchClinicEmployeeList() {
                try {
                    const { data } = await ClinicAPI.fetchClinicEmployeeList();
                    this.employeeList = data.rows || [];
                } catch (error) {
                    console.error(error);
                }
            },

            formatEmployeeName(val) {
                const findItem = this.employeeList.find((item) => item.id === val);
                return findItem?.name || '';
            },
        },
    };
</script>

<style lang="scss">
@import '../../_index.scss';

.hospital-daily__blood-sugar {
    height: 100%;
    max-height: 100%;
    padding: 20px;
}

.hospital-daily__blood-sugar-table {
    .abc-fixed-table.blood-sugar-table {
        border-right: 1px solid $P6;
        border-left: 1px solid $P6;
        border-radius: var(--abc-border-radius-small);

        .abc-table__header-wrapper {
            table th {
                padding: 6px 8px;
                font-weight: bold;
            }
        }

        .is-disabled .abc-input__inner {
            background-color: #ffffff !important;
        }

        .abc-table__body-wrapper,
        .abc-table__footer-wrapper {
            tbody td {
                height: 56px !important;
                border-left: none;

                .abc-select-wrapper,
                .abc-input-wrapper {
                    width: 100%;

                    .abc-input__inner {
                        height: 56px;
                        padding-left: 10px;
                        border: 0;
                        border-radius: 0;

                        &:focus {
                            border: 1px solid $theme1;
                        }
                    }
                }
            }
        }

        tr.is-hover {
            background: #ffffff;
        }
    }

    .edit-cell {
        height: 100%;
        line-height: 40px;
        cursor: pointer;
    }
}
</style>
