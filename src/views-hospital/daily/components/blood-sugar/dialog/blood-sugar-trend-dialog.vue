<template>
    <abc-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="血糖趋势"
        :auto-focus="false"
        append-to-body
        content-styles="width: 820px;padding: 0 24px; height: 560px"
        class="hospital-daily__blood-sugar-dialog"
    >
        <div class="search-bar">
            <abc-date-picker
                v-model="dateRange"
                placeholder="日期范围"
                clearable
                width="230"
                type="daterange"
                @change="handleDateChange"
            >
            </abc-date-picker>
        </div>
        <div class="content">
            <abc-line-chart
                v-if="bloodSugarData.length"
                id="blood-sugar-trend-chart"
                :key="params.begin"
                v-abc-loading="loading"
                :date="dates"
                :data="bloodSugarData"
                :legend="{
                    data: legendData,
                    top: 0,
                    left: '20%',
                }"
                :grid="{
                    show: true,
                    borderWidth: 0,
                    borderColor: '#E6E9EC',
                    containLabel: true,
                    top: '15%',
                    left: '3%',
                    bottom: '16%',
                    right: '4%',
                }"
                height="400px"
                :data-zoom-conf="null"
                line-color="#2680F7"
                item-color="#2680F7"
                x-axis-label-count="10"
                :x-axis-label-rotate="0"
                width="780px"
            ></abc-line-chart>
        </div>
    </abc-dialog>
</template>
<script>
    import { parseTime } from '@abc/utils-date';
    import NurseTherapyAPI from '@/api/nurse/nurse-therapy.js';
    export default {
        components: {
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            patientOrderId: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                dateRange: [parseTime(new Date, 'y-m-d', true), parseTime(new Date, 'y-m-d', true)],
                params: {
                    begin: '',
                    end: '',
                    patientOrderId: '',
                },
                loading: false,
                dates: [],
                legendData: [],
                bloodSugarData: [],
            };
        },
        computed: {
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        created() {
            this.params.begin = this.dateRange[0];
            this.params.end = this.dateRange[1];
            this.getHospitalBloodSugarRecord();
        },
        methods: {
            handleDateChange(list) {
                this.params.begin = list[0];
                this.params.end = list[1];
                this.getHospitalBloodSugarRecord();
            },

            getSelectedTableKey(header = []) {
                const selectedColumns = [];
                header?.forEach((item) => {
                    if (item?.columnChildren?.length) {
                        item.columnChildren.forEach((child) => {
                            selectedColumns.push({
                                key: child.prop,
                                name: child.label,
                            });
                        });
                    } else {
                        selectedColumns.push({
                            key: item.prop,
                            name: item.label,
                        });
                    }
                });

                this.legendData = selectedColumns.map((item) => item.name);
                return selectedColumns;

            },
            groupByKey(arr1, arr2) {
                const result = [];
                for (let i = 0; i < arr1.length; i++) {
                    const { key } = arr1[i];
                    const { name } = arr1[i];
                    const values = arr2.map((item) => item[key]).filter((value) => value !== null && value !== undefined && value !== 0);
                    result.push({
                        key, name, value: values,
                    });
                }
                return result;
            },

            generateBloodSugarData(selectedColumns = [], list = []) {
                const arr = this.groupByKey(selectedColumns, list);
                const colors = ['#005ed9', '#e52d5b', '#ff5b84', '#e5892d', '#08a446', '#e65f20', '#67ce0e', '#ec21ce', '#7990ee', '#79eee6', '#79ee97', '#ee799b', '#d3ee79', '#ed6969'];
                const res = [];
                for (let i = 0; i < arr.length; i++) {
                    res.push({
                        name: arr[i].name,
                        data: arr[i].value,
                        type: 'line',
                        itemStyle: {
                            normal: {
                                color: colors[i], // 点的颜色
                            },
                        },
                        lineStyle: {
                            normal: {
                                color: colors[i], // 线的颜色
                                width: 1,
                            },
                        },
                    });
                }
                this.bloodSugarData = res;
            },
            async getHospitalBloodSugarRecord() {
                try {
                    this.loading = true;
                    const {
                        begin, end,
                    } = this.params;
                    const res = await NurseTherapyAPI.getBloodSugarRecord({
                        begin,
                        end,
                        patientOrderId: this.patientOrderId,
                        isBetween: !(begin && end) ? false : true,
                    });

                    /**
                     * 【【0228】【静康】血糖单本地化调整】
                     * https://www.tapd.cn/47644659/prong/stories/view/1147644659001074201
                     * @旭东: 趋势图不展示血酮, 所以过滤掉
                     */
                    res.tableHeaderEmployeeItemsByTableKey = res.tableHeaderEmployeeItemsByTableKey.filter((it) => it.prop !== 'bloodSugarBloodKetone');

                    const selectedColumns = this.getSelectedTableKey(res.tableHeaderEmployeeItemsByTableKey);
                    if (res) {
                        const list = res?.result?.filter((item) => item.type === 0).sort((a, b) => new Date(a.measuringTime) - new Date(b.measuringTime));
                        this.generateBloodSugarData(selectedColumns, list);
                        this.dates = [];
                        list.forEach((item) => {
                            const date = parseTime(new Date(item.measuringTime), 'y-m-d', true);
                            const isExist = this.dates.includes(date);
                            if (!isExist) {
                                this.dates.push(date);
                            }
                            const dateSort = (arr) => {
                                return arr.sort((a, b) => {
                                    const dateA = new Date(a);
                                    const dateB = new Date(b);
                                    return dateA - dateB;
                                });
                            };

                            this.dates = dateSort(this.dates);
                        });
                    } else {
                        this.dates = [];
                        this.bloodSugarData = [];
                    }
                } catch (err) {
                    this.bloodSugarData = [];
                    this.dates = [];
                    console.log(err);
                } finally {
                    this.loading = false;
                }
            },
        },
    };
</script>
<style lang="scss" scoped>
.hospital-daily__blood-sugar-dialog {
    .search-bar {
        display: flex;
        justify-content: space-between;
        margin-top: 16px;
    }

    .content {
        padding-top: 16px;
    }
}
</style>
