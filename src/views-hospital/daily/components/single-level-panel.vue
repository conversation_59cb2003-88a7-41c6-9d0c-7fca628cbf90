<template>
    <abc-popover
        ref="singlePopoverRef"
        trigger="manual"
        :z-index="10000"
        :visible-arrow="false"
        placement="bottom-start"
        :value="showPopover"
        theme="white"
        popper-class="single-record-popover-wrapper"
        :disabled="disabled"
    >
        <div slot="reference">
            <abc-tooltip
                placement="top"
                theme="black"
                :arrow-offset="toolTipArrowOffset"
                :offset="toolTipOffset"
                :max-width="234"
                :content="toolTipInfo"
                :disabled="isDisableToolTip"
            >
                <abc-input
                    v-abc-click-outside="handleClickOutSide"
                    :value="displayValue"
                    :type="(isBreathe || isText) ? 'text' : 'number'"
                    :config="{
                        max: max,
                        formatLength: formatLength
                    }"
                    :input-custom-style="{
                        color: isDisableToolTip ? '#000' : '#ff9933'
                    }"
                    :disabled="disabled"
                    :max-length="maxLength"
                    @input="handleInput"
                    @focus="togglePopover"
                    @keydown.native="handleDown"
                ></abc-input>
            </abc-tooltip>
        </div>
        <single-level-record-list
            ref="recordList"
            :display-value="displayValue"
            :record="record"
            :width="width"
            @click="handleRecordListItemClick"
        ></single-level-record-list>
    </abc-popover>
</template>

<script>
    import SingleLevelRecordList from './single-level-record-list.vue';

    export default {
        components: {
            SingleLevelRecordList,
        },
        props: {
            record: {
                type: Object,
                default: () => ({}),
            },
            width: {
                type: Number,
                default: 150,
            },
            max: {
                type: Number,
                default: 150,
            },
            isBreathe: {
                type: Boolean,
                default: false,
            },
            formatLength: {
                type: Number,
                default: 0,
            },
            displayValue: {
                type: [ Number, String],
                default: '',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            isText: {
                type: Boolean,
                default: false,
            },
            maxLength: {
                type: Number,
                default: 500,
            },
            toolTipInfo: {
                type: String,
                default: '',
            },
            isDisableToolTip: {
                type: Boolean,
                default: true,
            },
            toolTipArrowOffset: {
                type: Number,
                default: 10,
            },
            toolTipOffset: {
                type: Number,
                default: 70,
            },
        },
        data() {
            return {
                showPopover: false,
            };
        },
        methods: {
            togglePopover() {
                this.showPopover = !this.showPopover;
            },
            handleInput(value) {
                this.$emit('click', value);
                this.showPopover = false;
            },
            handleDown(event) {
                const { key } = event;
                if (['Enter', 'ArrowRight', 'ArrowLeft', 'ArrowDown', 'ArrowUp', 'Tab'].includes(key)) {
                    this.showPopover = false;
                }
            },
            handleClickOutSide() {
                if (!this.showPopover) return;
                this.showPopover = false;
            },
            handleRecordListItemClick(val) {
                this.$emit('click', val);
            },
        },
    };
</script>

<style lang="scss">
.single-record-popover-wrapper {
    padding: 0;
}
</style>
