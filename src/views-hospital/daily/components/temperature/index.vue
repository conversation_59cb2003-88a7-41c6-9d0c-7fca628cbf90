<template>
    <div class="hospital-daily__temperature">
        <filter-bar
            :date-filter.sync="dateFilter"
            :time-filter.sync="timeFilter"
            :patient-id.sync="patientId"
            :setting-options="settingOptions"
            :save-loading="saveLoading"
            :disabled="isNotChangeData"
            :patient-options="effectivePatientList"
            :time-options="comTimeOptions"
            @time-change="handleTimeChange"
            @date-change="handleDateChange"
            @save-btn-click="handleSave"
            @setting-btn-click="handleOpenSettingDialog"
            @patient-change="handlePatientChange"
            @export-btn-click="handleExport"
        ></filter-bar>

        <div class="hospital-daily__temperature-table">
            <abc-table-fixed2
                ref="tableFixed2Ref"
                :key="params.measuringTime"
                style="max-height: 100%;"
                class="temperature-table"
                :class="{ 'show-border': !loading }"
                :loading="loading"
                :data="tableData"
                :max-height="tableHeight"
                :min-height="tableHeight"
                :header="tableRenderHeader"
                :cell-click-handler="handleCellClick"
                :empty-opt="{ label: '暂无数据' }"
            >
            </abc-table-fixed2>
        </div>
        <temperature-dialog
            v-if="temperatureFormDialog"
            v-model="temperatureFormDialog"
            :begin="params.begin"
            :end="params.end"
            :measuring-time="params.measuringTime"
            :inpatient-time="inpatientTime"
            :patient-order-ids="params.patientOrderIds"
            :patient-order-id="params.patientOrderId"
        ></temperature-dialog>
    </div>
</template>

<script>
    import FilterBar from '@/views-hospital/daily/components/filter-bar/index.vue';
    import TemperatureDialog from './dialog/temperature-dialog.vue';
    import { resolveHeader } from 'views/statistics/utils.js';
    import TemperaturePopover from '@/views-hospital/beds/components/bed-allocation-dialog/temperature-popover.vue';
    import HeightPopover from '@/views-hospital/beds/components/bed-allocation-dialog/height-popover.vue';
    import WeightPopover from '@/views-hospital/beds/components/bed-allocation-dialog/weight-popover.vue';
    import NurseTherapyAPI from '@/api/nurse/nurse-therapy.js';
    import MultiLevelPanel from '../multi-level-panel.vue';
    import SingleLevelPanel from '../single-level-panel.vue';
    import PatientEventPanel from '../patient-event-panel.vue';
    import StoolPanel from '../stool-panel.vue';
    import { outputsRecord } from '../temperature/config';
    import TemperatureUtils from '@/views-hospital/daily/utils/temperature.js';
    import {
        timeOptions,time1Options,
    } from '@/views-hospital/daily/constant.js';
    import {
        temperatureRecord,
        pulseRecord,
        urinateRecord,
        heartRateRecord ,
        breatheRecord,
        weightRecord,
        heightRecord,
        systolicPressurePmRecord,
        diastolicPressurePmRecord,
        painScoreRecord,
        patientEventList,
        stoolsTypeList,
        bloodSugar,
    } from './config';
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone.js';
    import { parseTime } from '@abc/utils-date';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { calBMI } from '@/utils';
    import { calcTableHeight } from '@/views-hospital/daily/utils/index.js';
    import {
        off, on,
    } from 'utils/dom';
    import propertyAPI from 'api/property';
    import { BizCustomHeader } from '@/components-composite/biz-custom-header';
    import TableHeaderAPI from 'views/statistics/core/api/table-header';

    export default {
        components: {
            FilterBar,
            TemperatureDialog,
            // eslint-disable-next-line vue/no-unused-components
            TemperaturePopover,
            // eslint-disable-next-line vue/no-unused-components
            HeightPopover,
            // eslint-disable-next-line vue/no-unused-components
            WeightPopover,
            // eslint-disable-next-line vue/no-unused-components
            MultiLevelPanel,
            // eslint-disable-next-line vue/no-unused-components
            SingleLevelPanel,
            // eslint-disable-next-line vue/no-unused-components
            PatientEventPanel,
            // eslint-disable-next-line vue/no-unused-components
            StoolPanel,
        },
        data() {
            return {
                loading: true,
                temperatureFormDialog: false,
                tableHeader: [],
                // 原始数据
                originalTableData: [],
                tableData: [],
                dateFilter: parseTime(new Date, 'y-m-d', true),
                temperatureRecord,
                pulseRecord,
                breatheRecord,
                weightRecord,
                heightRecord,
                systolicPressurePmRecord,
                diastolicPressurePmRecord,
                bloodSugar,
                painScoreRecord,
                timeFilter: '02:00',
                patientId: '',
                patientEventText: '',
                patientEventTime: '',
                params: {
                    patientOrderId: '',
                    begin: '',
                    end: '',
                    measuringTime: '',
                    patientOrderIds: [],
                },
                patientList: [],
                showPhysicalSuggestionPanel: false,
                currentCommonExamination: {
                    selectedTab: 1,
                    list: weightRecord.list,
                },
                currentHeightRecord: {
                    selectedTab: 1,
                    list: heightRecord.list,
                },
                patientEventList,
                stoolsTypeList,
                patientEventHour: '',
                patientEventMin: '',
                patientEventType: 1,
                stoolsType: 1,
                stoolsCount: '',
                settingOptions: [
                    {
                        text: '体征单',
                        value: 'physical',
                        isOpen: false,
                        groupName: '',
                    },
                    {
                        text: '体温单',
                        isOpen: false,
                        value: 'temperature',
                        groupName: '',
                    },
                ],
                tableHeight: 500,
                inpatientTime: '',
                saveLoading: false,
                timeMode: 0,
            };
        },
        computed: {
            comTimeOptions() {
                if (this.timeMode === 1) {
                    return time1Options;
                }
                return timeOptions;
            },
            tableRenderHeader() {
                return resolveHeader(this.tableHeader, this.renderTypeList);
            },
            renderTypeList() {
                const month = parseTime(this.params.measuringTime, 'm-d', true);
                return {
                    temperatureHeaderRender: (h, col) => {
                        if (col.prop === 'temperature') {
                            return <div class="cell">{month}</div>;
                        }
                    },
                    bloodPressureHeaderRender: (h, col) => {
                        const hourText = parseTime(this.params.measuringTime, 'hh', true) > 12 ? '下午' : '上午';
                        if (col.prop === 'bloodPressure') {
                            return <div class="cell">{month} {hourText}</div>;
                        }
                    },
                    physicalHeaderRender: (h, col) => {
                        if (col.prop === 'physical') {
                            return <div class="cell">{month} 全天</div>;
                        }
                    },
                    inputRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (<abc-input
                                    type="number"
                                    config={{ max: 200 }} value={displayValue} onChange={(val) => this.handleChange(val, row, col)}></abc-input>);
                    },
                    bmiRender: (h, row) => {
                        const {
                            weight = 0, height = 0,
                        } = row;
                        return (<abc-input
                            type="number"
                            disabled
                            value={calBMI(weight, height) || ''}></abc-input>);
                    },
                    patientRender: (h, row) => {
                        const { patientOrderId = '' } = row;
                        const patientName = this.patientList.find((item) => item.id === patientOrderId)?.name;
                        const patientBedNo = this.patientList.find((item) => item.id === patientOrderId)?.bedNo;
                        const inpatientTime = this.patientList.find((item) => item.id === patientOrderId)?.inpatientTime;
                        return <div class="cell" style="color:#005eD9" onClick={() => this.handlePatientClick(inpatientTime)}>
                                    <span style="margin-right: 5px">{patientBedNo >= 10 ? patientBedNo : `0${patientBedNo}`}床</span>
                                    <span title={patientName}>{patientName}</span>
                            </div>;
                    },
                    temperatureRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (<SingleLevelPanel record={ temperatureRecord} width={450} max={42} formatLength={1} displayValue={displayValue} on-click={(val) => this.handleChange(val, row, col)}>
                            </SingleLevelPanel>);
                    },
                    outputsRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (
                            <SingleLevelPanel record={ outputsRecord } width={450} max={10000} displayValue={displayValue} on-click={(val) => this.handleChange(val, row, col)}>

                            </SingleLevelPanel>);
                    },
                    urinateRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (
                            <SingleLevelPanel isText={true} record={ urinateRecord } maxLength={4} width={225} displayValue={displayValue} on-click={(val) => this.handleChange(val, row, col)}>
                            </SingleLevelPanel>);
                    },
                    patientEventRender: (h, row, col) => {
                        const {
                            patientEventType = '', patientEventTime = '',
                        } = row;
                        const patientEventText = this.patientEventList.find((item) => item.id === +patientEventType)?.name || '';
                        const displayEventTime = patientEventTime ? parseTime(patientEventTime, 'h:i', true) : '';
                        const displayValue = patientEventText && patientEventTime ? `于 ${displayEventTime} ${patientEventText}` : '';
                        return (<PatientEventPanel patientEventList={patientEventList} measuringTime={this.params.measuringTime} on-click={(val) => this.handleSavePatientEvent(val, row, col)}>
                            <abc-input
                                    value={displayValue }
                                    on-change={(val) => this.handlePatientEventChange(val, row, col)}
                                ></abc-input>
                            </PatientEventPanel>);
                    },
                    stoolsRender: (h, row, col) => {
                        const displayValue = TemperatureUtils.calcStoolTypeInfo(row);
                        return (<StoolPanel stoolsTypeList={stoolsTypeList} fromDoctor={true}
                                on-click={(val) => this.handleStoolTypeChange(val, row, col)}
                                displayValue={displayValue}>

                            </StoolPanel>);
                    },
                    pulseRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (
                            <SingleLevelPanel record={ pulseRecord} max={180} displayValue={displayValue} width={450} on-click={(val) => this.handleChange(val, row, col)}>

                            </SingleLevelPanel>);
                    },
                    heartRateRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (
                            <SingleLevelPanel max={138} displayValue={displayValue} record={ heartRateRecord } width={450} on-click={(val) => this.handleChange(val, row, col)}>

                            </SingleLevelPanel>);
                    },
                    breatheRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (
                            <SingleLevelPanel record={ breatheRecord } max={40} isBreathe={true} displayValue={displayValue} width={450} on-click={(val) => this.handleChange(val, row, col)}>
                            </SingleLevelPanel>);
                    },
                    weightRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (<WeightPopover unit={'Kg'} fromDaily={true} on-click={(val) => this.handleChange(val, row, col)}>
                            <abc-input
                                value={displayValue}
                                type="number"
                                config={{
                                    max: 200,
                                    formatLength: 1,
                                }}
                                onChange={(val) => this.handleChange(val, row, col)}
                            ></abc-input>
                        </WeightPopover>);
                    },
                    heightRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (<HeightPopover unit={'cm'} fromDaily={true} on-click={(val) => this.handleChange(val, row, col)}>
                            <abc-input
                                value={displayValue}
                                type="number"
                                config={{
                                    max: 300,
                                    formatLength: 1,
                                }}
                                onChange={(val) => this.handleChange(val, row, col)}
                            ></abc-input>
                        </HeightPopover>);
                    },
                    // 尿量
                    urineOutputRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (<SingleLevelPanel max={10000} formatLength={0} displayValue={displayValue} record={ outputsRecord } width={450} on-click={(val) => this.handleChange(val, row, col)}>

                            </SingleLevelPanel>);
                    },
                    temSystolicPressureRender: (h, row, col) => {
                        const displayValue = row.temSystolicPressure ;
                        const displayDiastolicValue = row.temDiastolicPressure ;
                        const isDisableToolTip = Number(displayValue ?? 0) >= Number(displayDiastolicValue ?? 0);
                        return (<SingleLevelPanel
                                    max={1000}
                                    displayValue={displayValue}
                                    record={systolicPressurePmRecord}
                                    width={450}
                                    toolTipInfo="数据异常，收缩压不能小于舒张压"
                                    isDisableToolTip={isDisableToolTip}
                                    on-click={(val) => this.handleTemSystolicPressureChange(val, row, col)}>
                                </SingleLevelPanel>
                        );
                    },

                    temDiastolicPressureRender: (h, row, col) => {
                        const displayValue = row.temDiastolicPressure ;
                        const displayDiastolicValue = row.temSystolicPressure ;
                        const isDisableToolTip = Number(displayDiastolicValue ?? 0) >= Number(displayValue ?? 0);
                        return (<SingleLevelPanel
                                    max={1000}
                                    displayValue={displayValue}
                                    record={diastolicPressurePmRecord}
                                    width={450}
                                    toolTipInfo="数据异常，收缩压不能小于舒张压"
                                    isDisableToolTip={isDisableToolTip}
                                    on-click={(val) => this.handleTemDiastolicPressureChange(val, row, col)}>
                                </SingleLevelPanel>
                        );
                    },
                    randomBloodSugarRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (<SingleLevelPanel
                                    max={1000}
                                    displayValue={displayValue}
                                    record={bloodSugar}
                                    width={450}
                                    on-click={(val) => this.handleChange(val, row, col)}>
                                </SingleLevelPanel>
                        );
                    },
                    skRender: (h, row, col) => {
                        const displayValue = parseTime(this.params.measuringTime, 'hh', true) > 12 ? row.skPm : row.skAm ;
                        return (<abc-input
                                   value={displayValue} onChange={(val) => this.handleSkChange(val, row, col)}></abc-input>);
                    },
                    systolicPressurePmRender: (h, row, col) => {
                        const displayValue = parseTime(this.params.measuringTime, 'hh', true) > 12 ? row.systolicPressurePm : row.systolicPressureAm ;
                        const displayDiastolicValue = parseTime(this.params.measuringTime, 'hh', true) > 12 ? row.diastolicPressurePm : row.diastolicPressureAm;
                        const isDisableToolTip = Number(displayValue ?? 0) >= Number(displayDiastolicValue ?? 0);
                        return (<SingleLevelPanel max={1000} displayValue={displayValue} record={ systolicPressurePmRecord} width={450} on-click={(val) => this.handleSystolicPressureChange(val, row, col)} toolTipInfo="数据异常，收缩压不能小于舒张压" isDisableToolTip={isDisableToolTip}>

                            </SingleLevelPanel>);
                    },
                    diastolicPressurePmRender: (h, row, col) => {
                        const displayValue = parseTime(this.params.measuringTime, 'hh', true) > 12 ? row.diastolicPressurePm : row.diastolicPressureAm;
                        const displaySystolicValue = parseTime(this.params.measuringTime, 'hh', true) > 12 ? row.systolicPressurePm : row.systolicPressureAm ;
                        const isDisableToolTip = Number(displaySystolicValue ?? 0) >= Number(displayValue ?? 0);
                        return (<SingleLevelPanel max={1000} displayValue={displayValue} record={ diastolicPressurePmRecord} width={450} on-click={(val) => this.handleDiastolicPressureChange(val, row, col)} toolTipInfo="数据异常，收缩压不能小于舒张压" isDisableToolTip={isDisableToolTip}>
                            </SingleLevelPanel>);
                    },
                    painScoreRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        return (
                            <SingleLevelPanel max={10} displayValue={displayValue} record={painScoreRecord} width={495} on-click={(val) => this.handleChange(val, row, col)}>

                            </SingleLevelPanel>);
                    },
                };
            },

            isNotChangeData() {
                return isEqual(this.tableData,this.originalTableData);
            },

            effectivePatientList() {
                const date = new Date(this.params.measuringTime);
                // date.setHours(date.getHours());
                // const before = new Date(date.getTime() - 4 * 60 * 60 * 1000);
                const after = new Date(date.getTime() + 4 * 60 * 60 * 1000);
                return this.patientList.filter((item) => ((new Date(item.inpatientTime) <= after) && !item.dischargeTime) || item.dischargeTime && new Date(item.dischargeTime) >= date && (new Date(item.inpatientTime) <= date));
            },
        },
        watch: {
            'tableData.length': {
                handler() {
                    this.onAgencyKeyboardEvent();
                },
            },
        },
        async created() {
            await this.fetchHospitalCareSettingRules();
            this.initParams();
            await this.getPatientList();
            this.getComposeTableData();
        },
        mounted() {
            this.calcHeightHandler();
        },
        beforeDestroy() {
            off(this._tableBodyEl, 'keydown', this.onKeyDown);
        },
        methods: {
            calcHeightHandler() {
                this._timer = setTimeout(() => {
                    this.tableHeight = calcTableHeight(this.$refs.tableFixed2Ref.$el, 31);
                }, 0);
            },
            handlePatientClick(time) {
                this.inpatientTime = parseTime(time, 'y-m-d', true);
            },
            disabledInput(inpatientDate, outPatientDate) {
                return (outPatientDate && new Date() > outPatientDate) || new Date() < inpatientDate;
            },
            initParams() {
                this.timeFilter = TemperatureUtils.findClosestTimeOption(this.comTimeOptions);
                this.params.measuringTime = `${this.dateFilter} ${this.timeFilter}`;
            },
            async getComposeTableData() {
                try {
                    const {
                        measuringTime, patientOrderIds,
                    } = this.params;
                    this.loading = true;
                    const [
                        { data: temperatureData },
                        { data: physicalData },
                    ] = await Promise.all([
                        NurseTherapyAPI.getTemperatureRecordByTime({
                            measuringTime,
                            patientOrderIds,
                        }),
                        NurseTherapyAPI.getPhysicalRecordByTime({
                            measuringTime: `${parseTime(measuringTime, 'y-m-d', true)} 00:00`,
                            patientOrderIds,
                        }),
                    ]);

                    const {
                        flatTableHeader,
                        flatTableData,
                    } = TemperatureUtils.composePhysicalRecord(temperatureData,physicalData, this.params.measuringTime);
                    const generatedTableData = this.normalizeComposeTableData(flatTableData);


                    this.tableHeader = flatTableHeader;
                    this.originalTableData = generatedTableData.map((item) => {
                        return {
                            ...item,
                            stools: JSON.parse(item?.stools || null) || {
                                enema_after: '',
                                enema_before: '',
                                stools: '',
                                type: 0,
                            },
                        };
                    });
                    this.tableData = clone(this.originalTableData);

                } catch (err) {
                    console.log('fetch compose tableData err', err);
                    this.tableHeader = [];
                    this.tableData = [];
                } finally {
                    this.loading = false;
                }
            },

            async handleExport() {
                const params = {
                    ids: this.params.patientOrderIds,
                    patientOrderIds: this.params.patientOrderIds,
                    measuringTime: this.params.measuringTime,
                };
                const res = await NurseTherapyAPI.exportTemperature(params);
                if (res) {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
                    if ('download' in document.createElement('a')) {
                        const link = document.createElement('a');
                        link.download = `体温单_${this.params.measuringTime}.xls`;
                        link.style.display = 'none';
                        link.href = URL.createObjectURL(blob);
                        document.body.appendChild(link);
                        link.click();
                        URL.revokeObjectURL(link.href);
                        document.body.removeChild(link);
                    } else {
                        navigator.msSaveBlob(blob);
                    }
                }
            },

            async getPatientList() {
                try {
                    const res = await PatientOrderAPI.getInHospitalPatientList({
                        limit: 200,
                        departmentId: '',
                        sceneType: 6,
                        wardId: this.$route.params.wardId,
                    });
                    // 过滤掉当前日期后的患者
                    const patientList = res?.data?.rows?.filter((item) => item.bedNo)
                        ?.sort((a, b) => (+a.bedNo) - (+b.bedNo))?.map((it) => {
                            return {
                                id: it.id,
                                name: it?.patient?.name,
                                bedNo: it.bedNo,
                                dischargeTime: it?.dischargeTime,
                                inpatientTime: it.inpatientTime,
                            };
                        });

                    patientList.forEach((item) => {
                        const isExist = this.patientList.find((it) => it.id === item.id);
                        if (!isExist) {
                            this.patientList.push({
                                ...item,
                            });
                        }
                    });

                    this.params.patientOrderIds = this.patientList.map((item) => item.id);

                } catch (err) {
                    console.log(err);
                }
            },
            normalizeComposeTableData(originTableData = []) {
                let newTableData = [];
                const arr = [];
                const tableItem = {
                    analTemperature: '',
                    axillaryTemperature: '',
                    earTemperature: '',
                    heartRate: '',
                    id: '',
                    measuringTime: this.params.measuringTime,
                    oralTemperature: '',
                    painScore: '',
                    patientEventTime: '',
                    patientEventType: '',
                    physicalCooling: '',
                    pulse: '',
                    respiratoryRate: '',
                    bloodOxygen: '',
                    urinate: '',
                    bmi: '',
                    diastolicPressureAm: '',
                    diastolicPressurePm: '',
                    height: '',
                    inputs: '',
                    outputs: '',
                    stools: '',
                    systolicPressureAm: '',
                    systolicPressurePm: '',
                    skAm: '',
                    skPm: '',
                    urineOutput: '',
                    weight: '',
                    temDiastolicPressure: '',
                    temSystolicPressure: '',
                    randomBloodSugar: '',
                    temperatureMeasuringTime: this.params.measuringTime,
                    physicalMeasuringTime: `${parseTime(this.params.measuringTime, 'y-m-d', true)} 00:00`,
                };

                if (this.patientId) {
                    newTableData = originTableData?.length && originTableData || [{
                        ...tableItem,
                        patientOrderId: this.patientId,
                        patientId: '',
                        dischargeTime: this.patientList.find((it) => it.id === this.patientId)?.dischargeTime,
                        inpatientTime: this.patientList.find((it) => it.id === this.patientId)?.inpatientTime,
                    }];

                } else {
                    this.effectivePatientList?.forEach((patient) => {
                        arr.push({
                            ...tableItem,
                            patientOrderId: patient.id,
                            patientId: '',
                            dischargeTime: patient?.dischargeTime,
                            inpatientTime: patient?.inpatientTime,
                        });
                    });

                    newTableData = arr;

                    if (originTableData?.length) {
                        originTableData.forEach((item) => {
                            arr.forEach((newItem, index) => {
                                if (item.patientOrderId === newItem.patientOrderId) {
                                    newTableData[index] = item;
                                }
                            });
                        });
                    }
                }

                return newTableData;
            },

            getDiffDataForm(newData, oldData) {
                const arr = [];
                newData.forEach((item, index) => {
                    if (!isEqual(item, oldData[index])) {
                        arr.push({
                            ...item,
                        });
                    }
                });
                return arr;
            },
            handlePatientTypeChange(item) {
                this.patientEventType = item.id;
            },

            handlePatientEventHourChange(val) {
                this.patientEventHour = val;
            },
            handlePatientEventMinChange(val) {
                this.patientEventMin = val;
            },
            handleEventChange(val, row, col) {
                const selectedIndex = this.tableData.findIndex((item) => item[col.prop] === row[col.prop] && item.patientOrderId === row.patientOrderId);
                if (selectedIndex !== -1) {
                    this.$set(this.tableData[selectedIndex], 'flag', 0);
                }

            },
            handleSavePatientEvent(payload, row) {
                const {
                    patientEventTime, patientEventType, patientEventText,
                } = payload || {};
                const selectedIndex = this.tableData.findIndex((item) => item.patientOrderId === row.patientOrderId);
                if (selectedIndex !== -1) {
                    this.$set(this.tableData[selectedIndex], 'patientEventTime', patientEventTime);
                    this.$set(this.tableData[selectedIndex], 'patientEventType', patientEventType);
                    this.$set(this.tableData[selectedIndex], 'patientEventText', patientEventText);
                }
            },
            handlePatientEventChange(val, row) {
                if (!val) {
                    const selectedIndex = this.tableData.findIndex((item) => item.patientOrderId === row.patientOrderId);
                    if (selectedIndex !== -1) {
                        this.$set(this.tableData[selectedIndex], 'patientEventTime', null);
                        this.$set(this.tableData[selectedIndex], 'patientEventType', null);
                        this.$set(this.tableData[selectedIndex], 'patientEventText', null);
                    }
                }
            },
            handleStoolTypeChange(stools, row) {
                const selectedIndex = this.tableData.findIndex((item) => item.patientOrderId === row.patientOrderId);
                if (selectedIndex !== -1) {
                    this.$set(this.tableData[selectedIndex], 'stools', stools);
                }
            },
            handleChange(val, row, col) {
                const selectedIndex = this.tableData.findIndex((item) => item[col.prop] === row[col.prop] && item.patientOrderId === row.patientOrderId);
                if (selectedIndex !== -1) {
                    this.$set(this.tableData[selectedIndex], [col.prop], val || '');
                }
            },

            handleTemSystolicPressureChange(val, row) {
                const selectedIndex = this.tableData.findIndex((item) =>
                    item.patientOrderId === row.patientOrderId);
                if (selectedIndex !== -1) {
                    this.$set(this.tableData[selectedIndex], 'temSystolicPressure', val || '');
                }
            },


            handleTemDiastolicPressureChange(val, row) {
                const selectedIndex = this.tableData.findIndex((item) => item.patientOrderId === row.patientOrderId);
                if (selectedIndex !== -1) {
                    this.$set(this.tableData[selectedIndex], 'temDiastolicPressure', val || '');
                }
            },
            handleSystolicPressureChange(val, row, col) {
                const isMorning = parseTime(this.params.measuringTime, 'hh', true) <= 12;
                const displayValue = isMorning ? 'systolicPressureAm' : 'systolicPressurePm';
                if (col.prop === 'systolicPressure') {
                    const selectedIndex = this.tableData.findIndex((item) =>
                        item[displayValue] === row[displayValue] && item.patientOrderId === row.patientOrderId);
                    if (selectedIndex !== -1) {
                        this.$set(this.tableData[selectedIndex], displayValue, val || '');
                    }
                }
            },
            handleSkChange(val, row, col) {
                const isMorning = parseTime(this.params.measuringTime, 'hh', true) <= 12;
                const displayValue = isMorning ? 'skAm' : 'skPm';
                if (col.prop === 'sk') {
                    const selectedIndex = this.tableData.findIndex((item) =>
                        item[displayValue] === row[displayValue] && item.patientOrderId === row.patientOrderId);
                    if (selectedIndex !== -1) {
                        this.$set(this.tableData[selectedIndex], displayValue, val || '');
                    }
                }
            },
            handleDiastolicPressureChange(val, row, col) {
                const displayValue = parseTime(this.params.measuringTime, 'hh', true) > 12 ? 'diastolicPressurePm' : 'diastolicPressureAm';
                if (col.prop === 'diastolicPressure') {
                    const selectedIndex = this.tableData.findIndex((item) => item[displayValue] ===
                        row[displayValue] && item.patientOrderId === row.patientOrderId);
                    if (selectedIndex !== -1) {
                        this.$set(this.tableData[selectedIndex], displayValue, val || '');
                    }
                }
            },
            handleSelectedTab(tab) {
                this.currentCommonExamination.selectedTab = tab.id;
                this.currentCommonExamination.list = tab.value;
            },
            handleSelectedHeightTab(tab) {
                this.currentHeightRecord.selectedTab = tab.id;
                this.currentHeightRecord.list = tab.value;
            },


            async handleDateChange() {
                this.params.measuringTime = `${this.dateFilter} ${this.timeFilter}`;
                this.loading = true;
                this.getComposeTableData();
            },
            async handleTimeChange() {
                this.params.measuringTime = `${this.dateFilter} ${this.timeFilter}`;
                this.getComposeTableData();
            },

            async handlePatientChange() {

                if (this.patientId) {
                    this.params.patientOrderIds = [this.patientId];
                    this.params.patientOrderId = this.patientId;
                } else {
                    this.params.patientOrderIds = this.patientList.map((item) => item.id);
                    this.params.patientOrderId = '';
                }

                this.getComposeTableData();
            },
            handleCellClick(row, col) {
                if (col.prop !== 'patient') return;
                this.params.patientOrderId = row.patientOrderId;
                this.temperatureFormDialog = true;
            },

            handleOpenSettingDialog() {
                new BizCustomHeader({
                    value: true,
                    tableKey: 'nurse.daily.physical',
                    titleName: '体征单',
                    showFixed: false,
                    finishFunc: this.getComposeTableData,
                    tableHeaderApi: TableHeaderAPI,
                }).generateDialog({ parent: this });
            },
            async handleSave() {
                this.saveLoading = true;
                // 分组
                const originalTableParams = this.generateTableGroup(this.originalTableData);
                const newTableParams = this.generateTableGroup(this.tableData);
                // 获取当前更改的数据
                const temperatureParams = this.getDiffDataForm(newTableParams.nurseTemperatureRecordDtos, originalTableParams.nurseTemperatureRecordDtos);
                const physicalParams = this.getDiffDataForm(newTableParams.nursePhysicalRecordDtos, originalTableParams.nursePhysicalRecordDtos);
                const params = {
                    nurseTemperatureRecordDtos: temperatureParams,
                    nursePhysicalRecordDtos: physicalParams,
                };
                try {
                    const res = await NurseTherapyAPI.createPhysicalRecord(params);
                    if (res) {
                        this.$Toast({
                            message: '保存成功',
                            type: 'success',
                        });
                        this.getComposeTableData();
                    }
                } catch (err) {
                    this.$Toast({
                        message: '保存失败',
                        type: 'error',
                    });
                } finally {
                    this.saveLoading = false;
                }
            },

            generateTableGroup(list) {
                const nursePhysicalRecordDtos = [];
                const nurseTemperatureRecordDtos = [];
                list.forEach((item) => {
                    nurseTemperatureRecordDtos.push({
                        analTemperature: item.analTemperature,
                        axillaryTemperature: item.axillaryTemperature,
                        earTemperature: item.earTemperature,
                        heartRate: item.heartRate,
                        id: item.temperatureId,
                        measuringTime: this.params.measuringTime,
                        // measuringTime: item.measuringTime,
                        oralTemperature: item.oralTemperature,
                        painScore: item.painScore,
                        patientEventTime: item.patientEventTime,
                        patientEventType: item.patientEventType,
                        patientId: item.patientId,
                        patientOrderId: item.patientOrderId,
                        physicalCooling: item.physicalCooling,
                        pulse: item.pulse,
                        respiratoryRate: item.respiratoryRate,
                        temSystolicPressure: item.temSystolicPressure,
                        temDiastolicPressure: item.temDiastolicPressure,
                        randomBloodSugar: item.randomBloodSugar,
                    });

                    nursePhysicalRecordDtos.push({
                        bloodOxygen: item.bloodOxygen,
                        urinate: item.urinate,
                        bmi: item.bmi,
                        diastolicPressureAm: item.diastolicPressureAm,
                        diastolicPressurePm: item.diastolicPressurePm,
                        height: item.height,
                        id: item.physicalId,
                        inputs: item.inputs,
                        measuringTime: item.physicalMeasuringTime,
                        // measuringTime: parseTime(item.physicalMeasuringTime, 'y-m-d', true),
                        outputs: item.outputs,
                        patientId: item.patientId,
                        patientOrderId: item.patientOrderId,
                        stools: item.stools,
                        systolicPressureAm: item.systolicPressureAm,
                        systolicPressurePm: item.systolicPressurePm,
                        skAm: item.skAm,
                        skPm: item.skPm,
                        urineOutput: item.urineOutput,
                        weight: item.weight,
                    });
                });

                return {
                    nursePhysicalRecordDtos,
                    nurseTemperatureRecordDtos,
                };

            },

            async onAgencyKeyboardEvent() {
                await this.$nextTick();
                this._tableBodyEl = this.$refs.tableFixed2Ref.$el.querySelector('.table-tbody');
                // 每行可输入input长度
                const trEl = this._tableBodyEl?.querySelector('tr');
                const trInputs = trEl?.querySelectorAll('input.abc-input__inner:not([disabled]):not([tabIndex="-1"])');
                // 每一行input个数
                const rowInputLength = trInputs?.length;
                // 所有input
                const inputs = this._tableBodyEl?.querySelectorAll('input.abc-input__inner:not([disabled]):not([tabIndex="-1"])');
                on(this._tableBodyEl, 'keydown', (e) => this.onKeyDown(e, inputs, rowInputLength));
            },

            onKeyDown(event, inputs, rowInputLength) {
                const {
                    key,
                    target,
                } = event;
                if (target.tagName !== 'INPUT') return;
                let targetIndex = Array.from(inputs).findIndex((x) => x === target);
                switch (key) {
                    case 'Enter':
                    case 'ArrowRight':
                        targetIndex++;
                        while (inputs[targetIndex]?.tabIndex === -1) {
                            targetIndex++;
                        }
                        break;
                    case 'ArrowLeft':
                        targetIndex--;
                        while (inputs[targetIndex]?.tabIndex === -1) {
                            targetIndex--;
                        }
                        break;
                    case 'ArrowDown':
                        targetIndex += rowInputLength;
                        while (inputs[targetIndex]?.tabIndex === -1) {
                            targetIndex += rowInputLength;
                        }
                        break;
                    case 'ArrowUp':
                        targetIndex -= rowInputLength;
                        while (inputs[targetIndex]?.tabIndex === -1) {
                            targetIndex -= rowInputLength;
                        }
                        break;
                    default:
                        return;
                }
                if (!inputs[targetIndex]) {
                    return false;
                }
                inputs[targetIndex].focus();
            },

            async fetchHospitalCareSettingRules() {
                try {
                    const { data } = await propertyAPI.getV3('hisNurse.daily.temperature.timeMode', 'clinic');
                    this.timeMode = data;
                    if (this.timeMode === 1) {
                        this.timeFilter = '03:00';
                    }
                } catch (e) {
                    console.log(e);
                }
            },
        },
    };
</script>

<style lang="scss">
@import '../../_index.scss';

.temperature__physical-record-list {
    display: flex;
    flex-wrap: wrap;

    .count {
        padding: 5px 6px;
        line-height: 35px;

        &:hover {
            color: #0090ff;
            cursor: pointer;
            background-color: #c6e2ff;
            border-radius: var(--abc-border-radius-small);
        }
    }
}

.temperature__weight-record-wrapper {
    display: flex;
    flex-wrap: wrap;
    min-width: 200px;
    max-width: 890px;

    .tabs {
        width: 114px;

        .tab {
            height: 26px;
            margin-bottom: 12px;
            line-height: 26px;
            cursor: pointer;

            &.is-selected {
                background-color: #eff3f6;
            }

            &:hover {
                background-color: #eff3f6;
            }
        }
    }

    .list {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        max-width: 710px;

        .count {
            padding: 5px 6px;
            line-height: 24px;

            &:hover {
                color: #0090ff;
                cursor: pointer;
                background-color: #c6e2ff;
                border-radius: var(--abc-border-radius-small);
            }
        }
    }
}

.temperature__patient-event-wrapper {
    min-width: 200px;
    max-width: 490px;
    margin-bottom: 10px;

    .event-type-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .type {
            padding: 5px 8px;

            &.is-selected {
                color: #005ed9;
                cursor: pointer;
                background: #eff3f6;
                border-radius: 2px;
            }

            &:hover {
                color: #005ed9;
                cursor: pointer;
                background: #eff3f6;
                border-radius: 2px;
            }
        }
    }

    .event-time {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .footer {
        display: flex;
        justify-content: flex-end;
    }
}
</style>
