<template>
    <div class="single-record-list" :style="{ width: `${width}px` }">
        <template v-for="(item, index) in record.list">
            <div :key="index" class="single-record-row">
                <div
                    v-for="(value, i) in item.value"
                    :key="i"
                    class="single-record-col"
                >
                    <input
                        ref="itemRefs"
                        class="count"
                        :class=" {
                            'is-focus': isActive(index, i)
                        }"
                        tabindex="0"
                        :readonly="true"
                        :value="value"
                        :data-row="index"
                        :data-col="i"
                        @click="handleClick(value)"
                        @keydown="onKeyDown"
                    />
                </div>
            </div>
            <div v-if="splitIndexGroup.includes(index)" :key="`${index}_split`" class="single-record-row_spliter"></div>
        </template>
    </div>
</template>

<script>
    export default {
        name: 'SingleLevelRecordList',
        props: {
            record: {
                type: Object,
                default: () => ({ list: [] }),
            },
            displayValue: {
                type: [ Number, String],
                default: '',
            },
            width: {
                type: Number,
                default: 150,
            },
            enableFocus: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                activeItemIndex: 0,
                activeIndex: 0,
                itemRefs: [],
            };
        },
        computed: {
            splitIndexGroup() {
                return this.record.splitIndexGroup || [];
            },
        },
        watch: {
            displayValue: {
                handler(val) {
                    this.$nextTick(() => this.updateItemRefs(val));
                },
                immediate: true,
                deep: true,
            },
        },
        mounted() {
            this.itemRefs = this.$refs.itemRefs.flat();
            this.enableFocus && this.itemRefs[this.activeIndex].focus();
        },
        methods: {
            updateItemRefs(val) {
                this.itemRefs = this.$refs.itemRefs.flat();
                if (val) {
                    const itemRef = this.itemRefs.find((item) => item._value?.toString() === this.displayValue?.toString());
                    if (itemRef) {
                        this.enableFocus && itemRef.focus();
                        this.activeIndex = +itemRef.getAttribute('data-col');
                        this.activeItemIndex = +itemRef.getAttribute('data-row');
                    }
                } else {
                    this.activeIndex = 0;
                    this.enableFocus && this.itemRefs[this.activeIndex].focus();
                }
            },
            handleClick(value) {
                this.handleSelect(value);
            },
            handleSelect(value) {
                this.$emit('click', value);
                this.showPopover = false;
            },
            isActive(itemIndex, index) {
                return itemIndex === this.activeItemIndex && index === this.activeIndex;
            },
            handleDown() {
                this.itemRefs[this.activeIndex].focus();
            },
            onKeyDown(event) {
                const { key } = event;

                switch (key) {
                    case 'ArrowLeft':
                        if (this.activeIndex > 0) {
                            this.activeIndex--;
                        }
                        break;
                    case 'ArrowRight':
                        if (this.activeIndex < this.record.list[this.activeItemIndex].value.length - 1) {
                            this.activeIndex++;
                        }
                        break;
                    case 'ArrowUp':
                        if (this.activeItemIndex > 0) {
                            this.activeItemIndex--;
                            this.activeIndex = Math.min(
                                this.activeIndex,
                                this.record.list[this.activeItemIndex].value.length - 1,
                            );
                        }
                        break;
                    case 'ArrowDown':
                        if (this.activeItemIndex < this.record.list.length - 1) {
                            this.activeItemIndex++;
                            this.activeIndex = Math.min(
                                this.activeIndex,
                                this.record.list[this.activeItemIndex].value.length - 1,
                            );
                        }
                        break;
                    case 'Enter':
                        // eslint-disable-next-line no-case-declarations
                        const value = this.record.list[this.activeItemIndex].value[this.activeIndex];
                        this.handleSelect(value);
                        break;
                    default:
                        break;
                }
                this.itemRefs[this.activeIndex].focus();
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/mixin.scss';

.single-record-list {
    display: flex;
    flex-wrap: wrap;

    .single-record-row {
        border-bottom: 1px solid #eff3f6;

        &:last-child {
            border-bottom: 0;
        }

        .single-record-col {
            display: inline-block;
            width: 45px;
            height: 30px;
            line-height: 30px;
            text-align: center;

            &.is-focus {
                font-weight: bold;
                background-color: #e5f2ff;
            }

            &:hover {
                cursor: pointer;
                background-color: $P4;
            }

            &:active {
                font-weight: bold;
                background-color: #e5f2ff;
            }
        }
    }

    .single-record-row_spliter {
        width: 100%;
        height: 12px;
        border-bottom: 1px solid #eff3f6;
    }

    .count {
        display: inline-block;
        width: 45px;
        height: 30px;
        text-align: center;
        border: 0;
        border-right: 1px solid #eff3f6;
        outline: none;

        &.is-focus {
            font-weight: bold;
            background-color: #e5f2ff;
        }

        &:hover {
            cursor: pointer;
            background-color: $P4;
        }

        &:active {
            font-weight: bold;
            background-color: #e5f2ff;
        }
    }
}
</style>
