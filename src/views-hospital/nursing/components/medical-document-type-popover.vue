<template>
    <abc-popover
        ref="popover"
        width="auto"
        placement="bottom-start"
        theme="white"
        trigger="click"
        :visible-arrow="false"
        :popper-style="{
            padding: 0,
            marginTop: '11px',
        }"
        @show="initRenderList"
    >
        <span slot="reference">
            <slot></slot>
        </span>

        <div class="medical-document-type-content-popover">
            <div class="medical-document-type-search">
                <abc-search
                    v-model="keyword"
                    :width="408"
                    placeholder="搜索"
                >
                </abc-search>
            </div>
            <div class="medical-document-type-tree">
                <abc-content-empty v-if="!renderTree.length" value="暂未搜索到结果，请重新搜索">
                </abc-content-empty>
                <template v-else>
                    <div
                        v-for="group in renderTree"
                        :key="group.catalogueId"
                        class="medical-document-type-group"
                    >
                        <div class="medical-document-list-popover_title">
                            {{ group.name }}
                        </div>
                        <div class="medical-document-list-popover_list">
                            <div
                                v-for="item in group.medicalViews"
                                :key="item.id"
                                class="medical-document-list-popover_list-item ellipsis"
                                :class="{ 'medical-document-list-popover_list-item--disabled': item.hasEdited }"
                                @click="handleClickMedicalDocument(item)"
                            >
                                <abc-tooltip v-if="mode === HandleMode.BATCH" content="该文书只能添加一次" :disabled="!item.disabled">
                                    <abc-checkbox
                                        :disabled="item.disabled"
                                        control
                                        :value="item.checked"
                                        @click="handleMedicalBatchClick(item)"
                                    >
                                        <abc-flex>
                                            <abc-text :title="item.name" class="ellipsis" style="max-width: 150px;">
                                                {{ item.name }}
                                            </abc-text>
                                            <abc-text theme="gray-light" class="medical-document-list-popover_list-item-index">
                                                {{ item.checkedIndex }}
                                            </abc-text>
                                        </abc-flex>
                                    </abc-checkbox>
                                </abc-tooltip>
                                <abc-text v-else class="ellipsis">
                                    {{ item.name }}
                                </abc-text>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
            <div v-if="businessType === MedicalDocumentBusinessType.HOSPITAL" class="medical-document-type-footer">
                <abc-flex justify="space-between" align="center">
                    <template v-if="mode === HandleMode.NORMAL">
                        <abc-button
                            variant="text"
                            size="small"
                            @click="handleClinicBatchAdd"
                        >
                            批量添加病历
                        </abc-button>
                        <abc-text size="mini" theme="gray-light">
                            *病历将按选中顺序依次添加
                        </abc-text>
                    </template>

                    <template v-else>
                        <abc-flex>
                            <abc-button
                                variant="text"
                                size="small"
                                :disabled="!selectedMedicalIds.length"
                                @click="handleClinicBatchAddConfirm"
                            >
                                添加病历
                            </abc-button>
                            <abc-button
                                variant="text"
                                size="small"
                                @click="handleClinicBatchAddCancel"
                            >
                                取消
                            </abc-button>
                        </abc-flex>
                        <abc-text bold>
                            已选 {{ selectedMedicalIds.length }} 项
                        </abc-text>
                    </template>
                </abc-flex>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import { NUMBER_ICONS } from '@/printer/common/constant';
    import { MedicalDocumentTagEnum } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants';
    import { MedicalDocumentBusinessType } from '@/views-hospital/nursing/common/constants';
    import AbcSearch from 'components/abc-search/index.vue';

    const HandleMode = {
        NORMAL: 'normal',
        BATCH: 'batch',
    };

    export default {
        name: 'MedicalDocumentTypePopover',
        components: { AbcSearch },
        props: {
            medicalDocumentList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            businessType: {
                type: Number,
            },
            medicalRecordList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                keyword: '',
                mode: HandleMode.NORMAL,
                selectedMedicalIds: [],
            };
        },
        computed: {
            MedicalDocumentBusinessType() {
                return MedicalDocumentBusinessType;
            },
            HandleMode() {
                return HandleMode;
            },
            renderTree() {
                return this.medicalDocumentList.map((group) => {
                    const children = group.medicalViews?.filter((item) => {
                        return item.name.indexOf(this.keyword) > -1;
                    });
                    return {
                        ...group,
                        medicalViews: children.map((it) => ({
                            ...it,
                            checked: this.selectedMedicalIds.includes(it.id),
                            checkedIndex: this.getCheckedIndex(it.id),
                            // 只能添加一次的如果已经添加过了就不能再添加了
                            disabled: !!(MedicalDocumentTagEnum.AddOnce & it.tag && this.medicalRecordList.some((item) => {
                                return item.medicalId === it.id;
                            })),
                        })),
                    };
                }).filter((group) => {
                    return !!group.medicalViews.length;
                });
            },
        },
        methods: {
            getCheckedIndex(id) {
                const index = this.selectedMedicalIds.findIndex((it) => it === id);
                return NUMBER_ICONS[index + 1];
            },
            handleClickMedicalDocument(item) {
                if (this.mode === HandleMode.BATCH) {
                    this.handleMedicalBatchClick(item);
                    return;
                }
                if (item.hasEdited) {
                    return;
                }
                this.$emit('select', item);
            },
            initRenderList() {
                this.keyword = '';
                this.mode = HandleMode.NORMAL;
                this.selectedMedicalIds = [];
                this.$emit('popover-show');
            },
            // 外部使用 切勿删除
            close() {
                this.$refs.popover.doClose();
            },
            handleClinicBatchAdd() {
                this.mode = HandleMode.BATCH;
            },
            handleClinicBatchAddCancel() {
                this.mode = HandleMode.NORMAL;
            },
            handleMedicalBatchClick(item) {
                const {
                    checked, id,
                } = item;
                if (!checked) {
                    this.selectedMedicalIds.push(id);
                } else {
                    const index = this.selectedMedicalIds.findIndex((it) => it === id);
                    this.selectedMedicalIds.splice(index, 1);
                }
            },
            handleClinicBatchAddConfirm() {
                this.$emit('batch-select', this.selectedMedicalIds);
                this.mode = HandleMode.NORMAL;
                this.close();
            },
        },
    };
</script>

<style lang="scss">
@import "~styles/theme.scss";
@import "~styles/mixin.scss";

.medical-document-type-content-popover {
    width: 440px;
    height: 524px;

    .medical-document-type-search {
        padding: 8px 16px;
        background-color: #f3f5f7;
        border-bottom: 1px solid var(--abc-color-P6);
        border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;
    }

    .medical-document-type-tree {
        height: calc(100% - 92px);
        padding: 16px;
        overflow-y: auto;
        overflow-y: overlay;

        @include scrollBar;
    }

    .medical-document-type-footer {
        padding: 8px 16px;
        background: var(--abc-color-S2);
        border-top: 1px solid var(--abc-color-P6);

        .medical-document-type-footer_tip {
            font-size: 12px;
            color: var(--abc-color-T3);
        }

        .medical-document-type-footer_selected {
            font-weight: bold;
        }
    }

    .medical-document-type-group:not(:last-child) {
        margin-bottom: 16px;
    }

    .medical-document-list-popover_title {
        margin-bottom: 4px;
        line-height: 20px;
        color: var(--abc-color-T2);
    }

    .medical-document-list-popover_list {
        display: grid;
        grid-template-columns: 200px 200px;
        grid-row-gap: 4px;
        grid-column-gap: 8px;

        .medical-document-list-popover_list-item {
            display: flex;
            align-items: center;
            width: 200px;
            height: 32px;
            padding: 6px 8px;
            line-height: 20px;
            cursor: pointer;
            border-radius: var(--abc-border-radius-small);

            .medical-document-file-icon {
                height: 14px;
                margin-right: 4px;
            }

            &:hover {
                background: var(--abc-color-P4);
            }

            .medical-document-list-popover_list-item-index {
                margin-left: 4px;
            }
        }

        .medical-document-list-popover_list-item--disabled {
            color: var(--abc-color-T3);
            cursor: not-allowed;

            &:hover {
                background: initial;
            }
        }
    }
}
</style>
