<!--以患者为维度的血糖单-->
<template>
    <div class="blood-sugar-wrapper">
        <div class="blood-sugar-header">
            <abc-text
                bold
                size="large"
                style="line-height: 32px; text-align: center;"
                class="blood-sugar-header-title"
            >
                血糖监测记录单
            </abc-text>
            <abc-button
                class="blood-sugar-header-btn"
                icon="s-b-chart-line"
                variant="text"
                style="margin-left: auto;"
                @click="handleClickPreview"
            >
                血糖趋势
            </abc-button>
        </div>

        <abc-table-fixed2
            ref="tableFixed2Ref"
            :loading="loading"
            :class="{ 'blood-sugar-table': !loading }"
            :data="tableListByConfig"
            :header="tableRenderHeader"
        >
            <template v-if="!disabled" #tableFooter>
                <abc-button
                    icon="s-b-add-line-medium"
                    variant="text"
                    size="small"
                    @click="handleAddLine"
                >
                    新增
                </abc-button>
            </template>
        </abc-table-fixed2>
        <blood-sugar-trend-dialog
            v-if="bloodSugarTrendDialogVisible"
            v-model="bloodSugarTrendDialogVisible"
            :patient-order-id="patientOrderId"
        ></blood-sugar-trend-dialog>
    </div>
</template>

<script>
    import { resolveHeader } from 'views/statistics/utils';
    import { formatDate } from '@abc/utils-date';
    import { mapGetters } from 'vuex';
    import { createGUID } from '@/utils';
    import BloodSugarTrendDialog
        from '@/views-hospital/daily/components/blood-sugar/dialog/blood-sugar-trend-dialog.vue';
    import {
        calculateCumulativeValues,
    } from '@/views-hospital/nursing/components/nursing-daily-blood-sugar/helper/utils';
    import NurseTherapyAPI from 'api/nurse/nurse-therapy';

    export default {
        name: 'BloodSugarTable',
        components: { BloodSugarTrendDialog },
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
            tableHeader: {
                type: Array,
                default: () => [],
            },
            tableList: {
                type: Array,
                default: () => [],
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            patientId: {
                type: String,
                default: '',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            employeeList: {
                type: Array,
                default: () => [],
            },
            config: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                tableHeight: 584,
                bloodSugarTrendDialogVisible: false,
                isDelete: false,
            };
        },
        computed: {
            ...mapGetters(['userInfo']),
            userId() {
                return this.userInfo?.id;
            },
            existedMeasuringTime() {
                const arr = this.tableList.map((item) => formatDate(item.measuringTime,'YYYY-MM-DD'));
                return [...new Set(arr)];
            },
            isShowSignLine() {
                //签字行 0:显示签字行 1:不显示
                return this.config?.signLine === 0;
            },
            tableListByConfig() {
                //signWay: 0:显示签字行 1:不显示
                const { signLine } = this.config;
                //type: 0 血糖(name)  1 签名(select)
                return signLine === 1 ? this.tableList.filter((item) => item.type !== 1) : this.tableList;
            },
            tableRenderHeader() {
                const customPreHeader = [
                    {
                        label: '日期',
                        prop: 'measuringTime',
                        renderType: 'timeRender',
                        width: 180,
                        groupBy: 'combineId',
                    },
                ];
                const customSuffixHeader = [
                    {
                        label: '',
                        prop: '',
                        renderType: 'deleteRender',
                        width: 40,
                        groupBy: 'combineId',
                        isHidden: this.disabled,
                        tdStyle: {
                            borderLeft: '1px solid #e6eaee',
                        },
                    },
                ].filter((item) => !item.isHidden);
                const header = [...customPreHeader,...this.tableHeader,...customSuffixHeader];
                const resolvedHeader = resolveHeader(header, this.renderTypeList);
                this.handleStyle(resolvedHeader);
                return resolvedHeader;
            },
            renderTypeList() {
                const { disabled } = this;
                return {
                    inputRender: (h, row, col) => {
                        const { type } = row; //type: 0 血糖(name)  1 签名(select)
                        const displayValue = row[col.prop];
                        if (type === 0) {
                            const inputConfig = {
                                min: null, // 最少输入多少位，type 为 number/money 时生效
                                max: 200, // 最多输入多少位，type 为 number/money 时生效
                                formatLength: 1, // 支持小数点后几位，type 为 number/money 时生效
                                supportZero: false, // 是否支持零，type 为 number/money 时生效
                                supportFraction: false, // 是否支持分数，type 为 number/money 时生效
                                supportNegative: false, // 是否支持负数，type 为 number/money 时生效
                            };
                            // 血酮支持 2 位小数
                            if (col.prop === 'bloodSugarBloodKetone') {
                                inputConfig.formatLength = 2;
                            }
                            return (
                                <abc-input value={displayValue}
                                           type="number"
                                           max-length={6}
                                           disabled={disabled}
                                           config={inputConfig}
                                           onChange={(val) => this.handleInputChange(val, row, col)}>
                                </abc-input>
                            );
                        }
                        if (type === 1) {
                            return (
                                <abc-select
                                    value={displayValue}
                                    adaptive-width
                                    disabled={disabled}
                                    clearable
                                    no-icon
                                    size="medium"
                                    onChange={(val) => this.handleSelectChange(val, row, col)}>
                                    {
                                        this.employeeList.map((item) => {
                                            return <abc-option label={item.name} value={item.id}></abc-option>;
                                        })
                                    }
                                </abc-select>
                            );
                        }
                    },
                    timeRender: (h, row,col) => {
                        const { measuringTime = '' } = row;
                        const _this = this;
                        const pickerOptions = {
                            shortcuts: [{
                                text: '今天',
                                onClick(cb) {
                                    cb(new Date());
                                },
                            }],
                            disabledDate(date) {
                                const _formatDate = _this.formatDate(date,'YYYY-MM-DD');
                                return _this.existedMeasuringTime.includes(_formatDate);
                            },
                        };
                        return (
                            <abc-flex class="cell" style="height:100%;padding:0" align="center">
                                <abc-flex style="border-right: 1px solid #e6eaee;height:100%;width:55%;cursor:pointer" align="center" justify="center">
                                  <abc-date-picker
                                      value={measuringTime}
                                      width={160}
                                      disabled={disabled}
                                      value-format="YYYY-MM-DD"
                                      picker-options={pickerOptions}
                                      onChange={(val) => this.handleShortcutChange(val,row,col)}
                                  >
                                      <abc-button type="text">{formatDate(measuringTime,'YYYY-MM-DD') || '选择日期'}</abc-button>
                                </abc-date-picker>
                                </abc-flex>
                                <abc-flex vertical="vertical" align="center" style="height:100%;flex:1;fontWeight:bold">
                                    <abc-text theme="gray" style="flex:1;display:flex;align-items:center">血糖值<br/>mmol/L</abc-text>
                                    {this.isShowSignLine ? (<abc-flex style="border-top: 1px solid #e6eaee;flex:1;width:100%;color:#7a8794" align="center" justify="center">签字</abc-flex>
                                    ) : null}
                                </abc-flex>
                            </abc-flex>
                        );
                    },
                    deleteRender: (h,row) => {
                        const { isDelete = false } = row;
                        return (
                          <abc-flex class="cell" align="center" justify="center" v-abc-click-outside={() => {row.isDelete = false;}}>
                              <abc-popover
                                  value={isDelete}
                                  width="120px"
                                  placement="right"
                                  trigger="click"
                                  theme="white"
                                  popper-style={{ padding: '8px' }}
                              >
                                  <div class="cut-wrapper">
                                      <span class="cut-btn" style="color: #ff5b84; cursor: pointer;" onClick={() => this.handleDelete(row)}>确认删除</span><div class="cut-line"></div> <span class="cut-btn" style="color: #005ed9; cursor: pointer;" onClick={() => {row.isDelete = false;}}>取消</span>
                                  </div>
                                  <abc-delete-icon slot="reference" size="large" onDelete={() => {row.isDelete = true;}}></abc-delete-icon>
                              </abc-popover>
                          </abc-flex>
                        );
                    },
                };
            },
        },
        methods: {
            formatDate,
            handleClickPreview() {
                this.bloodSugarTrendDialogVisible = true;
            },
            handleInputChange(val, row, col) {
                if (this.disabled) return;
                this.tableList?.forEach((item) => {
                    if ((item.combineId === row.combineId) && item.type === row.type) {
                        let fixedCount = 1;
                        if (col.prop === 'bloodSugarBloodKetone') {
                            fixedCount = 2;
                        }
                        this.$set(item, [col.prop], val && typeof +val === 'number' ? parseFloat(val || 0).toFixed(fixedCount) : val);
                        this.handleSelectChange(this.userId,{
                            ...row,type: 1,
                        },col,'input');
                    }
                });
            },
            handleSelectChange(val, row, col,way) {
                if (this.disabled) return;
                this.tableList?.forEach((item) => {
                    if ((item.combineId === row.combineId) && item.type === row.type) {
                        //如果血糖值输入导致的触发签名select，且输入框有值，不进行赋值
                        if (way === 'input' && item[col.prop]) return;
                        this.$set(item, [col.prop], val);
                    }
                });
            },
            handleShortcutChange(val,row,col) {
                if (this.disabled) return;
                this.tableList?.forEach((item) => {
                    if ((item.combineId === row.combineId)) {
                        this.$set(item, [col.prop], val);
                    }
                });
            },
            async handleDelete(row) {
                if (this.disabled) return;
                row.isDelete = false;
                if (row.isAdd) {
                    // 如果是前端添加，使用 splice 方法删除该元素
                    for (let i = this.tableList.length - 1; i >= 0; i--) {
                        if (this.tableList[i].combineId === row.combineId) {
                            this.tableList.splice(i, 1);
                        }
                    }
                } else if (row.type === 0) { //这里判断type===0是为了删除请求只删除一次，因为type=0和=1的combineId一样
                    try {
                        const res = await NurseTherapyAPI.deleteBloodSugarRecord({
                            combineIds: [row.combineId],
                        });
                        if (res) {
                            this.$Toast({
                                message: '删除成功',
                                type: 'success',
                            });
                        }
                        this.$emit('refresh');
                    } catch (e) {
                        console.log(e);
                    }
                }

            },
            handleAddLine() {
                const combineId = createGUID();
                const _measuringTime = this.findMaxDateAndReturnNextDay(this.existedMeasuringTime);
                const commonTemp = {
                    'measuringTime': _measuringTime ? _measuringTime : formatDate(new Date(), 'YYYY-MM-DD'),
                    'bloodSugarNoon_beforeLunchBloodSugar': null,
                    'patientId': '',
                    'patientOrderId': this.patientOrderId,
                    'bloodSugarAm_afterBreakfastOneHourBloodSugar': null,
                    'bloodSugarAm_afterMedicineBloodSugar': null,
                    'bloodSugarRandomBloodSugar': null,
                    'bloodSugarNoon_afterLunchTwoHourBloodSugar': null,
                    'bloodSugarAfterNoon_afterDinnerTwoHourBloodSugar': null,
                    'bloodSugarAfterNoon_beforeDinnerBloodSugar': null,
                    'bloodSugarNoon_afterLunchOneHourBloodSugar': null,
                    'bloodSugarAm_afterBreakfastTwoHourBloodSugar': null,
                    'bloodSugarNight_beforeSleepBloodSugar': null,
                    'bloodSugarNoon_noonAfterMedicineBloodSugar': null,
                    'bloodSugarAm_beforeBreakfastBloodSugar': null,
                    combineId,
                    'bloodSugarAfterNoon_AfterNoonAfterMedicineBloodSugar': null,
                    'bloodSugarNight_sleepingBloodSugar': null,
                    'bloodSugarAfterNoon_afterDinnerOneHourBloodSugar': null,
                    'bloodSugarMorn_wakeUpBloodSugar': null,
                    isAdd: true,
                };
                const temp = [
                    {
                        ...commonTemp,
                        type: 0,
                    },
                    {
                        ...commonTemp,
                        type: 1,
                    },
                ];
                this.tableList.push(...temp);
            },

            findMaxDateAndReturnNextDay(dates) {
                const dateObjects = dates.map((dateStr) => new Date(dateStr));
                if (dateObjects && dateObjects.length > 0) {
                    const maxDate = new Date(Math.max(...dateObjects));
                    maxDate.setDate(maxDate.getDate() + 1);
                    return formatDate(maxDate, 'YYYY-MM-DD');
                }
                return false;
            },
            handleStyle(headers) {
                const count = calculateCumulativeValues(headers);
                const hadRandomField = headers.some((item) => item.label === '随机');
                headers.forEach((item) => {
                    this.handleItemStyle(item,count,hadRandomField);
                    if (item.children && Array.isArray(item.children)) {
                        item.children.forEach((_it) => {
                            this.handleItemStyle(_it,count,hadRandomField);
                        });
                    }
                });
            },
            handleItemStyle(item,count,hadRandomField) {
                item.sortable = 0;
                item.tdStyle = {
                    ...item.tdStyle,
                    height: '56px',
                };
                const ALL_WIDTH = 1130;

                if (item.label === '日期') {
                    item.width = 170;
                } else if (item.label === '随机') {
                    item.width = 60;
                } else if (item.renderType === 'deleteRender') {
                    item.width = 40;
                } else {
                    item.width = hadRandomField ? Math.floor((ALL_WIDTH - 170 - 60 - 40) / (count - 3)) : Math.floor((ALL_WIDTH - 170 - 40) / (count - 2));
                }
            },
        },
    };
</script>

<style lang="scss">
.blood-sugar-wrapper {
    min-height: 100%;
    padding: 8px 24px;
    background: var(--abc-color-cp-white, #ffffff);
    border: var(--abc-border-1, 1px) solid var(--abc-color-P7, #e0e5ee);
    border-radius: var(--abc-border-radius-mini, 4px);

    .blood-sugar-header {
        position: relative;
        height: 56px;
        padding: 12px 0;

        &-title {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        &-btn {
            position: absolute;
            right: 0;
        }
    }

    .abc-fixed-table.blood-sugar-table {
        border-right: 1px solid $P6;
        border-left: 1px solid $P6;
        border-radius: var(--abc-border-radius-small);

        .abc-table__header-wrapper {
            table th {
                padding: 6px 8px;
                font-weight: bold;
                word-wrap: break-word;
                white-space: normal;
            }
        }

        .is-disabled .abc-input__inner {
            background-color: #ffffff !important;
        }

        .abc-table__body-wrapper,
        .abc-table__footer-wrapper {
            tbody td {
                border-left: none;
            }

            .abc-input-wrapper,
            .abc-select-wrapper {
                .abc-input__inner {
                    height: 56px;
                    border: 0;
                    border-radius: 0;

                    &:focus {
                        border: 1px solid $theme1;
                    }
                }
            }
        }

        .table-footer-wrapper {
            padding: 8px 12px;
            border-top: 1px solid var(--abc-table-fixed2-border-color);
        }

        tr.is-hover {
            background: #ffffff;
        }
    }
}

.cut-wrapper {
    display: inline-flex;
    align-items: center;

    .cut-btn {
        padding: 0 2px;

        &:hover {
            background: var(--abc-color-cp-grey4);
        }
    }

    .cut-line {
        width: 1px;
        height: 18px;
        margin: 0 2px;
        background-color: $P6;
    }
}
</style>
