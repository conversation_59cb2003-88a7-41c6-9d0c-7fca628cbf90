<template>
    <div class="abc-collapse-item" :class="{ 'abc-collapse-item_active': active }" @click="handleClickItem">
        <div class="abc-collapse-item_inner">
            <div v-if="isSelectedMode" class="abc-collapse-item_checkbox">
                <abc-checkbox
                    :value="isSelected"
                    control
                    @click="handleClickCheckbox"
                ></abc-checkbox>
            </div>
            <div class="abc-collapse-item_label ellipsis">
                {{ label }}
            </div>
            <div class="abc-collapse-item_created-name ellipsis">
                {{ createdByName }}
            </div>
            <div class="abc-collapse-item_created ellipsis">
                <span class="create-year">{{ created.slice(0, 5) }}</span><span class="create-date">{{ created.slice(5) }}</span>
            </div>
            <div v-if="showErrorIcon" class="abc-collapse-item_archive-status">
                退
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'AbcCollapseItem',
        inject: ['$abcCollapsePanel'],
        props: {
            label: {
                type: String,
                default: '',
            },
            createdByName: {
                type: String,
                default: '',
            },
            created: {
                type: String,
                default: '',
            },
            active: {
                type: Boolean,
                default: false,
            },
            hasEdited: {
                type: Boolean,
                default: false,
            },
            showErrorIcon: {
                type: Boolean,
                default: false,
            },
            isSelectedMode: {
                type: Boolean,
                default: false,
            },
            isSelected: {
                type: Boolean,
                default: false,
            },
        },
        created() {
            if (!Array.isArray(this.$abcCollapsePanel.$abcCollapseItems)) {
                return console.warn('AbcCollapseItem必须在AbcCollapsePanel中使用');
            }
            this.$abcCollapsePanel.$abcCollapseItems.push(this);
        },
        beforeDestroy() {
            if (!Array.isArray(this.$abcCollapsePanel.$abcCollapseItems)) {
                return console.warn('AbcCollapseItem必须在AbcCollapsePanel中使用');
            }
            const index = this.$abcCollapsePanel.$abcCollapseItems.indexOf(this);
            if (index > -1) {
                this.$abcCollapsePanel.$abcCollapseItems.splice(index, 1);
            }
        },
        methods: {
            handleClickItem() {
                this.$emit('click');
            },
            handleClickCheckbox() {
                this.$emit('click-checkbox');
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.abc-collapse-item {
    padding-left: 14px;
    color: $T1;

    .abc-collapse-item_inner {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        line-height: 18px;
        cursor: pointer;
        border-radius: var(--abc-border-radius-small);

        .abc-collapse-item_checkbox {
            height: 16px;
            margin-right: 8px;
        }

        .abc-collapse-item_label {
            flex: 1;
            width: 0;
        }

        .abc-collapse-item_created-name {
            width: 56px;
            font-size: 12px;
            color: $T2;
            text-align: right;
        }

        .abc-collapse-item_created {
            width: 114px;
            font-size: 12px;
            color: $T2;
            text-align: right;
        }

        .medical-file-icon {
            height: 14px;
            margin-right: 4px;
        }

        .abc-collapse-item_archive-status {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            margin-left: 8px;
            font-size: 12px;
            color: #f99b33;
            background: #fff5e6;
            border: 1px solid #ffebd6;
            border-radius: var(--abc-border-radius-small);
        }

        &:hover {
            background: #f0f3f6;
        }
    }

    &.abc-collapse-item_active {
        .abc-collapse-item_inner {
            font-weight: 500;
            color: #005ed9;
            background: #d6eaff;
        }

        .abc-collapse-item_label {
            font-weight: 500;
        }

        .abc-collapse-item_created,
        .abc-collapse-item_created-name {
            font-weight: 400;
            color: #005ed9;
        }
    }

    &.is-edited {
        color: $T1;
    }
}
</style>
