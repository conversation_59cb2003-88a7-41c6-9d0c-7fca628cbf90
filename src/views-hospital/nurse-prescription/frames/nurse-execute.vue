<template>
    <div
        class="main-content medical-prescription-content"
        style="padding-bottom: 0;"
    >
        <div class="top-toolbar">
            <abc-flex align="center" :gap="8">
                <abc-tabs-v2
                    v-if="showFilterTabs"
                    v-model="tabsFilterParam"
                    :option="tabOptions"
                    size="middle"
                    type="outline"
                    adaptation
                    style="width: 282px;"
                    @change="onChangeTab"
                >
                </abc-tabs-v2>
                <abc-date-picker
                    v-model="filterParams.planExecuteDate"
                    :disabled="!!searchKeyword"
                    placeholder="日期"
                    :describe-list="dateDescribeList"
                    :date-icon="dateDescribeList.length ? 'Attention' : undefined"
                    :date-icon-color="dateDescribeList.length ? '#ff9933' : undefined"
                ></abc-date-picker>
                <abc-select
                    v-model="filterParams.adviceType"
                    :width="120"
                    placeholder="医嘱"
                    clearable
                    focus-show-options
                    :disabled="!!searchKeyword"
                >
                    <abc-option
                        v-for="d in medicalPrescriptionTypeList"
                        :key="d.value"
                        :value="d.value"
                        :label="d.label"
                    >
                    </abc-option>
                </abc-select>
                <abc-input
                    v-model.trim="searchKeyword"
                    type="text"
                    :width="searchInputWidth"
                    placeholder="医嘱内容"
                    :icon="searchKeyword ? 'cis-icon-cross_small' : ''"
                    @icon-click="searchKeyword = ''"
                >
                    <template #prepend>
                        <abc-search-icon></abc-search-icon>
                    </template>
                </abc-input>
                <abc-checkbox
                    v-model="onlyNotExecute"
                    type="number"
                    style="color: var(--abc-color-T2);"
                    :disabled="!!searchKeyword"
                    @change="onChangeFilterStatus"
                >
                    未执行
                </abc-checkbox>
                <abc-checkbox
                    v-model="onlyNotPrint"
                    style="color: var(--abc-color-T2);"
                    type="number"
                    :disabled="!!searchKeyword"
                    @change="onChangeFilterStatus"
                >
                    未打印
                </abc-checkbox>
            </abc-flex>

            <div class="right">
                <abc-space>
                    <abc-tooltip :content="isWaitSettleOrDischargeToolTip" :disabled="!isWaitSettleOrDischargeStatus">
                        <div>
                            <abc-button
                                :disabled="!toExecuteList.length || isWaitSettleOrDischargeStatus"
                                :count="toExecuteList.length"
                                @click="handleExecute"
                            >
                                执行
                            </abc-button>
                        </div>
                    </abc-tooltip>

                    <abc-tooltip :content="isWaitSettleOrDischargeToolTip" :disabled="!isWaitSettleOrDischargeStatus">
                        <div>
                            <abc-button
                                :disabled="!toRevokeExeList.length || isWaitSettleOrDischargeStatus"
                                type="danger"
                                :count="toRevokeExeList.length"
                                @click="onRevokeExeAdvices"
                            >
                                撤销
                            </abc-button>
                        </div>
                    </abc-tooltip>

                    <abc-button
                        icon="print"
                        type="blank"
                        @click="handleClickPrintNursePrescription"
                    >
                        打印
                    </abc-button>
                </abc-space>
            </div>
        </div>
        <div class="doctor-advice-table">
            <table-nurse-execute
                ref="neTable"
                :loading="contentLoading"
                :show-content-empty="!pageLoading"
                :data-list="mpTaskList"
                :selected-advice-id="selectedAdviceId"
                :fill-reference-el="fillReferenceEl"
                @astConfirm="fetchData"
                @success="fetchData"
            >
            </table-nurse-execute>
        </div>

        <nurse-execute-m-p-dialog
            v-if="showExecuteDialog"
            v-model="showExecuteDialog"
            :list="toExecuteList"
            @onSuccess="handleSuccess"
        ></nurse-execute-m-p-dialog>

        <revoke-medical-prescription-dialog
            v-if="isShowRevokeMedicalPrescriptionDialog"
            v-model="isShowRevokeMedicalPrescriptionDialog"
            :checked-list="checkedList"
            action="execute"
            @success="handleSuccess"
        ></revoke-medical-prescription-dialog>

        <medical-prescription-detail-popover
            v-if="isShowDetailPopover"
            :id="selectedAdviceId"
            :key="selectedAdviceId"
            v-model="isShowDetailPopover"
            v-abc-click-outside="handleCloseDetail"
            style="top: 168px; right: 24px;"
            @success="handleSuccess"
        ></medical-prescription-detail-popover>

        <nurse-prescription-print-dialog
            v-if="isShowPrescriptionPrintDialog"
            v-model="isShowPrescriptionPrintDialog"
            :mp-list="mpTaskList"
            @print-success="handlePrintSuccess"
        >
        </nurse-prescription-print-dialog>
    </div>
</template>

<script>
    import MedicalPrescriptionAPI from 'api/hospital/medical-prescription/index.js';
    import NurseExecuteMPDialog from '@/views-hospital/nurse-prescription/components/nurse-execute-dialog.vue';
    import RevokeMedicalPrescriptionDialog from '@/views-hospital/medical-prescription/components/revoke-medical-prescription-dialog/index.vue';
    import TableNurseExecute from '@/views/layout/tables/table-nurse-execute/index.vue';
    import MedicalPrescriptionDetailPopover from '@/views-hospital/medical-prescription/components/medical-prescription-detail-popover.vue';
    import {
        MedicalPrescriptionTypeList,
        DiagnosisTreatmentTypeList,
    } from '@/views-hospital/medical-prescription/utils/config';
    import { parseTime } from '@/filters/index.js';
    import {
        AdviceSocketTypeEnum,
        MedicalAdvicePrintStatusEnum,
        MedicalAdviceStatusEnum,
        TreatmentTypeEnum,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import MedicineConfig from 'src/assets/configure/western-medicine-config';
    import { debounce } from 'utils/lodash.js';
    import NursePrescriptionPrintDialog from '@/views-hospital/nurse-prescription/components/nurse-prescription-print-dialog.vue';
    import { prevDate } from '@abc/utils-date';
    import { HospitalStatusEnum } from '@/views-hospital/register/utils/constants';
    import { mapGetters } from 'vuex';

    export default {
        name: 'NurseExecute',
        components: {
            NursePrescriptionPrintDialog,
            NurseExecuteMPDialog,
            RevokeMedicalPrescriptionDialog,
            MedicalPrescriptionDetailPopover,
            TableNurseExecute,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            patientOrderIds: {
                type: Array,
                required: true,
            },
            showFilterTabs: {
                type: Boolean,
                default: true,
            },
            fillReferenceEl: {
                type: HTMLElement,
                default: null,
            },
            pageLoading: {
                type: Boolean,
                default: false,
            },
            isPageLoading: {
                type: Boolean,
                default: false,
            },
            inPatientDetailInfo: {
                type: Object,
            },
        },
        data() {
            return {
                HospitalStatusEnum,
                contentLoading: false,
                medicalPrescriptionTypeList: MedicalPrescriptionTypeList,
                diagnosisTreatmentTypeList: DiagnosisTreatmentTypeList,
                filterParams: {
                    planExecuteDate: parseTime(new Date(), 'y-m-d', true),
                    adviceType: undefined,
                    diagnosisTreatmentType: undefined,
                    usages: undefined,
                    executeStatusList: [],
                },
                searchKeyword: '',
                tabsFilterParam: undefined,
                mpTaskList: [],
                showExecuteDialog: false,
                isShowRevokeMedicalPrescriptionDialog: false,

                isShowDetailPopover: false,
                selectedAdviceId: '',
                isShowPrescriptionPrintDialog: false,

                // 只看未执行
                onlyNotExecute: false,
                // 只看未打印
                onlyNotPrint: false,

                dateDescribeList: [],
            };
        },
        computed: {
            ...mapGetters(['chineseMedicineConfig']),
            tabOptions() {
                return [
                    {
                        label: '全部',
                        value: undefined,
                        disabled: !!this.searchKeyword,
                    },
                    {
                        label: '口服',
                        value: '口服',
                        disabled: !!this.searchKeyword,
                    },
                    {
                        label: '输注',
                        value: '输注',
                        disabled: !!this.searchKeyword,
                    },
                    {
                        label: '中药',
                        value: '中药',
                        disabled: !!this.searchKeyword,
                    },
                    {
                        label: '护理',
                        value: TreatmentTypeEnum.NURSE,
                        disabled: !!this.searchKeyword,
                    },
                    {
                        label: '检验',
                        value: TreatmentTypeEnum.ASSAY,
                        disabled: !!this.searchKeyword,
                    },
                    {
                        label: '检查',
                        value: TreatmentTypeEnum.INSPECTION,
                        disabled: !!this.searchKeyword,
                    },
                    {
                        label: '会诊',
                        value: TreatmentTypeEnum.CONSULTATION,
                        disabled: !!this.searchKeyword,
                    },
                ];
            },
            checkedList() {
                return this.mpTaskList
                    .flatMap((x) => {
                        x.timeGroupViews.forEach((item) => {
                            item.diagnosisTreatmentType = x.diagnosisTreatmentType;
                            item.checkedTime = x.checkedTime;
                        });
                        return x.timeGroupViews;
                    })
                    .filter((y) => y.checked)
                    .flatMap((z) => {
                        z.adviceExecutes.forEach((item) => {
                            item.diagnosisTreatmentType = z.diagnosisTreatmentType;
                            item.checkedTime = z.checkedTime;
                        });
                        return z.adviceExecutes;
                    });
            },
            toExecuteList() {
                return this.checkedList.filter((x) => (
                    x.status === MedicalAdviceStatusEnum.CHECKED ||
                    x.status === MedicalAdviceStatusEnum.UNDONE
                ));
            },
            toRevokeExeList() {
                return this.checkedList.filter((x) => x.status === MedicalAdviceStatusEnum.EXECUTED);
            },
            searchInputWidth() {
                const clientWidth = document.documentElement.clientWidth || document.body.clientWidth;
                return clientWidth > 1440 ? 180 : 120;
            },
            printCount() {
                return this.checkedList.filter((it) => {
                    return it.printContentSummary.reduce((acc, cur) => {
                        return !cur.isPrinted ? acc + 1 : acc;
                    }, 0) > 0;
                }).length;
            },
            // 出院结算中
            waitSettleStatus() {
                return this.inPatientDetailInfo?.status === HospitalStatusEnum.WAIT_SETTLE;
            },
            // 已出院
            dischargeStatus() {
                return this.inPatientDetailInfo?.status === HospitalStatusEnum.DISCHARGE;
            },
            isWaitSettleOrDischargeStatus () {
                return this.waitSettleStatus || this.dischargeStatus;
            },
            isWaitSettleOrDischargeToolTip() {
                return '已出院，不可更改医嘱';
            },
        },
        watch: {
            filterParams: {
                handler() {
                    this.fetchData();
                },
                deep: true,
            },
            searchKeyword() {
                this._debounceSearch();
            },
            patientOrderIds: {
                handler() {
                    this.fetchData(this.isPageLoading);
                    this.fetchExecutesDailyCount();
                },
                deep: true,
                immediate: true,
            },
        },
        created() {
            this._debounceSearch = debounce(async () => {
                this.fetchData();
            }, 250, true);

            this._debounceFetch = debounce(async () => {
                this.fetchData();
                this.fetchExecutesDailyCount();
            }, 250, true);

            const adviceExecuteServiceCallback = {
                onUpdate: this.handleAdviceExecuteUpdate,
            };
            this.$abcPage.AdviceExecuteService?.addCallback(adviceExecuteServiceCallback);
            this.$on('hook:beforeDestroy', () => {
                this.$abcPage.AdviceExecuteService?.removeCallback(adviceExecuteServiceCallback);
            });

            this.$abcEventBus.$on('advice-click', (item) => {
                this.handleAdviceClick(item);
            }, this);
        },
        methods: {
            async fetchData(isPageLoading = false) {
                try {
                    if (isPageLoading) {
                        this.$emit('update:pageLoading',true);
                    } else {
                        this.contentLoading = true;
                    }
                    if (this.patientOrderIds.length === 0) {
                        this.mpTaskList = [];
                        this.$emit('todoCount', {
                            showCheckedAdviceExecuteCount: 0,
                            showInitAdviceCount: 0,
                            showWaitingStopOrUndoAdviceCount: 0,
                        });
                        return;
                    }

                    const { data } = await MedicalPrescriptionAPI.getTaskList({
                        ...this.filterParams,
                        keyword: this.searchKeyword,
                        patientOrderIds: this.patientOrderIds,
                    });
                    const {
                        rows,
                        showCheckedAdviceExecuteCount,
                        showInitAdviceCount,
                        showWaitingStopOrUndoAdviceCount,
                    } = data;
                    this.mpTaskList = rows.map((item) => {
                        item.timeGroupViews = item.timeGroupViews.map((it) => {
                            it.checked = false;
                            return it;
                        });
                        return item;
                    });
                    this.$emit('todoCount', {
                        showCheckedAdviceExecuteCount,
                        showInitAdviceCount,
                        showWaitingStopOrUndoAdviceCount,
                    });
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                    this.$emit('update:pageLoading',false);
                }
            },
            async fetchExecutesDailyCount() {
                const rows = await MedicalPrescriptionAPI.fetchExecutesDailyCount({
                    patientOrderIds: this.patientOrderIds,
                    status: MedicalAdviceStatusEnum.CHECKED,
                    endDate: parseTime(prevDate(new Date()), 'y-m-d', true),
                });
                this.dateDescribeList = rows.map((it) => {
                    return {
                        ...it,
                        describe: '待执行',
                        describeClass: 'nurse-execute-date-warn',
                    };
                });
            },
            async updateData() {
                try {
                    this.contentLoading = true;
                    if (this.patientOrderIds.length === 0) {
                        this.mpTaskList = [];
                        this.$emit('todoCount', {
                            showCheckedAdviceExecuteCount: 0,
                            showInitAdviceCount: 0,
                        });
                        return;
                    }

                    const { data } = await MedicalPrescriptionAPI.getTaskList({
                        ...this.filterParams,
                        keyword: this.searchKeyword,
                        patientOrderIds: this.patientOrderIds,
                    });

                    const {
                        rows,
                        showCheckedAdviceExecuteCount,
                        showInitAdviceCount,
                    } = data;

                    // 保留选中状态
                    const { checkedList } = this;
                    this.mpTaskList = rows.map((item) => {
                        item.timeGroupViews = item.timeGroupViews.map((it) => {
                            it.checked = checkedList.some((c) => it.adviceExecutes.some((a) => a.id === c.id));
                            return it;
                        });
                        return item;
                    });

                    this.$emit('todoCount', {
                        showCheckedAdviceExecuteCount,
                        showInitAdviceCount,
                    });
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
            onChangeTab(val) {
                const { usage: usageType } = MedicineConfig;
                if (!val) {
                    this.filterParams.usages = undefined;
                    this.filterParams.diagnosisTreatmentType = undefined;
                    return;
                }
                if (val === '口服') {
                    const kfUsage = usageType.filter((x) => x.type === 1)
                        .map((x) => x.name);
                    this.filterParams.usages = kfUsage;
                    this.filterParams.diagnosisTreatmentType = undefined;
                } else if (val === '输注') {
                    const szUsage = usageType.filter((x) => x.type === 2)
                        .map((x) => x.name);
                    this.filterParams.usages = szUsage;
                    this.filterParams.diagnosisTreatmentType = undefined;
                } else if (val === '中药') {
                    const { usages = [] } = this.chineseMedicineConfig || {};
                    const zyUsage = usages.map((x) => x.name);
                    this.filterParams.usages = zyUsage;
                    this.filterParams.diagnosisTreatmentType = undefined;
                } else {
                    this.filterParams.usages = undefined;
                    this.filterParams.diagnosisTreatmentType = val;
                }
            },
            openAddMedicalPrescriptionDialog() {
                this.isShowAddMedicalPrescriptionDialog = true;
            },
            handleExecute() {
                this.showExecuteDialog = true;
            },
            onRevokeExeAdvices() {
                this.isShowRevokeMedicalPrescriptionDialog = true;
            },
            onChangeFilterStatus() {
                if (this.onlyNotExecute) {
                    this.filterParams.executeStatusList = [MedicalAdviceStatusEnum.CHECKED];
                } else {
                    this.filterParams.executeStatusList = [];
                }

                if (this.onlyNotPrint) {
                    this.filterParams.printStatusList = [MedicalAdvicePrintStatusEnum.NOT_PRINTED, MedicalAdvicePrintStatusEnum.PART_PRINTED];
                } else {
                    this.filterParams.printStatusList = [];
                }
            },
            // 消息更新列表
            handleAdviceExecuteUpdate(socketData) {
                const { data } = socketData;
                const {
                    type, patientOrderIds,
                } = data || {};
                const set = new Set(this.patientOrderIds);
                if (type === AdviceSocketTypeEnum.ADVICE_EXECUTE && patientOrderIds?.some((x) => set.has(x))) {
                    this._debounceFetch();
                }
            },

            handleAdviceClick({ adviceId }) {
                this.selectedAdviceId = adviceId;
                this.isShowDetailPopover = true;
            },
            handleCloseDetail() {
                this.isShowDetailPopover = false;
                this.selectedAdviceId = '';
                const { handleClearSelected } = this.$refs.neTable || {};
                handleClearSelected && handleClearSelected();
            },
            handleClickPrintNursePrescription() {
                this.isShowPrescriptionPrintDialog = true;
            },
            handlePrintSuccess() {
                this.updateData();
            },

            handleSuccess() {
                this._debounceFetch();
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
