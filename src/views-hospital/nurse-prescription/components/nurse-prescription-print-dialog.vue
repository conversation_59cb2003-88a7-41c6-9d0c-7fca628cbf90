<template>
    <abc-dialog
        v-model="showDialog"
        title="打印"
        append-to-body
        content-styles="width: 760px;"
        custom-class="nurse-prescription-print-dialog"
    >
        <div class="print-form">
            <div class="print-form-title">
                医嘱执行单
            </div>
            <div class="print-card-wrapper">
                <div class="print-form-card">
                    <div class="card-title">
                        执行单
                    </div>
                    <div class="card-content">
                        <div class="card-item">
                            长期医嘱
                            <abc-button
                                data-cy="abc-button-执行单-长期医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(zxLongTimeMpList)"
                                :disabled="!getPrintCount(zxLongTimeMpList)"
                                @click="handlePrintMp(zxLongTimeMpList, '长期医嘱执行单', MedicalPrintContentTypeEnum.EXECUTION)"
                            >
                                打印
                            </abc-button>
                        </div>

                        <div class="card-item">
                            临时医嘱
                            <abc-button
                                data-cy="abc-button-执行单-临时医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(zxOneTimeMpList)"
                                :disabled="!getPrintCount(zxOneTimeMpList)"
                                @click="handlePrintMp(zxOneTimeMpList, '临时医嘱执行单', MedicalPrintContentTypeEnum.EXECUTION)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>

                <div class="print-form-card">
                    <div class="card-title">
                        输液注射单
                    </div>
                    <div class="card-content">
                        <div class="card-item">
                            长期医嘱
                            <abc-button
                                data-cy="abc-button-输液注射单-长期医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(szLongTimeMpList)"
                                :disabled="!getPrintCount(szLongTimeMpList)"
                                @click="handlePrintMp(szLongTimeMpList, '长期医嘱执行单(输液注射)', MedicalPrintContentTypeEnum.INFUSION)"
                            >
                                打印
                            </abc-button>
                        </div>

                        <div class="card-item">
                            临时医嘱
                            <abc-button
                                data-cy="abc-button-输液注射单-临时医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(szOneTimeMpList)"
                                :disabled="!getPrintCount(szOneTimeMpList)"
                                @click="handlePrintMp(szOneTimeMpList, '临时医嘱执行单(输液注射)', MedicalPrintContentTypeEnum.INFUSION)"
                            >
                                打印
                            </abc-button>
                        </div>

                        <div class="card-item">
                            输液记录
                            <abc-button
                                data-cy="abc-button-输液记录"
                                size="small"
                                type="blank"
                                :count="getPrintCount(sjTimeMpList)"
                                :disabled="!getPrintCount(sjTimeMpList)"
                                @click="handlePrintMp(sjTimeMpList, '输液记录单', MedicalPrintContentTypeEnum.INFUSION_RECORD)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>

                <div class="print-form-card">
                    <div class="card-title">
                        治疗护理单
                    </div>
                    <div class="card-content">
                        <div class="card-item">
                            长期医嘱
                            <abc-button
                                data-cy="abc-button-治疗护理单-长期医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(zlLongTimeMpList)"
                                :disabled="!getPrintCount(zlLongTimeMpList)"
                                @click="handlePrintMp(zlLongTimeMpList, '长期医嘱执行单(治疗护理)', MedicalPrintContentTypeEnum.TREATMENT_NURSE)"
                            >
                                打印
                            </abc-button>
                        </div>

                        <div class="card-item">
                            临时医嘱
                            <abc-button
                                data-cy="abc-button-治疗护理单-临时医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(zlOneTimeMpList)"
                                :disabled="!getPrintCount(zlOneTimeMpList)"
                                @click="handlePrintMp(zlOneTimeMpList, '临时医嘱执行单(治疗护理)', MedicalPrintContentTypeEnum.TREATMENT_NURSE)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>

                <div class="print-form-card">
                    <div class="card-title">
                        口服药单
                    </div>
                    <div class="card-content">
                        <div class="card-item">
                            长期医嘱
                            <abc-button
                                data-cy="abc-button-口服药单-长期医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(kfLongTimeMpList)"
                                :disabled="!getPrintCount(kfLongTimeMpList)"
                                @click="handlePrintMp(kfLongTimeMpList, '长期医嘱执行单(口服药)', MedicalPrintContentTypeEnum.ORAL_MEDICINE)"
                            >
                                打印
                            </abc-button>
                        </div>

                        <div class="card-item">
                            临时医嘱
                            <abc-button
                                data-cy="abc-button-口服药单-临时医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(kfOneTimeMpList)"
                                :disabled="!getPrintCount(kfOneTimeMpList)"
                                @click="handlePrintMp(kfOneTimeMpList, '临时医嘱执行单(口服药)', MedicalPrintContentTypeEnum.ORAL_MEDICINE)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="print-form single-line-card">
            <div class="print-form-title">
                检查检验单
            </div>
            <div class="print-card-wrapper">
                <div class="print-form-card">
                    <div class="card-content">
                        <div class="card-item">
                            检查申请
                            <abc-button
                                data-cy="abc-button-检查申请单"
                                size="small"
                                type="blank"
                                :count="getPrintCount(jcTimeMpList)"
                                :disabled="!getPrintCount(jcTimeMpList)"
                                @click="handlePrintMp(jcTimeMpList, '检查申请单', MedicalPrintContentTypeEnum.INSPECTION_APPLY_SHEET)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>

                <div class="print-form-card">
                    <div class="card-content">
                        <div class="card-item">
                            检验申请
                            <abc-button
                                data-cy="abc-button-检验申请单"
                                size="small"
                                type="blank"
                                :count="getPrintCount(jyTimeMpList)"
                                :disabled="!getPrintCount(jyTimeMpList)"
                                @click="handlePrintMp(jyTimeMpList, '检验申请单', MedicalPrintContentTypeEnum.ASSAY_APPLY_SHEET)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>

                <div class="print-form-card">
                    <div class="card-content">
                        <div class="card-item">
                            样本条码
                            <abc-button
                                data-cy="abc-button-样本条码"
                                size="small"
                                type="blank"
                                :count="getPrintCount(tmTimeMpList)"
                                :disabled="!getPrintCount(tmTimeMpList)"
                                @click="handlePrintMp(tmTimeMpList, '样本条码', MedicalPrintContentTypeEnum.SAMPLE_NO)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用药标签 -->
        <div class="print-form">
            <div class="print-form-title">
                用药标签
            </div>
            <div class="print-card-wrapper">
                <div class="print-form-card">
                    <div class="card-content">
                        <div class="card-item">
                            全部药签
                            <abc-button
                                data-cy="abc-button-执行单-长期医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(ptTimeMpList)"
                                :disabled="!getPrintCount(ptTimeMpList)"
                                @click="handlePrintMp(ptTimeMpList, '用药标签', MedicalPrintContentTypeEnum.BOTTLE_LABEL)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>

                <div class="print-form-card">
                    <div class="card-content">
                        <div class="card-item">
                            口服药签
                            <abc-button
                                data-cy="abc-button-输液注射单-长期医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(kfptTimeMpList)"
                                :disabled="!getPrintCount(kfptTimeMpList)"
                                @click="handlePrintMp(kfptTimeMpList, '用药标签', MedicalPrintContentTypeEnum.BOTTLE_LABEL)"
                            >
                                打印
                            </abc-button>
                        </div>

                        <div class="card-item">
                            雾化药签
                            <abc-button
                                data-cy="abc-button-输液注射单-临时医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(whptTimeMpList)"
                                :disabled="!getPrintCount(whptTimeMpList)"
                                @click="handlePrintMp(whptTimeMpList, '用药标签', MedicalPrintContentTypeEnum.BOTTLE_LABEL)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>

                <div class="print-form-card">
                    <div class="card-content">
                        <div class="card-item">
                            输液药签
                            <abc-button
                                data-cy="abc-button-治疗护理单-长期医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(syptTimeMpList)"
                                :disabled="!getPrintCount(syptTimeMpList)"
                                @click="handlePrintMp(syptTimeMpList, '用药标签', MedicalPrintContentTypeEnum.BOTTLE_LABEL)"
                            >
                                打印
                            </abc-button>
                        </div>

                        <div class="card-item">
                            外用药签
                            <abc-button
                                data-cy="abc-button-治疗护理单-临时医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(wyptTimeMpList)"
                                :disabled="!getPrintCount(wyptTimeMpList)"
                                @click="handlePrintMp(wyptTimeMpList, '用药标签', MedicalPrintContentTypeEnum.BOTTLE_LABEL)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>

                <div class="print-form-card">
                    <div class="card-content">
                        <div class="card-item">
                            注射药签
                            <abc-button
                                data-cy="abc-button-口服药单-长期医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(zsptTimeMpList)"
                                :disabled="!getPrintCount(zsptTimeMpList)"
                                @click="handlePrintMp(zsptTimeMpList, '用药标签', MedicalPrintContentTypeEnum.BOTTLE_LABEL)"
                            >
                                打印
                            </abc-button>
                        </div>

                        <div class="card-item">
                            中药药签
                            <abc-button
                                data-cy="abc-button-口服药单-临时医嘱"
                                size="small"
                                type="blank"
                                :count="getPrintCount(zyptTimeMpList)"
                                :disabled="!getPrintCount(zyptTimeMpList)"
                                @click="handlePrintMp(zyptTimeMpList, '用药标签', MedicalPrintContentTypeEnum.BOTTLE_LABEL)"
                            >
                                打印
                            </abc-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <abc-flex slot="footer" align="center" justify="space-between">
            <abc-button variant="ghost" icon="n-settings-line" @click="openPrintSettingDialog">
                设置
            </abc-button>
            <abc-button variant="ghost" @click="showDialog = false">
                关闭
            </abc-button>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    import {
        AdviceRuleType,
        MedicalAdviceTypeEnum, MedicalPrintContentTypeEnum, TreatmentTypeEnum,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import {
        GoodsTypeEnum, GoodsSubTypeEnum,
    } from '@abc/constants';
    import AbcPrinter from '@/printer/index.js';
    import {
        ABCPrintConfigKeyMap, PrintMode,
    } from '@/printer/constants.js';
    import {
        mapGetters, mapState,
    } from 'vuex';
    import Clone from 'utils/clone.js';
    import PrintAPI from '@/api/hospital/print';
    import { isLocal } from '@/assets/configure/build-env.js';
    import { MEDICINE_USAGE } from 'utils/constants';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');

    export default {
        name: 'NursePrescriptionPrintDialog',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            mpList: {
                type: Array,
                required: true,
                default: () => [],
            },
            printCount: {
                type: Number,
                default: 0,
            },
        },
        data() {
            return {
                btnLoading: false,
                MedicalPrintContentTypeEnum,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'userInfo',
            ]),
            ...mapState('hospitalGlobal', [
                'currentWardAreaName',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            checkedMpList() {
                return this.filterListByChecked(this.mpList);
            },
            oneTimeMpList() {
                return this.checkedMpList.filter((item) => {
                    return item.type === MedicalAdviceTypeEnum.ONE_TIME;
                });
            },
            // 临时医嘱 + 出院带药
            oneTimeAndDischargeWidthMedicineMpList() {
                return this.checkedMpList.filter((item) => {
                    return item.type === MedicalAdviceTypeEnum.ONE_TIME || item.type === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE;
                });
            },
            longTimeMpList() {
                return this.checkedMpList.filter((item) => {
                    return item.type === MedicalAdviceTypeEnum.LONG_TIME;
                });
            },
            zxLongTimeMpList() {
                return this.filterListPrintContentType(this.longTimeMpList, MedicalPrintContentTypeEnum.EXECUTION);
            },
            zxOneTimeMpList() {
                return this.filterListPrintContentType(this.oneTimeAndDischargeWidthMedicineMpList, MedicalPrintContentTypeEnum.EXECUTION);
            },
            szLongTimeMpList() {
                return this.filterListPrintContentType(this.longTimeMpList, MedicalPrintContentTypeEnum.INFUSION);
            },
            szOneTimeMpList() {
                return this.filterListPrintContentType(this.oneTimeMpList, MedicalPrintContentTypeEnum.INFUSION);
            },
            zlLongTimeMpList() {
                return this.filterListPrintContentType(this.longTimeMpList, MedicalPrintContentTypeEnum.TREATMENT_NURSE);
            },
            zlOneTimeMpList() {
                return this.filterListPrintContentType(this.oneTimeMpList, MedicalPrintContentTypeEnum.TREATMENT_NURSE);
            },
            kfLongTimeMpList() {
                return this.filterListPrintContentType(this.longTimeMpList,MedicalPrintContentTypeEnum.ORAL_MEDICINE);
            },
            kfOneTimeMpList() {
                return this.filterListPrintContentType(this.oneTimeMpList,MedicalPrintContentTypeEnum.ORAL_MEDICINE);
            },
            jcTimeMpList() {
                return this.filterListPrintContentType(this.oneTimeMpList,MedicalPrintContentTypeEnum.INSPECTION_APPLY_SHEET);
            },
            jyTimeMpList() {
                return this.filterListPrintContentType(this.oneTimeMpList,MedicalPrintContentTypeEnum.ASSAY_APPLY_SHEET);
            },
            tmTimeMpList() {
                return this.filterListPrintContentType(this.oneTimeMpList,MedicalPrintContentTypeEnum.SAMPLE_NO);
            },
            // 输注记录单
            sjTimeMpList() {
                return this.checkedMpList.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        timeGroup.adviceExecutes = timeGroup.adviceExecutes.filter((adviceExecute) => {
                            return ['静脉滴注', '直肠滴注', '入壶静滴', '输液冲管', '鼻饲', '膀胱给药'].includes(adviceExecute.usage);
                        });
                        return timeGroup.adviceExecutes.length > 0;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
            // 瓶贴
            ptTimeMpList() {
                return Clone(this.checkedMpList).filter((item) => {
                    return item.diagnosisTreatmentType === TreatmentTypeEnum.MEDICINE;
                });
            },
            // 口服瓶贴
            kfptTimeMpList() {
                return this.ptTimeMpList.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        timeGroup.adviceExecutes = timeGroup.adviceExecutes.filter((adviceExecute) => {
                            return MEDICINE_USAGE.oral.includes(adviceExecute.usage);
                        });
                        return timeGroup.adviceExecutes.length > 0;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
            // 雾化瓶贴
            whptTimeMpList() {
                return this.ptTimeMpList.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        timeGroup.adviceExecutes = timeGroup.adviceExecutes.filter((adviceExecute) => {
                            return MEDICINE_USAGE.atomization.includes(adviceExecute.usage);
                        });
                        return timeGroup.adviceExecutes.length > 0;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
            // 输液瓶贴
            syptTimeMpList() {
                return this.ptTimeMpList.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        timeGroup.adviceExecutes = timeGroup.adviceExecutes.filter((adviceExecute) => {
                            return MEDICINE_USAGE.infusion.includes(adviceExecute.usage);
                        });
                        return timeGroup.adviceExecutes.length > 0;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
            // 外用瓶贴
            wyptTimeMpList() {
                return this.ptTimeMpList.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        timeGroup.adviceExecutes = timeGroup.adviceExecutes.filter((adviceExecute) => {
                            return MEDICINE_USAGE.external.includes(adviceExecute.usage);
                        });
                        return timeGroup.adviceExecutes.length > 0;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
            // 注射瓶贴
            zsptTimeMpList() {
                return this.ptTimeMpList.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        timeGroup.adviceExecutes = timeGroup.adviceExecutes.filter((adviceExecute) => {
                            return MEDICINE_USAGE.injections.includes(adviceExecute.usage);
                        });
                        return timeGroup.adviceExecutes.length > 0;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
            // 煎服(中药)瓶贴
            zyptTimeMpList() {
                return this.ptTimeMpList.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        timeGroup.adviceExecutes = timeGroup.adviceExecutes.filter((adviceExecute) => {
                            return [AdviceRuleType.CHINESE_MEDICINE_GRANULES, AdviceRuleType.CHINESE_MEDICINE_TABLETS].includes(adviceExecute.type);
                        });
                        return timeGroup.adviceExecutes.length > 0;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
        },
        methods: {
            async openPrintSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'hospital-nurse-advice-execute' }).generateDialogAsync({ parent: this });
            },
            filterListPrintContentType(list, PrintContentType) {
                return list.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        timeGroup.adviceExecutes = timeGroup.adviceExecutes.filter((adviceExecute) => {
                            return adviceExecute.printContentSummary.some((printContent) => {
                                return printContent.type === PrintContentType;
                            });
                        });
                        return timeGroup.adviceExecutes.length > 0;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
            filterListByChecked(list) {
                return list.map(Clone).filter((item) => {
                    item.timeGroupViews = item.timeGroupViews.filter((timeGroup) => {
                        return timeGroup.checked;
                    });
                    return item.timeGroupViews.length > 0;
                });
            },
            async handlePrintMp(list, formName, contentType) {
                let printConfig = {
                    templateKey: '',
                    printConfigKey: '',
                    data: {},
                };

                switch (contentType) {
                    case MedicalPrintContentTypeEnum.EXECUTION:
                    case MedicalPrintContentTypeEnum.INFUSION:
                    case MedicalPrintContentTypeEnum.ORAL_MEDICINE:
                    case MedicalPrintContentTypeEnum.TREATMENT_NURSE:
                        printConfig = {
                            templateKey: window.AbcPackages.AbcTemplates?.hospitalNursePrescription,
                            printConfigKey: ABCPrintConfigKeyMap.hospitalNursePrescription,
                            data: {
                                userInfo: {
                                    name: this.userInfo.name || '',
                                    handSign: this.userInfo.handSign || '',
                                },
                                clinicName: this.currentClinic.clinicName,
                                formName,
                                tableData: list,
                                currentWardAreaName: this.currentWardAreaName,
                            },
                            mode: isLocal ? PrintMode.Lodop : PrintMode.Electron,
                            extra: {
                                forceMultiPage: true,
                            },
                        };
                        break;
                    case MedicalPrintContentTypeEnum.ASSAY_APPLY_SHEET:
                        printConfig = {
                            templateKey: window.AbcPackages.AbcTemplates?.examinationApplySheet,
                            printConfigKey: ABCPrintConfigKeyMap.examinationInspectApplySheet,
                            data: {
                                isInHospital: true,
                                rows: this.getExaminationApplySheetData(list),
                            },
                        };
                        break;
                    case MedicalPrintContentTypeEnum.INSPECTION_APPLY_SHEET:
                        printConfig = {
                            templateKey: window.AbcPackages.AbcTemplates?.examinationApplySheet,
                            printConfigKey: ABCPrintConfigKeyMap.examineApplySheet,
                            data: {
                                isInHospital: true,
                                rows: this.getExaminationApplySheetData(list),
                            },
                        };
                        break;
                    case MedicalPrintContentTypeEnum.SAMPLE_NO:
                        printConfig = {
                            templateKey: window.AbcPackages.AbcTemplates?.examinationTag,
                            printConfigKey: ABCPrintConfigKeyMap.hospitalExaminationTag,
                            data: {
                                rows: list.reduce((res, item) => {
                                    const patient = item.patientOrderHospital?.patient;
                                    let simpleExamSheets = item.timeGroupViews[0]?.adviceExecutes[0]?.simpleExamSheets || [];
                                    simpleExamSheets = simpleExamSheets.map((simpleExamSheet) => {
                                        simpleExamSheet.patient = patient;
                                        return simpleExamSheet;
                                    });
                                    return res.concat(simpleExamSheets);
                                }, []).map((simpleExamSheet, i) => {
                                    // 样本条码
                                    return {
                                        id: i + 1,
                                        orderNo: simpleExamSheet?.orderNo,
                                        samplePipe: simpleExamSheet?.samplePipe,
                                        sampleType: simpleExamSheet?.sampleType,
                                        patient: simpleExamSheet.patient,
                                        examinationName: simpleExamSheet.examinationName,
                                    };
                                }).reduce((res, item) => {
                                    // 医嘱存在多条，检验项目可能是合并的，所以这里需要去重
                                    if (res.findIndex((r) => r.orderNo === item.orderNo) === -1) {
                                        res.push(item);
                                    }
                                    return res;
                                }, []),
                            },
                        };
                        break;
                    case MedicalPrintContentTypeEnum.INFUSION_RECORD:
                        printConfig = {
                            templateKey: window.AbcPackages.AbcTemplates?.hospitalInfusionRecord,
                            printConfigKey: ABCPrintConfigKeyMap.hospitalPrescription,
                            data: {
                                clinicName: this.currentClinic.clinicName,
                                formName,
                                tableData: list,
                                currentWardAreaName: this.currentWardAreaName,
                            },
                            mode: PrintMode.Electron,
                        };
                        break;
                    case MedicalPrintContentTypeEnum.BOTTLE_LABEL:
                        printConfig = await this.printHospitalMedicineTag(list);
                        break;
                    default:
                        break;
                }

                if (!printConfig) {
                    this.$Toast.error('没有打印数据');
                    return;
                }
                AbcPrinter.abcPrint({
                    ...printConfig,
                    onPrintSuccess: async () => {
                        // 瓶贴不用上报
                        if (contentType === MedicalPrintContentTypeEnum.BOTTLE_LABEL) return;
                        try {
                            await PrintAPI.updatePrintedFlag({
                                printContentType: contentType,
                                adviceExecuteIds: list.reduce((total, item) => {
                                    return total.concat(item.timeGroupViews.reduce((adviceExecuteIds, timeGroup) => {
                                        return adviceExecuteIds.concat(timeGroup.adviceExecutes.map((adviceExecute) => {
                                            return adviceExecute.id;
                                        }));
                                    }, []));
                                }, []),
                            });
                            this.$emit('print-success');
                        } catch (e) {
                            console.error(e);
                        }
                    },
                });
            },
            getPrintCount(list) {
                return list.reduce((total, item) => {
                    return total + item.timeGroupViews.reduce((adviceCount, timeGroup) => {
                        return adviceCount + timeGroup.adviceExecutes.length;
                    }, 0);
                }, 0);
            },
            async printHospitalMedicineTag(list) {
                let ids = [];
                list.forEach((it) => {
                    (it.timeGroupViews || []).forEach((item) => {
                        ids = ids.concat((item.adviceExecutes || []).map((ele) => ele.id));
                    });
                });
                const { data } = await this.getHospitalMedicineTagData(ids);
                if (!data) return null;
                return {
                    templateKey: window.AbcPackages.AbcTemplates?.hospitalMedicineTag,
                    printConfigKey: ABCPrintConfigKeyMap.hospitalMedicineTag,
                    data: {
                        forms: data,
                        clinicName: this.currentClinic.clinicName,
                        isManualPreview: true,
                    },
                    mode: PrintMode.Electron,
                };
            },
            async getHospitalMedicineTagData(ids) {
                try {
                    return PrintAPI.fetchHospitalMedicineTagData({
                        type: 1, ids,
                    });
                } catch (e) {
                    console.error(e);
                    return null;
                }
            },
            getExaminationApplySheetData(list) {
                const applyList = list.map((l) => ({
                    // 机构信息
                    organPrintView: {
                        name: this.currentClinic.name,
                        qrUrl: '',
                    },
                    id: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.id,
                    no: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.no,
                    type: l.diagnosisTreatmentType === TreatmentTypeEnum.ASSAY ?
                        GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect : GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test,
                    patient: l?.patientOrderHospital?.patient,
                    healthCardPayLevel: l?.patientOrderHospital?.feeTypeName,
                    healthCardNo: '',
                    bedNo: l.bedNo,
                    wardName: l.patientOrderHospital?.wardName,
                    departmentName: l.patientOrderHospital?.departmentName,
                    created: l.created,
                    diagnosisInfos: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.diagnosisInfos,
                    inpatientNo: l.patientOrderHospital?.no,
                    businessFormItems: [
                        {
                            id: l?.timeGroupViews[0]?.adviceExecutes[0]?.firstRuleItem?.goodsId,
                            name: l?.timeGroupViews[0]?.adviceExecutes[0].name,
                            unit: l?.timeGroupViews[0]?.adviceExecutes[0].singleDosageUnit,
                            unitCount: l?.timeGroupViews[0]?.adviceExecutes[0].singleDosageCount,
                            unitPrice: l?.timeGroupViews[0]?.adviceExecutes[0]?.firstRuleItem?.unitPrice,
                        },
                    ],
                    chiefComplaint: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.chiefComplaint,
                    presentHistory: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.presentHistory,
                    physicalExamination: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.physicalExamination,
                    purpose: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.purpose,
                    doctorName: l.createdByName,
                    deviceType: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.deviceType,
                    subType: l?.timeGroupViews[0]?.adviceExecutes[0]?.examApplySheet?.subType,
                }));

                // 合并同一张申请单上的项目
                return applyList.reduce((res, item) => {
                    const idx = res.findIndex((r) => r.id === item.id);
                    if (idx !== -1) {
                        res[idx].businessFormItems.push(...(item.businessFormItems));
                    } else {
                        res.push(item);
                    }

                    return res;
                }, []);
            },
        },
    };
</script>

<style lang="scss">
@import "~styles/theme.scss";

.nurse-prescription-print-dialog {
    .print-form-title {
        font-size: 12px;
        line-height: 16px;
        color: $T2;
    }

    .print-card-wrapper {
        display: flex;
        padding: 12px;
        margin-top: 6px;
        background: #f5f7fb;
    }

    .print-form:not(:first-child) {
        margin-top: 24px;
    }

    .single-line-card .card-item {
        margin-top: 0 !important;
    }

    .print-form-card {
        width: 136px;

        &:not(:first-child) {
            margin-left: 48px;
        }

        .card-title {
            font-size: 12px;
            line-height: 1;
            color: $T2;
        }

        .card-content {
            .card-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 12px;

                .abc-button {
                    min-width: 70px;
                    white-space: nowrap;
                }
            }
        }
    }
}
</style>

