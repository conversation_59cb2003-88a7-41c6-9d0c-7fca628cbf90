<template>
    <div v-abc-click-outside="blur" class="hospital-bed-select">
        <abc-popover
            v-model="isShowSelect"
            width="348px"
            placement="bottom-start"
            trigger="manual"
            theme="custom"
            style="height: 100%;"
            :disabled="disabled"
        >
            <abc-input
                slot="reference"
                v-model="currentValue"
                :readonly="true"
                style="width: 100%;"
                :disabled="disabled"
                @focus="focus"
                @enter="enter"
                @up="enter"
                @down="enter"
                @left="enter"
                @right="enter"
            >
                <span v-if="!isBed" slot="appendInner">个</span>
            </abc-input>
            <div class="hospital-bed-select-box">
                <div class="hospital-bed-select-box-title" @click.stop.prevent="changeFocus">
                    <abc-tabs
                        ref="tabs"
                        v-model="current"
                        size="middle"
                        :option="tabsOption"
                    ></abc-tabs>
                </div>
                <div class="hospital-bed-select-box-body">
                    <div
                        v-for="(item,num) in selectOptionsHandle"
                        :key="num"
                        class="hospital-bed-select-box-body-button"
                        :class="{ 'hospital-bed-select-box-body-button-disabled': item.disabled }"
                        @click.stop.prevent="selectItem(item)"
                    >
                        {{ item.label }}
                    </div>
                </div>
                <div class="hospital-bed-select-box-bottom">
                    <abc-icon
                        icon="delete_circle"
                        size="16"
                        color="#005ed9"
                        style="margin-right: 8px;"
                        @click="blur"
                    ></abc-icon>
                    关闭
                </div>
            </div>
        </abc-popover>
    </div>
</template>

<script>
    import { getTableOptionsByRange } from '../../utils';

    const bedTableOptions1 = getTableOptionsByRange(1, 40);
    const bedTableOptions2 = getTableOptionsByRange(41, 80);
    const bedTableOptions3 = getTableOptionsByRange(81, 120);
    const bedTableOptions4 = getTableOptionsByRange(121, 150);

    export default {
        name: 'BedSelect',
        props: {
            index: {
                type: Number,
                default: 0,
            },
            value: {
                type: [Number, String],
            },
            disabledList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            isBed: {
                type: Boolean,
                default: false,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                isShowSelect: false,
                current: 0,
            };
        },
        computed: {
            selectOptions() {
                if (!this.isBed) {
                    return getTableOptionsByRange(1, 40, false);
                }

                if (this.current === 1) {
                    return bedTableOptions2;
                }
                if (this.current === 2) {
                    return bedTableOptions3;
                }
                if (this.current === 3) {
                    return bedTableOptions4;
                }
                return bedTableOptions1;
            },
            tabsOption() {
                if (!this.isBed) {
                    return [
                        {
                            label: '1~40',
                            value: 0,
                        },
                    ];
                }
                return [
                    {
                        label: '01~40',
                        value: 0,
                    },
                    {
                        label: '41~80',
                        value: 1,
                    },
                    {
                        label: '81-120',
                        value: 2,
                    },
                    {
                        label: '121-150',
                        value: 3,
                    },
                ];
            },
            currentValue: {
                get() {
                    if (this.value < 10 && !!this.value && this.isBed) {
                        return `0${this.value}`;
                    }
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            selectOptionsHandle() {
                if (this.disabledList?.length) {
                    return this.selectOptions.map((item) => {
                        return {
                            ...item,
                            disabled: this.disabledList.includes(item.value),
                        };
                    });
                }
                return this.selectOptions;
            },
            selectOptionsCheck() {
                // 链接起来做检查
                const selectOptions = bedTableOptions1.concat(bedTableOptions2, bedTableOptions3, bedTableOptions4);
                if (this.disabledList?.length) {
                    return selectOptions.map((item) => {
                        return {
                            ...item,
                            disabled: this.disabledList.includes(item.value),
                        };
                    });
                }
                return selectOptions;
            },
        },
        methods: {
            enter(e) {
                this.isShowSelect = false;
                this.$emit('enter', e);
            },
            selectItem(item) {
                if (item.disabled) {
                    return false;
                }
                this.$emit('changeSelect', item.value, this.index);
            },
            focus() {
                if (this.isBed) {
                    if (this.selectOptionsCheck?.slice(0, 39).find((item) => {
                        return !item.disabled;
                    })) {
                        this.current = 0;
                    } else if (this.selectOptionsCheck?.slice(40, 79).find((item) => {
                        return !item.disabled;
                    })) {
                        this.current = 1;
                    } else if (this.selectOptionsCheck?.slice(80, 119).find((item) => {
                        return !item.disabled;
                    })) {
                        this.current = 2;
                    } else {
                        this.current = 3;
                    }
                } else {
                    this.current = 0;
                }
                this.isShowSelect = true;
            },
            changeFocus() {
                this.isShowSelect = true;
            },
            blur() {
                this.isShowSelect = false;
            },
        },
    };
</script>

<style lang="scss">
.hospital-bed-select {
    position: relative;
    z-index: 2 !important;
    height: 40px;

    .select-display {
        position: absolute;
        z-index: 100;
        height: 40px;
        padding: 0 10px;
        line-height: 40px;
    }

    input {
        text-align: center;
    }

    &-box {
        z-index: 9999;
        width: 348px;
        min-height: 54px;
        background: #ffffff;
        border: 1px solid #b7b9c2;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

        &-title {
            width: calc(100% - 24px);
            height: 40px;
            margin-left: 12px;
        }

        &-body {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            justify-content: flex-start;
            height: calc(100% - 40px);
            padding: 6px 12px;
            font-size: 14px;
            color: #7a8794;

            &-button {
                width: 32.2px;
                height: 26px;
                line-height: 26px;
                text-align: center;
                cursor: pointer;

                &:hover {
                    color: #0090ff;
                }

                &-disabled {
                    color: #0090ff;
                    cursor: not-allowed;
                    background: #eff3f6;
                    border-radius: 2px;
                }
            }
        }

        &-bottom {
            height: 40px;
            font-size: 14px;
            font-weight: bold;
            line-height: 40px;
            color: #005ed9;
            text-align: center;
            cursor: pointer;
            background: #f7f7f7;
        }
    }
}
</style>
