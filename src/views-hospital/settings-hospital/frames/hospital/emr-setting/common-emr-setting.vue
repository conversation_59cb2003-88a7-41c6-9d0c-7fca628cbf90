<template>
    <div class="common-emr-setting">
        <div class="common-emr-setting_content">
            <div class="common-emr-setting_templates-bar">
                <emr-editor-template-catalog
                    ref="catalog"
                    :selected-catalog-node.sync="currentSelectedCatalogNode"
                    :medical-document-list.sync="medicalDocumentList"
                    :business-type="businessType"
                ></emr-editor-template-catalog>
            </div>
            <div v-abc-loading="fetchLoading" class="common-emr-setting_main">
                <biz-setting-layout v-if="currentSelectedCatalogNode && !currentSelectedCatalogNode.isFolder">
                    <biz-setting-content>
                        <biz-setting-form :label-width="100">
                            <abc-form
                                ref="form"
                                label-position="left"
                                :label-width="132"
                            >
                                <div v-if="enableMedicalDocumentBaseSetting" class="common-emr-setting_vertical-group">
                                    <div class="common-emr-setting_vertical-left">
                                        <biz-setting-form-group title="文书设置">
                                            <biz-setting-form-item label="文书名称" label-line-height-size="medium">
                                                <abc-form-item required>
                                                    <abc-input
                                                        v-model="postData.name"
                                                        :width="256"
                                                        placeholder="请输入文书名称"
                                                    ></abc-input>
                                                </abc-form-item>
                                            </biz-setting-form-item>

                                            <biz-setting-form-item v-if="enableSharedPageDocument" label="文书页面">
                                                <abc-radio-group v-model="postData.medicalDocumentType">
                                                    <abc-tooltip
                                                        placement="top-start"
                                                        :disabled="!isDisableSetupMedicalDocument"
                                                        content="如需更改，请联系ABC工程人员"
                                                    >
                                                        <abc-radio
                                                            :label="MedicalDocumentTypeEnum.Self"
                                                            :disabled="isDisableSetupMedicalDocument"
                                                        >
                                                            独立页面
                                                        </abc-radio>
                                                    </abc-tooltip>

                                                    <abc-tooltip
                                                        placement="top-start"
                                                        :disabled="!isDisableSetupMedicalDocument && !isBanShare"
                                                        :content="isBanShare ? '护理记录单暂不支持共享' : '如需更改，请联系ABC工程人员'"
                                                    >
                                                        <abc-radio
                                                            :label="MedicalDocumentTypeEnum.Share"
                                                            :disabled="isDisableSetupMedicalDocument || isBanShare"
                                                        >
                                                            <abc-space :size="16" style="min-height: 28px;">
                                                                <span>共享页面</span>
                                                                <abc-form-item v-if="postData.medicalDocumentType === MedicalDocumentTypeEnum.Share" style="margin-bottom: 0;" required>
                                                                    <abc-select
                                                                        ref="sharePageSelect"
                                                                        v-model="postData.medicalDocumentSharedPageId"
                                                                        size="small"
                                                                        :width="160"
                                                                        setting
                                                                        @set="handleClickShareGroupDialog"
                                                                    >
                                                                        <abc-option
                                                                            v-for="sharedPage in sharedPageList"
                                                                            :key="sharedPage.id"
                                                                            :value="sharedPage.id"
                                                                            :label="sharedPage.name"
                                                                        >
                                                                            {{ sharedPage.name }}
                                                                        </abc-option>

                                                                        <abc-tooltip slot="setting-slot" placement="bottom" content="">
                                                                            <span style=" padding-left: 6px; margin-right: auto; font-size: 12px; color: #7a8794;">什么是共享页面组?</span>
                                                                            <template slot="content">
                                                                                <div style="width: 216px; font-size: 12px; line-height: 16px;">
                                                                                    设为同一共享页面组的文书，将在同一页面上查阅、续写、打印。
                                                                                </div>
                                                                            </template>
                                                                        </abc-tooltip>
                                                                    </abc-select>
                                                                </abc-form-item>
                                                            </abc-space>
                                                        </abc-radio>
                                                    </abc-tooltip>
                                                </abc-radio-group>
                                            </biz-setting-form-item>

                                            <biz-setting-form-item v-if="enableDepartmentSetup" label="可用科室">
                                                <abc-radio-group v-model="postData.medicalDocumentDepartmentAvailableType">
                                                    <abc-radio :label="MedicalDocumentAvailableDepartmentTypeEnum.All">
                                                        所有科室
                                                    </abc-radio>

                                                    <abc-radio :label="MedicalDocumentAvailableDepartmentTypeEnum.Department">
                                                        <abc-space :size="16" style="min-height: 24px;">
                                                            <span>指定科室</span>
                                                            <abc-button v-if="postData.medicalDocumentDepartmentAvailableType === MedicalDocumentAvailableDepartmentTypeEnum.Department" type="text" @click="handleClickAddDepartments">
                                                                添加
                                                            </abc-button>
                                                        </abc-space>
                                                    </abc-radio>
                                                </abc-radio-group>

                                                <div
                                                    v-if="
                                                        postData.medicalDocumentDepartmentAvailableType === MedicalDocumentAvailableDepartmentTypeEnum.Department &&
                                                            postData.medicalDocumentDepartments.length
                                                    "
                                                    class="common-emr-setting_department-tags"
                                                >
                                                    <abc-tag-group>
                                                        <abc-tag-v2
                                                            v-for="department in postData.medicalDocumentDepartments"
                                                            :key="department.id"
                                                            variant="outline"
                                                            icon="s-department-color"
                                                            closable
                                                            @close="handleRemoveDepartment(department)"
                                                        >
                                                            {{ department.name }}
                                                        </abc-tag-v2>
                                                    </abc-tag-group>
                                                </div>
                                            </biz-setting-form-item>
                                        </biz-setting-form-group>
                                    </div>
                                    <div class="common-emr-setting_vertical-right">
                                        <div class="common-emr-setting_master-page" :class="{ 'common-emr-setting_master-page-landscape': isCommonMedicalDocumentLandscape }">
                                            <div class="common-emr-setting_master-page-tip">
                                                <abc-space :size="4">
                                                    <span>母版</span>
                                                    <abc-tooltip placement="bottom">
                                                        <abc-icon icon="info" :size="12"></abc-icon>
                                                        <template slot="content">
                                                            <div style="width: 216px; font-size: 12px; line-height: 16px;">
                                                                母版用于设计文书的统一结构和样式，包括页眉、页脚、常用内容等。更新母版，所有相关模板内容自动更新。
                                                            </div>
                                                        </template>
                                                    </abc-tooltip>
                                                </abc-space>
                                            </div>
                                            <div class="common-emr-setting_master-page-handler">
                                                <abc-space>
                                                    <abc-button
                                                        :disabled="!commonMedicalDocumentContent"
                                                        type="blank"
                                                        :style="`width: ${isCommonMedicalDocumentLandscape ? 146 : 100}px;`"
                                                        @click="print"
                                                    >
                                                        打印
                                                    </abc-button>
                                                    <abc-tooltip placement="top-start" :disabled="!isDisableSetupMedicalDocument" content="如需更改，请联系ABC工程人员">
                                                        <abc-button
                                                            :style="`width: ${isCommonMedicalDocumentLandscape ? 146 : 100}px;`"
                                                            type="primary"
                                                            :disabled="isDisableSetupMedicalDocument"
                                                            @click="handleClickDesignCommonMedicalDocument"
                                                        >
                                                            设计母版
                                                        </abc-button>
                                                    </abc-tooltip>
                                                </abc-space>
                                            </div>
                                            <div class="common-emr-setting_master-page-preview-tip" @click="handleOpenPreview">
                                                <abc-icon icon="search" color="#fff"></abc-icon>
                                            </div>
                                            <div :key="currentSelectedCatalogNode.id" ref="previewEditorWrapper" class="common-emr-setting_master-page-content">
                                                <emr-editor
                                                    v-if="commonMedicalDocumentContent"
                                                    ref="previewEditor"
                                                    :editable="false"
                                                    :page-size="commonMedicalDocumentContent.templateSetting.pageSize"
                                                    :page-size-reduce="commonMedicalDocumentContent.templateSetting.pageSizeReduce"
                                                    :page-orientation="commonMedicalDocumentContent.templateSetting.pageOrientation"
                                                    :value="commonMedicalDocumentContent.templateContent"
                                                    @mountedEditor="handleMountedEditor"
                                                ></emr-editor>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <biz-setting-form-group v-if="enableMedicalDocumentSetting && !isDisableSetupMedicalDocument" title="模版设置">
                                    <div v-if="$slots.close" slot="close">
                                        <slot name="close"></slot>
                                    </div>
                                    <div class="common-emr-setting_table-header">
                                        <!--<abc-select :width="200">-->
                                        <!--    <abc-option value="1">-->
                                        <!--        所有科室-->
                                        <!--    </abc-option>-->
                                        <!--    <abc-option value="2">-->
                                        <!--        儿科-->
                                        <!--    </abc-option>-->
                                        <!--</abc-select>-->
                                        <abc-tooltip placement="top-start" :disabled="!isDisableSetupMedicalDocument" content="如需更改，请联系ABC工程人员">
                                            <abc-button
                                                v-if="enableMedicalDocumentBaseSetting"
                                                :disabled="isDisableSetupMedicalDocument"
                                                theme="success"
                                                @click="handleClickCreateMedicalDocument"
                                            >
                                                新增模版
                                            </abc-button>
                                        </abc-tooltip>
                                    </div>

                                    <div class="common-emr-setting_table-main-table">
                                        <abc-table
                                            :render-config="tableRenderConfig"
                                            :data-list="postData.medicalDocumentFileList"
                                            type="excel"
                                        >
                                            <template #name="{ trData: fileItem }">
                                                <abc-table-cell class="ellipsis" @click.native="handleUpdateMedicalDocument(fileItem)">
                                                    <abc-text theme="primary-light">
                                                        {{ fileItem.name }}
                                                    </abc-text>
                                                </abc-table-cell>
                                            </template>

                                            <template #templateType="{ trData: fileItem }">
                                                <abc-select v-model="fileItem.ownerType" :inner-width="160" adaptive-width>
                                                    <abc-option
                                                        v-for="ownerType in ownerTypeOptions"
                                                        :key="ownerType.value"
                                                        :value="ownerType.value"
                                                        :label="ownerType.label"
                                                    ></abc-option>
                                                </abc-select>
                                            </template>

                                            <template #department="{ trData: fileItem }">
                                                <abc-form-item required>
                                                    <abc-select
                                                        v-if="fileItem.ownerType === CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT"
                                                        v-model="fileItem.ownerId"
                                                        :inner-width="160"
                                                        multiple
                                                        multi-label-mode="tag"
                                                        :max-tag="2"
                                                        style="width: 280px;"
                                                        size="large"
                                                        :tag-max-width="120"
                                                    >
                                                        <abc-option
                                                            v-for="department in departments"
                                                            :key="department.id"
                                                            :value="department.id"
                                                            :label="department.name"
                                                        ></abc-option>
                                                    </abc-select>
                                                </abc-form-item>
                                            </template>

                                            <template #operate="{ trData: fileItem }">
                                                <abc-table-cell>
                                                    <abc-space>
                                                        <abc-button variant="ghost" size="small" @click="handleUpdateMedicalDocument(fileItem)">
                                                            编辑
                                                        </abc-button>
                                                        <!--<abc-button type="ghost" icon="copy" size="small"></abc-button>-->
                                                        <abc-button
                                                            variant="ghost"
                                                            icon="trash"
                                                            size="small"
                                                            @click="handleDeleteMedicalDocument(fileItem)"
                                                        ></abc-button>
                                                    </abc-space>
                                                </abc-table-cell>
                                            </template>
                                        </abc-table>
                                    </div>
                                </biz-setting-form-group>

                                <biz-setting-form-group v-if="isEmrGodMode" title="不清楚勿动！！！">
                                    <biz-setting-form-item label="type">
                                        <abc-form-item>
                                            <abc-space>
                                                <abc-input
                                                    v-model="godModePostData.type"
                                                    :width="256"
                                                ></abc-input>
                                                <span>{{ MedicalDocumentRecordTypeEnum }}</span>
                                            </abc-space>
                                        </abc-form-item>
                                    </biz-setting-form-item>

                                    <biz-setting-form-item label="tag">
                                        <abc-form-item>
                                            <abc-space>
                                                <abc-input
                                                    v-model="godModePostData.tag"
                                                    :width="256"
                                                ></abc-input>
                                                <span>{{ MedicalDocumentTagEnum }}</span>
                                            </abc-space>
                                        </abc-form-item>
                                    </biz-setting-form-item>
                                </biz-setting-form-group>
                            </abc-form>
                        </biz-setting-form>

                        <template #footer>
                            <biz-setting-footer>
                                <abc-button
                                    :loading="saveLoading"
                                    :disabled="(!isModified || fetchLoading) && !isEmrGodMode"
                                    @click="handleClickSave"
                                >
                                    保存
                                </abc-button>
                            </biz-setting-footer>
                        </template>
                    </biz-setting-content>
                </biz-setting-layout>

                <div v-else class="emr-empty-folder">
                    <abc-space direction="vertical">
                        <img :src="FolderEmptyImage" alt="空文件夹图标" style="height: 80px;" />
                        <span>{{ currentSelectedCatalogNode && currentSelectedCatalogNode.name }}</span>
                        <span v-if="currentSelectedCatalogNode && currentSelectedCatalogNode.children.length">- {{ currentSelectedCatalogNode && currentSelectedCatalogNode.children.length }}份 -</span>
                    </abc-space>
                </div>
            </div>
        </div>

        <div v-if="showPreview" class="common-emr-setting_document-preview__cover" @click.self="handleClosePreview">
            <div class="cover-content">
                <emr-editor
                    v-if="commonMedicalDocumentContent"
                    class="common-emr-setting_document-preview__content"
                    :editable="false"
                    :page-size="commonMedicalDocumentContent.templateSetting.pageSize"
                    :page-size-reduce="commonMedicalDocumentContent.templateSetting.pageSizeReduce"
                    :page-orientation="commonMedicalDocumentContent.templateSetting.pageOrientation"
                    :value="commonMedicalDocumentContent.templateContent"
                ></emr-editor>
            </div>
        </div>

        <share-group-dialog
            v-if="showShareGroupDialog"
            v-model="showShareGroupDialog"
            :shared-page-list="sharedPageList"
            :medical-document-list="medicalDocumentList"
            @create-shared-page="handleCreateSharedPage"
            @delete-shared-page="handleDeleteSharedPage"
            @submit-shared-page="handleSubmitSharedPage"
        ></share-group-dialog>

        <select-department-dialog
            v-if="showSelectDepartmentDialog"
            v-model="showSelectDepartmentDialog"
            :default-selected="postData.medicalDocumentDepartments"
            :departments="departments"
            @confirm="handleSelectDepartments"
        ></select-department-dialog>

        <emr-editor-design-dialog-v2
            v-if="showCommonMedicalDocumentDesignDialog"
            v-model="showCommonMedicalDocumentDesignDialog"
            :design-mode="designMode"
            :on-submit="handleOnSubmitCommonMedicalDocument"
            :default-post-data="Clone(postData)"
            :is-emr-god-mode="isEmrGodMode"
        ></emr-editor-design-dialog-v2>

        <emr-editor-design-dialog-v2
            v-if="showMedicalDocumentDesignDialog"
            v-model="showMedicalDocumentDesignDialog"
            :design-mode="designMode"
            :on-submit="handleOnSubmitMedicalDocument"
            :default-post-data="Clone(currentMedicalDocumentContent)"
            :departments="departments"
            :owner-type-options="ownerTypeOptions"
            :default-owner-id="currentMedicalDocumentOwnerId"
            :default-owner-type="currentMedicalDocumentOwnerType"
            :is-emr-god-mode="isEmrGodMode"
        ></emr-editor-design-dialog-v2>
    </div>
</template>

<script>
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';

    import ShareGroupDialog from '@/views-hospital/settings-hospital/components/share-group-dialog.vue';
    import SelectDepartmentDialog from '@/views-hospital/settings-hospital/components/select-department-dialog.vue';
    import EmrEditorTemplateCatalog from '@/views-hospital/settings-hospital/components/emr-editor-template-catalog.vue';
    import {
        EnableBreakAtFirstPageType,
        EnableBreakPageHeaderAndFooterType,
        MedicalDocumentAvailableDepartmentTypeEnum,
        MedicalDocumentRecordTypeEnum,
        MedicalDocumentTagEnum,
        MedicalDocumentTypeEnum,
        OwnerTypeOptions,
    } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants';
    import EmrAPI from 'api/hospital/emr';
    import { MedicalDocumentBusinessType } from '@/views-hospital/nursing/common/constants';
    import {
        CATALOGUE_FILE_OWNER_TYPE,
    } from 'utils/constants';
    import SettingAPI from 'api/settings';
    import { mapGetters } from 'vuex';
    import {
        DesignModeEnum, EditorModeEnum, EditorPageOrientation,
    } from '@abc-emr-editor/constants';
    import { GLOBAL_PAGE_LARGE_REDUCER } from '@/printer/config';
    import { AbcEmrEditorService } from '@/views-hospital/medical-prescription/components/emr-editor/AbcEmrEditorService';
    import {
        handlePrintEmr,
        mergeCommonTemplate,
    } from '@/views-hospital/medical-prescription/components/emr-editor/common/tools';
    import Clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';
    import FolderEmptyImage from 'src/assets/images/hospital/folder_empty.png';

    const MedicalDocumentSubmitMode = {
        Create: 'create',
        Update: 'update',
    };

    const emrGodModeKey = 'DO_NOT_USE_THIS_KEY_EMR_GOD_MODE_KEY';

    export default {
        name: 'CommonEmrSetting',

        components: {
            ShareGroupDialog,
            SelectDepartmentDialog,
            EmrEditorDesignDialogV2: () => import('@/views-hospital/medical-prescription/components/emr-editor/emr-editor-design-dialog-v2.vue'),
            EmrEditorTemplateCatalog,
            EmrEditor: AbcEmrEditorService.loadEmrEditor,

            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
        },

        props: {
            businessType: {
                type: Number,
                default: MedicalDocumentBusinessType.HOSPITAL,
            },
            enableMedicalDocumentBaseSetting: {
                type: Boolean,
                default: true,
            },
            enableMedicalDocumentSetting: {
                type: Boolean,
                default: true,
            },
            enableSharedPageDocument: {
                type: Boolean,
                default: true,
            },
            enableDepartmentSetup: {
                type: Boolean,
                default: false,
            },
            title: {
                type: String,
                default: '文书设置',
            },
        },

        data() {
            return {
                FolderEmptyImage,
                MedicalDocumentTypeEnum,
                MedicalDocumentAvailableDepartmentTypeEnum,
                DesignModeEnum,
                CATALOGUE_FILE_OWNER_TYPE,
                showPreview: false,
                showShareGroupDialog: false,
                showSelectDepartmentDialog: false,
                showCommonMedicalDocumentDesignDialog: false,
                showMedicalDocumentDesignDialog: false,
                options: [],
                currentSelectedCatalogNode: null,
                postData: {
                    name: '',
                    category: 1,
                    type: 1,
                    parentId: 0,
                    medicalDocumentType: MedicalDocumentTypeEnum.Self,
                    medicalDocumentDepartments: [],
                    medicalDocumentDepartmentAvailableType: MedicalDocumentAvailableDepartmentTypeEnum.All,
                    medicalDocumentSharedPageId: null,
                    medicalDocumentFileList: [],
                    content: this.createDefaultPostDataContent(),
                },
                godModePostData: {
                    type: undefined, // 1. 病案首页 2.护理单 3.西医病案首页
                    tag: undefined, // 参考 MedicalDocumentTagEnum
                },
                tag: undefined,
                type: undefined,
                postDataOld: null,
                saveLoading: false,
                sharedPageList: [],
                medicalDocumentList: [],
                ownerTypeOptions: OwnerTypeOptions,
                departments: [],
                designMode: DesignModeEnum.ShareMedicalDocument,
                submitMode: MedicalDocumentSubmitMode.Create,
                currentMedicalDocumentContent: null,
                currentMedicalDocumentId: null,
                currentMedicalDocumentOwnerType: null,
                currentMedicalDocumentOwnerId: null,
                currentUpdateMedicalDocument: null,
                fetchLoading: false,
            };
        },

        computed: {
            MedicalDocumentRecordTypeEnum() {
                return MedicalDocumentRecordTypeEnum;
            },
            MedicalDocumentTagEnum() {
                return MedicalDocumentTagEnum;
            },
            ...mapGetters(['currentClinic']),
            clinicId() {
                return this.currentClinic?.id;
            },
            userId() {
                return this.currentClinic?.userId;
            },
            EditorModeEnum() {
                return EditorModeEnum;
            },
            commonMedicalDocumentContent() {
                return Clone(this.postData.content);
            },
            isModified() {
                try {
                    // 没有原始值视为未修改
                    // 接口出错时应该为未修改
                    return this.postDataOld && !isEqual(this.postData, this.postDataOld);
                } catch (e) {
                    return false;
                }
            },
            // 禁止编辑文书
            isDisableSetupMedicalDocument() {
                if (this.isEmrGodMode) {
                    return false;
                }
                return !!(this.currentSelectedCatalogNode.tag & MedicalDocumentTagEnum.ReadOnly);
            },
            isBanShare() {
                return !!(this.currentSelectedCatalogNode.tag & MedicalDocumentTagEnum.BanShare);
            },
            isCommonMedicalDocumentLandscape() {
                return this.commonMedicalDocumentContent.templateSetting.pageOrientation === EditorPageOrientation.landscape;
            },
            isEmrGodMode() {
                return !!localStorage.getItem(emrGodModeKey);
            },
            tableRenderConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: '模板名称',
                            key: 'name',
                            style: {
                                flex: 1,
                            },
                        },

                        {
                            label: '模板类型',
                            key: 'templateType',
                            style: {
                                flex: 'none',
                                width: '160px',
                            },
                        },

                        {
                            label: '使用科室',
                            key: 'department',
                            style: {
                                flex: 'none',
                                width: '280px',
                            },
                        },

                        {
                            label: '操作',
                            key: 'operate',
                            style: {
                                flex: 'none',
                                width: '160px',
                            },
                        },
                    ],
                };
            },
        },

        watch: {
            currentSelectedCatalogNode(newNode) {
                if (!newNode) {
                    return;
                }
                if (newNode.isFolder) {
                    return;
                }
                this.postData.name = newNode.name;
                this.postData.category = newNode.category;
                this.postData.type = newNode.type;
                this.postData.parentId = newNode.parentId;
                this.postData.id = newNode.id;
                this.fetchMedicalDocumentDetail(newNode);
            },
        },

        created() {
            this.fetchSharedPageList();
            this.fetchDepartments();

            window.enterEmrGodMode = () => {
                localStorage.setItem(emrGodModeKey, '1');
                location.reload();
            };
            window.exitEmrGodMode = () => {
                localStorage.removeItem(emrGodModeKey);
                location.reload();
            };
        },

        beforeDestroy() {
            delete window.enterEmrGodMode;
            delete window.exitEmrGodMode;
        },

        methods: {
            Clone,
            handleMountedEditor() {
                const {
                    width: editorWidth,
                } = this.$refs.previewEditor.$el.getBoundingClientRect();
                const { width: wrapperWidth } = this.$refs.previewEditorWrapper.getBoundingClientRect();
                this.$refs.previewEditor.$el.style.zoom = `${wrapperWidth / editorWidth}`;
            },
            createDefaultPostDataContent() {
                return {
                    templateSetting: {
                        pageSize: 'A4',
                        pageSizeReduce: GLOBAL_PAGE_LARGE_REDUCER,
                        pageOrientation: EditorPageOrientation.portrait,
                    },
                    templateContent: {
                        type: 'doc',
                        content: [],
                    },
                };
            },
            createUpdateTemplatePostData(postData, customProps) {
                return {
                    businessType: this.businessType,
                    medicalId: this.postData.id,
                    combineIds: postData.combineIds,
                    content: postData.content,
                    name: postData.name,
                    ...customProps,
                };
            },
            handleClosePreview() {
                this.showPreview = false;
            },
            handleOpenPreview() {
                this.showPreview = true;
            },
            handleClickAddDepartments() {
                this.showSelectDepartmentDialog = true;
            },
            // 新建模板
            handleClickCreateMedicalDocument() {
                this.currentMedicalDocumentContent = {
                    name: this.postData.name,
                    content: this.postData.content ?? this.createDefaultPostDataContent(),
                };
                this.currentMedicalDocumentOwnerId = [];
                this.currentMedicalDocumentOwnerType = CATALOGUE_FILE_OWNER_TYPE.CLINIC;
                this.designMode = DesignModeEnum.MedicalDocument;
                this.showMedicalDocumentDesignDialog = true;
                this.submitMode = MedicalDocumentSubmitMode.Create;
            },
            // 更新模板
            async handleUpdateMedicalDocument(currentUpdateMedicalDocument) {
                const {
                    id,
                    name,
                    ownerType,
                    ownerId,
                } = currentUpdateMedicalDocument;
                const { data } = await EmrAPI.fetchEmrTemplate(id);
                // 增加页面方向
                // 兼容老数据
                if (data.content && !data.content.templateSetting.pageOrientation) {
                    data.content.templateSetting.pageOrientation = EditorPageOrientation.portrait;
                }
                this.currentMedicalDocumentContent = {
                    name,
                    content: data.content ?? this.createDefaultPostDataContent(),
                };
                await mergeCommonTemplate(this.currentMedicalDocumentContent.content?.templateContent?.content);
                this.designMode = DesignModeEnum.MedicalDocument;
                this.showMedicalDocumentDesignDialog = true;
                this.submitMode = MedicalDocumentSubmitMode.Update;
                this.currentUpdateMedicalDocument = currentUpdateMedicalDocument;
                this.currentMedicalDocumentId = id;
                this.currentMedicalDocumentOwnerId = ownerId;
                this.currentMedicalDocumentOwnerType = ownerType;
            },
            handleDeleteMedicalDocument({ id }) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不可恢复，是否确定删除？',
                    closeAfterConfirm: true,
                    onConfirm: async () => {
                        try {
                            await EmrAPI.deleteMedicalDocumentV2(id);
                            await this.fetchMedicalDocumentDetail(this.currentSelectedCatalogNode);
                        } catch (e) {
                            console.error(e);
                        }
                    },
                });
            },
            // 新建母版
            handleClickDesignCommonMedicalDocument() {
                if (!this.postData.content) {
                    this.postData.content = this.createDefaultPostDataContent();
                }
                this.designMode = DesignModeEnum.CommonMedicalDocument;
                this.showCommonMedicalDocumentDesignDialog = true;
            },
            handleClickShareGroupDialog() {
                this.showShareGroupDialog = true;
            },
            handleSelectDepartments(departments) {
                this.postData.medicalDocumentDepartments = departments;
            },
            handleRemoveDepartment(department) {
                this.postData.medicalDocumentDepartments = this.postData.medicalDocumentDepartments.filter((item) => item.id !== department.id);
            },
            // 更新母版
            async handleOnSubmitCommonMedicalDocument(postData, onSuccess, onError) {
                try {
                    await EmrAPI.updateEmrTemplate(this.createUpdateTemplatePostData(postData, {
                        isDefault: 1,
                    }));
                    onSuccess();
                    await this.fetchMedicalDocumentDetail(this.currentSelectedCatalogNode);
                    await this.$refs.catalog.fetchMedicalDocumentList();
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: '更新母版失败',
                    });
                    onError(e);
                    console.error(e);
                }
            },
            // 更新模板
            async handleOnSubmitMedicalDocument(postData, onSuccess, onError) {
                try {
                    if (this.submitMode === MedicalDocumentSubmitMode.Create) {
                        await EmrAPI.createEmrTemplate(this.createUpdateTemplatePostData(postData, {
                            isDefault: 0,
                            permissions: this.handleTemplatePermission(postData),
                        }));
                    }
                    if (this.submitMode === MedicalDocumentSubmitMode.Update) {
                        this.currentUpdateMedicalDocument.ownerId = postData.ownerId;
                        this.currentUpdateMedicalDocument.ownerType = postData.ownerType;
                        await EmrAPI.updateEmrTemplate(this.createUpdateTemplatePostData(postData, {
                            isDefault: 0,
                            id: this.currentMedicalDocumentId,
                            permissions: this.handleTemplatePermission(this.currentUpdateMedicalDocument),
                        }));
                    }
                    onSuccess();
                    await this.fetchMedicalDocumentDetail(this.currentSelectedCatalogNode);
                } catch (e) {
                    this.$Toast({
                        type: 'error',
                        message: this.submitMode === MedicalDocumentSubmitMode.Create ? '创建模板失败' : '更新模板失败',
                    });
                    onError(e);
                    console.error(e);
                }
            },
            handleTemplatePermission(templateItem) {
                let permissions = [];
                if (templateItem.ownerType === CATALOGUE_FILE_OWNER_TYPE.CLINIC) {
                    permissions = [{
                        ownerId: this.clinicId,
                        ownerType: templateItem.ownerType,
                    }];
                }
                if (templateItem.ownerType === CATALOGUE_FILE_OWNER_TYPE.PERSONAL) {
                    permissions = [{
                        ownerId: this.userId,
                        ownerType: templateItem.ownerType,
                    }];
                }
                if (templateItem.ownerType === CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT) {
                    permissions = templateItem.ownerId.map((id) => ({
                        ownerId: id,
                        ownerType: templateItem.ownerType,
                    }));
                }
                return permissions;
            },
            handleClickSave() {
                this.$refs.form.validate(async (valid) => {
                    if (!valid) {
                        return;
                    }
                    this.saveLoading = true;
                    try {
                        let sharedPageId;
                        let medicalDocumentPermissions = [];
                        // 独立页面
                        if (this.postData.medicalDocumentType === MedicalDocumentTypeEnum.Self) {
                            sharedPageId = undefined;
                        }
                        // 共享页面
                        if (this.postData.medicalDocumentType === MedicalDocumentTypeEnum.Share) {
                            sharedPageId = this.postData.medicalDocumentSharedPageId;
                        }

                        // 所有科室
                        if (this.postData.medicalDocumentDepartmentAvailableType === MedicalDocumentAvailableDepartmentTypeEnum.All) {
                            medicalDocumentPermissions = [{
                                ownerId: this.clinicId,
                                ownerType: CATALOGUE_FILE_OWNER_TYPE.CLINIC,
                            }];
                        }

                        // 指定科室
                        if (this.postData.medicalDocumentDepartmentAvailableType === MedicalDocumentAvailableDepartmentTypeEnum.Department) {
                            medicalDocumentPermissions = this.postData.medicalDocumentDepartments.map(({ id }) => ({
                                ownerId: id,
                                ownerType: CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT,
                            }));
                            if (!medicalDocumentPermissions.length) {
                                return this.$Toast({
                                    type: 'error',
                                    message: '请选择指定科室',
                                });
                            }
                        }

                        const postData = {
                            id: this.postData.id,
                            name: this.postData.name,
                            sharedPageId,
                            permissions: medicalDocumentPermissions,
                            emrTemplates: this.postData.medicalDocumentFileList.map((fileItem) => {
                                return {
                                    ...fileItem,
                                    permissions: this.handleTemplatePermission(fileItem),
                                };
                            }),
                        };

                        if (this.isEmrGodMode) {
                            postData.tag = this.godModePostData.tag;
                            postData.type = this.godModePostData.type;
                        }

                        await EmrAPI.updateMedicalDocumentDetail(postData);
                        await this.fetchMedicalDocumentDetail(this.currentSelectedCatalogNode);
                        await this.$refs.catalog.fetchMedicalDocumentList();
                        this.$Toast({
                            type: 'success',
                            message: '保存成功',
                        });
                    } catch (e) {
                        console.error(e);
                        this.$Toast({
                            type: 'error',
                            message: '保存失败',
                        });
                    } finally {
                        this.saveLoading = false;
                    }
                });
            },
            async handleCreateSharedPage() {
                try {
                    await EmrAPI.createSharedPage({
                        businessType: this.businessType,
                        content: {},
                        medicalIds: [],
                        name: '新建共享组页面',
                    });
                    await this.fetchSharedPageList();
                } catch (e) {
                    console.error(e);
                }
            },
            async handleDeleteSharedPage(id) {
                this.$confirm({
                    content: '确定删除该共享页面组？',
                    onConfirm: async () => {
                        try {
                            await EmrAPI.deleteSharedPage(id);
                            await this.fetchSharedPageList();
                        } catch (e) {
                            console.error(e);
                        }
                    },
                });
            },
            async handleSubmitSharedPage(sharedPageList) {
                try {
                    await EmrAPI.updateSharedPagePackage({
                        emrSharedPages: sharedPageList,
                    });
                    await this.fetchSharedPageList();
                    this.showShareGroupDialog = false;
                } catch (e) {
                    console.error(e);
                }
            },
            print() {
                this.$refs.previewEditor.print(handlePrintEmr, {
                    enableBreakAtFirstPage: EnableBreakAtFirstPageType.includes(this.type),
                    enableBreakPageHeaderAndFooter: EnableBreakPageHeaderAndFooterType.includes(this.type),
                });
            },
            async fetchDepartments() {
                try {
                    this.departments = await this.getDepartments();
                } catch (e) {
                    console.error(e);
                }
            },
            async getDepartments() {
                const res = await SettingAPI.clinic.fetchClinicDepartments();
                return res.data.data.rows || [];
            },
            async fetchSharedPageList() {
                try {
                    if (!this.enableSharedPageDocument) {
                        return;
                    }
                    const { data: { rows } } = await EmrAPI.fetchSharedPageList();
                    this.sharedPageList = rows;
                } catch (e) {
                    console.error(e);
                }
            },
            async fetchMedicalDocumentDetail(newNode) {
                try {
                    if (newNode.isFolder) {
                        return;
                    }
                    this.fetchLoading = true;
                    const { data } = await EmrAPI.fetchMedicalDocumentDetail(newNode.id);

                    if (!data.content) {
                        // 更新文书母版信息
                        const commonMedicalDocumentPostData = {
                            businessType: this.businessType,
                            medicalId: data.id,
                            combineIds: [],
                            content: this.createDefaultPostDataContent(),
                            name: data.name,
                            isDefault: 1,
                        };
                        await EmrAPI.updateEmrTemplate(commonMedicalDocumentPostData);
                        data.content = this.createDefaultPostDataContent();
                    }

                    // 增加页面方向
                    // 兼容老数据
                    if (!data.content.templateSetting.pageOrientation) {
                        data.content.templateSetting.pageOrientation = EditorPageOrientation.portrait;
                    }

                    await mergeCommonTemplate(data.content?.templateContent?.content);

                    this.postData.id = data.id;
                    this.postData.name = data.name;
                    this.postData.medicalDocumentDepartments = [];
                    this.postData.content = data.content;

                    this.tag = data.tag;
                    this.type = data.type;

                    if (this.isEmrGodMode) {
                        this.godModePostData.tag = data.tag;
                        this.godModePostData.type = data.type;
                    }

                    // 共享页面
                    if (data.sharedPageId) {
                        this.postData.medicalDocumentType = MedicalDocumentTypeEnum.Share;
                        this.postData.medicalDocumentSharedPageId = data.sharedPageId;
                    }

                    // 独立页面
                    if (!data.sharedPageId) {
                        this.postData.medicalDocumentType = MedicalDocumentTypeEnum.Self;
                        this.postData.medicalDocumentSharedPageId = undefined;
                    }

                    if (data.permissions?.length) {
                        // 所有科室
                        if (data.permissions.some((it) => it.ownerType === CATALOGUE_FILE_OWNER_TYPE.CLINIC)) {
                            this.postData.medicalDocumentDepartmentAvailableType = MedicalDocumentAvailableDepartmentTypeEnum.All;
                        }

                        // 指定科室
                        if (data.permissions.every((it) => it.ownerType === CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT)) {
                            this.postData.medicalDocumentDepartmentAvailableType = MedicalDocumentAvailableDepartmentTypeEnum.Department;
                            this.postData.medicalDocumentDepartments = data.permissions.map((it) => ({
                                id: it.ownerId,
                                name: this.departments.find((department) => department.id === it.ownerId)?.name,
                            }));
                        }
                    } else {
                        this.postData.medicalDocumentDepartmentAvailableType = MedicalDocumentAvailableDepartmentTypeEnum.All;
                    }

                    // 模板设置
                    this.postData.medicalDocumentFileList = data.emrTemplateViews.map((emrTemplate) => {
                        const { permissions } = emrTemplate;
                        let ownerType, ownerId;
                        // 全院公用
                        if (permissions.some((it) => it.ownerType === CATALOGUE_FILE_OWNER_TYPE.CLINIC)) {
                            ownerType = CATALOGUE_FILE_OWNER_TYPE.CLINIC;
                            ownerId = [];
                        }
                        // 个人专用
                        if (permissions.some((it) => it.ownerType === CATALOGUE_FILE_OWNER_TYPE.PERSONAL)) {
                            ownerType = CATALOGUE_FILE_OWNER_TYPE.PERSONAL;
                            ownerId = [];
                        }
                        // 科室公用
                        if (permissions.every((it) => it.ownerType === CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT)) {
                            ownerType = CATALOGUE_FILE_OWNER_TYPE.DEPARTMENT;
                            ownerId = permissions.map((it) => it.ownerId);
                        }

                        // 没有权限列表
                        // 默认为全院公用
                        if (!permissions.length) {
                            ownerType = CATALOGUE_FILE_OWNER_TYPE.CLINIC;
                            ownerId = [];
                        }
                        return {
                            ...emrTemplate,
                            ownerType,
                            ownerId,
                        };
                    });

                    this.postDataOld = Clone(this.postData);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.fetchLoading = false;
                }
            },

            // 注册供外部使用的方法
            beforeRouteLeave(to, from, next) {
                if (this.isModified) {
                    this.$confirm({
                        content: '你的修改内容还未保存，确定离开？',
                        showIcon: false,
                        onConfirm: () => {
                            next();
                        },
                        onCancel: () => {
                            next(false);
                        },
                    });
                } else {
                    next();
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme.scss';

.common-emr-setting {
    height: 100%;

    .common-emr-setting_content {
        display: flex;
        flex: 1;
        align-items: stretch;
        height: 100%;
    }

    .common-emr-setting_templates-bar {
        width: 360px;
        background: $P5;
    }

    .common-emr-setting_main {
        position: relative;
        flex: 1;
        height: 100%;
        overflow-y: auto;

        .common-emr-setting_table-header {
            display: flex;
        }

        .common-emr-setting_department-tags {
            padding: 8px 0 0 8px;
            margin-top: -4px;
            background: $P5;
            border-radius: var(--abc-border-radius-small);
        }

        .common-emr-setting_vertical-group {
            display: flex;

            .common-emr-setting_vertical-left {
                flex: 1;
            }

            .common-emr-setting_vertical-right {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 394px;
                height: 396px;

                .common-emr-setting_master-page {
                    position: relative;
                    width: 240px;
                    height: 332px;
                    overflow: hidden;
                    background: $S2;
                    border-radius: var(--abc-border-radius-small);
                    box-shadow: 0 0 0 1px $P2, 0 2px 16px 0 #00000026;

                    &.common-emr-setting_master-page-landscape {
                        width: 332px;
                        height: 240px;
                    }
                }

                .common-emr-setting_master-page-tip {
                    position: absolute;
                    top: 17px;
                    left: -23px;
                    z-index: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 106px;
                    height: 24px;
                    font-size: 12px;
                    color: $S2;
                    background: red;
                    background: $G2;
                    transform: rotate(-45deg);
                }

                .common-emr-setting_master-page-handler {
                    position: absolute;
                    bottom: 0;
                    z-index: 1;
                    width: 100%;
                    padding: 16px;
                }

                .common-emr-setting_master-page-preview-tip {
                    position: absolute;
                    top: 0;
                    right: 0;
                    z-index: 1;
                    display: none;
                    width: 48px;
                    height: 48px;
                    cursor: pointer;
                    background: linear-gradient(45deg, transparent, transparent 50%, #00000080 50%, #00000080 100%);

                    .abc-icon {
                        position: absolute;
                        top: 7px;
                        right: 7px;
                    }
                }

                .common-emr-setting_master-page-content {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    border-radius: var(--abc-border-radius-small);

                    .emr-editor-wrapper {
                        margin: 0;
                        border-color: transparent;
                        box-shadow: none;
                    }
                }

                &:hover {
                    .common-emr-setting_master-page-preview-tip {
                        display: block;
                    }
                }
            }
        }

        .common-emr-setting_table-main-table {
            margin-top: 16px;

            .common-emr-setting_table-td-template-name {
                padding: 12px;
                color: $theme1;
                cursor: pointer;
            }

            .common-emr-setting_table-td-template-handlers {
                display: flex;
                padding: 0 12px;
            }
        }
    }

    .emr-empty-folder {
        position: absolute;
        top: 30%;
        left: 50%;
        font-size: 14px;
        transform: translateX(-50%);

        .abc-space-item:nth-of-type(2) {
            color: $T2;
        }

        .abc-space-item:nth-of-type(3) {
            color: $T3;
        }
    }
}

.common-emr-setting_document-preview__cover {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2023;
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 24px;
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.5);
}
</style>
