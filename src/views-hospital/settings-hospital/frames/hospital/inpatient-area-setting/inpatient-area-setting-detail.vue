<template>
    <div class="content-form-wrapper inpatient_area-setting--detail">
        <navigation-bar :title="navbarTitle">
            <abc-button
                :disabled="!isUpdate"
                :loading="btnLoading"
                @click="confirmSubmit"
            >
                保存
            </abc-button>
            <template v-if="supportDeactiveEmployeeRelation">
                <abc-button
                    v-if="inpatientAreaStatus === RELATIVE_STATUS.ACTIVE"
                    variant="ghost"
                    theme="danger"
                    @click="handleDeactiveInpatientArea"
                >
                    停用
                </abc-button>
                <abc-button v-else variant="ghost" @click="handleActiveInpatientArea">
                    启用
                </abc-button>
            </template>
            <abc-button v-if="currentWardAreaId !== 'add'" type="danger" @click="deleteDetail">
                删除
            </abc-button>
        </navigation-bar>

        <div v-abc-loading="loading" class="inpatient_area-setting--detail-wrapper">
            <abc-flex vertical :gap="16">
                <abc-flex vertical>
                    <div>
                        病区信息
                    </div>
                    <abc-divider margin="small" variant="dashed"></abc-divider>
                    <abc-form
                        ref="inpatientAreaForm"
                        item-block
                        class="inpatient_area-form"
                        label-position="left"
                        :label-width="138"
                    >
                        <abc-form-item label="病区名称" required style="margin-top: 12px;">
                            <abc-input
                                v-model="postData.name"
                                :disabled="disableHandle"
                                :max-length="20"
                                placeholder="请填写病区名称"
                                :width="300"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="病区位置" required style="margin-top: 12px;">
                            <abc-input
                                v-model="postData.location"
                                :disabled="disableHandle"
                                :max-length="100"
                                placeholder="请填写病区位置"
                                :width="300"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="关联科室" required style="margin-top: 12px; margin-bottom: 0;">
                            <abc-select
                                v-model="postData.departmentIds"
                                :disabled="disableHandle"
                                multiple
                                :width="300"
                                placeholder="请选择关联科室"
                                multi-label-mode="tag"
                                @change="handleDepartmentChange"
                            >
                                <abc-option
                                    v-for="(option,index) in options"
                                    :key="index"
                                    :value="option.value"
                                    :label="option.label"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-form>
                </abc-flex>
                <abc-flex vertical>
                    <div>
                        病区成员
                    </div>
                    <abc-divider margin="small" variant="dashed"></abc-divider>
                    <div class="inpatient_area-setting--employee" style="padding-top: 4px;">
                        <label>成员姓名</label>
                        <abc-tag-group style="padding-top: 8px;">
                            <abc-tag-v2
                                v-for="(employee, index) in postData.employeeList"
                                :key="employee.id"
                                closable
                                variant="outline"
                                icon="people"
                                icon-size="12px"
                                size="large"
                                @close="deleteEmployee(index)"
                            >
                                {{ employee.name }}
                            </abc-tag-v2>

                            <abc-button
                                :disabled="disableHandle"
                                type="text"
                                style="height: 28px;"
                                @click="openSelectEmployeeDialog"
                            >
                                添加
                            </abc-button>
                        </abc-tag-group>
                    </div>
                </abc-flex>
                <abc-flex vertical>
                    <div>
                        病房病床
                    </div>
                    <abc-divider margin="small" variant="dashed"></abc-divider>
                    <div class="inpatient_area-setting--ward">
                        <inpatient-ward-setting
                            ref="inpatientWardSetting"
                            :disabled="disableHandle"
                            :current-inpatient-ward="currentWardAreaId"
                            :is-ward-update.sync="isWardUpdate"
                        ></inpatient-ward-setting>
                    </div>
                </abc-flex>
            </abc-flex>
        </div>

        <abc-dialog
            v-if="selectEmployeeDialog"
            v-model="selectEmployeeDialog"
            title="选择病区人员"
            content-styles="padding: 0 0 0 24px;height: 494px"
        >
            <abc-transfer
                v-model="selectedEmployee"
                v-abc-loading="employeeLoading"
                searchable
                :data="employeeList"
                @search="(keyword)=> { queryKeyword = keyword }"
            >
                <template #selected="{ item }">
                    <abc-icon
                        icon="patient"
                        size="12"
                        color="#58a0ff"
                        style="margin-right: 6px;"
                    ></abc-icon>
                    {{ item.name }}
                </template>
            </abc-transfer>

            <div slot="footer" class="dialog-footer">
                <abc-button @click="confirmSelect">
                    确定
                </abc-button>
                <abc-button type="blank" @click="selectEmployeeDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
    </div>
</template>

<script>
    import NavigationBar from 'views/layout/navigation-bar/navigation-bar';
    import ClinicAPI from 'api/clinic';
    import {
        debounce, isEqual,
    } from 'utils/lodash';
    import SettingAPI from 'api/settings';
    import clone from 'utils/clone';
    import WardAreaAPI from 'api/hospital/setting/ward-area';
    import { mapGetters } from 'vuex';
    import { RELATIVE_STATUS } from 'utils/constants';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { HospitalStatusEnum } from '@/views-hospital/register/utils/constants';
    import InpatientWardSetting
        from '@/views-hospital/settings-hospital/frames/hospital/inpatient-area-setting/inpatient-ward-setting.vue';

    export default {
        name: 'InpatientAreaSettingDetail',
        components: {
            NavigationBar,
            InpatientWardSetting,
        },
        data() {
            return {
                btnLoading: false,
                loading: true,
                postData: {
                    name: '',
                    location: '',
                    departmentIds: [],
                    employeeList: [],
                },
                postDataCache: {

                },
                queryKeyword: '',
                employeeLoading: false,
                selectedEmployee: [],
                departmentIdsCache: [],
                employeeList: [],
                selectEmployeeDialog: false,
                departments: [],
                inpatientAreaStatus: this.$route.params.status || RELATIVE_STATUS.ACTIVE,
                isWardUpdate: false,

                currentWardAreaId: this.$route.params?.id || 'add',
            };
        },
        computed: {
            ...mapGetters('viewDistribute',['viewDistributeConfig']),
            RELATIVE_STATUS() {
                return RELATIVE_STATUS;
            },
            supportDeactiveEmployeeRelation() {
                return this.currentWardAreaId !== 'add' && this.viewDistributeConfig.supportDeactiveEmployeeRelation;
            },
            disableHandle() {
                return this.supportDeactiveEmployeeRelation && this.inpatientAreaStatus === RELATIVE_STATUS.DEACTIVE;
            },
            isUpdate() {
                return !this.loading && !isEqual(this.postDataCache, this.postData) || this.isWardUpdate;
            },
            options() {
                return this.departments?.map((item) => {
                    return {
                        value: item?.id,
                        label: item?.name,
                    };
                }) || [];
            },
            navbarTitle() {
                return this.currentWardAreaId === 'add' ? '新增病区' : '编辑病区';
            },
        },
        watch: {
            queryKeyword(newValue) {
                this._selectEmployeeList(newValue);
            },
        },
        mounted() {
            this._selectEmployeeList = debounce(this.openSelectEmployeeDialog, 600, true);
        },
        async created() {
            await this.fetchDepartments();
            this.currentWardAreaId !== 'add' && await this.getWardAreaSetting();
            this.loading = false;
        },
        methods: {
            async getWardAreaSetting() {
                try {
                    const { data } = await WardAreaAPI.getWardArea(this.currentWardAreaId);
                    Object.assign(this.postData, data);
                    this.postData.departmentIds = this.postData?.departmentList?.map((item) => {return item.id;}) || [];
                    this.postDataCache = clone(this.postData);
                    this.selectedEmployee = this.postData.employeeList.slice();
                    this.departmentIdsCache = clone(this.postData.departmentIds);
                    if (this.selectedEmployee.length) {
                        this.selectedEmployee = this.selectedEmployee.map((item) => {
                            return Object.assign(item,{
                                id: item?.employeeId || item?.id,
                                name: item?.employeeName || item?.name,
                            });
                        });
                    }
                } catch (e) {
                    console.log('error', e);
                }
            },
            confirmSubmit() {
                this.$refs.inpatientAreaForm.validate((valid) => {
                    if (valid) {
                        const next = this.$refs.inpatientWardSetting.confirmSubmit;
                        if (this.currentWardAreaId === 'add') {
                            this.confirmSetting(next);
                        } else {
                            this.modifySetting(next);
                        }
                    }
                });
            },
            async modifySetting(next) {
                this.btnLoading = true;
                try {
                    const params = this.getParams(this.postData);
                    const data = await WardAreaAPI.modifyWardArea(params, this.currentWardAreaId);
                    if (data && await next()) {
                        this.$Toast({
                            message: '修改病区成功',
                            type: 'success',
                            duration: 2000,
                        });
                    }
                    this.postDataCache = clone(this.postData);
                } catch (e) {
                    console.log('error', e);
                } finally {
                    this.btnLoading = false;
                }
            },
            getParams(data) {
                return {
                    name: data?.name,
                    location: data?.location,
                    departmentIds: data?.departmentIds,
                    employeeIds: data?.employeeList?.map((item) => {
                        return item.id;
                    }) || [],
                    employeeList: data?.employeeList,
                };
            },
            async confirmSetting(next) {
                this.btnLoading = true;
                try {
                    const params = this.getParams(this.postData);
                    const { id } = await WardAreaAPI.createWardArea(params);
                    this.currentWardAreaId = id;
                    await this.$nextTick();
                    if (await next()) {
                        this.$Toast({
                            message: '创建病区成功',
                            type: 'success',
                            duration: 2000,
                        });
                        this.$router.go(-1);
                    }
                } catch (e) {
                    console.log('error', e);
                } finally {
                    this.btnLoading = false;
                }
            },
            deleteDetail() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除病区后无法恢复，是否确认删除？',
                    onConfirm: async () => {
                        this.btnLoading = true;
                        try {
                            const data = await WardAreaAPI.deleteWardArea(this.currentWardAreaId);
                            if (data) {
                                this.$Toast({
                                    message: '删除病区成功',
                                    type: 'success',
                                    duration: 2000,
                                });
                                this.$router.go(-1);
                            }
                        } catch (e) {
                            console.log('error', e);
                        } finally {
                            this.btnLoading = false;
                        }
                    },
                });
            },
            async fetchDepartments() {
                try {
                    const res = await SettingAPI.clinic.fetchClinicOutpatientDepartments();
                    const { data } = res.data;
                    this.departments = data?.rows || [];
                } catch (e) {
                    console.log('科室信息获取错误=', e);
                }
            },
            confirmSelect() {
                this.selectedEmployee = this.selectedEmployee.map((item) => {
                    return Object.assign(item,{
                        id: item.id,
                        employeeName: item.name,
                    });
                });
                this.postData.employeeList = this.selectedEmployee;
                this.selectEmployeeDialog = false;
            },
            deleteEmployee(index) {
                this.postData.employeeList.splice(index, 1);
            },
            async openSelectEmployeeDialog(val) {
                val = typeof val === 'object' ? '' : val;
                this.employeeLoading = true;
                try {
                    if (!this.selectedEmployee.length) {
                        this.selectedEmployee = this.postData.employeeList.slice();
                    }
                    const { data } = await ClinicAPI.getClinicEmployee(false,val);
                    this.employeeList = data?.rows || [];

                    this.postData.employeeList.forEach((item) => {
                        this.employeeList.forEach((employee) => {
                            if (item.id === employee.id) {
                                employee.checked = true;
                            }
                        });
                    });
                    this.selectEmployeeDialog = true;
                } catch (error) {
                    console.log('fetchEmployeeList error', error);
                }
                this.employeeLoading = false;
            },
            handleDeactiveInpatientArea() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '停用后该病区不可使用，系统将保留历史信息，是否确认停用?',
                    onConfirm: async () => {
                        try {
                            await SettingAPI.ward.updateWardStatus(this.currentWardAreaId, {
                                status: RELATIVE_STATUS.DEACTIVE,
                            });
                            this.inpatientAreaStatus = RELATIVE_STATUS.DEACTIVE;
                        } catch (e) {
                            console.error(e);
                        }
                    },
                });
            },

            handleActiveInpatientArea() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '启用后该病区即可正常使用，是否确认启用?',
                    onConfirm: async () => {
                        try {
                            await SettingAPI.ward.updateWardStatus(this.currentWardAreaId, {
                                status: RELATIVE_STATUS.ACTIVE,
                            });
                            this.inpatientAreaStatus = RELATIVE_STATUS.ACTIVE;
                        } catch (e) {
                            console.error(e);
                        }
                    },
                });
            },

            async handleDepartmentChange(newVal, _ ,oldVal) {
                if (newVal.length < oldVal.length) {
                    // 找到删除的科室
                    const deleteId = oldVal.filter((item) => {
                        return !newVal.includes(item);
                    })[0];
                    const { rows: patientList } = await PatientOrderAPI.fetchPatientListByWardAndDepartment({
                        wardId: this.currentWardAreaId,
                        departmentIds: [ deleteId ],
                    });

                    // 待分配患者
                    const waitPatientList = patientList.filter((item) => {
                        return item.status < HospitalStatusEnum.INPATIENT;
                    });

                    // 在院患者
                    const inPatientList = patientList.filter((item) => {
                        return item.status >= HospitalStatusEnum.INPATIENT && item.status < HospitalStatusEnum.WAIT_DISCHARGE;
                    });

                    //  待出院
                    const waitDischargedPatientList = patientList.filter((item) => {
                        return [HospitalStatusEnum.WAIT_DISCHARGE, HospitalStatusEnum.WAIT_SETTLE].includes(item.status);
                    });

                    if (waitPatientList.length || inPatientList.length || waitDischargedPatientList.length) {
                        const messages = [];
                        const departmentName = this.departments.find((item) => deleteId === item.id).name;
                        if (inPatientList.length) {
                            messages.push(`${departmentName}存在在院患者，不可从病区中移除。`);
                        }
                        if (waitPatientList.length) {
                            messages.push(`${departmentName}存在待分配的住院患者，不可从病区中移除。`);
                        }
                        if (waitDischargedPatientList.length) {
                            messages.push(`${departmentName}存在待出院的患者，不可从病区中移除。`);
                        }
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: messages[0],
                        });
                        // 还原
                        this.postData.departmentIds = oldVal;
                    }
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';
@import 'src/styles/mixin';

.inpatient_area-setting {
    width: 100%;

    &--detail-wrapper {
        padding: 24px 14px 0  24px;

        > section {
            padding: 24px 0;

            .section-title {
                padding-bottom: 8px;
                font-size: 16px;
                font-weight: 400;
                line-height: 1;
                color: $T1;
                border-bottom: 1px dashed $P6;
            }

            .tips {
                position: absolute;
                bottom: -4px;
                left: 0;
                height: 12px;
                font-size: 12px;
                font-weight: 400;
                line-height: 12px;
                color: $T3;
            }

            .abc-form-item:last-child {
                margin-bottom: 0;
            }
        }
    }

    &--employee {
        display: flex;
        min-height: 32px;
        padding: 8px 0 0;

        label {
            width: 128px;
            padding-top: 4px;
            line-height: 32px;
            color: $T2;
        }

        > div {
            flex: 1;
            padding-right: 80px;
        }

        .employee-item {
            position: relative;
            display: inline-block;
            padding: 8px;
            margin: 0 12px 8px 0;
            font-size: 12px;
            color: $T1;
            border: 1px solid $P3;

            .cis-icon-patient {
                margin-right: 4px;
                font-size: 12px;
                color: #58a0ff;
            }

            &:hover {
                .operation i {
                    display: inline-block;
                }
            }
        }

        .abc-button-text {
            height: 38px;
        }

        .operation {
            position: absolute;
            top: -10px;
            right: -10px;
            display: block;
            width: 22px;
            height: 20px;
            line-height: 22px;
            text-align: center;

            i {
                display: none;
                width: 20px;
                height: 20px;
                font-size: 22px;
                color: $P1;
                text-align: right;
                cursor: pointer;
                background: url('~assets/images/<EMAIL>') no-repeat  center;
                background-size: contain;
            }

            &:hover {
                i {
                    color: $T3;
                    background: url('~assets/images/<EMAIL>') no-repeat center;
                    background-size: contain;
                }
            }
        }
    }

    &--ward {
        padding: 8px 0 0;
    }
}
</style>
