<template>
    <div class="emr-editor-template-catalog">
        <div class="emr-editor-template-catalog_title">
            <abc-space>
                <abc-input
                    v-model="catalogSearchKey"
                    :width="252"
                    placeholder="搜索文书"
                >
                    <abc-search-icon slot="prepend"></abc-search-icon>
                </abc-input>

                <!--                <abc-select-->
                <!--                    v-model="selectedDepartments"-->
                <!--                    :width="100"-->
                <!--                    placeholder="所有科室"-->
                <!--                    multiple-->
                <!--                    multi-label-mode="text"-->
                <!--                >-->
                <!--                    <abc-option value="1" label="妇科"></abc-option>-->
                <!--                    <abc-option value="2" label="儿科"></abc-option>-->
                <!--                </abc-select>-->

                <abc-dropdown size="small" @change="handleCreateMedicalDocument">
                    <abc-button slot="reference" variant="fill" theme="success">
                        新增
                    </abc-button>
                    <abc-dropdown-item label="新增文书" :value="NodeCreateTypeEnum.Document"></abc-dropdown-item>
                    <abc-dropdown-item label="新增分组" :value="NodeCreateTypeEnum.Folder"></abc-dropdown-item>
                </abc-dropdown>
            </abc-space>
        </div>

        <div class="emr-editor-template-catalog_content">
            <abc-tree
                :indent="18"
                :draggable="!catalogSearchKey"
                is-drag-in-parent
                :data.sync="currentCatalogList"
                :selecteds="catalogSelected"
                @node-drop="handleNodeDrop"
            >
                <template
                    #default="{
                        node,
                    }"
                >
                    <div
                        class="custom-node-wrapper"
                        @click="handleClickNode(node)"
                    >
                        <abc-space>
                            <div class="icon-wrapper">
                                <img
                                    v-if="node.isFolder"
                                    src="~assets/images/<EMAIL>"
                                    alt="文书目录"
                                />
                                <img
                                    v-else
                                    src="~assets/images/<EMAIL>"
                                    alt="文件"
                                />
                            </div>

                            <abc-input
                                v-if="isEditing && curEditNodeId === node.id"
                                ref="rename-input"
                                v-model="curEditNodeName"
                                v-abc-focus-selected
                                v-abc-auto-focus
                                max-length="20"
                                size="small"
                                @blur="handleChangeNodeName"
                                @enter="handleChangeNodeName"
                            >
                            </abc-input>

                            <template v-else>
                                <div class="node-content" :title="node.name">
                                    {{ node.name }}
                                </div>

                                <div class="node-append" :title="node.append">
                                    {{ node.append }}
                                </div>

                                <div class="node-handler" @click="e => handleNodeClick(e, node)">
                                    <abc-dropdown
                                        v-if="!(node.tag & MedicalDocumentTagEnum.ReadOnly)"
                                        :margin-top="20"
                                        size="small"
                                        @change="val => handleNodeOperation(val, node)"
                                    >
                                        <template slot="reference">
                                            <abc-icon icon="three_dot_v" color="#7a8794" size="12"></abc-icon>
                                        </template>
                                        <abc-dropdown-item v-if="node.isFolder" label="新增" value="create"></abc-dropdown-item>
                                        <abc-dropdown-item label="重命名" value="rename"></abc-dropdown-item>
                                        <abc-dropdown-item label="删除" value="delete"></abc-dropdown-item>
                                    </abc-dropdown>
                                </div>
                            </template>
                        </abc-space>
                    </div>
                </template>
            </abc-tree>
        </div>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import EmrAPI from 'api/hospital/emr';
    import { MedicalDocumentBusinessType } from '@/views-hospital/nursing/common/constants';
    import clone from 'utils/clone.js';
    import {
        MedicalDocumentTagEnum,
    } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants';

    const NodeCreateTypeEnum = {
        Document: 'document',
        Folder: 'folder',
    };

    export default {
        name: 'EmrEditorTemplateCatalog',

        props: {
            selectedCatalogNode: {
                type: Object,
                default: () => ({}),
            },
            medicalDocumentList: {
                type: Array,
                default: () => [],
            },
            businessType: {
                type: Number,
                default: MedicalDocumentBusinessType.HOSPITAL,
            },
        },

        data() {
            return {
                NodeCreateTypeEnum,
                catalogSelected: [],
                selectedDepartments: [],
                catalogList: [],
                currentCatalogList: [],
                catalogSearchKey: '',
                curEditNodeName: '',
                curEditNodeId: '',
                curEditNodeIsFolder: false,
                isEditing: false,
                linkedList: [],
            };
        },

        computed: {
            ...mapGetters([ 'currentClinic' ]),
            MedicalDocumentTagEnum() {
                return MedicalDocumentTagEnum;
            },
        },

        watch: {
            catalogSearchKey() {
                this.setCatalogListKeyword(this.catalogList);
                this.catalogSelected = [];
            },
        },

        created() {
            this.handleInit();
        },

        methods: {
            async handleInit() {
                await this.fetchMedicalDocumentList();
                // 选择第一个
                for (let i = 0; i < this.currentCatalogList.length; i++) {
                    const currentCatalog = this.currentCatalogList[i];
                    if (currentCatalog.children?.[0]) {
                        this.handleClickNode((currentCatalog.children?.[0]));
                        currentCatalog.expand = true;
                        break;
                    }
                }
            },

            handleClickNode(node) {
                if (this.isEditing) {
                    return false;
                }
                this.catalogSelected = [node.id];
                // node是文件夹时设置展开状态
                if (node.isFolder) {
                    node.expand = !node.expand;
                }
                this.$emit('update:selectedCatalogNode', node);
            },

            setCatalogListKeyword(catalogList) {
                const _catalogSearchKey = this.catalogSearchKey.trim().toLowerCase();
                if (_catalogSearchKey) {
                    this.currentCatalogList = catalogList.map((group) => {
                        const children = group.children.filter((item) => {
                            return item.name.includes(_catalogSearchKey) ||
                                item.namePy.toLowerCase().includes(_catalogSearchKey) ||
                                item.namePyFirst.toLowerCase().includes(_catalogSearchKey);
                        });
                        return {
                            ...group,
                            children,
                        };
                    }).filter((group) => {
                        return !!group.children.length;
                    });
                } else {
                    this.currentCatalogList = clone(catalogList);
                }

            },

            buildLinkedList(medicalDocumentList) {
                const results = [];
                const walk = (list) => {
                    if (list?.length) {
                        list.forEach((item) => {
                            results.push(item);
                            walk(item.children);
                        });
                    }
                };

                walk(medicalDocumentList);

                return results;
            },

            async fetchMedicalDocumentList() {
                try {
                    // 获取医疗列表
                    const { data } = await EmrAPI.fetchMedicalDocumentList(this.businessType);

                    // 所有文书列表
                    // 用于共享文书的选择项
                    const medicalDocumentList = data.emrCatalogueViews.reduce((total, current) => {
                        return total.concat(current.medicalViews);
                    }, []);

                    this.$emit('update:medicalDocumentList', medicalDocumentList);

                    // 处理目录树
                    const newList = data.emrCatalogueViews.map((group) => {
                        return {
                            id: group.medicalTypeId,
                            name: group.name,
                            isFolder: 1,
                            tag: group.tag,
                            isReadOnly: group.isReadOnly,
                            namePy: group.namePy,
                            namePyFirst: group.namePyFirst,
                            draggableDepths: [1],
                            parentId: null,
                            children: group.medicalViews.map((item) => {
                                return {
                                    id: item.id,
                                    name: item.name,
                                    isFolder: 0,
                                    children: [],
                                    tag: item.tag,
                                    isReadOnly: item.isReadOnly,
                                    namePy: item.namePy,
                                    namePyFirst: item.namePyFirst,
                                    draggableDepths: [2],
                                    parentId: group.medicalTypeId,
                                };
                            }),
                        };
                    });

                    // 第二次拉取时需要保持之前的展开状态
                    const currentExpandIds = this.currentCatalogList.filter((it) => it.expand).map((it) => it.id);
                    newList.forEach((newItem) => {
                        newItem.expand = !!currentExpandIds.includes(newItem.id);
                    });

                    // 构造链表
                    // 方便后续的查找
                    // 假设有一下目录结构
                    // 其中A、B为目录 A带序号的如A1为文件
                    // ----A
                    // -------A1
                    // -------A2
                    // -------A3
                    // ----B
                    // -------B1
                    // -------B2
                    // -------B3
                    // 考虑以下几种删除情况
                    // 1. 删除B3时 我们应该跳转到B2
                    // 2. 假设只有B1时 我们删除B1则应该跳转到A3
                    // 3. 删除B1时我们应该跳转到B2
                    // 构建链表之后我们得到如下结构 A -> A1 -> A2 -> A3 -> B -> B1 -> B2 -> B3
                    // 查找规则参考下面的deleteCatalogue函数
                    // 1. 当删除节点的下一个节点是文件时跳转到该节点
                    // 2. 当删除节点的上一个节点是文件时跳转到该节点
                    // 3. 向后查找到最近的第一个文件
                    // 4. 向前查找到最近的第一个文件
                    this.linkedList = this.buildLinkedList(newList);
                    this.catalogList = newList;
                    this.setCatalogListKeyword(newList);
                } catch (e) {
                    console.error(e);
                }
            },

            async handleChangeNodeName() {
                try {
                    // blur和enter会同时触发
                    // 只需要运行其中一次即可
                    if (this._isChange) {
                        return;
                    }
                    this._isChange = true;
                    let onFinish;
                    if (!this.curEditNodeName.trim()) {
                        this.$Toast({
                            type: 'error',
                            message: '请输入名称',
                        });
                        return;
                    }

                    if (this.curEditNodeId === NodeCreateTypeEnum.Document) {
                        const { data: node } = await EmrAPI.createMedical({
                            name: this.curEditNodeName,
                            medicalTypeId: this.selectedCatalogNode.id,
                            businessType: this.businessType,
                        });
                        if (node) {
                            // 强制设置isFolder为0
                            // 原因找思源
                            node.isFolder = 0;
                            // 新增后选中目录
                            onFinish = () => this.handleClickNode(node);
                        }
                    } else if (this.curEditNodeId === NodeCreateTypeEnum.Folder) {
                        await EmrAPI.createMedicalCatalogue({
                            name: this.curEditNodeName,
                            businessType: this.businessType,
                        });
                    } else {
                        if (this.curEditNodeIsFolder) {
                            await EmrAPI.renameMedicalCatalogue(this.curEditNodeId, {
                                name: this.curEditNodeName,
                                businessType: this.businessType,
                            });
                        } else {
                            const { data: node } = await EmrAPI.renameMedicalDocument(this.curEditNodeId, {
                                name: this.curEditNodeName,
                                businessType: this.businessType,
                            });
                            if (node) {
                                onFinish = () => this.handleClickNode(node);
                            }
                        }
                    }
                    await this.fetchMedicalDocumentList();
                    this.isEditing = false;
                    await this.$nextTick();
                    onFinish?.();
                } catch (e) {
                    console.error(e);
                } finally {
                    this._isChange = false;
                }
            },

            handleDeleteConfirm() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不可恢复，是否确定删除？',
                    closeAfterConfirm: true,
                    onConfirm: () => this.deleteSourceItem(this.curEditNodeId),
                });
            },

            // 删除文书或者目录
            async deleteSourceItem(id) {
                try {
                    const currentNodeIndex = this.linkedList.findIndex((item) => item.id === id);
                    if (this.curEditNodeIsFolder) {
                        await EmrAPI.deleteMedicalCatalogue(id);
                    } else {
                        await EmrAPI.deleteMedical(id);
                    }
                    await this.fetchMedicalDocumentList();

                    const nextNode = this.linkedList[currentNodeIndex];
                    const preNode = this.linkedList[currentNodeIndex - 1];

                    // 下一个节点不是目录
                    if (nextNode && !nextNode.isFolder) {
                        this.handleClickNode(nextNode);
                        return;
                    }

                    // 前一个节点不是目录
                    if (preNode && !preNode.isFolder) {
                        this.handleClickNode(preNode);
                        return;
                    }

                    // 向后查找
                    for (let start = currentNodeIndex; start < this.linkedList.length; start++) {
                        const currentNode = this.linkedList[start];
                        if (currentNode && !currentNode?.isFolder) {
                            this.handleClickNode(currentNode);
                            return;
                        }
                    }

                    // 向前查找
                    for (let end = currentNodeIndex; end >= 0; end--) {
                        const currentNode = this.linkedList[end];
                        if (currentNode && !currentNode?.isFolder) {
                            this.handleClickNode(currentNode);
                            return;
                        }
                    }

                    // 找不到则为空
                    this.$emit('update:selectedCatalogNode', null);
                } catch (err) {
                    console.log(err);
                }
            },

            handleNodeClick(e, node) {
                if (this.isEditing) {
                    return false;
                }
                if (node.isFolder) {
                    this.catalogSelected = [node.id];
                    this.$emit('update:selectedCatalogNode', node);
                    e.stopPropagation();
                }
            },

            handleNodeOperation(value, node) {
                switch (value) {
                    case 'rename':
                        this.isEditing = true;
                        this.curEditNodeName = node.name;
                        this.curEditNodeId = node.id;
                        this.curEditNodeIsFolder = !!node.isFolder;
                        break;
                    case 'delete':
                        this.curEditNodeId = node.id;
                        this.curEditNodeIsFolder = !!node.isFolder;
                        this.handleDeleteConfirm();
                        break;
                    case 'create':
                        this.handleCreateMedicalDocument(NodeCreateTypeEnum.Document);
                        break;
                    default:
                        break;
                }
            },

            async handleCreateMedicalDocument(value) {
                if (value === NodeCreateTypeEnum.Document) {
                    // 初始状态未选中任何节点
                    if (!this.selectedCatalogNode) {
                        return;
                    }
                    // 选中节点是文书本身时
                    // 找到该文书节点的父目录
                    if (!this.selectedCatalogNode.isFolder) {
                        for (let i = 0; i < this.currentCatalogList.length; i++) {
                            const currentFolder = this.currentCatalogList[i];
                            if (currentFolder.children.some((it) => it.id === this.selectedCatalogNode.id)) {
                                this.handleClickNode(currentFolder);
                                await this.$nextTick();
                                break;
                            }
                        }
                    }
                    const currentSelectedNode = this.currentCatalogList.find((node) => node.id === this.selectedCatalogNode.id);
                    currentSelectedNode.expand = true;
                    currentSelectedNode.children.push({
                        id: NodeCreateTypeEnum.Document,
                        name: '未命名文书',
                        isFolder: 0,
                        children: [],
                    });
                    this.isEditing = true;
                    this.curEditNodeName = '未命名文书';
                    this.curEditNodeId = NodeCreateTypeEnum.Document;
                }

                if (value === NodeCreateTypeEnum.Folder) {
                    this.currentCatalogList.push({
                        id: NodeCreateTypeEnum.Folder,
                        name: '未命名目录',
                        isFolder: 1,
                        children: [],
                    });
                    this.isEditing = true;
                    this.curEditNodeName = '未命名目录';
                    this.curEditNodeId = NodeCreateTypeEnum.Folder;
                }
            },

            handleNodeDrop(dragNode) {
                this.$nextTick(async () => {
                    const { isFolder } = dragNode.node;
                    let sort = -1;
                    if (isFolder) {
                        sort = this.currentCatalogList.findIndex((it) => it.id === dragNode.node.id);
                    } else {
                        const parent = this.currentCatalogList.find((it) => it.id === dragNode.node.parentId);
                        sort = parent.children.findIndex((it) => it.id === dragNode.node.id);
                    }
                    if (sort === -1) {
                        return;
                    }
                    await EmrAPI.sortCatalogue(this.businessType, {
                        id: dragNode.node.id,
                        parentId: dragNode.node.parentId,
                        sceneType: isFolder ? 1 : 2,
                        sort,
                    });
                });
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/theme.scss';

.emr-editor-template-catalog {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    height: 100%;
    border-right: 1px solid $P6;

    &_title {
        display: flex;
        justify-content: space-between;
        padding: 12px  16px;
        border-bottom: 1px solid $P2;
    }

    &_content {
        flex: 1;
        height: 0;
        overflow-y: auto;
    }

    .abc-tree-wrapper {
        .custom-node-wrapper {
            display: flex;
            align-items: center;
            cursor: pointer;

            .icon-wrapper {
                width: 14px;
                height: 14px;
                margin-right: 0;

                img {
                    width: 14px;
                    height: 14px;
                    margin-right: 0;
                }
            }

            .node-content {
                flex: 1;
                color: $T1;
            }

            .node-append {
                color: $T2;
            }

            .node-handler {
                position: absolute;
                top: 50%;
                right: 6px;
                visibility: hidden;
                transform: translateY(-50%);
            }

            &:hover {
                .node-handler {
                    visibility: visible;
                }
            }
        }
    }
}
</style>
