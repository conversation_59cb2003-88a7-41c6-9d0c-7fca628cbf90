<template>
    <abc-dialog
        v-model="dialogVisible"
        v-abc-loading="loading"
        title="选择指定科室"
        content-styles="padding: 0 0 0 24px;min-height: 408px;"
    >
        <abc-transfer
            v-model="selected"
            v-abc-loading="loading"
            :data="departments"
        >
            <template #selected="{ item }">
                <abc-icon
                    icon="patient"
                    size="12"
                    color="#58a0ff"
                    style="margin-right: 6px;"
                ></abc-icon>
                {{ item.name }}
            </template>
        </abc-transfer>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="handleCancel">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>
<script>
    export default {
        name: 'SelectDepartmentDialog',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            defaultSelected: {
                type: Array,
                default: () => [],
            },
            departments: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            // 先清理选中状态
            this.departments.forEach((department) => {
                department.checked = false;
            });
            return {
                // 根据选中状态正确设置check
                selected: this.defaultSelected.map((defaultSelected) => {
                    const item = this.departments.find((department) => {
                        return department.id === defaultSelected.id;
                    });
                    if (item) {
                        item.checked = true;
                    }
                    return item;
                }).filter((item) => !!item),
                loading: false,
            };
        },
        computed: {
            dialogVisible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input',val);
                },
            },
        },

        methods: {
            handleCancel() {
                this.dialogVisible = false;
            },

            handleConfirm() {
                this.$emit('confirm', this.selected);
                this.dialogVisible = false;
            },
        },
    };
</script>
