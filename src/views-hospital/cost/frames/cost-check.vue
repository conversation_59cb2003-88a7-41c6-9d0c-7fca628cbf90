<template>
    <abc-layout class="hospital-cost-check-content" preset="page-table">
        <abc-layout-header>
            <div class="filter-toolbar">
                <abc-space>
                    <abc-date-time-range-picker
                        v-model="filterChargedTimeRangeValue"
                        :start-date-placeholder="filterParams.sceneType === 2 ? '计费开始时间' : '下达时间'"
                        :end-date-placeholder="filterParams.sceneType === 2 ? '计费结束时间' : '下达时间'"
                        :default-show-time-check="false"
                        :show-time-check-option="false"
                        :width="250"
                        @change="handleChangeDate"
                    >
                    </abc-date-time-range-picker>
                    <!--医嘱类型筛选-->
                    <abc-select
                        v-model="filterParams.adviceType"
                        :width="120"
                        placeholder="医嘱类型"
                        clearable
                        focus-show-options
                        @change="handleChangeType"
                    >
                        <slot></slot>
                        <abc-option
                            v-for="d in medicalPrescriptionTypeList"
                            :key="d.value"
                            :value="d.value"
                            :label="d.label"
                        >
                        </abc-option>
                        <slot name="append"></slot>
                    </abc-select>
                    <abc-search
                        v-if="filterParams.sceneType === 2"
                        v-model="feeDetailFilterParams.name"
                        placeholder="费用项"
                        :width="160"
                        @search="handleSearchNameChange"
                    >
                    </abc-search>
                </abc-space>

                <abc-space v-if="supportSupplement" class="buttons-wrapper">
                    <abc-radio-group v-model="filterParams.sceneType" @change="handleChangeSceneType">
                        <abc-radio-button :label="0">
                            按医嘱
                        </abc-radio-button>
                        <abc-radio-button :label="1">
                            按费用项
                        </abc-radio-button>
                        <abc-radio-button :label="2">
                            计费明细
                        </abc-radio-button>
                    </abc-radio-group>
                    <abc-button type="blank" icon="s-b-bookkeeping-line" @click="showCostSupplementDialog = true">
                        记账
                    </abc-button>
                    <cost-print :patient-order-id="patientOrderId"></cost-print>
                </abc-space>
            </div>
            <table-banner :list="bannerList" style="margin-bottom: 0;">
            </table-banner>
        </abc-layout-header>

        <abc-layout-content ref="hospitalCostCheckLayoutContent" style=" display: flex; flex-direction: column; gap: 16px; background-color: #ffffff;">
            <template v-if="filterParams.sceneType === 2">
                <div ref="hospitalCostCheckDetailWrapper" style="flex: 1;">
                    <cost-check-fee-detail-pro-table
                        v-if="detailFillReferenceEl"
                        :data-list="costCheckListDetailList"
                        :price-scope-type="costPriceScopeType"
                        :price-settle-type="costPriceSettleType"
                        :fill-reference-el="detailFillReferenceEl"
                        :loading="costCheckLoading"
                        :show-content-empty="!pageLoading"
                        :patient-order-id="patientOrderId"
                        @table-mounted="handleTableMounted"
                        @refund-fee-success="handleRefundFeeSuccess"
                    >
                    </cost-check-fee-detail-pro-table>
                </div>
                <abc-pagination
                    :count="feeDetailFilterParams.pagination.count"
                    :pagination-params="feeDetailFilterParams.pagination"
                    show-total-page
                    style="margin: 0 0 12px 12px;"
                    @current-change="handlePageChange"
                >
                </abc-pagination>
            </template>
            <cost-check-fee-item-pro-table
                v-else-if="filterParams.sceneType === 1"
                :data-list="hisChargeFormItemGroupByGoods"
                :price-scope-type="costPriceScopeType"
                :price-settle-type="costPriceSettleType"
                :fill-reference-el="fillReferenceEl"
                :loading="costCheckLoading"
                :show-content-empty="!pageLoading"
            ></cost-check-fee-item-pro-table>
            <cost-check-table
                v-else
                ref="costCheckTable"
                :list="costCheckList"
                :price-scope-type="costPriceScopeType"
                :price-settle-type="costPriceSettleType"
                :fill-reference-el="fillReferenceEl"
                :loading="costCheckLoading"
                :show-content-empty="!pageLoading"
                @click-tr="handleTrClick"
            ></cost-check-table>
        </abc-layout-content>

        <div
            v-if="showCostDetailPopover"
            v-abc-click-outside="handleClose"
            v-abc-loading.middle.gray="detailLoading"
            class="cost-check-fixed-content"
        >
            <div class="filter-toolbar">
                <h4>计费明细</h4>

                <abc-button
                    v-if="!disabledOperationCostCheck"
                    :loading="refundLoading"
                    :disabled="checkedList.length === 0"
                    class="refund-btn"
                    type="danger"
                    :count="checkedList.length"
                    @click="handleClickRefund"
                >
                    撤销计费
                </abc-button>
            </div>
            <hospital-cost-detail-pro-table
                :show-check-box="!disabledOperationCostCheck"
                :his-charge-sheet-type="curCostCheckItem && curCostCheckItem.hisChargeSheetType"
                :price-scope-type="costDetailPriceScopeType"
                :price-settle-type="costDetailPriceSettleType"
                :list="costDetailList"
            ></hospital-cost-detail-pro-table>
        </div>
        <cost-supplement-dialog
            v-if="showCostSupplementDialog"
            v-model="showCostSupplementDialog"
            :supplement-type="1"
            :patient-order="{
                patients: [{
                    patientOrderId: patientOrderId,
                    id: patientOrderId,
                    patientName: patientInfo.name,
                    name: patientInfo.name,
                }],
                list: costSupplementList,
            }"
            :force-enable-supplement="true"
            @supplement-success="fetchCheckFeeList"
        ></cost-supplement-dialog>
    </abc-layout>
</template>

<script>
    import CostCheckTable from '@/views/layout/tables/table-cost-check/index.vue';
    import {
        MedicalPrescriptionTypeList,
        DiagnosisTreatmentTypeList,
        DoctorAdviceStatusList,
    } from '@/views-hospital/medical-prescription/utils/config';
    import HospitalChargeAPI from 'api/hospital/charge.js';
    import costSupplementDialog from '@/views-hospital/cost/components/cost-supplement-dialog.vue';
    import CostPrint from 'src/views-hospital/cost/components/cost-print.vue';
    import {
        HOSPITAL_PRICE_SCOPE_TYPE, HOSPITAL_PRICE_SETTLE_TYPE,
    } from 'utils/constants';
    import TableBanner from 'views/statistics/family-doctor/components/table-banner.vue';
    import { formatMoney } from '@/filters';
    import { DispenseMethodType } from '@/views-hospital/medicine-apply/utils/constant';
    import HospitalCostDetailProTable from 'views/layout/tables/table-hospital-cost-detail/index.vue';
    import CostCheckFeeItemProTable from 'views/layout/tables/table-cost-check-fee-item/index.vue';
    import CostCheckFeeDetailProTable from 'views/layout/tables/table-cost-check-fee-detail/index.vue';
    import AbcSearch from 'components/abc-search/index.vue';
    import { debounce } from 'utils/lodash';

    export default {
        name: 'CostCheck',
        components: {
            AbcSearch,
            CostCheckFeeDetailProTable,
            CostCheckFeeItemProTable,
            HospitalCostDetailProTable,
            TableBanner,
            CostCheckTable,
            costSupplementDialog,
            CostPrint,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            patientOrderId: {
                type: String,
                required: true,
            },
            patientInfo: {
                type: Object,
                default: () => {},
            },
            disabledOperationCostCheck: {
                type: Boolean,
                required: false,
            },
            supportSupplement: {
                type: Boolean,
                default: false,
            },
            fillReferenceEl: {
                type: HTMLElement,
                default: null,
            },
            pageLoading: {
                type: Boolean,
                default: false,
            },
            isPageLoading: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                diagnosisTreatmentTypeList: DiagnosisTreatmentTypeList,
                doctorAdviceStatusList: DoctorAdviceStatusList,

                filterParams: {
                    chargedTimeStart: '',
                    chargedTimeEnd: '',
                    adviceType: '',
                    sceneType: 0,
                },
                feeDetailFilterParams: {
                    name: '',
                    pagination: {
                        count: 0,
                        pageIndex: 0,
                        pageSize: 13,
                    },
                },

                costCheckLoading: false,
                costCheckList: [],
                hisChargeFormItemGroupByGoods: [],
                costCheckListDetailList: [],
                costCheckSummary: null,

                costPriceScopeType: HOSPITAL_PRICE_SCOPE_TYPE.SOURCE,
                costPriceSettleType: HOSPITAL_PRICE_SETTLE_TYPE.SOURCE,

                costDetailPriceScopeType: HOSPITAL_PRICE_SCOPE_TYPE.SOURCE,
                costDetailPriceSettleType: HOSPITAL_PRICE_SETTLE_TYPE.SOURCE,

                curCostCheckItem: null,

                showCostDetailPopover: false,
                detailLoading: false,
                refundLoading: false,
                costDetailList: [],

                showCostSupplementDialog: false,

                detailFillReferenceEl: null,
            };
        },
        computed: {
            medicalPrescriptionTypeList() {
                return [
                    ...MedicalPrescriptionTypeList,
                    {
                        label: '非医嘱',
                        value: -1,
                    },
                ];
            },
            checkedList() {
                return this.costDetailList.filter((it) => it.checked) || [];
            },
            bannerList() {
                const costMapping = {
                    [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: {
                        realTotalPrice: 'totalPrice',
                        realChargedTotalPrice: 'chargedTotalPrice',
                        realUnchargedTotalPrice: 'unchargedTotalPrice',
                    },
                    [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: {
                        realTotalPrice: 'shebaoDisplayTotalPrice',
                        realChargedTotalPrice: 'shebaoDisplayChargedTotalPrice',
                        realUnchargedTotalPrice: 'shebaoDisplayUnchargedTotalPrice',
                    },
                    [HOSPITAL_PRICE_SCOPE_TYPE.SETTLE]: {
                        realTotalPrice: 'settleTotalPrice',
                        realChargedTotalPrice: 'settleChargedTotalPrice',
                        realUnchargedTotalPrice: 'settleUnchargedTotalPrice',
                    },
                };
                return this.costCheckSummary ? [
                    {
                        text: '总费用',
                        value: formatMoney(this.costCheckSummary[costMapping[this.costPriceScopeType].realTotalPrice]),
                    },
                    {
                        text: '已计费',
                        value: formatMoney(this.costCheckSummary[costMapping[this.costPriceScopeType].realChargedTotalPrice]),
                    },
                    {
                        text: '未计费',
                        value: formatMoney(this.costCheckSummary[costMapping[this.costPriceScopeType].realUnchargedTotalPrice]),
                    },
                    {
                        text: '缴纳金额',
                        value: formatMoney(this.costCheckSummary.depositTotalFee),
                    },
                    {
                        text: '押金余额',
                        value: formatMoney(this.costCheckSummary.depositTotalFee - this.costCheckSummary[costMapping[this.costPriceScopeType].realChargedTotalPrice]),
                    },
                ] : [];
            },

            quickList() {
                return this.$abcPage.$store.initialQL;
            },

            costSupplementList() {
                return this.quickList.map((o) => {
                    const { bedNo } = o;

                    return {
                        patientName: o.patient?.name,
                        id: o.id,
                        name: o.patient?.name,
                        patientOrderId: o.id,
                        tip: bedNo && bedNo.length === 1 ? `(0${bedNo})` : `(${bedNo})`,
                    };
                });
            },
            filterChargedTimeRangeValue: {
                get() {
                    return [this.filterParams.chargedTimeStart, this.filterParams.chargedTimeEnd];
                },
                set(val) {
                    const [chargedTimeStart, chargedTimeEnd] = val;
                    this.filterParams.chargedTimeStart = chargedTimeStart;
                    this.filterParams.chargedTimeEnd = chargedTimeEnd;
                },
            },
        },
        watch: {
            patientOrderId: {
                handler(val,oldVal) {
                    if (val !== oldVal) {
                        this.fetchCheckFeeList(this.isPageLoading);
                    }
                },
                immediate: true,
            },
            'filterParams.sceneType': {
                handler(val) {
                    if (val === 2) {
                        this.detailTimer = setTimeout(() => {
                            this.detailFillReferenceEl = this.$refs.hospitalCostCheckDetailWrapper ?? null;
                        });
                    }
                },
            },
        },
        created() {
            this._debounceFetchCheckFeeList = debounce(this.fetchCheckFeeList, 500, true);
        },
        beforeDestroy() {
            if (this.detailTimer) {
                clearTimeout(this.detailTimer);
            }
        },
        methods: {
            // 用于外部调用
            changeFeeTypeName() {
                this.fetchCheckFeeList();
            },
            handleChangeDate() {
                this.feeDetailFilterParams.pagination.pageIndex = 0;
                this.fetchCheckFeeList();
            },

            handleChangeType() {
                this.feeDetailFilterParams.pagination.pageIndex = 0;
                this.fetchCheckFeeList();
            },
            handleSearchNameChange() {
                this.feeDetailFilterParams.pagination.pageIndex = 0;
                this._debounceFetchCheckFeeList();
            },
            handleTableMounted(data) {
                const { paginationLimit } = data;
                this.feeDetailFilterParams.pagination.pageSize = paginationLimit;
                this.fetchCheckFeeList();
            },
            handleChangeSceneType(val) {
                if (val !== 2) {
                    this.feeDetailFilterParams.name = '';
                    this.feeDetailFilterParams.pagination.pageIndex = 0;
                    this.fetchCheckFeeList();
                }
            },
            handlePageChange(val) {
                this.feeDetailFilterParams.pagination.pageIndex = val - 1;
                this.fetchCheckFeeList();
            },
            handleRefundFeeSuccess() {
                if (this.costCheckListDetailList <= 1 && this.feeDetailFilterParams.pagination.pageIndex > 0) {
                    this.feeDetailFilterParams.pagination.pageIndex = this.feeDetailFilterParams.pagination.pageIndex - 1;
                }
                this.fetchCheckFeeList();
            },
            async fetchCheckFeeList(isPageLoading) {
                if (!this.patientOrderId) return;

                try {
                    if (isPageLoading) {
                        this.$emit('update:pageLoading',true);
                    } else {
                        this.costCheckLoading = true;
                    }
                    if (this.filterParams.sceneType === 2) {
                        const detailFilterParams = {
                            adviceType: this.filterParams.adviceType,
                            chargedTimeStart: this.filterParams.chargedTimeStart,
                            chargedTimeEnd: this.filterParams.chargedTimeEnd,
                            limit: this.feeDetailFilterParams.pagination.pageSize,
                            name: this.feeDetailFilterParams.name,
                            offset: this.feeDetailFilterParams.pagination.pageSize * this.feeDetailFilterParams.pagination.pageIndex,
                        };
                        const { data } = await HospitalChargeAPI.fetchCheckFeeDetailList(this.patientOrderId, detailFilterParams);
                        this.costCheckListDetailList = data.page.rows || [];
                        this.costCheckSummary = data.summary;
                        this.costPriceScopeType = data.priceScopeType;
                        this.costPriceSettleType = data.settleType || HOSPITAL_PRICE_SETTLE_TYPE.SOURCE;
                        this.feeDetailFilterParams.pagination.count = data.page.total;
                    } else {
                        const requestParams = {
                            adviceType: this.filterParams.adviceType,
                            sceneType: this.filterParams.sceneType,
                            adviceRuleCreateDate: this.filterParams.chargedTimeStart,
                            adviceRuleCreateEndDate: this.filterParams.chargedTimeEnd,
                        };
                        const { data } = await HospitalChargeAPI.fetchCheckFeeList(this.patientOrderId, requestParams);
                        this.costCheckList = data.rows;
                        this.hisChargeFormItemGroupByGoods = data.hisChargeFormItemGroupByGoods || [];
                        this.costCheckSummary = data.summary;
                        this.costPriceScopeType = data.priceScopeType;
                        this.costPriceSettleType = data.settleType || HOSPITAL_PRICE_SETTLE_TYPE.SOURCE;
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.$emit('update:pageLoading',false);
                    this.costCheckLoading = false;
                }
            },

            handleTrClick(item) {
                this.showCostDetailPopover = true;
                if (this.curCostCheckItem && this.curCostCheckItem.id === item.id) return;
                this.curCostCheckItem = item;
                this.fetchCheckFeeDetail();
            },
            handleClose() {
                this.showCostDetailPopover = false;
                this.curCostCheckItem = null;
                const { handleClearSelected } = this.$refs.costCheckTable;
                handleClearSelected && handleClearSelected();
                this.costDetailList = [];
            },
            async fetchCheckFeeDetail() {
                this.detailLoading = true;
                const {
                    goodsId,
                    adviceRuleItemId,
                    hisChargeSheetType,
                    goodsSignatureKey,
                } = this.curCostCheckItem;
                const { data } = await HospitalChargeAPI.fetchCheckFeeDetail(this.patientOrderId, goodsId, {
                    hisChargeSheetType,
                    goodsSignatureKey,
                    adviceRuleItemId,
                });
                this.costDetailList = data?.rows.map((it) => {
                    it.checked = false;
                    return it;
                }) || [];
                this.costDetailPriceScopeType = data.priceScopeType;
                this.costDetailPriceSettleType = data.settleType || HOSPITAL_PRICE_SETTLE_TYPE.SOURCE;
                this.detailLoading = false;
            },
            handleClickRefund() {
                if (this.checkedList.length === 0) {
                    return;
                }
                if (this.checkedList.some((x) => x.dispensingMethod === DispenseMethodType.AUTO)) {
                    this.message = this.$confirm({
                        type: 'warn',
                        title: '撤销确认',
                        content: '撤销计费将同时退药。',
                        referenceEl: document.querySelector('.cost-check-fixed-content'),
                        closeAfterConfirm: true,
                        onConfirm: this.handleRefund,
                    });
                    return;
                }
                this.$confirm({
                    type: 'warn',
                    title: '撤销确认',
                    content: '是否确认撤销本次计费？',
                    referenceEl: document.querySelector('.cost-check-fixed-content'),
                    onConfirm: this.handleRefund,
                });
            },
            async handleRefund() {
                this.refundLoading = true;
                try {
                    await HospitalChargeAPI.refundBill(this.patientOrderId, {
                        chargeFormItemIds: this.checkedList.map((it) => it.id),
                    });
                    this.$Toast({
                        message: '撤销计费成功',
                        type: 'success',
                    });
                    this.fetchCheckFeeDetail();
                } catch (err) {
                    console.error(err);
                } finally {
                    this.refundLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .hospital-cost-check-content {
        position: relative;
        height: 100% !important;

        .filter-toolbar {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            h4 {
                font-size: 14px;
                font-weight: bold;
                line-height: 32px;
            }

            .buttons-wrapper {
                margin-left: auto;
            }
        }

        .cost-check-fixed-content {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 11;
            width: 790px;
            height: 100%;
            min-height: 400px;
            max-height: 600px;
            padding: 16px;
            overflow-y: auto;
            overflow-y: overlay;
            background: var(--abc-color-S2);
            border-radius: 4px 0 0 4px;
            box-shadow: 0 3px 18px 0 rgba(0, 0, 0, 0.15);

            @include scrollBar;

            .refund-btn {
                position: sticky;
                top: 16px;
                right: 0;
                margin-left: auto;
            }
        }
    }
</style>
<style module lang="scss" src="@/styles/theme.module.scss">
</style>
