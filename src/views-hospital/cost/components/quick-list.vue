<template>
    <div class="quick-list-wrapper">
        <div class="quick-search-wrapper">
            <div class="search-patient-wrapper">
                <abc-icon v-if="scrollParamsKey" icon="cross_small" @click.native="scrollParamsKey = ''"></abc-icon>
                <abc-input
                    v-model.trim="scrollParamsKey"
                    type="text"
                    placeholder="患者/床号/住院号"
                >
                    <abc-search-icon slot="prepend"></abc-search-icon>
                </abc-input>
            </div>
        </div>

        <div class="quick-content-wrapper">
            <div class="quick-list-tabs" style="height: 41px;">
                <div v-if="scrollParamsKey && totalCount > -1" class="search-tips">
                    <div>
                        搜索到<span style=" margin: 0 4px; font-size: 12px; color: #000000;">{{ totalCount }}</span>条信息
                    </div>
                </div>
                <template v-else>
                    <abc-tabs
                        v-model="selectedTab"
                        size="middle"
                        :option="tabsOption"
                        @change="changeTab"
                    ></abc-tabs>

                    <div class="quick-list-tabs_right_part">
                        <!--                        <abc-dropdown-->
                        <!--                            style="width: 70px;"-->
                        <!--                            @change="handleChangeFilter"-->
                        <!--                        >-->
                        <!--                            <span slot="reference" style="color: #7a8794;">-->
                        <!--                                {{ patientFilter === 1 ? '欠费患者' : '全部患者' }}<abc-icon icon="dropdown_triangle" size="14px" color="#aab4bf"></abc-icon>-->
                        <!--                            </span>-->
                        <!--                            <abc-dropdown-item :value="0">-->
                        <!--                                全部患者-->
                        <!--                            </abc-dropdown-item>-->
                        <!--                            <abc-dropdown-item :value="1">-->
                        <!--                                欠费患者-->
                        <!--                            </abc-dropdown-item>-->
                        <!--                        </abc-dropdown>-->
                    </div>
                </template>
            </div>

            <!--收费QL-->
            <div
                v-abc-loading.middle.gray.noCover="loading"
                class="quick-list-small"
                style="height: calc(100% - 36px);"
            >
                <ul>
                    <quick-list-item
                        v-for="item in quickList"
                        :key="item.id"
                        :quick-item="item"
                        :is-active="currentQuickItem && currentQuickItem.id === item.id"
                        @select="handleSelect(item)"
                    >
                    </quick-list-item>
                </ul>

                <p
                    v-if="!quickList.length && !loading"
                    class="no-patient"
                >
                    暂无患者
                </p>
                <div v-if="quickList.length" class="no-more">
                    没有更多了
                </div>
            </div>
        </div>

        <div class="quick-footer-wrapper">
            <div class="quick-footer-title">
                <abc-tabs
                    v-model="tabValue"
                    size="small"
                    :custom-gap="16"
                    disable-indicator
                    :border-style="{ borderBottom: 'none' }"
                    :option="quickFooterTabsOption"
                ></abc-tabs>
            </div>
            <div class="quick-footer-content">
                <div class="entry-item" @click="showDepositManagerDialog = true">
                    <img src="~assets/images/icon/<EMAIL>" alt="" />
                    <div class="content">
                        押金管理
                    </div>

                    <div class="describe">
                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                    </div>
                </div>
                <div class="entry-item" @click="showPrintBatchDialog = true">
                    <img src="~assets/images/icon/<EMAIL>" alt="" />
                    <div class="content">
                        批量打印费用清单
                    </div>

                    <div class="describe">
                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                    </div>
                </div>
            </div>
        </div>

        <cost-deposit-manager-dialog
            v-if="showDepositManagerDialog"
            v-model="showDepositManagerDialog"
            :ward-area-id="currentWardAreaId"
            :ward-area-name="currentWardAreaName"
        ></cost-deposit-manager-dialog>

        <cost-print-batch-dialog
            v-if="showPrintBatchDialog"
            v-model="showPrintBatchDialog"
        ></cost-print-batch-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapState } from 'vuex';
    import QuickListItem from 'src/views-hospital/components/quick-list/quick-list-item.vue';
    import CostDepositManagerDialog from './cost-deposit-manager-dialog.vue';
    import CostPrintBatchDialog from './cost-print-batch-dialog.vue';
    import { RouterNameKeys } from '../core/routes.js';
    import { HospitalQLSceneType } from 'utils/constants-hospital.js';
    import { debounce } from 'utils/lodash';
    import clone from 'utils/clone';

    export default {
        name: 'QuickList',
        components: {
            CostDepositManagerDialog,
            QuickListItem,
            CostPrintBatchDialog,
        },
        mixins: [],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                params: {
                    sceneType: HospitalQLSceneType.NURSE_MODULE_LIST,
                    limit: 500,
                    offset: 0,
                },
                scrollParamsKey: '',
                loading: false,
                totalCount: -1,
                selectedTab: HospitalQLSceneType.NURSE_MODULE_LIST,
                tabValue: 0,
                patientFilter: 0,

                showDepositManagerDialog: false,
                showPrintBatchDialog: false,
            };
        },
        computed: {
            ...mapState('hospitalGlobal', [
                'currentWardAreaId',
                'currentWardAreaName',
            ]),
            tabsOption() {
                return [
                    {
                        label: '在院',
                        value: HospitalQLSceneType.NURSE_MODULE_LIST,
                    },
                ];
            },

            quickFooterTabsOption() {
                return [
                    {
                        label: '小工具',
                        value: 0,
                    },
                ];
            },

            quickList() {
                return this.$abcPage.$store.quickList;
            },

            currentQuickItem() {
                return this.$abcPage.$store.selectedQuickItem;
            },
        },
        watch: {
            scrollParamsKey(val) {
                if (val) {
                    this._debounceSearch();
                } else {
                    this.clearKey();
                }
            },
        },
        created() {
            this.loading = true;
            this.selectedTab = +this.$route.query.tab || HospitalQLSceneType.NURSE_MODULE_LIST;
            this.initQuickList();

            const qlServiceCallback = {
                onUpdate: () => {
                    this.filterByKeyword();
                },
            };
            this.$abcPage.QlService.addNurseQlCallback(qlServiceCallback);
            this.$on('hook:beforeDestroy', () => {
                this.$abcPage.QlService.removeNurseQlCallback(qlServiceCallback);
            });

            // 注册防抖search函数
            this._debounceSearch = debounce(async () => {
                this.filterByKeyword();
            }, 250, true);
        },
        methods: {

            handleChangeFilter(val) {
                this.patientFilter = val;
            },

            /**
             * @desc 根据条件初始化quicklist
             * <AUTHOR>
             * @date 2019/11/20 15:52:32
             */
            async initQuickList() {
                this.loading = true;
                const data = await this.$abcPage.QlService.initData({
                    ...this.params,
                    wardId: this.currentWardAreaId,
                });
                const newData = clone(data);
                this.$abcPage.$store.setQuickList(newData, true);
                this.selectFirst();
                this.loading = false;
            },

            /**
             * @desc keyword过滤
             * <AUTHOR>
             * @date 2023/02/22 14:51:29
             */
            async filterByKeyword() {
                this.loading = true;
                const data = await this.$abcPage.QlService.filterByKeyword(this.scrollParamsKey);
                this.totalCount = data.length;
                const newData = clone(data);
                this.$abcPage.$store.setQuickList(newData);
                this.selectFirst();
                this.loading = false;
            },

            /** ------------------------------------------------------------------------------------------
             * quicklist 点击后的处理函数
             */
            handleSelect(selectedItem) {
                if (selectedItem && selectedItem.id) {
                    this.$abcPage.$store.setSelectedQuickItem(selectedItem);
                    this.replaceRouter(selectedItem.id);
                } else {
                    this.replaceRouter();
                }
            },

            replaceRouter(id) {
                if (id) {
                    this.$router.replace({
                        name: RouterNameKeys.hospitalCostForm,
                        params: {
                            ...this.$route.params,
                            id,
                        },
                        query: {
                            ...this.$route.query,
                            tab: this.selectedTab,
                        },
                    });
                } else {
                    this.$router.replace({
                        name: RouterNameKeys.hospitalCostBlank,
                    });
                }
            },
            selectFirst() {
                let selectedItem = null;
                if (this.quickList.length > 0) {
                    selectedItem = this.quickList[0];
                } else {
                    selectedItem = null;
                }
                this.handleSelect(selectedItem);
            },

            clearKey() {
                this.scrollParamsKey = '';
                this.loading = true;
                this.filterByKeyword();
            },

            /**
             * @desc 切换tab
             * <AUTHOR>
             * @date 2019/11/18 15:35:19
             */
            changeTab(index, item) {
                this.selectedTab = item.value;
                this.initQuickList();
            },
        },
    };
</script>
