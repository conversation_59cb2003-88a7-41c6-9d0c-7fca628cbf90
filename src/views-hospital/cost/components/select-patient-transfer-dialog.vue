<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        title=""
        content-styles="padding: 0"
    >
        <abc-transfer-v2
            :data="list"
            :default-checked-keys="defaultCheckedKeys"
            :result-width="280"
            :props="{
                label: 'displayName',
                subLabel: 'tip'
            }"
            leaf-icon="s-role-color"
            node-key="id"
            show-check-all
            result-title="请选择患者"
            @confirm="confirmHandle"
            @cancel="showDialog = false"
        ></abc-transfer-v2>
    </abc-dialog>
</template>

<script>
    export default {
        props: {
            value: Boolean,
            allPatients: {
                type: Array,
                default() {
                    return [];
                },
            },
            currentPatients: {
                type: Array,
                default() {
                    return [];
                },
            },
        },
        data() {
            return {
                defaultCheckedKeys: [],
                list: [],
            };
        },

        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    return this.$emit('input', v);
                },
            },
        },
        created() {
            this.defaultCheckedKeys = this.currentPatients.map((item) => {
                return item.id;
            });

            this.list = this.allPatients.map((item) => {
                return {
                    ...item,
                    displayName: item.patientName || item.name,
                };
            });
        },
        methods: {
            confirmHandle(payload) {
                this.$emit('confirm', payload);
                this.showDialog = false;
            },
        },
    };
</script>
