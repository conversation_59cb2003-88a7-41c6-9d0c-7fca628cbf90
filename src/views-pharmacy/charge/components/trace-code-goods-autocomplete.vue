<template>
    <div
        id="trace-code-autocomplete-wrapper"
        :class="{
            'is-disabled': disabled,
            'is-piece': showPieceSearch
        }"
    >
        <goods-autocomplete
            ref="goodsAutocomplete"
            v-abc-auto-focus
            :search.sync="search"
            :pharmacy-no="pharmacyNo"
            version="3"
            class="goods-autocomplete-wrapper"
            custom-class="pharmacy-goods-autocomplete"
            :suggestion-titles="suggestionTitles"
            :suggestion-items="suggestionItems"
            :width="500"
            :auto-clear="false"
            inner-width="720px"
            :json-type="jsonType4Search"
            icon-name="n-search-line-medium"
            icon-size="16"
            ignore-disable-no-stock-goods
            is-support-manufacturer-filter
            :focus-search="true"
            :selected-manufacturer="selectedManufacturer"
            :create-manufacturer-options="createManufacturerOptions"
            :custom-filter-suggestions="filterManufacturer"
            :clear-manufacturer-data="clearManufacturerData"
            :disabled="disabled"
            disabled-package-price-is-null
            :spec="cMSpec"
            size="large"
            data-cy="pharmacy-goods-button-追溯码管理-搜索"
            placeholder="追溯码 / 药名 / 条形码"
            focus-placeholder="追溯码 / 药名 / 条形码"
            clearable
            :focus-show="true"
            :split-by-not-initial-stock="splitByNotInitialStock"
            :need-handle-trace-code="true"
            @fetch-data="(val)=>{
                $emit('fetch-data', val)
            }"
            @clear="handleClear"
            @blur="handleBlur"
            @focus="handleFocus"
            @enter="handleEnter"
            @selectGoods="handleSelectGoods"
        ></goods-autocomplete>
        <div
            class="btn-placeholder"
            style="padding-right: 20px;"
            data-cy="pharmacy-goods-button-追溯码管理-搜索按钮"
        >
            搜索
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import { GoodsTypeEnum } from '@abc/constants';
    import {
        displayInventory,
        goodsHoverTitle,
        multiMedicalFeeGrade2Str,
    } from '@/filters/index.js';
    import GoodsAutocomplete from 'views/layout/goods-autocomplete/goods-autocomplete.vue';
    import {
        Ingredient,
        IngredientLevelObj,
        IngredientObj,
        OtcType,
        OtcTypeLevelObj,
        OtcTypeObj,
    } from 'views/common/inventory/constants.js';
    import { TagV2 as AbcTagV2 } from '@abc/ui-pc';
    import { GoodsSubTypeEnum } from '@abc/constants/src';
    import ManufacturerSelect from 'views/inventory/common/manufacturer-select/index.vue';
    import useAutoCompleteManufacturerSelect from 'views/inventory/common/manufacturer-select';

    export default {
        name: 'PharmacyGoodsAutocomplete',
        components: {
            GoodsAutocomplete,
        },
        props: {
            profitClassificationList: {
                type: Array,
                default: () => [],
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            showPieceSearch: {
                type: Boolean,
                default: false,
            },
            splitByNotInitialStock: {
                type: Boolean,
                default: false,
            },
            disabledChange: {
                type: Boolean,
                default: false,
            },
            search: {
                type: String,
                default: '',
            },
            pharmacyNo: Number,
        },
        setup() {
            const {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            } = useAutoCompleteManufacturerSelect();

            return {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            };
        },
        data() {
            return {

            };
        },
        computed: {
            searchName() {
                return !this.showPieceSearch ? '全部' : '饮片';
            },
            jsonType4Search() {
                if (this.showPieceSearch) {
                    return [
                        {
                            type: GoodsTypeEnum.MEDICINE, subType: [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine],
                        },
                    ];
                }
                return [
                    { type: GoodsTypeEnum.MEDICINE },
                    { type: GoodsTypeEnum.MATERIAL },
                    { type: GoodsTypeEnum.GOODS },
                ];
            },
            suggestionTitles() {
                const handleManufacturerChange = (e) => {
                    console.log('handleManufacturerChange',e);
                    this.$refs.goodsAutocomplete.onFilterChange();
                };
                return [
                    {
                        label: '商品编码',
                        style: 'width:72px;margin-right: 5px;flex: none;',
                    },
                    {
                        label: '名称',
                        style: 'flex:1;margin-right: 5px',
                    },
                    {
                        label: '规格',
                        style: 'margin-right: 5px;width: 105px',
                    },
                    {
                        label: '生产厂家',
                        className: 'gray',
                        render: () => {
                            return <ManufacturerSelect
                            size="tiny"
                            placeholder="生产厂家"
                            value={this.selectedManufacturer}
                            manufacturerOptions={this.manufacturerOptions}
                            style='width: 100px;margin-right: 5px'
                            onInput={(v) => {
                                this.selectedManufacturer = v;
                            }}
                            onChange={handleManufacturerChange}
                        >
                        </ManufacturerSelect>;
                        },
                    },
                    {
                        label: '库存',
                        style: 'width: 60px;margin-right: 5px',
                    },
                    {
                        label: '医保',
                        style: 'width: 80px;text-align: center;',
                    },
                    {
                        label: '备注',
                        className: 'gray ellipsis',
                        style: 'width: 112px;margin-right: 5px',
                    },

                ];
            },
            suggestionItems() {
                return [
                    {
                        prop: 'shortId',
                        style: 'width:72px;margin-right: 5px;flex: none;',
                        titleFunction: (suggestion) => {
                            return suggestion.shortId;
                        },
                        formatFunction: (suggestion) => {
                            return suggestion.shortId;
                        },
                    },
                    {
                        prop: 'name',
                        style: 'flex:1;margin-right: 5px;',
                        titleFunction: goodsHoverTitle,
                        render: (h, suggestion) => {
                            const {
                                displayName,
                                name,
                                dangerIngredient,
                                otcType,
                                profitCategoryType,
                            } = suggestion;
                            const _arr = [];

                            if (otcType & OtcType.NON_OTC) {
                                _arr.push({
                                    label: OtcTypeObj[OtcType.NON_OTC],
                                    value: OtcType.NON_OTC,
                                    level: OtcTypeLevelObj[OtcType.NON_OTC],
                                    minWidth: 32,
                                });
                            }
                            if (dangerIngredient & Ingredient.MA_HUANG_JIAN) {
                                _arr.push({
                                    label: IngredientObj[Ingredient.MA_HUANG_JIAN],
                                    value: Ingredient.MA_HUANG_JIAN,
                                    level: IngredientLevelObj[Ingredient.MA_HUANG_JIAN],
                                });
                            }
                            const res = this.profitClassificationList.find((it) => it.id === profitCategoryType);
                            if (res) {
                                _arr.push({
                                    label: res.name,
                                    value: profitCategoryType,
                                    level: 'success',
                                });
                            }

                            return (
                            <div style="flex:2;margin-right: 5px;display: inline-flex">
                                <span class="ellipsis name" style="display:inline-block;max-width:80%">{displayName || name || ''}</span>
                                <span>
                                        {
                                            _arr.map((it) => {
                                                return <AbcTagV2 style="margin-left: 4px;" variant="outline"
                                                                 shape="round"
                                                                 size="tiny"
                                                                 min-width={it.minWidth}
                                                                 theme={it.level}>{it.label}</AbcTagV2>;
                                            })
                                        }
                                    </span>
                            </div>
                            );
                        },
                    },
                    {
                        prop: 'displaySpec',
                        className: 'gray',
                        style: 'margin-right: 5px;width: 105px;',
                        formatFunction: (suggestion) => {
                            return suggestion.displaySpec;
                        },
                        titleFunction: (suggestion) => {
                            return suggestion.displaySpec;
                        },
                    },
                    {
                        prop: 'manufacturer',
                        className: 'gray',
                        style: 'width: 100px;padding:4px;margin-right: 5px;',
                        formatFunction: (suggestion) => {
                            return suggestion.manufacturer;
                        },
                        titleFunction: (suggestion) => {
                            return suggestion.manufacturer;
                        },
                    },
                    {
                        prop: 'displayInventory',
                        className: 'gray',
                        style: 'width: 60px;margin-right: 5px;',
                        formatFunction: (suggestion) => {
                            return displayInventory(suggestion);
                        },
                        titleFunction: (suggestion) => {
                            return displayInventory(suggestion);
                        },
                    },
                    {
                        prop: 'medicalFeeGrade',
                        className: 'gray',
                        style: 'width: 80px;text-align: center;',
                        formatFunction: (suggestion) => {
                            return multiMedicalFeeGrade2Str(suggestion);
                        },
                    },
                    {
                        prop: 'remark',
                        className: 'gray ellipsis',
                        style: 'width: 112px;margin-right: 5px;',
                        formatFunction: (suggestion) => {
                            return suggestion.remark;
                        },
                        titleFunction: (suggestion) => {
                            return suggestion.remark;
                        },
                    },

                ];
            },
            cMSpec() {
                return this.showPieceSearch ? '' : '';
            // return this.showPieceSearch ? '中药饮片' : '';
            },
        },
        methods: {
            handleClear() {
                this.clearManufacturerData();
                this.$emit('clear');
            },
            handleBlur() {
                this.$emit('blur');
            },
            handleFocus() {
                this.$emit('focus');
            },
            handleSelectGoods(goods) {
                this.$emit('select', {
                    goods,
                });
            },
            handleSearchRangeSwitch() {
                this.$emit('handle-search-range-switch');
            },
            handleEnter(event) {
                this.$emit('enter', event);
            },
            focus() {
                this.$refs.goodsAutocomplete?.focusInput();
            },
            clearKeyword() {
                this.$refs.goodsAutocomplete?.clearQueryString();
            },
        },
    };
</script>

<style lang="scss">
#trace-code-autocomplete-wrapper {
    position: relative;
    height: 40px;

    &:not(.is-disabled) .abc-input__inner {
        padding-right: 112px;
        padding-left: 40px;
        border-width: 2px;
        box-shadow: none !important;

        &:hover {
            border-color: $theme6 !important;
        }

        &:focus,
        &:active {
            border-color: $theme2 !important;
        }
    }

    .clear-btn {
        right: 80px !important;
    }

    .btn-placeholder {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 4;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 85px;
        height: 40px;
        padding-right: 16px;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 40px; /* 157.143% */
        color: var(--abc-color-cp-white, #ffffff);
        cursor: default;
        background: url("~assets/images/biz-pharmacy/retail-search-btn-default.png") no-repeat center;
        background-size: cover;
        border-radius: $borderRadiusSmall;
    }
}
</style>

