<template>
    <abc-dialog
        v-if="showDialog"
        v-bind="dialogProps"
        ref="dialog"
        v-model="showDialog"
        :show-close="showClose"
        :custom-class="customClass"
        @open="onDialogOpen"
        @close="onDialogClose"
    >
        <template slot="title">
            <slot name="title">
                <abc-layout>
                    <abc-section>
                        <abc-space>
                            <abc-title level="1">
                                {{ title }}
                            </abc-title>
                            <template v-if="showTitleAppend">
                                <abc-p v-if="orderNo" gray>
                                    {{ orderNo }}
                                </abc-p>
                                <abc-tag-v2 v-if="statusName" v-bind="tagProps">
                                    {{ statusName }}
                                </abc-tag-v2>
                            </template>
                        </abc-space>
                    </abc-section>
                </abc-layout>
            </slot>
        </template>
        <template v-if="$slots['title-append']" slot="title-append">
            <slot name="title-append"></slot>
        </template>
        <abc-layout
            v-abc-loading.coverOpaque="loading"
            :has-sidebar="!!rightWidth"
            class="order-layout"
        >
            <abc-layout-content class="order-content" :style="leftStyle">
                <slot></slot>
            </abc-layout-content>

            <abc-layout-sidebar v-if="$slots.right && rightWidth" class="order-sidebar" :width="rightWidth">
                <slot name="right"></slot>
            </abc-layout-sidebar>
        </abc-layout>
        <template slot="footer">
            <slot name="footer">
                <abc-flex justify="flex-end">
                    <abc-button
                        type="blank"
                        @click="showDialog = false"
                    >
                        关闭
                    </abc-button>
                </abc-flex>
            </slot>
        </template>


        <!--gsp审批-->
        <abc-dialog
            v-if="showReviewDialog"
            v-model="showReviewDialog"
            :title="approvalPrefix"
            content-styles="width:480px;"
            class="pharmacy__components__approval-edit__dialog"
            append-to-body
            @open="onReviewDialogOpen"
            @close="onReviewDialogClose"
        >
            <slot name="review-content" :data="reviewData">
                <abc-form
                    ref="formData"
                    label-position="left-top"
                    :label-width="72"
                    item-no-margin
                >
                    <abc-flex vertical :gap="12">
                        <abc-form-item :label="`${approvalPrefix}结果`">
                            <span
                                :style="{
                                    color: isAgree ? $store.state.theme.style.G1 : $store.state.theme.style.R1, fontWeight: 'bold'
                                }"
                            >{{ isAgree ? '同意' : '驳回' }}</span>
                        </abc-form-item>
                        <abc-form-item
                            :label="isAgree ? `${approvalPrefix}意见` : '驳回原因'"
                            class="label-start-wrapper"
                            :required="isReject"
                            hidden-red-dot
                        >
                            <abc-textarea
                                v-model="reviewData.content"
                                :width="360"
                                :height="64"
                                placeholder=""
                            ></abc-textarea>
                        </abc-form-item>
                    </abc-flex>
                </abc-form>
            </slot>
            <template slot="footer">
                <div class="dialog-footer">
                    <abc-space>
                        <abc-button
                            @click="handleConfirm"
                        >
                            确定
                        </abc-button>
                        <abc-button
                            type="blank"
                            @click="handleCancel"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </div>
            </template>
        </abc-dialog>
    </abc-dialog>
</template>

<script>
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';

    export default {
        name: 'OrderFrameDialog',
        props: {
            value: Boolean,
            title: String,
            loading: Boolean,
            reviewTitle: {
                type: String,
                default: '审批',
            },
            orderNo: {
                type: String,
                default: '',
            },
            statusName: {
                type: String,
                default: '',
            },
            size: String,
            rightWidth: {
                type: Number,
                default: 0,
            },
            showTitleAppend: {
                type: Boolean,
                default: false,
            },
            needComputedDialogStyle: {
                type: Boolean,
                default: false,
            },
            showClose: {
                type: Boolean,
                default: true,
            },
            showFullscreenToggle: {
                type: Boolean,
                default: false,
            },
            responsiveDialog: {
                type: Boolean,
                default: false,
            },
            beforeClose: {
                type: Function,
            },
            tagConfig: {
                type: Object,
                default: () => ({
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                    size: 'medium',
                }),
            },
            dialogConfig: {
                type: Object,
                default: () => ({}),
            },
            contentStyle: {
                type: Object,
                default: () => ({}),
            },
            gspInstId: {
                type: String,
                default: '',
            },
            customClass: {
                type: String,
                default: '',
            },
        },
        setup(props) {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager(`${props.title}-${Math.random().toString(36).slice(2)}`);

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,
            };
        },
        data() {
            return {
                showDialog: this.value,
                showReviewDialog: false,
                reviewData: {
                    pass: 1,
                    content: '',
                },
            };
        },
        computed: {
            dialogProps() {
                return {
                    title: this.title,
                    'is-scroll': false,
                    'auto-focus': false,
                    class: 'biz-pharmacy-order-frame-dialog',
                    'content-styles': 'padding:0;',
                    'append-to-body': true,
                    responsive: true,
                    showFullscreenToggle: this.showFullscreenToggle,
                    size: this.responsiveDialog ? undefined : (this.size || (this.rightWidth ? 'default' : 'hugely')),
                    disabledKeyboard: this.disabledKeyboard,
                    'before-close': this.beforeClose,
                    ...this.dialogConfig,
                };
            },
            leftStyle() {
                return {
                    width: `calc(100% - ${this.rightWidth}px)`,
                    minWidth: `calc(100% - ${this.rightWidth}px)`,
                    maxWidth: `calc(100% - ${this.rightWidth}px)`,
                    ...this.contentStyle,
                };
            },
            tagProps() {
                return {
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                    size: 'medium',
                    ...this.tagConfig,
                };
            },
            // 是否同意
            isAgree() {
                return this.reviewData.pass === 1;
            },
            // 是否驳回
            isReject() {
                return !this.isAgree;
            },

            approvalPrefix() {
                if (this.gspInstId) return '审批';
                return '审核';
            },
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        methods: {
            onDialogOpen() {
                this.pushDialogName();
                this.$emit('open');
            },
            onDialogClose() {
                this.popDialogName();
                this.$emit('close');
            },
            onReviewDialogOpen() {
                this.pushDialogName(this.approvalPrefix);
            },
            onReviewDialogClose() {
                this.popDialogName(this.approvalPrefix);
            },
            openReviewDialog(type = 'resolve') {
                this.reviewData.pass = type === 'resolve' ? 1 : 0;
                this.showReviewDialog = true;
            },
            handleConfirm() {
                this.$refs.formData.validate((valid) => {
                    if (!valid) {
                        return;
                    }
                    // 审批列表需要知道这个事件
                    this.$emit('confirm');
                    this.$emit('review-confirm', this.reviewData, () => {
                        this.showReviewDialog = false;
                    });
                });

            },
            handleCancel() {
                // 审批列表需要知道这个事件
                this.$emit('cancel');
                this.$emit('review-cancel', this.reviewData);
                this.showReviewDialog = false;
            },
        },
    };
</script>


<style lang="scss">
// 单据基础弹窗样式
.biz-pharmacy-order-frame-dialog {
    .order-layout {
        display: flex;
        width: 100%;
        height: 100%;

        .order-content {
            padding: 24px 14px 24px 24px;
            overflow-x: hidden;
            overflow-y: scroll;
        }

        .order-sidebar {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            padding: 24px 6px 24px 16px;
            overflow-x: hidden;
            overflow-y: scroll;
            background-color: #f9fafc;
            border-left: 1px solid $P8;
        }
    }
}
</style>
