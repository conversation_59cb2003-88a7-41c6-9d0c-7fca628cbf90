<template>
    <div class="external-file-list">
        <div class="external-file-list__navigator">
            <span
                v-for="path in dirPathList"
                :key="path.fullPath"
                class="external-file-list__navigator-item"
                :class="path.isLast ? 'is-disabled' : ''"
            >
                <span
                    class="name"
                    @click="!path.isLast && handleFolderClick(path.fullPath)"
                >
                    {{ path.name }}
                </span>
                <span v-if="!path.isLast" class="divider">></span>
            </span>
        </div>
        <div
            class="external-file-sync"
            @dragover.prevent
            @dragenter.prevent.stop="handleFileDragenter"
            @dragleave.prevent.stop="handleFileDragLeave"
            @drop.prevent="handleFileDrop"
        >
            <ul v-abc-loading="loading" class="external-file-list__item-wrapper">
                <li v-for="file in fileList" :key="file.name" class="external-file-list__item">
                    <div v-if="!file.isFolder" class="external-file-list__item--file">
                        <img class="icon" src="~assets/images/<EMAIL>" alt="" />
                        <a :href="file.url" target="_blank">{{ file.name }}</a>
                        <span class="file-size">{{ file.displaySize }}</span>
                    </div>
                    <div v-else class="external-file-list__item--folder" @click="handleFolderClick(file.fullPath)">
                        <img class="icon" src="~assets/images/<EMAIL>" alt="" />
                        <span>{{ file.name }}</span>
                    </div>
                </li>
            </ul>
            <div class="upload-file-button__wrapper" @click="handleFileUpload">
                <abc-button :loading="loading">
                    上传
                </abc-button>
                <input
                    v-if="showUploadNode"
                    ref="fileInputRef"
                    class="hidden-inp"
                    type="file"
                    @change="handleFileChange"
                />
            </div>
            <div v-show="isDragover" class="external-file-sync__mask">
                <div class="external-file-sync__upload-icon">
                    <abc-icon icon="arrow_up" size="26" color="#fff"></abc-icon>
                </div>
                <span class="external-file-sync__upload-text">上传到文件</span>
            </div>
        </div>
        <verify @loaded="loaded"></verify>
        <abc-dialog
            v-if="showSlideCaptcha"
            v-model="showSlideCaptcha"
            content-styles="padding: 24px;"
            title="请完成安全验证"
        >
            <!-- 验证码挂载点 -->
            <div id="captcha"></div>
        </abc-dialog>
    </div>
</template>

<script>
    import ExternalAPI from '../../api/index';
    import OSSAPI from 'api/oss.js';
    import LoginAPI from 'api/login';
    export default {
        name: 'FileList',

        components: {
            Verify: {
                render(createElement) {
                    const self = this;
                    return createElement('script', {
                        attrs: {
                            type: 'text/javascript',
                            src: '//g.alicdn.com/sd/nvc/1.1.112/guide.js',
                        },
                        on: {
                            load() {
                                self.$emit('loaded');
                            },
                        },
                    });
                },
            },
        },

        data() {
            return {
                loading: true,
                fileList: [],
                isDragover: false,
                draggingCount: 0,
                showSlideCaptcha: false,
                ic: null,
                showUploadNode: true,
            };
        },

        computed: {
            requestFolder() {
                return this.dir === '/' ? '/' : this.dir.substring(1);
            },

            dir() {
                return this.$route.query?.dir || '/';
            },

            dirPathList() {
                const rootDir = {
                    name: '/',
                    fullPath: '/',
                };
                const pathList = [rootDir];
                let path = this.dir;
                // 根目录
                if (path === '/') {
                    return pathList;
                }
                // 删除根目录，获得子目录
                if (path[0] === '/') {
                    path = path.substring(1);
                }
                const dirs = path.split('/');

                dirs.reduce((acc, cur, index) => {
                    const fullPath = `${acc}/${cur}`;
                    pathList.push({
                        name: cur,
                        fullPath,
                        isLast: index === dirs.length - 1,
                    });
                    return fullPath;
                }, '');
                return pathList;
            },

            isSyncDir() {
                return this.requestFolder === '同步盘';
            },
        },

        watch: {
            dir: {
                handler() {
                    this.fetchFileList();
                },
                immediate: true,
            },
        },

        methods: {
            async fetchFileList() {
                this.loading = true;
                try {
                    const { data: { objects = [] } } = await ExternalAPI.fetchOssToolList(this.requestFolder);
                    this.fileList = objects.map((item) => {
                        const name = item.name.replace('/', '');
                        return {
                            ...item,
                            name,
                            fullPath: this.resolveFullPath(name),
                            displaySize: this.convertFileSize(item.size),
                        };
                    });
                } catch (e) {
                    this.$Toast({
                        message: '获取文件列表失败',
                        type: 'error',
                    });
                } finally {
                    this.loading = false;
                }
            },

            resolveFullPath(folderName) {
                let subDir = folderName;
                if (this.dir && this.dir !== '/') {
                    subDir = `${this.dir}/${subDir}`;
                } else {
                    subDir = `/${subDir}`;
                }
                return subDir;
            },

            handleFolderClick(fullPath) {
                this.$router.push({
                    name: this.$route.name,
                    query: {
                        dir: fullPath,
                    },
                });
            },

            convertFileSize(size) {
                if (!size) {
                    return '';
                }
                let res = '';
                const kb = (size / 1024).toFixed(2);
                if (kb > 1024) {
                    res = `${(kb / 1024).toFixed(2)}MB`;
                } else {
                    res = `${kb}KB`;
                }
                return res;
            },

            handleFileDragenter() {
                if (this.isSyncDir) {
                    this.draggingCount++;
                    this.isDragover = true;
                }
            },

            /**
             * @description: 更新上传节点
             * @date: 2023-02-16 15:04:59
             * @author: Horace
             * @param null:
             * @return
            */
            updateUploadNode() {
                this.showUploadNode = false;
                this.$nextTick(() => {
                    this.showUploadNode = true;
                });
            },
            /**
             * @description: 点击上传
             * @date: 2023-02-16 15:05:20
             * @author: Horace
             * @param null:
             * @return
            */
            handleFileUpload(event) {
                if (event && event.target === this.$refs.fileInputRef) return;
                const uploadFileRef = this.$refs.fileInputRef;
                uploadFileRef && uploadFileRef.click();
            },
            /**
             * @description: 之前有文件存在时点击上传
             * @date: 2023-02-16 15:06:42
             * @author: Horace
             * @param null:
             * @return
            */
            handleFileChange(e) {
                if (e.currentTarget.files.length === 0) {
                    return;
                }
                const files = [];
                const _length = e.currentTarget.files.length;
                for (let i = 0; i < _length; i++) {
                    const file = event.currentTarget.files[i];
                    files.push(file);
                }
                this.initCaptcha(files);
            },

            handleFileDragLeave() {
                if (this.isSyncDir) {
                    this.draggingCount--;
                    if (this.draggingCount === 0) {
                        this.isDragover = false;
                    }
                }
            },

            async _getOSSToken() {
                let tokenRes = {};
                try {
                    tokenRes = await OSSAPI.postAbcToolOssToken();
                } catch (e) {
                    console.error(e.message || e);
                }
                const { data: tokenData } = tokenRes.data;
                return tokenData;
            },
            loaded() {
                console.log('智能验证加载成功');
            },

            initCaptcha(files) {
                const _this = this;
                // eslint-disable-next-line
                this.ic = new smartCaptcha({
                    appkey: 'FFFF0N00000000009829',
                    scene: 'ic_login',
                    renderTo: '#captcha',
                    width: 300,
                    height: 42,
                    default_txt: '点击按钮开始智能验证',
                    success_txt: '验证成功',
                    fail_txt: '验证失败，请在此点击按钮刷新',
                    scaning_txt: '智能检测中',
                    async success(successData = {}) {
                        let res = {};
                        try {
                            res = await LoginAPI.postAfsAuthSign({
                                scene: 'ic_login',
                                sessionId: successData.sessionId || '',
                                sig: successData.sig || '',
                                token: window.NVC_Opt?.token || '',
                            });
                        } catch (e) {
                            this.$message.error(e.message || e);
                            return false;
                        }
                        const { data = {} } = res;
                        if (data.code === 200) {
                            await _this.uploadFiles(files);
                        }
                    },
                    fail(data) {
                        console.log('ic error', data);
                    },
                });
                this.showSlideCaptcha = true;
                this.$nextTick(() => {
                    this.ic.init();
                });
            },
            async uploadFiles(files) {
                const region = 'oss-cn-chengdu';
                const bucket = 'abc-tool-files';
                const accessToken = await this._getOSSToken();
                try {
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        const data = await new window.OSS.Wrapper({
                            region,
                            bucket,
                            accessKeyId: accessToken.accessKeyId,
                            accessKeySecret: accessToken.accessKeySecret,
                            stsToken: accessToken.securityToken,
                            secure: true,
                        }).multipartUpload(`同步盘/${file.name}`, file, {
                            // fix: 下载文件时名字不能显示正确的文件名(OSS上跨域后 a标签的download属性不生效了)
                            headers: {
                                'Content-Disposition': `filename=${encodeURIComponent(file.name)}`,
                            },
                        });

                        const { requestUrls } = data.res;

                        if (requestUrls) {
                            data.res.requestUrls = requestUrls.map((url) => {
                                return url.split('?')[0];
                            });
                        }
                    }
                    this.fetchFileList();
                    this.$Toast({
                        message: '上传成功',
                        type: 'success',
                    });
                    this.showSlideCaptcha = false;
                } catch (e) {
                    console.error(e);
                    this.$Toast({
                        message: '上传失败',
                        type: 'error',
                    });
                }
            },

            async handleFileDrop(e) {
                this.isDragover = false;
                this.draggingCount = 0;
                const { files } = e.dataTransfer;

                this.initCaptcha(files);
            },
        },
    };
</script>
<style lang="scss">
    @import 'src/styles/theme.scss';
    @import "src/styles/mixin.scss";

    .external-file-list {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 40px 100px;

        .external-file-list__navigator {
            display: flex;
            align-items: center;
            height: 48px;

            &-item {
                &:not(.is-disabled) {
                    .name {
                        cursor: pointer;
                    }
                }

                .divider {
                    padding: 0 4px;
                    color: #d3d2d2;
                }
            }
        }

        .external-file-sync {
            position: relative;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            height: 100%;

            ul.external-file-list__item-wrapper {
                position: relative;
                flex: 1;
                margin-top: 24px;

                @include scrollBar();

                .external-file-list__item {
                    border-bottom: 1px solid #f2f2f2;

                    &--file,
                    &--folder {
                        display: flex;
                        align-items: center;
                        height: 40px;
                        cursor: pointer;

                        .icon {
                            width: 36px;
                            height: 36px;
                            margin-right: 12px;
                        }

                        .file-size {
                            margin-left: auto;
                        }
                    }
                }
            }

            .external-file-sync__upload-area {
                width: 100%;
                height: 200px;
                background: dimgrey;
            }

            &__mask {
                position: absolute;
                top: 0;
                left: 0;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                pointer-events: none;

                &::before {
                    position: absolute;
                    top: 0;
                    left: 0;
                    display: block;
                    width: 100%;
                    height: 100%;
                    content: "";
                    background: #eeeeee;
                    opacity: 0.8;
                }

                .external-file-sync__upload-icon {
                    z-index: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 80px;
                    height: 80px;
                    font-size: 20px;
                    color: $S2;
                    background: $C2;
                    border-radius: 40px;
                }

                .external-file-sync__upload-text {
                    z-index: 1;
                    margin-top: 24px;
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 1.5;
                    color: $C2;
                }
            }

            .upload-file-button__wrapper {
                .hidden-inp {
                    opacity: 0;
                }
            }
        }
    }
</style>
