import useCloudDraft from './index';

export default {
    title: 'HOOKS/业务/useCloudDraft',
};

export const Usage = () => ({
    setup() {
        const {
            saveDraftButtonLoading,
            isShowCloudStatus,
            cloudStatusText,
            cloudStatusIcon,
            cloudStatusType,
            saveDraftToCloud,
            deleteCloudDraft,
            setCloudSyncStatus,
        } = useCloudDraft({
            draftType: 'goods-check',
            createDraftData: () => ({
                id: Date.now(),
                name: '测试草稿',
                data: { test: true },
            }),
            onSaveSuccess: (draft) => {
                console.log('保存成功', draft);
            },
            onSaveError: (error) => {
                console.error('保存失败', error);
            },
        });

        // 模拟不同状态
        const simulateSaving = () => {
            setCloudSyncStatus('saving', '正在保存...');
            setTimeout(() => {
                setCloudSyncStatus('saved', '保存成功');
            }, 2000);
        };

        const simulateError = () => {
            setCloudSyncStatus('error', '保存失败');
        };

        return {
            saveDraftButtonLoading,
            isShowCloudStatus,
            cloudStatusText,
            cloudStatusIcon,
            cloudStatusType,
            saveDraftToCloud,
            deleteCloudDraft,
            simulateSaving,
            simulateError,
        };
    },
    template: `
        <div style="padding: 20px;">
            <h3>云草稿管理 Hook 示例</h3>

            <div style="margin-bottom: 20px;">
                <abc-button
                    type="primary"
                    :loading="saveDraftButtonLoading"
                    @click="saveDraftToCloud()"
                    style="margin-right: 10px;"
                >
                    保存草稿
                </abc-button>

                <abc-button
                    type="success"
                    @click="simulateSaving"
                    style="margin-right: 10px;"
                >
                    模拟保存中
                </abc-button>

                <abc-button
                    type="danger"
                    @click="simulateError"
                >
                    模拟错误
                </abc-button>
            </div>

            <div v-if="isShowCloudStatus" style="margin-bottom: 20px;">
                <abc-tag
                    :type="cloudStatusType"
                    size="small"
                >
                    <abc-icon
                        v-if="cloudStatusIcon"
                        :name="cloudStatusIcon"
                        size="12"
                        style="margin-right: 4px;"
                    />
                    {{ cloudStatusText }}
                </abc-tag>
            </div>

            <abc-card title="状态信息">
                <p><strong>是否显示状态：</strong> {{ isShowCloudStatus }}</p>
                <p><strong>状态文本：</strong> {{ cloudStatusText }}</p>
                <p><strong>状态图标：</strong> {{ cloudStatusIcon }}</p>
                <p><strong>状态类型：</strong> {{ cloudStatusType }}</p>
            </abc-card>
        </div>
    `,
});
