import { ref, computed, getCurrentInstance } from 'vue';

/**
 * @desc 云草稿管理hooks，用于管理云草稿的状态数据和同步方法
 * <AUTHOR>
 * @date 2025-09-23
 * @param {Object} options 配置选项
 * @param {string} options.draftType 草稿类型，如 'goods-check', 'goods-in' 等
 * @param {Function} options.createDraftData 创建草稿数据的方法
 * @param {Function} options.onSaveSuccess 保存成功回调
 * @param {Function} options.onSaveError 保存失败回调
 * @param {Function} options.onDeleteSuccess 删除成功回调
 * @param {Function} options.onDeleteError 删除失败回调
 */
export default function useCloudDraft(options = {}) {
    const {
        draftType = '',
        createDraftData = () => ({}),
        onSaveSuccess = () => {},
        onSaveError = () => {},
        onDeleteSuccess = () => {},
        onDeleteError = () => {},
    } = options;

    const instance = getCurrentInstance();
    const { $store, $Toast } = instance.proxy;

    // 草稿状态
    const saveDraftButtonLoading = ref(false);
    const deleteDraftButtonLoading = ref(false);
    const autoSaveTimer = ref(null);
    const draftId = ref(null);

    // 云草稿同步状态
    const cloudSyncStatus = ref('idle'); // idle, saving, saved, error
    const cloudSyncMessage = ref('');
    const lastSyncTime = ref(null);

    // 计算属性：是否显示云草稿状态
    const isShowCloudStatus = computed(() => {
        return Boolean(draftId.value && cloudSyncStatus.value !== 'idle');
    });

    // 计算属性：云草稿状态文本
    const cloudStatusText = computed(() => {
        switch (cloudSyncStatus.value) {
            case 'saving':
                return '保存中...';
            case 'saved':
                return '已保存';
            case 'error':
                return '保存失败';
            default:
                return '';
        }
    });

    // 计算属性：云草稿状态图标
    const cloudStatusIcon = computed(() => {
        switch (cloudSyncStatus.value) {
            case 'saving':
                return 'loading';
            case 'saved':
                return 'success';
            case 'error':
                return 'error';
            default:
                return '';
        }
    });

    // 计算属性：云草稿状态类型
    const cloudStatusType = computed(() => {
        switch (cloudSyncStatus.value) {
            case 'saving':
                return 'info';
            case 'saved':
                return 'success';
            case 'error':
                return 'danger';
            default:
                return '';
        }
    });

    /**
     * 设置云草稿同步状态
     * @param {string} status 状态：idle, saving, saved, error
     * @param {string} message 状态消息
     */
    const setCloudSyncStatus = (status, message = '') => {
        cloudSyncStatus.value = status;
        cloudSyncMessage.value = message;
        if (status === 'saved') {
            lastSyncTime.value = new Date();
        }
    };

    /**
     * 保存草稿到云端
     * @param {Object} extraData 额外的草稿数据
     * @param {boolean} isForceSave 是否强制保存
     */
    const saveDraftToCloud = async (extraData = {}, isForceSave = false) => {
        if (!draftType) {
            console.error('useCloudDraft: draftType is required');
            return;
        }

        try {
            saveDraftButtonLoading.value = true;
            setCloudSyncStatus('saving', '正在保存草稿...');

            const draft = createDraftData(extraData);

            if (isForceSave) {
                // 强制提交
                draft.forceSubmit = 1;
                // 保证调新建
                draft.id = null;
            }

            // 清除自动保存定时器
            if (autoSaveTimer.value) {
                clearTimeout(autoSaveTimer.value);
                autoSaveTimer.value = null;
            }

            // 调用store中的保存方法
            await $store.dispatch('saveGoodsCloudDraft', {
                key: draftType,
                record: draft,
            });

            setCloudSyncStatus('saved', '草稿保存成功');

            // 调用成功回调
            onSaveSuccess(draft);

            $Toast({
                type: 'success',
                message: '保存成功',
            });

        } catch (error) {
            console.error('保存草稿失败:', error);
            setCloudSyncStatus('error', error.message || '保存失败');

            // 处理特定错误码
            if (error.code === 12812) {
                // 冲突错误，询问是否强制保存
                instance.proxy.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: error.message,
                    confirmText: '确认',
                    onConfirm: () => saveDraftToCloud(extraData, true),
                });
            } else {
                $Toast({
                    type: 'error',
                    message: '保存失败',
                });

                // 调用错误回调
                onSaveError(error);
            }
        } finally {
            saveDraftButtonLoading.value = false;
        }
    };

    /**
     * 删除云草稿
     * @param {string} targetDraftId 要删除的草稿ID
     */
    const deleteCloudDraft = async (targetDraftId) => {
        if (!draftType || !targetDraftId) {
            console.error('useCloudDraft: draftType and draftId are required');
            return;
        }

        instance.proxy.$confirm({
            type: 'warn',
            title: '删除确认',
            content: '删除后不能恢复，确定删除该草稿？',
            onConfirm: async () => {
                try {
                    deleteDraftButtonLoading.value = true;

                    await $store.dispatch('deleteGoodsCloudDraft', {
                        key: draftType,
                        draftId: targetDraftId,
                    });

                    setCloudSyncStatus('idle');

                    // 调用成功回调
                    onDeleteSuccess(targetDraftId);

                    $Toast({
                        type: 'success',
                        message: '删除成功',
                    });

                } catch (error) {
                    console.error('删除草稿失败:', error);

                    if (error.code === 12813) {
                        instance.proxy.$alert({
                            type: 'warn',
                            title: '提示',
                            content: error.message,
                            onClose: () => {
                                onDeleteSuccess(targetDraftId);
                            },
                        });
                    } else {
                        $Toast({
                            type: 'error',
                            message: '删除失败',
                        });

                        // 调用错误回调
                        onDeleteError(error);
                    }
                } finally {
                    deleteDraftButtonLoading.value = false;
                }
            },
        });
    };

    /**
     * 获取云草稿详情
     * @param {string} targetDraftId 草稿ID
     */
    const getCloudDraftDetail = async (targetDraftId) => {
        if (!draftType || !targetDraftId) {
            console.error('useCloudDraft: draftType and draftId are required');
            return null;
        }

        try {
            const result = await $store.dispatch('getGoodsCloudDraftDetail', {
                key: draftType,
                draftId: targetDraftId,
            });
            return result;
        } catch (error) {
            console.error('获取草稿详情失败:', error);
            return null;
        }
    };

    /**
     * 启动自动保存
     * @param {Function} autoSaveHandler 自动保存处理函数
     * @param {number} interval 自动保存间隔（毫秒），默认30秒
     */
    const startAutoSave = (autoSaveHandler, interval = 30000) => {
        if (autoSaveTimer.value) {
            clearTimeout(autoSaveTimer.value);
        }

        autoSaveTimer.value = setTimeout(() => {
            if (typeof autoSaveHandler === 'function') {
                autoSaveHandler();
            }
            // 递归调用，实现定时自动保存
            startAutoSave(autoSaveHandler, interval);
        }, interval);
    };

    /**
     * 停止自动保存
     */
    const stopAutoSave = () => {
        if (autoSaveTimer.value) {
            clearTimeout(autoSaveTimer.value);
            autoSaveTimer.value = null;
        }
    };

    /**
     * 清除草稿状态
     */
    const clearDraftStatus = () => {
        draftId.value = null;
        setCloudSyncStatus('idle');
        stopAutoSave();
    };

    return {
        // 状态
        saveDraftButtonLoading,
        deleteDraftButtonLoading,
        draftId,
        cloudSyncStatus,
        cloudSyncMessage,
        lastSyncTime,

        // 计算属性
        isShowCloudStatus,
        cloudStatusText,
        cloudStatusIcon,
        cloudStatusType,

        // 方法
        setCloudSyncStatus,
        saveDraftToCloud,
        deleteCloudDraft,
        getCloudDraftDetail,
        startAutoSave,
        stopAutoSave,
        clearDraftStatus,
    };
}
