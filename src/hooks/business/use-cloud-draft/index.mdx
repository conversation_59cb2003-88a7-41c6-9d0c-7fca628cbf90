import { Meta, Story, Canvas, Source } from '@storybook/blocks';
import * as useCloudDraftStories from './index.stories';

<Meta of={ useCloudDraftStories} />

# useCloudDraft

云草稿管理hooks，用于管理云草稿的状态数据和同步方法。提供了统一的云草稿保存、删除、获取详情等功能，支持自动保存和状态显示。

## 特性

- 🔄 云草稿同步状态管理
- 💾 自动保存功能
- 🎯 统一的错误处理
- 📊 实时状态显示
- 🔧 高度可配置
- 🚀 支持多种草稿类型

## 使用示例

<Canvas>
    <Story of={ useCloudDraftStories.Usage} />
</Canvas>

## 基础用法

```javascript
import useCloudDraft from '@/hooks/business/use-cloud-draft';

export default {
    setup() {
        const {
            saveDraftButtonLoading,
            isShowCloudStatus,
            cloudStatusText,
            cloudStatusIcon,
            cloudStatusType,
            saveDraftToCloud,
            deleteCloudDraft,
        } = useCloudDraft({
            draftType: 'goods-check',
            createDraftData: (extraData) => ({
                // 创建草稿数据的逻辑
                ...this.formData,
                ...extraData,
            }),
            onSaveSuccess: (draft) => {
                console.log('保存成功', draft);
                this.$emit('refresh');
            },
            onSaveError: (error) => {
                console.error('保存失败', error);
            },
        });

        return {
            saveDraftButtonLoading,
            isShowCloudStatus,
            cloudStatusText,
            cloudStatusIcon,
            cloudStatusType,
            saveDraftToCloud,
            deleteCloudDraft,
        };
    },
};
```

## 在模板中使用

```vue
<template>
    <div>
        <!-- 保存按钮 -->
        <abc-button
            type="primary"
            :loading="saveDraftButtonLoading"
            @click="saveDraftToCloud()"
        >
            保存草稿
        </abc-button>

        <!-- 云草稿状态显示 -->
        <abc-tag
            v-if="isShowCloudStatus"
            :type="cloudStatusType"
            size="small"
        >
            <abc-icon
                v-if="cloudStatusIcon"
                :name="cloudStatusIcon"
                size="12"
            />
            {{ cloudStatusText }}
        </abc-tag>
    </div>
</template>
```

## API 参数

| 参数名 | 类型 | 默认值 | 说明 |
| ------ | ---- | ------ | ---- |
| options.draftType | string | '' | 草稿类型，如 'goods-check', 'goods-in' 等 |
| options.createDraftData | Function | () => ({}) | 创建草稿数据的方法 |
| options.onSaveSuccess | Function | () => {} | 保存成功回调 |
| options.onSaveError | Function | () => {} | 保存失败回调 |
| options.onDeleteSuccess | Function | () => {} | 删除成功回调 |
| options.onDeleteError | Function | () => {} | 删除失败回调 |

## 返回值

### 状态

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| saveDraftButtonLoading | Ref\<boolean\> | 保存按钮加载状态 |
| deleteDraftButtonLoading | Ref\<boolean\> | 删除按钮加载状态 |
| draftId | Ref\<string\> | 当前草稿ID |
| cloudSyncStatus | Ref\<string\> | 云同步状态：idle, saving, saved, error |
| isShowCloudStatus | ComputedRef\<boolean\> | 是否显示云草稿状态 |
| cloudStatusText | ComputedRef\<string\> | 云草稿状态文本 |
| cloudStatusIcon | ComputedRef\<string\> | 云草稿状态图标 |
| cloudStatusType | ComputedRef\<string\> | 云草稿状态类型 |

### 方法

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| saveDraftToCloud | Function | 保存草稿到云端 |
| deleteCloudDraft | Function | 删除云草稿 |
| getCloudDraftDetail | Function | 获取云草稿详情 |
| startAutoSave | Function | 启动自动保存 |
| stopAutoSave | Function | 停止自动保存 |
| clearDraftStatus | Function | 清除草稿状态 |

## 支持的草稿类型

- `goods-check` - 盘点草稿
- `goods-in` - 入库草稿
- `goods-out` - 出库草稿
- `goods-trans` - 调拨草稿
- `goods-apply` - 领用草稿
