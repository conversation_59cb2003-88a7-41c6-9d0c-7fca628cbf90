import BaseService from '@/service/base.js';
import SignService from 'src/utils/sign-service';
import Clone from 'utils/clone';
import Logger from 'utils/logger';

export default class DecodeService extends BaseService {
    static _decKey = '';

    static setKey(key) {
        this._decKey = key;
    }
    /**
     * Encrypt data using AES encryption
     * @param {Object|String} data - Data to decrypt
     * @returns {String} - Decrypted data
     */
    static async aecSignForDec(data) {
        const res = await SignService.aecSignForDec(data, DecodeService._decKey);
        return res;
    }

    /**
     * @desc 为原始对象中的指定key解码
     * @date 2025/09/18 09:44:35
     * @param {Object} originObj 原始对象
     * @param {Object} decodeKeyObj 原始对象中需要解密的key
     * @return {Object}
     */
    static async decodeAESObjectForKey(originObj, decodeKeyObj) {
        async function process(obj, spec, currentPath = []) {
            for (const key in spec) {
                if (Object.prototype.hasOwnProperty.call(spec, key)) {
                    const specValue = spec[key];
                    if (obj[key] === undefined || obj[key] === null) {
                        continue;
                    }
                    if (typeof specValue === 'object' && !Array.isArray(specValue)) {
                        if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
                            await process(obj[key], specValue, [...currentPath, key]);
                        }
                    } else if (specValue === true && typeof obj[key] === 'string') {
                        obj[key] = await DecodeService.aecSignForDec(obj[key]);
                    }
                }
            }
        }
        try {
            if (!originObj || !decodeKeyObj) return originObj;
            const result = Clone(originObj);
            await process(result, decodeKeyObj);
            return result;
        } catch (e) {
            Logger.error({
                scene: 'decodeAESObjectForKey err',
                err: e,
            });
            return originObj;
        }
    }
}
