import BaseService from '@/service/base.js';
import { throttle } from 'utils/lodash';
import ReportAPI from 'api/report';
import 'requestidlecallback-polyfill';
const _eventMap = {
    'mousemove': 'mm',
    'click': 'mc',
    'scroll': 's',
};
import SignService from 'src/utils/sign-service';

export default class TrackService extends BaseService {
    constructor(key) {
        super();
        this._instance = null;
        this._isTracking = false;
        this.eventTrack = [];
        this._AesKey = key;
    }

    /**
     * Encrypt data using AES encryption
     * @param {Object|String} data - Data to encrypt
     * @returns {String} - Encrypted data
     */
    async encryptData(data) {
        const dataStr = typeof data === 'object' ? JSON.stringify(data) : data;
        const res = await SignService.aesSign(dataStr, this._AesKey);
        return res;
    }

    static getInstance(key) {
        if (!this._instance) {
            this._instance = new TrackService(key);
        }
        return this._instance;
    }

    async report() {
        window.requestIdleCallback(async () => {
            if (this.eventTrack.length > 0) {
                try {
                    const encryptedData = await this.encryptData({
                        url: window.location.href,
                        evts: this.eventTrack,
                    });
                    await ReportAPI.pvReport(encryptedData);
                    this.eventTrack = [];
                } catch (e) {
                    console.log(e);
                }
            }
        }, {
            timeout: 2000,
        });
    }

    /**
     * Collect tracking data
     * @param {Object} data - Tracking data to collect
     */
    collectTrack(data) {
        this.eventTrack.unshift(data);
        if (this.eventTrack.length === 100) {
            this.report();
        }
        if (this.eventTrack.length > 100) {
            this.eventTrack.length = 100;
        }
    }

    /**
     * 开始追踪用户操作
     * 捕获带有data-cy属性的元素的各种交互事件
     */
    startTracking() {
        if (this._isTracking) return;

        this._isTracking = true;
        this._handleMouseClick = this._trackUserAction.bind(this);
        // 对鼠标移动事件使用更高的节流值，避免过多数据收集
        this._handleMouseMove = throttle(this._trackUserAction.bind(this), 500);
        // 对滚动事件使用节流，避免过多数据收集
        this._handleScroll = throttle(this._trackUserAction.bind(this), 1000);

        document.body.addEventListener('click', this._handleMouseClick, true);

        // 单独监听鼠标移动事件
        document.body.addEventListener('mousemove', this._handleMouseMove, true);

        // 监听滚动事件
        window.addEventListener('scroll', this._handleScroll, true);
    }

    /**
     * 停止追踪用户操作
     */
    stopTracking() {
        if (!this._isTracking) return;

        this._isTracking = false;

        // 移除所有事件监听
        document.body.removeEventListener('click', this._handleMouseClick, true);

        // 移除鼠标移动事件监听
        document.body.removeEventListener('mousemove', this._handleMouseMove, true);

        // 移除滚动事件监听
        window.removeEventListener('scroll', this._handleScroll, true);

        clearInterval(this._timer);
    }

    /**
     * 追踪用户操作
     * @param {Event} event - DOM事件对象
     * @private
     */
    _trackUserAction(event) {
        // 获取事件目标元素
        const { target } = event;
        if (!event.isTrusted) {
            return;
        }

        // 查找最近的带有data-cy属性的父元素
        let element = target;
        let dataCy = null;

        while (element && element !== document.body) {
            if (element.hasAttribute('data-cy')) {
                dataCy = element.getAttribute('data-cy');
                break;
            }
            element = element.parentElement;
        }

        // 基础数据
        const baseData = {
            et: _eventMap[event.type],
            dc: dataCy,
            t: new Date().getTime(),
        };

        // 根据不同事件类型收集额外数据
        const extraData = this._getExtraDataByEventType(event, dataCy);

        this.collectTrack({
            ...baseData,
            ...extraData,
        });

        if (event.type === 'click') {
            this.report();
        }
    }

    /**
     * 根据事件类型获取额外的数据
     * @param {Event} event - DOM事件对象
     * @returns {Object} - 额外的数据
     * @private
     */
    _getExtraDataByEventType(event) {
        const extraData = {};
        const eventType = event.type;

        // 针对不同类型的事件收集不同的数据
        switch (eventType) {
            case 'click':
                // 对于点击事件，记录点击的坐标
                extraData.cx = event.clientX;
                extraData.cy = event.clientY;
                break;

            case 'mousemove':
                // 对于鼠标移动事件，记录鼠标坐标
                extraData.cx = event.clientX;
                extraData.cy = event.clientY;
                break;

            case 'scroll':
                // 对于滚动事件，记录滚动位置
                extraData.st = event.target.scrollTop;
                extraData.sl = event.target.scrollLeft;
                break;

            default:
                break;
        }

        return extraData;
    }
}
