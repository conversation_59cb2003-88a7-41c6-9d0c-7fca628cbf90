<template>
    <div class="inspect-aside-wrapper sidebar-container">
        <div class="inspect-aside-header">
            <abc-tabs-v2
                v-model="curTab"
                :option="tabItems"
                size="huge"
                disable-indicator
                :custom-gap="0.1"
            ></abc-tabs-v2>
        </div>

        <inspect-history
            :patient-id="patientId"
            is-need-copy
            :editable="editable"
            :cur-report-item="curReportItem"
        ></inspect-history>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { INSPECT_EVENT_KEY } from '@/views-hospital/inspect-diagnosis/utils/constant.js';
    import InspectHistory from '@/views-hospital/inspect-diagnosis/components/inspect-assist/inspect-history/index.vue';

    export default {
        name: 'InspectAside',
        components: {
            InspectHistory,
        },
        props: {
            editable: {
                type: Boolean,
                default: false,
            },
        },
        data () {
            return {
                historyReportTotal: 0,
                curTab: '历史报告',
            };
        },
        computed: {
            ...mapGetters([ 'inspect' ]),

            patientId() {
                return this.inspect.selectedPatient?.id;
            },

            tabItems() {
                return [
                    {
                        label: '历史报告' ,
                        value: '历史报告' ,
                        statisticsNumber: this.historyReportTotal,
                    },
                ];
            },
            curReportItem() {
                return this.inspect.selectedItem;
            },
        },
        mounted() {
            this.$abcEventBus.$on(INSPECT_EVENT_KEY.UPDATE_HISTORY_COUNT, (v) => {
                this.historyReportTotal = v;
            }, this);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
    };
</script>

<style lang='scss'>
.inspect-aside-wrapper {
    height: 100%;

    .inspect-aside-header {
        width: 100%;
        height: 56px;
        padding: 0 4px;
        border-bottom: 1px solid var(--abc-color-P6);
    }
}
</style>
