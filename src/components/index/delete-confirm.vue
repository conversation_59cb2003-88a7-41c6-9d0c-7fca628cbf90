<template>
    <abc-popover
        ref="delete-popover"
        :placement="placement"
        trigger="click"
        theme="white"
        :popper-style="{
            padding: '4px'
        }"
        :popper-class="popperClass"
    >
        <template slot="reference">
            <slot>
                <abc-delete-icon @delete="onDelete"></abc-delete-icon>
            </slot>
        </template>

        <abc-space :size="4">
            <abc-button
                variant="text"
                theme="danger"
                @click="onConfirm"
            >
                确认删除
            </abc-button>
            <abc-button variant="text" @click="onCancel">
                取消
            </abc-button>
        </abc-space>
    </abc-popover>
</template>

<script>
    export default {
        name: 'DeleteConfirm',
        props: {
            placement: {
                type: String,
                default: 'right',
            },
            popperClass: {
                type: String,
                default: '',
            },
        },
        methods: {
            onDelete() {
                this.$emit('delete');
            },
            onConfirm() {
                this.$emit('confirm');
                this.$refs['delete-popover']?.doClose();
            },
            onCancel() {
                this.$emit('cancel');
                this.$refs['delete-popover']?.doClose();
            },
        },
    };
</script>
