import { formatDate } from '@abc/utils-date';
import PatientsAPI from 'api/patients';
import CrmAP<PERSON> from 'api/crm.js';
import LRUCache from 'lru-cache';

const options = {
    max: 10,
    maxSize: 500,
    ttl: 5 * 60 * 1000, // ms
    sizeCalculation: (value) => {
        return value?.length || 1;
    },
};

const ENABLE_CACHE = true;

const cache = new LRUCache(options);

function cacheStudiesByPatientId(patientId, attachments) {
    let studies = [];
    if (attachments) {
        studies = attachments.map((item) => {
            // 暂时用 patientId_date 的形式作为 StudyInstanceUID
            const StudyInstanceUID = `${patientId}_${item.date}`;
            return {
                StudyInstanceUID,
                date: item.date,
                series: item.rows.map((attachment) => {
                    return {
                        SeriesDate: item.date,
                        StudyInstanceUID,
                        RetrieveURL: attachment.url,
                        SeriesInstanceUID: attachment.url,
                        instances: [
                            {
                                SeriesDate: item.date,
                                RetrieveURL: attachment.url,
                                StudyInstanceUID,
                                SeriesInstanceUID: attachment.url,
                                SOPInstanceUID: attachment.url,
                                labelData: (attachment.labelData ?? []).map((labelData) => ({
                                    ...labelData,
                                    RetrieveURL: attachment.url ?? labelData.RetrieveURL,
                                    SeriesInstanceUID: attachment.url ?? labelData.SeriesInstanceUID,
                                    SOPInstanceUID: attachment.url ?? labelData.SOPInstanceUID,
                                })),
                            },
                        ],
                    };
                }),
            };
        });
        if (studies.length) {
            cache.set(patientId, studies);
        }
    }
    return studies;
}

function cacheStudiesByOutpatientId(outpatientId, attachments, created) {
    const SeriesDate = formatDate(created, 'YYYY-MM-DD');
    const StudyInstanceUID = `${outpatientId}_${SeriesDate}`;
    const studies = [
        {
            StudyInstanceUID,
            SeriesDate,
            series: attachments?.map((attachment) => {
                return {
                    StudyInstanceUID,
                    SeriesDate,
                    RetrieveURL: attachment.url,
                    SeriesInstanceUID: attachment.url,
                    instances: [
                        {
                            SeriesDate,
                            RetrieveURL: attachment.url,
                            StudyInstanceUID,
                            SeriesInstanceUID: attachment.url,
                            SOPInstanceUID: attachment.url,
                            labelData: attachment.labelData,
                        },
                    ],
                };
            }),
        },
    ];
    cache.set(outpatientId, studies);
    return studies;
}

async function initStudiesByPatientId(patientId) {
    try {
        const { data } = await CrmAPI.fetchMedicalRecordAttachmentsByPatientId(patientId);
        return cacheStudiesByPatientId(patientId, data.rows);
    } catch (e) {
        console.warn('initStudiesByPatientId err', e);
    }
    return [];
}

async function initStudiesByOutpatientId(outpatientId) {
    try {
        const { rows } = await PatientsAPI.fetchPatientsAttachmentsLabel(outpatientId.split(','));
        return cacheStudiesByOutpatientId(outpatientId, rows);
    } catch (e) {
        console.warn('initStudiesByOutpatientId err', e);
    }
    return [];
}

async function getStudiesByPatientId(patientId) {
    if (ENABLE_CACHE) {
        const studies = cache.get(patientId);
        if (studies) {
            return studies;
        }
    }
    return initStudiesByPatientId(patientId);
}

async function getStudiesByOutpatientId(outpatientId) {
    if (ENABLE_CACHE) {
        const studies = cache.get(outpatientId);
        if (studies) {
            return studies;
        }
    }
    return initStudiesByOutpatientId(outpatientId);
}

export const studyCache = {
    getStudiesByPatientId,
    cacheStudiesByPatientId,
    getStudiesByOutpatientId,
    cacheStudiesByOutpatientId,
    initStudiesByPatientId,
    initStudiesByOutpatientId,
};
