<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>migrate</title>
</head>
<script>
    // 监听 message 事件，接收来自主页面的消息
    window.addEventListener('message', function(event) {
        // 判断消息内容是否为请求获取 localStorage
        var method = event.data && event.data.method;
        if (method === 'getLocalStorage') {
            var localStorageData = {};

            // 将 localStorage 数据发送回主页面
            event.source.postMessage({
                method: 'onGetLocalStorage',
                data: localStorageData,
            }, event.origin);
        }
    }, false);
</script>
<body>

</body>
</html>
