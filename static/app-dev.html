<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="robots" content="noindex">
    <meta charset="UTF-8">
    <title>ABC数字医疗云</title>
    <link rel="shortcut icon" href="/static/homeimg/favicon.ico" type="image/x-icon">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
    <meta name="keywords" content="ABC数字医疗云，ABC诊所管家、电子病历、诊所系统、诊所软件、门诊系统、医疗软件、病历软件、医疗系统、连锁诊所、智能诊断、用药审核、处方安全、诊所运营、诊所开办、在线咨询、在线问诊、诊所开店、诊所创收"/>
    <meta name="description" content="ABC数字医疗云，ABC诊所管家，移动互联网时代好用的诊所管家，包含预约挂号、电子病历、检验检查、药品进销存、患者管理、连锁管理，向诊所提供强大的CIS系统和微诊所解决方案。">
    <style>
        * {
            font-size: 0;
            margin: 0;
            padding: 0;
            list-style: none;
            text-decoration: none;
            color: #fff;
            border: 0;
            outline: 0;
            -webkit-touch-callout:none;
            -webkit-user-select:none;
            -khtml-user-select:none;
            -moz-user-select:none;
            -ms-user-select:none;
            user-select:none;
            box-sizing: border-box;
            font-family: Helvetica Neue, Helvetica, Arial, PingFang SC, Hiragino Sans GB, Heiti SC, Microsoft YaHei, WenQuanYi Micro Hei, sans-serif;
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -webkit-overflow-scrolling: touch
        }
        /* pc尺寸 */
        @media screen and (min-width: 800px) {
            .mobile {
                display: none !important;
            }
            body {
                overflow: hidden;
            }
            .index-wrapper {
                width: 100vw;
                height: 100vh;
                min-width: 1200px;
                overflow-x: hidden;
                overflow-y: auto;
                margin: 0px auto;
            }
            .index-wrapper.scroll-stop {
                overflow-y: hidden;
            }
            .index-wrapper::-webkit-scrollbar {
                width: 10px;
                height: 10px;
            }
            .index-wrapper::-webkit-scrollbar-thumb {
                border-radius:3px;
                background: #d9dbe3;
            }
            .index-wrapper::-webkit-scrollbar-track {
                background: #f0f2f5;
            }
            .index-wrapper > .scroll-body {
                position: relative;
            }
        }
        /* mobile尺寸 */
        @media screen and (max-width: 800px) {
            .pc {
                display: none !important;
            }
            .index-wrapper {
                position: relative;
                width: 100vw;
                height: 100vh;
                overflow: auto;
                margin: 0px auto;
            }
            .index-wrapper.scroll-stop {
                overflow-y: hidden;
            }
            .index-wrapper > .scroll-body {
                position: relative;
            }
        }
    </style>
    <!--动画-->
    <style>
        @keyframes loading-rotate {
            100% {
                transform: rotate(360deg);
            }
        }
    </style>
    <!--导航样式__pc-->
    <style>
        /* pc尺寸 */
        @media screen and (min-width: 800px) {
            .nav-wrapper {
                width: 100%;
                height: 80px;
                padding: 0px 32px;
                box-sizing: border-box;
                display: flex;
                align-items: stretch;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 2018
            }
            .nav-wrapper > .left-wrapper {
                width: 300px;
                flex-wrap: 0;
                display: flex;
                justify-content: flex-start;
                align-items: center;
            }
            .nav-wrapper > .left-wrapper .nav-logo {
                height: 22px;
                width: 173px;
                cursor: pointer;
                background-image: url("/static/homeimg/logo-cloud-white.png");
                background-origin: content-box;
                background-position: center;
                background-size: contain;
                background-repeat: no-repeat
            }
            .nav-wrapper > .content-wrapper {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .nav-wrapper > .content-wrapper a {
                padding: 0px 32px;
                color: #FFFFFF;
                font-size: 18px;
                font-weight: 400;
                font-family: Noto Sans SC, Helvetica Neue, Helvetica, Arial, PingFang SC, Microsoft YaHei, Hiragino Sans GB, Heiti SC, WenQuanYi Micro Hei, sans-serif;
                line-height: 18px;
                cursor: pointer;
                position: relative;
            }
            .nav-wrapper > .content-wrapper a.active {
                font-weight: 500;
            }
            .nav-wrapper > .content-wrapper .active::after {
                content: '';
                width: 36px;
                height: 3px;
                border-radius: 1.5px;
                display: inline-block;
                background-color: #FFFFFF;
                position: absolute;
                bottom: -11px;
                left: 50%;
                transform: translateX(-50%)
            }
            .nav-wrapper > .right-wrapper {
                width: 300px;
                flex-wrap: 0;
                display: flex;
                justify-content: flex-end;
                align-items: center;
            }
            .nav-wrapper > .right-wrapper button {
                width: 97px;
                height: 40px;
                line-height: 40px;
                margin-left: 64px;
                color: #007AFF;
                font-size: 16px;
                font-weight: 500;
                border-radius: var(--abc-border-radius-small);
                background-color: #FFFFFF;
                cursor: pointer;
            }
            .nav-wrapper > .right-wrapper .blank {
                color: #FFFFFF;
                width: 74px;
                font-size: 18px;
                font-weight: 400;
                line-height: 18px;
                border-radius: 0px;
                background-color: transparent;
            }
            .nav-wrapper.nav-white {
                background-color: #fff;
                box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
                transition: box-shadow .1s linear
            }
            .nav-wrapper.nav-white > .left-wrapper .nav-logo {
                background-image: url("/static/homeimg/logo-cloud.png");
            }
            .nav-wrapper.nav-white > .content-wrapper a {
                color: #007AFF;
            }
            .nav-wrapper.nav-white > .content-wrapper .active::after {
                background-color: #007AFF;
            }
            .nav-wrapper.nav-white > .right-wrapper button {
                color: #FFFFFF;
                background-color: #007AFF;
            }
            .nav-wrapper.nav-white > .right-wrapper button.blank {
                color: #007AFF;
                background-color: #ffffff;
            }
        }
    </style>
    <!--导航样式__mobile-->
    <style>
        /* mobile尺寸 */
        @media screen and (max-width: 800px) {
            .nav-wrapper {
                width: 100%;
                height: 1.76rem;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 2018
            }
            .nav-wrapper.nav-abs {
                position: absolute;
            }
            .nav-wrapper > .content-wrapper {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .nav-wrapper > .content-wrapper a {
                color: #ffffff;
                font-size: 0.37rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.48rem;
                opacity: 0.5;
                padding: 0px 0.535rem;
                cursor: pointer;
            }
            .nav-wrapper > .content-wrapper a.active {
                opacity: 1;
            }
            .nav-wrapper.nav-white {
                background-color: #fff;
                box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
                transition: box-shadow .1s linear
            }
            .nav-wrapper.nav-white > .content-wrapper a {
                color: #007AFF;
            }
        }
    </style>
    <!--诊所管家-->
    <style>
        /* pc尺寸 */
        @media screen and (min-width: 800px) {
            .clinic-home {
                position: relative;
                padding-top: 660px;
                padding-bottom: 255px;
                display: none;
            }
            .clinic-home.active{
                display: block;
            }
            .clinic-home .mock-bg {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100%;
                height: 660px;
                background: linear-gradient(225deg, rgba(0, 183, 220, 1) 0, rgba(38, 128, 247, 1) 100%);
                z-index: 1;
            }
            .clinic-home .page-index {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100%;
                height: 660px;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/price-zs-bg1.png");
                background-size: 100% 660px;
                background-position: top center;
                background-repeat: no-repeat;
                display: flex;
                justify-content: center;
                padding-top: 200px;
                margin: 0px auto;
                z-index: 2;
            }
            .clinic-home .page-index .left-wrapper {
                width: 454px;
                margin: 28px 39px 0px 0px;
                display: flex;
                flex-direction: column;
            }
            .clinic-home .page-index .left-wrapper > .desc {
                width: 407px;
                height: 128px;
                font-family: SourceHanSansCN-Bold;
                font-weight: 700;
                font-size: 50px;
                color: #FFFFFF;
                letter-spacing: 0;
                line-height: 64px;
            }
            .clinic-home .page-index .left-wrapper > .flag {
                width: 266px;
                height: 33px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: 24px;
                color: #FFFFFF;
                letter-spacing: 4.8px;
                margin-top: 32px;
            }
            .clinic-home .page-index .left-wrapper .btn-wrapper {
                width: 100%;
                height: 48px;
                display: flex;
                align-items: center;
                margin-top: 60px;
            }
            .clinic-home .page-index .left-wrapper .btn-wrapper > button.btn-apply {
                width: 258px;
                height: 48px;
                color: #007AFF;
                font-size: 18px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 48px;
                letter-spacing: 0;
                border-radius: var(--abc-border-radius-small);
                background-color: #FFFFFF;
                text-align: center;
                cursor: pointer;
            }
            .clinic-home .page-index .left-wrapper .btn-wrapper > button.btn-price {
                width: 128px;
                height: 48px;
                color: #FFFFFF;
                font-size: 18px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 48px;
                letter-spacing: 0;
                border-radius: var(--abc-border-radius-small);
                background-color: #33C4FF;
                text-align: center;
                cursor: pointer;
                margin-left: 8px;
            }
            .clinic-home .page-index .right-wrapper {
                width: 609px;
                height: 334px;
                background-image: url("/static/homeimg/<EMAIL>");
                background-size: cover;
                background-repeat: no-repeat;
            }
            .clinic-home .page-feature {
                width: 100%;
                height: 588px;
                padding-top: 120px;
                display: flex;
                justify-content: center;
                align-items: flex-start;
                text-align: center;
            }
            .clinic-home .page-feature .item-feature {
                width: 300px;
                height: 388px;
                padding-top: 40px;
                box-sizing: border-box;
                border-radius: var(--abc-border-radius-small);
                box-shadow: 0 10px 34px 0 #00000012;
                background-color: #fff;
                margin: 0px 50px;
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .clinic-home .page-feature .item-feature > img {
                width: 138px;
                height: 138px;
            }
            .clinic-home .page-feature .item-feature .info-wrapper > .tit {
                color: #05B387;
                font-size: 27px;
                font-weight: 100;
                font-family: PingFangSC-Thin;
                letter-spacing: 0;
                margin-top: 40px;
            }
            .clinic-home .page-feature .item-feature .info-wrapper > .des {
                margin-top: 14px;
            }
            .clinic-home .page-feature .item-feature .info-wrapper > .des p {
                color: #6B778C;
                font-size: 16px;
                font-weight: 400;
                line-height: 22px;
                font-family: PingFangSC-Regular;
                letter-spacing: 0;
                text-align: center;
            }
            .clinic-home .page-process {
                width: 100%;
                padding-top: 237px;
                box-sizing: border-box;
                background-image: url("/static/homeimg/<EMAIL>");
                background-size: auto 497px;
                background-position: top center;
                background-repeat: no-repeat;
            }
            .clinic-home .page-process h3 {
                color: #091e42;
                font-size: 32px;
                font-family: PingFang-SC-Light;
                font-weight: 300;
                line-height: 45px;
                text-align: center;
            }
            .clinic-home .page-process h3::after {
                content: '';
                display: block;
                width: 48px;
                height: 4px;
                border-radius: 100px;
                background-color: #459eff;
                margin-left: 50%;
                margin-top: 16px;
                transform: translateX(-50%)
            }
            .clinic-home .page-process .process-wrapper {
                width: 1020px;
                height: 738px;
                margin: 0px auto;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-wrap: wrap;
                margin-top: 32px;
            }
            .clinic-home .page-process .item-wrapper {
                width: 340px;
                height: 246px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
            }
            .clinic-home .page-process .item-wrapper > img {
                width: 64px;
                height: 64px;
                margin-top: 32px;
            }
            .clinic-home .page-process .item-wrapper .info-wrapper {
                margin-top: 24px;
            }
            .clinic-home .page-process .item-wrapper .info-wrapper > .title {
                color: #091E42;
                font-size: 16px;
                font-weight: 400;
                line-height: 22px;
                font-family: PingFang-SC-Regular;
                letter-spacing: 0.8px;
                text-align: center;
            }
            .clinic-home .page-process .item-wrapper .info-wrapper > .desc {
                color: #6B778C;
                font-size: 14px;
                font-weight: 300;
                line-height: 20px;
                font-family: PingFang-SC-Light;
                text-align: center;
                width: 260px;
                margin-top: 12px;
            }
            .clinic-home .page-process .item-wrapper:nth-child(1)::before,
            .clinic-home .page-process .item-wrapper:nth-child(4)::before {
                position: absolute;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 1px;
                content: "";
                display: block;
                background: linear-gradient(90deg, rgba(255, 255, 255, 1) 0, #e6e7e6 100%);
            }
            .clinic-home .page-process .item-wrapper:nth-child(1)::after,
            .clinic-home .page-process .item-wrapper:nth-child(2)::after {
                position: absolute;
                right: 0;
                bottom: 0;
                height: 100%;
                width: 1px;
                content: "";
                display: block;
                background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0, #e6e7e6 100%);
            }
            .clinic-home .page-process .item-wrapper:nth-child(3)::after,
            .clinic-home .page-process .item-wrapper:nth-child(6)::after {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 1px;
                content: "";
                display: block;
                background: linear-gradient(270deg, rgba(255, 255, 255, 1) 0, #e6e7e6 100%);
            }
            .clinic-home .page-process .item-wrapper:nth-child(7)::after,
            .clinic-home .page-process .item-wrapper:nth-child(8)::after {
                position: absolute;
                right: 0;
                top: 0;
                height: 100%;
                width: 1px;
                content: "";
                display: block;
                background: linear-gradient(360deg, rgba(255, 255, 255, 1) 0, #e6e7e6 100%);
            }
            .clinic-home .page-process .item-wrapper:nth-child(2)::before,
            .clinic-home .page-process .item-wrapper:nth-child(5)::before {
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 1px;
                background: #e6e7e6;
                content: "";
                display: block;
            }
            .clinic-home .page-process .item-wrapper:nth-child(4)::after,
            .clinic-home .page-process .item-wrapper:nth-child(5)::after {
                position: absolute;
                right: 0;
                top: 0;
                height: 100%;
                width: 1px;
                background: #e6e7e6;
                content: "";
                display: block;
            }
            .clinic-home .page-adaptation {
                margin-top: 120px;
                height: 590px;
                text-align: center
            }
            .clinic-home .page-adaptation > h3 {
                font-size: 32px;
                line-height: 45px;
                color: #091e42;
                font-weight: 300;
                font-family: PingFang-SC-Light;
                position: relative
            }
            .clinic-home .page-adaptation > h3::after {
                content: '';
                display: block;
                width: 32px;
                height: 4px;
                border-radius: var(--abc-border-radius-small);
                background-color: #459eff;
                margin-left: 50%;
                margin-top: 16px;
                transform: translateX(-50%)
            }
            .clinic-home .page-adaptation .adaptation-content {
                text-align: center;
                font-size: 20px;
                color: #6b778c;
                font-family: PingFang-SC-Light;
                font-weight: 300;
                line-height: 28px;
                margin-top: 16px
            }
            .clinic-home .page-adaptation .adaptation-img-wrapper {
                margin-top: 80px;
                height: 401px
            }
            .clinic-home .page-adaptation .adaptation-img {
                height: 100%;
                height: 401px;
                width: 538px
            }
            .page-questions {
                width: 100%;
                height: 1257px;
                overflow: hidden;
                position: relative;
                background-color: #FAFAFA;;
                padding-top: 80px;
                margin-top: 160px;
            }
            .page-questions h3 {
                margin: 0 auto;
                font-size: 32px;
                font-family: PingFang-SC-Light;
                font-weight: 300;
                color: #091E42;
                line-height: 45px;
                text-align: center;
                margin: 0 auto;
                position: relative
            }
            .page-questions h3:after {
                content: '';
                width: 32px;
                height: 4px;
                border-radius: var(--abc-border-radius-small);
                background-color: #459eff;
                position: absolute;
                left: 50%;
                transform: translateX(-16px);
                bottom: -16px
            }
            .page-questions .question-content {
                margin: 20px auto 0;
                width: 1200px;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-wrap: wrap;
            }
            .page-questions .question-content .issue {
                width: 520px;
                text-align: justify;
                margin-top: 80px;
                position: relative;
                z-index: 1;
            }
            .page-questions .question-content > .issue:nth-child(2n + 1) {
                margin-right: 100px;
            }
            .page-questions .question-content .left {
                margin-left: 30px;
                margin-right: 50px
            }
            .page-questions .question-content .right {
                margin-left: 50px;
                margin-right: 30px
            }
            .page-questions .question-content .issue .issue-title {
                font-size: 0
            }
            .page-questions .question-content .issue .issue-title img {
                width: 24px;
                height: 24px;
                vertical-align: middle
            }
            .page-questions .question-content .issue .issue-title .issue-name {
                font-size: 20px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                color: #091e42;
                margin-left: 10px;
                vertical-align: middle
            }
            .page-questions .question-content .issue .issue-content {
                width: 520px;
                margin-top: 8px;
                height: 160px;
                color: #6b778c;
                font-size: 16px;
                font-family: PingFang-SC-Light;
                font-weight: 300;
                line-height: 32px;
                text-align: justify;
                overflow: hidden;
                text-overflow: ellipsis;
                position: relative;
                z-index: 1
            }
            .page-questions .question-content .issue .issue-content .content {
                width: 520px;
                height: 160px;
                color: #6b778c;
                font-size: 16px;
                font-family: PingFang-SC-Light;
                font-weight: 300;
                line-height: 32px;
                text-align: justify;
                overflow: hidden;
                text-overflow: ellipsis;
                z-index: 1;
                position: absolute;
                top: 0;
                left: 0
            }
            .page-questions .question-content .issue .issue-content .more {
                color: #005ed9;
                font-size: 16px;
                font-weight: 300;
                position: absolute;
                right: 0;
                bottom: 0;
                z-index: 2;
                background-color: #fafafa
            }
            .page-questions .question-content .issue .issue-content .ellipsis {
                position: absolute;
                font-size: 22px;
                font-weight: 300;
                bottom: 0;
                right: 32px;
                z-index: 2;
                background-color: #fafafa;
                color: #6b778c
            }
            .page-questions .question-content .issue:hover {
                z-index: 3
            }
            .page-questions .question-content .issue:hover .issue-content {
                overflow: visible
            }
            .page-questions .question-content .issue:hover .issue-content .content {
                height: 236px;
                z-index: 2;
                transition: height .3s cubic-bezier(.15, .15, .08, .88);
                position: absolute
            }
            .page-questions .question-content .issue:hover .issue-content .AI-content {
                height: 436px
            }
            .page-questions .question-content .issue:hover .more, .page-questions .question-content .issue:hover .ellipsis {
                display: none
            }
            .clinic-home .page-footer {
                width: 100%;
                height: 510px;
                padding-top: 56px;
                box-sizing: border-box;
                background-color: #FAFAFA;
                background-image: url("/static/homeimg/Wave_4.png");
                background-position: center bottom;
                background-size: 100% 400px;
                background-repeat: no-repeat;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
            }
            .clinic-home .page-footer .apply-btn {
                color: #fff;
                font-size: 18px;
                font-weight: 500;
                line-height: 58px;
                width: 254px;
                height: 58px;
                text-align: center;
                background: #007AFF;
                box-shadow: 0 4px 11px 0 rgba(2,65,153,0.27);
                border-radius: var(--abc-border-radius-small);
                text-align: center;
                cursor: pointer;
            }
            .clinic-home .page-footer .tit-wrapper {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 228px;
                height: 85px;
            }
            .clinic-home .page-footer .tit-wrapper > img {
                width: 100px;
                height: 85px;
                margin-right: 51px;
            }
            .clinic-home .page-footer .tit-wrapper > span {
                color: #FFFFFF;
                font-size: 46px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                line-height: 65px;
                letter-spacing: 0;
                text-align: center;
            }
        }
    </style>
    <!--诊所管家__mobile-->
    <style>
        /* mobile尺寸 */
        @media screen and (max-width: 800px) {
            .clinic-home {
                position: relative;
                padding-top: 10.67rem;
                padding-bottom: 8.5rem;
                display: none;
            }
            .clinic-home.active{
                display: block;
            }
            .clinic-home .mock-bg {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100%;
                height: 10.67rem;
                background: linear-gradient(225deg, #00B7DC 0%, #2680F7 100%);
                z-index: 1;
            }
            .clinic-home .page-index {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100%;
                height: 10.67rem;
                background-image: url("/static/homeimg/home-zs-bg_mobile1.png");
                background-size: 100% auto;
                background-repeat: no-repeat;
                z-index: 2;
            }
            .clinic-home .page-index .left-wrapper {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
            }
            .clinic-home .page-index .left-wrapper > .logo {
                width: 3.88rem;
                height: 0.53rem;
                margin-top: 2.93rem;
                background-image: url("/static/homeimg/<EMAIL>");
                background-size: cover;
            }
            .clinic-home .page-index .left-wrapper > .desc-1 {
                color: #FFFFFF;
                font-size: 0.53rem;
                font-weight: 300;
                font-family: PingFangSC-Light;
                letter-spacing: 0.13rem;
                line-height: 0.75rem;
                text-align: center;
                margin-top: 1.84rem;
            }
            .clinic-home .page-index .left-wrapper > .desc-2 {
                color: #FFFFFF;
                font-size: 0.75rem;
                font-weight: 300;
                font-family: PingFangSC-Light;
                letter-spacing: 0.15rem;
                line-height: 1.07rem;
                text-align: center;
                margin-top: 0.16rem;
            }
            .clinic-home .page-feature {
                margin: 0.43rem 0px 0.53rem 0px;
            }
            .clinic-home .page-feature .item-feature {
                width: 9.15rem;
                height: 3.47rem;
                padding: 0px 1.07rem;
                box-sizing: border-box;
                border-radius: 0.05rem;
                box-shadow: 0 0.13rem 0.45rem 0 rgba(0,0,0,0.07);
                background-color: #fff;
                margin: 0.21rem auto;
                display: flex;
                align-items: center;
            }
            .clinic-home .page-feature .item-feature > img {
                width: 1.71rem;
                height: 1.71rem;
            }
            .clinic-home .page-feature .item-feature .info-wrapper {
                padding-left: 0.96rem;
            }
            .clinic-home .page-feature .item-feature .info-wrapper > .tit {
                color: #05B387;
                font-size: 0.43rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.59rem;
                letter-spacing: 0;
            }
            .clinic-home .page-feature .item-feature .info-wrapper > .des {
                margin-top: 0.11rem
            }
            .clinic-home .page-feature .item-feature .info-wrapper > .des p {
                color: #6B778C;
                font-size: 0.32rem;
                font-weight: 400;
                line-height: 0.45rem;
                font-family: PingFangSC-Regular;
                letter-spacing: 0;
            }
            .clinic-home .page-process {
                width: 100%;
                padding-top: 2.43rem;
                box-sizing: border-box;
                background-image: url("/static/homeimg/<EMAIL>");
                background-position: top center;
                background-size: auto 7.07rem;
                background-repeat: no-repeat;
            }
            .clinic-home .page-process h3 {
                color: #091E42;
                font-size: 0.48rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                line-height: 0.67rem;
                letter-spacing: 0;
                text-align: center;
            }
            .clinic-home .page-process h3::after {
                content: '';
                display: block;
                width: 1.28rem;
                height: 0.11rem;
                border-radius: 2.67rem;
                background-color: #459eff;
                margin-left: 50%;
                margin-top: 0.43rem;
                transform: translateX(-50%);
            }
            .clinic-home .page-process .process-wrapper {
                margin-top: 2.03rem;
            }
            .clinic-home .page-process .item-wrapper {
                width: 9.15rem;
                height: 1.71rem;
                margin: 0.75rem auto 0;
                display: flex;
            }
            .clinic-home .page-process .item-wrapper > img {
                width: 1.71rem;
                height: 1.71rem;
            }
            .clinic-home .page-process .item-wrapper .info-wrapper {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
                margin-left: 0.43rem;
            }
            .clinic-home .page-process .item-wrapper .info-wrapper > .title {
                color: #091E42;
                font-size: 0.37rem;
                font-weight: 500;
                line-height: 0.53rem;
                font-family: PingFangSC-Medium;
                letter-spacing: 0;
            }
            .clinic-home .page-process .item-wrapper .info-wrapper > .desc {
                color: #6B778C;
                font-size: 0.32rem;
                font-weight: 400;
                line-height: 0.45rem;
                font-family: PingFangSC-Regular;
                width: 7.01rem;
                margin-top: 0.11rem;
                text-align: left;
            }
            .clinic-home .page-adaptation {
                margin-top: 2.13rem;
            }
            .clinic-home .page-adaptation > h3 {
                color: #091E42;
                font-size: 0.48rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                line-height: 0.67rem;
                letter-spacing: 0;
                text-align: center;
            }
            .clinic-home .page-adaptation > h3::after {
                content: '';
                display: block;
                width: 1.28rem;
                height: 0.11rem;
                border-radius: 2.67rem;
                background-color: #459eff;
                margin-left: 50%;
                margin-top: 0.43rem;
                transform: translateX(-50%);
            }
            .clinic-home .page-adaptation .adaptation-content {
                color: #6B778C;
                font-size: 0.37rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.53rem;
                margin: 0.43rem 0px;
                text-align: center;
            }
            .clinic-home .page-adaptation .adaptation-img-wrapper {
                width: 8.03rem;
                height: 5.97rem;
                margin: 0px auto;
            }
            .clinic-home .page-adaptation .adaptation-img {
                width: 100%;
                height: 100%;
            }
            .clinic-home .page-questions {
                width: 100%;
                padding-top: 0.85rem;
                margin-top: 2.35rem;
                background: #FAFAFA;
            }
            .clinic-home .page-questions > h3 {
                color: #091E42;
                font-size: 0.48rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                line-height: 0.67rem;
                letter-spacing: 0;
                text-align: center;
            }
            .clinic-home .page-questions > h3::after {
                content: '';
                display: block;
                width: 1.28rem;
                height: 0.11rem;
                border-radius: 2.67rem;
                background-color: #459eff;
                margin-left: 50%;
                margin-top: 0.43rem;
                transform: translateX(-50%);
            }
            .clinic-home .page-questions .question-content {
                margin-top: 0.72rem;
            }
            .clinic-home .page-questions .issue {
                width: 9.57rem;
                margin: 0px auto;
                border-bottom: 1px solid #E6EAEE;
            }
            .clinic-home .page-questions .issue:first-child {
                border-top: 1px solid #E6EAEE;
            }
            .clinic-home .page-questions .issue .issue-title {
                height: 1.28rem;
                padding: 0.32rem 0.21rem;
                box-sizing: border-box;
                display: flex;
                justify-content: flex-start;
                align-items: center;
            }
            .clinic-home .page-questions .issue .issue-title.active {
                background-color: #EFF3F6;
            }
            .clinic-home .page-questions .issue .issue-title >img {
                width: 0.64rem;
                height: 0.64rem;
            }
            .clinic-home .page-questions .issue .issue-title .issue-name {
                color: #091E42;
                font-size: 0.37rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                letter-spacing: 0;
                margin-left: 0.21rem;
                flex-shrink: 0;
                width: 7.3rem;
                margin-right: 0.6rem;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                text-overflow: ellipsis;
            }
            .clinic-home .page-questions .issue .issue-title .close-box {
                width: 0.43rem;
                text-align: right;
            }
            .clinic-home .page-questions .issue .issue-title .close-box img {
                width: 0.43rem;
                height: 0.43rem;
                transition: all 0.3s ease;
                transform: rotate(0deg);
                cursor: pointer;
            }
            .clinic-home .page-questions .issue .issue-content {
                height: 0;
                overflow: hidden;
            }
            .clinic-home .page-questions .issue .content {
                color: #6b778c;
                font-size: 0.32rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.45rem;
                letter-spacing: 0;
                padding: 0 0.21rem 0.32rem;
            }
            .clinic-home .page-questions .opened .close-box > img {
                transform: rotate(45deg) !important;
            }
            .clinic-home .page-questions .opened .issue-content {
                height: auto;
            }
            .clinic-home .page-footer {
                width: 100%;
                height: 7.49rem;
                padding-top: 0.8rem;
                box-sizing: border-box;
                background-image: url("/static/homeimg/<EMAIL>");
                background-position: bottom center;
                background-size: 100% auto;
                background-repeat: no-repeat;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
            }
            .clinic-home .page-footer .apply-btn {
                color: #fff;
                font-size: 0.37rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.91rem;
                width: 4.16rem;
                height: 0.91rem;
                text-align: center;
                background: #007AFF;
                box-shadow: 0 0.11rem 0.29rem 0 #02419945;
                border-radius: 0.05rem;
                letter-spacing: 0;
                cursor: pointer;
            }
            .clinic-home .page-footer .tit-wrapper {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 3.2rem;
            }
            .clinic-home .page-footer .tit-wrapper > img {
                width: 1.01rem;
                height: 1.01rem;
                margin-right: 0.43rem;
            }
            .clinic-home .page-footer .tit-wrapper > span {
                color: #FFFFFF;
                font-size: 0.43rem;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                line-height: 0.59rem;
                letter-spacing: 0;
                text-align: center;
            }
        }
    </style>
    <!--口腔管家-->
    <style>
        /* pc尺寸 */
        @media screen and (min-width: 800px) {
            .dentistry-home {
                position: relative;
                width: 100%;
                padding-top: 726px;
                display: none;
            }
            .dentistry-home.active {
                display: block;
            }
            .dentistry-home .mock-bg {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100%;
                height: 726px;
                background: linear-gradient(180deg, #2A6DFF 0%, #4785FF 100%);
                z-index: 1;
            }
            .dentistry-home .page-index {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100%;
                height: 726px;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/page-index_bg2.png");
                background-size: 100% 1637px;
                background-position: top center;
                background-repeat: no-repeat;
                display: flex;
                justify-content: center;
                padding-top: 200px;
                margin: 0px auto;
                z-index: 2;
            }
            .dentistry-home .page-index .left-wrapper {
                width: 409px;
                margin: 28px 55px 0px 17px;
                display: flex;
                flex-direction: column;
            }
            .dentistry-home .page-index .left-wrapper > .desc {
                height: 128px;
                font-family: SourceHanSansCN-Bold;
                font-weight: 700;
                font-size: 50px;
                color: #FFFFFF;
                letter-spacing: 0;
                line-height: 64px;
            }
            .dentistry-home .page-index .left-wrapper > .flag {
                width: 266px;
                height: 33px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                font-size: 24px;
                color: #FFFFFF;
                letter-spacing: 4.8px;
                margin-top: 32px;
            }
            .dentistry-home .page-index .left-wrapper .btn-wrapper {
                width: 100%;
                height: 48px;
                display: flex;
                align-items: center;
                margin-top: 60px;
            }
            .dentistry-home .page-index .left-wrapper .btn-wrapper > button.btn-apply {
                width: 258px;
                height: 48px;
                color: #007AFF;
                font-size: 18px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 48px;
                letter-spacing: 0;
                border-radius: var(--abc-border-radius-small);
                background-color: #FFFFFF;
                text-align: center;
                cursor: pointer;
            }
            .dentistry-home .page-index .left-wrapper .btn-wrapper > button.btn-price {
                width: 128px;
                height: 48px;
                color: #FFFFFF;
                font-size: 18px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 48px;
                letter-spacing: 0;
                border-radius: var(--abc-border-radius-small);
                background-color: #33C4FF;
                text-align: center;
                cursor: pointer;
                margin-left: 8px;
            }
            .dentistry-home .page-index .right-wrapper {
                width: 657px;
                height: 414px;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/<EMAIL>");
                background-size: 100% 100%;
            }
            .dentistry-home .page-major .title {
                color: #007AFF;
                font-size: 44px;
                font-weight: 600;
                line-height: 62px;
                padding: 80px 0px;
                text-align: center;
            }
            .dentistry-home .page-major .content {
                background-image: linear-gradient(180deg, #f9fbff00 0%, #F9FBFF 52%, #f9fbff00 100%);
            }
            .dentistry-home .page-major .cell-template {
                width: 600px;
                height: 540px;
            }
            .dentistry-home .page-major .cell-template > .tit {
                color: #000;
                font-size: 34px;
                font-weight: 500;
                line-height: 48px;
                font-family: PingFangSC-Medium;
                letter-spacing: 0;
                text-align: left;
                padding-left: 48px;
            }
            .dentistry-home .page-major .cell-template > .des {
                color: #777777;
                font-size: 16px;
                line-height: 22px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                letter-spacing: 0;
                text-align: left;
                margin-top: 16px;
                padding-left: 48px;
            }
            .dentistry-home .page-major .cell-template > .img {
                width: 600px;
                height: 434px;
                background-size: cover;
                margin-top: 20px;
            }
            .dentistry-home .page-major .cell-group {
                width: 1200px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0px auto;
            }
            .dentistry-home .page-major .group-1 {
                margin-top: 140px;
            }
            .dentistry-home .page-major .group-2 {
                margin-top: 161px;
                margin-bottom: 81px;
            }
            .dentistry-home .page-major .cell-1 {
                width: 1200px;
                height: 626px;
                margin: 0px auto;
            }
            .dentistry-home .page-major .cell-1 > .img {
                width: 1200px;
                height: 500px;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/<EMAIL>");
            }
            .dentistry-home .page-major .cell-2 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-major .cell-3 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-major .cell-4 > .img {
                background-image: url("//static-common-cdn.abcyun.cn/img/official/<EMAIL>");
            }
            .dentistry-home .page-major .cell-5 > .img {
                background-image: url("//static-common-cdn.abcyun.cn/img/official/<EMAIL>");
            }
            .dentistry-home .page-major .cell-3 > .tit,
            .dentistry-home .page-major .cell-3 > .des,
            .dentistry-home .page-major .cell-5 > .tit,
            .dentistry-home .page-major .cell-5 > .des {
                padding-left: 93px;
            }
            .dentistry-home .page-almighty {
                background-image: url("//static-common-cdn.abcyun.cn/img/official/page-almighty_bg.png");
                background-size: auto 209px;
                background-position: top center;
                background-repeat: no-repeat;
            }
            .dentistry-home .page-almighty .title {
                color: #007AFF;
                font-size: 44px;
                font-weight: 600;
                line-height: 62px;
                padding-top: 160px;
                text-align: center;
            }
            .dentistry-home .page-almighty .cell-template {
                width: 1200px;
                height: 467px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                background-size: 600px auto;
                background-position: top right;
                background-repeat: no-repeat;
                padding-left: 48px;
                box-sizing: border-box;
                margin: 0px auto;
            }
            .dentistry-home .page-almighty .align-right {
                padding-right: 48px;
                align-items: flex-end;
                background-position: top left;
            }
            .dentistry-home .page-almighty .cell-template .tit {
                color: #000000;
                font-size: 34px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                line-height: 48px;
                letter-spacing: 0;
            }
            .dentistry-home .page-almighty .cell-template .des {
                color: #777777;
                font-size: 16px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                line-height: 22px;
                letter-spacing: 0;
                margin-top: 16px;
            }
            .dentistry-home .page-almighty .cell-1 {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-2 {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-3 {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-4 {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-5 {
                background-image: url("//static-common-cdn.abcyun.cn/img/official/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-3 .des {
                max-width: 438px;
            }
            .dentistry-home .page-referral {
                background-image: url("//static-common-cdn.abcyun.cn/img/official/page-referral_bg1.png");
                background-size: 100% auto;
                background-position: top center;
                background-repeat: no-repeat;
            }
            .dentistry-home .page-referral .title {
                color: #007AFF;
                font-size: 44px;
                font-weight: 600;
                line-height: 62px;
                padding: 160px 0px 90px 0px;
                text-align: center;
            }
            .dentistry-home .page-referral .cell-template {
                width: 600px;
                height: 540px;
            }
            .dentistry-home .page-referral .cell-template > .tit {
                color: #000;
                font-size: 34px;
                font-weight: 500;
                line-height: 48px;
                font-family: PingFangSC-Medium;
                letter-spacing: 0;
                text-align: left;
                padding-left: 48px;
            }
            .dentistry-home .page-referral .cell-template > .des {
                color: #777777;
                font-size: 16px;
                line-height: 22px;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                letter-spacing: 0;
                text-align: left;
                margin-top: 16px;
                padding-left: 48px;
                max-width: 430px;
            }
            .dentistry-home .page-referral .cell-template > .img {
                width: 600px;
                height: 434px;
                background-size: cover;
                margin-top: 20px;
            }
            .dentistry-home .page-referral .cell-group {
                width: 1200px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0px auto;
                margin-bottom: 160px;
            }
            .dentistry-home .page-referral .cell-1 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .cell-2 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .cell-3 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .cell-4 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .cell-2 > .tit,
            .dentistry-home .page-referral .cell-2 > .des,
            .dentistry-home .page-referral .cell-4 > .tit,
            .dentistry-home .page-referral .cell-4 > .des {
                padding-left: 95px;
            }
            .dentistry-home .page-referral .card-box > .tit {
                color: #000000;
                font-size: 34px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 48px;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-referral .item-group {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 60px 0px 160px;
            }
            .dentistry-home .page-referral .item-wrapper {
                width: 338px;
                height: 449px;
                background: #FFFFFF;
                border: 1px solid #DEDEDE;
                box-shadow: 0 2px 12px 0 #EEF0F5;
                border-radius: 6px;
                margin: 0px 22.5px;
                padding: 16px;
                box-sizing: border-box;
            }
            .dentistry-home .page-referral .item-wrapper > .img {
                width: 306px;
                height: 268px;
                background-size: cover;
            }
            .dentistry-home .page-referral .item-wrapper > .tit {
                color: #000;
                font-size: 18px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 25px;
                margin-top: 16px;
                text-align: center;
                letter-spacing: 0;
            }
            .dentistry-home .page-referral .item-wrapper > .des {
                color: #777777;
                font-size: 16px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 22px;
                margin: 0px auto;
                margin-top: 20px;
                max-width: 273px;
                text-align: center;
                letter-spacing: 0;
            }
            .dentistry-home .page-referral .item-1 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .item-2 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .item-3 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-increase-income .box-1 {
                width: 100%;
                height: 1060px;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/page-increase-income_bg1.png");
                background-size: auto 100%;
                background-position: top center;
                background-repeat: no-repeat;
                padding-top: 120px;
                box-sizing: border-box;
            }
            .dentistry-home .page-increase-income .box-1 > .title {
                color: #007AFF;
                font-size: 46px;
                font-weight: 600;
                font-family: PingFangSC-SNaNpxibold;
                line-height: 65px;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-increase-income .box-1 > .desc-wrapper {
                height: 48px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 60px;
            }
            .dentistry-home .page-increase-income .box-1 > .desc-wrapper .tit {
                color: #000000;
                font-size: 34px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 48px;
                letter-spacing: 0;
            }
            .dentistry-home .page-increase-income .box-1 > .desc-wrapper .des {
                color: #000000;
                font-size: 24px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 33px;
                letter-spacing: 0;
                margin: 0px 48px 0px 8px;
            }
            .dentistry-home .page-increase-income .box-1 > .img {
                width: 1130px;
                height: 615px;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/<EMAIL>");
                background-size: cover;
                margin: 48px auto 0px;
            }
            .dentistry-home .page-increase-income .box-2 {
                width: 100%;
                height: 936px;
                padding: 160px 0px;
                box-sizing: border-box;
                margin: 0px auto;
            }
            .dentistry-home .page-increase-income .box-2 > .tit {
                color: #000000;
                font-size: 36px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 50px;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-increase-income .box-2 .feat-wrapper {
                width: 1149px;
                margin: 0px auto;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                flex-wrap: wrap;
            }
            .dentistry-home .page-increase-income .box-2 .feat-wrapper > .item-feat {
                width: 383px;
                padding: 0px 22.5px;
                margin-top: 61px;
                box-sizing: border-box;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat .title-wrapper {
                width: 100%;
                height: 24px;
                display: flex;
                justify-content: flex-start;
                align-items: center;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat .title-wrapper > img {
                width: 28px;
                height: 28px;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat .title-wrapper > span {
                color: #000;
                font-size: 20px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                letter-spacing: 1px;
                line-height: 28px;
                margin-left: 8px;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat > p {
                color: #777777;
                font-size: 14px;
                font-weight: 300;
                font-family: PingFangSC-Light;
                line-height: 20px;
                margin-top: 7px;
                letter-spacing: 0;
            }
            .dentistry-home .page-increase-income .box-3 {
                width: 100%;
                height: 538px;
                padding-top: 80px;
                box-sizing: border-box;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/page-increase-income_box3_bg1.png");
                background-size: cover;
            }
            .dentistry-home .page-increase-income .box-3 > .tit {
                color: #000000;
                font-size: 36px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 50px;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-increase-income .box-3 .more-wrapper {
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                width: 1104px;
                margin: 0px auto;
                margin-top: 20px;
            }
            .dentistry-home .page-increase-income .box-3 .item-more {
                width: 260px;
                height: 134px;
                padding: 30px 36px 30px 24px;
                box-sizing: border-box;
                background-image: linear-gradient(180deg, #E3EAF9 0%, #FFFFFF 100%);
                border: 2px solid #FFFFFF;
                box-shadow: 0 2px 12px 0 #EEF0F5;
                border-radius: 6px;
                display: flex;
                justify-content: space-between;
                align-items: stretch;
                margin-top: 20px;
            }
            .dentistry-home .page-increase-income .box-3 .item-more .left-box {
                width: 142px;
            }
            .dentistry-home .page-increase-income .box-3 .item-more .left-box > .title {
                color: #000;
                font-size: 20px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                letter-spacing: 1px;
                line-height: 28px;
                text-align: left;
            }
            .dentistry-home .page-increase-income .box-3 .item-more .left-box > .content {
                color: #777777;
                font-size: 14px;
                font-weight: 300;
                font-family: PingFangSC-Light;
                line-height: 20px;
                letter-spacing: 0;
                margin-top: 10px;
            }
            .dentistry-home .page-increase-income .box-3 .item-more .right-box {
                width: 38px;
            }
            .dentistry-home .page-increase-income .box-3 .item-more .right-box > img {
                width: 38px;
                height: 38px;
                margin-top: 36px;
            }
            /* .dentistry-home .page-increase-income .card-wrapper {
                width: 1200px;
                height: 180px;
                opacity: 0.6;
                background-image: linear-gradient(87deg, #F9DBA3 31%, rgba(253,240,220,0.00) 100%, rgba(253,240,220,0.00) 100%);
                border-radius: 6px;
                padding: 40px 36px;
                box-sizing: border-box;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin: 0px auto;
                margin-top: 60px;
            }
            .dentistry-home .page-increase-income .card-wrapper > img {
                width: 101px;
                height: 101px;
                border: 4px solid #DDAD6C;
                border-radius: 101px;
                flex-shrink: 0;
            }
            .dentistry-home .page-increase-income .card-wrapper > .info-wrapper {
                width: 136px;
                margin-left: 24px;
            }
            .dentistry-home .page-increase-income .card-wrapper > .info-wrapper .name {
                color: #091E42;
                font-size: 20px;
                font-weight: 500;
                line-height: 28px;
            }
            .dentistry-home .page-increase-income .card-wrapper > .info-wrapper .desc {
                color: #6B778C;
                font-size: 16px;
                font-weight: 300;
                line-height: 24px;
                margin-top: 4px;
            }
            .dentistry-home .page-increase-income .card-wrapper > .content {
                width: 803px;
                color: #333333;
                font-size: 18px;
                font-weight: 300;
                line-height: 28px;
            } */
            .dentistry-home .page-increase-income .box-4 {
                padding: 160px 0px 80px;
            }
            .dentistry-home .page-increase-income .box-4 > .tit {
                color: #000000;
                font-size: 36px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 50px;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-increase-income .box-4 .firends-wrapper {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 41px calc(50% - 600px);
                margin-top: 56px;
            }
            .dentistry-home .page-increase-income .box-4 .item-firend {
                width: 290px;
                height: 158px;
                background: #ffffff;
                border: 0.5px solid #DEDEDE;
                border-radius: 6px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .dentistry-home .page-increase-income .box-4 .item-firend > img {
                width: 206px;
                height: 113px;
                border: 1px dashed #C3C4C5;
            }
            .dentistry-home .page-increase-income .box-4 .firends-more-wrapper {
                width: 1200px;
                overflow-x: auto;
                margin: 60px auto 0px;
            }
            .dentistry-home .page-increase-income .box-4 .firends-more-wrapper::-webkit-scrollbar {
                display: none;
            }
            .dentistry-home .page-increase-income .box-4 .scroll-body {
                width: 1164px;
                /* height: 256px; */
                display: flex;
                justify-content: flex-start;
                flex-wrap: wrap;
                margin: 0px auto;
            }
            .dentistry-home .page-increase-income .box-4 .item-firends-more {
                width: 220px;
                height: 120px;
                background: #ffffff;
                border: 0.5px solid #DFE3E8;
                margin-right: 16px;
                margin-bottom: 16px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .dentistry-home .page-increase-income .box-4 .scroll-body > .item-firends-more:nth-child(5n + 5) {
                margin-right: 0px;
            }
            .dentistry-home .page-increase-income .box-4 .item-firends-more > img {
                width: 100%;
                height: 100%;
            }
            .dentistry-home .page-footer {
                width: 100%;
                height: 541px;
                padding-top: 140px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/page-footer_bg1.png");
                background-size: auto 100%;
                background-repeat: no-repeat;
                background-position: bottom center;
            }
            .dentistry-home .page-footer .title-wrapper {
                width: 100%;
                height: 42px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .dentistry-home .page-footer .title-wrapper > h5 {
                color: #000;
                font-size: 30px;
                font-weight: 600;
                line-height: 42px;
                font-family: PingFangSC-SNaNpxibold;
                letter-spacing: 0;
            }
            .dentistry-home .page-footer .title-wrapper > span {
                color: #000;
                font-size: 24px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 33px;
                letter-spacing: 0;
                margin-left: 16px;
            }
            .dentistry-home .page-footer .apply-btn {
                color: #fff;
                font-size: 18px;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                letter-spacing: 0;
                line-height: 58px;
                width: 260px;
                height: 58px;
                text-align: center;
                background: #007AFF;
                box-shadow: 0 4px 11px 0 #02419945;
                border-radius: var(--abc-border-radius-small);
                margin-top: 46px;
                cursor: pointer;
            }
        }
    </style>
    <!--口腔管家__mobile-->
    <style>
        /* mobile尺寸 */
        @media screen and (max-width: 800px) {
            .dentistry-home {
                position: relative;
                width: 100%;
                padding-top: 10.67rem;
                display: none;
            }
            .dentistry-home.active{
                display: block;
            }
            .dentistry-home .mock-bg {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100%;
                height: 10.67rem;
                background: linear-gradient(180deg, #2A6DFF 0%, #4785FF 100%);
                z-index: 1;
            }
            .dentistry-home .page-index {
                position: absolute;
                top: 0px;
                left: 0px;
                width: 100%;
                height: 10.67rem;
                background-image: url("/static/homeimg/home-kq-bg_mobile.png");
                background-size: 100% auto;
                background-repeat: no-repeat;
                z-index: 2;
            }
            .dentistry-home .page-index .left-wrapper {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
            }
            .dentistry-home .page-index .left-wrapper > .title {
                color: #FFFFFF;
                font-size: 0.53rem;
                font-weight: 600;
                font-family: PingFangSC-SNaNremibold;
                letter-spacing: 0.11rem;
                line-height: 0.75rem;
                margin-top: 3.2rem;
            }
            .dentistry-home .page-index .left-wrapper > .desc {
                color: #ffffff;
                font-size: 0.64rem;
                font-weight: 600;
                font-family: PingFangSC-SNaNremibold;
                letter-spacing: 0;
                line-height: 0.88rem;
                margin-top: 1.04rem;
            }
            .dentistry-home .page-index .left-wrapper > .flag {
                color: #FFFFFF;
                font-size: 0.43rem;
                font-weight: 300;
                font-family: PingFangSC-Light;
                letter-spacing: 0.09rem;
                line-height: 0.59rem;
                margin-top: 0.21rem;
            }
            .dentistry-home .cell-template {
                width: 100%;
                padding-top: 0.8rem;
                box-sizing: border-box;
            }
            .dentistry-home .cell-template > .tit {
                color: #000;
                font-size: 0.43rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.59rem;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .cell-template > .des {
                color: #777777;
                font-size: 0.32rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.45rem;
                letter-spacing: 0;
                text-align: center;
                max-width: 8.96rem;
                margin: 0.21rem auto 0px;
            }
            .dentistry-home .cell-template > .img {
                width: 100%;
                background-size: auto 100%;
                background-repeat: no-repeat;
                background-position: center;
            }
            .dentistry-home .page-major {
                padding-top: 1.6rem;
                background-image: linear-gradient(180deg, #eff4ffcc 0%, #EFF4FF 8%, #ecf1fb00 100%);
            }
            .dentistry-home .page-major > .title {
                color: #007AFF;
                font-size: 0.53rem;
                font-weight: 600;
                font-family: PingFangSC-SNaNremibold;
                line-height: 0.75rem;
                text-align: center;
            }
            .dentistry-home .page-major .cell-1 > .img {
                height: 5.15rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-major .cell-2 > .img {
                height: 7.36rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-major .cell-3 > .img {
                height: 8.16rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-major .cell-4 > .img {
                height: 6.56rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-major .cell-5 > .img {
                height: 6.45rem;
                background-image: url("//static-common-cdn.abcyun.cn/img/official/<EMAIL>");
            }
            .dentistry-home .page-almighty {
                background-image: url("/static/homeimg/page-almighty_mobile_bg.png");
                background-size: 100% 1.44rem;
                background-position: top center;
                background-repeat: no-repeat;
                padding-top: 1.44rem;
            }
            .dentistry-home .page-almighty .title {
                color: #007AFF;
                font-size: 0.53rem;
                line-height: 0.75rem;
                font-weight: 600;
                font-family: PingFangSC-SNaNremibold;
                text-align: center;
            }
            .dentistry-home .page-almighty .cell-1 > .img {
                height: 6.13rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-2 > .img {
                height: 5.65rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-3 > .img {
                height: 5.65rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-4 > .img {
                height: 6.93rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .cell-5 > .img {
                height: 6.75rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral {
                background-image: url("/static/homeimg/page-referral_mobile_bg.png");
                background-size: 100% auto;
                background-position: top center;
                background-repeat: no-repeat;
                padding-top: 1.49rem;
            }
            .dentistry-home .page-referral .title {
                color: #007AFF;
                font-size: 0.53rem;
                line-height: 0.75rem;
                font-weight: 600;
                font-family: PingFangSC-SNaNremibold;
                text-align: center;
            }
            .dentistry-home .page-referral .cell-1 > .img {
                height: 8.85rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .cell-2 > .img {
                height: 8.51rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .cell-3 > .img {
                height: 6.27rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .cell-4 > .img {
                height: 7.65rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .card-box {
                padding: 0.8rem 0px;
            }
            .dentistry-home .page-referral .card-box .tit {
                color: #000000;
                font-size: 0.43rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.59rem;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-referral .card-box .item-group {
                padding: 0.03rem;
            }
            .dentistry-home .page-referral .card-box .item-wrapper {
                width: 7.89rem;
                background: #FFFFFF;
                border: 1px solid #DEDEDE;
                box-shadow: 0 0.05rem 0.32rem 0 #EEF0F5;
                border-radius: 0.16rem;
                padding: 0.64rem 0px;
                box-sizing: border-box;
                margin: 0px auto;
                margin-top: 0.64rem;
            }
            .dentistry-home .page-referral .card-box .item-wrapper > .tit {
                color: #000000;
                font-size: 0.37rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.53rem;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-referral .card-box .item-wrapper > .des {
                color: #777777;
                font-size: 0.32rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                letter-spacing: 0;
                text-align: center;
                width: 6.61rem;
                margin: 0.08rem auto 0px;
            }
            .dentistry-home .page-referral .card-box .item-wrapper > .img {
                width: 6.61rem;
                margin: 0px auto;
                background-size: cover;
            }
            .dentistry-home .page-referral .card-box .item-1 > .img {
                height: 4.45rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .card-box .item-2 > .img {
                height: 4.72rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-referral .card-box .item-3 > .img {
                height: 4.72rem;
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .item-wrapper > .tit {
                color: #000;
                font-size: 0.37rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.53rem;
                margin-top: 0.8rem;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-almighty .item-wrapper > .des {
                color: #000000;
                font-size: 0.32rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.45rem;
                letter-spacing: 0;
                text-align: center;
                max-width: 8.27rem;
                margin: 0.21rem auto 0.32rem;
            }
            .dentistry-home .page-almighty .item-wrapper > .img {
                width: 100%;
                height: 4.85rem;
                background-repeat: no-repeat;
                background-size: auto 100%;
                background-position: center;
            }
            .dentistry-home .page-almighty .item-1 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .item-2 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-almighty .item-3 > .img {
                background-image: url("/static/homeimg/<EMAIL>");
            }
            .dentistry-home .page-increase-income .box-1 {
                height: 9.31rem;
                padding-top: 1.07rem;
                box-sizing: border-box;
                background-image: linear-gradient(90deg, #F2F6FF 0%, #E5EEFF 100%);
            }
            .dentistry-home .page-increase-income .box-1 > .title {
                color: #007AFF;
                font-size: 0.53rem;
                font-weight: 600;
                font-family: PingFangSC-SNaNremibold;
                line-height: 0.75rem;
                text-align: center;
            }
            .dentistry-home .page-increase-income .box-1 > .desc-wrapper {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 0.53rem;
            }
            .dentistry-home .page-increase-income .box-1 > .desc-wrapper .tit {
                line-height: 0.59rem;
                color: #000000;
                font-size: 0.43rem;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                letter-spacing: 0;
            }
            .dentistry-home .page-increase-income .box-1 > .desc-wrapper .des {
                color: #000000;
                font-size: 0.32rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.45rem;
                letter-spacing: 0;
                text-align: center;
                margin-left: 0.11rem;
            }
            .dentistry-home .page-increase-income .box-1 > .tit {
                color: #000000;
                font-size: 0.32rem;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                line-height: 0.45rem;
                letter-spacing: 0;
                text-align: center;
                margin-top: 0.11rem;
            }
            .dentistry-home .page-increase-income .box-1 > .img {
                width: 100%;
                height: 4.91rem;
                background-image: url("/static/homeimg/<EMAIL>");
                background-size: cover;
                margin-top: 0.24rem;
            }
            .dentistry-home .page-increase-income .box-2 {
                padding: 0.8rem 0px;
            }
            .dentistry-home .page-increase-income .box-2 > .tit {
                color: #000000;
                font-size: 0.48rem;
                font-family: PingFangSC-SNaNremibold;
                font-weight: 600;
                line-height: 0.67rem;
                text-align: center;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat {
                width: 8.72rem;
                margin: 0.64rem auto 0px;
                box-sizing: border-box;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat .title-wrapper {
                width: 100%;
                height: 0.64rem;
                display: flex;
                justify-content: flex-start;
                align-items: center;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat .title-wrapper > img {
                width: 0.64rem;
                height: 0.64rem;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat .title-wrapper > span {
                color: #000;
                font-size: 0.43rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                letter-spacing: 0.02rem;
                line-height: 0.59rem;
                margin-left: 0.11rem;
            }
            .dentistry-home .page-increase-income .box-2 .item-feat > p {
                color: #777777;
                font-size: 0.37rem;
                font-weight: 300;
                font-family: PingFangSC-Light;
                line-height: 0.53rem;
                margin-top: 0.24rem;
                letter-spacing: 0;
            }
            .dentistry-home .page-increase-income .box-3 {
                padding: 0.8rem 0px;
                background-color: #F9FBFF;
            }
            .dentistry-home .page-increase-income .box-3 > .tit {
                color: #000000;
                font-size: 0.48rem;
                font-family: PingFangSC-SNaNremibold;
                font-weight: 600;
                line-height: 0.67rem;
                text-align: center;
            }
            .dentistry-home .page-increase-income .more-wrapper {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
                padding-top: 0.37rem;
            }
            .dentistry-home .page-increase-income .item-more {
                width: 8.72rem;
                height: 3.25rem;
                padding: 0.64rem;
                box-sizing: border-box;
                background-image: linear-gradient(180deg, #E3EAF9 0%, #FFFFFF 100%);
                border: 0.05rem solid #FFFFFF;
                box-shadow: 0 0.05rem 0.32rem 0 #EEF0F5;
                border-radius: 0.16rem;
                margin-top: 0.61rem;
                display: flex;
                justify-content: space-between;
                align-items: stretch;
            }
            .dentistry-home .page-increase-income .item-more .left-box {
                width: 5.79rem;
            }
            .dentistry-home .page-increase-income .item-more .left-box > .title {
                color: #000;
                font-size: 0.43rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.59rem;
                text-align: left;
                letter-spacing: 0.02rem;
            }
            .dentistry-home .page-increase-income .item-more .left-box > .content {
                color: #777777;
                font-size: 0.37rem;
                font-weight: 300;
                font-family: PingFangSC-Light;
                line-height: 0.53rem;
                margin-top: 0.32rem;
                letter-spacing: 0;
            }
            .dentistry-home .page-increase-income .item-more .right-box {
                width: 1.01rem
            }
            .dentistry-home .page-increase-income .item-more .right-box > img {
                width: 1.01rem;
                height: 1.01rem;
                margin-top: 0.93rem;
            }
            /* .dentistry-home .page-increase-income .card-wrapper {
                width: 8.72rem;
                height: 5.57rem;
                opacity: 0.6;
                background-image: linear-gradient(87deg, #F9DBA3 31%, rgba(253,240,220,0.00) 100%, rgba(253,240,220,0.00) 100%);
                border-radius: 0.05rem;
                margin: 0px auto;
                position: relative;
                padding-top: 1.48rem;
                box-sizing: border-box;
            }
            .dentistry-home .page-increase-income .card-wrapper > img {
                display: block;
                width: 1.63rem;
                height: 1.63rem;
                border: 0.05rem solid #DDAD6C;
                border-radius: 1.63rem;
                position: absolute;
                top: -0.32rem;
                left: 50%;
                transform: translateX(-50%);
            }
            .dentistry-home .page-increase-income .card-wrapper > .info-wrapper {
                text-align: center;
            }
            .dentistry-home .page-increase-income .card-wrapper > .info-wrapper .name {
                color: #091E42;
                font-size: 0.37rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 0.53rem;
                letter-spacing: 0;
            }
            .dentistry-home .page-increase-income .card-wrapper > .info-wrapper .desc {
                color: #6B778C;
                font-size: 0.32rem;
                font-weight: 300;
                line-height: 0.32rem;
                margin-top: 0.1rem;
                letter-spacing: 0;
            }
            .dentistry-home .page-increase-income .card-wrapper > .content {
                color: #333333;
                font-size: 0.37rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.56rem;
                letter-spacing: 0;
                width: 7.44rem;
                margin: 0.26rem auto 0;
            } */
            .dentistry-home .page-increase-income .box-4 {
                padding: 0.8rem 0px;
                background-color: #ffffff;
            }
            .dentistry-home .page-increase-income .box-4 > .tit {
                color: #000000;
                font-size: 0.48rem;
                font-family: PingFangSC-SNaNremibold;
                font-weight: 600;
                line-height: 0.67rem;
                text-align: center;
            }
            .dentistry-home .page-increase-income .box-4 .firends-more-wrapper {
                padding-top: 0.64rem;
            }

            .dentistry-home .page-increase-income .firends-wrapper {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 0.64rem;
            }
            .dentistry-home .page-increase-income .item-firend {
                width: 2.08rem;
                height: 1.15rem;
                background: #ffffff;
                border: 0.5px solid #DEDEDE;
                border-radius: 0.04rem;
                margin: 0px 0.065rem;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .dentistry-home .page-increase-income .item-firend > img {
                width: 1.5rem;
                height: 0.82rem;
                border: 1px dashed #C3C4C5;
            }
            .dentistry-home .page-increase-income .firends-more-wrapper {
                padding-left: 0.64rem;
                box-sizing: border-box;
                overflow-x: hidden;
            }
            .dentistry-home .page-increase-income .scroll-body {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                flex-wrap: wrap
            }
            .dentistry-home .page-increase-income .item-firends-more {
                width: 2.83rem;
                height: 1.55rem;
                background: #ffffff;
                border: 0.5px solid #DFE3E8;
                box-sizing: border-box;
                margin-right: 0.13rem;
                margin-bottom: 0.13rem;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .dentistry-home .page-increase-income .scroll-body > .item-firends-more:nth-child(3n) {
                margin-right: 0px;
            }
            .dentistry-home .page-increase-income .item-firends-more > img {
                width: 100%;
                height: 100%;
            }
            .dentistry-home .page-footer {
                width: 100%;
                height: 13.98rem;
                padding-top: 1.87rem;
                box-sizing: border-box;
                background-image: url("/static/homeimg/page-footer_mobile_bg.png");
                background-size: 100% auto;
            }
            .dentistry-home .page-footer .title-wrapper {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            .dentistry-home .page-footer .title-wrapper > h5 {
                color: #000;
                font-size: 0.48rem;
                font-weight: 600;
                font-family: PingFangSC-SNaNremibold;
                line-height: 0.67rem;
                text-align: center;
            }
            .dentistry-home .page-footer .title-wrapper > span {
                color: #000;
                font-size: 0.32rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.45rem;
                margin-top: 0.21rem;
                letter-spacing: 0;
                text-align: center;
            }
            .dentistry-home .page-footer .apply-btn {
                color: #fff;
                font-size: 0.43rem;
                font-weight: 500;
                font-family: PingFangSC-Medium;
                line-height: 1.07rem;
                width: 5.33rem;
                height: 1.07rem;
                letter-spacing: 0;
                text-align: center;
                background: #007AFF;
                box-shadow: 0 0.11rem 0.29rem 0 #02419945;
                border-radius: 0.05rem;
                display: block;
                margin: 0.64rem auto 0;
                cursor: pointer;
            }
        }
    </style>
    <!--页脚样式__pc-->
    <style>
        /* pc尺寸 */
        @media screen and (min-width: 800px) {
            .index-footer {
                position: absolute;
                left: 0px;
                right: 0px;
                bottom: 0px;
                width: 100%;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
            }
            .index-footer .certificate {
                color: #000;
                font-size: 12px;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 17px;
                letter-spacing: 0;
            }
            .index-footer .contcat-info {
                width: 810px;
                height: 40px;
                background-image: url("/static/homeimg/<EMAIL>");
                background-size: cover;
                margin: 24px auto 0px;
                position: relative;
            }
            .index-footer .contcat-info .hover-box {
                position: absolute;
                right: 0px;
                top: 0px;
                width: 126px;
                height: 30px;
                cursor: pointer;
            }
            .index-footer .contcat-info .img-qrcode {
                position: absolute;
                top: -200px;
                right: -26px;
                width:176px;
                height:183px;
                box-shadow:0px 2px 6px 0px rgba(0,0,0,0.24);
                border:1px solid #d9dbe3;
                background-color: #fff;
                text-align: center;
                padding: 8px;
                border-radius: 5px;
                display: none;
            }
            .index-footer .contcat-info .img-qrcode:before {
                position: absolute;
                z-index: 1;
                bottom: -7px;
                left: 81px;
                width: 12px;
                height: 12px;
                content: "";
                transform: rotate(225deg);
                border-radius: 0;
                background: #FFFFFF;
                border: 1px solid #DADBE0;
                border-right: 0;
                border-bottom: 0;
            }
            .index-footer .contcat-info .img-qrcode > img {
                width: 160px;
                height: 160px;
            }
            .index-footer .contcat-info .hover-box:hover + .img-qrcode {
                display: block;
            }
            .index-footer .copyright-wrapper {
                height: 16px;
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 24px 0px 74px;
            }
            .index-footer .copyright-wrapper a {
                color: #A0B1C4;
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
            }
            .index-footer .copyright-wrapper span {
                color: #A0B1C4;
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
            }
            .index-footer .copyright-wrapper .ip-licnese {
                margin-left: 16px;
            }
            .index-footer .copyright-wrapper .ip-licnese:hover {
                color: #000000;
            }
            .index-footer .copyright-wrapper .ip-maintain {
                height: 100%;
                display: flex;
                align-items: center;
                margin-left: 16px;
            }
            .index-footer .copyright-wrapper .ip-maintain:hover > span {
                color: #000000;
            }
            .index-footer .copyright-wrapper .ip-maintain > img {
                width: 14px;
                height: 14px;
                margin-right: 4px;
            }
            .index-footer .copyright-wrapper .about-me {
                color: #A0B1C4;
                cursor: pointer;
                margin-left: 16px;
            }
            .index-footer .copyright-wrapper .about-me:hover {
                color: #000000;
            }
        }
    </style>
    <!--页脚样式__mobile-->
    <style>
        @media screen and (max-width: 800px) {
            .index-footer {
                position: absolute;
                left: 0;
                right: 0;
                bottom: 1.92rem;
                width: 100%;
                padding-bottom: 1.34rem;
            }
            .index-footer .contcat-info .item-contcat {
                height: 0.48rem;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 0.11rem;
            }
            .index-footer .contcat-info .item-contcat.tel > img {
                width: 0.48rem;
                height: 0.48rem;
                margin-right: 0.21rem;
            }
            .index-footer .contcat-info .item-contcat.qq > img {
                width: 0.43rem;
                height: 0.51rem;
                margin-right: 0.21rem;
            }
            .index-footer .contcat-info .item-contcat.weixin > img {
                width: 0.51rem;
                height: 0.43rem;
                margin-right: 0.21rem;
            }
            .index-footer .contcat-info .item-contcat > span {
                color: #AAABB3;
                font-size: 0.32rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.32rem;
                letter-spacing: 0;
            }
            .index-footer .copyright-wrapper {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin-top: 0.64rem;
            }
            .index-footer .copyright-wrapper a {
                color: #A0B1C4;
                font-size: 0.32rem;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                line-height: 0.45rem;
                margin-top: 0.21rem;
                letter-spacing: 0;
                text-align: center;
                margin-top: 0.21rem;
            }
            .index-footer .copyright-wrapper span {
                color: #A0B1C4;
                font-size: 0.32rem;
                font-family: PingFangSC-Regular;
                font-weight: 400;
                line-height: 0.45rem;
                letter-spacing: 0;
                text-align: center;
            }
            .index-footer .copyright-wrapper .ip-licnese {
                margin-left: 16px;
            }
            .index-footer .copyright-wrapper .ip-maintain {
                height: 100%;
                display: flex;
                align-items: center;
                margin-left: 16px;
            }
            .index-footer .copyright-wrapper .ip-maintain > img {
                width: 0.45rem;
                height: 0.45rem;
                margin-right: 0.1rem;
            }
            .index-footer .copyright-wrapper .about-me {
                color: #A0B1C4;
                cursor: pointer;
                margin-left: 0.21rem;
            }
            .index-footer .copyright-wrapper .about-me:hover {
                color: #000000;
            }
        }
    </style>
    <!--腾讯企点__pc-->
    <style>
        @media screen and (min-width: 800px) {
            .online-communicate-wrapper {
                position: fixed;
                right: 24px;
                bottom: 84px;
                width: 68px;
                text-align: center;
                cursor: pointer;
                outline-style: none;
                transition: all 0.25s ease-out;
                z-index: 3000;
            }
            .online-communicate-wrapper .online-communicate-item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            .online-communicate-wrapper .online-communicate-item > img {
                width: 20px;
                height: 20px;
            }
            .online-communicate-wrapper .online-communicate-item > p {
                color: #007AFF;
                font-size: 12px;
                font-weight: 500;
                line-height: 17px;
                margin-top: 6px;
            }
            .online-communicate-wrapper .btn-group {
                width: 68px;
                height: 136px;
                background-image: linear-gradient(180deg, #E3EAF9 0%, #FFFFFF 86%);
                border: 2px solid #FFFFFF;
                box-shadow: 0 2px 6px 0 #C6D0E1;
                border-radius: 6px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
            }
            .online-communicate-wrapper .btn-group:hover {
                box-shadow: 0 2px 12px 0 #C6D0E1
            }
            .online-communicate-wrapper .btn-group .online-communicate-item {
                width: 100%;
                height: 50%;
            }
            .online-communicate-wrapper .phone-call {
                width: 130px;
                height: 68px;
                background-image: linear-gradient(180deg, #E3EAF9 0%, #FFFFFF 86%);
                border: 2px solid #FFFFFF;
                box-shadow: 0 2px 12px 0 #EEF0F5;
                border-radius: 6px;
                position: absolute;
                right: 72px;
                top: 68px;
                display: flex;
                justify-content: center;
                align-items: center;
                visibility: hidden;
                opacity: 0;
                transform-origin: center;
                -webkit-transition: all 0.3s ease-in;
                -moz-transition: all 0.3s ease-in;
                -o-transition: all 0.3s ease-in;
                -ms-transition: all 0.3s ease-in;
                transition: all 0.3s ease-in;
            }
            .online-communicate-wrapper .phone-call > span {
                color: #007AFF;
                font-size: 14px;
                font-weight: 500;
                line-height: 20px;
            }
            .online-communicate-wrapper .online-communicate-item.phone:hover .phone-call {
                visibility: visible;
                opacity: 1;
            }
            .online-communicate-wrapper .line {
                width: 48px;
                height: 1px;
                background: rgba(0,122,255,0.12);
            }
            .online-communicate-wrapper .btn-try {
                width: 68px;
                height: 68px;
                background: #007AFF;
                box-shadow: 0 4px 11px 0 #02419945;
                border-radius: 6px;
                margin-top: 12px;
            }
            .online-communicate-wrapper .btn-try > img {
                width: 29px;
                height: 22px;
            }
            .online-communicate-wrapper .btn-try > p {
                color: #fff;
            }
        }
    </style>
    <!--腾讯企点__mobile-->
    <style>
        @media screen and (max-width: 800px) {
            .online-communicate-wrapper {
                position: fixed;
                left: 0;
                right: 0;
                bottom: 0;
                height: 1.92rem;
                padding: 0px 0.32rem;
                padding-bottom: constant(safe-area-inset-bottom);
                padding-bottom: env(safe-area-inset-bottom);
                box-sizing: content-box;
                background: #FFFFFF;
                box-shadow: inset 0 0.03rem 0 0 #E6EAEE;
                display: flex;
                justify-content: space-between;
                align-items: center;
                transition: all 0.25s ease-out;
                z-index: 3000;
            }
            .online-communicate-wrapper .online-communicate-item {
                width: 1.485rem;
                height: 1.15rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            .online-communicate-wrapper .online-communicate-item > img {
                width: 0.53rem;
                height: 0.53rem;
            }
            .online-communicate-wrapper .online-communicate-item > p {
                color: #333333;
                font-size: 0.29rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                line-height: 0.43rem;
                margin-top: 0.19rem;
                letter-spacing: 0;
            }
            .online-communicate-wrapper .btn-pay {
                width: 2.88rem;
                height: 1.28rem;
                background: #ffffff;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #007AFF;
                font-size: 0.43rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                letter-spacing: 0;
                text-align: center;
                line-height: 0.64rem;
                border-radius: 0.11rem;
                border: 0.03rem solid #007AFF;;
            }
            .online-communicate-wrapper .btn-try {
                width: 2.88rem;
                height: 1.28rem;
                background: #007AFF;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #ffffff;
                font-size: 0.43rem;
                font-weight: 400;
                font-family: PingFangSC-Regular;
                letter-spacing: 0;
                text-align: center;
                line-height: 0.64rem;
                border-radius: 0.11rem;
            }
        }
    </style>
    <!--价格__pc-->
    <style>
        @media screen and (min-width: 800px) {
            #win-price {
                position: fixed;
                top: 0px;
                left: 0px;
                width: 100vw;
                height: 100vh;
                z-index: 999999999999;
                background-color: #ffffff;
                display: none;
            }
            #win-price.is-opened {
                display: block;
            }
            #win-price .body-wrapper {
                position: relative;
                width: 100%;
                height: 100%;
                background: #FFFFFF;
                overflow: hidden;
            }
            #win-price .loading-wrapper {
                position: absolute;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                z-index: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #FFFFFF;
            }
            #win-price .loading-wrapper.is-opened {
                z-index: 3;
            }
            #win-price .loading-wrapper img {
                width: 24px;
                height: 24px;
                animation: loading-rotate 1s linear infinite;
            }
            #win-price iframe {
                position: absolute;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                width: 100%;
                height: 100%;
                z-index: 2;
            }
        }
    </style>
    <!--价格__mibile-->
    <style>
        @media screen and (max-width: 800px) {
            #win-price {
                position: fixed;
                top: 0px;
                left: 0px;
                width: 100vw;
                height: 100vh;
                z-index: 999999999999;
                background-color: #ffffff;
                display: none;
            }
            #win-price.is-opened {
                display: block;
            }
            #win-price .body-wrapper {
                position: relative;
                width: 100%;
                height: 100%;
                background: #FFFFFF;
                overflow: hidden;
            }
            #win-price .loading-wrapper {
                position: absolute;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                z-index: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #FFFFFF;
            }
            #win-price .loading-wrapper.is-opened {
                z-index: 3;
            }
            #win-price .loading-wrapper img {
                width: 0.64rem;
                height: 0.64rem;
                animation: loading-rotate 1s linear infinite;
            }
            #win-price iframe {
                position: absolute;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                width: 100%;
                height: 100%;
                z-index: 2;
            }
        }
    </style>
    <!--申请试用__pc-->
    <style>
        @media screen and (min-width: 800px) {
            #win-apply {
                position: fixed;
                top: 0px;
                left: 0px;
                width: 100vw;
                height: 100vh;
                background: #00000066;
                z-index: 999999999999;
                display: none;
            }
            #win-apply.is-opened {
                display: block;
            }
            #win-apply .body-wrapper {
                position: relative;
                width: 853px;
                height: 558px;
                background: #FFFFFF;
                box-shadow: 0 10px 34px 0 #00000012;
                border-radius: var(--abc-border-radius-small);
                overflow: hidden;
                margin: 0px auto;
                margin-top: calc((100vh - 558px)*0.4);
            }
            #win-apply .loading-wrapper {
                position: absolute;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                z-index: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #FFFFFF;
            }
            #win-apply .loading-wrapper.is-opened {
                z-index: 3;
            }
            #win-apply .loading-wrapper img {
                width: 24px;
                height: 24px;
                animation: loading-rotate 1s linear infinite;
            }
            #win-apply iframe {
                position: absolute;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                width: 100%;
                height: 100%;
                z-index: 2;
            }
        }
    </style>
    <!--申请试用__mobile-->
    <style>
        @media screen and (max-width: 800px) {
            #win-apply {
                position: fixed;
                top: 0px;
                left: 0px;
                width: 100vw;
                height: 100vh;
                background: #00000066;
                z-index: 999999999999;
                display: none;
            }
            #win-apply.is-opened {
                display: block;
            }
            #win-apply .body-wrapper {
                position: relative;
                width: 100%;
                height: 100%;
                background: #FFFFFF;
                overflow: hidden;
            }
            #win-apply .loading-wrapper {
                position: absolute;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                z-index: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                background: #FFFFFF;
            }
            #win-apply .body-wrapper.is-opened {
                z-index: 3;
            }
            #win-apply .loading-wrapper img {
                width: 0.64rem;
                height: 0.64rem;
                animation: loading-rotate 1s linear infinite;
            }
            #win-apply iframe {
                position: absolute;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                width: 100%;
                height: 100%;
                z-index: 2;
                overflow: hidden;
            }
        }
    </style>
</head>
<body>
    <div class="index-wrapper">
        <div class="nav-wrapper" id="nav">
            <div class="left-wrapper pc">
                <div class="nav-logo" onclick="toSuper()"></div>
            </div>
            <div class="content-wrapper">
                <a class="clinic" onclick="onClickNav('#clinic')">诊所管家</a>
                <a class="dentistry" onclick="onClickNav('#dentistry')">口腔管家</a>
            </div>
            <div class="right-wrapper pc">
                <button onclick="onClickApply()">免费试用</button>
                <button class="blank" onclick="onClickLogin()">登录诊所</button>
            </div>
        </div>
        <div class="scroll-body">
            <div class="clinic-home">
                <div class="mock-bg"></div>
                <div class="page-index pc">
                    <div class="left-wrapper">
                        <h5 class="title mobile">诊所管家</h5>
                        <p class="desc">懂医懂药懂经营的智慧诊所管家</p>
                        <p class="flag">专业 · 全能 · 增收</p>
                        <div class="btn-wrapper">
                            <button class="btn-apply" onclick="onClickApply()">免费试用</button>
                            <button class="btn-price" onclick="onClickPrice()">价格</button>
                        </div>
                    </div>
                    <div class="right-wrapper"></div>
                </div>
                <div class="page-index mobile">
                    <div class="left-wrapper">
                        <h5 class="logo"></h5>
                        <p class="desc-1">懂医懂药懂经营的</p>
                        <p class="desc-2">智慧诊所管家</p>
                    </div>
                </div>

                <div class="page-feature">
                    <div class="item-feature">
                        <img src="/static/homeimg/<EMAIL>" alt="">
                        <div class="info-wrapper">
                            <div class="tit" style="color:#05B387">全面</div>
                            <div class="des">
                                <p>西医中医全科流程完备流畅</p>
                                <p>个体/连锁/社区医院轻松管理</p>
                                <p>精通诊断用药又懂得决策经营</p>
                            </div>
                        </div>
                    </div>
                    <div class="item-feature">
                        <img src="/static/homeimg/<EMAIL>" alt="">
                        <div class="info-wrapper">
                            <div class="tit" style="color:#116DFF">智能</div>
                            <div class="des">
                                <p>海量医药知识图谱和决策网络</p>
                                <p>智能辅助循证诊断准确率90%</p>
                                <p>精准用药审核减少80%风险</p>
                            </div>
                        </div>
                    </div>
                    <div class="item-feature">
                        <img src="/static/homeimg/<EMAIL>" alt="">
                        <div class="info-wrapper">
                            <h5 class="tit" style="color:#FA6400">高效</h5>
                            <div class="des">
                                <p>AI 提升诊所医疗水平服务质量</p>
                                <p>营销聚焦诊所流量和收入增长</p>
                                <p>CRM帮助诊所获得患者信任认同</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page-process">
                    <h3>诊所全流程管理</h3>
                    <div class="process-wrapper">
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">预约挂号</div>
                                <div class="desc">微信、电话预约，现场挂号叫号。患者方便快捷，诊所资源调配轻松有序</div>
                            </div>
                        </div>
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">电子病历</div>
                                <div class="desc">免输入快捷录入，AI 模型全程辅助诊断。病历永久安全保存，语音、照片、检验报告多维度呈现</div>
                            </div>
                        </div>
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">电子处方</div>
                                <div class="desc">内置几百种常见处方模板快速调取。AI 医疗大脑审核医生处方用药安全性，极大降低诊疗风险</div>
                            </div>
                        </div>
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">药房管理</div>
                                <div class="desc">药品进销存管理、全局动态库存预警、连锁间配送入库和调拨、药品销售统计分析</div>
                            </div>
                        </div>
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">患者和会员</div>
                                <div class="desc">“以患者为中心”的设计思路，帮助诊所搭建对患者诊前、诊中、诊后的全方位服务。支持多种会员设定和优惠促销工具</div>
                            </div>
                        </div>
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">经营管理</div>
                                <div class="desc">强大的数据沉淀、查询、分析功能，多维度的经营统计报表，多级管理者视图一目了然</div>
                            </div>
                        </div>
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">检查和检验</div>
                                <div class="desc">支持主流诊所检验设备的数据采集，检验报告自动和患者病历连接。支持更多第三方检验中心的接入</div>
                            </div>
                        </div>
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">微信营销</div>
                                <div class="desc">一键搭建诊所自己的微信公众号、小程序，简单丰富的流量营销工具，明显提升诊所获客、留存、客单</div>
                            </div>
                        </div>
                        <div class="item-wrapper">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                            <div class="info-wrapper">
                                <div class="title">医疗知识库</div>
                                <div class="desc">超过50000种药品说明书，3000份权威用药指南，6000份临床路径资料，方便医生随时查阅参考</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page-adaptation">
                    <h3>多端适配</h3>
                    <div class="adaptation-content">多种屏幕尺寸适配，满足不同应用场景！</div>
                    <div class="adaptation-img-wrapper">
                        <img class="adaptation-img" src="/static/homeimg/<EMAIL>" alt="" />
                    </div>
                </div>

                <div class="page-questions">
                    <h3>常见问题</h3>
                    <div class="question-content">
                        <div class="issue">
                            <div class="issue-title">
                                <img src="/static/homeimg/<EMAIL>" alt="">
                                <span class="issue-name">ABC诊所管家对哪些诊所适用呢？</span>
                                <div class="close-box mobile">
                                    <img src="/static/homeimg/Icon_cross_mobile.png" alt="">
                                </div>
                            </div>
                            <div class="issue-content">
                                <div class="content">
                                    从设计之初我们就考虑了不同类型和规模的诊所情况，研发上也做了细致架构。目前ABC诊所管家适用于全科、儿科、妇科、西医、中医等多类型诊所机构，同时也适用于中小个体诊所、大型诊所、连锁诊所、社区医院等多种规模形态的医疗机构。体验简单快捷、功能丰富强大。
                                </div>
                            </div>
                        </div>
                        <div class="issue">
                            <div class="issue-title">
                                <img src="/static/homeimg/<EMAIL>" alt="">
                                <span class="issue-name">这个产品对我诊所里哪些角色适用呢？</span>
                                <div class="close-box mobile">
                                    <img src="/static/homeimg/Icon_cross_mobile.png" alt="">
                                </div>
                            </div>
                            <div class="issue-content">
                                <div class="content ">
                                    ABC诊所管家提供基层医疗机构所需的全业务流程支持，适用于所有角色：患者预约挂号；医生书写病历处方；护士执行医嘱；检验人员安排执行化验；药房人员管理药品采购、出入和调拨；管理员或老板关注日常经营情况、查看经营报表、制定和调度经营计划。<br>
                                    除了医疗服务的全角色支持，系统还提供了强大的营销支持，可以更轻松地制作营销物料、策划营销活动，更有效拓展新增客流和回购客流，是营销人员的强力工具。
                                    <div class="ellipsis pc">...</div>
                                    <div class="more pc">更多</div>
                                </div>
                            </div>
                        </div>
                        <div class="issue">
                            <div class="issue-title">
                                <img src="/static/homeimg/<EMAIL>" alt="">
                                <span class="issue-name">ABC诊所管家安装方便吗？</span>
                                <div class="close-box mobile">
                                    <img src="/static/homeimg/Icon_cross_mobile.png" alt="">
                                </div>
                            </div>
                            <div class="issue-content">
                                <div class="content">
                                    无需安装，极其方便。ABC诊所管家是基于云技术研发的全新一代医疗SaaS产品，不需要像传统系统一样花巨资购买昂贵的服务器，不需要聘请技术人员专职维护，也不需要花很长时间安装调试，诊所用户只需要完成在线注册即可快速开通使用。
                                </div>
                            </div>
                        </div>
                        <div class="issue">
                            <div class="issue-title">
                                <img src="/static/homeimg/<EMAIL>" alt="">
                                <span class="issue-name">我们的数据安全吗？</span>
                                <div class="close-box mobile">
                                    <img src="/static/homeimg/Icon_cross_mobile.png" alt="">
                                </div>
                            </div>
                            <div class="issue-content">
                                <div class="content">
                                    客户的经营数据、患者的医疗数据，是我们审慎珍视的核心用户数据。ABC诊所管家非同常规地同时使用了阿里云和腾讯云两套高安全等级的云数据服务器，“双保险”绝对保障用户数据安全。同时，我们7X24小时全年不间断实时监控运维，确保系统稳定性。
                                </div>
                            </div>
                        </div>
                        <div class="issue">
                            <div class="issue-title">
                                <img src="/static/homeimg/<EMAIL>" alt="">
                                <span class="issue-name">系统里的AI辅助诊断和AI用药审核，可信度高吗？</span>
                                <div class="close-box mobile">
                                    <img src="/static/homeimg/Icon_cross_mobile.png" alt="">
                                </div>
                            </div>
                            <div class="issue-content">
                                <div class="content AI-content">
                                    我们的AI技术团队来自腾讯、百度、微软等大型互联网公司的核心团队，每个人都从事多年人工智能、云计算、大数据开发等前沿技术领域的研究开发。<br>
                                    但，人工智能技术，特别是医疗人工智能，在可见的未来都更倾向扮演辅助人类医生的角色，它并不能完全代替人类医生做决定。我们更希望通过AI技术，显著提升基层医生的医疗水平，帮助基层医疗机构建立更广泛的患者信任。<br>
                                    <div class="ellipsis pc">...</div>
                                    <div class="more pc">更多</div>
                                </div>
                            </div>
                        </div>
                        <div class="issue">
                            <div class="issue-title">
                                <img src="/static/homeimg/<EMAIL>" alt="">
                                <span class="issue-name">系统会持续更新吗？如果我有新需求怎么支持呢？</span>
                                <div class="close-box mobile">
                                    <img src="/static/homeimg/Icon_cross_mobile.png" alt="">
                                </div>
                            </div>
                            <div class="issue-content">
                                <div class="content">
                                    不同于传统软件一次性安装的方式，ABC诊所管家是部署在云端的业务系统，我们会充分发挥云计算的优势，使用户随时地、实时地、无缝地获得产品的更新迭代。通过各种反馈通道，我们收集用户声音和用户新需求，快速响应，确保用户问题的即时解决，也帮助用户真正利用互联网技术提升经营。
                                </div>
                            </div>
                        </div>
                        <div class="issue">
                            <div class="issue-title">
                                <img src="/static/homeimg/<EMAIL>" alt="">
                                <span class="issue-name">使用系统前必须为每个角色配备电脑吗？</span>
                                <div class="close-box mobile">
                                    <img src="/static/homeimg/Icon_cross_mobile.png" alt="">
                                </div>
                            </div>
                            <div class="issue-content">
                                <div class="content">
                                    ABC诊所管家提供网页和应用程序版本，可以随时通过电脑上的浏览器进入系统，也可以安装应用程序更方便地使用。除此之外，我们也开发了所有移动终端的产品版本，包括安卓手机、iPhone手机、iPad平板，甚至还有基于微信的小程序版本。不管是医生、药剂师、收费人员，还是财务人员、管理团队、老板们，都可以以自己方便的形式使用产品。
                                </div>
                            </div>
                        </div>
                        <div class="issue">
                            <div class="issue-title">
                                <img src="/static/homeimg/<EMAIL>" alt="">
                                <span class="issue-name">这套系统怎么收费呢？</span>
                                <div class="close-box mobile">
                                    <img src="/static/homeimg/Icon_cross_mobile.png" alt="">
                                </div>
                            </div>
                            <div class="issue-content">
                                <div class="content">
                                    针对不同的诊所规模和业务需求，我们会推出适合诊所当前状况的阶梯服务价格，一方面确保用户在服务选购上的灵活性，一方面尽可能节省用户在业务系统上的支出。我们收取的年费基本都用于客户数据的云端存储成本和计算力成本。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="page-footer">
                    <button class="apply-btn" onclick="onClickApply()">免费试用</button>
                    <div class="tit-wrapper">
                        <img src="/static/homeimg/<EMAIL>" alt="">
                        <span>开诊所，用ABC诊所管家</span>
                    </div>
                </div>
            </div>

            <div class="dentistry-home">
                <div class="mock-bg"></div>
                <div class="page-index">
                    <div class="left-wrapper">
                        <h5 class="title mobile">口腔管家</h5>
                        <p class="desc">全新一代口腔诊所数智化管家</p>
                        <p class="flag">专业 · 全能 · 增收</p>
                        <div class="btn-wrapper pc">
                            <button class="btn-apply" onclick="onClickApply()">免费试用</button>
                            <button class="btn-price" onclick="onClickPrice()">价格</button>
                        </div>
                    </div>
                    <div class="right-wrapper pc"></div>
                </div>
                <div class="page-major">
                    <h5 class="title">为口腔量身定制</h5>
                    <div class="cell-template cell-1">
                        <div class="tit">预约情况一目了然</div>
                        <div class="des">自动根据预约项目确定占用时间，鼠标拖拽快速调整</div>
                        <div class="img"></div>
                    </div>
                    <div class="content">
                        <div class="cell-group group-1">
                            <div class="cell-template cell-2">
                                <div class="tit">一分钟写出专业病历</div>
                                <div class="des">智能辅助面板告别手写病历，丰富模版快速调用</div>
                                <div class="img"></div>
                            </div>
                            <div class="cell-template cell-3">
                                <div class="tit">随时随地都可用</div>
                                <div class="des">电脑、手机、Pad多端操作，随身的移动互联网诊所</div>
                                <div class="img"></div>
                            </div>
                        </div>
                        <div class="cell-group group-2">
                            <div class="cell-template cell-4">
                                <div class="tit">软硬一体无缝连接</div>
                                <div class="des">无缝串联诊疗过程，智能识别影像自动存入顾客档案</div>
                                <div class="img"></div>
                            </div>
                            <div class="cell-template cell-5">
                                <div class="tit">阅片体验从未如此轻松</div>
                                <div class="des">全屏查看顾客影像，多图清晰对比展示治疗前后效果</div>
                                <div class="img"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page-almighty">
                    <h5 class="title">全业务全流程数智化</h5>
                    <div class="cell-template cell-1">
                        <div class="tit">业务全线升级</div>
                        <div class="des">诊前、诊中、诊后全面数智化流程，轻松串联各角色的工作</div>
                        <div class="img mobile"></div>
                    </div>
                    <div class="cell-template cell-2 align-right">
                        <div class="tit">绩效轻松管理</div>
                        <div class="des">开单业绩、复诊率、转诊情况、患者评价，多维度数据统计分析</div>
                        <div class="img mobile"></div>
                    </div>
                    <div class="cell-template cell-3">
                        <div class="tit">营收实时掌握</div>
                        <div class="des">管理者实时查看营收状况，自动生成营收日报，收入、趋势、规模、构成一目了然</div>
                        <div class="img mobile"></div>
                    </div>
                    <div class="cell-template cell-4 align-right">
                        <div class="tit">全面支持国家新医保</div>
                        <div class="des">全程助力30+省级医保平台接入，自动贯标/智能对账/一键清算</div>
                        <div class="img mobile"></div>
                    </div>
                    <div class="cell-template cell-5">
                        <div class="tit">医疗聚合支付新体验</div>
                        <div class="des">所有支付方式一个盒子搞定，费率低到账快，央行授权安全可信</div>
                        <div class="img mobile"></div>
                    </div>
                </div>
                <div class="page-referral">
                    <h5 class="title">全方位提升复购和转介绍</h5>
                    <div class="cell-group">
                        <div class="cell-template cell-1">
                            <div class="tit">把诊所开到微信上</div>
                            <div class="des">预约到店、在线咨询、顾客都集中到机构的私域中反复触达</div>
                            <div class="img"></div>
                        </div>
                        <div class="cell-template cell-2 align-right">
                            <div class="tit">自助预约，就诊提醒</div>
                            <div class="des">顾客微信预约医生、项目，就诊前自动发送就诊提醒，引导到店就诊</div>
                            <div class="img"></div>
                        </div>
                    </div>
                    <div class="cell-group">
                        <div class="cell-template cell-3">
                            <div class="tit">到店治疗，“躺”着就够了</div>
                            <div class="des">扫码签到等候叫号；看检查、听方案、手机缴费、医生治疗，躺着就够了</div>
                            <div class="img"></div>
                        </div>
                        <div class="cell-template cell-4 align-right">
                            <div class="tit">离店后，服务还在继续</div>
                            <div class="des">就诊报告、复诊预约、自动随访，全程贴心服务，让你的诊所成为顾客的不二选择</div>
                            <div class="img"></div>
                        </div>
                    </div>
                    <div class="card-box">
                        <div class="tit">丰富强大的运营工具</div>
                        <div class="item-group">
                            <div class="item-wrapper item-1">
                                <div class="img pc"></div>
                                <div class="tit">自动随访</div>
                                <div class="des">每日就诊顾客自动创建日常诊后随访；根据不同治疗项目，自动创建针对性周期随访</div>
                                <div class="img mobile"></div>
                            </div>
                            <div class="item-wrapper item-2">
                                <div class="img pc"></div>
                                <div class="tit">营销工具</div>
                                <div class="des">会员/卡项/积分/折扣/满减/满赠/优惠券等丰富的营销工具，提升付费转化、客单和复购</div>
                                <div class="img mobile"></div>
                            </div>
                            <div class="item-wrapper item-3">
                                <div class="img pc"></div>
                                <div class="tit">服务评价</div>
                                <div class="des">自动给顾客微信推送服务评价邀请，对医生、服务过程做出评价，为诊疗服务优化提供依据</div>
                                <div class="img mobile"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page-increase-income">
                    <div class="box-1">
                        <h5 class="title">营收翻倍-只要一套口腔专属SCRM</h5>
                        <div class="desc-wrapper">
                            <div class="tit">ABC企微管家</div>
                            <div class="des">医疗SCRM</div>
                            <div class="tit pc">获客 · 留客 · 成交 · 复购</div>
                        </div>
                        <div class="tit mobile">获客 · 留客 · 成交 · 复购</div>
                        <div class="img"></div>
                    </div>
                    <div class="box-2">
                        <p class="tit">不止这些，你可能还关心</p>
                        <div class="feat-wrapper">
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>便捷预约</span>
                                </div>
                                <p>微信/电话/现场等多渠道快速预约；一键预约下次复诊；科室转诊快捷预约；一目了然的预约看板</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>电子病历</span>
                                </div>
                                <p>免输入写病历、开处置；图形化智能牙周牙位图；丰富病历模板一键使用</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>影像同步</span>
                                </div>
                                <p>设备影像一键同步；智能识别影像自动存入患者档案；影像档案永久安全保存</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>影像查阅</span>
                                </div>
                                <p>全屏阅片、影像标注；多图对比，清晰展示治疗前后对比效果</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>科室转诊</span>
                                </div>
                                <p>顾客新需求转诊其他科室医生，不错失销售机会；统计分析医生转诊率和成交率，促进综合客单</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>聚合支付</span>
                                </div>
                                <p>所有支付方式（含医保电子凭证），一个扫码盒子就够了；费率更低隔天到账；央行授权安全可信</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>欠费管理</span>
                                </div>
                                <p>所有欠费一目了然；多笔欠费批量还款；细粒度划款统计分析，避免坏账</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>外送加工</span>
                                </div>
                                <p>一键导入加工价目表，批量创建加工项目；开单-送件-回件-试戴全流程支持；自动生成加工结算统计</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>智能库管</span>
                                </div>
                                <p>库存智能预警提醒补货，智能推荐补货量，定制采购计划；扫码入库简单快捷</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>医保支付</span>
                                </div>
                                <p>所有省份医保接入；自动贯标对码，结算、对账、清算，电子凭证，限价提醒，核拨/控费统计，全套解决方案</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>统计分析</span>
                                </div>
                                <p>多维度经营统计分析，准确把握经营关键数据：收入、客流、患者、医务等，既有宏观数据，又有细节对比</p>
                            </div>
                            <div class="item-feat">
                                <div class="title-wrapper">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                    <span>连锁经营</span>
                                </div>
                                <p>总部统一会员、项目、商品、营销等管控策略；支持直营、加盟等连锁模式；连锁内跨机构协作业务开展</p>
                            </div>
                        </div>
                    </div>
                    <div class="box-3">
                        <p class="tit">不止软件，全方位服务为诊所赋能</p>
                        <div class="more-wrapper">
                            <div class="item-more">
                                <div class="left-box">
                                    <div class="title">数据迁移</div>
                                    <div class="content">协助迁移数据，确保无缝切换，数据不丢失</div>
                                </div>
                                <div class="right-box">
                                    <img src="/static/homeimg/<EMAIL>" alt=""/>
                                </div>
                            </div>
                            <div class="item-more">
                                <div class="left-box">
                                    <div class="title">实施培训</div>
                                    <div class="content">为各角色提供详细培训辅导诊所快速启动</div>
                                </div>
                                <div class="right-box">
                                    <img src="/static/homeimg/<EMAIL>" alt=""/>
                                </div>
                            </div>
                            <div class="item-more">
                                <div class="left-box">
                                    <div class="title">技术支持</div>
                                    <div class="content">专属通道，为诊所提供随时在线的保障服务</div>
                                </div>
                                <div class="right-box">
                                    <img src="/static/homeimg/<EMAIL>" alt=""/>
                                </div>
                            </div>
                            <div class="item-more">
                                <div class="left-box">
                                    <div class="title">定期培训</div>
                                    <div class="content">定期开展线上培训，讲解系统和优秀客户案例</div>
                                </div>
                                <div class="right-box">
                                    <img src="/static/homeimg/<EMAIL>" alt=""/>
                                </div>
                            </div>
                            <div class="item-more">
                                <div class="left-box">
                                    <div class="title">免费升级</div>
                                    <div class="content">持续免费升级，紧跟行业趋势</div>
                                </div>
                                <div class="right-box">
                                    <img src="/static/homeimg/<EMAIL>" alt=""/>
                                </div>
                            </div>
                            <div class="item-more">
                                <div class="left-box">
                                    <div class="title">硬件支持</div>
                                    <div class="content">影像设备、叫号大屏、收款盒子等无缝对接</div>
                                </div>
                                <div class="right-box">
                                    <img src="/static/homeimg/<EMAIL>" alt=""/>
                                </div>
                            </div>
                            <div class="item-more">
                                <div class="left-box">
                                    <div class="title">最佳实践</div>
                                    <div class="content">头部客户共同打造，用行业最佳实践优化业务</div>
                                </div>
                                <div class="right-box">
                                    <img src="/static/homeimg/<EMAIL>" alt=""/>
                                </div>
                            </div>
                            <div class="item-more">
                                <div class="left-box">
                                    <div class="title">数据安全</div>
                                    <div class="content">公安部三级等保认证，阿里腾讯双云护航</div>
                                </div>
                                <div class="right-box">
                                    <img src="/static/homeimg/<EMAIL>" alt=""/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--东哥说暂且不上-->
                    <!-- <p class="desc desc-more">他们都在用ABC口腔管家</p>
                    <div class="card-wrapper">
                        <img src="" alt="">
                        <div class="info-wrapper">
                            <div class="name">陈志强</div>
                            <div class="desc">秉正堂创始人</div>
                        </div>
                        <span class="yinghao">“</span>
                        <div class="content">我赌100000万ABC一定会上市！如果ABC高层看到这条留言，请给我一个机会，我愿意免费义务为你们提供所有的临床短板。赣信仁济堂中医馆负责人立誓！</div>
                    </div>
                    <div class="firends-wrapper">
                        <div class="item-firend active">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                        </div>
                        <div class="item-firend">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                        </div>
                        <div class="item-firend">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                        </div>
                        <div class="item-firend">
                            <img src="/static/homeimg/<EMAIL>" alt="">
                        </div>
                    </div> -->
                    <div class="box-4">
                        <p class="tit">12000+口腔共同的选择</p>
                        <div class="firends-more-wrapper">
                            <div class="scroll-body">
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <!-- <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div>
                                <div class="item-firends-more">
                                    <img src="/static/homeimg/<EMAIL>" alt="">
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page-footer">
                    <div class="title-wrapper">
                        <h5>ABC口腔管家</h5>
                        <span>专业 · 全能 · 增收</span>
                    </div>
                    <button class="apply-btn" onclick="onClickApply()">免费试用</button>
                </div>
            </div>

            <div class="index-footer">
                <div class="certificate pc">公安部信息系统等级保护三级证书 | 增值电信业务经营许可证 | 互联网药品信息服务资格证书 | 第二类医疗器械经营备案凭证｜网络食品交易主体备案｜知识产权证书</div>
                <div class="contcat-info pc">
                    <div class="hover-box"></div>
                    <div class="img-qrcode">
                        <img src="/static/homeimg/<EMAIL>" alt=""/>
                    </div>
                </div>
                <div class="contcat-info mobile">
                    <div class="item-contcat tel">
                        <img src="/static/homeimg/<EMAIL>" alt="">
                        <span>************ 转 1 (销售咨询) 转 2 (客服咨询)</span>
                    </div>
                    <div class="item-contcat qq">
                        <img src="/static/homeimg/<EMAIL>" alt="" />
                        <span>（4001751775客服QQ）</span>
                    </div>
                    <div class="item-contcat weixin">
                        <img src="/static/homeimg/<EMAIL>" alt="">
                        <span>ABC数字医疗云（微信公众号）</span>
                    </div>
                </div>
                <div class="copyright-wrapper">
                    <span>Copyright &#169;2025 成都字节流科技有限公司. All Rights Reserved</span>
                    <a class="ip-licnese" href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">蜀ICP备17030180号-2</a>
                    <a class="ip-maintain" target="_blank" href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=51019002002612">
                        <img src="/static/homeimg/icon_icp.png" />
                        <span>川公网安备 51019002002612号</span>
                    </a>
                    <a class="about-me" onclick="onClickAbout()">关于我们</a>
                </div>
            </div>
        </div>
    </div>

    <div class="online-communicate-wrapper pc">
        <div class="btn-group">
            <div class="consultation online-communicate-item online-service-point1">
                <img src="/static/homeimg/icon_consultant.png" alt="">
                <p>在线咨询</p>
            </div>
            <div class="line"></div>
            <a class="phone online-communicate-item" href="tel:15680016097">
                <img src="/static/homeimg/icon_phone_call.png" alt="">
                <p>电话咨询</p>
                <div class="phone-call">
                    <span>156 8001 6097</span>
                </div>
            </a>
        </div>
        <div class="btn-try online-communicate-item" onclick="onClickApply()">
            <img src="/static/homeimg/try_out.png" alt="">
            <p>免费试用</p>
        </div>
    </div>

    <div class="online-communicate-wrapper mobile">
        <div class="consultation online-communicate-item online-service-point2">
            <img src="/static/homeimg/<EMAIL>" alt="">
            <p>在线咨询</p>
        </div>
        <a class="phone online-communicate-item" href="tel:15680016097">
            <img src="/static/homeimg/<EMAIL>" alt="">
            <p>电话咨询</p>
        </a>
        <button class="btn-pay" onclick="onClickPriceMobile()">价格</button>
        <button class="btn-try" onclick="onClickApplyMobile()">免费试用</button>
    </div>

    <div id="win-price">
        <div class="body-wrapper">
            <div class="loading-wrapper">
                <img src="/static/homeimg/<EMAIL>" alt="">
            </div>
            <iframe frameborder="0"></iframe>
        </div>
    </div>

    <div id="win-apply">
        <div class="body-wrapper">
            <div class="loading-wrapper">
                <img src="/static/homeimg/<EMAIL>" alt="">
            </div>
            <iframe frameborder="0"></iframe>
        </div>
    </div>
</body>

<script>
    let type = ''
    /**
     * 处理页面滑动到顶部
     * <AUTHOR>
     * @date 2023-02-09
     */
    function handleBodyScrollTop() {
        setTimeout(() => scrollWin.scrollTop = 0, 10)
    }
    /**
     * 切换导航
     * <AUTHOR>
     * @date 2023-02-09
     * @param {String} hash
     */
    function onClickNav(hash) {
        setApplyPath(hash.slice(1))
        setPricePath(hash.slice(1));
        window.location.replace('//' + window.location.host + '/' + hash)
    }
    /**
     * 前往登录页面
     * <AUTHOR>
     * @date 2023-03-17
     */
    function onClickLogin() {
        window.location.href = '//' + window.location.host + '/login'
    }

    function getTargetHost() {
        var targetHost = 'global.abcyun.cn';
        if(location.host.indexOf('abcyun.cn') > -1) {
            targetHost = 'global.abcyun.cn';
        }
        if(location.host.indexOf('test.abczs.cn') > -1) {
            targetHost = 'global-test.abczs.cn';
        }
        if(location.host.indexOf('dev.abczs.cn') > -1 || location.host.indexOf('own.abczs.cn') > -1) {
            targetHost = 'global-dev.abczs.cn';
        }
        if(location.host.indexOf(':8080') > -1) {
            targetHost = 'global-dev.abczs.cn';
        }
        return targetHost;
    }
    const TARGET_HOST = getTargetHost();
    /**
     * 前往关于我们
     * <AUTHOR>
     * @date 2023-03-17
     */
    function onClickAbout() {
        window.open(`//${TARGET_HOST}/about?type=${type}`, '_blank')
    }

    const dialogPriceNode = document.querySelector('#win-price')
    const dialogPriceLoading = document.querySelector('#win-price .loading-wrapper')
    const dialogPriceIframe = document.querySelector('#win-price iframe')
    /**
     * 设置价格地址
     * <AUTHOR>
     * @date 2023-03-21
     * @param {String} type
     */
    function setPricePath(type) {
        dialogPriceIframe.src = `//${TARGET_HOST}/price?type=${type}`
    }
    /**
     * 打开价格弹窗
     * <AUTHOR>
     * @date 2023-03-17
     */
    function onClickPrice() {
        dialogPriceNode.classList.add('is-opened')
    }

    function onClickPriceMobile() {
        dialogPriceIframe.src = `//${TARGET_HOST}/price?type=${type}`
        dialogPriceLoading.classList.add('is-opened')
        dialogPriceNode.classList.add('is-opened')
        dialogPriceIframe.onload = () => {
            dialogPriceLoading.classList.remove('is-opened')
        }
    }
    /**
     * 关闭价格弹窗
     * <AUTHOR>
     * @date 2023-03-17
     */
    function onClickClosePrice() {
        dialogPriceNode.classList.remove('is-opened')
    }

    const dialogApplyNode = document.querySelector('#win-apply')
    const dialogApplyLoading = document.querySelector('#win-apply .loading-wrapper')
    const dialogApplyIframe = document.querySelector('#win-apply iframe')
    /**
     * 设置申请地址
     * <AUTHOR>
     * @date 2023-03-21
     * @param {String} type
     */
    function setApplyPath(type) {
        dialogApplyIframe.src = `//${TARGET_HOST}/apply?type=${type}`
    }
    /**
     * 打开申请弹窗
     * <AUTHOR>
     * @date 2023-03-17
     */
    function onClickApply() {
        dialogApplyNode.classList.add('is-opened')
        if (document.documentElement.clientWidth < 800) {
            scrollWin.classList.add('scroll-stop')
        }
    }
    function onClickApplyMobile() {
        dialogApplyIframe.src = `//${TARGET_HOST}/apply?type=${type}`
        dialogApplyLoading.classList.add('is-opened')
        dialogApplyNode.classList.add('is-opened')
        if (document.documentElement.clientWidth < 800) {
            scrollWin.classList.add('scroll-stop')
        }
        dialogApplyIframe.onload = () => {
            dialogApplyLoading.classList.remove('is-opened')
        }
    }
    /**
     * 关闭申请弹窗
     * <AUTHOR>
     * @date 2023-03-17
     */
    function onClickCloseApply() {
        dialogApplyNode.classList.remove('is-opened')
        scrollWin.classList.remove('scroll-stop')
    }

    window.addEventListener('message', function(event) {
        if (event.data === 'closePriceIframe') {
            dialogPriceNode.classList.remove('is-opened')
        }
        if(event.data === 'closeApplyIframe') {
            dialogApplyNode.classList.remove('is-opened')
            scrollWin.classList.remove('scroll-stop')
        }
    });

    /**
     * @desc 提前加载iframe
     * <AUTHOR>
     * @date 2023-09-07 09:54:03
     */
    setTimeout(() => {
        dialogApplyIframe.src = `//${TARGET_HOST}/apply?type=${type}`
        dialogPriceIframe.src = `//${TARGET_HOST}/price?type=${type}`
    }, 0)
</script>

<script>
    // 从global过来带有reset参数，清除BUser, 停留在首页
    ;(function (){
        const params = new URLSearchParams(window.location.search);
        const resetParam = params.get('reset');
        if(resetParam) {
            var storage = window.localStorage
            storage && storage.removeItem('BUser')
        }
    })();

    // 当本地缓存有用户登录痕迹，直接跳转至登录页面
    ;(function () {
        var storage = window.localStorage
        var BUser = storage && storage.getItem('BUser')
        BUser && onClickLogin()
    })();

    // 给在线咨询插件打锚点
    ;(function () {
        var onlineServicePoint = null
        if(window.screen.width > 800) {
            onlineServicePoint = document.getElementsByClassName(' online-service-point1')
        } else {
            onlineServicePoint = document.getElementsByClassName(' online-service-point2')
        }
        if (onlineServicePoint && onlineServicePoint.length) {
            onlineServicePoint[0].setAttribute('id','onlineService')
            console.warn('DOM属性已更新')
        }
    })();

    //移动端，常见问题展示详情监听
    ;(function() {
        (document.querySelectorAll('.issue-title') || []).forEach(function(node) {
            node.onclick = function() {
                let openedNode = node.parentNode
                openedNode.classList.toggle('opened')
            }
            node.ontouchstart = function() {
                node.classList.add('active')
            }
            node.ontouchend = function() {
                node.classList.remove('active')
            }
        })
    })();

    // 在视图宽度小于minWidth时，设置html标签fontSize，使rem随窗口适配
    const setHtmlSize = (function () {
        var baseSize = 75
        var minWidth = 800
        return function () {
            var clientWidth = document.documentElement.clientWidth
            if (clientWidth <= minWidth) {
                var scale = clientWidth / 750
                //设置一个最大值
                var curSize = baseSize * Math.min(scale, 2)
                curSize = curSize > 54 ? 54 : curSize
                //设置页面根节点字体大小
                document.documentElement.style.fontSize = curSize + 'px'
            } else {
                document.documentElement.style.fontSize = ''
            }
        }
    })();
    setHtmlSize()
    window.onresize = setHtmlSize

    const navConfigList = [
        {
            id: 'clinic',
            test: (hash) => hash === '' || hash.startsWith('#clinic'),
            node: document.querySelector('#nav a.clinic'),
            view: document.querySelector('.clinic-home'),
            keywords: 'ABC诊所管家、电子病历、诊所系统、诊所软件、门诊系统、医疗软件、病历软件、医疗系统、连锁诊所、智能诊断、用药审核、处方安全、诊所运营、诊所开办、在线咨询、在线问诊、诊所开店、诊所创收',
            description: 'ABC诊所管家，移动互联网时代好用的诊所管家，包含预约挂号、电子病历、检验检查、药品进销存、患者管理、连锁管理，向诊所提供强大的CIS系统和微诊所解决方案。',
        },
        {
            id: 'dentistry',
            test: (hash) => hash.startsWith('#dentistry'),
            node: document.querySelector('#nav a.dentistry'),
            view: document.querySelector('.dentistry-home'),
            keywords: 'ABC口腔管家、电子病历、诊所系统、诊所软件、门诊系统、医疗软件、病历软件、医疗系统、连锁诊所、智能诊断、用药审核、处方安全、诊所运营、诊所开办、在线咨询、在线问诊、诊所开店、诊所创收',
            description: 'ABC口腔管家，移动互联网时代好用的诊所管家，包含预约挂号、电子病历、检验检查、药品进销存、患者管理、连锁管理，向诊所提供强大的CIS系统和微诊所解决方案。',
        },
    ]
    /**
     * 获取当前导航配置
     * <AUTHOR>
     * @date 2023-02-09
     * @returns {Object}
     */
    const getNavConfig = function () {
        return navConfigList.find((item) => item.test(location.hash)) || navConfigList[0]
    }
    // 更新当前导航的激活状态
    const updateNavActive = function () {
        const metaNodes = Array.from(document.querySelectorAll('meta'))
        const keywordsNode = metaNodes.find((node) => node.name === 'keywords')
        const descriptionNode = metaNodes.find((node) => node.name === 'description')

        // 先全部清除
        navConfigList.forEach((item) => {
            item.node && item.node.classList && item.node.classList.remove('active')
            item.view && item.view.classList && item.view.classList.remove('active')
        })
        // 找到匹配的
        const navConfig = getNavConfig()
        if (navConfig) {
            // 把匹配的加上active
            navConfig.node && navConfig.node.classList && navConfig.node.classList.add('active')
            // 把匹配的视图显示出来
            navConfig.view && navConfig.view.classList && navConfig.view.classList.add('active')
            // 更新 keywords
            keywordsNode && (keywordsNode.content = navConfig.keywords)
            // 更新 description
            descriptionNode && (descriptionNode.content = navConfig.description)
            // 处理页面滑动到顶部
            handleBodyScrollTop()
            // 设置类型
            type = navConfig.id
        }
    }
    updateNavActive()
    window.onhashchange = updateNavActive

    // 处理头部导航固定
    const scrollWin = document.querySelector('.index-wrapper')
    const handleNavFix = function () {
        // 导航header的主题切换
        const top = scrollWin.scrollTop
        const navNode = document.getElementById('nav')
        if (top > 0) {
            navNode.classList.add('nav-white')
            navNode.classList.remove('nav-abs') // 移动端微信下滑，nav位置问题; position: sticky 也可解决
        } else {
            navNode.classList.remove('nav-white')
            navNode.classList.add('nav-abs') // 移动端微信下滑，nav位置问题; position: sticky 也可解决
        }
    }
    handleNavFix()
    scrollWin.addEventListener('scroll', handleNavFix)
</script>

<!-- WPA start -->
<script id="qd28857803221748cb150741e3d26458ea00ebf31137" src="https://wp.qiye.qq.com/qidian/2885780322/1748cb150741e3d26458ea00ebf31137" charset="utf-8" async defer></script>
<!-- WPA end -->
<script>
if (location.href.indexOf('abcyun.cn') > -1) {
    var _hmt = _hmt || [];
    (function() {
        var hm = document.createElement("script");
        hm.defer = true;
        hm.src = "https://hm.baidu.com/hm.js?0e76138257ab1ae8bc1a9375b4180e64";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
}
</script>
<script>
    (function(b,a,e,h,f,c,g,s){b[h]=b[h]||function(){(b[h].c=b[h].c||[]).push(arguments)};
        b[h].s=!!c;g=a.getElementsByTagName(e)[0];s=a.createElement(e);
        s.src="//s.union.360.cn/"+f+".js";s.defer=!0;s.async=!0;g.parentNode.insertBefore(s,g)
    })(window,document,"script","_qha",359035,false);
</script>
<script type="text/javascript">
    (function(w,n){
        w[n] = typeof w[n] === 'function' ? w[n]:function(){
            (w[n].c = w[n].c || []).push(arguments);
        }

        _qha('send', {
            et: 31,
            order: [{
                id:'applyBtn',/* 注册id, 必填项*/
                orderType:'1'/* 常量，请勿修改*/
            }]
        });
    })(window, '_qha');
</script>

<script>
    // logo 连续点击5次打开工具库
    let clickCount = 0;
    let clickTimer;

    function toSuper() {
        clickTimer && clearTimeout(clickTimer);
        clickCount ++;
        clickTimer = setTimeout(()=> {
            clickCount = 0;
        }, 500);
        if (clickCount === 5) {
            clickCount = 0;
            window.location.href = '//' + window.location.host + '/external/tools';
            window.event.returnValue = false;
        }
    }
</script>

</html>
