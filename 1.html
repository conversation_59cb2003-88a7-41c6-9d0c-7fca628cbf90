<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>HTML5 Five-in-a-row Game</title>
    <style>
        .board {
            margin: 0 auto;
            display: flex;
            border-collapse: collapse;
        }

        .cell {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            background-color: #333;
            cursor: pointer;
        }

        .white {
            background-color: white;
        }
    </style>
</head>
<body>
<div id="game">
    <table class="board" border="1">
        <!-- 省略9*9的方格初始化标记符，由 JS 自动生成 -->
    </table>
</div>

<script>
    const boardSize = 9;
    const boardContainer = document.getElementById('game');
    let isWhite = true;

    // 初始化棋盘
    function initBoard() {
        for (let i = 0; i < boardSize * boardSize; i++) {
            const cell = document.createElement('div');
            cell.classList.add('cell');
            if (i % 2 === 0) {
                isWhite = !isWhite;
            }
            cell.dataset.index = i;
            cell.addEventListener('click', onCellClick);
            boardContainer.appendChild(cell);
        }
    }

    // 检查是否五子连珠
    function checkWin(index, color) {
        const row = Math.floor(index / boardSize);
        const col = index % boardSize;

        let count = 1;
        for (let i = 1; i <= boardSize; i++) {
            if (row + i >= boardSize || boardContainer.children[row + i * boardSize].classList.contains(color)) {
                break;
            }
            count++;
        }
        if (count === 5) return true;

        count = 1;
        for (let i = -1; i >= -boardSize; i--) {
            if (row + i < 0 || boardContainer.children[row + i * boardSize].classList.contains(color)) {
                break;
            }
            count++;
        }
        if (count === 5) return true;

        count = 1;
        for (let i = 1; i <= boardSize && col + i <= 8 && boardContainer.children[row + i * boardSize + col + 1].classList.contains(color);) {
            i++;
            count++;
        }
        if (count === 5) return true;

        count = 1;
        for (let i = -1; i >= -boardSize && col + i > 0 && boardContainer.children[row + i * boardSize + col - 1].classList.contains(color);) {
            i--;
            count++;
        }
        if (count === 5) return true;

        return false;
    }

    // 当棋盘某个单元格被点击
    function onCellClick(event) {
        const index = parseInt(this.dataset.index);
        this.classList.add(isWhite ? 'white' : 'black');
        isWhite = !isWhite;
        if (checkWin(index, isWhite ? 'white' : 'black')) {
            alert(`Player ${color.toUpperCase()} wins!`);
        }
    }

    // 开始游戏
    initBoard();
</script>
</body>
</html>
