import BaseAPI from 'MfBase/base-api';
import Qs from 'qs';

export default class ECStatisticsAPI extends BaseAPI {
    // 实收金额趋势
    static async getPriceTrend(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/actual/price/trend', {
            params,
        });
        return res;
    }
    // 商品销售额top
    static async getSaleTop(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/product/sale/top', {
            params,
        });
        return res;
    }
    // b2c电商订单统计-选择
    static async getOrderSelection(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/selection', {
            params,
        });
        return res;
    }
    // b2c电商订单统计-汇总
    static async getOrderSummary(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/summary', {
            params,
        });
        return res;
    }

    // 外卖订单统计-单据
    static async getTakeawayOrder(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/takeout/transaction', {
            params,
            paramsSerializer(value) {
                return Qs.stringify(value);
            },
        });
        return res;
    }

    // 外卖订单统计-商品
    static async getTakeawayProduct(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/takeout/commodity', {
            params,
            paramsSerializer(value) {
                return Qs.stringify(value);
            },
        });
        return res;
    }

    // 电商订单统计-选择
    static async getEcOrderSelection(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/ec/selection', {
            params,
        });
        return res;
    }
    // 电商订单统计-单据
    static async getEcTransaction(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/ec/transaction', {
            params,
            paramsSerializer(value) {
                return Qs.stringify(value);
            },
        });
        return res;
    }
    // 电商订单统计-商品
    static async getEcCommodity(params) {
        const res = await this.get('/api/v2/sc/stat/b2c/order/ec/commodity', {
            params,
            paramsSerializer(value) {
                return Qs.stringify(value);
            },
        });
        return res;
    }
}
