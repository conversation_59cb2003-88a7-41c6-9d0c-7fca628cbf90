// https://dev.abczs.cn/swagger-ui/?urls.primaryName=ec#/
import BaseAPI, { fetch } from 'MfBase/base-api';
export default class ECOrderAPI extends BaseAPI {

    static async fetchEcOrderList(params) {
        const res = await this.get('/api/ec/order/list', {
            params,
        });
        return res;
    }

    static async createShipperContact(postData) {
        const res = await this.post('/api/ec/order/shipper-contact/add', postData);
        return res;
    }
    static async fetchShipperContactList(params) {
        const res = await this.get('/api/ec/order/shipper-contact/list', {
            params,
        });
        return res;
    }

    static async updateShipperContact(postData) {
        const res = await this.put('/api/ec/order/shipper-contact/update', postData);
        return res;
    }
    static async deleteShipperContact(id) {
        const res = await this.del(`/api/ec/order/shipper-contact/delete?id=${id}`);
        return res;
    }

    /**
     * @desc 订单发货前校验
     *  @param {object} postData -
     *  @param {string} postData.orderIds - 订单号
     *  */
    static async preCheckOrder(postData) {
        const res = await this.post('/api/ec/order/ship/pre-check', postData);
        return res;
    }

    /**
     * @desc 批量发货
     *  @param {object} postData -
     *  @param {string} postData.orderIds - 订单号
     *  @param {string} postData.shipMode - 发货方式默认1，1：异常订单锁单后发货，2：全部发货
     *  */
    static async batchShipOrder(postData) {
        const res = await this.post('/api/ec/order/ship', postData);
        return res;
    }

    /**
     * @desc 更新商家备注
     *  @param {object} postData -
     *  @param {Array} postData.orderIds - 订单号
     *  @param {string} postData.note - 备注
     *  */
    static async updateOrderRemark(postData) {
        const res = await this.post('/api/ec/order/update-note', postData);
        return res;
    }

    /**
     * @desc 订单锁定/解锁
     *  @param {object} postData -
     *  @param {number} postData.orderIds - ABC订单id
     *  @param {string} postData.orderLock - 锁定状态 0:解锁 1:锁定
     *  */
    static async orderLock(postData) {
        const res = await this.post('/api/ec/order/lock', postData);
        return res;
    }

    /**
     * @desc 获取可以合并的订单列表
     * <AUTHOR>
     * @date 2024-04-11 18:34:59
     * @param {object} params -
     * @param {object} params.orderId -
     */
    static async getCanMergeOrders(params) {
        const res = await this.get('/api/ec/order/list-by-merge-tag', {
            params,
        });
        return res;
    }

    /**
     * @desc 订单合单
     * <AUTHOR>
     * @date 2024-04-11 18:34:59
     * @param {object} postData -
     * @param {Array} postData.orderIds -
     */
    static async mergeOrders(postData) {
        const res = await this.post('/api/ec/order/merge', postData);
        return res;
    }
    /**
     * @desc 订单合单
     * <AUTHOR>
     * @date 2024-04-11 18:34:59
     * @param {object} postData -
     * @param {string} postData.orderId -
     */
    static async splitOrders(postData) {
        const res = await this.post('/api/ec/order/split', postData);
        return res;
    }

    /**
     * @desc 更新收件人地址
     * <AUTHOR>
     * @date 2024-04-15 15:19:14
     * @param {object} postData -
     * @param {string} postData.address
     * @param {string} postData.city
     * @param {string} postData.cityId
     * @param {string} postData.ecType
     * @param {string} postData.orderId
     * @param {string} postData.province
     * @param {string} postData.provinceId
     * @param {string} postData.receiverName
     * @param {string} postData.receiverPhone
     * @param {string} postData.town
     * @param {string} postData.townId
     * @return
     */
    static async updateOrderAddress(postData) {
        const res = await this.post('/api/ec/order/update-address', postData);
        return res;
    }

    /**
     * @desc 获取物流
     * <AUTHOR>
     * @date 2024-04-16 17:29:27
     * @param {object} params
     * @param {number} params.ecType
     * @param {number} params.orderId
     */
    static async fetchLogisticsTrace(params) {
        const res = await this.get('/api/ec/order/logistics-trace', {
            params,
            customErrorTips: true,
        });
        return res;
    }

    /**
     * @desc 批量获取获取面单信息
     * <AUTHOR>
     * @date 2024-04-16 17:29:27
     * @param {object} data
     * @param {Object[]} data.templates - 模版列表
     * @param {number} data.templates[].ecType
     * @param {string} data.templates[].templateId
     * @param {Array} data.templates[].orderIds
     */
    static async batchGetOrderWaybill(data) {
        const res = await this.post('/api/ec/order/waybill/get', data);
        return res;
    }

    /**
     * @desc 获取面单信息
     * <AUTHOR>
     * @date 2024-04-16 17:29:27
     * @param {object} data
     * @param {array} data.orderIds
     */
    static async fetchOrderWaybill(data) {
        const res = await this.post('/api/ec/order/waybill/info', data);
        return res;
    }

    /**
     * @desc 获取发货单打印信息
     * <AUTHOR>
     * @date 2024-04-16 17:29:27
     * @param {object} data
     * @param {array} data.orderIds
     */
    static async fetchOrderPrintInfo(data) {
        const res = await this.post('/api/ec/order/print', data);
        return res;
    }

    /**
     * @desc 回收电子面单号
     * <AUTHOR>
     * @date 2024-04-16 20:16:53
     * @param {object} data
     * @param {object[]} data.waybillList
     * @param {string} data.ecMallId
     * @param {number} data.ecType
     * @param {string} data.waybillCode
     * @param {string} data.wpId
     */
    static async cancelEcWaybill(data) {
        const res = await this.post('/api/ec/order/waybill/cancel', data);
        return res;
    }

    /**
     * @desc 改变打印状态
     * <AUTHOR>
     * @date 2024-04-16 20:16:53
     * @param {Object} data - The employees who are responsible for the project.
     * @param {Object[]} data.statusList
     * @param {string} data.statusList[].orderId
     * @param {number} data.statusList[].printStatus
     * @param {number} data.statusList[].waybillCode
     */
    static async updatePrintStatus(data) {
        const res = await this.post('/api/ec/order/waybill/print-status', data);
        return res;
    }

    /**
     * @desc 获取订单详情
     * <AUTHOR>
     * @param {object} params
     * @param {number} params.orderId
     */
    static async fetchOrderDetail(params, customErrorTips = false) {
        const res = await this.get('/api/ec/order/detail', {
            params,
            customErrorTips,
        });
        return res;
    }

    /**
     * @desc 获取订单订单统计概要信息
     * <AUTHOR>
     */
    static async fetchECOrderSummary() {
        const res = await this.get('/api/ec/order/summary');
        return res;
    }

    /**
     * @desc 获取打印发货单配置
     * <AUTHOR>
     */
    static async fetchShipPrintConfig() {
        const res = await this.get('/api/ec/order/print/setting');
        return res;
    }
    /**
     * @desc update打印发货单配置
     * <AUTHOR>
     */
    static async updateShipPrintConfig(data) {
        const res = await this.put('/api/ec/order/print/setting', data);
        return res;
    }

    /**
     * @desc 同步美团订单
     * <AUTHOR> Yang
     * @date 2025-02-21 16:10:12
     * @param
     * @return
    */
    static async syncMeituanOrder(data) {
        const res = await fetch({
            url: '/api/ec/mt/orders/batch-sync',
            method: 'post',
            data,
            disabledCancel: true,
            customErrorTips: true,
        });
        return res;
    }


    /**
     * @desc 获取美团订单汇总信息
     * <AUTHOR> Yang
     * @date 2025-02-21 16:10:12
    */
    static async getOrderSummaryInfo(data) {
        const res = await this.get('/api/ec/mt/orders/summary-info', data);
        return res;
    }

    /**
     * @desc 同步美团处方信息
     * <AUTHOR> Yang
     * @date 2025-02-21 16:10:12
     */
    static async syncMeituanPrescription(data) {
        const res = await this.post('/api/v2/gsp/register/batch/sync/mt-prescription', data);
        return res;
    }

    /**
     * @desc 获取订单处方信息
     * <AUTHOR> Yang
     * @date 2025-02-21 16:10:12
     * @param {object} params
     * @param {string} params.orderId
     * @param {string} params.extOrderId
     */
    static async fetchOrderPrescription(params) {
        const {
            orderId,
            extOrderId,
        } = params;
        const res = await this.get(`/api/v2/gsp/register/mt/${orderId}/${extOrderId}`);
        return res;
    }

    /**
     * @desc 更新订单处方信息
     * <AUTHOR> Yang
     * @date 2025-02-21 16:10:12
     * @param {string} orderId
     * @param {string} extOrderId
     * @param {object} data
     */
    static async updateOrderPrescription(orderId, extOrderId, data) {
        const res = await this.put(`/api/v2/gsp/register/mt/${orderId}/${extOrderId}`, data);
        return res;
    }

    /**
     * @desc 保存订单
     * <AUTHOR> Yang
     * @param {string} orderId
     * @param {object} data
     */
    static async saveOrder(orderId, data) {
        const res = await this.put(`/api/ec/order/${orderId}`, data);
        return res;
    }

    /**
     * @desc 单个订单发货
     * <AUTHOR> Yang
     * @param {string} orderId
     * @return
     */
    static async shipOrderById(orderId, data) {
        const res = await this.put(`/api/ec/order/${orderId}/ship`, data);
        return res;
    }

    /**
     * @desc 订单计算批次等信息
     * <AUTHOR> Yang
     * @param {string} orderId
     * @param {object} data
     * @return
     */
    static async calculateEcOrder(orderId, data) {
        const res = await this.post(`/api/ec/order/${orderId}/calculate`, data);
        return res;
    }

    /**
     * @desc 订单退库列表
     * <AUTHOR>
     * @return
     */
    static async fetchEcRefundOrderList(params) {
        const res = await this.get('/api/ec/orders/after-sale/list', {
            params,
        });
        return res;
    }
    /**
     * @desc 获取售后单详情
     * <AUTHOR> Yang
     * @param {string} afterSaleId
     * @return
     */
    static async getAfterSale(afterSaleId) {
        const res = await this.get(`/api/ec/orders/after-sale/${afterSaleId}`);
        return res;
    }
    /**
     * @desc 处理售后单
     * <AUTHOR> Yang
     * @param {string} afterSaleId
     * @param {object} data
     * @param {number} data.operationType 操作类型；0：保存草稿；1：完成处理
     * @return
     */
    static async updateAfterSale(afterSaleId, data) {
        const res = await this.put(`/api/ec/orders/after-sale/${afterSaleId}`, data);
        return res;
    }

    /**
     * @desc 上传美团实时获取的汇总信息，无法从ABC系统出的汇总数据
     * <AUTHOR> Yang
     * @return
     */
    static async syncMTOrderInfo(data) {
        const res = await this.post('/api/ec/mt/orders/today/count-info/from-mt', data);
        return res;
    }

    /**
     * @desc 更新订单打印信息
     */
    static async updateOrderPrintInfo(data) {
        const res = await this.post('/api/ec/order/order/item/print/abbreviation', data);
        return res;
    }

    /**
     * @desc 更新订单打印简称
     */
    static async updateOrderPrintShortName(data) {
        const res = await this.post('/api/ec/order/product/spec/abbreviation', data);
        return res;
    }
}
