<template>
    <abc-container class="abc-pharmacy-order-cloud-container">
        <abc-flex vertical style="position: relative; width: 100%;">
            <abc-card :border="false" class="pharmacy__app-tabs-card">
                <abc-tabs-v2
                    :value="currentTab"
                    :option="routerOptions"
                    size="huge"
                    no-close-border
                    :style="{ padding: '0 16px 0 12px !important' }"
                    @change="onChangeTab"
                ></abc-tabs-v2>
            </abc-card>
            <router-view></router-view>
        </abc-flex>
    </abc-container>
</template>

<script>
    import * as core from '@/core/index.js';
    import { createAbcPage } from 'MfBase/core';
    import {
        OrderCloudModuleId, PharmacyOrderCloudRouterNameKeys,
    } from './core/routes';
    import {
        ASSIST_MESSAGE, assistMessageOn,
    } from './utils/message.js';
    import * as business from 'MfFeEngine/business';
    import AbcSocket from 'MfBase/single-socket';
    import { mapGetters } from 'vuex';
    import ECOrderAPI from './api/order';
    import ECAuthAPI from '@/api/auth';
    import DialogDesktopAssistant from './components/dialog-desktop-assistant';
    import OrderCloudDaemonService from './daemon/order-cloud-daemon-service';
    import { EditionKeyEnum } from 'MfBase/access-constant';

    export default {
        name: 'PharmacyOrderCloud',
        mixins: [
            createAbcPage(core),
        ],
        provide() {
            return {
                eCOrderStockOutStat: this.eCOrderStockOutStat,
                eCOrderStat: this.eCOrderStat,
            };
        },
        beforeRouteEnter (to, from, next) {
            // 初次从其他模块进入订单云模块，非刷新
            if (!from.fullPath.includes('/order-cloud') && from.fullPath !== '/') {
                localStorage.setItem('first_go_order_cloud', 1);
            }
            next();
        },
        beforeRouteLeave (to, from, next) {
            if (!to.fullPath.includes('/order-cloud')) {
                localStorage.removeItem('first_go_order_cloud');
            }
            next();
        },
        data() {
            return {
                PharmacyOrderCloudRouterNameKeys,
                eCOrderStockOutStat: {
                    stockOutExceptionCount: 0,
                },
                eCOrderStat: {
                    toShipUnPrintCount: 0,
                    toShipPrintedCount: 0,
                    toShipLockedCount: 0,
                    totalMtTodoCount: 0,
                    totalPddTodoCount: 0,
                    newOrderCount: 0, // mt-新订单数量
                    pendingDeliveryCount: 0, // mt-待配送数量
                    deliveringCount: 0, // mt-配送中数量
                    deliveredCount: 0, // mt-已送达数量
                    confirmedCount: 0, // mt-已确认收货数量
                    waitingOutStockCount: 0, // mt-待处理ABC出库数量
                    afterSaleWaitingDealCount: 0, // mt-待处理售后数量
                },
                hasBindedEc_takeaway: false,
                hasBindedEc_ecommerce: false,
            };
        },

        computed: {
            ...mapGetters([
                'isChainAdmin',
                'isAdmin',
                'isChainSubStore',
                'isSingleStore',
                'userInfo',
                'currentEdition',
                'subClinics',
            ]),
            installChromeStatus() {
                return this.page?.$store?.state?.installChromeStatus;
            },
            moduleIds() {
                return (this.userInfo && this.userInfo.moduleIds) || '';
            },
            // 当前选中的tab
            currentTab() {
                const target = this.routerOptions.find((item) => this.$route.name.startsWith(item.value));
                return target?.value || '';
            },
            shipPrintTotalCount() {
                const {
                    totalPddTodoCount = 0,
                } = this.eCOrderStat;
                return totalPddTodoCount;
            },
            hasSettingModule() {
                if (this.isChainAdmin) return false;
                return this.hasOrderCloudModule || this.moduleArr.includes(OrderCloudModuleId.setting);
            },
            moduleArr() {
                if (!this.moduleIds) {
                    return [];
                }
                return this.moduleIds.split(',');
            },
            hasOrderCloudModule() {
                if (!this.moduleIds) {
                    return false;
                }
                return this.moduleIds === '0' || this.moduleArr.includes(OrderCloudModuleId.main);
            },
            calcTabAccess() {
                const isBasicEdition = this.currentEdition.key === EditionKeyEnum.BASIC;
                const subClinicList = this.subClinics.filter((item) => !item.chainAdmin);
                const hasProfessional = subClinicList.some((item) => item.edition.key === EditionKeyEnum.PROFESSIONAL);

                if (this.isSingleStore) {
                    if (isBasicEdition) {
                        return false;
                    }
                    return true;
                }

                if (this.isChainSubStore) {
                    if (isBasicEdition) {
                        if (hasProfessional) {
                            return true;
                        }
                        return false;
                    }
                    return true;
                }

                if (this.isChainAdmin) {
                    if (subClinicList.every((item) => item.edition.key === EditionKeyEnum.BASIC)) {
                        return false;
                    }

                    if (subClinicList.some((item) => item.edition.key === EditionKeyEnum.PROFESSIONAL)) {
                        return true;
                    }
                }

                return true;
            },
            hasTakeawayModule() {
                return this.hasOrderCloudModule ||
                    this.moduleArr.includes(OrderCloudModuleId.takeaway) ||
                    this.moduleArr.includes(OrderCloudModuleId.takeawayOrder) ||
                    this.moduleArr.includes(OrderCloudModuleId.takeawayMtGoods) ||
                    this.moduleArr.includes(OrderCloudModuleId.takeawayOutStorage);
            },
            hasEcommerceModule() {
                return this.hasOrderCloudModule ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerce) ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerceOrder) ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerceOutRecord) ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerceEcStatistics) ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerceEcGoods);
            },
            // 基础版
            isBasic() {
                return this.currentEdition.key === EditionKeyEnum.BASIC;
            },
            // tab 禁用判断
            tabDisabled() {
                return this.isBasic;
            },
            routerOptions() {
                let res = [];
                // 未开通与安装
                if (this.tabDisabled) {
                    res = [
                        {
                            label: '外卖',
                            disabled: this.tabDisabled,
                            disabledTips: '请先开通与安装订单云',
                        },
                        {
                            label: '电商',
                            disabled: this.tabDisabled,
                            disabledTips: '请先开通与安装订单云',
                        },
                        {
                            label: '订单云设置',
                            value: PharmacyOrderCloudRouterNameKeys.ecSettings,
                        },
                    ];
                } else {
                    // 开通与安装后
                    if (this.hasTakeawayModule) {
                        res.push({
                            label: '外卖',
                            value: PharmacyOrderCloudRouterNameKeys.takeaway,
                            moduleId: OrderCloudModuleId.takeaway,
                            maxNoticeNumber: 99,
                            noticeNumber: this.eCOrderStat.totalMtTodoCount || 0,
                            separation: false,
                            disabled: this.tabDisabled,
                            disabledTips: '请先开通与安装订单云',
                        });
                    }
                    if (this.hasEcommerceModule) {
                        res.push({
                            label: '电商',
                            value: PharmacyOrderCloudRouterNameKeys.ecommerce,
                            moduleId: OrderCloudModuleId.ecommerce,
                            maxNoticeNumber: 99,
                            noticeNumber: this.eCOrderStat.totalPddTodoCount || 0,
                            separation: false,
                            disabled: this.tabDisabled,
                            disabledTips: '请先开通与安装订单云',
                        });
                    }
                    res.push({
                        label: '订单云设置',
                        value: PharmacyOrderCloudRouterNameKeys.ecSettings,
                        moduleId: OrderCloudModuleId.setting,
                        separation: false,
                    });
                }

                return res;
            },
        },
        async created() {
            this.$store.dispatch('fetchChainSubClinics');//拉取所有门店数据
            assistMessageOn(ASSIST_MESSAGE, this.receiveMessage);
            const { socket } = AbcSocket.getSocket();
            if (business.ECOrderService) {
                this._SocketService = new business.ECOrderService(socket);
                this._SocketService.onECOrderStat(this.refreshECOrderStat);
                this._SocketService.onECOrderStockOutStat(this.refreshECOrderStockOutStat);
                this.$on('hook:beforeDestroy', () => {
                    this._SocketService?.offECOrderStat(this.refreshECOrderStat);
                    this._SocketService?.offECOrderStockOutStat(this.refreshECOrderStockOutStat);
                });
            }
            this.fetchECOrderSummary();
            this.desktopAssistantTips();
            if (this.tabDisabled) {
                this.$router.push({
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsServiceIntro,
                });
            } else {
                // 初次进入订单云模块
                if (+localStorage.getItem('first_go_order_cloud')) {
                    // 判断是否安装订单云
                    if (!this.installChromeStatus) {
                        this.$router.push({
                            name: PharmacyOrderCloudRouterNameKeys.ecSettingsServiceIntro,
                        });
                    } else if (this.routerOptions.length === 1) {
                        // 判断是否只有一个模块
                        this.$router.push({
                            name: this.routerOptions[0].value,
                        });
                    } else {
                        // 获取外卖及电商绑定网店信息
                        await this.getBindAuthorizedEc();
                        // 判断是否同时存在外卖及电商模块
                        if (this.hasTakeawayModule && this.hasEcommerceModule) {
                            // 判断是否同时存在外卖及电商绑定网店
                            if (!this.hasBindedEc_takeaway && this.hasBindedEc_ecommerce) {
                                this.$router.push({
                                    name: PharmacyOrderCloudRouterNameKeys.ecommerce,
                                });
                            } else {
                                this.$router.push({
                                    name: PharmacyOrderCloudRouterNameKeys.takeaway,
                                });
                            }
                        }
                    }
                    localStorage.setItem('first_go_order_cloud', 0);
                }
            }
        },
        methods: {
            async fetchECOrderSummary() {
                const res = await ECOrderAPI.fetchECOrderSummary();
                this.eCOrderStockOutStat.stockOutExceptionCount = res.stockOutExceptionCount;
                Object.assign(this.eCOrderStat, res);
            },
            refreshECOrderStat(data) {
                data && Object.assign(this.eCOrderStat, data);
            },
            refreshECOrderStockOutStat(data) {
                data && Object.assign(this.eCOrderStockOutStat, data);
            },
            onChangeTab(value) {
                this.$router.push({
                    name: value,
                });
            },
            /**
             * @desc 接受订单云助手发的消息，action对应前面的数字
             * 1.待打单列表
             * 2.待发货列表
             * 3.订单异常-出现浮窗
             * 4.物流预警-出现浮窗
             * 5.桌面助手设置
             * 6.售后订单状态列表-搜索售后单号定位订单
             * <AUTHOR>
             * @date 2024-04-24 09:41:58
             */
            receiveMessage(event, data) {
                console.log(event, data);
                const { action } = data;
                switch (action) {
                    case 1:
                        this.$router.push({
                            name: PharmacyOrderCloudRouterNameKeys.ecommerce,
                        });
                        break;
                    case 2:
                        this.$router.push({
                            name: PharmacyOrderCloudRouterNameKeys.ecommerce,
                            query: {
                                tab: 1,
                            },
                        });
                        break;
                    case 3:
                        this.$router.push({
                            name: PharmacyOrderCloudRouterNameKeys.ecommerce,
                        });
                        this.$nextTick(() => {
                            document.querySelector('#order-cloud-warn-exception-btn')?.click();
                        });
                        break;
                    case 4:
                        this.$router.push({
                            name: PharmacyOrderCloudRouterNameKeys.ecommerce,
                        });
                        this.$nextTick(() => {
                            document.querySelector('#order-cloud-warn-trace-btn')?.click();
                        });
                        break;
                    case 5:
                        this.$router.push({
                            name: PharmacyOrderCloudRouterNameKeys.ecSettingDesktopAssistant,
                        });
                        break;
                    case 6:
                        break;
                    case 7:
                        this.$nextTick(() => {
                            this.$router.push({
                                name: PharmacyOrderCloudRouterNameKeys.takeawayOrder,
                            });
                        });
                        break;
                    case 8:
                        this.$nextTick(() => {
                            this.$router.push({
                                name: PharmacyOrderCloudRouterNameKeys.takeawayOutStorage,
                            });
                        });
                        break;
                    case 9:
                        this.$nextTick(() => {
                            this.$router.push({
                                name: PharmacyOrderCloudRouterNameKeys.takeawayRefundStorage,
                            });
                        });
                        break;
                    default:
                        break;
                }
            },
            desktopAssistantTips() {
                const desktopAssistManager = OrderCloudDaemonService
                    .getInstance()
                    .getDesktopAssistManager();
                if (desktopAssistManager && desktopAssistManager.getPreferenceEnable()) {
                    return;
                }
                const desktopOpenFlag = 'desktop_assistant_dialog_open_flag';
                const _printConfig = localStorage.getItem('last_selected_waybill_print_config');
                const _openFlag = sessionStorage.getItem(desktopOpenFlag);
                if (_printConfig && !_openFlag) {
                    sessionStorage.setItem(desktopOpenFlag, '1');
                    new DialogDesktopAssistant({
                        $store: this.page.$store,
                    }).generateDialogAsync();
                }
            },
            // 获取已绑定的外卖电商店铺
            async getBindAuthorizedEc() {
                try {
                    const res = await ECAuthAPI.fetchAuthorizedEcList({
                        limit: 10,
                        offset: 0,
                    });
                    if (res?.rows?.length) {
                        this.hasBindedEc_takeaway = res.rows.filter((item) => item.ecType === 4).length > 0;
                        this.hasBindedEc_ecommerce = res.rows.filter((item) => item.ecType === 1).length > 0;
                    }
                } catch (err) {
                    console.error(err);
                }
            },
        },
    };
</script>

<style lang="scss">
    @import './_index.scss';
</style>
