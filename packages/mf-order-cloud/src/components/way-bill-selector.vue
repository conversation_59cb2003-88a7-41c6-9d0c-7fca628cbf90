<template>
    <abc-popover
        ref="popover"
        placement="bottom-start"
        trigger="click"
        :visible-arrow="false"
        padding-size="large"
        style="height: 100%;"
    >
        <div slot="reference" class="way-bill-select-input">
            <abc-input :value="selectedWaybill && selectedWaybill.standardTemplateName" style="display: none;"></abc-input>
            <div v-if="selectedWaybill && selectedWaybill.wpCode" class="express-way-bill-item-wrapper">
                <div class="express-logo">
                    <abc-image :width="22" :height="22" :src="WpCodeLogo[selectedWaybill.wpCode]"></abc-image>
                </div>
                <div class="express-info">
                    <div class="name">
                        {{ selectedWaybill.wpName }} - {{ selectedWaybill.ecMallName }} - {{ selectedWaybill.standardTemplateName }}
                    </div>
                    <div v-if="selectedWaybill.waybillInfo && selectedWaybill.waybillInfo.quantity" class="quantity">
                        余额 {{ selectedWaybill.waybillInfo.quantity }}
                    </div>
                </div>
            </div>
            <div v-else class="placeholder">
                请选择
            </div>
            <abc-icon
                style="margin-right: 8px;"
                icon="dropdown_triangle"
                color="#aab4bf"
            ></abc-icon>
        </div>

        <div id="way-bill-select-popover">
            <template v-if="waybillTemplates && waybillTemplates.length">
                <div
                    v-for="template in waybillTemplates"
                    :key="`${template.wpCode}_${ template.id}`"
                    class="express-way-bill-item-wrapper"
                    @click="handleClick(template)"
                >
                    <div class="express-logo">
                        <abc-image :width="32" :height="32" :src="WpCodeLogo[template.wpCode]"></abc-image>
                    </div>
                    <div class="express-info">
                        <div class="name">
                            {{ template.wpName }} - {{ template.ecMallName }} - {{ template.standardTemplateName }}
                        </div>
                        <div v-if="template.waybillInfo && template.wpType !== 3" class="print-info">
                            <span class="warn">余额 {{ template.waybillInfo.quantity || 0 }}</span>
                        </div>
                    </div>
                </div>
            </template>
            <div v-else class="blank-tips-wrapper">
                <div class="tips">
                    尚未设置打单模版
                    请在【设置】-【打印】新建面单模板
                </div>
                <abc-button type="text" @click="handleViewOpen">
                    去设置
                </abc-button>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import { WpCodeLogo } from '../utils/constants';
    import { PharmacyOrderCloudRouterNameKeys } from '../core/routes.js';

    export default {
        props: {
            value: Object,
            waybillTemplates: {
                type: Array,
                required: true,
            },
        },
        data() {
            return {
                WpCodeLogo,
            };
        },
        computed: {
            selectedWaybill: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        methods: {
            handleViewOpen() {
                this.$emit('router-change');
                this.$router.push({
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsPrint,
                });
            },
            handleClick(template) {
                this.selectedWaybill = template;
                this.$refs.popover.doClose();
            },
        },
    };
</script>

<style lang="scss">
@import "@/styles/theme.scss";
@import "@/styles/mixin.scss";

.way-bill-select-input {
    display: flex;
    align-items: center;
    width: 318px;
    min-height: 32px;
    cursor: pointer;
    border: var(--abc-border-1, 1px) solid $P7;
    border-radius: var(--abc-border-radius-small, 6px);

    &:hover {
        border-color: var(--abc-color-theme3, #459eff);
    }

    &:focus,
    &.is-focus {
        border-color: #0270c9;
        box-shadow: 0 0 0 2px #c6e2ff;
        transition: border 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .placeholder {
        flex: 1;
        padding: 5px 8px;
        font-size: 14px;
        line-height: 22px; /* 157.143% */
        color: var(--abc-color-T3, #aab4bf);
    }
}

.express-print-form-item.is-error .way-bill-select-input {
    background-color: #fef7e9;
    border-color: var(--abc-color-Y2) !important;
}

.express-way-bill-item-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 5px 8px;

    .express-logo {
        margin-right: 8px;
    }

    .express-info {
        flex: 1;
        width: 0;

        .name {
            font-size: 14px;
            line-height: 22px;
            color: var(--abc-color-T1, #000000);
        }

        .quantity {
            font-size: 12px;
            color: var(--abc-color-Y2, var(--abc-color-Y2));
        }

        .print-info {
            font-size: 12px;
            line-height: 16px; /* 133.333% */
            color: var(--abc-color-T3, #aab4bf);
        }

        .warn {
            margin-right: 8px;
            font-size: 12px;
            line-height: 16px; /* 133.333% */
            color: var(--abc-color-Y2, var(--abc-color-Y2));
        }
    }
}

#way-bill-select-popover {
    width: 318px;
    padding: 4px;
    background: var(--abc-color-cp-white, #ffffff);
    border: var(--abc-border-1, 1px) solid var(--abc-color-P7, #e0e5ee);
    border-radius: var(--abc-border-radius-small, 6px);
    box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.1);

    .blank-tips-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 142px;

        .tips {
            width: 215px;
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            color: var(--abc-color-T3, #aab4bf);
            text-align: center;
        }
    }

    .express-way-bill-item-wrapper {
        cursor: pointer;
        border-radius: var(--abc-border-radius-mini, 4px);

        &:hover {
            background: var(--abc-color-cp-grey4, #f2f4f7);
        }

        &.is-selected {
            background: var(--abc-color-B4, #e9f2fe);
        }
    }
}
</style>
