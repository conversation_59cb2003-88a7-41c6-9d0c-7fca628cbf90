<template>
    <div v-if="visible" class="dialog-mask">
        <div class="dialog-install-status">
            <div class="logo-wrapper">
                <abc-image :width="136" :height="24" :src="logo"></abc-image>
            </div>
            <abc-text tag="div" size="large" style="margin-bottom: 48px;">
                聚合线上订单 高效同步库存
            </abc-text>
            <div class="progress-bar">
                <div class="progress-inner" :style="{ width: `${progress }%` }"></div>
            </div>
            <abc-text
                tag="div"
                size="mini"
                theme="gray-light"
                style="margin-bottom: 72px;"
            >
                {{ statusText }}
            </abc-text>
            <div class="copyright">
                Copyright ©2025 · All Rights Reserved
            </div>
            <abc-image
                :width="142"
                :height="142"
                :src="flower"
                style="position: absolute; right: 0; bottom: 0;"
            ></abc-image>
        </div>
    </div>
</template>

<script>
    import OrderCloudDaemonService from '../../daemon/order-cloud-daemon-service';
    import flower from '../../assets/images/install/install-flower.png';
    import logo from '../../assets/images/install/install-logo.png';

    export default {
        name: 'DialogInstallStatus',

        props: {
            value: Boolean,
        },

        data() {
            return {
                progress: 0,
                statusText: '下载更新中...',
                timer: null,
                flower,
                logo,
                timeoutTimer: null,
            };
        },

        computed: {
            visible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },

        mounted() {
            this.startProgress();
        },

        async created() {
            this.setProgress(1);
            this.status = 'installing';
            const res = await OrderCloudDaemonService.getInstance().chromePuppeteerManager.launchPuppeteer();
            if (!res) {
                this.status = 'error';
                return;
            }
            this.status = 'success';
            this.setProgress(100);
            this.timeoutTimer = setTimeout(() => {
                this.$Toast({
                    message: '安装成功',
                    type: 'success',
                });
                this.$emit('install-success');
                this.visible = false;
                this.timeoutTimer = null;
            }, 1000);
        },

        beforeDestroy() {
            this.stopProgress();
        },

        methods: {
            handleClose() {
                this.visible = false;
            },

            startProgress() {
                this.progress = 0;
                this.timer = setInterval(() => {
                    if (this.progress < 90) {
                        this.progress += 1;
                    } else {
                        this.stopProgress();
                    }
                }, 800);
            },

            stopProgress() {
                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            },

            // 外部可调用此方法来设置进度
            setProgress(value) {
                this.progress = Math.min(100, Math.max(0, value));
            },
        },
    };
</script>

<style lang="scss" scoped>
.dialog-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.dialog-install-status {
    position: relative;
    width: 560px;
    padding: 72px 0 24px 0;
    text-align: center;
    background: linear-gradient(180deg, #ffffff 0%, #f7fbff 100%);
    border-radius: 8px;
    box-shadow: var(--abc-shadow-1);
    opacity: 0.85;
    backdrop-filter: blur(2px);

    .logo-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
    }

    .progress-bar {
        width: 160px;
        height: 4px;
        margin: 0 auto 8px;
        overflow: hidden;
        background: #f0f0f0;
        border-radius: 2px;

        .progress-inner {
            height: 100%;
            background: #1890ff;
            border-radius: 2px;
            transition: width 0.3s ease;
        }
    }

    .copyright {
        font-size: 12px;
        line-height: 20px;
        color: #666666;
        opacity: 0.45;
    }
}
</style>
