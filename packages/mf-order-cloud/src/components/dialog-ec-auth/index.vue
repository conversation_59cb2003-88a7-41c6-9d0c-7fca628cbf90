<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        title="获取网店授权"
        custom-class="authorized-ec-dialog"
        append-to-body
        size="medium"
        :on-confirm="conConfirm"
        :confirm-text="confirmButtonText"
        :confirm-loading="loading"
        :close-after-confirm="false"
    >
        <abc-flex
            vertical
            align="center"
            justify="center"
            :gap="24"
            style="height: 248px;"
        >
            <abc-image
                width="48"
                height="48"
                :src="WpCodeLogo.MT"
                alt=""
            ></abc-image>
            <abc-text size="largex" bold>
                {{ bindClinicInfo ? bindClinicInfo.accountInfo.wmPoiName : '请登录美团网店授权' }}
            </abc-text>
            <abc-flex :gap="6" align="center">
                <abc-icon icon="s-store-fill" size="16" color="var(--abc-color-P10)"></abc-icon>
                <abc-text theme="gray">
                    接单门店: {{ currentClinic.clinicName }}
                </abc-text>
            </abc-flex>
        </abc-flex>
    </abc-modal>
</template>


<script>
    import { mapGetters } from 'vuex';
    import { MeituanAuthManager } from '@/daemon/crawler/provider/meituan';
    import { formatDate } from '@abc/utils-date';
    import {
        EcShopTypeEnum, ECTypeEnum, WpCodeLogo,
    } from '@/utils/constants';
    import ECAuthAPI from '@/api/auth';

    export default {
        props: {
            authCode: {
                type: String,
                default: '',
            },
            extMallId: {
                type: String,
                default: '',
            },
            mallId: {
                type: String,
                default: '',
            },
            finishFunc: {
                type: Function,
                default: null,
            },
        },
        data() {
            return {
                WpCodeLogo,
                loading: false,
                visible: false,
                bindClinicInfo: null,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            confirmButtonText() {
                return this.bindClinicInfo ? '确定授权' : '登录美团';
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        methods: {
            async conConfirm() {
                if (this.bindClinicInfo) {
                    await this.handleAuth();
                } else {
                    await this.handleRegistration();
                }
            },
            async handleRegistration() {
                try {
                    this.loading = true;
                    const response = await MeituanAuthManager.getInstance().requestAuth(this.extMallId);
                    if (response.status === false) {
                        const errorMessage = response.data?.error?.message || response.message || '未知错误';
                        // 显示错误信息
                        this.$alert({
                            type: 'warn',
                            title: '网店续期失败',
                            content: errorMessage,
                        });
                    } else {
                        this.bindClinicInfo = response.data;
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            async handleAuth() {
                try {
                    this.loading = true;
                    const {
                        accountInfo,
                        cookies,
                    } = this.bindClinicInfo;
                    const tokenCookie = cookies.find((it) => it.name === 'token');
                    // 调用ECAuthAPI.authEc
                    const authEcParams = {
                        authCode: this.authCode,
                        ecId: accountInfo.id,
                        ecAccessToken: tokenCookie.value,
                        ecAccessTokenExpireTime: formatDate(tokenCookie.expires * 1000, 'YYYY-MM-DD HH:mm:ss'),
                        clientCookie: JSON.stringify(cookies),
                        ecMallInfo: {
                            mallDesc: accountInfo.mallDesc,
                            mallId: accountInfo.id,
                            mallLogo: accountInfo.headImgUrl,
                            mallName: accountInfo.wmPoiName,
                            shopType: EcShopTypeEnum.O2O,
                        },
                        ecType: ECTypeEnum.MT, // 美团的ecType
                    };

                    await ECAuthAPI.continueAuthEc(this.mallId, authEcParams);
                    this.$Toast({
                        type: 'success',
                        message: '续期成功',
                    });
                    localStorage.setItem('isAuthOrderCloud', true);

                    this.finishFunc && this.finishFunc();
                    this.visible = false;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
.authorized-ec-dialog {
    left: 40%;
    height: auto;

    .authorized-ec-dialog__content {
        height: 296px;
    }
}
</style>
