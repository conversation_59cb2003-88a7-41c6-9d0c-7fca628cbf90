import PDDPrint from './PDDPrint';
import { ECTypeEnum } from '../constants';
import ECOrderAPI from '@/api/order';
import AbcPrinter from '@/printer';
import OrderCloudPrinterConfig from '../../printer/config.js';
import PrintManager from '@/printer/manager/print-manager';
import { clone } from '@abc/utils';

export default class ExpressPrintManage {
    constructor(options) {
        this.printTask = clone(options.printTask) || [];
        this.shipPrintConfig = options.shipPrintConfig;
        this.onProcess = options.onProcess;
        this.onError = options.onError;
        this.onComplete = options.onComplete;
    }

    async startPrint() {
        while (this.printTask.length) {
            const data = this.printTask.shift();
            try {
                await ExpressPrintManage.print(data, {
                    shipPrintConfig: this.shipPrintConfig,
                });
                this.onProcess({
                    leftCount: this.printTask.length,
                    curPrint: data,
                });
            } catch (e) {
                console.error(e);
                this.onError && this.onError(e);
            }
        }
        // 全部打印完
        this.onComplete && this.onComplete();
    }
    stopPrint() {
        this.printTask.length = 0;
    }

    static async getPrinterList() {
        if (window.electron) {
            return PrintManager.getInstance().getPrinterList();
        }
        const res = await new PDDPrint().getPrinters();
        return res.printers;
    }

    /**
     * @desc 校验打印可用
     * <AUTHOR>
     * @date 2024-04-23 18:01:31
     */
    static checkPrintStatus(printTypeList) {
        const _arr = [];
        printTypeList.forEach((item) => {
            if (item.ecType === ECTypeEnum.PDD) {
                _arr.push(new PDDPrint().connectPDDPrinter());
            }
        });
        return Promise.all(_arr);
    }

    static getCustomAreaStr(task) {
        const {
            orderInfo,
            templateInfo,
        } = task;
        const {
            customAreaSwitch,
        } = templateInfo;

        let customAreaStr = '';
        if (customAreaSwitch) {
            customAreaStr = orderInfo?.goodsList?.map((it, index) => {
                const {
                    printInfo,
                    productSpecAbbreviation,
                } = it;
                const productAbbreviation = printInfo?.printAbbreviation?.productAbbreviation || productSpecAbbreviation?.printAbbreviation?.productAbbreviation;
                const specificationAbbreviation = printInfo?.printAbbreviation?.specificationAbbreviation || productSpecAbbreviation?.printAbbreviation?.specificationAbbreviation;
                return `(${index + 1}) ${it.isGift ? '[赠品] ' : ''}${productAbbreviation || it.goodsName} ${specificationAbbreviation || it.goodsSpec || ''}  【${it.goodsCount}件】`;
            }).join('；') || '';
        }
        return customAreaStr;
    }

    /**
     * @desc 根据 发货单打印配置 处理数据，不打印的数据置为null
     * <AUTHOR> Yang
     * @date 2024-06-28 10:58:03
    */
    static handleShipPrintData(shipmentPrintData, shipPrintConfig) {
        if (!shipPrintConfig || !shipmentPrintData) return;
        const {
            mallName,
            receiverAddressEnable,
            receiverNameEnable,
            receiverPhoneEnable,
            sellerAddress,
            sellerPhone,
        } = shipPrintConfig;
        Object.assign(shipmentPrintData, {
            mallName,
            receiverAddressEnable,
            receiverNameEnable,
            receiverPhoneEnable,
            sellerAddress,
            sellerPhone,
        });
    }

    static async print(task, options) {
        const {
            templateInfo,
            printConfig,
        } = task;

        const {
            shipPrintConfig,
        } = options;

        const {
            expressPrinter,
            alternatelyPrint,
            printShipment,
            shipmentPrinter,
            shipmentTemplate,
        } = printConfig || {};


        if (expressPrinter) {

            if (task.ecType === ECTypeEnum.PDD) {
                let waybillInfo = {};
                if (task.waybillInfo) {
                    waybillInfo = task.waybillInfo;
                } else {
                    const {
                        id,
                    } = templateInfo;
                    if (!id) {
                        console.error('pdd打单需要传模板id');
                        return;
                    }
                    const res = await ECOrderAPI.batchGetOrderWaybill({
                        waybillInfos: [
                            {
                                ecType: task.ecType,
                                templateId: id,
                                orderIds: [task.orderInfo.id],
                                waybillType: task.waybillType,
                            },
                        ],
                    });
                    waybillInfo = res?.rows[0];
                }

                await new PDDPrint().print({
                    printer: expressPrinter || '',
                    customAreaStr: this.getCustomAreaStr(task),
                    ...waybillInfo,
                });
            }

        }

        // 同时打印发货单 || 指定打印发货单
        if (alternatelyPrint || printShipment) {
            const printerList = PrintManager.getInstance().getPrinterList() || [];
            const printerName = alternatelyPrint ? expressPrinter : shipmentPrinter;
            const curPrinter = printerList.find((item) => {
                return item.name === printerName;
            });
            let templateKey = 'orderCloudShipmentA4a5';
            let orient = 2;
            let pageSize = shipmentTemplate;
            if (alternatelyPrint) {
                pageSize = '76';
            }
            if (pageSize === '76') {
                templateKey = 'orderCloudShipment76';
                orient = 1;
            }
            const res = await ECOrderAPI.fetchOrderPrintInfo({
                orderIds: [ task.orderInfo.id ],
            });
            const shipmentPrintData = res.rows[0];
            ExpressPrintManage.handleShipPrintData(shipmentPrintData, shipPrintConfig);
            await AbcPrinter.abcPrint({
                templateKey: window.AbcPackages.AbcTemplates[templateKey],
                printConfig: Object.assign({}, OrderCloudPrinterConfig.orderCloud, {
                    deviceIndex: curPrinter?.deviceIndex,
                    deviceName: printerName,
                    orient,
                    pageSize,
                }),
                data: shipmentPrintData,
            });
        }

    }

}
