import MeituanCrawler from './meituan-crawler.js';
import RPAConfig from './rpa-config';
import BaseLogger from 'MfBase/logger';
// 美团订单爬虫
// 这儿因为处方的审方后会产生新订单，所以刚好可以去同步处方审核
export default class MTOrderPrescriptionCrawler extends MeituanCrawler {
    orderList = [];
    prescriptionList = [];
    isSyncing = false;
    orderSummary = {
        totalOrderCount: 0,
        totalOrderSum: 0,
        unauditCount: 0, // 未审核处方订单
        preOrderCount: 0, // 今日预订单
        nearPreOrderCount: 0, // 到期预约单
        exceptionOrderCount: 0, // 异常配送
        reminderOrderCount: 0, // 用户催单
        refundOrderCount: 0, // 用户申请退款
        compensateOrderCount: 0, // 货损赔付
    };

    newOrderSymbol = Symbol('new_order');
    orderListSymbol = Symbol('order_list');

    constructor(accountId, authMallCookies) {
        super(accountId, authMallCookies);
        this.orderList = [];
    }



    getOrderList() {
        return this.orderList;
    }

    getOrderSummary() {
        return this.orderSummary;
    }

    // 添加 orderList change listener
    addOrderListListener(listener) {
        this.orderListListener = listener;
    }

    // 添加 orderSummary change listener
    addOrderSummaryListener(listener) {
        this.orderSummaryListener = listener;
    }

    // 添加处方 change listener
    addPrescriptionListener(listener) {
        this.prescriptionListener = listener;
    }

    addOrderNewListener(listener) {
        this.orderNewListener = listener;
    }

    addSyncIsDoneListener(listener) {
        this.syncIsDoneListener = listener;
    }

    addSyncIsStartListener(listener) {
        this.syncIsStartListener = listener;
    }

    static getTargetStruct(order) {
        if (!order) return {};
        const {
            commonInfo,
            orderInfo,
        } = order;
        try {
            const commonInfoObj = JSON.parse(commonInfo);
            const orderInfoObj = JSON.parse(orderInfo);
            return {
                orderNo: commonInfoObj?.wm_poi_order_dayseq,
                orderViewId: orderInfoObj?.unifiedBasicInfo?.wmOrderViewId,
                commonInfo: commonInfoObj,
                orderInfo: orderInfoObj,
            };
        } catch (e) {
            this.logger.error('Parse order info failed:', e);
            return null;
        }
    }

    addOrderList(orderList) {
        if (!orderList) return;
        const syncOrderList = [];
        orderList.forEach((order) => {
            const existOrder = this.orderList.find((o) => o.orderNo === order.orderNo);
            if (existOrder) {
                Object.assign(existOrder, order);
            } else {
                this.orderList.push(order);
                this.orderList.sort((a, b) => b.orderNo - a.orderNo);
                syncOrderList.push(order);
            }
        });

        if (this.orderListListener) {
            this.orderListListener(syncOrderList);
        }
    }

    syncOrderSummary(summary) {
        Object.assign(this.orderSummary, summary);

        if (this.orderSummaryListener) {
            this.orderSummaryListener(this.orderSummary);
        }
    }

    addPrescriptionList(prescriptionList) {
        if (!prescriptionList) return;
        prescriptionList.forEach((prescription) => {
            const existPrescription = this.prescriptionList.find((p) => p.orderViewId === prescription.orderViewId);
            if (existPrescription) {
                Object.assign(existPrescription, prescription);
            } else {
                this.prescriptionList.push(prescription);
            }
        });

        if (this.prescriptionListener) {
            this.prescriptionListener(prescriptionList);
        }
    }

    async scrapeOrderAndPrescription() {
        await this.preparePage();
        if (!this.page) throw new Error('Browser not initialized');
        await this.scrapeOrders();
        this.scrapeSummary();
    }

    async scrapeOrders() {

        this.logger.info('Scraping orders start...');
        this.isSyncing = true;
        this.syncIsStartListener && this.syncIsStartListener();
        // Set localStorage to avoid guide popups
        await this.prepareContext(this.page);
        // 同步此时历史订单中的所有订单，一进页面就会有拉取，所以等点击全部订单后再监听
        this.registerResponseHandler(this.orderListSymbol, this._handleOrderListResponse.bind(this));
        // 监听新订单，打开新page 监听其他订单数据
        this.registerResponseHandler(this.newOrderSymbol, this._handleNewOrderResponse.bind(this));
        // 再进入页面
        await this.page.goto(RPAConfig.Page.OrderHistory.url);

        await this.closeGlobalModal(this.page);

        //  判断当前的 url 是否是订单列表页面，首次进入页面可能发生了重定向，需要重新进入
        if (this.page.url() !== RPAConfig.Page.OrderHistory.url) {
            await this.page.goto(RPAConfig.Page.OrderHistory.url, { waitUntil: 'networkidle0' });
        }
    }

    async _handleOrderListResponse(url, response) {
        if (!url.includes(RPAConfig.WatchAPI.OrderList)) return;

        try {
            const { data } = await response.json();
            let {
                orderList = [],
            } = data || {};

            if (orderList.length === 0) {
                this.logger.info('无订单');
                this.listSyncDone();
                return;
            }
            orderList = orderList.map((it) => {
                return Object.assign(it, MTOrderPrescriptionCrawler.getTargetStruct(it));
            });
            this.addOrderList(orderList);

            if (orderList.length < 10) {
                this.logger.info('No more orders to crawl.');
                this.listSyncDone();
                return;
            }
        } catch (e) {
            this.logger.error('Parse order list response failed:', e);
        }

        try {
            const iframeElement = await this.page.waitForSelector(RPAConfig.DomSelector.HistoryOrder.IFrame);
            const iframe = await iframeElement.contentFrame();

            // 点击下一页
            const element = await iframe.$(RPAConfig.DomSelector.HistoryOrder.PaginationNextBtn);
            if (element) {
                await iframe.click(RPAConfig.DomSelector.HistoryOrder.PaginationNextBtn);
                await iframe.waitForTimeout(Math.floor(Math.random() * 1000) + 300); // 小延迟确保页面稳定
            } else {
                this.listSyncDone();
            }
        } catch (error) {
            this.logger.error('Error orders:', error);
            this.listSyncDone();
            throw error;
        }
    }

    listSyncDone() {
        BaseLogger.report({
            scene: 'scrape-mt-order',
            data: {
                scene: 'order-list-done',
            },
        });
        this.isSyncing = false;
        this.syncIsDoneListener && this.syncIsDoneListener();
        this.removeResponseHandler(this.orderListSymbol);
        this.startNewOrderHandler();
        this.syncTimer = setTimeout(() => {
            // 同步昨日订单
            this.syncYesterdayOrders();
            // 同步处方信息
            this.scrapePrescriptionDetail();
        }, 1000 * 60 * 10);
    }

    async _handleNewOrderResponse(url, response) {
        if (!url.includes(RPAConfig.WatchAPI.NewOrderV2)) return;
        this.logger.info('meituan 新订单推送 start');
        try {
            const { data } = await response.json();
            const order = MTOrderPrescriptionCrawler.getTargetStruct(data);
            this.addOrderList([order]);
            this.orderNewListener && this.orderNewListener(order);
            await this.syncPrescriptionDetail(order);
            this.logger.info('meituan 新订单推送 end', data);
        } catch (e) {
            this.logger.error('meituan 新订单推送 error', e);
        }
    }
    async startNewOrderHandler() {
        this.newOrderTimer = setInterval(() => {
            this.newOrderHandler();
        }, 1000 * 70);

        this.summaryTimer = setInterval(() => {
            this.scrapeSummary();
        }, 1000 * 60 * 5);
    }

    // 同步第一页最新订单
    async syncFirstPageOrder() {
        return new Promise((resolve, reject) => {
            const firstPageOrderSymbol = Symbol('firstPageOrder');
            async function _handleFirstPageOrderResponse(url, response) {
                if (!url.includes(RPAConfig.WatchAPI.OrderList)) return;
                try {
                    const { data } = await response.json();
                    let {
                        orderList,
                    } = data || {};
                    orderList = orderList.map((it) => {
                        return Object.assign(it, MTOrderPrescriptionCrawler.getTargetStruct(it));
                    });
                    const newOrders = [];
                    orderList.forEach((it) => {
                        newOrders.push(it);
                    });
                    this.addOrderList(newOrders);
                    console.log('meituan 获取订单页面第一页数据:', newOrders);

                    // for (const order of newOrders) {
                    //     await this.syncPrescriptionDetail(order);
                    // }
                    resolve();
                } catch (e) {
                    reject();
                    this.logger.error('Error first page order:', e);
                } finally {
                    this.removeResponseHandler(firstPageOrderSymbol);
                }
            }
            this.registerResponseHandler(firstPageOrderSymbol, _handleFirstPageOrderResponse.bind(this));
            if (this.page.url() === RPAConfig.Page.OrderHistory.url) {
                this.page.reload();
                console.log('meituan reload 订单页面获取第一页数据:');
            } else {
                this.page.goto(RPAConfig.Page.OrderHistory.url);
            }
        });
    }

    // 同步未审核处方订单数量
    async syncOrderUnauditCount(orderPage) {
        return new Promise((resolve, reject) => {
            const orderUnauditCountSymbol = Symbol('orderUnauditCountSymbol');
            orderPage.goto(RPAConfig.Page.PrescriptionList.url);
            async function _handleOrderUnauditCountResponse(url, response) {
                if (url.includes(RPAConfig.WatchAPI.PrescriptionPharmacistStatus)) {
                    // 没绑定药师，会请求这个接口
                    resolve();
                    return;
                }
                if (!url.includes(RPAConfig.WatchAPI.OrderUnauditCount)) return;
                try {
                    const { data } = await response.json();
                    this.logger.info('处方未审核数据:', data);
                    data && this.syncOrderSummary({
                        unauditCount: data?.unprocessedCount || 0,
                    });
                    resolve();
                } catch (e) {
                    reject();
                    console.error(e);
                } finally {
                    this.removeResponseHandler(orderUnauditCountSymbol);
                }
            }
            // 监听催单订单列表
            this.registerResponseHandler(orderUnauditCountSymbol, _handleOrderUnauditCountResponse.bind(this));
        });
    }

    // 同步首页订单提醒信息
    async syncOrderNoticeInfo(orderPage) {
        return new Promise((resolve, reject) => {
            const orderNoticeInfoSymbol = Symbol('orderNoticeInfoSymbol');
            orderPage.goto(RPAConfig.Page.HomePage.url);
            async function _handleOrderNoticeInfoResponse(url, response) {
                if (!url.includes(RPAConfig.WatchAPI.OrderNoticeInfo)) return;
                try {
                    const { data } = await response.json();
                    this.logger.info('首页订单提醒信息:', data);
                    const {
                        preOrderCount = 0, // 今日预订单
                        nearPreOrderCount = 0, // 到期预约单
                        exceptionOrderCount = 0, // 异常配送
                        reminderOrderCount = 0, // 用户催单
                        refundOrderCount = 0, // 用户申请退款
                        compensateOrdderCount = 0, // 货损赔付
                    } = data || {};
                    data && this.syncOrderSummary({
                        preOrderCount,
                        nearPreOrderCount,
                        exceptionOrderCount,
                        reminderOrderCount,
                        refundOrderCount,
                        compensateOrderCount: compensateOrdderCount,
                    });
                    resolve();
                } catch (e) {
                    reject();
                    console.error(e);
                } finally {
                    this.removeResponseHandler(orderNoticeInfoSymbol);
                }
            }
            // 监听催单订单列表
            this.registerResponseHandler(orderNoticeInfoSymbol, _handleOrderNoticeInfoResponse.bind(this));
        });
    }

    // 同步当天订单数量和金额
    async syncOrderCountAndSum(orderPage) {
        return new Promise((resolve, reject) => {
            const orderCountAndSumSymbol = Symbol('orderCountAndSumSymbol');
            orderPage.goto(RPAConfig.Page.TodayTodo.url);
            async function _handleOrderCountAndSumResponse(url, response) {
                if (!url.includes(RPAConfig.WatchAPI.OrderCountAndSum)) return;
                try {
                    const { data } = await response.json();
                    this.logger.info('当天订单数量和金额:', data);
                    const {
                        count,
                        sum,
                    } = data;

                    this.syncOrderSummary({
                        totalOrderCount: count,
                        totalOrderSum: sum,
                    });
                    resolve();
                } catch (e) {
                    reject();
                    console.error(e);
                } finally {
                    this.removeResponseHandler(orderCountAndSumSymbol);
                }
            }
            this.registerResponseHandler(orderCountAndSumSymbol, _handleOrderCountAndSumResponse.bind(this));
        });
    }

    // 新订单来了新开page同步特殊订单数量
    async newOrderHandler() {
        if (this._isOrderProcessing) return;
        this._isOrderProcessing = true;
        // 新单子来直接刷新 this.page
        try {
            await this.syncFirstPageOrder(this.page);
        } catch (err) {
            console.error('订单处理异常:', err);
        } finally {
            this._isOrderProcessing = false;
        }
    }

    // 同步昨日订单
    async syncYesterdayOrders() {
        const Page = await this.browser.newPage();
        this.initializePage(Page);

        const YesterdayOrdersSymbol = Symbol('YesterdayOrders');

        try {
            await Page.goto(RPAConfig.Page.OrderHistory.url);
            await this.closeGlobalModal(Page);

            const iframeElement = await Page.waitForSelector(RPAConfig.DomSelector.HistoryOrder.IFrame);
            const iframe = await iframeElement.contentFrame();

            // 点击昨天
            await iframe.waitForSelector(RPAConfig.DomSelector.HistoryOrder.YesterdayTab);
            await iframe.click(RPAConfig.DomSelector.HistoryOrder.YesterdayTab);
        } catch (e) {
            console.error(e);
            Page.close();
            return;
        }

        async function _handleYesterdayOrderListResponse(url, response) {
            if (!url.includes(RPAConfig.WatchAPI.OrderList)) return;

            try {
                const { data } = await response.json();
                let {
                    orderList = [],
                } = data || {};

                if (orderList.length === 0) {
                    this.logger.info('无昨日订单');
                    this.removeResponseHandler(YesterdayOrdersSymbol);
                    Page.close();
                    return;
                }
                orderList = orderList.map((it) => {
                    return Object.assign(it, MTOrderPrescriptionCrawler.getTargetStruct(it));
                });
                this.addOrderList(orderList);

                if (orderList.length < 10) {
                    this.logger.info('没有更多 昨日订单');
                    this.removeResponseHandler(YesterdayOrdersSymbol);
                    Page.close();
                    return;
                }
            } catch (e) {
                this.logger.error('Parse order list response failed:', e);
                this.removeResponseHandler(YesterdayOrdersSymbol);
                Page.close();
            }

            try {
                const iframeElement = await Page.waitForSelector(RPAConfig.DomSelector.HistoryOrder.IFrame);
                const iframe = await iframeElement.contentFrame();

                // 点击下一页
                const element = await iframe.$(RPAConfig.DomSelector.HistoryOrder.PaginationNextBtn);
                if (element) {
                    await iframe.click(RPAConfig.DomSelector.HistoryOrder.PaginationNextBtn);
                    await iframe.waitForTimeout(Math.floor(Math.random() * 1000) + 300); // 小延迟确保页面稳定
                } else {
                    this.removeResponseHandler(YesterdayOrdersSymbol);
                    Page.close();
                }
            } catch (error) {
                this.logger.error('同步昨日订单出错', error);
                this.removeResponseHandler(YesterdayOrdersSymbol);
                Page.close();
                throw error;
            }
        }
        // 点击昨日tab后注册监听
        this.registerResponseHandler(YesterdayOrdersSymbol, _handleYesterdayOrderListResponse.bind(this));
    }

    async scrapeSummary() {
        // 统计信息拉取
        if (this._isSummaryProcessing) return;
        this._isSummaryProcessing = true;
        try {
            if (!this._countPage || this._countPage.isClosed()) {
                this._countPage = await this.browser.newPage();
                this.initializePage(this._countPage);
            }
            await this.prepareContext(this._countPage);
            await this.syncOrderCountAndSum(this._countPage);
            await this.syncOrderNoticeInfo(this._countPage);
            await this.syncOrderUnauditCount(this._countPage);
        } catch (err) {
            console.error('统计信息处理异常:', err);
        } finally {
            this._isSummaryProcessing = false;
            await this._countPage.close();
            this._countPage = null;
        }
    }

    // 点击指定订单号的查看详情链接
    async scrapePrescriptionDetail() {
        const _hasPROrderList = this.orderList.filter((order) => {
            const {
                orderInfo,
            } = order;
            return orderInfo?.unifiedUserInfo?.rxOrder;
        });
        for (const order of _hasPROrderList) {
            await this.syncPrescriptionDetail(order);
        }
    }

    // 同步该订单的处方详情
    async syncPrescriptionDetail(order) {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve, reject) => {

            const {
                orderInfo,
                orderNo,
            } = order;
            if (!orderInfo?.unifiedUserInfo?.rxOrder) {
                resolve();
                return;
            }
            const _prescriptionPage = await this.browser.newPage();
            this.initializePage(_prescriptionPage);

            // 跳转去处方列表
            _prescriptionPage.goto(RPAConfig.Page.PrescriptionList.url);
            const prescriptionDetailSymbol = Symbol('prescriptionDetailSymbol');
            const iframeElement = await _prescriptionPage.waitForSelector(RPAConfig.DomSelector.HistoryOrder.IFrame);
            const iframe = await iframeElement.contentFrame();

            async function _handlePrescriptionDetailResponse(url, response) {
                if (!url.includes(RPAConfig.WatchAPI.PrescriptionDetail)) return;
                try {
                    const { data } = await response.json();
                    this.logger.info('同步订单关联的处方详情:', data);
                    data && this.addPrescriptionList([data]);
                    this.removeResponseHandler(prescriptionDetailSymbol);
                    _prescriptionPage.close();
                } catch (e) {
                    reject();
                    console.error(e);
                } finally {
                    this.removeResponseHandler(prescriptionDetailSymbol);
                    _prescriptionPage.close();
                }
                resolve();
            }
            // 监听处方详情
            this.registerResponseHandler(prescriptionDetailSymbol, _handlePrescriptionDetailResponse.bind(this));
            try {
                await iframe.waitForSelector(RPAConfig.DomSelector.PrescriptionList.Tabs);
                await iframe.click(RPAConfig.DomSelector.PrescriptionList.AllTab);
                // 等待点击后的页面加载
                await iframe.waitForTimeout(1000);
                // 等待表格加载完成
                await iframe.waitForSelector(RPAConfig.DomSelector.PrescriptionList.PRTable);
                const viewDetailBtn = await iframe.waitForXPath(`//div[@class="boo-table-fixed-body"]//tr[.//span[text()="${order.orderViewId}"]]//span[text()="查看详情"]`);
                // 在表格中找到对应订单号的行，并点击其中的查看详情链接
                if (viewDetailBtn) {
                    viewDetailBtn.click();
                } else {
                    resolve();
                    console.warn(`未找到订单号为 ${orderNo} 的订单详情链接`);
                    this.removeResponseHandler(prescriptionDetailSymbol);
                    _prescriptionPage?.close();
                }
            } catch (e) {
                reject(e);
                this.removeResponseHandler(prescriptionDetailSymbol);
                _prescriptionPage?.close();
            }
        });
    }

    stopSync() {
        this.isSyncing = false;
        this.browser?.close();
        clearInterval(this.newOrderTimer);
        clearInterval(this.summaryTimer);
    }
}
