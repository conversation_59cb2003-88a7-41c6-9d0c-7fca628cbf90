import {
    MtClientUA,
    MtCookies,
} from './constants';
import RPAConfig from './rpa-config';
import { sleep } from '../../common/utils';
import Response from '../../common/Response';
import { ResponseErrorCode } from '../../common/constants';
import BaseCrawler from '../../base/base-crawler';
import MeituanAuthManager from './auth-manager';
import Logger from '../../../../utils/logger';
import MeituanCrawlerSteps from './crawler-steps';
const puppeteer = window.remote?.require?.('puppeteer') || {};
const logger = Logger.create('MeituanCrawler');
import { ToastFunc as Toast } from '@abc/ui-pc';
import BaseLogger from 'MfBase/logger';
import ECStockRecordAPI from '../../../../api/stock-record';

window.MockOrderCloudCookies = function () {
    MeituanAuthManager.getInstance().addAccount('mt353783qf', {
        accountInfo: {
            id: 'mt353783qf',
        },
        cookies: MtCookies,
    });
    console.log('MockOrderCloudCookies', MeituanAuthManager.getInstance().getAccount('mt353783qf'));
};

export default class MeituanCrawler extends BaseCrawler {
    browser = null;
    productBrowser = null;
    page = null;
    productPage = null;
    logger = null;
    syncingProductStock = false;
    productPageReadying = false;
    syncStep = 1;
    /**
     * @type {MeituanAuthManager}
     */

    authManager = null;
    /**
     * @type {string[]}
     */
    authMallCookies = null;
    orderInfoSymbol = Symbol('order_info');
    constructor(accountId, authMallCookies, mallId) {
        super();

        this.accountId = accountId;
        this.authMallCookies = authMallCookies;
        this.mallId = mallId;
        this.authManager = MeituanAuthManager.getInstance();
        this.logger = logger;
    }

    setAccountId(accountId) {
        this.accountId = accountId;
    }

    async executeTask(task) {
        const { type } = task;
        switch (type) {
            case 'checkAuth':
                return this.checkAuth();
            case 'requestAuth':
                return this.requestAuth();
            case 'scrapeProductsSummary':
                await this.preparePage();
                return this.scrapeProductsSummary(task);
            case 'scrapeProducts':
                await this.preparePage();
                return this.scrapeProducts(task);
            case 'scrapeProductDetail':
                await this.preparePage();
                return this.scrapeProductDetail(task);
            case 'scrapeProductsBySkuIds':
                await this.preparePage();
                return this.scrapeProductsBySkuIds(task);
            case 'syncProductStockBySkuId':
                await this.prepareProductPage();
                return this.syncProductStockBySkuId();
            default:
                return Response.error('Invalid task type', { code: ResponseErrorCode.INVALID_TASK_TYPE });
        }
    }

    async getAccountInfo(page) {
        // 打开登录页面
        await page.goto(RPAConfig.Page.Login.url, { waitUntil: 'networkidle0' });
        // 需要在进home界面前注册
        const loginInfoPromise = this.waitForLoginInfo(page);
        const data = await loginInfoPromise;
        if (!data) {
            return null;
        }

        return {
            id: data.acctName,
            accountName: data.acctName,
            mobile: data.boundPhoneNum,
            wmPoiName: data.wmPoiName,
            wmPoiStatus: data.wmPoiStatus,
            wmPoiStatusDesc: data.wmPoiStatusDesc,
            headImgUrl: data.wmPoiData?.logoUrl ? `http:${data.wmPoiData?.logoUrl}` : '',
        };
    }

    async waitForLoginInfo() {
        return new Promise((resolve) => {
            // 登录获取用户信息的接口调用
            const loginInfo = {};
            let homeRequestDone = false;
            let poiRequestDone = false;

            const handleLoginResponse = async (url, response) => {
                if (url.includes(RPAConfig.WatchAPI.Home)) {
                    try {
                        const data = await response.json();
                        Object.assign(loginInfo, data?.data);
                        homeRequestDone = true;
                    } catch (e) {
                        this.logger.error('Parse home response failed:', e);
                    }
                } else if (url.includes(RPAConfig.WatchAPI.PoiInfo)) {
                    try {
                        const data = await response.json();
                        loginInfo.wmPoiData = data?.data?.wmPoiData;
                        poiRequestDone = true;
                    } catch (e) {
                        this.logger.error('Parse poi info response failed:', e);
                    }
                }

                if (homeRequestDone && poiRequestDone) {
                    this.removeResponseHandler('loginInfo');
                    resolve(loginInfo);
                }
            };

            this.registerResponseHandler('loginInfo', handleLoginResponse);
        });
    }

    /**
     * 等待 ProductSearchList 接口响应且 searchWord/skuid 匹配
     * @param {String} skuId
     * @returns {Promise<Response>}
     */
    async waitForSearchResponse(skuId) {
        const that = this;
        return new Promise((resolve, reject) => {
            const pageKey = Symbol('searchBySkuIdResponse');
            let timeoutId;

            async function _responseHandler(url, response) {
                if (!url.includes(RPAConfig.WatchAPI.ProductSearchList)) return;
                try {
                    if (response.status() !== 200) return;
                    // 判断请求体中 searchWord 是否等于 skuId
                    const req = response.request();
                    const postData = req.postData();
                    let matched = false;
                    if (postData && postData.includes(skuId)) {
                        matched = true;
                    }
                    if (!matched) return;

                    // 清除超时定时器
                    clearTimeout(timeoutId);
                    resolve(response);
                } catch (e) {
                    // 清除超时定时器
                    clearTimeout(timeoutId);
                    that.logger.error('Parse search response failed:', e);
                    reject(e);
                } finally {
                    that.removeResponseHandler(pageKey);
                }
            }

            that.registerResponseHandler(pageKey, _responseHandler);

            // 设置超时定时器
            timeoutId = setTimeout(() => {
                that.removeResponseHandler(pageKey);
                reject(new Error(`搜索响应超时 skuId: ${skuId}`));
            }, 10000);
        });
    }

    /**
     * 请求绑定
     * @returns
     */
    async requestBind(onBrowsersClosed) {

        let loginPage = null;
        let destroyLoginPage = null;

        try {
            const {
                loginPage: _loginPage, destroyLoginPage: _destroyLoginPage,
            } = await this._createLoginPage({
                headless: false,
                onBrowsersClosed, // 传递回调函数
            });
            loginPage = _loginPage;
            destroyLoginPage = _destroyLoginPage;
        } catch (error) {
            this.logger.error('requestAuth _createLoginPage error', error);
            return Response.error('打开美团授权窗口失败', {
                code: ResponseErrorCode.REQUEST_AUTH_OPEN_WINDOW_FAILED,
                error,
            });
        }

        let cookies;
        let accountInfo = null;
        try {
            accountInfo = await this.getAccountInfo(loginPage);
            if (accountInfo) {
                cookies = await loginPage.cookies();
                this.logger.info('requestAuth await loginCheckResponse', accountInfo, cookies);
                return Response.success({
                    cookies,
                    accountInfo,
                });
            }
            return Response.error('请求授权失败', {
                code: ResponseErrorCode.REQUEST_AUTH_GET_USER_INFO_FAILED,
                error: {
                    message: '获取账号信息失败',
                    status: 400,
                },
            });

        } catch (error) {
            this.logger.info('requestAuth catch loginCheckResponse error', error.message);
            BaseLogger.report({
                scene: 'mt-auth-error',
                data: {
                    scene: 'bind',
                    error,
                },
            });
            if (error.message?.includes('detached')) {
                return Response.error('取消授权', {
                    code: ResponseErrorCode.REQUEST_AUTH_CANCEL,
                    error: {
                        ...error,
                        message: '取消授权',
                    },
                });
            }
            return Response.error('请求授权失败', {
                code: ResponseErrorCode.REQUEST_AUTH_GET_USER_INFO_FAILED,
                error,
            });
        } finally {
            destroyLoginPage();
        }
    }

    // 获取授权信息
    async getCheckAuthInfo(checkLoginPage) {
        return new Promise((resolve, reject) => {
            const checkAuthSymbol = Symbol('CheckAuth');
            async function _handleCheckAuthResponse(url, response) {
                if (!url.includes(RPAConfig.WatchAPI.Home)) return;
                try {
                    const res = await response.json();
                    const { data } = res;
                    this.logger.info('CheckAuthInfo',res);
                    resolve({
                        id: data.acctName,
                        accountName: data.acctName,
                        mobile: data.boundPhoneNum,
                        wmPoiName: data.wmPoiName,
                        wmPoiStatus: data.wmPoiStatus,
                        wmPoiStatusDesc: data.wmPoiStatusDesc,
                    });
                } catch (e) {
                    reject(e);
                } finally {
                    this.removeResponseHandler(checkAuthSymbol);
                }
            }
            this.registerResponseHandler(checkAuthSymbol, _handleCheckAuthResponse.bind(this));

            checkLoginPage.goto(RPAConfig.Page.HomePage.url);
        });
    }

    /**
     * 检查授权
     */
    async checkAuth() {
        this.logger.debug('checkAuth start');

        if (!this.authMallCookies?.length) {
            return Response.error('未授权', {
                code: ResponseErrorCode.AUTH_NOT_AUTHED,
            });
        }

        // 发起请求，检查是否过期
        const {
            loginPage: checkLoginPage,
            destroyLoginPage: destroyCheckLoginPage,
        } = await this._createLoginPage({
            headless: true,
        });

        let isAuth = false;
        let accountInfo;

        // 存在cookie，直接使用
        await checkLoginPage.setCookie(...this.authMallCookies);

        // 检查是否已登录
        try {
            const checkAuthInfo = await this.getCheckAuthInfo(checkLoginPage);
            accountInfo = {
                id: checkAuthInfo.acctName,
                accountName: checkAuthInfo.acctName,
                mobile: checkAuthInfo.boundPhoneNum,
                wmPoiName: checkAuthInfo.wmPoiName,
                wmPoiStatus: checkAuthInfo.wmPoiStatus,
                wmPoiStatusDesc: checkAuthInfo.wmPoiStatusDesc,
            };
            isAuth = true;
            this.logger.info('checkAuth fetchAccountInfo', checkAuthInfo);
        } catch (error) {
            this.logger.error('checkAuth failed fetchAccountInfo', error);
            BaseLogger.report({
                scene: 'mt-auth-error',
                data: {
                    scene: 'checkAuth',
                    error,
                },
            });
            Toast({
                message: '获取美团授权信息失败，请在设置中授权续期',
                type: 'error',
                duration: 2000,
            });
        }
        await destroyCheckLoginPage();
        if (isAuth) {
            return Response.success({
                accountInfo,
                cookies: this.authMallCookies,
            });
        }
        this.logger.info('checkAuth failed');
        return Response.error('获取授权信息失败', {
            code: ResponseErrorCode.REQUEST_AUTH_GET_USER_INFO_FAILED,
        });
    }

    /**
     * 请求授权
     * @returns
     */
    async requestAuth(onBrowsersClosed) {
        const checkAuthResponse = await this.checkAuth();
        if (checkAuthResponse.status === true) {
            this.logger.info('requestAuth 已经授权');
            return checkAuthResponse;
        }

        this.logger.info('requestAuth start');


        let loginPage = null;
        let destroyLoginPage = null;

        try {
            const {
                loginPage: _loginPage, destroyLoginPage: _destroyLoginPage,
            } = await this._createLoginPage({
                headless: false,
                onBrowsersClosed,
            });
            loginPage = _loginPage;
            destroyLoginPage = _destroyLoginPage;
        } catch (error) {
            this.logger.error('requestAuth _createLoginPage error', error);
            return Response.error('打开美团授权窗口失败', {
                code: ResponseErrorCode.REQUEST_AUTH_OPEN_WINDOW_FAILED,
                error,
            });
        }

        try {
            const accountInfo = await this.getAccountInfo(loginPage);

            if (!accountInfo) {
                this.logger.info('requestAuth await loginCheckResponse error');
                return Response.error('请求授权失败', {
                    code: ResponseErrorCode.REQUEST_AUTH_GET_USER_INFO_FAILED,
                    error: {
                        message: '获取账号信息失败',
                        status: 400,
                    },
                });
            }
            const cookies = await loginPage.cookies();

            return Response.success({
                cookies,
                accountInfo,
            });
        } catch (error) {
            this.logger.info('requestAuth catch loginCheckResponse error', error.message);
            BaseLogger.report({
                scene: 'mt-auth-error',
                data: {
                    scene: 'auth',
                    error,
                },
            });
            if (error.message?.includes('detached')) {
                return Response.error('取消授权', {
                    code: ResponseErrorCode.REQUEST_AUTH_CANCEL,
                    error: {
                        ...error,
                        message: '取消授权',
                    },
                });
            }
            return Response.error('请求授权失败', {
                code: ResponseErrorCode.REQUEST_AUTH_GET_USER_INFO_FAILED,
                error,
            });
        } finally {
            await destroyLoginPage();
        }

    }

    async preparePage() {
        if (!this.authMallCookies?.length) {
            return Response.error('未授权', {
                code: ResponseErrorCode.AUTH_NOT_AUTHED,
            });
        }
        await this._prepareRunnerPage();
        return Response.success({
            isAuth: true,
            cookies: this.authMallCookies,
        });
    }

    async prepareProductPage() {
        if (this.syncingProductStock || this.productPageReadying) return;
        this.productPageReadying = true;
        if (!this.authMallCookies?.length) {
            return Response.error('未授权', {
                code: ResponseErrorCode.AUTH_NOT_AUTHED,
            });
        }
        await this._prepareRunnerProductPage(this.authMallCookies);
        this.productPageReadying = false;
        return Response.success({
            isAuth: true,
            cookies: this.authMallCookies,
        });
    }

    async scrapeProductsSummary(task) {
        if (!this.browser) throw new Error('Browser not initialized');

        const scrapedPage = await this.browser.newPage();
        scrapedPage.setViewport({
            width: 1200, height: 600,
        });
        // Set localStorage to avoid guide popups
        await this.prepareContext(scrapedPage);
        this.initializePage(scrapedPage);

        this.logger.info('Scraping products summary start...');

        // 导航到商品列表页面
        await scrapedPage.goto(RPAConfig.Page.ProductList.url, {
            waitUntil: 'networkidle0',
            timeout: 120000,
        });

        await this.closeGlobalModal(scrapedPage);

        await scrapedPage.waitForNetworkIdle({ idleTime: 500 });

        const iframeElement = await scrapedPage.waitForSelector(RPAConfig.DomSelector.ProductList.IFrame);
        const iframe = await iframeElement.contentFrame();

        await this.closeConfirmationDialogModal(iframe);

        try {
            // this.logger.info('scrapeProductsSummary 筛选条件');
            // await MeituanCrawlerSteps.clickExpandButton(scrapedPage, iframe);
            //
            // const responsePromise = new Promise((resolve) => {
            //     const responseHandler = async (url, response) => {
            //         if (!url.includes(RPAConfig.WatchAPI.ProductSearchList)) return;
            //         this.removeResponseHandler('searchListPageV2');
            //         resolve(response);
            //     };
            //     this.registerResponseHandler('searchListPageV2', responseHandler);
            // });
            //
            // this.logger.info('scrapeProductsSummary 点击查询');
            // await MeituanCrawlerSteps.iframeClickSearch(scrapedPage, iframe);
            //
            // await responsePromise;

            await iframe.waitForSelector(RPAConfig.DomSelector.ProductList.ProductTotal);
            const totalElement = await iframe.$(RPAConfig.DomSelector.ProductList.ProductTotal);
            this.logger.info(`Scraping products summary total...${!!totalElement}`);
            if (totalElement) {
                this.logger.info('Scraping products summary total...');
                const propertyHandle = await totalElement.getProperty('innerText');
                const totalStr = await propertyHandle.jsonValue();
                const total = parseInt(totalStr.replace(/[^\d]/g, '') || 0);
                this.logger.info(`Scraping products summary total...${total}`);
                if (task.strategy?.callback) {
                    task.strategy.callback(total);
                }
                return Response.success();
            }
        } catch (error) {
            if (task.strategy?.callback) {
                task.strategy.callback(0);
            }
            return Response.error(error);
        } finally {
            if (scrapedPage) {
                await scrapedPage.close();
            }
        }
    }

    async scrapeProducts(task) {
        if (!this.browser) throw new Error('Browser not initialized');

        const scrapedPage = await this.browser.newPage();
        scrapedPage.setViewport({
            width: 1200, height: 600,
        });
        // Set localStorage to avoid guide popups
        await this.prepareContext(scrapedPage);
        this.initializePage(scrapedPage);

        this.logger.info('Scraping products start...');
        BaseLogger.report({
            scene: 'scrape-mt-product',
            data: {
                scene: 'start',
            },
        });

        // 导航到商品列表页面
        await scrapedPage.goto(RPAConfig.Page.ProductList.url, {
            waitUntil: 'networkidle0',
            timeout: 120000,
        });

        await this.closeGlobalModal(scrapedPage);

        await scrapedPage.waitForNetworkIdle({ idleTime: 500 });

        const iframeElement = await scrapedPage.waitForSelector(RPAConfig.DomSelector.ProductList.IFrame);
        const iframe = await iframeElement.contentFrame();

        await this.closeConfirmationDialogModal(iframe);

        if (task.extraParams?.onlyOnSale) {
            await MeituanCrawlerSteps.clickProductOnSaleTab(scrapedPage, iframe);
        }

        // 等待分页器的选择框可用
        await iframe.waitForSelector(RPAConfig.DomSelector.ProductList.PageSizeDropdown);

        // 在切换分页前等待响应
        const firstResponsePromise = new Promise((resolve) => {
            const responseHandler = async (url, response) => {
                if (!url.includes(RPAConfig.WatchAPI.ProductSearchList)) return;
                this.removeResponseHandler('searchListPageV2');
                resolve(response);
            };
            this.registerResponseHandler('searchListPageV2', responseHandler);
        });

        // 点击分页器选择框以展示选项
        await iframe.click(RPAConfig.DomSelector.ProductList.PageSizeDropdown);

        try {
            // 等待分页选项的显示
            await iframe.waitForSelector(RPAConfig.DomSelector.ProductList.PageSizePopup);

            // 选择 "100条/页"
            await iframe.click(RPAConfig.DomSelector.ProductList.PageSize100Option);
        } catch (error) {
            await iframe.click(RPAConfig.DomSelector.CrawlerSteps.SearchButton); // 找不到分页按钮，使用查询按钮触发一次接口拉取
            this.logger.error('Failed to select "100条/页" option', error);
        }

        if (task.extraParams?.isNeedScrapeTotal) {
            await iframe.waitForSelector(RPAConfig.DomSelector.ProductList.ProductTotal);
            const totalElement = await iframe.$(RPAConfig.DomSelector.ProductList.ProductTotal);
            this.logger.info(`Scraping products summary total...${!!totalElement}`);
            if (totalElement) {
                this.logger.info('Scraping products summary total...');
                const propertyHandle = await totalElement.getProperty('innerText');
                const totalStr = await propertyHandle.jsonValue();
                const total = parseInt(totalStr.replace(/[^\d]/g, '') || 0);
                this.logger.info(`Scraping products summary total...${total}`);
                if (task.strategy?.callback) {
                    task.strategy.callback([], [], total);
                }
            }
        }

        const products = [];
        let allTags = []; // Array to collect all tags from all pages

        try {
            let hasNextPage = true;
            let pageNum = 1;
            let currentResponse = firstResponsePromise;


            while (hasNextPage) {
                this.logger.info(`Processing page ${pageNum}...`);
                BaseLogger.report({
                    scene: 'scrape-mt-product',
                    data: {
                        scene: 'scrape',
                        page: pageNum,
                    },
                });

                // 等待并获取当前页面的响应
                const productResponse = await currentResponse;
                const jsonResponse = await productResponse.json();

                if (!jsonResponse || !jsonResponse.data || !jsonResponse.data.productList) {
                    this.logger.warn(`Page ${pageNum} processed: No products found`);
                } else {
                    this.logger.info(`Page ${pageNum} processed: ${jsonResponse.data.productList.length} products`);

                    // 收集当前页面的标签列表
                    if (jsonResponse.data.tagList && jsonResponse.data.tagList.length > 0) {
                        this.logger.info(`Found ${jsonResponse.data.tagList.length} tags on page ${pageNum}`);
                        // 将当前页的标签添加到总标签列表中,每一页的标签是一样的，不能重复添加，但又必要带过去
                        allTags = jsonResponse.data.tagList;
                    }
                    const currentPageProducts = jsonResponse.data.productList.map((product) => {
                        // 获取商品规格信息
                        const sku = product.wmProductSkus?.[0] || {};

                        // 处理商品属性
                        const attrs = {};
                        if (product.wmProductSpuExtends) {
                            Object.values(product.wmProductSpuExtends).forEach((attr) => {
                                attrs[attr.name] = attr.value;
                            });
                        }

                        return {
                            id: product.id,
                            name: product.name,
                            price: sku.price || 0,
                            stock: sku.stock || 0,
                            spec: sku.spec || '',
                            upcCode: sku.upcCode || product.upcCode || '',
                            skuId: sku.id || '',
                            categoryId: product.categoryId,
                            categoryName: product.categoryName,
                            picture: product.picture,
                            sellStatus: product.sellStatus,
                            sellCount: product.sellCount,
                            brandName: product.brandName,
                            description: product.description,
                            attributes: attrs,
                            wmProductSkus: product.wmProductSkus,
                            combinationLabel: product.combinationLabel,
                            tagList: product.tagList,
                            wmProductSpuExtends: product.wmProductSpuExtends,
                            categoryNamePath: product.categoryNamePath,
                            categoryIdPath: product.categoryIdPath,
                            extSourceFoodCode: sku.sourceFoodCode,
                        };
                    });

                    if (task.strategy?.callback) {
                        task.strategy.callback(currentPageProducts, allTags);
                    }

                    products.push(...currentPageProducts);
                    this.logger.info(`Page ${pageNum} processed: ${currentPageProducts.length} products`);
                }

                // 检查是否有下一页
                const nextPageButton = await iframe.$(RPAConfig.DomSelector.ProductList.NextPageButton);
                const isNextPageButtonDisabled = nextPageButton ? nextPageButton.classList?.contains('disabled') : true;

                if (isNextPageButtonDisabled) {
                    hasNextPage = false;
                    break;
                }

                // 在点击下一页之前设置下一个响应的 Promise
                currentResponse = new Promise((resolve) => {
                    const responseHandler = async (url, response) => {
                        if (!url.includes(RPAConfig.WatchAPI.ProductSearchList)) return;
                        this.removeResponseHandler('response');
                        resolve(response);
                    };
                    this.registerResponseHandler('response', responseHandler);
                });

                // 点击下一页
                await nextPageButton.click();

                await sleep(Math.floor(Math.random() * 1000) + 500); // 随机延迟以避免被检测
                pageNum++;
            }

            this.logger.info(`Product scraping completed: ${products.length} products scraped`);
            BaseLogger.report({
                scene: 'scrape-mt-product',
                data: {
                    scene: 'success',
                    number: products.length,
                },
            });

            return Response.success({
                products,
                tags: allTags,
            });
        } catch (error) {
            this.logger.error('Product scraping error', error);
            if (allTags.length > 0 && products.length > 0) {
                BaseLogger.report({
                    scene: 'scrape-mt-product',
                    data: {
                        scene: 'success',
                        number: products.length,
                    },
                });
                return Response.success({
                    products,
                    tags: allTags,
                });
            }

            BaseLogger.report({
                scene: 'scrape-mt-product',
                data: {
                    scene: 'error',
                    error,
                },
            });
            throw error;
        } finally {
            if (scrapedPage) {
                await scrapedPage.close();
            }
        }
    }

    async scrapeProductsBySkuIds(task) {
        if (!this.browser) throw new Error('Browser not initialized');

        const scrapedPage = await this.browser.newPage();
        scrapedPage.setViewport({
            width: 1200, height: 600,
        });
        // Set localStorage to avoid guide popups
        await this.prepareContext(scrapedPage);
        this.initializePage(scrapedPage);

        this.logger.info('Scraping products start...');

        // 导航到商品列表页面
        await scrapedPage.goto(RPAConfig.Page.ProductList.url, {
            waitUntil: 'networkidle0',
            timeout: 120000,
        });

        await this.closeGlobalModal(scrapedPage);

        await scrapedPage.waitForNetworkIdle({ idleTime: 500 });

        const iframeElement = await scrapedPage.waitForSelector(RPAConfig.DomSelector.ProductList.IFrame);
        const iframe = await iframeElement.contentFrame();

        await this.closeConfirmationDialogModal(iframe);

        await MeituanCrawlerSteps.iframeClickSearchSkuId(scrapedPage, iframe);
        this.logger.info('syncProductStockBySkuId 点击下拉框打开选项');

        const products = [];

        const responsePromise = new Promise((resolve) => {
            const responseHandler = async (url, response) => {
                if (!url.includes(RPAConfig.WatchAPI.ProductSearchList)) return;
                this.removeResponseHandler('searchListPageV2');
                resolve(response);
            };
            this.registerResponseHandler('searchListPageV2', responseHandler);
        });

        try {
            let currentResponse = responsePromise;
            const skuIdList = task.data.skuIds;

            do {
                try {
                    const skuId = skuIdList.shift();

                    await MeituanCrawlerSteps.iframeTypeSkuId(scrapedPage, iframe, skuId);
                    this.logger.info(`syncProductStockBySkuId 输入skuId-${skuId}`);

                    await MeituanCrawlerSteps.iframeClickSearch(scrapedPage, iframe);
                    this.logger.info('syncProductStockBySkuId 点击查询');

                    // 等待并获取当前页面的响应
                    const productResponse = await currentResponse;
                    const jsonResponse = await productResponse.json();

                    if (!jsonResponse || !jsonResponse.data || !jsonResponse.data.productList) {
                        this.logger.warn(`skuId ${skuId} processed: No products found`);
                    } else {
                        this.logger.info(`skuId ${skuId} processed: ${jsonResponse.data.productList.length} products`);

                        const currentPageProducts = jsonResponse.data.productList.map((product) => {
                        // 获取商品规格信息
                            const sku = product.wmProductSkus?.[0] || {};

                            // 处理商品属性
                            const attrs = {};
                            if (product.wmProductSpuExtends) {
                                Object.values(product.wmProductSpuExtends).forEach((attr) => {
                                    attrs[attr.name] = attr.value;
                                });
                            }

                            return {
                                id: product.id,
                                name: product.name,
                                price: sku.price || 0,
                                stock: sku.stock || 0,
                                spec: sku.spec || '',
                                upcCode: sku.upcCode || product.upcCode || '',
                                skuId: sku.id || '',
                                categoryId: product.categoryId,
                                categoryName: product.categoryName,
                                picture: product.picture,
                                sellStatus: product.sellStatus,
                                sellCount: product.sellCount,
                                brandName: product.brandName,
                                description: product.description,
                                attributes: attrs,
                                wmProductSkus: product.wmProductSkus,
                                combinationLabel: product.combinationLabel,
                                tagList: product.tagList,
                                wmProductSpuExtends: product.wmProductSpuExtends,
                                categoryNamePath: product.categoryNamePath,
                                categoryIdPath: product.categoryIdPath,
                                extSourceFoodCode: sku.sourceFoodCode,
                            };
                        });

                        if (task.strategy?.callback) {
                            task.strategy.callback(currentPageProducts);
                        }

                        products.push(...currentPageProducts);
                        this.logger.info(`skuId ${skuId} processed: ${currentPageProducts.length} products`);
                    }

                    // 在点击下一页之前设置下一个响应的 Promise
                    currentResponse = new Promise((resolve) => {
                        const responseHandler = async (url, response) => {
                            if (!url.includes(RPAConfig.WatchAPI.ProductSearchList)) return;
                            this.removeResponseHandler('response');
                            resolve(response);
                        };
                        this.registerResponseHandler('response', responseHandler);
                    });

                    await sleep(Math.floor(Math.random() * 1000) + 500); // 随机延迟以避免被检测
                } catch (error) {
                    this.logger.info(`Error scrapeProductsBySkuIds: ${error}`);
                    break;
                }
            } while (skuIdList.length);

            this.logger.info(`Product scraping completed: ${products.length} products scraped`);

            return Response.success({ products });
        } catch (error) {
            this.logger.error('Product scraping error', error);
            if (products.length > 0) return Response.success({ products });
            throw error;
        } finally {
            if (scrapedPage) {
                await scrapedPage.close();
            }
        }
    }

    async syncProductStockBySkuId() {
        if (this.syncingProductStock) return;

        if (!this.productBrowser) throw new Error('productBrowser not initialized');
        if (!this.productPage) throw new Error('productPage not initialized');

        const uuid = localStorage.getItem('OrderCloudUUID');

        BaseLogger.report({
            scene: 'sync-mt-stock',
            data: { scene: 'openPage' },
        });

        try {
            this.syncingProductStock = true;
            this.logger.info('syncProductStockBySkuId start');

            const syncProductStockPage = this.productPage;
            // 设置 localStorge 必须在跳转页面之前
            await this.prepareContext(syncProductStockPage);
            this.initializePage(syncProductStockPage);

            if (syncProductStockPage.url() !== RPAConfig.Page.ProductList.url) {
                await syncProductStockPage.goto(RPAConfig.Page.ProductList.url, {
                    waitUntil: 'networkidle0',
                    timeout: 90000,
                });
            }

            // 使用封装的方法初始化页面环境
            const { iframe } = await this.initProductPageEnvironment(syncProductStockPage);

            let rows = [];
            let skuId = '';
            let stock = 0;
            let idList = [];
            do {
                try {
                    const randomDelay = Math.floor(Math.random() * 50000) + 40000; // 40000-90000ms 等待40秒 - 1分钟分半
                    await syncProductStockPage.waitForTimeout(randomDelay);

                    const res = await ECStockRecordAPI.getWaitSyncList({
                        mallId: this.mallId,
                        offset: 0,
                        limit: 50,
                    });
                    rows = res?.rows || [];
                    if (!rows.length) break;

                    const item = rows[0];
                    skuId = item.extSkuId;
                    stock = item.quantity;
                    idList = item.syncIds;

                    BaseLogger.report({
                        scene: 'sync-mt-stock',
                        data: {
                            scene: 'start',
                            uuid,
                            skuId,
                            stock,
                        },
                    });

                    this.syncStep = 1;
                    await MeituanCrawlerSteps.iframeTypeSkuId(syncProductStockPage, iframe, skuId);
                    this.logger.info(`syncProductStockBySkuId 输入skuId-${skuId}`);

                    this.syncStep = 2;
                    await this.waitForSearchResponse(skuId);

                    this.syncStep = 3;
                    await MeituanCrawlerSteps.clickProductEditSvgButton(syncProductStockPage, iframe);
                    this.logger.info(`syncProductStockBySkuId 点击编辑-${skuId}`);

                    this.syncStep = 4;
                    // 更新库存
                    const isUpdateStock = await MeituanCrawlerSteps.updateProductStock(syncProductStockPage, iframe, stock);

                    if (isUpdateStock) {
                        this.syncStep = 5;
                        this.logger.info(`syncProductStockBySkuId 输入新的库存值：${stock}`);
                        const updateResponse = new Promise((resolve) => {
                            const pageKey = Symbol('syncProductStockPage');
                            const responseHandler = async (url, response) => {
                                if (!url.includes(RPAConfig.WatchAPI.UpdateStock)) return;
                                this.removeResponseHandler(pageKey);
                                resolve(response);
                            };
                            this.registerResponseHandler(pageKey, responseHandler);
                        });
                        await updateResponse;
                    }
                    this.logger.info('syncStockBySkuId end');

                    await ECStockRecordAPI.updateWaitSyncFlag({
                        syncIds: idList,
                    });

                    BaseLogger.report({
                        scene: 'sync-mt-stock',
                        data: {
                            scene: 'success',
                            uuid,
                            skuId,
                            stock,
                            idList,
                            remain: rows.length,
                        },
                    });
                } catch (error) {
                    let errorMessage = '';
                    switch (this.syncStep) {
                        case 1:
                            errorMessage = '搜索条件输入失败';
                            break;
                        case 2:
                            errorMessage = '调取查询接口失败';
                            break;
                        case 3:
                            errorMessage = '查找编辑按钮失败';
                            break;
                        case 4:
                            errorMessage = '编辑库存失败';
                            break;
                        case 5:
                            errorMessage = '更新库存失败';
                            break;
                        default:
                            break;
                    }

                    BaseLogger.report({
                        scene: 'sync-mt-stock',
                        data: {
                            scene: 'cycleError',
                            uuid,
                            error,
                            errorMessage,
                        },
                    });
                    this.logger.error('syncProductStockBySkuId error', error);

                    await ECStockRecordAPI.updateWaitSyncFlag({
                        syncIds: idList,
                    });

                    BaseLogger.report({
                        scene: 'sync-mt-stock',
                        data: {
                            scene: 'clear',
                            uuid,
                            skuId,
                            idList,
                            remain: rows.length,
                        },
                    });
                }
            } while (rows.length);

            this.logger.info('syncStockBySkuId all end');
        } catch (error) {
            BaseLogger.report({
                scene: 'sync-mt-stock',
                data: {
                    scene: 'error',
                    uuid,
                    error,
                },
            });
            this.logger.error('syncProductStockBySkuId error', error);
            return Response.error(error);
        } finally {
            this.syncingProductStock = false;
        }
    }

    async initProductPageEnvironment(page = this.productPage) {
        try {
            await this.closeGlobalModal(page);

            BaseLogger.report({
                scene: 'sync-mt-stock',
                data: { scene: 'close-global-modal' },
            });

            const iframe = await MeituanCrawlerSteps.getHashFrame(page);
            this.logger.info('获取iframe');

            await this.closeConfirmationDialogModal(iframe);

            BaseLogger.report({
                scene: 'sync-mt-stock',
                data: { scene: 'close-confirm-modal' },
            });

            const input = await iframe.$(RPAConfig.DomSelector.CrawlerSteps.SkuIdInput);
            if (!input) {
                await MeituanCrawlerSteps.iframeClickSearchSkuId(page, iframe);
                this.logger.info('点击下拉框打开选项');
            }

            return { iframe };
        } catch (error) {
            this.logger.error('初始化页面环境失败:', error);
            throw error;
        }
    }

    async scrapeProductDetail(task) {
        const skuId = task.data?.skuId;
        if (!this.browser) throw new Error('Browser not initialized');

        const scrapedProductDetailPage = await this.browser.newPage();
        await this.prepareContext(scrapedProductDetailPage);

        scrapedProductDetailPage.setViewport({
            width: 1200, height: 800,
        });
        this.initializePage(scrapedProductDetailPage);

        try {
            this.logger.info('scrapeProductDetail start');

            await MeituanCrawlerSteps.navigateToPage(scrapedProductDetailPage, RPAConfig.Page.ProductList.url, {
                waitUntil: 'networkidle0',
                timeout: 90000,
            });

            await this.closeGlobalModal(scrapedProductDetailPage);
            await scrapedProductDetailPage.waitForNetworkIdle({ idleTime: 500 });

            const iframe = await MeituanCrawlerSteps.getHashFrame(scrapedProductDetailPage);
            this.logger.info('scrapeProductDetail 获取iframe');

            await this.closeConfirmationDialogModal(iframe);

            await MeituanCrawlerSteps.iframeClickSearchSkuId(scrapedProductDetailPage, iframe);
            this.logger.info('scrapeProductDetail 点击下拉框打开选项');

            await MeituanCrawlerSteps.iframeTypeSkuId(scrapedProductDetailPage, iframe, skuId);
            this.logger.info(`scrapeProductDetail 输入skuId-${skuId}`);

            await this.waitForSearchResponse(skuId);

            await MeituanCrawlerSteps.clickProductEditButton(iframe);
            this.logger.info(`scrapeProductDetail 点击编辑-${skuId}`);

            // 等待商品详情页面加载
            let isPackage = false;
            const productDetailResponse = new Promise((resolve) => {
                const responseHandler = async (url, response) => {
                    if (!url.includes(RPAConfig.WatchAPI.ProductDetail)) return;
                    this.removeResponseHandler('productDetailResponse');
                    resolve(response);
                };
                this.registerResponseHandler('productDetailResponse', responseHandler);
            });

            const productPackageDetailResponse = new Promise((resolve) => {
                const responseHandler = async (url, response) => {
                    if (!url.includes(RPAConfig.WatchAPI.ProductPackageDetail)) return;
                    this.removeResponseHandler('productPackageDetailResponse');
                    resolve(response);
                };
                this.registerResponseHandler('productPackageDetailResponse', responseHandler);
            });

            const response = await Promise.race([
                productDetailResponse,
                productPackageDetailResponse,
            ]);

            const productOrPackageDetail = await response.json();
            console.log('productDetailJson', productOrPackageDetail);

            if (productOrPackageDetail.data?.combinationSpus?.length) {
                isPackage = true;
            }
            if (!productOrPackageDetail?.data) {
                this.logger.warn('No product found');
            } else {
                return Response.success({
                    isPackage,
                    detail: productOrPackageDetail?.data,
                });
            }
        } catch (error) {
            this.logger.error('scrapeProductDetail error', error);
            return Response.error(error, {
                skuId,
            });
        } finally {
            if (scrapedProductDetailPage) {
                await scrapedProductDetailPage.close();
            }
        }
    }

    async _createLoginPage({
        headless = true,
        onBrowsersClosed = null,
    } = {}) {
        const loginBrowser = await puppeteer.launch({
            headless,
            defaultViewport: {
                width: 1366,
                height: 768,
            },
            ignoreHTTPSErrors: true,
            args: [
                '--no-sandbox',
                '--window-size=1366,768',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-webgl',
                '--disable-threaded-animation',
                '--disable-threaded-scrolling',
                '--disable-in-process-stack-traces',
                '--disable-histogram-customizer',
                '--disable-site-isolation-trials',
                '--disable-extensions',
                '--disable-component-extensions-with-background-pages',
                '--disable-ipc-flooding-protection',
                '--disable-accelerated-2d-canvas',
                '--disable-accelerated-jpeg-decoding',
                '--disable-accelerated-mjpeg-decode',
                '--disable-app-list-dismiss-on-blur',
                '--disable-canvas-aa',
                '--disable-composited-antialiasing',
                '--disable-features=IsolateOrigins,site-per-process',
                '--disable-blink-features=AutomationControlled', // 关闭自动化特征检测
                '--disable-infobars', // 关闭信息栏提示
                '--no-default-browser-check', // 禁止默认浏览器检查
                '--disable-setuid-sandbox', // 禁用沙盒权限检查
                '--disable-dev-shm-usage',
                '--disable-extensions', // 禁用所有扩展
                '--disable-default-apps', // 禁用默认应用
                '--disable-component-extensions-with-background-pages', // 禁止后台扩展
                '--disable-features=site-per-process', // 防止跨站跟踪
            ],
            ignoreDefaultArgs: ['--enable-automation'], // 禁用自动化标志
            executablePath: window.localStorage.getItem('chromePath'),
            userDataDir: './user_data', // 同一目录复用用户数据
        });

        // 获取浏览器打开时的默认页面，而不是创建新页面
        let [loginPage] = await loginBrowser.pages();
        if (!loginPage) {
            loginPage = await loginBrowser.newPage();
        }

        // 设置浏览器指纹
        await loginPage.evaluateOnNewDocument(() => {
            // 修改 navigator.webdriver
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
                configurable: true,
                enumerable: true,
            });
            // 删除浏览器指纹中的自动化痕迹
            window.navigator.chrome = undefined;
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register = undefined;
            }
        });

        await loginPage.setViewport({
            width: 1366,
            height: 768,
        });

        await loginPage.setUserAgent(MtClientUA);
        // 设置语言偏好（避免 en-US 暴露自动化特征）
        await loginPage.setExtraHTTPHeaders({
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        });

        // 将浏览器实例添加到全局跟踪数组中
        if (!headless) {
            this.meituanBrowserInstances = [];
            this.meituanBrowserInstances.push(loginBrowser);
            logger.info(`添加美团浏览器实例到跟踪列表，当前共有 ${this.meituanBrowserInstances.length} 个实例`);

            // 监听浏览器断开连接事件，当用户手动关闭浏览器窗口时触发
            loginBrowser.on('disconnected', () => {
                const index = this.meituanBrowserInstances.indexOf(loginBrowser);
                if (index !== -1) {
                    this.meituanBrowserInstances.splice(index, 1);

                    // 如果没有浏览器实例了，并且提供了回调函数，则调用回调函数
                    if (this.meituanBrowserInstances.length === 0 && typeof onBrowsersClosed === 'function') {
                        onBrowsersClosed();
                    }
                }
            });
        }

        const destroyLoginPage = async () => {
            // 从跟踪数组中移除浏览器实例
            const index = (this.meituanBrowserInstances || []).indexOf(loginBrowser);
            if (index !== -1) {
                this.meituanBrowserInstances.splice(index, 1);

                // 如果没有浏览器实例了，并且提供了回调函数，则调用回调函数
                if (this.meituanBrowserInstances.length === 0 && typeof onBrowsersClosed === 'function') {
                    onBrowsersClosed();
                }
            }

            // 调用原始的销毁方法
            await loginPage.close();
            await loginBrowser.close();
        };

        this.initializePage(loginPage);
        return {
            loginPage,
            destroyLoginPage,
        };
    }

    async _prepareRunnerPage() {
        try {
            if (!this.browser) {
                this.browser = await puppeteer.launch({
                    headless: window.isHeadlessMock ? false : true,
                    defaultViewport: {
                        width: 1366,
                        height: 768,
                    },
                    ignoreHTTPSErrors: true,
                    args: [
                        '--no-sandbox',
                        '--window-size=1366,768',
                        '--no-first-run',
                        '--no-default-browser-check',
                        '--disable-webgl',
                        '--disable-threaded-animation',
                        '--disable-threaded-scrolling',
                        '--disable-in-process-stack-traces',
                        '--disable-histogram-customizer',
                        '--disable-site-isolation-trials',
                        '--disable-extensions',
                        '--disable-component-extensions-with-background-pages',
                        '--disable-ipc-flooding-protection',
                        '--disable-accelerated-2d-canvas',
                        '--disable-accelerated-jpeg-decoding',
                        '--disable-accelerated-mjpeg-decode',
                        '--disable-app-list-dismiss-on-blur',
                        '--disable-canvas-aa',
                        '--disable-composited-antialiasing',
                        '--disable-features=IsolateOrigins,site-per-process',
                        '--disable-blink-features=AutomationControlled', // 关闭自动化特征检测
                        '--disable-infobars', // 关闭信息栏提示
                        '--no-default-browser-check', // 禁止默认浏览器检查
                        '--disable-setuid-sandbox', // 禁用沙盒权限检查
                        '--disable-dev-shm-usage',
                        '--disable-extensions', // 禁用所有扩展
                        '--disable-default-apps', // 禁用默认应用
                        '--disable-component-extensions-with-background-pages', // 禁止后台扩展
                        '--disable-features=site-per-process', // 防止跨站跟踪
                    ],
                    ignoreDefaultArgs: ['--enable-automation'], // 禁用自动化标志
                    executablePath: window.localStorage.getItem('chromePath'),
                    userDataDir: './other_user_data', // 同一目录复用用户数据
                });
            }

            // 获取浏览器打开时的默认页面，而不是创建新页面
            let [loginPage] = await this.browser.pages();
            if (!loginPage) {
                loginPage = await this.browser.newPage();
            }
            this.page = loginPage;

            // 添加页面刷新事件监听
            window.addEventListener('beforeunload', () => {
                this.page && this.page.close();
                this.browser && this.browser.close();
            });

            await this.pageHandler(this.page);
        } catch (error) {
            this.logger.error('Failed to initialize browser', error);
            throw error;
        }
    }

    async pageHandler(page) {
        this.initializePage(page);

        // 设置浏览器指纹
        await page.evaluateOnNewDocument(() => {
            // 修改 navigator.webdriver
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
                configurable: true,
                enumerable: true,
            });
            // 删除浏览器指纹中的自动化痕迹
            window.navigator.chrome = undefined;
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register = undefined;
            }
        });

        // 设置页面大小和用户代理
        await page.setViewport({
            width: 1366,
            height: 768,
        });

        await page.setUserAgent(MtClientUA);

        // 设置语言偏好（避免 en-US 暴露自动化特征）
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        });

        // 第一步：访问首页初始化设备
        await page.goto(RPAConfig.Page.Login.url, {
            waitUntil: 'networkidle2',
        });

        // 第二步：获取设备指纹
        const deviceCookies = await page.cookies();
        const newDeviceCookie = deviceCookies.find((c) => c.name === 'device_uuid');
        if (newDeviceCookie && this.authMallCookies) {
            const oldDeviceCookie = this.authMallCookies.find((c) => c.name === 'device_uuid');
            console.log('meituan oldDeviceCookie', oldDeviceCookie.value);
            console.log('meituan newDeviceCookie', newDeviceCookie.value);
            oldDeviceCookie && Object.assign(oldDeviceCookie, newDeviceCookie);
            console.log('meituan merged', this.authMallCookies);
        }
        this.authMallCookies && await page.setCookie(...this.authMallCookies);
    }

    async _prepareRunnerProductPage() {
        try {
            if (!this.productBrowser) {
                this.productBrowser = await puppeteer.launch({
                    headless: window.isHeadlessMock ? false : true,
                    defaultViewport: {
                        width: 1366,
                        height: 768,
                    },
                    ignoreHTTPSErrors: true,
                    args: [
                        '--no-sandbox',
                        '--window-size=1366,768',
                        '--no-first-run',
                        '--no-default-browser-check',
                        '--disable-webgl',
                        '--disable-threaded-animation',
                        '--disable-threaded-scrolling',
                        '--disable-in-process-stack-traces',
                        '--disable-histogram-customizer',
                        '--disable-site-isolation-trials',
                        '--disable-extensions',
                        '--disable-component-extensions-with-background-pages',
                        '--disable-ipc-flooding-protection',
                        '--disable-accelerated-2d-canvas',
                        '--disable-accelerated-jpeg-decoding',
                        '--disable-accelerated-mjpeg-decode',
                        '--disable-app-list-dismiss-on-blur',
                        '--disable-canvas-aa',
                        '--disable-composited-antialiasing',
                        '--disable-features=IsolateOrigins,site-per-process',
                        '--disable-blink-features=AutomationControlled', // 关闭自动化特征检测
                        '--disable-infobars', // 关闭信息栏提示
                        '--no-default-browser-check', // 禁止默认浏览器检查
                        '--disable-setuid-sandbox', // 禁用沙盒权限检查
                        '--disable-dev-shm-usage',
                        '--disable-extensions', // 禁用所有扩展
                        '--disable-default-apps', // 禁用默认应用
                        '--disable-component-extensions-with-background-pages', // 禁止后台扩展
                        '--disable-features=site-per-process', // 防止跨站跟踪
                    ],
                    ignoreDefaultArgs: ['--enable-automation'], // 禁用自动化标志
                    executablePath: window.localStorage.getItem('chromePath'),
                    userDataDir: './user_data', // 同一目录复用用户数据
                });
            }
            if (this.productPage) return;

            // 获取浏览器打开时的默认页面，而不是创建新页面
            let [loginPage] = await this.productBrowser.pages();
            if (!loginPage) {
                loginPage = await this.productBrowser.newPage();
            }
            this.productPage = loginPage;
            // 添加页面刷新事件监听
            window.addEventListener('beforeunload', () => {
                this.productPage && this.productPage.close();
                this.productBrowser && this.productBrowser.close();
            });
            await this.pageHandler(this.productPage);
        } catch (error) {
            this.logger.error('Failed to initialize productBrowser', error);
            throw error;
        }
    }

    async prepareContext(page = this.page) {
        // 使用 CDP 直接设置 localStorage
        const client = await page.target().createCDPSession();
        await client.send('Page.enable');

        // 设置本地存储的脚本
        const { localStorageScript } = RPAConfig;

        // 注册脚本，在文档开始加载时执行
        await client.send('Page.addScriptToEvaluateOnNewDocument', {
            source: localStorageScript,
        });
    }

    async closeConfirmationDialogModal(iframe) {
        // Handle confirmation dialog modal
        try {
            const aiAlertModalExists = await iframe.$$(RPAConfig.DomSelector.AiAlertModal.CloseButton);
            if (aiAlertModalExists && aiAlertModalExists.length) {
                for (let i = aiAlertModalExists.length - 1; i >= 0; i--) {
                    await aiAlertModalExists[i].click();
                    this.logger.info('ai alert modal closed.');
                }
            } else {
                this.logger.info('ai alert modal not found.');
            }

            // 关闭商品体检内容升级指引弹窗
            const featureGuideModalExists = await iframe.$(RPAConfig.DomSelector.FeatureGuideModal.Modal);
            if (featureGuideModalExists) {
                await iframe.click(RPAConfig.DomSelector.FeatureGuideModal.CloseButton);
                this.logger.info('Feature guide modal closed by clicking default button.');
            }

            const confirmModalExists = await iframe.$(RPAConfig.DomSelector.ConfirmationDialog.Modal);
            if (confirmModalExists) {
                await iframe.click(RPAConfig.DomSelector.ConfirmationDialog.CloseButton);
                this.logger.info('Confirmation modal closed.');
            }

            // Find all guide modals and close them
            const guideModalCloseButtons = await iframe.$$(RPAConfig.DomSelector.GuideModal.CloseButton);
            if (guideModalCloseButtons && guideModalCloseButtons.length) {
                for (let i = guideModalCloseButtons.length - 1; i >= 0; i--) {
                    await guideModalCloseButtons[i].click();
                    this.logger.info(`Guide modal ${i + 1}/${guideModalCloseButtons.length} closed.`);
                }
            } else {
                this.logger.info('No guide modals found.');
            }
        } catch (error) {
            this.logger.info('Confirmation modal not found.');
            this.logger.info('Feature guide modal not found.');
        }
    }

    async closeGlobalModal(page = this.page) {
        try {
            // Handle multiple-modal-item type modals
            const multipleModalCloseButtons = await page.$$(RPAConfig.DomSelector.MultipleModal.CloseButton);
            if (multipleModalCloseButtons && multipleModalCloseButtons.length) {
                for (let i = multipleModalCloseButtons.length - 1; i >= 0; i--) {
                    await multipleModalCloseButtons[i].click();
                    this.logger.info(`Multiple modal ${i + 1}/${multipleModalCloseButtons.length} closed.`);
                }
            } else {
                this.logger.info('Multiple modal not found.');
            }

            const upgradeModalExists = await page.$(RPAConfig.DomSelector.UpgradeModal.Modal);
            if (upgradeModalExists) {
                await page.click(RPAConfig.DomSelector.UpgradeModal.CloseButton);
                this.logger.info('Upgrade modal closed.');
            } else {
                this.logger.info('Upgrade modal not found.');
            }

            const globalModalExists = await page.$(RPAConfig.DomSelector.GlobalModal.Modal);
            if (globalModalExists) {
                await page.click(RPAConfig.DomSelector.GlobalModal.CloseButton);
                this.logger.info('Global modal closed.');
            } else {
                this.logger.info('Global modal not found.');
            }

            const newVersionModalExists = await page.$(RPAConfig.DomSelector.NewVersionModal.Modal);
            if (newVersionModalExists) {
                await page.click(RPAConfig.DomSelector.NewVersionModal.CloseButton);
                this.logger.info('New version modal closed.');
            } else {
                this.logger.info('New version modal not found.');
            }

            const servicePlatformModalExists = await page.$(RPAConfig.DomSelector.ServicePlatformModal.Modal);
            if (servicePlatformModalExists) {
                await page.click(RPAConfig.DomSelector.ServicePlatformModal.CloseButton);
                this.logger.info('service platform modal closed.');
            } else {
                this.logger.info('service platform modal not found.');
            }

            const newMenuModalExists = await page.$(RPAConfig.DomSelector.NewMenuModal.Modal);
            if (newMenuModalExists) {
                await page.click(RPAConfig.DomSelector.NewMenuModal.CloseButton);
                this.logger.info('new menu modal closed.');
            } else {
                this.logger.info('new menu modal not found.');
            }

            // Find all guide modals and close them
            const guideModalCloseButtons = await page.$$(RPAConfig.DomSelector.GuideModal.CloseButton);
            if (guideModalCloseButtons && guideModalCloseButtons.length) {
                for (let i = guideModalCloseButtons.length - 1; i >= 0; i--) {
                    await guideModalCloseButtons[i].click();
                    this.logger.info(`Guide modal ${i + 1}/${guideModalCloseButtons.length} closed.`);
                }
            } else {
                this.logger.info('No guide modals found.');
            }
            await page.addStyleTag({
                content: RPAConfig.CSSContent,
            });
            this.logger.info('Applied multiple methods to handle survey popups');
        } catch (error) {
            this.logger.info('Failed to handle survey popups', error);
        }

    }

    async cleanup() {
        try {
            if (this.page) await this.page.close();
            if (this.browser) await this.browser.close();
        } catch (error) {
            this.logger.error('Cleanup error', error);
        }
    }
}
