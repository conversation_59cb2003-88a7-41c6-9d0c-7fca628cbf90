<template>
    <abc-layout
        v-abc-loading="loading"
        preset="page-table"
        style="height: calc(100% - 48px);"
        class="order-cloud-out-storage"
    >
        <abc-layout-header>
            <abc-flex vertical gap="large">
                <abc-tips-card-v2 v-if="!isChainAdmin && !loading && currentMall.status !== 1" align="center">
                    <abc-space>
                        {{ currentMall.status === 2 ? '当前网店授权已失效，无法同步数据，请重新授权' : '当前未绑定网店，无法同步数据，请重新绑定' }}
                        <abc-link @click="handleEcAuthorized(currentMall.status === 2 ? '2' : '1')">
                            {{ currentMall.status === 2 ? '立即授权' : '绑定网店' }}
                        </abc-link>
                    </abc-space>
                </abc-tips-card-v2>
                <abc-flex align="center">
                    <abc-space>
                        <abc-space is-compact>
                            <abc-select
                                v-model="params.ecType"
                                :width="92"
                                trigger-icon="s-triangle-select-color"
                                :input-style="{ fontWeight: 600 }"
                            >
                                <abc-image
                                    slot="prepend"
                                    :src="WpCodeLogo.MT"
                                    :width="16"
                                ></abc-image>
                                <abc-option
                                    v-for="(op) in ecTypeOptions"
                                    :key="op.value"
                                    :value="op.value"
                                    :label="op.label"
                                >
                                </abc-option>
                            </abc-select>
                            <biz-select-tabs
                                v-model="params.ecMallId"
                                :options="mallList"
                                :width="240"
                                :max-width="240"
                            >
                            </biz-select-tabs>
                        </abc-space>

                        <abc-date-picker
                            v-model="datePickerValue"
                            :picker-options="pickerOptions"
                            type="daterange"
                            :width="256"
                            :clearable="false"
                            placeholder="选择日期范围"
                            @change="handleChangeDate"
                        >
                        </abc-date-picker>

                        <clinic-select
                            v-if="isChainAdmin"
                            v-model="params.clinicId"
                            :show-all-clinic="false"
                            :exclude-chain-clinics="true"
                            clearable
                            placeholder="总部/门店"
                            @change="fetchData"
                        ></clinic-select>

                        <abc-input
                            v-model="params.keyword"
                            placeholder="联系人/手机号/订单号"
                            clearable
                            :width="220"
                            @input="handleSearchInput"
                        >
                            <abc-search-icon slot="prepend"></abc-search-icon>
                        </abc-input>

                        <abc-select
                            v-model="params.orderStatus"
                            :width="140"
                            placeholder="订单状态"
                            clearable
                            @change="fetchData"
                        >
                            <abc-option
                                v-for="item in statusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></abc-option>
                        </abc-select>

                        <abc-select
                            v-model="params.logisticsStatus"
                            :width="120"
                            placeholder="配送状态"
                            clearable
                            @change="fetchData"
                        >
                            <abc-option
                                v-for="item in logisticStatusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></abc-option>
                        </abc-select>

                        <abc-select
                            v-model="params.outStockStatus"
                            :width="120"
                            placeholder="ABC出库状态"
                            clearable
                            @change="fetchData"
                        >
                            <abc-option
                                v-for="item in outStockStatusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></abc-option>
                        </abc-select>
                    </abc-space>
                    <div style="flex: 1;"></div>
                    <sync-order-button :disabled="!isValidMall"></sync-order-button>
                </abc-flex>
            </abc-flex>
        </abc-layout-header>

        <abc-layout-content>
            <abc-table
                theme="white"
                :loading="contentLoading"
                :empty-content="'暂无数据'"
                :render-config="tableConfig"
                :data-list="tableData"
                cell-size="xlarge"
                class="out-storage-table"
                @handleClickTr="handleOperation"
            >
                <template #ecType="{ trData }">
                    <abc-table-cell>
                        {{ ECTypeText[trData.ecType] }}
                    </abc-table-cell>
                </template>

                <template #receiverInfo="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text class="ellipsis">
                                #{{ trData.ecAdditionalInfo?.orderDaySeq || '' }}
                                {{ trData.receiverInfo?.receiverNameMask || '-' }}
                            </abc-text>
                            <abc-text size="mini" theme="gray">
                                {{ trData.receiverInfo?.receiverPhoneMask || '-' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #orderStatus="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text>
                                {{ statusOptions.find(item => item.value === trData.orderStatus).label || '-' }}
                            </abc-text>
                            <abc-text size="mini" theme="gray">
                                {{ OrderLogisticsStatusTextEnum[trData.logisticsStatus] || '-' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #outStockStatus="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text :theme="getOutStockStatusType(trData)">
                                {{ outStockStatusOptions.find((item) => item.value === trData.outStockStatus)?.label || '-' }}
                            </abc-text>
                            <abc-text v-if="trData.isExistReturnStockItem" size="mini" theme="gray">
                                部分退货
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #goodsList="{ trData }">
                    <abc-popover
                        :value="showGoodsDetailPopover && hoverRowId === trData.id && currentRow.id === trData.id"
                        theme="yellow"
                        trigger="manual"
                        placement="bottom-start"
                        :visible-arrow="false"
                        style="width: 100%;"
                        :offset="-5"
                        :popper-style="{
                            padding: 0,
                            margin: 0,
                        }"
                    >
                        <abc-table-cell slot="reference" style="cursor: pointer;">
                            <div
                                class="ellipsis"
                                @mouseenter="handleGoodsMouseEnter(trData)"
                                @mouseleave="handleReferenceMouseLeave"
                            >
                                <abc-text bold>
                                    {{ (trData.goodsList || [])
                                        .map(item => `${item.goodsName} x ${item.goodsCount}`)
                                        .join(' + ')
                                    }}
                                </abc-text>
                            </div>
                        </abc-table-cell>
                        <div
                            @mouseenter="handlePopoverMouseEnter"
                            @mouseleave="handlePopoverMouseLeave"
                        >
                            <abc-table
                                :data-list="currentRow.orderItems"
                                :render-config="goodsTableConfig"
                                style="border: none;"
                                cell-size="xxxlarge"
                                class="order-cloud-goods-list-table"
                            >
                                <template #goodsInfo="{ trData: row }">
                                    <abc-table-cell align="start" style="padding-top: 8px;">
                                        <abc-flex style="width: 100%;" justify="space-between" gap="large">
                                            <abc-flex gap="middle" style="flex: 1;">
                                                <abc-image
                                                    :src="row.extGoodsImg || ''"
                                                    :width="56"
                                                    :height="56"
                                                ></abc-image>
                                                <abc-flex vertical :gap="2" style="flex: 1;">
                                                    <abc-text>
                                                        {{ row.extGoodsName }}
                                                    </abc-text>
                                                    <abc-space>
                                                        <abc-text size="mini" theme="gray">
                                                            SKUID: {{ row.extSkuId }}
                                                        </abc-text>
                                                        <abc-text size="mini" theme="gray">
                                                            店内码/货号: {{ row.ecGoodsSku?.extSourceFoodCode || '-' }}
                                                        </abc-text>
                                                    </abc-space>
                                                </abc-flex>
                                            </abc-flex>
                                            <abc-text>
                                                x {{ row.extGoodsCount }}
                                            </abc-text>
                                        </abc-flex>
                                    </abc-table-cell>
                                </template>
                                <template #bindInfo="{ trData: row }">
                                    <abc-table-cell align="start" style="padding-top: 8px;">
                                        <abc-flex v-if="row.goodsStockList && row.goodsStockList && row.goodsStockList.length" justify="space-between" style="width: 100%;">
                                            <abc-flex vertical :gap="2" style="flex: 1;">
                                                <abc-text>
                                                    {{ row.goodsStockList[0].hisGoodsInfo.displayName }}
                                                </abc-text>
                                                <abc-space>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.goodsStockList[0].hisGoodsInfo.displaySpec || '' }}
                                                    </abc-text>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.goodsStockList[0].hisGoodsInfo.manufacturer || '' }}
                                                    </abc-text>
                                                    <abc-text size="mini" theme="gray">
                                                        {{ row.goodsStockList[0].hisGoodsInfo.shortId || '' }}
                                                    </abc-text>
                                                </abc-space>
                                            </abc-flex>
                                            <abc-text>
                                                x {{ row.extGoodsCount }} {{ row.goodsStockList[0].packageUnit }}
                                            </abc-text>
                                        </abc-flex>
                                        <abc-text v-else theme="gray">
                                            未绑定ABC商品
                                        </abc-text>
                                    </abc-table-cell>
                                </template>
                            </abc-table>
                        </div>
                    </abc-popover>
                </template>

                <template #orderNo="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-space>
                                <abc-text>{{ trData.orderNo || '-' }}</abc-text>
                                <abc-tag-v2
                                    v-if="trData.orderTypeFlag & 8"
                                    variant="outline"
                                    shape="square"
                                    size="small"
                                    theme="danger"
                                >
                                    处方
                                </abc-tag-v2>
                            </abc-space>
                            <abc-text size="mini" theme="gray">
                                {{ trData.createdTime ? `${formatDate(trData.createdTime, 'MM-DD HH:mm')}下单` : '-' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #mallName="{ trData }">
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text>{{ trData.mallName || '-' }}</abc-text>
                            <abc-text size="mini" theme="gray">
                                {{ trData.clinicName || '-' }}
                            </abc-text>
                        </abc-flex>
                    </abc-table-cell>
                </template>

                <template #operation="{ trData }">
                    <abc-table-cell>
                        <abc-button
                            type="text"
                            @click.stop="handleOperation(trData)"
                        >
                            {{ !isChainAdmin && trData.outStockStatus === 0 && trData.orderStatus !== OrderStatusEnum.canceled && trData.bindStatus === 1 ? '出库' : '查看' }}
                        </abc-button>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>

        <abc-layout-footer>
            <abc-pagination
                :pagination-params="paginationParams"
                :count="totalCount"
                :show-total-page="true"
                :page-sizes="[15, 30, 50, 100]"
                show-size
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import { formatDate } from '@abc/utils-date';
    import {
        ECTypeEnum, ECTypeText, WpCodeLogo,
    } from '@/utils/constants';
    import { debounce } from '@abc/utils';
    import ECOrderAPI from '@/api/order';
    import DialogOrderOut from '@/components/dialog-order-out/index.js';
    import SyncOrderButton from '@/components/sync-order-button.vue';
    import { EcOrderOutStatus } from '@/utils/constants.js';
    import { mapGetters } from 'vuex';
    import {
        OrderStatusEnum,
        OrderStatusTextEnum,
        OrderLogisticsStatusEnum,
        OrderLogisticsStatusTextEnum,
    } from '@/daemon/crawler/provider/meituan/constants';
    import ClinicSelect from 'MfBase/clinic-select';
    import ECAuthAPI from '@/api/auth';
    import BizSelectTabs from 'MfBase/biz-select-tabs';
    import AbcSocket from 'MfBase/single-socket';
    import * as business from 'MfFeEngine/business';
    import DialogEcAuth from '@/components/dialog-ec-auth';
    import { PharmacyOrderCloudRouterNameKeys } from '@/core/routes';

    export default {
        name: 'OutStorage',
        components: {
            SyncOrderButton,
            ClinicSelect,
            BizSelectTabs,
        },
        data() {
            const now = new Date();
            const today = formatDate(now);
            const nowDayOfWeek = now.getDay();
            const nowDay = now.getDate();
            const nowMonth = now.getMonth();
            const nowYear = now.getFullYear();

            return {
                // 搜索条件相关
                WpCodeLogo,
                ECTypeText,
                OrderStatusEnum,
                OrderStatusTextEnum,
                OrderLogisticsStatusTextEnum,
                params: {
                    limit: 15,
                    offset: 0,
                    clinicId: '',
                    ecMallId: '',
                    ecType: 4,
                    keyword: '',
                    orderStatus: '',
                    logisticsStatus: '',
                    outStockStatus: '',
                    beginDate: today,
                    endDate: today,
                    dateType: 4,
                },
                totalCount: 0,
                loading: false,
                contentLoading: false,

                statusOptions: [
                    {
                        label: OrderStatusTextEnum[OrderStatusEnum.newOrder], value: OrderStatusEnum.newOrder,
                    },
                    {
                        label: OrderStatusTextEnum[OrderStatusEnum.waitingDelivery], value: OrderStatusEnum.waitingDelivery,
                    },
                    {
                        label: OrderStatusTextEnum[OrderStatusEnum.delivering], value: OrderStatusEnum.delivering,
                    },
                    {
                        label: OrderStatusTextEnum[OrderStatusEnum.delivered], value: OrderStatusEnum.delivered,
                    },
                    {
                        label: '确认收货', value: OrderStatusEnum.received,
                    },
                    {
                        label: '已取消', value: OrderStatusEnum.canceled,
                    },
                ],
                logisticStatusOptions: [
                    {
                        label: OrderLogisticsStatusTextEnum[OrderLogisticsStatusEnum.waitingDelivery], value: OrderLogisticsStatusEnum.waitingDelivery,
                    },
                    {
                        label: OrderLogisticsStatusTextEnum[OrderLogisticsStatusEnum.waitingRider], value: OrderLogisticsStatusEnum.waitingRider,
                    },
                    {
                        label: OrderLogisticsStatusTextEnum[OrderLogisticsStatusEnum.waitingGet], value: OrderLogisticsStatusEnum.waitingGet,
                    },
                    {
                        label: OrderLogisticsStatusTextEnum[OrderLogisticsStatusEnum.riderGet], value: OrderLogisticsStatusEnum.riderGet,
                    },
                    {
                        label: OrderLogisticsStatusTextEnum[OrderLogisticsStatusEnum.riderDelivered], value: OrderLogisticsStatusEnum.riderDelivered,
                    },
                    {
                        label: OrderLogisticsStatusTextEnum[OrderLogisticsStatusEnum.canceled], value: OrderLogisticsStatusEnum.canceled,
                    },
                ],
                outStockStatusOptions: [
                    {
                        label: '待出库', value: EcOrderOutStatus.WAIT,
                    },
                    {
                        label: '已出库', value: EcOrderOutStatus.OUT_STOCK,
                    },
                    {
                        label: '已退入', value: EcOrderOutStatus.RETURN_STOCK,
                    },
                    {
                        label: '已取消', value: EcOrderOutStatus.CANCELLED,
                    },
                ],

                datePickerValue: [today, today],
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date() || date < new Date('2001-11-01');
                    },
                    shortcuts: [{
                        text: '今天',
                        onClick(cb) {
                            const start = new Date();
                            const end = new Date();
                            cb([start, end]);
                        },
                    }, {
                        text: '昨天',
                        onClick(cb) {
                            const end = new Date(nowYear, nowMonth, nowDay - 1);
                            const start = new Date(nowYear, nowMonth, nowDay - 1);
                            cb([start, end]);
                        },
                    }, {
                        text: '本周',
                        onClick(cb) {
                            let start = new Date(nowYear, nowMonth, nowDay - 6);
                            if (nowDayOfWeek) {
                                start = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1);
                            }
                            const end = new Date();
                            cb([start, end]);
                        },
                    }, {
                        text: '本月',
                        onClick(cb) {
                            const start = new Date(nowYear, nowMonth, 1);
                            const end = new Date();
                            cb([start, end]);
                        },
                    }],
                },
                tableData: [],
                showGoodsDetailPopover: false,
                isMouseInReference: false,
                isMouseInPopover: false,
                hoverRowId: null,
                closeTimer: null,
                currentRow: {},
                ecTypes: [],
                mallList: [],
                ecTypeOptions: [
                    {
                        label: '美团',
                        value: ECTypeEnum.MT,
                    },
                ],
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
            ]),
            paginationParams() {
                const {
                    limit, offset,
                } = this.params;
                const pageIndex = Math.ceil(offset / limit);
                return {
                    pageIndex,
                    pageSize: limit,
                };
            },

            // 表格相关
            tableConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'ecType',
                            label: '平台',
                            style: {
                                flex: 'none',
                                width: '80px',
                            },
                        },
                        {
                            key: 'receiverInfo',
                            label: '联系人',
                            style: {
                                flex: 'none',
                                width: '140px',
                            },

                        },
                        {
                            key: 'orderStatus',
                            label: '订单/配送状态',
                            style: {
                                flex: 'none',
                                width: '140px',
                                textAlign: 'center',
                            },
                        },
                        {
                            key: 'outStockStatus',
                            label: 'ABC出库状态',
                            style: {
                                flex: 'none',
                                width: '120px',
                                textAlign: 'center',
                            },
                        },
                        {
                            key: 'goodsList',
                            label: '订单商品',
                            style: {
                                minWidth: '240px',
                            },
                        },
                        {
                            key: 'goodsAfter',
                            label: '',
                            style: {
                                flex: 'none',
                                width: '20px',
                            },
                        },
                        {
                            key: 'orderNo',
                            label: '订单号',
                            style: {
                                flex: 'none',
                                width: '216px',
                            },
                        },
                        {
                            key: 'actualAmount',
                            label: '实付金额',
                            style: {
                                flex: 'none',
                                width: '100px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'amountAfter',
                            label: '',
                            style: {
                                flex: 'none',
                                width: '20px',
                            },
                        },
                        {
                            key: 'mallName',
                            label: '网店/线下门店',
                            style: {
                                flex: 'none',
                                width: '220px',
                            },
                        },
                        {
                            key: 'operation',
                            label: '操作',
                            pinned: 'right',
                            style: {
                                flex: 'none',
                                width: '80px',
                                textAlign: 'center',
                            },
                        },
                    ],
                };
            },
            goodsTableConfig() {
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'goodsInfo',
                            label: '订单商品',
                            style: {
                                flex: 'none',
                                width: '400px',
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                            headerStyle: {
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                        },
                        {
                            key: 'bindInfo',
                            label: '绑定的ABC系统商品',
                            style: {
                                flex: 'none',
                                width: '300px',
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                            headerStyle: {
                                backgroundColor: 'var(--abc-color-LY4)',
                                borderColor: 'var(--abc-color-LY1)',
                            },
                        },
                    ],
                };
            },
            currentMall() {
                return this.mallList.find((item) => item.ecMallId === this.params.ecMallId) || {};
            },
            isValidMall() {
                return this.currentMall.status === 1;
            },
        },
        async created() {
            await this.getEcTypes();
            await this.getMallList();
            this.params.ecMallId = this.mallList?.[0]?.ecMallId || '';
            this.fetchData();
            this._debounceFetchData = debounce(this.fetchData, 800, true);
            this._debounceFetchOrderDetail = debounce(this.fetchOrderDetail, 300, true);
            const { socket } = AbcSocket.getSocket();
            if (business.ECOrderService) {
                this._SocketService = new business.ECOrderService(socket);
                this._SocketService.onECOrderStat(this.refreshOrder);
                this.$on('hook:beforeDestroy', () => {
                    this._SocketService?.offECOrderStat(this.refreshOrder);
                });
            }
        },
        methods: {
            formatDate,
            refreshOrder() {
                // 收到消息且在第一页需要更新列表
                if (this.params.offset === 0) {
                    this._debounceFetchData();
                }
            },
            handleGoodsMouseEnter(trData) {
                this.clearCloseTimer();

                this.hoverRowId = trData.id;
                this.isMouseInReference = true;
                this.isMouseInPopover = false;

                if (this.currentRow.id === trData.id) {
                    this.showGoodsDetailPopover = true;
                } else {
                    this._debounceFetchOrderDetail(trData);
                }
            },
            setCloseTimer() {
                // 清除之前的定时器
                if (this.closeTimer) {
                    clearTimeout(this.closeTimer);
                }

                // 设置新的定时器
                this.closeTimer = setTimeout(() => {
                    if (!this.isMouseInReference && !this.isMouseInPopover) {
                        this.showGoodsDetailPopover = false;
                        this.hoverRowId = null;
                    }
                    this.closeTimer = null;
                }, 300);
            },
            clearCloseTimer() {
                if (this.closeTimer) {
                    clearTimeout(this.closeTimer);
                    this.closeTimer = null;
                }
            },
            handleReferenceMouseLeave() {
                this.isMouseInReference = false;
                this.setCloseTimer();
            },
            handlePopoverMouseEnter() {
                this.isMouseInPopover = true;
                this.clearCloseTimer();
            },
            handlePopoverMouseLeave() {
                this.isMouseInPopover = false;
                this.setCloseTimer();
            },

            // 获取订单详情 - 使用防抖函数调用
            async fetchOrderDetail(trData) {
                try {
                    const data = await ECOrderAPI.fetchOrderDetail({
                        orderId: trData.id,
                    });
                    if (this.hoverRowId === trData.id) {
                        this.currentRow = data;
                    }
                    this.showGoodsDetailPopover = true;
                } catch (e) {
                    console.error(e);
                }
            },

            getOutStockStatusType(row) {
                const type = {
                    0: 'warning-light',
                    10: 'success-light',
                    99: 'black',
                };
                return type[row.outStockStatus] || 'black';
            },
            // 处理操作按钮点击
            handleOperation(row) {
                const _dialog = new DialogOrderOut({
                    orderId: row.id,
                    orderNo: row.orderNo,
                    outStockStatus: row.outStockStatus,
                    orderStatus: row.orderStatus,
                    onRefresh: this.fetchData,
                    isValidMall: this.isValidMall,
                });
                _dialog.generateDialogAsync({
                    parent: this,
                });
            },
            handleChangeDate() {
                this.params.beginDate = this.datePickerValue[0];
                this.params.endDate = this.datePickerValue[1];
                this.fetchData();
            },
            // 处理每页显示数量变化
            handleSizeChange(pageSize) {
                this.params.limit = pageSize;
                this.fetchData();
            },
            // 处理页码变化
            handleCurrentChange(pageIndex) {
                this.params.offset = (pageIndex - 1) * this.params.limit;
                this.fetchData(false);
            },
            // 获取数据
            async fetchData(needResetOffset = true) {
                try {
                    this.contentLoading = true;
                    if (needResetOffset) {
                        this.params.offset = 0;
                    }
                    const {
                        rows, total = 0,
                    } = await ECOrderAPI.fetchEcOrderList(this.params);
                    this.tableData = rows ?? [];
                    this.totalCount = total;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
            async getEcTypes() {
                try {
                    this.loading = true;
                    const { rows } = await ECAuthAPI.fetchEcTypes({
                        ...this.params,
                    });
                    this.ecTypes = rows || [];
                } catch (err) {
                    console.error(err);
                } finally {
                    this.loading = true;
                }
            },
            async getMallList() {
                try {
                    this.loading = true;
                    const { rows = [] } = await ECAuthAPI.fetchBindAuthList({
                        offset: 0,
                        limit: 1000,
                        onlyUnexpired: 0, // 在授权有效期内
                        includeHistory: 1, // 是否包含历史绑定的电商店铺
                        includeDeleted: 1, // 是否包含已删除的绑定数据
                    });
                    this.mallList = rows
                        .filter((item) => item.ecType === ECTypeEnum.MT)
                        .sort((a, b) => {
                            const getOrder = (order) => (order === 1 ? 1 : 0);
                            const orderA = getOrder(a.status);
                            const orderB = getOrder(b.status);
                            return orderB - orderA;
                        })
                        .reduce((pre, cur) => {
                            if (pre.find((item) => item.ecMallId === cur.ecMallId)) return pre;
                            pre.push(cur);
                            return pre;
                        }, [])
                        .map((item) => {
                            return {
                                ...item,
                                key: item.ecMallId,
                                label: `${item.mallName}（${item.bindClinicName}）`,
                                value: item.ecMallId,
                                icon: 's-s-drugstore-color',
                            };
                        });
                } catch (err) {
                    console.error(err);
                } finally {
                    this.loading = false;
                }
            },
            handleSearchInput() {
                this._debounceFetchData();
            },
            async handleEcAuthorized(controlType) {
                if (controlType === '2') {
                    await new DialogEcAuth({
                        authCode: this.authCode,
                        extMallId: this.params.extMallId,
                        mallId: this.params.mallId,
                        finishFunc: () => {
                            this.getMallList();
                        },
                    }).generateDialogAsync({ parent: this });
                } else {
                    this.$router.push({
                        name: PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOnlineStore,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
.order-cloud-goods-list-table .abc-table-header {
    border-color: var(--abc-color-LY1) !important;
}
</style>
