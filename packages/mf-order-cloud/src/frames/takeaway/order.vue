<template>
    <abc-flex v-abc-loading="ecTypeLoading" class="takeaway-wrapper" gap="middle">
        <div
            v-if="openMT"
            class="takeaway-card"
        >
            <!-- 头部信息 -->
            <abc-flex gap="12" align="center">
                <img src="../../assets/images/express-logo/img-meituan.png" class="platform-logo" />
                <abc-text size="xxlarge" :bold="true">
                    美团外卖
                </abc-text>
                <div style="flex: 1;"></div>
                <sync-order-button></sync-order-button>
            </abc-flex>

            <abc-flex v-if="isTest" style="margin: 16px 0;">
                <abc-input v-model="MockOrderId" width="100" placeholder="mock订单id"></abc-input>
                <abc-select v-model="MockOrderStatus" width="120">
                    <abc-option :value="4" label="待配送"></abc-option>
                    <abc-option :value="8" label="确认收货"></abc-option>
                    <abc-option :value="9" label="订单取消"></abc-option>
                </abc-select>
                <abc-select v-model="MockLogisticStatus" width="120">
                    <abc-option :value="0" label="待发起配送"></abc-option>
                    <abc-option :value="1" label="待骑手接单"></abc-option>
                    <abc-option :value="5" label="待配送侧压单"></abc-option>
                    <abc-option :value="10" label="待骑手取货"></abc-option>
                    <abc-option :value="15" label="骑手已到店"></abc-option>
                    <abc-option :value="20" label="骑手已取货"></abc-option>
                    <abc-option :value="40" label="骑手已送达"></abc-option>
                    <abc-option :value="100" label="配送已取消"></abc-option>
                </abc-select>
                <abc-button size="small" @click="MockOrder">
                    Mock 订单
                </abc-button>
                <abc-button size="small" @click="MockOrder('all')">
                    Mock 整单售后
                </abc-button>
                <abc-button size="small" @click="MockOrder('part')">
                    Mock 部分售后
                </abc-button>
            </abc-flex>

            <template v-if="isChainAdmin">
                <abc-space size="middle" style="margin: 16px 0;">
                    <abc-statistic
                        v-for="(item, index) in statisticDataList"
                        :key="index"
                        variant="colorful"
                        theme="C7"
                        icon="s-order-1-fill"
                        style="width: 296px;"
                    >
                        <template #content>
                            <abc-flex vertical>
                                <abc-text theme="black" size="xxlarge" style="line-height: 1;">
                                    {{ item.title }}
                                </abc-text>
                                <abc-text theme="black" style="opacity: 0.4;">
                                    {{ item.content }}
                                </abc-text>
                            </abc-flex>
                        </template>
                    </abc-statistic>
                </abc-space>
                <!-- 美团店铺列表 -->
                <abc-table
                    theme="white"
                    :render-config="shopTableConfig"
                    :data-list="clinicMtOrderSummaryList"
                    cell-size="large"
                    :loading="contentLoading"
                    :show-hover-tr-bg="true"
                    :show-content-empty="true"
                    empty-content="暂无店铺数据"
                >
                    <template #icon="{ trData }">
                        <abc-table-cell>
                            <abc-icon :icon="`s-no${trData.keyId}-color`" :size="16"></abc-icon>
                        </abc-table-cell>
                    </template>
                    <template #orderCount="{ trData }">
                        <abc-table-cell>
                            <abc-text>{{ trData.orderCount }}单</abc-text>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </template>
            <template v-else>
                <!-- 消息提示 -->
                <abc-tips-card-v2
                    theme="primary"
                    class="message-tip"
                    :icon="false"
                >
                    <abc-space size="small">
                        <abc-icon icon="s-volume-fill" color="var(--abc-color-Y2)"></abc-icon>
                        <abc-text>{{ MTBannerData || '暂无消息' }}</abc-text>
                    </abc-space>
                </abc-tips-card-v2>

                <abc-space size="middle">
                    <abc-statistic
                        v-for="(item, index) in MTStatisticDataList"
                        :key="index"
                        variant="colorful"
                        theme="C7"
                        icon="s-order-1-fill"
                        style="width: 296px; background: linear-gradient(269.88deg, #fbbd01 0.1%, #fcc909 99.9%);"
                    >
                        <template #content>
                            <abc-flex vertical>
                                <abc-text theme="black" size="xxlarge" style="line-height: 1;">
                                    {{ item.title }}
                                </abc-text>
                                <abc-text theme="black" style="opacity: 0.4;">
                                    {{ item.content }}
                                </abc-text>
                            </abc-flex>
                        </template>
                    </abc-statistic>
                </abc-space>

                <!-- 订单状态统计 -->
                <abc-card class="order-status-card">
                    <abc-text size="large" bold class="order-status-card-title">
                        待办
                    </abc-text>
                    <div class="order-status-card-content">
                        <abc-flex
                            v-for="(item, index) in MTorderStatsList"
                            :key="index"
                            vertical
                            align="center"
                            gap="middle"
                            class="order-status-card-item"
                        >
                            <abc-text>
                                {{ item.text }}
                            </abc-text>
                            <abc-text size="xxlarge">
                                {{ item.value }}
                            </abc-text>
                        </abc-flex>

                        <abc-popover
                            placement="bottom-end"
                            trigger="click"
                            :width="300"
                            theme="white"
                            :popper-style="{
                                padding: '4px 0 4px 4px'
                            }"
                            @show="fetchOutStockOrders"
                        >
                            <abc-flex
                                slot="reference"
                                class="order-status-card-item cursor"
                                style="margin-left: 8px;"
                                align="center"
                                gap="middle"
                                vertical
                            >
                                <abc-text>
                                    ABC待出库
                                </abc-text>

                                <abc-text
                                    theme="warning-light"
                                    size="xxlarge"
                                >
                                    {{ waitingOutStockCount }}
                                </abc-text>

                                <abc-icon icon="s-triangle-select-fill" class="select-icon" style="color: var(--abc-color-T3);"></abc-icon>
                            </abc-flex>

                            <div v-abc-loading="outStockOrdersLoading" class="out-stock-order-list-wrapper">
                                <abc-flex
                                    v-if="!outStockOrders.length && !outStockOrdersLoading"
                                    justify="center"
                                    align="center"
                                    style="width: 100%; height: 100%; min-height: 120px;"
                                >
                                    <abc-text theme="gray">
                                        暂无待出库订单
                                    </abc-text>
                                </abc-flex>
                                <abc-list
                                    :data-list="outStockOrders"
                                    large="large"
                                    class="out-stock-order-list"
                                    :create-key="(it)=>it.id"
                                    @click-item="handleOutStockOrder"
                                >
                                    <template #default="{ item }">
                                        <abc-flex
                                            gap="small"
                                            align="center"
                                            justify="space-between"
                                            style="width: 100%;"
                                        >
                                            <abc-space>
                                                <abc-text>
                                                    {{ `#${item.daySeq}` }}
                                                </abc-text>
                                                <abc-text>
                                                    {{ item.receiverNameMask || '' }}
                                                </abc-text>
                                            </abc-space>
                                            <abc-text theme="gray" size="small">
                                                {{ item.receiverPhoneMask || '' }}
                                            </abc-text>
                                        </abc-flex>
                                    </template>
                                </abc-list>
                            </div>
                        </abc-popover>
                    </div>
                </abc-card>

                <abc-flex class="exception-stats" gap="large" style="width: 100%;">
                    <abc-card
                        v-for="(card, index) in MTOrderCardList"
                        :key="index"
                        class="exception-card"
                        padding-size="small"
                        style="padding-top: 10px;"
                    >
                        <abc-text size="large" :bold="true" style="padding: 8px 10px;">
                            {{ card.title }}
                        </abc-text>
                        <abc-list
                            :data-list="card.list"
                            size="large"
                            show-divider
                            :create-key="(it)=>it.label"
                            :hover-item-func="(item) => item.key === 'afterSaleWaitingDealCount'"
                            :no-close-border="card.noCloseBorder"
                            :scrollable="false"
                        >
                            <template #default="{ item }">
                                <abc-popover
                                    v-if="item.key === 'afterSaleWaitingDealCount'"
                                    placement="right-start"
                                    :width="300"
                                    theme="white"
                                    style="width: 100%;"
                                    :arrow-offset="14"
                                    :popper-style="{
                                        padding: '4px 0 4px 4px'
                                    }"

                                    @show="fetchAfterSaleOrders"
                                >
                                    <abc-flex
                                        slot="reference"
                                        justify="space-between"
                                        align="center"
                                        style="width: 100%; height: 34px; padding-top: 10px; margin-top: -10px;"
                                    >
                                        <abc-text>{{ item.label }}</abc-text>
                                        <abc-flex gap="8" align="center">
                                            <abc-text theme="warning-light">
                                                {{ item.value }}
                                            </abc-text>
                                            <abc-icon icon="s-b-right-line-medium" style="color: var(--abc-color-T3);"></abc-icon>
                                        </abc-flex>
                                    </abc-flex>

                                    <div v-abc-loading="afterSaleOrdersLoading" class="out-stock-order-list-wrapper">
                                        <abc-flex
                                            v-if="!afterSaleOrders.length && !afterSaleOrdersLoading"
                                            justify="center"
                                            align="center"
                                            style="width: 100%; height: 180px;"
                                        >
                                            <abc-text theme="gray">
                                                暂无售后退库订单
                                            </abc-text>
                                        </abc-flex>
                                        <abc-list
                                            :data-list="afterSaleOrders"
                                            large="large"
                                            @click-item="handleAfterSaleOrder"
                                        >
                                            <template #default="{ item }">
                                                <abc-flex
                                                    gap="small"
                                                    align="center"
                                                    justify="space-between"
                                                    style="width: 100%;"
                                                >
                                                    <abc-space>
                                                        <abc-text>
                                                            {{ `#${item.daySeq}` }}
                                                        </abc-text>
                                                        <abc-text>
                                                            {{ item.receiverNameMask || '' }}
                                                        </abc-text>
                                                    </abc-space>
                                                    <abc-text theme="gray" size="small">
                                                        {{ item.receiverPhoneMask || '' }}
                                                    </abc-text>
                                                </abc-flex>
                                            </template>
                                        </abc-list>
                                    </div>
                                </abc-popover>

                                <abc-flex
                                    v-else
                                    justify="space-between"
                                    align="center"
                                    style="width: 100%; height: 24px; padding-right: 24px;"
                                >
                                    <abc-text>{{ item.label }}</abc-text>
                                    <abc-text>
                                        {{ item.value }}
                                    </abc-text>
                                </abc-flex>
                            </template>
                        </abc-list>
                    </abc-card>
                </abc-flex>
            </template>
        </div>
        <div v-else-if="openELM" class="takeaway-card">
            <abc-flex gap="12" align="center">
                <img src="../../assets/images/express-logo/img-eleme.png" class="platform-logo" />
                <abc-text size="xxlarge" :bold="true">
                    饿了么
                </abc-text>
            </abc-flex>
            <abc-content-empty size="large" show-icon></abc-content-empty>
        </div>
        <abc-flex
            v-else-if="!ecTypeLoading"
            vertical
            justify="center"
            align="center"
            :gap="20"
            class="not-open-any"
        >
            <abc-flex vertical align="center">
                <abc-icon icon="s-emptyIcon-permission" :size="72"></abc-icon>
                <abc-text theme="gray-light">
                    暂无网店，请先接入外卖网店
                </abc-text>
            </abc-flex>

            <abc-button
                v-if="!isChainAdmin"
                v-abc-check-electron="{
                    disableTips: '当前为网页浏览器，不能进行网店绑定，请在ABC客户端中使用。', installTips: true
                }"
                icon="n-add-line-medium"
                size="large"
                @click="openAuthorizedECDialog"
            >
                新增网店
            </abc-button>
        </abc-flex>
    </abc-flex>
</template>

<script>
    import { mapGetters } from 'vuex';
    import AbcSocket from 'MfBase/single-socket';
    import * as business from 'MfFeEngine/business';
    import {
        EcShopTypeEnum, ECTypeEnum,
    } from '@/utils/constants';
    import { OrderStatusEnum } from '@/daemon/crawler/provider/meituan/constants';
    import ECOrderAPI from '@/api/order';
    import SyncOrderButton from '@/components/sync-order-button.vue';
    import { formatDate } from '@abc/utils-date';
    import DialogOrderOut from '@/components/dialog-order-out/index.js';
    import DialogOrderAfterSale from '@/components/dialog-order-after-sale/index.js';
    import ECAuthAPI from '@/api/auth';
    import { PharmacyOrderCloudRouterNameKeys } from '@/core/routes';

    export default {
        name: 'Takeaway',
        components: {
            SyncOrderButton,
        },
        inject: {
            eCOrderStat: {
                default: () => ({
                    totalOrderCount: 0,
                    totalOrderSum: 0,
                    totalMtTodoCount: 0,
                    newOrderCount: 0, // mt-新订单数量
                    pendingDeliveryCount: 0, // mt-待配送数量
                    deliveringCount: 0, // mt-配送中数量
                    deliveredCount: 0, // mt-已送达数量
                    confirmedCount: 0, // mt-已确认收货数量
                    waitingOutStockCount: 0, // mt-待处理ABC出库数量
                    afterSaleWaitingDealCount: 0, // mt-待处理售后数量
                }),
            },
        },
        data() {
            return {
                MockOrderId: '',
                MockOrderStatus: 4,
                MockLogisticStatus: 0,
                authMallList: [],
                ecTypeLoading: false,
                contentLoading: false,
                warnOrderList: [],
                totalValidOrderCount: 0,
                totalEstimatedIncome: 0,
                authorizedEcList: [],

                shopTableConfig: {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'icon',
                            label: '',
                            style: {
                                width: '40px',
                                minWidth: '40px',
                                maxWidth: '40px',
                                textAlign: 'center',
                            },
                        },
                        {
                            key: 'ecMallName',
                            label: '网店',
                            style: {
                                width: '200px',
                            },
                        },
                        {
                            key: 'clinicName',
                            label: '门店',
                            style: {
                                width: '200px',
                            },
                        },
                        {
                            key: 'totalValidOrderCount',
                            label: '已接订单',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                maxWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'totalEstimatedIncome',
                            label: '预计收入',
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                maxWidth: '120px',
                                textAlign: 'right',
                            },
                        },
                    ],
                },
                clinicMtOrderSummaryList: [],
                outStockOrders: [],
                outStockOrdersLoading: false,
                afterSaleOrders: [],
                afterSaleOrdersLoading: false,

                isTest: false,

                ecTypes: [],
                params: {
                    limit: 10,
                    offset: 0,
                    shopType: EcShopTypeEnum.O2O,
                    ecType: '',
                    bindClinicId: '',
                    ecMallId: '',
                    purchaseInfo: 1,
                },
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
            ]),
            openMT() {
                return this.authorizedEcList.some((item) => item.ecType === ECTypeEnum.MT && item.status !== 99);
            },
            openELM() {
                return false;
            },
            statisticDataList() {
                return [
                    {
                        content: '今日已接订单',
                        title: this.totalValidOrderCount || 0,
                    },
                    {
                        content: '今日预计收入（元）',
                        title: (this.totalEstimatedIncome || 0).toFixed(2),
                    },
                ];
            } ,
            MTBannerData() {
                const warnStatusList = [
                    OrderStatusEnum.newOrder,
                    OrderStatusEnum.waitingDelivery,
                    OrderStatusEnum.delivering,
                ];
                const order = this.warnOrderList.find((item) => warnStatusList.indexOf(item.orderInfo?.unifiedBasicInfo?.orderStatus) > -1);
                if (!order) return null;
                const {
                    orderInfo,
                } = order;
                const {
                    unifiedBasicInfo,
                    unifiedUserInfo,
                } = orderInfo;
                const {
                    recipientPhoneVO,
                } = unifiedUserInfo || {};
                const {
                    pcPhoneVOS,
                } = recipientPhoneVO?.pcRecipientPhoneVO || {};
                return `#${unifiedBasicInfo.daySeq} ${unifiedUserInfo.recipientName}${pcPhoneVOS?.find((it) => it.type === 1)?.phoneShow || ''} ${unifiedBasicInfo?.orderStatusDesc}`;
            },

            MTStatisticDataList() {
                return [
                    {
                        title: this.eCOrderStat.totalOrderCount || 0,
                        content: '今日订单数',
                    },
                    {
                        topTitle: '今日预计收入（元）',
                        title: (this.eCOrderStat.totalOrderSum || 0).toFixed(2),
                        content: '今日销售收入',
                    },
                ];
            },

            waitingOutStockCount() {
                return this.eCOrderStat.waitingOutStockCount || 0;
            },

            waitingAfterSaleCount() {
                return this.eCOrderStat.afterSaleWaitingDealCount || 0;
            },

            MTorderStatsList() {
                const {
                    newOrderCount,
                    pendingDeliveryCount,
                    deliveringCount,
                    deliveredCount,
                    confirmedCount,
                } = this.eCOrderStat;
                return [
                    {
                        key: 'newOrder',
                        text: '新订单',
                        value: newOrderCount,
                    },
                    {
                        key: 'waitingDelivery',
                        text: '待配送',
                        value: pendingDeliveryCount,
                    },
                    {
                        key: 'delivery',
                        text: '配送中',
                        value: deliveringCount,
                    },
                    {
                        key: 'delivered',
                        text: '已送达',
                        value: deliveredCount,
                    },
                    {
                        key: 'received',
                        text: '确认收货',
                        value: confirmedCount,
                    },
                ];
            },

            MTOrderCardList() {
                return [
                    {
                        title: '预订单',
                        list: [
                            {
                                label: '60分钟超过送达时间',
                                value: this.eCOrderStat.nearPreOrderCount,
                            },
                        ],
                    },
                    {
                        title: '配送',
                        list: [
                            {
                                label: '配送异常',
                                value: this.eCOrderStat.exceptionOrderCount,
                            },
                            {
                                label: '催单',
                                value: this.eCOrderStat.reminderOrderCount,
                            },
                        ],
                    },
                    {
                        title: '售后',
                        noCloseBorder: true,
                        list: [
                            {
                                label: '用户退款申请',
                                value: this.eCOrderStat.refundOrderCount,
                            },
                            {
                                label: '货损赔付',
                                value: this.eCOrderStat.compensateOrderCount,
                            },
                            {

                                key: 'afterSaleWaitingDealCount',
                                label: 'ABC待退库',
                                value: this.eCOrderStat.afterSaleWaitingDealCount,
                            },
                        ],
                    },
                ];
            },
        },
        async created() {
            await this.getEcTypes();
            await this.getAuthorizedEcList();
            this.isTest = location.href.indexOf('test') > -1;
            if (this.isChainAdmin) {
                this.getOrderSummaryInfo();
            }
            if (this.openMT) {
                this.fetchOrderList();
                const { socket } = AbcSocket.getSocket();
                if (business.ECOrderService) {
                    this._SocketService = new business.ECOrderService(socket);
                    this._SocketService.onECOrderStat(this.fetchOrderList);
                    this.$on('hook:beforeDestroy', () => {
                        this._SocketService?.offECOrderStat(this.fetchOrderList);
                    });
                }
            }
        },
        methods: {
            // 获取数据
            async fetchOrderList() {
                const now = new Date();
                const today = formatDate(now);

                try {
                    this.loading = true;
                    const {
                        rows,
                    } = await ECOrderAPI.fetchEcOrderList({
                        limit: 9999,
                        offset: 0,
                        ecType: ECTypeEnum.MT,
                        beginDate: today,
                        endDate: today,
                        dateType: 4,
                    });
                    this.warnOrderList = rows?.filter((order) => {
                        const warnStatusList = [
                            OrderStatusEnum.newOrder,
                            OrderStatusEnum.waitingDelivery,
                            OrderStatusEnum.delivering,
                        ];
                        return warnStatusList.includes(order.orderInfo?.unifiedBasicInfo?.orderStatus);
                    }) || [];
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            async getOrderSummaryInfo() {
                this.contentLoading = true;
                try {
                    const { data } = await ECOrderAPI.getOrderSummaryInfo();
                    const {
                        totalValidOrderCount,
                        totalEstimatedIncome,
                        clinicMtOrderSummaryList,
                    } = data || {};
                    this.totalValidOrderCount = totalValidOrderCount || 0;
                    this.totalEstimatedIncome = totalEstimatedIncome || 0;
                    this.clinicMtOrderSummaryList = clinicMtOrderSummaryList || [];
                } catch (e) {
                    console.log(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            // 获取待出库订单列表
            async fetchOutStockOrders() {
                if (this.outStockOrdersLoading || this.outStockOrders.length > 0) {
                    return;
                }
                this.outStockOrdersLoading = true;
                const now = new Date();
                const today = formatDate(now);
                const {
                    rows, total = 0,
                } = await ECOrderAPI.fetchEcOrderList({
                    limit: 9999,
                    offset: 0,
                    ecMallId: '',
                    ecType: ECTypeEnum.MT,
                    dateType: 4, // 时间类型，1：支付时间；2：打印时间； 3：发货时间
                    keyword: '',
                    orderStatus: '',
                    logisticStatus: '',
                    outStockStatus: 0,
                    beginDate: today,
                    endDate: today,
                });
                this.outStockOrders = rows ?? [];
                this.outStockOrders = this.outStockOrders.map((order) => {
                    return {
                        ...order,
                        daySeq: order.ecAdditionalInfo?.orderDaySeq || '',
                        receiverNameMask: order.receiverInfo?.receiverNameMask || '',
                        receiverPhoneMask: order.receiverInfo?.receiverPhoneMask || '',
                    };
                });
                this.outStockOrdersTotalCount = total;
                this.outStockOrdersLoading = false;
            },

            // 处理出库订单点击
            handleOutStockOrder(e, order) {
                const _dialog = new DialogOrderOut({
                    orderId: order.id,
                    orderNo: order.orderNo,
                    outStockStatus: order.outStockStatus,
                    onRefresh: this.fetchOutStockOrders,
                });
                _dialog.generateDialogAsync({
                    parent: this,
                });
            },

            // 获取售后退库订单列表
            async fetchAfterSaleOrders() {
                this.afterSaleOrdersLoading = true;
                const now = new Date();
                const today = formatDate(now);
                const {
                    rows, total = 0,
                } = await ECOrderAPI.fetchEcRefundOrderList({
                    ecType: ECTypeEnum.MT,
                    limit: 9999,
                    offset: 0,
                    shopType: 1,
                    keyword: '',
                    stockDealStatus: 0,
                    extCreatedBeginDate: today,
                    extCreatedEndDate: today,
                });
                this.afterSaleOrders = rows ?? [];
                this.afterSaleOrders = this.afterSaleOrders.map((order) => {
                    return {
                        ...order,
                        daySeq: order.orderAdditionalInfo?.orderDaySeq || '',
                        receiverNameMask: order.orderReceiverNameMask || '',
                        receiverPhoneMask: order.orderReceiverPhoneMask || '',
                    };
                });
                this.afterSaleOrdersTotalCount = total;
                this.afterSaleOrdersLoading = false;
            },

            // 处理售后退库订单点击
            handleAfterSaleOrder(e, order) {
                const _dialog = new DialogOrderAfterSale({
                    afterSaleId: order.id,
                    orderNo: order.orderNo,
                    stockDealStatus: order.stockDealStatus,
                    onRefresh: this.fetchAfterSaleOrders,
                });
                _dialog.generateDialogAsync({
                    parent: this,
                });
            },


            MockOrder(refundType) {
                const isTest = location.href.indexOf('test') > -1;
                ECOrderAPI.syncMeituanOrder({
                    ecMallId: isTest ? '3814546308482383872' : '3813939649733918720',
                    mtOrders: [
                        {
                            'businessType': 0,
                            'commonInfo': {
                                'wm_poi_order_dayseq': 1,
                                'wm_order_id_view': 3801479584072610000,
                                'wmOrderViewIdStr': this.MockOrderId,
                                'order_time': Math.ceil(Date.now() / 1000),
                                'delivery_btime': 0,
                                'estimateArrivalTime': Math.ceil(Date.now() / 1000),
                                'poiPushDay': 0,
                                'orderStatus': this.MockOrderStatus,
                                'flashOrder': false,
                                'payStatus': 0,
                                'logisticsStatus': 0,
                                'isLogisticsAbnormal': false,
                                'foodDoneStatus': 0,
                                'pickType': 0,
                                'wmOrderPayType': 0,
                                'estimateArrivalTimeLeft': 0,
                                'estimateArrivalTimeRight': 0,
                                'jointOrder': false,
                                'hasRemark': false,
                                'shippingNoThreshold': false,
                                'transferOrder': false,
                                'isPreOrder': false,
                                'isPreSale': false,
                                'logisticsScene': 0,
                                'shippingService': 0,
                                'estimatedDeliveryTime': 0,
                                'estimateArrivalTimeV2': 0,
                                'preSale': false,
                                'preOrder': false,
                            },
                            'orderInfo': {
                                'unifiedBasicInfo': {
                                    'daySeq': 1,
                                    'labels': [],
                                    'needTableWare': true,
                                    'orderTag': 0,
                                    'expectTimeVO': {
                                        'expectPrefix': '立即送达 ',
                                        'expectTimeFmt': '建议 12:09',
                                        'expectSuffix': ' 前送达',
                                        'expectType': null,
                                        'expectTimeBeforeModify': '',
                                        'expectTimeBeforeModifyTips': '',
                                        'expectTimeFmtColor': '',
                                        'deliveryBtimeFmt': '01-01 08:00',
                                    },
                                    'orderStatusDesc': '顾客已确认收货',
                                    'orderCompleteText': '',
                                    'cancelReason': '',
                                    'orderStatusText': null,
                                    'orderModifyInfoVO': null,
                                    'poiName': '恒杏园（高新天久南巷店）',
                                    'cityName': '成都市',
                                    'orderTimeFmt': '02-21 11:43',
                                    'wmOrderViewId': '3801479584072609700',
                                    'postSellerId': 0,
                                    'showConfirmButton': false,
                                    'showGPRSAutoConfirmBtn': false,
                                    'showCopyOrderButton': true,
                                    'cancelButton': {
                                        'isShow': true,
                                        'content': '取消订单并退款',
                                        'canClick': true,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'refundButton': {
                                        'isShow': true,
                                        'content': '部分退款',
                                        'canClick': true,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'showPrintBtn': true,
                                    'printCnt': '1',
                                    'fontString': null,
                                    'orderCopyContent': '2月21日  #17号单#  恒杏园（高新天久南巷店）  贾(先生)  为保护顾客隐私，电话及地址已隐藏  [葵花]风寒感冒颗粒8g*7袋/盒 (8g*7袋/盒) 2份  [感康]复方氨酚烷胺片6片*2板/盒 (6片*2板/盒) 1份  [九寨沟]荆防颗粒15g*18袋/大袋 (15g*18袋/大袋) 1份  合计：76.6元 (已付款)  在线支付订单',
                                    'wmPoiId': 24925045,
                                    'medicareTag': 0,
                                    'b2cOrder': false,
                                    'orderStatus': this.MockOrderStatus,
                                    'confirmTime': 1740109408,
                                    'preDays': 0,
                                },
                                'orderChargeBlockBO': {
                                    'giftDetails': [],
                                    'settlementItems': [
                                        {
                                            'type': 3,
                                            'name': '小计',
                                            'priceText': '￥100.00',
                                            'nameTag': null,
                                            'tipsInfo': {
                                                'isShow': false,
                                                'type': 0,
                                                'title': null,
                                                'formulaDesc': null,
                                                'desc': null,
                                                'details': null,
                                                'priceText': null,
                                                'detailUrl': null,
                                                'priceTextGreyDisplayKeyword': null,
                                            },
                                            'subSettlementItems': null,
                                            'price': 0,
                                        },
                                        {
                                            'type': 4,
                                            'name': '商家活动支出',
                                            'priceText': '-￥14.00',
                                            'nameTag': null,
                                            'tipsInfo': {
                                                'isShow': true,
                                                'type': 1,
                                                'title': '活动支出详情',
                                                'formulaDesc': null,
                                                'desc': null,
                                                'details': [
                                                    {
                                                        'name': '购买[品信药业]小儿肠胃康颗粒5g*6袋/盒原价10元现价4元',
                                                        'priceText': '-￥10',
                                                        'subDetails': null,
                                                    },
                                                    {
                                                        'name': '减配送费4.0元',
                                                        'priceText': '-￥4.00',
                                                        'subDetails': null,
                                                    },
                                                ],
                                                'priceText': '-￥14.00',
                                                'detailUrl': null,
                                                'priceTextGreyDisplayKeyword': null,
                                            },
                                            'subSettlementItems': null,
                                            'price': 0,
                                        },
                                        {
                                            'type': 5,
                                            'name': '佣金',
                                            'priceText': '-￥5.40',
                                            'nameTag': null,
                                            'tipsInfo': {
                                                'isShow': true,
                                                'type': 2,
                                                'title': '佣金',
                                                'formulaDesc': null,
                                                'desc': null,
                                                'details': [
                                                    {
                                                        'name': '计算规则详见协议和对账单',
                                                        'priceText': null,
                                                        'subDetails': null,
                                                    },
                                                ],
                                                'priceText': null,
                                                'detailUrl': null,
                                                'priceTextGreyDisplayKeyword': null,
                                            },
                                            'subSettlementItems': null,
                                            'price': 0,
                                        },
                                        {
                                            'type': 6,
                                            'name': '配送服务费',
                                            'priceText': '-￥3.00',
                                            'nameTag': null,
                                            'tipsInfo': {
                                                'isShow': true,
                                                'type': 3,
                                                'title': '配送服务费',
                                                'formulaDesc': null,
                                                'desc': '',
                                                'details': [
                                                    {
                                                        'name': '距离收费（阶梯计费）',
                                                        'priceText': '',
                                                        'subDetails': [],
                                                    },
                                                    {
                                                        'name': '导航距离0.84公里',
                                                        'priceText': '￥3.00',
                                                        'subDetails': [
                                                            {
                                                                'name': '0-50公里(3.00元)',
                                                                'priceText': null,
                                                            },
                                                        ],
                                                    },
                                                ],
                                                'priceText': null,
                                                'detailUrl': null,
                                                'priceTextGreyDisplayKeyword': null,
                                            },
                                            'subSettlementItems': null,
                                            'price': 0,
                                        },
                                        {
                                            'type': 8,
                                            'name': '店铺环保捐赠',
                                            'priceText': '-￥0.02',
                                            'nameTag': null,
                                            'tipsInfo': {
                                                'isShow': true,
                                                'type': 2,
                                                'title': null,
                                                'formulaDesc': null,
                                                'desc': '商家参与青山捐助计划，每单将捐献一定金额助力环保',
                                                'details': null,
                                                'priceText': null,
                                                'detailUrl': null,
                                                'priceTextGreyDisplayKeyword': null,
                                            },
                                            'subSettlementItems': null,
                                            'price': 0,
                                        },
                                        {
                                            'type': 17,
                                            'name': '本单顾客实际支付（已支付）',
                                            'priceText': '￥91.5',
                                            'nameTag': null,
                                            'tipsInfo': {
                                                'isShow': false,
                                                'type': 0,
                                                'title': null,
                                                'formulaDesc': null,
                                                'desc': null,
                                                'details': null,
                                                'priceText': null,
                                                'detailUrl': null,
                                                'priceTextGreyDisplayKeyword': null,
                                            },
                                            'subSettlementItems': null,
                                            'price': 0,
                                        },
                                    ],
                                    'settlementAmounts': [
                                        {
                                            'type': 5,
                                            'name': '本单预计收入',
                                            'priceText': '￥77.58',
                                            'heighlight': false,
                                            'tipsInfo': null,
                                        },
                                    ],
                                    'finalSettlementAmount': 77.58,
                                },
                                'orderFoodBlockBO': {
                                    'foodKindsAndNum': '共4件商品',
                                    'cartDetailVOs': [
                                        {
                                            'cartName': '1号口袋',
                                            'details': [
                                                {
                                                    'foodName': '[云昆]小儿感冒颗粒12g*6袋/盒',
                                                    'count': 3,
                                                    'originFoodPrice': '￥10.00',
                                                    'foodId': isTest ? 29541896177 : 29541896190,
                                                    'detailId': '8051350860363',
                                                    'totalFoodPrice': '￥30.00',
                                                    'cartId': 0,
                                                    'foodRealPayTotalPrice': '￥20.00',
                                                    'unit': '盒',
                                                },
                                                {
                                                    'foodName': '[同仁堂]小儿感冒口服液10ml*10支/盒',
                                                    'count': 1,
                                                    'originFoodPrice': '￥10.00',
                                                    'foodId': isTest ? 29541896176 : 29541896192,
                                                    'detailId': '8051350860427',
                                                    'totalFoodPrice': '￥10.00',
                                                    'cartId': 0,
                                                    'foodRealPayTotalPrice': '￥10.00',
                                                    'unit': '盒',
                                                },
                                                {
                                                    'foodName': '[亚宝]三黄片24片/盒',
                                                    'count': 1,
                                                    'originFoodPrice': '￥10.00',
                                                    'foodId': isTest ? 34743413505 : 29541896193,
                                                    'detailId': '8051350860491',
                                                    'totalFoodPrice': '￥10.00',
                                                    'cartId': 0,
                                                    'foodRealPayTotalPrice': '￥10.00',
                                                    'unit': '瓶',
                                                },
                                                {
                                                    'foodName': '[一正]小儿热速清口服液10ml*6支/盒',
                                                    'count': 2,
                                                    'originFoodPrice': '￥10.00',
                                                    'foodId': isTest ? 29541896175 : 30496941435,
                                                    'detailId': '8051350860492',
                                                    'totalFoodPrice': '￥20.00',
                                                    'cartId': 0,
                                                    'foodRealPayTotalPrice': '￥20.00',
                                                    'unit': '盒',
                                                },
                                                {
                                                    'foodName': '[群山]小儿感冒宁糖浆100ml/瓶/盒',
                                                    'count': 3,
                                                    'originFoodPrice': '￥10.00',
                                                    'foodId': isTest ? 29540075412 : 29541896190,
                                                    'detailId': '8051350860493',
                                                    'totalFoodPrice': '￥30.00',
                                                    'cartId': 0,
                                                    'foodRealPayTotalPrice': '￥30.00',
                                                    'unit': '盒',
                                                },
                                            ],
                                        },
                                    ],
                                    'remarkInfo': {
                                        'remark': '',
                                    },
                                    'invoiceInfo': {
                                        'isShow': false,
                                        'invoiceTitle': null,
                                        'invoiceTaxpayerId': null,
                                    },
                                    'foodRealPayExtInfo': {
                                        'foodRealPayShowType': 1,
                                        'foodRealPayNoticeMsg': '拟计算为用户实付价格 仅供参考',
                                    },
                                },
                                'unifiedReminderInfo': {
                                    'orderRemindRecordVOs': null,
                                    'invalidTips': null,
                                    'isShowReplyBtn': null,
                                },
                                'unifiedUserInfo': {
                                    'recipientName': '贾(先生)',
                                    'wmUserId': *********,
                                    'recipientPhoneVO': {
                                        'pcRecipientPhoneVO': {
                                            'phoneTips': '',
                                            'pcPhoneVOS': [
                                                {
                                                    'type': 2,
                                                    'name': '隐私号码',
                                                    'phoneShow': '18428942693 转 9819',
                                                    'queryPrivacyPhoneButtonVO': null,
                                                    'encrypted': 0,
                                                },
                                                {
                                                    'type': 2,
                                                    'name': '备用号码',
                                                    'phoneShow': '17882960619 转 1526',
                                                    'queryPrivacyPhoneButtonVO': null,
                                                    'encrypted': 0,
                                                },
                                                {
                                                    'type': 1,
                                                    'name': '顾客电话',
                                                    'phoneShow': '手机尾号0070',
                                                    'queryPrivacyPhoneButtonVO': {
                                                        'isShow': false,
                                                        'content': null,
                                                        'canClick': false,
                                                        'tips': null,
                                                        'showType': 2,
                                                    },
                                                    'encrypted': 0,
                                                },
                                            ],
                                        },
                                    },
                                    'customerAddressVO': {
                                        'isShow': true,
                                        'isHide': false,
                                        'recipientAddress': '为保护用户隐私具体地址已隐藏',
                                        'isShowMap': false,
                                        'distanceAndAddressVO': {
                                            'inArea': 0,
                                            'addressLatitude': 30590665,
                                            'addressLongitude': 104078485,
                                            'poiLatitude': 30592654,
                                            'poiLongitude': 104073244,
                                            'orderDistance': 836,
                                        },
                                    },
                                    'showIMBtn': false,
                                    'rxOrder': false,
                                    'customerLabels': [
                                        {
                                            'type': 1,
                                            'text': '门店新客',
                                        },
                                    ],
                                },
                                'unifiedLogisticsInfo': {
                                    'isShow': true,
                                    'title': '',
                                    'selfLogisticsVO': {
                                        'isShow': false,
                                        'statusDesc': null,
                                        'expressInfoVO': null,
                                    },
                                    'dispatcherInfoVO': {
                                        'dispatcherId': '45595869',
                                        'isShow': true,
                                        'dispatcherName': '徐昆',
                                        'dispatcherTeam': '',
                                        'phoneQueryBtn': {
                                            'isShow': true,
                                            'content': '查看骑手电话',
                                            'canClick': null,
                                            'tips': null,
                                            'showType': 0,
                                        },
                                        'dispatcherPhoneInfo': {
                                            'isShow': null,
                                            'showType': 0,
                                            'showRealPhoneBtn': false,
                                            'dispatcherPhoneShow': '',
                                            'orgLeaderPhoneShow': '',
                                            'orgEmergencyPhoneShow': '',
                                        },
                                    },
                                    'logisticsHistoryVO': {
                                        'lastTimeFormat': '11:53',
                                        'lastStatusDesc': '订单已送达',
                                        'isShow': true,
                                    },
                                    'showShippingFee': true,
                                    'deliveryFeeVO': {
                                        'isShow': false,
                                        'showShippingFee': false,
                                        'shippingFee': 0,
                                        'couponAmount': 0,
                                        'activityName': null,
                                        'activityAmount': 0,
                                        'extraFee': 0,
                                        'tipAmount': 0,
                                        'payAmount': 0,
                                        'payType': 0,
                                        'shippingFeeBase': 0,
                                        'bmlAmount': 0,
                                    },
                                    'poiShippingFeeVO': {
                                        'isShow': false,
                                        'poiShippingFee': 0,
                                        'tipAmount': 0,
                                    },
                                    'cancelDeliveryButtonVO': {
                                        'isShow': false,
                                        'content': '取消配送',
                                        'canClick': null,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'isShowLocationIcon': true,
                                    'evaluateDispatcherButtonVO': {
                                        'isShow': true,
                                        'content': '评价骑手',
                                        'canClick': null,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'logisticsRightVO': {
                                        'isShow': null,
                                        'rightsIcon': null,
                                        'rightsContent': null,
                                        'rightsColor': null,
                                        'rightsBgColor': null,
                                        'rightsValid': null,
                                    },
                                    'sendDeliveryButtonVO': {
                                        'isShow': false,
                                        'content': '发起配送',
                                        'zbResourceInfoVOListAndRights': null,
                                    },
                                    'logisticsTimeVO': {
                                        'isShow': false,
                                        'showType': 0,
                                        'showTime': 0,
                                        'timerPrefix': null,
                                        'timerSuffix': null,
                                        'fixedTimeDesc': null,
                                    },
                                    'reportExceptionButtonVO': {
                                        'isShow': false,
                                        'content': '骑手未到店投诉骑手',
                                        'canClick': null,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'addFeeButtonVO': {
                                        'isShow': false,
                                        'content': '',
                                        'canClick': null,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'delayDispatchInfo': {
                                        'isShow': false,
                                        'delayDispatchTimeVO': null,
                                        'delayDispatchButton': null,
                                    },
                                    'juheDeliveryButtonVO': {
                                        'isShow': false,
                                        'content': null,
                                        'canClick': null,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'sendSelfDeliveryButtonVO': {
                                        'isShow': false,
                                        'content': null,
                                        'canClick': null,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'uploadSelfDeliveryButtonVO': {
                                        'isShow': false,
                                        'content': '',
                                        'canClick': null,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'changeToSelfDeliveryButtonVO': {
                                        'isShow': false,
                                        'content': null,
                                        'canClick': null,
                                        'tips': null,
                                        'showType': 0,
                                    },
                                    'logisticsCode': '1003',
                                    'logisticsStatus': this.MockLogisticStatus,
                                    'logisticsId': 101,
                                    'ptGray': true,
                                    'isKS': true,
                                    'hasJuheAndZB': false,
                                    'shippingService': 1020,
                                    'tipAmount': 0,
                                    'shippingFeeErrorMsg': null,
                                    'ptTimeShowVO': {
                                        'show': false,
                                        'estimatedAllocationDesc': null,
                                    },
                                    'ownRights': false,
                                    'deliveryTipAmount': 0,
                                    'deliveryShippingFee': 0,
                                },
                                'unifiedMealInfo': {
                                    'isShow': true,
                                    'dispatchExceptionTips': '',
                                    'showFoodDoneInfo': true,
                                    'curSysTime': 1740131913,
                                    'pcFoodDoneInfoVO': {
                                        'title': '备货时长',
                                        'foodDoneStatusDesc': '已完成',
                                        'pickTimeInfo': {
                                            'isShow': true,
                                            'showType': 3,
                                            'showTime': 0,
                                            'timerPrefix': null,
                                            'timerSuffix': null,
                                            'fixedTimeDesc': '00:02:50',
                                        },
                                        'suggestPickTimeText': '',
                                        'speedRefundPickTimer': {
                                            'isShow': false,
                                            'showType': 0,
                                            'showTime': 0,
                                            'timerPrefix': null,
                                            'timerSuffix': null,
                                            'fixedTimeDesc': null,
                                        },
                                    },
                                    'foodDoneButtonVO': {
                                        'isShow': false,
                                        'content': null,
                                        'canClickButtonTime': 0,
                                    },
                                    'prepareStatus': 2,
                                },
                                'orderRefundBlockBO': refundType === 'part' ? {
                                    'refundTitle': '退款信息',
                                    'refundGoodsInfoList': [
                                        {
                                            'titleVO': {
                                                'title': '商家通过部分退款申请',
                                                'opTimeFmt': formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
                                            },
                                            'moneyVO': null,
                                            'refundFoodItemInfolist': null,
                                            'refundReasonVO': null,
                                            'refundOperationDescVO': null,
                                            'rejectReasonVO': null,
                                            'customerPictures': null,
                                            'returnWayInfo': null,
                                            'returnTimeInfo': null,
                                            'merchantOrCServicePics': null,
                                            'rejectBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'onlyRefundBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'agreeBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'countdownTime': null,
                                            'recordViewId': '14383744134123123',
                                            'serviceType': 1,
                                            'status': 21,
                                            'isApplyOrAppeal': false,
                                        },
                                        {
                                            'titleVO': {
                                                'title': '商家发起部分退款',
                                                'opTimeFmt': formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
                                            },
                                            'moneyVO': {
                                                'title': '退款金额',
                                                'money': 19,
                                                'moneyTips': '查看计价规则',
                                                'priceRule': '部分退款时单个商品的可退金额，按照优惠活动等比均摊计算得出，打包袋和配送费不参与均摊。',
                                            },
                                            'refundFoodItemInfolist': [
                                                {
                                                    'foodName': '[太极]藿香正气口服液10ml*10支/盒',
                                                    'refundPrice': 19.8000,
                                                    'count': 2,
                                                    'countDesc': 'x2',
                                                    'totalPrice': 19.8000,
                                                },
                                            ],
                                            'refundReasonVO': {
                                                'title': '退款理由',
                                                'reason': '部分商品已售完',
                                            },
                                            'refundOperationDescVO': null,
                                            'rejectReasonVO': null,
                                            'customerPictures': null,
                                            'returnWayInfo': null,
                                            'returnTimeInfo': null,
                                            'merchantOrCServicePics': null,
                                            'rejectBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'onlyRefundBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'agreeBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'countdownTime': null,
                                            'recordViewId': '14383744134123123',
                                            'serviceType': 1,
                                            'status': 1,
                                            'isApplyOrAppeal': true,
                                        },
                                    ],
                                    'refundDescUrl': 'https://page.meituan.net/html/1706845048743_ee4899/index.html',
                                } : refundType === 'all' ? {
                                    'refundTitle': '退款信息',
                                    'refundGoodsInfoList': [
                                        {
                                            'titleVO': {
                                                'title': '商家通过部分退款申请',
                                                'opTimeFmt': formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
                                            },
                                            'moneyVO': null,
                                            'refundFoodItemInfolist': null,
                                            'refundReasonVO': null,
                                            'refundOperationDescVO': null,
                                            'rejectReasonVO': null,
                                            'customerPictures': null,
                                            'returnWayInfo': null,
                                            'returnTimeInfo': null,
                                            'merchantOrCServicePics': null,
                                            'rejectBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'onlyRefundBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'agreeBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'countdownTime': null,
                                            'recordViewId': '14383744134123123',
                                            'serviceType': 1,
                                            'status': 21,
                                            'isApplyOrAppeal': false,
                                        },
                                        {
                                            'titleVO': {
                                                'title': '商家发起部分退款',
                                                'opTimeFmt': formatDate(new Date(), 'YYYY-MM-DD HH:mm'),
                                            },
                                            'moneyVO': {
                                                'title': '退款金额',
                                                'money': 19,
                                                'moneyTips': '查看计价规则',
                                                'priceRule': '部分退款时单个商品的可退金额，按照优惠活动等比均摊计算得出，打包袋和配送费不参与均摊。',
                                            },
                                            'refundFoodItemInfolist': [],
                                            'refundReasonVO': {
                                                'title': '退款理由',
                                                'reason': '部分商品已售完',
                                            },
                                            'refundOperationDescVO': null,
                                            'rejectReasonVO': null,
                                            'customerPictures': null,
                                            'returnWayInfo': null,
                                            'returnTimeInfo': null,
                                            'merchantOrCServicePics': null,
                                            'rejectBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'onlyRefundBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'agreeBtn': {
                                                'isShow': false,
                                                'content': null,
                                                'canClick': false,
                                                'type': 0,
                                            },
                                            'countdownTime': null,
                                            'recordViewId': '14383744134123123',
                                            'serviceType': 1,
                                            'status': 1,
                                            'isApplyOrAppeal': true,
                                        },
                                    ],
                                    'refundDescUrl': 'https://page.meituan.net/html/1706845048743_ee4899/index.html',
                                } : {
                                    'refundTitle': null,
                                    'refundGoodsInfoList': [],
                                    'refundDescUrl': 'https://page.meituan.net/html/1706845048743_ee4899/index.html',
                                },
                                'orderCansunBlockBO': {
                                    'isShow': false,
                                    'canSunRecords': null,
                                },
                            },
                            'orderNo': 17,
                        },
                    ],
                });
            },

            async getEcTypes() {
                try {
                    this.ecTypeLoading = true;
                    const { rows } = await ECAuthAPI.fetchEcTypes({
                        ...this.params,
                    });
                    this.ecTypes = rows || [];
                } catch (err) {
                    console.error(err);
                } finally {
                    this.ecTypeLoading = true;
                }
            },

            async getAuthorizedEcList() {
                try {
                    this.ecTypeLoading = true;
                    const res = await ECAuthAPI.fetchBindAuthList({
                        offset: 0,
                        limit: 1000,
                        onlyUnexpired: 0, // 在授权有效期内
                    });
                    this.authorizedEcList = res?.rows || [];
                } catch (err) {
                    console.error(err);
                } finally {
                    this.ecTypeLoading = false;
                }
            },

            openAuthorizedECDialog() {
                this.$router.push({
                    name: PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOnlineStore,
                });
            },
        },
    };
</script>

<style lang="scss" scoped>
.takeaway-wrapper {
    width: 100%;
    height: calc(100% - 48px);
    overflow-y: scroll;

    .takeaway-card {
        flex: 1;
        max-width: 1000px;
        padding: 40px;

        &:not(:first-child) {
            border-left: 1px solid var(--abc-color-P6);
        }

        .platform-logo {
            width: 48px;
            height: 48px;
            border: 1px solid var(--abc-color-P6);
            border-radius: var(--abc-border-radius-medium);
        }

        .message-tip {
            margin: 16px 0;
        }

        .order-stats {
            margin: 24px 0;

            ::v-deep(.data-statistics-card) {
                height: auto;
            }
        }

        .order-status-card {
            padding: 20px 8px 16px;
            margin-top: 16px;
            background: linear-gradient(180deg, rgba(251, 188, 1, 0.04) 0%, rgba(251, 188, 1, 0) 34.67%);

            .order-status-card-title {
                padding: 0 12px;
            }

            .order-status-card-item {
                position: relative;
                padding: 12px;

                &.cursor {
                    cursor: pointer;
                    border-radius: var(--abc-border-radius-small);

                    &:hover {
                        background-color: var(--abc-color-cp-grey4);
                    }
                }

                .select-icon {
                    position: absolute;
                    right: 12px;
                    bottom: 12px;
                }

                &.is-show-popover {
                    background-color: var(--abc-color-cp-grey4);
                }
            }

            .order-status-card-content {
                display: grid;
                grid-template-columns: repeat(6, 1fr);

                .order-status-card-item:not(:last-child) {
                    border-right: 1px solid var(--abc-color-P6);
                }
            }
        }

        .exception-stats {
            margin-top: 24px;

            .exception-card {
                flex: 1;
                margin-top: 0 !important;
                background: linear-gradient(179.32deg, rgba(251, 188, 1, 0.04) 0.58%, rgba(251, 188, 1, 0) 22.27%);

                .exception-card-item {
                    margin-top: 12px;

                    > div {
                        height: 44px;
                        padding: 0 10px;
                        border-radius: var(--abc-border-radius-small);

                        &:hover {
                            background: var(--abc-color-cp-grey4);
                        }
                    }
                }
            }
        }
    }

    .not-open-any {
        width: 100%;
        height: 100%;
    }
}

// 出库订单列表样式
.out-stock-order-list-wrapper {
    width: 245px;
    height: 180px;
    padding: 0 0;
    overflow-x: hidden;
    overflow-y: scroll;

    .out-stock-order-list {
        height: 100%;
        overflow-y: auto;
    }
}
</style>
