<template>
    <abc-layout preset="page-table" style="height: calc(100% - 48px);">
        <abc-layout-header>
            <stat-toolbar
                :enable-features="toolbarFeatures"
                :date-filter.sync="params.dateFilter$"
                :dimension-filter.sync="params.dimension"
                :dimension-picker-options="dimensionOptions"
                :handle-export="handleExports"
                :export-task-type="exportTaskType"
                @change-dimension="handleDimensionChange"
                @change-date="handleDateChange"
            >
                <template slot="prepend">
                    <abc-date-picker
                        v-model="computedDateRange"
                        :picker-options="pickerOptions"
                        type="daterange"
                        value-format="YYYY-MM-DD"
                        :clearable="false"
                        placeholder="选择日期范围"
                    >
                    </abc-date-picker>
                </template>
                <template slot="custom-clinic">
                    <filter-select
                        v-if="isChainAdmin"
                        v-model="params.clinicId"
                        :width="110"
                        :inner-width="110"
                        :options="clinicOptions"
                        placeholder="门店"
                        @change="handleClinicChange"
                    >
                    </filter-select>
                </template>
                <template slot="compose">
                    <abc-input
                        v-model="params.orderNo"
                        placeholder="订单编号"
                        :width="130"
                        clearable
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                    </abc-input>
                    <abc-input
                        v-model="params.receiverNameOrPhone"
                        placeholder="联系人/尾号"
                        :width="130"
                        clearable
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                    </abc-input>
                    <filter-select
                        v-model="params.chargeType"
                        :width="100"
                        :inner-width="100"
                        :options="chargeTypeOptions"
                        placeholder="接单/退单"
                        @change="getTableData"
                    >
                    </filter-select>
                    <abc-input
                        v-if="showDetailTab"
                        v-model="params.keyWord"
                        placeholder="网店商品名称/店内码"
                        :width="180"
                        clearable
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                    </abc-input>
                    <employee-selector
                        v-model="params.sellerId"
                        clearable
                        :width="100"
                        :inner-width="100"
                        placeholder="销售人"
                        :employees="employeeList"
                        @change="getTableData"
                    ></employee-selector>
                </template>
            </stat-toolbar>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="takeawayOrderStatisticsTableRef"
                theme="white"
                :loading="loading"
                :render-config="tableRenderHeader"
                :data-list="list"
                :custom-tr-key="(e)=> e.id || e.orderId"
                :summary-render-keys="summaryRenderKeys"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="params"
                :count="totalCount"
                :show-total-page="false"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li v-html="summaryInfoHtml"></li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import StatToolbar from 'MfBase/stat-toolbar';
    import {
        mapGetters,
    } from 'vuex';
    import ECStatisticsAPI from '@/api/statistics';
    import FilterSelect from 'MfBase/filter-select';
    import PickerOptions from 'MfBase/picker-options';
    import DateParamsMixins from 'MfBase/date-params-mixin';
    import EmployeeSelector from 'MfBase/employee-selector';
    import ExportService from 'MfBase/export-service';
    import { formatDate } from '@abc/utils-date';
    import { debounce } from 'MfBase/lodash';
    import {
        getSummaryRenderKeys, resolveHeader,
    } from 'MfBase/table';

    export default {
        components: {
            StatToolbar,
            FilterSelect,
            EmployeeSelector,
        },

        mixins: [PickerOptions, DateParamsMixins],

        data() {
            return {
                params: {
                    dimension: 'sheet',
                    clinicId: '',
                    chargeType: '',
                    sellerId: '',
                    keyWord: '',
                    orderNo: '',
                    receiverNameOrPhone: '',
                    pageIndex: 0,
                    pageSize: 10,
                },
                totalCount: 0,
                loading: false,
                summaryInfoHtml: '',
                list: [],
                tableHeader: [],
                dimensionOptions: [
                    {
                        label: 'sheet',
                        name: '单据',
                    },
                    {
                        label: 'detail',
                        name: '商品',
                    },
                ],
                chargeTypeOptions: [
                    {
                        name: '接单',
                        id: '0',
                    },
                    {
                        name: '退单',
                        id: '10',
                    },
                ],
                exportTaskType: 'b2c-order-takeout',
            };
        },

        computed: {
            ...mapGetters(['currentClinic', 'subClinics', 'isChainAdmin']),
            ...mapGetters([
                'employeeList',
            ]),
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.currentClinic?.clinicId;
            },
            clinicOptions() {
                return this.subClinics.filter((item) => item.chainAdmin !== 1).map((item) => ({
                    ...item,
                    name: item.shortName || item.name,
                }));
            },

            showDetailTab() {
                return this.params.dimension === 'detail';
            },
            tableRenderHeader() {
                const list = resolveHeader(
                    this.tableHeader,
                    {},
                    true,
                );
                list.forEach((item) => {
                    item.children.forEach((child) => {
                        child.autoSort = child.sortable;
                    });
                    item.autoSort = item.sortable;
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list,
                };
            },
            summaryRenderKeys() {
                return getSummaryRenderKeys(this.tableHeader);
            },
            computedDateRange: {
                get() {
                    return this.params.dateFilter$.dateRange;
                },
                set(val) {
                    this.params.dateFilter$ = {
                        begin: val[0],
                        end: val[1],
                        dateRange: [...val],
                    };
                    this.handleDateChange();
                },
            },
            toolbarFeatures() {
                const features = [StatToolbar.Feature.EXPORT, StatToolbar.Feature.DIMENSION];
                return features;
            },
        },

        watch: {
            'params.orderNo': {
                handler() {
                    this._getTableData();
                },
            },
            'params.receiverNameOrPhone': {
                handler() {
                    this._getTableData();
                },
            },
            'params.keyWord': {
                handler() {
                    this._getTableData();
                },
            },
        },

        created() {
            this.exportService = new ExportService();
            this._getTableData = debounce(this.getTableData, 500, true);
        },

        beforeDestroy() {
            this.exportService?.destroy();
        },

        mounted() {
            this.initDate();
        },

        methods: {
            initDate() {
                const date = formatDate(new Date());

                const dateParams = {
                    begin: date,
                    end: date,
                    dateRange: [ date, date ],
                };

                this.params.dateFilter$ = dateParams;
            },
            handleMounted(data) {
                this.params.pageSize = (data.paginationLimit - 1) || 10;
                this.getTableData();
            },

            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            handleDimensionChange() {
                this.getTableData();
            },

            handleDateChange() {
                this.getTableData();
            },

            handleClinicChange() {
                this.getTableData();
            },

            async handleExports() {
                const params = this.getTableParams();
                delete params.offset;
                delete params.size;
                try {
                    await this.exportService.startExport(this.exportTaskType, params);
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            getTableParams() {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                    pageSize: limit, pageIndex, keyWord, orderNo, receiverNameOrPhone, chargeType, sellerId,
                } = this.params;
                const { queryClinicId: clinicId } = this;

                const { chainId } = this;
                const offset = pageIndex * limit;

                return {
                    chainId,
                    clinicId,
                    beginDate,
                    endDate,
                    keyWord: this.showDetailTab ? keyWord : undefined,
                    orderNo,
                    chargeType,
                    receiverNameOrPhone,
                    sellerId,
                    offset,
                    limit,
                };
            },

            transTemplateToTotalInfo(data = [], template = '') {
                if (!template) return '';
                let index = 0;
                const result = template.replace(
                    RegExp('%s', 'g'),
                    () =>
                        `<span style="color: #000; font-weight: bold">${
                            index === 0 ?
                                data[index++] :
                                parseFloat(data[index++] || 0).toFixed(2)
                        }</span>`,
                );
                return result;
            },

            async getTableData(resetPageParams = true) {
                await this.$nextTick();
                this.summaryInfoHtml = '';
                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }
                const params = this.getTableParams();
                this.loading = true;

                const { dimension } = this.params;
                if (dimension === 'sheet') {
                    try {
                        const {
                            data, total, header,
                        } = await ECStatisticsAPI.getTakeawayOrder(params);
                        this.list = data || [];
                        this.tableHeader = header;
                        this.totalCount = total?.count || 0;
                        this.summaryInfoHtml = this.transTemplateToTotalInfo(total?.data || [], total?.template || '');
                    } catch (err) {
                        this.summaryInfoHtml = '';
                        this.loading = false;
                        console.log(err);
                    } finally {
                        this.loading = false;
                    }
                } else if (dimension === 'detail') {
                    try {
                        const {
                            data, total, header,
                        } = await ECStatisticsAPI.getTakeawayProduct(params);
                        this.list = data || [];
                        this.tableHeader = header || [];
                        this.totalCount = total?.count || 0;
                        this.summaryInfoHtml = this.transTemplateToTotalInfo(total?.data || [], total?.template || '');
                    } catch (err) {
                        this.summaryInfoHtml = '';
                        this.loading = false;
                        console.log(err);
                    } finally {
                        this.loading = false;
                    }
                }
            },
        },

    };
</script>
