<template>
    <abc-layout class="ec-ship-print-setting-wrapper">
        <abc-layout-content style="padding: 0;">
            <abc-layout has-sidebar>
                <abc-layout-content style="flex: 1; padding: 0;">
                    <abc-manage-page>
                        <abc-form
                            ref="printForm"
                            label-width="120"
                            item-block
                            label-position="left"
                        >
                            <abc-form-item label="买家信息">
                                <abc-space :size="16">
                                    <abc-checkbox
                                        v-model="postData.receiverNameEnable"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleChange"
                                    >
                                        收件人
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.receiverPhoneEnable"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleChange"
                                    >
                                        收件电话
                                    </abc-checkbox>
                                    <abc-checkbox
                                        v-model="postData.receiverAddressEnable"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleChange"
                                    >
                                        收件地址
                                    </abc-checkbox>
                                </abc-space>
                            </abc-form-item>
                            <abc-form-item label="店铺名称">
                                <abc-input
                                    v-model="postData.mallName"
                                    v-abc-focus-selected
                                    :max-length="20"
                                    trim
                                    :width="260"
                                    @change="handleChange"
                                >
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item label="卖家电话">
                                <abc-input
                                    v-model="postData.sellerPhone"
                                    trim
                                    :width="260"
                                    @change="handleChange"
                                ></abc-input>
                            </abc-form-item>
                            <abc-form-item
                                label="发货地址-展示"
                                :custom-label-style="{
                                    height: '30px', lineHeight: '30px'
                                }"
                                style="align-items: normal;"
                            >
                                <seller-address-dropdown v-model="postData.sellerAddress" :width="260" @change="handleChange">
                                </seller-address-dropdown>
                            </abc-form-item>
                        </abc-form>
                        <template slot="footer">
                            <abc-button :disabled="isDisabled" :loading="btnLoading" @click="handleSave">
                                保存
                            </abc-button>
                        </template>
                    </abc-manage-page>
                </abc-layout-content>
                <abc-layout-sidebar width="640">
                    <div class="print-preview-layout">
                        <img src="~assets/images/icon/<EMAIL>" alt="" class="preview-img" />
                        <div class="preview-tab">
                            <div
                                v-for="(item, index) in previewTabs"
                                :key="item.label"
                                class="preview-tab-item"
                                :class="{ 'preview-tab-item__active': index === currentPreviewTab }"
                                @click="handleClickTab(index)"
                            >
                                {{ item.label }}
                            </div>
                        </div>
                        <div
                            ref="abcPage"
                            class="preview-html-wrapper abc-page abc-page_preview has-tab"
                            :class="{
                                'is-76-preview-layout': currentPreviewTab === 0,
                                'is-a4-preview-layout': currentPreviewTab === 1,
                                'is-a5-preview-layout': currentPreviewTab === 2,
                            }"
                        >
                            <div class="preview-html">
                                <div ref="previewMountPoint"></div>
                            </div>
                        </div>
                        <slot></slot>
                    </div>
                </abc-layout-sidebar>
            </abc-layout>
        </abc-layout-content>
    </abc-layout>
</template>

<script>
    import {
        AbcManagePage,
    } from 'MfBase/abc-manage';
    import ECOrderAPI from '../../api/order';
    import sellerAddressDropdown from '../../components/seller-address-dropdown.vue';
    import { isEqual } from 'MfBase/lodash';
    import { clone } from '@abc/utils';

    export default {
        name: 'ECShipPrintConfig',
        components: {
            AbcManagePage,
            sellerAddressDropdown,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                postData: {
                    mallName: '',
                    receiverAddressEnable: 1,
                    receiverNameEnable: 1,
                    receiverPhoneEnable: 1,
                    sellerAddress: {
                        address: {
                            city: '',
                            cityCode: '',
                            country: '',
                            countryCode: '',
                            detail: '',
                            district: '',
                            districtCode: '',
                            province: '',
                            provinceCode: '',
                            town: '',
                            townCode: '',
                        },
                        mobile: '',
                        mobileCountryCode: '',
                        name: '',
                        phone: '',
                        phoneCountryCode: '',
                    },
                    sellerPhone: '',
                },
                btnLoading: false,
                isDisabled: false,
                currentPreviewTab: 0,

                shipperContactList: [],
            };
        },
        computed: {
            previewTabs() {
                return [
                    {
                        label: '热敏一联单',
                        value: '76',
                    },
                    {
                        label: 'A4横版',
                        value: 'A4',
                    },
                    {
                        label: 'A5横版',
                        value: 'A5',
                    },
                ];
            },
        },
        watch: {
            postData: {
                handler(val) {
                    this.isDisabled = isEqual(val, this._cachePostData);
                },
                deep: true,
            },
        },
        created() {
            this.initHandler();
        },
        beforeDestroy() {
            this.destroyPrintInstanceStyle();
            this.$destroyed = true;
        },
        methods: {
            async initHandler() {
                try {
                    this.contentLoading = true;
                    await this.fetchShipPrintConfig();
                    await this.fetchShipperContactList();
                    this.mountPrintInstance();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },
            async fetchShipPrintConfig() {
                const res = await ECOrderAPI.fetchShipPrintConfig();
                this._cachePostData = Object.assign(this.postData, res);
                this.postData = clone(this._cachePostData);
            },
            async fetchShipperContactList() {
                const res = await ECOrderAPI.fetchShipperContactList();
                this.shipperContactList = res.rows;
            },
            handleChange() {
                this.mountPrintInstance();
            },

            handleSave() {
                this.$refs.printForm.validate(async (val) => {
                    if (val) {
                        await this.updateShipPrintConfig();
                    }
                });
            },
            async updateShipPrintConfig() {
                try {
                    this.btnLoading = true;
                    await ECOrderAPI.updateShipPrintConfig({
                        ...this.postData,
                        shipperContactId: this.postData.sellerAddress.id,
                    });
                    this._cachePostData = clone(this.postData);
                    this.$abcPage.$store.fetchShipPrintConfig();
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.isDisabled = true;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
            async handleClickTab(index) {
                if (this.currentPreviewTab === index) return;
                this.currentPreviewTab = index;
                this.printInstance = null;
                await this.mountPrintInstance();
            },
            destroyPrintInstanceStyle() {
                this._destroyStyle?.();
                this._destroyStyle = null;
            },
            async mountPrintInstance() {
                try {
                    const printData = {
                        'ecMallId': 0,
                        'ecMallName': '测试门店',
                        'orderInfo': {
                            'actualAmount': 0,
                            'afterSalesDetail': {
                                'desc': 'string',
                                'statusCode': 0,
                            },
                            'buyerMemo': 'string',
                            'clinicId': 'string',
                            'clinicName': 'string',
                            'createdTime': '2024-06-28T07:27:51.436Z',
                            'detailOrders': [
                                {
                                    'actualAmount': 0,
                                    'afterSalesDetail': {
                                        'desc': 'string',
                                        'statusCode': 0,
                                    },
                                    'buyerMemo': 'string',
                                    'clinicId': 'string',
                                    'clinicName': 'string',
                                    'createdTime': '2024-06-28T07:27:51.436Z',
                                    'detailOrders': [
                                        null,
                                    ],
                                    'ecMallId': 0,
                                    'ecType': 0,
                                    'extMallId': 'string',
                                    'extraDeliveryList': [
                                        {
                                            'trackingNumber': 'string',
                                            'wpCode': 'string',
                                            'wpId': 0,
                                            'wpName': 'string',
                                        },
                                    ],
                                    'giftDeliveryList': [
                                        {
                                            'trackingNumber': 'string',
                                            'wpCode': 'string',
                                            'wpId': 0,
                                            'wpName': 'string',
                                        },
                                    ],
                                    'goodsList': [
                                        {
                                            'goodsCount': 1,
                                            'goodsId': 'string',
                                            'goodsImg': 'string',
                                            'goodsName': '商品简称/名称',
                                            'goodsPrice': 0,
                                            'goodsSpec': '规格简称/名称',
                                            'isGift': 0,
                                            'outerGoodsId': 'string',
                                            'outerId': 'string',
                                            'skuId': 'string',
                                        },
                                    ],
                                    'id': 0,
                                    'lastShipTime': '2024-06-28T07:27:51.436Z',
                                    'logisticsTraceList': [
                                        {
                                            'errorCode': 0,
                                            'errorMsg': 'string',
                                            'traceList': [
                                                {
                                                    'action': 'string',
                                                    'desc': 'string',
                                                    'nodeDescription': 'string',
                                                    'statusDesc': 'string',
                                                    'statusTime': '2024-06-28T07:27:51.436Z',
                                                    'time': '2024-06-28T07:27:51.436Z',
                                                },
                                            ],
                                            'waybillCode': 'string',
                                            'wpCode': 'string',
                                            'wpName': 'string',
                                        },
                                    ],
                                    'mallName': 'string',
                                    'mergeStatus': 0,
                                    'mergeTag': 'string',
                                    'orderLock': 0,
                                    'orderNo': '25250781-478203145610786',
                                    'orderStatus': 0,
                                    'parentOrderId': 0,
                                    'payNo': 'string',
                                    'payTime': '2024-06-25T07:27:51.436Z',
                                    'payType': 'string',
                                    'receiverChanged': 0,
                                    'receiverInfo': {
                                        'receiverAddress': 'string',
                                        'receiverAddressMask': '浙江省杭州市西湖区西湖大学2栋 508',
                                        'receiverName': 'string',
                                        'receiverNameMask': '爱耍酷的小哥',
                                        'receiverPhone': 'string',
                                        'receiverPhoneMask': '18625654785',
                                    },
                                    'refundStatus': 0,
                                    'remark': 'string',
                                    'shipTimeRemaining': 0,
                                    'shippingTime': '2024-06-28T07:27:51.436Z',
                                    'totalPrice': 0,
                                    'trackingNumber': 'string',
                                    'waybillPrintStatus': 0,
                                    'waybillStatus': 0,
                                    'wpCode': 'string',
                                    'wpId': 0,
                                    'wpName': 'string',
                                },
                            ],
                            'ecMallId': 0,
                            'ecType': 0,
                            'extMallId': 'string',
                            'extraDeliveryList': [
                                {
                                    'trackingNumber': 'string',
                                    'wpCode': 'string',
                                    'wpId': 0,
                                    'wpName': 'string',
                                },
                            ],
                            'giftDeliveryList': [
                                {
                                    'trackingNumber': 'string',
                                    'wpCode': 'string',
                                    'wpId': 0,
                                    'wpName': 'string',
                                },
                            ],
                            'goodsList': [
                                {
                                    'goodsCount': 0,
                                    'goodsId': 'string',
                                    'goodsImg': 'string',
                                    'goodsName': 'string',
                                    'goodsPrice': 0,
                                    'goodsSpec': 'string',
                                    'isGift': 0,
                                    'outerGoodsId': 'string',
                                    'outerId': 'string',
                                    'skuId': 'string',
                                },
                            ],
                            'id': 0,
                            'lastShipTime': '2024-06-28T07:27:51.436Z',
                            'logisticsTraceList': [
                                {
                                    'errorCode': 0,
                                    'errorMsg': 'string',
                                    'traceList': [
                                        {
                                            'action': 'string',
                                            'desc': 'string',
                                            'nodeDescription': 'string',
                                            'statusDesc': 'string',
                                            'statusTime': '2024-06-28T07:27:51.436Z',
                                            'time': '2024-06-28T07:27:51.436Z',
                                        },
                                    ],
                                    'waybillCode': 'string',
                                    'wpCode': 'string',
                                    'wpName': 'string',
                                },
                            ],
                            'mallName': 'string',
                            'mergeStatus': 0,
                            'mergeTag': 'string',
                            'orderLock': 0,
                            'orderNo': '测试单号',
                            'orderStatus': 0,
                            'parentOrderId': 0,
                            'payNo': 'string',
                            'payTime': '2024-06-25T06:25:51.436Z',
                            'payType': 'string',
                            'receiverChanged': 0,
                            'receiverInfo': {
                                'receiverAddress': 'string',
                                'receiverAddressMask': '浙江省杭州市西湖区西湖大学2栋 508',
                                'receiverName': 'string',
                                'receiverNameMask': '爱耍酷的小哥',
                                'receiverPhone': 'string',
                                'receiverPhoneMask': '18625654785',
                            },
                            'refundStatus': 0,
                            'remark': 'string',
                            'shipTimeRemaining': 0,
                            'shippingTime': '2024-06-28T07:27:51.436Z',
                            'totalPrice': 0,
                            'trackingNumber': 'string',
                            'waybillPrintStatus': 0,
                            'waybillStatus': 0,
                            'wpCode': 'string',
                            'wpId': 0,
                            'wpName': 'string',
                        },
                        'shipper': {
                            'address': {
                                'city': 'string',
                                'cityCode': 'string',
                                'country': 'string',
                                'countryCode': 'string',
                                'detail': 'string',
                                'district': 'string',
                                'districtCode': 'string',
                                'province': 'string',
                                'provinceCode': 'string',
                                'town': 'string',
                                'townCode': 'string',
                            },
                            'mobile': 'string',
                            'mobileCountryCode': 'string',
                            'name': 'string',
                            'phone': 'string',
                            'phoneCountryCode': 'string',
                        },
                    };
                    const {
                        mallName,
                        receiverAddressEnable,
                        receiverNameEnable,
                        receiverPhoneEnable,
                        sellerAddress,
                        sellerPhone,
                    } = this.postData;
                    Object.assign(printData, {
                        mallName,
                        receiverAddressEnable,
                        receiverNameEnable,
                        receiverPhoneEnable,
                        sellerAddress,
                        sellerPhone,
                    });
                    console.log(printData);

                    const pageSize = this.previewTabs[this.currentPreviewTab].value;

                    let templateKey = 'orderCloudShipmentA4a5';
                    let orient = 2;
                    if (pageSize === '76') {
                        templateKey = 'orderCloudShipment76';
                        orient = 1;
                    }
                    const printInstance = new window.AbcPackages.AbcPrint({
                        template: window.AbcPackages.AbcTemplates[templateKey],
                        page: {
                            size: pageSize,
                            orientation: orient,
                        },
                        originData: printData,
                        extra: {
                            isPreview: true,
                        },
                    });
                    await printInstance.init();
                    this.destroyPrintInstanceStyle();
                    if (!this.$destroyed) {
                        this._destroyStyle = printInstance.loadInstanceStyle();
                    }
                    this.$refs.previewMountPoint.innerHTML = '';
                    this.$refs.previewMountPoint.appendChild(printInstance.instance.$el);
                    this.printInstance = printInstance;
                } catch (e) {
                    console.error('AbcPrint挂载失败', e);
                }
            },

        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.ec-ship-print-setting-wrapper {
    padding: 0;

    .print-preview-layout {
        width: 100%;
        height: 100%;
        padding: 0 30px 0;
        overflow: hidden;
        overflow-y: scroll;
        background-color: var(--abc-color-cp-grey3);
        border-left: 1px solid var(--abc-color-P8);
        box-shadow: 1px 0 0 0 $P6;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
            width: 0;
        }

        .is-a4-preview-layout.preview-html-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding: 4mm;
            padding-bottom: 70.7%;
            margin: 78px auto 0;
            overflow: hidden;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
            transform-origin: left top;

            .preview-html {
                position: absolute;
                width: 297mm;
                height: 210mm;
                transform: scale(0.49);
            }
        }

        .is-a5-preview-layout.preview-html-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding: 4mm;
            padding-bottom: 70.4%;
            margin: 78px auto 0;
            overflow: hidden;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
            transform-origin: left top;

            .preview-html {
                position: absolute;
                width: 210mm;
                height: 148mm;
                transform: scale(0.69);
            }
        }

        .is-76-preview-layout.preview-html-wrapper {
            width: 78mm;
            height: auto;
            padding: 4mm 4mm 0 4mm;

            .preview-html {
                width: 78mm;
                height: auto;
                transform: scale(0.9);
            }
        }

        .preview-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 48px;
            height: 48px;
        }

        .preview-html-wrapper {
            height: 568px;
            padding: 3mm;
            margin: 78px auto 0;
            overflow: hidden;
            background: $S2;
            border: 1px solid $P1;
            box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);

            &.has-tab {
                margin-top: 24px;
            }
        }

        .preview-html {
            position: relative;
            width: 142mm;
            height: 204mm;
            transform: scale(0.7);
            transform-origin: left top;
        }

        .preview-tab {
            display: flex;
            justify-content: center;
            width: 100%;
            padding: 0 78px;
            margin-top: 34px;

            .preview-tab-item {
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                color: $T2;
                cursor: pointer;

                &:not(:last-child) {
                    margin-right: 24px;
                }

                &.preview-tab-item__active {
                    color: $T1;
                }
            }
        }

        [data-type~=footer] {
            position: absolute;
            bottom: 0;
            width: 100%;

            .print-row {
                overflow: initial;
            }
        }

        .next-page {
            display: none;
        }

        .prev-page {
            display: none;
        }
    }
}
</style>


