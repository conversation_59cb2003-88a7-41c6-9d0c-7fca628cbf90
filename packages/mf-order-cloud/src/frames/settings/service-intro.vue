<template>
    <abc-card v-abc-loading="loading" class="content-card-wrapper" :border="false">
        <abc-layout>
            <abc-layout-content>
                <!-- 改版说屏蔽掉该部分 -->
                <!-- <template v-if="isChainAdmin && !installChromeStatus">
                    <abc-flex
                        v-if="editionCenterData"
                        vertical
                        gap="large"
                        class="version-info"
                    >
                        <abc-text size="large" bold>
                            {{ editionCenterData.name }}（连锁）
                        </abc-text>

                        <abc-flex wrap="wrap" gap="middle" class="clinic-list">
                            <abc-flex
                                v-for="item in childrenClinicList"
                                :key="item.id"
                                vertical
                                gap="middle"
                                class="item-clinic"
                            >
                                <abc-flex justify="space-between">
                                    <abc-text size="large" class="name" style="flex: 1;">
                                        {{ item.name }}
                                    </abc-text>
                                    <abc-tag-v2
                                        shape="round"
                                        size="small"
                                        :style="{
                                            'background-color': item.color,
                                            'color': 'var(--abc-color-T4)',
                                        }"
                                    >
                                        {{ item.edition.name }}
                                    </abc-tag-v2>
                                </abc-flex>
                                <abc-text theme="gray">
                                    到期：{{ item.edition.endDate | formatDate }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>
                    </abc-flex>
                </template> -->
                <abc-card :border="false" class="b2c-mall__service-intro-banner-info-wrapper">
                    <div
                        class="b2c-mall__service-intro-banner-info"
                        :style="!isBasic ? {
                            display: 'flex', alignItems: 'center'
                        } : {
                            display: 'flex', flexDirection: 'column', justifyContent: 'center', gap: '8px'
                        }"
                    >
                        <template v-if="!loading">
                            <abc-flex style="padding-left: 64px;">
                                <abc-text class="title">
                                    开网店就用
                                </abc-text>
                                <img
                                    width="184"
                                    height="30"
                                    src="@/assets/images/service-intro/logo.png"
                                    alt=""
                                />

                                <abc-tag-v2
                                    v-if="!isBasic"
                                    variant="outline"
                                    theme="success"
                                    shape="square"
                                    size="huge"
                                >
                                    已开通
                                </abc-tag-v2>
                            </abc-flex>
                            <abc-space
                                :custom-style="{
                                    paddingLeft: '64px'
                                }"
                            >
                                <template v-if="isBasic">
                                    <abc-button
                                        theme="warning"
                                        size="large"
                                        variant="fill"
                                        width="150"
                                        shape="square"
                                        @click="handleApplyOpenOnlineStore"
                                    >
                                        申请开通
                                    </abc-button>
                                </template>
                                <template v-else>
                                    <!-- <abc-button
                                        v-if="hasInstallChrome"
                                        size="large"
                                        variant="fill"
                                        width="150"
                                        shape="square"
                                        @click="handleStartUsage"
                                    >
                                        开始使用
                                    </abc-button> -->
                                    <!--                                    <abc-button-->
                                    <!--                                        v-if="!hasInstallChrome"-->
                                    <!--                                        theme="warning"-->
                                    <!--                                        size="large"-->
                                    <!--                                        variant="fill"-->
                                    <!--                                        width="150"-->
                                    <!--                                        shape="square"-->
                                    <!--                                        @click="handleInstallChrome"-->
                                    <!--                                    >-->
                                    <!--                                        安装至本机-->
                                    <!--                                    </abc-button>-->
                                </template>
                            </abc-space>
                        </template>
                    </div>
                </abc-card>

                <abc-card :border="false" style="background-color: transparent;">
                    <abc-row :gutter="[16, 16]" style="margin-top: 16px;">
                        <abc-col v-for="item in featureList.slice(0, 4)" :key="item.name" :span="6">
                            <abc-card padding-size="medium" style="height: 100%; padding: 6px 0;">
                                <abc-flex justify="flex-start">
                                    <img
                                        :src="item.icon"
                                        alt=""
                                        width="48"
                                        height="48"
                                    />
                                    <div style="margin-left: 12px;">
                                        <abc-text bold size="large">
                                            {{ item.name }}
                                        </abc-text>
                                        <abc-text style="display: block; margin-top: 8px;" theme="gray">
                                            {{ item.desc }}
                                        </abc-text>
                                    </div>
                                </abc-flex>
                            </abc-card>
                        </abc-col>
                    </abc-row>
                    <abc-row :gutter="[16, 16]" style="margin-top: 16px;">
                        <abc-col v-for="item in featureList.slice(4)" :key="item.name" :span="6">
                            <abc-card padding-size="medium" style="height: 100%; padding: 6px 0;">
                                <abc-flex justify="flex-start">
                                    <img
                                        :src="item.icon"
                                        alt=""
                                        width="48"
                                        height="48"
                                    />
                                    <div style="margin-left: 12px;">
                                        <abc-text bold size="large">
                                            {{ item.name }}
                                        </abc-text>
                                        <abc-text style="display: block; margin-top: 8px;" theme="gray">
                                            {{ item.desc }}
                                        </abc-text>
                                    </div>
                                </abc-flex>
                            </abc-card>
                        </abc-col>
                    </abc-row>
                </abc-card>
            </abc-layout-content>
        </abc-layout>
        <dialog-install-status
            v-if="showInstallChrome"
            :value="showInstallChrome"
            @input="handleInstallChromeStatus"
        ></dialog-install-status>
    </abc-card>
</template>

<script>
    import ECAuthAPI from '@/api/auth';
    import {
        OrderCloudModuleId, PharmacyOrderCloudRouterNameKeys,
    } from '../../core/routes.js';
    import { mapGetters } from 'vuex';
    import { EditionKeyEnum } from 'MfBase/access-constant';
    import { isClientSupportPharmacy } from '@/utils/electron';
    import DialogDownloadClient from '@/components/dialog-download-client';
    import DialogInstallStatus from '../../components/dialog-install-status/index.vue';
    import { formatDate } from '@abc/utils-date';

    export default {

        name: 'OrderCloudServiceIntro',
        components: {
            DialogInstallStatus,
        },
        filters: {
            formatDate,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                hasBindMall: false,
                featureList: [
                    {
                        name: '一站打单发货',
                        desc: '实时接收所有电商、外卖平台订单，在ABC系统一键打单发货',
                        icon: require('@/assets/images/service-intro/01.png'),
                    },
                    {
                        name: '库存实时同步，避免缺货',
                        desc: 'ABC库存与电商、外卖平台实时同步，避免缺货导致买家投诉、平台处罚',
                        icon: require('@/assets/images/service-intro/02.png'),
                    },
                    {
                        name: '主流电商/外卖平台全覆盖',
                        desc: '已支持拼多多，正在对接美团外卖、饿了么',
                        icon: require('@/assets/images/service-intro/03.png'),
                    },
                    {
                        name: '发货自动扣库，无需手动下账',
                        desc: '完成订单发货后，ABC系统将自动下账扣库，无需手动开单下账',
                        icon: require('@/assets/images/service-intro/04.png'),
                    },
                    {
                        name: '新订单语音播报',
                        desc: '新订单、新售后单语音实时播报提醒，发货更及时，避免漏单',
                        icon: require('@/assets/images/service-intro/05.png'),
                    },
                    {
                        name: '桌面助手',
                        desc: '常驻电脑桌面，实时查看新订单及异常预警',
                        icon: require('@/assets/images/service-intro/06.png'),
                    },
                    {
                        name: 'App销售日报',
                        desc: '销售日报自动推送，随时随地掌握网店销售情况',
                        icon: require('@/assets/images/service-intro/07.png'),
                    },
                    {
                        name: '商品销售洞察',
                        desc: '洞察商品销量涨跌，智能补货预警，避免缺货影响销量',
                        icon: require('@/assets/images/service-intro/08.png'),
                    },
                ],
                hasBindClinic: false,
                hasInstallChrome: false,
                showInstallChrome: false,
                editionCenterData: null, // 版本信息
                loading: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentEdition',
                'isChainAdmin',
                'isSingleStore',
                'isChainSubStore',
                'userInfo',
            ]),
            ...mapGetters('edition', ['availableEditionList']),

            childrenClinicList() {
                const { children } = this.editionCenterData || {};
                return (children || []).filter((item) => !!item.edition).map((item) => ({
                    ...item,
                    color: this.availableEditionList.find((it) => it.key === item.edition.key)?.color || '',
                }));
            },
            // 基础版
            isBasic() {
                return this.currentEdition.key === EditionKeyEnum.BASIC;
            },
            installChromeStatus() {
                return this.$abcPage.$store.state.installChromeStatus;
            },
            moduleIds() {
                return (this.userInfo && this.userInfo.moduleIds) || '';
            },
            moduleArr() {
                if (!this.moduleIds) {
                    return [];
                }
                return this.moduleIds.split(',');
            },
            hasOrderCloudModule() {
                if (!this.moduleIds) {
                    return false;
                }
                return this.moduleIds === '0' || this.moduleArr.includes(OrderCloudModuleId.main);
            },
            hasTakeawayModule() {
                return this.hasOrderCloudModule ||
                    this.moduleArr.includes(OrderCloudModuleId.takeaway) ||
                    this.moduleArr.includes(OrderCloudModuleId.takeawayOrder) ||
                    this.moduleArr.includes(OrderCloudModuleId.takeawayMtGoods) ||
                    this.moduleArr.includes(OrderCloudModuleId.takeawayOutStorage);
            },
            hasEcommerceModule() {
                return this.hasOrderCloudModule ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerce) ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerceOrder) ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerceOutRecord) ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerceEcStatistics) ||
                    this.moduleArr.includes(OrderCloudModuleId.ecommerceEcGoods);
            },
        },
        async created() {
            this.loading = true;
            try {
                await this.getAuthorizedEcList();
                this.$store.dispatch('fetchChainSubClinics');//拉取所有门店数据
                if (this.isChainAdmin && !this.installChromeStatus) {
                    await this.fetchEditionCenter();
                }
                if (!isClientSupportPharmacy()) {
                    return;
                }

                this.hasInstallChrome = this.installChromeStatus;
            } catch (error) {
                console.error(error);
            } finally {
                this.loading = false;
            }
        },
        methods: {
            initOrderCloud() {

            },
            handleApplyOpenOnlineStore() {

                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: '请联系客户经理',
                });
                // if (this.isChainAdmin) {
                //     new UpgradeVersionDialog().generateDialogAsync();
                // } else {
                //     new UpgradeVersionDialog().generateDialogAsync();
                // }
            },
            handleInstallChrome() {
                if (window.localStorage.getItem('mockInstallChrome')) {
                    this.handleStartUsage();
                    return;
                }
                if (!isClientSupportPharmacy()) {
                    new DialogDownloadClient().generateDialogAsync();
                    return;
                }
                this.showInstallChrome = true;
            },
            async getAuthorizedEcList() {
                try {
                    const res = await ECAuthAPI.fetchAuthorizedEcList({
                        limit: 10,
                        offset: 0,
                    });
                    this.hasBindMall = !!res?.rows?.length;
                    this.hasBindClinic = res.rows?.some((item) => item.bindClinicId);
                } catch (err) {
                    console.error(err);
                }
            },
            handleStartUsage() {
                this.$abcPage.$store.state.installChromeStatus = true;
                if (this.hasTakeawayModule) {
                    this.$router.push({
                        name: PharmacyOrderCloudRouterNameKeys.takeaway,
                    });
                    return;
                }
                if (this.hasEcommerceModule) {
                    this.$router.push({
                        name: PharmacyOrderCloudRouterNameKeys.ecommerce,
                    });
                }
            },
            handleInstallChromeStatus() {
                this.hasInstallChrome = true;
                this.showInstallChrome = false;
                this.handleStartUsage();
            },
            /**
             * 拉取版本中心信息
             * <AUTHOR>
             * @date 2020-09-15
             */
            async fetchEditionCenter() {
                try {
                    this.editionCenterData = await ECAuthAPI.fetchEditionCenter();
                } catch (error) {
                    console.log('fetchEditionCenter error', error);
                }
            },
        },
    };
</script>
<style lang="scss">
.b2c-mall__service-intro-banner-info-wrapper {
    background: linear-gradient(0deg, #fff2d3 0%, #fff2d3 100%), linear-gradient(203deg, #ffb203 32.61%, #fed788 120.38%), linear-gradient(89deg, #3085f4 -2.14%, #7da9ff 120.15%);
    border-radius: var(--abc-border-radius-medium);

    .b2c-mall__service-intro-banner-info {
        height: 228px;
        background-image: url("@/assets/images/service-intro/logo-list-bg.png");
        background-repeat: no-repeat;
        background-position: right;
        background-size: contain;
        border-radius: var(--abc-border-radius-medium);

        .title {
            display: flex;
            align-items: center;
            font-size: 30px;
            font-weight: bold;
            line-height: 28px;
            color: #6a420e;
        }

        img {
            margin-right: 4px;
            margin-left: 10px;
        }
    }
}

.version-info {
    padding: 16px;
    background-color: var(--abc-color-P5);
    border-radius: var(--abc-border-radius-small);

    .item-clinic {
        width: 256px;
        padding: 14px 16px;
        background-color: var(--abc-color-cp-white);
        border-radius: var(--abc-border-radius-small);

        .name {
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
            word-wrap: break-word;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }
    }
}
</style>


