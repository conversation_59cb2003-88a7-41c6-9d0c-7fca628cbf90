<template>
    <abc-layout
        v-abc-loading="loading"
        preset="page-table"
        style="height: calc(100% - 48px);"
        class="mt-goods-table-wrapper"
    >
        <abc-layout-header>
            <abc-flex vertical gap="large">
                <abc-tips-card-v2 v-if="!isChainAdmin && !loading && currentMall.status !== 1" align="center">
                    <abc-space>
                        {{ currentMall.status === 2 ? '当前网店授权已失效，无法同步数据，请重新授权' : '当前未绑定网店，无法同步数据，请重新绑定' }}
                        <abc-link @click="handleEcAuthorized(currentMall.status === 2 ? '2' : '1')">
                            {{ currentMall.status === 2 ? '立即授权' : '绑定网店' }}
                        </abc-link>
                    </abc-space>
                </abc-tips-card-v2>
                <abc-flex :gap="8" justify="space-between">
                    <abc-flex :gap="8">
                        <abc-space is-compact>
                            <abc-select
                                v-model="params.ecType"
                                :width="92"
                                trigger-icon="s-triangle-select-color"
                                :input-style="{ fontWeight: 600 }"
                            >
                                <abc-image
                                    slot="prepend"
                                    :src="WpCodeLogo.MT"
                                    :width="16"
                                ></abc-image>
                                <abc-option
                                    v-for="(op) in ecTypeOptions"
                                    :key="op.value"
                                    :value="op.value"
                                    :label="op.label"
                                ></abc-option>
                            </abc-select>
                            <biz-select-tabs
                                v-model="params.mallId"
                                :options="mallList"
                                :width="240"
                                :max-width="240"
                            >
                            </biz-select-tabs>
                        </abc-space>

                        <abc-tabs-v2
                            v-model="params.goodsStatus"
                            :option="goodsStatusOptions"
                            size="middle"
                            type="outline"
                            @change="handleChangeTab"
                        ></abc-tabs-v2>

                        <abc-input
                            v-model="params.keyword"
                            placeholder="网店品名/ID/店内码/货号"
                            :icon="params.keyword ? 'cis-icon-cross_small' : ''"
                            @icon-click="clearInput"
                            @input="handleSearchGoods"
                        ></abc-input>

                        <abc-cascader
                            ref="catTypeRef"
                            v-model="catIdList"
                            :props="{
                                children: 'children',
                                label: 'catName',
                                value: 'catId',
                            }"
                            placeholder="商品分类"
                            multiple
                            :width="200"
                            :options="goodsTypesList"
                            @change="handleGoodsTypeChange"
                        >
                        </abc-cascader>

                        <abc-space is-compact :border-style="false" split>
                            <div class="price-container">
                                价格
                            </div>
                            <abc-input
                                v-model="params.goodsSkuPriceStart"
                                :width="72"
                                clearable
                                type="number"
                                :config="{
                                    formatLength: 2, supportZero: true, max: 99999
                                }"
                                :input-custom-style="{
                                    textAlign: 'center'
                                }"
                                placeholder="最低"
                                @input="inputHandler"
                            ></abc-input>
                            <div class="separator-container">
                                ~
                            </div>
                            <abc-input
                                v-model="params.goodsSkuPriceEnd"
                                style="margin-right: 6px;"
                                :width="72"
                                type="number"
                                clearable
                                :config="{
                                    formatLength: 2, supportZero: true, max: 99999
                                }"
                                :input-custom-style="{
                                    textAlign: 'center'
                                }"
                                placeholder="最高"
                                @input="inputHandler"
                            ></abc-input>
                        </abc-space>
                    </abc-flex>

                    <sync-product-button :disabled="!isValidMall" @finish="getGoodsList"></sync-product-button>
                </abc-flex>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                theme="white"
                :loading="contentLoading"
                cell-size="xxxlarge"
                header-size="large"
                :show-content-empty="true"
                :empty-content="mallList && mallList.length ? '暂无数据' : '未绑定网店，无法同步商品'"
                :render-config="tableConfig"
                :data-list="tableData"
                :tr-click-trigger-checked="false"
                :show-checked="false"
                :need-selected="false"
            >
                <abc-flex
                    v-if="!isSearchingGoods"
                    slot="topHeader"
                    justify="space-between"
                    align="center"
                >
                    <biz-mixed-selection-filter
                        v-model="paramsConditionSelection"
                        type="radio"
                        :options="warningList"
                        @change="handleChangeWarningType"
                    ></biz-mixed-selection-filter>
                </abc-flex>
                <template
                    #name="{
                        trData: item
                    }"
                >
                    <goods-package-popover
                        :sku-list="item.skuList"
                        :item="item"
                    >
                        <abc-table-cell
                            class="ec-goods-detail__name"
                        >
                            <div class="goods-item" style="width: 100%;">
                                <div class="goods-item-left">
                                    <img
                                        :src="item?.imageUrl"
                                        style="border-radius: 6px;"
                                        alt=""
                                        width="56"
                                        height="56"
                                    />
                                    <abc-tag-v2
                                        v-if="item.skuList[0]?.composeType === MeituanComposeType.COMPOSE_PACKAGE"
                                        class="goods-item-tag"
                                        variant="dark"
                                        theme="success"
                                        size="mini"
                                        use-first-letter
                                    >
                                        组
                                    </abc-tag-v2>
                                </div>
                                <div class="goods-item-right" style="width: 85%;">
                                    <div class="goods-name ellipsis">
                                        <abc-tag-v2
                                            variant="outline"
                                            min-width="54"
                                            size="mini"
                                            :theme="getGoodsStatusTheme(item.status)"
                                        >
                                            {{ getGoodsStatusText(item.status) }}
                                        </abc-tag-v2>
                                        <abc-text bold>
                                            {{ item.name }}
                                        </abc-text>
                                        <div>
                                            <abc-text
                                                class="ellipsis"
                                                theme="gray-light"
                                                size="mini"
                                            >
                                                SKUID: {{ item.skuList[0]?.externalSkuId }}
                                            </abc-text>
                                            <abc-text
                                                v-if="item.skuList[0]?.composeType !== MeituanComposeType.COMPOSE_PACKAGE"
                                                class="ellipsis"
                                                theme="gray-light"
                                                size="mini"
                                            >
                                                店内码/货号: {{ item.skuList[0]?.extSourceFoodCode || '-' }}
                                            </abc-text>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </abc-table-cell>
                    </goods-package-popover>
                </template>
                <template
                    #price="{
                        trData: item
                    }"
                >
                    <abc-table-cell>
                        <abc-text>{{ item.skuList[0]?.price | formatMoney }}</abc-text>
                    </abc-table-cell>
                </template>
                <template
                    #lastMonthSellCount="{
                        trData: item
                    }"
                >
                    <abc-table-cell>
                        <abc-text>
                            {{ item.skuList[0]?.lastMonthSellCount || '-' }}
                        </abc-text>
                    </abc-table-cell>
                </template>
                <template #quantity="{ trData: item }">
                    <abc-table-cell>
                        <div>
                            <abc-text>
                                {{ item.skuList[0]?.quantity ? item.skuList[0]?.quantity : '-' }}
                            </abc-text>
                        </div>
                    </abc-table-cell>
                </template>
                <template #relHisGoodsList="{ trData: item }">
                    <goods-package-popover
                        :sku-list="item.skuList"
                        :item="item"
                    >
                        <abc-table-cell
                            vertical
                            justify="center"
                            align="flex-start"
                        >
                            <div v-if="item.skuList[0]?.bindStatus === 0">
                                <abc-text theme="warning" class="unbind-text">
                                    未绑定
                                </abc-text>
                            </div>
                            <template v-else-if="item.skuList[0]?.composeType === MeituanComposeType.SINGLE">
                                <abc-flex
                                    v-for="goods in item.skuList[0]?.relHisGoodsList"
                                    :key="goods.id"
                                    class="ellipsis"
                                    style="width: 100%;"
                                    vertical
                                >
                                    <template v-if="goods.hisGoodsInfo">
                                        <abc-text :title="goods.hisGoodsInfo.displayName || ''" style="max-width: 410px;">
                                            {{ goods.hisGoodsInfo.displayName || '' }}
                                        </abc-text>
                                        <abc-flex align="center" gap="8">
                                            <abc-text
                                                size="mini"
                                                theme="gray"
                                            >
                                                {{ goods.hisGoodsInfo.displaySpec || '' }}
                                            </abc-text>
                                            <abc-text
                                                size="mini"
                                                theme="gray"
                                            >
                                                {{ goods.hisGoodsInfo.manufacturer || '' }}
                                            </abc-text>
                                            <abc-text
                                                size="mini"
                                                theme="gray"
                                            >
                                                {{ goods.hisGoodsInfo.shortId || '' }}
                                            </abc-text>
                                        </abc-flex>
                                    </template>
                                </abc-flex>
                            </template>
                            <div v-else class="ellipsis" style="width: 100%;">
                                <abc-text :theme="isMeituanUnbindWarn(item.skuList[0]?.warnFlag) ? 'warning' : ''">
                                    {{ packageBindInfo(item) }}
                                </abc-text>
                            </div>
                        </abc-table-cell>
                    </goods-package-popover>
                </template>
                <template #bindGoodsCount="{ trData: { skuList: [sku] } }">
                    <abc-table-cell>
                        <div>
                            <abc-text>
                                {{ getBindCount(sku) }}
                            </abc-text>
                        </div>
                    </abc-table-cell>
                </template>
                <template #bindGoodsUnit="{ trData: { skuList: [sku] } }">
                    <abc-table-cell>
                        <div>
                            <abc-text>
                                {{ getUnit(sku) }}
                            </abc-text>
                        </div>
                    </abc-table-cell>
                </template>
                <template #dispStockGoodsCount="{ trData: item }">
                    <abc-table-cell>
                        <abc-popover
                            width="220px"
                            trigger="hover"
                            theme="yellow"
                            @show="$set(item, 'showPopoverStockCard', true)"
                            @hide="$set(item, 'showPopoverStockCard', false)"
                        >
                            <template slot="reference">
                                <abc-text>
                                    {{ getDispStockCount(item) }}
                                </abc-text>
                            </template>
                            <popover-stock-card
                                v-if="item.showPopoverStockCard"
                                :row="item?.skuList[0]?.relHisGoodsList[0] || {}"
                            ></popover-stock-card>
                        </abc-popover>
                    </abc-table-cell>
                </template>
                <template
                    #turnoverDays="{
                        trData: item
                    }"
                >
                    <abc-table-cell>
                        <abc-text>
                            {{ item.skuList[0]?.turnoverDays || '-' }}
                        </abc-text>
                    </abc-table-cell>
                </template>
                <template #allocationStock="{ trData: { skuList: [sku] } }">
                    <table-cell-allocation-stock
                        v-if="sku?.composeType === MeituanComposeType.SINGLE && sku?.relHisGoodsList[0]"
                        :ec-type="ECTypeEnum.MT"
                        :item="sku?.relHisGoodsList[0]"
                        :clinic-id="bindClinicId"
                        @change="getGoodsList"
                    ></table-cell-allocation-stock>
                    <abc-table-cell v-else>
                        <abc-text>
                            -
                        </abc-text>
                    </abc-table-cell>
                </template>
                <template
                    #operation="{
                        trData: item, cellRowSpan
                    }"
                >
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                        :cell-row-span="cellRowSpan"
                    >
                        <div style="width: 100%;">
                            <abc-button
                                v-if="isValidMall && (isChainSubStore || isSingleStore)"
                                slot="reference"
                                variant="text"
                                @click.stop="handleBindGoods(item)"
                            >
                                {{ isValidMall ? item.skuList[0]?.relHisGoodsList?.length ? '换绑' : '绑定' : '查看' }}
                            </abc-button>
                            <abc-tooltip v-else placement="top" :content="isValidMall ? '请联系子店操作' : '网店授权过期'">
                                <abc-button
                                    type="ghost"
                                    disabled
                                    variant="text"
                                >
                                    {{ isValidMall ? item.skuList[0]?.relHisGoodsList?.length ? '换绑' : '绑定' : '查看' }}
                                </abc-button>
                            </abc-tooltip>
                        </div>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="paginationParams"
                :count="totalCount"
                :show-total-page="true"
                :page-sizes="[20, 50, 100]"
                show-size
                @current-change="pageChange"
                @size-change="sizeChange"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import TAGoodsAPI from '@/api/ta-goods';
    import ECAuthAPI from '@/api/auth';
    import { mapGetters } from 'vuex';
    import { debounce } from 'MfBase/lodash';
    import {
        formatMoney,
    } from '@abc/utils';
    import BizSelectTabs from 'MfBase/biz-select-tabs';
    import BizMixedSelectionFilter from 'MfBase/biz-mixed-selection-filter';
    import GoodsPackagePopover from './package-popover.vue';
    import PopoverStockCard from './goods-stock-popover.vue';
    import DialogBindGoodsO2O from '../../../components/dialog-bind-goods-o2o';
    import {
        ECTypeEnum, WpCodeLogo,
    } from '../../../utils/constants';
    import SyncProductButton from '@/components/sync-product-button.vue';
    import {
        MeituanComposeType, isMeituanUnbindWarn,
    } from './constants.js';
    import { PharmacyOrderCloudRouterNameKeys } from '@/core/routes';
    import DialogEcAuth from '@/components/dialog-ec-auth';
    import TableCellAllocationStock from '@/components/table-cell-allocation-stock.vue';

    export default {
        name: 'MtGoodsTable',
        components: {
            TableCellAllocationStock,
            PopoverStockCard,
            GoodsPackagePopover,
            BizMixedSelectionFilter,
            BizSelectTabs,
            SyncProductButton,
        },
        data() {
            return {
                ECTypeEnum,
                WpCodeLogo,
                MeituanComposeType,
                catIdList: [],
                paramsConditionSelection: '',
                params: {
                    limit: 20,
                    offset: 0,
                    mallId: '',
                    catId: '',
                    customCatId: '',
                    goodsStatus: 20,
                    keyword: '',
                    goodsSkuPriceStart: '',
                    goodsSkuPriceEnd: '',
                    goodsSkuStockWarnFlag: '',
                    assignStockWarnFlag: '',
                    goodsSkuBindStatus: '',
                    hisGoodsWarnFlag: '',
                    composeType: '',
                    ecType: 4,
                },
                totalCount: 0,
                tableData: [],
                tableTrHeight: 72,
                loading: false,
                contentLoading: false,
                clinicList: [],
                ecTypes: [],
                mallList: [],
                goodsTypesList: [],
                summaryData: {},
                bindClinicId: '',
                tableMaxHeight: '400px',
                isSearchingGoods: false,
                ecTypeOptions: [
                    {
                        label: '美团',
                        value: ECTypeEnum.MT,
                    },
                ],
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isChainSubStore',
                'isChainAdmin',
                'isSingleStore',
            ]),
            warningList() {
                return [
                    {
                        label: '未绑定',
                        value: 'goodsSkuBindStatus',
                        statisticsNumber: this.summaryData.unboundHisGoodsCount || 0,
                    },
                    {
                        label: '库存预警',
                        value: 'goodsSkuStockWarnFlag',
                        statisticsNumber: this.summaryData.stockWarnCount || 0,
                    },
                    {
                        label: '绑定商品异常',
                        value: 'hisGoodsWarnFlag',
                        statisticsNumber: this.summaryData.hisGoodsWarnCount || 0,
                    },
                    {
                        label: '未分配库存',
                        value: 'assignStockWarnFlag',
                        statisticsNumber: this.summaryData.assignStockWarnCount || 0,
                    },
                ];
            },
            tableConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'name',
                            label: '网店商品',
                            style: {
                                flex: 3,
                                minWidth: '400px',
                            },
                        },
                        {
                            key: 'price',
                            label: '价格',
                            style: {
                                flex: 'none',
                                width: '90px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'lastMonthSellCount',
                            label: '30日销量',
                            style: {
                                flex: 'none',
                                width: '90px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'turnoverDays',
                            label: '周转',
                            style: {
                                flex: 'none',
                                width: '90px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'quantity',
                            label: '库存',
                            style: {
                                flex: 'none',
                                width: '90px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'relHisGoodsList',
                            label: '绑定的ABC系统商品',
                            style: {
                                flex: 2,
                                minWidth: '300px',
                                maxWidth: '400px',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'bindGoodsCount',
                            label: '绑定数量',
                            style: {
                                flex: 'none',
                                width: '90px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'bindGoodsUnit',
                            label: '绑定单位',
                            style: {
                                flex: 'none',
                                width: '90px',
                                textAlign: 'center',
                            },
                        },
                        {
                            key: 'dispStockGoodsCount',
                            label: 'ABC可售库存',
                            style: {
                                flex: 'none',
                                width: '110px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'allocationStock',
                            label: '分配的ABC库存',
                            style: {
                                flex: 'none',
                                width: '130px',
                            },
                        },
                        {
                            key: 'operation',
                            label: '操作',
                            pinned: 'right',
                            style: {
                                flex: 'none',
                                width: '64px',
                                textAlign: 'center',
                            },
                        },
                    ],
                };
            },

            paginationParams() {
                const {
                    limit: pageSize, offset,
                } = this.params;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                    count: this.totalCount,
                };
            },
            goodsStatusOptions() {
                const {
                    offShelfCount,
                    onShelfCount,
                    sellOutCount,
                    totalCount,
                } = this.summaryData || {};
                return [
                    {
                        label: '全部',
                        value: 0,
                        statisticsNumber: totalCount || 0,
                    },
                    {
                        label: '在售中',
                        value: 20,
                        statisticsNumber: onShelfCount || 0,
                    },
                    {
                        label: '已下架',
                        value: 10,
                        statisticsNumber: offShelfCount || 0,
                    },
                    {
                        label: '已售罄',
                        value: 30,
                        statisticsNumber: sellOutCount || 0,
                    },
                ];
            },
            currentMall() {
                return this.mallList.find((item) => item.ecMallId === this.params.mallId) || {};
            },
            isValidMall() {
                return this.currentMall.status === 1;
            },
        },
        async created() {
            await this.getEcTypes();
            await this.getMallList();
            this.params.mallId = this.mallList?.[0]?.ecMallId || '';
            this.bindClinicId = this.mallList.find((item) => item.ecMallId === this.params.mallId)?.bindClinicId || '';
            this.fetchGoodsTypesList();
            this.getGoodsList();
            this._debounceSearch = debounce(
                () => {
                    this.getGoodsList();
                },
                250,
                true,
            );
            this.$abcEventBus.$on('refresh-ec-goods', () => this.getGoodsList(false), this);
            this._handleClickTag = debounce(
                (val, key) => {
                    this.handleClickTag(val, key);
                },
                500,
                true,
            );
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            isMeituanUnbindWarn,
            formatMoney,
            pageChange(pageIndex) {
                this.params.offset = (pageIndex - 1) * this.params.limit;
                this.getGoodsList(false);
            },
            getGoodsInfo(item) {
                const {
                    name,
                    brand,
                    manufacturer,
                    spec,
                    medicineNmpn,
                    medicineClassify,
                    externalGoodsId,
                    dosage,
                } = item;
                return [
                    {
                        label: '商品ID',
                        value: externalGoodsId,
                    },
                    {
                        label: '通用名',
                        value: name,
                    },
                    {
                        label: '品牌',
                        value: brand || '-',
                    },
                    {
                        label: '生产企业',
                        value: manufacturer || '-',
                    },
                    {
                        label: '药品规格',
                        value: spec || '-',
                    },
                    {
                        label: '批准文号',
                        value: medicineNmpn || '-',
                    },
                    {
                        label: '药品分类',
                        value: medicineClassify || '-',
                    },
                    {
                        label: '剂型',
                        value: dosage || '-',
                    },
                ];

            },
            sizeChange(pageSize) {
                this.params.limit = pageSize;
                this.getGoodsList();
            },
            handleMounted(data) {
                this.tableMaxHeight = `${data.height}px`;
            },
            onChangeTab() {},
            handleClickTag(val, key) {
                if (key) this.params[key] = val;
                this.getGoodsList();
            },
            getGoodsStatusText(status) {
                if (status === 0) return '售卖中';
                if (status === 10) return '已下架';
                if (status === 20) return '在售中';
                if (status === 30) return '已售罄';
                if (status === 99) return '已删除';
            },
            getGoodsStatusTheme(status) {
                if (status === 0) return 'success';
                if (status === 20) return 'success';
                if (status === 10) return 'danger';
                if (status === 30) return 'warning';
                if (status === 99) return 'warning';
            },
            clearInput() {
                this.params.keyword = '';
                this.inputHandler();
                this.isSearchingGoods = false;
            },

            handleBindGoods(item) {
                if (!this.isValidMall) return;
                new DialogBindGoodsO2O({
                    clinicId: this.bindClinicId,
                    ecMallId: this.params.mallId,
                    mallName: this.mallList?.find((it) => it.ecMallId === this.params.mallId)?.mallName,
                    goodsId: item.id,
                    imageUrl: item.imageUrl,
                    goodsName: item.name || '-',
                    status: item.status,
                    goodsSkuId: item.skuList[0]?.id,
                    desc: item.skuList[0]?.externalSkuId || '-',
                    extSourceFoodCode: item.skuList[0]?.extSourceFoodCode || '-',
                    price: item.skuList[0]?.price,
                    skuList: item.skuList,

                    hisGoodsList: item.skuList[0]?.relHisGoodsList || [],
                    composeItems: item.skuList[0]?.composeItems || [],
                }).generateDialogAsync({ parent: this });
            },
            handleChangeTab() {
                this.getGoodsList();
            },
            handleSearchGoods() {
                this.isSearchingGoods = true;
                this.params.goodsSkuPriceStart = '';
                this.params.goodsSkuPriceEnd = '';
                this.params.goodsSkuStockWarnFlag = '';
                this.params.assignStockWarnFlag = '';
                this.params.goodsSkuBindStatus = '';
                this.params.hisGoodsWarnFlag = '';
                this.params.catId = '';
                this.params.customCatId = '';
                this.catIdList = [];
                this._debounceSearch();
            },
            inputHandler() {
                this._debounceSearch();
            },
            handleMallChange() {
                this.bindClinicId = this.mallList.find((item) => item.ecMallId === this.params.mallId)?.bindClinicId || '';
                this.getGoodsList();
                this.fetchGoodsTypesList();
            },
            async getEcTypes() {
                try {
                    this.loading = true;
                    const { rows } = await ECAuthAPI.fetchEcTypes({
                        ...this.params,
                    });
                    this.ecTypes = rows || [];
                } catch (err) {
                    console.error(err);
                } finally {
                    this.loading = true;
                }
            },
            async getMallList() {
                try {
                    this.loading = true;
                    const { rows = [] } = await ECAuthAPI.fetchBindAuthList({
                        offset: 0,
                        limit: 1000,
                        onlyUnexpired: 0, // 在授权有效期内
                        includeHistory: 1, // 是否包含历史绑定的电商店铺
                        includeDeleted: 1, // 是否包含已删除的绑定数据
                    });
                    this.mallList = rows
                        .filter((item) => item.ecType === ECTypeEnum.MT)
                        .sort((a, b) => {
                            const getOrder = (order) => (order === 1 ? 1 : 0);
                            const orderA = getOrder(a.status);
                            const orderB = getOrder(b.status);
                            return orderB - orderA;
                        })
                        .reduce((pre, cur) => {
                            if (pre.find((item) => item.ecMallId === cur.ecMallId)) return pre;
                            pre.push(cur);
                            return pre;
                        }, [])
                        .map((item) => {
                            return {
                                ...item,
                                key: item.ecMallId,
                                label: `${item.mallName}（${item.bindClinicName}）`,
                                value: item.ecMallId,
                                icon: 's-s-drugstore-color',
                            };
                        });
                } catch (err) {
                    console.error(err);
                } finally {
                    this.loading = false;
                }
            },
            handleGoodsTypeChange(list) {
                const newList = list.filter((item) => item !== null);
                const lastValues = newList.map((arr) => {
                    if (arr && arr.length > 0) {
                        return arr[arr.length - 1].value;
                    }
                    return null;
                }).filter((value) => value !== null);
                this.params.customCatId = lastValues.join(',');
                this.getGoodsList();
            },
            async fetchGoodsTypesList() {
                try {
                    const res = await TAGoodsAPI.getGoodsTypes({
                        mallId: this.params.mallId,
                        ecType: 1, // 现在只有拼多多
                    });
                    if (res) {
                        this.goodsTypesList = res.rows || [];
                    }
                } catch (err) {
                    console.error(err);
                }
            },

            async getGoodsList(isNeedPage = true) {
                if (!this.params.mallId) return;
                try {
                    if (isNeedPage) {
                        this.params.offset = 0;
                    }
                    this.contentLoading = true;
                    const { params } = this;
                    const res = await TAGoodsAPI.getTaGoods({
                        ...params,
                        goodsSkuStockWarnFlag: params.goodsSkuStockWarnFlag === 1 ? 1 : '',
                        goodsSkuBindStatus: params.goodsSkuBindStatus === 1 ? 0 : '',
                        hisGoodsWarnFlag: params.hisGoodsWarnFlag === 1 ? 1 : '',
                        assignStockWarnFlag: params.assignStockWarnFlag === 1 ? 1 : undefined,
                    });
                    this.totalCount = res.total || 0;
                    this.tableData = res.rows || [];
                    this.summaryData = res?.summary || {
                        offShelfCount: 0,
                        onShelfCount: 0,
                        sellOutCount: 0,
                        sellableCount: 0,
                        totalCount: 0,
                    };
                    this.summaryData = {
                        ...this.summaryData,
                        unboundHisGoodsCount: res.unboundHisGoodsCount || 0,
                        stockWarnCount: res.stockWarnCount || 0,
                        hisGoodsWarnCount: res.hisGoodsWarnCount || 0,
                        assignStockWarnCount: res.assignStockWarnCount || 0,
                    };
                } catch (err) {
                    console.error(err);
                } finally {
                    this.contentLoading = false;
                }
            },

            handleChangeWarningType(val) {
                this.params.goodsSkuBindStatus = '';
                this.params.goodsSkuStockWarnFlag = '';
                this.params.hisGoodsWarnFlag = '';
                this.params.assignStockWarnFlag = '';
                let checked = 0;
                if (val) {
                    checked = 1;
                }
                this._handleClickTag(checked, val);
            },
            packageBindInfo(item) {
                return item.skuList[0]?.composeItems?.map((item) => {
                    const displayName = item?.relHisGoodsList[0]?.hisGoodsInfo.displayName;
                    const countUnit = item?.relHisGoodsList[0]?.useDismounting ?
                        `x${item?.relHisGoodsList[0]?.bindPieceCount}` :
                        `x${item?.relHisGoodsList[0]?.bindPackageCount}`;
                    const unit = item?.relHisGoodsList[0]?.unit;
                    return displayName + countUnit + unit;
                }).join('，') || '';
            },
            getBindCount(sku) {
                if (sku?.composeType === MeituanComposeType.COMPOSE_PACKAGE) {
                    if (sku?.composeItems?.length === 1 && sku?.composeItems[0]?.relHisGoodsList?.length) {
                        return sku?.composeItems[0]?.relHisGoodsList[0]?.useDismounting ?
                            sku?.composeItems[0]?.relHisGoodsList[0]?.bindPieceCount :
                            sku?.composeItems[0]?.relHisGoodsList[0]?.bindPackageCount;
                    }
                    return '-';
                }
                if (!sku?.relHisGoodsList.length) return '-';
                return sku?.relHisGoodsList[0].useDismounting ? sku?.relHisGoodsList[0].bindPieceCount : sku?.relHisGoodsList[0].bindPackageCount;
            },

            getUnit(sku) {
                if (sku?.composeType === MeituanComposeType.COMPOSE_PACKAGE) {
                    if (sku?.composeItems?.length === 1 && sku?.composeItems[0]?.relHisGoodsList?.length) {
                        return sku?.composeItems[0]?.relHisGoodsList[0]?.unit;
                    }
                    return '-';
                }
                if (!sku?.relHisGoodsList.length) return '-';
                return sku?.relHisGoodsList[0].unit;
            },

            getDispStockCount(item) {
                if (item.skuList[0]?.composeType === MeituanComposeType.COMPOSE_PACKAGE) {
                    if (item.skuList[0]?.composeItems?.length === 1 && item.skuList[0]?.composeItems[0]?.relHisGoodsList?.length) {
                        return item.skuList[0]?.composeItems[0]?.relHisGoodsList[0]?.dispStockGoodsCount;
                    }
                    return '-';
                }
                if (!item.skuList[0]?.relHisGoodsList.length) return '-';
                return item.skuList[0]?.relHisGoodsList[0]?.dispStockGoodsCount;
            },
            async handleEcAuthorized(controlType) {
                if (controlType === '2') {
                    await new DialogEcAuth({
                        authCode: this.authCode,
                        extMallId: this.params.extMallId,
                        mallId: this.params.mallId,
                        finishFunc: () => {
                            this.getMallList();
                        },
                    }).generateDialogAsync({ parent: this });
                } else {
                    this.$router.push({
                        name: PharmacyOrderCloudRouterNameKeys.ecSettingsTakeawayOnlineStore,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
.mt-goods-table-wrapper {
    .price-container {
        box-sizing: border-box;
        width: 48px;
        height: 32px;
        padding-left: 6px;
        overflow: hidden;
        line-height: 31px;
        text-align: center;
        background: #ffffff;
        border: 1px solid #e6eaee;
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
    }

    .separator-container {
        box-sizing: border-box;
        height: 32px;
        overflow: hidden;
        line-height: 31px;
        color: var(--abc-color-T3);
        text-align: center;
        background: #ffffff;
        border: 1px solid #e6eaee;
        border-left: none;
    }
}

.ec-goods-detail__name {
    .goods-item {
        display: flex;
        align-items: center;
        padding: 9px 0;

        .goods-item-left {
            position: relative;
            height: 56px;

            .goods-item-tag {
                position: absolute;
                top: 0;
                left: 0;
            }
        }

        .goods-item-right {
            width: 336px;
            margin-left: 10px;

            .goods-name {
                span {
                    margin-left: 4px;
                }
            }
        }
    }

    &-content {
        margin-top: 10px;

        li {
            display: flex;
            align-items: center;

            .label {
                display: inline-block;
                width: 66px;
                color: #7a8794;
            }

            .value {
                display: inline-block;
                max-width: 260px;
            }
        }
    }

    &-footer {
        text-align: end;
    }
}
</style>
