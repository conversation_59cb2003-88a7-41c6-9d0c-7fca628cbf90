<template>
    <abc-card class="content-card-wrapper" :border="false">
        <abc-layout preset="page-table">
            <abc-layout-header>
                <stat-toolbar
                    :enable-features="toolbarFeatures"
                    :date-filter.sync="params.dateFilter$"
                    :dimension-filter.sync="dimension"
                    :dimension-picker-options="dimensionOptions"
                    :handle-export="handleExports"
                    :export-task-type="exportTaskType"
                    @change-dimension="handleDimensionChange"
                >
                    <template slot="prepend">
                        <abc-space is-compact>
                            <abc-select v-model="dateType" width="88" @change="getTableData">
                                <abc-option
                                    v-for="item in dateTypeOptions"
                                    :key="item.value"
                                    :value="item.value"
                                    :label="item.label"
                                >
                                </abc-option>
                            </abc-select>
                            <abc-date-picker
                                v-model="computedDateRange"
                                :picker-options="pickerOptions"
                                type="daterange"
                                value-format="YYYY-MM-DD"
                                :clearable="false"
                            >
                            </abc-date-picker>
                        </abc-space>
                    </template>
                    <template slot="custom-clinic">
                        <filter-select
                            v-if="isChainAdmin"
                            v-model="params.clinicId"
                            :width="150"
                            :inner-width="150"
                            :options="clinicOptions"
                            placeholder="门店"
                            @change="getTableData"
                        >
                        </filter-select>
                    </template>
                    <template slot="compose">
                        <filter-select
                            v-model="params.ecTypeId"
                            placeholder="平台"
                            :width="100"
                            :options="ecTypeList"
                            @change="getTableData"
                        ></filter-select>

                        <filter-select
                            v-model="params.mallId"
                            placeholder="网店"
                            :width="150"
                            :options="mallList"
                            @change="getTableData"
                        ></filter-select>

                        <abc-input
                            v-model="params.orderNo"
                            placeholder="订单编号"
                            :width="150"
                            clearable
                            @clear="getTableData"
                            @change="getTableData"
                        >
                            <abc-search-icon slot="prepend"></abc-search-icon>
                        </abc-input>

                        <abc-select
                            v-model="params.status"
                            width="100"
                            placeholder="状态"
                            clearable
                            @change="getTableData"
                        >
                            <abc-option
                                v-for="item in statusOptions"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                                :disabled="item.disabled"
                            >
                            </abc-option>
                        </abc-select>
                    </template>
                </stat-toolbar>
            </abc-layout-header>
            <abc-layout-content>
                <abc-table
                    ref="takeawayOrderStatisticsTableRef"
                    theme="white"
                    :loading="loading"
                    :render-config="tableRenderHeader"
                    :data-list="list"
                    :summary-render-keys="summaryRenderKeys"
                >
                </abc-table>
            </abc-layout-content>
            <abc-layout-footer>
                <abc-pagination
                    :pagination-params="params"
                    :count="totalCount"
                    :show-total-page="false"
                    :page-sizes="[20, 50, 100]"
                    show-size
                    @size-change="setPageSize"
                    @current-change="handlePageIndexChange"
                >
                    <ul slot="tipsContent">
                        <li v-html="summaryInfoHtml"></li>
                    </ul>
                </abc-pagination>
            </abc-layout-footer>
        </abc-layout>
    </abc-card>
</template>
<script>
    import { mapGetters } from 'vuex';
    import { formatDate } from '@abc/utils-date';
    import { EcShopTypeEnum } from '@/utils/constants';
    import ECAuthAPI from '@/api/auth';
    import ECStatisticsAPI from '@/api/statistics';
    import StatToolbar from 'MfBase/stat-toolbar';
    import FilterSelect from 'MfBase/filter-select';
    import ExportService from 'MfBase/export-service';
    import {
        getSummaryRenderKeys, resolveHeader,
    } from 'MfBase/table';

    export default {
        components: {
            StatToolbar,
            FilterSelect,
        },
        data() {
            const now = new Date();
            const nowDayOfWeek = now.getDay();
            const nowDay = now.getDate();
            const nowMonth = now.getMonth();
            const nowYear = now.getFullYear();
            return {
                dateType: 0,
                dateTypeOptions: [
                    {
                        label: '支付时间',
                        value: 0,
                    }, {
                        label: '发货时间',
                        value: 1,
                    },
                ],
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                    shortcuts: [{
                        text: '今天',
                        onClick(cb) {
                            cb([now, now]);
                        },
                    }, {
                        text: '昨天',
                        onClick(cb) {
                            const end = new Date(nowYear, nowMonth, nowDay - 1);
                            const start = new Date(nowYear, nowMonth, nowDay - 1);
                            cb([start, end]);
                        },
                    }, {
                        text: '本周',
                        onClick(cb) {
                            let start = new Date(nowYear, nowMonth, nowDay - 6);
                            if (nowDayOfWeek) {
                                start = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1);
                            }
                            cb([start, now]);
                        },
                    }, {
                        text: '本月',
                        onClick(cb) {
                            const start = new Date(nowYear, nowMonth, 1);
                            cb([start, now]);
                        },
                    }, {
                        text: '本年',
                        onClick(cb) {
                            const start = new Date(nowYear, 0, 1);
                            cb([start, now]);
                        },
                    }],
                },
                ecTypeList: [],
                mallList: [],
                statusOptions: [
                    {
                        value: 100, label: '订单状态', disabled: true,
                    },
                    {
                        value: 0, label: '待发货',
                    },
                    {
                        value: 1, label: '已发货',
                    },
                    {
                        value: 2, label: '售后中',
                    },
                    {
                        value: 3, label: '售后成功',
                    },
                    {
                        value: 200, label: '出库状态', disabled: true,
                    },
                    {
                        value: 10, label: '未出库',
                    },
                    {
                        value: 11, label: '已出库',
                    },
                    {
                        value: 12, label: '已退入',
                    },
                ],

                dimension: 'transaction',
                dimensionOptions: [
                    {
                        name: '单据',
                        label: 'transaction',
                    },
                    {
                        name: '商品',
                        label: 'commodity',
                    },
                ],


                list: [],
                tableHeader: [],
                totalCount: 0,
                summaryInfoHtml: '',
                loading: false,

                params: {
                    dateFilter$: {
                        begin: now,
                        end: now,
                        dateRange: [now, now],
                    },
                    clinicId: '',
                    ecTypeId: '',
                    mallId: '',
                    orderNo: '',
                    status: '',
                    pageIndex: 0,
                    pageSize: 20,
                },

                exportTaskType: 'b2c-order-ec',
            };
        },
        computed: {
            ...mapGetters([
                'subClinics',
                'isChainAdmin',
                'currentClinic',
            ]),
            toolbarFeatures() {
                return [StatToolbar.Feature.EXPORT, StatToolbar.Feature.DIMENSION];
            },
            computedDateRange: {
                get() {
                    return this.params.dateFilter$.dateRange;
                },
                set(val) {
                    this.params.dateFilter$ = {
                        begin: val[0],
                        end: val[1],
                        dateRange: [...val],
                    };
                    this.handleDateChange();
                },
            },

            clinicOptions() {
                return this.subClinics.filter((item) => item.chainAdmin !== 1).map((item) => ({
                    ...item,
                    name: item.shortName || item.name,
                }));
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.currentClinic?.clinicId;
            },

            tableRenderHeader() {
                const list = resolveHeader(
                    this.tableHeader,
                    {},
                    true,
                );
                list.forEach((item) => {
                    if (item.key === 'receiverName' || item.key === 'receiverPhone' || item.key === 'receiverAddress') {
                        item.description = '因电商平台加密协议，可能会隐藏相关信息';
                    }
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list,
                };
            },
            summaryRenderKeys() {
                return getSummaryRenderKeys(this.tableHeader);
            },
        },
        created() {
            this.exportService = new ExportService();
            this.getOrderSelectionData();
            this.getMallList();
        },
        beforeDestroy() {
            this.exportService?.destroy();
        },
        methods: {
            setPageSize(pageSize) {
                this.params.pageSize = pageSize;
                this.getTableData();
            },
            async getOrderSelectionData() {
                try {
                    const {
                        dateFilter$: {
                            begin,
                            end,
                        },
                        ecTypeId,
                        mallId,
                    } = this.params;
                    const beginDate = formatDate(begin);
                    const endDate = formatDate(end);
                    const params = {
                        ecTypeId,
                        mallId,
                    };
                    if (this.dateType === 1) {
                        params.beginDeliveryDate = beginDate;
                        params.endDeliveryDate = endDate;
                    } else {
                        params.beginPayDate = beginDate;
                        params.endPayDate = endDate;
                    }
                    const res = await ECStatisticsAPI.getEcOrderSelection(params);
                    this.ecTypeList = res.ecTypeList.map((item) => {
                        const key = Object.keys(item)[0];
                        const name = item[key];
                        return {
                            name,
                            id: key,
                        };
                    });
                } catch (e) {
                    console.log(e);
                }
            },
            async getMallList() {
                try {
                    const res = await ECAuthAPI.fetchBindAuthList({
                        includeHistory: 1,
                    });
                    this.mallList = res.rows.reduce((pre, cur) => {
                        if (pre.find((item) => item.id === cur.ecMallId)) return pre;
                        pre.push({
                            name: cur.mallName,
                            id: cur.ecMallId,
                            shopType: cur.shopType,
                        });
                        return pre;
                    }, []).filter((item) => item.shopType === EcShopTypeEnum.B2C);
                } catch (e) {
                    console.log(e);
                }
            },
            getTableParams() {
                const {
                    dateFilter$: {
                        begin,
                        end,
                    },
                    clinicId,
                    ecTypeId,
                    mallId,
                    orderNo,
                    status,
                    pageIndex,
                    pageSize,
                } = this.params;
                const beginDate = formatDate(begin);
                const endDate = formatDate(end);
                const params = {
                    clinicId,
                    ecTypeId,
                    mallId,
                    orderNo,
                    limit: pageSize,
                    offset: pageIndex * pageSize,
                };
                if (this.dateType === 1) {
                    params.beginDeliveryDate = beginDate;
                    params.endDeliveryDate = endDate;
                } else {
                    params.beginPayDate = beginDate;
                    params.endPayDate = endDate;
                }
                if (status >= 10) {
                    params.outStockStatus = status - 10;
                } else {
                    params.orderStatus = status;
                }
                return params;
            },
            async getTableData(resetPageParams = true) {
                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }
                const params = this.getTableParams();
                try {
                    this.loading = true;
                    const {
                        data, total, header,
                    } = this.dimension === 'commodity' ?
                        await ECStatisticsAPI.getEcCommodity(params) :
                        await ECStatisticsAPI.getEcTransaction(params);
                    this.list = data || [];
                    this.tableHeader = header || [];
                    this.totalCount = total?.count || 0;
                    this.summaryInfoHtml = this.transTemplateToTotalInfo(total?.data || [], total?.template || '');
                } catch (err) {
                    this.summaryInfoHtml = '';
                    console.log(err);
                } finally {
                    this.loading = false;
                }
            },
            transTemplateToTotalInfo(data = [], template = '') {
                if (!template) return '';
                let index = 0;
                return template.replace(
                    RegExp('%s', 'g'),
                    () =>
                        `<span style="color: #000; font-weight: bold">${
                            index === 0 ?
                                data[index++] :
                                parseFloat(data[index++] || 0).toFixed(2)
                        }</span>`,
                );
            },
            handleDateChange() {
                this.getOrderSelectionData();
                this.getTableData();
            },
            handleDimensionChange(val) {
                this.dimension = val;
                this.getTableData();
            },
            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },
            async handleExports() {
                const params = this.getTableParams();
                delete params.offset;
                delete params.size;
                try {
                    await this.exportService.startExport(this.exportTaskType, params);
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },
        },
    };
</script>
