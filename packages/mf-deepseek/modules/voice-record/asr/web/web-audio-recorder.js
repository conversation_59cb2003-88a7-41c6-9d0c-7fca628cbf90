export function to16BitPCM(input) {
    const dataLength = input.length * (16 / 8);
    const dataBuffer = new ArrayBuffer(dataLength);
    const dataView = new DataView(dataBuffer);
    let offset = 0;
    for (let i = 0; i < input.length; i++, offset += 2) {
        const s = Math.max(-1, Math.min(1, input[i]));
        dataView.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
    }
    return dataView;
}
export function to16kHz(audioData, sampleRate = 44100) {
    const data = new Float32Array(audioData);
    const fitCount = Math.round(data.length * (16000 / sampleRate));
    const newData = new Float32Array(fitCount);
    const springFactor = (data.length - 1) / (fitCount - 1);
    newData[0] = data[0];
    for (let i = 1; i < fitCount - 1; i++) {
        const tmp = i * springFactor;
        const before = Math.floor(tmp).toFixed();
        const after = Math.ceil(tmp).toFixed();
        const atPoint = tmp - before;
        newData[i] = data[before] + (data[after] - data[before]) * atPoint;
    }
    newData[fitCount - 1] = data[data.length - 1];
    return newData;
}

const audioWorkletCode = `
class MyProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super(options);
    this.waveformBuffer = new Float32Array(512); // 波形专用缓冲区
    this.bufferIndex = 0;
    this.lastWaveformUpdateTime = 0;
    this.audioData = [];
    this.sampleCount = 0;
    this.bitCount = 0;
    this.preTime = 0;
  }

  process(inputs) {
    // 去处理音频数据
    // eslint-disable-next-line no-undef
    if (inputs[0][0]) {
      // 原始音频数据（时域）
      const rawData = inputs[0][0];
      const resampled = ${to16kHz}(rawData, sampleRate);
      this.sampleCount += 1;
      const pcmData = ${to16BitPCM}(resampled);
      this.bitCount += 1;
      const data = [...new Int8Array(pcmData.buffer)];
      this.audioData = this.audioData.concat(data);

      if (new Date().getTime() - this.preTime > 100) {
        this.port.postMessage({
          type: 'audio',
          audioData: new Int8Array(this.audioData),
          sampleCount: this.sampleCount,
          bitCount: this.bitCount,
        });
        this.preTime = new Date().getTime();
        this.audioData = [];
      }

      // 波形处理
      this.processWaveForm(rawData);
      return true;
    }
  }

  processWaveForm(data) {
    // 环形缓冲区存储波形数据
    for (let i = 0; i < data.length; i++) {
      this.waveformBuffer[this.bufferIndex] = data[i];
      this.bufferIndex = (this.bufferIndex + 1) % this.waveformBuffer.length;
    }

    // 按屏幕刷新率发送数据（60fps）
    const now = currentTime;
    if (now - this.lastWaveformUpdateTime > 0.016) {
      this.port.postMessage({
        type: 'waveform',
        data: this.waveformBuffer.slice()
      });
      this.lastWaveformUpdateTime = now;
    }
  }
}

registerProcessor('my-processor', MyProcessor);
`;
const TAG = 'WebAudioRecorder';
navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia ||
    navigator.mozGetUserMedia || navigator.msGetUserMedia;


export default class WebAudioRecorder {
    constructor(requestId, params, isLog) {
        this.audioData = [];
        this.allAudioData = [];
        this.stream = null;
        this.audioContext = null;
        this.audioWorkletNode = null; // 新增：保存AudioWorkletNode引用
        this.scriptProcessor = null; // 新增：保存ScriptProcessor引用
        this.requestId = requestId;
        this.frameTime = [];
        this.frameCount = 0;
        this.sampleCount = 0;
        this.bitCount = 0;
        this.mediaStreamSource = null;
        this.isLog = isLog;
        this.params = params;
    }
    static isSupportMediaDevicesMedia() {
        return !!(navigator.getUserMedia || (navigator.mediaDevices && navigator.mediaDevices.getUserMedia));
    }
    static isSupportUserMediaMedia() {
        return !!navigator.getUserMedia;
    }
    static isSupportAudioContext() {
        return typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined';
    }
    static isSupportMediaStreamSource(requestId, audioContext) {
        return typeof audioContext.createMediaStreamSource === 'function';
    }
    static isSupportAudioWorklet(audioContext) {
        return audioContext.audioWorklet && typeof audioContext.audioWorklet.addModule === 'function' &&
        typeof AudioWorkletNode !== 'undefined';
    }
    static isSupportCreateScriptProcessor(requestId, audioContext) {
        return typeof audioContext.createScriptProcessor === 'function';
    }
    start() {
        this.frameTime = [];
        this.frameCount = 0;
        this.allAudioData = [];
        this.audioData = [];
        this.sampleCount = 0;
        this.bitCount = 0;
        this.getDataCount = 0;
        this.audioContext = null;
        this.mediaStreamSource = null;
        this.stream = null;
        this.preTime = 0;
        try {
            if (WebAudioRecorder.isSupportAudioContext()) {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            } else {
                this.isLog && console.log(this.requestId, '浏览器不支持AudioContext', TAG);
                this.onError('录音失败，浏览器不支持AudioContext');
            }
        } catch (e) {
            this.isLog && console.log(this.requestId, '浏览器不支持webAudioApi相关接口', e, TAG);
            this.onError('录音失败，浏览器不支持webAudioApi相关接口');
        }
        this.getUserMedia(this.requestId, this.getAudioSuccess, this.getAudioFail);
    }
    async stop() {
        try {
            if (this.audioWorkletNode) {
                this.audioWorkletNode.disconnect();
                this.audioWorkletNode.port.onmessage = null;
                this.audioWorkletNode = null;
            }
            if (this.scriptProcessor) {
                this.scriptProcessor.disconnect();
                this.scriptProcessor.onaudioprocess = null;
                this.scriptProcessor = null;
            }
            this.audioContext && this.audioContext.suspend();
            this.isLog && console.log(this.requestId, `webRecorder stop ${this.sampleCount}/${this.bitCount}/${this.getDataCount}` , JSON.stringify(this.frameTime), TAG);
            await this.onStop(this.allAudioData);
        } catch (e) {
            this.onError(`停止录音时出错： ${e?.message}`);
        }
    }
    destroyStream() {
    // 关闭通道
        if (this.stream) {
            this.stream.getTracks().map((val) => {
                val.stop();
            });
            this.stream = null;
        }
    }
    async getUserMedia(requestId, getStreamAudioSuccess, getStreamAudioFail) {
        let audioOption = {
            echoCancellation: true,
        };
        if (this.params && String(this.params.echoCancellation) === 'false') { // 关闭回声消除
            audioOption = {
                echoCancellation: false,
            };
        }
        if (this.params?.deviceId) {
            audioOption.deviceId = {
                exact: this.params.deviceId,
            };
        }
        const mediaOption = {
            audio: audioOption,
            video: false,
        };
        // 获取用户的麦克风
        if (WebAudioRecorder.isSupportMediaDevicesMedia()) {
            navigator.mediaDevices
                .getUserMedia(mediaOption)
                .then((stream) => {
                    this.stream = stream;
                    getStreamAudioSuccess.call(this, requestId, stream);
                })
                .catch((e) => {
                    getStreamAudioFail.call(this, requestId, e);
                });
        } else if (WebAudioRecorder.isSupportUserMediaMedia()) {
            navigator.getUserMedia(mediaOption,
                (stream) => {
                    this.stream = stream;
                    getStreamAudioSuccess.call(this, requestId, stream);
                },
                function(err) {
                    getStreamAudioFail.call(this, requestId, err);
                },
            );
        } else {
            if (navigator.userAgent.toLowerCase().match(/chrome/) && location.origin.indexOf('https://') < 0) {
                this.isLog && console.log(this.requestId, 'chrome下获取浏览器录音功能，因为安全性问题，需要在localhost或127.0.0.1或https下才能获取权限', TAG);
                this.onError('录音失败，chrome下获取浏览器录音功能，因为安全性问题，需要在localhost或127.0.0.1或https下才能获取权限');
            } else {
                this.isLog && console.log(this.requestId, '无法获取浏览器录音功能，请升级浏览器或使用chrome', TAG);
                this.onError('录音失败，无法获取浏览器录音功能，请升级浏览器或使用chrome');
            }
            this.audioContext && this.audioContext.close();
        }
    }
    async getAudioSuccess(requestId, stream) {
        if (!this.audioContext) {
            return false;
        }
        if (this.mediaStreamSource) {
            this.mediaStreamSource.disconnect();
            this.mediaStreamSource = null;
        }
        this.audioTrack = stream.getAudioTracks()[0];
        const mediaStream = new MediaStream();
        mediaStream.addTrack(this.audioTrack);
        this.mediaStreamSource = this.audioContext.createMediaStreamSource(mediaStream);
        if (WebAudioRecorder.isSupportMediaStreamSource(requestId, this.audioContext)) {
            if (WebAudioRecorder.isSupportAudioWorklet(this.audioContext)) { // 不支持 AudioWorklet 降级
                await this.audioWorkletNodeDealAudioData(this.mediaStreamSource, requestId);
            } else {
                this.scriptNodeDealAudioData(this.mediaStreamSource, requestId);
            }
            this.onStart(this.audioTrack);
        } else { // 不支持 MediaStreamSource
            this.isLog && console.log(this.requestId, '不支持MediaStreamSource', TAG);
            this.onError('录音失败，不支持MediaStreamSource');
        }
    }
    getAudioFail(requestId, err) {
        if (err && err.err && err.err.name === 'NotAllowedError') {
            this.isLog && console.log(requestId,'授权失败', JSON.stringify(err.err), TAG);
        }
        this.isLog && console.log(this.requestId, 'getAudioFail', JSON.stringify(err), TAG);
        this.onError(err);
        this.stop();
    }
    scriptNodeDealAudioData(mediaStreamSource, requestId) {
        if (WebAudioRecorder.isSupportCreateScriptProcessor(requestId, this.audioContext)) {
            // 创建一个音频分析对象，采样的缓冲区大小为0（自动适配），输入和输出都是单声道
            const scriptProcessor = this.audioContext.createScriptProcessor(1024, 1, 1);
            this.scriptProcessor = scriptProcessor;

            // 初始化波形缓冲区，与 AudioWorklet 中的设置保持一致
            this.waveformBuffer = new Float32Array(512); // 波形专用缓冲区
            this.bufferIndex = 0;
            this.lastWaveformUpdateTime = 0;

            // 连接
            this.mediaStreamSource && this.mediaStreamSource.connect(scriptProcessor);
            scriptProcessor && scriptProcessor.connect(this.audioContext.destination);

            scriptProcessor.onaudioprocess = (e) => {
                this.getDataCount += 1;
                // 去处理音频数据
                const inputData = e.inputBuffer.getChannelData(0);

                // 处理波形数据，与 AudioWorklet 中的 processWaveForm 方法保持一致
                this.processWaveForm(inputData);

                const output = to16kHz(inputData, this.audioContext.sampleRate);
                const audioData = to16BitPCM(output);
                this.audioData.push(...new Int8Array(audioData.buffer));
                this.allAudioData.push(...new Int8Array(audioData.buffer));

                if (new Date().getTime() - this.preTime > 100) {
                    const audioDataArray = new Int8Array(this.audioData);

                    this.frameTime.push(`${Date.now()}-${this.frameCount}`);
                    this.frameCount += 1;
                    this.preTime = new Date().getTime();
                    this.onReceivedData(audioDataArray);
                    this.audioData = [];
                    this.sampleCount += 1;
                    this.bitCount += 1;
                }
            };
        } else { // 不支持
            this.isLog && console.log(this.requestId, '不支持createScriptProcessor', TAG);
        }
    }

    // 处理波形数据，与 AudioWorklet 中的 processWaveForm 方法保持一致
    processWaveForm(data) {
        // 环形缓冲区存储波形数据
        for (let i = 0; i < data.length; i++) {
            this.waveformBuffer[this.bufferIndex] = data[i];
            this.bufferIndex = (this.bufferIndex + 1) % this.waveformBuffer.length;
        }

        // 按屏幕刷新率发送数据（60fps）
        const now = Date.now();
        if (now - this.lastWaveformUpdateTime > 16) { // 16ms 大约相当于 60fps
            this.onWaveformUpdate(this.waveformBuffer.slice());
            this.lastWaveformUpdateTime = now;
        }
    }
    async audioWorkletNodeDealAudioData(mediaStreamSource, requestId) {
        try {
            const audioWorkletBlobURL = window.URL.createObjectURL(new Blob([audioWorkletCode], { type: 'text/javascript' }));
            await this.audioContext.audioWorklet.addModule(audioWorkletBlobURL);
            const myNode = new AudioWorkletNode(this.audioContext, 'my-processor', {
                numberOfInputs: 1, numberOfOutputs: 1, channelCount: 1,
            });
            this.audioWorkletNode = myNode;
            myNode.onprocessorerror = () => {
                // 降级
                this.scriptNodeDealAudioData(mediaStreamSource, this.requestId);
                return false;
            };
            myNode.port.onmessage = (event) => {
                if (event.data.type === 'audio') {
                    this.frameTime.push(`${Date.now()}-${this.frameCount}`);
                    this.onReceivedData(event.data.audioData);
                    this.frameCount += 1;
                    this.allAudioData.push(...event.data.audioData);
                    this.sampleCount = event.data.sampleCount;
                    this.bitCount = event.data.bitCount;
                } else if (event.data.type === 'waveform') {
                    this.onWaveformUpdate(event.data.data);
                }
            };
            myNode.port.onmessageerror = () => {
                // 降级
                this.scriptNodeDealAudioData(mediaStreamSource, requestId);
                return false;
            };
            mediaStreamSource && mediaStreamSource.connect(myNode).connect(this.audioContext.destination);
        } catch (e) {
            this.isLog && console.log(this.requestId, 'audioWorkletNodeDealAudioData catch error', JSON.stringify(e), TAG);
            this.onError(e);
        }
    }
    // 获取音频数据
    onReceivedData(data) {
        this.isLog && console.log(this.requestId, 'onReceivedData', JSON.stringify(data), TAG);
    }
    onWaveformUpdate(data) {
        this.isLog && console.log(this.requestId, 'onWaveformUpdate', JSON.stringify(data), TAG);
    }
    onStart() {
        this.isLog && console.log(this.requestId, 'onStart', TAG);
    }
    onError(res) {
        this.isLog && console.log(this.requestId, 'onError', JSON.stringify(res), TAG);
    }
    onStop(res) {
        this.isLog && console.log(this.requestId, 'onStop', JSON.stringify(res), TAG);
    }
}
