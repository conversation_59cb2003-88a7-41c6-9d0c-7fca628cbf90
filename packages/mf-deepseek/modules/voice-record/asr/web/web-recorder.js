import WebAudioRecorder from './web-audio-recorder.js';
import { checkMicPermission } from '../../../../common/utils/audio';
import BaseRecorder from '../common/base-recorder';
import { SOCKET_BUSINESS_TYPE } from '../common/constants';
import Logger from '../../../../common/utils/logger';

// GUID生成函数（从 speechrecognizer.js 迁移）
export const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

const TAG = 'WebRecorder';

/**
 * 重构后的Web录音器
 * 替代原有的WebAudioSpeechRecognizer
 *
 * @class WebRecorder
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */
export default class WebRecorder extends BaseRecorder {
    constructor(options) {
        super({
            ...options,
            businessType: SOCKET_BUSINESS_TYPE.VOICE_MR,
        });
        this.webRecorder = null; // Web音频录制器
        this.requestId = null; // 单次录制 id
        this.config = null; // 录音配置
    }
    
    /**
     * @param {Object} config 录音配置
     * @param {string} config.deviceId 设备ID
     * @param {string} config.bizId 业务ID
     * @returns {Promise<unknown>}
     */
    async startRecorder(config) {
        // 检查麦克风权限，未授权会触发授权
        await checkMicPermission();
        return new Promise((resolve) => {
            try {
                this.isLog && console.log('WebRecorder start function is click');
                this.requestId = guid();
                
                // 创建Web音频录制器
                this.webRecorder = new WebAudioRecorder(this.requestId, config, this.isLog);
                
                // 设置录制器事件
                this.webRecorder.onReceivedData = (data) => {
                    this.writeAudioData(data);
                };
                
                this.webRecorder.onWaveformUpdate = (data) => {
                    this.onWaveformUpdate && this.onWaveformUpdate(data);
                };
                
                this.webRecorder.onStart = async (audioTrack) => {
                    resolve(audioTrack);
                };
                
                // 录音失败时
                this.webRecorder.onError = (err) => {
                    this.stop();
                    this.onError(err);
                    resolve(false);
                };
                
                this.webRecorder.start();
            } catch (e) {
                console.error(TAG, 'start exception', e);
                this.onError(e);
            }
        });
    }
    
    /**
     * 开始录音
     * @param {Object} config 录音配置
     * @param {string} config.deviceId 设备ID
     * @param {string} config.bizId 业务ID
     */
    async start(config) {
        this.config = config;
        const res = await this.startRecorder(this.config);
        if (res === false) {
            return false;
        }
        Logger.reportAnalytics('asr-context', {
            mic: res?.label,
            bizId: config.bizId,
        });
        return super.start();
    }
    
    
    async pause() {
        if (this.webRecorder) {
            this.webRecorder.stop();
            this.webRecorder.destroyStream();
            this.webRecorder = null;
        }
        return super.pause();
    }
    
    async resume() {
        const res = await this.startRecorder(this.config);
        if (res === false) {
            return;
        }
        return super.resume();
    }
    
    /**
     * 停止录音
     */
    async stop() {
        this.isLog && console.log('WebRecorder.stop');
        
        if (this.webRecorder) {
            this.webRecorder.stop();
            this.webRecorder.destroyStream();
        }
        super.stop();
    }
    
    clearState() {
    
    }
    

    /**
     * 发送音频数据（从 SpeechRecognizer.write 迁移）
     * @param {ArrayBuffer|Uint8Array} data 音频数据
     */
    writeAudioData(data) {
        if (!data) {
            return;
        }
        try {
            // 解构赋值，转为普通数组
            const audioData = [...data];

            if (!this.socket || !this.socket.connected) {
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    if (this.socket && this.socket.connected) {
                        this.socket.emit('audio-data', audioData);
                    }
                }, 100);
                return false;
            }

            this.socket.emit('audio-data', audioData);
            return true;
        } catch (e) {
            this.isLog && console.log(this.requestId, '发送音频数据 error catch', e, TAG);
            this.logger?.report('WebRecorder音频数据发送异常', {
                requestId: this.requestId,
                err: e,
            });
            return false;
        }
    }
    
    /**
     * 销毁音频流
     */
    cleanup() {
        this.logger = null;
        this.config = null;
        this.isLog && console.log('WebRecorder.cleanup', this.webRecorder);

        if (this.webRecorder) {
            this.webRecorder.stop();
            this.webRecorder.destroyStream();
        }

        // 清理Socket连接
        this.socket = null;
    }
}
