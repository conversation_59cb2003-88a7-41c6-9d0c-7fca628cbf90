/**
 * ASR 录音系统事件常量定义
 * 统一管理所有事件名称，避免硬编码字符串，提高代码可维护性
 *
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */

// ================================
// Socket 连接事件
// ================================
export const SOCKET_EVENTS = {
    // 基础连接事件
    CONNECT: 'connect',
    DISCONNECT: 'disconnect',
    ERROR: 'error',
};

// ================================
// 设备管理事件
// ================================
export const DEVICE_EVENTS = {
    // PC 创建房间 事件（注册设备，需要传房间号）
    JOIN_ROOM: 'join',
    
    // PC 创建房间 回复
    JOIN_ROOM_ACK: 'join',
    // PC 创建房间 ERROR
    JOIN_ROOM_ERROR: 'error',
    
    WAITING_DEVICE: 'waiting-join',
    
    // App连接事件
    APP_CONNECTED: 'joined',
    /**
     * @typedef {Object} DeviceShutdownEvent
     * @property {'pc'|'app'} loginSide 登录方
     * @property {int} isNew 是否为新连接
     * @property {0|10|99} shutdownReason 断开原因: 0-未下线，10-正常下线，99-异常崩溃下线
     */
    APP_DISCONNECTED: 'shutdown',
    
    // 设备状态查询事件
    APP_STATUS_CHANGED: 'device-status-change',
    CHECK_DEVICE_STATUS: 'check-device-status',
};

// ================================
// 录音控制事件（重构为统一的 recording-action 事件）
// ================================
export const RECORDING_EVENTS = {
    // 统一的录音操作事件
    RECORDING_ACTION: 'recording-action',
};

// ================================
// 录音操作类型（recording-action 事件的 event 参数）
// ================================
export const RECORDING_ACTION_TYPES = {
    // 控制操作
    START_RECORDING: 'start-recording',
    STOP_RECORDING: 'stop-recording',
    PAUSE_RECORDING: 'pause-recording',
    RESUME_RECORDING: 'restart-recording',
    CANCEL_RECORDING: 'cancel-recording',

    // 状态响应
    RECORDING_STARTED: 'recording-started',
    RECORDING_STOPPED: 'recording-stopped',
    RECORDING_PAUSED: 'recording-paused',
    RECORDING_RESUMED: 'recording-restarted',
    RECORDING_INTERRUPTED: 'recording-interrupted',
    RECORDING_ERROR: 'error',
};

// ================================
// 数据传输事件
// ================================
export const DATA_EVENTS = {
    // ASR 识别结果（服务器端统一发送此事件）
    ASR_RESULT: 'asr-result',
    
    // 波形数据
    WAVEFORM_DATA: 'waveform-data',
    
    // 录制时间
    RECORDING_TIME: 'stop-watch',
};

// ================================
// 业务层事件（Manager内部事件系统）
// ================================
export const BUSINESS_EVENTS = {
    // 设备连接状态变化
    DEVICE_CONNECTED: 'device-connected',
    DEVICE_DISCONNECTED: 'device-disconnected',

    // 设备状态变化
    DEVICE_STATUS_CHANGED: 'device-status-changed',

    // 录音流程事件
    DEVICE_WAITING: 'device-waiting', // 等待设备连接
};

// ================================
// 事件分组（便于批量操作）
// ================================
export const EVENT_GROUPS = {
    // 所有Socket原始事件
    SOCKET_RAW_EVENTS: [
        ...Object.values(SOCKET_EVENTS),
        ...Object.values(DEVICE_EVENTS),
        ...Object.values(RECORDING_EVENTS),
        ...Object.values(DATA_EVENTS),
    ],
    
    // 设备管理相关事件
    DEVICE_MANAGEMENT_EVENTS: [
        SOCKET_EVENTS.CONNECT,
        SOCKET_EVENTS.DISCONNECT,
        SOCKET_EVENTS.ERROR,
        DEVICE_EVENTS.APP_CONNECTED,
        DEVICE_EVENTS.APP_DISCONNECTED,
        DEVICE_EVENTS.APP_STATUS_CHANGED,
    ],
    
    // 录音会话相关事件
    RECORDING_SESSION_EVENTS: [
        ...Object.values(RECORDING_EVENTS),
        ...Object.values(DATA_EVENTS),
    ],
    
    // 业务层事件
    BUSINESS_LAYER_EVENTS: [
        ...Object.values(BUSINESS_EVENTS),
    ],
};

/**
 * 设备连接状态
 * @type {{NONE: number, ONLINE: number, OFFLINE: number}}
 */
export const DEVICE_CONNECT_STATUS = {
    NONE: 0, // 无设备，从来没用过 app
    ONLINE: 1, // 设备在线
    OFFLINE: 2, // 设备离线
};

/**
 * Socket业务类型
 * @type {{VOICE_MR: number, VOICE_MR_PC_JOIN_APP: number}}
 */
export const SOCKET_BUSINESS_TYPE = {
    VOICE_MR: 0, // 语音病历
    VOICE_MR_PC_JOIN_APP: 1, // PC 端 join APP 端
};

// ================================
// 默认导出（便于整体导入）
// ================================
export default {
    SOCKET_EVENTS,
    DEVICE_EVENTS,
    RECORDING_EVENTS,
    RECORDING_ACTION_TYPES,
    DATA_EVENTS,
    BUSINESS_EVENTS,
    EVENT_GROUPS,
    DEVICE_CONNECT_STATUS,
    SOCKET_BUSINESS_TYPE,
};

// ================================
// 类型定义（用于 TypeScript 或 JSDoc）
// ================================
/**
 * @typedef {Object} EventConstants
 * @property {Object} SOCKET_EVENTS Socket连接事件
 * @property {Object} DEVICE_EVENTS 设备管理事件
 * @property {Object} RECORDING_EVENTS 录音控制事件
 * @property {Object} DATA_EVENTS 数据传输事件
 * @property {Object} BUSINESS_EVENTS 业务层事件
 * @property {Object} EVENT_GROUPS 事件分组
 */
