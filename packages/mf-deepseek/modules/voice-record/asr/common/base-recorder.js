import {
    RECORDING_EVENTS,
    RECORDING_ACTION_TYPES, DATA_EVENTS,
} from '../common/constants';

const TAG = 'BaseRecorder';
/**
 * 基础录音器类
 * 提供录音器的公共逻辑
 *
 * @class BaseRecorder
 * <AUTHOR> Assistant
 * @date 2025-01-19
 */
export default class BaseRecorder {
    /**
     * 构造函数
     * @param {Object} options 配置选项
     * @param {Function} options.businessId 业务 id
     * @param {Function} options.businessType 业务 type
     * @param {Function} options.onStart 开始录音回调
     * @param {Function} options.onPause 暂停录音回调
     * @param {Function} options.onResume 继续录音回调
     * @param {Function} options.onStop 停止录音回调
     * @param {Function} options.onError 错误回调
     * @param {Function} options.onWaitingDevice 等待设备连接回调
     * @param {Function} options.onWaitingDeviceCancel 取消设备等待
     * @param {Function} options.onWaveformUpdate 波形数据回调
     * @param {Function} options.onTimeUpdate 时间更新回调
     * @param {Socket} options.socket 共享的Socket对象
     * @param {Object} options.logger 日志记录器
     */
    constructor(options = {}) {
        this.businessType = options.businessType;
        this.businessId = options.businessId;
        
        // 事件回调函数
        this.onStart = options.onStart || (() => {});
        this.onPause = options.onPause || (() => {});
        this.onResume = options.onResume || (() => {});
        this.onStop = options.onStop || (() => {});
        this.onError = options.onError || (() => {});
        this.onWaitingDevice = options.onWaitingDevice || (() => {});
        this.onWaitingDeviceCancel = options.onWaitingDeviceCancel || (() => {});
        this.onWaveformUpdate = options.onWaveformUpdate || (() => {});
        this.onTimeUpdate = options.onTimeUpdate || (() => {});

        // 日志记录器
        this.logger = options.logger;

        // Socket连接
        this.socket = options.socket;

        // Manager引用（用于设备状态检测和事件发送）
        this.manager = options.manager;
        
        // 是否开启日志
        this.isLog = options.isLog;
        
        // 临时逻辑
        this.socket.on('join', (data) => {
            this.taskId = data.taskId;
        });
        
        this.socket.on(RECORDING_EVENTS.RECORDING_ACTION, (data) => {
            this.handleRecordingAction(data);
        });
        
        this.socket.on(DATA_EVENTS.RECORDING_TIME, (data) => {
            this.onTimeUpdate(data);
        });
        
        this.recordingStartResolve = null;
    }
    
    /**
     * 处理统一的录音操作事件
     * 根据 event 字段分发到对应的处理逻辑
     * @param {Object} data 事件数据
     * @param {string} data.event 操作类型
     */
    handleRecordingAction(data) {
        const {
            event: actionType, data: eventData,
        } = data;

        this.logger?.report(`[${TAG}]handleRecordingAction`, {
            actionType,
            data,
        });

        // 根据操作类型分发到对应的处理方法
        switch (actionType) {
            case RECORDING_ACTION_TYPES.RECORDING_STARTED: {
                const payload = eventData || {
                    taskId: this.taskId,
                };
                // 如果有等待的Promise，resolve它
                if (this.recordingStartResolve) {
                    this.recordingStartResolve(payload);
                    this.recordingStartResolve = null;
                }
                this.onStart(payload);
                break;
            }
            case RECORDING_ACTION_TYPES.RECORDING_PAUSED:
                this.onPause(eventData);
                break;
            case RECORDING_ACTION_TYPES.RECORDING_RESUMED:
                this.onResume(eventData);
                break;
            case RECORDING_ACTION_TYPES.RECORDING_STOPPED:
                this.onStop(eventData);
                break;
            case RECORDING_ACTION_TYPES.RECORDING_INTERRUPTED:
                // 触发暂停回调（中断视为暂停）
                this.onPause(eventData);
                break;
            case RECORDING_ACTION_TYPES.RECORDING_ERROR:
                this.onError(eventData);
                break;
            default:
                this.logger?.report('未知的录音操作类型', {
                    actionType, eventData,
                });
                console.warn('未知的录音操作类型:', actionType);
        }
    }

    /**
     * 发送录音控制指令的统一方法
     * @param {string} actionType 操作类型（RECORDING_ACTION_TYPES 中的值）
     * @param {Object} data
     * @returns {boolean} 是否发送成功
     */
    sendRecordingCommand(actionType, data = {}) {
        if (!this.socket || !this.socket.isConnected) {
            this.logger?.report('Socket未连接，无法发送录音指令', { actionType });
            return false;
        }

        const payload = {
            event: actionType, // 通过 event 参数指定操作类型
            ...data,
        };
        
        // 使用统一的 recording-action 事件
        this.socket.emit(RECORDING_EVENTS.RECORDING_ACTION, payload);
        this.logger?.report('录音指令已发送', {
            event: RECORDING_EVENTS.RECORDING_ACTION,
            actionType,
            payload,
        });
        return true;
    }

    /**
     * 开始录音（统一入口）
     * 支持新的智能录音逻辑和向后兼容
     * @param {Object} config 录音配置
     * @param {string} config.deviceId 设备ID
     * @returns {Promise<boolean>} 是否成功开始录音
     */
    async start(config) {
        this.logger?.log('开始录音', config);

        try {
            // 清理之前的录音状态
            this.clearState();

            // 发送录音指令
            const success = this.sendRecordingCommand(
                RECORDING_ACTION_TYPES.START_RECORDING,
                {
                    businessId: this.businessId,
                    businessType: this.businessType,
                },
            );
            
            if (!success) {
                throw new Error('发送录音指令失败');
            }
            
            // 等待录音开始确认
            return await this.waitForRecordingStart();

        } catch (error) {
            console.error('开始录音失败:', error);
            this.onError(error);
            return false;
        }
    }

    /**
     * 暂停录音
     * @returns {Promise<void>}
     */
    async pause() {
        this.sendRecordingCommand(RECORDING_ACTION_TYPES.PAUSE_RECORDING);
    }

    /**
     * 继续录音
     * 根据之前的错误状态智能选择恢复策略
     * @returns {Promise<void>}
     */
    async resume() {
        this.sendRecordingCommand(RECORDING_ACTION_TYPES.RESUME_RECORDING);
    }

    /**
     * 停止录音
     * @returns {Promise<void>}
     */
    async stop() {
        this.sendRecordingCommand(RECORDING_ACTION_TYPES.STOP_RECORDING);
    }
    
    /**
     * 等待录音开始
     * @returns {Promise<boolean>} 是否成功开始
     */
    async waitForRecordingStart() {
        return new Promise((resolve) => {
            // 保存resolve函数，在录音开始时调用
            this.recordingStartResolve = resolve;
        });
    }
    
    cancelWaitingForDevice() {
        this.sendRecordingCommand(RECORDING_ACTION_TYPES.CANCEL_RECORDING);
    }
    
    /**
     * 清理录音状态
     */
    clearState() {
        // 重置状态
        this.recordingStartResolve = null;
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 清理录音状态
        this.clearState();

        // 清理录音事件监听器
        if (this.socket) {
            this.socket.off(RECORDING_EVENTS.RECORDING_ACTION);
            this.socket.off(DATA_EVENTS.RECORDING_TIME);
        }
        this.logger?.log('BaseRecorder资源已清理');
    }
}
