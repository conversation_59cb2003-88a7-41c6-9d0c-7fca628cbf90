// packages/mf-deepseek/modules/voice-record/components/audio-merger.js
// 音频数据合并工具，用于将多段音频合并成一个连续的音频文件

/**
 * 音频合并器类
 * 负责下载多个音频片段并将它们拼接成一个完整的音频数据流
 */
export default class AudioMerger {
    constructor() {
        this.audioContext = null;
        this.sampleRate = 16000; // 默认采样率
        this.numberOfChannels = 1; // 单声道
    }

    /**
     * 初始化音频上下文
     */
    initAudioContext() {
        if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        return this.audioContext;
    }

    /**
     * 下载音频文件并转换为 ArrayBuffer
     * @param {string} url 音频文件 URL
     * @returns {Promise<ArrayBuffer>} 音频数据
     */
    async downloadAudioData(url) {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`下载音频失败: ${response.status} ${response.statusText}`);
            }
            return await response.arrayBuffer();
        } catch (error) {
            console.error('下载音频数据失败:', url, error);
            throw error;
        }
    }

    /**
     * 将 ArrayBuffer 解码为 AudioBuffer
     * @param {ArrayBuffer} arrayBuffer 音频数据
     * @returns {Promise<AudioBuffer>} 解码后的音频缓冲区
     */
    async decodeAudioData(arrayBuffer) {
        const audioContext = this.initAudioContext();
        
        return new Promise((resolve, reject) => {
            audioContext.decodeAudioData(
                arrayBuffer,
                (audioBuffer) => {
                    resolve(audioBuffer);
                },
                (error) => {
                    reject(new Error(`音频解码失败: ${error}`));
                },
            );
        });
    }

    /**
     * 合并多个 AudioBuffer 为一个连续的 AudioBuffer
     * @param {AudioBuffer[]} audioBuffers 音频缓冲区数组
     * @returns {AudioBuffer} 合并后的音频缓冲区
     */
    mergeAudioBuffers(audioBuffers) {
        if (!audioBuffers.length) {
            throw new Error('没有音频缓冲区可合并');
        }

        const audioContext = this.initAudioContext();
        
        // 计算总长度
        let totalLength = 0;
        let maxChannels = 0;
        const commonSampleRate = audioBuffers[0].sampleRate;

        audioBuffers.forEach((buffer) => {
            totalLength += buffer.length;
            maxChannels = Math.max(maxChannels, buffer.numberOfChannels);
            
            // 检查采样率一致性
            if (buffer.sampleRate !== commonSampleRate) {
                console.warn(`音频采样率不一致: ${buffer.sampleRate} vs ${commonSampleRate}`);
            }
        });

        // 创建合并后的音频缓冲区
        const mergedBuffer = audioContext.createBuffer(
            maxChannels,
            totalLength,
            commonSampleRate,
        );

        // 合并音频数据
        let offset = 0;
        audioBuffers.forEach((buffer) => {
            for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
                const sourceData = buffer.getChannelData(channel);
                const targetData = mergedBuffer.getChannelData(channel);
                
                // 复制数据到目标缓冲区
                for (let i = 0; i < sourceData.length; i++) {
                    targetData[offset + i] = sourceData[i];
                }
            }
            offset += buffer.length;
        });

        return mergedBuffer;
    }

    /**
     * 将 AudioBuffer 转换为 Blob URL
     * @param {AudioBuffer} audioBuffer 音频缓冲区
     * @returns {string} Blob URL
     */
    audioBufferToBlobUrl(audioBuffer) {
        const wavArrayBuffer = this.audioBufferToWav(audioBuffer);
        const blob = new Blob([wavArrayBuffer], { type: 'audio/wav' });
        return URL.createObjectURL(blob);
    }

    /**
     * 将 AudioBuffer 转换为 WAV 格式的 ArrayBuffer
     * @param {AudioBuffer} audioBuffer 音频缓冲区
     * @returns {ArrayBuffer} WAV 格式的数据
     */
    audioBufferToWav(audioBuffer) {
        const { numberOfChannels } = audioBuffer;
        const { sampleRate } = audioBuffer;
        const { length } = audioBuffer;
        const bitsPerSample = 16;
        const bytesPerSample = bitsPerSample / 8;
        const blockAlign = numberOfChannels * bytesPerSample;
        const byteRate = sampleRate * blockAlign;
        const dataSize = length * blockAlign;
        const bufferSize = 44 + dataSize;

        const arrayBuffer = new ArrayBuffer(bufferSize);
        const view = new DataView(arrayBuffer);

        // WAV 文件头
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, bufferSize - 8, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, numberOfChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, bitsPerSample, true);
        writeString(36, 'data');
        view.setUint32(40, dataSize, true);

        // 写入音频数据
        let offset = 44;
        for (let i = 0; i < length; i++) {
            for (let channel = 0; channel < numberOfChannels; channel++) {
                const sample = audioBuffer.getChannelData(channel)[i];
                const intSample = Math.max(-1, Math.min(1, sample)) * 0x7FFF;
                view.setInt16(offset, intSample, true);
                offset += 2;
            }
        }

        return arrayBuffer;
    }

    /**
     * 合并多段音频为单个音频文件
     * @param {Array} audioSegments 音频段信息数组
     * @returns {Promise<{blobUrl: string, duration: number, audioBuffer: AudioBuffer}>} 合并结果
     */
    async mergeAudioSegments(audioSegments) {
        if (!audioSegments || !audioSegments.length) {
            throw new Error('没有音频段可合并');
        }

        console.log(`开始合并 ${audioSegments.length} 个音频段...`);

        try {
            // 1. 下载所有音频数据
            const downloadPromises = audioSegments.map(async (segment, index) => {
                console.log(`下载音频段 ${index + 1}/${audioSegments.length}: ${segment.voiceUrl}`);
                const arrayBuffer = await this.downloadAudioData(segment.voiceUrl);
                const audioBuffer = await this.decodeAudioData(arrayBuffer);
                return audioBuffer;
            });

            const audioBuffers = await Promise.all(downloadPromises);
            console.log('所有音频段下载完成');

            // 2. 合并音频缓冲区
            const mergedBuffer = this.mergeAudioBuffers(audioBuffers);
            console.log(`音频合并完成，总时长: ${mergedBuffer.duration.toFixed(2)} 秒`);

            // 3. 转换为 Blob URL
            const blobUrl = this.audioBufferToBlobUrl(mergedBuffer);

            return {
                blobUrl,
                duration: mergedBuffer.duration * 1000, // 转换为毫秒
                audioBuffer: mergedBuffer,
                sampleRate: mergedBuffer.sampleRate,
                numberOfChannels: mergedBuffer.numberOfChannels,
            };

        } catch (error) {
            console.error('音频合并失败:', error);
            throw error;
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
            this.audioContext = null;
        }
    }
}
