<template>
    <abc-dropdown
        custom-class="device-selector-dropdown"
        placement="bottom"
        @change="handleDeviceChange"
    >
        <abc-button
            slot="reference"
            class="device-selector-trigger"
            theme="default"
            variant="text"
        >
            <abc-icon :icon="currentDevice.icon" color="var(--abc-color-T3)"></abc-icon>
            <span v-abc-title.ellipsis="currentDevice.label" style="max-width: 140px;">
                {{ currentDevice.label }}
            </span>
            <abc-icon icon="s-dowline-medium" color="var(--abc-color-T3)"></abc-icon>
        </abc-button>

        <!-- 手机App录音选项 -->
        <div class="device-group-header">
            <abc-text size="mini">
                选择录音设备
            </abc-text>
        </div>
        <abc-dropdown-item
            v-for="device in availableDevices"
            :key="device.deviceId"
            :value="device"
            class="device-dropdown-item"
            :class="{ 'is-selected': selectedDeviceId === device.deviceId }"
        >
            <abc-flex align="center" justify="space-between" gap="small">
                <abc-flex gap="4" align="center" style="max-width: calc(100% - 14px);">
                    <abc-icon :icon="device.icon" size="14" color="var(--abc-color-T3)"></abc-icon>
                    <abc-text v-abc-title.ellipsis="device.label" size="normal">
                        {{ device.label }}
                    </abc-text>
                </abc-flex>
                <abc-icon
                    v-if="selectedDeviceId === device.deviceId"
                    icon="s-check-checkbox-medium"
                    color="var(--abc-color-theme2)"
                    size="14"
                ></abc-icon>
                <div v-else style="width: 14px;"></div>
            </abc-flex>
        </abc-dropdown-item>

        <abc-dropdown-item v-if="availableDevices.length === 0" disabled>
            未识别到录音设备
        </abc-dropdown-item>

        <!-- 当前没有推荐的麦克风，推荐购买 -->
        <template v-if="!hasSuggestMic">
            <div style="padding: 0 8px;">
                <abc-divider margin="mini"></abc-divider>
            </div>
            <abc-dropdown-item label="推荐购买专业录音设备" value="@buy">
                <abc-link>
                    推荐购买专业录音设备
                </abc-link>
            </abc-dropdown-item>
        </template>
    </abc-dropdown>
</template>

<script>
    /**
     * 录音设备选择下拉组件
     * 支持PC录音和手机App录音两种模式的设备选择
     *
     * <AUTHOR> Assistant
     * @date 2025-01-19
     */
    export default {
        name: 'DeviceSelectorDropdown',
        props: {
            // 可用设备列表
            availableDevices: {
                type: Array,
                default: () => [],
                validator: (devices) => {
                    return devices.every((device) =>
                        device.deviceId &&
                        device.label &&
                        device.type &&
                        device.icon,
                    );
                },
            },
            // 当前选中的设备ID
            selectedDeviceId: {
                type: String,
                default: '',
            },
        },
        computed: {
            // PC设备列表
            pcDevices() {
                return this.availableDevices.filter((device) => device.type === 'pc');
            },
            // 手机设备列表
            mobileDevices() {
                return this.availableDevices.filter((device) => device.type === 'mobile');
            },
            currentDevice() {
                if (!this.availableDevices.length) {
                    return {
                        deviceId: '',
                        label: '未识别到录音设备',
                        type: 'pc',
                        isOnline: false,
                    };
                }
                return this.availableDevices.find((device) => device.deviceId === this.selectedDeviceId) ?? {
                    deviceId: 'app',
                    label: '我的手机',
                    type: 'mobile',
                    icon: 's-mobile-small-fill',
                    isOnline: false,
                };
            },
            hasSuggestMic() {
                return this.availableDevices.some((device) => device.isSuggest);
            },
        },
        methods: {
            /**
             * 处理设备切换
             * @param {Object} device 选中的设备对象
             */
            handleDeviceChange(device) {
                if (device === '@buy') {
                    this.handleBuySuggestDeviceClick();
                    return;
                }
                if (!device || device.deviceId === this.selectedDeviceId) {
                    // 如果选择的是当前设备，不做任何操作
                    return;
                }

                this.$emit('device-change', device);
            },

            handleBuySuggestDeviceClick() {
                if (window.electron?.remote?.shell) {
                    window.electron?.remote?.shell.openExternal('https://item.jd.com/10154312755928.html');
                } else {
                    window.open('https://item.jd.com/10154312755928.html', '_blank', 'width=1200,height=680');
                }
            },
        },
    };
</script>

<style lang="scss">
/* 设备选择下拉菜单样式 */
.device-selector-dropdown {
    width: 240px;

    /* 设备分组标题样式 */
    .device-group-header {
        display: flex;
        align-items: center;
        height: 32px;
        padding-left: 8px;
        color: var(--abc-color-T2);
    }
}
</style>
