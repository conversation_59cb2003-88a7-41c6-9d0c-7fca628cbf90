<template>
    <div class="waiting-device-connection">
        <abc-flex
            vertical
            align="center"
            :gap="40"
        >
            <!-- 手机图标 -->
            <div class="open-guide-area">
                <video autoplay loop src="https://cis-static-common.oss-cn-shanghai.aliyuncs.com/media/app-guide-video.mp4"></video>
            </div>

            <!-- 提示文字 -->
            <abc-text size="large" theme="success-light">
                请在手机上打开 ABC App
            </abc-text>
        </abc-flex>
        <abc-button
            class="cancel-btn"
            variant="text"
            theme="default"
            size="large"
            @click="handleCancel"
        >
            取消录音
        </abc-button>
        <abc-button
            class="download-app-btn"
            theme="default"
            variant="text"
            @click="handleDownloadApp"
        >
            下载手机 App
        </abc-button>
    </div>
</template>

<script>
    export default {
        name: 'WaitingDeviceConnection',
        emits: ['cancel', 'download-app'],
        methods: {
            handleCancel() {
                this.$emit('cancel');
            },
            handleDownloadApp() {
                window.open('https://www.abcyun.cn/client-download', '_blank');
            },
        },
    };
</script>

<style lang="scss">
.waiting-device-connection {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-top: 40px;

    .open-guide-area {
        position: relative;
        width: 360px;
        height: 194px;

        video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .cancel-btn {
        margin-top: 16px;
    }

    .download-app-btn {
        position: absolute;
        right: 12px;
        bottom: 12px;
    }
}
</style>
