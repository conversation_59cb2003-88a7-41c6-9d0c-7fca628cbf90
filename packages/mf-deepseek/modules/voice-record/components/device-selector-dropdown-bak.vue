<template>
    <abc-button-group class="device-selector-button-group">
        <!-- 主要的开始录音按钮 -->
        <abc-button
            class="start-recording-button"
            shape="round"
            theme="success"
            variant="fill"
            size="large"
            width="164"
            :icon="currentDeviceIcon"
            :loading="isLoading"
            :disabled="disabled || isLoading"
            @click="handleStartRecording"
        >
            开始录音
        </abc-button>

        <!-- 设备选择下拉菜单 -->
        <abc-dropdown
            custom-class="device-selector-dropdown"
            placement="bottom-start"
            :offset="-164"
            :disabled="disabled || isLoading"
            @change="handleDeviceChange"
        >
            <abc-button
                slot="reference"
                class="device-selector-trigger"
                shape="round"
                theme="success"
                variant="fill"
                size="large"
                icon="s-b-downline-medium"
                width="32"
                min-width="32"
                :disabled="disabled || isLoading"
            ></abc-button>

            <!-- 手机App录音选项 -->
            <div class="device-group-header">
                <abc-space>
                    <abc-text size="small">
                        手机设备
                    </abc-text>
                </abc-space>
            </div>
            <abc-dropdown-item
                v-for="device in mobileDevices"
                :key="device.deviceId"
                :value="device"
                class="device-dropdown-item"
                :class="{ 'is-selected': selectedDeviceId === device.deviceId }"
            >
                <abc-flex align="center" justify="space-between" gap="small">
                    <abc-text size="normal">
                        {{ device.label }} {{ device.isOnline ? '在线' : '离线' }}
                    </abc-text>
                    <abc-icon
                        v-if="selectedDeviceId === device.deviceId"
                        icon="s-check-checkbox-medium"
                        color="var(--abc-color-theme2)"
                        size="14"
                    ></abc-icon>
                    <div v-else style="width: 14px;"></div>
                </abc-flex>
            </abc-dropdown-item>

            <div style="padding: 0 var(--abc-paddingLR-m);">
                <abc-divider margin="small"></abc-divider>
            </div>

            <!-- 电脑麦克风录音选项 -->
            <div class="device-group-header">
                <abc-space>
                    <!--                    <abc-icon icon="s-computer-fill" size="14"></abc-icon>-->
                    <abc-text size="small">
                        电脑设备
                    </abc-text>
                </abc-space>
            </div>
            <abc-dropdown-item
                v-for="device in pcDevices"
                :key="device.deviceId"
                :value="device"
                class="device-dropdown-item"
                :class="{ 'is-selected': selectedDeviceId === device.deviceId }"
            >
                <abc-flex align="center" justify="space-between" gap="small">
                    <abc-text size="normal">
                        {{ device.label }}
                    </abc-text>
                    <abc-icon
                        v-if="selectedDeviceId === device.deviceId"
                        icon="s-check-checkbox-medium"
                        color="var(--abc-color-theme2)"
                        size="14"
                    ></abc-icon>
                    <div v-else style="width: 14px;"></div>
                </abc-flex>
            </abc-dropdown-item>

            <!-- 无设备提示 -->
            <abc-dropdown-item v-if="pcDevices.length === 0 && !isLoading" :disabled="true">
                <abc-space>
                    <div style="width: 14px;"></div>
                    <abc-text size="normal" theme="gray-light">
                        没有检测到麦克风
                    </abc-text>
                </abc-space>
            </abc-dropdown-item>
        </abc-dropdown>
    </abc-button-group>
</template>

<script>
    /**
     * 录音设备选择下拉组件
     * 支持PC录音和手机App录音两种模式的设备选择
     *
     * <AUTHOR> Assistant
     * @date 2025-01-19
     */
    export default {
        name: 'DeviceSelectorDropdown',
        props: {
            // 可用设备列表
            availableDevices: {
                type: Array,
                default: () => [],
                validator: (devices) => {
                    return devices.every((device) =>
                        device.deviceId &&
                        device.label &&
                        device.type &&
                        device.icon,
                    );
                },
            },
            // 当前选中的设备ID
            selectedDeviceId: {
                type: String,
                default: '',
            },
            // 是否正在加载设备
            isLoading: {
                type: Boolean,
                default: false,
            },
            // 是否禁用组件
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            // PC设备列表
            pcDevices() {
                return this.availableDevices.filter((device) => device.type === 'pc');
            },
            // 手机设备列表
            mobileDevices() {
                return this.availableDevices.filter((device) => device.type === 'mobile');
            },
            currentDevice() {
                return this.availableDevices.find((device) => device.deviceId === this.selectedDeviceId);
            },
            // 当前选中设备的图标
            currentDeviceIcon() {
                return this.currentDevice?.type === 'pc' ?
                    's-computer-fill' :
                    's-mobile-small-fill';
            },
        },
        methods: {
            /**
             * 处理开始录音按钮点击
             */
            handleStartRecording() {
                this.$emit('start-recording');
            },
            /**
             * 处理设备切换
             * @param {Object} device 选中的设备对象
             */
            handleDeviceChange(device) {
                if (!device || device.deviceId === this.selectedDeviceId) {
                    // 如果选择的是当前设备，不做任何操作
                    return;
                }

                this.$emit('device-change', device);
            },
        },
    };
</script>

<style lang="scss">
.device-selector-button-group {
    .start-recording-button {
        height: 48px;
        // 保持视觉居中
        padding-left: 32px;
    }

    .device-selector-trigger {
        height: 48px !important;
    }
}

/* 设备选择下拉菜单样式 */
.device-selector-dropdown {
    min-width: 196px;

    /* 设备分组标题样式 */
    .device-group-header {
        display: flex;
        align-items: center;
        height: 32px;
        padding-left: 8px;
        color: var(--abc-color-T2);
    }
}
</style>
