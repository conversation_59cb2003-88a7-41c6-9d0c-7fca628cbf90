// packages/mf-deepseek/modules/voice-record/components/unified-audio-player.js
// 统一音频播放器，支持单段和多段音频的统一播放体验

import AudioPlayer from '@/common/components/audio-player';
import AudioMerger from './audio-merger';

/**
 * 统一音频播放器类
 * 统一处理所有音频（单段和多段），提供一致的播放接口
 */
export default class UnifiedAudioPlayer {
    constructor(audioData) {
        this.audioData = audioData;
        this.audioPlayer = null;
        this.audioMerger = null;
        this.isInitialized = false;
        this.isInitializing = false;

        // 播放状态
        this.isPlaying = false;
        this.isPaused = false;
        this.currentTime = 0;
        this.duration = 0;
        this.playbackRate = 1.0;

        // 事件监听器
        this.eventListeners = {};

        // 合并后的音频信息
        this.mergedAudioInfo = null;

        console.log('统一音频播放器初始化，音频段数量:', this.audioData?.audioSegments?.length || 0);
    }

    /**
     * 初始化播放器
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.isInitialized || this.isInitializing) {
            return;
        }

        this.isInitializing = true;

        try {
            // 统一使用音频合并处理逻辑
            await this.initializeAudioPlayer();

            this.isInitialized = true;
            this.emit('initialized');
            console.log('统一音频播放器初始化完成');

        } catch (error) {
            console.error('统一音频播放器初始化失败:', error);
            this.emit('error', error);
            throw error;
        } finally {
            this.isInitializing = false;
        }
    }

    /**
     * 初始化音频播放器（统一处理单段和多段音频）
     */
    async initializeAudioPlayer() {
        // 检查是否有音频段数据
        if (!this.audioData?.audioSegments || this.audioData.audioSegments.length === 0) {
            throw new Error('没有可用的音频段数据');
        }

        // 创建音频合并器
        this.audioMerger = new AudioMerger();

        // 合并音频段（单段音频也会被处理为单元素数组）
        this.mergedAudioInfo = await this.audioMerger.mergeAudioSegments(
            this.audioData.audioSegments,
        );

        // 创建标准播放器使用合并后的音频
        this.audioPlayer = new AudioPlayer();
        this.audioPlayer.setSrc(this.mergedAudioInfo.blobUrl);
        this.audioPlayer.setPlaybackRate(this.playbackRate);

        // 设置时长
        this.duration = this.mergedAudioInfo.duration;

        // 绑定事件
        this.setupPlayerEvents();

        const segmentCount = this.audioData.audioSegments.length;
        console.log(`音频合并完成，段数: ${segmentCount}, 总时长: ${this.duration}ms`);
    }



    /**
     * 设置播放器事件监听
     */
    setupPlayerEvents() {
        if (!this.audioPlayer) return;

        this.audioPlayer.on('loadedmetadata', () => {
            // 如果没有预设时长，使用音频文件的时长
            if (!this.duration) {
                this.duration = this.audioPlayer.getDuration() * 1000;
            }
            this.emit('loadedmetadata');
        });

        this.audioPlayer.on('play', () => {
            this.isPlaying = true;
            this.isPaused = false;
            this.startTimeUpdate();
            this.emit('play');
        });

        this.audioPlayer.on('pause', () => {
            this.isPlaying = false;
            this.isPaused = true;
            this.emit('pause');
        });

        this.audioPlayer.on('ended', () => {
            this.isPlaying = false;
            this.isPaused = false;
            this.currentTime = this.duration;
            this.emit('ended');
        });

        this.audioPlayer.on('error', (error) => {
            console.error('音频播放错误:', error);
            this.isPlaying = false;
            this.isPaused = true;
            this.emit('error', error);
        });
    }

    /**
     * 开始时间更新循环
     */
    startTimeUpdate() {
        const updateTime = () => {
            if (!this.isPlaying || !this.audioPlayer) {
                return;
            }

            this.currentTime = this.audioPlayer.getCurrentTime() * 1000;
            this.emit('timeupdate', this.currentTime);
            requestAnimationFrame(updateTime);
        };
        updateTime();
    }

    /**
     * 播放音频
     * @returns {Promise<void>}
     */
    async play() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        if (this.audioPlayer) {
            return this.audioPlayer.play();
        }
    }

    /**
     * 暂停播放
     */
    pause() {
        if (this.audioPlayer) {
            this.audioPlayer.pause();
        }
    }

    /**
     * 设置当前播放时间
     * @param {number} time 时间（毫秒）
     */
    setCurrentTime(time) {
        if (!this.audioPlayer || time < 0 || time > this.duration) {
            return;
        }

        const timeInSeconds = time / 1000;
        this.audioPlayer.setCurrentTime(timeInSeconds);
        this.currentTime = time;
    }

    /**
     * 设置播放倍速
     * @param {number} rate 播放倍速
     */
    setPlaybackRate(rate) {
        this.playbackRate = rate;
        if (this.audioPlayer) {
            this.audioPlayer.setPlaybackRate(rate);
        }
    }

    /**
     * 获取当前播放时间（毫秒）
     */
    getCurrentTime() {
        return this.currentTime;
    }

    /**
     * 获取总时长（毫秒）
     */
    getDuration() {
        return this.duration;
    }

    /**
     * 获取合并后的音频缓冲区（用于波形生成）
     * @returns {AudioBuffer|null}
     */
    getAudioBuffer() {
        return this.mergedAudioInfo?.audioBuffer || null;
    }

    /**
     * 获取合并后的音频 Blob URL
     * @returns {string|null}
     */
    getMergedAudioUrl() {
        return this.mergedAudioInfo?.blobUrl || null;
    }

    /**
     * 是否为多段音频（现在所有音频都按多段处理）
     */
    isMultiSegmentAudio() {
        return this.audioData?.audioSegments?.length > 1;
    }

    /**
     * 添加事件监听器
     * @param {string} event 事件名
     * @param {Function} handler 处理函数
     */
    on(event, handler) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(handler);
    }

    /**
     * 移除事件监听器
     * @param {string} event 事件名
     * @param {Function} handler 处理函数
     */
    off(event, handler) {
        if (this.eventListeners[event]) {
            const index = this.eventListeners[event].indexOf(handler);
            if (index > -1) {
                this.eventListeners[event].splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} event 事件名
     * @param {...any} args 参数
     */
    emit(event, ...args) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach((handler) => {
                try {
                    handler(...args);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 销毁播放器
     */
    destroy() {
        // 清理播放器
        if (this.audioPlayer) {
            this.audioPlayer.destroy();
            this.audioPlayer = null;
        }

        // 清理音频合并器
        if (this.audioMerger) {
            this.audioMerger.cleanup();
            this.audioMerger = null;
        }

        // 清理 Blob URL
        if (this.mergedAudioInfo?.blobUrl) {
            URL.revokeObjectURL(this.mergedAudioInfo.blobUrl);
        }

        // 清理事件监听器
        this.eventListeners = {};
        
        // 重置状态
        this.isInitialized = false;
        this.isInitializing = false;
        this.isPlaying = false;
        this.isPaused = false;
        this.mergedAudioInfo = null;
        
        console.log('统一音频播放器已销毁');
    }
}
