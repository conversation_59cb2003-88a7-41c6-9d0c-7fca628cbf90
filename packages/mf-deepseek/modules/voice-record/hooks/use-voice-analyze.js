/* eslint-disable abc/no-timer-id */
import { defineStore } from 'MfBase/pinia';
import voiceAnalyzeService from '../services/voice-analyze-service';

/**
 * Pinia store for voiceRecord state management
 */
export const useVoiceAnalyzeStore = defineStore('voiceRecordAnalyze', {
    state: () => {
        return {
            // 主要状态
            currentAbort: null,

            // 使用对象代替 reactive
            analyzeResult: {
                content: '',
                loading: false,
                canRetry: false,
                error: null,
            },
        };
    },

    actions: {
        setMedicalRecord(medicalRecord) {
            this.analyzeResult.content = medicalRecord;
            this.analyzeResult.loading = false;
            this.analyzeResult.canRetry = false;
            this.analyzeResult.error = null;
        },
        createRequestParams(asrContent, switchSetting, context) {
            let settingData = {};
            if (switchSetting) {
                settingData = {
                    chineseExamination: {
                        value: switchSetting.chineseExamination,
                    },
                    physicalExamination: {
                        value: switchSetting.physicalExamination,
                    },
                    medicalRecordType: {
                        value: switchSetting.type,
                    },
                };
            }
            const contextData = context || {};
            return {
                text: asrContent,
                data: settingData,
                ...contextData,
                promptName: 'asr-generate-medical-record',
            };
        },

        // 设置错误状态
        setError(error, canRetry = true) {
            this.analyzeResult.error = error;
            this.analyzeResult.loading = false;
            this.analyzeResult.canRetry = canRetry;
        },

        initState() {
            // 初始化状态
            this.analyzeResult.content = '';
            this.analyzeResult.loading = true;
            this.analyzeResult.canRetry = false;
            this.analyzeResult.error = null;
        },

        // 处理流式请求的通用函数
        async handleFetchDeepseekStream(params) {
            if (!params.text) {
                console.error('没有可识别内容');
                this.setError('未识别到有效病历内容，请确定收音设备是否正常，重新录音', false);
                return;
            }

            console.log('开始调用流式推理接口', params.text);

            try {
                const {
                    promise, abort,
                } = voiceAnalyzeService.getVoiceAnalyzeStream(params, (data) => {
                    if (!data || data.code !== 200) {
                        console.error('接口错误:', data);
                        return;
                    }

                    // 处理回答内容
                    if (data.data.answer) {
                        this.analyzeResult.content += data.data.answer;
                    }
                }, () => {
                    // 在关闭回调中存入缓存
                    console.log('Stream closed, saving to cache');
                    // 清空当前终止函数
                    this.currentAbort = null;
                }, (error) => {
                    console.error('Stream error:', error);
                    this.setError(error?.message || '请求异常，请稍后重试');
                });

                // 保存当前终止函数
                this.currentAbort = abort;

                // 等待请求完成
                await promise;
            } catch (error) {
                console.error('Stream error:', error);
                this.setError(error?.message || '请求异常，请稍后重试');
            } finally {
                this.analyzeResult.loading = false;
            }
        },

        // 开始语音分析
        async startVoiceAnalyze(asrContent, switchSetting, context) {
            this.initState();
            const params = this.createRequestParams(asrContent, switchSetting, context);
            await this.handleFetchDeepseekStream(params);
        },

        // 重试语音分析
        retryVoiceAnalyze() {
            this.initState();
            this.startVoiceAnalyze();
        },

        // 取消获取语音分析流
        cancelFetchVoiceAnalyzeStream() {
            if (this.currentAbort) {
                console.log('cancel stream');
                this.currentAbort();
                this.currentAbort = null;
            }

            this.initState();
        },

        destroy() {
            this.cancelFetchVoiceAnalyzeStream();
        },
    },
});

