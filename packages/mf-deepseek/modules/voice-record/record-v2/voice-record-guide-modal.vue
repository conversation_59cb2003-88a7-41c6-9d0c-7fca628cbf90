<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        :show-footer="false"
        :show-close="false"
        disabled-keyboard
        content-styles="padding: 0"
        custom-class="voice-record-guide-dialog"
        @close-dialog="handleClose"
    >
        <div slot="top-extend" class="voice-record-guide-close" @click="handleClose">
            <abc-icon
                variant="fill"
                size="18"
                icon="s-close-line-medium"
                color="var(--abc-color-T4)"
                @delete="handleClose"
            ></abc-icon>
        </div>

        <div class="video-wrapper">
            <abc-video
                ref="video"
                poster="https://static-common-cdn.abcyun.cn/media/voice-record-medical-record-guide.png"
                src-url="https://static-common-cdn.abcyun.cn/media/voice-record-medical-record-guide.mp4"
                :autoplay="true"
                :controls="false"
                :loop="true"
                :width="640"
                :height="480"
            ></abc-video>
        </div>
        <div class="voice-record-guide-footer">
            <abc-button
                shape="square"
                variant="ghost"
                theme="primary"
                size="large"
                width="92"
                @click="handleIntroClick"
            >
                视频介绍
            </abc-button>
            <abc-button
                theme="primary"
                size="large"
                width="92"
                @click="handleClose"
            >
                知道了
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { NavigateHelper } from 'MfBase/core';
    import AbcVideo from '@/common/components/abc-video.vue';
    import { mapGetters } from 'vuex';

    export default {
        name: 'VoiceRecordGuideModal',
        components: { AbcVideo },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                showPlayBtn: true,
            };
        },
        computed: {
            ...mapGetters(['isElectron']),
            visible: {
                get() {
                    return this.value;
                },
                set(value) {
                    this.$emit('input', value);
                },
            },
        },
        methods: {
            handleIntroClick() {
                const viewportWidth = document.documentElement.clientWidth;
                const viewportHeight = document.documentElement.clientHeight;

                let width = viewportWidth * 0.6;
                let height = viewportHeight * 0.8;

                width = Math.max(788, Math.min(width, 960));
                height = Math.max(640, Math.min(height, 820));

                // 计算窗口的位置，使其在浏览器窗口中居中
                const left = window.screenX + (viewportWidth - width) / 2;
                const top = window.screenY + (viewportHeight - height) / 2;

                const url = 'https://www.abcyun.cn/cms/view/loginfo/ffffffff00000000350c27a2a172c000?mode=pure';

                if (this.isElectron) {
                    const options = {
                        modal: true, // 模态对话框形态,默认为true
                        width,
                        height,
                        openDevTools: false, // 是否打开调试窗口，默认false
                        nodeIntegration: false, // 纯浏览器环境，不需要注入node
                    };
                    window.openURL(url, '', options);
                } else {
                    NavigateHelper.windowOpen(url, '', `width=${width},height=${height},left=${left},top=${top}`);
                }
            },
            handleClose() {
                this.visible = false;
                this.$emit('close');
            },
        },
    };
</script>

<style lang="scss">
    .voice-record-guide-dialog.abc-dialog {
        position: relative;
        width: 640px;
        height: 480px;
        background: transparent;

        .dialog-content {
            height: 100%;
        }

        .voice-record-guide-close {
            position: absolute;
            top: 6px;
            right: -40px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            cursor: pointer;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 50%;

            &:hover {
                background-color: rgba(0, 0, 0, 0.4);
            }

            &:active {
                background-color: rgba(0, 0, 0, 0.6);
            }
        }

        .video-wrapper {
            position: relative;
            height: 480px;
            overflow: hidden;
            border-radius: var(--abc-border-radius-large, 12px);
        }

        .voice-record-guide-footer {
            position: absolute;
            right: 32px;
            bottom: 32px;
            font-size: 16px;
            font-weight: bold;
        }
    }
</style>
