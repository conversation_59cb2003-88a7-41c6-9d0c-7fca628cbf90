<template>
    <div class="voice-record-panel">
        <div class="voice-record-main">
            <abc-text size="large" bold class="voice-record-title">
                语音病历
            </abc-text>
            <!-- 中间内容 -->
            <template v-if="state === 'initial'">
                <abc-flex
                    vertical
                    align="center"
                    gap="middle"
                >
                    <abc-icon icon="s-track-line" size="28px" color="var(--abc-color-T2)"></abc-icon>
                    <abc-text theme="gray" size="large" class="voice-record-subtitle">
                        医生专注沟通，AI精准记录
                    </abc-text>
                </abc-flex>
            </template>
            <template v-else-if="state === 'waiting-device'">
                <waiting-device-connection
                    @cancel="handleCancelWaiting"
                ></waiting-device-connection>
            </template>
            <template v-else>
                <div class="audio-wave">
                    <abc-text class="record-time" size="normal" theme="gray">
                        {{ formattedTime }}
                    </abc-text>
                    <wave-visualization
                        class="wave-visualization"
                        :is-paused="state === 'paused'"
                        :waveform-data="waveformData"
                        :height="56"
                        :width="468"
                    ></wave-visualization>
                    <div class="subtitle-container">
                        <div class="subtitle-wrapper">
                            <div class="subtitle-content">
                                {{ fullSentence }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <!--  底部操作  -->
            <abc-flex
                v-if="state !== 'waiting-device'"
                vertical
                gap="middle"
                align="center"
            >
                <div style="height: 48px;"></div>

                <!-- 录音设备选择 -->
                <transition name="record-btn-fade">
                    <!-- 主要的开始录音按钮 -->
                    <abc-button
                        v-show="state === 'initial'"
                        class="start-recording-button"
                        shape="round"
                        theme="success"
                        variant="fill"
                        size="large"
                        width="196"
                        :loading="isStarting"
                        @click="handleStartBtnClick"
                    >
                        开始录音
                    </abc-button>
                </transition>
                <!-- 暂停/继续/结束按钮动画 -->
                <transition
                    name="record-btn-enter"
                >
                    <div v-show="state === 'recording' || state === 'paused'" class="record-controls">
                        <abc-button
                            v-show="state === 'recording'"
                            key="pause"
                            class="pause-btn"
                            shape="round"
                            theme="warning"
                            variant="ghost"
                            size="large"
                            width="116"
                            icon="s-pause-fill"
                            @click="pauseRecording"
                        >
                            暂停
                        </abc-button>
                        <abc-button
                            v-show="state === 'paused'"
                            key="continue"
                            class="continue-btn"
                            shape="round"
                            theme="success"
                            variant="ghost"
                            size="large"
                            width="116"
                            icon="s-play-fill"
                            :loading="isStarting"
                            @click="continueRecording"
                        >
                            继续
                        </abc-button>
                        <abc-button
                            v-show="state === 'recording' || state === 'paused'"
                            key="stop"
                            class="stop-btn"
                            shape="round"
                            theme="danger"
                            variant="fill"
                            size="large"
                            width="116"
                            icon="s-stop-fill"
                            :loading="isStopping"
                            @click="stopRecording()"
                        >
                            结束
                        </abc-button>
                    </div>
                </transition>

                <template v-if="state === 'initial'">
                    <device-selector-dropdown
                        v-show="state === 'initial'"
                        :available-devices="availableDevices"
                        :selected-device-id="selectedDeviceId"
                        @device-change="handleDeviceChange"
                    ></device-selector-dropdown>
                    <!--                    <abc-link-->
                    <!--                        v-if="state === 'initial'"-->
                    <!--                        size="small"-->
                    <!--                        theme="default"-->
                    <!--                        class="asr-tips-link"-->
                    <!--                        @click="handleTipsClick"-->
                    <!--                    >-->
                    <!--                        语音病历使用说明-->
                    <!--                    </abc-link>-->
                </template>
                <div v-else style="height: 32px; padding-top: 8px;">
                    <abc-text
                        theme="gray-light"
                        size="mini"
                    >
                        {{ remainTime < 60 ? `录音即将结束 ${formattedRemainTime}` : '普通话效果更佳，最长录音 15 分钟' }}
                    </abc-text>
                </div>
            </abc-flex>
        </div>

        <voice-record-guide-modal
            v-if="showTipsModal"
            v-model="showTipsModal"
        >
        </voice-record-guide-modal>

        <voice-record-error-modal
            v-if="showErrorModal"
            v-model="showErrorModal"
            :error-info="recordingError"
        >
        </voice-record-error-modal>
    </div>
</template>

<script>
    import WaveVisualization from '../components/wave-visualization-v2.vue';
    import DeviceSelectorDropdown from '../components/device-selector-dropdown.vue';
    import WaitingDeviceConnection from '../components/waiting-device-connection.vue';
    import { useVoiceRecordStore } from '../hooks/use-voice-record';
    import { storeToRefs } from 'MfBase/pinia';
    import VoiceRecordGuideModal from './voice-record-guide-modal.vue';
    import VoiceRecordErrorModal from './voice-record-error-modal.vue';

    export default {
        name: 'VoiceRecordPanel',
        components: {
            VoiceRecordErrorModal,
            VoiceRecordGuideModal,
            WaveVisualization,
            DeviceSelectorDropdown,
            WaitingDeviceConnection,
        },
        props: {
            outpatientSheetId: {
                type: String,
                default: '',
            },
            patientInfo: {
                type: Object,
                required: false,
                default: null,
            },
        },
        setup() {
            // 使用 pinia store 获取所有响应式数据和方法
            const store = useVoiceRecordStore();

            const {
                state,
                isStarting,
                isStopping,
                currentSubtitleLines,
                isSubtitleScrolling,
                waveformData,
                formattedTime,
                remainTime,
                formattedRemainTime,
                recordTime,
                fullSentence,
                // 设备相关状态
                recordingDeviceType,
                availableDevices,
                selectedDeviceId,
                isLoadingDevices,
                isMobileConnected,
                recordingError,
                showRecordingError,
            } = storeToRefs(store);

            const {
                startRecording,
                pauseRecording,
                continueRecording,
                stopRecording,
                cleanup,
                // 设备管理方法
                getAvailableDevices,
                switchRecordingDevice,
                // 等待设备连接方法
                cancelWaitingForDevice,
            } = store;

            return {
                state,
                isStarting,
                isStopping,
                currentSubtitleLines,
                isSubtitleScrolling,
                waveformData,
                recordTime,
                formattedTime,
                remainTime,
                formattedRemainTime,
                startRecording,
                pauseRecording,
                continueRecording,
                stopRecording,
                cleanup,
                fullSentence,
                recordingError,
                showRecordingError,
                // 设备相关
                recordingDeviceType,
                availableDevices,
                selectedDeviceId,
                isLoadingDevices,
                isMobileConnected,
                getAvailableDevices,
                switchRecordingDevice,
                cancelWaitingForDevice,
            };
        },
        data() {
            return {
                showTipsModal: false,
            };
        },
        computed: {
            showErrorModal: {
                get() {
                    return this.showRecordingError;
                },
                set(val) {
                    this.showRecordingError = val;
                },
            },
        },
        watch: {
            remainTime(value) {
                if (value <= 0) {
                    console.log('RecordPanel timeout stop recording');
                    this.stopRecording();
                }
            },
        },
        async mounted() {
            // 组件挂载后获取可用设备
            await this.getAvailableDevices();
        },
        beforeDestroy() {
            console.log('record-panel beforeDestroy');
        },
        methods: {
            handleStartBtnClick() {
                this.$abcPlatform.service.report.reportEventSLS('voice_record_start_btn_clk', '点击开始录音');
                this.startRecording();
            },
            handleTipsClick() {
                this.showTipsModal = true;
            },
            /**
             * 处理设备切换
             */
            async handleDeviceChange(device) {
                if (!device || device.deviceId === this.selectedDeviceId) {
                    // 如果选择的是当前设备，不做任何操作
                    return;
                }

                try {
                    // 只切换设备，不自动开始录音
                    await this.switchRecordingDevice(device.deviceId, device.type);
                    // this.$Toast.success(`已切换到: ${device.label}`);
                } catch (error) {
                    console.error('切换录音设备失败:', error);
                    this.$Toast.error('切换录音设备失败');
                }
            },

            /**
             * 处理取消等待设备连接
             */
            handleCancelWaiting() {
                this.cancelWaitingForDevice();
            },
        },
    };
</script>

<style lang="scss">
@import "@/common/style/effect.scss";

.voice-record-panel {
    position: relative;
    height: 100%;

    .voice-record-main {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        padding: 40px 86px 68px 86px;

        .voice-record-title {
            font-size: 20px;
        }

        .audio-wave {
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 24px;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 164px;
            padding-bottom: var(--abc-paddingTB-xl);

            .wave-visualization {
                position: relative;
                width: 100%;
                height: 56px;
            }

            .subtitle-container {
                position: relative;
                width: 100%;
                overflow: hidden;
            }

            .subtitle-wrapper {
                position: relative;
                display: flex;
                justify-content: flex-end;
                width: 100%;
                height: 22px;
                overflow: hidden;
            }

            .wave-visualization::after,
            .subtitle-wrapper::after {
                position: absolute;
                top: 0;
                right: 0;
                z-index: 1;
                width: 40px;
                height: 100%;
                pointer-events: none;
                content: "";
                background: linear-gradient(270deg, #eef6ff 0%, rgba(238, 246, 255, 0) 100%);
            }

            .subtitle-content {
                overflow: visible;
                color: var(--abc-color-T2);
                text-align: right;
                white-space: nowrap;
            }
        }

        .start-recording-button {
            position: absolute;
            height: 48px;
        }

        .abc-link--default.asr-tips-link {
            color: var(--abc-color-T3);

            &:not(.is-disabled):hover {
                color: var(--abc-color-T2);
            }
        }

        .record-controls {
            position: absolute;
            display: flex;
            gap: 16px;
            justify-content: center;
            width: 100%;

            .abc-button {
                height: 48px;
            }

            .abc-button + .abc-button {
                margin-left: 0;
            }
        }
    }
}

:root {
    --record-btn-animation-time: 0.3s;
}

/* 开始按钮收缩消失动画 */
.record-btn-fade-leave-active {
    transition: transform var(--record-btn-animation-time) cubic-bezier(0.4, 0, 0.2, 1), opacity var(--record-btn-animation-time) cubic-bezier(0.4, 0, 0.2, 1);
}

.record-btn-fade-leave-to {
    opacity: 0;
    transform: scale(0);
}

/* 暂停/继续/结束按钮入场动画 */
.record-btn-enter-enter-active,
.record-btn-enter-leave-active {
    transition: all var(--record-btn-animation-time) cubic-bezier(0.4, 0, 0.2, 1);
}

.record-btn-enter-enter {
    opacity: 0;
    transform: scale(0);
}

.record-btn-enter-enter-to {
    opacity: 1;
    transform: scale(1);
}
</style>
