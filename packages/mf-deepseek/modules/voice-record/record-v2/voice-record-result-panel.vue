<template>
    <abc-flex class="voice-record-result-panel-wrapper">
        <!-- 左侧问诊记录 -->
        <div class="conversation-panel">
            <abc-flex vertical style="height: 100%;">
                <abc-flex
                    v-if="isLoadingConversation"
                    align="center"
                    justify="center"
                    style="height: calc(100% - 80px);"
                >
                    <abc-space direction="vertical" size="4">
                        <abc-loading-spinner :loading="isLoadingConversation" size="large" theme="gray"></abc-loading-spinner>
                        <abc-text v-if="!isDetailView" theme="gray">
                            正在保存录音
                        </abc-text>
                    </abc-space>
                </abc-flex>
                <div
                    v-else-if="isSpeakerRecognitionError"
                    style=" width: 100%; height: calc(100% - 80px);"
                >
                    <abc-content-empty></abc-content-empty>
                </div>
                <template v-else>
                    <abc-flex class="panel-header" align="center">
                        <abc-text bold>
                            问诊记录
                        </abc-text>
                    </abc-flex>

                    <abc-list
                        ref="conversationList"
                        class="conversation-list"
                        :data-list="conversationList"
                        :list-item-flex-config="{ align: 'start' }"
                        :custom-padding="[10, 12]"
                        :custom-item-class="(item) => isActiveConversationItem(item) ? 'is-active-conversation' : ''"
                        @click-item="handleConverstaionItemClick"
                    >
                        <template #prepend="{ item }">
                            <!-- 头像 -->
                            <abc-icon
                                icon="s-avatar-fill"
                                size="20"
                                :color="isActiveConversationItem(item) ? 'var(--abc-color-T2)' : 'var(--abc-color-T3)'"
                            ></abc-icon>
                        </template>
                        <template
                            #default="{
                                item, index
                            }"
                        >
                            <div
                                :id="`conversation-item-${ index}`"
                                class="conversation-item-content"
                                :class="{
                                    'doctor-item': item.role === 'doctor',
                                    'patient-item': item.role === 'patient',
                                }"
                            >
                                <div class="speaker-info">
                                    <!-- <abc-text theme="gray" size="normal">
                                        {{ `${item.roleName }` }}
                                    </abc-text> -->
                                    <abc-text :theme="isActiveConversationItem(item) ? 'gray' : 'gray-light'" size="small">
                                        [{{ item.time }}]
                                    </abc-text>
                                </div>
                                <abc-text :theme="isActiveConversationItem(item) ? 'black' : 'gray'" class="conversation-content" size="normal">
                                    {{ item.content }}
                                </abc-text>
                            </div>
                        </template>
                    </abc-list>
                </template>
                <div class="conversation-footer">
                    <div class="voice-record-player">
                        <abc-flex vertical class="player-container">
                            <abc-flex
                                align="center"
                                justify="space-between"
                                class="player-controls"
                                gap="12"
                            >
                                <abc-button
                                    class="play-button"
                                    shape="round"
                                    variant="ghost"
                                    theme="default"
                                    @click="togglePlayback"
                                >
                                    <abc-icon :icon="isPlaying ? 's-pause-fill' : 's-play-fill'" size="16"></abc-icon>
                                </abc-button>
                                <abc-flex gap="middle" align="center" justify="space-between">
                                    <abc-text class="time-display" size="mini" theme="gray">
                                        {{ playbackTimeFormatted }}
                                    </abc-text>
                                    <div
                                        ref="waveContainer"
                                        class="wave-container"
                                        @click="handleProgressClick"
                                        @mousedown="startDrag"
                                        @mousemove="onDrag"
                                        @mouseup="stopDrag"
                                        @mouseleave="stopDrag"
                                    >
                                        <div class="wave-track">
                                            <div class="wave-bars">
                                                <div
                                                    v-for="(bar, index) in waveBars"
                                                    :key="index"
                                                    class="wave-bar"
                                                    :style="{
                                                        height: `${bar}px`,
                                                        background: (progressPercentage / 100) * waveBars.length >= index ? '#1890ff' : '#e6e6e6'
                                                    }"
                                                ></div>
                                            </div>
                                        </div>
                                        <div class="progress-bar-container">
                                            <div class="progress-bar-bg"></div>
                                            <div class="progress-bar" :style="{ width: `${progressPercentage}%` }"></div>
                                        </div>
                                        <div class="progress-handle" :style="{ left: `${progressHandlePosition}%` }"></div>
                                    </div>
                                    <abc-text class="time-display" size="mini" theme="gray">
                                        {{ recordDurationFormated }}
                                    </abc-text>
                                </abc-flex>
                                <abc-button
                                    class="speed-button"
                                    variant="text"
                                    theme="default"
                                    @click="togglePlaybackRate"
                                >
                                    <abc-icon :icon="currentSpeedIcon" size="20"></abc-icon>
                                </abc-button>
                            </abc-flex>
                        </abc-flex>
                    </div>
                </div>
            </abc-flex>
        </div>

        <!-- 右侧病历内容 -->
        <div class="medical-record-panel">
            <abc-flex vertical style="height: 100%;">
                <abc-scrollbar class="medical-record-scroll" padding-size="none">
                    <abc-flex vertical class="medical-record-content">
                        <abc-space class="generate-status">
                            <ai-loading-spinner :loading="analyzeResult.loading"></ai-loading-spinner>
                            <abc-text v-if="analyzeResult.loading" size="normal" theme="gray">
                                生成病历中...
                            </abc-text>
                            <abc-text v-else-if="analyzeResult.error" size="normal" theme="gray">
                                生成失败
                            </abc-text>
                            <abc-text v-else size="normal" theme="gray">
                                已生成病历
                            </abc-text>
                        </abc-space>
                        <!-- 病历头部信息 -->
                        <abc-flex class="medical-record-header" vertical>
                            <abc-text bold style=" font-size: 20px; text-align: center;">
                                门诊病历
                            </abc-text>
                            <abc-flex justify="space-between" style="margin-top: 12px;">
                                <div>
                                    <abc-text theme="gray">
                                        患者：
                                    </abc-text>
                                    <abc-text theme="gray">
                                        {{ patientInfo?.name || '匿名患者' }}{{ patientInfo?.sex ? `&nbsp;&nbsp;${patientInfo?.sex}` : '' }}{{ patientInfo?.age?.year ? `&nbsp;&nbsp;${patientInfo?.age?.year }岁` : '' }}
                                    </abc-text>
                                </div>
                                <abc-text theme="gray">
                                    就诊时间：{{ diagnosisTime }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>

                        <abc-divider variant="dashed"></abc-divider>

                        <!-- 病历内容 -->
                        <markdown-renderer v-if="analyzeResult.content" :content="analyzeResult.content"></markdown-renderer>
                        <!-- 生成错误 -->
                        <ai-error-tips v-if="analyzeResult.error">
                            {{ analyzeResult.error }}
                            <template v-if="analyzeResult.canRetry">
                                ，<abc-link size="small" theme="primary" @click="handleRetryClick">
                                    点击重试
                                </abc-link>
                            </template>
                        </ai-error-tips>
                    </abc-flex>
                </abc-scrollbar>

                <!-- 操作按钮 -->
                <abc-flex class="action-buttons" justify="end">
                    <abc-button
                        shape="round"
                        variant="ghost"
                        size="large"
                        theme="danger"
                        width="96"
                        @click="handleReRecordClick"
                    >
                        重新录制
                    </abc-button>
                    <abc-button
                        v-if="analyzeResult.content && !analyzeResult.error && !analyzeResult.loading"
                        shape="round"
                        size="large"
                        width="96"
                        class="adopt-button"
                        @click="handleAcceptClick"
                    >
                        采纳
                    </abc-button>
                </abc-flex>
            </abc-flex>
        </div>
    </abc-flex>
</template>

<script>
    import UnifiedAudioPlayer from '../components/unified-audio-player';
    import { formatDate } from '@abc/utils-date';
    import AiLoadingSpinner from '@/common/components/ai-loading-spinner.vue';
    import { useVoiceAnalyzeStore } from '../hooks/use-voice-analyze';
    import { storeToRefs } from 'MfBase/pinia';
    import {
        formatTime,
        sleep,
        smoothScroll,
    } from '@/common/utils';
    import MarkdownRenderer from '../components/markdown-renderer.vue';
    import AiErrorTips from '@/common/components/ai-error-tips.vue';
    import { EVENT_MR_BUS_NAME } from '@/common/utils/constant';
    import {
        saveAsrResult,
        getAsrResultDetail,
        getAsrResultDetailByTaskId,
    } from '../services/api';
    import config from '../config.js';

    export default {
        name: 'VoiceRecordResultPanel',
        components: {
            AiLoadingSpinner,
            MarkdownRenderer,
            AiErrorTips,
        },
        props: {
            /**
             * 病历字段开关设置
             */
            switchSetting: {
                type: Object,
            },
            /**
             * 患者信息
             */
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
            /**
             * 门诊信息
             */
            outpatientInfo: {
                type: Object,
                default: () => ({}),
            },
            /**
             * 附件 ID
             */
            asrId: {
                type: [String, Number],
                default: '',
            },
            /**
             * 录音数据
             * @type {{outpatientSheetId: string, taskId: string, logger: Object}}
             */
            recordData: {
                type: Object,
                default: () => null,
            },
        },
        setup() {
            const voiceAnalyzeStore = useVoiceAnalyzeStore();
            const {
                analyzeResult,
            } = storeToRefs(voiceAnalyzeStore);
            const {
                setMedicalRecord,
                startVoiceAnalyze,
                destroy,
            } = voiceAnalyzeStore;
            return {
                analyzeResult,
                setMedicalRecord,
                startVoiceAnalyze,
                destroyVoiceAnalyze: destroy,
            };
        },
        data() {
            return {
                generateError: '',
                isGenerating: false,
                generationError: false,
                isLoadingConversation: false,
                // 播放器相关数据
                audioPlayer: null,
                isPlaying: false,
                currentTime: 0, // 播放器当前时间，单位为毫秒
                waveBars: Array(110).fill(0).map(() => Math.floor(Math.random() * 22)),
                currentBarIndex: 0,
                playbackInterval: null,
                playbackRate: 1.0,
                isDragging: false,
                hasDragged: false, // 标记是否发生了拖动操作
                wasPlayingBeforeDrag: false, // 记录拖动前的播放状态
                recordDuration: 0,
                scrollOffset: 0, // 滚动偏移

                asrResultDetail: null,
                isDestroy: false,
            };
        },
        computed: {
            /**
             * 是否是查看详情模式
             */
            isDetailView() {
                return !!this.asrId;
            },
            currentActiveIndex() {
                // 找到当前播放项的 index
                return this.conversationList.findIndex((item) => this.isActiveConversationItem(item));
            },
            progressPercentage() {
                return this.recordDuration > 0 ? (this.currentTime / this.recordDuration) * 100 : 0;
            },
            progressHandlePosition() {
                return this.recordDuration > 0 ? (this.currentTime / this.recordDuration) * 93.8 : 0;
            },
            playbackTimeFormatted() {
                return this.formatTime(this.currentTime);
            },
            recordDurationFormated() {
                return this.formatTime(this.recordDuration);
            },
            outpatientSheetId() {
                return this.outpatientInfo?.id;
            },
            diagnosisTime() {
                return formatDate(this.outpatientInfo?.created, 'YYYY-MM-DD HH:mm:ss');
            },
            asrContent() {
                if (this.asrResultDetail?.asrMetadataList?.length > 0) {
                    return this.asrResultDetail?.asrMetadataList?.map((item) => `[${formatTime(item.startTime)}]: ${item.text}`).filter(Boolean).join('\n');
                }
                return '';
            },
            isSpeakerRecognitionError() {
                return this.asrResultDetail?.asrMetadataList?.length === 0;
            },
            conversationList() {
                const roleChNum = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
                const metadataList = this.asrResultDetail?.asrMetadataList || [];

                // 先获取原始数据
                const rawList = metadataList.map((item) => {
                    const speaker = item.additions?.speaker;
                    const role = speaker === '1' ? 'doctor' : 'patient';
                    const roleName = speaker && roleChNum[speaker - 1] ? `说话人${roleChNum[speaker - 1]}` : '匿名';
                    return {
                        role,
                        roleName,
                        content: item.text,
                        time: formatTime(item.startTime),
                        startTime: +item.startTime,
                        endTime: +item.endTime,
                        // 添加原始结束时间，用于后续处理
                        originalEndTime: +item.endTime,
                    };
                });

                // 处理时间间隔，确保前一个元素的结束时间等于下一个元素的开始时间
                return rawList.map((item, index) => {
                    // 如果不是最后一个元素，则结束时间等于下一个元素的开始时间
                    if (index < rawList.length - 1) {
                        item.endTime = rawList[index + 1].startTime - 1;
                    } else if (this.recordDuration && item.endTime < this.recordDuration) {
                        // 如果是最后一个元素且结束时间小于录音总时长，则延伸到录音结束
                        item.endTime = this.recordDuration;
                    }
                    return item;
                });
            },
            currentSpeedIcon() {
                switch (this.playbackRate) {
                    case 1.0:
                        return 's-a-1x-fill';
                    case 1.5:
                        return 's-a-15x-fill';
                    case 2.0:
                        return 's-a-2x-fill';
                    default:
                        return 's-a-1x-fill';
                }
            },
        },
        watch: {
            currentActiveIndex(newIdx) {
                this.scrollToActiveConversationItem(newIdx);
            },
        },
        async created() {
            // 获取 asr 详情
            await this.fetchAsrResultDetail();

            // 异步初始化音频播放器
            await this.initAudioPlayer();

            // 如果不是详情模式，需要生成病历并保存
            if (!this.isDetailView) {
                try {
                    await this.generateMedicalRecord(this.asrContent, this.switchSetting);

                    this.logger.report('生成病历结果', {
                        duration: this.recordDuration,
                        analyzeResult: this.analyzeResult,
                    });

                    // 存储到后台便于后续分析
                    if (this.outpatientSheetId && this.analyzeResult.content) {
                        const saveAsrResultRsp = await saveAsrResult(this.asrResultDetail.asrId, {
                            medicalRecord: this.analyzeResult.content,
                        });

                        if (!saveAsrResultRsp?.data?.id) {
                            this.$Toast({
                                type: 'error',
                                message: '存储 ASR 结果失败',
                            });
                        }
                    }
                } catch (error) {
                    this.logger.report('生成病历异常', {
                        error,
                    });
                    this.$Toast({
                        type: 'error',
                        message: '处理录音数据失败',
                    });
                }
            }
        },
        beforeDestroy() {
            this.isDestroy = true;
            this.logger = null;
            if (this.audioPlayer) {
                this.stopPlayback();
                this.audioPlayer.destroy();
                this.audioPlayer = null;
            }
            this.destroyVoiceAnalyze();
        },
        methods: {
            formatTime,
            formatDate,
            async fetchAsrResultDetail() {
                this.isLoadingConversation = true;
                try {
                    if (this.isDetailView) {
                        // 传了 asrId，使用详情接口进行获取
                        const asrResultDetail = await getAsrResultDetail(this.asrId);
                        if (asrResultDetail) {
                            // 设置录音时长
                            if (asrResultDetail.duration) {
                                this.recordDuration = asrResultDetail.duration;
                            }

                            this.asrResultDetail = asrResultDetail;

                            // 设置病历内容
                            this.setMedicalRecord(asrResultDetail.medicalRecord);
                        } else {
                            console.error('获取ASR详情失败');
                            this.$Toast({
                                type: 'error',
                                message: '获取录音详情失败',
                            });
                        }
                    } else {
                        if (config.debug) {
                            await sleep(3000).promise;
                            this.isLoadingConversation = false;
                            return;
                        }
                        this.logger = this.recordData.logger;
                        this.logger?.report('fetchAsrResultDetailByTaskId', {
                            outpatientSheetId: this.recordData.outpatientSheetId,
                            taskId: this.recordData.taskId,
                        });

                        const asrResultDetail = await getAsrResultDetailByTaskId(this.recordData.outpatientSheetId, this.recordData.taskId);
                        if (asrResultDetail) {
                            if (asrResultDetail.duration) {
                                this.recordDuration = asrResultDetail.duration;
                            }

                            this.asrResultDetail = asrResultDetail;
                        } else {
                            this.logger?.report('根据录音任务ID获取录音详情失败');
                        }
                    }
                } catch (error) {
                    console.error('获取ASR详情异常:', error);
                    this.$Toast({
                        type: 'error',
                        message: '获取录音详情异常',
                    });
                } finally {
                    this.isLoadingConversation = false;
                }

            },
            async initAudioPlayer() {
                try {
                    // 使用统一音频播放器，自动处理单段和多段音频
                    this.audioPlayer = new UnifiedAudioPlayer(this.asrResultDetail);
                    this.audioPlayer.setPlaybackRate(this.playbackRate);

                    // 设置事件监听器
                    this.audioPlayer.on('initialized', () => {
                        console.log('音频播放器初始化完成');
                        // 生成波形数据
                        this.generateWaveformData();
                    });

                    this.audioPlayer.on('loadedmetadata', () => {
                        // 如果没有从后端获取到时长，则使用播放器计算的总时长
                        if (!this.recordDuration) {
                            this.recordDuration = this.audioPlayer.getDuration();
                        }
                        console.log('音频元数据加载完成，时长:', this.recordDuration, 'ms');
                    });

                    this.audioPlayer.on('play', () => {
                        this.isPlaying = true;
                        console.log('播放开始');
                    });

                    this.audioPlayer.on('pause', () => {
                        this.isPlaying = false;
                        console.log('播放暂停');
                    });

                    this.audioPlayer.on('timeupdate', (currentTime) => {
                        this.currentTime = currentTime;
                    });

                    this.audioPlayer.on('ended', () => {
                        this.isPlaying = false;
                        this.currentTime = this.recordDuration;
                        console.log('播放结束');
                    });

                    this.audioPlayer.on('error', (error) => {
                        console.error('音频播放器错误:', error);
                        this.$Toast({
                            type: 'error',
                            message: '音频播放器初始化失败',
                        });
                    });

                    // 异步初始化播放器
                    await this.audioPlayer.initialize();

                } catch (error) {
                    console.error('初始化音频播放器失败:', error);
                    this.$Toast({
                        type: 'error',
                        message: '音频播放器初始化失败',
                    });
                }
            },
            // 生成真实的波形数据
            async generateWaveformData() {
                const barCount = 110;
                const barMaxHeight = 22;
                const barMinHeight = 0;

                // 生成随机波形的辅助函数
                const generateRandomBars = () => {
                    const bars = [];
                    for (let i = 0; i < barCount; i++) {
                        const height = Math.floor(Math.random() * barMaxHeight) + barMinHeight;
                        bars.push(height);
                    }
                    return bars;
                };

                if (!this.audioPlayer) {
                    this.waveBars = generateRandomBars();
                    return;
                }

                try {
                    // 优先使用合并后的音频缓冲区生成波形
                    const audioBuffer = this.audioPlayer.getAudioBuffer();
                    if (audioBuffer) {
                        console.log('使用合并后的音频缓冲区生成波形数据');
                        this.generateWaveformFromAudioBuffer(audioBuffer, barCount, barMaxHeight, barMinHeight);
                        return;
                    }

                    // 如果没有音频缓冲区，使用音频 URL
                    const audioUrl = this.audioPlayer.getMergedAudioUrl();
                    if (!audioUrl) {
                        console.warn('没有可用的音频数据，使用随机波形');
                        this.waveBars = generateRandomBars();
                        return;
                    }

                    console.log('使用音频 URL 生成波形数据:', audioUrl);
                    await this.generateWaveformFromUrl(audioUrl, barCount, barMaxHeight, barMinHeight);

                } catch (error) {
                    console.error('生成波形数据失败:', error);
                    this.waveBars = generateRandomBars();
                }
            },

            /**
             * 从音频缓冲区生成波形数据
             */
            generateWaveformFromAudioBuffer(audioBuffer, barCount, barMaxHeight, barMinHeight) {
                const bars = [];
                const channelData = audioBuffer.getChannelData(0);
                const blockSize = Math.floor(channelData.length / barCount);

                for (let i = 0; i < barCount; i++) {
                    let sum = 0;
                    for (let j = 0; j < blockSize; j++) {
                        const sampleIndex = (i * blockSize) + j;
                        if (sampleIndex < channelData.length) {
                            sum += Math.abs(channelData[sampleIndex]);
                        }
                    }
                    // 计算平均振幅并映射到合适的高度范围
                    const average = sum / blockSize;
                    const height = Math.max(barMinHeight, Math.min(barMaxHeight, Math.floor(average * 150)));
                    bars.push(height);
                }

                this.waveBars = bars;
                console.log('从音频缓冲区生成波形数据完成');
            },

            /**
             * 从音频 URL 生成波形数据
             */
            async generateWaveformFromUrl(audioUrl, barCount, barMaxHeight, barMinHeight) {

                const generateRandomBars = () => {
                    const bars = [];
                    for (let i = 0; i < barCount; i++) {
                        const height = Math.floor(Math.random() * barMaxHeight) + barMinHeight;
                        bars.push(height);
                    }
                    return bars;
                };

                try {
                    const response = await fetch(audioUrl);
                    const arrayBuffer = await response.arrayBuffer();
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                    const audioBuffer = await new Promise((resolve, reject) => {
                        audioContext.decodeAudioData(arrayBuffer, resolve, reject);
                    });

                    // 直接使用音频缓冲区生成波形
                    this.generateWaveformFromAudioBuffer(audioBuffer, barCount, barMaxHeight, barMinHeight);

                } catch (error) {
                    console.error('从 URL 生成波形数据失败:', error);
                    this.waveBars = generateRandomBars();
                }
            },
            scrollToActiveConversationItem(index) {
                // 让当前项距离容器顶部 150px，使用自定义动画在300ms内完成滚动
                this.$nextTick(() => {
                    const wrapper = this.$el.querySelector('.conversation-list');
                    const activeEl = this.$el.querySelector(`#conversation-item-${index}`);

                    if (wrapper && activeEl) {
                        const wrapperRect = wrapper.getBoundingClientRect();
                        const activeRect = activeEl.getBoundingClientRect();
                        // 当前 active 距离 wrapper 顶部的距离
                        const offset = activeRect.top - wrapperRect.top;
                        // 目标是让 active 距离 wrapper 、顶部 150px
                        // 计算目标位置并限制边界
                        const rawTarget = wrapper.scrollTop + offset - 150;
                        const targetScroll = Math.max(
                            0,
                            Math.min(
                                wrapper.scrollHeight - wrapper.clientHeight,
                                rawTarget,
                            ),
                        );

                        // 调用通用滚动函数
                        smoothScroll(wrapper, targetScroll, 300, 'easeInOutQuad');
                    }
                });
            },
            handleConverstaionItemClick(e, item) {
                // 点击某项，跳转到该项对应的时间
                if (item && this.audioPlayer) {
                    console.log('点击对话项，跳转到时间:', item.startTime, 'ms');
                    this.currentTime = item.startTime;
                    // 统一播放器统一使用毫秒时间
                    this.audioPlayer.setCurrentTime(item.startTime);
                }
            },
            isActiveConversationItem(item) {
                return item.startTime <= this.currentTime && item.endTime > this.currentTime;
            },
            togglePlayback() {
                if (!this.audioPlayer) return;
                if (this.isPlaying) {
                    this.pauseAudio();
                } else {
                    this.playAudio();
                }
            },
            playAudio() {
                if (!this.audioPlayer) return;
                this.audioPlayer.play();
                this.isPlaying = true;
            },
            pauseAudio() {
                if (!this.audioPlayer) return;
                this.audioPlayer.pause();
                this.isPlaying = false;
            },
            setPlaybackRate(rate) {
                this.playbackRate = rate;
                if (this.audioPlayer) {
                    this.audioPlayer.setPlaybackRate(rate);
                }
            },
            stopPlayback() {
                this.isPlaying = false;
                this.currentTime = 0;
                this.currentBarIndex = 0;

                this.timeUpdateCallback = null;
                this.endedCallback = null;
            },

            togglePlaybackRate() {
                // 1x 1.5x 2.0x 循环
                const rates = [1.0, 1.5, 2.0];
                const index = rates.indexOf(this.playbackRate);
                this.setPlaybackRate(rates[(index + 1) % rates.length]);
            },

            // 拖动进度相关方法
            startDrag(event) {
                if (!this.audioPlayer) return;
                this.isDragging = true;
                this.hasDragged = false; // 重置拖动标记

                // 记录开始拖动时的播放状态，但不立即暂停
                // 等到确认真正发生拖动时再暂停播放
                this._shouldResumeAfterDrag = false;
                this.wasPlayingBeforeDrag = this.isPlaying;

                this.updateProgressFromEvent(event);
            },

            onDrag(event) {
                if (!this.isDragging || !this.audioPlayer) return;

                // 第一次拖动时才暂停播放
                if (!this.hasDragged && this.wasPlayingBeforeDrag) {
                    console.log('检测到拖动操作，暂停播放');
                    this.pauseAudio();
                    this._shouldResumeAfterDrag = true;
                }

                this.hasDragged = true; // 标记发生了拖动
                this.updateProgressFromEvent(event);
            },
            stopDrag() {
                if (!this.audioPlayer) return;

                // 只有在真正发生拖动时才执行跳转逻辑
                if (this.hasDragged && this.recordDuration > 0) {
                    // 统一播放器统一使用毫秒时间
                    const newTime = (this.progressPercentage / 100) * this.recordDuration;
                    console.log('拖动进度条到:', this.progressPercentage, '%, 对应时间:', newTime, 'ms');
                    this.audioPlayer.setCurrentTime(newTime);
                }

                // 拖动结束后，若之前在播放则恢复播放，否则停在当前位置
                if (this._shouldResumeAfterDrag && this.hasDragged) {
                    this.playAudio();
                }

                // 重置状态
                this.isDragging = false;
                this.hasDragged = false;
                this._shouldResumeAfterDrag = false;
                this.wasPlayingBeforeDrag = false;
            },
            updateProgressFromEvent(event) {
                if (!this.$refs.waveContainer) return;

                const { waveContainer } = this.$refs;
                const rect = waveContainer.getBoundingClientRect();
                const offsetX = event.clientX - rect.left;
                const containerWidth = rect.width;

                // 计算百分比，并限制在0-100之间
                let percentage = (offsetX / containerWidth) * 100;
                percentage = Math.max(0, Math.min(100, percentage));

                // 更新当前时间显示
                if (this.recordDuration > 0) {
                    this.currentTime = (percentage / 100) * this.recordDuration;
                }
            },

            /**
             * 处理进度条点击事件
             * 实现点击跳转功能
             */
            handleProgressClick(event) {
                // 如果刚刚发生了拖动操作，则忽略点击事件
                if (this.hasDragged) {
                    console.log('忽略拖动后的点击事件');
                    return;
                }

                if (!this.audioPlayer || this.recordDuration <= 0) {
                    return;
                }

                console.log('点击进度条跳转');

                // 计算点击位置对应的时间
                const { waveContainer } = this.$refs;
                const rect = waveContainer.getBoundingClientRect();
                const offsetX = event.clientX - rect.left;
                const containerWidth = rect.width;

                // 计算百分比，并限制在0-100之间
                let percentage = (offsetX / containerWidth) * 100;
                percentage = Math.max(0, Math.min(100, percentage));

                // 计算目标时间（毫秒）
                const targetTime = (percentage / 100) * this.recordDuration;

                console.log('点击进度条到:', percentage.toFixed(2), '%, 对应时间:', targetTime.toFixed(0), 'ms');

                // 使用拖动前记录的播放状态，如果没有拖动则使用当前状态
                const wasPlaying = this.wasPlayingBeforeDrag || this.isPlaying;
                console.log('点击前播放状态:', wasPlaying, '(拖动前状态:', this.wasPlayingBeforeDrag, ', 当前状态:', this.isPlaying, ')');

                // 跳转到目标时间
                this.audioPlayer.setCurrentTime(targetTime);
                this.currentTime = targetTime;

                // 如果之前在播放，跳转后继续播放
                if (wasPlaying) {
                    console.log('点击跳转后恢复播放');
                    // 使用 setTimeout 确保跳转完成后再恢复播放
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        if (!this.isPlaying) {
                            this.playAudio();
                        }
                    }, 100);
                }

                // 清理拖动前状态记录
                this.wasPlayingBeforeDrag = false;
            },

            // 生成病历
            async generateMedicalRecord(asrContent, switchSetting) {
                this.isGenerating = true;
                try {
                    const context = {
                        businessId: this.outpatientSheetId,
                        patientId: this.patientInfo?.id,
                        patientOrderId: this.outpatientInfo?.patientOrderId,
                    };
                    await this.startVoiceAnalyze(asrContent, switchSetting, context);
                } catch (error) {
                    console.error('生成病历错误:', error);
                    this.logger.report('生成病历错误', {
                        err: error,
                    });
                } finally {
                    this.isGenerating = false;
                }
            },
            // 解析病历内容，根据 AllMRKey 中定义的字段提取对应内容
            parseMedicalRecord(medicalRecord, mrKeys) {
                const result = {};
                if (!medicalRecord) {
                    return result;
                }

                const trimedMedicalRecord = medicalRecord.replaceAll('**', '');

                // 遍历所有病历字段
                mrKeys.forEach((item) => {
                    const {
                        key, matchLabel,
                    } = item;
                    const regex = new RegExp(`${matchLabel}[：:](.*?)(?=\\n\\n|\\n[^\\n]|$)`, 's');
                    const match = trimedMedicalRecord?.match(regex);

                    if (match && match[1]?.trim()) {
                        result[key] = match[1].trim();
                    }
                });

                return result;
            },

            handleAcceptClick() {
                this.$abcPlatform.service.report.reportEventSLS('voice_record_accept', '采纳语音病历');
                const AllMRKey = [
                    {
                        key: 'chiefComplaint', label: '主诉', matchLabel: '主诉',
                    },
                    {
                        key: 'presentHistory', label: '现病史', matchLabel: '现病史',
                    },
                    {
                        key: 'pastHistory', label: '既往史', matchLabel: '既往史',
                    },
                    {
                        key: 'familyHistory', label: '家族史', matchLabel: '家族史',
                    },
                    {
                        key: 'allergicHistory', label: '过敏史', matchLabel: '过敏史',
                    },
                    {
                        key: 'personalHistory', label: '个人史', matchLabel: '个人史',
                    },
                    {
                        key: 'obstetricalHistory', label: '月经婚育史', matchLabel: '月经婚育史',
                    },
                    {
                        key: 'physicalExamination', label: '体格检查', matchLabel: '体格检查',
                    },
                    {
                        key: 'chineseExamination', label: '望闻切诊', matchLabel: '望闻切诊',
                    },
                    {
                        key: 'oralExamination', label: '口腔检查', matchLabel: '口腔检查',
                    },
                    {
                        key: 'auxiliaryExaminations', label: '辅助检查', matchLabel: '辅助检查',
                    },
                    {
                        key: 'diagnosis', label: '诊断', matchLabel: '诊断',
                    },
                    {
                        key: 'syndrome', label: '辨证', matchLabel: '辨证',
                    },
                    {
                        key: 'therapy', label: '治法', matchLabel: '治法',
                    },
                    {
                        key: 'disposals', label: '处置', matchLabel: '处置-口腔',
                    },
                ];

                try {
                    // 解析病历内容
                    const medicalRecordData = this.parseMedicalRecord(this.analyzeResult.content, AllMRKey);
                    Object.assign(medicalRecordData, {
                        outpatientSheetId: this.outpatientSheetId,
                    });
                    console.log('解析后的病历数据:', { ...medicalRecordData });

                    // 发送解析后的病历数据
                    this.$abcEventBus.$emit(EVENT_MR_BUS_NAME, {
                        type: 'medicalRecord',
                        value: medicalRecordData,
                    });
                    // 关闭对话框
                    this.$emit('finish');
                } catch (error) {
                    console.error('采纳病历失败:', error);
                }
            },

            async handleRetryClick() {
                await this.generateMedicalRecord(this.asrContent, this.switchSetting);

                if (!this.analyzeResult.content) {
                    this.logger?.report('生成病历无内容，中断保存');
                    return;
                }

                await saveAsrResult(this.asrResultDetail.asrId, {
                    medicalRecord: this.analyzeResult.content,
                });
            },
            handleReRecordClick() {
                this.$abcPlatform.service.report.reportEventSLS('voice_record_re_record_btn_clk', '点击重新录音');
                this.$emit('re-record');
            },
        },
    };
</script>

<style lang="scss">
.voice-record-result-panel-wrapper {
    display: flex;
    width: 100%;
    height: 100%;

    // 左侧问诊记录面板
    .conversation-panel {
        flex: 1;
        max-width: 440px;
        height: 100%;
        background-color: #f9fafc;
        border-right: 1px solid #eaedf1;

        .panel-header {
            height: 56px;
            padding: 0 24px;
        }

        .conversation-list {
            height: calc(100% - 56px - 80px) !important;
            padding-right: 2px;
            padding-bottom: 150px;
            padding-left: 12px;
            transform: translateZ(0);
            will-change: scroll-position;

            .is-active-conversation {
                background-color: var(--abc-color-cp-grey4);
            }

            .conversation-item-content {
                flex: 1;

                .speaker-info {
                    display: inline-flex;
                    gap: 8px;
                    justify-content: space-between;
                }

                .conversation-content {
                    display: block;
                    word-break: break-all;
                }
            }
        }

        .conversation-footer {
            position: relative;
            display: flex;
            align-items: center;
            height: 80px;
            margin: 0 24px;
            border-top: 1px solid #eaedf1;

            &::before {
                position: absolute;
                bottom: calc(100% + 1px);
                left: -12px;
                width: 415px;
                height: 40px;
                pointer-events: none;
                // 做个遮罩
                content: '';
                background: linear-gradient(180deg, rgba(249, 250, 252, 0) 0%, rgba(249, 250, 252, 0.8) 100%);
            }

            .voice-record-player {
                width: 100%;

                .player-container {
                    width: 100%;
                }

                .player-controls {
                    width: 100%;
                    height: 32px;
                }

                .wave-container {
                    position: relative;
                    display: flex;
                    align-items: center;
                    width: 226px;
                    height: 32px;

                    .wave-track {
                        position: absolute;
                        bottom: 3px;
                        display: flex;
                        align-items: center;
                        width: 100%;
                        padding: 0;
                        transform: translateY(-50%);

                        .wave-bars {
                            position: relative;
                            bottom: 4px;
                            left: 3px;
                            z-index: 0;
                            display: flex;
                            gap: 1px;
                            align-items: flex-end;
                            justify-content: center;
                            width: 220px;
                            height: 22px;

                            .wave-bar {
                                flex: 1;
                                width: 1px;
                                min-width: 1px;
                                max-width: 1px;
                                background-color: var(--abc-color-P10, #e0e2eb);
                                border-radius: 50px;
                                transition: background-color 0.2s;
                            }
                        }
                    }

                    .progress-bar-container {
                        position: absolute;
                        z-index: 1;
                        display: flex;
                        align-items: center;
                        width: 100%;
                        height: 4px;
                        overflow: hidden;
                        cursor: pointer;
                        border-radius: 2px;
                    }

                    .progress-bar-bg {
                        position: absolute;
                        width: 100%;
                        height: 4px;
                        background: var(--abc-color-P1, #e0e2eb);
                        border-radius: 2px;
                    }

                    .progress-bar {
                        position: absolute;
                        height: 4px;
                        background: var(--abc-color-B2);
                        border-radius: 2px;
                    }

                    .progress-handle {
                        position: absolute;
                        z-index: 3;
                        width: 14px;
                        height: 14px;
                        cursor: pointer;
                        background: var(--abc-color-B2);
                        border: 2px solid #ffffff;
                        border-radius: 50%;
                        box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);

                        &:hover {
                            background: var(--abc-color-B3);
                        }

                        &:active {
                            background: var(--abc-color-theme1);
                        }
                    }
                }
            }
        }
    }

    // 右侧病历内容面板
    .medical-record-panel {
        flex: 1;
        min-width: 240px;
        height: 100%;
        background-color: #ffffff;

        .panel-header {
            height: 56px;
            padding: 0 16px;
            border-bottom: 1px solid #eaedf1;
        }

        .medical-record-scroll {
            height: calc(100% - 80px); // 减去头部和底部按钮区域高度
            padding: 16px 90px 16px 100px;

            .generate-status {
                width: fit-content;
                padding: 6px 12px;
                background: var(--abc-color-cp-grey4);
                border-radius: var(--abc-border-radius-huge, 50px);
            }

            .medical-record-header {
                margin-top: 16px;
            }
        }

        .generating-status {
            height: 200px;
        }

        .error-status {
            height: 200px;
        }

        .medical-record-sections {
            gap: 16px;
        }

        .medical-record-section {
            margin-bottom: 16px;

            .abc-text {
                line-height: 1.6;
            }
        }

        .action-buttons {
            align-items: center;
            height: 80px;
            margin: 0 24px;
            border-top: 1px solid #eaedf1;

            .adopt-button {
                min-width: 96px;
                background: linear-gradient(135deg, #5acafd 0%, #5694fe 51.15%, #9055fe 100%);
                border: none;

                &:hover {
                    opacity: 0.9;
                }
            }
        }
    }
}
</style>
