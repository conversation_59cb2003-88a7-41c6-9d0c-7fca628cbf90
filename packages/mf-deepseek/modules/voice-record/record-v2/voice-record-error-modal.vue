<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        dialog-type="tiny"
        :show-footer="false"
        :on-close="handleClose"
        :show-close="false"
        disabled-keyboard
        content-styles="padding: 0;"
    >
        <abc-flex style="width: 460px; height: 316px;" align="center" justify="center">
            <abc-flex vertical gap="16" align="center">
                <abc-icon :icon="parsedError.icon" size="48" color="var(--abc-color-Y2)"></abc-icon>
                <div>
                    <h4 class="build-in-h4" style="margin-bottom: 8px; text-align: center;">
                        {{ parsedError.title }}
                    </h4>
                    <abc-text size="normal" theme="gray">
                        {{ parsedError.tips }}
                    </abc-text>
                </div>
            </abc-flex>
            <abc-button
                variant="text"
                theme="default"
                style="position: absolute; right: 12px; bottom: 12px;"
                @click="handleClose"
            >
                结束录音
            </abc-button>
        </abc-flex>
    </abc-modal>
</template>

<script>
    export default {
        name: 'VoiceRecordErrorModal',
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            errorInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            visible: {
                get() {
                    return this.value;
                },
                set(value) {
                    this.$emit('input', value);
                },
            },
            parsedError() {
                const errorInfo = {
                    icon: 's-disconnected-fill',
                    title: '手机 App 断连',
                    tips: '请重新打开再点击继续录音',
                };
                const {
                    code,
                } = this.errorInfo || {};

                // 通话占用
                switch (code) {
                    case 4010:
                        errorInfo.icon = 's-occupied-fill';
                        errorInfo.title = '手机被其他通话应用占用';
                        errorInfo.tips = '请等待结束后点击继续录音';
                        break;
                    case 301001:
                        errorInfo.icon = 's-occupied-fill';
                        errorInfo.title = '启用语音识别失败';
                        errorInfo.tips = '正在进行语音识别，不可重复操作';
                        break;
                    default:
                        break;
                }
                return errorInfo;
            },
        },
        methods: {
            handleClose() {
                this.visible = false;
                this.$emit('close');
            },
        },
    };
</script>
