// api/deepseek-api.js
import { fetch } from 'MfBase/base-api';
import { SOCKET_BUSINESS_TYPE } from '../asr/common/constants';

const SPEAKER_BUSINESS_TYPE = 0;

export const fetchIsReadTips = async () => {
    try {
        const res = await fetch.get('/api/v3/clinics/reminder/asr-medical-notice/read');
        return !!res.data?.data?.created;
    } catch (error) {
        console.error('Fetch is read tips error:', error);
        return false;
    }
};

export const markReadTips = async () => {
    try {
        await fetch.post('/api/v3/clinics/reminder/asr-medical-notice/read');
        return true;
    } catch (error) {
        console.error('Mark read tips error:', error);
        return false;
    }
};

/**
 * @typedef AsrResult
 * @property {string} medicalRecord
 */
/**
 *
 * @param {string} outpatientSheetId
 * @param {AsrResult} data
 * @returns
 */
export const saveAsrResult = async (outpatientSheetId, data) => {
    // const payload = {
    //     'businessType': ASR_BUSINESS_TYPE,
    //     'businessInfo': {
    //         // 'voiceUrl': data.uploadAudioUrl,
    //         // 'duration': data.duration,
    //         // 'asrMetadataList': data.asrResult.map((item) => ({
    //         //     'startTime': item.startTime,
    //         //     'endTime': item.endTime,
    //         //     'text': item.text,
    //         // })),
    //         'medicalRecord': data.medicalRecord,
    //     },
    // };
    // if (asrId) {
    //     payload.id = asrId;
    // }
    const payload = {
        aiResult: data.medicalRecord,
    };
    try {
        // const res = await fetch.put(`/api/v2/outpatients/${outpatientSheetId}/medical-record/attachments`, payload);
        const res = await fetch.put(`/api/asr/result/ai-result/${outpatientSheetId}`, payload);
        return res.data;
    } catch (error) {
        console.error('Save asr result error:', error);
        return null;
    }
};

//
function resolveAsrResultDetail(res) {
    if (res?.id) {
        const totalDuration = parseInt(res.duration) || 0;
        let audioSegments = [];
        let voiceUrl = null;

        if (res.voiceUrls && Array.isArray(res.voiceUrls) && res.voiceUrls.length > 0) {
            // 新协议：voiceUrls 数组，统一转换为 audioSegments
            voiceUrl = res.voiceUrls[0]; // 兼容性：使用第一个音频链接

            if (res.voiceUrls.length === 1) {
                // 单个音频文件，也转换为单元素数组
                audioSegments = [{
                    id: `${res.id}_segment_0`,
                    voiceUrl: res.voiceUrls[0],
                    duration: totalDuration,
                    segmentIndex: 0,
                }];
            } else {
                // 多个音频文件，平均分配时长
                const avgSegmentDuration = Math.floor(totalDuration / res.voiceUrls.length);
                audioSegments = res.voiceUrls.map((url, index) => {
                    const isLastSegment = index === res.voiceUrls.length - 1;
                    const segmentDuration = isLastSegment ?
                        totalDuration - (avgSegmentDuration * index) :
                        avgSegmentDuration;

                    return {
                        id: `${res.id}_segment_${index}`,
                        voiceUrl: url,
                        duration: segmentDuration,
                        segmentIndex: index,
                    };
                });
            }
        } else if (res.voiceUrl) {
            // 旧协议：单个 voiceUrl 字段，也转换为单元素数组
            voiceUrl = res.voiceUrl;
            audioSegments = [{
                id: `${res.id}_segment_0`,
                voiceUrl: res.voiceUrl,
                duration: totalDuration,
                segmentIndex: 0,
            }];
        }

        return {
            id: res.id,
            asrId: res.id,
            taskId: res.taskId,
            businessId: res.businessId,
            businessType: res.businessType,
            voiceUrl,
            duration: totalDuration,
            asrMetadataList: res.result || [],
            medicalRecord: res.aiResult,
            audioSegments,
            voiceUrls: res.voiceUrls, // 保留原始数组
        };
    }
    return null;
}

export const getAsrResult = async (outpatientSheetId, taskId = '') => {
    try {
        const res = await fetch.post('/api/asr/result/query', {
            businessId: outpatientSheetId,
            taskId,
            businessTypes: [
                SOCKET_BUSINESS_TYPE.VOICE_MR,
                SOCKET_BUSINESS_TYPE.VOICE_MR_PC_JOIN_APP,
            ],
        });
        
        const rows = res.data?.data?.rows || [];
        let item = null;
        if (rows.length) {
            item = rows[rows.length - 1];
        }
        
        if (!item) {
            return null;
        }
        return resolveAsrResultDetail(item);
        
    } catch (error) {
        console.error('Get asr result error:', error);
        return null;
    }
};

export const getAsrResultDetail = async (asrId) => {
    try {
        const res = await fetch.get(`/api/asr/result/${asrId}`);
        if (!res.data?.data) {
            return null;
        }
        return resolveAsrResultDetail(res.data.data);
    } catch (error) {
        console.error('Get asr result error:', error);
        return null;
    }
};

/**
 * 根据当前录音任务ID获取录音详情
 * @param {string} businessId 业务ID
 * @param {string} taskId 录音任务ID
 * @returns {Promise<AsrResult>} 录音详情
 */
export const getAsrResultDetailByTaskId = async (businessId, taskId) => {
    if (!taskId) {
        console.warn('获取录音音频链接：taskId为空');
        return null;
    }

    try {
        const res = await fetch.post(`/api/asr/result/task/${taskId}`, {
            businessId,
            businessTypes: [
                SOCKET_BUSINESS_TYPE.VOICE_MR,
                SOCKET_BUSINESS_TYPE.VOICE_MR_PC_JOIN_APP,
            ],
        });
        if (!res.data?.data) {
            return null;
        }
        const responseData = res.data.data;
        return resolveAsrResultDetail(responseData);
    } catch (error) {
        console.error('Get asr result error:', error);
        return null;
    }
};

/**
 * 查询说话人分离任务状态
 * @param {*} taskId
 * @param {*} businessType
 * @returns
 */
export const querySpeakerRecognitionStatus = async (taskId) => {
    try {
        const res = await fetch.get(`/api/v2/ai/speech/bytedance/asr/result/${taskId}/${SPEAKER_BUSINESS_TYPE}`);
        return {
            taskResult: res.data?.data?.taskResult,
            status: res.data?.data?.status,
        };
    } catch (error) {
        console.error('Get speaker recognition status error:', error);
        return null;
    }
};


/**
 * 提交说话人分离任务
 * @param {*} taskId 说话人分离任务 ID，使用附件的 id 作为任务 id
 * @param {{format: string, url: string}} audio
 * @returns
 */
export const submitSpeakerRecognitionTask = async (taskId, audio) => {
    try {
        const res = await fetch.post('/api/v2/ai/speech/bytedance/asr/async-task/submit', {
            businessId: taskId,
            businessType: SPEAKER_BUSINESS_TYPE,
            audio,
        });
        return res.data;
    } catch (error) {
        console.error('Submit speaker recognition task error:', error);
        return null;
    }
};

// 统一导出
export default {
    fetchIsReadTips,
    markReadTips,
    saveAsrResult,
    getAsrResult,
    getAsrResultDetail,
    getAsrResultDetailByTaskId,
    querySpeakerRecognitionStatus,
    submitSpeakerRecognitionTask,
};
