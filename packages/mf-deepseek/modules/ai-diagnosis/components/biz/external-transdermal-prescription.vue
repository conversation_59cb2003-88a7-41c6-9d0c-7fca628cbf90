<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
    <abc-flex
        vertical
        class="external-prescription-comp-wrapper"
    >
        <div v-if="formatData.type" class="external-prescription-comp__title">
            {{ handleTitle(formatData.type) }}
        </div>

        <abc-flex
            v-if="formatData.acupoint"
            style="margin-top: 4px;"
            vertical
        >
            <abc-text
                v-if="isArrayComplete(formatData.acupoint.place)"
                tag="div"
                bold
                style="font-size: 13px;"
            >
                穴位：{{ handlePlace(formatData.acupoint.place) }}
            </abc-text>

            <span
                v-if="formatData.acupoint.purpose"
                style="margin-top: 2px; font-size: 13px; color: var(--abc-color-T2);"
            >
                {{ formatData.acupoint.purpose }}
            </span>
        </abc-flex>

        <template v-if="formatData.herbs && isArrayComplete(formatData.herbs.herbs)">
            <abc-flex
                vertical
                style="margin-top: 8px;"
            >
                <abc-text tag="div" bold style="font-size: 13px;">
                    药物：{{ handleHerbs(formatData.herbs.herbs) }}
                </abc-text>

                <span
                    v-if="formatData.herbs.purpose"
                    style="margin-top: 2px; font-size: 13px; color: var(--abc-color-T2);"
                >
                    {{ formatData.herbs.purpose }}
                </span>
            </abc-flex>
        </template>

        <div v-if="handleUsage(formatData, true)" style="margin-top: 8px;">
            <span style="font-weight: 600;">用法：</span>{{ handleUsage(formatData, true) }}
        </div>

        <div v-if="formatData.operation" style="margin-top: 4px;">
            <span style="font-weight: 600;">操作：</span>{{ formatData.operation }}
        </div>

        <abc-flex
            v-if="loaded && isShowAcceptButton"
            style="padding-top: 8px;"
            align="center"
            justify="flex-end"
        >
            <accept-button @click="handleClick(formatData)"></accept-button>
        </abc-flex>
    </abc-flex>
</template>

<script>
    import { defineComponent } from 'vue';
    import {
        ExternalPRUsageTypeEnum,
    } from '../../utils/constant';
    import AcceptButton from '@/common/components/accept-button.vue';
    import deepseekService from '../../services/deepseek-service';
    import { ANALYSIS_EXTEND_FLAG } from '../../utils/constant';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import {
        numberPrefixRegex, numberRegex,
    } from '@/common/utils/regex';
    import {
        isString,
    } from '@/common/utils/index';
    import { useExternalPrescriptionStore } from '../../hooks/use-external-prescription';
    import { useDeepseekDataStore } from '../../hooks/use-deepseek-data';
    import { storeToRefs } from 'MfBase/pinia';

    export default defineComponent({
        name: 'ExternalTransdermalPrescription',

        components: {
            AcceptButton,
        },

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },

            loaded: {
                type: Boolean,
                default: false,
            },
        },

        setup() {
            const externalPrescriptionStore = useExternalPrescriptionStore();
            const {
                handleTitle, handlePlace, handleUsage, handleCount, handleHerbs, isArrayComplete, handleDurationTime,
            } = externalPrescriptionStore;

            const store = useDeepseekDataStore();

            const {
                isShowAcceptButton,
            } = storeToRefs(store);

            return {
                handleTitle,
                handlePlace,
                handleUsage,
                handleCount,
                handleHerbs,
                isArrayComplete,
                handleDurationTime,
                isShowAcceptButton,
            };
        },

        computed: {
            formatData() {
                return this.data || {};
            },
        },

        methods: {
            handleCustomGoods(herbs) {
                if (!herbs || !Array.isArray(herbs)) return [];

                return herbs.map((h) => {
                    const {
                        name, dosage,
                    } = h;

                    if (!dosage || !isString(dosage)) {
                        return {
                            name,
                            unitCount: '',
                            unit: '',
                        };
                    }

                    if (numberRegex.test(dosage)) {
                        return {
                            name,
                            unitCount: parseInt(dosage, 10),
                            unit: 'g',
                        };
                    }

                    const match = dosage.match(numberPrefixRegex);
                    if (match) {
                        return {
                            name,
                            unitCount: parseInt(match[1], 10),
                            unit: match[2],
                        };
                    }

                    return {
                        name,
                        unitCount: '',
                        unit: '',
                    };
                });
            },


            handleClick(o) {
                const {
                    // eslint-disable-next-line camelcase
                    type, frequency, duration_per_time, acupoint, herbs, total_times,
                } = o;

                const payload = {
                    usageType: ExternalPRUsageTypeEnum[type],
                    freq: frequency,
                    // eslint-disable-next-line camelcase
                    specialRequirement: duration_per_time ? `${this.handleDurationTime(duration_per_time)}` : '',
                    dosage: this.handleCount(total_times),
                    acupoints: acupoint.place || [],
                    externalGoodsItems: this.handleCustomGoods(herbs.herbs),
                };

                this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                    type: 'prescriptionExternalForms',
                    value: payload,
                });

                deepseekService.analysisDeepseekResult(ANALYSIS_EXTEND_FLAG.EXTERNAL_PRESCRIPTION);
            },
        },
    });
</script>

<style lang="scss" scoped>
.external-prescription-comp-wrapper {
    padding-left: 12px;
    font-size: 13px;
    line-height: 20px;

    .external-prescription-comp__title {
        padding: 4px 0;
        font-weight: 500;
        color: var(--abc-color-B8);
    }
}
</style>

