<template>
    <div class="diagnosis-comp-wrapper">
        <abc-flex v-for="(group, index) in chineseGroups" :key="index" vertical>
            <abc-flex vertical>
                <div
                    v-if="group.content[0]"
                    style="padding: 2px 0 2px 12px; font-size: 13px;"
                >
                    <span style="color: var(--abc-color-T2);">病名</span><span style="margin-left: 8px; font-weight: 600;">{{ group.content[0] }}</span>
                </div>

                <div
                    v-if="group.content[1]"
                    style="padding: 2px 0 2px 12px; font-size: 13px;"
                >
                    <span style="color: var(--abc-color-T2);">证型</span><span style=" margin-left: 8px; font-weight: 600;">{{ group.content[1] }}</span>
                </div>
            </abc-flex>

            <abc-flex
                v-if="(loaded || westernGroups.length > 0) && isShowAcceptButton"
                style="padding-top: 8px;"
                align="center"
                justify="flex-end"
            >
                <accept-button @click="handleClick(group)"></accept-button>
            </abc-flex>

            <abc-divider
                v-if="westernGroups.length > 0"
                variant="dashed"
                style="margin: 20px 0;"
            ></abc-divider>
        </abc-flex>

        <abc-flex
            v-for="(group, key) in westernGroups"
            :key="key"
            class="western-diagnostic-item"
            align="center"
        >
            <abc-text tag="div" style="flex: 1; font-weight: 600;" class="ellipsis">
                {{ group.content }}{{ isWaitCheck(group.status) ? ' ?' : '' }}
            </abc-text>

            <template v-if="(loaded || westernGroups.length - 1 > key) && isShowAcceptButton">
                <accept-button
                    v-if="!isWaitCheck(group.status)"
                    style="margin-left: 8px;"
                    @click="handleClick(group)"
                ></accept-button>

                <abc-button
                    v-else
                    variant="ghost"
                    theme="primary"
                    shape="round"
                    size="small"
                    width="58"
                    style="margin-left: 8px;"
                    @click="handleClick(group)"
                >
                    采纳
                </abc-button>
            </template>
        </abc-flex>
    </div>
</template>

<script>
    import { defineComponent } from 'vue';
    import AcceptButton from '@/common/components/accept-button.vue';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import { getMarkdownText } from '@/common/utils/index';
    import deepseekService from '../../services/deepseek-service';
    import { ANALYSIS_EXTEND_FLAG } from '../../utils/constant';
    import { useDeepseekDataStore } from '../../hooks/use-deepseek-data';
    import { storeToRefs } from 'MfBase/pinia';

    export default defineComponent({
        name: 'Diagnosis',

        components: {
            AcceptButton,
        },

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },
            loaded: {
                type: Boolean,
            },
        },

        setup() {
            const store = useDeepseekDataStore();

            const {
                isShowAcceptButton,
            } = storeToRefs(store);

            return {
                isShowAcceptButton,
            };
        },

        computed: {
            content() {
                return this.data?.content || '';
            },

            parsedGroups() {
                if (!this.content) return [];

                const contentWithMarkers = this.content;

                // 使用正则表达式提取出各部分内容
                const tcmMatch = contentWithMarkers.match(/中医诊断：([\s\S]*?)(?=西医诊断：|$)/);
                const westernMatch = contentWithMarkers.match(/西医诊断：([\s\S]*?)$/);

                const groups = [];

                // 处理中医诊断部分
                if (tcmMatch && tcmMatch[1].trim()) {
                    groups.push(this.processTcmDiagnosis(tcmMatch[1]));
                }

                // 处理西医诊断部分
                if (westernMatch && westernMatch[1].trim()) {
                    groups.push(...this.processWesternDiagnosis(westernMatch[1]));
                }

                // 如果有内容但没有解析出任何分组，进行兼容处理
                if (this.loaded && this.content && groups.length === 0) {
                    // 如果内容中包含**字样，则当做西医诊断处理
                    if (this.content.includes('**')) {
                        groups.push(...this.processWesternDiagnosis(this.content));
                    } else {
                        // 否则当做中医诊断处理
                        groups.push(this.processTcmDiagnosis(this.content));
                    }
                }

                const sortedGroups = groups.reduce((acc, group) => {
                    if (!group.status) {
                        acc.normal.push(group);
                    } else if (this.isWaitCheck(group.status)) {
                        acc.waitCheck.push(group);
                    } else {
                        acc.confirmed.push(group);
                    }
                    return acc;
                }, {
                    normal: [],
                    waitCheck: [],
                    confirmed: [],
                });

                return [
                    ...sortedGroups.normal,
                    ...sortedGroups.confirmed,
                    ...sortedGroups.waitCheck,
                ];
            },

            chineseGroups() {
                return this.parsedGroups.filter((group) => group.isTCM);
            },

            westernGroups() {
                return this.parsedGroups.filter((group) => !group.isTCM);
            },
        },

        methods: {
            isWaitCheck(str) {
                if (!str) return false;

                return str.startsWith('待查');
            },

            // 处理中医诊断内容
            processTcmDiagnosis(content) {
                const tcmContent = getMarkdownText(content).replace(/\r\s/g, '');
                // 正则分别提取病名和证型
                const diseaseName = tcmContent.match(/病名：(.*?)证型/)?.[1]?.trim() || tcmContent.trim() || '';
                const syndrome = tcmContent.match(/证型：(.*?)$/)?.[1]?.trim() || '';

                return {
                    isTCM: true,
                    status: '',
                    content: [diseaseName, syndrome],
                };
            },

            // 处理西医诊断内容
            processWesternDiagnosis(content) {
                const westernContent = content.trim();
                const groups = [];

                // 使用正则表达式直接匹配 **标题** 和 ***状态*** 的组合
                const diagnosisRegex = /\*\*(.*?)\*\*[\s\S]*?\*\*\*(.*?)\*\*\*/g;
                const diagnosisMatches = [...westernContent.matchAll(diagnosisRegex)];

                // 如果能匹配到完整的诊断+状态格式
                if (diagnosisMatches.length) {
                    diagnosisMatches.forEach((match) => {
                        groups.push({
                            isTCM: false,
                            content: match[1].trim(),
                            status: match[2].trim(),
                        });
                    });

                    return groups;
                }

                // 如果没有匹配到完整格式，尝试匹配所有标题和状态
                const titleRegex = /\*\*(.*?)\*\*/g;
                const statusRegex = /\*\*\*(.*?)\*\*\*/g;

                // 获取所有标题和状态，并记录它们的位置
                const titles = [];
                const statuses = [];

                let titleMatch;
                while ((titleMatch = titleRegex.exec(westernContent)) !== null) {
                    titles.push({
                        text: titleMatch[1].trim(),
                        index: titleMatch.index,
                        endIndex: titleMatch.index + titleMatch[0].length,
                    });
                }

                let statusMatch;
                while ((statusMatch = statusRegex.exec(westernContent)) !== null) {
                    statuses.push({
                        text: statusMatch[1].trim(),
                        index: statusMatch.index,
                        endIndex: statusMatch.index + statusMatch[0].length,
                    });
                }

                // 如果有标题
                if (titles.length) {
                    // 为每个标题找到最近的状态
                    titles.forEach((title, i) => {
                        let status = '';
                        const nextTitleIndex = i < titles.length - 1 ? titles[i + 1].index : Infinity;

                        // 查找此标题之后、下一个标题之前的状态
                        const matchingStatus = statuses.find((s) =>
                            s.index > title.endIndex && s.index < nextTitleIndex,
                        );

                        if (matchingStatus) {
                            status = matchingStatus.text;
                        }

                        groups.push({
                            isTCM: false,
                            content: title.text,
                            status,
                        });
                    });

                    return groups;
                }

                if (westernContent.includes('**')) {
                    // 不完整的格式
                    groups.push({
                        isTCM: false,
                        content: westernContent.replace(/\*/g, '').trim(),
                        status: '',
                    });
                }

                return groups;
            },

            handleClick(group) {
                const {
                    isTCM, content,
                } = group;
                if (isTCM) {
                    this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                        type: 'medicalRecord',
                        value: {
                            diagnosis: content[0],
                            syndrome: content[1],
                        },
                    });
                } else {
                    this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                        type: 'medicalRecord',
                        value: {
                            diagnosis: content,
                        },
                    });
                }

                deepseekService.analysisDeepseekResult(ANALYSIS_EXTEND_FLAG.DIALECTICAL);
            },
        },
    });
</script>

<style lang="scss">
.diagnosis-comp-wrapper {
    line-height: 20px;

    .western-diagnostic-item {
        position: relative;
        padding-left: 12px;
        line-height: 20px;

        &::before {
            position: absolute;
            top: 3px;
            left: 0;
            font-size: 18px;
            line-height: 1;
            content: "•";
        }
    }

    .western-diagnostic-item + .western-diagnostic-item {
        margin-top: 16px;
    }
}
</style>
