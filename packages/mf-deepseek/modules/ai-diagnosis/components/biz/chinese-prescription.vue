<template>
    <base-card
        class="chinese-prescription-comp-wrapper"
        :loaded="loaded"
        :is-show-accept-button="isShowAcceptButton"
        @click="handleClick"
    >
        <template #default>
            <div class="prescription-comp__title">
                {{ title }}
            </div>

            <abc-flex
                vertical
                class="prescription-comp__list-wrapper"
            >
                <abc-flex
                    v-for="(item, key) in renderList"
                    :key="key"
                    vertical
                    class="prescription-comp__list"
                >
                    <abc-text bold tag="div" class="list-content">
                        {{ item.content }}
                    </abc-text>

                    <abc-text
                        theme="gray"
                        size="small"
                        class="list-purpose"
                    >
                        {{ item.purpose }}
                    </abc-text>
                </abc-flex>
            </abc-flex>
        </template>
    </base-card>
</template>

<script>
    import { defineComponent } from 'vue';
    import {
        PROCESSING_ENUM,
    } from '../../utils/constant';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import BaseCard from '@/common/components/base-card';
    import {
        isString,
    } from '@/common/utils/index';
    import {
        numberRegex, numberPrefixRegex, bracketRegex,
    } from '@/common/utils/regex';
    import deepseekService from '../../services/deepseek-service';
    import { ANALYSIS_EXTEND_FLAG } from '../../utils/constant';
    import { storeToRefs } from 'MfBase/pinia';
    import { useDeepseekDataStore } from '../../hooks/use-deepseek-data';

    export default defineComponent({
        name: 'ChinesePrescription',
        components: {
            BaseCard,
        },
        props: {
            data: {
                type: Object,
                default: () => ({}),
            },
            loaded: {
                type: Boolean,
            },
        },
        setup() {
            const store = useDeepseekDataStore();

            const {
                resultId,
                isShowAcceptButton,
            } = storeToRefs(store);

            return {
                resultId,
                isShowAcceptButton,
            };
        },
        computed: {
            title() {
                return this.data?.formula_name || '';
            },

            formatList() {
                if (!this.data || !this.data.categories) {
                    return [];
                }

                if (!Array.isArray(this.data.categories)) {
                    return [];
                }

                return this.data.categories.map((item) => {
                    return {
                        purpose: item.purpose,
                        list: Array.isArray(item.herbs) ? item.herbs.map((it) => {
                            return this.parsePrescriptionData(it);
                        }) : [],
                    };
                });
            },

            renderList() {
                return this.formatList.map((item) => {
                    return {
                        content: item.list.map((o) => {
                            return `${o.name || ''}  ${o.dosage || ''}${o.processing ? `(${o.processing})` : ''}`;
                        }).join('，') || '',
                        purpose: item.purpose,
                    };
                });
            },
        },

        methods: {
            handleClick() {
                const value = this.formatList.reduce((res, cur) => {
                    const _list = cur.list.map((o) => {
                        const {
                            name, dosage, processing,
                        } = o;

                        if (dosage && isString(dosage)) {
                            const match = dosage.match(numberPrefixRegex);
                            if (match) {
                                return {
                                    name,
                                    specialRequirement: processing,
                                    unitCount: parseInt(match[1], 10),
                                    unit: match[2],
                                };
                            }
                        }
                        return {
                            name,
                            specialRequirement: processing,
                            unit: '',
                            unitCount: '',
                        };
                    });

                    res = res.concat(_list);
                    return res;
                }, []);


                this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                    type: 'prescriptionChineseForm',
                    value,
                    resultId: this.resultId,
                });

                deepseekService.analysisDeepseekResult(ANALYSIS_EXTEND_FLAG.CHINESE_PRESCRIPTION);
            },

            parsePrescriptionData(item) {
                if (!item) {
                    return {};
                }

                const result = {
                    ...item, processing: '',
                };

                if (item.name && isString(item.name)) {
                    // 处理药品名称
                    const nameMatch = item.name.match(bracketRegex);
                    if (nameMatch) {
                        // 提取括号内容作为处理说明
                        const processing = nameMatch[1];
                        // 移除括号及其内容
                        result.name = item.name.replace(bracketRegex, '').trim();
                        // 添加处理说明字段

                        if (processing && PROCESSING_ENUM.includes(processing.trim())) {
                            result.processing = processing.trim();
                        }
                    }
                }

                if (item.dosage && isString(item.dosage)) {
                    // 处理剂量
                    const dosageMatch = item.dosage.match(bracketRegex);
                    if (dosageMatch) {
                        // 提取括号内容作为处理说明
                        const processing = dosageMatch[1];
                        // 移除括号及其内容
                        result.dosage = item.dosage.replace(bracketRegex, '').trim();

                        // 正则匹配result.dosage 如果是纯数字，需要添加单位 g
                        if (numberRegex.test(result.dosage)) {
                            result.dosage += 'g';
                        }

                        // 添加处理说明字段， 允许覆盖
                        if (processing && PROCESSING_ENUM.includes(processing.trim())) {
                            result.processing = processing.trim();
                        }
                    }
                }

                return result;
            },
        },
    });
</script>

<style lang="scss" scoped>
.chinese-prescription-comp-wrapper {
    padding-left: 12px;
    line-height: 20px;

    .prescription-comp__title {
        padding: 4px 0;
        font-size: 13px;
        font-weight: 500;
        color: var(--abc-color-B8);
    }

    .prescription-comp__list-wrapper {
        padding-top: 8px;

        .prescription-comp__list + .prescription-comp__list {
            margin-top: 12px;
        }

        .prescription-comp__list {
            .list-purpose {
                margin-top: 2px;
                font-size: 13px;
                line-height: 20px;
            }

            .list-content {
                font-size: 13px;
            }
        }
    }
}
</style>
