<template>
    <base-card :loaded="loaded" :is-show-accept-button="isShowAcceptButton" @click="handleClick">
        <abc-text
            bold
            tag="div"
            style="padding: 4px 0 0 12px; font-size: 13px; line-height: 20px;"
        >
            {{ content }}
        </abc-text>
    </base-card>
</template>

<script>
    import { defineComponent } from 'vue';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import { getMarkdownText } from '@/common/utils/index';
    import BaseCard from '@/common/components/base-card.vue';
    import { useDeepseekDataStore } from '../../hooks/use-deepseek-data';
    import { storeToRefs } from 'MfBase/pinia';

    export default defineComponent({
        name: 'Treatment',

        components: {
            BaseCard,
        },

        props: {
            data: {
                type: Object,
                default: () => ({}),
            },

            loaded: {
                type: <PERSON>olean,
                default: false,
            },
        },

        setup() {
            const store = useDeepseekDataStore();

            const {
                isShowAcceptButton,
            } = storeToRefs(store);

            return {
                isShowAcceptButton,
            };
        },

        computed: {
            content() {
                return getMarkdownText(this.data?.content);
            },
        },

        methods: {
            handleClick() {
                this.$abcEventBus.$emit(EVENT_BUS_NAME, {
                    type: 'medicalRecord',
                    value: {
                        therapy: this.content,
                    },
                });
            },
        },
    });
</script>
