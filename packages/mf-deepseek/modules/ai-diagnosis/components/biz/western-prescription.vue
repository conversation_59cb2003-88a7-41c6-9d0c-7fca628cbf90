<template>
    <div class="western-prescription-comp-wrapper">
        <div v-if="currentCategory" class="current-category-wrapper">
            <abc-flex v-if="currentCategory.pr && currentCategory.pr.length > 0" vertical style="padding-top: 4px;">
                <western-item
                    v-for="(o, key) in currentCategory.pr"
                    :key="key"
                    :index="key"
                    :name="o.name"
                    :dose="o.dose"
                    :route="o.route"
                    :frequency="o.frequency"
                    :remarks="o.remarks"
                    class="current-category-item"
                ></western-item>
            </abc-flex>

            <abc-flex
                v-if="(loaded || resetCategories.length > 0) && currentCategory.pr && currentCategory.pr.length > 0 && isShowAcceptButton"
                style="padding-top: 8px;"
                align="center"
                justify="flex-end"
            >
                <accept-button @click="handleClick(currentCategory.pr)"></accept-button>
            </abc-flex>
        </div>

        <abc-divider v-if="resetCategories.length > 0 && currentCategory.pr && currentCategory.pr.length > 0" variant="dashed" style="margin: 20px 0;"></abc-divider>

        <div v-for="(o, key) in resetCategories" :key="key" class="reset-category-wrapper">
            <abc-flex
                align="center"
                gap="8"
                class="reset-category-item"
            >
                <abc-text
                    v-if="o.diagnosis"
                    tag="div"
                    style="font-size: 13px; font-weight: 600;"
                >
                    {{ o.diagnosis }}{{ o.plan ? ' ?' : '' }}
                </abc-text>

                <abc-text
                    v-if="o.plan"
                    theme="warning"
                    tag="div"
                    style="font-size: 13px;"
                >
                    待确诊
                </abc-text>
            </abc-flex>


            <abc-flex v-if="o.pr && o.pr.length > 0" vertical style="padding-top: 4px;">
                <western-item
                    v-for="(m, n) in o.pr"
                    :key="n"
                    :index="n"
                    :name="m.name"
                    :dose="m.dose"
                    :route="m.route"
                    :frequency="m.frequency"
                    :remarks="m.remarks"
                    class="current-category-item"
                ></western-item>
            </abc-flex>

            <abc-text v-if="(!o.pr || !o.pr.length) && loaded" tag="div" style="padding: 4px 0 4px 12px; font-size: 13px;">
                需依据临床病情谨慎考虑处方
            </abc-text>

            <abc-flex
                v-if="(loaded || resetCategories.length - 1 > key) && o.pr && o.pr.length > 0 && isShowAcceptButton"
                style="padding-top: 8px;"
                align="center"
                justify="flex-end"
            >
                <accept-button v-if="!o.plan" @click="handleClick(o.pr)"></accept-button>

                <abc-button
                    v-else
                    variant="ghost"
                    theme="primary"
                    shape="round"
                    size="small"
                    width="58"
                    @click="handleClick(o.pr)"
                >
                    采纳
                </abc-button>
            </abc-flex>

            <abc-divider v-if="key !== resetCategories.length - 1" variant="dashed" style="margin: 20px 0;"></abc-divider>
        </div>
    </div>
</template>

<script>
    import { defineComponent } from 'vue';
    import AcceptButton from '@/common/components/accept-button.vue';
    import WesternItem from '../base/western-item.vue';
    import { EVENT_BUS_NAME } from '@/common/utils/constant';
    import {
        isString ,
    } from '@/common/utils/index';
    import { numberPrefixRegex } from '@/common/utils/regex';
    import deepseekService from '../../services/deepseek-service';
    import { ANALYSIS_EXTEND_FLAG } from '../../utils/constant';
    import { storeToRefs } from 'MfBase/pinia';
    import { useDeepseekDataStore } from '../../hooks/use-deepseek-data';

    export default defineComponent({
        name: 'WesternPrescription',
        components: {
            AcceptButton,
            WesternItem,
        },
        props: {
            data: {
                type: Object,
                default: () => ({}),
            },
            loaded: {
                type: Boolean,
            },
        },

        setup() {
            const store = useDeepseekDataStore();

            const {
                resultId,
                isShowAcceptButton,
            } = storeToRefs(store);

            return {
                resultId,
                isShowAcceptButton,
            };
        },

        computed: {
            categories() {
                return this.data?.categories || [];
            },

            currentCategory() {
                return this.categories[0] || null;
            },

            resetCategories() {
                // eslint-disable-next-line no-unused-vars
                const [current, ...rest] = this.categories;

                return rest || [];
            },
        },

        methods: {
            handleDose(dose) {
                if (!dose || !isString(dose)) {
                    return {
                        dosage: '',
                        dosageUnit: '',
                    };
                }

                const match = dose.match(numberPrefixRegex);
                if (match) {
                    return {
                        dosage: match[1],
                        dosageUnit: match[2],
                    };
                }

                return {
                    dosage: '',
                    dosageUnit: '',
                };
            },

            handleCourse(course) {
                if (!course || !isString(course)) {
                    return {
                        days: '',
                    };
                }

                const match = course.match(numberPrefixRegex);
                if (match) {
                    return {
                        days: match[1],
                    };
                }

                return {
                    days: '',
                };
            },

            handleClick(pr) {
                const data = {
                    type: 'prescriptionWesternForms',
                    value: [],
                    resultId: this.resultId,
                };

                if (!pr || pr.length === 0) {
                    return;
                }

                data.value = pr.map((o) => {
                    const {
                        // eslint-disable-next-line camelcase
                        name, dosage_form, dose, frequency, remarks, course, route,
                    } = o;

                    return {
                        name: name || '',
                        // eslint-disable-next-line camelcase
                        medicineDosageForm: dosage_form || '',
                        specialRequirement: remarks || '',
                        freq: frequency || '',
                        usage: route || '',
                        ...this.handleDose(dose),
                        ...this.handleCourse(course),
                    };
                });


                this.$abcEventBus.$emit(EVENT_BUS_NAME, data);
                deepseekService.analysisDeepseekResult(ANALYSIS_EXTEND_FLAG.WESTERN_PRESCRIPTION);
            },
        },
    });
</script>

<style lang="scss">
.western-prescription-comp-wrapper {
    line-height: 20px;

    .current-category-item + .current-category-item {
        margin-top: 12px;
    }

    .reset-category-item {
        position: relative;
        padding: 4px 0 4px 12px;
        line-height: 20px;

        &::before {
            position: absolute;
            top: 6px;
            left: 0;
            font-size: 18px;
            line-height: 1;
            content: "•";
        }
    }
}
</style>
