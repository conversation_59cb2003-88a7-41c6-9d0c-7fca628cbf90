/**
 * PartialJSONParser 类 - 尝试解析不完整的 JSON 字符串
 */
export default class PartialJsonParser {
    /**
   * 构造函数
   * @param {string} input - 输入的 JSON 字符串
   */
    constructor(input) {
        this.input = input;
        this.pos = 0;
        this._partialResult = null;
    }

    /**
   * 解析入口点
   * @returns {Object|null} 解析结果或 null
   */
    parse() {
        try {
            return this.parseValue();
        } catch (e) {
            return this._partialResult;
        }
    }

    /**
   * 查看当前字符，不移动指针
   * @returns {string|null} 当前字符或 null
   */
    peek() {
        return this.pos < this.input.length ? this.input[this.pos] : null;
    }

    /**
   * 移动到下一个字符
   */
    advance() {
        if (this.pos < this.input.length) this.pos++;
    }

    /**
   * 跳过空白字符
   */
    skipWhitespace() {
        while (/\s/.test(this.peek())) this.advance();
    }

    /**
   * 解析值（对象、数组、字符串等）
   * @returns {*} 解析的值
   */
    parseValue() {
        this.skipWhitespace();
        const char = this.peek();

        if (char === '{') {
            return this.parseObject();
        } if (char === '[') {
            return this.parseArray();
        } if (char === '"') {
            return this.parseString();
        } if (char === 't' || char === 'f' || char === 'n') {
            return this.parseLiteral();
        } if (char === '-' || (char >= '0' && char <= '9')) {
            return this.parseNumber();
        }

        throw new Error(`Unexpected character: ${char}`);
    }

    /**
   * 解析对象
   * @returns {Object} 解析的对象
   */
    parseObject() {
        const obj = {};
        this._partialResult = obj;
        this.consume('{');
        this.skipWhitespace();

        if (this.peek() === '}') {
            this.consume('}');
            return obj;
        }

        while (this.pos < this.input.length) {
            try {
                const key = this.parseString();
                this.skipWhitespace();
                this.consume(':');
                const value = this.parseValue();
                obj[key] = value;
                this.skipWhitespace();

                if (this.peek() === ',') {
                    this.consume(',');
                    this.skipWhitespace();
                    if (this.pos >= this.input.length || this.peek() === null) {
                        return obj;
                    }
                } else if (this.peek() === '}') {
                    this.consume('}');
                    break;
                } else {
                    break;
                }
            } catch (e) {
                return obj;
            }
        }

        return obj;
    }

    /**
   * 解析数组
   * @returns {Array} 解析的数组
   */
    parseArray() {
        const arr = [];
        this.consume('[');
        this.skipWhitespace();

        if (this.peek() === ']') {
            this.consume(']');
            return arr;
        }

        while (this.pos < this.input.length) {
            try {
                const value = this.parseValue();
                arr.push(value);
            } catch (e) {
                break;
            }

            this.skipWhitespace();

            if (this.peek() === ',') {
                this.consume(',');
                this.skipWhitespace();
            } else if (this.peek() === ']') {
                this.consume(']');
                break;
            } else {
                break;
            }
        }

        return arr;
    }

    /**
   * 辅助方法：消费特定字符
   * @param {string} expected - 期望的字符
   */
    consume(expected) {
        this.skipWhitespace();
        if (this.peek() === expected) {
            this.advance();
        } else {
            throw new Error(`Expected ${expected}, found ${this.peek()}`);
        }
    }

    /**
   * 解析字符串
   * @returns {string} 解析的字符串
   */
    parseString() {
        let result = '';
        this.consume('"');

        while (this.pos < this.input.length && this.peek() !== '"') {
            if (this.peek() === '\\' && this.pos + 1 < this.input.length) {
                result += this.input[this.pos++];
                if (this.pos < this.input.length) {
                    result += this.input[this.pos++];
                }
            } else {
                result += this.input[this.pos++];
            }
        }

        if (this.peek() === '"') {
            this.consume('"');
        }

        return result;
    }

    /**
   * 解析数字
   * @returns {number|null} 解析的数字或 null
   */
    parseNumber() {
        let numberStr = '';
        while (this.pos < this.input.length && /[0-9.eE+-]/.test(this.peek())) {
            numberStr += this.input[this.pos++];
        }

        const number = parseFloat(numberStr);
        return isNaN(number) ? null : number;
    }

    /**
   * 解析字面量（true/false/null）
   * @returns {boolean|null} 解析的字面量值
   */
    parseLiteral() {
        const start = this.pos;
        while (this.pos < this.input.length && /[a-z]/.test(this.peek())) {
            this.advance();
        }

        const literal = this.input.slice(start, this.pos);
        if (literal === 'true') return true;
        if (literal === 'false') return false;
        if (literal === 'null') return null;

        throw new Error(`Unexpected literal: ${literal}`);
    }
}
