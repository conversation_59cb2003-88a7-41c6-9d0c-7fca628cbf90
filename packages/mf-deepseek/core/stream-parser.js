import TokenizerEngine from './tokenizer-engine';

/**
 * StreamParser 类 - 流式解析器
 * 组合 TokenizerEngine 和 ParserEngine，提供完整的解析流程
 */
export default class StreamParser {
    /**
   * 构造函数
   * @param {Object} options - 配置选项
   * @param {Array} options.rules - 初始规则集合
   * @param {Function} options.defaultProcessor  默认处理
   */
    constructor(options = {}) {
        const {
            rules = [], defaultProcessor,
        } = options;

        if (!defaultProcessor) {
            throw new Error('defaultProcessor is required');
        }

        this.tokenizer = new TokenizerEngine({
            rules,
            defaultProcessor,
        });
    }

    /**
   * 获取标记器引擎
   * @returns {TokenizerEngine} 标记器引擎实例
   */
    getTokenizer() {
        return this.tokenizer;
    }

    /**
   * 添加规则
   * @param {Rule} rule - 要添加的规则
   * @returns {StreamParser} 当前实例，支持链式调用
   */
    addRule(rule) {
        this.tokenizer.addRule(rule);
        return this;
    }

    /**
   * 添加多个规则
   * @param {Array} rules - 要添加的规则数组
   * @returns {StreamParser} 当前实例，支持链式调用
   */
    addRules(rules) {
        this.tokenizer.addRules(rules);
        return this;
    }


    /**
   * 解析内容
   * @param {string} content - 要解析的内容
   * @returns {Array} 解析后的结果数组
   */
    parse(content) {
        this.tokenizer.tokenize(content);
    }
}
