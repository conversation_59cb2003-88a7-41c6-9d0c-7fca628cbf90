/**
 * Rule 类 - 解析规则的基础类
 * 每个规则需要定义如何识别开始和结束标记，以及如何处理匹配的内容
 */
export default class Rule {
    /**
     * 构造函数
     * @param {Object} options - 规则配置选项
     * @param {string|RegExp} options.startMatcher - 开始标记匹配器
     * @param {string|RegExp} options.endMatcher - 结束标记匹配器
     * @param {string} options.type - 规则类型标识符
     * @param {Function} options.processor - 处理匹配内容的函数
     * @param {boolean} options.includeMarkers - 是否在处理的内容中包含开始和结束标记，默认为 false。
     *                                          当设置为 true 时，处理的内容将包含开始和结束标记；
     *                                          当设置为 false 时，处理的内容将不包含开始和结束标记。
     */
    constructor(options = {}) {
        const {
            startMatcher, endMatcher, type, processor, includeMarkers = false,
        } = options;

        this.startMatcher = startMatcher;
        this.endMatcher = endMatcher;
        this.type = type || 'unknown';
        this.processor = processor || ((content) => ({
            type: this.type, content,
        }));
        this.includeMarkers = includeMarkers;
    }

    /**
     * 检查内容是否匹配开始标记
     * @param {string} content - 要检查的内容
     * @param {number} position - 当前位置
     * @returns {number} 匹配的位置，如果不匹配则返回 -1
     */
    matchStart(content, position) {
        if (typeof this.startMatcher === 'string') {
            return content.indexOf(this.startMatcher, position);
        }

        if (this.startMatcher instanceof RegExp) {
            const substring = content.substring(position);
            const match = substring.match(this.startMatcher);
            return match ? position + match.index : -1;
        }


        return -1;
    }

    /**
     * 检查内容是否匹配结束标记
     * @param {string} content - 要检查的内容
     * @param {number} position - 当前位置
     * @returns {number} 匹配的位置，如果不匹配则返回 -1
     */
    matchEnd(content, position) {
        if (!this.endMatcher) return -1;

        if (typeof this.endMatcher === 'string') {
            return content.indexOf(this.endMatcher, position);
        }

        if (this.endMatcher instanceof RegExp) {
            const substring = content.substring(position);
            const match = substring.match(this.endMatcher);
            return match ? position + match.index : -1;
        }

        return -1;
    }

    /**
     * 获取开始标记的长度
     * @param {string} content - 匹配的内容
     * @param {number} position - 匹配的位置
     * @returns {number} 标记的长度
     */
    getStartMatcherLength(content, position) {
        if (typeof this.startMatcher === 'string') {
            return this.startMatcher.length;
        }

        if (this.startMatcher instanceof RegExp) {
            const substring = content.substring(position);
            const match = substring.match(this.startMatcher);
            return match ? match[0].length : 0;
        }

        return 0;
    }

    /**
     * 获取结束标记的长度
     * @param {string} content - 匹配的内容
     * @param {number} position - 匹配的位置
     * @returns {number} 标记的长度
     */
    getEndMatcherLength(content, position) {
        if (!this.endMatcher) return 0;

        if (typeof this.endMatcher === 'string') {
            return this.endMatcher.length;
        }

        if (this.endMatcher instanceof RegExp) {
            const substring = content.substring(position);
            const match = substring.match(this.endMatcher);
            return match ? match[0].length : 0;
        }

        return 0;
    }

    /**
     * 处理匹配的内容
     * @param {string} content - 匹配的内容
     * @param {boolean} isComplete - 是否完整匹配（有结束标记）
     * @returns {Object} 处理后的结果
     */
    process(content, isComplete = true) {
        return this.processor(content, isComplete);
    }
}
