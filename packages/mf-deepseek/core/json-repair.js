import { jsonrepair } from 'jsonrepair';


function jsonParse(jsonString) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        return false;
    }
}
  
/**
   * 安全地解析 JSON 字符串
   * 先尝试直接解析，如果失败则尝试修复后再解析
   * @param {string} jsonString - JSON 字符串
   * @param {*} defaultValue - 解析失败时返回的默认值
   * @returns {object|*} - 解析结果或默认值
   */
function safeParseJson(jsonString, defaultValue = null) {
    // 基本验证
    if (!jsonString || typeof jsonString !== 'string') {
        return defaultValue;
    }
    
    const baseResult = jsonParse(jsonString);
    if (baseResult) {
        return baseResult;
    }
    
    const cleanJsonString = jsonString.replace(/：/g, ':');
    
    try {
        return jsonrepair(cleanJsonString);
    } catch (e) {
        return defaultValue;
    }
}

export {
    safeParseJson, 
};
