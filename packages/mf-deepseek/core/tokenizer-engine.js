/**
 * TokenizerEngine 类 - 基于规则的标记器引擎
 * 负责管理规则集合并根据规则将内容分解为标记
 */
export default class TokenizerEngine {
    /**
   * 构造函数
   * @param {Object} options - 引擎配置选项
   * @param {Array} options.rules - 初始规则集合
   */
    constructor(options = {}) {
        const {
            rules = [], defaultProcessor,
        } = options;

        this.rules = [...rules];
        this.defaultProcessor = defaultProcessor;
    }

    /**
   * 添加单个规则
   * @param {Rule} rule - 要添加的规则
   * @returns {TokenizerEngine} 当前实例，支持链式调用
   */
    addRule(rule) {
        this.rules.push(rule);
        return this;
    }

    /**
   * 添加多个规则
   * @param {Array} rules - 要添加的规则数组
   * @returns {TokenizerEngine} 当前实例，支持链式调用
   */
    addRules(rules) {
        this.rules.push(...rules);
        return this;
    }

    /**
   * 标记化内容
   * @param {string} content - 要标记化的内容
   * @returns {Array} 标记数组
   */
    tokenize(content) {
        let currentPos = 0;

        while (currentPos < content.length) {
            // 查找下一个匹配的规则
            let nextRuleMatch = null;
            let nextRuleIndex = Infinity;
            let matchedRule = null;

            for (const rule of this.rules) {
                const matchPos = rule.matchStart(content, currentPos);
                if (matchPos !== -1 && matchPos < nextRuleIndex) {
                    nextRuleIndex = matchPos;
                    nextRuleMatch = {
                        rule,
                        position: matchPos,
                    };
                    matchedRule = rule;
                }
            }

            // 如果没有找到匹配的规则，将剩余内容作为默认类型
            if (!nextRuleMatch) {
                this.defaultProcessor({
                    content: content.substring(currentPos),
                });

                break;
            }

            // 处理规则匹配前的默认内容
            if (nextRuleIndex > currentPos) {
                this.defaultProcessor({
                    content: content.substring(currentPos, nextRuleIndex),
                });
            }

            // 处理匹配的规则内容
            const startMatcherLength = matchedRule.getStartMatcherLength(content, nextRuleIndex);
            const contentStart = nextRuleIndex + startMatcherLength;

            if (nextRuleMatch.rule.includeMarkers) {
                this.defaultProcessor({
                    content: content.substring(nextRuleIndex, contentStart),
                });
                // 更新当前位置到标题后
                currentPos = contentStart;
            }

            // 查找结束标记
            const endMatchPos = matchedRule.matchEnd(content, contentStart);

            if (endMatchPos === -1) {
                // 没有找到结束标记，处理未完成的内容
                const incompleteContent = content.substring(contentStart);
                // 使用规则的处理器处理内容
                matchedRule.process(incompleteContent, false);
                break;
            }

            // 找到了结束标记，处理完整的内容
            const matchedContent = content.substring(contentStart, endMatchPos);
            const endMatcherLength = matchedRule.getEndMatcherLength(content, endMatchPos);

            // 使用规则的处理器处理内容
            matchedRule.process(matchedContent, true);

            if (nextRuleMatch.rule.includeMarkers) {
                currentPos = endMatchPos;
            } else {
                currentPos = endMatchPos + endMatcherLength;
            }
        }
    }
}
