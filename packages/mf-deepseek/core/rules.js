import Rule from './rule';
import PartialJsonParser from './partial-json-parser';
import { safeParseJson } from './json-repair';

/**
 * 创建 JSON 规则
 * @param {Object} options - 配置选项
 * @param {string} options.startMarker - 开始标记，默认为 ':::json'
 * @param {string} options.endMarker - 结束标记，默认为 ':::'
 * @param {Function} options.callback - 回调函数，用于处理解析后的数据
 * @param {boolean} options.includeMarkers - 是否在处理的内容中包含开始和结束标记，默认为 false。
 *                                          对于 JSON 规则，通常应设置为 false，因为标记不是 JSON 的一部分
 * @returns {Rule} JSON 规则实例
 */
export const createJsonRule = (options = {}) => {
    const {
        startMarker = '```json',
        endMarker = '```',
        callback,
        includeMarkers = false,
    } = options;

    return new Rule({
        startMatcher: startMarker,
        endMatcher: endMarker,
        type: 'json',
        includeMarkers,
        processor: (content, isComplete) => {
            if (isComplete) {
                const data = safeParseJson(content);
                
                if (data !== null) {
                    callback({
                        data,
                        complete: true,
                        content,
                    });
                    
                    return;
                } 
                
                callback({
                    error: true,
                    complete: isComplete,
                    content,
                });

                return;
            }
            
            const parser = new PartialJsonParser(content);
            const parsedData = parser.parse();
            if (parsedData && Object.keys(parsedData).length > 0) {
                callback({
                    data: parsedData,
                    complete: false,
                    content,
                });
            }
        },
    });
};

/**
 * 创建文本规则
 * @param {Object} options - 配置选项
 * @param {string} options.startMarker - 开始标记，例如 '### 证型'
 * @param {string} options.endMarker - 结束标记，例如 '### 辨证要点'
 * @param {string} options.type - 规则类型
 * @param {Function} options.callback - 回调函数，用于处理解析后的数据
 * @param {boolean} options.includeMarkers - 是否在处理的内容中包含开始和结束标记，默认为 false。
 *                                          对于文本规则，可能需要设置为 true，以保留标题等标记
 * @returns {Rule} 文本规则实例
 */
export const createTextRule = (options = {}) => {
    const {
        startMarker,
        endMarker,
        callback,
        type = 'text',
        includeMarkers = true,
    } = options;

    return new Rule({
        startMatcher: startMarker,
        endMatcher: endMarker,
        type,
        includeMarkers,
        processor: (content, isComplete) => {
            callback({
                content,
                complete: isComplete,
            });
        },
    });
};

/**
 * 创建自定义规则
 * @param {Object} options - 配置选项
 * @param {string|RegExp} options.startMatcher - 开始标记匹配器
 * @param {string|RegExp} options.endMatcher - 结束标记匹配器
 * @param {string} options.type - 规则类型
 * @param {Function} options.processor - 处理函数
 * @param {boolean} options.includeMarkers - 是否在处理的内容中包含开始和结束标记，默认为 false
 * @returns {Rule} 自定义规则实例
 */
export const createCustomRule = (options = {}) => {
    return new Rule(options);
};
