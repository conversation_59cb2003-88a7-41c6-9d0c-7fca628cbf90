// services/stream-service.js
import axios from 'axios';
import { fetch } from 'MfBase/base-api';

/**
 * 创建流式数据服务
 * @param {Object} options - 配置选项
 * @returns {Object} 流式数据服务实例
 */
export const createStreamService = (options = {}) => {
    const {
        defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
        },
        customFetch = fetch,
    } = options;

    /**
   * 发起流式请求
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @returns {Object} 请求控制对象
   */
    const fetchStream = (url, params) => {
        const {
            onmessage,
            onclose,
            onerror,
            body,
            method,
            headers = {},
        } = params;

        // 创建 CancelToken
        const source = axios.CancelToken.source();

        let buffer = '';
        let processedLength = 0;
        let xhr = null; // 保存底层 XHR 对象

        const promise = customFetch({
            url,
            method,
            data: body,
            headers: {
                ...defaultHeaders,
                ...headers,
            },
            responseType: 'text',
            cancelToken: source.token,
            timeout: 200000,
            onDownloadProgress: (progressEvent) => {
                xhr = progressEvent.currentTarget;
                const { responseText } = progressEvent.currentTarget;
                const newData = responseText.slice(processedLength);
                processedLength = responseText.length;

                buffer += newData;

                const messages = buffer.split('\n');
                buffer = messages.pop() || '';

                for (const msg of messages) {
                    if (msg.trim()) {
                        onmessage?.(msg);
                    }
                }
            },
        }).catch((error) => {
            console.error('Error fetching data:', error);
            if (error?.message === '中断切换路由之前的请求') {
                return;
            }
            
            onerror?.(error);
        }).finally(() => {
            onclose?.();
        });

        return {
            promise,
            abort: () => {
                source.cancel('Aborted by user');
                if (xhr) {
                    xhr.abort();
                    xhr = null;
                }
            },
        };
    };

    return {
        fetchStream,
    };
};

// 导出默认实例
export default createStreamService();
