.deepseek-markdown-renderer {
    font-size: 13px;
    hyphens: auto;
    line-height: 1.5;
    color: #000000;
    word-break: break-word;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.deepseek-markdown-renderer > *:first-child {
    margin-top: 16px;
}

.deepseek-markdown-renderer > *:last-child {
    margin-bottom: 16px;
}

.deepseek-markdown-renderer h1,
.deepseek-markdown-renderer h2,
.deepseek-markdown-renderer h3,
.deepseek-markdown-renderer h4,
.deepseek-markdown-renderer h5,
.deepseek-markdown-renderer h6 {
    padding: 0 !important;
    margin: 20px 0 8px !important;
    font-weight: 600 !important;
    line-height: 1.5 !important;
}

.deepseek-markdown-renderer h1 { font-size: 1.85em; }
.deepseek-markdown-renderer h2 { font-size: 1.54em; }
.deepseek-markdown-renderer h3 { font-size: 1.23em; }

.deepseek-markdown-renderer h4,
.deepseek-markdown-renderer h5,
.deepseek-markdown-renderer h6 { font-size: 1em; }

.deepseek-markdown-renderer p {
    margin: 8px 0;
    line-height: 1.5;
}

.deepseek-markdown-renderer strong {
    font-weight: 600 !important;
}

.deepseek-markdown-renderer code {
    padding: 6px 6px;
    font-family: Roboto, Helvetica Neue, Helvetica, Arial, PingFang SC, MyHeiTi, Hiragino Sans GB, Heiti SC, WenQuanYi Micro Hei, sans-serif !important;
    font-size: 1em;
    white-space: pre-wrap;
    background-color: rgba(0, 0, 0, 0.06);
    border-radius: 6px;
}

.deepseek-markdown-renderer pre {
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
    background-color: rgba(0, 0, 0, 0.06);
    border-radius: 6px;
    -webkit-overflow-scrolling: touch;
}

.deepseek-markdown-renderer pre code {
    display: block;
    min-width: 100%;
    padding: 0;
    font-size: 13px;
    tab-size: 2;
    background-color: transparent;
}

.deepseek-markdown-renderer ul,
.deepseek-markdown-renderer ol {
    padding-left: 12px;
    margin: 8px 0;
}

.deepseek-markdown-renderer li {
    padding: 4px 0;
    line-height: 1.6;
}

.deepseek-markdown-renderer li + li {
    margin-top: 2px;
}

.deepseek-markdown-renderer ul > li {
    list-style-type: disc !important;
}

.deepseek-markdown-renderer ol > li {
    list-style-type: decimal !important;
}

.deepseek-markdown-renderer ul ul > li {
    list-style-type: circle !important;
}

.deepseek-markdown-renderer ul ul ul > li {
    list-style-type: square !important;
}

.deepseek-markdown-renderer ol ol > li {
    list-style-type: lower-alpha !important;
}

.deepseek-markdown-renderer ol ol ol > li {
    list-style-type: lower-roman !important;
}

.deepseek-markdown-renderer li > p {
    margin: 4px 0;
}

.deepseek-markdown-renderer blockquote {
    padding: 4px 0 4px 12px;
    margin: 16px 0;
    color: #7a8794;
    border-left: 2px solid #e0e2eb;
}

.deepseek-markdown-renderer blockquote > :first-child {
    margin-top: 8px;
}

.deepseek-markdown-renderer blockquote > :last-child {
    margin-bottom: 8px;
}

.deepseek-markdown-renderer blockquote p {
    margin: 4px 0;
}

.deepseek-markdown-renderer a {
    color: #005ed9;
    text-decoration: none;
    word-break: break-word;
}

.deepseek-markdown-renderer a:hover {
    text-decoration: underline;
}

.deepseek-markdown-renderer table {
    display: block;
    width: 100%;
    margin: 16px 0;
    overflow-x: auto;
    font-size: 13px;
    border-collapse: collapse;
    -webkit-overflow-scrolling: touch;
}

.deepseek-markdown-renderer table thead {
    background-color: #eaedf1;
}

.deepseek-markdown-renderer th,
.deepseek-markdown-renderer td {
    min-width: 80px;
    padding: 4px 8px;
    border: 1px solid #dddddd;
}

.deepseek-markdown-renderer img {
    display: block;
    max-width: 100%;
    height: auto;
    margin: 8px 0;
    border-radius: var(--abc-border-radius-small);
}

.deepseek-markdown-renderer hr {
    height: 1px;
    margin: 20px 0 16px;
    background-color: var(--abc-color-P7);
    border: none;
}

@media (max-width: 768px) {
    .deepseek-markdown-renderer {
        font-size: 13px;
    }

    .deepseek-markdown-renderer pre {
        padding: 6px;
        margin: 6px 0;
    }

    .deepseek-markdown-renderer pre code {
        font-size: 12px;
    }

    .deepseek-markdown-renderer blockquote {
        padding: 3px 0 3px 10px;
    }

    .deepseek-markdown-renderer th,
    .deepseek-markdown-renderer td {
        min-width: 60px;
        padding: 3px 6px;
    }
}

.deepseek-markdown-renderer .markdown-custom-renderer-heading-top-divider {
    height: 1px;
    margin: 20px 0 0 0 !important;
    background-color: var(--abc-color-P6) !important;
}

.deepseek-markdown-renderer .markdown-custom-renderer-paragraph-top-divider {
    height: 1px;
    margin: 20px 0 16px !important;
    border-bottom: 1px dashed var(--abc-color-P6) !important;
}

.deepseek-markdown-renderer .markdown-custom-hr-divider {
    height: 1px;
    margin: 20px 0 16px !important;
    border-bottom: 1px dashed var(--abc-color-P6) !important;
}

.deepseek-markdown-renderer .markdown-custom-renderer-add-sub-suggestion {
    padding: 4px 0 4px 12px !important;
    margin: 0 !important;
    font-size: 13px;
    font-weight: 600;
    line-height: 20px;
}
