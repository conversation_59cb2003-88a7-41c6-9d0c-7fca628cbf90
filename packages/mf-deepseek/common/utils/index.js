import { markdownToText } from './markdown-to-text';

export const isString = (value) => typeof value === 'string';

export const sleep = (t) => {
    let timerId;
    const promise = new Promise((resolve) => {
        timerId = setTimeout(() => resolve(), t);
    });

    return {
        promise,
        cancel: () => {
            if (timerId) {
                clearTimeout(timerId);
                timerId = null;
            }
        },
    };
};

export const getMarkdownText = (content) => {
    if (!content) return '';

    return markdownToText(content).trim().replace(/\n/g, '');
};

/**
 * 格式化时间
 * @param ms {number} - 毫秒数
 * @returns {string} - 格式化后的时间
 */
export const formatTime = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60).toString().padStart(2, '0');
    const remainingSeconds = (seconds % 60).toString().padStart(2, '0');
    return `${minutes}:${remainingSeconds}`;
};
/**
 * 去除 markdown 代码块包裹（如 ```json ... ```），返回纯 JSON 字符串
 * @param {string} str
 * @returns {string}
 */
export function stripJsonCodeBlock(str) {
    return str
        .replace(/^```json\s*/i, '') // 去掉开头 ```json
        .replace(/```$/, '') // 去掉结尾 ```
        .trim();
}

/**
 * 将有序列表转换为无序列表
 * @param {string} str - 可能包含有序列表的字符串
 * @returns {string} - 转换后的字符串，有序列表被替换为无序列表
 */
export function convertOrderedToUnorderedList(str) {
    if (!str || typeof str !== 'string') return str;
    
    // 检查是否包含有序列表（数字+点+空格开头的行）
    const hasOrderedList = /^\s*\d+\.\s+/m.test(str);
    
    if (!hasOrderedList) return str;
    
    // 将有序列表转换为无序列表
    // 匹配数字+点+空格开头的行，替换为-+空格
    return str.replace(/^(\s*)\d+\.\s+/gm, '$1- ');
}

export const smoothScroll = (element, target, duration = 300, easing = 'easeInOutQuad') => {
    const start = element.scrollTop;
    const distance = target - start;
    const startTime = performance.now();
    let isAnimating = true;

    const easingFunctions = {
        easeInOutQuad: (t) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),
        linear: (t) => t,
    };

    const animate = (time) => {
        if (!isAnimating) return;

        const elapsed = time - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeProgress = easingFunctions[easing](progress);

        element.scrollTop = start + distance * easeProgress;

        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            isAnimating = false;
        }
    };

    requestAnimationFrame(animate);

    return () => { isAnimating = false; }; // 返回取消函数
};
