/* eslint-disable no-useless-escape */
export function markdownToText(markdown) {
    return (
        markdown
        // 移除代码块
            .replace(/```[\s\S]*?```/g, '')
        // 移除行内代码（保留代码内容）
            .replace(/`([^`]+)`/g, '$1')
        // 修复点1：精准匹配图片（允许包含括号）
            .replace(/!\[(.*?)\]\([^)]*?\)/g, '$1')
        // 修复点2：精准匹配链接（允许特殊符号）
            .replace(/\[(.*?)\]\([^)]*?\)/g, '$1')
        // 移除加粗/斜体
            .replace(/(\*{1,3})(.*?)\1/g, '$2')
        // 移除标题
            .replace(/#{1,6}\s*/g, '')
        // 移除引用标记
            .replace(/^>\s+/gm, '')
        // 移除列表标记
            .replace(/^[\*\-+]\s+/gm, '')
        // 清理表格线
            .replace(/[\|\-]/g, '')
        // 移除删除线
            .replace(/~~(.*?)~~/g, '$1')
        // 合并换行
            .replace(/\n{3,}/g, '\n\n')
            .trim()
    );
}