/**
 * 流式输出模拟器
 * 用于模拟字符串的流式输出
 */

/**
 * 默认配置项
 */
const DEFAULT_OPTIONS = {
    // 字符输出间隔时间（毫秒）
    interval: 20,
    // 每次输出的字符数量
    chunkSize: 3,
    // 是否在流结束后自动销毁
    autoDestroy: true,
};

/**
 * 流式输出模拟器类
 */
class StreamSimulator {
    /**
     * 构造函数
     * @param {string} content - 要流式输出的内容
     * @param {Object} options - 配置选项
     */
    constructor(content, options = {}) {
        // 验证输入内容
        if (typeof content !== 'string') {
            throw new TypeError('内容必须是字符串类型');
        }

        // 合并配置
        this.options = {
            ...DEFAULT_OPTIONS,
            ...options,
        };
        
        // 初始化状态
        this.content = content;
        this.outputContent = '';
        this.currentIndex = 0;
        this.intervalId = null;
        this.isDestroyed = false;
        this.callback = null;
    }

    /**
     * 开始流式输出
     * @param {Function} callback - 每次输出块的回调函数，接收当前输出块和累计输出内容
     * @returns {StreamSimulator} 当前实例，支持链式调用
     */
    start(callback) {
        if (this.isDestroyed) {
            console.warn('流模拟器已销毁，无法启动');
            return this;
        }

        if (this.intervalId !== null) {
            console.warn('流模拟器已在运行中');
            return this;
        }

        this.callback = callback;
        
        this.intervalId = setInterval(() => {
            this._outputNextChunk();
        }, this.options.interval);

        return this;
    }

    /**
     * 重置流式输出
     * @returns {StreamSimulator} 当前实例，支持链式调用
     */
    reset() {
        if (this.isDestroyed) {
            console.warn('流模拟器已销毁，无法重置');
            return this;
        }

        this._clearInterval();
        this.outputContent = '';
        this.currentIndex = 0;
        
        return this;
    }

    /**
     * 销毁流式输出模拟器
     */
    destroy() {
        if (this.isDestroyed) {
            return;
        }

        this._clearInterval();
        this.isDestroyed = true;
        this.callback = null;
    }

    /**
     * 获取当前已输出的内容
     * @returns {string} 当前已输出的内容
     */
    getOutputContent() {
        return this.outputContent;
    }


    /**
     * 获取剩余未输出的内容
     * @returns {string} 剩余未输出的内容
     */
    getRemainingContent() {
        return this.content.slice(this.currentIndex);
    }
    
    /**
     * 获取当前进度（0-1之间的小数）
     * @returns {number} 当前进度
     */
    getProgress() {
        if (this.currentIndex >= this.content.length) {
            return 1;
        }
        
        return this.currentIndex / this.content.length;
    }


    /**
     * 清除定时器
     * @private
     */
    _clearInterval() {
        if (this.intervalId !== null) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    /**
     * 输出下一块内容
     * @private
     */
    _outputNextChunk() {
        if (this.isDestroyed) {
            return;
        }

        // 已完成所有输出
        if (this.currentIndex >= this.content.length) {
            if (this.options.autoDestroy) {
                this.destroy();
            }
            
            return;
        }

        // 计算本次要输出的内容
        const remainingLength = this.content.length - this.currentIndex;
        const chunkSize = Math.min(this.options.chunkSize, remainingLength);

        // 获取本次输出的内容块
        const chunk = this.content.substr(this.currentIndex, chunkSize);
        this.outputContent += chunk;
        this.currentIndex += chunkSize;

        // 调用回调函数
        if (typeof this.callback === 'function') {
            this.callback(chunk, this.getProgress());
        }
    }
}


/**
 * 简单的流式输出函数，返回一个Promise和simulator实例
 * @param {string} content - 要流式输出的内容
 * @param {Function} onChunk - 每次输出块的回调函数
 * @param {Object} options - 配置选项
 * @returns {{ promise: Promise<string>, simulator: StreamSimulator }}
 */
export const createStreamSimulator = (content, onChunk, options = {}) => {
    let resolvePromise;
    const promise = new Promise((resolve) => {
        resolvePromise = resolve;
    });
    const simulator = new StreamSimulator(content, {
        autoDestroy: true,
        ...options,
    });
    simulator.start((outputContent, progress) => {
        if (typeof onChunk === 'function') {
            onChunk(outputContent);
        }
        if (progress === 1) {
            resolvePromise(outputContent);
        }
    });
    return {
        promise, simulator, 
    };
};

export default {
    createStreamSimulator,
};
