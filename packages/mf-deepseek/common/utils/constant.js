// 正式环境
export const PROD_ENV = process.env.BUILD_ENV === 'prod';
// 灰度环境
export const GRAY_ENV = process.env.BUILD_ENV === 'gray';
// 预发布环境
export const PRE_ENV = process.env.BUILD_ENV === 'pre';
// 测试环境
export const TEST_ENV = process.env.BUILD_ENV === 'test';
// 开发环境
export const DEV_ENV = process.env.BUILD_ENV === 'dev';
// 本地环境
export const LOCAL_ENV = process.env.BUILD_ENV === undefined;

// 统一正式环境
export const isProd = PROD_ENV || GRAY_ENV || PRE_ENV;

// 统一测试环境
export const isTest = TEST_ENV;

// 统一开发环境
export const isDev = DEV_ENV || LOCAL_ENV;

// 本地环境
export const isLocal = LOCAL_ENV;


export const NODE_TYPE = {
    MARKDOWN: 'markdown', // Markdown 内容
    CUSTOM: 'custom', // 自定义组件
};

export const EVENT_BUS_NAME = 'use-deepseek-suggestion';
export const EVENT_MR_BUS_NAME = 'use-global-deepseek-suggestion';
