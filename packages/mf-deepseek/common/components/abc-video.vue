<script>
    export default {
        props: {
            srcUrl: {
                type: String,
                required: true,
            },
            poster: {
                type: String,
                required: true,
            },
            controls: {
                type: Boolean,
                default: true,
            },
            muted: {
                type: Boolean,
                default: true,
            },
            autoplay: {
                type: Boolean,
                default: true,
            },
            width: {
                type: Number,
                default: 662,
            },
            height: {
                type: Number,
                default: 402,
            },
        },
        computed: {
            processedVideoUrl() {
                return this.interceptUrl(this.srcUrl);
            },
        },
        methods: {
            interceptUrl(url) {
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    return url;
                }

                if (window.remote && window.remote.app && window.remote.app.urlIntercept) {
                    return window.remote.app.urlIntercept(url);
                }

                const isChongqingPrivateNetworkMode = window.electron && window.electron.appConfig && this.getChongqingPrivateNetworkMode();

                if (isChongqingPrivateNetworkMode) {
                    const protocol = url.startsWith('https://') ? 'https' : 'http';
                    const path = url.substring(protocol.length + 3);
                    return `http://***********:7001/${protocol}/${path}`;
                }

                return url;
            },
            // 通过下面代码判断是否是专网模式
            getChongqingPrivateNetworkMode() {
                const isChongqingPrivateNetworkMode = window.electron.appConfig.get('proxy.proxyNetworkRegion') === 'chongqing';
                return isChongqingPrivateNetworkMode;
            },
            play() {
                this.$refs.video.play();
            },
        },
    };
</script>

<template>
    <video
        ref="video"
        :src="processedVideoUrl"
        :poster="poster"
        :controls="controls"
        :muted="muted"
        :autoplay="autoplay"
        :height="height"
        :width="width"
    >
        <source :src="processedVideoUrl" type="video/mp4" />
    </video>
</template>
