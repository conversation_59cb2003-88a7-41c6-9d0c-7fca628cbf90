<template>
    <abc-flex
        vertical
        gap="large"
    >
        <skeleton :width="90"></skeleton>
        <skeleton :width="180"></skeleton>
    </abc-flex>
</template>

<script>
    import { defineComponent } from 'vue';
    import Skeleton from './skeleton.vue';

    export default defineComponent({
        name: 'Loading',
        components: {
            Skeleton,
        },
    });
</script>
