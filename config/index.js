"use strict";
const Tool = require("abc-fed-build-tool");
const path = require("path");
const useOSS = process.env.BUILD_ENV === 'gray' || process.env.BUILD_ENV === 'test'

const ossType = process.env.OSS_TYPE || "ali";
const strategy = {
    "ali": function(env) {
        return Tool.OSS.getOSSInfo(env, `pc`, useOSS);
    },
    "tencent": function(env) {
        return Tool.OSS.getCOSInfo(env, `pc`, useOSS);
    }
};

const bucketInfo = strategy[ossType](process.env.BUILD_ENV || "dev");

const FeEngineStrategy = {
    "ali": function(env) {
        env = env === "own" ? "dev" : env === "feature" ? "test" : env;
        return Tool.OSS.getOSSInfo(env, `abc-fe-engine`,  useOSS);
    },
    "tencent": function(env) {
        env = env === "own" ? "dev" : env === "feature" ? "test" : env;
        return Tool.OSS.getCOSInfo(env, `abc-fe-engine`, useOSS);
    }
};

const MedicalImagingViewerSDKStrategy = {
    "ali": function(env) {
        env = env === "own" ? "dev" : env === "feature" ? "test" : env;
        return Tool.OSS.getOSSInfo(env, `abc-medical-imaging-viewer-sdk`, useOSS);
    },
    "tencent": function(env) {
        env = env === "own" ? "dev" : env === "feature" ? "test" : env;
        return Tool.OSS.getCOSInfo(env, `abc-medical-imaging-viewer-sdk`, useOSS);
    }
};

const EmrEditorSDKStrategy = {
    "ali": function(env) {
        env = env === "own" ? "dev" : env === "feature" ? "test" : env;
        return Tool.OSS.getOSSInfo(env, `abc-emr-editor-sdk`, useOSS);
    },
    "tencent": function(env) {
        env = env === "own" ? "dev" : env === "feature" ? "test" : env;
        return Tool.OSS.getCOSInfo(env, `abc-emr-editor-sdk`, useOSS);
    }
};

const AbcFedConfigStrategy = {
    "ali": function(env) {
        env = env === "own" ? "dev" : env === "feature" ? "test" : env;
        return Tool.OSS.getOSSInfo(env, `abc-fed-config`, true);
    },
    "tencent": function(env) {
        env = env === "own" ? "dev" : env === "feature" ? "test" : env;
        return Tool.OSS.getCOSInfo(env, `abc-fed-config`, true);
    }
};

let abcFeEngineLoaderUrl = FeEngineStrategy[ossType](process.env.BUILD_ENV || "dev").url + "loader.js";
let abcMedicalImagingViewerSDKLoader = MedicalImagingViewerSDKStrategy[ossType](process.env.BUILD_ENV || "dev").url + "loader.js";
let abcEmrEditorSDKLoader = EmrEditorSDKStrategy[ossType](process.env.BUILD_ENV || "dev").url + "loader.js";
let abcFedConfigLoaderUrl = AbcFedConfigStrategy[ossType](process.env.BUILD_ENV || "dev").url + 'abc-fed-config.js';

function getAbcFedConfigInnerHtml() {
    return `
        const RUNTIME_INFO = window.ABC_RUNTIME || {};

        window.ABC_FED_BUILD_ENV = '${process.env.BUILD_ENV || "dev"}';
        window.ABC_FED_BASE_URL = '${AbcFedConfigStrategy[ossType](process.env.BUILD_ENV || "dev").url}';
        window.ABC_FED_PROJECT_BUILD_TIME = {
            'pc': 1749639600000,
            'abc-fe-engine': 1749639600000,
            'mf-order-cloud': 1749639600000,
        }

        const { host } = location;
        const ownRegex = /(\\d+)\\.own\\.abczs\\.cn/;
        const featureRegex = /(\\d+)\\.ftest\\.abczs\\.cn/;
        const devOrTestRegex = /^(.*?)\\.abczs\\.cn/;
        const prodRegex = /^(\\*\\.)?(\\w+\\.)?abcyun\\.cn$/;

        const ownMatch = host.match(ownRegex);
        const featureMatch = host.match(featureRegex);
        const devOrTestMatch = host.match(devOrTestRegex);
        const prodMatch = host.match(prodRegex);

        function resolveFedConfigEnv() {
            if (ownMatch) {
                return 'o' + ownMatch[1];
            }

            if (featureMatch) {
                return 'f' + featureMatch[1];
            }

            if (devOrTestMatch) {
                if (devOrTestMatch[1].startsWith('region')) {
                    return devOrTestMatch[1].split('-')[1];
                }

                return devOrTestMatch[1];
            }

            if (prodMatch) {
                return RUNTIME_INFO.env || window.ABC_FED_BUILD_ENV;
            }

            return 'dev';
        }

        function resolveFedConfigRegion() {
            if (RUNTIME_INFO.region) {
                return RUNTIME_INFO.region;
            }

            if (host.startsWith('region2')) {
                return 'region2';
            }

            return 'region1';
        }

        function resolveFedConfigZone() {
            if (ownMatch || featureMatch) {
                return null;
            }

            if (RUNTIME_INFO.zone) {
                return RUNTIME_INFO.zone;
            }

            return null;
        }

        function checkOfflineFedConfigSupport() {
            if (location.protocol !== 'abcyun:') {
                return false;
            }

            if (!window.electron) {
                return false;
            }

            try {
                const electronRemote = window.electron && window.electron.remote;
                const offlineBundler = electronRemote && electronRemote.app && electronRemote.app.offlineBundler;

                const info = offlineBundler && offlineBundler.getOfflineBundleInfoListSync ? offlineBundler.getOfflineBundleInfoListSync() : null;
                if (!info || !Array.isArray(info.list) || info.list.length === 0) {
                    return false;
                }

                const projectKeys = Object.keys(window.ABC_FED_PROJECT_BUILD_TIME);

                return projectKeys.every((key) => {
                    const project = info.list.find((item) => item.name === key);
                    if (!project || !project.version) {
                        return false;
                    }

                    const buildTime = parseInt(project.buildTime, 10) || 0;
                    const requiredTime = parseInt(window.ABC_FED_PROJECT_BUILD_TIME[key], 10) || 0;
                    return buildTime >= requiredTime;
                });
            } catch (e) {
                console.error('Error checking offline bundle info:', e);
                return false;
            }
        }

        const region = resolveFedConfigRegion();
        const env = resolveFedConfigEnv();
        const zone = resolveFedConfigZone();
        (function(){
            const ipRegex = /^(?:\\d{1,3}\\.){3}\\d{1,3}$/;
            if (ipRegex.test(host)) {
                document.write("<script src='" + window.ABC_FED_BASE_URL + "region1-dev-his.js'><\\/script>");
            } else if (checkOfflineFedConfigSupport()) {
                document.write("<script src='" + window.ABC_FED_BASE_URL + region + "-" + env + (zone ? '-' + zone : '') + "-his-offline.js'><\\/script>");
            } else {
                document.write("<script src='" + window.ABC_FED_BASE_URL + region + "-" + env + (zone ? '-' + zone : '') + "-his.js'><\\/script>");
            }

            let link = document.createElement('link');
            link.rel = 'icon';
            link.href = "//cis-static-common.oss-cn-shanghai.aliyuncs.com/img/favicon/favicon_" + env + (zone === 'standby' ? '_standby' : '') + ".png";
            document.head.appendChild(link);
        })();
    `
}

module.exports = {
    // 本地调试
    devonline: {

        // Paths
        assetsSubDirectory: "static",
        assetsPublicPath: "/",
        abcFeEngineLoaderUrl,
        abcMedicalImagingViewerSDKLoader,
        abcEmrEditorSDKLoader,
        abcFedConfigLoaderUrl,
        proxyTable: [
            {
                context: ['/api/mall/'],
                target: 'http://mall.dev.abczs.cn',
                changeOrigin: true,
            },
            {
                context: ['/api/chat'],
                target: 'http://localhost:3600',
                changeOrigin: true,
                onProxyReq: (proxyReq) => {
                    // 移除可能触发压缩的头
                    proxyReq.removeHeader('accept-encoding');
                },
                onProxyRes: (proxyRes) => {
                    // 移除响应中的压缩相关头
                    delete proxyRes.headers['content-encoding'];
                    delete proxyRes.headers['content-length'];
                },
            },
            {
                context: '/api/v2/bpcrm',
                target: 'http://dev-oa.abczs.cn',
                changeOrigin: true,
            },
            {
                context: '/rpc/supervision',
                target: 'https://dev.abczs.cn',
                changeOrigin: true,
            },
            {
                context: '/api/v3/goods/stocks/in/order-draft/*/(parse-file-data|parse-mall-data)',
                target: 'http://region1-dev.abczs.cn',
                ws: true,
                changeOrigin: true,
                selfHandleResponse: true,
                onProxyRes(proxyRes, req, res) {
                    if (proxyRes.headers['set-cookie']) {
                        proxyRes.headers['set-cookie'] = proxyRes.headers['set-cookie'].map(item => {
                            item = item.replace(/[Dd]omain=\.?abczs.cn/g, 'domain=' + req.headers.host.split(':')[0]);
                            return item;
                        });
                    }
                    proxyRes.on('data', function (data) {
                        res.write(data);
                    });

                    proxyRes.on('end', function () {
                        res.end();
                    });
                },
                bypass: function(req, res, proxyOptions) {
                    req.headers['origin'] = 'http://region1-dev.abczs.cn';
                },
            },
            // 本地开发 abc-cis-print-service 时打开
            // {
            //     context: '/api/print',
            //     target: 'http://127.0.0.1:3070',
            //     ws: true,
            //     changeOrigin: true,
            //     onProxyRes(proxyRes, req, res) {
            //         if (proxyRes.headers['set-cookie']) {
            //             proxyRes.headers['set-cookie'] = proxyRes.headers['set-cookie'].map(item => {
            //                 item = item.replace(/[Dd]omain=\.?abczs.cn/g, 'domain=' + req.headers.host.split(':')[0]);
            //                 return item;
            //             });
            //         }
            //     },
            //     bypass: function(req, res, proxyOptions) {
            //         req.headers['origin'] = 'http://region1-dev.abczs.cn';
            //     },
            // },
            {
                context: '/api/',
                target: 'http://region1-dev.abczs.cn',
                ws: true,
                changeOrigin: true,
                onProxyRes(proxyRes, req, res) {
                    if (proxyRes.headers['set-cookie']) {
                        proxyRes.headers['set-cookie'] = proxyRes.headers['set-cookie'].map(item => {
                            item = item.replace(/[Dd]omain=\.?abczs.cn/g, 'domain=' + req.headers.host.split(':')[0]);
                            return item;
                        });
                    }
                },
                bypass: function(req, res, proxyOptions) {
                    req.headers['origin'] = 'http://region1-dev.abczs.cn';
                },
            },
            {
                context: '/socket.io/',
                target: 'http://region1-dev.abczs.cn',
                changeOrigin: true,
                ws: true,
            },
            {
                context: '/asr/',
                target: 'http://127.0.0.1:3000',
                changeOrigin: true,
                ws: true,
            }
        ],

        // Various Dev Server settings
        host: "localhost", // can be overwritten by process.env.HOST
        port: 8080, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
        autoOpenBrowser: true,
        errorOverlay: true,
        notifyOnErrors: true,
        poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

        /**
         * Source Maps
         */

        // https://webpack.js.org/configuration/devtool/#development
        devtool: "eval-cheap-module-source-map",

        // If you have problems debugging vue-files in devtools,
        // set this to false - it *may* help
        // https://vue-loader.vuejs.org/en/options.html#cachebusting
        cacheBusting: true,

        // CSS Sourcemaps off by default because relative paths are "buggy"
        // with this option, according to the CSS-Loader README
        // (https://github.com/webpack/css-loader#sourcemaps)
        // In our experience, they generally work as expected,
        // just be aware of this issue when enabling this option.
        cssSourceMap: true
    },

    // 测试环境/生产环境
    build: {
        // Template for index.html
        index: path.resolve(__dirname, "../dist/index.html"),

        // Paths
        assetsRoot: path.resolve(__dirname, "../dist"),
        assetsSubDirectory: "static",
        assetsPublicPath: bucketInfo.url,
        abcFeEngineLoaderUrl,
        abcMedicalImagingViewerSDKLoader,
        abcEmrEditorSDKLoader,
        abcFedConfigLoaderUrl,
        staticPath: "/", //生产环境 staticPath:''

        /**
         * Source Maps
         */
        productionSourceMap: false,
        // https://webpack.js.org/configuration/devtool/#production
        devtool: "#source-map",

        // Gzip off by default as many popular static hosts such as
        // Surge or Netlify already gzip all static assets for you.
        // Before setting to `true`, make sure to:
        // npm install --save-dev compression-webpack-plugin
        productionGzip: false,
        productionGzipExtensions: ["js", "css"],

        // Run the build command with an extra argument to
        // View the bundle analyzer report after build finishes:
        // `npm run build --report`
        // Set to `true` or `false` to always turn it on or off
        bundleAnalyzerReport: process.env.npm_config_report,
        bucketInfo
    },

    getAbcFedConfigInnerHtml
};

